{"ast": null, "code": "import { NgForO<PERSON>, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { signal, inject, EventEmitter, computed, isSignal, Component, ChangeDetectionStrategy, ViewChildren, forwardRef, Input, Output, ViewChild } from '@angular/core';\nimport { TuiElement } from '@taiga-ui/cdk/directives/element';\nimport * as i1 from '@taiga-ui/core/components/data-list';\nimport { tuiInjectDataListSize, tuiAsDataListAccessor, TuiDataListComponent, TuiOption, TuiOptionWithValue, TuiDataList, TuiDataListDirective } from '@taiga-ui/core/components/data-list';\nimport { TuiLoader } from '@taiga-ui/core/components/loader';\nimport { PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiIsNativeFocused } from '@taiga-ui/cdk/utils/focus';\nimport { tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { TUI_ITEMS_HANDLERS as TUI_ITEMS_HANDLERS$1 } from '@taiga-ui/kit/tokens';\nfunction TuiDataListWrapperComponent_tui_data_list_0_ng_container_1_button_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r4 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r4, \" \");\n  }\n}\nfunction TuiDataListWrapperComponent_tui_data_list_0_ng_container_1_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 7, 2);\n    i0.ɵɵlistener(\"click.capture\", function TuiDataListWrapperComponent_tui_data_list_0_ng_container_1_button_1_Template_button_click_capture_0_listener() {\n      const item_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.itemClick.emit(item_r2));\n    });\n    i0.ɵɵtemplate(2, TuiDataListWrapperComponent_tui_data_list_0_ng_container_1_button_1_ng_container_2_Template, 2, 1, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const elementRef_r5 = i0.ɵɵreference(1);\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabledItemHandler(item_r2))(\"value\", item_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r2.itemContent)(\"polymorpheusOutletContext\", ctx_r2.getContext(item_r2, elementRef_r5));\n  }\n}\nfunction TuiDataListWrapperComponent_tui_data_list_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiDataListWrapperComponent_tui_data_list_0_ng_container_1_button_1_Template, 3, 4, \"button\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.$cast(ctx_r2.items));\n  }\n}\nfunction TuiDataListWrapperComponent_tui_data_list_0_ng_template_2_button_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r8 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r8, \" \");\n  }\n}\nfunction TuiDataListWrapperComponent_tui_data_list_0_ng_template_2_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10, 2);\n    i0.ɵɵlistener(\"click\", function TuiDataListWrapperComponent_tui_data_list_0_ng_template_2_button_0_Template_button_click_0_listener() {\n      const item_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.itemClick.emit(item_r7));\n    });\n    i0.ɵɵelementStart(2, \"span\", 11);\n    i0.ɵɵtemplate(3, TuiDataListWrapperComponent_tui_data_list_0_ng_template_2_button_0_ng_container_3_Template, 2, 1, \"ng-container\", 8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const elementRef_r9 = i0.ɵɵreference(1);\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabledItemHandler(item_r7))(\"value\", item_r7);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r2.itemContent)(\"polymorpheusOutletContext\", ctx_r2.getContext(item_r7, elementRef_r9));\n  }\n}\nfunction TuiDataListWrapperComponent_tui_data_list_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiDataListWrapperComponent_tui_data_list_0_ng_template_2_button_0_Template, 4, 4, \"button\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.$cast(ctx_r2.items));\n  }\n}\nfunction TuiDataListWrapperComponent_tui_data_list_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tui-data-list\", 4);\n    i0.ɵɵtemplate(1, TuiDataListWrapperComponent_tui_data_list_0_ng_container_1_Template, 2, 1, \"ng-container\", 5)(2, TuiDataListWrapperComponent_tui_data_list_0_ng_template_2_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const legacyOptionFallback_r10 = i0.ɵɵreference(3);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"emptyContent\", ctx_r2.emptyContent)(\"size\", ctx_r2.size);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newOptionMode)(\"ngIfElse\", legacyOptionFallback_r10);\n  }\n}\nfunction TuiDataListWrapperComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-loader\", 12);\n  }\n}\nconst _c0 = [\"labels\", \"\"];\nfunction TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_container_1_button_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r4 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r4, \" \");\n  }\n}\nfunction TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_container_1_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9, 2);\n    i0.ɵɵlistener(\"click.capture\", function TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_container_1_button_1_Template_button_click_capture_0_listener() {\n      const item_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.itemClick.emit(item_r2));\n    });\n    i0.ɵɵtemplate(2, TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_container_1_button_1_ng_container_2_Template, 2, 1, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const elementRef_r5 = i0.ɵɵreference(1);\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabledItemHandler(item_r2))(\"value\", item_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r2.itemContent)(\"polymorpheusOutletContext\", ctx_r2.getContext(item_r2, elementRef_r5));\n  }\n}\nfunction TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_container_1_button_1_Template, 3, 4, \"button\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const group_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", group_r6);\n  }\n}\nfunction TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_template_2_button_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r9 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r9, \" \");\n  }\n}\nfunction TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_template_2_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12, 2);\n    i0.ɵɵlistener(\"click\", function TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_template_2_button_0_Template_button_click_0_listener() {\n      const item_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.itemClick.emit(item_r8));\n    });\n    i0.ɵɵtemplate(2, TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_template_2_button_0_ng_container_2_Template, 2, 1, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const elementRef_r10 = i0.ɵɵreference(1);\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabledItemHandler(item_r8))(\"value\", item_r8);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r2.itemContent)(\"polymorpheusOutletContext\", ctx_r2.getContext(item_r8, elementRef_r10));\n  }\n}\nfunction TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_template_2_button_0_Template, 3, 4, \"button\", 11);\n  }\n  if (rf & 2) {\n    const group_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngForOf\", group_r6);\n  }\n}\nfunction TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tui-opt-group\", 6);\n    i0.ɵɵtemplate(1, TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_container_1_Template, 2, 1, \"ng-container\", 7)(2, TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_template_2_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const index_r11 = ctx.index;\n    const legacyOptionFallback_r12 = i0.ɵɵreference(3);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", ctx_r2.labels[index_r11]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newOptionMode)(\"ngIfElse\", legacyOptionFallback_r12);\n  }\n}\nfunction TuiDataListGroupWrapperComponent_tui_data_list_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tui-data-list\", 4);\n    i0.ɵɵtemplate(1, TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_Template, 4, 3, \"tui-opt-group\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"emptyContent\", ctx_r2.emptyContent)(\"size\", ctx_r2.size);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.items);\n  }\n}\nfunction TuiDataListGroupWrapperComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-loader\", 13);\n  }\n}\nconst _c1 = \"[_nghost-%COMP%]{display:block}.t-content[_ngcontent-%COMP%]{flex:1;min-inline-size:0}.t-loader[_ngcontent-%COMP%]{margin:.75rem 0}\";\nclass TuiDataListWrapperComponent {\n  constructor() {\n    this.datalist = signal(null);\n    this.itemsHandlers = inject(TUI_ITEMS_HANDLERS);\n    // TODO(v5): delete\n    this.itemsHandlersLegacy = inject(TUI_ITEMS_HANDLERS$1);\n    // TODO(v5): delete\n    this.legacyOptionsQuery = EMPTY_QUERY;\n    // TODO(v5): delete\n    this.optionsQuery = EMPTY_QUERY;\n    this.newOptionMode = tuiInjectElement().hasAttribute('new');\n    this.items = [];\n    this.disabledItemHandler = this.newOptionMode ? this.itemsHandlers?.disabledItemHandler() : this.itemsHandlersLegacy.disabledItemHandler;\n    this.size = tuiInjectDataListSize();\n    this.itemClick = new EventEmitter();\n    this.options = computed(() => this.datalist()?.options() ?? []);\n    this.itemContent = ({\n      $implicit\n    }) => this.newOptionMode ? this.itemsHandlers.stringify()($implicit) : this.itemsHandlersLegacy.stringify($implicit);\n  }\n  getContext($implicit, {\n    nativeElement\n  }) {\n    return {\n      $implicit,\n      active: tuiIsNativeFocused(nativeElement)\n    };\n  }\n  // TODO(v5): delete\n  getOptions(includeDisabled = false) {\n    return [...this.legacyOptionsQuery, ...this.optionsQuery].filter(({\n      disabled\n    }) => includeDisabled || !disabled).map(({\n      value\n    }) => isSignal(value) ? value() : value).filter(tuiIsPresent);\n  }\n  // TODO(v5): use signal `viewChild`\n  set datalistSetter(x) {\n    this.datalist.set(x);\n  }\n  $cast(items) {\n    return items;\n  }\n  static {\n    this.ɵfac = function TuiDataListWrapperComponent_Factory(t) {\n      return new (t || TuiDataListWrapperComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiDataListWrapperComponent,\n      selectors: [[\"tui-data-list-wrapper\", 3, \"labels\", \"\"], [\"tui-data-list-wrapper\", \"new\", \"\", 3, \"labels\", \"\"]],\n      viewQuery: function TuiDataListWrapperComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TuiDataListComponent, 5);\n          i0.ɵɵviewQuery(TuiOption, 5);\n          i0.ɵɵviewQuery(TuiOptionWithValue, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.datalistSetter = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.legacyOptionsQuery = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionsQuery = _t);\n        }\n      },\n      inputs: {\n        items: \"items\",\n        disabledItemHandler: \"disabledItemHandler\",\n        emptyContent: \"emptyContent\",\n        size: \"size\",\n        itemContent: \"itemContent\"\n      },\n      outputs: {\n        itemClick: \"itemClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsDataListAccessor(TuiDataListWrapperComponent)]), i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[\"loading\", \"\"], [\"legacyOptionFallback\", \"\"], [\"elementRef\", \"elementRef\"], [3, \"emptyContent\", \"size\", 4, \"ngIf\", \"ngIfElse\"], [3, \"emptyContent\", \"size\"], [4, \"ngIf\", \"ngIfElse\"], [\"new\", \"\", \"tuiElement\", \"\", \"tuiOption\", \"\", \"type\", \"button\", 3, \"disabled\", \"value\", \"click.capture\", 4, \"ngFor\", \"ngForOf\"], [\"new\", \"\", \"tuiElement\", \"\", \"tuiOption\", \"\", \"type\", \"button\", 3, \"click.capture\", \"disabled\", \"value\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [\"automation-id\", \"tui-data-list-wrapper__option\", \"tuiElement\", \"\", \"tuiOption\", \"\", \"type\", \"button\", 3, \"disabled\", \"value\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"automation-id\", \"tui-data-list-wrapper__option\", \"tuiElement\", \"\", \"tuiOption\", \"\", \"type\", \"button\", 3, \"click\", \"disabled\", \"value\"], [1, \"t-content\"], [\"automation-id\", \"tui-data-list-wrapper__loader\", 1, \"t-loader\"]],\n      template: function TuiDataListWrapperComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiDataListWrapperComponent_tui_data_list_0_Template, 4, 4, \"tui-data-list\", 3)(1, TuiDataListWrapperComponent_ng_template_1_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const loading_r11 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.items)(\"ngIfElse\", loading_r11);\n        }\n      },\n      dependencies: [NgForOf, NgIf, PolymorpheusOutlet, i1.TuiDataListComponent, i1.TuiOption, i1.TuiOptionNew, i1.TuiOptionWithValue, TuiElement, TuiLoader],\n      styles: [\"[_nghost-%COMP%]{display:block}.t-content[_ngcontent-%COMP%]{flex:1;min-inline-size:0}.t-loader[_ngcontent-%COMP%]{margin:.75rem 0}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDataListWrapperComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-data-list-wrapper:not([labels]), tui-data-list-wrapper:not([labels])[new]',\n      imports: [NgForOf, NgIf, PolymorpheusOutlet, TuiDataList, TuiElement, TuiLoader],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAsDataListAccessor(TuiDataListWrapperComponent)],\n      template: \"<tui-data-list\\n    *ngIf=\\\"items; else loading\\\"\\n    [emptyContent]=\\\"emptyContent\\\"\\n    [size]=\\\"size\\\"\\n>\\n    <ng-container *ngIf=\\\"newOptionMode; else legacyOptionFallback\\\">\\n        <button\\n            *ngFor=\\\"let item of $cast(items)\\\"\\n            #elementRef=\\\"elementRef\\\"\\n            new\\n            tuiElement\\n            tuiOption\\n            type=\\\"button\\\"\\n            [disabled]=\\\"disabledItemHandler(item)\\\"\\n            [value]=\\\"item\\\"\\n            (click.capture)=\\\"itemClick.emit(item)\\\"\\n        >\\n            <ng-container *polymorpheusOutlet=\\\"itemContent as text; context: getContext(item, elementRef)\\\">\\n                {{ text }}\\n            </ng-container>\\n        </button>\\n    </ng-container>\\n\\n    <!-- TODO(v5): delete fallback -->\\n    <ng-template #legacyOptionFallback>\\n        <button\\n            *ngFor=\\\"let item of $cast(items)\\\"\\n            #elementRef=\\\"elementRef\\\"\\n            automation-id=\\\"tui-data-list-wrapper__option\\\"\\n            tuiElement\\n            tuiOption\\n            type=\\\"button\\\"\\n            [disabled]=\\\"disabledItemHandler(item)\\\"\\n            [value]=\\\"item\\\"\\n            (click)=\\\"itemClick.emit(item)\\\"\\n        >\\n            <span class=\\\"t-content\\\">\\n                <ng-container *polymorpheusOutlet=\\\"itemContent as text; context: getContext(item, elementRef)\\\">\\n                    {{ text }}\\n                </ng-container>\\n            </span>\\n        </button>\\n    </ng-template>\\n</tui-data-list>\\n<ng-template #loading>\\n    <tui-loader\\n        automation-id=\\\"tui-data-list-wrapper__loader\\\"\\n        class=\\\"t-loader\\\"\\n    />\\n</ng-template>\\n\",\n      styles: [\":host{display:block}.t-content{flex:1;min-inline-size:0}.t-loader{margin:.75rem 0}\\n\"]\n    }]\n  }], null, {\n    legacyOptionsQuery: [{\n      type: ViewChildren,\n      args: [forwardRef(() => TuiOption)]\n    }],\n    optionsQuery: [{\n      type: ViewChildren,\n      args: [forwardRef(() => TuiOptionWithValue)]\n    }],\n    items: [{\n      type: Input\n    }],\n    disabledItemHandler: [{\n      type: Input\n    }],\n    emptyContent: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    itemClick: [{\n      type: Output\n    }],\n    itemContent: [{\n      type: Input\n    }],\n    datalistSetter: [{\n      type: ViewChild,\n      args: [TuiDataListComponent]\n    }]\n  });\n})();\nclass TuiDataListGroupWrapperComponent extends TuiDataListWrapperComponent {\n  constructor() {\n    super(...arguments);\n    this.labels = [];\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiDataListGroupWrapperComponent_BaseFactory;\n      return function TuiDataListGroupWrapperComponent_Factory(t) {\n        return (ɵTuiDataListGroupWrapperComponent_BaseFactory || (ɵTuiDataListGroupWrapperComponent_BaseFactory = i0.ɵɵgetInheritedFactory(TuiDataListGroupWrapperComponent)))(t || TuiDataListGroupWrapperComponent);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiDataListGroupWrapperComponent,\n      selectors: [[\"tui-data-list-wrapper\", \"labels\", \"\"]],\n      inputs: {\n        labels: \"labels\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsDataListAccessor(TuiDataListGroupWrapperComponent)]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 3,\n      vars: 2,\n      consts: [[\"loading\", \"\"], [\"legacyOptionFallback\", \"\"], [\"elementRef\", \"elementRef\"], [3, \"emptyContent\", \"size\", 4, \"ngIf\", \"ngIfElse\"], [3, \"emptyContent\", \"size\"], [3, \"label\", 4, \"ngFor\", \"ngForOf\"], [3, \"label\"], [4, \"ngIf\", \"ngIfElse\"], [\"new\", \"\", \"tuiElement\", \"\", \"tuiOption\", \"\", \"type\", \"button\", 3, \"disabled\", \"value\", \"click.capture\", 4, \"ngFor\", \"ngForOf\"], [\"new\", \"\", \"tuiElement\", \"\", \"tuiOption\", \"\", \"type\", \"button\", 3, \"click.capture\", \"disabled\", \"value\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [\"automation-id\", \"tui-data-list-wrapper__option\", \"tuiElement\", \"\", \"tuiOption\", \"\", \"type\", \"button\", 3, \"disabled\", \"value\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"automation-id\", \"tui-data-list-wrapper__option\", \"tuiElement\", \"\", \"tuiOption\", \"\", \"type\", \"button\", 3, \"click\", \"disabled\", \"value\"], [1, \"t-loader\"]],\n      template: function TuiDataListGroupWrapperComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiDataListGroupWrapperComponent_tui_data_list_0_Template, 2, 3, \"tui-data-list\", 3)(1, TuiDataListGroupWrapperComponent_ng_template_1_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const loading_r13 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.items)(\"ngIfElse\", loading_r13);\n        }\n      },\n      dependencies: [NgForOf, NgIf, PolymorpheusOutlet, i1.TuiDataListComponent, i1.TuiOption, i1.TuiOptionNew, i1.TuiOptionWithValue, i1.TuiOptGroup, TuiElement, TuiLoader],\n      styles: [_c1],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDataListGroupWrapperComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-data-list-wrapper[labels]',\n      imports: [NgForOf, NgIf, PolymorpheusOutlet, TuiDataList, TuiElement, TuiLoader],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAsDataListAccessor(TuiDataListGroupWrapperComponent)],\n      template: \"<tui-data-list\\n    *ngIf=\\\"items; else loading\\\"\\n    [emptyContent]=\\\"emptyContent\\\"\\n    [size]=\\\"size\\\"\\n>\\n    <tui-opt-group\\n        *ngFor=\\\"let group of items; let index = index\\\"\\n        [label]=\\\"labels[index]\\\"\\n    >\\n        <ng-container *ngIf=\\\"newOptionMode; else legacyOptionFallback\\\">\\n            <button\\n                *ngFor=\\\"let item of group\\\"\\n                #elementRef=\\\"elementRef\\\"\\n                new\\n                tuiElement\\n                tuiOption\\n                type=\\\"button\\\"\\n                [disabled]=\\\"disabledItemHandler(item)\\\"\\n                [value]=\\\"item\\\"\\n                (click.capture)=\\\"itemClick.emit(item)\\\"\\n            >\\n                <ng-container *polymorpheusOutlet=\\\"itemContent as text; context: getContext(item, elementRef)\\\">\\n                    {{ text }}\\n                </ng-container>\\n            </button>\\n        </ng-container>\\n\\n        <!-- TODO(v5): delete fallback -->\\n        <ng-template #legacyOptionFallback>\\n            <button\\n                *ngFor=\\\"let item of group\\\"\\n                #elementRef=\\\"elementRef\\\"\\n                automation-id=\\\"tui-data-list-wrapper__option\\\"\\n                tuiElement\\n                tuiOption\\n                type=\\\"button\\\"\\n                [disabled]=\\\"disabledItemHandler(item)\\\"\\n                [value]=\\\"item\\\"\\n                (click)=\\\"itemClick.emit(item)\\\"\\n            >\\n                <ng-container *polymorpheusOutlet=\\\"itemContent as text; context: getContext(item, elementRef)\\\">\\n                    {{ text }}\\n                </ng-container>\\n            </button>\\n        </ng-template>\\n    </tui-opt-group>\\n</tui-data-list>\\n<ng-template #loading>\\n    <tui-loader class=\\\"t-loader\\\" />\\n</ng-template>\\n\",\n      styles: [\":host{display:block}.t-content{flex:1;min-inline-size:0}.t-loader{margin:.75rem 0}\\n\"]\n    }]\n  }], null, {\n    labels: [{\n      type: Input\n    }]\n  });\n})();\nconst TuiDataListWrapper = [TuiDataListWrapperComponent, TuiDataListGroupWrapperComponent, TuiDataListDirective];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiDataListGroupWrapperComponent, TuiDataListWrapper, TuiDataListWrapperComponent };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i0", "signal", "inject", "EventEmitter", "computed", "isSignal", "Component", "ChangeDetectionStrategy", "ViewChildren", "forwardRef", "Input", "Output", "ViewChild", "TuiElement", "i1", "tuiInjectDataListSize", "tuiAsDataListAccessor", "TuiDataListComponent", "TuiOption", "TuiOptionWithValue", "TuiDataList", "TuiDataListDirective", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Polymorpheus<PERSON><PERSON>let", "EMPTY_QUERY", "tuiInjectElement", "tuiIsNativeFocused", "tuiIsPresent", "TUI_ITEMS_HANDLERS", "TUI_ITEMS_HANDLERS$1", "TuiDataListWrapperComponent_tui_data_list_0_ng_container_1_button_1_ng_container_2_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "text_r4", "polymorpheusOutlet", "ɵɵadvance", "ɵɵtextInterpolate1", "TuiDataListWrapperComponent_tui_data_list_0_ng_container_1_button_1_Template", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiDataListWrapperComponent_tui_data_list_0_ng_container_1_button_1_Template_button_click_capture_0_listener", "item_r2", "ɵɵrestoreView", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "itemClick", "emit", "ɵɵtemplate", "ɵɵelementEnd", "elementRef_r5", "ɵɵreference", "ɵɵproperty", "disabledItemHandler", "itemContent", "getContext", "TuiDataListWrapperComponent_tui_data_list_0_ng_container_1_Template", "$cast", "items", "TuiDataListWrapperComponent_tui_data_list_0_ng_template_2_button_0_ng_container_3_Template", "text_r8", "TuiDataListWrapperComponent_tui_data_list_0_ng_template_2_button_0_Template", "_r6", "TuiDataListWrapperComponent_tui_data_list_0_ng_template_2_button_0_Template_button_click_0_listener", "item_r7", "elementRef_r9", "TuiDataListWrapperComponent_tui_data_list_0_ng_template_2_Template", "TuiDataListWrapperComponent_tui_data_list_0_Template", "ɵɵtemplateRefExtractor", "legacyOptionFallback_r10", "emptyContent", "size", "newOptionMode", "TuiDataListWrapperComponent_ng_template_1_Template", "ɵɵelement", "_c0", "TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_container_1_button_1_ng_container_2_Template", "TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_container_1_button_1_Template", "TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_container_1_button_1_Template_button_click_capture_0_listener", "TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_container_1_Template", "group_r6", "TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_template_2_button_0_ng_container_2_Template", "text_r9", "TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_template_2_button_0_Template", "_r7", "TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_template_2_button_0_Template_button_click_0_listener", "item_r8", "elementRef_r10", "TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_ng_template_2_Template", "TuiDataListGroupWrapperComponent_tui_data_list_0_tui_opt_group_1_Template", "index_r11", "index", "legacyOptionFallback_r12", "labels", "TuiDataListGroupWrapperComponent_tui_data_list_0_Template", "TuiDataListGroupWrapperComponent_ng_template_1_Template", "_c1", "TuiDataListWrapperComponent", "constructor", "datalist", "itemsHandlers", "itemsHandlersLegacy", "legacyOptionsQuery", "optionsQuery", "hasAttribute", "options", "stringify", "nativeElement", "active", "getOptions", "includeDisabled", "filter", "disabled", "map", "value", "datalistSetter", "x", "set", "ɵfac", "TuiDataListWrapperComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "TuiDataListWrapperComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "inputs", "outputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiDataListWrapperComponent_Template", "loading_r11", "dependencies", "TuiOptionNew", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "TuiDataListGroupWrapperComponent", "arguments", "ɵTuiDataListGroupWrapperComponent_BaseFactory", "TuiDataListGroupWrapperComponent_Factory", "ɵɵgetInheritedFactory", "ɵɵInheritDefinitionFeature", "attrs", "TuiDataListGroupWrapperComponent_Template", "loading_r13", "TuiOptGroup", "TuiDataListWrapper"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-data-list-wrapper.mjs"], "sourcesContent": ["import { NgForOf, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { signal, inject, EventEmitter, computed, isSignal, Component, ChangeDetectionStrategy, ViewChildren, forwardRef, Input, Output, ViewChild } from '@angular/core';\nimport { TuiElement } from '@taiga-ui/cdk/directives/element';\nimport * as i1 from '@taiga-ui/core/components/data-list';\nimport { tuiInjectDataListSize, tuiAsDataListAccessor, TuiDataListComponent, TuiOption, TuiOptionWithValue, TuiDataList, TuiDataListDirective } from '@taiga-ui/core/components/data-list';\nimport { TuiLoader } from '@taiga-ui/core/components/loader';\nimport { PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiIsNativeFocused } from '@taiga-ui/cdk/utils/focus';\nimport { tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { TUI_ITEMS_HANDLERS as TUI_ITEMS_HANDLERS$1 } from '@taiga-ui/kit/tokens';\n\nclass TuiDataListWrapperComponent {\n    constructor() {\n        this.datalist = signal(null);\n        this.itemsHandlers = inject(TUI_ITEMS_HANDLERS);\n        // TODO(v5): delete\n        this.itemsHandlersLegacy = inject(TUI_ITEMS_HANDLERS$1);\n        // TODO(v5): delete\n        this.legacyOptionsQuery = EMPTY_QUERY;\n        // TODO(v5): delete\n        this.optionsQuery = EMPTY_QUERY;\n        this.newOptionMode = tuiInjectElement().hasAttribute('new');\n        this.items = [];\n        this.disabledItemHandler = this.newOptionMode\n            ? this.itemsHandlers?.disabledItemHandler()\n            : this.itemsHandlersLegacy.disabledItemHandler;\n        this.size = tuiInjectDataListSize();\n        this.itemClick = new EventEmitter();\n        this.options = computed(() => this.datalist()?.options() ?? []);\n        this.itemContent = ({ $implicit }) => this.newOptionMode\n            ? this.itemsHandlers.stringify()($implicit)\n            : this.itemsHandlersLegacy.stringify($implicit);\n    }\n    getContext($implicit, { nativeElement }) {\n        return { $implicit, active: tuiIsNativeFocused(nativeElement) };\n    }\n    // TODO(v5): delete\n    getOptions(includeDisabled = false) {\n        return [\n            ...this.legacyOptionsQuery,\n            ...this.optionsQuery,\n        ]\n            .filter(({ disabled }) => includeDisabled || !disabled)\n            .map(({ value }) => (isSignal(value) ? value() : value))\n            .filter(tuiIsPresent);\n    }\n    // TODO(v5): use signal `viewChild`\n    set datalistSetter(x) {\n        this.datalist.set(x);\n    }\n    $cast(items) {\n        return items;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDataListWrapperComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDataListWrapperComponent, isStandalone: true, selector: \"tui-data-list-wrapper:not([labels]), tui-data-list-wrapper:not([labels])[new]\", inputs: { items: \"items\", disabledItemHandler: \"disabledItemHandler\", emptyContent: \"emptyContent\", size: \"size\", itemContent: \"itemContent\" }, outputs: { itemClick: \"itemClick\" }, providers: [tuiAsDataListAccessor(TuiDataListWrapperComponent)], viewQueries: [{ propertyName: \"datalistSetter\", first: true, predicate: TuiDataListComponent, descendants: true }, { propertyName: \"legacyOptionsQuery\", predicate: i0.forwardRef(function () { return TuiOption; }), descendants: true }, { propertyName: \"optionsQuery\", predicate: i0.forwardRef(function () { return TuiOptionWithValue; }), descendants: true }], ngImport: i0, template: \"<tui-data-list\\n    *ngIf=\\\"items; else loading\\\"\\n    [emptyContent]=\\\"emptyContent\\\"\\n    [size]=\\\"size\\\"\\n>\\n    <ng-container *ngIf=\\\"newOptionMode; else legacyOptionFallback\\\">\\n        <button\\n            *ngFor=\\\"let item of $cast(items)\\\"\\n            #elementRef=\\\"elementRef\\\"\\n            new\\n            tuiElement\\n            tuiOption\\n            type=\\\"button\\\"\\n            [disabled]=\\\"disabledItemHandler(item)\\\"\\n            [value]=\\\"item\\\"\\n            (click.capture)=\\\"itemClick.emit(item)\\\"\\n        >\\n            <ng-container *polymorpheusOutlet=\\\"itemContent as text; context: getContext(item, elementRef)\\\">\\n                {{ text }}\\n            </ng-container>\\n        </button>\\n    </ng-container>\\n\\n    <!-- TODO(v5): delete fallback -->\\n    <ng-template #legacyOptionFallback>\\n        <button\\n            *ngFor=\\\"let item of $cast(items)\\\"\\n            #elementRef=\\\"elementRef\\\"\\n            automation-id=\\\"tui-data-list-wrapper__option\\\"\\n            tuiElement\\n            tuiOption\\n            type=\\\"button\\\"\\n            [disabled]=\\\"disabledItemHandler(item)\\\"\\n            [value]=\\\"item\\\"\\n            (click)=\\\"itemClick.emit(item)\\\"\\n        >\\n            <span class=\\\"t-content\\\">\\n                <ng-container *polymorpheusOutlet=\\\"itemContent as text; context: getContext(item, elementRef)\\\">\\n                    {{ text }}\\n                </ng-container>\\n            </span>\\n        </button>\\n    </ng-template>\\n</tui-data-list>\\n<ng-template #loading>\\n    <tui-loader\\n        automation-id=\\\"tui-data-list-wrapper__loader\\\"\\n        class=\\\"t-loader\\\"\\n    />\\n</ng-template>\\n\", styles: [\":host{display:block}.t-content{flex:1;min-inline-size:0}.t-loader{margin:.75rem 0}\\n\"], dependencies: [{ kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"component\", type: i1.TuiDataListComponent, selector: \"tui-data-list\", inputs: [\"emptyContent\", \"size\"] }, { kind: \"component\", type: i1.TuiOption, selector: \"button[tuiOption]:not([new]), a[tuiOption]:not([new]), label[tuiOption]:not([new])\", inputs: [\"disabled\", \"value\"] }, { kind: \"directive\", type: i1.TuiOptionNew, selector: \"button[tuiOption][new], a[tuiOption][new], label[tuiOption][new]\", inputs: [\"disabled\"] }, { kind: \"directive\", type: i1.TuiOptionWithValue, selector: \"button[tuiOption][value][new], a[tuiOption][value][new], label[tuiOption][value][new]\", inputs: [\"disabled\", \"value\"] }, { kind: \"directive\", type: TuiElement, selector: \"[tuiElement]\", exportAs: [\"elementRef\"] }, { kind: \"component\", type: TuiLoader, selector: \"tui-loader\", inputs: [\"size\", \"inheritColor\", \"overlay\", \"textContent\", \"showLoader\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDataListWrapperComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-data-list-wrapper:not([labels]), tui-data-list-wrapper:not([labels])[new]', imports: [NgForOf, NgIf, PolymorpheusOutlet, TuiDataList, TuiElement, TuiLoader], changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiAsDataListAccessor(TuiDataListWrapperComponent)], template: \"<tui-data-list\\n    *ngIf=\\\"items; else loading\\\"\\n    [emptyContent]=\\\"emptyContent\\\"\\n    [size]=\\\"size\\\"\\n>\\n    <ng-container *ngIf=\\\"newOptionMode; else legacyOptionFallback\\\">\\n        <button\\n            *ngFor=\\\"let item of $cast(items)\\\"\\n            #elementRef=\\\"elementRef\\\"\\n            new\\n            tuiElement\\n            tuiOption\\n            type=\\\"button\\\"\\n            [disabled]=\\\"disabledItemHandler(item)\\\"\\n            [value]=\\\"item\\\"\\n            (click.capture)=\\\"itemClick.emit(item)\\\"\\n        >\\n            <ng-container *polymorpheusOutlet=\\\"itemContent as text; context: getContext(item, elementRef)\\\">\\n                {{ text }}\\n            </ng-container>\\n        </button>\\n    </ng-container>\\n\\n    <!-- TODO(v5): delete fallback -->\\n    <ng-template #legacyOptionFallback>\\n        <button\\n            *ngFor=\\\"let item of $cast(items)\\\"\\n            #elementRef=\\\"elementRef\\\"\\n            automation-id=\\\"tui-data-list-wrapper__option\\\"\\n            tuiElement\\n            tuiOption\\n            type=\\\"button\\\"\\n            [disabled]=\\\"disabledItemHandler(item)\\\"\\n            [value]=\\\"item\\\"\\n            (click)=\\\"itemClick.emit(item)\\\"\\n        >\\n            <span class=\\\"t-content\\\">\\n                <ng-container *polymorpheusOutlet=\\\"itemContent as text; context: getContext(item, elementRef)\\\">\\n                    {{ text }}\\n                </ng-container>\\n            </span>\\n        </button>\\n    </ng-template>\\n</tui-data-list>\\n<ng-template #loading>\\n    <tui-loader\\n        automation-id=\\\"tui-data-list-wrapper__loader\\\"\\n        class=\\\"t-loader\\\"\\n    />\\n</ng-template>\\n\", styles: [\":host{display:block}.t-content{flex:1;min-inline-size:0}.t-loader{margin:.75rem 0}\\n\"] }]\n        }], propDecorators: { legacyOptionsQuery: [{\n                type: ViewChildren,\n                args: [forwardRef(() => TuiOption)]\n            }], optionsQuery: [{\n                type: ViewChildren,\n                args: [forwardRef(() => TuiOptionWithValue)]\n            }], items: [{\n                type: Input\n            }], disabledItemHandler: [{\n                type: Input\n            }], emptyContent: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], itemClick: [{\n                type: Output\n            }], itemContent: [{\n                type: Input\n            }], datalistSetter: [{\n                type: ViewChild,\n                args: [TuiDataListComponent]\n            }] } });\n\nclass TuiDataListGroupWrapperComponent extends TuiDataListWrapperComponent {\n    constructor() {\n        super(...arguments);\n        this.labels = [];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDataListGroupWrapperComponent, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDataListGroupWrapperComponent, isStandalone: true, selector: \"tui-data-list-wrapper[labels]\", inputs: { labels: \"labels\" }, providers: [tuiAsDataListAccessor(TuiDataListGroupWrapperComponent)], usesInheritance: true, ngImport: i0, template: \"<tui-data-list\\n    *ngIf=\\\"items; else loading\\\"\\n    [emptyContent]=\\\"emptyContent\\\"\\n    [size]=\\\"size\\\"\\n>\\n    <tui-opt-group\\n        *ngFor=\\\"let group of items; let index = index\\\"\\n        [label]=\\\"labels[index]\\\"\\n    >\\n        <ng-container *ngIf=\\\"newOptionMode; else legacyOptionFallback\\\">\\n            <button\\n                *ngFor=\\\"let item of group\\\"\\n                #elementRef=\\\"elementRef\\\"\\n                new\\n                tuiElement\\n                tuiOption\\n                type=\\\"button\\\"\\n                [disabled]=\\\"disabledItemHandler(item)\\\"\\n                [value]=\\\"item\\\"\\n                (click.capture)=\\\"itemClick.emit(item)\\\"\\n            >\\n                <ng-container *polymorpheusOutlet=\\\"itemContent as text; context: getContext(item, elementRef)\\\">\\n                    {{ text }}\\n                </ng-container>\\n            </button>\\n        </ng-container>\\n\\n        <!-- TODO(v5): delete fallback -->\\n        <ng-template #legacyOptionFallback>\\n            <button\\n                *ngFor=\\\"let item of group\\\"\\n                #elementRef=\\\"elementRef\\\"\\n                automation-id=\\\"tui-data-list-wrapper__option\\\"\\n                tuiElement\\n                tuiOption\\n                type=\\\"button\\\"\\n                [disabled]=\\\"disabledItemHandler(item)\\\"\\n                [value]=\\\"item\\\"\\n                (click)=\\\"itemClick.emit(item)\\\"\\n            >\\n                <ng-container *polymorpheusOutlet=\\\"itemContent as text; context: getContext(item, elementRef)\\\">\\n                    {{ text }}\\n                </ng-container>\\n            </button>\\n        </ng-template>\\n    </tui-opt-group>\\n</tui-data-list>\\n<ng-template #loading>\\n    <tui-loader class=\\\"t-loader\\\" />\\n</ng-template>\\n\", styles: [\":host{display:block}.t-content{flex:1;min-inline-size:0}.t-loader{margin:.75rem 0}\\n\"], dependencies: [{ kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"component\", type: i1.TuiDataListComponent, selector: \"tui-data-list\", inputs: [\"emptyContent\", \"size\"] }, { kind: \"component\", type: i1.TuiOption, selector: \"button[tuiOption]:not([new]), a[tuiOption]:not([new]), label[tuiOption]:not([new])\", inputs: [\"disabled\", \"value\"] }, { kind: \"directive\", type: i1.TuiOptionNew, selector: \"button[tuiOption][new], a[tuiOption][new], label[tuiOption][new]\", inputs: [\"disabled\"] }, { kind: \"directive\", type: i1.TuiOptionWithValue, selector: \"button[tuiOption][value][new], a[tuiOption][value][new], label[tuiOption][value][new]\", inputs: [\"disabled\", \"value\"] }, { kind: \"directive\", type: i1.TuiOptGroup, selector: \"tui-opt-group\", inputs: [\"label\"] }, { kind: \"directive\", type: TuiElement, selector: \"[tuiElement]\", exportAs: [\"elementRef\"] }, { kind: \"component\", type: TuiLoader, selector: \"tui-loader\", inputs: [\"size\", \"inheritColor\", \"overlay\", \"textContent\", \"showLoader\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDataListGroupWrapperComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-data-list-wrapper[labels]', imports: [NgForOf, NgIf, PolymorpheusOutlet, TuiDataList, TuiElement, TuiLoader], changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiAsDataListAccessor(TuiDataListGroupWrapperComponent)], template: \"<tui-data-list\\n    *ngIf=\\\"items; else loading\\\"\\n    [emptyContent]=\\\"emptyContent\\\"\\n    [size]=\\\"size\\\"\\n>\\n    <tui-opt-group\\n        *ngFor=\\\"let group of items; let index = index\\\"\\n        [label]=\\\"labels[index]\\\"\\n    >\\n        <ng-container *ngIf=\\\"newOptionMode; else legacyOptionFallback\\\">\\n            <button\\n                *ngFor=\\\"let item of group\\\"\\n                #elementRef=\\\"elementRef\\\"\\n                new\\n                tuiElement\\n                tuiOption\\n                type=\\\"button\\\"\\n                [disabled]=\\\"disabledItemHandler(item)\\\"\\n                [value]=\\\"item\\\"\\n                (click.capture)=\\\"itemClick.emit(item)\\\"\\n            >\\n                <ng-container *polymorpheusOutlet=\\\"itemContent as text; context: getContext(item, elementRef)\\\">\\n                    {{ text }}\\n                </ng-container>\\n            </button>\\n        </ng-container>\\n\\n        <!-- TODO(v5): delete fallback -->\\n        <ng-template #legacyOptionFallback>\\n            <button\\n                *ngFor=\\\"let item of group\\\"\\n                #elementRef=\\\"elementRef\\\"\\n                automation-id=\\\"tui-data-list-wrapper__option\\\"\\n                tuiElement\\n                tuiOption\\n                type=\\\"button\\\"\\n                [disabled]=\\\"disabledItemHandler(item)\\\"\\n                [value]=\\\"item\\\"\\n                (click)=\\\"itemClick.emit(item)\\\"\\n            >\\n                <ng-container *polymorpheusOutlet=\\\"itemContent as text; context: getContext(item, elementRef)\\\">\\n                    {{ text }}\\n                </ng-container>\\n            </button>\\n        </ng-template>\\n    </tui-opt-group>\\n</tui-data-list>\\n<ng-template #loading>\\n    <tui-loader class=\\\"t-loader\\\" />\\n</ng-template>\\n\", styles: [\":host{display:block}.t-content{flex:1;min-inline-size:0}.t-loader{margin:.75rem 0}\\n\"] }]\n        }], propDecorators: { labels: [{\n                type: Input\n            }] } });\n\nconst TuiDataListWrapper = [\n    TuiDataListWrapperComponent,\n    TuiDataListGroupWrapperComponent,\n    TuiDataListDirective,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiDataListGroupWrapperComponent, TuiDataListWrapper, TuiDataListWrapperComponent };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,IAAI,QAAQ,iBAAiB;AAC/C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,YAAY,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACxK,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,qBAAqB,EAAEC,qBAAqB,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,oBAAoB,QAAQ,qCAAqC;AAC1L,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,kBAAkB,QAAQ,2BAA2B;AAC9D,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASA,kBAAkB,IAAIC,oBAAoB,QAAQ,sBAAsB;AAAC,SAAAC,4FAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA4CmB/B,EAAE,CAAAiC,uBAAA,EAC63C,CAAC;IADh4CjC,EAAE,CAAAkC,MAAA,EACu6C,CAAC;IAD16ClC,EAAE,CAAAmC,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAAFrC,EAAE,CAAAsC,SAAA,CACu6C,CAAC;IAD16CtC,EAAE,CAAAuC,kBAAA,MAAAH,OAAA,KACu6C,CAAC;EAAA;AAAA;AAAA,SAAAI,6EAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAU,GAAA,GAD16CzC,EAAE,CAAA0C,gBAAA;IAAF1C,EAAE,CAAA2C,cAAA,kBAC8wC,CAAC;IADjxC3C,EAAE,CAAA4C,UAAA,2BAAAC,6GAAA;MAAA,MAAAC,OAAA,GAAF9C,EAAE,CAAA+C,aAAA,CAAAN,GAAA,EAAAO,SAAA;MAAA,MAAAC,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAC8uCF,MAAA,CAAAG,SAAA,CAAAC,IAAA,CAAAP,OAAmB,CAAC;IAAA,CAAC,CAAC;IADtwC9C,EAAE,CAAAsD,UAAA,IAAAxB,2FAAA,yBAC63C,CAAC;IADh4C9B,EAAE,CAAAuD,YAAA,CACy8C,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAe,OAAA,GAAAd,GAAA,CAAAgB,SAAA;IAAA,MAAAQ,aAAA,GAD58CxD,EAAE,CAAAyD,WAAA;IAAA,MAAAR,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA0D,UAAA,aAAAT,MAAA,CAAAU,mBAAA,CAAAb,OAAA,CAC+qC,CAAC,UAAAA,OAA6B,CAAC;IADhtC9C,EAAE,CAAAsC,SAAA,EAC40C,CAAC;IAD/0CtC,EAAE,CAAA0D,UAAA,uBAAAT,MAAA,CAAAW,WAC40C,CAAC,8BAAAX,MAAA,CAAAY,UAAA,CAAAf,OAAA,EAAAU,aAAA,CAA6C,CAAC;EAAA;AAAA;AAAA,SAAAM,oEAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD73C/B,EAAE,CAAAiC,uBAAA,EACk7B,CAAC;IADr7BjC,EAAE,CAAAsD,UAAA,IAAAd,4EAAA,mBAC8wC,CAAC;IADjxCxC,EAAE,CAAAmC,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAkB,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAsC,SAAA,CACk/B,CAAC;IADr/BtC,EAAE,CAAA0D,UAAA,YAAAT,MAAA,CAAAc,KAAA,CAAAd,MAAA,CAAAe,KAAA,CACk/B,CAAC;EAAA;AAAA;AAAA,SAAAC,2FAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADr/B/B,EAAE,CAAAiC,uBAAA,EAC4kE,CAAC;IAD/kEjC,EAAE,CAAAkC,MAAA,EAC8nE,CAAC;IADjoElC,EAAE,CAAAmC,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAmC,OAAA,GAAAlC,GAAA,CAAAK,kBAAA;IAAFrC,EAAE,CAAAsC,SAAA,CAC8nE,CAAC;IADjoEtC,EAAE,CAAAuC,kBAAA,MAAA2B,OAAA,KAC8nE,CAAC;EAAA;AAAA;AAAA,SAAAC,4EAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqC,GAAA,GADjoEpE,EAAE,CAAA0C,gBAAA;IAAF1C,EAAE,CAAA2C,cAAA,mBACi7D,CAAC;IADp7D3C,EAAE,CAAA4C,UAAA,mBAAAyB,oGAAA;MAAA,MAAAC,OAAA,GAAFtE,EAAE,CAAA+C,aAAA,CAAAqB,GAAA,EAAApB,SAAA;MAAA,MAAAC,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CACi5DF,MAAA,CAAAG,SAAA,CAAAC,IAAA,CAAAiB,OAAmB,CAAC;IAAA,CAAC,CAAC;IADz6DtE,EAAE,CAAA2C,cAAA,cACy9D,CAAC;IAD59D3C,EAAE,CAAAsD,UAAA,IAAAW,0FAAA,yBAC4kE,CAAC;IAD/kEjE,EAAE,CAAAuD,YAAA,CACkqE,CAAC,CAAkB,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAuC,OAAA,GAAAtC,GAAA,CAAAgB,SAAA;IAAA,MAAAuB,aAAA,GADxrEvE,EAAE,CAAAyD,WAAA;IAAA,MAAAR,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA0D,UAAA,aAAAT,MAAA,CAAAU,mBAAA,CAAAW,OAAA,CAC01D,CAAC,UAAAA,OAA6B,CAAC;IAD33DtE,EAAE,CAAAsC,SAAA,EAC2hE,CAAC;IAD9hEtC,EAAE,CAAA0D,UAAA,uBAAAT,MAAA,CAAAW,WAC2hE,CAAC,8BAAAX,MAAA,CAAAY,UAAA,CAAAS,OAAA,EAAAC,aAAA,CAA6C,CAAC;EAAA;AAAA;AAAA,SAAAC,mEAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD5kE/B,EAAE,CAAAsD,UAAA,IAAAa,2EAAA,mBACi7D,CAAC;EAAA;EAAA,IAAApC,EAAA;IAAA,MAAAkB,MAAA,GADp7DjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA0D,UAAA,YAAAT,MAAA,CAAAc,KAAA,CAAAd,MAAA,CAAAe,KAAA,CACinD,CAAC;EAAA;AAAA;AAAA,SAAAS,qDAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADpnD/B,EAAE,CAAA2C,cAAA,sBAC22B,CAAC;IAD92B3C,EAAE,CAAAsD,UAAA,IAAAQ,mEAAA,yBACk7B,CAAC,IAAAU,kEAAA,gCADr7BxE,EAAE,CAAA0E,sBACijD,CAAC;IADpjD1E,EAAE,CAAAuD,YAAA,CAC2tE,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAA4C,wBAAA,GAD9tE3E,EAAE,CAAAyD,WAAA;IAAA,MAAAR,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA0D,UAAA,iBAAAT,MAAA,CAAA2B,YACm1B,CAAC,SAAA3B,MAAA,CAAA4B,IAAoB,CAAC;IAD32B7E,EAAE,CAAAsC,SAAA,CACs5B,CAAC;IADz5BtC,EAAE,CAAA0D,UAAA,SAAAT,MAAA,CAAA6B,aACs5B,CAAC,aAAAH,wBAAwB,CAAC;EAAA;AAAA;AAAA,SAAAI,mDAAAhD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADl7B/B,EAAE,CAAAgF,SAAA,oBACi2E,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,iHAAAnD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADp2E/B,EAAE,CAAAiC,uBAAA,EAmCqhC,CAAC;IAnCxhCjC,EAAE,CAAAkC,MAAA,EAmCukC,CAAC;IAnC1kClC,EAAE,CAAAmC,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAAFrC,EAAE,CAAAsC,SAAA,CAmCukC,CAAC;IAnC1kCtC,EAAE,CAAAuC,kBAAA,MAAAH,OAAA,KAmCukC,CAAC;EAAA;AAAA;AAAA,SAAA+C,kGAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAU,GAAA,GAnC1kCzC,EAAE,CAAA0C,gBAAA;IAAF1C,EAAE,CAAA2C,cAAA,kBAmCk6B,CAAC;IAnCr6B3C,EAAE,CAAA4C,UAAA,2BAAAwC,kIAAA;MAAA,MAAAtC,OAAA,GAAF9C,EAAE,CAAA+C,aAAA,CAAAN,GAAA,EAAAO,SAAA;MAAA,MAAAC,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAmC83BF,MAAA,CAAAG,SAAA,CAAAC,IAAA,CAAAP,OAAmB,CAAC;IAAA,CAAC,CAAC;IAnCt5B9C,EAAE,CAAAsD,UAAA,IAAA4B,gHAAA,0BAmCqhC,CAAC;IAnCxhClF,EAAE,CAAAuD,YAAA,CAmC6mC,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAe,OAAA,GAAAd,GAAA,CAAAgB,SAAA;IAAA,MAAAQ,aAAA,GAnChnCxD,EAAE,CAAAyD,WAAA;IAAA,MAAAR,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA0D,UAAA,aAAAT,MAAA,CAAAU,mBAAA,CAAAb,OAAA,CAmCuzB,CAAC,UAAAA,OAAiC,CAAC;IAnC51B9C,EAAE,CAAAsC,SAAA,EAmCo+B,CAAC;IAnCv+BtC,EAAE,CAAA0D,UAAA,uBAAAT,MAAA,CAAAW,WAmCo+B,CAAC,8BAAAX,MAAA,CAAAY,UAAA,CAAAf,OAAA,EAAAU,aAAA,CAA6C,CAAC;EAAA;AAAA;AAAA,SAAA6B,yFAAAtD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnCrhC/B,EAAE,CAAAiC,uBAAA,EAmCiiB,CAAC;IAnCpiBjC,EAAE,CAAAsD,UAAA,IAAA6B,iGAAA,mBAmCk6B,CAAC;IAnCr6BnF,EAAE,CAAAmC,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAuD,QAAA,GAAFtF,EAAE,CAAAkD,aAAA,GAAAF,SAAA;IAAFhD,EAAE,CAAAsC,SAAA,CAmCkmB,CAAC;IAnCrmBtC,EAAE,CAAA0D,UAAA,YAAA4B,QAmCkmB,CAAC;EAAA;AAAA;AAAA,SAAAC,gHAAAxD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnCrmB/B,EAAE,CAAAiC,uBAAA,EAmCyvD,CAAC;IAnC5vDjC,EAAE,CAAAkC,MAAA,EAmC2yD,CAAC;IAnC9yDlC,EAAE,CAAAmC,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAyD,OAAA,GAAAxD,GAAA,CAAAK,kBAAA;IAAFrC,EAAE,CAAAsC,SAAA,CAmC2yD,CAAC;IAnC9yDtC,EAAE,CAAAuC,kBAAA,MAAAiD,OAAA,KAmC2yD,CAAC;EAAA;AAAA;AAAA,SAAAC,iGAAA1D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA2D,GAAA,GAnC9yD1F,EAAE,CAAA0C,gBAAA;IAAF1C,EAAE,CAAA2C,cAAA,mBAmCsoD,CAAC;IAnCzoD3C,EAAE,CAAA4C,UAAA,mBAAA+C,yHAAA;MAAA,MAAAC,OAAA,GAAF5F,EAAE,CAAA+C,aAAA,CAAA2C,GAAA,EAAA1C,SAAA;MAAA,MAAAC,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAmCkmDF,MAAA,CAAAG,SAAA,CAAAC,IAAA,CAAAuC,OAAmB,CAAC;IAAA,CAAC,CAAC;IAnC1nD5F,EAAE,CAAAsD,UAAA,IAAAiC,+GAAA,0BAmCyvD,CAAC;IAnC5vDvF,EAAE,CAAAuD,YAAA,CAmCi1D,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAA6D,OAAA,GAAA5D,GAAA,CAAAgB,SAAA;IAAA,MAAA6C,cAAA,GAnCp1D7F,EAAE,CAAAyD,WAAA;IAAA,MAAAR,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA0D,UAAA,aAAAT,MAAA,CAAAU,mBAAA,CAAAiC,OAAA,CAmCmiD,CAAC,UAAAA,OAAiC,CAAC;IAnCxkD5F,EAAE,CAAAsC,SAAA,EAmCwsD,CAAC;IAnC3sDtC,EAAE,CAAA0D,UAAA,uBAAAT,MAAA,CAAAW,WAmCwsD,CAAC,8BAAAX,MAAA,CAAAY,UAAA,CAAA+B,OAAA,EAAAC,cAAA,CAA6C,CAAC;EAAA;AAAA;AAAA,SAAAC,wFAAA/D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnCzvD/B,EAAE,CAAAsD,UAAA,IAAAmC,gGAAA,oBAmCsoD,CAAC;EAAA;EAAA,IAAA1D,EAAA;IAAA,MAAAuD,QAAA,GAnCzoDtF,EAAE,CAAAkD,aAAA,GAAAF,SAAA;IAAFhD,EAAE,CAAA0D,UAAA,YAAA4B,QAmCkyC,CAAC;EAAA;AAAA;AAAA,SAAAS,0EAAAhE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnCryC/B,EAAE,CAAA2C,cAAA,sBAmCsd,CAAC;IAnCzd3C,EAAE,CAAAsD,UAAA,IAAA+B,wFAAA,yBAmCiiB,CAAC,IAAAS,uFAAA,gCAnCpiB9F,EAAE,CAAA0E,sBAmCiuC,CAAC;IAnCpuC1E,EAAE,CAAAuD,YAAA,CAmC+3D,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAiE,SAAA,GAAAhE,GAAA,CAAAiE,KAAA;IAAA,MAAAC,wBAAA,GAnCl4DlG,EAAE,CAAAyD,WAAA;IAAA,MAAAR,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA0D,UAAA,UAAAT,MAAA,CAAAkD,MAAA,CAAAH,SAAA,CAmC+c,CAAC;IAnCldhG,EAAE,CAAAsC,SAAA,CAmCqgB,CAAC;IAnCxgBtC,EAAE,CAAA0D,UAAA,SAAAT,MAAA,CAAA6B,aAmCqgB,CAAC,aAAAoB,wBAAwB,CAAC;EAAA;AAAA;AAAA,SAAAE,0DAAArE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnCjiB/B,EAAE,CAAA2C,cAAA,sBAmC8V,CAAC;IAnCjW3C,EAAE,CAAAsD,UAAA,IAAAyC,yEAAA,0BAmCsd,CAAC;IAnCzd/F,EAAE,CAAAuD,YAAA,CAmCi5D,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAkB,MAAA,GAnCp5DjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA0D,UAAA,iBAAAT,MAAA,CAAA2B,YAmCsU,CAAC,SAAA3B,MAAA,CAAA4B,IAAoB,CAAC;IAnC9V7E,EAAE,CAAAsC,SAAA,CAmCyZ,CAAC;IAnC5ZtC,EAAE,CAAA0D,UAAA,YAAAT,MAAA,CAAAe,KAmCyZ,CAAC;EAAA;AAAA;AAAA,SAAAqC,wDAAAtE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnC5Z/B,EAAE,CAAAgF,SAAA,oBAmCg9D,CAAC;EAAA;AAAA;AAAA,MAAAsB,GAAA;AA7ExjE,MAAMC,2BAA2B,CAAC;EAC9BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAGxG,MAAM,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACyG,aAAa,GAAGxG,MAAM,CAAC0B,kBAAkB,CAAC;IAC/C;IACA,IAAI,CAAC+E,mBAAmB,GAAGzG,MAAM,CAAC2B,oBAAoB,CAAC;IACvD;IACA,IAAI,CAAC+E,kBAAkB,GAAGpF,WAAW;IACrC;IACA,IAAI,CAACqF,YAAY,GAAGrF,WAAW;IAC/B,IAAI,CAACsD,aAAa,GAAGrD,gBAAgB,CAAC,CAAC,CAACqF,YAAY,CAAC,KAAK,CAAC;IAC3D,IAAI,CAAC9C,KAAK,GAAG,EAAE;IACf,IAAI,CAACL,mBAAmB,GAAG,IAAI,CAACmB,aAAa,GACvC,IAAI,CAAC4B,aAAa,EAAE/C,mBAAmB,CAAC,CAAC,GACzC,IAAI,CAACgD,mBAAmB,CAAChD,mBAAmB;IAClD,IAAI,CAACkB,IAAI,GAAG9D,qBAAqB,CAAC,CAAC;IACnC,IAAI,CAACqC,SAAS,GAAG,IAAIjD,YAAY,CAAC,CAAC;IACnC,IAAI,CAAC4G,OAAO,GAAG3G,QAAQ,CAAC,MAAM,IAAI,CAACqG,QAAQ,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;IAC/D,IAAI,CAACnD,WAAW,GAAG,CAAC;MAAEZ;IAAU,CAAC,KAAK,IAAI,CAAC8B,aAAa,GAClD,IAAI,CAAC4B,aAAa,CAACM,SAAS,CAAC,CAAC,CAAChE,SAAS,CAAC,GACzC,IAAI,CAAC2D,mBAAmB,CAACK,SAAS,CAAChE,SAAS,CAAC;EACvD;EACAa,UAAUA,CAACb,SAAS,EAAE;IAAEiE;EAAc,CAAC,EAAE;IACrC,OAAO;MAAEjE,SAAS;MAAEkE,MAAM,EAAExF,kBAAkB,CAACuF,aAAa;IAAE,CAAC;EACnE;EACA;EACAE,UAAUA,CAACC,eAAe,GAAG,KAAK,EAAE;IAChC,OAAO,CACH,GAAG,IAAI,CAACR,kBAAkB,EAC1B,GAAG,IAAI,CAACC,YAAY,CACvB,CACIQ,MAAM,CAAC,CAAC;MAAEC;IAAS,CAAC,KAAKF,eAAe,IAAI,CAACE,QAAQ,CAAC,CACtDC,GAAG,CAAC,CAAC;MAAEC;IAAM,CAAC,KAAMnH,QAAQ,CAACmH,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,GAAGA,KAAM,CAAC,CACvDH,MAAM,CAAC1F,YAAY,CAAC;EAC7B;EACA;EACA,IAAI8F,cAAcA,CAACC,CAAC,EAAE;IAClB,IAAI,CAACjB,QAAQ,CAACkB,GAAG,CAACD,CAAC,CAAC;EACxB;EACA3D,KAAKA,CAACC,KAAK,EAAE;IACT,OAAOA,KAAK;EAChB;EACA;IAAS,IAAI,CAAC4D,IAAI,YAAAC,oCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFvB,2BAA2B;IAAA,CAAmD;EAAE;EAC3L;IAAS,IAAI,CAACwB,IAAI,kBAD+E/H,EAAE,CAAAgI,iBAAA;MAAAC,IAAA,EACJ1B,2BAA2B;MAAA2B,SAAA;MAAAC,SAAA,WAAAC,kCAAArG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADzB/B,EAAE,CAAAqI,WAAA,CACscpH,oBAAoB;UAD5djB,EAAE,CAAAqI,WAAA,CACqkBnH,SAAS;UADhlBlB,EAAE,CAAAqI,WAAA,CACurBlH,kBAAkB;QAAA;QAAA,IAAAY,EAAA;UAAA,IAAAuG,EAAA;UAD3sBtI,EAAE,CAAAuI,cAAA,CAAAD,EAAA,GAAFtI,EAAE,CAAAwI,WAAA,QAAAxG,GAAA,CAAAyF,cAAA,GAAAa,EAAA,CAAAG,KAAA;UAAFzI,EAAE,CAAAuI,cAAA,CAAAD,EAAA,GAAFtI,EAAE,CAAAwI,WAAA,QAAAxG,GAAA,CAAA4E,kBAAA,GAAA0B,EAAA;UAAFtI,EAAE,CAAAuI,cAAA,CAAAD,EAAA,GAAFtI,EAAE,CAAAwI,WAAA,QAAAxG,GAAA,CAAA6E,YAAA,GAAAyB,EAAA;QAAA;MAAA;MAAAI,MAAA;QAAA1E,KAAA;QAAAL,mBAAA;QAAAiB,YAAA;QAAAC,IAAA;QAAAjB,WAAA;MAAA;MAAA+E,OAAA;QAAAvF,SAAA;MAAA;MAAAwF,UAAA;MAAAC,QAAA,GAAF7I,EAAE,CAAA8I,kBAAA,CACwU,CAAC9H,qBAAqB,CAACuF,2BAA2B,CAAC,CAAC,GAD9XvG,EAAE,CAAA+I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAArH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/B,EAAE,CAAAsD,UAAA,IAAAmB,oDAAA,0BAC22B,CAAC,IAAAM,kDAAA,gCAD92B/E,EAAE,CAAA0E,sBACmvE,CAAC;QAAA;QAAA,IAAA3C,EAAA;UAAA,MAAAsH,WAAA,GADtvErJ,EAAE,CAAAyD,WAAA;UAAFzD,EAAE,CAAA0D,UAAA,SAAA1B,GAAA,CAAAgC,KACgyB,CAAC,aAAAqF,WAAW,CAAC;QAAA;MAAA;MAAAC,YAAA,GAAstDxJ,OAAO,EAAmHC,IAAI,EAA6FwB,kBAAkB,EAA8HT,EAAE,CAACG,oBAAoB,EAA4FH,EAAE,CAACI,SAAS,EAA8JJ,EAAE,CAACyI,YAAY,EAAmIzI,EAAE,CAACK,kBAAkB,EAAiKN,UAAU,EAAmFS,SAAS;MAAAkI,MAAA;MAAAC,eAAA;IAAA,EAA4J;EAAE;AAC9zH;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG1J,EAAE,CAAA2J,iBAAA,CAGXpD,2BAA2B,EAAc,CAAC;IAC1H0B,IAAI,EAAE3H,SAAS;IACfsJ,IAAI,EAAE,CAAC;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,QAAQ,EAAE,+EAA+E;MAAEC,OAAO,EAAE,CAAChK,OAAO,EAAEC,IAAI,EAAEwB,kBAAkB,EAAEH,WAAW,EAAEP,UAAU,EAAES,SAAS,CAAC;MAAEmI,eAAe,EAAElJ,uBAAuB,CAACwJ,MAAM;MAAEC,SAAS,EAAE,CAAChJ,qBAAqB,CAACuF,2BAA2B,CAAC,CAAC;MAAE4C,QAAQ,EAAE,wnDAAwnD;MAAEK,MAAM,EAAE,CAAC,sFAAsF;IAAE,CAAC;EACpiE,CAAC,CAAC,QAAkB;IAAE5C,kBAAkB,EAAE,CAAC;MACnCqB,IAAI,EAAEzH,YAAY;MAClBoJ,IAAI,EAAE,CAACnJ,UAAU,CAAC,MAAMS,SAAS,CAAC;IACtC,CAAC,CAAC;IAAE2F,YAAY,EAAE,CAAC;MACfoB,IAAI,EAAEzH,YAAY;MAClBoJ,IAAI,EAAE,CAACnJ,UAAU,CAAC,MAAMU,kBAAkB,CAAC;IAC/C,CAAC,CAAC;IAAE6C,KAAK,EAAE,CAAC;MACRiE,IAAI,EAAEvH;IACV,CAAC,CAAC;IAAEiD,mBAAmB,EAAE,CAAC;MACtBsE,IAAI,EAAEvH;IACV,CAAC,CAAC;IAAEkE,YAAY,EAAE,CAAC;MACfqD,IAAI,EAAEvH;IACV,CAAC,CAAC;IAAEmE,IAAI,EAAE,CAAC;MACPoD,IAAI,EAAEvH;IACV,CAAC,CAAC;IAAE0C,SAAS,EAAE,CAAC;MACZ6E,IAAI,EAAEtH;IACV,CAAC,CAAC;IAAEiD,WAAW,EAAE,CAAC;MACdqE,IAAI,EAAEvH;IACV,CAAC,CAAC;IAAE+G,cAAc,EAAE,CAAC;MACjBQ,IAAI,EAAErH,SAAS;MACfgJ,IAAI,EAAE,CAAC3I,oBAAoB;IAC/B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgJ,gCAAgC,SAAS1D,2BAA2B,CAAC;EACvEC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAG0D,SAAS,CAAC;IACnB,IAAI,CAAC/D,MAAM,GAAG,EAAE;EACpB;EACA;IAAS,IAAI,CAACyB,IAAI;MAAA,IAAAuC,6CAAA;MAAA,gBAAAC,yCAAAtC,CAAA;QAAA,QAAAqC,6CAAA,KAAAA,6CAAA,GAlC+EnK,EAAE,CAAAqK,qBAAA,CAkCQJ,gCAAgC,IAAAnC,CAAA,IAAhCmC,gCAAgC;MAAA;IAAA,IAAqD;EAAE;EAClM;IAAS,IAAI,CAAClC,IAAI,kBAnC+E/H,EAAE,CAAAgI,iBAAA;MAAAC,IAAA,EAmCJgC,gCAAgC;MAAA/B,SAAA;MAAAQ,MAAA;QAAAvC,MAAA;MAAA;MAAAyC,UAAA;MAAAC,QAAA,GAnC9B7I,EAAE,CAAA8I,kBAAA,CAmCsI,CAAC9H,qBAAqB,CAACiJ,gCAAgC,CAAC,CAAC,GAnCjMjK,EAAE,CAAAsK,0BAAA,EAAFtK,EAAE,CAAA+I,mBAAA;MAAAwB,KAAA,EAAAtF,GAAA;MAAA+D,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAqB,0CAAAzI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/B,EAAE,CAAAsD,UAAA,IAAA8C,yDAAA,0BAmC8V,CAAC,IAAAC,uDAAA,gCAnCjWrG,EAAE,CAAA0E,sBAmCy6D,CAAC;QAAA;QAAA,IAAA3C,EAAA;UAAA,MAAA0I,WAAA,GAnC56DzK,EAAE,CAAAyD,WAAA;UAAFzD,EAAE,CAAA0D,UAAA,SAAA1B,GAAA,CAAAgC,KAmCmR,CAAC,aAAAyG,WAAW,CAAC;QAAA;MAAA;MAAAnB,YAAA,GAAk1DxJ,OAAO,EAAmHC,IAAI,EAA6FwB,kBAAkB,EAA8HT,EAAE,CAACG,oBAAoB,EAA4FH,EAAE,CAACI,SAAS,EAA8JJ,EAAE,CAACyI,YAAY,EAAmIzI,EAAE,CAACK,kBAAkB,EAAiKL,EAAE,CAAC4J,WAAW,EAA6E7J,UAAU,EAAmFS,SAAS;MAAAkI,MAAA,GAAAlD,GAAA;MAAAmD,eAAA;IAAA,EAA4J;EAAE;AACxgH;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArCqG1J,EAAE,CAAA2J,iBAAA,CAqCXM,gCAAgC,EAAc,CAAC;IAC/HhC,IAAI,EAAE3H,SAAS;IACfsJ,IAAI,EAAE,CAAC;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,QAAQ,EAAE,+BAA+B;MAAEC,OAAO,EAAE,CAAChK,OAAO,EAAEC,IAAI,EAAEwB,kBAAkB,EAAEH,WAAW,EAAEP,UAAU,EAAES,SAAS,CAAC;MAAEmI,eAAe,EAAElJ,uBAAuB,CAACwJ,MAAM;MAAEC,SAAS,EAAE,CAAChJ,qBAAqB,CAACiJ,gCAAgC,CAAC,CAAC;MAAEd,QAAQ,EAAE,ovDAAovD;MAAEK,MAAM,EAAE,CAAC,sFAAsF;IAAE,CAAC;EACrnE,CAAC,CAAC,QAAkB;IAAErD,MAAM,EAAE,CAAC;MACvB8B,IAAI,EAAEvH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiK,kBAAkB,GAAG,CACvBpE,2BAA2B,EAC3B0D,gCAAgC,EAChC5I,oBAAoB,CACvB;;AAED;AACA;AACA;;AAEA,SAAS4I,gCAAgC,EAAEU,kBAAkB,EAAEpE,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}