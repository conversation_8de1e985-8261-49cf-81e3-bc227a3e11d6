{"ast": null, "code": "import { NgIf, NgForOf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Input, ViewEncapsulation, Directive } from '@angular/core';\nimport { tuiCreateToken, tuiProvideOptions, tuiIsString, tuiPure, tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearanceOptionsProvider, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport * as i2 from '@taiga-ui/core/directives/icons';\nimport { TuiIcons } from '@taiga-ui/core/directives/icons';\nimport { __decorate } from 'tslib';\nimport { TuiFade } from '@taiga-ui/kit/directives/fade';\nconst _c0 = [\"*\"];\nfunction TuiAvatar_img_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.value, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction TuiAvatar_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.value);\n  }\n}\nfunction TuiAvatarLabeled_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r1, \" \");\n  }\n}\nfunction TuiAvatarLabeled_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiAvatarLabeled_ng_container_1_span_1_Template, 2, 1, \"span\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.split(ctx_r1.label));\n  }\n}\nconst TUI_AVATAR_DEFAULT_OPTIONS = {\n  appearance: '',\n  round: true,\n  size: 'l'\n};\nconst TUI_AVATAR_OPTIONS = tuiCreateToken(TUI_AVATAR_DEFAULT_OPTIONS);\nfunction tuiAvatarOptionsProvider(options) {\n  return tuiProvideOptions(TUI_AVATAR_OPTIONS, options, TUI_AVATAR_DEFAULT_OPTIONS);\n}\nclass TuiAvatar {\n  constructor() {\n    this.options = inject(TUI_AVATAR_OPTIONS);\n    this.size = this.options.size;\n    this.round = this.options.round;\n  }\n  get value() {\n    return this.src || '';\n  }\n  get svg() {\n    return tuiIsString(this.value) && this.value.endsWith('.svg');\n  }\n  get type() {\n    if (this.value && !tuiIsString(this.value)) {\n      return 'img';\n    }\n    if (this.value.startsWith('@tui.')) {\n      return 'icon';\n    }\n    if (this.value.length > 0 && this.value.length < 3) {\n      return 'text';\n    }\n    return this.value.length ? 'img' : 'content';\n  }\n  static {\n    this.ɵfac = function TuiAvatar_Factory(t) {\n      return new (t || TuiAvatar)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiAvatar,\n      selectors: [[\"tui-avatar\"], [\"button\", \"tuiAvatar\", \"\"], [\"a\", \"tuiAvatar\", \"\"]],\n      hostVars: 6,\n      hostBindings: function TuiAvatar_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size)(\"data-type\", ctx.type);\n          i0.ɵɵclassProp(\"_round\", ctx.round)(\"_svg\", ctx.svg);\n        }\n      },\n      inputs: {\n        size: \"size\",\n        round: \"round\",\n        src: \"src\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAppearanceOptionsProvider(TUI_AVATAR_OPTIONS)]), i0.ɵɵHostDirectivesFeature([i1.TuiWithAppearance, {\n        directive: i2.TuiIcons,\n        inputs: [\"iconStart\", \"src\"]\n      }]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 2,\n      consts: [[\"alt\", \"\", \"loading\", \"lazy\", 3, \"src\", 4, \"ngIf\"], [4, \"ngIf\"], [\"alt\", \"\", \"loading\", \"lazy\", 3, \"src\"]],\n      template: function TuiAvatar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, TuiAvatar_img_0_Template, 1, 1, \"img\", 0)(1, TuiAvatar_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n          i0.ɵɵprojection(2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.type === \"img\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.type === \"text\");\n        }\n      },\n      dependencies: [NgIf],\n      styles: [\"[_nghost-%COMP%]{--t-size: 3.5rem;--t-radius: .75rem;position:relative;display:inline-flex;flex-shrink:0;inline-size:var(--t-size);block-size:var(--t-size);align-items:center;justify-content:center;white-space:nowrap;border-radius:var(--t-radius);border:none;background:var(--tui-background-neutral-1);color:var(--tui-text-secondary);vertical-align:middle;box-sizing:border-box;padding:.25rem;opacity:.999}[data-size=xs][_nghost-%COMP%]{--t-size: var(--tui-height-xs);--t-radius: .5rem;font:var(--tui-font-text-xs);font-weight:700}[data-size=xs][data-type=content][_nghost-%COMP%]{font:var(--tui-font-text-m);font-size:.5625rem}[data-size=s][_nghost-%COMP%]{--t-size: var(--tui-height-s);--t-radius: .5rem;font:var(--tui-font-text-s);font-weight:700}[data-size=s][data-type=content][_nghost-%COMP%]{font:var(--tui-font-text-xs);font-weight:700}[data-size=m][_nghost-%COMP%]{--t-size: calc(var(--tui-height-m) - .25rem);--t-radius: .75rem;font:var(--tui-font-text-l);font-weight:700}[data-size=m][data-type=content][_nghost-%COMP%]{font:var(--tui-font-text-m);font-weight:700}[data-size=l][_nghost-%COMP%]{--t-size: var(--tui-height-l);--t-radius: .75rem;font:var(--tui-font-heading-5)}[data-size=l][data-type=content][_nghost-%COMP%]{font:var(--tui-font-text-l);font-weight:700}[data-size=xl][_nghost-%COMP%]{--t-size: 5rem;--t-radius: .75rem;font:var(--tui-font-heading-3)}[data-size=xl][data-type=content][_nghost-%COMP%]{font:var(--tui-font-heading-4)}[data-size=xxl][_nghost-%COMP%]{--t-size: 6rem;--t-radius: 1rem;font:var(--tui-font-heading-3)}[data-size=xxl][data-type=content][_nghost-%COMP%]{font:var(--tui-font-heading-3)}[data-size=xxxl][_nghost-%COMP%]{--t-size: 8rem;--t-radius: 1.25rem;font:var(--tui-font-heading-2)}[data-size=xxxl][data-type=content][_nghost-%COMP%]{font:var(--tui-font-heading-3)}[data-type][_nghost-%COMP%]:before{display:none}[data-type=img][_nghost-%COMP%]:not(._svg){background:transparent}[data-type=icon][_nghost-%COMP%]:before{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);content:\\\"\\\";display:block;inline-size:60%;block-size:60%}._round[_nghost-%COMP%]{--t-radius: calc(var(--t-size) / 2)}._svg[_nghost-%COMP%]   img[_ngcontent-%COMP%]{padding:20%;object-fit:contain}[_nghost-%COMP%]     img, [_nghost-%COMP%]     picture, [_nghost-%COMP%]     video{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;object-fit:cover;box-sizing:border-box;border-radius:inherit}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAvatar, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-avatar,button[tuiAvatar],a[tuiAvatar]',\n      imports: [NgIf],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAppearanceOptionsProvider(TUI_AVATAR_OPTIONS)],\n      hostDirectives: [TuiWithAppearance, {\n        directive: TuiIcons,\n        inputs: ['iconStart: src']\n      }],\n      host: {\n        '[attr.data-size]': 'size',\n        '[attr.data-type]': 'type',\n        '[class._round]': 'round',\n        '[class._svg]': 'svg'\n      },\n      template: \"<img\\n    *ngIf=\\\"type === 'img'\\\"\\n    alt=\\\"\\\"\\n    loading=\\\"lazy\\\"\\n    [src]=\\\"value\\\"\\n/>\\n<ng-container *ngIf=\\\"type === 'text'\\\">{{ value }}</ng-container>\\n<ng-content />\\n\",\n      styles: [\":host{--t-size: 3.5rem;--t-radius: .75rem;position:relative;display:inline-flex;flex-shrink:0;inline-size:var(--t-size);block-size:var(--t-size);align-items:center;justify-content:center;white-space:nowrap;border-radius:var(--t-radius);border:none;background:var(--tui-background-neutral-1);color:var(--tui-text-secondary);vertical-align:middle;box-sizing:border-box;padding:.25rem;opacity:.999}:host[data-size=xs]{--t-size: var(--tui-height-xs);--t-radius: .5rem;font:var(--tui-font-text-xs);font-weight:700}:host[data-size=xs][data-type=content]{font:var(--tui-font-text-m);font-size:.5625rem}:host[data-size=s]{--t-size: var(--tui-height-s);--t-radius: .5rem;font:var(--tui-font-text-s);font-weight:700}:host[data-size=s][data-type=content]{font:var(--tui-font-text-xs);font-weight:700}:host[data-size=m]{--t-size: calc(var(--tui-height-m) - .25rem);--t-radius: .75rem;font:var(--tui-font-text-l);font-weight:700}:host[data-size=m][data-type=content]{font:var(--tui-font-text-m);font-weight:700}:host[data-size=l]{--t-size: var(--tui-height-l);--t-radius: .75rem;font:var(--tui-font-heading-5)}:host[data-size=l][data-type=content]{font:var(--tui-font-text-l);font-weight:700}:host[data-size=xl]{--t-size: 5rem;--t-radius: .75rem;font:var(--tui-font-heading-3)}:host[data-size=xl][data-type=content]{font:var(--tui-font-heading-4)}:host[data-size=xxl]{--t-size: 6rem;--t-radius: 1rem;font:var(--tui-font-heading-3)}:host[data-size=xxl][data-type=content]{font:var(--tui-font-heading-3)}:host[data-size=xxxl]{--t-size: 8rem;--t-radius: 1.25rem;font:var(--tui-font-heading-2)}:host[data-size=xxxl][data-type=content]{font:var(--tui-font-heading-3)}:host[data-type]:before{display:none}:host[data-type=img]:not(._svg){background:transparent}:host[data-type=icon]:before{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);content:\\\"\\\";display:block;inline-size:60%;block-size:60%}:host._round{--t-radius: calc(var(--t-size) / 2)}:host._svg img{padding:20%;object-fit:contain}:host ::ng-deep img,:host ::ng-deep picture,:host ::ng-deep video{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;object-fit:cover;box-sizing:border-box;border-radius:inherit}\\n\"]\n    }]\n  }], null, {\n    size: [{\n      type: Input\n    }],\n    round: [{\n      type: Input\n    }],\n    src: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiAvatarLabeled {\n  constructor() {\n    this.label = '';\n  }\n  split(label) {\n    return label.split(' ');\n  }\n  static {\n    this.ɵfac = function TuiAvatarLabeled_Factory(t) {\n      return new (t || TuiAvatarLabeled)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiAvatarLabeled,\n      selectors: [[\"tui-avatar-labeled\"]],\n      inputs: {\n        label: \"label\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 1,\n      consts: [[4, \"ngIf\"], [\"tuiFade\", \"\", 4, \"ngFor\", \"ngForOf\"], [\"tuiFade\", \"\"]],\n      template: function TuiAvatarLabeled_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵtemplate(1, TuiAvatarLabeled_ng_container_1_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.label.length);\n        }\n      },\n      dependencies: [NgForOf, NgIf, TuiFade],\n      styles: [\"tui-avatar-labeled{display:flex;inline-size:3.5rem;box-sizing:content-box;flex-direction:column;text-align:center;align-items:center;line-height:.895rem;font-size:.75rem;padding:0 .5rem;white-space:nowrap}tui-avatar-labeled tui-avatar{margin-bottom:.375rem}tui-avatar-labeled [tuiFade]{inline-size:calc(100% + 1rem)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([tuiPure], TuiAvatarLabeled.prototype, \"split\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAvatarLabeled, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-avatar-labeled',\n      imports: [NgForOf, NgIf, TuiFade],\n      template: `\n        <ng-content />\n        <ng-container *ngIf=\"label.length\">\n            <span\n                *ngFor=\"let item of split(label)\"\n                tuiFade\n            >\n                {{ item }}\n            </span>\n        </ng-container>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\"tui-avatar-labeled{display:flex;inline-size:3.5rem;box-sizing:content-box;flex-direction:column;text-align:center;align-items:center;line-height:.895rem;font-size:.75rem;padding:0 .5rem;white-space:nowrap}tui-avatar-labeled tui-avatar{margin-bottom:.375rem}tui-avatar-labeled [tuiFade]{inline-size:calc(100% + 1rem)}\\n\"]\n    }]\n  }], null, {\n    label: [{\n      type: Input\n    }],\n    split: []\n  });\n})();\nclass TuiAvatarOutlineStyles {\n  static {\n    this.ɵfac = function TuiAvatarOutlineStyles_Factory(t) {\n      return new (t || TuiAvatarOutlineStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiAvatarOutlineStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-avatar-outline\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiAvatarOutlineStyles_Template(rf, ctx) {},\n      styles: [\"[tuiAvatarOutline]{--t-outline: .1875rem;--t-gap: .125rem}[tuiAvatarOutline][data-size=xs],[tuiAvatarOutline][data-size=s],[tuiAvatarOutline][data-size=m]{--t-outline: .125rem;--t-gap: .0625rem}[tuiAvatarOutline]._outline{-webkit-mask:radial-gradient(closest-side,#000,#000 calc(100% - var(--t-gap) - var(--t-outline) - .5px),transparent calc(100% - var(--t-gap) - var(--t-outline)),transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)));mask:radial-gradient(closest-side,#000,#000 calc(100% - var(--t-gap) - var(--t-outline) - .5px),transparent calc(100% - var(--t-gap) - var(--t-outline)),transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)))}[tuiAvatarOutline]._outline:after{content:\\\"\\\";position:absolute;top:0;left:0;inline-size:100%;block-size:100%;background:var(--t-fill);-webkit-mask:radial-gradient(closest-side,transparent,transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)));mask:radial-gradient(closest-side,transparent,transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)))}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAvatarOutlineStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-avatar-outline'\n      },\n      styles: [\"[tuiAvatarOutline]{--t-outline: .1875rem;--t-gap: .125rem}[tuiAvatarOutline][data-size=xs],[tuiAvatarOutline][data-size=s],[tuiAvatarOutline][data-size=m]{--t-outline: .125rem;--t-gap: .0625rem}[tuiAvatarOutline]._outline{-webkit-mask:radial-gradient(closest-side,#000,#000 calc(100% - var(--t-gap) - var(--t-outline) - .5px),transparent calc(100% - var(--t-gap) - var(--t-outline)),transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)));mask:radial-gradient(closest-side,#000,#000 calc(100% - var(--t-gap) - var(--t-outline) - .5px),transparent calc(100% - var(--t-gap) - var(--t-outline)),transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)))}[tuiAvatarOutline]._outline:after{content:\\\"\\\";position:absolute;top:0;left:0;inline-size:100%;block-size:100%;background:var(--t-fill);-webkit-mask:radial-gradient(closest-side,transparent,transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)));mask:radial-gradient(closest-side,transparent,transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)))}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiAvatarOutline {\n  constructor() {\n    this.nothing = tuiWithStyles(TuiAvatarOutlineStyles);\n    this.tuiAvatarOutline = '';\n  }\n  get value() {\n    return this.tuiAvatarOutline === '' ? 'var(--tui-background-accent-1)' : this.tuiAvatarOutline;\n  }\n  static {\n    this.ɵfac = function TuiAvatarOutline_Factory(t) {\n      return new (t || TuiAvatarOutline)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiAvatarOutline,\n      selectors: [[\"\", \"tuiAvatarOutline\", \"\"]],\n      hostVars: 4,\n      hostBindings: function TuiAvatarOutline_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-fill\", ctx.value);\n          i0.ɵɵclassProp(\"_outline\", ctx.value);\n        }\n      },\n      inputs: {\n        tuiAvatarOutline: \"tuiAvatarOutline\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAvatarOutline, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiAvatarOutline]',\n      host: {\n        '[style.--t-fill]': 'value',\n        '[class._outline]': 'value'\n      }\n    }]\n  }], null, {\n    tuiAvatarOutline: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiAvatarStack {\n  constructor() {\n    this.direction = 'right';\n  }\n  static {\n    this.ɵfac = function TuiAvatarStack_Factory(t) {\n      return new (t || TuiAvatarStack)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiAvatarStack,\n      selectors: [[\"tui-avatar-stack\"]],\n      hostVars: 1,\n      hostBindings: function TuiAvatarStack_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-direction\", ctx.direction);\n        }\n      },\n      inputs: {\n        direction: \"direction\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function TuiAvatarStack_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\"tui-avatar-stack{display:flex;--t-gap: .125rem}tui-avatar-stack tui-avatar._round{-webkit-mask-image:radial-gradient(circle at var(--t-x) 50%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px));mask-image:radial-gradient(circle at var(--t-x) 50%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px))}tui-avatar-stack[data-direction=right] tui-avatar._round{--t-x: 100%}tui-avatar-stack[data-direction=right] tui-avatar._round:last-child{-webkit-mask-image:none;mask-image:none}tui-avatar-stack[data-direction=left] tui-avatar._round{--t-x: 0}tui-avatar-stack[data-direction=left] tui-avatar._round:first-child{-webkit-mask-image:none;mask-image:none}tui-avatar-stack[data-direction=left] tui-avatar:not(._round):not(:first-child){-webkit-mask-image:radial-gradient(circle at 0% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 0% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to right,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));mask-image:radial-gradient(circle at 0% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 0% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to right,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));-webkit-mask-position:calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(-1 * var(--t-gap)),calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(100% + var(--t-gap)),bottom;mask-position:calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(-1 * var(--t-gap)),calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(100% + var(--t-gap)),bottom}tui-avatar-stack[data-direction=right] tui-avatar:not(._round):not(:last-child){-webkit-mask-image:radial-gradient(circle at 150% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 150% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to left,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));mask-image:radial-gradient(circle at 150% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 150% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to left,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));-webkit-mask-position:calc(50% - var(--t-gap)) calc(-1 * var(--t-gap)),calc(50% - var(--t-gap)) calc(100% + var(--t-gap)),bottom;mask-position:calc(50% - var(--t-gap)) calc(-1 * var(--t-gap)),calc(50% - var(--t-gap)) calc(100% + var(--t-gap)),bottom}tui-avatar-stack tui-avatar:not(._round){-webkit-mask-size:calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),100%;mask-size:calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),100%;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}tui-avatar-stack tui-avatar:not(:last-child){margin-right:calc(var(--t-size) / -2)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAvatarStack, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-avatar-stack',\n      template: '<ng-content />',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[attr.data-direction]': 'direction'\n      },\n      styles: [\"tui-avatar-stack{display:flex;--t-gap: .125rem}tui-avatar-stack tui-avatar._round{-webkit-mask-image:radial-gradient(circle at var(--t-x) 50%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px));mask-image:radial-gradient(circle at var(--t-x) 50%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px))}tui-avatar-stack[data-direction=right] tui-avatar._round{--t-x: 100%}tui-avatar-stack[data-direction=right] tui-avatar._round:last-child{-webkit-mask-image:none;mask-image:none}tui-avatar-stack[data-direction=left] tui-avatar._round{--t-x: 0}tui-avatar-stack[data-direction=left] tui-avatar._round:first-child{-webkit-mask-image:none;mask-image:none}tui-avatar-stack[data-direction=left] tui-avatar:not(._round):not(:first-child){-webkit-mask-image:radial-gradient(circle at 0% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 0% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to right,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));mask-image:radial-gradient(circle at 0% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 0% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to right,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));-webkit-mask-position:calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(-1 * var(--t-gap)),calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(100% + var(--t-gap)),bottom;mask-position:calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(-1 * var(--t-gap)),calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(100% + var(--t-gap)),bottom}tui-avatar-stack[data-direction=right] tui-avatar:not(._round):not(:last-child){-webkit-mask-image:radial-gradient(circle at 150% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 150% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to left,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));mask-image:radial-gradient(circle at 150% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 150% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to left,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));-webkit-mask-position:calc(50% - var(--t-gap)) calc(-1 * var(--t-gap)),calc(50% - var(--t-gap)) calc(100% + var(--t-gap)),bottom;mask-position:calc(50% - var(--t-gap)) calc(-1 * var(--t-gap)),calc(50% - var(--t-gap)) calc(100% + var(--t-gap)),bottom}tui-avatar-stack tui-avatar:not(._round){-webkit-mask-size:calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),100%;mask-size:calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),100%;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}tui-avatar-stack tui-avatar:not(:last-child){margin-right:calc(var(--t-size) / -2)}\\n\"]\n    }]\n  }], null, {\n    direction: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_AVATAR_DEFAULT_OPTIONS, TUI_AVATAR_OPTIONS, TuiAvatar, TuiAvatarLabeled, TuiAvatarOutline, TuiAvatarStack, tuiAvatarOptionsProvider };", "map": {"version": 3, "names": ["NgIf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i0", "inject", "Component", "ChangeDetectionStrategy", "Input", "ViewEncapsulation", "Directive", "tuiCreateToken", "tuiProvideOptions", "tuiIsString", "tuiPure", "tuiWithStyles", "i1", "tuiAppearanceOptionsProvider", "TuiWithAppearance", "i2", "TuiIcons", "__decorate", "TuiFade", "_c0", "TuiAvatar_img_0_Template", "rf", "ctx", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "value", "ɵɵsanitizeUrl", "TuiAvatar_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵtextInterpolate", "TuiAvatarLabeled_ng_container_1_span_1_Template", "ɵɵelementStart", "ɵɵelementEnd", "item_r1", "$implicit", "ɵɵtextInterpolate1", "TuiAvatarLabeled_ng_container_1_Template", "ɵɵtemplate", "ctx_r1", "split", "label", "TUI_AVATAR_DEFAULT_OPTIONS", "appearance", "round", "size", "TUI_AVATAR_OPTIONS", "tuiAvatarOptionsProvider", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "src", "svg", "endsWith", "type", "startsWith", "length", "ɵfac", "TuiAvatar_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostVars", "hostBindings", "TuiAvatar_HostBindings", "ɵɵattribute", "ɵɵclassProp", "inputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "directive", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "TuiAvatar_Template", "ɵɵprojectionDef", "ɵɵprojection", "dependencies", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "hostDirectives", "host", "TuiAvat<PERSON><PERSON><PERSON><PERSON>", "TuiAvatarLabeled_Factory", "TuiAvatarLabeled_Template", "encapsulation", "prototype", "None", "TuiAvatarOutlineStyles", "TuiAvatarOutlineStyles_Factory", "hostAttrs", "TuiAvatarOutlineStyles_Template", "class", "TuiAvatarOutline", "nothing", "tuiAvatarOutline", "TuiAvatarOutline_Factory", "ɵdir", "ɵɵdefineDirective", "TuiAvatarOutline_HostBindings", "ɵɵstyleProp", "TuiAvatarStack", "direction", "TuiAvatarStack_Factory", "TuiAvatarStack_HostBindings", "TuiAvatarStack_Template"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-avatar.mjs"], "sourcesContent": ["import { NgI<PERSON>, NgForOf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Input, ViewEncapsulation, Directive } from '@angular/core';\nimport { tuiCreateToken, tuiProvideOptions, tuiIsString, tuiPure, tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearanceOptionsProvider, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport * as i2 from '@taiga-ui/core/directives/icons';\nimport { TuiIcons } from '@taiga-ui/core/directives/icons';\nimport { __decorate } from 'tslib';\nimport { TuiFade } from '@taiga-ui/kit/directives/fade';\n\nconst TUI_AVATAR_DEFAULT_OPTIONS = {\n    appearance: '',\n    round: true,\n    size: 'l',\n};\nconst TUI_AVATAR_OPTIONS = tuiCreateToken(TUI_AVATAR_DEFAULT_OPTIONS);\nfunction tuiAvatarOptionsProvider(options) {\n    return tuiProvideOptions(TUI_AVATAR_OPTIONS, options, TUI_AVATAR_DEFAULT_OPTIONS);\n}\n\nclass TuiAvatar {\n    constructor() {\n        this.options = inject(TUI_AVATAR_OPTIONS);\n        this.size = this.options.size;\n        this.round = this.options.round;\n    }\n    get value() {\n        return this.src || '';\n    }\n    get svg() {\n        return tuiIsString(this.value) && this.value.endsWith('.svg');\n    }\n    get type() {\n        if (this.value && !tuiIsString(this.value)) {\n            return 'img';\n        }\n        if (this.value.startsWith('@tui.')) {\n            return 'icon';\n        }\n        if (this.value.length > 0 && this.value.length < 3) {\n            return 'text';\n        }\n        return this.value.length ? 'img' : 'content';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAvatar, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAvatar, isStandalone: true, selector: \"tui-avatar,button[tuiAvatar],a[tuiAvatar]\", inputs: { size: \"size\", round: \"round\", src: \"src\" }, host: { properties: { \"attr.data-size\": \"size\", \"attr.data-type\": \"type\", \"class._round\": \"round\", \"class._svg\": \"svg\" } }, providers: [tuiAppearanceOptionsProvider(TUI_AVATAR_OPTIONS)], hostDirectives: [{ directive: i1.TuiWithAppearance }, { directive: i2.TuiIcons, inputs: [\"iconStart\", \"src\"] }], ngImport: i0, template: \"<img\\n    *ngIf=\\\"type === 'img'\\\"\\n    alt=\\\"\\\"\\n    loading=\\\"lazy\\\"\\n    [src]=\\\"value\\\"\\n/>\\n<ng-container *ngIf=\\\"type === 'text'\\\">{{ value }}</ng-container>\\n<ng-content />\\n\", styles: [\":host{--t-size: 3.5rem;--t-radius: .75rem;position:relative;display:inline-flex;flex-shrink:0;inline-size:var(--t-size);block-size:var(--t-size);align-items:center;justify-content:center;white-space:nowrap;border-radius:var(--t-radius);border:none;background:var(--tui-background-neutral-1);color:var(--tui-text-secondary);vertical-align:middle;box-sizing:border-box;padding:.25rem;opacity:.999}:host[data-size=xs]{--t-size: var(--tui-height-xs);--t-radius: .5rem;font:var(--tui-font-text-xs);font-weight:700}:host[data-size=xs][data-type=content]{font:var(--tui-font-text-m);font-size:.5625rem}:host[data-size=s]{--t-size: var(--tui-height-s);--t-radius: .5rem;font:var(--tui-font-text-s);font-weight:700}:host[data-size=s][data-type=content]{font:var(--tui-font-text-xs);font-weight:700}:host[data-size=m]{--t-size: calc(var(--tui-height-m) - .25rem);--t-radius: .75rem;font:var(--tui-font-text-l);font-weight:700}:host[data-size=m][data-type=content]{font:var(--tui-font-text-m);font-weight:700}:host[data-size=l]{--t-size: var(--tui-height-l);--t-radius: .75rem;font:var(--tui-font-heading-5)}:host[data-size=l][data-type=content]{font:var(--tui-font-text-l);font-weight:700}:host[data-size=xl]{--t-size: 5rem;--t-radius: .75rem;font:var(--tui-font-heading-3)}:host[data-size=xl][data-type=content]{font:var(--tui-font-heading-4)}:host[data-size=xxl]{--t-size: 6rem;--t-radius: 1rem;font:var(--tui-font-heading-3)}:host[data-size=xxl][data-type=content]{font:var(--tui-font-heading-3)}:host[data-size=xxxl]{--t-size: 8rem;--t-radius: 1.25rem;font:var(--tui-font-heading-2)}:host[data-size=xxxl][data-type=content]{font:var(--tui-font-heading-3)}:host[data-type]:before{display:none}:host[data-type=img]:not(._svg){background:transparent}:host[data-type=icon]:before{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);content:\\\"\\\";display:block;inline-size:60%;block-size:60%}:host._round{--t-radius: calc(var(--t-size) / 2)}:host._svg img{padding:20%;object-fit:contain}:host ::ng-deep img,:host ::ng-deep picture,:host ::ng-deep video{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;object-fit:cover;box-sizing:border-box;border-radius:inherit}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAvatar, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-avatar,button[tuiAvatar],a[tuiAvatar]', imports: [NgIf], changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiAppearanceOptionsProvider(TUI_AVATAR_OPTIONS)], hostDirectives: [\n                        TuiWithAppearance,\n                        {\n                            directive: TuiIcons,\n                            inputs: ['iconStart: src'],\n                        },\n                    ], host: {\n                        '[attr.data-size]': 'size',\n                        '[attr.data-type]': 'type',\n                        '[class._round]': 'round',\n                        '[class._svg]': 'svg',\n                    }, template: \"<img\\n    *ngIf=\\\"type === 'img'\\\"\\n    alt=\\\"\\\"\\n    loading=\\\"lazy\\\"\\n    [src]=\\\"value\\\"\\n/>\\n<ng-container *ngIf=\\\"type === 'text'\\\">{{ value }}</ng-container>\\n<ng-content />\\n\", styles: [\":host{--t-size: 3.5rem;--t-radius: .75rem;position:relative;display:inline-flex;flex-shrink:0;inline-size:var(--t-size);block-size:var(--t-size);align-items:center;justify-content:center;white-space:nowrap;border-radius:var(--t-radius);border:none;background:var(--tui-background-neutral-1);color:var(--tui-text-secondary);vertical-align:middle;box-sizing:border-box;padding:.25rem;opacity:.999}:host[data-size=xs]{--t-size: var(--tui-height-xs);--t-radius: .5rem;font:var(--tui-font-text-xs);font-weight:700}:host[data-size=xs][data-type=content]{font:var(--tui-font-text-m);font-size:.5625rem}:host[data-size=s]{--t-size: var(--tui-height-s);--t-radius: .5rem;font:var(--tui-font-text-s);font-weight:700}:host[data-size=s][data-type=content]{font:var(--tui-font-text-xs);font-weight:700}:host[data-size=m]{--t-size: calc(var(--tui-height-m) - .25rem);--t-radius: .75rem;font:var(--tui-font-text-l);font-weight:700}:host[data-size=m][data-type=content]{font:var(--tui-font-text-m);font-weight:700}:host[data-size=l]{--t-size: var(--tui-height-l);--t-radius: .75rem;font:var(--tui-font-heading-5)}:host[data-size=l][data-type=content]{font:var(--tui-font-text-l);font-weight:700}:host[data-size=xl]{--t-size: 5rem;--t-radius: .75rem;font:var(--tui-font-heading-3)}:host[data-size=xl][data-type=content]{font:var(--tui-font-heading-4)}:host[data-size=xxl]{--t-size: 6rem;--t-radius: 1rem;font:var(--tui-font-heading-3)}:host[data-size=xxl][data-type=content]{font:var(--tui-font-heading-3)}:host[data-size=xxxl]{--t-size: 8rem;--t-radius: 1.25rem;font:var(--tui-font-heading-2)}:host[data-size=xxxl][data-type=content]{font:var(--tui-font-heading-3)}:host[data-type]:before{display:none}:host[data-type=img]:not(._svg){background:transparent}:host[data-type=icon]:before{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);content:\\\"\\\";display:block;inline-size:60%;block-size:60%}:host._round{--t-radius: calc(var(--t-size) / 2)}:host._svg img{padding:20%;object-fit:contain}:host ::ng-deep img,:host ::ng-deep picture,:host ::ng-deep video{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;object-fit:cover;box-sizing:border-box;border-radius:inherit}\\n\"] }]\n        }], propDecorators: { size: [{\n                type: Input\n            }], round: [{\n                type: Input\n            }], src: [{\n                type: Input\n            }] } });\n\nclass TuiAvatarLabeled {\n    constructor() {\n        this.label = '';\n    }\n    split(label) {\n        return label.split(' ');\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAvatarLabeled, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAvatarLabeled, isStandalone: true, selector: \"tui-avatar-labeled\", inputs: { label: \"label\" }, ngImport: i0, template: `\n        <ng-content />\n        <ng-container *ngIf=\"label.length\">\n            <span\n                *ngFor=\"let item of split(label)\"\n                tuiFade\n            >\n                {{ item }}\n            </span>\n        </ng-container>\n    `, isInline: true, styles: [\"tui-avatar-labeled{display:flex;inline-size:3.5rem;box-sizing:content-box;flex-direction:column;text-align:center;align-items:center;line-height:.895rem;font-size:.75rem;padding:0 .5rem;white-space:nowrap}tui-avatar-labeled tui-avatar{margin-bottom:.375rem}tui-avatar-labeled [tuiFade]{inline-size:calc(100% + 1rem)}\\n\"], dependencies: [{ kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: TuiFade, selector: \"[tuiFade]\", inputs: [\"tuiFadeHeight\", \"tuiFadeSize\", \"tuiFadeOffset\", \"tuiFade\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    tuiPure\n], TuiAvatarLabeled.prototype, \"split\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAvatarLabeled, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-avatar-labeled', imports: [NgForOf, NgIf, TuiFade], template: `\n        <ng-content />\n        <ng-container *ngIf=\"label.length\">\n            <span\n                *ngFor=\"let item of split(label)\"\n                tuiFade\n            >\n                {{ item }}\n            </span>\n        </ng-container>\n    `, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\"tui-avatar-labeled{display:flex;inline-size:3.5rem;box-sizing:content-box;flex-direction:column;text-align:center;align-items:center;line-height:.895rem;font-size:.75rem;padding:0 .5rem;white-space:nowrap}tui-avatar-labeled tui-avatar{margin-bottom:.375rem}tui-avatar-labeled [tuiFade]{inline-size:calc(100% + 1rem)}\\n\"] }]\n        }], propDecorators: { label: [{\n                type: Input\n            }], split: [] } });\n\nclass TuiAvatarOutlineStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAvatarOutlineStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAvatarOutlineStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-avatar-outline\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiAvatarOutline]{--t-outline: .1875rem;--t-gap: .125rem}[tuiAvatarOutline][data-size=xs],[tuiAvatarOutline][data-size=s],[tuiAvatarOutline][data-size=m]{--t-outline: .125rem;--t-gap: .0625rem}[tuiAvatarOutline]._outline{-webkit-mask:radial-gradient(closest-side,#000,#000 calc(100% - var(--t-gap) - var(--t-outline) - .5px),transparent calc(100% - var(--t-gap) - var(--t-outline)),transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)));mask:radial-gradient(closest-side,#000,#000 calc(100% - var(--t-gap) - var(--t-outline) - .5px),transparent calc(100% - var(--t-gap) - var(--t-outline)),transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)))}[tuiAvatarOutline]._outline:after{content:\\\"\\\";position:absolute;top:0;left:0;inline-size:100%;block-size:100%;background:var(--t-fill);-webkit-mask:radial-gradient(closest-side,transparent,transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)));mask:radial-gradient(closest-side,transparent,transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)))}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAvatarOutlineStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-avatar-outline',\n                    }, styles: [\"[tuiAvatarOutline]{--t-outline: .1875rem;--t-gap: .125rem}[tuiAvatarOutline][data-size=xs],[tuiAvatarOutline][data-size=s],[tuiAvatarOutline][data-size=m]{--t-outline: .125rem;--t-gap: .0625rem}[tuiAvatarOutline]._outline{-webkit-mask:radial-gradient(closest-side,#000,#000 calc(100% - var(--t-gap) - var(--t-outline) - .5px),transparent calc(100% - var(--t-gap) - var(--t-outline)),transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)));mask:radial-gradient(closest-side,#000,#000 calc(100% - var(--t-gap) - var(--t-outline) - .5px),transparent calc(100% - var(--t-gap) - var(--t-outline)),transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)))}[tuiAvatarOutline]._outline:after{content:\\\"\\\";position:absolute;top:0;left:0;inline-size:100%;block-size:100%;background:var(--t-fill);-webkit-mask:radial-gradient(closest-side,transparent,transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)));mask:radial-gradient(closest-side,transparent,transparent calc(100% - var(--t-outline) - .5px),#000 calc(100% - var(--t-outline)))}\\n\"] }]\n        }] });\nclass TuiAvatarOutline {\n    constructor() {\n        this.nothing = tuiWithStyles(TuiAvatarOutlineStyles);\n        this.tuiAvatarOutline = '';\n    }\n    get value() {\n        return this.tuiAvatarOutline === ''\n            ? 'var(--tui-background-accent-1)'\n            : this.tuiAvatarOutline;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAvatarOutline, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAvatarOutline, isStandalone: true, selector: \"[tuiAvatarOutline]\", inputs: { tuiAvatarOutline: \"tuiAvatarOutline\" }, host: { properties: { \"style.--t-fill\": \"value\", \"class._outline\": \"value\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAvatarOutline, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiAvatarOutline]',\n                    host: {\n                        '[style.--t-fill]': 'value',\n                        '[class._outline]': 'value',\n                    },\n                }]\n        }], propDecorators: { tuiAvatarOutline: [{\n                type: Input\n            }] } });\n\nclass TuiAvatarStack {\n    constructor() {\n        this.direction = 'right';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAvatarStack, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAvatarStack, isStandalone: true, selector: \"tui-avatar-stack\", inputs: { direction: \"direction\" }, host: { properties: { \"attr.data-direction\": \"direction\" } }, ngImport: i0, template: '<ng-content />', isInline: true, styles: [\"tui-avatar-stack{display:flex;--t-gap: .125rem}tui-avatar-stack tui-avatar._round{-webkit-mask-image:radial-gradient(circle at var(--t-x) 50%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px));mask-image:radial-gradient(circle at var(--t-x) 50%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px))}tui-avatar-stack[data-direction=right] tui-avatar._round{--t-x: 100%}tui-avatar-stack[data-direction=right] tui-avatar._round:last-child{-webkit-mask-image:none;mask-image:none}tui-avatar-stack[data-direction=left] tui-avatar._round{--t-x: 0}tui-avatar-stack[data-direction=left] tui-avatar._round:first-child{-webkit-mask-image:none;mask-image:none}tui-avatar-stack[data-direction=left] tui-avatar:not(._round):not(:first-child){-webkit-mask-image:radial-gradient(circle at 0% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 0% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to right,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));mask-image:radial-gradient(circle at 0% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 0% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to right,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));-webkit-mask-position:calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(-1 * var(--t-gap)),calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(100% + var(--t-gap)),bottom;mask-position:calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(-1 * var(--t-gap)),calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(100% + var(--t-gap)),bottom}tui-avatar-stack[data-direction=right] tui-avatar:not(._round):not(:last-child){-webkit-mask-image:radial-gradient(circle at 150% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 150% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to left,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));mask-image:radial-gradient(circle at 150% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 150% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to left,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));-webkit-mask-position:calc(50% - var(--t-gap)) calc(-1 * var(--t-gap)),calc(50% - var(--t-gap)) calc(100% + var(--t-gap)),bottom;mask-position:calc(50% - var(--t-gap)) calc(-1 * var(--t-gap)),calc(50% - var(--t-gap)) calc(100% + var(--t-gap)),bottom}tui-avatar-stack tui-avatar:not(._round){-webkit-mask-size:calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),100%;mask-size:calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),100%;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}tui-avatar-stack tui-avatar:not(:last-child){margin-right:calc(var(--t-size) / -2)}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAvatarStack, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-avatar-stack', template: '<ng-content />', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[attr.data-direction]': 'direction',\n                    }, styles: [\"tui-avatar-stack{display:flex;--t-gap: .125rem}tui-avatar-stack tui-avatar._round{-webkit-mask-image:radial-gradient(circle at var(--t-x) 50%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px));mask-image:radial-gradient(circle at var(--t-x) 50%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px))}tui-avatar-stack[data-direction=right] tui-avatar._round{--t-x: 100%}tui-avatar-stack[data-direction=right] tui-avatar._round:last-child{-webkit-mask-image:none;mask-image:none}tui-avatar-stack[data-direction=left] tui-avatar._round{--t-x: 0}tui-avatar-stack[data-direction=left] tui-avatar._round:first-child{-webkit-mask-image:none;mask-image:none}tui-avatar-stack[data-direction=left] tui-avatar:not(._round):not(:first-child){-webkit-mask-image:radial-gradient(circle at 0% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 0% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to right,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));mask-image:radial-gradient(circle at 0% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 0% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to right,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));-webkit-mask-position:calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(-1 * var(--t-gap)),calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(100% + var(--t-gap)),bottom;mask-position:calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(-1 * var(--t-gap)),calc(50% - (var(--t-radius) - var(--t-gap)) / 2) calc(100% + var(--t-gap)),bottom}tui-avatar-stack[data-direction=right] tui-avatar:not(._round):not(:last-child){-webkit-mask-image:radial-gradient(circle at 150% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 150% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to left,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));mask-image:radial-gradient(circle at 150% 100%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),radial-gradient(circle at 150% 0%,transparent calc(var(--t-radius) + var(--t-gap)),#000 calc(var(--t-radius) + var(--t-gap) + .2px)),linear-gradient(to left,transparent calc(50% + var(--t-gap)),#000 calc(50% + var(--t-gap)));-webkit-mask-position:calc(50% - var(--t-gap)) calc(-1 * var(--t-gap)),calc(50% - var(--t-gap)) calc(100% + var(--t-gap)),bottom;mask-position:calc(50% - var(--t-gap)) calc(-1 * var(--t-gap)),calc(50% - var(--t-gap)) calc(100% + var(--t-gap)),bottom}tui-avatar-stack tui-avatar:not(._round){-webkit-mask-size:calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),100%;mask-size:calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),calc(var(--t-radius) + var(--t-gap)) calc(var(--t-radius) + var(--t-gap)),100%;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}tui-avatar-stack tui-avatar:not(:last-child){margin-right:calc(var(--t-size) / -2)}\\n\"] }]\n        }], propDecorators: { direction: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_AVATAR_DEFAULT_OPTIONS, TUI_AVATAR_OPTIONS, TuiAvatar, TuiAvatarLabeled, TuiAvatarOutline, TuiAvatarStack, tuiAvatarOptionsProvider };\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,OAAO,QAAQ,iBAAiB;AAC/C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,SAAS,QAAQ,eAAe;AAC/G,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,OAAO,EAAEC,aAAa,QAAQ,mCAAmC;AAC1H,OAAO,KAAKC,EAAE,MAAM,sCAAsC;AAC1D,SAASC,4BAA4B,EAAEC,iBAAiB,QAAQ,sCAAsC;AACtG,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,QAAQ,QAAQ,iCAAiC;AAC1D,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,OAAO,QAAQ,+BAA+B;AAAC,MAAAC,GAAA;AAAA,SAAAC,yBAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAoC6CrB,EAAE,CAAAuB,SAAA,YAC2iB,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAD9iBxB,EAAE,CAAAyB,aAAA;IAAFzB,EAAE,CAAA0B,UAAA,QAAAF,MAAA,CAAAG,KAAA,EAAF3B,EAAE,CAAA4B,aACuiB,CAAC;EAAA;AAAA;AAAA,SAAAC,kCAAAR,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD1iBrB,EAAE,CAAA8B,uBAAA,EACqlB,CAAC;IADxlB9B,EAAE,CAAA+B,MAAA,EACgmB,CAAC;IADnmB/B,EAAE,CAAAgC,qBAAA;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAG,MAAA,GAAFxB,EAAE,CAAAyB,aAAA;IAAFzB,EAAE,CAAAiC,SAAA,CACgmB,CAAC;IADnmBjC,EAAE,CAAAkC,iBAAA,CAAAV,MAAA,CAAAG,KACgmB,CAAC;EAAA;AAAA;AAAA,SAAAQ,gDAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADnmBrB,EAAE,CAAAoC,cAAA,aAuC3F,CAAC;IAvCwFpC,EAAE,CAAA+B,MAAA,EAyC5F,CAAC;IAzCyF/B,EAAE,CAAAqC,YAAA,CAyCrF,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAiB,OAAA,GAAAhB,GAAA,CAAAiB,SAAA;IAzCkFvC,EAAE,CAAAiC,SAAA,CAyC5F,CAAC;IAzCyFjC,EAAE,CAAAwC,kBAAA,MAAAF,OAAA,KAyC5F,CAAC;EAAA;AAAA;AAAA,SAAAG,yCAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzCyFrB,EAAE,CAAA8B,uBAAA,EAmC7D,CAAC;IAnC0D9B,EAAE,CAAA0C,UAAA,IAAAP,+CAAA,iBAuC3F,CAAC;IAvCwFnC,EAAE,CAAAgC,qBAAA;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAsB,MAAA,GAAF3C,EAAE,CAAAyB,aAAA;IAAFzB,EAAE,CAAAiC,SAAA,CAqCxD,CAAC;IArCqDjC,EAAE,CAAA0B,UAAA,YAAAiB,MAAA,CAAAC,KAAA,CAAAD,MAAA,CAAAE,KAAA,CAqCxD,CAAC;EAAA;AAAA;AAvEhD,MAAMC,0BAA0B,GAAG;EAC/BC,UAAU,EAAE,EAAE;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACV,CAAC;AACD,MAAMC,kBAAkB,GAAG3C,cAAc,CAACuC,0BAA0B,CAAC;AACrE,SAASK,wBAAwBA,CAACC,OAAO,EAAE;EACvC,OAAO5C,iBAAiB,CAAC0C,kBAAkB,EAAEE,OAAO,EAAEN,0BAA0B,CAAC;AACrF;AAEA,MAAMO,SAAS,CAAC;EACZC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,OAAO,GAAGnD,MAAM,CAACiD,kBAAkB,CAAC;IACzC,IAAI,CAACD,IAAI,GAAG,IAAI,CAACG,OAAO,CAACH,IAAI;IAC7B,IAAI,CAACD,KAAK,GAAG,IAAI,CAACI,OAAO,CAACJ,KAAK;EACnC;EACA,IAAIrB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC4B,GAAG,IAAI,EAAE;EACzB;EACA,IAAIC,GAAGA,CAAA,EAAG;IACN,OAAO/C,WAAW,CAAC,IAAI,CAACkB,KAAK,CAAC,IAAI,IAAI,CAACA,KAAK,CAAC8B,QAAQ,CAAC,MAAM,CAAC;EACjE;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC/B,KAAK,IAAI,CAAClB,WAAW,CAAC,IAAI,CAACkB,KAAK,CAAC,EAAE;MACxC,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACA,KAAK,CAACgC,UAAU,CAAC,OAAO,CAAC,EAAE;MAChC,OAAO,MAAM;IACjB;IACA,IAAI,IAAI,CAAChC,KAAK,CAACiC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACjC,KAAK,CAACiC,MAAM,GAAG,CAAC,EAAE;MAChD,OAAO,MAAM;IACjB;IACA,OAAO,IAAI,CAACjC,KAAK,CAACiC,MAAM,GAAG,KAAK,GAAG,SAAS;EAChD;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,kBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFV,SAAS;IAAA,CAAmD;EAAE;EACzK;IAAS,IAAI,CAACW,IAAI,kBAD+EhE,EAAE,CAAAiE,iBAAA;MAAAP,IAAA,EACJL,SAAS;MAAAa,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,uBAAAhD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADPrB,EAAE,CAAAsE,WAAA,cAAAhD,GAAA,CAAA2B,IAAA,eAAA3B,GAAA,CAAAoC,IAAA;UAAF1D,EAAE,CAAAuE,WAAA,WAAAjD,GAAA,CAAA0B,KACI,CAAC,SAAA1B,GAAA,CAAAkC,GAAD,CAAC;QAAA;MAAA;MAAAgB,MAAA;QAAAvB,IAAA;QAAAD,KAAA;QAAAO,GAAA;MAAA;MAAAkB,UAAA;MAAAC,QAAA,GADP1E,EAAE,CAAA2E,kBAAA,CAC+Q,CAAC9D,4BAA4B,CAACqC,kBAAkB,CAAC,CAAC,GADnUlD,EAAE,CAAA4E,uBAAA,EACiWhE,EAAE,CAACE,iBAAiB;QAAA+D,SAAA,EAAiB9D,EAAE,CAACC,QAAQ;QAAAwD,MAAA;MAAA,KADnZxE,EAAE,CAAA8E,mBAAA;MAAAC,kBAAA,EAAA5D,GAAA;MAAA6D,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mBAAA/D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrB,EAAE,CAAAqF,eAAA;UAAFrF,EAAE,CAAA0C,UAAA,IAAAtB,wBAAA,gBAC2iB,CAAC,IAAAS,iCAAA,yBAAyC,CAAC;UADxlB7B,EAAE,CAAAsF,YAAA,EAC+nB,CAAC;QAAA;QAAA,IAAAjE,EAAA;UADloBrB,EAAE,CAAA0B,UAAA,SAAAJ,GAAA,CAAAoC,IAAA,UAC4e,CAAC;UAD/e1D,EAAE,CAAAiC,SAAA,CACklB,CAAC;UADrlBjC,EAAE,CAAA0B,UAAA,SAAAJ,GAAA,CAAAoC,IAAA,WACklB,CAAC;QAAA;MAAA;MAAA6B,YAAA,GAAgvEzF,IAAI;MAAA0F,MAAA;MAAAC,eAAA;IAAA,EAAwH;EAAE;AACxiG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG1F,EAAE,CAAA2F,iBAAA,CAGXtC,SAAS,EAAc,CAAC;IACxGK,IAAI,EAAExD,SAAS;IACf0F,IAAI,EAAE,CAAC;MAAEnB,UAAU,EAAE,IAAI;MAAEoB,QAAQ,EAAE,2CAA2C;MAAEC,OAAO,EAAE,CAAChG,IAAI,CAAC;MAAE2F,eAAe,EAAEtF,uBAAuB,CAAC4F,MAAM;MAAEC,SAAS,EAAE,CAACnF,4BAA4B,CAACqC,kBAAkB,CAAC,CAAC;MAAE+C,cAAc,EAAE,CACvNnF,iBAAiB,EACjB;QACI+D,SAAS,EAAE7D,QAAQ;QACnBwD,MAAM,EAAE,CAAC,gBAAgB;MAC7B,CAAC,CACJ;MAAE0B,IAAI,EAAE;QACL,kBAAkB,EAAE,MAAM;QAC1B,kBAAkB,EAAE,MAAM;QAC1B,gBAAgB,EAAE,OAAO;QACzB,cAAc,EAAE;MACpB,CAAC;MAAEf,QAAQ,EAAE,uLAAuL;MAAEK,MAAM,EAAE,CAAC,woEAAwoE;IAAE,CAAC;EACt2E,CAAC,CAAC,QAAkB;IAAEvC,IAAI,EAAE,CAAC;MACrBS,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAE4C,KAAK,EAAE,CAAC;MACRU,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEmD,GAAG,EAAE,CAAC;MACNG,IAAI,EAAEtD;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+F,gBAAgB,CAAC;EACnB7C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACT,KAAK,GAAG,EAAE;EACnB;EACAD,KAAKA,CAACC,KAAK,EAAE;IACT,OAAOA,KAAK,CAACD,KAAK,CAAC,GAAG,CAAC;EAC3B;EACA;IAAS,IAAI,CAACiB,IAAI,YAAAuC,yBAAArC,CAAA;MAAA,YAAAA,CAAA,IAAyFoC,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACnC,IAAI,kBAjC+EhE,EAAE,CAAAiE,iBAAA;MAAAP,IAAA,EAiCJyC,gBAAgB;MAAAjC,SAAA;MAAAM,MAAA;QAAA3B,KAAA;MAAA;MAAA4B,UAAA;MAAAC,QAAA,GAjCd1E,EAAE,CAAA8E,mBAAA;MAAAC,kBAAA,EAAA5D,GAAA;MAAA6D,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAkB,0BAAAhF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrB,EAAE,CAAAqF,eAAA;UAAFrF,EAAE,CAAAsF,YAAA,EAkClF,CAAC;UAlC+EtF,EAAE,CAAA0C,UAAA,IAAAD,wCAAA,yBAmC7D,CAAC;QAAA;QAAA,IAAApB,EAAA;UAnC0DrB,EAAE,CAAAiC,SAAA,CAmC/D,CAAC;UAnC4DjC,EAAE,CAAA0B,UAAA,SAAAJ,GAAA,CAAAuB,KAAA,CAAAe,MAmC/D,CAAC;QAAA;MAAA;MAAA2B,YAAA,GAQoWxF,OAAO,EAAmHD,IAAI,EAA6FoB,OAAO;MAAAsE,MAAA;MAAAc,aAAA;MAAAb,eAAA;IAAA,EAAiM;EAAE;AAClzB;AACAxE,UAAU,CAAC,CACPP,OAAO,CACV,EAAEyF,gBAAgB,CAACI,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC;AAC7C;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KAhDqG1F,EAAE,CAAA2F,iBAAA,CAgDXQ,gBAAgB,EAAc,CAAC;IAC/GzC,IAAI,EAAExD,SAAS;IACf0F,IAAI,EAAE,CAAC;MAAEnB,UAAU,EAAE,IAAI;MAAEoB,QAAQ,EAAE,oBAAoB;MAAEC,OAAO,EAAE,CAAC/F,OAAO,EAAED,IAAI,EAAEoB,OAAO,CAAC;MAAEiE,QAAQ,EAAE;AACpH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEmB,aAAa,EAAEjG,iBAAiB,CAACmG,IAAI;MAAEf,eAAe,EAAEtF,uBAAuB,CAAC4F,MAAM;MAAEP,MAAM,EAAE,CAAC,gUAAgU;IAAE,CAAC;EACna,CAAC,CAAC,QAAkB;IAAE3C,KAAK,EAAE,CAAC;MACtBa,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEwC,KAAK,EAAE;EAAG,CAAC;AAAA;AAE3B,MAAM6D,sBAAsB,CAAC;EACzB;IAAS,IAAI,CAAC5C,IAAI,YAAA6C,+BAAA3C,CAAA;MAAA,YAAAA,CAAA,IAAyF0C,sBAAsB;IAAA,CAAmD;EAAE;EACtL;IAAS,IAAI,CAACzC,IAAI,kBAnE+EhE,EAAE,CAAAiE,iBAAA;MAAAP,IAAA,EAmEJ+C,sBAAsB;MAAAvC,SAAA;MAAAyC,SAAA;MAAAlC,UAAA;MAAAC,QAAA,GAnEpB1E,EAAE,CAAA8E,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAAyB,gCAAAvF,EAAA,EAAAC,GAAA;MAAAkE,MAAA;MAAAc,aAAA;MAAAb,eAAA;IAAA,EAmEq2C;EAAE;AAC98C;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArEqG1F,EAAE,CAAA2F,iBAAA,CAqEXc,sBAAsB,EAAc,CAAC;IACrH/C,IAAI,EAAExD,SAAS;IACf0F,IAAI,EAAE,CAAC;MAAEnB,UAAU,EAAE,IAAI;MAAEU,QAAQ,EAAE,EAAE;MAAEmB,aAAa,EAAEjG,iBAAiB,CAACmG,IAAI;MAAEf,eAAe,EAAEtF,uBAAuB,CAAC4F,MAAM;MAAEG,IAAI,EAAE;QAC3HW,KAAK,EAAE;MACX,CAAC;MAAErB,MAAM,EAAE,CAAC,4lCAA4lC;IAAE,CAAC;EACvnC,CAAC,CAAC;AAAA;AACV,MAAMsB,gBAAgB,CAAC;EACnBxD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyD,OAAO,GAAGpG,aAAa,CAAC8F,sBAAsB,CAAC;IACpD,IAAI,CAACO,gBAAgB,GAAG,EAAE;EAC9B;EACA,IAAIrF,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACqF,gBAAgB,KAAK,EAAE,GAC7B,gCAAgC,GAChC,IAAI,CAACA,gBAAgB;EAC/B;EACA;IAAS,IAAI,CAACnD,IAAI,YAAAoD,yBAAAlD,CAAA;MAAA,YAAAA,CAAA,IAAyF+C,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACI,IAAI,kBAtF+ElH,EAAE,CAAAmH,iBAAA;MAAAzD,IAAA,EAsFJoD,gBAAgB;MAAA5C,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAgD,8BAAA/F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtFdrB,EAAE,CAAAqH,WAAA,aAAA/F,GAAA,CAAAK,KAsFW,CAAC;UAtFd3B,EAAE,CAAAuE,WAAA,aAAAjD,GAAA,CAAAK,KAsFW,CAAC;QAAA;MAAA;MAAA6C,MAAA;QAAAwC,gBAAA;MAAA;MAAAvC,UAAA;IAAA,EAAuM;EAAE;AAC5T;AACA;EAAA,QAAAiB,SAAA,oBAAAA,SAAA,KAxFqG1F,EAAE,CAAA2F,iBAAA,CAwFXmB,gBAAgB,EAAc,CAAC;IAC/GpD,IAAI,EAAEpD,SAAS;IACfsF,IAAI,EAAE,CAAC;MACCnB,UAAU,EAAE,IAAI;MAChBoB,QAAQ,EAAE,oBAAoB;MAC9BK,IAAI,EAAE;QACF,kBAAkB,EAAE,OAAO;QAC3B,kBAAkB,EAAE;MACxB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEc,gBAAgB,EAAE,CAAC;MACjCtD,IAAI,EAAEtD;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkH,cAAc,CAAC;EACjBhE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiE,SAAS,GAAG,OAAO;EAC5B;EACA;IAAS,IAAI,CAAC1D,IAAI,YAAA2D,uBAAAzD,CAAA;MAAA,YAAAA,CAAA,IAAyFuD,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACtD,IAAI,kBA3G+EhE,EAAE,CAAAiE,iBAAA;MAAAP,IAAA,EA2GJ4D,cAAc;MAAApD,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAqD,4BAAApG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3GZrB,EAAE,CAAAsE,WAAA,mBAAAhD,GAAA,CAAAiG,SAAA;QAAA;MAAA;MAAA/C,MAAA;QAAA+C,SAAA;MAAA;MAAA9C,UAAA;MAAAC,QAAA,GAAF1E,EAAE,CAAA8E,mBAAA;MAAAC,kBAAA,EAAA5D,GAAA;MAAA6D,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAAuC,wBAAArG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrB,EAAE,CAAAqF,eAAA;UAAFrF,EAAE,CAAAsF,YAAA,EA2GsM,CAAC;QAAA;MAAA;MAAAE,MAAA;MAAAc,aAAA;MAAAb,eAAA;IAAA,EAAgjH;EAAE;AACh2H;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7GqG1F,EAAE,CAAA2F,iBAAA,CA6GX2B,cAAc,EAAc,CAAC;IAC7G5D,IAAI,EAAExD,SAAS;IACf0F,IAAI,EAAE,CAAC;MAAEnB,UAAU,EAAE,IAAI;MAAEoB,QAAQ,EAAE,kBAAkB;MAAEV,QAAQ,EAAE,gBAAgB;MAAEmB,aAAa,EAAEjG,iBAAiB,CAACmG,IAAI;MAAEf,eAAe,EAAEtF,uBAAuB,CAAC4F,MAAM;MAAEG,IAAI,EAAE;QACvK,uBAAuB,EAAE;MAC7B,CAAC;MAAEV,MAAM,EAAE,CAAC,k7GAAk7G;IAAE,CAAC;EAC78G,CAAC,CAAC,QAAkB;IAAE+B,SAAS,EAAE,CAAC;MAC1B7D,IAAI,EAAEtD;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAAS0C,0BAA0B,EAAEI,kBAAkB,EAAEG,SAAS,EAAE8C,gBAAgB,EAAEW,gBAAgB,EAAEQ,cAAc,EAAEnE,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}