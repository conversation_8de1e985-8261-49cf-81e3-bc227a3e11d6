{"ast": null, "code": "import { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Directive, ContentChild, ViewContainerRef, forwardRef, createComponent, EnvironmentInjector, INJECTOR, Input, signal, Component, ChangeDetectionStrategy, NgZone, DestroyRef, ChangeDetectorRef, isSignal, ViewEncapsulation, ContentChildren } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiQueryListChanges, tuiZonefree, tuiTakeUntilDestroyed } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiIsNativeFocused, tuiMoveFocus, tuiIsNativeFocusedIn } from '@taiga-ui/cdk/utils/focus';\nimport { tuiCreateToken, tuiProvide, tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiAsAuxiliary, TUI_NOTHING_FOUND_MESSAGE } from '@taiga-ui/core/tokens';\nimport { PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { ReplaySubject, switchMap, combineLatest, map, startWith, timer } from 'rxjs';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { TuiDropdownDirective } from '@taiga-ui/core/directives/dropdown';\nimport * as i1 from '@taiga-ui/core/directives/icons';\nimport { TuiWithIcons } from '@taiga-ui/core/directives/icons';\n\n/**\n * Accessor for data-list options\n */\nconst _c0 = [\"tuiOption\", \"\"];\nconst _c1 = [\"*\"];\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nfunction TuiOption_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r1, \" \");\n  }\n}\nfunction TuiOption_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction TuiDataListComponent_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r1, \" \");\n  }\n}\nfunction TuiDataListComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, TuiDataListComponent_div_1_ng_container_1_Template, 2, 1, \"ng-container\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r1.emptyContent || ctx_r1.fallback());\n  }\n}\nconst TUI_DATA_LIST_ACCESSOR = tuiCreateToken();\nfunction tuiAsDataListAccessor(accessor) {\n  return [tuiProvide(TUI_DATA_LIST_ACCESSOR, accessor), tuiAsAuxiliary(accessor)];\n}\n/**\n * DataList controller\n */\nconst TUI_DATA_LIST_HOST = tuiCreateToken();\nfunction tuiAsDataListHost(host) {\n  return tuiProvide(TUI_DATA_LIST_HOST, host);\n}\n\n/**\n * Content for tuiOption component\n */\nconst TUI_OPTION_CONTENT = tuiCreateToken();\nfunction tuiAsOptionContent(useValue) {\n  return {\n    provide: TUI_OPTION_CONTENT,\n    useValue\n  };\n}\nclass TuiWithOptionContent {\n  constructor() {\n    this.localContent = null;\n    this.globalContent = inject(TUI_OPTION_CONTENT, {\n      optional: true\n    });\n  }\n  get content() {\n    return this.globalContent ?? this.localContent;\n  }\n  static {\n    this.ɵfac = function TuiWithOptionContent_Factory(t) {\n      return new (t || TuiWithOptionContent)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiWithOptionContent,\n      contentQueries: function TuiWithOptionContent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TUI_OPTION_CONTENT, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.localContent = _t.first);\n        }\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiWithOptionContent, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], null, {\n    localContent: [{\n      type: ContentChild,\n      args: [TUI_OPTION_CONTENT, {\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n// TODO(v5): rename `TuiOptionNew` => `TuiOption` & remove [new] from selector\n// TODO: Consider all use cases for aria roles\nclass TuiOptionNew {\n  constructor() {\n    this.vcr = inject(ViewContainerRef);\n    this.isMobile = inject(TUI_IS_MOBILE);\n    this.el = tuiInjectElement();\n    this.dataList = inject(forwardRef(() => TuiDataListComponent), {\n      optional: true\n    });\n    this.content = inject(TUI_OPTION_CONTENT, {\n      optional: true\n    });\n    this.ref = this.content && createComponent(this.content, {\n      environmentInjector: inject(EnvironmentInjector),\n      elementInjector: inject(INJECTOR),\n      hostElement: tuiInjectElement()\n    });\n    this.dropdown = inject(TuiDropdownDirective, {\n      self: true,\n      optional: true\n    })?.ref;\n    this.disabled = false;\n    if (this.ref) {\n      this.vcr.insert(this.ref.hostView);\n      this.ref.changeDetectorRef.detectChanges();\n    }\n  }\n  // Preventing focus loss upon focused option removal\n  ngOnDestroy() {\n    this.dataList?.handleFocusLossIfNecessary(this.el);\n  }\n  onMouseMove() {\n    if (!this.isMobile && !tuiIsNativeFocused(this.el) && this.dataList && this.el.closest('[tuiDataListDropdownManager]')) {\n      this.el.focus({\n        preventScroll: true\n      });\n    }\n  }\n  static {\n    this.ɵfac = function TuiOptionNew_Factory(t) {\n      return new (t || TuiOptionNew)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiOptionNew,\n      selectors: [[\"button\", \"tuiOption\", \"\", \"new\", \"\"], [\"a\", \"tuiOption\", \"\", \"new\", \"\"], [\"label\", \"tuiOption\", \"\", \"new\", \"\"]],\n      hostAttrs: [\"type\", \"button\", \"role\", \"option\"],\n      hostVars: 3,\n      hostBindings: function TuiOptionNew_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mousemove.zoneless\", function TuiOptionNew_mousemove_zoneless_HostBindingHandler() {\n            return ctx.onMouseMove();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"disabled\", ctx.disabled || null);\n          i0.ɵɵclassProp(\"_with-dropdown\", ctx.dropdown == null ? null : ctx.dropdown());\n        }\n      },\n      inputs: {\n        disabled: \"disabled\"\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([i1.TuiWithIcons])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiOptionNew, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'button[tuiOption][new], a[tuiOption][new], label[tuiOption][new]',\n      hostDirectives: [TuiWithIcons],\n      host: {\n        type: 'button',\n        role: 'option',\n        '[attr.disabled]': 'disabled || null',\n        '[class._with-dropdown]': 'dropdown?.()',\n        '(mousemove.zoneless)': 'onMouseMove()'\n      }\n    }]\n  }], function () {\n    return [];\n  }, {\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\n// TODO(v5): remove [new] from selector\nclass TuiOptionWithValue {\n  constructor() {\n    this.host = inject(TUI_DATA_LIST_HOST, {\n      optional: true\n    });\n    this.disabled = false;\n    this.value = signal(undefined);\n  }\n  // TODO(v5): use `input.required<T>()` to remove `undefined` from `this.value()`\n  set valueSetter(x) {\n    this.value.set(x);\n  }\n  onClick(value = this.value()) {\n    if (this.host?.handleOption && value !== undefined) {\n      this.host.handleOption(value);\n    }\n  }\n  static {\n    this.ɵfac = function TuiOptionWithValue_Factory(t) {\n      return new (t || TuiOptionWithValue)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiOptionWithValue,\n      selectors: [[\"button\", \"tuiOption\", \"\", \"value\", \"\", \"new\", \"\"], [\"a\", \"tuiOption\", \"\", \"value\", \"\", \"new\", \"\"], [\"label\", \"tuiOption\", \"\", \"value\", \"\", \"new\", \"\"]],\n      hostBindings: function TuiOptionWithValue_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TuiOptionWithValue_click_HostBindingHandler() {\n            return ctx.onClick();\n          });\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        valueSetter: [i0.ɵɵInputFlags.None, \"value\", \"valueSetter\"]\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiOptionWithValue, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'button[tuiOption][value][new], a[tuiOption][value][new], label[tuiOption][value][new]',\n      host: {\n        '(click)': 'onClick()'\n      }\n    }]\n  }], null, {\n    disabled: [{\n      type: Input\n    }],\n    valueSetter: [{\n      type: Input,\n      args: [{\n        alias: 'value',\n        required: true\n      }]\n    }]\n  });\n})();\n\n/**\n * TODO(v5): delete\n * @deprecated use `<button tuiOption new />` / `<a tuiOption new /> / `<label tuiOption new /> instead\n */\nclass TuiOption {\n  constructor() {\n    this.isMobile = inject(TUI_IS_MOBILE);\n    this.el = tuiInjectElement();\n    this.dataList = inject(forwardRef(() => TuiDataListComponent), {\n      optional: true\n    });\n    this.host = inject(TUI_DATA_LIST_HOST, {\n      optional: true\n    });\n    this.content = inject(TUI_OPTION_CONTENT, {\n      optional: true\n    });\n    this.dropdown = inject(TuiDropdownDirective, {\n      self: true,\n      optional: true\n    })?.ref;\n    this.disabled = false;\n  }\n  // Preventing focus loss upon focused option removal\n  ngOnDestroy() {\n    this.dataList?.handleFocusLossIfNecessary(this.el);\n  }\n  onClick() {\n    if (this.host?.handleOption && this.value !== undefined) {\n      this.host.handleOption(this.value);\n    }\n  }\n  onMouseMove() {\n    if (!this.isMobile && !tuiIsNativeFocused(this.el) && this.dataList && this.el.closest('[tuiDataListDropdownManager]')) {\n      this.el.focus({\n        preventScroll: true\n      });\n    }\n  }\n  static {\n    this.ɵfac = function TuiOption_Factory(t) {\n      return new (t || TuiOption)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiOption,\n      selectors: [[\"button\", \"tuiOption\", \"\", 3, \"new\", \"\"], [\"a\", \"tuiOption\", \"\", 3, \"new\", \"\"], [\"label\", \"tuiOption\", \"\", 3, \"new\", \"\"]],\n      hostAttrs: [\"type\", \"button\", \"role\", \"option\"],\n      hostVars: 3,\n      hostBindings: function TuiOption_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TuiOption_click_HostBindingHandler() {\n            return ctx.onClick();\n          })(\"mousemove.zoneless\", function TuiOption_mousemove_zoneless_HostBindingHandler() {\n            return ctx.onMouseMove();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"disabled\", ctx.disabled || null);\n          i0.ɵɵclassProp(\"_with-dropdown\", ctx.dropdown == null ? null : ctx.dropdown());\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        value: \"value\"\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([i1.TuiWithIcons]), i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 3,\n      vars: 4,\n      consts: [[\"t\", \"\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"]],\n      template: function TuiOption_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, TuiOption_ng_container_0_Template, 2, 1, \"ng-container\", 1)(1, TuiOption_ng_template_1_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const t_r2 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.content || t_r2)(\"polymorpheusOutletContext\", i0.ɵɵpureFunction1(2, _c2, t_r2));\n        }\n      },\n      dependencies: [PolymorpheusOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiOption, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'button[tuiOption]:not([new]), a[tuiOption]:not([new]), label[tuiOption]:not([new])',\n      imports: [PolymorpheusOutlet],\n      template: `\n        <ng-container *polymorpheusOutlet=\"content || t as text; context: {$implicit: t}\">\n            {{ text }}\n        </ng-container>\n        <ng-template #t>\n            <ng-content />\n        </ng-template>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      hostDirectives: [TuiWithIcons],\n      host: {\n        type: 'button',\n        role: 'option',\n        '[attr.disabled]': 'disabled || null',\n        '[class._with-dropdown]': 'dropdown?.()',\n        '(click)': 'onClick()',\n        '(mousemove.zoneless)': 'onMouseMove()'\n      }\n    }]\n  }], null, {\n    disabled: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }]\n  });\n})();\nfunction tuiInjectDataListSize() {\n  const sizes = ['s', 'm', 'l'];\n  const size = inject(TUI_DATA_LIST_HOST, {\n    optional: true\n  })?.size;\n  return size && sizes.includes(size) ? size : 'l';\n}\n// TODO: Consider aria-activedescendant for proper accessibility implementation\nclass TuiDataListComponent {\n  constructor() {\n    // TODO(v5): delete\n    this.legacyOptionsQuery = EMPTY_QUERY;\n    this.optionsQuery = EMPTY_QUERY;\n    this.ngZone = inject(NgZone);\n    this.destroyRef = inject(DestroyRef);\n    this.el = tuiInjectElement();\n    this.cdr = inject(ChangeDetectorRef);\n    this.contentReady$ = new ReplaySubject(1);\n    this.fallback = toSignal(inject(TUI_NOTHING_FOUND_MESSAGE));\n    this.empty = signal(false);\n    this.size = tuiInjectDataListSize();\n    // TODO(v5): use signal `contentChildren`\n    this.options = toSignal(this.contentReady$.pipe(switchMap(() => combineLatest([tuiQueryListChanges(this.legacyOptionsQuery), tuiQueryListChanges(this.optionsQuery)])), map(([legacyOptions, options]) => [...legacyOptions.map(({\n      value\n    }) => value), ...options.map(({\n      value\n    }) => value())].filter(tuiIsPresent)), startWith([])), {\n      requireSync: true\n    });\n  }\n  onKeyDownArrow(current, step) {\n    const {\n      elements\n    } = this;\n    tuiMoveFocus(elements.indexOf(current), elements, step);\n  }\n  handleFocusLossIfNecessary(element = this.el) {\n    if (tuiIsNativeFocusedIn(element)) {\n      this.origin?.focus({\n        preventScroll: true\n      });\n    }\n  }\n  ngAfterContentInit() {\n    this.contentReady$.next(true);\n  }\n  // TODO: Refactor to :has after Safari support bumped to 15\n  ngAfterContentChecked() {\n    timer(0).pipe(tuiZonefree(this.ngZone), tuiTakeUntilDestroyed(this.destroyRef)).subscribe(() => {\n      this.empty.set(!this.elements.length);\n      this.cdr.detectChanges();\n    });\n  }\n  // TODO(v5): delete\n  getOptions(includeDisabled = false) {\n    return [...this.legacyOptionsQuery, ...this.optionsQuery].filter(({\n      disabled\n    }) => includeDisabled || !disabled).map(({\n      value\n    }) => isSignal(value) ? value() : value).filter(tuiIsPresent);\n  }\n  onFocusIn(relatedTarget, currentTarget) {\n    if (!currentTarget.contains(relatedTarget) && !this.origin) {\n      this.origin = relatedTarget;\n    }\n  }\n  get elements() {\n    return Array.from(this.el.querySelectorAll('[tuiOption]'));\n  }\n  static {\n    this.ɵfac = function TuiDataListComponent_Factory(t) {\n      return new (t || TuiDataListComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiDataListComponent,\n      selectors: [[\"tui-data-list\"]],\n      contentQueries: function TuiDataListComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiOption, 5);\n          i0.ɵɵcontentQuery(dirIndex, TuiOptionWithValue, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.legacyOptionsQuery = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionsQuery = _t);\n        }\n      },\n      hostAttrs: [\"role\", \"listbox\"],\n      hostVars: 1,\n      hostBindings: function TuiDataListComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focusin\", function TuiDataListComponent_focusin_HostBindingHandler($event) {\n            return ctx.onFocusIn($event.relatedTarget, $event.currentTarget);\n          })(\"mousedown.prevent\", function TuiDataListComponent_mousedown_prevent_HostBindingHandler() {\n            return 0;\n          })(\"wheel.zoneless.passive\", function TuiDataListComponent_wheel_zoneless_passive_HostBindingHandler() {\n            return ctx.handleFocusLossIfNecessary();\n          })(\"mouseleave\", function TuiDataListComponent_mouseleave_HostBindingHandler($event) {\n            return ctx.handleFocusLossIfNecessary($event.target);\n          })(\"keydown.tab\", function TuiDataListComponent_keydown_tab_HostBindingHandler() {\n            return ctx.handleFocusLossIfNecessary();\n          })(\"keydown.shift.tab\", function TuiDataListComponent_keydown_shift_tab_HostBindingHandler() {\n            return ctx.handleFocusLossIfNecessary();\n          })(\"keydown.arrowDown.prevent\", function TuiDataListComponent_keydown_arrowDown_prevent_HostBindingHandler($event) {\n            return ctx.onKeyDownArrow($event.target, 1);\n          })(\"keydown.arrowUp.prevent\", function TuiDataListComponent_keydown_arrowUp_prevent_HostBindingHandler($event) {\n            return ctx.onKeyDownArrow($event.target, -1);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n        }\n      },\n      inputs: {\n        emptyContent: \"emptyContent\",\n        size: \"size\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsDataListAccessor(TuiDataListComponent), {\n        provide: TUI_OPTION_CONTENT,\n        useFactory: () => inject(TuiWithOptionContent, {\n          optional: true\n        })?.content ?? inject(TUI_OPTION_CONTENT, {\n          skipSelf: true,\n          optional: true\n        })\n      }]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 2,\n      vars: 1,\n      consts: [[\"class\", \"t-empty\", 4, \"ngIf\"], [1, \"t-empty\"], [4, \"polymorpheusOutlet\"]],\n      template: function TuiDataListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵtemplate(1, TuiDataListComponent_div_1_Template, 2, 1, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.empty());\n        }\n      },\n      dependencies: [NgIf, PolymorpheusOutlet],\n      styles: [\"tui-data-list{--tui-data-list-padding: .25rem;--tui-data-list-margin: .0625rem;display:flex;font:var(--tui-font-text-m);flex-direction:column;padding:calc(var(--tui-data-list-padding) - var(--tui-data-list-margin)) var(--tui-data-list-padding);color:var(--tui-text-tertiary)}tui-data-list:focus-within .t-trap{display:none}tui-data-list:focus-within [tuiOption]._with-dropdown:not(:focus){background-color:transparent}tui-data-list[data-size=s]{--tui-data-list-margin: 0rem}tui-data-list[data-size=s] [tuiOption][new]:not([tuiCell]){gap:.5rem}tui-data-list[data-size=s]>.t-empty,tui-data-list[data-size=s] [tuiOption]{--t-option-padding-inline: .5rem;font:var(--tui-font-text-s);min-block-size:2rem;padding-top:.3125rem;padding-bottom:.3125rem}tui-data-list[data-size=s]>.t-empty:before,tui-data-list[data-size=s] [tuiOption]:before{font-size:1rem}tui-data-list[data-size=m] [tuiOption][new]:not([tuiCell]){gap:.75rem}tui-data-list[data-size=m]>.t-empty,tui-data-list[data-size=m] [tuiOption]{--t-option-padding-inline: .5rem;font:var(--tui-font-text-s);min-block-size:2.5rem;padding-top:.375rem;padding-bottom:.375rem}tui-data-list[data-size=l]{--tui-data-list-padding: .375rem;--tui-data-list-margin: .125rem}tui-data-list[data-size=l] [tuiOption][new]:not([tuiCell]){gap:1rem}tui-data-list[data-size=l]>.t-empty,tui-data-list[data-size=l] [tuiOption]{--t-option-padding-inline: .625rem;font:var(--tui-font-text-m);min-block-size:2.75rem;padding-top:.375rem;padding-bottom:.375rem}tui-data-list>.t-empty{display:flex;align-items:center;box-sizing:border-box;margin:var(--tui-data-list-margin) 0}tui-data-list [tuiOption]:not([new]){justify-content:space-between}tui-data-list [tuiOption]{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:flex;align-items:center;box-sizing:border-box;margin:var(--tui-data-list-margin) 0;text-align:start;color:var(--tui-text-primary);border-radius:var(--tui-radius-s);outline:none;cursor:pointer;background-clip:padding-box}tui-data-list [tuiOption]:disabled{opacity:var(--tui-disabled-opacity);cursor:default}@media (hover: hover) and (pointer: fine){tui-data-list [tuiOption]:hover:not(:disabled){background-color:var(--tui-background-neutral-1)}}tui-data-list [tuiOption]:active:not(:disabled),tui-data-list [tuiOption]:focus-within,tui-data-list [tuiOption]._with-dropdown{background-color:var(--tui-background-neutral-1)}tui-data-list [tuiOption]:not([new]):before{margin-inline-end:.5rem}tui-data-list [tuiOption]:after{font-size:1rem;margin:0 -.75rem 0 auto;border-left:.5rem solid;border-right:.5rem solid}tui-data-list>.t-empty,tui-data-list [tuiOption]{padding-left:var(--t-option-padding-inline);padding-right:var(--t-option-padding-inline)}tui-opt-group{position:relative;display:flex;font:var(--tui-font-text-xs);font-weight:700;color:var(--tui-text-primary);flex-direction:column;line-height:1rem}tui-data-list[data-size=l] tui-opt-group{font:var(--tui-font-text-m);font-weight:700;line-height:1.25rem}tui-data-list[data-size=l] tui-opt-group:before{padding-left:.625rem;padding-right:.625rem}tui-data-list[data-size=l] tui-opt-group:after{left:.625rem;right:.625rem}tui-opt-group:empty:before,tui-opt-group:empty:after{display:none}tui-opt-group:before{content:attr(data-label);padding:var(--tui-data-list-padding) .5rem var(--tui-data-list-padding);margin:var(--tui-data-list-margin) 0;white-space:normal;word-break:break-word}tui-opt-group:after{position:absolute;left:.5rem;right:.5rem;top:var(--tui-data-list-padding);block-size:1px;background:var(--tui-border-normal)}tui-opt-group:not(:empty)~tui-opt-group:before{padding-top:calc(.75rem + var(--tui-data-list-padding))}tui-opt-group:not(:empty)~tui-opt-group[data-label=\\\"\\\"]:before,tui-opt-group:not(:empty)~tui-opt-group:not([data-label]):before{padding:var(--tui-data-list-padding) 0}tui-opt-group:not(:empty)~tui-opt-group:after{content:\\\"\\\"}tui-opt-group[data-label=\\\"\\\"]:before,tui-opt-group:not([data-label]):before{content:\\\"\\\";padding:0;margin:0}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDataListComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-data-list',\n      imports: [NgIf, PolymorpheusOutlet],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAsDataListAccessor(TuiDataListComponent), {\n        provide: TUI_OPTION_CONTENT,\n        useFactory: () => inject(TuiWithOptionContent, {\n          optional: true\n        })?.content ?? inject(TUI_OPTION_CONTENT, {\n          skipSelf: true,\n          optional: true\n        })\n      }],\n      host: {\n        role: 'listbox',\n        '[attr.data-size]': 'size',\n        '(focusin)': 'onFocusIn($event.relatedTarget, $event.currentTarget)',\n        '(mousedown.prevent)': '(0)',\n        '(wheel.zoneless.passive)': 'handleFocusLossIfNecessary()',\n        '(mouseleave)': 'handleFocusLossIfNecessary($event.target)',\n        '(keydown.tab)': 'handleFocusLossIfNecessary()',\n        '(keydown.shift.tab)': 'handleFocusLossIfNecessary()',\n        '(keydown.arrowDown.prevent)': 'onKeyDownArrow($event.target, 1)',\n        '(keydown.arrowUp.prevent)': 'onKeyDownArrow($event.target, -1)'\n      },\n      template: \"<ng-content />\\n<div\\n    *ngIf=\\\"empty()\\\"\\n    class=\\\"t-empty\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"emptyContent || fallback() as text\\\">\\n        {{ text }}\\n    </ng-container>\\n</div>\\n\",\n      styles: [\"tui-data-list{--tui-data-list-padding: .25rem;--tui-data-list-margin: .0625rem;display:flex;font:var(--tui-font-text-m);flex-direction:column;padding:calc(var(--tui-data-list-padding) - var(--tui-data-list-margin)) var(--tui-data-list-padding);color:var(--tui-text-tertiary)}tui-data-list:focus-within .t-trap{display:none}tui-data-list:focus-within [tuiOption]._with-dropdown:not(:focus){background-color:transparent}tui-data-list[data-size=s]{--tui-data-list-margin: 0rem}tui-data-list[data-size=s] [tuiOption][new]:not([tuiCell]){gap:.5rem}tui-data-list[data-size=s]>.t-empty,tui-data-list[data-size=s] [tuiOption]{--t-option-padding-inline: .5rem;font:var(--tui-font-text-s);min-block-size:2rem;padding-top:.3125rem;padding-bottom:.3125rem}tui-data-list[data-size=s]>.t-empty:before,tui-data-list[data-size=s] [tuiOption]:before{font-size:1rem}tui-data-list[data-size=m] [tuiOption][new]:not([tuiCell]){gap:.75rem}tui-data-list[data-size=m]>.t-empty,tui-data-list[data-size=m] [tuiOption]{--t-option-padding-inline: .5rem;font:var(--tui-font-text-s);min-block-size:2.5rem;padding-top:.375rem;padding-bottom:.375rem}tui-data-list[data-size=l]{--tui-data-list-padding: .375rem;--tui-data-list-margin: .125rem}tui-data-list[data-size=l] [tuiOption][new]:not([tuiCell]){gap:1rem}tui-data-list[data-size=l]>.t-empty,tui-data-list[data-size=l] [tuiOption]{--t-option-padding-inline: .625rem;font:var(--tui-font-text-m);min-block-size:2.75rem;padding-top:.375rem;padding-bottom:.375rem}tui-data-list>.t-empty{display:flex;align-items:center;box-sizing:border-box;margin:var(--tui-data-list-margin) 0}tui-data-list [tuiOption]:not([new]){justify-content:space-between}tui-data-list [tuiOption]{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:flex;align-items:center;box-sizing:border-box;margin:var(--tui-data-list-margin) 0;text-align:start;color:var(--tui-text-primary);border-radius:var(--tui-radius-s);outline:none;cursor:pointer;background-clip:padding-box}tui-data-list [tuiOption]:disabled{opacity:var(--tui-disabled-opacity);cursor:default}@media (hover: hover) and (pointer: fine){tui-data-list [tuiOption]:hover:not(:disabled){background-color:var(--tui-background-neutral-1)}}tui-data-list [tuiOption]:active:not(:disabled),tui-data-list [tuiOption]:focus-within,tui-data-list [tuiOption]._with-dropdown{background-color:var(--tui-background-neutral-1)}tui-data-list [tuiOption]:not([new]):before{margin-inline-end:.5rem}tui-data-list [tuiOption]:after{font-size:1rem;margin:0 -.75rem 0 auto;border-left:.5rem solid;border-right:.5rem solid}tui-data-list>.t-empty,tui-data-list [tuiOption]{padding-left:var(--t-option-padding-inline);padding-right:var(--t-option-padding-inline)}tui-opt-group{position:relative;display:flex;font:var(--tui-font-text-xs);font-weight:700;color:var(--tui-text-primary);flex-direction:column;line-height:1rem}tui-data-list[data-size=l] tui-opt-group{font:var(--tui-font-text-m);font-weight:700;line-height:1.25rem}tui-data-list[data-size=l] tui-opt-group:before{padding-left:.625rem;padding-right:.625rem}tui-data-list[data-size=l] tui-opt-group:after{left:.625rem;right:.625rem}tui-opt-group:empty:before,tui-opt-group:empty:after{display:none}tui-opt-group:before{content:attr(data-label);padding:var(--tui-data-list-padding) .5rem var(--tui-data-list-padding);margin:var(--tui-data-list-margin) 0;white-space:normal;word-break:break-word}tui-opt-group:after{position:absolute;left:.5rem;right:.5rem;top:var(--tui-data-list-padding);block-size:1px;background:var(--tui-border-normal)}tui-opt-group:not(:empty)~tui-opt-group:before{padding-top:calc(.75rem + var(--tui-data-list-padding))}tui-opt-group:not(:empty)~tui-opt-group[data-label=\\\"\\\"]:before,tui-opt-group:not(:empty)~tui-opt-group:not([data-label]):before{padding:var(--tui-data-list-padding) 0}tui-opt-group:not(:empty)~tui-opt-group:after{content:\\\"\\\"}tui-opt-group[data-label=\\\"\\\"]:before,tui-opt-group:not([data-label]):before{content:\\\"\\\";padding:0;margin:0}\\n\"]\n    }]\n  }], null, {\n    legacyOptionsQuery: [{\n      type: ContentChildren,\n      args: [forwardRef(() => TuiOption), {\n        descendants: true\n      }]\n    }],\n    optionsQuery: [{\n      type: ContentChildren,\n      args: [forwardRef(() => TuiOptionWithValue), {\n        descendants: true\n      }]\n    }],\n    emptyContent: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiDataListDirective {\n  static {\n    this.ɵfac = function TuiDataListDirective_Factory(t) {\n      return new (t || TuiDataListDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDataListDirective,\n      selectors: [[\"ng-template\", \"tuiDataList\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDataListDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-template[tuiDataList]'\n    }]\n  }], null, null);\n})();\nfunction tuiAsDataList(list) {\n  return tuiProvide(TuiDataListDirective, list);\n}\nclass TuiOptGroup {\n  static {\n    this.ɵfac = function TuiOptGroup_Factory(t) {\n      return new (t || TuiOptGroup)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiOptGroup,\n      selectors: [[\"tui-opt-group\"]],\n      hostAttrs: [\"role\", \"group\"],\n      hostVars: 1,\n      hostBindings: function TuiOptGroup_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-label\", ctx.label);\n        }\n      },\n      inputs: {\n        label: \"label\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiOptGroup, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'tui-opt-group',\n      host: {\n        role: 'group',\n        '[attr.data-label]': 'label'\n      }\n    }]\n  }], null, {\n    label: [{\n      type: Input\n    }]\n  });\n})();\nconst TuiDataList = [TuiDataListComponent, TuiDataListDirective, TuiOption, TuiOptionNew, TuiOptionWithValue, TuiOptGroup];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DATA_LIST_ACCESSOR, TUI_DATA_LIST_HOST, TUI_OPTION_CONTENT, TuiDataList, TuiDataListComponent, TuiDataListDirective, TuiOptGroup, TuiOption, TuiOptionNew, TuiOptionWithValue, TuiWithOptionContent, tuiAsDataList, tuiAsDataListAccessor, tuiAsDataListHost, tuiAsOptionContent, tuiInjectDataListSize };", "map": {"version": 3, "names": ["NgIf", "i0", "inject", "Directive", "ContentChild", "ViewContainerRef", "forwardRef", "createComponent", "EnvironmentInjector", "INJECTOR", "Input", "signal", "Component", "ChangeDetectionStrategy", "NgZone", "DestroyRef", "ChangeDetectorRef", "isSignal", "ViewEncapsulation", "ContentChildren", "toSignal", "EMPTY_QUERY", "tui<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tuiZonefree", "tuiTakeUntilDestroyed", "tuiInjectElement", "tuiIsNativeFocused", "tuiMoveFocus", "tuiIsNativeFocusedIn", "tuiCreateToken", "tui<PERSON><PERSON><PERSON>", "tuiIsPresent", "tuiAsAuxiliary", "TUI_NOTHING_FOUND_MESSAGE", "Polymorpheus<PERSON><PERSON>let", "ReplaySubject", "switchMap", "combineLatest", "map", "startWith", "timer", "TUI_IS_MOBILE", "TuiDropdownDirective", "i1", "TuiWithIcons", "_c0", "_c1", "_c2", "a0", "$implicit", "TuiOption_ng_container_0_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "text_r1", "polymorpheusOutlet", "ɵɵadvance", "ɵɵtextInterpolate1", "TuiOption_ng_template_1_Template", "ɵɵprojection", "TuiDataListComponent_div_1_ng_container_1_Template", "TuiDataListComponent_div_1_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "ctx_r1", "ɵɵnextContext", "ɵɵproperty", "emptyContent", "fallback", "TUI_DATA_LIST_ACCESSOR", "tuiAsDataListAccessor", "accessor", "TUI_DATA_LIST_HOST", "tuiAsDataListHost", "host", "TUI_OPTION_CONTENT", "tuiAsOptionContent", "useValue", "provide", "TuiWithOptionContent", "constructor", "localContent", "globalContent", "optional", "content", "ɵfac", "TuiWithOptionContent_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "contentQueries", "TuiWithOptionContent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "descendants", "TuiOptionNew", "vcr", "isMobile", "el", "dataList", "TuiDataListComponent", "ref", "environmentInjector", "elementInjector", "hostElement", "dropdown", "self", "disabled", "insert", "<PERSON><PERSON><PERSON><PERSON>", "changeDetectorRef", "detectChanges", "ngOnDestroy", "handleFocusLossIfNecessary", "onMouseMove", "closest", "focus", "preventScroll", "TuiOptionNew_Factory", "selectors", "hostAttrs", "hostVars", "hostBindings", "TuiOptionNew_HostBindings", "ɵɵlistener", "TuiOptionNew_mousemove_zoneless_HostBindingHandler", "ɵɵattribute", "ɵɵclassProp", "inputs", "features", "ɵɵHostDirectivesFeature", "selector", "hostDirectives", "role", "TuiOptionWithValue", "value", "undefined", "valueSetter", "x", "set", "onClick", "handleOption", "TuiOptionWithValue_Factory", "TuiOptionWithValue_HostBindings", "TuiOptionWithValue_click_HostBindingHandler", "ɵɵInputFlags", "None", "alias", "required", "TuiOption", "TuiOption_Factory", "ɵcmp", "ɵɵdefineComponent", "TuiOption_HostBindings", "TuiOption_click_HostBindingHandler", "TuiOption_mousemove_zoneless_HostBindingHandler", "ɵɵStandaloneFeature", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "TuiOption_Template", "ɵɵprojectionDef", "ɵɵtemplateRefExtractor", "t_r2", "ɵɵreference", "ɵɵpureFunction1", "dependencies", "encapsulation", "changeDetection", "imports", "OnPush", "tuiInjectDataListSize", "sizes", "size", "includes", "legacyOptionsQuery", "optionsQuery", "ngZone", "destroyRef", "cdr", "contentReady$", "empty", "options", "pipe", "legacyOptions", "filter", "requireSync", "onKeyDownArrow", "current", "step", "elements", "indexOf", "element", "origin", "ngAfterContentInit", "next", "ngAfterContentChecked", "subscribe", "length", "getOptions", "includeDisabled", "onFocusIn", "relatedTarget", "currentTarget", "contains", "Array", "from", "querySelectorAll", "TuiDataListComponent_Factory", "TuiDataListComponent_ContentQueries", "TuiDataListComponent_HostBindings", "TuiDataListComponent_focusin_HostBindingHandler", "$event", "TuiDataListComponent_mousedown_prevent_HostBindingHandler", "TuiDataListComponent_wheel_zoneless_passive_HostBindingHandler", "TuiDataListComponent_mouseleave_HostBindingHandler", "target", "TuiDataListComponent_keydown_tab_HostBindingHandler", "TuiDataListComponent_keydown_shift_tab_HostBindingHandler", "TuiDataListComponent_keydown_arrowDown_prevent_HostBindingHandler", "TuiDataListComponent_keydown_arrowUp_prevent_HostBindingHandler", "ɵɵProvidersFeature", "useFactory", "skipSelf", "TuiDataListComponent_Template", "styles", "providers", "TuiDataListDirective", "TuiDataListDirective_Factory", "tuiAsDataList", "list", "TuiOptGroup", "TuiOptGroup_Factory", "TuiOptGroup_HostBindings", "label", "TuiDataList"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-components-data-list.mjs"], "sourcesContent": ["import { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Directive, ContentChild, ViewContainerRef, forwardRef, createComponent, EnvironmentInjector, INJECTOR, Input, signal, Component, ChangeDetectionStrategy, NgZone, DestroyRef, ChangeDetectorRef, isSignal, ViewEncapsulation, ContentChildren } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiQueryListChanges, tuiZonefree, tuiTakeUntilDestroyed } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiIsNativeFocused, tuiMoveFocus, tuiIsNativeFocusedIn } from '@taiga-ui/cdk/utils/focus';\nimport { tuiCreateToken, tuiProvide, tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiAsAuxiliary, TUI_NOTHING_FOUND_MESSAGE } from '@taiga-ui/core/tokens';\nimport { PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { ReplaySubject, switchMap, combineLatest, map, startWith, timer } from 'rxjs';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { TuiDropdownDirective } from '@taiga-ui/core/directives/dropdown';\nimport * as i1 from '@taiga-ui/core/directives/icons';\nimport { TuiWithIcons } from '@taiga-ui/core/directives/icons';\n\n/**\n * Accessor for data-list options\n */\nconst TUI_DATA_LIST_ACCESSOR = tuiCreateToken();\nfunction tuiAsDataListAccessor(accessor) {\n    return [tuiProvide(TUI_DATA_LIST_ACCESSOR, accessor), tuiAsAuxiliary(accessor)];\n}\n/**\n * DataList controller\n */\nconst TUI_DATA_LIST_HOST = tuiCreateToken();\nfunction tuiAsDataListHost(host) {\n    return tuiProvide(TUI_DATA_LIST_HOST, host);\n}\n\n/**\n * Content for tuiOption component\n */\nconst TUI_OPTION_CONTENT = tuiCreateToken();\nfunction tuiAsOptionContent(useValue) {\n    return {\n        provide: TUI_OPTION_CONTENT,\n        useValue,\n    };\n}\nclass TuiWithOptionContent {\n    constructor() {\n        this.localContent = null;\n        this.globalContent = inject(TUI_OPTION_CONTENT, {\n            optional: true,\n        });\n    }\n    get content() {\n        return this.globalContent ?? this.localContent;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiWithOptionContent, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiWithOptionContent, isStandalone: true, queries: [{ propertyName: \"localContent\", first: true, predicate: TUI_OPTION_CONTENT, descendants: true }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiWithOptionContent, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                }]\n        }], propDecorators: { localContent: [{\n                type: ContentChild,\n                args: [TUI_OPTION_CONTENT, { descendants: true }]\n            }] } });\n\n// TODO(v5): rename `TuiOptionNew` => `TuiOption` & remove [new] from selector\n// TODO: Consider all use cases for aria roles\nclass TuiOptionNew {\n    constructor() {\n        this.vcr = inject(ViewContainerRef);\n        this.isMobile = inject(TUI_IS_MOBILE);\n        this.el = tuiInjectElement();\n        this.dataList = inject(forwardRef(() => TuiDataListComponent), { optional: true });\n        this.content = inject(TUI_OPTION_CONTENT, {\n            optional: true,\n        });\n        this.ref = this.content &&\n            createComponent(this.content, {\n                environmentInjector: inject(EnvironmentInjector),\n                elementInjector: inject(INJECTOR),\n                hostElement: tuiInjectElement(),\n            });\n        this.dropdown = inject(TuiDropdownDirective, {\n            self: true,\n            optional: true,\n        })?.ref;\n        this.disabled = false;\n        if (this.ref) {\n            this.vcr.insert(this.ref.hostView);\n            this.ref.changeDetectorRef.detectChanges();\n        }\n    }\n    // Preventing focus loss upon focused option removal\n    ngOnDestroy() {\n        this.dataList?.handleFocusLossIfNecessary(this.el);\n    }\n    onMouseMove() {\n        if (!this.isMobile &&\n            !tuiIsNativeFocused(this.el) &&\n            this.dataList &&\n            this.el.closest('[tuiDataListDropdownManager]')) {\n            this.el.focus({ preventScroll: true });\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiOptionNew, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiOptionNew, isStandalone: true, selector: \"button[tuiOption][new], a[tuiOption][new], label[tuiOption][new]\", inputs: { disabled: \"disabled\" }, host: { attributes: { \"type\": \"button\", \"role\": \"option\" }, listeners: { \"mousemove.zoneless\": \"onMouseMove()\" }, properties: { \"attr.disabled\": \"disabled || null\", \"class._with-dropdown\": \"dropdown?.()\" } }, hostDirectives: [{ directive: i1.TuiWithIcons }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiOptionNew, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'button[tuiOption][new], a[tuiOption][new], label[tuiOption][new]',\n                    hostDirectives: [TuiWithIcons],\n                    host: {\n                        type: 'button',\n                        role: 'option',\n                        '[attr.disabled]': 'disabled || null',\n                        '[class._with-dropdown]': 'dropdown?.()',\n                        '(mousemove.zoneless)': 'onMouseMove()',\n                    },\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { disabled: [{\n                type: Input\n            }] } });\n// TODO(v5): remove [new] from selector\nclass TuiOptionWithValue {\n    constructor() {\n        this.host = inject(TUI_DATA_LIST_HOST, {\n            optional: true,\n        });\n        this.disabled = false;\n        this.value = signal(undefined);\n    }\n    // TODO(v5): use `input.required<T>()` to remove `undefined` from `this.value()`\n    set valueSetter(x) {\n        this.value.set(x);\n    }\n    onClick(value = this.value()) {\n        if (this.host?.handleOption && value !== undefined) {\n            this.host.handleOption(value);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiOptionWithValue, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiOptionWithValue, isStandalone: true, selector: \"button[tuiOption][value][new], a[tuiOption][value][new], label[tuiOption][value][new]\", inputs: { disabled: \"disabled\", valueSetter: [\"value\", \"valueSetter\"] }, host: { listeners: { \"click\": \"onClick()\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiOptionWithValue, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'button[tuiOption][value][new], a[tuiOption][value][new], label[tuiOption][value][new]',\n                    host: {\n                        '(click)': 'onClick()',\n                    },\n                }]\n        }], propDecorators: { disabled: [{\n                type: Input\n            }], valueSetter: [{\n                type: Input,\n                args: [{ alias: 'value', required: true }]\n            }] } });\n\n/**\n * TODO(v5): delete\n * @deprecated use `<button tuiOption new />` / `<a tuiOption new /> / `<label tuiOption new /> instead\n */\nclass TuiOption {\n    constructor() {\n        this.isMobile = inject(TUI_IS_MOBILE);\n        this.el = tuiInjectElement();\n        this.dataList = inject(forwardRef(() => TuiDataListComponent), { optional: true });\n        this.host = inject(TUI_DATA_LIST_HOST, {\n            optional: true,\n        });\n        this.content = inject(TUI_OPTION_CONTENT, { optional: true });\n        this.dropdown = inject(TuiDropdownDirective, {\n            self: true,\n            optional: true,\n        })?.ref;\n        this.disabled = false;\n    }\n    // Preventing focus loss upon focused option removal\n    ngOnDestroy() {\n        this.dataList?.handleFocusLossIfNecessary(this.el);\n    }\n    onClick() {\n        if (this.host?.handleOption && this.value !== undefined) {\n            this.host.handleOption(this.value);\n        }\n    }\n    onMouseMove() {\n        if (!this.isMobile &&\n            !tuiIsNativeFocused(this.el) &&\n            this.dataList &&\n            this.el.closest('[tuiDataListDropdownManager]')) {\n            this.el.focus({ preventScroll: true });\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiOption, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiOption, isStandalone: true, selector: \"button[tuiOption]:not([new]), a[tuiOption]:not([new]), label[tuiOption]:not([new])\", inputs: { disabled: \"disabled\", value: \"value\" }, host: { attributes: { \"type\": \"button\", \"role\": \"option\" }, listeners: { \"click\": \"onClick()\", \"mousemove.zoneless\": \"onMouseMove()\" }, properties: { \"attr.disabled\": \"disabled || null\", \"class._with-dropdown\": \"dropdown?.()\" } }, hostDirectives: [{ directive: i1.TuiWithIcons }], ngImport: i0, template: `\n        <ng-container *polymorpheusOutlet=\"content || t as text; context: {$implicit: t}\">\n            {{ text }}\n        </ng-container>\n        <ng-template #t>\n            <ng-content />\n        </ng-template>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiOption, decorators: [{\n            type: Component,\n            args: [{\n                    standalone: true,\n                    selector: 'button[tuiOption]:not([new]), a[tuiOption]:not([new]), label[tuiOption]:not([new])',\n                    imports: [PolymorpheusOutlet],\n                    template: `\n        <ng-container *polymorpheusOutlet=\"content || t as text; context: {$implicit: t}\">\n            {{ text }}\n        </ng-container>\n        <ng-template #t>\n            <ng-content />\n        </ng-template>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    hostDirectives: [TuiWithIcons],\n                    host: {\n                        type: 'button',\n                        role: 'option',\n                        '[attr.disabled]': 'disabled || null',\n                        '[class._with-dropdown]': 'dropdown?.()',\n                        '(click)': 'onClick()',\n                        '(mousemove.zoneless)': 'onMouseMove()',\n                    },\n                }]\n        }], propDecorators: { disabled: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }] } });\n\nfunction tuiInjectDataListSize() {\n    const sizes = ['s', 'm', 'l'];\n    const size = inject(TUI_DATA_LIST_HOST, { optional: true })?.size;\n    return size && sizes.includes(size) ? size : 'l';\n}\n// TODO: Consider aria-activedescendant for proper accessibility implementation\nclass TuiDataListComponent {\n    constructor() {\n        // TODO(v5): delete\n        this.legacyOptionsQuery = EMPTY_QUERY;\n        this.optionsQuery = EMPTY_QUERY;\n        this.ngZone = inject(NgZone);\n        this.destroyRef = inject(DestroyRef);\n        this.el = tuiInjectElement();\n        this.cdr = inject(ChangeDetectorRef);\n        this.contentReady$ = new ReplaySubject(1);\n        this.fallback = toSignal(inject(TUI_NOTHING_FOUND_MESSAGE));\n        this.empty = signal(false);\n        this.size = tuiInjectDataListSize();\n        // TODO(v5): use signal `contentChildren`\n        this.options = toSignal(this.contentReady$.pipe(switchMap(() => combineLatest([\n            tuiQueryListChanges(this.legacyOptionsQuery),\n            tuiQueryListChanges(this.optionsQuery),\n        ])), map(([legacyOptions, options]) => [\n            ...legacyOptions.map(({ value }) => value),\n            ...options.map(({ value }) => value()),\n        ].filter(tuiIsPresent)), startWith([])), { requireSync: true });\n    }\n    onKeyDownArrow(current, step) {\n        const { elements } = this;\n        tuiMoveFocus(elements.indexOf(current), elements, step);\n    }\n    handleFocusLossIfNecessary(element = this.el) {\n        if (tuiIsNativeFocusedIn(element)) {\n            this.origin?.focus({ preventScroll: true });\n        }\n    }\n    ngAfterContentInit() {\n        this.contentReady$.next(true);\n    }\n    // TODO: Refactor to :has after Safari support bumped to 15\n    ngAfterContentChecked() {\n        timer(0)\n            .pipe(tuiZonefree(this.ngZone), tuiTakeUntilDestroyed(this.destroyRef))\n            .subscribe(() => {\n            this.empty.set(!this.elements.length);\n            this.cdr.detectChanges();\n        });\n    }\n    // TODO(v5): delete\n    getOptions(includeDisabled = false) {\n        return [\n            ...this.legacyOptionsQuery,\n            ...this.optionsQuery,\n        ]\n            .filter(({ disabled }) => includeDisabled || !disabled)\n            .map(({ value }) => (isSignal(value) ? value() : value))\n            .filter(tuiIsPresent);\n    }\n    onFocusIn(relatedTarget, currentTarget) {\n        if (!currentTarget.contains(relatedTarget) && !this.origin) {\n            this.origin = relatedTarget;\n        }\n    }\n    get elements() {\n        return Array.from(this.el.querySelectorAll('[tuiOption]'));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDataListComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDataListComponent, isStandalone: true, selector: \"tui-data-list\", inputs: { emptyContent: \"emptyContent\", size: \"size\" }, host: { attributes: { \"role\": \"listbox\" }, listeners: { \"focusin\": \"onFocusIn($event.relatedTarget, $event.currentTarget)\", \"mousedown.prevent\": \"(0)\", \"wheel.zoneless.passive\": \"handleFocusLossIfNecessary()\", \"mouseleave\": \"handleFocusLossIfNecessary($event.target)\", \"keydown.tab\": \"handleFocusLossIfNecessary()\", \"keydown.shift.tab\": \"handleFocusLossIfNecessary()\", \"keydown.arrowDown.prevent\": \"onKeyDownArrow($event.target, 1)\", \"keydown.arrowUp.prevent\": \"onKeyDownArrow($event.target, -1)\" }, properties: { \"attr.data-size\": \"size\" } }, providers: [\n            tuiAsDataListAccessor(TuiDataListComponent),\n            {\n                provide: TUI_OPTION_CONTENT,\n                useFactory: () => inject(TuiWithOptionContent, { optional: true })?.content ??\n                    inject(TUI_OPTION_CONTENT, { skipSelf: true, optional: true }),\n            },\n        ], queries: [{ propertyName: \"legacyOptionsQuery\", predicate: i0.forwardRef(function () { return TuiOption; }), descendants: true }, { propertyName: \"optionsQuery\", predicate: i0.forwardRef(function () { return TuiOptionWithValue; }), descendants: true }], ngImport: i0, template: \"<ng-content />\\n<div\\n    *ngIf=\\\"empty()\\\"\\n    class=\\\"t-empty\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"emptyContent || fallback() as text\\\">\\n        {{ text }}\\n    </ng-container>\\n</div>\\n\", styles: [\"tui-data-list{--tui-data-list-padding: .25rem;--tui-data-list-margin: .0625rem;display:flex;font:var(--tui-font-text-m);flex-direction:column;padding:calc(var(--tui-data-list-padding) - var(--tui-data-list-margin)) var(--tui-data-list-padding);color:var(--tui-text-tertiary)}tui-data-list:focus-within .t-trap{display:none}tui-data-list:focus-within [tuiOption]._with-dropdown:not(:focus){background-color:transparent}tui-data-list[data-size=s]{--tui-data-list-margin: 0rem}tui-data-list[data-size=s] [tuiOption][new]:not([tuiCell]){gap:.5rem}tui-data-list[data-size=s]>.t-empty,tui-data-list[data-size=s] [tuiOption]{--t-option-padding-inline: .5rem;font:var(--tui-font-text-s);min-block-size:2rem;padding-top:.3125rem;padding-bottom:.3125rem}tui-data-list[data-size=s]>.t-empty:before,tui-data-list[data-size=s] [tuiOption]:before{font-size:1rem}tui-data-list[data-size=m] [tuiOption][new]:not([tuiCell]){gap:.75rem}tui-data-list[data-size=m]>.t-empty,tui-data-list[data-size=m] [tuiOption]{--t-option-padding-inline: .5rem;font:var(--tui-font-text-s);min-block-size:2.5rem;padding-top:.375rem;padding-bottom:.375rem}tui-data-list[data-size=l]{--tui-data-list-padding: .375rem;--tui-data-list-margin: .125rem}tui-data-list[data-size=l] [tuiOption][new]:not([tuiCell]){gap:1rem}tui-data-list[data-size=l]>.t-empty,tui-data-list[data-size=l] [tuiOption]{--t-option-padding-inline: .625rem;font:var(--tui-font-text-m);min-block-size:2.75rem;padding-top:.375rem;padding-bottom:.375rem}tui-data-list>.t-empty{display:flex;align-items:center;box-sizing:border-box;margin:var(--tui-data-list-margin) 0}tui-data-list [tuiOption]:not([new]){justify-content:space-between}tui-data-list [tuiOption]{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:flex;align-items:center;box-sizing:border-box;margin:var(--tui-data-list-margin) 0;text-align:start;color:var(--tui-text-primary);border-radius:var(--tui-radius-s);outline:none;cursor:pointer;background-clip:padding-box}tui-data-list [tuiOption]:disabled{opacity:var(--tui-disabled-opacity);cursor:default}@media (hover: hover) and (pointer: fine){tui-data-list [tuiOption]:hover:not(:disabled){background-color:var(--tui-background-neutral-1)}}tui-data-list [tuiOption]:active:not(:disabled),tui-data-list [tuiOption]:focus-within,tui-data-list [tuiOption]._with-dropdown{background-color:var(--tui-background-neutral-1)}tui-data-list [tuiOption]:not([new]):before{margin-inline-end:.5rem}tui-data-list [tuiOption]:after{font-size:1rem;margin:0 -.75rem 0 auto;border-left:.5rem solid;border-right:.5rem solid}tui-data-list>.t-empty,tui-data-list [tuiOption]{padding-left:var(--t-option-padding-inline);padding-right:var(--t-option-padding-inline)}tui-opt-group{position:relative;display:flex;font:var(--tui-font-text-xs);font-weight:700;color:var(--tui-text-primary);flex-direction:column;line-height:1rem}tui-data-list[data-size=l] tui-opt-group{font:var(--tui-font-text-m);font-weight:700;line-height:1.25rem}tui-data-list[data-size=l] tui-opt-group:before{padding-left:.625rem;padding-right:.625rem}tui-data-list[data-size=l] tui-opt-group:after{left:.625rem;right:.625rem}tui-opt-group:empty:before,tui-opt-group:empty:after{display:none}tui-opt-group:before{content:attr(data-label);padding:var(--tui-data-list-padding) .5rem var(--tui-data-list-padding);margin:var(--tui-data-list-margin) 0;white-space:normal;word-break:break-word}tui-opt-group:after{position:absolute;left:.5rem;right:.5rem;top:var(--tui-data-list-padding);block-size:1px;background:var(--tui-border-normal)}tui-opt-group:not(:empty)~tui-opt-group:before{padding-top:calc(.75rem + var(--tui-data-list-padding))}tui-opt-group:not(:empty)~tui-opt-group[data-label=\\\"\\\"]:before,tui-opt-group:not(:empty)~tui-opt-group:not([data-label]):before{padding:var(--tui-data-list-padding) 0}tui-opt-group:not(:empty)~tui-opt-group:after{content:\\\"\\\"}tui-opt-group[data-label=\\\"\\\"]:before,tui-opt-group:not([data-label]):before{content:\\\"\\\";padding:0;margin:0}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDataListComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-data-list', imports: [NgIf, PolymorpheusOutlet], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        tuiAsDataListAccessor(TuiDataListComponent),\n                        {\n                            provide: TUI_OPTION_CONTENT,\n                            useFactory: () => inject(TuiWithOptionContent, { optional: true })?.content ??\n                                inject(TUI_OPTION_CONTENT, { skipSelf: true, optional: true }),\n                        },\n                    ], host: {\n                        role: 'listbox',\n                        '[attr.data-size]': 'size',\n                        '(focusin)': 'onFocusIn($event.relatedTarget, $event.currentTarget)',\n                        '(mousedown.prevent)': '(0)',\n                        '(wheel.zoneless.passive)': 'handleFocusLossIfNecessary()',\n                        '(mouseleave)': 'handleFocusLossIfNecessary($event.target)',\n                        '(keydown.tab)': 'handleFocusLossIfNecessary()',\n                        '(keydown.shift.tab)': 'handleFocusLossIfNecessary()',\n                        '(keydown.arrowDown.prevent)': 'onKeyDownArrow($event.target, 1)',\n                        '(keydown.arrowUp.prevent)': 'onKeyDownArrow($event.target, -1)',\n                    }, template: \"<ng-content />\\n<div\\n    *ngIf=\\\"empty()\\\"\\n    class=\\\"t-empty\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"emptyContent || fallback() as text\\\">\\n        {{ text }}\\n    </ng-container>\\n</div>\\n\", styles: [\"tui-data-list{--tui-data-list-padding: .25rem;--tui-data-list-margin: .0625rem;display:flex;font:var(--tui-font-text-m);flex-direction:column;padding:calc(var(--tui-data-list-padding) - var(--tui-data-list-margin)) var(--tui-data-list-padding);color:var(--tui-text-tertiary)}tui-data-list:focus-within .t-trap{display:none}tui-data-list:focus-within [tuiOption]._with-dropdown:not(:focus){background-color:transparent}tui-data-list[data-size=s]{--tui-data-list-margin: 0rem}tui-data-list[data-size=s] [tuiOption][new]:not([tuiCell]){gap:.5rem}tui-data-list[data-size=s]>.t-empty,tui-data-list[data-size=s] [tuiOption]{--t-option-padding-inline: .5rem;font:var(--tui-font-text-s);min-block-size:2rem;padding-top:.3125rem;padding-bottom:.3125rem}tui-data-list[data-size=s]>.t-empty:before,tui-data-list[data-size=s] [tuiOption]:before{font-size:1rem}tui-data-list[data-size=m] [tuiOption][new]:not([tuiCell]){gap:.75rem}tui-data-list[data-size=m]>.t-empty,tui-data-list[data-size=m] [tuiOption]{--t-option-padding-inline: .5rem;font:var(--tui-font-text-s);min-block-size:2.5rem;padding-top:.375rem;padding-bottom:.375rem}tui-data-list[data-size=l]{--tui-data-list-padding: .375rem;--tui-data-list-margin: .125rem}tui-data-list[data-size=l] [tuiOption][new]:not([tuiCell]){gap:1rem}tui-data-list[data-size=l]>.t-empty,tui-data-list[data-size=l] [tuiOption]{--t-option-padding-inline: .625rem;font:var(--tui-font-text-m);min-block-size:2.75rem;padding-top:.375rem;padding-bottom:.375rem}tui-data-list>.t-empty{display:flex;align-items:center;box-sizing:border-box;margin:var(--tui-data-list-margin) 0}tui-data-list [tuiOption]:not([new]){justify-content:space-between}tui-data-list [tuiOption]{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:flex;align-items:center;box-sizing:border-box;margin:var(--tui-data-list-margin) 0;text-align:start;color:var(--tui-text-primary);border-radius:var(--tui-radius-s);outline:none;cursor:pointer;background-clip:padding-box}tui-data-list [tuiOption]:disabled{opacity:var(--tui-disabled-opacity);cursor:default}@media (hover: hover) and (pointer: fine){tui-data-list [tuiOption]:hover:not(:disabled){background-color:var(--tui-background-neutral-1)}}tui-data-list [tuiOption]:active:not(:disabled),tui-data-list [tuiOption]:focus-within,tui-data-list [tuiOption]._with-dropdown{background-color:var(--tui-background-neutral-1)}tui-data-list [tuiOption]:not([new]):before{margin-inline-end:.5rem}tui-data-list [tuiOption]:after{font-size:1rem;margin:0 -.75rem 0 auto;border-left:.5rem solid;border-right:.5rem solid}tui-data-list>.t-empty,tui-data-list [tuiOption]{padding-left:var(--t-option-padding-inline);padding-right:var(--t-option-padding-inline)}tui-opt-group{position:relative;display:flex;font:var(--tui-font-text-xs);font-weight:700;color:var(--tui-text-primary);flex-direction:column;line-height:1rem}tui-data-list[data-size=l] tui-opt-group{font:var(--tui-font-text-m);font-weight:700;line-height:1.25rem}tui-data-list[data-size=l] tui-opt-group:before{padding-left:.625rem;padding-right:.625rem}tui-data-list[data-size=l] tui-opt-group:after{left:.625rem;right:.625rem}tui-opt-group:empty:before,tui-opt-group:empty:after{display:none}tui-opt-group:before{content:attr(data-label);padding:var(--tui-data-list-padding) .5rem var(--tui-data-list-padding);margin:var(--tui-data-list-margin) 0;white-space:normal;word-break:break-word}tui-opt-group:after{position:absolute;left:.5rem;right:.5rem;top:var(--tui-data-list-padding);block-size:1px;background:var(--tui-border-normal)}tui-opt-group:not(:empty)~tui-opt-group:before{padding-top:calc(.75rem + var(--tui-data-list-padding))}tui-opt-group:not(:empty)~tui-opt-group[data-label=\\\"\\\"]:before,tui-opt-group:not(:empty)~tui-opt-group:not([data-label]):before{padding:var(--tui-data-list-padding) 0}tui-opt-group:not(:empty)~tui-opt-group:after{content:\\\"\\\"}tui-opt-group[data-label=\\\"\\\"]:before,tui-opt-group:not([data-label]):before{content:\\\"\\\";padding:0;margin:0}\\n\"] }]\n        }], propDecorators: { legacyOptionsQuery: [{\n                type: ContentChildren,\n                args: [forwardRef(() => TuiOption), { descendants: true }]\n            }], optionsQuery: [{\n                type: ContentChildren,\n                args: [forwardRef(() => TuiOptionWithValue), { descendants: true }]\n            }], emptyContent: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }] } });\n\nclass TuiDataListDirective {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDataListDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDataListDirective, isStandalone: true, selector: \"ng-template[tuiDataList]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDataListDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-template[tuiDataList]',\n                }]\n        }] });\nfunction tuiAsDataList(list) {\n    return tuiProvide(TuiDataListDirective, list);\n}\n\nclass TuiOptGroup {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiOptGroup, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiOptGroup, isStandalone: true, selector: \"tui-opt-group\", inputs: { label: \"label\" }, host: { attributes: { \"role\": \"group\" }, properties: { \"attr.data-label\": \"label\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiOptGroup, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-opt-group',\n                    host: {\n                        role: 'group',\n                        '[attr.data-label]': 'label',\n                    },\n                }]\n        }], propDecorators: { label: [{\n                type: Input\n            }] } });\n\nconst TuiDataList = [\n    TuiDataListComponent,\n    TuiDataListDirective,\n    TuiOption,\n    TuiOptionNew,\n    TuiOptionWithValue,\n    TuiOptGroup,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DATA_LIST_ACCESSOR, TUI_DATA_LIST_HOST, TUI_OPTION_CONTENT, TuiDataList, TuiDataListComponent, TuiDataListDirective, TuiOptGroup, TuiOption, TuiOptionNew, TuiOptionWithValue, TuiWithOptionContent, tuiAsDataList, tuiAsDataListAccessor, tuiAsDataListHost, tuiAsOptionContent, tuiInjectDataListSize };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,iBAAiB;AACtC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,eAAe;AACrR,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,EAAEC,WAAW,EAAEC,qBAAqB,QAAQ,2BAA2B;AACnG,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,2BAA2B;AAClG,SAASC,cAAc,EAAEC,UAAU,EAAEC,YAAY,QAAQ,mCAAmC;AAC5F,SAASC,cAAc,EAAEC,yBAAyB,QAAQ,uBAAuB;AACjF,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,aAAa,EAAEC,SAAS,EAAEC,aAAa,EAAEC,GAAG,EAAEC,SAAS,EAAEC,KAAK,QAAQ,MAAM;AACrF,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,oBAAoB,QAAQ,oCAAoC;AACzE,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,YAAY,QAAQ,iCAAiC;;AAE9D;AACA;AACA;AAFA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,SAAAE,kCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAmCqGlD,EAAE,CAAAoD,uBAAA,EAmJd,CAAC;IAnJWpD,EAAE,CAAAqD,MAAA,EAqJhG,CAAC;IArJ6FrD,EAAE,CAAAsD,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAAFxD,EAAE,CAAAyD,SAAA,CAqJhG,CAAC;IArJ6FzD,EAAE,CAAA0D,kBAAA,MAAAH,OAAA,KAqJhG,CAAC;EAAA;AAAA;AAAA,SAAAI,iCAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArJ6FlD,EAAE,CAAA4D,YAAA,EAuJ9E,CAAC;EAAA;AAAA;AAAA,SAAAC,mDAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvJ2ElD,EAAE,CAAAoD,uBAAA,EAqQ8U,CAAC;IArQjVpD,EAAE,CAAAqD,MAAA,EAqQwW,CAAC;IArQ3WrD,EAAE,CAAAsD,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAAFxD,EAAE,CAAAyD,SAAA,CAqQwW,CAAC;IArQ3WzD,EAAE,CAAA0D,kBAAA,MAAAH,OAAA,KAqQwW,CAAC;EAAA;AAAA;AAAA,SAAAO,oCAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArQ3WlD,EAAE,CAAA+D,cAAA,YAqQ+P,CAAC;IArQlQ/D,EAAE,CAAAgE,UAAA,IAAAH,kDAAA,yBAqQ8U,CAAC;IArQjV7D,EAAE,CAAAiE,YAAA,CAqQ+X,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAgB,MAAA,GArQlYlE,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAyD,SAAA,CAqQoU,CAAC;IArQvUzD,EAAE,CAAAoE,UAAA,uBAAAF,MAAA,CAAAG,YAAA,IAAAH,MAAA,CAAAI,QAAA,EAqQoU,CAAC;EAAA;AAAA;AArS5a,MAAMC,sBAAsB,GAAG3C,cAAc,CAAC,CAAC;AAC/C,SAAS4C,qBAAqBA,CAACC,QAAQ,EAAE;EACrC,OAAO,CAAC5C,UAAU,CAAC0C,sBAAsB,EAAEE,QAAQ,CAAC,EAAE1C,cAAc,CAAC0C,QAAQ,CAAC,CAAC;AACnF;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG9C,cAAc,CAAC,CAAC;AAC3C,SAAS+C,iBAAiBA,CAACC,IAAI,EAAE;EAC7B,OAAO/C,UAAU,CAAC6C,kBAAkB,EAAEE,IAAI,CAAC;AAC/C;;AAEA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAGjD,cAAc,CAAC,CAAC;AAC3C,SAASkD,kBAAkBA,CAACC,QAAQ,EAAE;EAClC,OAAO;IACHC,OAAO,EAAEH,kBAAkB;IAC3BE;EACJ,CAAC;AACL;AACA,MAAME,oBAAoB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,aAAa,GAAGnF,MAAM,CAAC4E,kBAAkB,EAAE;MAC5CQ,QAAQ,EAAE;IACd,CAAC,CAAC;EACN;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACF,aAAa,IAAI,IAAI,CAACD,YAAY;EAClD;EACA;IAAS,IAAI,CAACI,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFR,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACS,IAAI,kBAD+E1F,EAAE,CAAA2F,iBAAA;MAAAC,IAAA,EACJX,oBAAoB;MAAAY,cAAA,WAAAC,oCAAA5C,EAAA,EAAAC,GAAA,EAAA4C,QAAA;QAAA,IAAA7C,EAAA;UADlBlD,EAAE,CAAAgG,cAAA,CAAAD,QAAA,EACwGlB,kBAAkB;QAAA;QAAA,IAAA3B,EAAA;UAAA,IAAA+C,EAAA;UAD5HjG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAAhD,GAAA,CAAAgC,YAAA,GAAAc,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,UAAA;IAAA,EACiK;EAAE;AAC1Q;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGtG,EAAE,CAAAuG,iBAAA,CAGXtB,oBAAoB,EAAc,CAAC;IACnHW,IAAI,EAAE1F,SAAS;IACfsG,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAElB,YAAY,EAAE,CAAC;MAC7BS,IAAI,EAAEzF,YAAY;MAClBqG,IAAI,EAAE,CAAC3B,kBAAkB,EAAE;QAAE4B,WAAW,EAAE;MAAK,CAAC;IACpD,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA,MAAMC,YAAY,CAAC;EACfxB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyB,GAAG,GAAG1G,MAAM,CAACG,gBAAgB,CAAC;IACnC,IAAI,CAACwG,QAAQ,GAAG3G,MAAM,CAACuC,aAAa,CAAC;IACrC,IAAI,CAACqE,EAAE,GAAGrF,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACsF,QAAQ,GAAG7G,MAAM,CAACI,UAAU,CAAC,MAAM0G,oBAAoB,CAAC,EAAE;MAAE1B,QAAQ,EAAE;IAAK,CAAC,CAAC;IAClF,IAAI,CAACC,OAAO,GAAGrF,MAAM,CAAC4E,kBAAkB,EAAE;MACtCQ,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,CAAC2B,GAAG,GAAG,IAAI,CAAC1B,OAAO,IACnBhF,eAAe,CAAC,IAAI,CAACgF,OAAO,EAAE;MAC1B2B,mBAAmB,EAAEhH,MAAM,CAACM,mBAAmB,CAAC;MAChD2G,eAAe,EAAEjH,MAAM,CAACO,QAAQ,CAAC;MACjC2G,WAAW,EAAE3F,gBAAgB,CAAC;IAClC,CAAC,CAAC;IACN,IAAI,CAAC4F,QAAQ,GAAGnH,MAAM,CAACwC,oBAAoB,EAAE;MACzC4E,IAAI,EAAE,IAAI;MACVhC,QAAQ,EAAE;IACd,CAAC,CAAC,EAAE2B,GAAG;IACP,IAAI,CAACM,QAAQ,GAAG,KAAK;IACrB,IAAI,IAAI,CAACN,GAAG,EAAE;MACV,IAAI,CAACL,GAAG,CAACY,MAAM,CAAC,IAAI,CAACP,GAAG,CAACQ,QAAQ,CAAC;MAClC,IAAI,CAACR,GAAG,CAACS,iBAAiB,CAACC,aAAa,CAAC,CAAC;IAC9C;EACJ;EACA;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACb,QAAQ,EAAEc,0BAA0B,CAAC,IAAI,CAACf,EAAE,CAAC;EACtD;EACAgB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACjB,QAAQ,IACd,CAACnF,kBAAkB,CAAC,IAAI,CAACoF,EAAE,CAAC,IAC5B,IAAI,CAACC,QAAQ,IACb,IAAI,CAACD,EAAE,CAACiB,OAAO,CAAC,8BAA8B,CAAC,EAAE;MACjD,IAAI,CAACjB,EAAE,CAACkB,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;IAC1C;EACJ;EACA;IAAS,IAAI,CAACzC,IAAI,YAAA0C,qBAAAxC,CAAA;MAAA,YAAAA,CAAA,IAAyFiB,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAAChB,IAAI,kBArD+E1F,EAAE,CAAA2F,iBAAA;MAAAC,IAAA,EAqDJc,YAAY;MAAAwB,SAAA;MAAAC,SAAA,WAAoK,QAAQ,UAAU,QAAQ;MAAAC,QAAA;MAAAC,YAAA,WAAAC,0BAAApF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArDxMlD,EAAE,CAAAuI,UAAA,gCAAAC,mDAAA;YAAA,OAqDJrF,GAAA,CAAA0E,WAAA,CAAY,CAAC;UAAA,CAAF,CAAC;QAAA;QAAA,IAAA3E,EAAA;UArDVlD,EAAE,CAAAyI,WAAA,aAAAtF,GAAA,CAAAmE,QAAA,IAqDQ,IAAI;UArDdtH,EAAE,CAAA0I,WAAA,mBAAAvF,GAAA,CAAAiE,QAAA,kBAAAjE,GAAA,CAAAiE,QAAA,EAqDO,CAAC;QAAA;MAAA;MAAAuB,MAAA;QAAArB,QAAA;MAAA;MAAAjB,UAAA;MAAAuC,QAAA,GArDV5I,EAAE,CAAA6I,uBAAA,EAqD6XnG,EAAE,CAACC,YAAY;IAAA,EAAoB;EAAE;AACzgB;AACA;EAAA,QAAA2D,SAAA,oBAAAA,SAAA,KAvDqGtG,EAAE,CAAAuG,iBAAA,CAuDXG,YAAY,EAAc,CAAC;IAC3Gd,IAAI,EAAE1F,SAAS;IACfsG,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChByC,QAAQ,EAAE,kEAAkE;MAC5EC,cAAc,EAAE,CAACpG,YAAY,CAAC;MAC9BiC,IAAI,EAAE;QACFgB,IAAI,EAAE,QAAQ;QACdoD,IAAI,EAAE,QAAQ;QACd,iBAAiB,EAAE,kBAAkB;QACrC,wBAAwB,EAAE,cAAc;QACxC,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAE1B,QAAQ,EAAE,CAAC;MACrE1B,IAAI,EAAEnF;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMwI,kBAAkB,CAAC;EACrB/D,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,IAAI,GAAG3E,MAAM,CAACyE,kBAAkB,EAAE;MACnCW,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,CAACiC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC4B,KAAK,GAAGxI,MAAM,CAACyI,SAAS,CAAC;EAClC;EACA;EACA,IAAIC,WAAWA,CAACC,CAAC,EAAE;IACf,IAAI,CAACH,KAAK,CAACI,GAAG,CAACD,CAAC,CAAC;EACrB;EACAE,OAAOA,CAACL,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,EAAE;IAC1B,IAAI,IAAI,CAACtE,IAAI,EAAE4E,YAAY,IAAIN,KAAK,KAAKC,SAAS,EAAE;MAChD,IAAI,CAACvE,IAAI,CAAC4E,YAAY,CAACN,KAAK,CAAC;IACjC;EACJ;EACA;IAAS,IAAI,CAAC3D,IAAI,YAAAkE,2BAAAhE,CAAA;MAAA,YAAAA,CAAA,IAAyFwD,kBAAkB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAACvD,IAAI,kBA3F+E1F,EAAE,CAAA2F,iBAAA;MAAAC,IAAA,EA2FJqD,kBAAkB;MAAAf,SAAA;MAAAG,YAAA,WAAAqB,gCAAAxG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3FhBlD,EAAE,CAAAuI,UAAA,mBAAAoB,4CAAA;YAAA,OA2FJxG,GAAA,CAAAoG,OAAA,CAAQ,CAAC;UAAA,CAAQ,CAAC;QAAA;MAAA;MAAAZ,MAAA;QAAArB,QAAA;QAAA8B,WAAA,GA3FhBpJ,EAAE,CAAA4J,YAAA,CAAAC,IAAA;MAAA;MAAAxD,UAAA;IAAA,EA2F8Q;EAAE;AACvX;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7FqGtG,EAAE,CAAAuG,iBAAA,CA6FX0C,kBAAkB,EAAc,CAAC;IACjHrD,IAAI,EAAE1F,SAAS;IACfsG,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChByC,QAAQ,EAAE,uFAAuF;MACjGlE,IAAI,EAAE;QACF,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE0C,QAAQ,EAAE,CAAC;MACzB1B,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAE2I,WAAW,EAAE,CAAC;MACdxD,IAAI,EAAEnF,KAAK;MACX+F,IAAI,EAAE,CAAC;QAAEsD,KAAK,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAK,CAAC;IAC7C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZ9E,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0B,QAAQ,GAAG3G,MAAM,CAACuC,aAAa,CAAC;IACrC,IAAI,CAACqE,EAAE,GAAGrF,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACsF,QAAQ,GAAG7G,MAAM,CAACI,UAAU,CAAC,MAAM0G,oBAAoB,CAAC,EAAE;MAAE1B,QAAQ,EAAE;IAAK,CAAC,CAAC;IAClF,IAAI,CAACT,IAAI,GAAG3E,MAAM,CAACyE,kBAAkB,EAAE;MACnCW,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,CAACC,OAAO,GAAGrF,MAAM,CAAC4E,kBAAkB,EAAE;MAAEQ,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC7D,IAAI,CAAC+B,QAAQ,GAAGnH,MAAM,CAACwC,oBAAoB,EAAE;MACzC4E,IAAI,EAAE,IAAI;MACVhC,QAAQ,EAAE;IACd,CAAC,CAAC,EAAE2B,GAAG;IACP,IAAI,CAACM,QAAQ,GAAG,KAAK;EACzB;EACA;EACAK,WAAWA,CAAA,EAAG;IACV,IAAI,CAACb,QAAQ,EAAEc,0BAA0B,CAAC,IAAI,CAACf,EAAE,CAAC;EACtD;EACA0C,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC3E,IAAI,EAAE4E,YAAY,IAAI,IAAI,CAACN,KAAK,KAAKC,SAAS,EAAE;MACrD,IAAI,CAACvE,IAAI,CAAC4E,YAAY,CAAC,IAAI,CAACN,KAAK,CAAC;IACtC;EACJ;EACArB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACjB,QAAQ,IACd,CAACnF,kBAAkB,CAAC,IAAI,CAACoF,EAAE,CAAC,IAC5B,IAAI,CAACC,QAAQ,IACb,IAAI,CAACD,EAAE,CAACiB,OAAO,CAAC,8BAA8B,CAAC,EAAE;MACjD,IAAI,CAACjB,EAAE,CAACkB,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;IAC1C;EACJ;EACA;IAAS,IAAI,CAACzC,IAAI,YAAA0E,kBAAAxE,CAAA;MAAA,YAAAA,CAAA,IAAyFuE,SAAS;IAAA,CAAmD;EAAE;EACzK;IAAS,IAAI,CAACE,IAAI,kBAlJ+ElK,EAAE,CAAAmK,iBAAA;MAAAvE,IAAA,EAkJJoE,SAAS;MAAA9B,SAAA;MAAAC,SAAA,WAAsM,QAAQ,UAAU,QAAQ;MAAAC,QAAA;MAAAC,YAAA,WAAA+B,uBAAAlH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlJvOlD,EAAE,CAAAuI,UAAA,mBAAA8B,mCAAA;YAAA,OAkJJlH,GAAA,CAAAoG,OAAA,CAAQ,CAAC;UAAA,CAAD,CAAC,gCAAAe,gDAAA;YAAA,OAATnH,GAAA,CAAA0E,WAAA,CAAY,CAAC;UAAA,CAAL,CAAC;QAAA;QAAA,IAAA3E,EAAA;UAlJPlD,EAAE,CAAAyI,WAAA,aAAAtF,GAAA,CAAAmE,QAAA,IAkJQ,IAAI;UAlJdtH,EAAE,CAAA0I,WAAA,mBAAAvF,GAAA,CAAAiE,QAAA,kBAAAjE,GAAA,CAAAiE,QAAA,EAkJI,CAAC;QAAA;MAAA;MAAAuB,MAAA;QAAArB,QAAA;QAAA4B,KAAA;MAAA;MAAA7C,UAAA;MAAAuC,QAAA,GAlJP5I,EAAE,CAAA6I,uBAAA,EAkJkbnG,EAAE,CAACC,YAAY,IAlJnc3C,EAAE,CAAAuK,mBAAA;MAAAC,KAAA,EAAA5H,GAAA;MAAA6H,kBAAA,EAAA5H,GAAA;MAAA6H,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mBAAA5H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlD,EAAE,CAAA+K,eAAA;UAAF/K,EAAE,CAAAgE,UAAA,IAAAf,iCAAA,yBAmJd,CAAC,IAAAU,gCAAA,gCAnJW3D,EAAE,CAAAgL,sBAsJhF,CAAC;QAAA;QAAA,IAAA9H,EAAA;UAAA,MAAA+H,IAAA,GAtJ6EjL,EAAE,CAAAkL,WAAA;UAAFlL,EAAE,CAAAoE,UAAA,uBAAAjB,GAAA,CAAAmC,OAAA,IAAA2F,IAmJhD,CAAC,8BAnJ6CjL,EAAE,CAAAmL,eAAA,IAAArI,GAAA,EAAAmI,IAAA,CAmJhB,CAAC;QAAA;MAAA;MAAAG,YAAA,GAMvBnJ,kBAAkB;MAAAoJ,aAAA;MAAAC,eAAA;IAAA,EAAyJ;EAAE;AAC9O;AACA;EAAA,QAAAhF,SAAA,oBAAAA,SAAA,KA3JqGtG,EAAE,CAAAuG,iBAAA,CA2JXyD,SAAS,EAAc,CAAC;IACxGpE,IAAI,EAAEjF,SAAS;IACf6F,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChByC,QAAQ,EAAE,oFAAoF;MAC9FyC,OAAO,EAAE,CAACtJ,kBAAkB,CAAC;MAC7B4I,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MACeS,eAAe,EAAE1K,uBAAuB,CAAC4K,MAAM;MAC/CzC,cAAc,EAAE,CAACpG,YAAY,CAAC;MAC9BiC,IAAI,EAAE;QACFgB,IAAI,EAAE,QAAQ;QACdoD,IAAI,EAAE,QAAQ;QACd,iBAAiB,EAAE,kBAAkB;QACrC,wBAAwB,EAAE,cAAc;QACxC,SAAS,EAAE,WAAW;QACtB,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE1B,QAAQ,EAAE,CAAC;MACzB1B,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAEyI,KAAK,EAAE,CAAC;MACRtD,IAAI,EAAEnF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,SAASgL,qBAAqBA,CAAA,EAAG;EAC7B,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC7B,MAAMC,IAAI,GAAG1L,MAAM,CAACyE,kBAAkB,EAAE;IAAEW,QAAQ,EAAE;EAAK,CAAC,CAAC,EAAEsG,IAAI;EACjE,OAAOA,IAAI,IAAID,KAAK,CAACE,QAAQ,CAACD,IAAI,CAAC,GAAGA,IAAI,GAAG,GAAG;AACpD;AACA;AACA,MAAM5E,oBAAoB,CAAC;EACvB7B,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAAC2G,kBAAkB,GAAGzK,WAAW;IACrC,IAAI,CAAC0K,YAAY,GAAG1K,WAAW;IAC/B,IAAI,CAAC2K,MAAM,GAAG9L,MAAM,CAACY,MAAM,CAAC;IAC5B,IAAI,CAACmL,UAAU,GAAG/L,MAAM,CAACa,UAAU,CAAC;IACpC,IAAI,CAAC+F,EAAE,GAAGrF,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACyK,GAAG,GAAGhM,MAAM,CAACc,iBAAiB,CAAC;IACpC,IAAI,CAACmL,aAAa,GAAG,IAAIhK,aAAa,CAAC,CAAC,CAAC;IACzC,IAAI,CAACoC,QAAQ,GAAGnD,QAAQ,CAAClB,MAAM,CAAC+B,yBAAyB,CAAC,CAAC;IAC3D,IAAI,CAACmK,KAAK,GAAGzL,MAAM,CAAC,KAAK,CAAC;IAC1B,IAAI,CAACiL,IAAI,GAAGF,qBAAqB,CAAC,CAAC;IACnC;IACA,IAAI,CAACW,OAAO,GAAGjL,QAAQ,CAAC,IAAI,CAAC+K,aAAa,CAACG,IAAI,CAAClK,SAAS,CAAC,MAAMC,aAAa,CAAC,CAC1Ef,mBAAmB,CAAC,IAAI,CAACwK,kBAAkB,CAAC,EAC5CxK,mBAAmB,CAAC,IAAI,CAACyK,YAAY,CAAC,CACzC,CAAC,CAAC,EAAEzJ,GAAG,CAAC,CAAC,CAACiK,aAAa,EAAEF,OAAO,CAAC,KAAK,CACnC,GAAGE,aAAa,CAACjK,GAAG,CAAC,CAAC;MAAE6G;IAAM,CAAC,KAAKA,KAAK,CAAC,EAC1C,GAAGkD,OAAO,CAAC/J,GAAG,CAAC,CAAC;MAAE6G;IAAM,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,CACzC,CAACqD,MAAM,CAACzK,YAAY,CAAC,CAAC,EAAEQ,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MAAEkK,WAAW,EAAE;IAAK,CAAC,CAAC;EACnE;EACAC,cAAcA,CAACC,OAAO,EAAEC,IAAI,EAAE;IAC1B,MAAM;MAAEC;IAAS,CAAC,GAAG,IAAI;IACzBlL,YAAY,CAACkL,QAAQ,CAACC,OAAO,CAACH,OAAO,CAAC,EAAEE,QAAQ,EAAED,IAAI,CAAC;EAC3D;EACA/E,0BAA0BA,CAACkF,OAAO,GAAG,IAAI,CAACjG,EAAE,EAAE;IAC1C,IAAIlF,oBAAoB,CAACmL,OAAO,CAAC,EAAE;MAC/B,IAAI,CAACC,MAAM,EAAEhF,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;IAC/C;EACJ;EACAgF,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACd,aAAa,CAACe,IAAI,CAAC,IAAI,CAAC;EACjC;EACA;EACAC,qBAAqBA,CAAA,EAAG;IACpB3K,KAAK,CAAC,CAAC,CAAC,CACH8J,IAAI,CAAC/K,WAAW,CAAC,IAAI,CAACyK,MAAM,CAAC,EAAExK,qBAAqB,CAAC,IAAI,CAACyK,UAAU,CAAC,CAAC,CACtEmB,SAAS,CAAC,MAAM;MACjB,IAAI,CAAChB,KAAK,CAAC7C,GAAG,CAAC,CAAC,IAAI,CAACsD,QAAQ,CAACQ,MAAM,CAAC;MACrC,IAAI,CAACnB,GAAG,CAACvE,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACA;EACA2F,UAAUA,CAACC,eAAe,GAAG,KAAK,EAAE;IAChC,OAAO,CACH,GAAG,IAAI,CAACzB,kBAAkB,EAC1B,GAAG,IAAI,CAACC,YAAY,CACvB,CACIS,MAAM,CAAC,CAAC;MAAEjF;IAAS,CAAC,KAAKgG,eAAe,IAAI,CAAChG,QAAQ,CAAC,CACtDjF,GAAG,CAAC,CAAC;MAAE6G;IAAM,CAAC,KAAMlI,QAAQ,CAACkI,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,GAAGA,KAAM,CAAC,CACvDqD,MAAM,CAACzK,YAAY,CAAC;EAC7B;EACAyL,SAASA,CAACC,aAAa,EAAEC,aAAa,EAAE;IACpC,IAAI,CAACA,aAAa,CAACC,QAAQ,CAACF,aAAa,CAAC,IAAI,CAAC,IAAI,CAACT,MAAM,EAAE;MACxD,IAAI,CAACA,MAAM,GAAGS,aAAa;IAC/B;EACJ;EACA,IAAIZ,QAAQA,CAAA,EAAG;IACX,OAAOe,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/G,EAAE,CAACgH,gBAAgB,CAAC,aAAa,CAAC,CAAC;EAC9D;EACA;IAAS,IAAI,CAACtI,IAAI,YAAAuI,6BAAArI,CAAA;MAAA,YAAAA,CAAA,IAAyFsB,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACmD,IAAI,kBA9P+ElK,EAAE,CAAAmK,iBAAA;MAAAvE,IAAA,EA8PJmB,oBAAoB;MAAAmB,SAAA;MAAArC,cAAA,WAAAkI,oCAAA7K,EAAA,EAAAC,GAAA,EAAA4C,QAAA;QAAA,IAAA7C,EAAA;UA9PlBlD,EAAE,CAAAgG,cAAA,CAAAD,QAAA,EAqQEiE,SAAS;UArQbhK,EAAE,CAAAgG,cAAA,CAAAD,QAAA,EAqQoHkD,kBAAkB;QAAA;QAAA,IAAA/F,EAAA;UAAA,IAAA+C,EAAA;UArQxIjG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAAhD,GAAA,CAAA0I,kBAAA,GAAA5F,EAAA;UAAFjG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAAhD,GAAA,CAAA2I,YAAA,GAAA7F,EAAA;QAAA;MAAA;MAAAkC,SAAA,WA8PuJ,SAAS;MAAAC,QAAA;MAAAC,YAAA,WAAA2F,kCAAA9K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9PlKlD,EAAE,CAAAuI,UAAA,qBAAA0F,gDAAAC,MAAA;YAAA,OA8PJ/K,GAAA,CAAAoK,SAAA,CAAAW,MAAA,CAAAV,aAAA,EAAAU,MAAA,CAAAT,aAAoD,CAAC;UAAA,CAAlC,CAAC,+BAAAU,0DAAA;YAAA,OAAnB,CAAC;UAAA,CAAiB,CAAC,oCAAAC,+DAAA;YAAA,OAApBjL,GAAA,CAAAyE,0BAAA,CAA2B,CAAC;UAAA,CAAT,CAAC,wBAAAyG,mDAAAH,MAAA;YAAA,OAApB/K,GAAA,CAAAyE,0BAAA,CAAAsG,MAAA,CAAAI,MAAwC,CAAC;UAAA,CAAtB,CAAC,yBAAAC,oDAAA;YAAA,OAApBpL,GAAA,CAAAyE,0BAAA,CAA2B,CAAC;UAAA,CAAT,CAAC,+BAAA4G,0DAAA;YAAA,OAApBrL,GAAA,CAAAyE,0BAAA,CAA2B,CAAC;UAAA,CAAT,CAAC,uCAAA6G,kEAAAP,MAAA;YAAA,OAApB/K,GAAA,CAAAsJ,cAAA,CAAAyB,MAAA,CAAAI,MAAA,EAA8B,CAAC,CAAC;UAAA,CAAb,CAAC,qCAAAI,gEAAAR,MAAA;YAAA,OAApB/K,GAAA,CAAAsJ,cAAA,CAAAyB,MAAA,CAAAI,MAAA,GAA+B,CAAC,CAAC;UAAA,CAAd,CAAC;QAAA;QAAA,IAAApL,EAAA;UA9PlBlD,EAAE,CAAAyI,WAAA,cAAAtF,GAAA,CAAAwI,IAAA;QAAA;MAAA;MAAAhD,MAAA;QAAAtE,YAAA;QAAAsH,IAAA;MAAA;MAAAtF,UAAA;MAAAuC,QAAA,GAAF5I,EAAE,CAAA2O,kBAAA,CA8PoqB,CAC/vBnK,qBAAqB,CAACuC,oBAAoB,CAAC,EAC3C;QACI/B,OAAO,EAAEH,kBAAkB;QAC3B+J,UAAU,EAAEA,CAAA,KAAM3O,MAAM,CAACgF,oBAAoB,EAAE;UAAEI,QAAQ,EAAE;QAAK,CAAC,CAAC,EAAEC,OAAO,IACvErF,MAAM,CAAC4E,kBAAkB,EAAE;UAAEgK,QAAQ,EAAE,IAAI;UAAExJ,QAAQ,EAAE;QAAK,CAAC;MACrE,CAAC,CACJ,GArQ4FrF,EAAE,CAAAuK,mBAAA;MAAAE,kBAAA,EAAA5H,GAAA;MAAA6H,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAiE,8BAAA5L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlD,EAAE,CAAA+K,eAAA;UAAF/K,EAAE,CAAA4D,YAAA,EAqQwM,CAAC;UArQ3M5D,EAAE,CAAAgE,UAAA,IAAAF,mCAAA,gBAqQ+P,CAAC;QAAA;QAAA,IAAAZ,EAAA;UArQlQlD,EAAE,CAAAyD,SAAA,CAqQmO,CAAC;UArQtOzD,EAAE,CAAAoE,UAAA,SAAAjB,GAAA,CAAAgJ,KAAA,EAqQmO,CAAC;QAAA;MAAA;MAAAf,YAAA,GAAgyIrL,IAAI,EAA6FkC,kBAAkB;MAAA8M,MAAA;MAAA1D,aAAA;MAAAC,eAAA;IAAA,EAAmM;EAAE;AACn6J;AACA;EAAA,QAAAhF,SAAA,oBAAAA,SAAA,KAvQqGtG,EAAE,CAAAuG,iBAAA,CAuQXQ,oBAAoB,EAAc,CAAC;IACnHnB,IAAI,EAAEjF,SAAS;IACf6F,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,IAAI;MAAEyC,QAAQ,EAAE,eAAe;MAAEyC,OAAO,EAAE,CAACxL,IAAI,EAAEkC,kBAAkB,CAAC;MAAEoJ,aAAa,EAAEpK,iBAAiB,CAAC4I,IAAI;MAAEyB,eAAe,EAAE1K,uBAAuB,CAAC4K,MAAM;MAAEwD,SAAS,EAAE,CAClLxK,qBAAqB,CAACuC,oBAAoB,CAAC,EAC3C;QACI/B,OAAO,EAAEH,kBAAkB;QAC3B+J,UAAU,EAAEA,CAAA,KAAM3O,MAAM,CAACgF,oBAAoB,EAAE;UAAEI,QAAQ,EAAE;QAAK,CAAC,CAAC,EAAEC,OAAO,IACvErF,MAAM,CAAC4E,kBAAkB,EAAE;UAAEgK,QAAQ,EAAE,IAAI;UAAExJ,QAAQ,EAAE;QAAK,CAAC;MACrE,CAAC,CACJ;MAAET,IAAI,EAAE;QACLoE,IAAI,EAAE,SAAS;QACf,kBAAkB,EAAE,MAAM;QAC1B,WAAW,EAAE,uDAAuD;QACpE,qBAAqB,EAAE,KAAK;QAC5B,0BAA0B,EAAE,8BAA8B;QAC1D,cAAc,EAAE,2CAA2C;QAC3D,eAAe,EAAE,8BAA8B;QAC/C,qBAAqB,EAAE,8BAA8B;QACrD,6BAA6B,EAAE,kCAAkC;QACjE,2BAA2B,EAAE;MACjC,CAAC;MAAE6B,QAAQ,EAAE,yMAAyM;MAAEkE,MAAM,EAAE,CAAC,ykIAAykI;IAAE,CAAC;EACzzI,CAAC,CAAC,QAAkB;IAAElD,kBAAkB,EAAE,CAAC;MACnCjG,IAAI,EAAE1E,eAAe;MACrBsF,IAAI,EAAE,CAACnG,UAAU,CAAC,MAAM2J,SAAS,CAAC,EAAE;QAAEvD,WAAW,EAAE;MAAK,CAAC;IAC7D,CAAC,CAAC;IAAEqF,YAAY,EAAE,CAAC;MACflG,IAAI,EAAE1E,eAAe;MACrBsF,IAAI,EAAE,CAACnG,UAAU,CAAC,MAAM4I,kBAAkB,CAAC,EAAE;QAAExC,WAAW,EAAE;MAAK,CAAC;IACtE,CAAC,CAAC;IAAEpC,YAAY,EAAE,CAAC;MACfuB,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAEkL,IAAI,EAAE,CAAC;MACP/F,IAAI,EAAEnF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMwO,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAAC1J,IAAI,YAAA2J,6BAAAzJ,CAAA;MAAA,YAAAA,CAAA,IAAyFwJ,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACvJ,IAAI,kBA1S+E1F,EAAE,CAAA2F,iBAAA;MAAAC,IAAA,EA0SJqJ,oBAAoB;MAAA/G,SAAA;MAAA7B,UAAA;IAAA,EAA2E;EAAE;AACpM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5SqGtG,EAAE,CAAAuG,iBAAA,CA4SX0I,oBAAoB,EAAc,CAAC;IACnHrJ,IAAI,EAAE1F,SAAS;IACfsG,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChByC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;AACV,SAASqG,aAAaA,CAACC,IAAI,EAAE;EACzB,OAAOvN,UAAU,CAACoN,oBAAoB,EAAEG,IAAI,CAAC;AACjD;AAEA,MAAMC,WAAW,CAAC;EACd;IAAS,IAAI,CAAC9J,IAAI,YAAA+J,oBAAA7J,CAAA;MAAA,YAAAA,CAAA,IAAyF4J,WAAW;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAAC3J,IAAI,kBAzT+E1F,EAAE,CAAA2F,iBAAA;MAAAC,IAAA,EAyTJyJ,WAAW;MAAAnH,SAAA;MAAAC,SAAA,WAA2G,OAAO;MAAAC,QAAA;MAAAC,YAAA,WAAAkH,yBAAArM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzT3HlD,EAAE,CAAAyI,WAAA,eAAAtF,GAAA,CAAAqM,KAAA;QAAA;MAAA;MAAA7G,MAAA;QAAA6G,KAAA;MAAA;MAAAnJ,UAAA;IAAA,EAyT0L;EAAE;AACnS;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3TqGtG,EAAE,CAAAuG,iBAAA,CA2TX8I,WAAW,EAAc,CAAC;IAC1GzJ,IAAI,EAAE1F,SAAS;IACfsG,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChByC,QAAQ,EAAE,eAAe;MACzBlE,IAAI,EAAE;QACFoE,IAAI,EAAE,OAAO;QACb,mBAAmB,EAAE;MACzB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEwG,KAAK,EAAE,CAAC;MACtB5J,IAAI,EAAEnF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgP,WAAW,GAAG,CAChB1I,oBAAoB,EACpBkI,oBAAoB,EACpBjF,SAAS,EACTtD,YAAY,EACZuC,kBAAkB,EAClBoG,WAAW,CACd;;AAED;AACA;AACA;;AAEA,SAAS9K,sBAAsB,EAAEG,kBAAkB,EAAEG,kBAAkB,EAAE4K,WAAW,EAAE1I,oBAAoB,EAAEkI,oBAAoB,EAAEI,WAAW,EAAErF,SAAS,EAAEtD,YAAY,EAAEuC,kBAAkB,EAAEhE,oBAAoB,EAAEkK,aAAa,EAAE3K,qBAAqB,EAAEG,iBAAiB,EAAEG,kBAAkB,EAAE2G,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}