{"ast": null, "code": "import { isIos } from '@ng-web-apis/platform';\n\n/**\n * @description:\n * All Chrome / Chromium-based browsers will return MacIntel on macOS,\n * no matter what the hardware architecture is. See the source code here:\n * https://source.chromium.org/chromium/chromium/src/+/master:third_party/blink/renderer/core/frame/navigator_id.cc;l=64;drc=703d3c472cf27470dad21a3f2c8972aca3732cd6\n * But maybe in future years, it will be changed to MacM1\n *\n * Documentation:\n * https://developer.mozilla.org/en-US/docs/Web/API/Navigator/platform\n */\nfunction tuiIsApple(navigator) {\n  return navigator.platform.startsWith('Mac') || navigator.platform === 'iPhone';\n}\nfunction tuiIsEdge(userAgent) {\n  return userAgent.toLowerCase().includes('edge');\n}\nfunction tuiIsFirefox(userAgent) {\n  return userAgent.toLowerCase().includes('firefox');\n}\n\n/**\n * @deprecated: drop in v5, use import {isApple} from '@ng-web-apis/platform';\n * @param navigator\n */\nconst tuiIsIos = isIos;\n\n// TODO: Drop change to Document in v5\nfunction tuiIsSafari({\n  ownerDocument: doc\n}) {\n  const win = doc?.defaultView;\n  const isMacOsSafari = win.safari !== undefined && win.safari?.pushNotification?.toString() === '[object SafariRemoteNotification]';\n  const isIosSafari = (win.navigator?.vendor?.includes('Apple') && !win.navigator?.userAgent?.includes('CriOS') && !win.navigator?.userAgent?.includes('FxiOS')) ?? false;\n  return isMacOsSafari || isIosSafari;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiIsApple, tuiIsEdge, tuiIsFirefox, tuiIsIos, tuiIsSafari };", "map": {"version": 3, "names": ["isIos", "tuiIsApple", "navigator", "platform", "startsWith", "tui<PERSON>s<PERSON>dge", "userAgent", "toLowerCase", "includes", "tuiIsFirefox", "tuiIsIos", "tui<PERSON>s<PERSON><PERSON><PERSON>", "ownerDocument", "doc", "win", "defaultView", "isMacOsSafari", "safari", "undefined", "pushNotification", "toString", "isIosSafari", "vendor"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-utils-browser.mjs"], "sourcesContent": ["import { isIos } from '@ng-web-apis/platform';\n\n/**\n * @description:\n * All Chrome / Chromium-based browsers will return MacIntel on macOS,\n * no matter what the hardware architecture is. See the source code here:\n * https://source.chromium.org/chromium/chromium/src/+/master:third_party/blink/renderer/core/frame/navigator_id.cc;l=64;drc=703d3c472cf27470dad21a3f2c8972aca3732cd6\n * But maybe in future years, it will be changed to MacM1\n *\n * Documentation:\n * https://developer.mozilla.org/en-US/docs/Web/API/Navigator/platform\n */\nfunction tuiIsApple(navigator) {\n    return navigator.platform.startsWith('Mac') || navigator.platform === 'iPhone';\n}\n\nfunction tuiIsEdge(userAgent) {\n    return userAgent.toLowerCase().includes('edge');\n}\n\nfunction tuiIsFirefox(userAgent) {\n    return userAgent.toLowerCase().includes('firefox');\n}\n\n/**\n * @deprecated: drop in v5, use import {isApple} from '@ng-web-apis/platform';\n * @param navigator\n */\nconst tuiIsIos = isIos;\n\n// TODO: Drop change to Document in v5\nfunction tuiIsSafari({ ownerDocument: doc }) {\n    const win = doc?.defaultView;\n    const isMacOsSafari = win.safari !== undefined &&\n        win.safari?.pushNotification?.toString() === '[object SafariRemoteNotification]';\n    const isIosSafari = (win.navigator?.vendor?.includes('Apple') &&\n        !win.navigator?.userAgent?.includes('CriOS') &&\n        !win.navigator?.userAgent?.includes('FxiOS')) ??\n        false;\n    return isMacOsSafari || isIosSafari;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiIsApple, tuiIsEdge, tuiIsFirefox, tuiIsIos, tuiIsSafari };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,uBAAuB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,SAAS,EAAE;EAC3B,OAAOA,SAAS,CAACC,QAAQ,CAACC,UAAU,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACC,QAAQ,KAAK,QAAQ;AAClF;AAEA,SAASE,SAASA,CAACC,SAAS,EAAE;EAC1B,OAAOA,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC;AACnD;AAEA,SAASC,YAAYA,CAACH,SAAS,EAAE;EAC7B,OAAOA,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA,MAAME,QAAQ,GAAGV,KAAK;;AAEtB;AACA,SAASW,WAAWA,CAAC;EAAEC,aAAa,EAAEC;AAAI,CAAC,EAAE;EACzC,MAAMC,GAAG,GAAGD,GAAG,EAAEE,WAAW;EAC5B,MAAMC,aAAa,GAAGF,GAAG,CAACG,MAAM,KAAKC,SAAS,IAC1CJ,GAAG,CAACG,MAAM,EAAEE,gBAAgB,EAAEC,QAAQ,CAAC,CAAC,KAAK,mCAAmC;EACpF,MAAMC,WAAW,GAAG,CAACP,GAAG,CAACZ,SAAS,EAAEoB,MAAM,EAAEd,QAAQ,CAAC,OAAO,CAAC,IACzD,CAACM,GAAG,CAACZ,SAAS,EAAEI,SAAS,EAAEE,QAAQ,CAAC,OAAO,CAAC,IAC5C,CAACM,GAAG,CAACZ,SAAS,EAAEI,SAAS,EAAEE,QAAQ,CAAC,OAAO,CAAC,KAC5C,KAAK;EACT,OAAOQ,aAAa,IAAIK,WAAW;AACvC;;AAEA;AACA;AACA;;AAEA,SAASpB,UAAU,EAAEI,SAAS,EAAEI,YAAY,EAAEC,QAAQ,EAAEC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}