{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { DAYS_IN_WEEK, TuiDay } from '@taiga-ui/cdk/date-time';\nimport { TUI_FIRST_DAY_OF_WEEK } from '@taiga-ui/core/tokens';\nimport { tuiInRange } from '@taiga-ui/cdk/utils/math';\n\n/**\n * Computes day of week offset of the beginning of the month\n */\nconst getMonthStartDaysOffset = (month, firstDayOfWeek) => {\n  const startMonthOffsetFromSunday = new Date(month.year, month.month, 1).getDay();\n  return startMonthOffsetFromSunday >= firstDayOfWeek ? startMonthOffsetFromSunday - firstDayOfWeek : DAYS_IN_WEEK - (firstDayOfWeek - startMonthOffsetFromSunday);\n};\n/**\n * Calculated day on a calendar grid\n * @return resulting day on these coordinates (could exceed passed month)\n */\nconst getDayFromMonthRowCol = ({\n  month,\n  rowIndex,\n  colIndex,\n  firstDayOfWeek\n}) => {\n  ngDevMode && console.assert(Number.isInteger(rowIndex));\n  ngDevMode && console.assert(tuiInRange(rowIndex, 0, 6));\n  ngDevMode && console.assert(Number.isInteger(colIndex));\n  ngDevMode && console.assert(tuiInRange(colIndex, 0, DAYS_IN_WEEK));\n  let day = rowIndex * DAYS_IN_WEEK + colIndex - getMonthStartDaysOffset(month, firstDayOfWeek) + 1;\n  if (day > month.daysCount) {\n    day -= month.daysCount;\n    month = month.append({\n      month: 1\n    });\n  }\n  if (day <= 0) {\n    month = month.append({\n      month: -1\n    });\n    day = month.daysCount + day;\n  }\n  return new TuiDay(month.year, month.month, day);\n};\nconst CALENDAR_ROWS_COUNT = 6;\nclass TuiCalendarSheetPipe {\n  constructor() {\n    this.firstDayOfWeek = inject(TUI_FIRST_DAY_OF_WEEK);\n    this.currentMonth = null;\n    this.currentSheet = [];\n  }\n  transform(month, showAdjacentDays = false) {\n    if (this.currentMonth?.monthSame(month)) {\n      return this.currentSheet;\n    }\n    const sheet = [];\n    for (let rowIndex = 0; rowIndex < CALENDAR_ROWS_COUNT; rowIndex++) {\n      const row = [];\n      for (let colIndex = 0; colIndex < DAYS_IN_WEEK; colIndex++) {\n        const day = getDayFromMonthRowCol({\n          month,\n          rowIndex,\n          colIndex,\n          firstDayOfWeek: this.firstDayOfWeek\n        });\n        const isPrevMonthDay = (day, relativeToMonth = month) => day.year < relativeToMonth.year || day.month < relativeToMonth.month;\n        const isNextMonthDay = (day, relativeToMonth = month) => day.year > relativeToMonth.year || day.month > relativeToMonth.month;\n        if (isPrevMonthDay(day) && !showAdjacentDays) {\n          continue;\n        }\n        if (isNextMonthDay(day) && !showAdjacentDays) {\n          break;\n        }\n        row.push(day);\n      }\n      sheet.push(row);\n    }\n    this.currentSheet = sheet.filter(row => row.length);\n    this.currentMonth = month;\n    return this.currentSheet;\n  }\n  static {\n    this.ɵfac = function TuiCalendarSheetPipe_Factory(t) {\n      return new (t || TuiCalendarSheetPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiCalendarSheet\",\n      type: TuiCalendarSheetPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCalendarSheetPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiCalendarSheet'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiCalendarSheetPipe };", "map": {"version": 3, "names": ["i0", "inject", "<PERSON><PERSON>", "DAYS_IN_WEEK", "TuiDay", "TUI_FIRST_DAY_OF_WEEK", "tuiInRange", "getMonthStartDaysOffset", "month", "firstDayOfWeek", "startMonthOffsetFromSunday", "Date", "year", "getDay", "getDayFromMonthRowCol", "rowIndex", "colIndex", "ngDevMode", "console", "assert", "Number", "isInteger", "day", "daysCount", "append", "CALENDAR_ROWS_COUNT", "TuiCalendarSheetPipe", "constructor", "currentMonth", "currentSheet", "transform", "showAdjacentDays", "monthSame", "sheet", "row", "isPrevMonthDay", "relativeToMonth", "isNextMonthDay", "push", "filter", "length", "ɵfac", "TuiCalendarSheetPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-pipes-calendar-sheet.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { DAYS_IN_WEEK, TuiDay } from '@taiga-ui/cdk/date-time';\nimport { TUI_FIRST_DAY_OF_WEEK } from '@taiga-ui/core/tokens';\nimport { tuiInRange } from '@taiga-ui/cdk/utils/math';\n\n/**\n * Computes day of week offset of the beginning of the month\n */\nconst getMonthStartDaysOffset = (month, firstDayOfWeek) => {\n    const startMonthOffsetFromSunday = new Date(month.year, month.month, 1).getDay();\n    return startMonthOffsetFromSunday >= firstDayOfWeek\n        ? startMonthOffsetFromSunday - firstDayOfWeek\n        : DAYS_IN_WEEK - (firstDayOfWeek - startMonthOffsetFromSunday);\n};\n/**\n * Calculated day on a calendar grid\n * @return resulting day on these coordinates (could exceed passed month)\n */\nconst getDayFromMonthRowCol = ({ month, rowIndex, colIndex, firstDayOfWeek, }) => {\n    ngDevMode && console.assert(Number.isInteger(rowIndex));\n    ngDevMode && console.assert(tuiInRange(rowIndex, 0, 6));\n    ngDevMode && console.assert(Number.isInteger(colIndex));\n    ngDevMode && console.assert(tuiInRange(colIndex, 0, DAYS_IN_WEEK));\n    let day = rowIndex * DAYS_IN_WEEK +\n        colIndex -\n        getMonthStartDaysOffset(month, firstDayOfWeek) +\n        1;\n    if (day > month.daysCount) {\n        day -= month.daysCount;\n        month = month.append({ month: 1 });\n    }\n    if (day <= 0) {\n        month = month.append({ month: -1 });\n        day = month.daysCount + day;\n    }\n    return new TuiDay(month.year, month.month, day);\n};\n\nconst CALENDAR_ROWS_COUNT = 6;\nclass TuiCalendarSheetPipe {\n    constructor() {\n        this.firstDayOfWeek = inject(TUI_FIRST_DAY_OF_WEEK);\n        this.currentMonth = null;\n        this.currentSheet = [];\n    }\n    transform(month, showAdjacentDays = false) {\n        if (this.currentMonth?.monthSame(month)) {\n            return this.currentSheet;\n        }\n        const sheet = [];\n        for (let rowIndex = 0; rowIndex < CALENDAR_ROWS_COUNT; rowIndex++) {\n            const row = [];\n            for (let colIndex = 0; colIndex < DAYS_IN_WEEK; colIndex++) {\n                const day = getDayFromMonthRowCol({\n                    month,\n                    rowIndex,\n                    colIndex,\n                    firstDayOfWeek: this.firstDayOfWeek,\n                });\n                const isPrevMonthDay = (day, relativeToMonth = month) => day.year < relativeToMonth.year || day.month < relativeToMonth.month;\n                const isNextMonthDay = (day, relativeToMonth = month) => day.year > relativeToMonth.year || day.month > relativeToMonth.month;\n                if (isPrevMonthDay(day) && !showAdjacentDays) {\n                    continue;\n                }\n                if (isNextMonthDay(day) && !showAdjacentDays) {\n                    break;\n                }\n                row.push(day);\n            }\n            sheet.push(row);\n        }\n        this.currentSheet = sheet.filter((row) => row.length);\n        this.currentMonth = month;\n        return this.currentSheet;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendarSheetPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendarSheetPipe, isStandalone: true, name: \"tuiCalendarSheet\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendarSheetPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiCalendarSheet',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiCalendarSheetPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,IAAI,QAAQ,eAAe;AAC5C,SAASC,YAAY,EAAEC,MAAM,QAAQ,yBAAyB;AAC9D,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,UAAU,QAAQ,0BAA0B;;AAErD;AACA;AACA;AACA,MAAMC,uBAAuB,GAAGA,CAACC,KAAK,EAAEC,cAAc,KAAK;EACvD,MAAMC,0BAA0B,GAAG,IAAIC,IAAI,CAACH,KAAK,CAACI,IAAI,EAAEJ,KAAK,CAACA,KAAK,EAAE,CAAC,CAAC,CAACK,MAAM,CAAC,CAAC;EAChF,OAAOH,0BAA0B,IAAID,cAAc,GAC7CC,0BAA0B,GAAGD,cAAc,GAC3CN,YAAY,IAAIM,cAAc,GAAGC,0BAA0B,CAAC;AACtE,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,qBAAqB,GAAGA,CAAC;EAAEN,KAAK;EAAEO,QAAQ;EAAEC,QAAQ;EAAEP;AAAgB,CAAC,KAAK;EAC9EQ,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACC,MAAM,CAACC,SAAS,CAACN,QAAQ,CAAC,CAAC;EACvDE,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACb,UAAU,CAACS,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACvDE,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACC,MAAM,CAACC,SAAS,CAACL,QAAQ,CAAC,CAAC;EACvDC,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACb,UAAU,CAACU,QAAQ,EAAE,CAAC,EAAEb,YAAY,CAAC,CAAC;EAClE,IAAImB,GAAG,GAAGP,QAAQ,GAAGZ,YAAY,GAC7Ba,QAAQ,GACRT,uBAAuB,CAACC,KAAK,EAAEC,cAAc,CAAC,GAC9C,CAAC;EACL,IAAIa,GAAG,GAAGd,KAAK,CAACe,SAAS,EAAE;IACvBD,GAAG,IAAId,KAAK,CAACe,SAAS;IACtBf,KAAK,GAAGA,KAAK,CAACgB,MAAM,CAAC;MAAEhB,KAAK,EAAE;IAAE,CAAC,CAAC;EACtC;EACA,IAAIc,GAAG,IAAI,CAAC,EAAE;IACVd,KAAK,GAAGA,KAAK,CAACgB,MAAM,CAAC;MAAEhB,KAAK,EAAE,CAAC;IAAE,CAAC,CAAC;IACnCc,GAAG,GAAGd,KAAK,CAACe,SAAS,GAAGD,GAAG;EAC/B;EACA,OAAO,IAAIlB,MAAM,CAACI,KAAK,CAACI,IAAI,EAAEJ,KAAK,CAACA,KAAK,EAAEc,GAAG,CAAC;AACnD,CAAC;AAED,MAAMG,mBAAmB,GAAG,CAAC;AAC7B,MAAMC,oBAAoB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClB,cAAc,GAAGR,MAAM,CAACI,qBAAqB,CAAC;IACnD,IAAI,CAACuB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,YAAY,GAAG,EAAE;EAC1B;EACAC,SAASA,CAACtB,KAAK,EAAEuB,gBAAgB,GAAG,KAAK,EAAE;IACvC,IAAI,IAAI,CAACH,YAAY,EAAEI,SAAS,CAACxB,KAAK,CAAC,EAAE;MACrC,OAAO,IAAI,CAACqB,YAAY;IAC5B;IACA,MAAMI,KAAK,GAAG,EAAE;IAChB,KAAK,IAAIlB,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGU,mBAAmB,EAAEV,QAAQ,EAAE,EAAE;MAC/D,MAAMmB,GAAG,GAAG,EAAE;MACd,KAAK,IAAIlB,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGb,YAAY,EAAEa,QAAQ,EAAE,EAAE;QACxD,MAAMM,GAAG,GAAGR,qBAAqB,CAAC;UAC9BN,KAAK;UACLO,QAAQ;UACRC,QAAQ;UACRP,cAAc,EAAE,IAAI,CAACA;QACzB,CAAC,CAAC;QACF,MAAM0B,cAAc,GAAGA,CAACb,GAAG,EAAEc,eAAe,GAAG5B,KAAK,KAAKc,GAAG,CAACV,IAAI,GAAGwB,eAAe,CAACxB,IAAI,IAAIU,GAAG,CAACd,KAAK,GAAG4B,eAAe,CAAC5B,KAAK;QAC7H,MAAM6B,cAAc,GAAGA,CAACf,GAAG,EAAEc,eAAe,GAAG5B,KAAK,KAAKc,GAAG,CAACV,IAAI,GAAGwB,eAAe,CAACxB,IAAI,IAAIU,GAAG,CAACd,KAAK,GAAG4B,eAAe,CAAC5B,KAAK;QAC7H,IAAI2B,cAAc,CAACb,GAAG,CAAC,IAAI,CAACS,gBAAgB,EAAE;UAC1C;QACJ;QACA,IAAIM,cAAc,CAACf,GAAG,CAAC,IAAI,CAACS,gBAAgB,EAAE;UAC1C;QACJ;QACAG,GAAG,CAACI,IAAI,CAAChB,GAAG,CAAC;MACjB;MACAW,KAAK,CAACK,IAAI,CAACJ,GAAG,CAAC;IACnB;IACA,IAAI,CAACL,YAAY,GAAGI,KAAK,CAACM,MAAM,CAAEL,GAAG,IAAKA,GAAG,CAACM,MAAM,CAAC;IACrD,IAAI,CAACZ,YAAY,GAAGpB,KAAK;IACzB,OAAO,IAAI,CAACqB,YAAY;EAC5B;EACA;IAAS,IAAI,CAACY,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFjB,oBAAoB;IAAA,CAA8C;EAAE;EAC/K;IAAS,IAAI,CAACkB,KAAK,kBAD8E5C,EAAE,CAAA6C,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMrB,oBAAoB;MAAAsB,IAAA;MAAAC,UAAA;IAAA,EAAiD;EAAE;AACpL;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KAHqGjB,EAAE,CAAAkD,iBAAA,CAGXxB,oBAAoB,EAAc,CAAC;IACnHqB,IAAI,EAAE7C,IAAI;IACViD,IAAI,EAAE,CAAC;MACCF,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASpB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}