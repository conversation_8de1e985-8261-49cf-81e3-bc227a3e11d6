{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { TUI_FIRST_DAY_OF_WEEK } from '@taiga-ui/core/tokens';\nimport { map } from 'rxjs';\nfunction convertToSundayFirstWeekFormat(weekDaysNames) {\n  const sundayIndex = weekDaysNames.length - 1;\n  return [weekDaysNames[sundayIndex] || '', ...weekDaysNames.slice(0, sundayIndex)];\n}\nclass TuiOrderWeekDaysPipe {\n  constructor() {\n    this.firstDayOfWeekIndex = inject(TUI_FIRST_DAY_OF_WEEK);\n  }\n  transform(mondayFirstWeekDays$) {\n    return mondayFirstWeekDays$.pipe(map(convertToSundayFirstWeekFormat), map(weekDays => [...weekDays.slice(this.firstDayOfWeekIndex), ...weekDays.slice(0, this.firstDayOfWeekIndex)]));\n  }\n  static {\n    this.ɵfac = function TuiOrderWeekDaysPipe_Factory(t) {\n      return new (t || TuiOrderWeekDaysPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiOrderWeekDays\",\n      type: TuiOrderWeekDaysPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiOrderWeekDaysPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiOrderWeekDays'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiOrderWeekDaysPipe };", "map": {"version": 3, "names": ["i0", "inject", "<PERSON><PERSON>", "TUI_FIRST_DAY_OF_WEEK", "map", "convertToSundayFirstWeekFormat", "weekDaysNames", "sundayIndex", "length", "slice", "TuiOrderWeekDaysPipe", "constructor", "firstDayOfWeekIndex", "transform", "mondayFirstWeekDays$", "pipe", "weekDays", "ɵfac", "TuiOrderWeekDaysPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-pipes-order-week-days.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { TUI_FIRST_DAY_OF_WEEK } from '@taiga-ui/core/tokens';\nimport { map } from 'rxjs';\n\nfunction convertToSundayFirstWeekFormat(weekDaysNames) {\n    const sundayIndex = weekDaysNames.length - 1;\n    return [weekDaysNames[sundayIndex] || '', ...weekDaysNames.slice(0, sundayIndex)];\n}\nclass TuiOrderWeekDaysPipe {\n    constructor() {\n        this.firstDayOfWeekIndex = inject(TUI_FIRST_DAY_OF_WEEK);\n    }\n    transform(mondayFirstWeekDays$) {\n        return mondayFirstWeekDays$.pipe(map(convertToSundayFirstWeekFormat), map((weekDays) => [\n            ...weekDays.slice(this.firstDayOfWeekIndex),\n            ...weekDays.slice(0, this.firstDayOfWeekIndex),\n        ]));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiOrderWeekDaysPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiOrderWeekDaysPipe, isStandalone: true, name: \"tuiOrderWeekDays\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiOrderWeekDaysPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiOrderWeekDays',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiOrderWeekDaysPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,IAAI,QAAQ,eAAe;AAC5C,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,GAAG,QAAQ,MAAM;AAE1B,SAASC,8BAA8BA,CAACC,aAAa,EAAE;EACnD,MAAMC,WAAW,GAAGD,aAAa,CAACE,MAAM,GAAG,CAAC;EAC5C,OAAO,CAACF,aAAa,CAACC,WAAW,CAAC,IAAI,EAAE,EAAE,GAAGD,aAAa,CAACG,KAAK,CAAC,CAAC,EAAEF,WAAW,CAAC,CAAC;AACrF;AACA,MAAMG,oBAAoB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,mBAAmB,GAAGX,MAAM,CAACE,qBAAqB,CAAC;EAC5D;EACAU,SAASA,CAACC,oBAAoB,EAAE;IAC5B,OAAOA,oBAAoB,CAACC,IAAI,CAACX,GAAG,CAACC,8BAA8B,CAAC,EAAED,GAAG,CAAEY,QAAQ,IAAK,CACpF,GAAGA,QAAQ,CAACP,KAAK,CAAC,IAAI,CAACG,mBAAmB,CAAC,EAC3C,GAAGI,QAAQ,CAACP,KAAK,CAAC,CAAC,EAAE,IAAI,CAACG,mBAAmB,CAAC,CACjD,CAAC,CAAC;EACP;EACA;IAAS,IAAI,CAACK,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFT,oBAAoB;IAAA,CAA8C;EAAE;EAC/K;IAAS,IAAI,CAACU,KAAK,kBAD8EpB,EAAE,CAAAqB,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMb,oBAAoB;MAAAc,IAAA;MAAAC,UAAA;IAAA,EAAiD;EAAE;AACpL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG1B,EAAE,CAAA2B,iBAAA,CAGXjB,oBAAoB,EAAc,CAAC;IACnHa,IAAI,EAAErB,IAAI;IACV0B,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASZ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}