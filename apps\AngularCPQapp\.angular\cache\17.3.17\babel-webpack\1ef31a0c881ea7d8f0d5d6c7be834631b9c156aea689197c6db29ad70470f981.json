{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Output } from '@angular/core';\nimport { BehaviorSubject, distinctUntilChanged, skip } from 'rxjs';\nclass TuiPresent {\n  constructor() {\n    this.visibility$ = new BehaviorSubject(false);\n    this.tuiPresentChange = this.visibility$.pipe(distinctUntilChanged(), skip(1));\n  }\n  ngOnDestroy() {\n    this.visibility$.next(false);\n  }\n  onAnimation(visibility) {\n    this.visibility$.next(visibility);\n  }\n  static {\n    this.ɵfac = function TuiPresent_Factory(t) {\n      return new (t || TuiPresent)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiPresent,\n      selectors: [[\"\", \"tuiPresentChange\", \"\"]],\n      hostVars: 2,\n      hostBindings: function TuiPresent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"animationcancel.self\", function TuiPresent_animationcancel_self_HostBindingHandler() {\n            return ctx.onAnimation(false);\n          })(\"animationstart.self\", function TuiPresent_animationstart_self_HostBindingHandler() {\n            return ctx.onAnimation(true);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"animation\", \"tuiPresent 1s infinite\");\n        }\n      },\n      outputs: {\n        tuiPresentChange: \"tuiPresentChange\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPresent, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiPresentChange]',\n      host: {\n        '[style.animation]': '\"tuiPresent 1s infinite\"',\n        '(animationcancel.self)': 'onAnimation(false)',\n        '(animationstart.self)': 'onAnimation(true)'\n      }\n    }]\n  }], null, {\n    tuiPresentChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPresent };", "map": {"version": 3, "names": ["i0", "Directive", "Output", "BehaviorSubject", "distinctUntilChanged", "skip", "TuiPresent", "constructor", "visibility$", "tuiPresentChange", "pipe", "ngOnDestroy", "next", "onAnimation", "visibility", "ɵfac", "TuiPresent_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostVars", "hostBindings", "TuiPresent_HostBindings", "rf", "ctx", "ɵɵlistener", "TuiPresent_animationcancel_self_HostBindingHandler", "TuiPresent_animationstart_self_HostBindingHandler", "ɵɵstyleProp", "outputs", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-present.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Output } from '@angular/core';\nimport { BehaviorSubject, distinctUntilChanged, skip } from 'rxjs';\n\nclass TuiPresent {\n    constructor() {\n        this.visibility$ = new BehaviorSubject(false);\n        this.tuiPresentChange = this.visibility$.pipe(distinctUntilChanged(), skip(1));\n    }\n    ngOnDestroy() {\n        this.visibility$.next(false);\n    }\n    onAnimation(visibility) {\n        this.visibility$.next(visibility);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPresent, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPresent, isStandalone: true, selector: \"[tuiPresentChange]\", outputs: { tuiPresentChange: \"tuiPresentChange\" }, host: { listeners: { \"animationcancel.self\": \"onAnimation(false)\", \"animationstart.self\": \"onAnimation(true)\" }, properties: { \"style.animation\": \"\\\"tuiPresent 1s infinite\\\"\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPresent, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiPresentChange]',\n                    host: {\n                        '[style.animation]': '\"tuiPresent 1s infinite\"',\n                        '(animationcancel.self)': 'onAnimation(false)',\n                        '(animationstart.self)': 'onAnimation(true)',\n                    },\n                }]\n        }], propDecorators: { tuiPresentChange: [{\n                type: Output\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPresent };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,eAAe,EAAEC,oBAAoB,EAAEC,IAAI,QAAQ,MAAM;AAElE,MAAMC,UAAU,CAAC;EACbC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,GAAG,IAAIL,eAAe,CAAC,KAAK,CAAC;IAC7C,IAAI,CAACM,gBAAgB,GAAG,IAAI,CAACD,WAAW,CAACE,IAAI,CAACN,oBAAoB,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAC,CAAC;EAClF;EACAM,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,WAAW,CAACI,IAAI,CAAC,KAAK,CAAC;EAChC;EACAC,WAAWA,CAACC,UAAU,EAAE;IACpB,IAAI,CAACN,WAAW,CAACI,IAAI,CAACE,UAAU,CAAC;EACrC;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,mBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFX,UAAU;IAAA,CAAmD;EAAE;EAC1K;IAAS,IAAI,CAACY,IAAI,kBAD+ElB,EAAE,CAAAmB,iBAAA;MAAAC,IAAA,EACJd,UAAU;MAAAe,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADRzB,EAAE,CAAA2B,UAAA,kCAAAC,mDAAA;YAAA,OACJF,GAAA,CAAAb,WAAA,CAAY,KAAK,CAAC;UAAA,CAAT,CAAC,iCAAAgB,kDAAA;YAAA,OAAVH,GAAA,CAAAb,WAAA,CAAY,IAAI,CAAC;UAAA,CAAR,CAAC;QAAA;QAAA,IAAAY,EAAA;UADRzB,EAAE,CAAA8B,WAAA,cACJ,wBAAS,CAAC;QAAA;MAAA;MAAAC,OAAA;QAAAtB,gBAAA;MAAA;MAAAuB,UAAA;IAAA,EAA4S;EAAE;AAC3Z;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGjC,EAAE,CAAAkC,iBAAA,CAGX5B,UAAU,EAAc,CAAC;IACzGc,IAAI,EAAEnB,SAAS;IACfkC,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,oBAAoB;MAC9BC,IAAI,EAAE;QACF,mBAAmB,EAAE,0BAA0B;QAC/C,wBAAwB,EAAE,oBAAoB;QAC9C,uBAAuB,EAAE;MAC7B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE5B,gBAAgB,EAAE,CAAC;MACjCW,IAAI,EAAElB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASI,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}