{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, Input } from '@angular/core';\nimport { tuiWithStyles, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiButtonOptionsProvider } from '@taiga-ui/core/components/button';\nimport { tuiLinkOptionsProvider } from '@taiga-ui/core/components/link';\nimport * as i2 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearanceOptionsProvider, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport * as i1 from '@taiga-ui/core/directives/icons';\nimport { TuiIcons, TuiWithIcons } from '@taiga-ui/core/directives/icons';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nconst ICONS = {\n  info: '@tui.info',\n  positive: '@tui.circle-check',\n  negative: '@tui.circle-x',\n  warning: '@tui.circle-alert',\n  neutral: '@tui.info',\n  /* TODO @deprecated remove in v5 */\n  success: '@tui.circle-check',\n  /* TODO @deprecated remove in v5 */\n  error: '@tui.circle-x'\n};\n/** Default values for the notification options. */\nconst TUI_NOTIFICATION_DEFAULT_OPTIONS = {\n  appearance: 'info',\n  icon: appearance => ICONS[appearance] ?? '',\n  size: 'l'\n};\n/**\n * Default parameters for notification alert component\n */\nconst [TUI_NOTIFICATION_OPTIONS, tuiNotificationOptionsProvider] = tuiCreateOptions(TUI_NOTIFICATION_DEFAULT_OPTIONS);\nclass TuiNotificationStyles {\n  static {\n    this.ɵfac = function TuiNotificationStyles_Factory(t) {\n      return new (t || TuiNotificationStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiNotificationStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-notification\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiNotificationStyles_Template(rf, ctx) {},\n      styles: [\"tui-notification,[tuiNotification]{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;position:relative;display:block;max-block-size:100%;color:var(--tui-text-primary);padding:1rem;font:var(--tui-font-text-m);line-height:1.5rem;border-radius:var(--tui-radius-l);box-sizing:border-box;text-align:start;text-decoration:none;border-inline-start:var(--t-start) solid transparent;border-inline-end:var(--t-end) solid transparent;--t-start: 0;--t-end: 0}tui-notification[style*=\\\"--t-icon-start:\\\"],[tuiNotification][style*=\\\"--t-icon-start:\\\"]{--t-start: 2rem}tui-notification[style*=\\\"--t-icon-end:\\\"],[tuiNotification][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.5rem}tui-notification:before,[tuiNotification]:before{position:absolute;left:-1rem}@supports (inset-inline-start: -1rem){tui-notification:before,[tuiNotification]:before{left:unset;inset-inline-start:-1rem}}tui-notification:after,[tuiNotification]:after{position:absolute;top:50%;transform:translateY(-50%);right:-.5rem;font-size:1rem;margin:0;margin-inline-end:-.25rem;margin-inline-start:auto;color:var(--tui-text-tertiary)!important}@supports (inset-inline-end: -.5rem){tui-notification:after,[tuiNotification]:after{right:unset;inset-inline-end:-.5rem}}tui-notification[data-size=s],[tuiNotification][data-size=s]{padding:.375rem .625rem;font:var(--tui-font-text-s);line-height:1.25rem;border-radius:var(--tui-radius-m)}tui-notification[data-size=s][style*=\\\"--t-icon-start:\\\"],[tuiNotification][data-size=s][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.5rem}tui-notification[data-size=s]:before,[tuiNotification][data-size=s]:before{top:.5rem;left:-.875rem;font-size:1rem}@supports (inset-inline-start: -.875rem){tui-notification[data-size=s]:before,[tuiNotification][data-size=s]:before{left:unset;inset-inline-start:-.875rem}}tui-notification[data-size=s]:after,[tuiNotification][data-size=s]:after{right:-.875rem}@supports (inset-inline-end: -.875rem){tui-notification[data-size=s]:after,[tuiNotification][data-size=s]:after{right:unset;inset-inline-end:-.875rem}}tui-notification[data-size=s] tui-icon,[tuiNotification][data-size=s] tui-icon{font-size:1rem}tui-notification[data-size=s] [tuiTitle],[tuiNotification][data-size=s] [tuiTitle]{font:var(--tui-font-text-s);font-weight:700}tui-notification[data-size=s] [tuiSubtitle],[tuiNotification][data-size=s] [tuiSubtitle]{font:var(--tui-font-text-s)}tui-notification[data-size=s] [tuiSubtitle]+*,[tuiNotification][data-size=s] [tuiSubtitle]+*{gap:1rem;margin:.375rem 0 .25rem}tui-notification[data-size=s]>[tuiIconButton],[tuiNotification][data-size=s]>[tuiIconButton]{top:0;right:0}@supports (inset-inline-end: 0){tui-notification[data-size=s]>[tuiIconButton],[tuiNotification][data-size=s]>[tuiIconButton]{right:unset;inset-inline-end:0}}tui-notification[data-size=m],[tuiNotification][data-size=m]{padding:.75rem;font:var(--tui-font-text-s);line-height:1.25rem;border-radius:var(--tui-radius-m)}tui-notification[data-size=m][style*=\\\"--t-icon-start:\\\"],[tuiNotification][data-size=m][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.625rem}tui-notification[data-size=m]:before,[tuiNotification][data-size=m]:before{left:-.875rem;font-size:1.25rem}@supports (inset-inline-start: -.875rem){tui-notification[data-size=m]:before,[tuiNotification][data-size=m]:before{left:unset;inset-inline-start:-.875rem}}tui-notification[data-size=m]:after,[tuiNotification][data-size=m]:after{right:-.75rem}@supports (inset-inline-end: -.75rem){tui-notification[data-size=m]:after,[tuiNotification][data-size=m]:after{right:unset;inset-inline-end:-.75rem}}tui-notification[data-size=m] tui-icon,[tuiNotification][data-size=m] tui-icon{font-size:1.25rem}tui-notification[data-size=m] [tuiTitle],[tuiNotification][data-size=m] [tuiTitle]{font:var(--tui-font-text-ui-m);font-weight:700}tui-notification[data-size=m] [tuiSubtitle],[tuiNotification][data-size=m] [tuiSubtitle]{font:var(--tui-font-text-s)}tui-notification[data-size=m] [tuiSubtitle]+*,[tuiNotification][data-size=m] [tuiSubtitle]+*{gap:1rem;margin:.625rem 0 .25rem}tui-notification[data-size=m]>[tuiIconButton],[tuiNotification][data-size=m]>[tuiIconButton]{top:.375rem;right:.5rem}@supports (inset-inline-end: .5rem){tui-notification[data-size=m]>[tuiIconButton],[tuiNotification][data-size=m]>[tuiIconButton]{right:unset;inset-inline-end:.5rem}}tui-notification [tuiTitle],[tuiNotification] [tuiTitle]{gap:.125rem;font:var(--tui-font-text-ui-l);font-weight:700}tui-notification [tuiSubtitle],[tuiNotification] [tuiSubtitle]{font:var(--tui-font-text-m)}tui-notification [tuiSubtitle]+*,[tuiNotification] [tuiSubtitle]+*{display:flex;align-items:center;gap:1.25rem;margin-top:.625rem;font:var(--tui-font-text-s)}tui-notification>[tuiIconButton],[tuiNotification]>[tuiIconButton]{position:absolute;top:.75rem;right:.75rem;box-shadow:none!important;background:transparent!important}@supports (inset-inline-end: .75rem){tui-notification>[tuiIconButton],[tuiNotification]>[tuiIconButton]{right:unset;inset-inline-end:.75rem}}[tuiNotification]{cursor:pointer}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiNotificationStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-notification'\n      },\n      styles: [\"tui-notification,[tuiNotification]{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;position:relative;display:block;max-block-size:100%;color:var(--tui-text-primary);padding:1rem;font:var(--tui-font-text-m);line-height:1.5rem;border-radius:var(--tui-radius-l);box-sizing:border-box;text-align:start;text-decoration:none;border-inline-start:var(--t-start) solid transparent;border-inline-end:var(--t-end) solid transparent;--t-start: 0;--t-end: 0}tui-notification[style*=\\\"--t-icon-start:\\\"],[tuiNotification][style*=\\\"--t-icon-start:\\\"]{--t-start: 2rem}tui-notification[style*=\\\"--t-icon-end:\\\"],[tuiNotification][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.5rem}tui-notification:before,[tuiNotification]:before{position:absolute;left:-1rem}@supports (inset-inline-start: -1rem){tui-notification:before,[tuiNotification]:before{left:unset;inset-inline-start:-1rem}}tui-notification:after,[tuiNotification]:after{position:absolute;top:50%;transform:translateY(-50%);right:-.5rem;font-size:1rem;margin:0;margin-inline-end:-.25rem;margin-inline-start:auto;color:var(--tui-text-tertiary)!important}@supports (inset-inline-end: -.5rem){tui-notification:after,[tuiNotification]:after{right:unset;inset-inline-end:-.5rem}}tui-notification[data-size=s],[tuiNotification][data-size=s]{padding:.375rem .625rem;font:var(--tui-font-text-s);line-height:1.25rem;border-radius:var(--tui-radius-m)}tui-notification[data-size=s][style*=\\\"--t-icon-start:\\\"],[tuiNotification][data-size=s][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.5rem}tui-notification[data-size=s]:before,[tuiNotification][data-size=s]:before{top:.5rem;left:-.875rem;font-size:1rem}@supports (inset-inline-start: -.875rem){tui-notification[data-size=s]:before,[tuiNotification][data-size=s]:before{left:unset;inset-inline-start:-.875rem}}tui-notification[data-size=s]:after,[tuiNotification][data-size=s]:after{right:-.875rem}@supports (inset-inline-end: -.875rem){tui-notification[data-size=s]:after,[tuiNotification][data-size=s]:after{right:unset;inset-inline-end:-.875rem}}tui-notification[data-size=s] tui-icon,[tuiNotification][data-size=s] tui-icon{font-size:1rem}tui-notification[data-size=s] [tuiTitle],[tuiNotification][data-size=s] [tuiTitle]{font:var(--tui-font-text-s);font-weight:700}tui-notification[data-size=s] [tuiSubtitle],[tuiNotification][data-size=s] [tuiSubtitle]{font:var(--tui-font-text-s)}tui-notification[data-size=s] [tuiSubtitle]+*,[tuiNotification][data-size=s] [tuiSubtitle]+*{gap:1rem;margin:.375rem 0 .25rem}tui-notification[data-size=s]>[tuiIconButton],[tuiNotification][data-size=s]>[tuiIconButton]{top:0;right:0}@supports (inset-inline-end: 0){tui-notification[data-size=s]>[tuiIconButton],[tuiNotification][data-size=s]>[tuiIconButton]{right:unset;inset-inline-end:0}}tui-notification[data-size=m],[tuiNotification][data-size=m]{padding:.75rem;font:var(--tui-font-text-s);line-height:1.25rem;border-radius:var(--tui-radius-m)}tui-notification[data-size=m][style*=\\\"--t-icon-start:\\\"],[tuiNotification][data-size=m][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.625rem}tui-notification[data-size=m]:before,[tuiNotification][data-size=m]:before{left:-.875rem;font-size:1.25rem}@supports (inset-inline-start: -.875rem){tui-notification[data-size=m]:before,[tuiNotification][data-size=m]:before{left:unset;inset-inline-start:-.875rem}}tui-notification[data-size=m]:after,[tuiNotification][data-size=m]:after{right:-.75rem}@supports (inset-inline-end: -.75rem){tui-notification[data-size=m]:after,[tuiNotification][data-size=m]:after{right:unset;inset-inline-end:-.75rem}}tui-notification[data-size=m] tui-icon,[tuiNotification][data-size=m] tui-icon{font-size:1.25rem}tui-notification[data-size=m] [tuiTitle],[tuiNotification][data-size=m] [tuiTitle]{font:var(--tui-font-text-ui-m);font-weight:700}tui-notification[data-size=m] [tuiSubtitle],[tuiNotification][data-size=m] [tuiSubtitle]{font:var(--tui-font-text-s)}tui-notification[data-size=m] [tuiSubtitle]+*,[tuiNotification][data-size=m] [tuiSubtitle]+*{gap:1rem;margin:.625rem 0 .25rem}tui-notification[data-size=m]>[tuiIconButton],[tuiNotification][data-size=m]>[tuiIconButton]{top:.375rem;right:.5rem}@supports (inset-inline-end: .5rem){tui-notification[data-size=m]>[tuiIconButton],[tuiNotification][data-size=m]>[tuiIconButton]{right:unset;inset-inline-end:.5rem}}tui-notification [tuiTitle],[tuiNotification] [tuiTitle]{gap:.125rem;font:var(--tui-font-text-ui-l);font-weight:700}tui-notification [tuiSubtitle],[tuiNotification] [tuiSubtitle]{font:var(--tui-font-text-m)}tui-notification [tuiSubtitle]+*,[tuiNotification] [tuiSubtitle]+*{display:flex;align-items:center;gap:1.25rem;margin-top:.625rem;font:var(--tui-font-text-s)}tui-notification>[tuiIconButton],[tuiNotification]>[tuiIconButton]{position:absolute;top:.75rem;right:.75rem;box-shadow:none!important;background:transparent!important}@supports (inset-inline-end: .75rem){tui-notification>[tuiIconButton],[tuiNotification]>[tuiIconButton]{right:unset;inset-inline-end:.75rem}}[tuiNotification]{cursor:pointer}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiNotification {\n  constructor() {\n    this.options = inject(TUI_NOTIFICATION_OPTIONS);\n    this.nothing = tuiWithStyles(TuiNotificationStyles);\n    this.icons = inject(TuiIcons);\n    this.appearance = this.options.appearance;\n    this.icon = this.options.icon;\n    this.size = this.options.size;\n  }\n  ngOnInit() {\n    this.refresh();\n  }\n  ngOnChanges() {\n    this.refresh();\n  }\n  refresh() {\n    this.icons.iconStart.set(tuiIsString(this.icon) ? this.icon : this.icon(this.appearance));\n  }\n  static {\n    this.ɵfac = function TuiNotification_Factory(t) {\n      return new (t || TuiNotification)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiNotification,\n      selectors: [[\"tui-notification\"], [\"a\", \"tuiNotification\", \"\"], [\"button\", \"tuiNotification\", \"\"]],\n      hostVars: 1,\n      hostBindings: function TuiNotification_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n        }\n      },\n      inputs: {\n        appearance: \"appearance\",\n        icon: \"icon\",\n        size: \"size\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAppearanceOptionsProvider(TUI_NOTIFICATION_OPTIONS), tuiLinkOptionsProvider({\n        appearance: '',\n        pseudo: true\n      }), tuiButtonOptionsProvider({\n        appearance: 'outline-grayscale',\n        size: 's'\n      })]), i0.ɵɵHostDirectivesFeature([i1.TuiWithIcons, i2.TuiWithAppearance]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiNotification, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'tui-notification,a[tuiNotification],button[tuiNotification]',\n      providers: [tuiAppearanceOptionsProvider(TUI_NOTIFICATION_OPTIONS), tuiLinkOptionsProvider({\n        appearance: '',\n        pseudo: true\n      }), tuiButtonOptionsProvider({\n        appearance: 'outline-grayscale',\n        size: 's'\n      })],\n      hostDirectives: [TuiWithIcons, TuiWithAppearance],\n      host: {\n        '[attr.data-size]': 'size'\n      }\n    }]\n  }], null, {\n    appearance: [{\n      type: Input\n    }],\n    icon: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_NOTIFICATION_DEFAULT_OPTIONS, TUI_NOTIFICATION_OPTIONS, TuiNotification, tuiNotificationOptionsProvider };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "Directive", "Input", "tuiWithStyles", "tuiIsString", "tuiButtonOptionsProvider", "tuiLinkOptionsProvider", "i2", "tuiAppearanceOptionsProvider", "TuiWithAppearance", "i1", "TuiIcons", "TuiWithIcons", "tuiCreateOptions", "ICONS", "info", "positive", "negative", "warning", "neutral", "success", "error", "TUI_NOTIFICATION_DEFAULT_OPTIONS", "appearance", "icon", "size", "TUI_NOTIFICATION_OPTIONS", "tuiNotificationOptionsProvider", "TuiNotificationStyles", "ɵfac", "TuiNotificationStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiNotificationStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiNotification", "constructor", "options", "nothing", "icons", "ngOnInit", "refresh", "ngOnChanges", "iconStart", "set", "TuiNotification_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiNotification_HostBindings", "ɵɵattribute", "inputs", "ɵɵProvidersFeature", "pseudo", "ɵɵHostDirectivesFeature", "ɵɵNgOnChangesFeature", "selector", "providers", "hostDirectives"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-components-notification.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, Input } from '@angular/core';\nimport { tuiWithStyles, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiButtonOptionsProvider } from '@taiga-ui/core/components/button';\nimport { tuiLinkOptionsProvider } from '@taiga-ui/core/components/link';\nimport * as i2 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearanceOptionsProvider, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport * as i1 from '@taiga-ui/core/directives/icons';\nimport { TuiIcons, TuiWithIcons } from '@taiga-ui/core/directives/icons';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\n\nconst ICONS = {\n    info: '@tui.info',\n    positive: '@tui.circle-check',\n    negative: '@tui.circle-x',\n    warning: '@tui.circle-alert',\n    neutral: '@tui.info',\n    /* TODO @deprecated remove in v5 */\n    success: '@tui.circle-check',\n    /* TODO @deprecated remove in v5 */\n    error: '@tui.circle-x',\n};\n/** Default values for the notification options. */\nconst TUI_NOTIFICATION_DEFAULT_OPTIONS = {\n    appearance: 'info',\n    icon: (appearance) => ICONS[appearance] ?? '',\n    size: 'l',\n};\n/**\n * Default parameters for notification alert component\n */\nconst [TUI_NOTIFICATION_OPTIONS, tuiNotificationOptionsProvider] = tuiCreateOptions(TUI_NOTIFICATION_DEFAULT_OPTIONS);\n\nclass TuiNotificationStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiNotificationStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiNotificationStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-notification\" }, ngImport: i0, template: '', isInline: true, styles: [\"tui-notification,[tuiNotification]{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;position:relative;display:block;max-block-size:100%;color:var(--tui-text-primary);padding:1rem;font:var(--tui-font-text-m);line-height:1.5rem;border-radius:var(--tui-radius-l);box-sizing:border-box;text-align:start;text-decoration:none;border-inline-start:var(--t-start) solid transparent;border-inline-end:var(--t-end) solid transparent;--t-start: 0;--t-end: 0}tui-notification[style*=\\\"--t-icon-start:\\\"],[tuiNotification][style*=\\\"--t-icon-start:\\\"]{--t-start: 2rem}tui-notification[style*=\\\"--t-icon-end:\\\"],[tuiNotification][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.5rem}tui-notification:before,[tuiNotification]:before{position:absolute;left:-1rem}@supports (inset-inline-start: -1rem){tui-notification:before,[tuiNotification]:before{left:unset;inset-inline-start:-1rem}}tui-notification:after,[tuiNotification]:after{position:absolute;top:50%;transform:translateY(-50%);right:-.5rem;font-size:1rem;margin:0;margin-inline-end:-.25rem;margin-inline-start:auto;color:var(--tui-text-tertiary)!important}@supports (inset-inline-end: -.5rem){tui-notification:after,[tuiNotification]:after{right:unset;inset-inline-end:-.5rem}}tui-notification[data-size=s],[tuiNotification][data-size=s]{padding:.375rem .625rem;font:var(--tui-font-text-s);line-height:1.25rem;border-radius:var(--tui-radius-m)}tui-notification[data-size=s][style*=\\\"--t-icon-start:\\\"],[tuiNotification][data-size=s][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.5rem}tui-notification[data-size=s]:before,[tuiNotification][data-size=s]:before{top:.5rem;left:-.875rem;font-size:1rem}@supports (inset-inline-start: -.875rem){tui-notification[data-size=s]:before,[tuiNotification][data-size=s]:before{left:unset;inset-inline-start:-.875rem}}tui-notification[data-size=s]:after,[tuiNotification][data-size=s]:after{right:-.875rem}@supports (inset-inline-end: -.875rem){tui-notification[data-size=s]:after,[tuiNotification][data-size=s]:after{right:unset;inset-inline-end:-.875rem}}tui-notification[data-size=s] tui-icon,[tuiNotification][data-size=s] tui-icon{font-size:1rem}tui-notification[data-size=s] [tuiTitle],[tuiNotification][data-size=s] [tuiTitle]{font:var(--tui-font-text-s);font-weight:700}tui-notification[data-size=s] [tuiSubtitle],[tuiNotification][data-size=s] [tuiSubtitle]{font:var(--tui-font-text-s)}tui-notification[data-size=s] [tuiSubtitle]+*,[tuiNotification][data-size=s] [tuiSubtitle]+*{gap:1rem;margin:.375rem 0 .25rem}tui-notification[data-size=s]>[tuiIconButton],[tuiNotification][data-size=s]>[tuiIconButton]{top:0;right:0}@supports (inset-inline-end: 0){tui-notification[data-size=s]>[tuiIconButton],[tuiNotification][data-size=s]>[tuiIconButton]{right:unset;inset-inline-end:0}}tui-notification[data-size=m],[tuiNotification][data-size=m]{padding:.75rem;font:var(--tui-font-text-s);line-height:1.25rem;border-radius:var(--tui-radius-m)}tui-notification[data-size=m][style*=\\\"--t-icon-start:\\\"],[tuiNotification][data-size=m][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.625rem}tui-notification[data-size=m]:before,[tuiNotification][data-size=m]:before{left:-.875rem;font-size:1.25rem}@supports (inset-inline-start: -.875rem){tui-notification[data-size=m]:before,[tuiNotification][data-size=m]:before{left:unset;inset-inline-start:-.875rem}}tui-notification[data-size=m]:after,[tuiNotification][data-size=m]:after{right:-.75rem}@supports (inset-inline-end: -.75rem){tui-notification[data-size=m]:after,[tuiNotification][data-size=m]:after{right:unset;inset-inline-end:-.75rem}}tui-notification[data-size=m] tui-icon,[tuiNotification][data-size=m] tui-icon{font-size:1.25rem}tui-notification[data-size=m] [tuiTitle],[tuiNotification][data-size=m] [tuiTitle]{font:var(--tui-font-text-ui-m);font-weight:700}tui-notification[data-size=m] [tuiSubtitle],[tuiNotification][data-size=m] [tuiSubtitle]{font:var(--tui-font-text-s)}tui-notification[data-size=m] [tuiSubtitle]+*,[tuiNotification][data-size=m] [tuiSubtitle]+*{gap:1rem;margin:.625rem 0 .25rem}tui-notification[data-size=m]>[tuiIconButton],[tuiNotification][data-size=m]>[tuiIconButton]{top:.375rem;right:.5rem}@supports (inset-inline-end: .5rem){tui-notification[data-size=m]>[tuiIconButton],[tuiNotification][data-size=m]>[tuiIconButton]{right:unset;inset-inline-end:.5rem}}tui-notification [tuiTitle],[tuiNotification] [tuiTitle]{gap:.125rem;font:var(--tui-font-text-ui-l);font-weight:700}tui-notification [tuiSubtitle],[tuiNotification] [tuiSubtitle]{font:var(--tui-font-text-m)}tui-notification [tuiSubtitle]+*,[tuiNotification] [tuiSubtitle]+*{display:flex;align-items:center;gap:1.25rem;margin-top:.625rem;font:var(--tui-font-text-s)}tui-notification>[tuiIconButton],[tuiNotification]>[tuiIconButton]{position:absolute;top:.75rem;right:.75rem;box-shadow:none!important;background:transparent!important}@supports (inset-inline-end: .75rem){tui-notification>[tuiIconButton],[tuiNotification]>[tuiIconButton]{right:unset;inset-inline-end:.75rem}}[tuiNotification]{cursor:pointer}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiNotificationStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-notification',\n                    }, styles: [\"tui-notification,[tuiNotification]{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;position:relative;display:block;max-block-size:100%;color:var(--tui-text-primary);padding:1rem;font:var(--tui-font-text-m);line-height:1.5rem;border-radius:var(--tui-radius-l);box-sizing:border-box;text-align:start;text-decoration:none;border-inline-start:var(--t-start) solid transparent;border-inline-end:var(--t-end) solid transparent;--t-start: 0;--t-end: 0}tui-notification[style*=\\\"--t-icon-start:\\\"],[tuiNotification][style*=\\\"--t-icon-start:\\\"]{--t-start: 2rem}tui-notification[style*=\\\"--t-icon-end:\\\"],[tuiNotification][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.5rem}tui-notification:before,[tuiNotification]:before{position:absolute;left:-1rem}@supports (inset-inline-start: -1rem){tui-notification:before,[tuiNotification]:before{left:unset;inset-inline-start:-1rem}}tui-notification:after,[tuiNotification]:after{position:absolute;top:50%;transform:translateY(-50%);right:-.5rem;font-size:1rem;margin:0;margin-inline-end:-.25rem;margin-inline-start:auto;color:var(--tui-text-tertiary)!important}@supports (inset-inline-end: -.5rem){tui-notification:after,[tuiNotification]:after{right:unset;inset-inline-end:-.5rem}}tui-notification[data-size=s],[tuiNotification][data-size=s]{padding:.375rem .625rem;font:var(--tui-font-text-s);line-height:1.25rem;border-radius:var(--tui-radius-m)}tui-notification[data-size=s][style*=\\\"--t-icon-start:\\\"],[tuiNotification][data-size=s][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.5rem}tui-notification[data-size=s]:before,[tuiNotification][data-size=s]:before{top:.5rem;left:-.875rem;font-size:1rem}@supports (inset-inline-start: -.875rem){tui-notification[data-size=s]:before,[tuiNotification][data-size=s]:before{left:unset;inset-inline-start:-.875rem}}tui-notification[data-size=s]:after,[tuiNotification][data-size=s]:after{right:-.875rem}@supports (inset-inline-end: -.875rem){tui-notification[data-size=s]:after,[tuiNotification][data-size=s]:after{right:unset;inset-inline-end:-.875rem}}tui-notification[data-size=s] tui-icon,[tuiNotification][data-size=s] tui-icon{font-size:1rem}tui-notification[data-size=s] [tuiTitle],[tuiNotification][data-size=s] [tuiTitle]{font:var(--tui-font-text-s);font-weight:700}tui-notification[data-size=s] [tuiSubtitle],[tuiNotification][data-size=s] [tuiSubtitle]{font:var(--tui-font-text-s)}tui-notification[data-size=s] [tuiSubtitle]+*,[tuiNotification][data-size=s] [tuiSubtitle]+*{gap:1rem;margin:.375rem 0 .25rem}tui-notification[data-size=s]>[tuiIconButton],[tuiNotification][data-size=s]>[tuiIconButton]{top:0;right:0}@supports (inset-inline-end: 0){tui-notification[data-size=s]>[tuiIconButton],[tuiNotification][data-size=s]>[tuiIconButton]{right:unset;inset-inline-end:0}}tui-notification[data-size=m],[tuiNotification][data-size=m]{padding:.75rem;font:var(--tui-font-text-s);line-height:1.25rem;border-radius:var(--tui-radius-m)}tui-notification[data-size=m][style*=\\\"--t-icon-start:\\\"],[tuiNotification][data-size=m][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.625rem}tui-notification[data-size=m]:before,[tuiNotification][data-size=m]:before{left:-.875rem;font-size:1.25rem}@supports (inset-inline-start: -.875rem){tui-notification[data-size=m]:before,[tuiNotification][data-size=m]:before{left:unset;inset-inline-start:-.875rem}}tui-notification[data-size=m]:after,[tuiNotification][data-size=m]:after{right:-.75rem}@supports (inset-inline-end: -.75rem){tui-notification[data-size=m]:after,[tuiNotification][data-size=m]:after{right:unset;inset-inline-end:-.75rem}}tui-notification[data-size=m] tui-icon,[tuiNotification][data-size=m] tui-icon{font-size:1.25rem}tui-notification[data-size=m] [tuiTitle],[tuiNotification][data-size=m] [tuiTitle]{font:var(--tui-font-text-ui-m);font-weight:700}tui-notification[data-size=m] [tuiSubtitle],[tuiNotification][data-size=m] [tuiSubtitle]{font:var(--tui-font-text-s)}tui-notification[data-size=m] [tuiSubtitle]+*,[tuiNotification][data-size=m] [tuiSubtitle]+*{gap:1rem;margin:.625rem 0 .25rem}tui-notification[data-size=m]>[tuiIconButton],[tuiNotification][data-size=m]>[tuiIconButton]{top:.375rem;right:.5rem}@supports (inset-inline-end: .5rem){tui-notification[data-size=m]>[tuiIconButton],[tuiNotification][data-size=m]>[tuiIconButton]{right:unset;inset-inline-end:.5rem}}tui-notification [tuiTitle],[tuiNotification] [tuiTitle]{gap:.125rem;font:var(--tui-font-text-ui-l);font-weight:700}tui-notification [tuiSubtitle],[tuiNotification] [tuiSubtitle]{font:var(--tui-font-text-m)}tui-notification [tuiSubtitle]+*,[tuiNotification] [tuiSubtitle]+*{display:flex;align-items:center;gap:1.25rem;margin-top:.625rem;font:var(--tui-font-text-s)}tui-notification>[tuiIconButton],[tuiNotification]>[tuiIconButton]{position:absolute;top:.75rem;right:.75rem;box-shadow:none!important;background:transparent!important}@supports (inset-inline-end: .75rem){tui-notification>[tuiIconButton],[tuiNotification]>[tuiIconButton]{right:unset;inset-inline-end:.75rem}}[tuiNotification]{cursor:pointer}\\n\"] }]\n        }] });\nclass TuiNotification {\n    constructor() {\n        this.options = inject(TUI_NOTIFICATION_OPTIONS);\n        this.nothing = tuiWithStyles(TuiNotificationStyles);\n        this.icons = inject(TuiIcons);\n        this.appearance = this.options.appearance;\n        this.icon = this.options.icon;\n        this.size = this.options.size;\n    }\n    ngOnInit() {\n        this.refresh();\n    }\n    ngOnChanges() {\n        this.refresh();\n    }\n    refresh() {\n        this.icons.iconStart.set(tuiIsString(this.icon) ? this.icon : this.icon(this.appearance));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiNotification, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiNotification, isStandalone: true, selector: \"tui-notification,a[tuiNotification],button[tuiNotification]\", inputs: { appearance: \"appearance\", icon: \"icon\", size: \"size\" }, host: { properties: { \"attr.data-size\": \"size\" } }, providers: [\n            tuiAppearanceOptionsProvider(TUI_NOTIFICATION_OPTIONS),\n            tuiLinkOptionsProvider({\n                appearance: '',\n                pseudo: true,\n            }),\n            tuiButtonOptionsProvider({\n                appearance: 'outline-grayscale',\n                size: 's',\n            }),\n        ], usesOnChanges: true, hostDirectives: [{ directive: i1.TuiWithIcons }, { directive: i2.TuiWithAppearance }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiNotification, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-notification,a[tuiNotification],button[tuiNotification]',\n                    providers: [\n                        tuiAppearanceOptionsProvider(TUI_NOTIFICATION_OPTIONS),\n                        tuiLinkOptionsProvider({\n                            appearance: '',\n                            pseudo: true,\n                        }),\n                        tuiButtonOptionsProvider({\n                            appearance: 'outline-grayscale',\n                            size: 's',\n                        }),\n                    ],\n                    hostDirectives: [TuiWithIcons, TuiWithAppearance],\n                    host: {\n                        '[attr.data-size]': 'size',\n                    },\n                }]\n        }], propDecorators: { appearance: [{\n                type: Input\n            }], icon: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_NOTIFICATION_DEFAULT_OPTIONS, TUI_NOTIFICATION_OPTIONS, TuiNotification, tuiNotificationOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAC/G,SAASC,aAAa,EAAEC,WAAW,QAAQ,mCAAmC;AAC9E,SAASC,wBAAwB,QAAQ,kCAAkC;AAC3E,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,OAAO,KAAKC,EAAE,MAAM,sCAAsC;AAC1D,SAASC,4BAA4B,EAAEC,iBAAiB,QAAQ,sCAAsC;AACtG,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iCAAiC;AACxE,SAASC,gBAAgB,QAAQ,wBAAwB;AAEzD,MAAMC,KAAK,GAAG;EACVC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAE,eAAe;EACzBC,OAAO,EAAE,mBAAmB;EAC5BC,OAAO,EAAE,WAAW;EACpB;EACAC,OAAO,EAAE,mBAAmB;EAC5B;EACAC,KAAK,EAAE;AACX,CAAC;AACD;AACA,MAAMC,gCAAgC,GAAG;EACrCC,UAAU,EAAE,MAAM;EAClBC,IAAI,EAAGD,UAAU,IAAKT,KAAK,CAACS,UAAU,CAAC,IAAI,EAAE;EAC7CE,IAAI,EAAE;AACV,CAAC;AACD;AACA;AACA;AACA,MAAM,CAACC,wBAAwB,EAAEC,8BAA8B,CAAC,GAAGd,gBAAgB,CAACS,gCAAgC,CAAC;AAErH,MAAMM,qBAAqB,CAAC;EACxB;IAAS,IAAI,CAACC,IAAI,YAAAC,8BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,qBAAqB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAACI,IAAI,kBAD+EpC,EAAE,CAAAqC,iBAAA;MAAAC,IAAA,EACJN,qBAAqB;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADnB1C,EAAE,CAAA2C,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC6sK;EAAE;AACtzK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGrD,EAAE,CAAAsD,iBAAA,CAGXtB,qBAAqB,EAAc,CAAC;IACpHM,IAAI,EAAErC,SAAS;IACfsD,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAEjD,iBAAiB,CAACsD,IAAI;MAAEJ,eAAe,EAAEjD,uBAAuB,CAACsD,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,u8JAAu8J;IAAE,CAAC;EACl+J,CAAC,CAAC;AAAA;AACV,MAAMU,eAAe,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG1D,MAAM,CAAC0B,wBAAwB,CAAC;IAC/C,IAAI,CAACiC,OAAO,GAAGxD,aAAa,CAACyB,qBAAqB,CAAC;IACnD,IAAI,CAACgC,KAAK,GAAG5D,MAAM,CAACW,QAAQ,CAAC;IAC7B,IAAI,CAACY,UAAU,GAAG,IAAI,CAACmC,OAAO,CAACnC,UAAU;IACzC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACkC,OAAO,CAAClC,IAAI;IAC7B,IAAI,CAACC,IAAI,GAAG,IAAI,CAACiC,OAAO,CAACjC,IAAI;EACjC;EACAoC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,OAAO,CAAC,CAAC;EAClB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,OAAO,CAAC,CAAC;EAClB;EACAA,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,KAAK,CAACI,SAAS,CAACC,GAAG,CAAC7D,WAAW,CAAC,IAAI,CAACoB,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,IAAI,CAACD,UAAU,CAAC,CAAC;EAC7F;EACA;IAAS,IAAI,CAACM,IAAI,YAAAqC,wBAAAnC,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACW,IAAI,kBA5B+EvE,EAAE,CAAAwE,iBAAA;MAAAlC,IAAA,EA4BJsB,eAAe;MAAArB,SAAA;MAAAkC,QAAA;MAAAC,YAAA,WAAAC,6BAAA3B,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5BbhD,EAAE,CAAA4E,WAAA,cAAA3B,GAAA,CAAApB,IAAA;QAAA;MAAA;MAAAgD,MAAA;QAAAlD,UAAA;QAAAC,IAAA;QAAAC,IAAA;MAAA;MAAAY,UAAA;MAAAC,QAAA,GAAF1C,EAAE,CAAA8E,kBAAA,CA4B2O,CACtUlE,4BAA4B,CAACkB,wBAAwB,CAAC,EACtDpB,sBAAsB,CAAC;QACnBiB,UAAU,EAAE,EAAE;QACdoD,MAAM,EAAE;MACZ,CAAC,CAAC,EACFtE,wBAAwB,CAAC;QACrBkB,UAAU,EAAE,mBAAmB;QAC/BE,IAAI,EAAE;MACV,CAAC,CAAC,CACL,GAtC4F7B,EAAE,CAAAgF,uBAAA,EAsCzClE,EAAE,CAACE,YAAY,EAAiBL,EAAE,CAACE,iBAAiB,IAtCbb,EAAE,CAAAiF,oBAAA;IAAA,EAsC+B;EAAE;AACxI;AACA;EAAA,QAAA5B,SAAA,oBAAAA,SAAA,KAxCqGrD,EAAE,CAAAsD,iBAAA,CAwCXM,eAAe,EAAc,CAAC;IAC9GtB,IAAI,EAAEjC,SAAS;IACfkD,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChByC,QAAQ,EAAE,6DAA6D;MACvEC,SAAS,EAAE,CACPvE,4BAA4B,CAACkB,wBAAwB,CAAC,EACtDpB,sBAAsB,CAAC;QACnBiB,UAAU,EAAE,EAAE;QACdoD,MAAM,EAAE;MACZ,CAAC,CAAC,EACFtE,wBAAwB,CAAC;QACrBkB,UAAU,EAAE,mBAAmB;QAC/BE,IAAI,EAAE;MACV,CAAC,CAAC,CACL;MACDuD,cAAc,EAAE,CAACpE,YAAY,EAAEH,iBAAiB,CAAC;MACjD6C,IAAI,EAAE;QACF,kBAAkB,EAAE;MACxB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE/B,UAAU,EAAE,CAAC;MAC3BW,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEsB,IAAI,EAAE,CAAC;MACPU,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEuB,IAAI,EAAE,CAAC;MACPS,IAAI,EAAEhC;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASoB,gCAAgC,EAAEI,wBAAwB,EAAE8B,eAAe,EAAE7B,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}