{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\nclass TuiCompass {\n  constructor() {\n    this.degrees = NaN;\n  }\n  static {\n    this.ɵfac = function TuiCompass_Factory(t) {\n      return new (t || TuiCompass)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiCompass,\n      selectors: [[\"tui-compass\"]],\n      hostVars: 2,\n      hostBindings: function TuiCompass_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-degrees\", ctx.degrees, \"deg\");\n        }\n      },\n      inputs: {\n        degrees: \"degrees\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiCompass_Template(rf, ctx) {},\n      styles: [\"tui-compass{position:relative;display:inline-block;color:var(--tui-background-accent-1);border-radius:50%;inline-size:1rem;block-size:1rem;border:.1875rem solid var(--tui-background-base);box-shadow:0 0 .1875rem #0000004d;transform-style:preserve-3d}tui-compass:before{content:\\\"\\\";position:absolute;top:0;left:.125rem;-webkit-mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M0 8.99993C4 8 8 8 12 8.99993C9.91509 5.73239 8.5 3 6 0C3.5 3 2 5.5 0 8.99993Z\\\"/></svg>');mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M0 8.99993C4 8 8 8 12 8.99993C9.91509 5.73239 8.5 3 6 0C3.5 3 2 5.5 0 8.99993Z\\\"/></svg>');transform:rotate(var(--t-degrees)) translateY(-.625rem) translateZ(-1rem);inline-size:.75rem;block-size:1rem;background:currentColor}tui-compass:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";border-radius:inherit;background:currentColor linear-gradient(-45deg,rgba(0,0,0,.2),transparent)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCompass, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-compass',\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[style.--t-degrees.deg]': 'degrees'\n      },\n      styles: [\"tui-compass{position:relative;display:inline-block;color:var(--tui-background-accent-1);border-radius:50%;inline-size:1rem;block-size:1rem;border:.1875rem solid var(--tui-background-base);box-shadow:0 0 .1875rem #0000004d;transform-style:preserve-3d}tui-compass:before{content:\\\"\\\";position:absolute;top:0;left:.125rem;-webkit-mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M0 8.99993C4 8 8 8 12 8.99993C9.91509 5.73239 8.5 3 6 0C3.5 3 2 5.5 0 8.99993Z\\\"/></svg>');mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M0 8.99993C4 8 8 8 12 8.99993C9.91509 5.73239 8.5 3 6 0C3.5 3 2 5.5 0 8.99993Z\\\"/></svg>');transform:rotate(var(--t-degrees)) translateY(-.625rem) translateZ(-1rem);inline-size:.75rem;block-size:1rem;background:currentColor}tui-compass:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";border-radius:inherit;background:currentColor linear-gradient(-45deg,rgba(0,0,0,.2),transparent)}\\n\"]\n    }]\n  }], null, {\n    degrees: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiCompass };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "TuiCompass", "constructor", "degrees", "NaN", "ɵfac", "TuiCompass_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "TuiCompass_HostBindings", "rf", "ctx", "ɵɵstyleProp", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiCompass_Template", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-compass.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\n\nclass TuiCompass {\n    constructor() {\n        this.degrees = NaN;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCompass, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCompass, isStandalone: true, selector: \"tui-compass\", inputs: { degrees: \"degrees\" }, host: { properties: { \"style.--t-degrees.deg\": \"degrees\" } }, ngImport: i0, template: '', isInline: true, styles: [\"tui-compass{position:relative;display:inline-block;color:var(--tui-background-accent-1);border-radius:50%;inline-size:1rem;block-size:1rem;border:.1875rem solid var(--tui-background-base);box-shadow:0 0 .1875rem #0000004d;transform-style:preserve-3d}tui-compass:before{content:\\\"\\\";position:absolute;top:0;left:.125rem;-webkit-mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M0 8.99993C4 8 8 8 12 8.99993C9.91509 5.73239 8.5 3 6 0C3.5 3 2 5.5 0 8.99993Z\\\"/></svg>');mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M0 8.99993C4 8 8 8 12 8.99993C9.91509 5.73239 8.5 3 6 0C3.5 3 2 5.5 0 8.99993Z\\\"/></svg>');transform:rotate(var(--t-degrees)) translateY(-.625rem) translateZ(-1rem);inline-size:.75rem;block-size:1rem;background:currentColor}tui-compass:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";border-radius:inherit;background:currentColor linear-gradient(-45deg,rgba(0,0,0,.2),transparent)}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCompass, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-compass', template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[style.--t-degrees.deg]': 'degrees',\n                    }, styles: [\"tui-compass{position:relative;display:inline-block;color:var(--tui-background-accent-1);border-radius:50%;inline-size:1rem;block-size:1rem;border:.1875rem solid var(--tui-background-base);box-shadow:0 0 .1875rem #0000004d;transform-style:preserve-3d}tui-compass:before{content:\\\"\\\";position:absolute;top:0;left:.125rem;-webkit-mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M0 8.99993C4 8 8 8 12 8.99993C9.91509 5.73239 8.5 3 6 0C3.5 3 2 5.5 0 8.99993Z\\\"/></svg>');mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M0 8.99993C4 8 8 8 12 8.99993C9.91509 5.73239 8.5 3 6 0C3.5 3 2 5.5 0 8.99993Z\\\"/></svg>');transform:rotate(var(--t-degrees)) translateY(-.625rem) translateZ(-1rem);inline-size:.75rem;block-size:1rem;background:currentColor}tui-compass:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";border-radius:inherit;background:currentColor linear-gradient(-45deg,rgba(0,0,0,.2),transparent)}\\n\"] }]\n        }], propDecorators: { degrees: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiCompass };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,QAAQ,eAAe;AAE5F,MAAMC,UAAU,CAAC;EACbC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGC,GAAG;EACtB;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,mBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFN,UAAU;IAAA,CAAmD;EAAE;EAC1K;IAAS,IAAI,CAACO,IAAI,kBAD+EZ,EAAE,CAAAa,iBAAA;MAAAC,IAAA,EACJT,UAAU;MAAAU,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADRnB,EAAE,CAAAqB,WAAA,gBAAAD,GAAA,CAAAb,OAAA,OACK,CAAC;QAAA;MAAA;MAAAe,MAAA;QAAAf,OAAA;MAAA;MAAAgB,UAAA;MAAAC,QAAA,GADRxB,EAAE,CAAAyB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,oBAAAV,EAAA,EAAAC,GAAA;MAAAU,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC8wC;EAAE;AACv3C;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGjC,EAAE,CAAAkC,iBAAA,CAGX7B,UAAU,EAAc,CAAC;IACzGS,IAAI,EAAEb,SAAS;IACfkC,IAAI,EAAE,CAAC;MAAEZ,UAAU,EAAE,IAAI;MAAEa,QAAQ,EAAE,aAAa;MAAER,QAAQ,EAAE,EAAE;MAAEG,aAAa,EAAE7B,iBAAiB,CAACmC,IAAI;MAAEL,eAAe,EAAE7B,uBAAuB,CAACmC,MAAM;MAAEC,IAAI,EAAE;QACpJ,yBAAyB,EAAE;MAC/B,CAAC;MAAET,MAAM,EAAE,CAAC,o+BAAo+B;IAAE,CAAC;EAC//B,CAAC,CAAC,QAAkB;IAAEvB,OAAO,EAAE,CAAC;MACxBO,IAAI,EAAEV;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}