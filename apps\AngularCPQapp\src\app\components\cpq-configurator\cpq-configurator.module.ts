import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { CpqConfiguratorComponent } from './cpq-configurator.component';
import { ProductTreeModule } from '../product-tree/product-tree.module';
import { TuiTreeComponent } from '@taiga-ui/kit';
import { TreeContentComponent } from './tree-content.component';

@NgModule({
  declarations: [CpqConfiguratorComponent],
  imports: [
    CommonModule,
    FormsModule,
    HttpClientModule,
    ProductTreeModule
  ],
  exports: [CpqConfiguratorComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CpqConfiguratorModule { }