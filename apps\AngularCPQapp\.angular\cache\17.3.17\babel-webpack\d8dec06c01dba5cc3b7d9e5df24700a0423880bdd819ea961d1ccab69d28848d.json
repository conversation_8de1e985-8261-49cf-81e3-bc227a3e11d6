{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Pi<PERSON> } from '@angular/core';\nclass TuiStringifyPipe {\n  transform(key) {\n    return value => String(value[key] ?? '');\n  }\n  static {\n    this.ɵfac = function TuiStringifyPipe_Factory(t) {\n      return new (t || TuiStringifyPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiStringify\",\n      type: TuiStringifyPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiStringifyPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiStringify'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiStringifyPipe };", "map": {"version": 3, "names": ["i0", "<PERSON><PERSON>", "TuiStringifyPipe", "transform", "key", "value", "String", "ɵfac", "TuiStringifyPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-pipes-stringify.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Pi<PERSON> } from '@angular/core';\n\nclass TuiStringifyPipe {\n    transform(key) {\n        return (value) => String(value[key] ?? '');\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStringifyPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStringifyPipe, isStandalone: true, name: \"tuiStringify\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStringifyPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiStringify',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiStringifyPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,IAAI,QAAQ,eAAe;AAEpC,MAAMC,gBAAgB,CAAC;EACnBC,SAASA,CAACC,GAAG,EAAE;IACX,OAAQC,KAAK,IAAKC,MAAM,CAACD,KAAK,CAACD,GAAG,CAAC,IAAI,EAAE,CAAC;EAC9C;EACA;IAAS,IAAI,CAACG,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFP,gBAAgB;IAAA,CAA8C;EAAE;EAC3K;IAAS,IAAI,CAACQ,KAAK,kBAD8EV,EAAE,CAAAW,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMX,gBAAgB;MAAAY,IAAA;MAAAC,UAAA;IAAA,EAA6C;EAAE;AAC5K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGhB,EAAE,CAAAiB,iBAAA,CAGXf,gBAAgB,EAAc,CAAC;IAC/GW,IAAI,EAAEZ,IAAI;IACViB,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASV,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}