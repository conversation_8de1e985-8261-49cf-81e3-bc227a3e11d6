{"ast": null, "code": "import { DOCUMENT, Async<PERSON>ipe, NgI<PERSON>, NgForOf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Injectable, computed, Component, ChangeDetectionStrategy, Directive, assertInInjectionContext, INJECTOR } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { TUI_TRUE_HANDLER } from '@taiga-ui/cdk/constants';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated, TuiAnimatedParent } from '@taiga-ui/cdk/directives/animated';\nimport { TuiAutoFocus } from '@taiga-ui/cdk/directives/auto-focus';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { TuiBreakpointService } from '@taiga-ui/core/services';\nimport { TUI_CLOSE_WORD, TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { injectContext, PolymorpheusOutlet, PolymorpheusComponent } from '@taiga-ui/polymorpheus';\nimport { BehaviorSubject, EMPTY, Observable, merge, filter, switchMap, take, map, isObservable, of, Subject, exhaustMap } from 'rxjs';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { WA_WINDOW } from '@ng-web-apis/common';\nimport { tuiCloseWatcher, tuiZonefull, tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement, tuiGetActualTarget, tuiIsElement, tuiContainsOrAfter } from '@taiga-ui/cdk/utils/dom';\nimport { tuiGetViewportWidth } from '@taiga-ui/core/utils';\nimport { TuiPopoverDirective } from '@taiga-ui/cdk/directives/popover';\nimport { TuiPopoverService, tuiAsPopover } from '@taiga-ui/cdk/services';\nimport { TuiFocusTrap } from '@taiga-ui/cdk/directives/focus-trap';\nimport { TuiScrollControls, TuiScrollRef } from '@taiga-ui/core/components/scrollbar';\nfunction TuiDialogComponent_header_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r1, \" \");\n  }\n}\nfunction TuiDialogComponent_header_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"header\", 6);\n    i0.ɵɵtemplate(1, TuiDialogComponent_header_0_ng_container_1_Template, 2, 1, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r1.header)(\"polymorpheusOutletContext\", ctx_r1.context);\n  }\n}\nfunction TuiDialogComponent_ng_container_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function TuiDialogComponent_ng_container_4_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.context.$implicit.complete());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.context.data == null ? null : ctx_r1.context.data.button) || \"OK\", \" \");\n  }\n}\nfunction TuiDialogComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 7);\n    i0.ɵɵtemplate(2, TuiDialogComponent_ng_container_4_div_2_Template, 3, 1, \"div\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r4 = ctx.polymorpheusOutlet;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", text_r4, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.context.closeable || ctx_r1.context.dismissible);\n  }\n}\nfunction TuiDialogComponent_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function TuiDialogComponent_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.close$.next());\n    })(\"mousedown.prevent.zoneless\", function TuiDialogComponent_button_6_Template_button_mousedown_prevent_zoneless_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      return i0.ɵɵresetView(0);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"border-radius\", 100, \"%\");\n    i0.ɵɵproperty(\"appearance\", ctx_r1.isMobile() ? \"icon\" : \"neutral\")(\"iconStart\", ctx_r1.icons.close)(\"size\", ctx_r1.isMobile() ? \"xs\" : \"s\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 6, ctx_r1.closeWord$), \"\\n\");\n  }\n}\nfunction TuiDialogs_section_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiDialogs_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 2);\n    i0.ɵɵtemplate(1, TuiDialogs_section_1_ng_container_1_Template, 1, 0, \"ng-container\", 3);\n    i0.ɵɵelement(2, \"tui-scroll-controls\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵattribute(\"aria-labelledby\", item_r1.id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", item_r1.component)(\"polymorpheusOutletContext\", item_r1);\n  }\n}\nconst TUI_DIALOGS = tuiCreateToken(new BehaviorSubject([]));\nconst TUI_DIALOG_DEFAULT_OPTIONS = {\n  appearance: '',\n  size: 'm',\n  required: false,\n  closeable: true,\n  dismissible: true,\n  label: '',\n  header: '',\n  data: undefined\n};\n/**\n * A stream to close dialogs\n */\nconst TUI_DIALOGS_CLOSE = tuiCreateToken(EMPTY);\n/**\n * Default parameters for dialog component\n */\nconst TUI_DIALOG_OPTIONS = tuiCreateToken(TUI_DIALOG_DEFAULT_OPTIONS);\nfunction tuiDialogOptionsProvider(options) {\n  return tuiProvideOptions(TUI_DIALOG_OPTIONS, options, TUI_DIALOG_DEFAULT_OPTIONS);\n}\nconst SCROLLBAR_PLACEHOLDER = 17;\nclass TuiDialogCloseService extends Observable {\n  constructor() {\n    super(subscriber => merge(this.esc$, this.mousedown$, tuiCloseWatcher().pipe(tuiZonefull())).subscribe(subscriber));\n    this.win = inject(WA_WINDOW);\n    this.doc = inject(DOCUMENT);\n    this.el = tuiInjectElement();\n    this.esc$ = tuiTypedFromEvent(this.doc, 'keydown').pipe(filter(event => {\n      const target = tuiGetActualTarget(event);\n      return (\n        // @ts-ignore\n        typeof CloseWatcher === 'undefined' && event.key?.toLowerCase() === 'escape' && !event.defaultPrevented && (this.el.contains(target) || this.isOutside(target))\n      );\n    }));\n    this.mousedown$ = tuiTypedFromEvent(this.doc, 'mousedown').pipe(filter(event => tuiGetViewportWidth(this.win) - event.clientX > SCROLLBAR_PLACEHOLDER && this.isOutside(tuiGetActualTarget(event))), switchMap(() => tuiTypedFromEvent(this.doc, 'mouseup').pipe(take(1), map(tuiGetActualTarget), filter(target => this.isOutside(target)))));\n  }\n  isOutside(target) {\n    return tuiIsElement(target) && (!tuiContainsOrAfter(this.el, target) || target === this.el);\n  }\n  static {\n    this.ɵfac = function TuiDialogCloseService_Factory(t) {\n      return new (t || TuiDialogCloseService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiDialogCloseService,\n      factory: TuiDialogCloseService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDialogCloseService, [{\n    type: Injectable\n  }], function () {\n    return [];\n  }, null);\n})();\nconst REQUIRED_ERROR = new Error('Required dialog was dismissed');\nfunction toObservable(valueOrStream) {\n  return isObservable(valueOrStream) ? valueOrStream : of(valueOrStream);\n}\nclass TuiDialogComponent {\n  constructor() {\n    this.close$ = new Subject();\n    this.context = injectContext();\n    this.closeWord$ = inject(TUI_CLOSE_WORD);\n    this.icons = inject(TUI_COMMON_ICONS);\n    this.from = computed(() => this.size === 'fullscreen' || this.size === 'page' || this.isMobile() ? 'translateY(100vh)' : 'translateY(2.5rem)');\n    this.isMobile = toSignal(inject(TuiBreakpointService).pipe(map(breakpoint => breakpoint === 'mobile')));\n    merge(this.close$.pipe(switchMap(() => toObservable(this.context.closeable))), inject(TuiDialogCloseService).pipe(exhaustMap(() => toObservable(this.context.dismissible).pipe(take(1)))), inject(TUI_DIALOGS_CLOSE).pipe(map(TUI_TRUE_HANDLER))).pipe(filter(Boolean), takeUntilDestroyed()).subscribe(() => {\n      this.close();\n    });\n  }\n  get size() {\n    return this.context.size;\n  }\n  get header() {\n    return this.context.header;\n  }\n  close() {\n    if (this.context.required) {\n      this.context.$implicit.error(REQUIRED_ERROR);\n    } else {\n      this.context.$implicit.complete();\n    }\n  }\n  static {\n    this.ɵfac = function TuiDialogComponent_Factory(t) {\n      return new (t || TuiDialogComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiDialogComponent,\n      selectors: [[\"tui-dialog\"]],\n      hostVars: 6,\n      hostBindings: function TuiDialogComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-appearance\", ctx.context.appearance)(\"data-size\", ctx.size);\n          i0.ɵɵstyleProp(\"--tui-from\", ctx.from());\n          i0.ɵɵclassProp(\"_centered\", ctx.header);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiDialogCloseService]), i0.ɵɵHostDirectivesFeature([i1.TuiAnimated]), i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 8,\n      consts: [[\"class\", \"t-header\", 4, \"ngIf\"], [1, \"t-content\"], [1, \"t-heading\", 3, \"id\", \"textContent\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [1, \"t-filler\"], [\"automation-id\", \"tui-dialog__close\", \"tuiIconButton\", \"\", \"type\", \"button\", \"class\", \"t-close\", 3, \"appearance\", \"iconStart\", \"size\", \"border-radius\", \"click\", \"mousedown.prevent.zoneless\", 4, \"ngIf\"], [1, \"t-header\"], [3, \"innerHTML\"], [\"class\", \"t-buttons\", 4, \"ngIf\"], [1, \"t-buttons\"], [\"size\", \"m\", \"tuiAutoFocus\", \"\", \"tuiButton\", \"\", \"type\", \"button\", 3, \"click\"], [\"automation-id\", \"tui-dialog__close\", \"tuiIconButton\", \"\", \"type\", \"button\", 1, \"t-close\", 3, \"click\", \"mousedown.prevent.zoneless\", \"appearance\", \"iconStart\", \"size\"]],\n      template: function TuiDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiDialogComponent_header_0_Template, 2, 2, \"header\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵelement(2, \"h2\", 2);\n          i0.ɵɵelementStart(3, \"section\");\n          i0.ɵɵtemplate(4, TuiDialogComponent_ng_container_4_Template, 3, 2, \"ng-container\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(5, \"div\", 4);\n          i0.ɵɵtemplate(6, TuiDialogComponent_button_6_Template, 3, 8, \"button\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.header);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"t-heading_closable\", ctx.context.closeable && !ctx.header);\n          i0.ɵɵproperty(\"id\", ctx.context.id)(\"textContent\", ctx.context.label);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.context.content)(\"polymorpheusOutletContext\", ctx.context);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.context.closeable);\n        }\n      },\n      dependencies: [AsyncPipe, NgIf, PolymorpheusOutlet, TuiAutoFocus, TuiButton],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:flex;font:var(--tui-font-text-m);flex-direction:column;box-sizing:border-box;margin:auto;border-radius:1.5rem;border:2.5rem solid transparent}.tui-enter[_nghost-%COMP%], .tui-leave[_nghost-%COMP%]{animation-name:tuiFade,tuiSlide}[_nghost-%COMP%]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";border-radius:inherit;pointer-events:none;box-shadow:var(--tui-shadow-popup)}[data-size=auto][_nghost-%COMP%]{inline-size:auto}[data-size=s][_nghost-%COMP%]{inline-size:30rem}[data-size=s][_nghost-%COMP%]   .t-content[_ngcontent-%COMP%]{padding:1.5rem}[data-size=s][_nghost-%COMP%]   .t-heading[_ngcontent-%COMP%]{font:var(--tui-font-heading-5)}[data-size=m][_nghost-%COMP%]{inline-size:42.5rem}[data-size=l][_nghost-%COMP%]{inline-size:55rem}[data-size=fullscreen][_nghost-%COMP%], [data-size=page][_nghost-%COMP%]{min-inline-size:100vw;min-block-size:100%;border-radius:0;border:none;background:var(--tui-background-elevation-1);box-shadow:0 4rem var(--tui-background-elevation-1)}[data-size=fullscreen][_nghost-%COMP%]   .t-content[_ngcontent-%COMP%], [data-size=page][_nghost-%COMP%]   .t-content[_ngcontent-%COMP%]{padding:3rem calc(50vw - 22.5rem)}[data-size=fullscreen][_nghost-%COMP%]   .t-heading[_ngcontent-%COMP%], [data-size=page][_nghost-%COMP%]   .t-heading[_ngcontent-%COMP%]{font:var(--tui-font-heading-3)}._centered[_nghost-%COMP%]{text-align:center}[_nghost-%COMP%]   tui-root._mobile[data-size][_nghost-%COMP%], tui-root._mobile   [data-size][_nghost-%COMP%]{min-inline-size:100%;inline-size:100%;max-inline-size:100%;border-radius:0;border:none;margin:auto 0 0;background:var(--tui-background-elevation-1);padding-bottom:env(safe-area-inset-bottom)}[_nghost-%COMP%]   tui-root._mobile[data-size][_nghost-%COMP%]   .t-content[_ngcontent-%COMP%], tui-root._mobile   [data-size][_nghost-%COMP%]   .t-content[_ngcontent-%COMP%]{padding:1rem}[_nghost-%COMP%]   tui-root._mobile[data-size][_nghost-%COMP%]   .t-heading[_ngcontent-%COMP%], tui-root._mobile   [data-size][_nghost-%COMP%]   .t-heading[_ngcontent-%COMP%]{font:var(--tui-font-heading-5)}[_nghost-%COMP%]   tui-root._mobile[data-size=fullscreen][_nghost-%COMP%], tui-root._mobile   [data-size=fullscreen][_nghost-%COMP%], [_nghost-%COMP%]   tui-root._mobile[data-size=page][_nghost-%COMP%], tui-root._mobile   [data-size=page][_nghost-%COMP%]{padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom)}[_nghost-%COMP%]   tui-root._mobile[data-size=fullscreen][_nghost-%COMP%]   .t-close[_ngcontent-%COMP%], tui-root._mobile   [data-size=fullscreen][_nghost-%COMP%]   .t-close[_ngcontent-%COMP%], [_nghost-%COMP%]   tui-root._mobile[data-size=page][_nghost-%COMP%]   .t-close[_ngcontent-%COMP%], tui-root._mobile   [data-size=page][_nghost-%COMP%]   .t-close[_ngcontent-%COMP%]{top:calc(1rem + env(safe-area-inset-top))}[data-size=page][_nghost-%COMP%]   .t-content[_ngcontent-%COMP%], tui-root._mobile   [data-size=page][_nghost-%COMP%]   .t-content[_ngcontent-%COMP%]{padding:0}.t-heading[_ngcontent-%COMP%]{margin:0 0 .5rem;overflow-wrap:break-word;font:var(--tui-font-heading-4)}.t-heading_closable[_ngcontent-%COMP%]{padding-inline-end:2rem}.t-heading[_ngcontent-%COMP%]:empty{display:none}.t-header[_ngcontent-%COMP%]{display:flex;border-top-left-radius:inherit;border-top-right-radius:inherit;overflow:hidden}[data-size=fullscreen][_nghost-%COMP%]   tui-root._mobile[_nghost-%COMP%]   .t-header[_ngcontent-%COMP%], tui-root._mobile   [_nghost-%COMP%]   .t-header[_ngcontent-%COMP%]{flex:1}.t-content[_ngcontent-%COMP%]{border-radius:inherit;padding:1.75rem;background:var(--tui-background-elevation-1)}.t-content[_ngcontent-%COMP%]:not(:first-child){border-top-left-radius:0;border-top-right-radius:0}.t-content[_ngcontent-%COMP%] > section[_ngcontent-%COMP%]{border-radius:inherit}.t-filler[_ngcontent-%COMP%]{flex-grow:1}.t-close[_ngcontent-%COMP%]{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:1rem;right:1rem}@supports (inset-inline-end: 1rem){.t-close[_ngcontent-%COMP%]{right:unset;inset-inline-end:1rem}}.t-buttons[_ngcontent-%COMP%]{margin-top:1.25rem;text-align:end}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDialogComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-dialog',\n      imports: [AsyncPipe, NgIf, PolymorpheusOutlet, TuiAutoFocus, TuiButton],\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [TuiDialogCloseService],\n      hostDirectives: [TuiAnimated],\n      host: {\n        '[attr.data-appearance]': 'context.appearance',\n        '[attr.data-size]': 'size',\n        '[class._centered]': 'header',\n        '[style.--tui-from]': 'from()'\n      },\n      template: \"<header\\n    *ngIf=\\\"header\\\"\\n    class=\\\"t-header\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"header as text; context: context\\\">\\n        {{ text }}\\n    </ng-container>\\n</header>\\n<div class=\\\"t-content\\\">\\n    <h2\\n        class=\\\"t-heading\\\"\\n        [class.t-heading_closable]=\\\"context.closeable && !header\\\"\\n        [id]=\\\"context.id\\\"\\n        [textContent]=\\\"context.label\\\"\\n    ></h2>\\n    <section>\\n        <ng-container *polymorpheusOutlet=\\\"context.content as text; context: context\\\">\\n            <div [innerHTML]=\\\"text\\\"></div>\\n            <div\\n                *ngIf=\\\"context.closeable || context.dismissible\\\"\\n                class=\\\"t-buttons\\\"\\n            >\\n                <button\\n                    size=\\\"m\\\"\\n                    tuiAutoFocus\\n                    tuiButton\\n                    type=\\\"button\\\"\\n                    (click)=\\\"context.$implicit.complete()\\\"\\n                >\\n                    {{ context.data?.button || 'OK' }}\\n                </button>\\n            </div>\\n        </ng-container>\\n    </section>\\n</div>\\n<div class=\\\"t-filler\\\"></div>\\n\\n<!-- Close button is insensitive to `context.closeable === Observable<false>` by design -->\\n<button\\n    *ngIf=\\\"context.closeable\\\"\\n    automation-id=\\\"tui-dialog__close\\\"\\n    tuiIconButton\\n    type=\\\"button\\\"\\n    class=\\\"t-close\\\"\\n    [appearance]=\\\"isMobile() ? 'icon' : 'neutral'\\\"\\n    [iconStart]=\\\"icons.close\\\"\\n    [size]=\\\"isMobile() ? 'xs' : 's'\\\"\\n    [style.border-radius.%]=\\\"100\\\"\\n    (click)=\\\"close$.next()\\\"\\n    (mousedown.prevent.zoneless)=\\\"(0)\\\"\\n>\\n    {{ closeWord$ | async }}\\n</button>\\n\",\n      styles: [\":host{position:relative;display:flex;font:var(--tui-font-text-m);flex-direction:column;box-sizing:border-box;margin:auto;border-radius:1.5rem;border:2.5rem solid transparent}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide}:host:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";border-radius:inherit;pointer-events:none;box-shadow:var(--tui-shadow-popup)}:host[data-size=auto]{inline-size:auto}:host[data-size=s]{inline-size:30rem}:host[data-size=s] .t-content{padding:1.5rem}:host[data-size=s] .t-heading{font:var(--tui-font-heading-5)}:host[data-size=m]{inline-size:42.5rem}:host[data-size=l]{inline-size:55rem}:host[data-size=fullscreen],:host[data-size=page]{min-inline-size:100vw;min-block-size:100%;border-radius:0;border:none;background:var(--tui-background-elevation-1);box-shadow:0 4rem var(--tui-background-elevation-1)}:host[data-size=fullscreen] .t-content,:host[data-size=page] .t-content{padding:3rem calc(50vw - 22.5rem)}:host[data-size=fullscreen] .t-heading,:host[data-size=page] .t-heading{font:var(--tui-font-heading-3)}:host._centered{text-align:center}:host :host-context(tui-root._mobile)[data-size]{min-inline-size:100%;inline-size:100%;max-inline-size:100%;border-radius:0;border:none;margin:auto 0 0;background:var(--tui-background-elevation-1);padding-bottom:env(safe-area-inset-bottom)}:host :host-context(tui-root._mobile)[data-size] .t-content{padding:1rem}:host :host-context(tui-root._mobile)[data-size] .t-heading{font:var(--tui-font-heading-5)}:host :host-context(tui-root._mobile)[data-size=fullscreen],:host :host-context(tui-root._mobile)[data-size=page]{padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom)}:host :host-context(tui-root._mobile)[data-size=fullscreen] .t-close,:host :host-context(tui-root._mobile)[data-size=page] .t-close{top:calc(1rem + env(safe-area-inset-top))}:host[data-size=page] .t-content,:host-context(tui-root._mobile) :host[data-size=page] .t-content{padding:0}.t-heading{margin:0 0 .5rem;overflow-wrap:break-word;font:var(--tui-font-heading-4)}.t-heading_closable{padding-inline-end:2rem}.t-heading:empty{display:none}.t-header{display:flex;border-top-left-radius:inherit;border-top-right-radius:inherit;overflow:hidden}:host[data-size=fullscreen] :host-context(tui-root._mobile) .t-header{flex:1}.t-content{border-radius:inherit;padding:1.75rem;background:var(--tui-background-elevation-1)}.t-content:not(:first-child){border-top-left-radius:0;border-top-right-radius:0}.t-content>section{border-radius:inherit}.t-filler{flex-grow:1}.t-close{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:1rem;right:1rem}@supports (inset-inline-end: 1rem){.t-close{right:unset;inset-inline-end:1rem}}.t-buttons{margin-top:1.25rem;text-align:end}\\n\"]\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nclass TuiDialogService extends TuiPopoverService {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiDialogService_BaseFactory;\n      return function TuiDialogService_Factory(t) {\n        return (ɵTuiDialogService_BaseFactory || (ɵTuiDialogService_BaseFactory = i0.ɵɵgetInheritedFactory(TuiDialogService)))(t || TuiDialogService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiDialogService,\n      factory: () => (() => new TuiDialogService(TUI_DIALOGS, TuiDialogComponent, inject(TUI_DIALOG_OPTIONS)))(),\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDialogService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => new TuiDialogService(TUI_DIALOGS, TuiDialogComponent, inject(TUI_DIALOG_OPTIONS))\n    }]\n  }], null, null);\n})();\nclass TuiDialog extends TuiPopoverDirective {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiDialog_BaseFactory;\n      return function TuiDialog_Factory(t) {\n        return (ɵTuiDialog_BaseFactory || (ɵTuiDialog_BaseFactory = i0.ɵɵgetInheritedFactory(TuiDialog)))(t || TuiDialog);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDialog,\n      selectors: [[\"ng-template\", \"tuiDialog\", \"\"]],\n      inputs: {\n        options: [i0.ɵɵInputFlags.None, \"tuiDialogOptions\", \"options\"],\n        open: [i0.ɵɵInputFlags.None, \"tuiDialog\", \"open\"]\n      },\n      outputs: {\n        openChange: \"tuiDialogChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsPopover(TuiDialogService)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDialog, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-template[tuiDialog]',\n      inputs: ['options: tuiDialogOptions', 'open: tuiDialog'],\n      outputs: ['openChange: tuiDialogChange'],\n      providers: [tuiAsPopover(TuiDialogService)]\n    }]\n  }], null, null);\n})();\nfunction tuiDialog(component, {\n  injector,\n  ...options\n} = {}) {\n  if (!injector) {\n    assertInInjectionContext(tuiDialog);\n    injector = inject(INJECTOR);\n  }\n  const dialogService = injector.get(TuiDialogService);\n  return data => dialogService.open(new PolymorpheusComponent(component, injector), {\n    ...options,\n    data\n  });\n}\nclass TuiDialogs {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.dialogs = toSignal(inject(TUI_DIALOGS), {\n      initialValue: []\n    });\n  }\n  static {\n    this.ɵfac = function TuiDialogs_Factory(t) {\n      return new (t || TuiDialogs)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiDialogs,\n      selectors: [[\"tui-dialogs\"]],\n      hostBindings: function TuiDialogs_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.zoneless\", function TuiDialogs_keydown_zoneless_HostBindingHandler() {\n            return ctx.el.scrollTop = ctx.el.scrollHeight / 2;\n          });\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 3,\n      consts: [[1, \"t-overlay\"], [\"aria-modal\", \"true\", \"role\", \"dialog\", \"tuiAnimatedParent\", \"\", \"tuiFocusTrap\", \"\", \"tuiScrollRef\", \"\", \"class\", \"t-dialog\", 4, \"ngFor\", \"ngForOf\"], [\"aria-modal\", \"true\", \"role\", \"dialog\", \"tuiAnimatedParent\", \"\", \"tuiFocusTrap\", \"\", \"tuiScrollRef\", \"\", 1, \"t-dialog\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [1, \"t-scrollbars\"]],\n      template: function TuiDialogs_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 0);\n          i0.ɵɵtemplate(1, TuiDialogs_section_1_Template, 3, 3, \"section\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"t-overlay_visible\", ctx.dialogs().length);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.dialogs());\n        }\n      },\n      dependencies: [NgForOf, PolymorpheusOutlet, TuiAnimatedParent, TuiFocusTrap, TuiScrollControls, TuiScrollRef],\n      styles: [\"[_nghost-%COMP%]{position:fixed;top:0;left:0;inline-size:100%;block-size:100%;scrollbar-width:none;-ms-overflow-style:none;pointer-events:none;overflow:hidden;overscroll-behavior:none;overflow-wrap:break-word;margin-top:var(--t-root-top)}[_nghost-%COMP%]::-webkit-scrollbar, [_nghost-%COMP%]::-webkit-scrollbar-thumb{display:none}[_nghost-%COMP%]:has(section){pointer-events:auto;overflow:auto}[_nghost-%COMP%]:before{content:\\\"\\\";display:block;block-size:1000%}.t-overlay[_ngcontent-%COMP%], .t-dialog[_ngcontent-%COMP%]{transition-property:filter;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;scrollbar-width:none;-ms-overflow-style:none;position:fixed;top:0;left:0;bottom:0;right:0;display:flex;block-size:100%;align-items:flex-start;outline:none;overflow:auto}.t-overlay[_ngcontent-%COMP%]::-webkit-scrollbar, .t-dialog[_ngcontent-%COMP%]::-webkit-scrollbar, .t-overlay[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .t-dialog[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{display:none}.t-overlay[_ngcontent-%COMP%]:has( > .tui-enter)[_ngcontent-%COMP%], .t-dialog[_ngcontent-%COMP%]:has( > .tui-enter)[_ngcontent-%COMP%], .t-overlay[_ngcontent-%COMP%]:has( > .tui-leave)[_ngcontent-%COMP%], .t-dialog[_ngcontent-%COMP%]:has( > .tui-leave)[_ngcontent-%COMP%]{overflow:clip}.t-dialog[_ngcontent-%COMP%]{position:sticky;overscroll-behavior:none;filter:brightness(.25)}.t-overlay[_ngcontent-%COMP%]{transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;background:var(--tui-service-backdrop);-webkit-backdrop-filter:var(--tui-backdrop, none);backdrop-filter:var(--tui-backdrop, none);opacity:0;transition-timing-function:ease-in}.t-overlay_visible[_ngcontent-%COMP%]{opacity:1;transition-timing-function:ease-out}.t-dialog[_ngcontent-%COMP%]:last-child{pointer-events:auto;filter:none}tui-root:has(tui-dropdown-mobile._sheet)[_nghost-%COMP%]   .t-dialog[_ngcontent-%COMP%]:last-child, tui-root:has(tui-dropdown-mobile._sheet)   [_nghost-%COMP%]   .t-dialog[_ngcontent-%COMP%]:last-child{filter:brightness(.5)}.t-scrollbars[_ngcontent-%COMP%]{position:fixed;top:0;left:0;bottom:0;right:0;margin:0;color:#747474}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDialogs, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-dialogs',\n      imports: [NgForOf, PolymorpheusOutlet, TuiAnimatedParent, TuiFocusTrap, TuiScrollControls, TuiScrollRef],\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        '(keydown.zoneless)': 'el.scrollTop = el.scrollHeight / 2'\n      },\n      template: \"<div\\n    class=\\\"t-overlay\\\"\\n    [class.t-overlay_visible]=\\\"dialogs().length\\\"\\n></div>\\n<section\\n    *ngFor=\\\"let item of dialogs()\\\"\\n    aria-modal=\\\"true\\\"\\n    role=\\\"dialog\\\"\\n    tuiAnimatedParent\\n    tuiFocusTrap\\n    tuiScrollRef\\n    class=\\\"t-dialog\\\"\\n    [attr.aria-labelledby]=\\\"item.id\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"item.component; context: item\\\" />\\n    <tui-scroll-controls class=\\\"t-scrollbars\\\" />\\n</section>\\n\",\n      styles: [\":host{position:fixed;top:0;left:0;inline-size:100%;block-size:100%;scrollbar-width:none;-ms-overflow-style:none;pointer-events:none;overflow:hidden;overscroll-behavior:none;overflow-wrap:break-word;margin-top:var(--t-root-top)}:host::-webkit-scrollbar,:host::-webkit-scrollbar-thumb{display:none}:host:has(section){pointer-events:auto;overflow:auto}:host:before{content:\\\"\\\";display:block;block-size:1000%}.t-overlay,.t-dialog{transition-property:filter;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;scrollbar-width:none;-ms-overflow-style:none;position:fixed;top:0;left:0;bottom:0;right:0;display:flex;block-size:100%;align-items:flex-start;outline:none;overflow:auto}.t-overlay::-webkit-scrollbar,.t-dialog::-webkit-scrollbar,.t-overlay::-webkit-scrollbar-thumb,.t-dialog::-webkit-scrollbar-thumb{display:none}.t-overlay:has(>.tui-enter),.t-dialog:has(>.tui-enter),.t-overlay:has(>.tui-leave),.t-dialog:has(>.tui-leave){overflow:clip}.t-dialog{position:sticky;overscroll-behavior:none;filter:brightness(.25)}.t-overlay{transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;background:var(--tui-service-backdrop);-webkit-backdrop-filter:var(--tui-backdrop, none);backdrop-filter:var(--tui-backdrop, none);opacity:0;transition-timing-function:ease-in}.t-overlay_visible{opacity:1;transition-timing-function:ease-out}.t-dialog:last-child{pointer-events:auto;filter:none}:host-context(tui-root:has(tui-dropdown-mobile._sheet)) .t-dialog:last-child{filter:brightness(.5)}.t-scrollbars{position:fixed;top:0;left:0;bottom:0;right:0;margin:0;color:#747474}\\n\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DIALOGS, TUI_DIALOGS_CLOSE, TUI_DIALOG_DEFAULT_OPTIONS, TUI_DIALOG_OPTIONS, TuiDialog, TuiDialogCloseService, TuiDialogComponent, TuiDialogService, TuiDialogs, tuiDialog, tuiDialogOptionsProvider };", "map": {"version": 3, "names": ["DOCUMENT", "AsyncPipe", "NgIf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i0", "inject", "Injectable", "computed", "Component", "ChangeDetectionStrategy", "Directive", "assertInInjectionContext", "INJECTOR", "toSignal", "takeUntilDestroyed", "TUI_TRUE_HANDLER", "i1", "TuiAnimated", "TuiAnimatedParent", "TuiAutoFocus", "TuiButton", "TuiBreakpointService", "TUI_CLOSE_WORD", "TUI_COMMON_ICONS", "injectContext", "Polymorpheus<PERSON><PERSON>let", "PolymorpheusComponent", "BehaviorSubject", "EMPTY", "Observable", "merge", "filter", "switchMap", "take", "map", "isObservable", "of", "Subject", "exhaustMap", "tuiCreateToken", "tuiProvideOptions", "WA_WINDOW", "tuiCloseWatcher", "tui<PERSON>onefull", "tuiTypedFromEvent", "tuiInjectElement", "tuiGetActualTarget", "tuiIsElement", "tuiContainsOrAfter", "tuiGetViewportWidth", "TuiPopoverDirective", "TuiPopoverService", "tuiAsPopover", "TuiFocusTrap", "TuiScrollControls", "TuiScrollRef", "TuiDialogComponent_header_0_ng_container_1_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "text_r1", "polymorpheusOutlet", "ɵɵadvance", "ɵɵtextInterpolate1", "TuiDialogComponent_header_0_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "ctx_r1", "ɵɵnextContext", "ɵɵproperty", "header", "context", "TuiDialogComponent_ng_container_4_div_2_Template", "_r3", "ɵɵgetCurrentView", "ɵɵlistener", "TuiDialogComponent_ng_container_4_div_2_Template_button_click_1_listener", "ɵɵrestoreView", "ɵɵresetView", "$implicit", "complete", "data", "button", "TuiDialogComponent_ng_container_4_Template", "ɵɵelement", "text_r4", "ɵɵsanitizeHtml", "closeable", "dismissible", "TuiDialogComponent_button_6_Template", "_r5", "TuiDialogComponent_button_6_Template_button_click_0_listener", "close$", "next", "TuiDialogComponent_button_6_Template_button_mousedown_prevent_zoneless_0_listener", "ɵɵpipe", "ɵɵstyleProp", "isMobile", "icons", "close", "ɵɵpipeBind1", "closeWord$", "TuiDialogs_section_1_ng_container_1_Template", "ɵɵelementContainer", "TuiDialogs_section_1_Template", "item_r1", "ɵɵattribute", "id", "component", "TUI_DIALOGS", "TUI_DIALOG_DEFAULT_OPTIONS", "appearance", "size", "required", "label", "undefined", "TUI_DIALOGS_CLOSE", "TUI_DIALOG_OPTIONS", "tuiDialogOptionsProvider", "options", "SCROLLBAR_PLACEHOLDER", "TuiDialogCloseService", "constructor", "subscriber", "esc$", "mousedown$", "pipe", "subscribe", "win", "doc", "el", "event", "target", "CloseWatcher", "key", "toLowerCase", "defaultPrevented", "contains", "isOutside", "clientX", "ɵfac", "TuiDialogCloseService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "REQUIRED_ERROR", "Error", "toObservable", "valueOrStream", "TuiDialogComponent", "from", "breakpoint", "Boolean", "error", "TuiDialogComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostVars", "hostBindings", "TuiDialogComponent_HostBindings", "ɵɵclassProp", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiDialogComponent_Template", "content", "dependencies", "styles", "args", "selector", "imports", "changeDetection", "<PERSON><PERSON><PERSON>", "providers", "hostDirectives", "host", "TuiDialogService", "ɵTuiDialogService_BaseFactory", "TuiDialogService_Factory", "ɵɵgetInheritedFactory", "providedIn", "useFactory", "TuiDialog", "ɵTuiDialog_BaseFactory", "TuiDialog_Factory", "ɵdir", "ɵɵdefineDirective", "inputs", "ɵɵInputFlags", "None", "open", "outputs", "openChange", "ɵɵInheritDefinitionFeature", "tui<PERSON><PERSON><PERSON>", "injector", "dialogService", "get", "TuiDialogs", "dialogs", "initialValue", "TuiDialogs_Factory", "TuiDialogs_HostBindings", "TuiDialogs_keydown_zoneless_HostBindingHandler", "scrollTop", "scrollHeight", "TuiDialogs_Template", "length"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-components-dialog.mjs"], "sourcesContent": ["import { DOCUMENT, Async<PERSON>ipe, NgI<PERSON>, NgForOf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Injectable, computed, Component, ChangeDetectionStrategy, Directive, assertInInjectionContext, INJECTOR } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { TUI_TRUE_HANDLER } from '@taiga-ui/cdk/constants';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated, TuiAnimatedParent } from '@taiga-ui/cdk/directives/animated';\nimport { TuiAutoFocus } from '@taiga-ui/cdk/directives/auto-focus';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { TuiBreakpointService } from '@taiga-ui/core/services';\nimport { TUI_CLOSE_WORD, TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { injectContext, PolymorpheusOutlet, PolymorpheusComponent } from '@taiga-ui/polymorpheus';\nimport { BehaviorSubject, EMPTY, Observable, merge, filter, switchMap, take, map, isObservable, of, Subject, exhaustMap } from 'rxjs';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { WA_WINDOW } from '@ng-web-apis/common';\nimport { tuiCloseWatcher, tuiZonefull, tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement, tuiGetActualTarget, tuiIsElement, tuiContainsOrAfter } from '@taiga-ui/cdk/utils/dom';\nimport { tuiGetViewportWidth } from '@taiga-ui/core/utils';\nimport { TuiPopoverDirective } from '@taiga-ui/cdk/directives/popover';\nimport { TuiPopoverService, tuiAsPopover } from '@taiga-ui/cdk/services';\nimport { TuiFocusTrap } from '@taiga-ui/cdk/directives/focus-trap';\nimport { TuiScrollControls, TuiScrollRef } from '@taiga-ui/core/components/scrollbar';\n\nconst TUI_DIALOGS = tuiCreateToken(new BehaviorSubject([]));\nconst TUI_DIALOG_DEFAULT_OPTIONS = {\n    appearance: '',\n    size: 'm',\n    required: false,\n    closeable: true,\n    dismissible: true,\n    label: '',\n    header: '',\n    data: undefined,\n};\n/**\n * A stream to close dialogs\n */\nconst TUI_DIALOGS_CLOSE = tuiCreateToken(EMPTY);\n/**\n * Default parameters for dialog component\n */\nconst TUI_DIALOG_OPTIONS = tuiCreateToken(TUI_DIALOG_DEFAULT_OPTIONS);\nfunction tuiDialogOptionsProvider(options) {\n    return tuiProvideOptions(TUI_DIALOG_OPTIONS, options, TUI_DIALOG_DEFAULT_OPTIONS);\n}\n\nconst SCROLLBAR_PLACEHOLDER = 17;\nclass TuiDialogCloseService extends Observable {\n    constructor() {\n        super((subscriber) => merge(this.esc$, this.mousedown$, tuiCloseWatcher().pipe(tuiZonefull())).subscribe(subscriber));\n        this.win = inject(WA_WINDOW);\n        this.doc = inject(DOCUMENT);\n        this.el = tuiInjectElement();\n        this.esc$ = tuiTypedFromEvent(this.doc, 'keydown').pipe(filter((event) => {\n            const target = tuiGetActualTarget(event);\n            return (\n            // @ts-ignore\n            typeof CloseWatcher === 'undefined' &&\n                event.key?.toLowerCase() === 'escape' &&\n                !event.defaultPrevented &&\n                (this.el.contains(target) || this.isOutside(target)));\n        }));\n        this.mousedown$ = tuiTypedFromEvent(this.doc, 'mousedown').pipe(filter((event) => tuiGetViewportWidth(this.win) - event.clientX > SCROLLBAR_PLACEHOLDER &&\n            this.isOutside(tuiGetActualTarget(event))), switchMap(() => tuiTypedFromEvent(this.doc, 'mouseup').pipe(take(1), map(tuiGetActualTarget), filter((target) => this.isOutside(target)))));\n    }\n    isOutside(target) {\n        return (tuiIsElement(target) &&\n            (!tuiContainsOrAfter(this.el, target) || target === this.el));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDialogCloseService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDialogCloseService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDialogCloseService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return []; } });\n\nconst REQUIRED_ERROR = new Error('Required dialog was dismissed');\nfunction toObservable(valueOrStream) {\n    return isObservable(valueOrStream) ? valueOrStream : of(valueOrStream);\n}\nclass TuiDialogComponent {\n    constructor() {\n        this.close$ = new Subject();\n        this.context = injectContext();\n        this.closeWord$ = inject(TUI_CLOSE_WORD);\n        this.icons = inject(TUI_COMMON_ICONS);\n        this.from = computed(() => this.size === 'fullscreen' || this.size === 'page' || this.isMobile()\n            ? 'translateY(100vh)'\n            : 'translateY(2.5rem)');\n        this.isMobile = toSignal(inject(TuiBreakpointService).pipe(map((breakpoint) => breakpoint === 'mobile')));\n        merge(this.close$.pipe(switchMap(() => toObservable(this.context.closeable))), inject(TuiDialogCloseService).pipe(exhaustMap(() => toObservable(this.context.dismissible).pipe(take(1)))), inject(TUI_DIALOGS_CLOSE).pipe(map(TUI_TRUE_HANDLER)))\n            .pipe(filter(Boolean), takeUntilDestroyed())\n            .subscribe(() => {\n            this.close();\n        });\n    }\n    get size() {\n        return this.context.size;\n    }\n    get header() {\n        return this.context.header;\n    }\n    close() {\n        if (this.context.required) {\n            this.context.$implicit.error(REQUIRED_ERROR);\n        }\n        else {\n            this.context.$implicit.complete();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDialogComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDialogComponent, isStandalone: true, selector: \"tui-dialog\", host: { properties: { \"attr.data-appearance\": \"context.appearance\", \"attr.data-size\": \"size\", \"class._centered\": \"header\", \"style.--tui-from\": \"from()\" } }, providers: [TuiDialogCloseService], hostDirectives: [{ directive: i1.TuiAnimated }], ngImport: i0, template: \"<header\\n    *ngIf=\\\"header\\\"\\n    class=\\\"t-header\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"header as text; context: context\\\">\\n        {{ text }}\\n    </ng-container>\\n</header>\\n<div class=\\\"t-content\\\">\\n    <h2\\n        class=\\\"t-heading\\\"\\n        [class.t-heading_closable]=\\\"context.closeable && !header\\\"\\n        [id]=\\\"context.id\\\"\\n        [textContent]=\\\"context.label\\\"\\n    ></h2>\\n    <section>\\n        <ng-container *polymorpheusOutlet=\\\"context.content as text; context: context\\\">\\n            <div [innerHTML]=\\\"text\\\"></div>\\n            <div\\n                *ngIf=\\\"context.closeable || context.dismissible\\\"\\n                class=\\\"t-buttons\\\"\\n            >\\n                <button\\n                    size=\\\"m\\\"\\n                    tuiAutoFocus\\n                    tuiButton\\n                    type=\\\"button\\\"\\n                    (click)=\\\"context.$implicit.complete()\\\"\\n                >\\n                    {{ context.data?.button || 'OK' }}\\n                </button>\\n            </div>\\n        </ng-container>\\n    </section>\\n</div>\\n<div class=\\\"t-filler\\\"></div>\\n\\n<!-- Close button is insensitive to `context.closeable === Observable<false>` by design -->\\n<button\\n    *ngIf=\\\"context.closeable\\\"\\n    automation-id=\\\"tui-dialog__close\\\"\\n    tuiIconButton\\n    type=\\\"button\\\"\\n    class=\\\"t-close\\\"\\n    [appearance]=\\\"isMobile() ? 'icon' : 'neutral'\\\"\\n    [iconStart]=\\\"icons.close\\\"\\n    [size]=\\\"isMobile() ? 'xs' : 's'\\\"\\n    [style.border-radius.%]=\\\"100\\\"\\n    (click)=\\\"close$.next()\\\"\\n    (mousedown.prevent.zoneless)=\\\"(0)\\\"\\n>\\n    {{ closeWord$ | async }}\\n</button>\\n\", styles: [\":host{position:relative;display:flex;font:var(--tui-font-text-m);flex-direction:column;box-sizing:border-box;margin:auto;border-radius:1.5rem;border:2.5rem solid transparent}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide}:host:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";border-radius:inherit;pointer-events:none;box-shadow:var(--tui-shadow-popup)}:host[data-size=auto]{inline-size:auto}:host[data-size=s]{inline-size:30rem}:host[data-size=s] .t-content{padding:1.5rem}:host[data-size=s] .t-heading{font:var(--tui-font-heading-5)}:host[data-size=m]{inline-size:42.5rem}:host[data-size=l]{inline-size:55rem}:host[data-size=fullscreen],:host[data-size=page]{min-inline-size:100vw;min-block-size:100%;border-radius:0;border:none;background:var(--tui-background-elevation-1);box-shadow:0 4rem var(--tui-background-elevation-1)}:host[data-size=fullscreen] .t-content,:host[data-size=page] .t-content{padding:3rem calc(50vw - 22.5rem)}:host[data-size=fullscreen] .t-heading,:host[data-size=page] .t-heading{font:var(--tui-font-heading-3)}:host._centered{text-align:center}:host :host-context(tui-root._mobile)[data-size]{min-inline-size:100%;inline-size:100%;max-inline-size:100%;border-radius:0;border:none;margin:auto 0 0;background:var(--tui-background-elevation-1);padding-bottom:env(safe-area-inset-bottom)}:host :host-context(tui-root._mobile)[data-size] .t-content{padding:1rem}:host :host-context(tui-root._mobile)[data-size] .t-heading{font:var(--tui-font-heading-5)}:host :host-context(tui-root._mobile)[data-size=fullscreen],:host :host-context(tui-root._mobile)[data-size=page]{padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom)}:host :host-context(tui-root._mobile)[data-size=fullscreen] .t-close,:host :host-context(tui-root._mobile)[data-size=page] .t-close{top:calc(1rem + env(safe-area-inset-top))}:host[data-size=page] .t-content,:host-context(tui-root._mobile) :host[data-size=page] .t-content{padding:0}.t-heading{margin:0 0 .5rem;overflow-wrap:break-word;font:var(--tui-font-heading-4)}.t-heading_closable{padding-inline-end:2rem}.t-heading:empty{display:none}.t-header{display:flex;border-top-left-radius:inherit;border-top-right-radius:inherit;overflow:hidden}:host[data-size=fullscreen] :host-context(tui-root._mobile) .t-header{flex:1}.t-content{border-radius:inherit;padding:1.75rem;background:var(--tui-background-elevation-1)}.t-content:not(:first-child){border-top-left-radius:0;border-top-right-radius:0}.t-content>section{border-radius:inherit}.t-filler{flex-grow:1}.t-close{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:1rem;right:1rem}@supports (inset-inline-end: 1rem){.t-close{right:unset;inset-inline-end:1rem}}.t-buttons{margin-top:1.25rem;text-align:end}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: TuiAutoFocus, selector: \"[tuiAutoFocus]\", inputs: [\"tuiAutoFocus\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }], changeDetection: i0.ChangeDetectionStrategy.Default }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDialogComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-dialog', imports: [AsyncPipe, NgIf, PolymorpheusOutlet, TuiAutoFocus, TuiButton], changeDetection: ChangeDetectionStrategy.Default, providers: [TuiDialogCloseService], hostDirectives: [TuiAnimated], host: {\n                        '[attr.data-appearance]': 'context.appearance',\n                        '[attr.data-size]': 'size',\n                        '[class._centered]': 'header',\n                        '[style.--tui-from]': 'from()',\n                    }, template: \"<header\\n    *ngIf=\\\"header\\\"\\n    class=\\\"t-header\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"header as text; context: context\\\">\\n        {{ text }}\\n    </ng-container>\\n</header>\\n<div class=\\\"t-content\\\">\\n    <h2\\n        class=\\\"t-heading\\\"\\n        [class.t-heading_closable]=\\\"context.closeable && !header\\\"\\n        [id]=\\\"context.id\\\"\\n        [textContent]=\\\"context.label\\\"\\n    ></h2>\\n    <section>\\n        <ng-container *polymorpheusOutlet=\\\"context.content as text; context: context\\\">\\n            <div [innerHTML]=\\\"text\\\"></div>\\n            <div\\n                *ngIf=\\\"context.closeable || context.dismissible\\\"\\n                class=\\\"t-buttons\\\"\\n            >\\n                <button\\n                    size=\\\"m\\\"\\n                    tuiAutoFocus\\n                    tuiButton\\n                    type=\\\"button\\\"\\n                    (click)=\\\"context.$implicit.complete()\\\"\\n                >\\n                    {{ context.data?.button || 'OK' }}\\n                </button>\\n            </div>\\n        </ng-container>\\n    </section>\\n</div>\\n<div class=\\\"t-filler\\\"></div>\\n\\n<!-- Close button is insensitive to `context.closeable === Observable<false>` by design -->\\n<button\\n    *ngIf=\\\"context.closeable\\\"\\n    automation-id=\\\"tui-dialog__close\\\"\\n    tuiIconButton\\n    type=\\\"button\\\"\\n    class=\\\"t-close\\\"\\n    [appearance]=\\\"isMobile() ? 'icon' : 'neutral'\\\"\\n    [iconStart]=\\\"icons.close\\\"\\n    [size]=\\\"isMobile() ? 'xs' : 's'\\\"\\n    [style.border-radius.%]=\\\"100\\\"\\n    (click)=\\\"close$.next()\\\"\\n    (mousedown.prevent.zoneless)=\\\"(0)\\\"\\n>\\n    {{ closeWord$ | async }}\\n</button>\\n\", styles: [\":host{position:relative;display:flex;font:var(--tui-font-text-m);flex-direction:column;box-sizing:border-box;margin:auto;border-radius:1.5rem;border:2.5rem solid transparent}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide}:host:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";border-radius:inherit;pointer-events:none;box-shadow:var(--tui-shadow-popup)}:host[data-size=auto]{inline-size:auto}:host[data-size=s]{inline-size:30rem}:host[data-size=s] .t-content{padding:1.5rem}:host[data-size=s] .t-heading{font:var(--tui-font-heading-5)}:host[data-size=m]{inline-size:42.5rem}:host[data-size=l]{inline-size:55rem}:host[data-size=fullscreen],:host[data-size=page]{min-inline-size:100vw;min-block-size:100%;border-radius:0;border:none;background:var(--tui-background-elevation-1);box-shadow:0 4rem var(--tui-background-elevation-1)}:host[data-size=fullscreen] .t-content,:host[data-size=page] .t-content{padding:3rem calc(50vw - 22.5rem)}:host[data-size=fullscreen] .t-heading,:host[data-size=page] .t-heading{font:var(--tui-font-heading-3)}:host._centered{text-align:center}:host :host-context(tui-root._mobile)[data-size]{min-inline-size:100%;inline-size:100%;max-inline-size:100%;border-radius:0;border:none;margin:auto 0 0;background:var(--tui-background-elevation-1);padding-bottom:env(safe-area-inset-bottom)}:host :host-context(tui-root._mobile)[data-size] .t-content{padding:1rem}:host :host-context(tui-root._mobile)[data-size] .t-heading{font:var(--tui-font-heading-5)}:host :host-context(tui-root._mobile)[data-size=fullscreen],:host :host-context(tui-root._mobile)[data-size=page]{padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom)}:host :host-context(tui-root._mobile)[data-size=fullscreen] .t-close,:host :host-context(tui-root._mobile)[data-size=page] .t-close{top:calc(1rem + env(safe-area-inset-top))}:host[data-size=page] .t-content,:host-context(tui-root._mobile) :host[data-size=page] .t-content{padding:0}.t-heading{margin:0 0 .5rem;overflow-wrap:break-word;font:var(--tui-font-heading-4)}.t-heading_closable{padding-inline-end:2rem}.t-heading:empty{display:none}.t-header{display:flex;border-top-left-radius:inherit;border-top-right-radius:inherit;overflow:hidden}:host[data-size=fullscreen] :host-context(tui-root._mobile) .t-header{flex:1}.t-content{border-radius:inherit;padding:1.75rem;background:var(--tui-background-elevation-1)}.t-content:not(:first-child){border-top-left-radius:0;border-top-right-radius:0}.t-content>section{border-radius:inherit}.t-filler{flex-grow:1}.t-close{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:1rem;right:1rem}@supports (inset-inline-end: 1rem){.t-close{right:unset;inset-inline-end:1rem}}.t-buttons{margin-top:1.25rem;text-align:end}\\n\"] }]\n        }], ctorParameters: function () { return []; } });\n\nclass TuiDialogService extends TuiPopoverService {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDialogService, deps: null, target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDialogService, providedIn: 'root', useFactory: () => new TuiDialogService(TUI_DIALOGS, TuiDialogComponent, inject(TUI_DIALOG_OPTIONS)) }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDialogService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                    useFactory: () => new TuiDialogService(TUI_DIALOGS, TuiDialogComponent, inject(TUI_DIALOG_OPTIONS)),\n                }]\n        }] });\n\nclass TuiDialog extends TuiPopoverDirective {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDialog, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDialog, isStandalone: true, selector: \"ng-template[tuiDialog]\", inputs: { options: [\"tuiDialogOptions\", \"options\"], open: [\"tuiDialog\", \"open\"] }, outputs: { openChange: \"tuiDialogChange\" }, providers: [tuiAsPopover(TuiDialogService)], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDialog, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-template[tuiDialog]',\n                    inputs: ['options: tuiDialogOptions', 'open: tuiDialog'],\n                    outputs: ['openChange: tuiDialogChange'],\n                    providers: [tuiAsPopover(TuiDialogService)],\n                }]\n        }] });\n\nfunction tuiDialog(component, { injector, ...options } = {}) {\n    if (!injector) {\n        assertInInjectionContext(tuiDialog);\n        injector = inject(INJECTOR);\n    }\n    const dialogService = injector.get(TuiDialogService);\n    return (data) => dialogService.open(new PolymorpheusComponent(component, injector), {\n        ...options,\n        data,\n    });\n}\n\nclass TuiDialogs {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.dialogs = toSignal(inject(TUI_DIALOGS), { initialValue: [] });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDialogs, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDialogs, isStandalone: true, selector: \"tui-dialogs\", host: { listeners: { \"keydown.zoneless\": \"el.scrollTop = el.scrollHeight / 2\" } }, ngImport: i0, template: \"<div\\n    class=\\\"t-overlay\\\"\\n    [class.t-overlay_visible]=\\\"dialogs().length\\\"\\n></div>\\n<section\\n    *ngFor=\\\"let item of dialogs()\\\"\\n    aria-modal=\\\"true\\\"\\n    role=\\\"dialog\\\"\\n    tuiAnimatedParent\\n    tuiFocusTrap\\n    tuiScrollRef\\n    class=\\\"t-dialog\\\"\\n    [attr.aria-labelledby]=\\\"item.id\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"item.component; context: item\\\" />\\n    <tui-scroll-controls class=\\\"t-scrollbars\\\" />\\n</section>\\n\", styles: [\":host{position:fixed;top:0;left:0;inline-size:100%;block-size:100%;scrollbar-width:none;-ms-overflow-style:none;pointer-events:none;overflow:hidden;overscroll-behavior:none;overflow-wrap:break-word;margin-top:var(--t-root-top)}:host::-webkit-scrollbar,:host::-webkit-scrollbar-thumb{display:none}:host:has(section){pointer-events:auto;overflow:auto}:host:before{content:\\\"\\\";display:block;block-size:1000%}.t-overlay,.t-dialog{transition-property:filter;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;scrollbar-width:none;-ms-overflow-style:none;position:fixed;top:0;left:0;bottom:0;right:0;display:flex;block-size:100%;align-items:flex-start;outline:none;overflow:auto}.t-overlay::-webkit-scrollbar,.t-dialog::-webkit-scrollbar,.t-overlay::-webkit-scrollbar-thumb,.t-dialog::-webkit-scrollbar-thumb{display:none}.t-overlay:has(>.tui-enter),.t-dialog:has(>.tui-enter),.t-overlay:has(>.tui-leave),.t-dialog:has(>.tui-leave){overflow:clip}.t-dialog{position:sticky;overscroll-behavior:none;filter:brightness(.25)}.t-overlay{transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;background:var(--tui-service-backdrop);-webkit-backdrop-filter:var(--tui-backdrop, none);backdrop-filter:var(--tui-backdrop, none);opacity:0;transition-timing-function:ease-in}.t-overlay_visible{opacity:1;transition-timing-function:ease-out}.t-dialog:last-child{pointer-events:auto;filter:none}:host-context(tui-root:has(tui-dropdown-mobile._sheet)) .t-dialog:last-child{filter:brightness(.5)}.t-scrollbars{position:fixed;top:0;left:0;bottom:0;right:0;margin:0;color:#747474}\\n\"], dependencies: [{ kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: TuiAnimatedParent, selector: \"[tuiAnimatedParent]\" }, { kind: \"directive\", type: TuiFocusTrap, selector: \"[tuiFocusTrap]\" }, { kind: \"component\", type: TuiScrollControls, selector: \"tui-scroll-controls\" }, { kind: \"directive\", type: TuiScrollRef, selector: \"[tuiScrollRef]\" }], changeDetection: i0.ChangeDetectionStrategy.Default }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDialogs, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-dialogs', imports: [\n                        NgForOf,\n                        PolymorpheusOutlet,\n                        TuiAnimatedParent,\n                        TuiFocusTrap,\n                        TuiScrollControls,\n                        TuiScrollRef,\n                    ], changeDetection: ChangeDetectionStrategy.Default, host: {\n                        '(keydown.zoneless)': 'el.scrollTop = el.scrollHeight / 2',\n                    }, template: \"<div\\n    class=\\\"t-overlay\\\"\\n    [class.t-overlay_visible]=\\\"dialogs().length\\\"\\n></div>\\n<section\\n    *ngFor=\\\"let item of dialogs()\\\"\\n    aria-modal=\\\"true\\\"\\n    role=\\\"dialog\\\"\\n    tuiAnimatedParent\\n    tuiFocusTrap\\n    tuiScrollRef\\n    class=\\\"t-dialog\\\"\\n    [attr.aria-labelledby]=\\\"item.id\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"item.component; context: item\\\" />\\n    <tui-scroll-controls class=\\\"t-scrollbars\\\" />\\n</section>\\n\", styles: [\":host{position:fixed;top:0;left:0;inline-size:100%;block-size:100%;scrollbar-width:none;-ms-overflow-style:none;pointer-events:none;overflow:hidden;overscroll-behavior:none;overflow-wrap:break-word;margin-top:var(--t-root-top)}:host::-webkit-scrollbar,:host::-webkit-scrollbar-thumb{display:none}:host:has(section){pointer-events:auto;overflow:auto}:host:before{content:\\\"\\\";display:block;block-size:1000%}.t-overlay,.t-dialog{transition-property:filter;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;scrollbar-width:none;-ms-overflow-style:none;position:fixed;top:0;left:0;bottom:0;right:0;display:flex;block-size:100%;align-items:flex-start;outline:none;overflow:auto}.t-overlay::-webkit-scrollbar,.t-dialog::-webkit-scrollbar,.t-overlay::-webkit-scrollbar-thumb,.t-dialog::-webkit-scrollbar-thumb{display:none}.t-overlay:has(>.tui-enter),.t-dialog:has(>.tui-enter),.t-overlay:has(>.tui-leave),.t-dialog:has(>.tui-leave){overflow:clip}.t-dialog{position:sticky;overscroll-behavior:none;filter:brightness(.25)}.t-overlay{transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;background:var(--tui-service-backdrop);-webkit-backdrop-filter:var(--tui-backdrop, none);backdrop-filter:var(--tui-backdrop, none);opacity:0;transition-timing-function:ease-in}.t-overlay_visible{opacity:1;transition-timing-function:ease-out}.t-dialog:last-child{pointer-events:auto;filter:none}:host-context(tui-root:has(tui-dropdown-mobile._sheet)) .t-dialog:last-child{filter:brightness(.5)}.t-scrollbars{position:fixed;top:0;left:0;bottom:0;right:0;margin:0;color:#747474}\\n\"] }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DIALOGS, TUI_DIALOGS_CLOSE, TUI_DIALOG_DEFAULT_OPTIONS, TUI_DIALOG_OPTIONS, TuiDialog, TuiDialogCloseService, TuiDialogComponent, TuiDialogService, TuiDialogs, tuiDialog, tuiDialogOptionsProvider };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,OAAO,QAAQ,iBAAiB;AACpE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,wBAAwB,EAAEC,QAAQ,QAAQ,eAAe;AAC/I,SAASC,QAAQ,EAAEC,kBAAkB,QAAQ,4BAA4B;AACzE,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,OAAO,KAAKC,EAAE,MAAM,mCAAmC;AACvD,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,mCAAmC;AAClF,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,uBAAuB;AACxE,SAASC,aAAa,EAAEC,kBAAkB,EAAEC,qBAAqB,QAAQ,wBAAwB;AACjG,SAASC,eAAe,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAEC,GAAG,EAAEC,YAAY,EAAEC,EAAE,EAAEC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AACrI,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,mCAAmC;AACrF,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,eAAe,EAAEC,WAAW,EAAEC,iBAAiB,QAAQ,2BAA2B;AAC3F,SAASC,gBAAgB,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,kBAAkB,QAAQ,yBAAyB;AAChH,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,wBAAwB;AACxE,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,qCAAqC;AAAC,SAAAC,oDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAgDerD,EAAE,CAAAuD,uBAAA,EA0C2c,CAAC;IA1C9cvD,EAAE,CAAAwD,MAAA,EA0Cqe,CAAC;IA1CxexD,EAAE,CAAAyD,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAAF3D,EAAE,CAAA4D,SAAA,CA0Cqe,CAAC;IA1Cxe5D,EAAE,CAAA6D,kBAAA,MAAAH,OAAA,KA0Cqe,CAAC;EAAA;AAAA;AAAA,SAAAI,qCAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1CxerD,EAAE,CAAA+D,cAAA,eA0C8X,CAAC;IA1CjY/D,EAAE,CAAAgE,UAAA,IAAAZ,mDAAA,yBA0C2c,CAAC;IA1C9cpD,EAAE,CAAAiE,YAAA,CA0C+f,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAa,MAAA,GA1ClgBlE,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAA4D,SAAA,CA0C+a,CAAC;IA1Clb5D,EAAE,CAAAoE,UAAA,uBAAAF,MAAA,CAAAG,MA0C+a,CAAC,8BAAAH,MAAA,CAAAI,OAAwB,CAAC;EAAA;AAAA;AAAA,SAAAC,iDAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAmB,GAAA,GA1C3cxE,EAAE,CAAAyE,gBAAA;IAAFzE,EAAE,CAAA+D,cAAA,YA0Cw/B,CAAC,gBAA+O,CAAC;IA1C3uC/D,EAAE,CAAA0E,UAAA,mBAAAC,yEAAA;MAAF3E,EAAE,CAAA4E,aAAA,CAAAJ,GAAA;MAAA,MAAAN,MAAA,GAAFlE,EAAE,CAAAmE,aAAA;MAAA,OAAFnE,EAAE,CAAA6E,WAAA,CA0CwrCX,MAAA,CAAAI,OAAA,CAAAQ,SAAA,CAAAC,QAAA,CAA2B,CAAC;IAAA,CAAC,CAAC;IA1CxtC/E,EAAE,CAAAwD,MAAA,EA0CkzC,CAAC;IA1CrzCxD,EAAE,CAAAiE,YAAA,CA0C2zC,CAAC,CAAmB,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAa,MAAA,GA1Cl1ClE,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAA4D,SAAA,EA0CkzC,CAAC;IA1CrzC5D,EAAE,CAAA6D,kBAAA,OAAAK,MAAA,CAAAI,OAAA,CAAAU,IAAA,kBAAAd,MAAA,CAAAI,OAAA,CAAAU,IAAA,CAAAC,MAAA,cA0CkzC,CAAC;EAAA;AAAA;AAAA,SAAAC,2CAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1CrzCrD,EAAE,CAAAuD,uBAAA,EA0Cg0B,CAAC;IA1Cn0BvD,EAAE,CAAAmF,SAAA,YA0C82B,CAAC;IA1Cj3BnF,EAAE,CAAAgE,UAAA,IAAAO,gDAAA,gBA0Cw/B,CAAC;IA1C3/BvE,EAAE,CAAAyD,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAA+B,OAAA,GAAA9B,GAAA,CAAAK,kBAAA;IAAA,MAAAO,MAAA,GAAFlE,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAA4D,SAAA,CA0Cu2B,CAAC;IA1C12B5D,EAAE,CAAAoE,UAAA,cAAAgB,OAAA,EAAFpF,EAAE,CAAAqF,cA0Cu2B,CAAC;IA1C12BrF,EAAE,CAAA4D,SAAA,CA0Ck8B,CAAC;IA1Cr8B5D,EAAE,CAAAoE,UAAA,SAAAF,MAAA,CAAAI,OAAA,CAAAgB,SAAA,IAAApB,MAAA,CAAAI,OAAA,CAAAiB,WA0Ck8B,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAAnC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAoC,GAAA,GA1Cr8BzF,EAAE,CAAAyE,gBAAA;IAAFzE,EAAE,CAAA+D,cAAA,gBA0Ci4D,CAAC;IA1Cp4D/D,EAAE,CAAA0E,UAAA,mBAAAgB,6DAAA;MAAF1F,EAAE,CAAA4E,aAAA,CAAAa,GAAA;MAAA,MAAAvB,MAAA,GAAFlE,EAAE,CAAAmE,aAAA;MAAA,OAAFnE,EAAE,CAAA6E,WAAA,CA0Cs0DX,MAAA,CAAAyB,MAAA,CAAAC,IAAA,CAAY,CAAC;IAAA,CAAC,CAAC,wCAAAC,kFAAA;MA1Cv1D7F,EAAE,CAAA4E,aAAA,CAAAa,GAAA;MAAA,OAAFzF,EAAE,CAAA6E,WAAA,CA0C23D,CAAC;IAAA,CAAE,CAAC;IA1Cj4D7E,EAAE,CAAAwD,MAAA,EA0Ci6D,CAAC;IA1Cp6DxD,EAAE,CAAA8F,MAAA;IAAF9F,EAAE,CAAAiE,YAAA,CA0C06D,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAa,MAAA,GA1C76DlE,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAA+F,WAAA,0BA0CqzD,CAAC;IA1CxzD/F,EAAE,CAAAoE,UAAA,eAAAF,MAAA,CAAA8B,QAAA,uBA0CusD,CAAC,cAAA9B,MAAA,CAAA+B,KAAA,CAAAC,KAAgC,CAAC,SAAAhC,MAAA,CAAA8B,QAAA,eAAuC,CAAC;IA1CnxDhG,EAAE,CAAA4D,SAAA,CA0Ci6D,CAAC;IA1Cp6D5D,EAAE,CAAA6D,kBAAA,MAAF7D,EAAE,CAAAmG,WAAA,OAAAjC,MAAA,CAAAkC,UAAA,OA0Ci6D,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAAhD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1Cp6DrD,EAAE,CAAAsG,kBAAA,EAmGkiB,CAAC;EAAA;AAAA;AAAA,SAAAC,8BAAAlD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnGriBrD,EAAE,CAAA+D,cAAA,gBAmGsd,CAAC;IAnGzd/D,EAAE,CAAAgE,UAAA,IAAAqC,4CAAA,yBAmGkiB,CAAC;IAnGriBrG,EAAE,CAAAmF,SAAA,4BAmGslB,CAAC;IAnGzlBnF,EAAE,CAAAiE,YAAA,CAmGkmB,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAmD,OAAA,GAAAlD,GAAA,CAAAwB,SAAA;IAnGrmB9E,EAAE,CAAAyG,WAAA,oBAAAD,OAAA,CAAAE,EAAA;IAAF1G,EAAE,CAAA4D,SAAA,CAmGghB,CAAC;IAnGnhB5D,EAAE,CAAAoE,UAAA,uBAAAoC,OAAA,CAAAG,SAmGghB,CAAC,8BAAAH,OAAY,CAAC;EAAA;AAAA;AAjJroB,MAAMI,WAAW,GAAGzE,cAAc,CAAC,IAAIZ,eAAe,CAAC,EAAE,CAAC,CAAC;AAC3D,MAAMsF,0BAA0B,GAAG;EAC/BC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE,KAAK;EACf1B,SAAS,EAAE,IAAI;EACfC,WAAW,EAAE,IAAI;EACjB0B,KAAK,EAAE,EAAE;EACT5C,MAAM,EAAE,EAAE;EACVW,IAAI,EAAEkC;AACV,CAAC;AACD;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGhF,cAAc,CAACX,KAAK,CAAC;AAC/C;AACA;AACA;AACA,MAAM4F,kBAAkB,GAAGjF,cAAc,CAAC0E,0BAA0B,CAAC;AACrE,SAASQ,wBAAwBA,CAACC,OAAO,EAAE;EACvC,OAAOlF,iBAAiB,CAACgF,kBAAkB,EAAEE,OAAO,EAAET,0BAA0B,CAAC;AACrF;AAEA,MAAMU,qBAAqB,GAAG,EAAE;AAChC,MAAMC,qBAAqB,SAAS/F,UAAU,CAAC;EAC3CgG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAEC,UAAU,IAAKhG,KAAK,CAAC,IAAI,CAACiG,IAAI,EAAE,IAAI,CAACC,UAAU,EAAEtF,eAAe,CAAC,CAAC,CAACuF,IAAI,CAACtF,WAAW,CAAC,CAAC,CAAC,CAAC,CAACuF,SAAS,CAACJ,UAAU,CAAC,CAAC;IACrH,IAAI,CAACK,GAAG,GAAG9H,MAAM,CAACoC,SAAS,CAAC;IAC5B,IAAI,CAAC2F,GAAG,GAAG/H,MAAM,CAACL,QAAQ,CAAC;IAC3B,IAAI,CAACqI,EAAE,GAAGxF,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACkF,IAAI,GAAGnF,iBAAiB,CAAC,IAAI,CAACwF,GAAG,EAAE,SAAS,CAAC,CAACH,IAAI,CAAClG,MAAM,CAAEuG,KAAK,IAAK;MACtE,MAAMC,MAAM,GAAGzF,kBAAkB,CAACwF,KAAK,CAAC;MACxC;QACA;QACA,OAAOE,YAAY,KAAK,WAAW,IAC/BF,KAAK,CAACG,GAAG,EAAEC,WAAW,CAAC,CAAC,KAAK,QAAQ,IACrC,CAACJ,KAAK,CAACK,gBAAgB,KACtB,IAAI,CAACN,EAAE,CAACO,QAAQ,CAACL,MAAM,CAAC,IAAI,IAAI,CAACM,SAAS,CAACN,MAAM,CAAC;MAAC;IAC5D,CAAC,CAAC,CAAC;IACH,IAAI,CAACP,UAAU,GAAGpF,iBAAiB,CAAC,IAAI,CAACwF,GAAG,EAAE,WAAW,CAAC,CAACH,IAAI,CAAClG,MAAM,CAAEuG,KAAK,IAAKrF,mBAAmB,CAAC,IAAI,CAACkF,GAAG,CAAC,GAAGG,KAAK,CAACQ,OAAO,GAAGnB,qBAAqB,IACnJ,IAAI,CAACkB,SAAS,CAAC/F,kBAAkB,CAACwF,KAAK,CAAC,CAAC,CAAC,EAAEtG,SAAS,CAAC,MAAMY,iBAAiB,CAAC,IAAI,CAACwF,GAAG,EAAE,SAAS,CAAC,CAACH,IAAI,CAAChG,IAAI,CAAC,CAAC,CAAC,EAAEC,GAAG,CAACY,kBAAkB,CAAC,EAAEf,MAAM,CAAEwG,MAAM,IAAK,IAAI,CAACM,SAAS,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/L;EACAM,SAASA,CAACN,MAAM,EAAE;IACd,OAAQxF,YAAY,CAACwF,MAAM,CAAC,KACvB,CAACvF,kBAAkB,CAAC,IAAI,CAACqF,EAAE,EAAEE,MAAM,CAAC,IAAIA,MAAM,KAAK,IAAI,CAACF,EAAE,CAAC;EACpE;EACA;IAAS,IAAI,CAACU,IAAI,YAAAC,8BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFrB,qBAAqB;IAAA,CAAoD;EAAE;EACtL;IAAS,IAAI,CAACsB,KAAK,kBAD8E9I,EAAE,CAAA+I,kBAAA;MAAAC,KAAA,EACYxB,qBAAqB;MAAAyB,OAAA,EAArBzB,qBAAqB,CAAAmB;IAAA,EAAG;EAAE;AAC7I;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAHqGlJ,EAAE,CAAAmJ,iBAAA,CAGX3B,qBAAqB,EAAc,CAAC;IACpH4B,IAAI,EAAElJ;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAMmJ,cAAc,GAAG,IAAIC,KAAK,CAAC,+BAA+B,CAAC;AACjE,SAASC,YAAYA,CAACC,aAAa,EAAE;EACjC,OAAOzH,YAAY,CAACyH,aAAa,CAAC,GAAGA,aAAa,GAAGxH,EAAE,CAACwH,aAAa,CAAC;AAC1E;AACA,MAAMC,kBAAkB,CAAC;EACrBhC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9B,MAAM,GAAG,IAAI1D,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACqC,OAAO,GAAGlD,aAAa,CAAC,CAAC;IAC9B,IAAI,CAACgF,UAAU,GAAGnG,MAAM,CAACiB,cAAc,CAAC;IACxC,IAAI,CAAC+E,KAAK,GAAGhG,MAAM,CAACkB,gBAAgB,CAAC;IACrC,IAAI,CAACuI,IAAI,GAAGvJ,QAAQ,CAAC,MAAM,IAAI,CAAC4G,IAAI,KAAK,YAAY,IAAI,IAAI,CAACA,IAAI,KAAK,MAAM,IAAI,IAAI,CAACf,QAAQ,CAAC,CAAC,GAC1F,mBAAmB,GACnB,oBAAoB,CAAC;IAC3B,IAAI,CAACA,QAAQ,GAAGvF,QAAQ,CAACR,MAAM,CAACgB,oBAAoB,CAAC,CAAC4G,IAAI,CAAC/F,GAAG,CAAE6H,UAAU,IAAKA,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC;IACzGjI,KAAK,CAAC,IAAI,CAACiE,MAAM,CAACkC,IAAI,CAACjG,SAAS,CAAC,MAAM2H,YAAY,CAAC,IAAI,CAACjF,OAAO,CAACgB,SAAS,CAAC,CAAC,CAAC,EAAErF,MAAM,CAACuH,qBAAqB,CAAC,CAACK,IAAI,CAAC3F,UAAU,CAAC,MAAMqH,YAAY,CAAC,IAAI,CAACjF,OAAO,CAACiB,WAAW,CAAC,CAACsC,IAAI,CAAChG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE5B,MAAM,CAACkH,iBAAiB,CAAC,CAACU,IAAI,CAAC/F,GAAG,CAACnB,gBAAgB,CAAC,CAAC,CAAC,CAC5OkH,IAAI,CAAClG,MAAM,CAACiI,OAAO,CAAC,EAAElJ,kBAAkB,CAAC,CAAC,CAAC,CAC3CoH,SAAS,CAAC,MAAM;MACjB,IAAI,CAAC5B,KAAK,CAAC,CAAC;IAChB,CAAC,CAAC;EACN;EACA,IAAIa,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACzC,OAAO,CAACyC,IAAI;EAC5B;EACA,IAAI1C,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO,CAACD,MAAM;EAC9B;EACA6B,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAAC5B,OAAO,CAAC0C,QAAQ,EAAE;MACvB,IAAI,CAAC1C,OAAO,CAACQ,SAAS,CAAC+E,KAAK,CAACR,cAAc,CAAC;IAChD,CAAC,MACI;MACD,IAAI,CAAC/E,OAAO,CAACQ,SAAS,CAACC,QAAQ,CAAC,CAAC;IACrC;EACJ;EACA;IAAS,IAAI,CAAC4D,IAAI,YAAAmB,2BAAAjB,CAAA;MAAA,YAAAA,CAAA,IAAyFY,kBAAkB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAACM,IAAI,kBA1C+E/J,EAAE,CAAAgK,iBAAA;MAAAZ,IAAA,EA0CJK,kBAAkB;MAAAQ,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,gCAAA/G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1ChBrD,EAAE,CAAAyG,WAAA,oBAAAnD,GAAA,CAAAgB,OAAA,CAAAwC,UAAA,eAAAxD,GAAA,CAAAyD,IAAA;UAAF/G,EAAE,CAAA+F,WAAA,eA0CJzC,GAAA,CAAAoG,IAAA,CAAK,CAAY,CAAC;UA1ChB1J,EAAE,CAAAqK,WAAA,cAAA/G,GAAA,CAAAe,MA0Ca,CAAC;QAAA;MAAA;MAAAiG,UAAA;MAAAC,QAAA,GA1ChBvK,EAAE,CAAAwK,kBAAA,CA0CoO,CAAChD,qBAAqB,CAAC,GA1C7PxH,EAAE,CAAAyK,uBAAA,EA0C2R7J,EAAE,CAACC,WAAW,IA1C3Sb,EAAE,CAAA0K,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAA1H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrD,EAAE,CAAAgE,UAAA,IAAAF,oCAAA,mBA0C8X,CAAC;UA1CjY9D,EAAE,CAAA+D,cAAA,YA0C0hB,CAAC;UA1C7hB/D,EAAE,CAAAmF,SAAA,WA0CutB,CAAC;UA1C1tBnF,EAAE,CAAA+D,cAAA,aA0CsuB,CAAC;UA1CzuB/D,EAAE,CAAAgE,UAAA,IAAAkB,0CAAA,yBA0Cg0B,CAAC;UA1Cn0BlF,EAAE,CAAAiE,YAAA,CA0Cw3C,CAAC,CAAO,CAAC;UA1Cn4CjE,EAAE,CAAAmF,SAAA,YA0Cg6C,CAAC;UA1Cn6CnF,EAAE,CAAAgE,UAAA,IAAAwB,oCAAA,mBA0Ci4D,CAAC;QAAA;QAAA,IAAAnC,EAAA;UA1Cp4DrD,EAAE,CAAAoE,UAAA,SAAAd,GAAA,CAAAe,MA0CiW,CAAC;UA1CpWrE,EAAE,CAAA4D,SAAA,EA0CqoB,CAAC;UA1CxoB5D,EAAE,CAAAqK,WAAA,uBAAA/G,GAAA,CAAAgB,OAAA,CAAAgB,SAAA,KAAAhC,GAAA,CAAAe,MA0CqoB,CAAC;UA1CxoBrE,EAAE,CAAAoE,UAAA,OAAAd,GAAA,CAAAgB,OAAA,CAAAoC,EA0CkqB,CAAC,gBAAApD,GAAA,CAAAgB,OAAA,CAAA2C,KAAwC,CAAC;UA1C9sBjH,EAAE,CAAA4D,SAAA,EA0CoyB,CAAC;UA1CvyB5D,EAAE,CAAAoE,UAAA,uBAAAd,GAAA,CAAAgB,OAAA,CAAA0G,OA0CoyB,CAAC,8BAAA1H,GAAA,CAAAgB,OAAwB,CAAC;UA1Ch0BtE,EAAE,CAAA4D,SAAA,EA0CuiD,CAAC;UA1C1iD5D,EAAE,CAAAoE,UAAA,SAAAd,GAAA,CAAAgB,OAAA,CAAAgB,SA0CuiD,CAAC;QAAA;MAAA;MAAA2F,YAAA,GAA0tGpL,SAAS,EAA8CC,IAAI,EAA6FuB,kBAAkB,EAA8HN,YAAY,EAAqFC,SAAS;MAAAkK,MAAA;IAAA,EAAgK;EAAE;AAC75K;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KA5CqGlJ,EAAE,CAAAmJ,iBAAA,CA4CXM,kBAAkB,EAAc,CAAC;IACjHL,IAAI,EAAEhJ,SAAS;IACf+K,IAAI,EAAE,CAAC;MAAEb,UAAU,EAAE,IAAI;MAAEc,QAAQ,EAAE,YAAY;MAAEC,OAAO,EAAE,CAACxL,SAAS,EAAEC,IAAI,EAAEuB,kBAAkB,EAAEN,YAAY,EAAEC,SAAS,CAAC;MAAEsK,eAAe,EAAEjL,uBAAuB,CAACkL,OAAO;MAAEC,SAAS,EAAE,CAAChE,qBAAqB,CAAC;MAAEiE,cAAc,EAAE,CAAC5K,WAAW,CAAC;MAAE6K,IAAI,EAAE;QAC3O,wBAAwB,EAAE,oBAAoB;QAC9C,kBAAkB,EAAE,MAAM;QAC1B,mBAAmB,EAAE,QAAQ;QAC7B,oBAAoB,EAAE;MAC1B,CAAC;MAAEZ,QAAQ,EAAE,wmDAAwmD;MAAEI,MAAM,EAAE,CAAC,iyFAAiyF;IAAE,CAAC;EACh7I,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAMS,gBAAgB,SAAS5I,iBAAiB,CAAC;EAC7C;IAAS,IAAI,CAAC4F,IAAI;MAAA,IAAAiD,6BAAA;MAAA,gBAAAC,yBAAAhD,CAAA;QAAA,QAAA+C,6BAAA,KAAAA,6BAAA,GAvD+E5L,EAAE,CAAA8L,qBAAA,CAuDQH,gBAAgB,IAAA9C,CAAA,IAAhB8C,gBAAgB;MAAA;IAAA,IAAsD;EAAE;EACnL;IAAS,IAAI,CAAC7C,KAAK,kBAxD8E9I,EAAE,CAAA+I,kBAAA;MAAAC,KAAA,EAwDY2C,gBAAgB;MAAA1C,OAAA,EAAAA,CAAA,MAAkC,MAAM,IAAI0C,gBAAgB,CAAC/E,WAAW,EAAE6C,kBAAkB,EAAExJ,MAAM,CAACmH,kBAAkB,CAAC,CAAC;MAAA2E,UAAA,EAA3G;IAAM,EAAwG;EAAE;AACjQ;AACA;EAAA,QAAA7C,SAAA,oBAAAA,SAAA,KA1DqGlJ,EAAE,CAAAmJ,iBAAA,CA0DXwC,gBAAgB,EAAc,CAAC;IAC/GvC,IAAI,EAAElJ,UAAU;IAChBiL,IAAI,EAAE,CAAC;MACCY,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAEA,CAAA,KAAM,IAAIL,gBAAgB,CAAC/E,WAAW,EAAE6C,kBAAkB,EAAExJ,MAAM,CAACmH,kBAAkB,CAAC;IACtG,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM6E,SAAS,SAASnJ,mBAAmB,CAAC;EACxC;IAAS,IAAI,CAAC6F,IAAI;MAAA,IAAAuD,sBAAA;MAAA,gBAAAC,kBAAAtD,CAAA;QAAA,QAAAqD,sBAAA,KAAAA,sBAAA,GAnE+ElM,EAAE,CAAA8L,qBAAA,CAmEQG,SAAS,IAAApD,CAAA,IAAToD,SAAS;MAAA;IAAA,IAAqD;EAAE;EAC3K;IAAS,IAAI,CAACG,IAAI,kBApE+EpM,EAAE,CAAAqM,iBAAA;MAAAjD,IAAA,EAoEJ6C,SAAS;MAAAhC,SAAA;MAAAqC,MAAA;QAAAhF,OAAA,GApEPtH,EAAE,CAAAuM,YAAA,CAAAC,IAAA;QAAAC,IAAA,GAAFzM,EAAE,CAAAuM,YAAA,CAAAC,IAAA;MAAA;MAAAE,OAAA;QAAAC,UAAA;MAAA;MAAArC,UAAA;MAAAC,QAAA,GAAFvK,EAAE,CAAAwK,kBAAA,CAoEyM,CAACxH,YAAY,CAAC2I,gBAAgB,CAAC,CAAC,GApE3O3L,EAAE,CAAA4M,0BAAA;IAAA,EAoEiR;EAAE;AAC1X;AACA;EAAA,QAAA1D,SAAA,oBAAAA,SAAA,KAtEqGlJ,EAAE,CAAAmJ,iBAAA,CAsEX8C,SAAS,EAAc,CAAC;IACxG7C,IAAI,EAAE9I,SAAS;IACf6K,IAAI,EAAE,CAAC;MACCb,UAAU,EAAE,IAAI;MAChBc,QAAQ,EAAE,wBAAwB;MAClCkB,MAAM,EAAE,CAAC,2BAA2B,EAAE,iBAAiB,CAAC;MACxDI,OAAO,EAAE,CAAC,6BAA6B,CAAC;MACxClB,SAAS,EAAE,CAACxI,YAAY,CAAC2I,gBAAgB,CAAC;IAC9C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASkB,SAASA,CAAClG,SAAS,EAAE;EAAEmG,QAAQ;EAAE,GAAGxF;AAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;EACzD,IAAI,CAACwF,QAAQ,EAAE;IACXvM,wBAAwB,CAACsM,SAAS,CAAC;IACnCC,QAAQ,GAAG7M,MAAM,CAACO,QAAQ,CAAC;EAC/B;EACA,MAAMuM,aAAa,GAAGD,QAAQ,CAACE,GAAG,CAACrB,gBAAgB,CAAC;EACpD,OAAQ3G,IAAI,IAAK+H,aAAa,CAACN,IAAI,CAAC,IAAInL,qBAAqB,CAACqF,SAAS,EAAEmG,QAAQ,CAAC,EAAE;IAChF,GAAGxF,OAAO;IACVtC;EACJ,CAAC,CAAC;AACN;AAEA,MAAMiI,UAAU,CAAC;EACbxF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACQ,EAAE,GAAGxF,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACyK,OAAO,GAAGzM,QAAQ,CAACR,MAAM,CAAC2G,WAAW,CAAC,EAAE;MAAEuG,YAAY,EAAE;IAAG,CAAC,CAAC;EACtE;EACA;IAAS,IAAI,CAACxE,IAAI,YAAAyE,mBAAAvE,CAAA;MAAA,YAAAA,CAAA,IAAyFoE,UAAU;IAAA,CAAmD;EAAE;EAC1K;IAAS,IAAI,CAAClD,IAAI,kBAnG+E/J,EAAE,CAAAgK,iBAAA;MAAAZ,IAAA,EAmGJ6D,UAAU;MAAAhD,SAAA;MAAAE,YAAA,WAAAkD,wBAAAhK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnGRrD,EAAE,CAAA0E,UAAA,8BAAA4I,+CAAA;YAAA,OAAAhK,GAAA,CAAA2E,EAAA,CAAAsF,SAAA,GAAAjK,GAAA,CAAA2E,EAAA,CAAAuF,YAAA,GAmG6B,CAAC;UAAA,CAAzB,CAAC;QAAA;MAAA;MAAAlD,UAAA;MAAAC,QAAA,GAnGRvK,EAAE,CAAA0K,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA2C,oBAAApK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrD,EAAE,CAAAmF,SAAA,YAmG0P,CAAC;UAnG7PnF,EAAE,CAAAgE,UAAA,IAAAuC,6BAAA,oBAmGsd,CAAC;QAAA;QAAA,IAAAlD,EAAA;UAnGzdrD,EAAE,CAAAqK,WAAA,sBAAA/G,GAAA,CAAA4J,OAAA,GAAAQ,MAmGiP,CAAC;UAnGpP1N,EAAE,CAAA4D,SAAA,CAmGwS,CAAC;UAnG3S5D,EAAE,CAAAoE,UAAA,YAAAd,GAAA,CAAA4J,OAAA,EAmGwS,CAAC;QAAA;MAAA;MAAAjC,YAAA,GAAu+DlL,OAAO,EAAmHsB,kBAAkB,EAA8HP,iBAAiB,EAAgEmC,YAAY,EAA2DC,iBAAiB,EAAgEC,YAAY;MAAA+H,MAAA;IAAA,EAAuF;EAAE;AAC/8F;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KArGqGlJ,EAAE,CAAAmJ,iBAAA,CAqGX8D,UAAU,EAAc,CAAC;IACzG7D,IAAI,EAAEhJ,SAAS;IACf+K,IAAI,EAAE,CAAC;MAAEb,UAAU,EAAE,IAAI;MAAEc,QAAQ,EAAE,aAAa;MAAEC,OAAO,EAAE,CACjDtL,OAAO,EACPsB,kBAAkB,EAClBP,iBAAiB,EACjBmC,YAAY,EACZC,iBAAiB,EACjBC,YAAY,CACf;MAAEmI,eAAe,EAAEjL,uBAAuB,CAACkL,OAAO;MAAEG,IAAI,EAAE;QACvD,oBAAoB,EAAE;MAC1B,CAAC;MAAEZ,QAAQ,EAAE,scAAsc;MAAEI,MAAM,EAAE,CAAC,knDAAknD;IAAE,CAAC;EAC/lE,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAStE,WAAW,EAAEO,iBAAiB,EAAEN,0BAA0B,EAAEO,kBAAkB,EAAE6E,SAAS,EAAEzE,qBAAqB,EAAEiC,kBAAkB,EAAEkC,gBAAgB,EAAEsB,UAAU,EAAEJ,SAAS,EAAExF,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}