{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, inject, Directive, ChangeDetectorRef, signal, ElementRef, ViewChild, Input, Output } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement, tuiIsCurrentTarget } from '@taiga-ui/cdk/utils/dom';\nimport * as i1$1 from '@taiga-ui/core/directives/hint';\nimport { TuiHintBaseComponent, TUI_HINT_PROVIDERS, TuiHintDirective, TUI_HINT_COMPONENT, TuiHint } from '@taiga-ui/core/directives/hint';\nimport { PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { BehaviorSubject, Subject, startWith, pairwise, switchMap, of, filter, map, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { TuiPositionAccessor, tuiAsPositionAccessor } from '@taiga-ui/core/classes';\nfunction TuiLineClampBox_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(text_r1);\n  }\n}\nfunction TuiLineClamp_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(text_r1);\n  }\n}\nconst TUI_LINE_CLAMP_DEFAULT_OPTIONS = {\n  showHint: true\n};\n/**\n * Default parameters for LineClamp component\n */\nconst TUI_LINE_CLAMP_OPTIONS = tuiCreateToken(TUI_LINE_CLAMP_DEFAULT_OPTIONS);\nfunction tuiLineClampOptionsProvider(options) {\n  return tuiProvideOptions(TUI_LINE_CLAMP_OPTIONS, options, TUI_LINE_CLAMP_DEFAULT_OPTIONS);\n}\nclass TuiLineClampBox extends TuiHintBaseComponent {\n  get width() {\n    return this.accessor.getClientRect().width;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiLineClampBox_BaseFactory;\n      return function TuiLineClampBox_Factory(t) {\n        return (ɵTuiLineClampBox_BaseFactory || (ɵTuiLineClampBox_BaseFactory = i0.ɵɵgetInheritedFactory(TuiLineClampBox)))(t || TuiLineClampBox);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiLineClampBox,\n      selectors: [[\"tui-line-clamp-box\"]],\n      hostVars: 2,\n      hostBindings: function TuiLineClampBox_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"min-width\", ctx.width, \"px\");\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature(TUI_HINT_PROVIDERS), i0.ɵɵHostDirectivesFeature([i1.TuiAnimated]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"polymorpheusOutlet\"]],\n      template: function TuiLineClampBox_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiLineClampBox_ng_container_0_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.content());\n        }\n      },\n      dependencies: [PolymorpheusOutlet],\n      styles: [\"[_nghost-%COMP%]{position:absolute;box-shadow:var(--tui-shadow-medium);inline-size:-webkit-min-content;inline-size:min-content;padding:.75rem 1rem;margin-left:calc(-1px - 1rem);margin-top:calc(-1px - .75rem);border-radius:var(--tui-radius-l);box-sizing:content-box;border:1px solid var(--tui-border-normal);background:var(--tui-background-base);color:var(--tui-text-primary);overflow-wrap:break-word}.tui-enter[_nghost-%COMP%], .tui-leave[_nghost-%COMP%]{animation-name:tuiFade}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiLineClampBox, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-line-clamp-box',\n      imports: [PolymorpheusOutlet],\n      template: `\n        <ng-container *polymorpheusOutlet=\"content() as text\">{{ text }}</ng-container>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: TUI_HINT_PROVIDERS,\n      hostDirectives: [TuiAnimated],\n      host: {\n        '[style.min-width.px]': 'width'\n      },\n      styles: [\":host{position:absolute;box-shadow:var(--tui-shadow-medium);inline-size:-webkit-min-content;inline-size:min-content;padding:.75rem 1rem;margin-left:calc(-1px - 1rem);margin-top:calc(-1px - .75rem);border-radius:var(--tui-radius-l);box-sizing:content-box;border:1px solid var(--tui-border-normal);background:var(--tui-background-base);color:var(--tui-text-primary);overflow-wrap:break-word}:host.tui-enter,:host.tui-leave{animation-name:tuiFade}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiLineClampPositionDirective extends TuiPositionAccessor {\n  constructor() {\n    super(...arguments);\n    this.accessor = inject(TuiHintDirective);\n    this.type = 'hint';\n  }\n  getPosition() {\n    const {\n      top,\n      left\n    } = this.accessor.getClientRect();\n    return [top, left];\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiLineClampPositionDirective_BaseFactory;\n      return function TuiLineClampPositionDirective_Factory(t) {\n        return (ɵTuiLineClampPositionDirective_BaseFactory || (ɵTuiLineClampPositionDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiLineClampPositionDirective)))(t || TuiLineClampPositionDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiLineClampPositionDirective,\n      selectors: [[\"\", \"tuiLineClampPosition\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsPositionAccessor(TuiLineClampPositionDirective)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiLineClampPositionDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiLineClampPosition]',\n      providers: [tuiAsPositionAccessor(TuiLineClampPositionDirective)]\n    }]\n  }], null, null);\n})();\nclass TuiLineClamp {\n  constructor() {\n    this.options = inject(TUI_LINE_CLAMP_OPTIONS);\n    this.el = tuiInjectElement();\n    this.cd = inject(ChangeDetectorRef);\n    this.linesLimit$ = new BehaviorSubject(1);\n    this.isOverflown$ = new Subject();\n    this.initialized = signal(false);\n    this.maxHeight = signal(0);\n    this.height = signal(0);\n    this.lineClamp = toSignal(this.linesLimit$.pipe(startWith(1), pairwise(), switchMap(([prev, next]) => next >= prev ? of(next) : tuiTypedFromEvent(this.el, 'transitionend').pipe(filter(tuiIsCurrentTarget), map(() => next))), takeUntilDestroyed()), {\n      initialValue: 0\n    });\n    this.lineHeight = 24;\n    this.overflownChange = this.isOverflown$.pipe(debounceTime(0), distinctUntilChanged());\n  }\n  set linesLimit(linesLimit) {\n    this.linesLimit$.next(linesLimit);\n  }\n  ngDoCheck() {\n    this.update();\n    this.isOverflown$.next(this.overflown);\n  }\n  ngAfterViewInit() {\n    this.initialized.set(true);\n  }\n  get overflown() {\n    if (!this.outlet) {\n      return false;\n    }\n    const {\n      scrollHeight,\n      scrollWidth\n    } = this.outlet.nativeElement;\n    const {\n      clientWidth\n    } = this.el;\n    return scrollHeight > this.maxHeight() || scrollWidth > clientWidth;\n  }\n  get computedContent() {\n    return this.options.showHint && this.overflown ? this.content : '';\n  }\n  updateView() {\n    this.cd.detectChanges();\n  }\n  update() {\n    if (this.outlet) {\n      this.height.set(this.outlet.nativeElement.scrollHeight);\n    }\n    this.maxHeight.set(this.lineHeight * this.linesLimit$.value);\n  }\n  static {\n    this.ɵfac = function TuiLineClamp_Factory(t) {\n      return new (t || TuiLineClamp)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiLineClamp,\n      selectors: [[\"tui-line-clamp\"]],\n      viewQuery: function TuiLineClamp_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TuiHintDirective, 5, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.outlet = _t.first);\n        }\n      },\n      hostVars: 6,\n      hostBindings: function TuiLineClamp_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"transitionend\", function TuiLineClamp_transitionend_HostBindingHandler() {\n            return ctx.updateView();\n          })(\"mouseenter\", function TuiLineClamp_mouseenter_HostBindingHandler() {\n            return ctx.updateView();\n          })(\"resize\", function TuiLineClamp_resize_HostBindingHandler() {\n            return ctx.updateView();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"height\", ctx.height(), \"px\")(\"max-height\", ctx.maxHeight(), \"px\");\n          i0.ɵɵclassProp(\"_initialized\", ctx.initialized());\n        }\n      },\n      inputs: {\n        lineHeight: \"lineHeight\",\n        content: \"content\",\n        linesLimit: \"linesLimit\"\n      },\n      outputs: {\n        overflownChange: \"overflownChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: TUI_HINT_COMPONENT,\n        useValue: TuiLineClampBox\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 6,\n      consts: [[\"tuiLineClampPosition\", \"\", 1, \"t-wrapper\", 3, \"tuiHint\"], [4, \"polymorpheusOutlet\"]],\n      template: function TuiLineClamp_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, TuiLineClamp_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"-webkit-line-clamp\", ctx.lineClamp())(\"word-break\", ctx.lineClamp() > 1 ? \"break-word\" : \"break-all\");\n          i0.ɵɵproperty(\"tuiHint\", ctx.computedContent);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.content);\n        }\n      },\n      dependencies: [PolymorpheusOutlet, i1$1.TuiHintDirective, TuiLineClampPositionDirective],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:block;overflow:hidden}._initialized[_nghost-%COMP%]{transition-property:max-height;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out}.t-wrapper[_ngcontent-%COMP%]{display:-webkit-box;-webkit-box-orient:vertical;overflow:hidden;overflow-wrap:anywhere}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiLineClamp, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-line-clamp',\n      imports: [PolymorpheusOutlet, TuiHint, TuiLineClampPositionDirective],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: TUI_HINT_COMPONENT,\n        useValue: TuiLineClampBox\n      }],\n      host: {\n        '[style.height.px]': 'height()',\n        '[style.max-height.px]': 'maxHeight()',\n        '[class._initialized]': 'initialized()',\n        '(transitionend)': 'updateView()',\n        '(mouseenter)': 'updateView()',\n        '(resize)': 'updateView()'\n      },\n      template: \"<div\\n    tuiLineClampPosition\\n    class=\\\"t-wrapper\\\"\\n    [style.-webkit-line-clamp]=\\\"lineClamp()\\\"\\n    [style.word-break]=\\\"lineClamp() > 1 ? 'break-word' : 'break-all'\\\"\\n    [tuiHint]=\\\"computedContent\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"content as text\\\">{{ text }}</ng-container>\\n</div>\\n\",\n      styles: [\":host{position:relative;display:block;overflow:hidden}:host._initialized{transition-property:max-height;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out}.t-wrapper{display:-webkit-box;-webkit-box-orient:vertical;overflow:hidden;overflow-wrap:anywhere}\\n\"]\n    }]\n  }], null, {\n    outlet: [{\n      type: ViewChild,\n      args: [TuiHintDirective, {\n        read: ElementRef\n      }]\n    }],\n    lineHeight: [{\n      type: Input\n    }],\n    content: [{\n      type: Input\n    }],\n    overflownChange: [{\n      type: Output\n    }],\n    linesLimit: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_LINE_CLAMP_DEFAULT_OPTIONS, TUI_LINE_CLAMP_OPTIONS, TuiLineClamp, TuiLineClampBox, tuiLineClampOptionsProvider };", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "inject", "Directive", "ChangeDetectorRef", "signal", "ElementRef", "ViewChild", "Input", "Output", "toSignal", "takeUntilDestroyed", "tuiTypedFromEvent", "tuiInjectElement", "tuiIsCurrentTarget", "i1$1", "TuiHintBaseComponent", "TUI_HINT_PROVIDERS", "TuiHintDirective", "TUI_HINT_COMPONENT", "TuiHint", "Polymorpheus<PERSON><PERSON>let", "BehaviorSubject", "Subject", "startWith", "pairwise", "switchMap", "of", "filter", "map", "debounceTime", "distinctUntilChanged", "tuiCreateToken", "tuiProvideOptions", "i1", "TuiAnimated", "TuiPositionAccessor", "tuiAsPositionAccessor", "TuiLineClampBox_ng_container_0_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "text_r1", "polymorpheusOutlet", "ɵɵadvance", "ɵɵtextInterpolate", "TuiLineClamp_ng_container_1_Template", "TUI_LINE_CLAMP_DEFAULT_OPTIONS", "showHint", "TUI_LINE_CLAMP_OPTIONS", "tuiLineClampOptionsProvider", "options", "TuiLineClampBox", "width", "accessor", "getClientRect", "ɵfac", "ɵTuiLineClampBox_BaseFactory", "TuiLineClampBox_Factory", "t", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "TuiLineClampBox_HostBindings", "ɵɵstyleProp", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiLineClampBox_Template", "ɵɵtemplate", "ɵɵproperty", "content", "dependencies", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "hostDirectives", "host", "TuiLineClampPositionDirective", "constructor", "arguments", "getPosition", "top", "left", "ɵTuiLineClampPositionDirective_BaseFactory", "TuiLineClampPositionDirective_Factory", "ɵdir", "ɵɵdefineDirective", "TuiLineClamp", "el", "cd", "linesLimit$", "isOverflown$", "initialized", "maxHeight", "height", "lineClamp", "pipe", "prev", "next", "initialValue", "lineHeight", "overflownChange", "linesLimit", "ngDoCheck", "update", "overflown", "ngAfterViewInit", "set", "outlet", "scrollHeight", "scrollWidth", "nativeElement", "clientWidth", "computedContent", "updateView", "detectChanges", "value", "TuiLineClamp_Factory", "viewQuery", "TuiLineClamp_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "TuiLineClamp_HostBindings", "ɵɵlistener", "TuiLineClamp_transitionend_HostBindingHandler", "TuiLineClamp_mouseenter_HostBindingHandler", "TuiLineClamp_resize_HostBindingHandler", "ɵɵclassProp", "inputs", "outputs", "provide", "useValue", "TuiLineClamp_Template", "ɵɵelementStart", "ɵɵelementEnd", "read"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-line-clamp.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, inject, Directive, ChangeDetectorRef, signal, ElementRef, ViewChild, Input, Output } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement, tuiIsCurrentTarget } from '@taiga-ui/cdk/utils/dom';\nimport * as i1$1 from '@taiga-ui/core/directives/hint';\nimport { TuiHintBaseComponent, TUI_HINT_PROVIDERS, TuiHintDirective, TUI_HINT_COMPONENT, TuiHint } from '@taiga-ui/core/directives/hint';\nimport { PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { BehaviorSubject, Subject, startWith, pairwise, switchMap, of, filter, map, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { TuiPositionAccessor, tuiAsPositionAccessor } from '@taiga-ui/core/classes';\n\nconst TUI_LINE_CLAMP_DEFAULT_OPTIONS = {\n    showHint: true,\n};\n/**\n * Default parameters for LineClamp component\n */\nconst TUI_LINE_CLAMP_OPTIONS = tuiCreateToken(TUI_LINE_CLAMP_DEFAULT_OPTIONS);\nfunction tuiLineClampOptionsProvider(options) {\n    return tuiProvideOptions(TUI_LINE_CLAMP_OPTIONS, options, TUI_LINE_CLAMP_DEFAULT_OPTIONS);\n}\n\nclass TuiLineClampBox extends TuiHintBaseComponent {\n    get width() {\n        return this.accessor.getClientRect().width;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLineClampBox, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiLineClampBox, isStandalone: true, selector: \"tui-line-clamp-box\", host: { properties: { \"style.min-width.px\": \"width\" } }, providers: TUI_HINT_PROVIDERS, usesInheritance: true, hostDirectives: [{ directive: i1.TuiAnimated }], ngImport: i0, template: `\n        <ng-container *polymorpheusOutlet=\"content() as text\">{{ text }}</ng-container>\n    `, isInline: true, styles: [\":host{position:absolute;box-shadow:var(--tui-shadow-medium);inline-size:-webkit-min-content;inline-size:min-content;padding:.75rem 1rem;margin-left:calc(-1px - 1rem);margin-top:calc(-1px - .75rem);border-radius:var(--tui-radius-l);box-sizing:content-box;border:1px solid var(--tui-border-normal);background:var(--tui-background-base);color:var(--tui-text-primary);overflow-wrap:break-word}:host.tui-enter,:host.tui-leave{animation-name:tuiFade}\\n\"], dependencies: [{ kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLineClampBox, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-line-clamp-box', imports: [PolymorpheusOutlet], template: `\n        <ng-container *polymorpheusOutlet=\"content() as text\">{{ text }}</ng-container>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, providers: TUI_HINT_PROVIDERS, hostDirectives: [TuiAnimated], host: {\n                        '[style.min-width.px]': 'width',\n                    }, styles: [\":host{position:absolute;box-shadow:var(--tui-shadow-medium);inline-size:-webkit-min-content;inline-size:min-content;padding:.75rem 1rem;margin-left:calc(-1px - 1rem);margin-top:calc(-1px - .75rem);border-radius:var(--tui-radius-l);box-sizing:content-box;border:1px solid var(--tui-border-normal);background:var(--tui-background-base);color:var(--tui-text-primary);overflow-wrap:break-word}:host.tui-enter,:host.tui-leave{animation-name:tuiFade}\\n\"] }]\n        }] });\n\nclass TuiLineClampPositionDirective extends TuiPositionAccessor {\n    constructor() {\n        super(...arguments);\n        this.accessor = inject(TuiHintDirective);\n        this.type = 'hint';\n    }\n    getPosition() {\n        const { top, left } = this.accessor.getClientRect();\n        return [top, left];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLineClampPositionDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiLineClampPositionDirective, isStandalone: true, selector: \"[tuiLineClampPosition]\", providers: [tuiAsPositionAccessor(TuiLineClampPositionDirective)], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLineClampPositionDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiLineClampPosition]',\n                    providers: [tuiAsPositionAccessor(TuiLineClampPositionDirective)],\n                }]\n        }] });\n\nclass TuiLineClamp {\n    constructor() {\n        this.options = inject(TUI_LINE_CLAMP_OPTIONS);\n        this.el = tuiInjectElement();\n        this.cd = inject(ChangeDetectorRef);\n        this.linesLimit$ = new BehaviorSubject(1);\n        this.isOverflown$ = new Subject();\n        this.initialized = signal(false);\n        this.maxHeight = signal(0);\n        this.height = signal(0);\n        this.lineClamp = toSignal(this.linesLimit$.pipe(startWith(1), pairwise(), switchMap(([prev, next]) => next >= prev\n            ? of(next)\n            : tuiTypedFromEvent(this.el, 'transitionend').pipe(filter(tuiIsCurrentTarget), map(() => next))), takeUntilDestroyed()), { initialValue: 0 });\n        this.lineHeight = 24;\n        this.overflownChange = this.isOverflown$.pipe(debounceTime(0), distinctUntilChanged());\n    }\n    set linesLimit(linesLimit) {\n        this.linesLimit$.next(linesLimit);\n    }\n    ngDoCheck() {\n        this.update();\n        this.isOverflown$.next(this.overflown);\n    }\n    ngAfterViewInit() {\n        this.initialized.set(true);\n    }\n    get overflown() {\n        if (!this.outlet) {\n            return false;\n        }\n        const { scrollHeight, scrollWidth } = this.outlet.nativeElement;\n        const { clientWidth } = this.el;\n        return scrollHeight > this.maxHeight() || scrollWidth > clientWidth;\n    }\n    get computedContent() {\n        return this.options.showHint && this.overflown ? this.content : '';\n    }\n    updateView() {\n        this.cd.detectChanges();\n    }\n    update() {\n        if (this.outlet) {\n            this.height.set(this.outlet.nativeElement.scrollHeight);\n        }\n        this.maxHeight.set(this.lineHeight * this.linesLimit$.value);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLineClamp, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiLineClamp, isStandalone: true, selector: \"tui-line-clamp\", inputs: { lineHeight: \"lineHeight\", content: \"content\", linesLimit: \"linesLimit\" }, outputs: { overflownChange: \"overflownChange\" }, host: { listeners: { \"transitionend\": \"updateView()\", \"mouseenter\": \"updateView()\", \"resize\": \"updateView()\" }, properties: { \"style.height.px\": \"height()\", \"style.max-height.px\": \"maxHeight()\", \"class._initialized\": \"initialized()\" } }, providers: [\n            {\n                provide: TUI_HINT_COMPONENT,\n                useValue: TuiLineClampBox,\n            },\n        ], viewQueries: [{ propertyName: \"outlet\", first: true, predicate: TuiHintDirective, descendants: true, read: ElementRef }], ngImport: i0, template: \"<div\\n    tuiLineClampPosition\\n    class=\\\"t-wrapper\\\"\\n    [style.-webkit-line-clamp]=\\\"lineClamp()\\\"\\n    [style.word-break]=\\\"lineClamp() > 1 ? 'break-word' : 'break-all'\\\"\\n    [tuiHint]=\\\"computedContent\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"content as text\\\">{{ text }}</ng-container>\\n</div>\\n\", styles: [\":host{position:relative;display:block;overflow:hidden}:host._initialized{transition-property:max-height;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out}.t-wrapper{display:-webkit-box;-webkit-box-orient:vertical;overflow:hidden;overflow-wrap:anywhere}\\n\"], dependencies: [{ kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: i1$1.TuiHintDirective, selector: \"[tuiHint]:not(ng-container):not(ng-template)\", inputs: [\"tuiHintContext\", \"tuiHintAppearance\", \"tuiHint\"] }, { kind: \"directive\", type: TuiLineClampPositionDirective, selector: \"[tuiLineClampPosition]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLineClamp, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-line-clamp', imports: [PolymorpheusOutlet, TuiHint, TuiLineClampPositionDirective], changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        {\n                            provide: TUI_HINT_COMPONENT,\n                            useValue: TuiLineClampBox,\n                        },\n                    ], host: {\n                        '[style.height.px]': 'height()',\n                        '[style.max-height.px]': 'maxHeight()',\n                        '[class._initialized]': 'initialized()',\n                        '(transitionend)': 'updateView()',\n                        '(mouseenter)': 'updateView()',\n                        '(resize)': 'updateView()',\n                    }, template: \"<div\\n    tuiLineClampPosition\\n    class=\\\"t-wrapper\\\"\\n    [style.-webkit-line-clamp]=\\\"lineClamp()\\\"\\n    [style.word-break]=\\\"lineClamp() > 1 ? 'break-word' : 'break-all'\\\"\\n    [tuiHint]=\\\"computedContent\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"content as text\\\">{{ text }}</ng-container>\\n</div>\\n\", styles: [\":host{position:relative;display:block;overflow:hidden}:host._initialized{transition-property:max-height;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out}.t-wrapper{display:-webkit-box;-webkit-box-orient:vertical;overflow:hidden;overflow-wrap:anywhere}\\n\"] }]\n        }], propDecorators: { outlet: [{\n                type: ViewChild,\n                args: [TuiHintDirective, { read: ElementRef }]\n            }], lineHeight: [{\n                type: Input\n            }], content: [{\n                type: Input\n            }], overflownChange: [{\n                type: Output\n            }], linesLimit: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_LINE_CLAMP_DEFAULT_OPTIONS, TUI_LINE_CLAMP_OPTIONS, TuiLineClamp, TuiLineClampBox, tuiLineClampOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AACtJ,SAASC,QAAQ,EAAEC,kBAAkB,QAAQ,4BAA4B;AACzE,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,gBAAgB,EAAEC,kBAAkB,QAAQ,yBAAyB;AAC9E,OAAO,KAAKC,IAAI,MAAM,gCAAgC;AACtD,SAASC,oBAAoB,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,OAAO,QAAQ,gCAAgC;AACxI,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,eAAe,EAAEC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,EAAE,EAAEC,MAAM,EAAEC,GAAG,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,MAAM;AACpI,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,mCAAmC;AACrF,OAAO,KAAKC,EAAE,MAAM,mCAAmC;AACvD,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,mBAAmB,EAAEC,qBAAqB,QAAQ,wBAAwB;AAAC,SAAAC,wCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAiBiBxC,EAAE,CAAA0C,uBAAA,EAE1C,CAAC;IAFuC1C,EAAE,CAAA2C,MAAA,EAEhC,CAAC;IAF6B3C,EAAE,CAAA4C,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAAF9C,EAAE,CAAA+C,SAAA,CAEhC,CAAC;IAF6B/C,EAAE,CAAAgD,iBAAA,CAAAH,OAEhC,CAAC;EAAA;AAAA;AAAA,SAAAI,qCAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAF6BxC,EAAE,CAAA0C,uBAAA,EAwFwU,CAAC;IAxF3U1C,EAAE,CAAA2C,MAAA,EAwFkV,CAAC;IAxFrV3C,EAAE,CAAA4C,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAAF9C,EAAE,CAAA+C,SAAA,CAwFkV,CAAC;IAxFrV/C,EAAE,CAAAgD,iBAAA,CAAAH,OAwFkV,CAAC;EAAA;AAAA;AAvG1b,MAAMK,8BAA8B,GAAG;EACnCC,QAAQ,EAAE;AACd,CAAC;AACD;AACA;AACA;AACA,MAAMC,sBAAsB,GAAGnB,cAAc,CAACiB,8BAA8B,CAAC;AAC7E,SAASG,2BAA2BA,CAACC,OAAO,EAAE;EAC1C,OAAOpB,iBAAiB,CAACkB,sBAAsB,EAAEE,OAAO,EAAEJ,8BAA8B,CAAC;AAC7F;AAEA,MAAMK,eAAe,SAAStC,oBAAoB,CAAC;EAC/C,IAAIuC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,QAAQ,CAACC,aAAa,CAAC,CAAC,CAACF,KAAK;EAC9C;EACA;IAAS,IAAI,CAACG,IAAI;MAAA,IAAAC,4BAAA;MAAA,gBAAAC,wBAAAC,CAAA;QAAA,QAAAF,4BAAA,KAAAA,4BAAA,GAA+E5D,EAAE,CAAA+D,qBAAA,CAAQR,eAAe,IAAAO,CAAA,IAAfP,eAAe;MAAA;IAAA,IAAqD;EAAE;EACjL;IAAS,IAAI,CAACS,IAAI,kBAD+EhE,EAAE,CAAAiE,iBAAA;MAAAC,IAAA,EACJX,eAAe;MAAAY,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,6BAAA9B,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADbxC,EAAE,CAAAuE,WAAA,cAAA9B,GAAA,CAAAe,KAAA,MACU,CAAC;QAAA;MAAA;MAAAgB,UAAA;MAAAC,QAAA,GADbzE,EAAE,CAAA0E,kBAAA,CACqIxD,kBAAkB,GADzJlB,EAAE,CAAA2E,uBAAA,EAC8MxC,EAAE,CAACC,WAAW,IAD9NpC,EAAE,CAAA4E,0BAAA,EAAF5E,EAAE,CAAA6E,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAA1C,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFxC,EAAE,CAAAmF,UAAA,IAAA5C,uCAAA,yBAE1C,CAAC;QAAA;QAAA,IAAAC,EAAA;UAFuCxC,EAAE,CAAAoF,UAAA,uBAAA3C,GAAA,CAAA4C,OAAA,EAEnD,CAAC;QAAA;MAAA;MAAAC,YAAA,GACwdhE,kBAAkB;MAAAiE,MAAA;MAAAC,eAAA;IAAA,EAAyJ;EAAE;AAC1rB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KALqGzF,EAAE,CAAA0F,iBAAA,CAKXnC,eAAe,EAAc,CAAC;IAC9GW,IAAI,EAAEjE,SAAS;IACf0F,IAAI,EAAE,CAAC;MAAEnB,UAAU,EAAE,IAAI;MAAEoB,QAAQ,EAAE,oBAAoB;MAAEC,OAAO,EAAE,CAACvE,kBAAkB,CAAC;MAAE2D,QAAQ,EAAE;AAChH;AACA,KAAK;MAAEO,eAAe,EAAEtF,uBAAuB,CAAC4F,MAAM;MAAEC,SAAS,EAAE7E,kBAAkB;MAAE8E,cAAc,EAAE,CAAC5D,WAAW,CAAC;MAAE6D,IAAI,EAAE;QACpG,sBAAsB,EAAE;MAC5B,CAAC;MAAEV,MAAM,EAAE,CAAC,gcAAgc;IAAE,CAAC;EAC3d,CAAC,CAAC;AAAA;AAEV,MAAMW,6BAA6B,SAAS7D,mBAAmB,CAAC;EAC5D8D,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAAC3C,QAAQ,GAAGtD,MAAM,CAACgB,gBAAgB,CAAC;IACxC,IAAI,CAAC+C,IAAI,GAAG,MAAM;EACtB;EACAmC,WAAWA,CAAA,EAAG;IACV,MAAM;MAAEC,GAAG;MAAEC;IAAK,CAAC,GAAG,IAAI,CAAC9C,QAAQ,CAACC,aAAa,CAAC,CAAC;IACnD,OAAO,CAAC4C,GAAG,EAAEC,IAAI,CAAC;EACtB;EACA;IAAS,IAAI,CAAC5C,IAAI;MAAA,IAAA6C,0CAAA;MAAA,gBAAAC,sCAAA3C,CAAA;QAAA,QAAA0C,0CAAA,KAAAA,0CAAA,GAxB+ExG,EAAE,CAAA+D,qBAAA,CAwBQmC,6BAA6B,IAAApC,CAAA,IAA7BoC,6BAA6B;MAAA;IAAA,IAAqD;EAAE;EAC/L;IAAS,IAAI,CAACQ,IAAI,kBAzB+E1G,EAAE,CAAA2G,iBAAA;MAAAzC,IAAA,EAyBJgC,6BAA6B;MAAA/B,SAAA;MAAAK,UAAA;MAAAC,QAAA,GAzB3BzE,EAAE,CAAA0E,kBAAA,CAyB8F,CAACpC,qBAAqB,CAAC4D,6BAA6B,CAAC,CAAC,GAzBtJlG,EAAE,CAAA4E,0BAAA;IAAA,EAyB4L;EAAE;AACrS;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KA3BqGzF,EAAE,CAAA0F,iBAAA,CA2BXQ,6BAA6B,EAAc,CAAC;IAC5HhC,IAAI,EAAE9D,SAAS;IACfuF,IAAI,EAAE,CAAC;MACCnB,UAAU,EAAE,IAAI;MAChBoB,QAAQ,EAAE,wBAAwB;MAClCG,SAAS,EAAE,CAACzD,qBAAqB,CAAC4D,6BAA6B,CAAC;IACpE,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMU,YAAY,CAAC;EACfT,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC7C,OAAO,GAAGnD,MAAM,CAACiD,sBAAsB,CAAC;IAC7C,IAAI,CAACyD,EAAE,GAAG/F,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACgG,EAAE,GAAG3G,MAAM,CAACE,iBAAiB,CAAC;IACnC,IAAI,CAAC0G,WAAW,GAAG,IAAIxF,eAAe,CAAC,CAAC,CAAC;IACzC,IAAI,CAACyF,YAAY,GAAG,IAAIxF,OAAO,CAAC,CAAC;IACjC,IAAI,CAACyF,WAAW,GAAG3G,MAAM,CAAC,KAAK,CAAC;IAChC,IAAI,CAAC4G,SAAS,GAAG5G,MAAM,CAAC,CAAC,CAAC;IAC1B,IAAI,CAAC6G,MAAM,GAAG7G,MAAM,CAAC,CAAC,CAAC;IACvB,IAAI,CAAC8G,SAAS,GAAGzG,QAAQ,CAAC,IAAI,CAACoG,WAAW,CAACM,IAAI,CAAC5F,SAAS,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC,CAAC,EAAEC,SAAS,CAAC,CAAC,CAAC2F,IAAI,EAAEC,IAAI,CAAC,KAAKA,IAAI,IAAID,IAAI,GAC5G1F,EAAE,CAAC2F,IAAI,CAAC,GACR1G,iBAAiB,CAAC,IAAI,CAACgG,EAAE,EAAE,eAAe,CAAC,CAACQ,IAAI,CAACxF,MAAM,CAACd,kBAAkB,CAAC,EAAEe,GAAG,CAAC,MAAMyF,IAAI,CAAC,CAAC,CAAC,EAAE3G,kBAAkB,CAAC,CAAC,CAAC,EAAE;MAAE4G,YAAY,EAAE;IAAE,CAAC,CAAC;IACjJ,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,eAAe,GAAG,IAAI,CAACV,YAAY,CAACK,IAAI,CAACtF,YAAY,CAAC,CAAC,CAAC,EAAEC,oBAAoB,CAAC,CAAC,CAAC;EAC1F;EACA,IAAI2F,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,CAACZ,WAAW,CAACQ,IAAI,CAACI,UAAU,CAAC;EACrC;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,MAAM,CAAC,CAAC;IACb,IAAI,CAACb,YAAY,CAACO,IAAI,CAAC,IAAI,CAACO,SAAS,CAAC;EAC1C;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACd,WAAW,CAACe,GAAG,CAAC,IAAI,CAAC;EAC9B;EACA,IAAIF,SAASA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACG,MAAM,EAAE;MACd,OAAO,KAAK;IAChB;IACA,MAAM;MAAEC,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI,CAACF,MAAM,CAACG,aAAa;IAC/D,MAAM;MAAEC;IAAY,CAAC,GAAG,IAAI,CAACxB,EAAE;IAC/B,OAAOqB,YAAY,GAAG,IAAI,CAAChB,SAAS,CAAC,CAAC,IAAIiB,WAAW,GAAGE,WAAW;EACvE;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAChF,OAAO,CAACH,QAAQ,IAAI,IAAI,CAAC2E,SAAS,GAAG,IAAI,CAACzC,OAAO,GAAG,EAAE;EACtE;EACAkD,UAAUA,CAAA,EAAG;IACT,IAAI,CAACzB,EAAE,CAAC0B,aAAa,CAAC,CAAC;EAC3B;EACAX,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACI,MAAM,EAAE;MACb,IAAI,CAACd,MAAM,CAACa,GAAG,CAAC,IAAI,CAACC,MAAM,CAACG,aAAa,CAACF,YAAY,CAAC;IAC3D;IACA,IAAI,CAAChB,SAAS,CAACc,GAAG,CAAC,IAAI,CAACP,UAAU,GAAG,IAAI,CAACV,WAAW,CAAC0B,KAAK,CAAC;EAChE;EACA;IAAS,IAAI,CAAC9E,IAAI,YAAA+E,qBAAA5E,CAAA;MAAA,YAAAA,CAAA,IAAyF8C,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAAC5C,IAAI,kBAnF+EhE,EAAE,CAAAiE,iBAAA;MAAAC,IAAA,EAmFJ0C,YAAY;MAAAzC,SAAA;MAAAwE,SAAA,WAAAC,mBAAApG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnFVxC,EAAE,CAAA6I,WAAA,CAwF5B1H,gBAAgB,KAA2BZ,UAAU;QAAA;QAAA,IAAAiC,EAAA;UAAA,IAAAsG,EAAA;UAxF3B9I,EAAE,CAAA+I,cAAA,CAAAD,EAAA,GAAF9I,EAAE,CAAAgJ,WAAA,QAAAvG,GAAA,CAAAwF,MAAA,GAAAa,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAA7E,QAAA;MAAAC,YAAA,WAAA6E,0BAAA1G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFxC,EAAE,CAAAmJ,UAAA,2BAAAC,8CAAA;YAAA,OAmFJ3G,GAAA,CAAA8F,UAAA,CAAW,CAAC;UAAA,CAAD,CAAC,wBAAAc,2CAAA;YAAA,OAAZ5G,GAAA,CAAA8F,UAAA,CAAW,CAAC;UAAA,CAAD,CAAC,oBAAAe,uCAAA;YAAA,OAAZ7G,GAAA,CAAA8F,UAAA,CAAW,CAAC;UAAA,CAAD,CAAC;QAAA;QAAA,IAAA/F,EAAA;UAnFVxC,EAAE,CAAAuE,WAAA,WAmFJ9B,GAAA,CAAA0E,MAAA,CAAO,CAAC,MAAG,CAAC,eAAZ1E,GAAA,CAAAyE,SAAA,CAAU,CAAC,OAAC;UAnFVlH,EAAE,CAAAuJ,WAAA,iBAmFJ9G,GAAA,CAAAwE,WAAA,CAAY,CAAD,CAAC;QAAA;MAAA;MAAAuC,MAAA;QAAA/B,UAAA;QAAApC,OAAA;QAAAsC,UAAA;MAAA;MAAA8B,OAAA;QAAA/B,eAAA;MAAA;MAAAlD,UAAA;MAAAC,QAAA,GAnFVzE,EAAE,CAAA0E,kBAAA,CAmFwb,CACnhB;QACIgF,OAAO,EAAEtI,kBAAkB;QAC3BuI,QAAQ,EAAEpG;MACd,CAAC,CACJ,GAxF4FvD,EAAE,CAAA6E,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA2E,sBAAApH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFxC,EAAE,CAAA6J,cAAA,YAwF4Q,CAAC;UAxF/Q7J,EAAE,CAAAmF,UAAA,IAAAlC,oCAAA,yBAwFwU,CAAC;UAxF3UjD,EAAE,CAAA8J,YAAA,CAwFyW,CAAC;QAAA;QAAA,IAAAtH,EAAA;UAxF5WxC,EAAE,CAAAuE,WAAA,uBAAA9B,GAAA,CAAA2E,SAAA,EAwF6J,CAAC,eAAA3E,GAAA,CAAA2E,SAAA,mCAAwE,CAAC;UAxFzOpH,EAAE,CAAAoF,UAAA,YAAA3C,GAAA,CAAA6F,eAwFyQ,CAAC;UAxF5QtI,EAAE,CAAA+C,SAAA,CAwF8T,CAAC;UAxFjU/C,EAAE,CAAAoF,UAAA,uBAAA3C,GAAA,CAAA4C,OAwF8T,CAAC;QAAA;MAAA;MAAAC,YAAA,GAAwYhE,kBAAkB,EAA8HN,IAAI,CAACG,gBAAgB,EAAqJ+E,6BAA6B;MAAAX,MAAA;MAAAC,eAAA;IAAA,EAA8F;EAAE;AACruC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA1FqGzF,EAAE,CAAA0F,iBAAA,CA0FXkB,YAAY,EAAc,CAAC;IAC3G1C,IAAI,EAAEjE,SAAS;IACf0F,IAAI,EAAE,CAAC;MAAEnB,UAAU,EAAE,IAAI;MAAEoB,QAAQ,EAAE,gBAAgB;MAAEC,OAAO,EAAE,CAACvE,kBAAkB,EAAED,OAAO,EAAE6E,6BAA6B,CAAC;MAAEV,eAAe,EAAEtF,uBAAuB,CAAC4F,MAAM;MAAEC,SAAS,EAAE,CAC9K;QACI2D,OAAO,EAAEtI,kBAAkB;QAC3BuI,QAAQ,EAAEpG;MACd,CAAC,CACJ;MAAE0C,IAAI,EAAE;QACL,mBAAmB,EAAE,UAAU;QAC/B,uBAAuB,EAAE,aAAa;QACtC,sBAAsB,EAAE,eAAe;QACvC,iBAAiB,EAAE,cAAc;QACjC,cAAc,EAAE,cAAc;QAC9B,UAAU,EAAE;MAChB,CAAC;MAAEhB,QAAQ,EAAE,uTAAuT;MAAEM,MAAM,EAAE,CAAC,kSAAkS;IAAE,CAAC;EAChoB,CAAC,CAAC,QAAkB;IAAE0C,MAAM,EAAE,CAAC;MACvB/D,IAAI,EAAE1D,SAAS;MACfmF,IAAI,EAAE,CAACxE,gBAAgB,EAAE;QAAE4I,IAAI,EAAExJ;MAAW,CAAC;IACjD,CAAC,CAAC;IAAEkH,UAAU,EAAE,CAAC;MACbvD,IAAI,EAAEzD;IACV,CAAC,CAAC;IAAE4E,OAAO,EAAE,CAAC;MACVnB,IAAI,EAAEzD;IACV,CAAC,CAAC;IAAEiH,eAAe,EAAE,CAAC;MAClBxD,IAAI,EAAExD;IACV,CAAC,CAAC;IAAEiH,UAAU,EAAE,CAAC;MACbzD,IAAI,EAAEzD;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASyC,8BAA8B,EAAEE,sBAAsB,EAAEwD,YAAY,EAAErD,eAAe,EAAEF,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}