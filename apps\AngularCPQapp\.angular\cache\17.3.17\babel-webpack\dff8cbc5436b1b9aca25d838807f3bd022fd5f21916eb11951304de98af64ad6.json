{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Injectable, Directive } from '@angular/core';\nimport { tuiTypedFromEvent, tuiPreventDefault } from '@taiga-ui/cdk/observables';\nimport { tuiCreateToken, tuiInjectElement, tuiDistanceBetweenTouches } from '@taiga-ui/cdk/utils';\nimport { Observable, merge, filter, switchMap, scan, map, takeUntil } from 'rxjs';\n\n/**\n * Zoom options\n */\nconst TUI_ZOOM_OPTIONS = tuiCreateToken({\n  wheelSensitivity: 0.01\n});\nconst TOUCH_SENSITIVITY = 0.01;\nclass TuiZoomService extends Observable {\n  constructor() {\n    const el = tuiInjectElement();\n    const {\n      wheelSensitivity\n    } = inject(TUI_ZOOM_OPTIONS);\n    super(subscriber => merge(tuiTypedFromEvent(el, 'touchstart', {\n      passive: true\n    }).pipe(filter(({\n      touches\n    }) => touches.length > 1), switchMap(startEvent => tuiTypedFromEvent(el, 'touchmove', {\n      passive: true\n    }).pipe(tuiPreventDefault(), scan((prev, event) => {\n      const distance = tuiDistanceBetweenTouches(event);\n      return {\n        event,\n        distance,\n        delta: (distance - prev.distance) * TOUCH_SENSITIVITY\n      };\n    }, {\n      event: startEvent,\n      distance: tuiDistanceBetweenTouches(startEvent),\n      delta: 0\n    }), map(({\n      event,\n      delta\n    }) => {\n      const clientX = ((event.touches[0]?.clientX ?? 0) + (event.touches[1]?.clientX ?? 0)) / 2;\n      const clientY = ((event.touches[0]?.clientY ?? 0) + (event.touches[1]?.clientY ?? 0)) / 2;\n      return {\n        clientX,\n        clientY,\n        delta,\n        event\n      };\n    }), takeUntil(tuiTypedFromEvent(el, 'touchend'))))), tuiTypedFromEvent(el, 'wheel', {\n      passive: false\n    }).pipe(tuiPreventDefault(), map(wheel => ({\n      clientX: wheel.clientX,\n      clientY: wheel.clientY,\n      delta: -wheel.deltaY * wheelSensitivity,\n      event: wheel\n    })))).subscribe(subscriber));\n  }\n  static {\n    this.ɵfac = function TuiZoomService_Factory(t) {\n      return new (t || TuiZoomService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiZoomService,\n      factory: TuiZoomService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiZoomService, [{\n    type: Injectable\n  }], function () {\n    return [];\n  }, null);\n})();\nclass TuiZoom {\n  constructor() {\n    this.tuiZoom = inject(TuiZoomService);\n  }\n  static {\n    this.ɵfac = function TuiZoom_Factory(t) {\n      return new (t || TuiZoom)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiZoom,\n      selectors: [[\"\", \"tuiZoom\", \"\"]],\n      hostVars: 2,\n      hostBindings: function TuiZoom_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"touch-action\", \"none\");\n        }\n      },\n      outputs: {\n        tuiZoom: \"tuiZoom\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiZoomService])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiZoom, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiZoom]',\n      outputs: ['tuiZoom'],\n      providers: [TuiZoomService],\n      host: {\n        '[style.touch-action]': '\"none\"'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_ZOOM_OPTIONS, TuiZoom, TuiZoomService };", "map": {"version": 3, "names": ["i0", "inject", "Injectable", "Directive", "tuiTypedFromEvent", "tuiPreventDefault", "tuiCreateToken", "tuiInjectElement", "tuiDistanceBetweenTouches", "Observable", "merge", "filter", "switchMap", "scan", "map", "takeUntil", "TUI_ZOOM_OPTIONS", "wheelSensitivity", "TOUCH_SENSITIVITY", "TuiZoomService", "constructor", "el", "subscriber", "passive", "pipe", "touches", "length", "startEvent", "prev", "event", "distance", "delta", "clientX", "clientY", "wheel", "deltaY", "subscribe", "ɵfac", "TuiZoomService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "TuiZoom", "tui<PERSON><PERSON>", "TuiZoom_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "hostVars", "hostBindings", "TuiZoom_HostBindings", "rf", "ctx", "ɵɵstyleProp", "outputs", "standalone", "features", "ɵɵProvidersFeature", "args", "selector", "providers", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-zoom.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Injectable, Directive } from '@angular/core';\nimport { tuiTypedFromEvent, tuiPreventDefault } from '@taiga-ui/cdk/observables';\nimport { tuiCreateToken, tuiInjectElement, tuiDistanceBetweenTouches } from '@taiga-ui/cdk/utils';\nimport { Observable, merge, filter, switchMap, scan, map, takeUntil } from 'rxjs';\n\n/**\n * Zoom options\n */\nconst TUI_ZOOM_OPTIONS = tuiCreateToken({\n    wheelSensitivity: 0.01,\n});\n\nconst TOUCH_SENSITIVITY = 0.01;\nclass TuiZoomService extends Observable {\n    constructor() {\n        const el = tuiInjectElement();\n        const { wheelSensitivity } = inject(TUI_ZOOM_OPTIONS);\n        super((subscriber) => merge(tuiTypedFromEvent(el, 'touchstart', { passive: true }).pipe(filter(({ touches }) => touches.length > 1), switchMap((startEvent) => tuiTypedFromEvent(el, 'touchmove', { passive: true }).pipe(tuiPreventDefault(), scan((prev, event) => {\n            const distance = tuiDistanceBetweenTouches(event);\n            return {\n                event,\n                distance,\n                delta: (distance - prev.distance) *\n                    TOUCH_SENSITIVITY,\n            };\n        }, {\n            event: startEvent,\n            distance: tuiDistanceBetweenTouches(startEvent),\n            delta: 0,\n        }), map(({ event, delta }) => {\n            const clientX = ((event.touches[0]?.clientX ?? 0) +\n                (event.touches[1]?.clientX ?? 0)) /\n                2;\n            const clientY = ((event.touches[0]?.clientY ?? 0) +\n                (event.touches[1]?.clientY ?? 0)) /\n                2;\n            return { clientX, clientY, delta, event };\n        }), takeUntil(tuiTypedFromEvent(el, 'touchend'))))), tuiTypedFromEvent(el, 'wheel', { passive: false }).pipe(tuiPreventDefault(), map((wheel) => ({\n            clientX: wheel.clientX,\n            clientY: wheel.clientY,\n            delta: -wheel.deltaY * wheelSensitivity,\n            event: wheel,\n        })))).subscribe(subscriber));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiZoomService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiZoomService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiZoomService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return []; } });\n\nclass TuiZoom {\n    constructor() {\n        this.tuiZoom = inject(TuiZoomService);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiZoom, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiZoom, isStandalone: true, selector: \"[tuiZoom]\", outputs: { tuiZoom: \"tuiZoom\" }, host: { properties: { \"style.touch-action\": \"\\\"none\\\"\" } }, providers: [TuiZoomService], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiZoom, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiZoom]',\n                    outputs: ['tuiZoom'],\n                    providers: [TuiZoomService],\n                    host: {\n                        '[style.touch-action]': '\"none\"',\n                    },\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_ZOOM_OPTIONS, TuiZoom, TuiZoomService };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,SAAS,QAAQ,eAAe;AAC7D,SAASC,iBAAiB,EAAEC,iBAAiB,QAAQ,2BAA2B;AAChF,SAASC,cAAc,EAAEC,gBAAgB,EAAEC,yBAAyB,QAAQ,qBAAqB;AACjG,SAASC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;;AAEjF;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGV,cAAc,CAAC;EACpCW,gBAAgB,EAAE;AACtB,CAAC,CAAC;AAEF,MAAMC,iBAAiB,GAAG,IAAI;AAC9B,MAAMC,cAAc,SAASV,UAAU,CAAC;EACpCW,WAAWA,CAAA,EAAG;IACV,MAAMC,EAAE,GAAGd,gBAAgB,CAAC,CAAC;IAC7B,MAAM;MAAEU;IAAiB,CAAC,GAAGhB,MAAM,CAACe,gBAAgB,CAAC;IACrD,KAAK,CAAEM,UAAU,IAAKZ,KAAK,CAACN,iBAAiB,CAACiB,EAAE,EAAE,YAAY,EAAE;MAAEE,OAAO,EAAE;IAAK,CAAC,CAAC,CAACC,IAAI,CAACb,MAAM,CAAC,CAAC;MAAEc;IAAQ,CAAC,KAAKA,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC,EAAEd,SAAS,CAAEe,UAAU,IAAKvB,iBAAiB,CAACiB,EAAE,EAAE,WAAW,EAAE;MAAEE,OAAO,EAAE;IAAK,CAAC,CAAC,CAACC,IAAI,CAACnB,iBAAiB,CAAC,CAAC,EAAEQ,IAAI,CAAC,CAACe,IAAI,EAAEC,KAAK,KAAK;MACjQ,MAAMC,QAAQ,GAAGtB,yBAAyB,CAACqB,KAAK,CAAC;MACjD,OAAO;QACHA,KAAK;QACLC,QAAQ;QACRC,KAAK,EAAE,CAACD,QAAQ,GAAGF,IAAI,CAACE,QAAQ,IAC5BZ;MACR,CAAC;IACL,CAAC,EAAE;MACCW,KAAK,EAAEF,UAAU;MACjBG,QAAQ,EAAEtB,yBAAyB,CAACmB,UAAU,CAAC;MAC/CI,KAAK,EAAE;IACX,CAAC,CAAC,EAAEjB,GAAG,CAAC,CAAC;MAAEe,KAAK;MAAEE;IAAM,CAAC,KAAK;MAC1B,MAAMC,OAAO,GAAG,CAAC,CAACH,KAAK,CAACJ,OAAO,CAAC,CAAC,CAAC,EAAEO,OAAO,IAAI,CAAC,KAC3CH,KAAK,CAACJ,OAAO,CAAC,CAAC,CAAC,EAAEO,OAAO,IAAI,CAAC,CAAC,IAChC,CAAC;MACL,MAAMC,OAAO,GAAG,CAAC,CAACJ,KAAK,CAACJ,OAAO,CAAC,CAAC,CAAC,EAAEQ,OAAO,IAAI,CAAC,KAC3CJ,KAAK,CAACJ,OAAO,CAAC,CAAC,CAAC,EAAEQ,OAAO,IAAI,CAAC,CAAC,IAChC,CAAC;MACL,OAAO;QAAED,OAAO;QAAEC,OAAO;QAAEF,KAAK;QAAEF;MAAM,CAAC;IAC7C,CAAC,CAAC,EAAEd,SAAS,CAACX,iBAAiB,CAACiB,EAAE,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEjB,iBAAiB,CAACiB,EAAE,EAAE,OAAO,EAAE;MAAEE,OAAO,EAAE;IAAM,CAAC,CAAC,CAACC,IAAI,CAACnB,iBAAiB,CAAC,CAAC,EAAES,GAAG,CAAEoB,KAAK,KAAM;MAC9IF,OAAO,EAAEE,KAAK,CAACF,OAAO;MACtBC,OAAO,EAAEC,KAAK,CAACD,OAAO;MACtBF,KAAK,EAAE,CAACG,KAAK,CAACC,MAAM,GAAGlB,gBAAgB;MACvCY,KAAK,EAAEK;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAACE,SAAS,CAACd,UAAU,CAAC,CAAC;EAChC;EACA;IAAS,IAAI,CAACe,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFpB,cAAc;IAAA,CAAoD;EAAE;EAC/K;IAAS,IAAI,CAACqB,KAAK,kBAD8ExC,EAAE,CAAAyC,kBAAA;MAAAC,KAAA,EACYvB,cAAc;MAAAwB,OAAA,EAAdxB,cAAc,CAAAkB;IAAA,EAAG;EAAE;AACtI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAHqG5C,EAAE,CAAA6C,iBAAA,CAGX1B,cAAc,EAAc,CAAC;IAC7G2B,IAAI,EAAE5C;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAM6C,OAAO,CAAC;EACV3B,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4B,OAAO,GAAG/C,MAAM,CAACkB,cAAc,CAAC;EACzC;EACA;IAAS,IAAI,CAACkB,IAAI,YAAAY,gBAAAV,CAAA;MAAA,YAAAA,CAAA,IAAyFQ,OAAO;IAAA,CAAmD;EAAE;EACvK;IAAS,IAAI,CAACG,IAAI,kBAZ+ElD,EAAE,CAAAmD,iBAAA;MAAAL,IAAA,EAYJC,OAAO;MAAAK,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAZLxD,EAAE,CAAA0D,WAAA,iBAYJ,MAAM,CAAC;QAAA;MAAA;MAAAC,OAAA;QAAAX,OAAA;MAAA;MAAAY,UAAA;MAAAC,QAAA,GAZL7D,EAAE,CAAA8D,kBAAA,CAYwJ,CAAC3C,cAAc,CAAC;IAAA,EAAiB;EAAE;AAClS;AACA;EAAA,QAAAyB,SAAA,oBAAAA,SAAA,KAdqG5C,EAAE,CAAA6C,iBAAA,CAcXE,OAAO,EAAc,CAAC;IACtGD,IAAI,EAAE3C,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,WAAW;MACrBL,OAAO,EAAE,CAAC,SAAS,CAAC;MACpBM,SAAS,EAAE,CAAC9C,cAAc,CAAC;MAC3B+C,IAAI,EAAE;QACF,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASlD,gBAAgB,EAAE+B,OAAO,EAAE5B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}