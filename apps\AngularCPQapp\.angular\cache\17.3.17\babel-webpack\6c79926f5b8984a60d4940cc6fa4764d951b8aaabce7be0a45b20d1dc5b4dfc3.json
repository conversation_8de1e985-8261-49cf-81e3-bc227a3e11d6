{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive } from '@angular/core';\nimport { TuiControl } from '@taiga-ui/cdk/classes';\nimport * as i2 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport { tuiAsOptionContent, tuiAsDataListHost } from '@taiga-ui/core/components/data-list';\nimport * as i3 from '@taiga-ui/core/components/textfield';\nimport { TuiWithTextfieldDropdown } from '@taiga-ui/core/components/textfield';\nimport * as i1 from '@taiga-ui/core/directives/dropdown';\nimport { tuiDropdownOpen, tuiDropdownOptionsProvider, TuiDropdownDirective, TuiWithDropdownOpen } from '@taiga-ui/core/directives/dropdown';\nimport { TuiSelectOption } from '@taiga-ui/kit/components/select';\nclass TuiButtonSelect extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.open = tuiDropdownOpen();\n    this.size = 's';\n  }\n  handleOption(option) {\n    this.onChange(option);\n    this.open.set(false);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiButtonSelect_BaseFactory;\n      return function TuiButtonSelect_Factory(t) {\n        return (ɵTuiButtonSelect_BaseFactory || (ɵTuiButtonSelect_BaseFactory = i0.ɵɵgetInheritedFactory(TuiButtonSelect)))(t || TuiButtonSelect);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiButtonSelect,\n      selectors: [[\"button\", \"tuiButtonSelect\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsOptionContent(TuiSelectOption), tuiAsDataListHost(TuiButtonSelect), tuiDropdownOptionsProvider({\n        align: 'right'\n      })]), i0.ɵɵHostDirectivesFeature([i1.TuiDropdownDirective, i2.TuiNativeValidator, i1.TuiWithDropdownOpen, i3.TuiWithTextfieldDropdown]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiButtonSelect, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'button[tuiButtonSelect]',\n      providers: [tuiAsOptionContent(TuiSelectOption), tuiAsDataListHost(TuiButtonSelect), tuiDropdownOptionsProvider({\n        align: 'right'\n      })],\n      hostDirectives: [TuiDropdownDirective, TuiNativeValidator, TuiWithDropdownOpen, TuiWithTextfieldDropdown]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiButtonSelect };", "map": {"version": 3, "names": ["i0", "Directive", "TuiControl", "i2", "TuiNativeValidator", "tuiAsOptionContent", "tuiAsDataListHost", "i3", "TuiWithTextfieldDropdown", "i1", "tuiDropdownOpen", "tuiDropdownOptionsProvider", "TuiDropdownDirective", "TuiWithDropdownOpen", "TuiSelectOption", "TuiButtonSelect", "constructor", "arguments", "open", "size", "handleOption", "option", "onChange", "set", "ɵfac", "ɵTuiButtonSelect_BaseFactory", "TuiButtonSelect_Factory", "t", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "standalone", "features", "ɵɵProvidersFeature", "align", "ɵɵHostDirectivesFeature", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "hostDirectives"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-button-select.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive } from '@angular/core';\nimport { TuiControl } from '@taiga-ui/cdk/classes';\nimport * as i2 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport { tuiAsOptionContent, tuiAsDataListHost } from '@taiga-ui/core/components/data-list';\nimport * as i3 from '@taiga-ui/core/components/textfield';\nimport { TuiWithTextfieldDropdown } from '@taiga-ui/core/components/textfield';\nimport * as i1 from '@taiga-ui/core/directives/dropdown';\nimport { tuiDropdownOpen, tuiDropdownOptionsProvider, TuiDropdownDirective, TuiWithDropdownOpen } from '@taiga-ui/core/directives/dropdown';\nimport { TuiSelectOption } from '@taiga-ui/kit/components/select';\n\nclass TuiButtonSelect extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.open = tuiDropdownOpen();\n        this.size = 's';\n    }\n    handleOption(option) {\n        this.onChange(option);\n        this.open.set(false);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiButtonSelect, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiButtonSelect, isStandalone: true, selector: \"button[tuiButtonSelect]\", providers: [\n            tuiAsOptionContent(TuiSelectOption),\n            tuiAsDataListHost(TuiButtonSelect),\n            tuiDropdownOptionsProvider({ align: 'right' }),\n        ], usesInheritance: true, hostDirectives: [{ directive: i1.TuiDropdownDirective }, { directive: i2.TuiNativeValidator }, { directive: i1.TuiWithDropdownOpen }, { directive: i3.TuiWithTextfieldDropdown }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiButtonSelect, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'button[tuiButtonSelect]',\n                    providers: [\n                        tuiAsOptionContent(TuiSelectOption),\n                        tuiAsDataListHost(TuiButtonSelect),\n                        tuiDropdownOptionsProvider({ align: 'right' }),\n                    ],\n                    hostDirectives: [\n                        TuiDropdownDirective,\n                        TuiNativeValidator,\n                        TuiWithDropdownOpen,\n                        TuiWithTextfieldDropdown,\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiButtonSelect };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAO,KAAKC,EAAE,MAAM,2CAA2C;AAC/D,SAASC,kBAAkB,QAAQ,2CAA2C;AAC9E,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,qCAAqC;AAC3F,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,wBAAwB,QAAQ,qCAAqC;AAC9E,OAAO,KAAKC,EAAE,MAAM,oCAAoC;AACxD,SAASC,eAAe,EAAEC,0BAA0B,EAAEC,oBAAoB,EAAEC,mBAAmB,QAAQ,oCAAoC;AAC3I,SAASC,eAAe,QAAQ,iCAAiC;AAEjE,MAAMC,eAAe,SAASb,UAAU,CAAC;EACrCc,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,IAAI,GAAGR,eAAe,CAAC,CAAC;IAC7B,IAAI,CAACS,IAAI,GAAG,GAAG;EACnB;EACAC,YAAYA,CAACC,MAAM,EAAE;IACjB,IAAI,CAACC,QAAQ,CAACD,MAAM,CAAC;IACrB,IAAI,CAACH,IAAI,CAACK,GAAG,CAAC,KAAK,CAAC;EACxB;EACA;IAAS,IAAI,CAACC,IAAI;MAAA,IAAAC,4BAAA;MAAA,gBAAAC,wBAAAC,CAAA;QAAA,QAAAF,4BAAA,KAAAA,4BAAA,GAA+EzB,EAAE,CAAA4B,qBAAA,CAAQb,eAAe,IAAAY,CAAA,IAAfZ,eAAe;MAAA;IAAA,IAAqD;EAAE;EACjL;IAAS,IAAI,CAACc,IAAI,kBAD+E7B,EAAE,CAAA8B,iBAAA;MAAAC,IAAA,EACJhB,eAAe;MAAAiB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADblC,EAAE,CAAAmC,kBAAA,CACiF,CAC5K9B,kBAAkB,CAACS,eAAe,CAAC,EACnCR,iBAAiB,CAACS,eAAe,CAAC,EAClCJ,0BAA0B,CAAC;QAAEyB,KAAK,EAAE;MAAQ,CAAC,CAAC,CACjD,GAL4FpC,EAAE,CAAAqC,uBAAA,EAKvC5B,EAAE,CAACG,oBAAoB,EAAiBT,EAAE,CAACC,kBAAkB,EAAiBK,EAAE,CAACI,mBAAmB,EAAiBN,EAAE,CAACC,wBAAwB,IAL3GR,EAAE,CAAAsC,0BAAA;IAAA,EAK6H;EAAE;AACtO;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAPqGvC,EAAE,CAAAwC,iBAAA,CAOXzB,eAAe,EAAc,CAAC;IAC9GgB,IAAI,EAAE9B,SAAS;IACfwC,IAAI,EAAE,CAAC;MACCR,UAAU,EAAE,IAAI;MAChBS,QAAQ,EAAE,yBAAyB;MACnCC,SAAS,EAAE,CACPtC,kBAAkB,CAACS,eAAe,CAAC,EACnCR,iBAAiB,CAACS,eAAe,CAAC,EAClCJ,0BAA0B,CAAC;QAAEyB,KAAK,EAAE;MAAQ,CAAC,CAAC,CACjD;MACDQ,cAAc,EAAE,CACZhC,oBAAoB,EACpBR,kBAAkB,EAClBS,mBAAmB,EACnBL,wBAAwB;IAEhC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASO,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}