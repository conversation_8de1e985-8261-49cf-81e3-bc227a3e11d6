{"ast": null, "code": "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { inject, EventEmitter, Directive, Output, signal, computed, ElementRef, Component, ChangeDetectionStrategy, Input, ViewChildren } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport { TuiControl } from '@taiga-ui/cdk/classes';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiFallbackValueProvider } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiClamp, tuiRound, tuiQuantize } from '@taiga-ui/cdk/utils/math';\nimport { tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i3 from '@taiga-ui/kit/components/slider';\nimport { TUI_FLOATING_PRECISION, TUI_SLIDER_OPTIONS, tuiPercentageToKeyStepValue, tuiKeyStepValueToPercentage, TuiSliderComponent, TuiSlider } from '@taiga-ui/kit/components/slider';\nimport { DOCUMENT } from '@angular/common';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tap, switchMap, startWith, map, takeUntil, repeat } from 'rxjs';\nconst _c0 = () => ({\n  standalone: true\n});\nclass TuiRangeChange {\n  constructor() {\n    this.doc = inject(DOCUMENT);\n    this.el = tuiInjectElement();\n    this.range = inject(TuiRange);\n    this.activeThumbChange = new EventEmitter();\n    let activeThumb;\n    tuiTypedFromEvent(this.el, 'pointerdown', {\n      passive: true,\n      capture: true\n    }).pipe(tap(({\n      clientX,\n      target,\n      pointerId\n    }) => {\n      activeThumb = this.detectActiveThumb(clientX, target);\n      this.range.slidersRefs.get(activeThumb === 'left' ? 0 : 1)?.nativeElement.setPointerCapture(pointerId);\n      this.activeThumbChange.emit(activeThumb);\n      if (this.range.focusable) {\n        this.el.focus();\n      }\n    }), switchMap(event => tuiTypedFromEvent(this.doc, 'pointermove').pipe(startWith(event))), map(({\n      clientX\n    }) => this.getFractionFromEvents(clientX ?? 0)), takeUntil(tuiTypedFromEvent(this.doc, 'pointerup', {\n      passive: true\n    })), repeat(), takeUntilDestroyed()).subscribe(fraction => {\n      const value = this.range.toValue(fraction);\n      this.range.processValue(value, activeThumb === 'right');\n    });\n  }\n  getFractionFromEvents(clickClientX) {\n    const hostRect = this.el.getBoundingClientRect();\n    const value = clickClientX - hostRect.left;\n    const total = hostRect.width;\n    return tuiClamp(tuiRound(value / total, TUI_FLOATING_PRECISION), 0, 1);\n  }\n  detectActiveThumb(clientX, target) {\n    const [leftSliderRef, rightSliderRef] = this.range.slidersRefs;\n    switch (target) {\n      case leftSliderRef?.nativeElement:\n        return 'left';\n      case rightSliderRef?.nativeElement:\n        return 'right';\n      default:\n        return this.findNearestActiveThumb(clientX);\n    }\n  }\n  findNearestActiveThumb(clientX) {\n    const fraction = this.getFractionFromEvents(clientX);\n    const deltaLeft = fraction * 100 - this.range.left();\n    const deltaRight = fraction * 100 - 100 + this.range.right();\n    return Math.abs(deltaLeft) > Math.abs(deltaRight) || deltaRight > 0 || this.range.left() === 0 && this.range.right() === 100 ? 'right' : 'left';\n  }\n  static {\n    this.ɵfac = function TuiRangeChange_Factory(t) {\n      return new (t || TuiRangeChange)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiRangeChange,\n      outputs: {\n        activeThumbChange: \"activeThumbChange\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiRangeChange, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], function () {\n    return [];\n  }, {\n    activeThumbChange: [{\n      type: Output\n    }]\n  });\n})();\nclass TuiRange extends TuiControl {\n  constructor() {\n    super(...arguments);\n    // TODO: refactor to signal inputs after Angular update\n    this.changes = signal(1);\n    this.el = tuiInjectElement();\n    this.options = inject(TUI_SLIDER_OPTIONS);\n    this.lastActiveThumb = 'right';\n    this.min = 0;\n    this.max = 100;\n    this.step = 1;\n    this.size = this.options.size;\n    this.segments = 1;\n    this.keySteps = null;\n    this.focusable = true;\n    this.margin = 0;\n    this.limit = Infinity;\n    this.slidersRefs = EMPTY_QUERY;\n    this.left = computed(() => this.toPercent(this.value()[0]));\n    this.right = computed(() => 100 - this.toPercent(this.value()[1]));\n  }\n  ngOnChanges() {\n    this.changes.set(this.changes() + 1);\n  }\n  processValue(value, right) {\n    if (right) {\n      this.updateEnd(value);\n    } else {\n      this.updateStart(value);\n    }\n    this.lastActiveThumb = right ? 'right' : 'left';\n  }\n  toValue(fraction) {\n    return tuiPercentageToKeyStepValue(tuiClamp(tuiQuantize(fraction, this.fractionStep), 0, 1) * 100, this.computedKeySteps);\n  }\n  get fractionStep() {\n    return this.step / (this.max - this.min);\n  }\n  get computedKeySteps() {\n    return this.computePureKeySteps(this.keySteps, this.min, this.max);\n  }\n  get segmentWidthRatio() {\n    return 1 / this.segments;\n  }\n  changeByStep(coefficient, target) {\n    const [sliderLeftRef, sliderRightRef] = this.slidersRefs;\n    const leftThumbElement = sliderLeftRef?.nativeElement;\n    const rightThumbElement = sliderRightRef?.nativeElement;\n    const isRightThumb = target === this.el ? this.lastActiveThumb === 'right' : target === rightThumbElement;\n    const activeThumbElement = isRightThumb ? rightThumbElement : leftThumbElement;\n    const previousValue = isRightThumb ? this.value()[1] : this.value()[0];\n    /** @bad TODO think about a solution without twice conversion */\n    const previousFraction = this.toPercent(previousValue) / 100;\n    const newFractionValue = previousFraction + coefficient * this.fractionStep;\n    this.processValue(this.toValue(newFractionValue), isRightThumb);\n    activeThumbElement?.focus();\n  }\n  toPercent(value) {\n    return this.changes() && tuiKeyStepValueToPercentage(value, this.computedKeySteps);\n  }\n  computePureKeySteps(keySteps, min, max) {\n    return keySteps || [[0, min], [100, max]];\n  }\n  updateStart(value) {\n    const newValue = Math.min(value, this.value()[1]);\n    const distance = this.value()[1] - newValue;\n    if (!this.checkDistance(distance)) {\n      return;\n    }\n    this.onChange([newValue, this.value()[1]]);\n  }\n  updateEnd(value) {\n    const newValue = Math.max(value, this.value()[0]);\n    const distance = newValue - this.value()[0];\n    if (!this.checkDistance(distance)) {\n      return;\n    }\n    this.onChange([this.value()[0], newValue]);\n  }\n  checkDistance(distance) {\n    return tuiClamp(distance, this.margin, this.limit) === distance;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiRange_BaseFactory;\n      return function TuiRange_Factory(t) {\n        return (ɵTuiRange_BaseFactory || (ɵTuiRange_BaseFactory = i0.ɵɵgetInheritedFactory(TuiRange)))(t || TuiRange);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiRange,\n      selectors: [[\"tui-range\"]],\n      viewQuery: function TuiRange_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TuiSliderComponent, 5, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slidersRefs = _t);\n        }\n      },\n      hostVars: 11,\n      hostBindings: function TuiRange_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focusout\", function TuiRange_focusout_HostBindingHandler() {\n            return ctx.onTouched();\n          })(\"keydown.arrowUp.prevent\", function TuiRange_keydown_arrowUp_prevent_HostBindingHandler($event) {\n            return ctx.changeByStep(1, $event.target);\n          })(\"keydown.arrowRight.prevent\", function TuiRange_keydown_arrowRight_prevent_HostBindingHandler($event) {\n            return ctx.changeByStep(1, $event.target);\n          })(\"keydown.arrowLeft.prevent\", function TuiRange_keydown_arrowLeft_prevent_HostBindingHandler($event) {\n            return ctx.changeByStep(-1, $event.target);\n          })(\"keydown.arrowDown.prevent\", function TuiRange_keydown_arrowDown_prevent_HostBindingHandler($event) {\n            return ctx.changeByStep(-1, $event.target);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size)(\"tabindex\", -1)(\"aria-disabled\", ctx.disabled());\n          i0.ɵɵstyleProp(\"--t-left\", ctx.left(), \"%\")(\"--t-right\", ctx.right(), \"%\")(\"background\", ctx.options.trackColor);\n          i0.ɵɵclassProp(\"_disabled\", ctx.disabled());\n        }\n      },\n      inputs: {\n        min: \"min\",\n        max: \"max\",\n        step: \"step\",\n        size: \"size\",\n        segments: \"segments\",\n        keySteps: \"keySteps\",\n        focusable: \"focusable\",\n        margin: \"margin\",\n        limit: \"limit\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiFallbackValueProvider([0, 0])]), i0.ɵɵHostDirectivesFeature([{\n        directive: TuiRangeChange,\n        outputs: [\"activeThumbChange\", \"activeThumbChange\"]\n      }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 22,\n      consts: [[1, \"t-track\"], [\"automation-id\", \"tui-range__left\", \"readonly\", \"\", \"step\", \"any\", \"tuiSlider\", \"\", \"type\", \"range\", 1, \"t-thumb\", 3, \"disabled\", \"keySteps\", \"max\", \"min\", \"ngModel\", \"ngModelOptions\", \"size\", \"tabIndex\"], [\"automation-id\", \"tui-range__right\", \"readonly\", \"\", \"step\", \"any\", \"tuiSlider\", \"\", \"type\", \"range\", 1, \"t-thumb\", 3, \"disabled\", \"keySteps\", \"max\", \"min\", \"ngModel\", \"ngModelOptions\", \"size\", \"tabIndex\"]],\n      template: function TuiRange_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"input\", 1)(2, \"input\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-bg-size-ratio\", 1 - ctx.segmentWidthRatio)(\"--t-segment-width\", ctx.segmentWidthRatio * 100, \"%\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.disabled())(\"keySteps\", ctx.computedKeySteps)(\"max\", ctx.max)(\"min\", ctx.min)(\"ngModel\", ctx.value()[0])(\"ngModelOptions\", i0.ɵɵpureFunction0(20, _c0))(\"size\", ctx.size)(\"tabIndex\", ctx.focusable ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.disabled())(\"keySteps\", ctx.computedKeySteps)(\"max\", ctx.max)(\"min\", ctx.min)(\"ngModel\", ctx.value()[1])(\"ngModelOptions\", i0.ɵɵpureFunction0(21, _c0))(\"size\", ctx.size)(\"tabIndex\", ctx.focusable ? 0 : -1);\n        }\n      },\n      dependencies: [FormsModule, i2.DefaultValueAccessor, i2.RangeValueAccessor, i2.NgControlStatus, i2.NgModel, i3.TuiSliderComponent, i3.TuiSliderKeyStepsBase, i3.TuiSliderKeySteps, i3.TuiSliderReadonly],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:block;block-size:.125rem;border-radius:var(--tui-radius-m);background:var(--tui-border-normal);cursor:pointer;outline:none;margin:.4375rem 0;touch-action:pan-x}[_nghost-%COMP%]:active{cursor:ew-resize}[_nghost-%COMP%]:after{content:\\\"\\\";position:absolute;top:-.4375rem;bottom:-.4375rem;inline-size:100%}._disabled[_nghost-%COMP%]{opacity:var(--tui-disabled-opacity);pointer-events:none}[data-size=s][_nghost-%COMP%]   .t-track[_ngcontent-%COMP%]{position:relative;margin:0 .25rem;block-size:100%}[data-size=s][_nghost-%COMP%]   .t-track[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:max(calc(var(--t-left) - 1px),1px);right:max(var(--t-right),1px);block-size:100%;background:var(--tui-background-accent-1);margin:0 -.25rem}[data-size=s][_nghost-%COMP%]   .t-track[_ngcontent-%COMP%]:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";left:.125rem;right:.375rem;background-image:repeating-linear-gradient(to right,var(--tui-text-tertiary) 0 .25rem,transparent 0 calc(var(--t-segment-width) / var(--t-bg-size-ratio)));background-position-x:right;background-repeat:no-repeat;background-size:calc(100% * var(--t-bg-size-ratio))}[data-size=m][_nghost-%COMP%]   .t-track[_ngcontent-%COMP%]{position:relative;margin:0 .375rem;block-size:100%}[data-size=m][_nghost-%COMP%]   .t-track[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:max(calc(var(--t-left) - 1px),1px);right:max(var(--t-right),1px);block-size:100%;background:var(--tui-background-accent-1);margin:0 -.375rem}[data-size=m][_nghost-%COMP%]   .t-track[_ngcontent-%COMP%]:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";left:.25rem;right:.5rem;background-image:repeating-linear-gradient(to right,var(--tui-text-tertiary) 0 .25rem,transparent 0 calc(var(--t-segment-width) / var(--t-bg-size-ratio)));background-position-x:right;background-repeat:no-repeat;background-size:calc(100% * var(--t-bg-size-ratio))}.t-thumb[_ngcontent-%COMP%]{pointer-events:none;position:absolute;top:.0625rem;left:0;right:0;z-index:1;transform:translateY(-50%)}.t-thumb[_ngcontent-%COMP%]::-webkit-slider-thumb{pointer-events:auto}.t-thumb[_ngcontent-%COMP%]::-moz-range-thumb{pointer-events:auto}._disabled[_nghost-%COMP%]   .t-thumb[_ngcontent-%COMP%]::-webkit-slider-thumb{pointer-events:none}._disabled[_nghost-%COMP%]   .t-thumb[_ngcontent-%COMP%]::-moz-range-thumb{pointer-events:none}input[type=range].t-thumb[_ngcontent-%COMP%]::-webkit-slider-runnable-track{background:transparent}input[type=range].t-thumb[_ngcontent-%COMP%]::-moz-range-track{background:transparent}input[type=range].t-thumb[_ngcontent-%COMP%]::-moz-range-progress{background:transparent}input[type=range].t-thumb[_ngcontent-%COMP%]::-ms-track{background:transparent}input[type=range].t-thumb[_ngcontent-%COMP%]::-ms-fill-lower{background:transparent}.t-thumb[_ngcontent-%COMP%]:last-of-type{--tui-slider-thumb-transform: translateX(50%) translateX(1px)}.t-thumb[_ngcontent-%COMP%]:first-of-type{--tui-slider-thumb-transform: translateX(-50%) translateX(-1px)}._disabled[_nghost-%COMP%]   .t-thumb[_ngcontent-%COMP%]{opacity:1}\"],\n      changeDetection: 0\n    });\n  }\n}\n__decorate([tuiPure], TuiRange.prototype, \"computePureKeySteps\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiRange, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-range',\n      imports: [FormsModule, TuiSlider],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiFallbackValueProvider([0, 0])],\n      hostDirectives: [{\n        directive: TuiRangeChange,\n        outputs: ['activeThumbChange']\n      }],\n      host: {\n        '[attr.data-size]': 'size',\n        '[attr.tabindex]': '-1',\n        '[attr.aria-disabled]': 'disabled()',\n        '[style.--t-left.%]': 'left()',\n        '[style.--t-right.%]': 'right()',\n        '[style.background]': 'options.trackColor',\n        '[class._disabled]': 'disabled()',\n        '(focusout)': 'onTouched()',\n        '(keydown.arrowUp.prevent)': 'changeByStep(1, $event.target)',\n        '(keydown.arrowRight.prevent)': 'changeByStep(1, $event.target)',\n        '(keydown.arrowLeft.prevent)': 'changeByStep(-1, $event.target)',\n        '(keydown.arrowDown.prevent)': 'changeByStep(-1, $event.target)'\n      },\n      template: \"<div\\n    class=\\\"t-track\\\"\\n    [style.--t-bg-size-ratio]=\\\"1 - segmentWidthRatio\\\"\\n    [style.--t-segment-width.%]=\\\"segmentWidthRatio * 100\\\"\\n>\\n    <input\\n        automation-id=\\\"tui-range__left\\\"\\n        readonly\\n        step=\\\"any\\\"\\n        tuiSlider\\n        type=\\\"range\\\"\\n        class=\\\"t-thumb\\\"\\n        [disabled]=\\\"disabled()\\\"\\n        [keySteps]=\\\"computedKeySteps\\\"\\n        [max]=\\\"max\\\"\\n        [min]=\\\"min\\\"\\n        [ngModel]=\\\"value()[0]\\\"\\n        [ngModelOptions]=\\\"{standalone: true}\\\"\\n        [size]=\\\"size\\\"\\n        [tabIndex]=\\\"focusable ? 0 : -1\\\"\\n    />\\n    <input\\n        automation-id=\\\"tui-range__right\\\"\\n        readonly\\n        step=\\\"any\\\"\\n        tuiSlider\\n        type=\\\"range\\\"\\n        class=\\\"t-thumb\\\"\\n        [disabled]=\\\"disabled()\\\"\\n        [keySteps]=\\\"computedKeySteps\\\"\\n        [max]=\\\"max\\\"\\n        [min]=\\\"min\\\"\\n        [ngModel]=\\\"value()[1]\\\"\\n        [ngModelOptions]=\\\"{standalone: true}\\\"\\n        [size]=\\\"size\\\"\\n        [tabIndex]=\\\"focusable ? 0 : -1\\\"\\n    />\\n</div>\\n\",\n      styles: [\":host{position:relative;display:block;block-size:.125rem;border-radius:var(--tui-radius-m);background:var(--tui-border-normal);cursor:pointer;outline:none;margin:.4375rem 0;touch-action:pan-x}:host:active{cursor:ew-resize}:host:after{content:\\\"\\\";position:absolute;top:-.4375rem;bottom:-.4375rem;inline-size:100%}:host._disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}:host[data-size=s] .t-track{position:relative;margin:0 .25rem;block-size:100%}:host[data-size=s] .t-track:before{content:\\\"\\\";position:absolute;top:0;left:max(calc(var(--t-left) - 1px),1px);right:max(var(--t-right),1px);block-size:100%;background:var(--tui-background-accent-1);margin:0 -.25rem}:host[data-size=s] .t-track:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";left:.125rem;right:.375rem;background-image:repeating-linear-gradient(to right,var(--tui-text-tertiary) 0 .25rem,transparent 0 calc(var(--t-segment-width) / var(--t-bg-size-ratio)));background-position-x:right;background-repeat:no-repeat;background-size:calc(100% * var(--t-bg-size-ratio))}:host[data-size=m] .t-track{position:relative;margin:0 .375rem;block-size:100%}:host[data-size=m] .t-track:before{content:\\\"\\\";position:absolute;top:0;left:max(calc(var(--t-left) - 1px),1px);right:max(var(--t-right),1px);block-size:100%;background:var(--tui-background-accent-1);margin:0 -.375rem}:host[data-size=m] .t-track:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";left:.25rem;right:.5rem;background-image:repeating-linear-gradient(to right,var(--tui-text-tertiary) 0 .25rem,transparent 0 calc(var(--t-segment-width) / var(--t-bg-size-ratio)));background-position-x:right;background-repeat:no-repeat;background-size:calc(100% * var(--t-bg-size-ratio))}.t-thumb{pointer-events:none;position:absolute;top:.0625rem;left:0;right:0;z-index:1;transform:translateY(-50%)}.t-thumb::-webkit-slider-thumb{pointer-events:auto}.t-thumb::-moz-range-thumb{pointer-events:auto}:host._disabled .t-thumb::-webkit-slider-thumb{pointer-events:none}:host._disabled .t-thumb::-moz-range-thumb{pointer-events:none}input[type=range].t-thumb::-webkit-slider-runnable-track{background:transparent}input[type=range].t-thumb::-moz-range-track{background:transparent}input[type=range].t-thumb::-moz-range-progress{background:transparent}input[type=range].t-thumb::-ms-track{background:transparent}input[type=range].t-thumb::-ms-fill-lower{background:transparent}.t-thumb:last-of-type{--tui-slider-thumb-transform: translateX(50%) translateX(1px)}.t-thumb:first-of-type{--tui-slider-thumb-transform: translateX(-50%) translateX(-1px)}:host._disabled .t-thumb{opacity:1}\\n\"]\n    }]\n  }], null, {\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    step: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    segments: [{\n      type: Input\n    }],\n    keySteps: [{\n      type: Input\n    }],\n    focusable: [{\n      type: Input\n    }],\n    margin: [{\n      type: Input\n    }],\n    limit: [{\n      type: Input\n    }],\n    slidersRefs: [{\n      type: ViewChildren,\n      args: [TuiSliderComponent, {\n        read: ElementRef\n      }]\n    }],\n    computePureKeySteps: []\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiRange, TuiRangeChange };", "map": {"version": 3, "names": ["__decorate", "i0", "inject", "EventEmitter", "Directive", "Output", "signal", "computed", "ElementRef", "Component", "ChangeDetectionStrategy", "Input", "ViewChildren", "i2", "FormsModule", "TuiControl", "EMPTY_QUERY", "tuiFallback<PERSON><PERSON>ue<PERSON>", "tuiInjectElement", "tui<PERSON><PERSON>", "tuiRound", "tuiQuantize", "tuiPure", "i3", "TUI_FLOATING_PRECISION", "TUI_SLIDER_OPTIONS", "tuiPercentageToKeyStepValue", "tuiKeyStepValueToPercentage", "TuiSliderComponent", "TuiSlider", "DOCUMENT", "takeUntilDestroyed", "tuiTypedFromEvent", "tap", "switchMap", "startWith", "map", "takeUntil", "repeat", "_c0", "standalone", "TuiRangeChange", "constructor", "doc", "el", "range", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activeThumbChange", "activeThumb", "passive", "capture", "pipe", "clientX", "target", "pointerId", "detectActiveThumb", "slidersRefs", "get", "nativeElement", "setPointerCapture", "emit", "focusable", "focus", "event", "getFractionFromEvents", "subscribe", "fraction", "value", "toValue", "processValue", "clickClientX", "hostRect", "getBoundingClientRect", "left", "total", "width", "leftSliderRef", "rightSliderRef", "findNearestActiveThumb", "deltaLeft", "deltaRight", "right", "Math", "abs", "ɵfac", "TuiRangeChange_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "outputs", "ngDevMode", "ɵsetClassMetadata", "args", "arguments", "changes", "options", "lastActiveThumb", "min", "max", "step", "size", "segments", "keySteps", "margin", "limit", "Infinity", "toPercent", "ngOnChanges", "set", "updateEnd", "updateStart", "fractionStep", "computedKeySteps", "computePureKeySteps", "segmentWidthRatio", "changeByStep", "coefficient", "sliderLeftRef", "sliderRightRef", "leftThumbElement", "rightThumbElement", "isRightThumb", "activeThumbElement", "previousValue", "previousFraction", "newFractionValue", "newValue", "distance", "checkDistance", "onChange", "ɵTuiRange_BaseFactory", "TuiRange_Factory", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "TuiRange_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostVars", "hostBindings", "TuiRange_HostBindings", "ɵɵlistener", "TuiRange_focusout_HostBindingHandler", "onTouched", "TuiRange_keydown_arrowUp_prevent_HostBindingHandler", "$event", "TuiRange_keydown_arrowRight_prevent_HostBindingHandler", "TuiRange_keydown_arrowLeft_prevent_HostBindingHandler", "TuiRange_keydown_arrowDown_prevent_HostBindingHandler", "ɵɵattribute", "disabled", "ɵɵstyleProp", "trackColor", "ɵɵclassProp", "inputs", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "directive", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiRange_Template", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "dependencies", "DefaultValueAccessor", "RangeValueAccessor", "NgControlStatus", "NgModel", "TuiSliderKeyStepsBase", "TuiSliderKeySteps", "Tui<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "styles", "changeDetection", "prototype", "selector", "imports", "OnPush", "providers", "hostDirectives", "host", "read"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-range.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { inject, EventEmitter, Directive, Output, signal, computed, ElementRef, Component, ChangeDetectionStrategy, Input, ViewChildren } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport { TuiControl } from '@taiga-ui/cdk/classes';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiFallbackValueProvider } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiClamp, tuiRound, tuiQuantize } from '@taiga-ui/cdk/utils/math';\nimport { tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i3 from '@taiga-ui/kit/components/slider';\nimport { TUI_FLOATING_PRECISION, TUI_SLIDER_OPTIONS, tuiPercentageToKeyStepValue, tuiKeyStepValueToPercentage, TuiSliderComponent, TuiSlider } from '@taiga-ui/kit/components/slider';\nimport { DOCUMENT } from '@angular/common';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tap, switchMap, startWith, map, takeUntil, repeat } from 'rxjs';\n\nclass TuiRangeChange {\n    constructor() {\n        this.doc = inject(DOCUMENT);\n        this.el = tuiInjectElement();\n        this.range = inject(TuiRange);\n        this.activeThumbChange = new EventEmitter();\n        let activeThumb;\n        tuiTypedFromEvent(this.el, 'pointerdown', {\n            passive: true,\n            capture: true,\n        })\n            .pipe(tap(({ clientX, target, pointerId }) => {\n            activeThumb = this.detectActiveThumb(clientX, target);\n            this.range.slidersRefs\n                .get(activeThumb === 'left' ? 0 : 1)\n                ?.nativeElement.setPointerCapture(pointerId);\n            this.activeThumbChange.emit(activeThumb);\n            if (this.range.focusable) {\n                this.el.focus();\n            }\n        }), switchMap((event) => tuiTypedFromEvent(this.doc, 'pointermove').pipe(startWith(event))), map(({ clientX }) => this.getFractionFromEvents(clientX ?? 0)), takeUntil(tuiTypedFromEvent(this.doc, 'pointerup', { passive: true })), repeat(), takeUntilDestroyed())\n            .subscribe((fraction) => {\n            const value = this.range.toValue(fraction);\n            this.range.processValue(value, activeThumb === 'right');\n        });\n    }\n    getFractionFromEvents(clickClientX) {\n        const hostRect = this.el.getBoundingClientRect();\n        const value = clickClientX - hostRect.left;\n        const total = hostRect.width;\n        return tuiClamp(tuiRound(value / total, TUI_FLOATING_PRECISION), 0, 1);\n    }\n    detectActiveThumb(clientX, target) {\n        const [leftSliderRef, rightSliderRef] = this.range.slidersRefs;\n        switch (target) {\n            case leftSliderRef?.nativeElement:\n                return 'left';\n            case rightSliderRef?.nativeElement:\n                return 'right';\n            default:\n                return this.findNearestActiveThumb(clientX);\n        }\n    }\n    findNearestActiveThumb(clientX) {\n        const fraction = this.getFractionFromEvents(clientX);\n        const deltaLeft = fraction * 100 - this.range.left();\n        const deltaRight = fraction * 100 - 100 + this.range.right();\n        return Math.abs(deltaLeft) > Math.abs(deltaRight) ||\n            deltaRight > 0 ||\n            (this.range.left() === 0 && this.range.right() === 100)\n            ? 'right'\n            : 'left';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiRangeChange, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiRangeChange, isStandalone: true, outputs: { activeThumbChange: \"activeThumbChange\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiRangeChange, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { activeThumbChange: [{\n                type: Output\n            }] } });\n\nclass TuiRange extends TuiControl {\n    constructor() {\n        super(...arguments);\n        // TODO: refactor to signal inputs after Angular update\n        this.changes = signal(1);\n        this.el = tuiInjectElement();\n        this.options = inject(TUI_SLIDER_OPTIONS);\n        this.lastActiveThumb = 'right';\n        this.min = 0;\n        this.max = 100;\n        this.step = 1;\n        this.size = this.options.size;\n        this.segments = 1;\n        this.keySteps = null;\n        this.focusable = true;\n        this.margin = 0;\n        this.limit = Infinity;\n        this.slidersRefs = EMPTY_QUERY;\n        this.left = computed(() => this.toPercent(this.value()[0]));\n        this.right = computed(() => 100 - this.toPercent(this.value()[1]));\n    }\n    ngOnChanges() {\n        this.changes.set(this.changes() + 1);\n    }\n    processValue(value, right) {\n        if (right) {\n            this.updateEnd(value);\n        }\n        else {\n            this.updateStart(value);\n        }\n        this.lastActiveThumb = right ? 'right' : 'left';\n    }\n    toValue(fraction) {\n        return tuiPercentageToKeyStepValue(tuiClamp(tuiQuantize(fraction, this.fractionStep), 0, 1) * 100, this.computedKeySteps);\n    }\n    get fractionStep() {\n        return this.step / (this.max - this.min);\n    }\n    get computedKeySteps() {\n        return this.computePureKeySteps(this.keySteps, this.min, this.max);\n    }\n    get segmentWidthRatio() {\n        return 1 / this.segments;\n    }\n    changeByStep(coefficient, target) {\n        const [sliderLeftRef, sliderRightRef] = this.slidersRefs;\n        const leftThumbElement = sliderLeftRef?.nativeElement;\n        const rightThumbElement = sliderRightRef?.nativeElement;\n        const isRightThumb = target === this.el\n            ? this.lastActiveThumb === 'right'\n            : target === rightThumbElement;\n        const activeThumbElement = isRightThumb ? rightThumbElement : leftThumbElement;\n        const previousValue = isRightThumb ? this.value()[1] : this.value()[0];\n        /** @bad TODO think about a solution without twice conversion */\n        const previousFraction = this.toPercent(previousValue) / 100;\n        const newFractionValue = previousFraction + coefficient * this.fractionStep;\n        this.processValue(this.toValue(newFractionValue), isRightThumb);\n        activeThumbElement?.focus();\n    }\n    toPercent(value) {\n        return (this.changes() && tuiKeyStepValueToPercentage(value, this.computedKeySteps));\n    }\n    computePureKeySteps(keySteps, min, max) {\n        return (keySteps || [\n            [0, min],\n            [100, max],\n        ]);\n    }\n    updateStart(value) {\n        const newValue = Math.min(value, this.value()[1]);\n        const distance = this.value()[1] - newValue;\n        if (!this.checkDistance(distance)) {\n            return;\n        }\n        this.onChange([newValue, this.value()[1]]);\n    }\n    updateEnd(value) {\n        const newValue = Math.max(value, this.value()[0]);\n        const distance = newValue - this.value()[0];\n        if (!this.checkDistance(distance)) {\n            return;\n        }\n        this.onChange([this.value()[0], newValue]);\n    }\n    checkDistance(distance) {\n        return tuiClamp(distance, this.margin, this.limit) === distance;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiRange, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiRange, isStandalone: true, selector: \"tui-range\", inputs: { min: \"min\", max: \"max\", step: \"step\", size: \"size\", segments: \"segments\", keySteps: \"keySteps\", focusable: \"focusable\", margin: \"margin\", limit: \"limit\" }, host: { listeners: { \"focusout\": \"onTouched()\", \"keydown.arrowUp.prevent\": \"changeByStep(1, $event.target)\", \"keydown.arrowRight.prevent\": \"changeByStep(1, $event.target)\", \"keydown.arrowLeft.prevent\": \"changeByStep(-1, $event.target)\", \"keydown.arrowDown.prevent\": \"changeByStep(-1, $event.target)\" }, properties: { \"attr.data-size\": \"size\", \"attr.tabindex\": \"-1\", \"attr.aria-disabled\": \"disabled()\", \"style.--t-left.%\": \"left()\", \"style.--t-right.%\": \"right()\", \"style.background\": \"options.trackColor\", \"class._disabled\": \"disabled()\" } }, providers: [tuiFallbackValueProvider([0, 0])], viewQueries: [{ propertyName: \"slidersRefs\", predicate: TuiSliderComponent, descendants: true, read: ElementRef }], usesInheritance: true, usesOnChanges: true, hostDirectives: [{ directive: TuiRangeChange, outputs: [\"activeThumbChange\", \"activeThumbChange\"] }], ngImport: i0, template: \"<div\\n    class=\\\"t-track\\\"\\n    [style.--t-bg-size-ratio]=\\\"1 - segmentWidthRatio\\\"\\n    [style.--t-segment-width.%]=\\\"segmentWidthRatio * 100\\\"\\n>\\n    <input\\n        automation-id=\\\"tui-range__left\\\"\\n        readonly\\n        step=\\\"any\\\"\\n        tuiSlider\\n        type=\\\"range\\\"\\n        class=\\\"t-thumb\\\"\\n        [disabled]=\\\"disabled()\\\"\\n        [keySteps]=\\\"computedKeySteps\\\"\\n        [max]=\\\"max\\\"\\n        [min]=\\\"min\\\"\\n        [ngModel]=\\\"value()[0]\\\"\\n        [ngModelOptions]=\\\"{standalone: true}\\\"\\n        [size]=\\\"size\\\"\\n        [tabIndex]=\\\"focusable ? 0 : -1\\\"\\n    />\\n    <input\\n        automation-id=\\\"tui-range__right\\\"\\n        readonly\\n        step=\\\"any\\\"\\n        tuiSlider\\n        type=\\\"range\\\"\\n        class=\\\"t-thumb\\\"\\n        [disabled]=\\\"disabled()\\\"\\n        [keySteps]=\\\"computedKeySteps\\\"\\n        [max]=\\\"max\\\"\\n        [min]=\\\"min\\\"\\n        [ngModel]=\\\"value()[1]\\\"\\n        [ngModelOptions]=\\\"{standalone: true}\\\"\\n        [size]=\\\"size\\\"\\n        [tabIndex]=\\\"focusable ? 0 : -1\\\"\\n    />\\n</div>\\n\", styles: [\":host{position:relative;display:block;block-size:.125rem;border-radius:var(--tui-radius-m);background:var(--tui-border-normal);cursor:pointer;outline:none;margin:.4375rem 0;touch-action:pan-x}:host:active{cursor:ew-resize}:host:after{content:\\\"\\\";position:absolute;top:-.4375rem;bottom:-.4375rem;inline-size:100%}:host._disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}:host[data-size=s] .t-track{position:relative;margin:0 .25rem;block-size:100%}:host[data-size=s] .t-track:before{content:\\\"\\\";position:absolute;top:0;left:max(calc(var(--t-left) - 1px),1px);right:max(var(--t-right),1px);block-size:100%;background:var(--tui-background-accent-1);margin:0 -.25rem}:host[data-size=s] .t-track:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";left:.125rem;right:.375rem;background-image:repeating-linear-gradient(to right,var(--tui-text-tertiary) 0 .25rem,transparent 0 calc(var(--t-segment-width) / var(--t-bg-size-ratio)));background-position-x:right;background-repeat:no-repeat;background-size:calc(100% * var(--t-bg-size-ratio))}:host[data-size=m] .t-track{position:relative;margin:0 .375rem;block-size:100%}:host[data-size=m] .t-track:before{content:\\\"\\\";position:absolute;top:0;left:max(calc(var(--t-left) - 1px),1px);right:max(var(--t-right),1px);block-size:100%;background:var(--tui-background-accent-1);margin:0 -.375rem}:host[data-size=m] .t-track:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";left:.25rem;right:.5rem;background-image:repeating-linear-gradient(to right,var(--tui-text-tertiary) 0 .25rem,transparent 0 calc(var(--t-segment-width) / var(--t-bg-size-ratio)));background-position-x:right;background-repeat:no-repeat;background-size:calc(100% * var(--t-bg-size-ratio))}.t-thumb{pointer-events:none;position:absolute;top:.0625rem;left:0;right:0;z-index:1;transform:translateY(-50%)}.t-thumb::-webkit-slider-thumb{pointer-events:auto}.t-thumb::-moz-range-thumb{pointer-events:auto}:host._disabled .t-thumb::-webkit-slider-thumb{pointer-events:none}:host._disabled .t-thumb::-moz-range-thumb{pointer-events:none}input[type=range].t-thumb::-webkit-slider-runnable-track{background:transparent}input[type=range].t-thumb::-moz-range-track{background:transparent}input[type=range].t-thumb::-moz-range-progress{background:transparent}input[type=range].t-thumb::-ms-track{background:transparent}input[type=range].t-thumb::-ms-fill-lower{background:transparent}.t-thumb:last-of-type{--tui-slider-thumb-transform: translateX(50%) translateX(1px)}.t-thumb:first-of-type{--tui-slider-thumb-transform: translateX(-50%) translateX(-1px)}:host._disabled .t-thumb{opacity:1}\\n\"], dependencies: [{ kind: \"ngmodule\", type: FormsModule }, { kind: \"directive\", type: i2.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { kind: \"directive\", type: i2.RangeValueAccessor, selector: \"input[type=range][formControlName],input[type=range][formControl],input[type=range][ngModel]\" }, { kind: \"directive\", type: i2.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i2.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }, { kind: \"component\", type: i3.TuiSliderComponent, selector: \"input[type=range][tuiSlider]\", inputs: [\"size\", \"segments\"] }, { kind: \"directive\", type: i3.TuiSliderKeyStepsBase, selector: \"input[tuiSlider][keySteps]\", inputs: [\"step\", \"keySteps\"] }, { kind: \"directive\", type: i3.TuiSliderKeySteps, selector: \"input[tuiSlider][keySteps][ngModel],input[tuiSlider][keySteps][formControl],input[tuiSlider][keySteps][formControlName]\", inputs: [\"keySteps\"] }, { kind: \"directive\", type: i3.TuiSliderReadonly, selector: \"input[tuiSlider][readonly]\", inputs: [\"readonly\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\n__decorate([\n    tuiPure\n], TuiRange.prototype, \"computePureKeySteps\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiRange, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-range', imports: [FormsModule, TuiSlider], changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiFallbackValueProvider([0, 0])], hostDirectives: [\n                        {\n                            directive: TuiRangeChange,\n                            outputs: ['activeThumbChange'],\n                        },\n                    ], host: {\n                        '[attr.data-size]': 'size',\n                        '[attr.tabindex]': '-1',\n                        '[attr.aria-disabled]': 'disabled()',\n                        '[style.--t-left.%]': 'left()',\n                        '[style.--t-right.%]': 'right()',\n                        '[style.background]': 'options.trackColor',\n                        '[class._disabled]': 'disabled()',\n                        '(focusout)': 'onTouched()',\n                        '(keydown.arrowUp.prevent)': 'changeByStep(1, $event.target)',\n                        '(keydown.arrowRight.prevent)': 'changeByStep(1, $event.target)',\n                        '(keydown.arrowLeft.prevent)': 'changeByStep(-1, $event.target)',\n                        '(keydown.arrowDown.prevent)': 'changeByStep(-1, $event.target)',\n                    }, template: \"<div\\n    class=\\\"t-track\\\"\\n    [style.--t-bg-size-ratio]=\\\"1 - segmentWidthRatio\\\"\\n    [style.--t-segment-width.%]=\\\"segmentWidthRatio * 100\\\"\\n>\\n    <input\\n        automation-id=\\\"tui-range__left\\\"\\n        readonly\\n        step=\\\"any\\\"\\n        tuiSlider\\n        type=\\\"range\\\"\\n        class=\\\"t-thumb\\\"\\n        [disabled]=\\\"disabled()\\\"\\n        [keySteps]=\\\"computedKeySteps\\\"\\n        [max]=\\\"max\\\"\\n        [min]=\\\"min\\\"\\n        [ngModel]=\\\"value()[0]\\\"\\n        [ngModelOptions]=\\\"{standalone: true}\\\"\\n        [size]=\\\"size\\\"\\n        [tabIndex]=\\\"focusable ? 0 : -1\\\"\\n    />\\n    <input\\n        automation-id=\\\"tui-range__right\\\"\\n        readonly\\n        step=\\\"any\\\"\\n        tuiSlider\\n        type=\\\"range\\\"\\n        class=\\\"t-thumb\\\"\\n        [disabled]=\\\"disabled()\\\"\\n        [keySteps]=\\\"computedKeySteps\\\"\\n        [max]=\\\"max\\\"\\n        [min]=\\\"min\\\"\\n        [ngModel]=\\\"value()[1]\\\"\\n        [ngModelOptions]=\\\"{standalone: true}\\\"\\n        [size]=\\\"size\\\"\\n        [tabIndex]=\\\"focusable ? 0 : -1\\\"\\n    />\\n</div>\\n\", styles: [\":host{position:relative;display:block;block-size:.125rem;border-radius:var(--tui-radius-m);background:var(--tui-border-normal);cursor:pointer;outline:none;margin:.4375rem 0;touch-action:pan-x}:host:active{cursor:ew-resize}:host:after{content:\\\"\\\";position:absolute;top:-.4375rem;bottom:-.4375rem;inline-size:100%}:host._disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}:host[data-size=s] .t-track{position:relative;margin:0 .25rem;block-size:100%}:host[data-size=s] .t-track:before{content:\\\"\\\";position:absolute;top:0;left:max(calc(var(--t-left) - 1px),1px);right:max(var(--t-right),1px);block-size:100%;background:var(--tui-background-accent-1);margin:0 -.25rem}:host[data-size=s] .t-track:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";left:.125rem;right:.375rem;background-image:repeating-linear-gradient(to right,var(--tui-text-tertiary) 0 .25rem,transparent 0 calc(var(--t-segment-width) / var(--t-bg-size-ratio)));background-position-x:right;background-repeat:no-repeat;background-size:calc(100% * var(--t-bg-size-ratio))}:host[data-size=m] .t-track{position:relative;margin:0 .375rem;block-size:100%}:host[data-size=m] .t-track:before{content:\\\"\\\";position:absolute;top:0;left:max(calc(var(--t-left) - 1px),1px);right:max(var(--t-right),1px);block-size:100%;background:var(--tui-background-accent-1);margin:0 -.375rem}:host[data-size=m] .t-track:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";left:.25rem;right:.5rem;background-image:repeating-linear-gradient(to right,var(--tui-text-tertiary) 0 .25rem,transparent 0 calc(var(--t-segment-width) / var(--t-bg-size-ratio)));background-position-x:right;background-repeat:no-repeat;background-size:calc(100% * var(--t-bg-size-ratio))}.t-thumb{pointer-events:none;position:absolute;top:.0625rem;left:0;right:0;z-index:1;transform:translateY(-50%)}.t-thumb::-webkit-slider-thumb{pointer-events:auto}.t-thumb::-moz-range-thumb{pointer-events:auto}:host._disabled .t-thumb::-webkit-slider-thumb{pointer-events:none}:host._disabled .t-thumb::-moz-range-thumb{pointer-events:none}input[type=range].t-thumb::-webkit-slider-runnable-track{background:transparent}input[type=range].t-thumb::-moz-range-track{background:transparent}input[type=range].t-thumb::-moz-range-progress{background:transparent}input[type=range].t-thumb::-ms-track{background:transparent}input[type=range].t-thumb::-ms-fill-lower{background:transparent}.t-thumb:last-of-type{--tui-slider-thumb-transform: translateX(50%) translateX(1px)}.t-thumb:first-of-type{--tui-slider-thumb-transform: translateX(-50%) translateX(-1px)}:host._disabled .t-thumb{opacity:1}\\n\"] }]\n        }], propDecorators: { min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], step: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], segments: [{\n                type: Input\n            }], keySteps: [{\n                type: Input\n            }], focusable: [{\n                type: Input\n            }], margin: [{\n                type: Input\n            }], limit: [{\n                type: Input\n            }], slidersRefs: [{\n                type: ViewChildren,\n                args: [TuiSliderComponent, { read: ElementRef }]\n            }], computePureKeySteps: [] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiRange, TuiRangeChange };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,YAAY,QAAQ,eAAe;AAC9J,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,0BAA0B;AAC1E,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,sBAAsB,EAAEC,kBAAkB,EAAEC,2BAA2B,EAAEC,2BAA2B,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,iCAAiC;AACrL,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,EAAEC,SAAS,EAAEC,MAAM,QAAQ,MAAM;AAAC,MAAAC,GAAA,GAAAA,CAAA;EAAAC,UAAA;AAAA;AAEzE,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,GAAG,GAAGzC,MAAM,CAAC4B,QAAQ,CAAC;IAC3B,IAAI,CAACc,EAAE,GAAG1B,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAAC2B,KAAK,GAAG3C,MAAM,CAAC4C,QAAQ,CAAC;IAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAI5C,YAAY,CAAC,CAAC;IAC3C,IAAI6C,WAAW;IACfhB,iBAAiB,CAAC,IAAI,CAACY,EAAE,EAAE,aAAa,EAAE;MACtCK,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACb,CAAC,CAAC,CACGC,IAAI,CAAClB,GAAG,CAAC,CAAC;MAAEmB,OAAO;MAAEC,MAAM;MAAEC;IAAU,CAAC,KAAK;MAC9CN,WAAW,GAAG,IAAI,CAACO,iBAAiB,CAACH,OAAO,EAAEC,MAAM,CAAC;MACrD,IAAI,CAACR,KAAK,CAACW,WAAW,CACjBC,GAAG,CAACT,WAAW,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAClCU,aAAa,CAACC,iBAAiB,CAACL,SAAS,CAAC;MAChD,IAAI,CAACP,iBAAiB,CAACa,IAAI,CAACZ,WAAW,CAAC;MACxC,IAAI,IAAI,CAACH,KAAK,CAACgB,SAAS,EAAE;QACtB,IAAI,CAACjB,EAAE,CAACkB,KAAK,CAAC,CAAC;MACnB;IACJ,CAAC,CAAC,EAAE5B,SAAS,CAAE6B,KAAK,IAAK/B,iBAAiB,CAAC,IAAI,CAACW,GAAG,EAAE,aAAa,CAAC,CAACQ,IAAI,CAAChB,SAAS,CAAC4B,KAAK,CAAC,CAAC,CAAC,EAAE3B,GAAG,CAAC,CAAC;MAAEgB;IAAQ,CAAC,KAAK,IAAI,CAACY,qBAAqB,CAACZ,OAAO,IAAI,CAAC,CAAC,CAAC,EAAEf,SAAS,CAACL,iBAAiB,CAAC,IAAI,CAACW,GAAG,EAAE,WAAW,EAAE;MAAEM,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,EAAEX,MAAM,CAAC,CAAC,EAAEP,kBAAkB,CAAC,CAAC,CAAC,CAC/PkC,SAAS,CAAEC,QAAQ,IAAK;MACzB,MAAMC,KAAK,GAAG,IAAI,CAACtB,KAAK,CAACuB,OAAO,CAACF,QAAQ,CAAC;MAC1C,IAAI,CAACrB,KAAK,CAACwB,YAAY,CAACF,KAAK,EAAEnB,WAAW,KAAK,OAAO,CAAC;IAC3D,CAAC,CAAC;EACN;EACAgB,qBAAqBA,CAACM,YAAY,EAAE;IAChC,MAAMC,QAAQ,GAAG,IAAI,CAAC3B,EAAE,CAAC4B,qBAAqB,CAAC,CAAC;IAChD,MAAML,KAAK,GAAGG,YAAY,GAAGC,QAAQ,CAACE,IAAI;IAC1C,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK;IAC5B,OAAOxD,QAAQ,CAACC,QAAQ,CAAC+C,KAAK,GAAGO,KAAK,EAAElD,sBAAsB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1E;EACA+B,iBAAiBA,CAACH,OAAO,EAAEC,MAAM,EAAE;IAC/B,MAAM,CAACuB,aAAa,EAAEC,cAAc,CAAC,GAAG,IAAI,CAAChC,KAAK,CAACW,WAAW;IAC9D,QAAQH,MAAM;MACV,KAAKuB,aAAa,EAAElB,aAAa;QAC7B,OAAO,MAAM;MACjB,KAAKmB,cAAc,EAAEnB,aAAa;QAC9B,OAAO,OAAO;MAClB;QACI,OAAO,IAAI,CAACoB,sBAAsB,CAAC1B,OAAO,CAAC;IACnD;EACJ;EACA0B,sBAAsBA,CAAC1B,OAAO,EAAE;IAC5B,MAAMc,QAAQ,GAAG,IAAI,CAACF,qBAAqB,CAACZ,OAAO,CAAC;IACpD,MAAM2B,SAAS,GAAGb,QAAQ,GAAG,GAAG,GAAG,IAAI,CAACrB,KAAK,CAAC4B,IAAI,CAAC,CAAC;IACpD,MAAMO,UAAU,GAAGd,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAACrB,KAAK,CAACoC,KAAK,CAAC,CAAC;IAC5D,OAAOC,IAAI,CAACC,GAAG,CAACJ,SAAS,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACH,UAAU,CAAC,IAC7CA,UAAU,GAAG,CAAC,IACb,IAAI,CAACnC,KAAK,CAAC4B,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC5B,KAAK,CAACoC,KAAK,CAAC,CAAC,KAAK,GAAI,GACrD,OAAO,GACP,MAAM;EAChB;EACA;IAAS,IAAI,CAACG,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyF7C,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAAC8C,IAAI,kBAD+EtF,EAAE,CAAAuF,iBAAA;MAAAC,IAAA,EACJhD,cAAc;MAAAiD,OAAA;QAAA3C,iBAAA;MAAA;MAAAP,UAAA;IAAA,EAA0F;EAAE;AAC7M;AACA;EAAA,QAAAmD,SAAA,oBAAAA,SAAA,KAHqG1F,EAAE,CAAA2F,iBAAA,CAGXnD,cAAc,EAAc,CAAC;IAC7GgD,IAAI,EAAErF,SAAS;IACfyF,IAAI,EAAE,CAAC;MACCrD,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEO,iBAAiB,EAAE,CAAC;MAC9E0C,IAAI,EAAEpF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyC,QAAQ,SAAS/B,UAAU,CAAC;EAC9B2B,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGoD,SAAS,CAAC;IACnB;IACA,IAAI,CAACC,OAAO,GAAGzF,MAAM,CAAC,CAAC,CAAC;IACxB,IAAI,CAACsC,EAAE,GAAG1B,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAAC8E,OAAO,GAAG9F,MAAM,CAACuB,kBAAkB,CAAC;IACzC,IAAI,CAACwE,eAAe,GAAG,OAAO;IAC9B,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,GAAG,GAAG,GAAG;IACd,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,IAAI,GAAG,IAAI,CAACL,OAAO,CAACK,IAAI;IAC7B,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC1C,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC2C,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,KAAK,GAAGC,QAAQ;IACrB,IAAI,CAAClD,WAAW,GAAGxC,WAAW;IAC9B,IAAI,CAACyD,IAAI,GAAGlE,QAAQ,CAAC,MAAM,IAAI,CAACoG,SAAS,CAAC,IAAI,CAACxC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACc,KAAK,GAAG1E,QAAQ,CAAC,MAAM,GAAG,GAAG,IAAI,CAACoG,SAAS,CAAC,IAAI,CAACxC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtE;EACAyC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACb,OAAO,CAACc,GAAG,CAAC,IAAI,CAACd,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;EACxC;EACA1B,YAAYA,CAACF,KAAK,EAAEc,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC6B,SAAS,CAAC3C,KAAK,CAAC;IACzB,CAAC,MACI;MACD,IAAI,CAAC4C,WAAW,CAAC5C,KAAK,CAAC;IAC3B;IACA,IAAI,CAAC8B,eAAe,GAAGhB,KAAK,GAAG,OAAO,GAAG,MAAM;EACnD;EACAb,OAAOA,CAACF,QAAQ,EAAE;IACd,OAAOxC,2BAA2B,CAACP,QAAQ,CAACE,WAAW,CAAC6C,QAAQ,EAAE,IAAI,CAAC8C,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,EAAE,IAAI,CAACC,gBAAgB,CAAC;EAC7H;EACA,IAAID,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACZ,IAAI,IAAI,IAAI,CAACD,GAAG,GAAG,IAAI,CAACD,GAAG,CAAC;EAC5C;EACA,IAAIe,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAACX,QAAQ,EAAE,IAAI,CAACL,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;EACtE;EACA,IAAIgB,iBAAiBA,CAAA,EAAG;IACpB,OAAO,CAAC,GAAG,IAAI,CAACb,QAAQ;EAC5B;EACAc,YAAYA,CAACC,WAAW,EAAEhE,MAAM,EAAE;IAC9B,MAAM,CAACiE,aAAa,EAAEC,cAAc,CAAC,GAAG,IAAI,CAAC/D,WAAW;IACxD,MAAMgE,gBAAgB,GAAGF,aAAa,EAAE5D,aAAa;IACrD,MAAM+D,iBAAiB,GAAGF,cAAc,EAAE7D,aAAa;IACvD,MAAMgE,YAAY,GAAGrE,MAAM,KAAK,IAAI,CAACT,EAAE,GACjC,IAAI,CAACqD,eAAe,KAAK,OAAO,GAChC5C,MAAM,KAAKoE,iBAAiB;IAClC,MAAME,kBAAkB,GAAGD,YAAY,GAAGD,iBAAiB,GAAGD,gBAAgB;IAC9E,MAAMI,aAAa,GAAGF,YAAY,GAAG,IAAI,CAACvD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACtE;IACA,MAAM0D,gBAAgB,GAAG,IAAI,CAAClB,SAAS,CAACiB,aAAa,CAAC,GAAG,GAAG;IAC5D,MAAME,gBAAgB,GAAGD,gBAAgB,GAAGR,WAAW,GAAG,IAAI,CAACL,YAAY;IAC3E,IAAI,CAAC3C,YAAY,CAAC,IAAI,CAACD,OAAO,CAAC0D,gBAAgB,CAAC,EAAEJ,YAAY,CAAC;IAC/DC,kBAAkB,EAAE7D,KAAK,CAAC,CAAC;EAC/B;EACA6C,SAASA,CAACxC,KAAK,EAAE;IACb,OAAQ,IAAI,CAAC4B,OAAO,CAAC,CAAC,IAAIpE,2BAA2B,CAACwC,KAAK,EAAE,IAAI,CAAC8C,gBAAgB,CAAC;EACvF;EACAC,mBAAmBA,CAACX,QAAQ,EAAEL,GAAG,EAAEC,GAAG,EAAE;IACpC,OAAQI,QAAQ,IAAI,CAChB,CAAC,CAAC,EAAEL,GAAG,CAAC,EACR,CAAC,GAAG,EAAEC,GAAG,CAAC,CACb;EACL;EACAY,WAAWA,CAAC5C,KAAK,EAAE;IACf,MAAM4D,QAAQ,GAAG7C,IAAI,CAACgB,GAAG,CAAC/B,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,MAAM6D,QAAQ,GAAG,IAAI,CAAC7D,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG4D,QAAQ;IAC3C,IAAI,CAAC,IAAI,CAACE,aAAa,CAACD,QAAQ,CAAC,EAAE;MAC/B;IACJ;IACA,IAAI,CAACE,QAAQ,CAAC,CAACH,QAAQ,EAAE,IAAI,CAAC5D,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C;EACA2C,SAASA,CAAC3C,KAAK,EAAE;IACb,MAAM4D,QAAQ,GAAG7C,IAAI,CAACiB,GAAG,CAAChC,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,MAAM6D,QAAQ,GAAGD,QAAQ,GAAG,IAAI,CAAC5D,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,IAAI,CAAC,IAAI,CAAC8D,aAAa,CAACD,QAAQ,CAAC,EAAE;MAC/B;IACJ;IACA,IAAI,CAACE,QAAQ,CAAC,CAAC,IAAI,CAAC/D,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE4D,QAAQ,CAAC,CAAC;EAC9C;EACAE,aAAaA,CAACD,QAAQ,EAAE;IACpB,OAAO7G,QAAQ,CAAC6G,QAAQ,EAAE,IAAI,CAACxB,MAAM,EAAE,IAAI,CAACC,KAAK,CAAC,KAAKuB,QAAQ;EACnE;EACA;IAAS,IAAI,CAAC5C,IAAI;MAAA,IAAA+C,qBAAA;MAAA,gBAAAC,iBAAA9C,CAAA;QAAA,QAAA6C,qBAAA,KAAAA,qBAAA,GApG+ElI,EAAE,CAAAoI,qBAAA,CAoGQvF,QAAQ,IAAAwC,CAAA,IAARxC,QAAQ;MAAA;IAAA,IAAqD;EAAE;EAC1K;IAAS,IAAI,CAACwF,IAAI,kBArG+ErI,EAAE,CAAAsI,iBAAA;MAAA9C,IAAA,EAqGJ3C,QAAQ;MAAA0F,SAAA;MAAAC,SAAA,WAAAC,eAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArGN1I,EAAE,CAAA4I,WAAA,CAqG61BjH,kBAAkB,KAA2BpB,UAAU;QAAA;QAAA,IAAAmI,EAAA;UAAA,IAAAG,EAAA;UArGt5B7I,EAAE,CAAA8I,cAAA,CAAAD,EAAA,GAAF7I,EAAE,CAAA+I,WAAA,QAAAJ,GAAA,CAAApF,WAAA,GAAAsF,EAAA;QAAA;MAAA;MAAAG,QAAA;MAAAC,YAAA,WAAAC,sBAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1I,EAAE,CAAAmJ,UAAA,sBAAAC,qCAAA;YAAA,OAqGJT,GAAA,CAAAU,SAAA,CAAU,CAAC;UAAA,CAAJ,CAAC,qCAAAC,oDAAAC,MAAA;YAAA,OAARZ,GAAA,CAAAxB,YAAA,CAAa,CAAC,EAAAoC,MAAA,CAAAnG,MAAe,CAAC;UAAA,CAAvB,CAAC,wCAAAoG,uDAAAD,MAAA;YAAA,OAARZ,GAAA,CAAAxB,YAAA,CAAa,CAAC,EAAAoC,MAAA,CAAAnG,MAAe,CAAC;UAAA,CAAvB,CAAC,uCAAAqG,sDAAAF,MAAA;YAAA,OAARZ,GAAA,CAAAxB,YAAA,EAAc,CAAC,EAAAoC,MAAA,CAAAnG,MAAe,CAAC;UAAA,CAAxB,CAAC,uCAAAsG,sDAAAH,MAAA;YAAA,OAARZ,GAAA,CAAAxB,YAAA,EAAc,CAAC,EAAAoC,MAAA,CAAAnG,MAAe,CAAC;UAAA,CAAxB,CAAC;QAAA;QAAA,IAAAsF,EAAA;UArGN1I,EAAE,CAAA2J,WAAA,cAAAhB,GAAA,CAAAvC,IAAA,eAqGH,CAAC,mBAAFuC,GAAA,CAAAiB,QAAA,CAAS,CAAC;UArGR5J,EAAE,CAAA6J,WAAA,aAqGJlB,GAAA,CAAAnE,IAAA,CAAK,CAAC,KAAC,CAAC,cAARmE,GAAA,CAAA3D,KAAA,CAAM,CAAC,MAAC,eAAA2D,GAAA,CAAA5C,OAAA,CAAA+D,UAAD,CAAC;UArGN9J,EAAE,CAAA+J,WAAA,cAqGJpB,GAAA,CAAAiB,QAAA,CAAS,CAAF,CAAC;QAAA;MAAA;MAAAI,MAAA;QAAA/D,GAAA;QAAAC,GAAA;QAAAC,IAAA;QAAAC,IAAA;QAAAC,QAAA;QAAAC,QAAA;QAAA1C,SAAA;QAAA2C,MAAA;QAAAC,KAAA;MAAA;MAAAjE,UAAA;MAAA0H,QAAA,GArGNjK,EAAE,CAAAkK,kBAAA,CAqGiwB,CAAClJ,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GArGryBhB,EAAE,CAAAmK,uBAAA;QAAAC,SAAA,EAqGm+B5H,cAAc;QAAAiD,OAAA;MAAA,KArGn/BzF,EAAE,CAAAqK,0BAAA,EAAFrK,EAAE,CAAAsK,oBAAA,EAAFtK,EAAE,CAAAuK,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kBAAAlC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1I,EAAE,CAAA6K,cAAA,YAqGutC,CAAC;UArG1tC7K,EAAE,CAAA8K,SAAA,cAqGqpD,CAAC,cAA8b,CAAC;UArGvlE9K,EAAE,CAAA+K,YAAA,CAqG4lE,CAAC;QAAA;QAAA,IAAArC,EAAA;UArG/lE1I,EAAE,CAAA6J,WAAA,0BAAAlB,GAAA,CAAAzB,iBAqGupC,CAAC,sBAAAyB,GAAA,CAAAzB,iBAAA,WAA4D,CAAC;UArGvtClH,EAAE,CAAAgL,SAAA,CAqG+5C,CAAC;UArGl6ChL,EAAE,CAAAiL,UAAA,aAAAtC,GAAA,CAAAiB,QAAA,EAqG+5C,CAAC,aAAAjB,GAAA,CAAA3B,gBAAwC,CAAC,QAAA2B,GAAA,CAAAzC,GAAsB,CAAC,QAAAyC,GAAA,CAAA1C,GAAsB,CAAC,YAAA0C,GAAA,CAAAzE,KAAA,KAAiC,CAAC,mBArG3hDlE,EAAE,CAAAkL,eAAA,KAAA5I,GAAA,CAqGykD,CAAC,SAAAqG,GAAA,CAAAvC,IAAwB,CAAC,aAAAuC,GAAA,CAAA/E,SAAA,SAA0C,CAAC;UArGhpD5D,EAAE,CAAAgL,SAAA,CAqG81D,CAAC;UArGj2DhL,EAAE,CAAAiL,UAAA,aAAAtC,GAAA,CAAAiB,QAAA,EAqG81D,CAAC,aAAAjB,GAAA,CAAA3B,gBAAwC,CAAC,QAAA2B,GAAA,CAAAzC,GAAsB,CAAC,QAAAyC,GAAA,CAAA1C,GAAsB,CAAC,YAAA0C,GAAA,CAAAzE,KAAA,KAAiC,CAAC,mBArG19DlE,EAAE,CAAAkL,eAAA,KAAA5I,GAAA,CAqGwgE,CAAC,SAAAqG,GAAA,CAAAvC,IAAwB,CAAC,aAAAuC,GAAA,CAAA/E,SAAA,SAA0C,CAAC;QAAA;MAAA;MAAAuH,YAAA,GAAspFtK,WAAW,EAA+BD,EAAE,CAACwK,oBAAoB,EAAyPxK,EAAE,CAACyK,kBAAkB,EAAyIzK,EAAE,CAAC0K,eAAe,EAAsF1K,EAAE,CAAC2K,OAAO,EAA8MjK,EAAE,CAACK,kBAAkB,EAAuGL,EAAE,CAACkK,qBAAqB,EAAqGlK,EAAE,CAACmK,iBAAiB,EAA0LnK,EAAE,CAACoK,iBAAiB;MAAAC,MAAA;MAAAC,eAAA;IAAA,EAAwH;EAAE;AACvrM;AACA7L,UAAU,CAAC,CACPsB,OAAO,CACV,EAAEwB,QAAQ,CAACgJ,SAAS,EAAE,qBAAqB,EAAE,IAAI,CAAC;AACnD;EAAA,QAAAnG,SAAA,oBAAAA,SAAA,KA1GqG1F,EAAE,CAAA2F,iBAAA,CA0GX9C,QAAQ,EAAc,CAAC;IACvG2C,IAAI,EAAEhF,SAAS;IACfoF,IAAI,EAAE,CAAC;MAAErD,UAAU,EAAE,IAAI;MAAEuJ,QAAQ,EAAE,WAAW;MAAEC,OAAO,EAAE,CAAClL,WAAW,EAAEe,SAAS,CAAC;MAAEgK,eAAe,EAAEnL,uBAAuB,CAACuL,MAAM;MAAEC,SAAS,EAAE,CAACjL,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAAEkL,cAAc,EAAE,CACzL;QACI9B,SAAS,EAAE5H,cAAc;QACzBiD,OAAO,EAAE,CAAC,mBAAmB;MACjC,CAAC,CACJ;MAAE0G,IAAI,EAAE;QACL,kBAAkB,EAAE,MAAM;QAC1B,iBAAiB,EAAE,IAAI;QACvB,sBAAsB,EAAE,YAAY;QACpC,oBAAoB,EAAE,QAAQ;QAC9B,qBAAqB,EAAE,SAAS;QAChC,oBAAoB,EAAE,oBAAoB;QAC1C,mBAAmB,EAAE,YAAY;QACjC,YAAY,EAAE,aAAa;QAC3B,2BAA2B,EAAE,gCAAgC;QAC7D,8BAA8B,EAAE,gCAAgC;QAChE,6BAA6B,EAAE,iCAAiC;QAChE,6BAA6B,EAAE;MACnC,CAAC;MAAExB,QAAQ,EAAE,6hCAA6hC;MAAEgB,MAAM,EAAE,CAAC,4kFAA4kF;IAAE,CAAC;EAChpH,CAAC,CAAC,QAAkB;IAAE1F,GAAG,EAAE,CAAC;MACpBT,IAAI,EAAE9E;IACV,CAAC,CAAC;IAAEwF,GAAG,EAAE,CAAC;MACNV,IAAI,EAAE9E;IACV,CAAC,CAAC;IAAEyF,IAAI,EAAE,CAAC;MACPX,IAAI,EAAE9E;IACV,CAAC,CAAC;IAAE0F,IAAI,EAAE,CAAC;MACPZ,IAAI,EAAE9E;IACV,CAAC,CAAC;IAAE2F,QAAQ,EAAE,CAAC;MACXb,IAAI,EAAE9E;IACV,CAAC,CAAC;IAAE4F,QAAQ,EAAE,CAAC;MACXd,IAAI,EAAE9E;IACV,CAAC,CAAC;IAAEkD,SAAS,EAAE,CAAC;MACZ4B,IAAI,EAAE9E;IACV,CAAC,CAAC;IAAE6F,MAAM,EAAE,CAAC;MACTf,IAAI,EAAE9E;IACV,CAAC,CAAC;IAAE8F,KAAK,EAAE,CAAC;MACRhB,IAAI,EAAE9E;IACV,CAAC,CAAC;IAAE6C,WAAW,EAAE,CAAC;MACdiC,IAAI,EAAE7E,YAAY;MAClBiF,IAAI,EAAE,CAACjE,kBAAkB,EAAE;QAAEyK,IAAI,EAAE7L;MAAW,CAAC;IACnD,CAAC,CAAC;IAAE0G,mBAAmB,EAAE;EAAG,CAAC;AAAA;;AAEzC;AACA;AACA;;AAEA,SAASpE,QAAQ,EAAEL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}