{"ast": null, "code": "import { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, computed, signal, Directive, Input, Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\nimport { TuiControl, tuiAsControl, tuiValueTransformerFrom } from '@taiga-ui/cdk/classes';\nimport { TuiTime } from '@taiga-ui/cdk/date-time';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldDirective, tuiTextfieldIconBinding, TuiTextfieldComponent, tuiAsTextfieldAccessor, TuiWithTextfield, TuiTextfieldContent } from '@taiga-ui/core/components/textfield';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport * as i2 from '@maskito/angular';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { maskitoTimeOptionsGenerator, maskitoSelectionChangeHandler } from '@maskito/kit';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiDirectiveBinding } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiAsOptionContent } from '@taiga-ui/core/components/data-list';\nimport { TuiDropdownDirective, tuiDropdownOpen, tuiDropdownEnabled } from '@taiga-ui/core/directives/dropdown';\nimport { tuiAsAuxiliary } from '@taiga-ui/core/tokens';\nimport { TuiSelectOption } from '@taiga-ui/kit/components/select';\nimport { TUI_TIME_TEXTS } from '@taiga-ui/kit/tokens';\nimport { tuiMaskito } from '@taiga-ui/kit/utils';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nconst _c0 = [\"tuiInputTime\", \"\", \"type\", \"time\"];\nfunction TuiInputTimeComponent_ng_container_0_input_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 2);\n    i0.ɵɵlistener(\"change\", function TuiInputTimeComponent_ng_container_0_input_1_Template_input_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setValue($event.target.value));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"step\", ctx_r1.step())(\"value\", ctx_r1.value());\n    i0.ɵɵattribute(\"list\", ctx_r1.list);\n  }\n}\nfunction TuiInputTimeComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiInputTimeComponent_ng_container_0_input_1_Template, 1, 3, \"input\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst TUI_INPUT_TIME_DEFAULT_OPTIONS = {\n  icon: () => '@tui.clock',\n  mode: 'HH:MM',\n  timeSegmentMaxValues: {},\n  valueTransformer: null\n};\nconst [TUI_INPUT_TIME_OPTIONS, tuiInputTimeOptionsProvider] = tuiCreateOptions(TUI_INPUT_TIME_DEFAULT_OPTIONS);\nclass TuiInputTimeDirective extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.textfield = inject(TuiTextfieldDirective);\n    this.dropdown = inject(TuiDropdownDirective);\n    this.open = tuiDropdownOpen();\n    this.options = inject(TUI_INPUT_TIME_OPTIONS);\n    this.fillers = toSignal(inject(TUI_TIME_TEXTS));\n    this.icon = tuiTextfieldIconBinding(TUI_INPUT_TIME_OPTIONS);\n    this.dropdownEnabled = tuiDropdownEnabled(computed(() => !this.native && this.interactive()));\n    this.filler = tuiDirectiveBinding(TuiTextfieldComponent, 'fillerSetter', computed((fillers = this.fillers()) => fillers?.[this.timeMode()] ?? ''), {});\n    this.mask = tuiMaskito(computed(() => this.computeMask({\n      ...this.options,\n      mode: this.timeMode(),\n      step: this.interactive() && !this.dropdown._content() ? 1 : 0\n    })));\n    this.accept = [];\n    this.native = tuiInjectElement().type === 'time' && inject(TUI_IS_MOBILE);\n    this.timeMode = signal(this.options.mode);\n  }\n  // TODO(v5): use signal inputs\n  set modeSetter(x) {\n    this.timeMode.set(x);\n  }\n  setValue(value) {\n    this.onChange(value);\n    this.textfield.value.set(value?.toString(this.timeMode()) ?? '');\n    if (!value) {\n      this.open.set(true);\n    }\n  }\n  writeValue(value) {\n    super.writeValue(value);\n    this.textfield.value.set(this.value()?.toString(this.timeMode()) ?? '');\n  }\n  onInput(value) {\n    const time = value.length === this.timeMode().length ? TuiTime.fromString(value) : null;\n    const newValue = this.accept.length && time ? this.findNearestTime(time, this.accept) : time;\n    this.control?.control?.updateValueAndValidity({\n      emitEvent: false\n    });\n    this.onChange(newValue);\n    if (newValue && newValue !== time) {\n      this.textfield.value.set(newValue?.toString(this.timeMode()));\n    }\n  }\n  toggle() {\n    this.open.update(x => !x);\n  }\n  computeMask(params) {\n    const options = maskitoTimeOptionsGenerator(params);\n    const {\n      mode\n    } = params;\n    const inputModeSwitchPlugin = maskitoSelectionChangeHandler(element => {\n      element.inputMode = element.selectionStart >= mode.indexOf(' AA') ? 'text' : 'numeric';\n    });\n    return {\n      ...options,\n      plugins: options.plugins.concat(mode.includes('AA') ? inputModeSwitchPlugin : [])\n    };\n  }\n  findNearestTime(value, items) {\n    // eslint-disable-next-line no-restricted-syntax\n    return items.reduce((previous, current) => Math.abs(current.valueOf() - value.valueOf()) < Math.abs(previous.valueOf() - value.valueOf()) ? current : previous);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiInputTimeDirective_BaseFactory;\n      return function TuiInputTimeDirective_Factory(t) {\n        return (ɵTuiInputTimeDirective_BaseFactory || (ɵTuiInputTimeDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiInputTimeDirective)))(t || TuiInputTimeDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiInputTimeDirective,\n      selectors: [[\"input\", \"tuiInputTime\", \"\"]],\n      hostAttrs: [\"inputmode\", \"numeric\"],\n      hostVars: 1,\n      hostBindings: function TuiInputTimeDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TuiInputTimeDirective_click_HostBindingHandler() {\n            return ctx.toggle();\n          })(\"input\", function TuiInputTimeDirective_input_HostBindingHandler($event) {\n            return ctx.onInput($event.target.value);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"disabled\", ctx.disabled());\n        }\n      },\n      inputs: {\n        accept: \"accept\",\n        modeSetter: [i0.ɵɵInputFlags.None, \"mode\", \"modeSetter\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsControl(TuiInputTimeDirective), tuiAsTextfieldAccessor(TuiInputTimeDirective), tuiAsAuxiliary(TuiInputTimeDirective), tuiValueTransformerFrom(TUI_INPUT_TIME_OPTIONS), tuiAsOptionContent(TuiSelectOption)]), i0.ɵɵHostDirectivesFeature([i1.TuiWithTextfield, i2.MaskitoDirective]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputTimeDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiInputTime]',\n      providers: [tuiAsControl(TuiInputTimeDirective), tuiAsTextfieldAccessor(TuiInputTimeDirective), tuiAsAuxiliary(TuiInputTimeDirective), tuiValueTransformerFrom(TUI_INPUT_TIME_OPTIONS), tuiAsOptionContent(TuiSelectOption)],\n      hostDirectives: [TuiWithTextfield, MaskitoDirective],\n      host: {\n        inputmode: 'numeric',\n        '[disabled]': 'disabled()',\n        '(click)': 'toggle()',\n        '(input)': 'onInput($event.target.value)'\n      }\n    }]\n  }], null, {\n    accept: [{\n      type: Input\n    }],\n    modeSetter: [{\n      type: Input,\n      args: ['mode']\n    }]\n  });\n})();\nclass TuiInputTimeComponent {\n  constructor() {\n    this.control = inject(TuiControl);\n    this.list = tuiInjectElement().getAttribute('list');\n    this.host = inject(TuiInputTimeDirective);\n    this.textfield = inject(TuiTextfieldDirective);\n    this.value = computed((value = this.control.value()) => value ? value.toString(this.host.timeMode().replace(' AA', '')) : '');\n    this.step = computed((mode = this.host.timeMode()) => {\n      switch (mode) {\n        case 'HH:MM:SS':\n        case 'HH:MM:SS AA':\n          return 1;\n        case 'HH:MM:SS.MSS':\n        case 'HH:MM:SS.MSS AA':\n          return 0.001;\n        default:\n          return 60;\n      }\n    });\n  }\n  setValue(value) {\n    const mode = this.host.timeMode();\n    const time = TuiTime.fromString(value);\n    this.control.onChange(time);\n    this.textfield.value.set(time.toString(mode));\n  }\n  static {\n    this.ɵfac = function TuiInputTimeComponent_Factory(t) {\n      return new (t || TuiInputTimeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiInputTimeComponent,\n      selectors: [[\"input\", \"tuiInputTime\", \"\", \"type\", \"time\"]],\n      hostAttrs: [\"ngSkipHydration\", \"true\"],\n      hostVars: 2,\n      hostBindings: function TuiInputTimeComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"type\", \"text\");\n          i0.ɵɵattribute(\"list\", null);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"ngIf\"], [\"type\", \"time\", 3, \"step\", \"value\", \"change\", 4, \"tuiTextfieldContent\"], [\"type\", \"time\", 3, \"change\", \"step\", \"value\"]],\n      template: function TuiInputTimeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiInputTimeComponent_ng_container_0_Template, 2, 0, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.host.native);\n        }\n      },\n      dependencies: [NgIf, TuiTextfieldContent],\n      styles: [\"tui-textfield input[tuiInputTime]~.t-content input[type=time]{position:absolute;right:0;left:auto;inline-size:calc(var(--t-right) + var(--t-padding));opacity:0;margin:0;padding:0}tui-textfield input[tuiInputTime]~.t-content input[type=time]::-webkit-calendar-picker-indicator{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputTimeComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiInputTime][type=\"time\"]',\n      imports: [NgIf, TuiTextfieldContent],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        ngSkipHydration: 'true',\n        '[type]': '\"text\"',\n        '[attr.list]': 'null'\n      },\n      template: \"<ng-container *ngIf=\\\"host.native\\\">\\n    <input\\n        *tuiTextfieldContent\\n        type=\\\"time\\\"\\n        [attr.list]=\\\"list\\\"\\n        [step]=\\\"step()\\\"\\n        [value]=\\\"value()\\\"\\n        (change)=\\\"setValue($any($event.target).value)\\\"\\n    />\\n</ng-container>\\n\",\n      styles: [\"tui-textfield input[tuiInputTime]~.t-content input[type=time]{position:absolute;right:0;left:auto;inline-size:calc(var(--t-right) + var(--t-padding));opacity:0;margin:0;padding:0}tui-textfield input[tuiInputTime]~.t-content input[type=time]::-webkit-calendar-picker-indicator{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}\\n\"]\n    }]\n  }], null, null);\n})();\nconst TuiInputTime = [TuiInputTimeDirective, TuiInputTimeComponent];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_TIME_DEFAULT_OPTIONS, TUI_INPUT_TIME_OPTIONS, TuiInputTime, TuiInputTimeComponent, TuiInputTimeDirective, tuiInputTimeOptionsProvider };", "map": {"version": 3, "names": ["NgIf", "i0", "inject", "computed", "signal", "Directive", "Input", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "TuiControl", "tuiAsControl", "tuiValueTransformerFrom", "TuiTime", "tuiInjectElement", "i1", "TuiTextfieldDirective", "tuiTextfieldIconBinding", "TuiTextfieldComponent", "tuiAsTextfieldAccessor", "TuiWithTextfield", "TuiTextfieldContent", "toSignal", "i2", "MaskitoDirective", "maskitoTimeOptionsGenerator", "maskitoSelectionChangeHandler", "TUI_IS_MOBILE", "tuiDirectiveBinding", "tuiAsOptionContent", "TuiDropdownDirective", "tuiDropdownOpen", "tuiDropdownEnabled", "tuiAsAuxiliary", "TuiSelectOption", "TUI_TIME_TEXTS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tuiCreateOptions", "_c0", "TuiInputTimeComponent_ng_container_0_input_1_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiInputTimeComponent_ng_container_0_input_1_Template_input_change_0_listener", "$event", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "setValue", "target", "value", "ɵɵelementEnd", "ɵɵproperty", "step", "ɵɵattribute", "list", "TuiInputTimeComponent_ng_container_0_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "TUI_INPUT_TIME_DEFAULT_OPTIONS", "icon", "mode", "timeSegmentMaxValues", "valueTransformer", "TUI_INPUT_TIME_OPTIONS", "tuiInputTimeOptionsProvider", "TuiInputTimeDirective", "constructor", "arguments", "textfield", "dropdown", "open", "options", "fillers", "dropdownEnabled", "native", "interactive", "filler", "timeMode", "mask", "computeMask", "_content", "accept", "type", "modeSetter", "x", "set", "onChange", "toString", "writeValue", "onInput", "time", "length", "fromString", "newValue", "findNearestTime", "control", "updateValueAndValidity", "emitEvent", "toggle", "update", "params", "inputModeSwitchPlugin", "element", "inputMode", "selectionStart", "indexOf", "plugins", "concat", "includes", "items", "reduce", "previous", "current", "Math", "abs", "valueOf", "ɵfac", "ɵTuiInputTimeDirective_BaseFactory", "TuiInputTimeDirective_Factory", "t", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "selectors", "hostAttrs", "hostVars", "hostBindings", "TuiInputTimeDirective_HostBindings", "TuiInputTimeDirective_click_HostBindingHandler", "TuiInputTimeDirective_input_HostBindingHandler", "ɵɵhostProperty", "disabled", "inputs", "ɵɵInputFlags", "None", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "hostDirectives", "host", "inputmode", "TuiInputTimeComponent", "getAttribute", "replace", "TuiInputTimeComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "TuiInputTimeComponent_HostBindings", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "consts", "template", "TuiInputTimeComponent_Template", "dependencies", "styles", "encapsulation", "changeDetection", "imports", "OnPush", "ngSkipHydration", "TuiInputTime"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-input-time.mjs"], "sourcesContent": ["import { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, computed, signal, Directive, Input, Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\nimport { TuiControl, tuiAsControl, tuiValueTransformerFrom } from '@taiga-ui/cdk/classes';\nimport { TuiTime } from '@taiga-ui/cdk/date-time';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldDirective, tuiTextfieldIconBinding, TuiTextfieldComponent, tuiAsTextfieldAccessor, TuiWithTextfield, TuiTextfieldContent } from '@taiga-ui/core/components/textfield';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport * as i2 from '@maskito/angular';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { maskitoTimeOptionsGenerator, maskitoSelectionChangeHandler } from '@maskito/kit';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiDirectiveBinding } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiAsOptionContent } from '@taiga-ui/core/components/data-list';\nimport { TuiDropdownDirective, tuiDropdownOpen, tuiDropdownEnabled } from '@taiga-ui/core/directives/dropdown';\nimport { tuiAsAuxiliary } from '@taiga-ui/core/tokens';\nimport { TuiSelectOption } from '@taiga-ui/kit/components/select';\nimport { TUI_TIME_TEXTS } from '@taiga-ui/kit/tokens';\nimport { tuiMaskito } from '@taiga-ui/kit/utils';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\n\nconst TUI_INPUT_TIME_DEFAULT_OPTIONS = {\n    icon: () => '@tui.clock',\n    mode: 'HH:MM',\n    timeSegmentMaxValues: {},\n    valueTransformer: null,\n};\nconst [TUI_INPUT_TIME_OPTIONS, tuiInputTimeOptionsProvider] = tuiCreateOptions(TUI_INPUT_TIME_DEFAULT_OPTIONS);\n\nclass TuiInputTimeDirective extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.textfield = inject(TuiTextfieldDirective);\n        this.dropdown = inject(TuiDropdownDirective);\n        this.open = tuiDropdownOpen();\n        this.options = inject(TUI_INPUT_TIME_OPTIONS);\n        this.fillers = toSignal(inject(TUI_TIME_TEXTS));\n        this.icon = tuiTextfieldIconBinding(TUI_INPUT_TIME_OPTIONS);\n        this.dropdownEnabled = tuiDropdownEnabled(computed(() => !this.native && this.interactive()));\n        this.filler = tuiDirectiveBinding(TuiTextfieldComponent, 'fillerSetter', computed((fillers = this.fillers()) => fillers?.[this.timeMode()] ?? ''), {});\n        this.mask = tuiMaskito(computed(() => this.computeMask({\n            ...this.options,\n            mode: this.timeMode(),\n            step: this.interactive() && !this.dropdown._content() ? 1 : 0,\n        })));\n        this.accept = [];\n        this.native = tuiInjectElement().type === 'time' && inject(TUI_IS_MOBILE);\n        this.timeMode = signal(this.options.mode);\n    }\n    // TODO(v5): use signal inputs\n    set modeSetter(x) {\n        this.timeMode.set(x);\n    }\n    setValue(value) {\n        this.onChange(value);\n        this.textfield.value.set(value?.toString(this.timeMode()) ?? '');\n        if (!value) {\n            this.open.set(true);\n        }\n    }\n    writeValue(value) {\n        super.writeValue(value);\n        this.textfield.value.set(this.value()?.toString(this.timeMode()) ?? '');\n    }\n    onInput(value) {\n        const time = value.length === this.timeMode().length ? TuiTime.fromString(value) : null;\n        const newValue = this.accept.length && time ? this.findNearestTime(time, this.accept) : time;\n        this.control?.control?.updateValueAndValidity({ emitEvent: false });\n        this.onChange(newValue);\n        if (newValue && newValue !== time) {\n            this.textfield.value.set(newValue?.toString(this.timeMode()));\n        }\n    }\n    toggle() {\n        this.open.update((x) => !x);\n    }\n    computeMask(params) {\n        const options = maskitoTimeOptionsGenerator(params);\n        const { mode } = params;\n        const inputModeSwitchPlugin = maskitoSelectionChangeHandler((element) => {\n            element.inputMode =\n                element.selectionStart >= mode.indexOf(' AA') ? 'text' : 'numeric';\n        });\n        return {\n            ...options,\n            plugins: options.plugins.concat(mode.includes('AA') ? inputModeSwitchPlugin : []),\n        };\n    }\n    findNearestTime(value, items) {\n        // eslint-disable-next-line no-restricted-syntax\n        return items.reduce((previous, current) => Math.abs(current.valueOf() - value.valueOf()) <\n            Math.abs(previous.valueOf() - value.valueOf())\n            ? current\n            : previous);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputTimeDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputTimeDirective, isStandalone: true, selector: \"input[tuiInputTime]\", inputs: { accept: \"accept\", modeSetter: [\"mode\", \"modeSetter\"] }, host: { attributes: { \"inputmode\": \"numeric\" }, listeners: { \"click\": \"toggle()\", \"input\": \"onInput($event.target.value)\" }, properties: { \"disabled\": \"disabled()\" } }, providers: [\n            tuiAsControl(TuiInputTimeDirective),\n            tuiAsTextfieldAccessor(TuiInputTimeDirective),\n            tuiAsAuxiliary(TuiInputTimeDirective),\n            tuiValueTransformerFrom(TUI_INPUT_TIME_OPTIONS),\n            tuiAsOptionContent(TuiSelectOption),\n        ], usesInheritance: true, hostDirectives: [{ directive: i1.TuiWithTextfield }, { directive: i2.MaskitoDirective }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputTimeDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiInputTime]',\n                    providers: [\n                        tuiAsControl(TuiInputTimeDirective),\n                        tuiAsTextfieldAccessor(TuiInputTimeDirective),\n                        tuiAsAuxiliary(TuiInputTimeDirective),\n                        tuiValueTransformerFrom(TUI_INPUT_TIME_OPTIONS),\n                        tuiAsOptionContent(TuiSelectOption),\n                    ],\n                    hostDirectives: [TuiWithTextfield, MaskitoDirective],\n                    host: {\n                        inputmode: 'numeric',\n                        '[disabled]': 'disabled()',\n                        '(click)': 'toggle()',\n                        '(input)': 'onInput($event.target.value)',\n                    },\n                }]\n        }], propDecorators: { accept: [{\n                type: Input\n            }], modeSetter: [{\n                type: Input,\n                args: ['mode']\n            }] } });\n\nclass TuiInputTimeComponent {\n    constructor() {\n        this.control = inject(TuiControl);\n        this.list = tuiInjectElement().getAttribute('list');\n        this.host = inject(TuiInputTimeDirective);\n        this.textfield = inject(TuiTextfieldDirective);\n        this.value = computed((value = this.control.value()) => value\n            ? value.toString(this.host.timeMode().replace(' AA', ''))\n            : '');\n        this.step = computed((mode = this.host.timeMode()) => {\n            switch (mode) {\n                case 'HH:MM:SS':\n                case 'HH:MM:SS AA':\n                    return 1;\n                case 'HH:MM:SS.MSS':\n                case 'HH:MM:SS.MSS AA':\n                    return 0.001;\n                default:\n                    return 60;\n            }\n        });\n    }\n    setValue(value) {\n        const mode = this.host.timeMode();\n        const time = TuiTime.fromString(value);\n        this.control.onChange(time);\n        this.textfield.value.set(time.toString(mode));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputTimeComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputTimeComponent, isStandalone: true, selector: \"input[tuiInputTime][type=\\\"time\\\"]\", host: { attributes: { \"ngSkipHydration\": \"true\" }, properties: { \"type\": \"\\\"text\\\"\", \"attr.list\": \"null\" } }, ngImport: i0, template: \"<ng-container *ngIf=\\\"host.native\\\">\\n    <input\\n        *tuiTextfieldContent\\n        type=\\\"time\\\"\\n        [attr.list]=\\\"list\\\"\\n        [step]=\\\"step()\\\"\\n        [value]=\\\"value()\\\"\\n        (change)=\\\"setValue($any($event.target).value)\\\"\\n    />\\n</ng-container>\\n\", styles: [\"tui-textfield input[tuiInputTime]~.t-content input[type=time]{position:absolute;right:0;left:auto;inline-size:calc(var(--t-right) + var(--t-padding));opacity:0;margin:0;padding:0}tui-textfield input[tuiInputTime]~.t-content input[type=time]::-webkit-calendar-picker-indicator{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: TuiTextfieldContent, selector: \"ng-template[tuiTextfieldContent]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputTimeComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'input[tuiInputTime][type=\"time\"]', imports: [NgIf, TuiTextfieldContent], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        ngSkipHydration: 'true',\n                        '[type]': '\"text\"',\n                        '[attr.list]': 'null',\n                    }, template: \"<ng-container *ngIf=\\\"host.native\\\">\\n    <input\\n        *tuiTextfieldContent\\n        type=\\\"time\\\"\\n        [attr.list]=\\\"list\\\"\\n        [step]=\\\"step()\\\"\\n        [value]=\\\"value()\\\"\\n        (change)=\\\"setValue($any($event.target).value)\\\"\\n    />\\n</ng-container>\\n\", styles: [\"tui-textfield input[tuiInputTime]~.t-content input[type=time]{position:absolute;right:0;left:auto;inline-size:calc(var(--t-right) + var(--t-padding));opacity:0;margin:0;padding:0}tui-textfield input[tuiInputTime]~.t-content input[type=time]::-webkit-calendar-picker-indicator{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}\\n\"] }]\n        }] });\n\nconst TuiInputTime = [TuiInputTimeDirective, TuiInputTimeComponent];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_TIME_DEFAULT_OPTIONS, TUI_INPUT_TIME_OPTIONS, TuiInputTime, TuiInputTimeComponent, TuiInputTimeDirective, tuiInputTimeOptionsProvider };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,iBAAiB;AACtC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,QAAQ,eAAe;AACjI,SAASC,UAAU,EAAEC,YAAY,EAAEC,uBAAuB,QAAQ,uBAAuB;AACzF,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,qBAAqB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,gBAAgB,EAAEC,mBAAmB,QAAQ,qCAAqC;AAC1L,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,OAAO,KAAKC,EAAE,MAAM,kBAAkB;AACtC,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,2BAA2B,EAAEC,6BAA6B,QAAQ,cAAc;AACzF,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,oCAAoC;AAC9G,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,gBAAgB,QAAQ,wBAAwB;AAAC,MAAAC,GAAA;AAAA,SAAAC,sDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GA4E2CzC,EAAE,CAAA0C,gBAAA;IAAF1C,EAAE,CAAA2C,cAAA,cAiE0d,CAAC;IAjE7d3C,EAAE,CAAA4C,UAAA,oBAAAC,8EAAAC,MAAA;MAAF9C,EAAE,CAAA+C,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAFhD,EAAE,CAAAiD,aAAA;MAAA,OAAFjD,EAAE,CAAAkD,WAAA,CAiE8aF,MAAA,CAAAG,QAAA,CAAAL,MAAA,CAAAM,MAAA,CAAAC,KAAkC,CAAC;IAAA,CAAC,CAAC;IAjErdrD,EAAE,CAAAsD,YAAA,CAiE0d,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAS,MAAA,GAjE7dhD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAuD,UAAA,SAAAP,MAAA,CAAAQ,IAAA,EAiE2X,CAAC,UAAAR,MAAA,CAAAK,KAAA,EAA4B,CAAC;IAjE3ZrD,EAAE,CAAAyD,WAAA,SAAAT,MAAA,CAAAU,IAAA;EAAA;AAAA;AAAA,SAAAC,8CAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFvC,EAAE,CAAA4D,uBAAA,EAiEiQ,CAAC;IAjEpQ5D,EAAE,CAAA6D,UAAA,IAAAvB,qDAAA,kBAiE0d,CAAC;IAjE7dtC,EAAE,CAAA8D,qBAAA;EAAA;AAAA;AA1EvG,MAAMC,8BAA8B,GAAG;EACnCC,IAAI,EAAEA,CAAA,KAAM,YAAY;EACxBC,IAAI,EAAE,OAAO;EACbC,oBAAoB,EAAE,CAAC,CAAC;EACxBC,gBAAgB,EAAE;AACtB,CAAC;AACD,MAAM,CAACC,sBAAsB,EAAEC,2BAA2B,CAAC,GAAGjC,gBAAgB,CAAC2B,8BAA8B,CAAC;AAE9G,MAAMO,qBAAqB,SAAS7D,UAAU,CAAC;EAC3C8D,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,SAAS,GAAGxE,MAAM,CAACc,qBAAqB,CAAC;IAC9C,IAAI,CAAC2D,QAAQ,GAAGzE,MAAM,CAAC4B,oBAAoB,CAAC;IAC5C,IAAI,CAAC8C,IAAI,GAAG7C,eAAe,CAAC,CAAC;IAC7B,IAAI,CAAC8C,OAAO,GAAG3E,MAAM,CAACmE,sBAAsB,CAAC;IAC7C,IAAI,CAACS,OAAO,GAAGxD,QAAQ,CAACpB,MAAM,CAACiC,cAAc,CAAC,CAAC;IAC/C,IAAI,CAAC8B,IAAI,GAAGhD,uBAAuB,CAACoD,sBAAsB,CAAC;IAC3D,IAAI,CAACU,eAAe,GAAG/C,kBAAkB,CAAC7B,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC6E,MAAM,IAAI,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;IAC7F,IAAI,CAACC,MAAM,GAAGtD,mBAAmB,CAACV,qBAAqB,EAAE,cAAc,EAAEf,QAAQ,CAAC,CAAC2E,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC,KAAKA,OAAO,GAAG,IAAI,CAACK,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtJ,IAAI,CAACC,IAAI,GAAGhD,UAAU,CAACjC,QAAQ,CAAC,MAAM,IAAI,CAACkF,WAAW,CAAC;MACnD,GAAG,IAAI,CAACR,OAAO;MACfX,IAAI,EAAE,IAAI,CAACiB,QAAQ,CAAC,CAAC;MACrB1B,IAAI,EAAE,IAAI,CAACwB,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAACN,QAAQ,CAACW,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG;IAChE,CAAC,CAAC,CAAC,CAAC;IACJ,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACP,MAAM,GAAGlE,gBAAgB,CAAC,CAAC,CAAC0E,IAAI,KAAK,MAAM,IAAItF,MAAM,CAACyB,aAAa,CAAC;IACzE,IAAI,CAACwD,QAAQ,GAAG/E,MAAM,CAAC,IAAI,CAACyE,OAAO,CAACX,IAAI,CAAC;EAC7C;EACA;EACA,IAAIuB,UAAUA,CAACC,CAAC,EAAE;IACd,IAAI,CAACP,QAAQ,CAACQ,GAAG,CAACD,CAAC,CAAC;EACxB;EACAtC,QAAQA,CAACE,KAAK,EAAE;IACZ,IAAI,CAACsC,QAAQ,CAACtC,KAAK,CAAC;IACpB,IAAI,CAACoB,SAAS,CAACpB,KAAK,CAACqC,GAAG,CAACrC,KAAK,EAAEuC,QAAQ,CAAC,IAAI,CAACV,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAChE,IAAI,CAAC7B,KAAK,EAAE;MACR,IAAI,CAACsB,IAAI,CAACe,GAAG,CAAC,IAAI,CAAC;IACvB;EACJ;EACAG,UAAUA,CAACxC,KAAK,EAAE;IACd,KAAK,CAACwC,UAAU,CAACxC,KAAK,CAAC;IACvB,IAAI,CAACoB,SAAS,CAACpB,KAAK,CAACqC,GAAG,CAAC,IAAI,CAACrC,KAAK,CAAC,CAAC,EAAEuC,QAAQ,CAAC,IAAI,CAACV,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;EAC3E;EACAY,OAAOA,CAACzC,KAAK,EAAE;IACX,MAAM0C,IAAI,GAAG1C,KAAK,CAAC2C,MAAM,KAAK,IAAI,CAACd,QAAQ,CAAC,CAAC,CAACc,MAAM,GAAGpF,OAAO,CAACqF,UAAU,CAAC5C,KAAK,CAAC,GAAG,IAAI;IACvF,MAAM6C,QAAQ,GAAG,IAAI,CAACZ,MAAM,CAACU,MAAM,IAAID,IAAI,GAAG,IAAI,CAACI,eAAe,CAACJ,IAAI,EAAE,IAAI,CAACT,MAAM,CAAC,GAAGS,IAAI;IAC5F,IAAI,CAACK,OAAO,EAAEA,OAAO,EAAEC,sBAAsB,CAAC;MAAEC,SAAS,EAAE;IAAM,CAAC,CAAC;IACnE,IAAI,CAACX,QAAQ,CAACO,QAAQ,CAAC;IACvB,IAAIA,QAAQ,IAAIA,QAAQ,KAAKH,IAAI,EAAE;MAC/B,IAAI,CAACtB,SAAS,CAACpB,KAAK,CAACqC,GAAG,CAACQ,QAAQ,EAAEN,QAAQ,CAAC,IAAI,CAACV,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjE;EACJ;EACAqB,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC5B,IAAI,CAAC6B,MAAM,CAAEf,CAAC,IAAK,CAACA,CAAC,CAAC;EAC/B;EACAL,WAAWA,CAACqB,MAAM,EAAE;IAChB,MAAM7B,OAAO,GAAGpD,2BAA2B,CAACiF,MAAM,CAAC;IACnD,MAAM;MAAExC;IAAK,CAAC,GAAGwC,MAAM;IACvB,MAAMC,qBAAqB,GAAGjF,6BAA6B,CAAEkF,OAAO,IAAK;MACrEA,OAAO,CAACC,SAAS,GACbD,OAAO,CAACE,cAAc,IAAI5C,IAAI,CAAC6C,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,SAAS;IAC1E,CAAC,CAAC;IACF,OAAO;MACH,GAAGlC,OAAO;MACVmC,OAAO,EAAEnC,OAAO,CAACmC,OAAO,CAACC,MAAM,CAAC/C,IAAI,CAACgD,QAAQ,CAAC,IAAI,CAAC,GAAGP,qBAAqB,GAAG,EAAE;IACpF,CAAC;EACL;EACAP,eAAeA,CAAC9C,KAAK,EAAE6D,KAAK,EAAE;IAC1B;IACA,OAAOA,KAAK,CAACC,MAAM,CAAC,CAACC,QAAQ,EAAEC,OAAO,KAAKC,IAAI,CAACC,GAAG,CAACF,OAAO,CAACG,OAAO,CAAC,CAAC,GAAGnE,KAAK,CAACmE,OAAO,CAAC,CAAC,CAAC,GACpFF,IAAI,CAACC,GAAG,CAACH,QAAQ,CAACI,OAAO,CAAC,CAAC,GAAGnE,KAAK,CAACmE,OAAO,CAAC,CAAC,CAAC,GAC5CH,OAAO,GACPD,QAAQ,CAAC;EACnB;EACA;IAAS,IAAI,CAACK,IAAI;MAAA,IAAAC,kCAAA;MAAA,gBAAAC,8BAAAC,CAAA;QAAA,QAAAF,kCAAA,KAAAA,kCAAA,GAA+E1H,EAAE,CAAA6H,qBAAA,CAAQvD,qBAAqB,IAAAsD,CAAA,IAArBtD,qBAAqB;MAAA;IAAA,IAAqD;EAAE;EACvL;IAAS,IAAI,CAACwD,IAAI,kBAD+E9H,EAAE,CAAA+H,iBAAA;MAAAxC,IAAA,EACJjB,qBAAqB;MAAA0D,SAAA;MAAAC,SAAA,gBAA4J,SAAS;MAAAC,QAAA;MAAAC,YAAA,WAAAC,mCAAA7F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADxLvC,EAAE,CAAA4C,UAAA,mBAAAyF,+CAAA;YAAA,OACJ7F,GAAA,CAAA+D,MAAA,CAAO,CAAC;UAAA,CAAY,CAAC,mBAAA+B,+CAAAxF,MAAA;YAAA,OAArBN,GAAA,CAAAsD,OAAA,CAAAhD,MAAA,CAAAM,MAAA,CAAAC,KAA2B,CAAC;UAAA,CAAR,CAAC;QAAA;QAAA,IAAAd,EAAA;UADnBvC,EAAE,CAAAuI,cAAA,aACJ/F,GAAA,CAAAgG,QAAA,CAAS,CAAW,CAAC;QAAA;MAAA;MAAAC,MAAA;QAAAnD,MAAA;QAAAE,UAAA,GADnBxF,EAAE,CAAA0I,YAAA,CAAAC,IAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAF7I,EAAE,CAAA8I,kBAAA,CAC8T,CACzZpI,YAAY,CAAC4D,qBAAqB,CAAC,EACnCpD,sBAAsB,CAACoD,qBAAqB,CAAC,EAC7CtC,cAAc,CAACsC,qBAAqB,CAAC,EACrC3D,uBAAuB,CAACyD,sBAAsB,CAAC,EAC/CxC,kBAAkB,CAACK,eAAe,CAAC,CACtC,GAP4FjC,EAAE,CAAA+I,uBAAA,EAOvCjI,EAAE,CAACK,gBAAgB,EAAiBG,EAAE,CAACC,gBAAgB,IAPlBvB,EAAE,CAAAgJ,0BAAA;IAAA,EAOoC;EAAE;AAC7I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KATqGjJ,EAAE,CAAAkJ,iBAAA,CASX5E,qBAAqB,EAAc,CAAC;IACpHiB,IAAI,EAAEnF,SAAS;IACf+I,IAAI,EAAE,CAAC;MACCP,UAAU,EAAE,IAAI;MAChBQ,QAAQ,EAAE,qBAAqB;MAC/BC,SAAS,EAAE,CACP3I,YAAY,CAAC4D,qBAAqB,CAAC,EACnCpD,sBAAsB,CAACoD,qBAAqB,CAAC,EAC7CtC,cAAc,CAACsC,qBAAqB,CAAC,EACrC3D,uBAAuB,CAACyD,sBAAsB,CAAC,EAC/CxC,kBAAkB,CAACK,eAAe,CAAC,CACtC;MACDqH,cAAc,EAAE,CAACnI,gBAAgB,EAAEI,gBAAgB,CAAC;MACpDgI,IAAI,EAAE;QACFC,SAAS,EAAE,SAAS;QACpB,YAAY,EAAE,YAAY;QAC1B,SAAS,EAAE,UAAU;QACrB,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAElE,MAAM,EAAE,CAAC;MACvBC,IAAI,EAAElF;IACV,CAAC,CAAC;IAAEmF,UAAU,EAAE,CAAC;MACbD,IAAI,EAAElF,KAAK;MACX8I,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMM,qBAAqB,CAAC;EACxBlF,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6B,OAAO,GAAGnG,MAAM,CAACQ,UAAU,CAAC;IACjC,IAAI,CAACiD,IAAI,GAAG7C,gBAAgB,CAAC,CAAC,CAAC6I,YAAY,CAAC,MAAM,CAAC;IACnD,IAAI,CAACH,IAAI,GAAGtJ,MAAM,CAACqE,qBAAqB,CAAC;IACzC,IAAI,CAACG,SAAS,GAAGxE,MAAM,CAACc,qBAAqB,CAAC;IAC9C,IAAI,CAACsC,KAAK,GAAGnD,QAAQ,CAAC,CAACmD,KAAK,GAAG,IAAI,CAAC+C,OAAO,CAAC/C,KAAK,CAAC,CAAC,KAAKA,KAAK,GACvDA,KAAK,CAACuC,QAAQ,CAAC,IAAI,CAAC2D,IAAI,CAACrE,QAAQ,CAAC,CAAC,CAACyE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,GACvD,EAAE,CAAC;IACT,IAAI,CAACnG,IAAI,GAAGtD,QAAQ,CAAC,CAAC+D,IAAI,GAAG,IAAI,CAACsF,IAAI,CAACrE,QAAQ,CAAC,CAAC,KAAK;MAClD,QAAQjB,IAAI;QACR,KAAK,UAAU;QACf,KAAK,aAAa;UACd,OAAO,CAAC;QACZ,KAAK,cAAc;QACnB,KAAK,iBAAiB;UAClB,OAAO,KAAK;QAChB;UACI,OAAO,EAAE;MACjB;IACJ,CAAC,CAAC;EACN;EACAd,QAAQA,CAACE,KAAK,EAAE;IACZ,MAAMY,IAAI,GAAG,IAAI,CAACsF,IAAI,CAACrE,QAAQ,CAAC,CAAC;IACjC,MAAMa,IAAI,GAAGnF,OAAO,CAACqF,UAAU,CAAC5C,KAAK,CAAC;IACtC,IAAI,CAAC+C,OAAO,CAACT,QAAQ,CAACI,IAAI,CAAC;IAC3B,IAAI,CAACtB,SAAS,CAACpB,KAAK,CAACqC,GAAG,CAACK,IAAI,CAACH,QAAQ,CAAC3B,IAAI,CAAC,CAAC;EACjD;EACA;IAAS,IAAI,CAACwD,IAAI,YAAAmC,8BAAAhC,CAAA;MAAA,YAAAA,CAAA,IAAyF6B,qBAAqB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAACI,IAAI,kBAjE+E7J,EAAE,CAAA8J,iBAAA;MAAAvE,IAAA,EAiEJkE,qBAAqB;MAAAzB,SAAA;MAAAC,SAAA,sBAA+G,MAAM;MAAAC,QAAA;MAAAC,YAAA,WAAA4B,mCAAAxH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjExIvC,EAAE,CAAAuI,cAAA,SAiEJ,MAAoB,CAAC;UAjEnBvI,EAAE,CAAAyD,WAAA,SAiEJ,IAAI;QAAA;MAAA;MAAAmF,UAAA;MAAAC,QAAA,GAjEF7I,EAAE,CAAAgK,mBAAA;MAAAC,KAAA,EAAA5H,GAAA;MAAA6H,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAA/H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvC,EAAE,CAAA6D,UAAA,IAAAF,6CAAA,yBAiEiQ,CAAC;QAAA;QAAA,IAAApB,EAAA;UAjEpQvC,EAAE,CAAAuD,UAAA,SAAAf,GAAA,CAAA+G,IAAA,CAAAxE,MAiE8P,CAAC;QAAA;MAAA;MAAAwF,YAAA,GAAgoBxK,IAAI,EAA6FqB,mBAAmB;MAAAoJ,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAkJ;EAAE;AAC9uC;AACA;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KAnEqGjJ,EAAE,CAAAkJ,iBAAA,CAmEXO,qBAAqB,EAAc,CAAC;IACpHlE,IAAI,EAAEjF,SAAS;IACf6I,IAAI,EAAE,CAAC;MAAEP,UAAU,EAAE,IAAI;MAAEQ,QAAQ,EAAE,kCAAkC;MAAEuB,OAAO,EAAE,CAAC5K,IAAI,EAAEqB,mBAAmB,CAAC;MAAEqJ,aAAa,EAAElK,iBAAiB,CAACoI,IAAI;MAAE+B,eAAe,EAAElK,uBAAuB,CAACoK,MAAM;MAAErB,IAAI,EAAE;QACjMsB,eAAe,EAAE,MAAM;QACvB,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE;MACnB,CAAC;MAAER,QAAQ,EAAE,kRAAkR;MAAEG,MAAM,EAAE,CAAC,wVAAwV;IAAE,CAAC;EACjpB,CAAC,CAAC;AAAA;AAEV,MAAMM,YAAY,GAAG,CAACxG,qBAAqB,EAAEmF,qBAAqB,CAAC;;AAEnE;AACA;AACA;;AAEA,SAAS1F,8BAA8B,EAAEK,sBAAsB,EAAE0G,YAAY,EAAErB,qBAAqB,EAAEnF,qBAAqB,EAAED,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}