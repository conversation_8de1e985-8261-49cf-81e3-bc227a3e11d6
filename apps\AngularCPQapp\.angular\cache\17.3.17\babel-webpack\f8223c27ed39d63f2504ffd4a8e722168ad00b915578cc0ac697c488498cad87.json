{"ast": null, "code": "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { inject, NgZone, Directive, Output, Input } from '@angular/core';\nimport { NgControl } from '@angular/forms';\nimport { tuiZoneOptimized } from '@taiga-ui/cdk/observables';\nimport { TUI_ACTIVE_ELEMENT } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement, tuiArrayRemove, tuiPure } from '@taiga-ui/cdk/utils';\nimport { map, startWith, distinctUntilChanged, skip, tap, share } from 'rxjs';\nclass TuiActiveZone {\n  constructor() {\n    // TODO: Should we remove in v5? It's no longer used in Taiga UI\n    this.control = inject(NgControl, {\n      self: true,\n      optional: true\n    });\n    this.active$ = inject(TUI_ACTIVE_ELEMENT);\n    this.zone = inject(NgZone);\n    this.el = tuiInjectElement();\n    this.tuiActiveZoneParent = null;\n    this.subActiveZones = [];\n    this.directParentActiveZone = inject(TuiActiveZone, {\n      skipSelf: true,\n      optional: true\n    });\n    this.tuiActiveZoneChange = this.active$.pipe(map(element => !!element && this.contains(element)), startWith(false), distinctUntilChanged(), skip(1), tap(active => {\n      if (!active && typeof this.control?.valueAccessor.onTouched === 'function') {\n        this.control.valueAccessor.onTouched();\n      }\n    }), tuiZoneOptimized(this.zone), share());\n    this.directParentActiveZone?.addSubActiveZone(this);\n  }\n  set tuiActiveZoneParentSetter(zone) {\n    this.setZone(zone);\n  }\n  ngOnDestroy() {\n    this.directParentActiveZone?.removeSubActiveZone(this);\n    this.tuiActiveZoneParent?.removeSubActiveZone(this);\n  }\n  contains(node) {\n    return this.el.contains(node) || this.subActiveZones.some((item, index, array) => array.indexOf(item) === index && item.contains(node));\n  }\n  setZone(zone) {\n    this.tuiActiveZoneParent?.removeSubActiveZone(this);\n    zone?.addSubActiveZone(this);\n    this.tuiActiveZoneParent = zone;\n  }\n  addSubActiveZone(activeZone) {\n    this.subActiveZones = [...this.subActiveZones, activeZone];\n  }\n  removeSubActiveZone(activeZone) {\n    this.subActiveZones = tuiArrayRemove(this.subActiveZones, this.subActiveZones.indexOf(activeZone));\n  }\n  static {\n    this.ɵfac = function TuiActiveZone_Factory(t) {\n      return new (t || TuiActiveZone)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiActiveZone,\n      selectors: [[\"\", \"tuiActiveZone\", \"\", 5, \"ng-container\"], [\"\", \"tuiActiveZoneChange\", \"\", 5, \"ng-container\"], [\"\", \"tuiActiveZoneParent\", \"\", 5, \"ng-container\"]],\n      hostBindings: function TuiActiveZone_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mousedown.zoneless\", function TuiActiveZone_mousedown_zoneless_HostBindingHandler() {\n            return 0;\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        tuiActiveZoneParentSetter: [i0.ɵɵInputFlags.None, \"tuiActiveZoneParent\", \"tuiActiveZoneParentSetter\"]\n      },\n      outputs: {\n        tuiActiveZoneChange: \"tuiActiveZoneChange\"\n      },\n      exportAs: [\"tuiActiveZone\"],\n      standalone: true\n    });\n  }\n}\n__decorate([tuiPure], TuiActiveZone.prototype, \"setZone\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiActiveZone, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiActiveZone]:not(ng-container), [tuiActiveZoneChange]:not(ng-container), [tuiActiveZoneParent]:not(ng-container)',\n      exportAs: 'tuiActiveZone',\n      host: {\n        '(document:mousedown.zoneless)': '(0)'\n      }\n    }]\n  }], function () {\n    return [];\n  }, {\n    tuiActiveZoneChange: [{\n      type: Output\n    }],\n    tuiActiveZoneParentSetter: [{\n      type: Input,\n      args: ['tuiActiveZoneParent']\n    }],\n    setZone: []\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiActiveZone };", "map": {"version": 3, "names": ["__decorate", "i0", "inject", "NgZone", "Directive", "Output", "Input", "NgControl", "tuiZoneOptimized", "TUI_ACTIVE_ELEMENT", "tuiInjectElement", "tuiA<PERSON><PERSON><PERSON><PERSON>ove", "tuiPure", "map", "startWith", "distinctUntilChanged", "skip", "tap", "share", "TuiActiveZone", "constructor", "control", "self", "optional", "active$", "zone", "el", "tuiActiveZoneParent", "subActiveZones", "directParentActiveZone", "skipSelf", "tuiActiveZoneChange", "pipe", "element", "contains", "active", "valueAccessor", "onTouched", "addSubActiveZone", "tuiActiveZoneParentSetter", "setZone", "ngOnDestroy", "removeSubActiveZone", "node", "some", "item", "index", "array", "indexOf", "activeZone", "ɵfac", "TuiActiveZone_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostBindings", "TuiActiveZone_HostBindings", "rf", "ctx", "ɵɵlistener", "TuiActiveZone_mousedown_zoneless_HostBindingHandler", "ɵɵresolveDocument", "inputs", "ɵɵInputFlags", "None", "outputs", "exportAs", "standalone", "prototype", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-active-zone.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { inject, NgZone, Directive, Output, Input } from '@angular/core';\nimport { NgControl } from '@angular/forms';\nimport { tuiZoneOptimized } from '@taiga-ui/cdk/observables';\nimport { TUI_ACTIVE_ELEMENT } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement, tuiArrayRemove, tuiPure } from '@taiga-ui/cdk/utils';\nimport { map, startWith, distinctUntilChanged, skip, tap, share } from 'rxjs';\n\nclass TuiActiveZone {\n    constructor() {\n        // TODO: Should we remove in v5? It's no longer used in Taiga UI\n        this.control = inject(NgControl, { self: true, optional: true });\n        this.active$ = inject(TUI_ACTIVE_ELEMENT);\n        this.zone = inject(NgZone);\n        this.el = tuiInjectElement();\n        this.tuiActiveZoneParent = null;\n        this.subActiveZones = [];\n        this.directParentActiveZone = inject(TuiActiveZone, {\n            skipSelf: true,\n            optional: true,\n        });\n        this.tuiActiveZoneChange = this.active$.pipe(map((element) => !!element && this.contains(element)), startWith(false), distinctUntilChanged(), skip(1), tap((active) => {\n            if (!active && typeof this.control?.valueAccessor.onTouched === 'function') {\n                this.control.valueAccessor.onTouched();\n            }\n        }), tuiZoneOptimized(this.zone), share());\n        this.directParentActiveZone?.addSubActiveZone(this);\n    }\n    set tuiActiveZoneParentSetter(zone) {\n        this.setZone(zone);\n    }\n    ngOnDestroy() {\n        this.directParentActiveZone?.removeSubActiveZone(this);\n        this.tuiActiveZoneParent?.removeSubActiveZone(this);\n    }\n    contains(node) {\n        return (this.el.contains(node) ||\n            this.subActiveZones.some((item, index, array) => array.indexOf(item) === index && item.contains(node)));\n    }\n    setZone(zone) {\n        this.tuiActiveZoneParent?.removeSubActiveZone(this);\n        zone?.addSubActiveZone(this);\n        this.tuiActiveZoneParent = zone;\n    }\n    addSubActiveZone(activeZone) {\n        this.subActiveZones = [...this.subActiveZones, activeZone];\n    }\n    removeSubActiveZone(activeZone) {\n        this.subActiveZones = tuiArrayRemove(this.subActiveZones, this.subActiveZones.indexOf(activeZone));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiActiveZone, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiActiveZone, isStandalone: true, selector: \"[tuiActiveZone]:not(ng-container), [tuiActiveZoneChange]:not(ng-container), [tuiActiveZoneParent]:not(ng-container)\", inputs: { tuiActiveZoneParentSetter: [\"tuiActiveZoneParent\", \"tuiActiveZoneParentSetter\"] }, outputs: { tuiActiveZoneChange: \"tuiActiveZoneChange\" }, host: { listeners: { \"document:mousedown.zoneless\": \"(0)\" } }, exportAs: [\"tuiActiveZone\"], ngImport: i0 }); }\n}\n__decorate([\n    tuiPure\n], TuiActiveZone.prototype, \"setZone\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiActiveZone, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiActiveZone]:not(ng-container), [tuiActiveZoneChange]:not(ng-container), [tuiActiveZoneParent]:not(ng-container)',\n                    exportAs: 'tuiActiveZone',\n                    host: {\n                        '(document:mousedown.zoneless)': '(0)',\n                    },\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { tuiActiveZoneChange: [{\n                type: Output\n            }], tuiActiveZoneParentSetter: [{\n                type: Input,\n                args: ['tuiActiveZoneParent']\n            }], setZone: [] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiActiveZone };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,QAAQ,eAAe;AACxE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,gBAAgB,EAAEC,cAAc,EAAEC,OAAO,QAAQ,qBAAqB;AAC/E,SAASC,GAAG,EAAEC,SAAS,EAAEC,oBAAoB,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,QAAQ,MAAM;AAE7E,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,OAAO,GAAGnB,MAAM,CAACK,SAAS,EAAE;MAAEe,IAAI,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChE,IAAI,CAACC,OAAO,GAAGtB,MAAM,CAACO,kBAAkB,CAAC;IACzC,IAAI,CAACgB,IAAI,GAAGvB,MAAM,CAACC,MAAM,CAAC;IAC1B,IAAI,CAACuB,EAAE,GAAGhB,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACiB,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,sBAAsB,GAAG3B,MAAM,CAACiB,aAAa,EAAE;MAChDW,QAAQ,EAAE,IAAI;MACdP,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,CAACQ,mBAAmB,GAAG,IAAI,CAACP,OAAO,CAACQ,IAAI,CAACnB,GAAG,CAAEoB,OAAO,IAAK,CAAC,CAACA,OAAO,IAAI,IAAI,CAACC,QAAQ,CAACD,OAAO,CAAC,CAAC,EAAEnB,SAAS,CAAC,KAAK,CAAC,EAAEC,oBAAoB,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAEkB,MAAM,IAAK;MACnK,IAAI,CAACA,MAAM,IAAI,OAAO,IAAI,CAACd,OAAO,EAAEe,aAAa,CAACC,SAAS,KAAK,UAAU,EAAE;QACxE,IAAI,CAAChB,OAAO,CAACe,aAAa,CAACC,SAAS,CAAC,CAAC;MAC1C;IACJ,CAAC,CAAC,EAAE7B,gBAAgB,CAAC,IAAI,CAACiB,IAAI,CAAC,EAAEP,KAAK,CAAC,CAAC,CAAC;IACzC,IAAI,CAACW,sBAAsB,EAAES,gBAAgB,CAAC,IAAI,CAAC;EACvD;EACA,IAAIC,yBAAyBA,CAACd,IAAI,EAAE;IAChC,IAAI,CAACe,OAAO,CAACf,IAAI,CAAC;EACtB;EACAgB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACZ,sBAAsB,EAAEa,mBAAmB,CAAC,IAAI,CAAC;IACtD,IAAI,CAACf,mBAAmB,EAAEe,mBAAmB,CAAC,IAAI,CAAC;EACvD;EACAR,QAAQA,CAACS,IAAI,EAAE;IACX,OAAQ,IAAI,CAACjB,EAAE,CAACQ,QAAQ,CAACS,IAAI,CAAC,IAC1B,IAAI,CAACf,cAAc,CAACgB,IAAI,CAAC,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,KAAKA,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,KAAKC,KAAK,IAAID,IAAI,CAACX,QAAQ,CAACS,IAAI,CAAC,CAAC;EAC9G;EACAH,OAAOA,CAACf,IAAI,EAAE;IACV,IAAI,CAACE,mBAAmB,EAAEe,mBAAmB,CAAC,IAAI,CAAC;IACnDjB,IAAI,EAAEa,gBAAgB,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACX,mBAAmB,GAAGF,IAAI;EACnC;EACAa,gBAAgBA,CAACW,UAAU,EAAE;IACzB,IAAI,CAACrB,cAAc,GAAG,CAAC,GAAG,IAAI,CAACA,cAAc,EAAEqB,UAAU,CAAC;EAC9D;EACAP,mBAAmBA,CAACO,UAAU,EAAE;IAC5B,IAAI,CAACrB,cAAc,GAAGjB,cAAc,CAAC,IAAI,CAACiB,cAAc,EAAE,IAAI,CAACA,cAAc,CAACoB,OAAO,CAACC,UAAU,CAAC,CAAC;EACtG;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFjC,aAAa;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACkC,IAAI,kBAD+EpD,EAAE,CAAAqD,iBAAA;MAAAC,IAAA,EACJpC,aAAa;MAAAqC,SAAA;MAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADX1D,EAAE,CAAA4D,UAAA,gCAAAC,oDAAA;YAAA,OACH,CAAC;UAAA,UADA7D,EAAE,CAAA8D,iBACQ,CAAC;QAAA;MAAA;MAAAC,MAAA;QAAAzB,yBAAA,GADXtC,EAAE,CAAAgE,YAAA,CAAAC,IAAA;MAAA;MAAAC,OAAA;QAAApC,mBAAA;MAAA;MAAAqC,QAAA;MAAAC,UAAA;IAAA,EACia;EAAE;AAC1gB;AACArE,UAAU,CAAC,CACPY,OAAO,CACV,EAAEO,aAAa,CAACmD,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC;AAC5C;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KANqGtE,EAAE,CAAAuE,iBAAA,CAMXrD,aAAa,EAAc,CAAC;IAC5GoC,IAAI,EAAEnD,SAAS;IACfqE,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE,IAAI;MAChBK,QAAQ,EAAE,qHAAqH;MAC/HN,QAAQ,EAAE,eAAe;MACzBO,IAAI,EAAE;QACF,+BAA+B,EAAE;MACrC;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAE5C,mBAAmB,EAAE,CAAC;MAChFwB,IAAI,EAAElD;IACV,CAAC,CAAC;IAAEkC,yBAAyB,EAAE,CAAC;MAC5BgB,IAAI,EAAEjD,KAAK;MACXmE,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEjC,OAAO,EAAE;EAAG,CAAC;AAAA;;AAE7B;AACA;AACA;;AAEA,SAASrB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}