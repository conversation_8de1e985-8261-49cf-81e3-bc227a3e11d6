{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_BUTTON_OPTIONS } from '@taiga-ui/core/components/button';\nimport { TuiLoader } from '@taiga-ui/core/components/loader';\nimport { tuiSizeBigger } from '@taiga-ui/core/utils/miscellaneous';\nconst _c0 = [\"tuiButton\", \"\", \"loading\", \"\"];\nconst _c1 = [\"*\"];\nclass TuiButtonLoading {\n  constructor() {\n    this.options = inject(TUI_BUTTON_OPTIONS);\n    this.size = this.options.size;\n    this.loading = false;\n  }\n  get loaderSize() {\n    return tuiSizeBigger(this.size) ? 'm' : 's';\n  }\n  get label() {\n    return tuiIsString(this.loading) ? this.loading : '';\n  }\n  onClick(event) {\n    if (this.loading) {\n      event.stopPropagation();\n    }\n  }\n  static {\n    this.ɵfac = function TuiButtonLoading_Factory(t) {\n      return new (t || TuiButtonLoading)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiButtonLoading,\n      selectors: [[\"\", \"tuiButton\", \"\", \"loading\", \"\"], [\"\", \"tuiIconButton\", \"\", \"loading\", \"\"]],\n      hostVars: 3,\n      hostBindings: function TuiButtonLoading_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click.capture\", function TuiButtonLoading_click_capture_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-disabled\", ctx.loading);\n          i0.ɵɵclassProp(\"_loading\", ctx.loading);\n        }\n      },\n      inputs: {\n        size: \"size\",\n        loading: \"loading\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 2,\n      vars: 4,\n      consts: [[\"aria-live\", \"polite\", \"role\", \"status\", 1, \"t-loader\", 3, \"inheritColor\", \"showLoader\", \"size\", \"textContent\"]],\n      template: function TuiButtonLoading_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵelement(1, \"tui-loader\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"inheritColor\", true)(\"showLoader\", !!ctx.loading)(\"size\", ctx.loaderSize)(\"textContent\", ctx.label);\n        }\n      },\n      dependencies: [TuiLoader],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiButtonLoading, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: '[tuiButton][loading],[tuiIconButton][loading]',\n      imports: [TuiLoader],\n      template: `\n        <ng-content />\n        <tui-loader\n            aria-live=\"polite\"\n            role=\"status\"\n            class=\"t-loader\"\n            [inheritColor]=\"true\"\n            [showLoader]=\"!!loading\"\n            [size]=\"loaderSize\"\n            [textContent]=\"label\"\n        />\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[attr.aria-disabled]': 'loading',\n        '[class._loading]': 'loading',\n        '(click.capture)': 'onClick($event)'\n      }\n    }]\n  }], null, {\n    size: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiButtonLoading };", "map": {"version": 3, "names": ["i0", "inject", "Component", "ChangeDetectionStrategy", "Input", "tuiIsString", "TUI_BUTTON_OPTIONS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tui<PERSON>izeBigger", "_c0", "_c1", "TuiButtonLoading", "constructor", "options", "size", "loading", "loaderSize", "label", "onClick", "event", "stopPropagation", "ɵfac", "TuiButtonLoading_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "TuiButtonLoading_HostBindings", "rf", "ctx", "ɵɵlistener", "TuiButtonLoading_click_capture_HostBindingHandler", "$event", "ɵɵattribute", "ɵɵclassProp", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "TuiButtonLoading_Template", "ɵɵprojectionDef", "ɵɵprojection", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "dependencies", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-button-loading.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_BUTTON_OPTIONS } from '@taiga-ui/core/components/button';\nimport { TuiLoader } from '@taiga-ui/core/components/loader';\nimport { tuiSizeBigger } from '@taiga-ui/core/utils/miscellaneous';\n\nclass TuiButtonLoading {\n    constructor() {\n        this.options = inject(TUI_BUTTON_OPTIONS);\n        this.size = this.options.size;\n        this.loading = false;\n    }\n    get loaderSize() {\n        return tuiSizeBigger(this.size) ? 'm' : 's';\n    }\n    get label() {\n        return tuiIsString(this.loading) ? this.loading : '';\n    }\n    onClick(event) {\n        if (this.loading) {\n            event.stopPropagation();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiButtonLoading, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiButtonLoading, isStandalone: true, selector: \"[tuiButton][loading],[tuiIconButton][loading]\", inputs: { size: \"size\", loading: \"loading\" }, host: { listeners: { \"click.capture\": \"onClick($event)\" }, properties: { \"attr.aria-disabled\": \"loading\", \"class._loading\": \"loading\" } }, ngImport: i0, template: `\n        <ng-content />\n        <tui-loader\n            aria-live=\"polite\"\n            role=\"status\"\n            class=\"t-loader\"\n            [inheritColor]=\"true\"\n            [showLoader]=\"!!loading\"\n            [size]=\"loaderSize\"\n            [textContent]=\"label\"\n        />\n    `, isInline: true, dependencies: [{ kind: \"component\", type: TuiLoader, selector: \"tui-loader\", inputs: [\"size\", \"inheritColor\", \"overlay\", \"textContent\", \"showLoader\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiButtonLoading, decorators: [{\n            type: Component,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiButton][loading],[tuiIconButton][loading]',\n                    imports: [TuiLoader],\n                    template: `\n        <ng-content />\n        <tui-loader\n            aria-live=\"polite\"\n            role=\"status\"\n            class=\"t-loader\"\n            [inheritColor]=\"true\"\n            [showLoader]=\"!!loading\"\n            [size]=\"loaderSize\"\n            [textContent]=\"label\"\n        />\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    host: {\n                        '[attr.aria-disabled]': 'loading',\n                        '[class._loading]': 'loading',\n                        '(click.capture)': 'onClick($event)',\n                    },\n                }]\n        }], propDecorators: { size: [{\n                type: Input\n            }], loading: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiButtonLoading };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,QAAQ,eAAe;AACjF,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,aAAa,QAAQ,oCAAoC;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAEnE,MAAMC,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGZ,MAAM,CAACK,kBAAkB,CAAC;IACzC,IAAI,CAACQ,IAAI,GAAG,IAAI,CAACD,OAAO,CAACC,IAAI;IAC7B,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAOR,aAAa,CAAC,IAAI,CAACM,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;EAC/C;EACA,IAAIG,KAAKA,CAAA,EAAG;IACR,OAAOZ,WAAW,CAAC,IAAI,CAACU,OAAO,CAAC,GAAG,IAAI,CAACA,OAAO,GAAG,EAAE;EACxD;EACAG,OAAOA,CAACC,KAAK,EAAE;IACX,IAAI,IAAI,CAACJ,OAAO,EAAE;MACdI,KAAK,CAACC,eAAe,CAAC,CAAC;IAC3B;EACJ;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFZ,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACa,IAAI,kBAD+ExB,EAAE,CAAAyB,iBAAA;MAAAC,IAAA,EACJf,gBAAgB;MAAAgB,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADd/B,EAAE,CAAAiC,UAAA,2BAAAC,kDAAAC,MAAA;YAAA,OACJH,GAAA,CAAAd,OAAA,CAAAiB,MAAc,CAAC;UAAA,EAAC;QAAA;QAAA,IAAAJ,EAAA;UADd/B,EAAE,CAAAoC,WAAA,kBAAAJ,GAAA,CAAAjB,OAAA;UAAFf,EAAE,CAAAqC,WAAA,aAAAL,GAAA,CAAAjB,OACW,CAAC;QAAA;MAAA;MAAAuB,MAAA;QAAAxB,IAAA;QAAAC,OAAA;MAAA;MAAAwB,UAAA;MAAAC,QAAA,GADdxC,EAAE,CAAAyC,mBAAA;MAAAC,KAAA,EAAAjC,GAAA;MAAAkC,kBAAA,EAAAjC,GAAA;MAAAkC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/B,EAAE,CAAAiD,eAAA;UAAFjD,EAAE,CAAAkD,YAAA,EAElF,CAAC;UAF+ElD,EAAE,CAAAmD,SAAA,mBAW9F,CAAC;QAAA;QAAA,IAAApB,EAAA;UAX2F/B,EAAE,CAAAoD,SAAA,CAOvE,CAAC;UAPoEpD,EAAE,CAAAqD,UAAA,qBAOvE,CAAC,iBAAArB,GAAA,CAAAjB,OACE,CAAC,SAAAiB,GAAA,CAAAhB,UACN,CAAC,gBAAAgB,GAAA,CAAAf,KACC,CAAC;QAAA;MAAA;MAAAqC,YAAA,GAEgC/C,SAAS;MAAAgD,aAAA;MAAAC,eAAA;IAAA,EAA4J;EAAE;AACxO;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAdqGzD,EAAE,CAAA0D,iBAAA,CAcX/C,gBAAgB,EAAc,CAAC;IAC/Ge,IAAI,EAAExB,SAAS;IACfyD,IAAI,EAAE,CAAC;MACCpB,UAAU,EAAE,IAAI;MAChBqB,QAAQ,EAAE,+CAA+C;MACzDC,OAAO,EAAE,CAACtD,SAAS,CAAC;MACpBwC,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MACeS,eAAe,EAAErD,uBAAuB,CAAC2D,MAAM;MAC/CC,IAAI,EAAE;QACF,sBAAsB,EAAE,SAAS;QACjC,kBAAkB,EAAE,SAAS;QAC7B,iBAAiB,EAAE;MACvB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEjD,IAAI,EAAE,CAAC;MACrBY,IAAI,EAAEtB;IACV,CAAC,CAAC;IAAEW,OAAO,EAAE,CAAC;MACVW,IAAI,EAAEtB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}