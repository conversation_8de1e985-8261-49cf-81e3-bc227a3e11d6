{"ast": null, "code": "export * from '@taiga-ui/kit/components';\nexport * from '@taiga-ui/kit/directives';\nexport * from '@taiga-ui/kit/pipes';\nexport * from '@taiga-ui/kit/tokens';\nexport * from '@taiga-ui/kit/utils';\n\n/**\n * Generated bundle index. Do not edit.\n */", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit.mjs"], "sourcesContent": ["export * from '@taiga-ui/kit/components';\nexport * from '@taiga-ui/kit/directives';\nexport * from '@taiga-ui/kit/pipes';\nexport * from '@taiga-ui/kit/tokens';\nexport * from '@taiga-ui/kit/utils';\n\n/**\n * Generated bundle index. Do not edit.\n */\n"], "mappings": "AAAA,cAAc,0BAA0B;AACxC,cAAc,0BAA0B;AACxC,cAAc,qBAAqB;AACnC,cAAc,sBAAsB;AACpC,cAAc,qBAAqB;;AAEnC;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}