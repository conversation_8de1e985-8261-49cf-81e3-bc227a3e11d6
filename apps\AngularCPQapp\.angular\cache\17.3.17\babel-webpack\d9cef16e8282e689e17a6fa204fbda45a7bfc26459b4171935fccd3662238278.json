{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Pi<PERSON> } from '@angular/core';\nclass TuiStringifyContentPipe {\n  transform(stringify) {\n    return ({\n      $implicit\n    }) => stringify($implicit);\n  }\n  static {\n    this.ɵfac = function TuiStringifyContentPipe_Factory(t) {\n      return new (t || TuiStringifyContentPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiStringifyContent\",\n      type: TuiStringifyContentPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiStringifyContentPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiStringifyContent'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiStringifyContentPipe };", "map": {"version": 3, "names": ["i0", "<PERSON><PERSON>", "TuiStringifyContentPipe", "transform", "stringify", "$implicit", "ɵfac", "TuiStringifyContentPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-pipes-stringify-content.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Pi<PERSON> } from '@angular/core';\n\nclass TuiStringifyContentPipe {\n    transform(stringify) {\n        return ({ $implicit }) => stringify($implicit);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStringifyContentPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStringifyContentPipe, isStandalone: true, name: \"tuiStringifyContent\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStringifyContentPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiStringifyContent',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiStringifyContentPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,IAAI,QAAQ,eAAe;AAEpC,MAAMC,uBAAuB,CAAC;EAC1BC,SAASA,CAACC,SAAS,EAAE;IACjB,OAAO,CAAC;MAAEC;IAAU,CAAC,KAAKD,SAAS,CAACC,SAAS,CAAC;EAClD;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,gCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFN,uBAAuB;IAAA,CAA8C;EAAE;EAClL;IAAS,IAAI,CAACO,KAAK,kBAD8ET,EAAE,CAAAU,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMV,uBAAuB;MAAAW,IAAA;MAAAC,UAAA;IAAA,EAAoD;EAAE;AAC1L;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGf,EAAE,CAAAgB,iBAAA,CAGXd,uBAAuB,EAAc,CAAC;IACtHU,IAAI,EAAEX,IAAI;IACVgB,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAST,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}