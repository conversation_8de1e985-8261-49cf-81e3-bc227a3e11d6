{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Pipe } from '@angular/core';\nimport { tuiStringHashToHsl } from '@taiga-ui/core/utils';\nclass TuiAutoColorPipe {\n  transform(text) {\n    return tuiStringHashToHsl(text);\n  }\n  static {\n    this.ɵfac = function TuiAutoColorPipe_Factory(t) {\n      return new (t || TuiAutoColorPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiAutoColor\",\n      type: TuiAutoColorPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAutoColorPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiAutoColor'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiAutoColorPipe };", "map": {"version": 3, "names": ["i0", "<PERSON><PERSON>", "tuiStringHashToHsl", "TuiAutoColorPipe", "transform", "text", "ɵfac", "TuiAutoColorPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-pipes-auto-color.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Pipe } from '@angular/core';\nimport { tuiStringHashToHsl } from '@taiga-ui/core/utils';\n\nclass TuiAutoColorPipe {\n    transform(text) {\n        return tuiStringHashToHsl(text);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAutoColorPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAutoColorPipe, isStandalone: true, name: \"tuiAutoColor\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAutoColorPipe, decorators: [{\n            type: <PERSON><PERSON>,\n            args: [{\n                    standalone: true,\n                    name: 'tuiAutoColor',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiAutoColorPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,IAAI,QAAQ,eAAe;AACpC,SAASC,kBAAkB,QAAQ,sBAAsB;AAEzD,MAAMC,gBAAgB,CAAC;EACnBC,SAASA,CAACC,IAAI,EAAE;IACZ,OAAOH,kBAAkB,CAACG,IAAI,CAAC;EACnC;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFL,gBAAgB;IAAA,CAA8C;EAAE;EAC3K;IAAS,IAAI,CAACM,KAAK,kBAD8ET,EAAE,CAAAU,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMT,gBAAgB;MAAAU,IAAA;MAAAC,UAAA;IAAA,EAA6C;EAAE;AAC5K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGf,EAAE,CAAAgB,iBAAA,CAGXb,gBAAgB,EAAc,CAAC;IAC/GS,IAAI,EAAEX,IAAI;IACVgB,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASR,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}