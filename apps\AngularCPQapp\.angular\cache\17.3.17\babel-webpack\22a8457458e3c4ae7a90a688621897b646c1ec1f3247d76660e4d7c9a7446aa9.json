{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Injectable, Directive, Output } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { Observable, merge, pairwise, filter, map } from 'rxjs';\nimport { tuiCreateToken } from '@taiga-ui/cdk/utils';\nconst TUI_SWIPE_OPTIONS = tuiCreateToken({\n  timeout: 500,\n  threshold: 30\n});\nclass TuiSwipeService extends Observable {\n  constructor() {\n    const doc = inject(DOCUMENT);\n    const el = tuiInjectElement();\n    const {\n      timeout,\n      threshold\n    } = inject(TUI_SWIPE_OPTIONS);\n    super(subscriber => merge(tuiTypedFromEvent(el, 'touchstart', {\n      passive: true\n    }), tuiTypedFromEvent(doc, 'touchend')).pipe(pairwise(), filter(([first, second]) => !!first.touches.length && first.touches[0]?.identifier === second.changedTouches[0]?.identifier), map(([start, end]) => {\n      const startX = start.touches[0]?.clientX ?? 0;\n      const startY = start.touches[0]?.clientY ?? 0;\n      const endX = end.changedTouches[0]?.clientX ?? 0;\n      const endY = end.changedTouches[0]?.clientY ?? 0;\n      const distanceX = startX - endX;\n      const distanceY = startY - endY;\n      const duration = end.timeStamp - start.timeStamp;\n      if ((Math.abs(distanceX) > threshold || Math.abs(distanceY) > threshold) && duration < timeout) {\n        return {\n          direction: tuiGetSwipeDirection(distanceX, distanceY),\n          events: [start, end]\n        };\n      }\n      return null;\n    }), filter(tuiIsPresent)).subscribe(subscriber));\n  }\n  static {\n    this.ɵfac = function TuiSwipeService_Factory(t) {\n      return new (t || TuiSwipeService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiSwipeService,\n      factory: TuiSwipeService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSwipeService, [{\n    type: Injectable\n  }], function () {\n    return [];\n  }, null);\n})();\nfunction tuiGetSwipeDirection(deltaX, deltaY) {\n  if (Math.abs(deltaY) > Math.abs(deltaX)) {\n    return deltaY > 0 ? 'top' : 'bottom';\n  }\n  return deltaX > 0 ? 'left' : 'right';\n}\nclass TuiSwipe {\n  constructor() {\n    this.tuiSwipe = inject(TuiSwipeService);\n  }\n  static {\n    this.ɵfac = function TuiSwipe_Factory(t) {\n      return new (t || TuiSwipe)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiSwipe,\n      selectors: [[\"\", \"tuiSwipe\", \"\"]],\n      outputs: {\n        tuiSwipe: \"tuiSwipe\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiSwipeService])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSwipe, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiSwipe]',\n      providers: [TuiSwipeService]\n    }]\n  }], null, {\n    tuiSwipe: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_SWIPE_OPTIONS, TuiSwipe, TuiSwipeService };", "map": {"version": 3, "names": ["i0", "inject", "Injectable", "Directive", "Output", "DOCUMENT", "tuiTypedFromEvent", "tuiInjectElement", "tuiIsPresent", "Observable", "merge", "pairwise", "filter", "map", "tuiCreateToken", "TUI_SWIPE_OPTIONS", "timeout", "threshold", "TuiSwipeService", "constructor", "doc", "el", "subscriber", "passive", "pipe", "first", "second", "touches", "length", "identifier", "changedTouches", "start", "end", "startX", "clientX", "startY", "clientY", "endX", "endY", "distanceX", "distanceY", "duration", "timeStamp", "Math", "abs", "direction", "tuiGetSwipeDirection", "events", "subscribe", "ɵfac", "TuiSwipeService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "deltaX", "deltaY", "TuiSwipe", "tui<PERSON><PERSON><PERSON>", "TuiSwipe_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "outputs", "standalone", "features", "ɵɵProvidersFeature", "args", "selector", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-swipe.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Injectable, Directive, Output } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { Observable, merge, pairwise, filter, map } from 'rxjs';\nimport { tuiCreateToken } from '@taiga-ui/cdk/utils';\n\nconst TUI_SWIPE_OPTIONS = tuiCreateToken({\n    timeout: 500,\n    threshold: 30,\n});\n\nclass TuiSwipeService extends Observable {\n    constructor() {\n        const doc = inject(DOCUMENT);\n        const el = tuiInjectElement();\n        const { timeout, threshold } = inject(TUI_SWIPE_OPTIONS);\n        super((subscriber) => merge(tuiTypedFromEvent(el, 'touchstart', { passive: true }), tuiTypedFromEvent(doc, 'touchend'))\n            .pipe(pairwise(), filter(([first, second]) => !!first.touches.length &&\n            first.touches[0]?.identifier ===\n                second.changedTouches[0]?.identifier), map(([start, end]) => {\n            const startX = start.touches[0]?.clientX ?? 0;\n            const startY = start.touches[0]?.clientY ?? 0;\n            const endX = end.changedTouches[0]?.clientX ?? 0;\n            const endY = end.changedTouches[0]?.clientY ?? 0;\n            const distanceX = startX - endX;\n            const distanceY = startY - endY;\n            const duration = end.timeStamp - start.timeStamp;\n            if ((Math.abs(distanceX) > threshold ||\n                Math.abs(distanceY) > threshold) &&\n                duration < timeout) {\n                return {\n                    direction: tuiGetSwipeDirection(distanceX, distanceY),\n                    events: [start, end],\n                };\n            }\n            return null;\n        }), filter(tuiIsPresent))\n            .subscribe(subscriber));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSwipeService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSwipeService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSwipeService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return []; } });\nfunction tuiGetSwipeDirection(deltaX, deltaY) {\n    if (Math.abs(deltaY) > Math.abs(deltaX)) {\n        return deltaY > 0 ? 'top' : 'bottom';\n    }\n    return deltaX > 0 ? 'left' : 'right';\n}\n\nclass TuiSwipe {\n    constructor() {\n        this.tuiSwipe = inject(TuiSwipeService);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSwipe, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSwipe, isStandalone: true, selector: \"[tuiSwipe]\", outputs: { tuiSwipe: \"tuiSwipe\" }, providers: [TuiSwipeService], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSwipe, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiSwipe]',\n                    providers: [TuiSwipeService],\n                }]\n        }], propDecorators: { tuiSwipe: [{\n                type: Output\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_SWIPE_OPTIONS, TuiSwipe, TuiSwipeService };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACrE,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,UAAU,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,GAAG,QAAQ,MAAM;AAC/D,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,MAAMC,iBAAiB,GAAGD,cAAc,CAAC;EACrCE,OAAO,EAAE,GAAG;EACZC,SAAS,EAAE;AACf,CAAC,CAAC;AAEF,MAAMC,eAAe,SAAST,UAAU,CAAC;EACrCU,WAAWA,CAAA,EAAG;IACV,MAAMC,GAAG,GAAGnB,MAAM,CAACI,QAAQ,CAAC;IAC5B,MAAMgB,EAAE,GAAGd,gBAAgB,CAAC,CAAC;IAC7B,MAAM;MAAES,OAAO;MAAEC;IAAU,CAAC,GAAGhB,MAAM,CAACc,iBAAiB,CAAC;IACxD,KAAK,CAAEO,UAAU,IAAKZ,KAAK,CAACJ,iBAAiB,CAACe,EAAE,EAAE,YAAY,EAAE;MAAEE,OAAO,EAAE;IAAK,CAAC,CAAC,EAAEjB,iBAAiB,CAACc,GAAG,EAAE,UAAU,CAAC,CAAC,CAClHI,IAAI,CAACb,QAAQ,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC,CAACa,KAAK,EAAEC,MAAM,CAAC,KAAK,CAAC,CAACD,KAAK,CAACE,OAAO,CAACC,MAAM,IACpEH,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,EAAEE,UAAU,KACxBH,MAAM,CAACI,cAAc,CAAC,CAAC,CAAC,EAAED,UAAU,CAAC,EAAEhB,GAAG,CAAC,CAAC,CAACkB,KAAK,EAAEC,GAAG,CAAC,KAAK;MACjE,MAAMC,MAAM,GAAGF,KAAK,CAACJ,OAAO,CAAC,CAAC,CAAC,EAAEO,OAAO,IAAI,CAAC;MAC7C,MAAMC,MAAM,GAAGJ,KAAK,CAACJ,OAAO,CAAC,CAAC,CAAC,EAAES,OAAO,IAAI,CAAC;MAC7C,MAAMC,IAAI,GAAGL,GAAG,CAACF,cAAc,CAAC,CAAC,CAAC,EAAEI,OAAO,IAAI,CAAC;MAChD,MAAMI,IAAI,GAAGN,GAAG,CAACF,cAAc,CAAC,CAAC,CAAC,EAAEM,OAAO,IAAI,CAAC;MAChD,MAAMG,SAAS,GAAGN,MAAM,GAAGI,IAAI;MAC/B,MAAMG,SAAS,GAAGL,MAAM,GAAGG,IAAI;MAC/B,MAAMG,QAAQ,GAAGT,GAAG,CAACU,SAAS,GAAGX,KAAK,CAACW,SAAS;MAChD,IAAI,CAACC,IAAI,CAACC,GAAG,CAACL,SAAS,CAAC,GAAGtB,SAAS,IAChC0B,IAAI,CAACC,GAAG,CAACJ,SAAS,CAAC,GAAGvB,SAAS,KAC/BwB,QAAQ,GAAGzB,OAAO,EAAE;QACpB,OAAO;UACH6B,SAAS,EAAEC,oBAAoB,CAACP,SAAS,EAAEC,SAAS,CAAC;UACrDO,MAAM,EAAE,CAAChB,KAAK,EAAEC,GAAG;QACvB,CAAC;MACL;MACA,OAAO,IAAI;IACf,CAAC,CAAC,EAAEpB,MAAM,CAACJ,YAAY,CAAC,CAAC,CACpBwC,SAAS,CAAC1B,UAAU,CAAC,CAAC;EAC/B;EACA;IAAS,IAAI,CAAC2B,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFjC,eAAe;IAAA,CAAoD;EAAE;EAChL;IAAS,IAAI,CAACkC,KAAK,kBAD8EpD,EAAE,CAAAqD,kBAAA;MAAAC,KAAA,EACYpC,eAAe;MAAAqC,OAAA,EAAfrC,eAAe,CAAA+B;IAAA,EAAG;EAAE;AACvI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAHqGxD,EAAE,CAAAyD,iBAAA,CAGXvC,eAAe,EAAc,CAAC;IAC9GwC,IAAI,EAAExD;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AACtD,SAAS4C,oBAAoBA,CAACa,MAAM,EAAEC,MAAM,EAAE;EAC1C,IAAIjB,IAAI,CAACC,GAAG,CAACgB,MAAM,CAAC,GAAGjB,IAAI,CAACC,GAAG,CAACe,MAAM,CAAC,EAAE;IACrC,OAAOC,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,QAAQ;EACxC;EACA,OAAOD,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO;AACxC;AAEA,MAAME,QAAQ,CAAC;EACX1C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2C,QAAQ,GAAG7D,MAAM,CAACiB,eAAe,CAAC;EAC3C;EACA;IAAS,IAAI,CAAC+B,IAAI,YAAAc,iBAAAZ,CAAA;MAAA,YAAAA,CAAA,IAAyFU,QAAQ;IAAA,CAAmD;EAAE;EACxK;IAAS,IAAI,CAACG,IAAI,kBAlB+EhE,EAAE,CAAAiE,iBAAA;MAAAP,IAAA,EAkBJG,QAAQ;MAAAK,SAAA;MAAAC,OAAA;QAAAL,QAAA;MAAA;MAAAM,UAAA;MAAAC,QAAA,GAlBNrE,EAAE,CAAAsE,kBAAA,CAkBgG,CAACpD,eAAe,CAAC;IAAA,EAAiB;EAAE;AAC3O;AACA;EAAA,QAAAsC,SAAA,oBAAAA,SAAA,KApBqGxD,EAAE,CAAAyD,iBAAA,CAoBXI,QAAQ,EAAc,CAAC;IACvGH,IAAI,EAAEvD,SAAS;IACfoE,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,YAAY;MACtBC,SAAS,EAAE,CAACvD,eAAe;IAC/B,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE4C,QAAQ,EAAE,CAAC;MACzBJ,IAAI,EAAEtD;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASW,iBAAiB,EAAE8C,QAAQ,EAAE3C,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}