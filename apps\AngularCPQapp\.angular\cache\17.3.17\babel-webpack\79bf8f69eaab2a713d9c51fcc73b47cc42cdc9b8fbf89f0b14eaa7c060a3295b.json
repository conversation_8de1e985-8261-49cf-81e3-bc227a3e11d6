{"ast": null, "code": "function tuiGetGradientData(gradient) {\n  return gradient.slice(0, Math.max(0, gradient.length - 1)).replace('linear-gradient(', '');\n}\nfunction tuiHexToRgb(hex) {\n  const matches = hex.replace('#', '').split('').map((char, _, array) => array.length === 3 ? char + char : char).join('').match(/.{2}/g);\n  return matches ? matches.map(x => Number.parseInt(x, 16)) : [0, 0, 0];\n}\nconst getChunksFromString = (hex, chunkSize) => hex.match(new RegExp(`.{${chunkSize}}`, 'g'));\nconst convertHexUnitTo256 = hexStr => parseInt(hexStr.repeat(2 / hexStr.length), 16);\nconst getAlphaFloat = (a, alpha) => {\n  if (a !== undefined) {\n    return Number((a / 255).toFixed(2));\n  }\n  if (typeof alpha !== 'number' || alpha < 0 || alpha > 1) {\n    return 1;\n  }\n  return alpha;\n};\nfunction tuiHexToRGBA(hex, alpha) {\n  const [r, g, b, a] = tuiParseHex(hex, alpha);\n  return a < 1 ? `rgba(${r}, ${g}, ${b}, ${a})` : `rgb(${r}, ${g}, ${b})`;\n}\nfunction tuiIsValidHex(hex) {\n  return /^#([A-Fa-f0-9]{3,4}){1,2}$/.test(hex);\n}\nfunction tuiParseHex(hex, alpha) {\n  if (!tuiIsValidHex(hex)) {\n    throw new Error('Invalid HEX');\n  }\n  const chunkSize = Math.floor((hex.length - 1) / 3);\n  const hexArr = getChunksFromString(hex.slice(1), chunkSize);\n  const [r = NaN, g = NaN, b = NaN, a] = hexArr?.map(convertHexUnitTo256) ?? [];\n  const floatAlpha = getAlphaFloat(a, alpha);\n  return [r, g, b, floatAlpha];\n}\nfunction hsvToColor(h, s, v, n) {\n  const k = (n + h / 60) % 6;\n  return Math.round(v - v * s * Math.max(Math.min(k, 4 - k, 1), 0));\n}\n/**\n * https://stackoverflow.com/a/54024653/2706426\n */\nfunction tuiHsvToRgb(h, s, v) {\n  return [hsvToColor(h, s, v, 5), hsvToColor(h, s, v, 3), hsvToColor(h, s, v, 1)];\n}\nconst DEFAULT = [0, 0, 0, 1];\nfunction tuiParseColor(color) {\n  const stripped = color.replace('#', '').replace('rgba(', '').replace('rgb(', '').replace(')', '');\n  const array = stripped.split(',').map(item => parseFloat(item));\n  if (array.length === 4) {\n    return array;\n  }\n  if (array.length === 3) {\n    return array.concat(1);\n  }\n  const matches = stripped.match(new RegExp(`(.{${stripped.length / 3}})`, 'g'));\n  if (!matches) {\n    return DEFAULT;\n  }\n  const parsed = matches.map(char => parseInt(stripped.length % 2 ? char + char : char, 16));\n  return [parsed[0] ?? DEFAULT[0], parsed[1] ?? DEFAULT[1], parsed[2] ?? DEFAULT[2], parsed[3] ?? DEFAULT[3]];\n}\n\n//\n// TypeScript parser based on Dean Taylor's answer:\n// https://stackoverflow.com/a/20238168/2706426\n//\n// SETUP CODE\nconst COMMA = String.raw`\\s*,\\s*`; // Allow space around comma.\nconst HEX = '#(?:[a-f0-9]{6}|[a-f0-9]{3})'; // 3 or 6 character form\nconst RGB = String.raw`\\(\\s*(?:\\d{1,3}\\s*,\\s*){2}\\d{1,3}\\s*\\)`; // \"(1, 2, 3)\"\nconst RGBA = String.raw`\\(\\s*(?:\\d{1,3}\\s*,\\s*){2}\\d{1,3}\\s*,\\s*\\d*\\.?\\d+\\)`; // \"(1, 2, 3, 4)\"\nconst VALUE = String.raw`(?:[+-]?\\d*\\.?\\d+)(?:%|[a-z]+)?`; // \".9\", \"-5px\", \"100%\".\nconst KEYWORD = '[_a-z-][_a-z0-9-]*'; // \"red\", \"transparent\", \"border-collapse\".\nconst COLOR = ['(?:', HEX, '|', '(?:rgb|hsl)', RGB, '|', '(?:rgba|hsla)', RGBA, '|', KEYWORD, ')'];\nconst REGEXP_ARRAY = [String.raw`\\s*(`, ...COLOR, ')', String.raw`(?:\\s+`, '(', VALUE, '))?', '(?:', COMMA, String.raw`\\s*)?`];\nfunction getPosition(match, stops) {\n  const fallback = stops === 1 ? '100%' : `${stops}%`;\n  return match?.includes('%') ? match : fallback;\n}\nfunction tuiParseGradient(input) {\n  const stopsRegexp = new RegExp(REGEXP_ARRAY.join(''), 'gi');\n  const stopsString = input.startsWith('to') || /^\\d/.exec(input) ? input.slice(Math.max(0, input.indexOf(',') + 1)).trim() : input;\n  const side = input.startsWith('to') ? input.split(',')[0] : 'to bottom';\n  let stops = [];\n  let matchColorStop = stopsRegexp.exec(stopsString);\n  while (matchColorStop !== null) {\n    stops = stops.concat({\n      color: matchColorStop[1] || '',\n      position: getPosition(matchColorStop[2] || '', stops.length)\n    });\n    matchColorStop = stopsRegexp.exec(stopsString);\n  }\n  stops = stops.filter(({\n    color\n  }) => color.startsWith('#') || color.startsWith('rgb'));\n  return {\n    stops,\n    side\n  };\n}\nfunction tuiToGradient({\n  stops,\n  side\n}) {\n  return `linear-gradient(${side}, ${stops.map(({\n    color,\n    position\n  }) => `rgba(${tuiParseColor(color).join(', ')}) ${position}`).join(', ')})`;\n}\nfunction tuiRgbToHex(r, g, b) {\n  return `#${[r, g, b].map(x => x.toString(16).padStart(2, '0')).join('')}`;\n}\nfunction tuiRgbToHsv(r, g, b) {\n  const v = Math.max(r, g, b);\n  const n = v - Math.min(r, g, b);\n  // eslint-disable-next-line no-nested-ternary\n  const h = n && (v === r ? (g - b) / n : v === g ? 2 + (b - r) / n : 4 + (r - g) / n);\n  return [60 * (h < 0 ? h + 6 : h), v && n / v, v];\n}\nfunction tuiRgbaToHex(color) {\n  if (!tuiIsValidRgba(color)) {\n    throw new Error('Invalid RGBa');\n  }\n  const rgb = /^rgba?\\((\\d+),(\\d+),(\\d+),?([^,\\s)]+)?/i.exec(color.replaceAll(/\\s/g, '')) ?? null;\n  let alpha = (rgb?.[4] ?? '').trim();\n  let hex = rgb ? ((parseInt(rgb?.[1] ?? '', 10) || 0) | 1 << 8).toString(16).slice(1) + ((parseInt(rgb?.[2] ?? '', 10) || 0) | 1 << 8).toString(16).slice(1) + ((parseInt(rgb?.[3] ?? '', 10) || 0) | 1 << 8).toString(16).slice(1) : color;\n  alpha = alpha !== '' ? alpha : 0o1;\n  alpha = (Number(alpha) * 255 | 1 << 8).toString(16).slice(1);\n  hex += alpha;\n  return `#${hex.toUpperCase()}`;\n}\nfunction tuiIsValidRgba(rgba) {\n  const range = String.raw`(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|2[0-5]{2})`;\n  const alpha = String.raw`([01]|0?\\.\\d+)`;\n  return new RegExp(`^(?:rgb\\\\(\\\\s*${range}\\\\s*,\\\\s*${range}\\\\s*,\\\\s*${range}\\\\s*\\\\)|rgba\\\\(\\\\s*${range}\\\\s*,\\\\s*${range}\\\\s*,\\\\s*${range}\\\\s*,\\\\s*${alpha}\\\\s*\\\\))$`).test(rgba);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiGetGradientData, tuiHexToRGBA, tuiHexToRgb, tuiHsvToRgb, tuiIsValidHex, tuiIsValidRgba, tuiParseColor, tuiParseGradient, tuiParseHex, tuiRgbToHex, tuiRgbToHsv, tuiRgbaToHex, tuiToGradient };", "map": {"version": 3, "names": ["tuiGetGradientData", "gradient", "slice", "Math", "max", "length", "replace", "tuiHexToRgb", "hex", "matches", "split", "map", "char", "_", "array", "join", "match", "x", "Number", "parseInt", "getChunksFromString", "chunkSize", "RegExp", "convertHexUnitTo256", "hexStr", "repeat", "getAlphaFloat", "a", "alpha", "undefined", "toFixed", "tuiHexToRGBA", "r", "g", "b", "tuiParseHex", "tuiIsValidHex", "test", "Error", "floor", "hexArr", "NaN", "floatAlpha", "hsvToColor", "h", "s", "v", "n", "k", "round", "min", "tuiHsvToRgb", "DEFAULT", "tuiParseColor", "color", "stripped", "item", "parseFloat", "concat", "parsed", "COMMA", "String", "raw", "HEX", "RGB", "RGBA", "VALUE", "KEYWORD", "COLOR", "REGEXP_ARRAY", "getPosition", "stops", "fallback", "includes", "tuiParseGradient", "input", "stopsRegexp", "stopsString", "startsWith", "exec", "indexOf", "trim", "side", "matchColorStop", "position", "filter", "tui<PERSON>oGradient", "tuiRgbToHex", "toString", "padStart", "tuiRgbToHsv", "tuiRgbaToHex", "tuiIsValidRgba", "rgb", "replaceAll", "toUpperCase", "rgba", "range"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-utils-color.mjs"], "sourcesContent": ["function tuiGetGradientData(gradient) {\n    return gradient\n        .slice(0, Math.max(0, gradient.length - 1))\n        .replace('linear-gradient(', '');\n}\n\nfunction tuiHexToRgb(hex) {\n    const matches = hex\n        .replace('#', '')\n        .split('')\n        .map((char, _, array) => (array.length === 3 ? char + char : char))\n        .join('')\n        .match(/.{2}/g);\n    return matches\n        ? matches.map((x) => Number.parseInt(x, 16))\n        : [0, 0, 0];\n}\n\nconst getChunksFromString = (hex, chunkSize) => hex.match(new RegExp(`.{${chunkSize}}`, 'g'));\nconst convertHexUnitTo256 = (hexStr) => parseInt(hexStr.repeat(2 / hexStr.length), 16);\nconst getAlphaFloat = (a, alpha) => {\n    if (a !== undefined) {\n        return Number((a / 255).toFixed(2));\n    }\n    if (typeof alpha !== 'number' || alpha < 0 || alpha > 1) {\n        return 1;\n    }\n    return alpha;\n};\nfunction tuiHexToRGBA(hex, alpha) {\n    const [r, g, b, a] = tuiParseHex(hex, alpha);\n    return a < 1 ? `rgba(${r}, ${g}, ${b}, ${a})` : `rgb(${r}, ${g}, ${b})`;\n}\nfunction tuiIsValidHex(hex) {\n    return /^#([A-Fa-f0-9]{3,4}){1,2}$/.test(hex);\n}\nfunction tuiParseHex(hex, alpha) {\n    if (!tuiIsValidHex(hex)) {\n        throw new Error('Invalid HEX');\n    }\n    const chunkSize = Math.floor((hex.length - 1) / 3);\n    const hexArr = getChunksFromString(hex.slice(1), chunkSize);\n    const [r = NaN, g = NaN, b = NaN, a] = hexArr?.map(convertHexUnitTo256) ?? [];\n    const floatAlpha = getAlphaFloat(a, alpha);\n    return [r, g, b, floatAlpha];\n}\n\nfunction hsvToColor(h, s, v, n) {\n    const k = (n + h / 60) % 6;\n    return Math.round(v - v * s * Math.max(Math.min(k, 4 - k, 1), 0));\n}\n/**\n * https://stackoverflow.com/a/54024653/2706426\n */\nfunction tuiHsvToRgb(h, s, v) {\n    return [hsvToColor(h, s, v, 5), hsvToColor(h, s, v, 3), hsvToColor(h, s, v, 1)];\n}\n\nconst DEFAULT = [0, 0, 0, 1];\nfunction tuiParseColor(color) {\n    const stripped = color\n        .replace('#', '')\n        .replace('rgba(', '')\n        .replace('rgb(', '')\n        .replace(')', '');\n    const array = stripped.split(',').map((item) => parseFloat(item));\n    if (array.length === 4) {\n        return array;\n    }\n    if (array.length === 3) {\n        return array.concat(1);\n    }\n    const matches = stripped.match(new RegExp(`(.{${stripped.length / 3}})`, 'g'));\n    if (!matches) {\n        return DEFAULT;\n    }\n    const parsed = matches.map((char) => parseInt(stripped.length % 2 ? char + char : char, 16));\n    return [\n        parsed[0] ?? DEFAULT[0],\n        parsed[1] ?? DEFAULT[1],\n        parsed[2] ?? DEFAULT[2],\n        parsed[3] ?? DEFAULT[3],\n    ];\n}\n\n//\n// TypeScript parser based on Dean Taylor's answer:\n// https://stackoverflow.com/a/20238168/2706426\n//\n// SETUP CODE\nconst COMMA = String.raw `\\s*,\\s*`; // Allow space around comma.\nconst HEX = '#(?:[a-f0-9]{6}|[a-f0-9]{3})'; // 3 or 6 character form\nconst RGB = String.raw `\\(\\s*(?:\\d{1,3}\\s*,\\s*){2}\\d{1,3}\\s*\\)`; // \"(1, 2, 3)\"\nconst RGBA = String.raw `\\(\\s*(?:\\d{1,3}\\s*,\\s*){2}\\d{1,3}\\s*,\\s*\\d*\\.?\\d+\\)`; // \"(1, 2, 3, 4)\"\nconst VALUE = String.raw `(?:[+-]?\\d*\\.?\\d+)(?:%|[a-z]+)?`; // \".9\", \"-5px\", \"100%\".\nconst KEYWORD = '[_a-z-][_a-z0-9-]*'; // \"red\", \"transparent\", \"border-collapse\".\nconst COLOR = [\n    '(?:',\n    HEX,\n    '|',\n    '(?:rgb|hsl)',\n    RGB,\n    '|',\n    '(?:rgba|hsla)',\n    RGBA,\n    '|',\n    KEYWORD,\n    ')',\n];\nconst REGEXP_ARRAY = [\n    String.raw `\\s*(`,\n    ...COLOR,\n    ')',\n    String.raw `(?:\\s+`,\n    '(',\n    VALUE,\n    '))?',\n    '(?:',\n    COMMA,\n    String.raw `\\s*)?`,\n];\nfunction getPosition(match, stops) {\n    const fallback = stops === 1 ? '100%' : `${stops}%`;\n    return match?.includes('%') ? match : fallback;\n}\nfunction tuiParseGradient(input) {\n    const stopsRegexp = new RegExp(REGEXP_ARRAY.join(''), 'gi');\n    const stopsString = input.startsWith('to') || /^\\d/.exec(input)\n        ? input.slice(Math.max(0, input.indexOf(',') + 1)).trim()\n        : input;\n    const side = input.startsWith('to')\n        ? input.split(',')[0]\n        : 'to bottom';\n    let stops = [];\n    let matchColorStop = stopsRegexp.exec(stopsString);\n    while (matchColorStop !== null) {\n        stops = stops.concat({\n            color: matchColorStop[1] || '',\n            position: getPosition(matchColorStop[2] || '', stops.length),\n        });\n        matchColorStop = stopsRegexp.exec(stopsString);\n    }\n    stops = stops.filter(({ color }) => color.startsWith('#') || color.startsWith('rgb'));\n    return {\n        stops,\n        side,\n    };\n}\nfunction tuiToGradient({ stops, side }) {\n    return `linear-gradient(${side}, ${stops\n        .map(({ color, position }) => `rgba(${tuiParseColor(color).join(', ')}) ${position}`)\n        .join(', ')})`;\n}\n\nfunction tuiRgbToHex(r, g, b) {\n    return `#${[r, g, b].map((x) => x.toString(16).padStart(2, '0')).join('')}`;\n}\n\nfunction tuiRgbToHsv(r, g, b) {\n    const v = Math.max(r, g, b);\n    const n = v - Math.min(r, g, b);\n    // eslint-disable-next-line no-nested-ternary\n    const h = n && (v === r ? (g - b) / n : v === g ? 2 + (b - r) / n : 4 + (r - g) / n);\n    return [60 * (h < 0 ? h + 6 : h), v && n / v, v];\n}\n\nfunction tuiRgbaToHex(color) {\n    if (!tuiIsValidRgba(color)) {\n        throw new Error('Invalid RGBa');\n    }\n    const rgb = /^rgba?\\((\\d+),(\\d+),(\\d+),?([^,\\s)]+)?/i.exec(color.replaceAll(/\\s/g, '')) ??\n        null;\n    let alpha = ((rgb?.[4] ?? '')).trim();\n    let hex = rgb\n        ? ((parseInt(rgb?.[1] ?? '', 10) || 0) | (1 << 8)).toString(16).slice(1) +\n            ((parseInt(rgb?.[2] ?? '', 10) || 0) | (1 << 8)).toString(16).slice(1) +\n            ((parseInt(rgb?.[3] ?? '', 10) || 0) | (1 << 8)).toString(16).slice(1)\n        : color;\n    alpha = alpha !== '' ? alpha : 0o1;\n    alpha = ((Number(alpha) * 255) | (1 << 8)).toString(16).slice(1);\n    hex += alpha;\n    return `#${hex.toUpperCase()}`;\n}\nfunction tuiIsValidRgba(rgba) {\n    const range = String.raw `(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|2[0-5]{2})`;\n    const alpha = String.raw `([01]|0?\\.\\d+)`;\n    return new RegExp(`^(?:rgb\\\\(\\\\s*${range}\\\\s*,\\\\s*${range}\\\\s*,\\\\s*${range}\\\\s*\\\\)|rgba\\\\(\\\\s*${range}\\\\s*,\\\\s*${range}\\\\s*,\\\\s*${range}\\\\s*,\\\\s*${alpha}\\\\s*\\\\))$`).test(rgba);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiGetGradientData, tuiHexToRGBA, tuiHexToRgb, tuiHsvToRgb, tuiIsValidHex, tuiIsValidRgba, tuiParseColor, tuiParseGradient, tuiParseHex, tuiRgbToHex, tuiRgbToHsv, tuiRgbaToHex, tuiToGradient };\n"], "mappings": "AAAA,SAASA,kBAAkBA,CAACC,QAAQ,EAAE;EAClC,OAAOA,QAAQ,CACVC,KAAK,CAAC,CAAC,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC,CAAC,CAC1CC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;AACxC;AAEA,SAASC,WAAWA,CAACC,GAAG,EAAE;EACtB,MAAMC,OAAO,GAAGD,GAAG,CACdF,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAChBI,KAAK,CAAC,EAAE,CAAC,CACTC,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,EAAEC,KAAK,KAAMA,KAAK,CAACT,MAAM,KAAK,CAAC,GAAGO,IAAI,GAAGA,IAAI,GAAGA,IAAK,CAAC,CAClEG,IAAI,CAAC,EAAE,CAAC,CACRC,KAAK,CAAC,OAAO,CAAC;EACnB,OAAOP,OAAO,GACRA,OAAO,CAACE,GAAG,CAAEM,CAAC,IAAKC,MAAM,CAACC,QAAQ,CAACF,CAAC,EAAE,EAAE,CAAC,CAAC,GAC1C,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnB;AAEA,MAAMG,mBAAmB,GAAGA,CAACZ,GAAG,EAAEa,SAAS,KAAKb,GAAG,CAACQ,KAAK,CAAC,IAAIM,MAAM,CAAC,KAAKD,SAAS,GAAG,EAAE,GAAG,CAAC,CAAC;AAC7F,MAAME,mBAAmB,GAAIC,MAAM,IAAKL,QAAQ,CAACK,MAAM,CAACC,MAAM,CAAC,CAAC,GAAGD,MAAM,CAACnB,MAAM,CAAC,EAAE,EAAE,CAAC;AACtF,MAAMqB,aAAa,GAAGA,CAACC,CAAC,EAAEC,KAAK,KAAK;EAChC,IAAID,CAAC,KAAKE,SAAS,EAAE;IACjB,OAAOX,MAAM,CAAC,CAACS,CAAC,GAAG,GAAG,EAAEG,OAAO,CAAC,CAAC,CAAC,CAAC;EACvC;EACA,IAAI,OAAOF,KAAK,KAAK,QAAQ,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;IACrD,OAAO,CAAC;EACZ;EACA,OAAOA,KAAK;AAChB,CAAC;AACD,SAASG,YAAYA,CAACvB,GAAG,EAAEoB,KAAK,EAAE;EAC9B,MAAM,CAACI,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEP,CAAC,CAAC,GAAGQ,WAAW,CAAC3B,GAAG,EAAEoB,KAAK,CAAC;EAC5C,OAAOD,CAAC,GAAG,CAAC,GAAG,QAAQK,CAAC,KAAKC,CAAC,KAAKC,CAAC,KAAKP,CAAC,GAAG,GAAG,OAAOK,CAAC,KAAKC,CAAC,KAAKC,CAAC,GAAG;AAC3E;AACA,SAASE,aAAaA,CAAC5B,GAAG,EAAE;EACxB,OAAO,4BAA4B,CAAC6B,IAAI,CAAC7B,GAAG,CAAC;AACjD;AACA,SAAS2B,WAAWA,CAAC3B,GAAG,EAAEoB,KAAK,EAAE;EAC7B,IAAI,CAACQ,aAAa,CAAC5B,GAAG,CAAC,EAAE;IACrB,MAAM,IAAI8B,KAAK,CAAC,aAAa,CAAC;EAClC;EACA,MAAMjB,SAAS,GAAGlB,IAAI,CAACoC,KAAK,CAAC,CAAC/B,GAAG,CAACH,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;EAClD,MAAMmC,MAAM,GAAGpB,mBAAmB,CAACZ,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,EAAEmB,SAAS,CAAC;EAC3D,MAAM,CAACW,CAAC,GAAGS,GAAG,EAAER,CAAC,GAAGQ,GAAG,EAAEP,CAAC,GAAGO,GAAG,EAAEd,CAAC,CAAC,GAAGa,MAAM,EAAE7B,GAAG,CAACY,mBAAmB,CAAC,IAAI,EAAE;EAC7E,MAAMmB,UAAU,GAAGhB,aAAa,CAACC,CAAC,EAAEC,KAAK,CAAC;EAC1C,OAAO,CAACI,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEQ,UAAU,CAAC;AAChC;AAEA,SAASC,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC5B,MAAMC,CAAC,GAAG,CAACD,CAAC,GAAGH,CAAC,GAAG,EAAE,IAAI,CAAC;EAC1B,OAAOzC,IAAI,CAAC8C,KAAK,CAACH,CAAC,GAAGA,CAAC,GAAGD,CAAC,GAAG1C,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC+C,GAAG,CAACF,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrE;AACA;AACA;AACA;AACA,SAASG,WAAWA,CAACP,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC1B,OAAO,CAACH,UAAU,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC,EAAEH,UAAU,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC,EAAEH,UAAU,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnF;AAEA,MAAMM,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC1B,MAAMC,QAAQ,GAAGD,KAAK,CACjBhD,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAChBA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CACpBA,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CACnBA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;EACrB,MAAMQ,KAAK,GAAGyC,QAAQ,CAAC7C,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAE6C,IAAI,IAAKC,UAAU,CAACD,IAAI,CAAC,CAAC;EACjE,IAAI1C,KAAK,CAACT,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOS,KAAK;EAChB;EACA,IAAIA,KAAK,CAACT,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOS,KAAK,CAAC4C,MAAM,CAAC,CAAC,CAAC;EAC1B;EACA,MAAMjD,OAAO,GAAG8C,QAAQ,CAACvC,KAAK,CAAC,IAAIM,MAAM,CAAC,MAAMiC,QAAQ,CAAClD,MAAM,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;EAC9E,IAAI,CAACI,OAAO,EAAE;IACV,OAAO2C,OAAO;EAClB;EACA,MAAMO,MAAM,GAAGlD,OAAO,CAACE,GAAG,CAAEC,IAAI,IAAKO,QAAQ,CAACoC,QAAQ,CAAClD,MAAM,GAAG,CAAC,GAAGO,IAAI,GAAGA,IAAI,GAAGA,IAAI,EAAE,EAAE,CAAC,CAAC;EAC5F,OAAO,CACH+C,MAAM,CAAC,CAAC,CAAC,IAAIP,OAAO,CAAC,CAAC,CAAC,EACvBO,MAAM,CAAC,CAAC,CAAC,IAAIP,OAAO,CAAC,CAAC,CAAC,EACvBO,MAAM,CAAC,CAAC,CAAC,IAAIP,OAAO,CAAC,CAAC,CAAC,EACvBO,MAAM,CAAC,CAAC,CAAC,IAAIP,OAAO,CAAC,CAAC,CAAC,CAC1B;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMQ,KAAK,GAAGC,MAAM,CAACC,GAAI,SAAS,CAAC,CAAC;AACpC,MAAMC,GAAG,GAAG,8BAA8B,CAAC,CAAC;AAC5C,MAAMC,GAAG,GAAGH,MAAM,CAACC,GAAI,wCAAwC,CAAC,CAAC;AACjE,MAAMG,IAAI,GAAGJ,MAAM,CAACC,GAAI,qDAAqD,CAAC,CAAC;AAC/E,MAAMI,KAAK,GAAGL,MAAM,CAACC,GAAI,iCAAiC,CAAC,CAAC;AAC5D,MAAMK,OAAO,GAAG,oBAAoB,CAAC,CAAC;AACtC,MAAMC,KAAK,GAAG,CACV,KAAK,EACLL,GAAG,EACH,GAAG,EACH,aAAa,EACbC,GAAG,EACH,GAAG,EACH,eAAe,EACfC,IAAI,EACJ,GAAG,EACHE,OAAO,EACP,GAAG,CACN;AACD,MAAME,YAAY,GAAG,CACjBR,MAAM,CAACC,GAAI,MAAM,EACjB,GAAGM,KAAK,EACR,GAAG,EACHP,MAAM,CAACC,GAAI,QAAQ,EACnB,GAAG,EACHI,KAAK,EACL,KAAK,EACL,KAAK,EACLN,KAAK,EACLC,MAAM,CAACC,GAAI,OAAO,CACrB;AACD,SAASQ,WAAWA,CAACtD,KAAK,EAAEuD,KAAK,EAAE;EAC/B,MAAMC,QAAQ,GAAGD,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,GAAGA,KAAK,GAAG;EACnD,OAAOvD,KAAK,EAAEyD,QAAQ,CAAC,GAAG,CAAC,GAAGzD,KAAK,GAAGwD,QAAQ;AAClD;AACA,SAASE,gBAAgBA,CAACC,KAAK,EAAE;EAC7B,MAAMC,WAAW,GAAG,IAAItD,MAAM,CAAC+C,YAAY,CAACtD,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EAC3D,MAAM8D,WAAW,GAAGF,KAAK,CAACG,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC,GACzDA,KAAK,CAACzE,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEuE,KAAK,CAACK,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,GACvDN,KAAK;EACX,MAAMO,IAAI,GAAGP,KAAK,CAACG,UAAU,CAAC,IAAI,CAAC,GAC7BH,KAAK,CAACjE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACnB,WAAW;EACjB,IAAI6D,KAAK,GAAG,EAAE;EACd,IAAIY,cAAc,GAAGP,WAAW,CAACG,IAAI,CAACF,WAAW,CAAC;EAClD,OAAOM,cAAc,KAAK,IAAI,EAAE;IAC5BZ,KAAK,GAAGA,KAAK,CAACb,MAAM,CAAC;MACjBJ,KAAK,EAAE6B,cAAc,CAAC,CAAC,CAAC,IAAI,EAAE;MAC9BC,QAAQ,EAAEd,WAAW,CAACa,cAAc,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEZ,KAAK,CAAClE,MAAM;IAC/D,CAAC,CAAC;IACF8E,cAAc,GAAGP,WAAW,CAACG,IAAI,CAACF,WAAW,CAAC;EAClD;EACAN,KAAK,GAAGA,KAAK,CAACc,MAAM,CAAC,CAAC;IAAE/B;EAAM,CAAC,KAAKA,KAAK,CAACwB,UAAU,CAAC,GAAG,CAAC,IAAIxB,KAAK,CAACwB,UAAU,CAAC,KAAK,CAAC,CAAC;EACrF,OAAO;IACHP,KAAK;IACLW;EACJ,CAAC;AACL;AACA,SAASI,aAAaA,CAAC;EAAEf,KAAK;EAAEW;AAAK,CAAC,EAAE;EACpC,OAAO,mBAAmBA,IAAI,KAAKX,KAAK,CACnC5D,GAAG,CAAC,CAAC;IAAE2C,KAAK;IAAE8B;EAAS,CAAC,KAAK,QAAQ/B,aAAa,CAACC,KAAK,CAAC,CAACvC,IAAI,CAAC,IAAI,CAAC,KAAKqE,QAAQ,EAAE,CAAC,CACpFrE,IAAI,CAAC,IAAI,CAAC,GAAG;AACtB;AAEA,SAASwE,WAAWA,CAACvD,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC1B,OAAO,IAAI,CAACF,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,CAACvB,GAAG,CAAEM,CAAC,IAAKA,CAAC,CAACuE,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC1E,IAAI,CAAC,EAAE,CAAC,EAAE;AAC/E;AAEA,SAAS2E,WAAWA,CAAC1D,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC1B,MAAMY,CAAC,GAAG3C,IAAI,CAACC,GAAG,CAAC4B,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAC3B,MAAMa,CAAC,GAAGD,CAAC,GAAG3C,IAAI,CAAC+C,GAAG,CAAClB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAC/B;EACA,MAAMU,CAAC,GAAGG,CAAC,KAAKD,CAAC,KAAKd,CAAC,GAAG,CAACC,CAAC,GAAGC,CAAC,IAAIa,CAAC,GAAGD,CAAC,KAAKb,CAAC,GAAG,CAAC,GAAG,CAACC,CAAC,GAAGF,CAAC,IAAIe,CAAC,GAAG,CAAC,GAAG,CAACf,CAAC,GAAGC,CAAC,IAAIc,CAAC,CAAC;EACpF,OAAO,CAAC,EAAE,IAAIH,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,EAAEE,CAAC,IAAIC,CAAC,GAAGD,CAAC,EAAEA,CAAC,CAAC;AACpD;AAEA,SAAS6C,YAAYA,CAACrC,KAAK,EAAE;EACzB,IAAI,CAACsC,cAAc,CAACtC,KAAK,CAAC,EAAE;IACxB,MAAM,IAAIhB,KAAK,CAAC,cAAc,CAAC;EACnC;EACA,MAAMuD,GAAG,GAAG,yCAAyC,CAACd,IAAI,CAACzB,KAAK,CAACwC,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,IACnF,IAAI;EACR,IAAIlE,KAAK,GAAG,CAAEiE,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,EAAGZ,IAAI,CAAC,CAAC;EACrC,IAAIzE,GAAG,GAAGqF,GAAG,GACP,CAAC,CAAC1E,QAAQ,CAAC0E,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,IAAK,CAAC,IAAI,CAAE,EAAEL,QAAQ,CAAC,EAAE,CAAC,CAACtF,KAAK,CAAC,CAAC,CAAC,GACpE,CAAC,CAACiB,QAAQ,CAAC0E,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,IAAK,CAAC,IAAI,CAAE,EAAEL,QAAQ,CAAC,EAAE,CAAC,CAACtF,KAAK,CAAC,CAAC,CAAC,GACtE,CAAC,CAACiB,QAAQ,CAAC0E,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,IAAK,CAAC,IAAI,CAAE,EAAEL,QAAQ,CAAC,EAAE,CAAC,CAACtF,KAAK,CAAC,CAAC,CAAC,GACxEoD,KAAK;EACX1B,KAAK,GAAGA,KAAK,KAAK,EAAE,GAAGA,KAAK,GAAG,GAAG;EAClCA,KAAK,GAAG,CAAEV,MAAM,CAACU,KAAK,CAAC,GAAG,GAAG,GAAK,CAAC,IAAI,CAAE,EAAE4D,QAAQ,CAAC,EAAE,CAAC,CAACtF,KAAK,CAAC,CAAC,CAAC;EAChEM,GAAG,IAAIoB,KAAK;EACZ,OAAO,IAAIpB,GAAG,CAACuF,WAAW,CAAC,CAAC,EAAE;AAClC;AACA,SAASH,cAAcA,CAACI,IAAI,EAAE;EAC1B,MAAMC,KAAK,GAAGpC,MAAM,CAACC,GAAI,wCAAwC;EACjE,MAAMlC,KAAK,GAAGiC,MAAM,CAACC,GAAI,gBAAgB;EACzC,OAAO,IAAIxC,MAAM,CAAC,iBAAiB2E,KAAK,YAAYA,KAAK,YAAYA,KAAK,sBAAsBA,KAAK,YAAYA,KAAK,YAAYA,KAAK,YAAYrE,KAAK,WAAW,CAAC,CAACS,IAAI,CAAC2D,IAAI,CAAC;AACnL;;AAEA;AACA;AACA;;AAEA,SAAShG,kBAAkB,EAAE+B,YAAY,EAAExB,WAAW,EAAE4C,WAAW,EAAEf,aAAa,EAAEwD,cAAc,EAAEvC,aAAa,EAAEqB,gBAAgB,EAAEvC,WAAW,EAAEoD,WAAW,EAAEG,WAAW,EAAEC,YAAY,EAAEL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}