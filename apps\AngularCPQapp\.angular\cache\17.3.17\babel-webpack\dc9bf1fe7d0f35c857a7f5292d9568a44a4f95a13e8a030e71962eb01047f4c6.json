{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, ViewContainerRef, ApplicationRef, afterNextRender, Directive, Renderer2 } from '@angular/core';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport * as i2 from '@ng-web-apis/mutation-observer';\nimport { provideMutationObserverInit, WaMutationObserver } from '@ng-web-apis/mutation-observer';\nconst TUI_ENTER = 'tui-enter';\nconst TUI_LEAVE = 'tui-leave';\nclass TuiAnimated {\n  constructor() {\n    // @ts-ignore https://github.com/angular/angular/blob/main/packages/core/src/render3/interfaces/view.ts#L56\n    this.renderer = inject(ViewContainerRef)._hostLView?.[11];\n    this.el = tuiInjectElement();\n    this.app = inject(ApplicationRef);\n    afterNextRender(() => this.remove());\n    if (!this.renderer) {\n      return;\n    }\n    // delegate is used in Angular Animations renderer\n    const renderer = this.renderer.delegate || this.renderer;\n    const {\n      removeChild,\n      data\n    } = renderer;\n    if (data[TUI_LEAVE]) {\n      data[TUI_LEAVE].push(this.el);\n      return;\n    }\n    data[TUI_LEAVE] = [this.el];\n    afterNextRender(() => {\n      renderer.removeChild = (parent, el, host) => {\n        const remove = () => removeChild.call(renderer, parent, el, host);\n        const elements = data[TUI_LEAVE];\n        const element = elements.find(leave => el.contains(leave));\n        const {\n          length\n        } = element?.getAnimations?.() || [];\n        if (!element) {\n          remove();\n          return;\n        }\n        elements.splice(elements.indexOf(element), 1);\n        element.classList.add(TUI_LEAVE);\n        const animations = element.getAnimations?.() ?? [];\n        const last = animations[animations.length - 1];\n        const finish = () => {\n          if (!parent || parent.contains(el)) {\n            remove();\n            this.app.tick();\n          }\n        };\n        if (animations.length > length && last) {\n          last.onfinish = finish;\n          last.oncancel = finish;\n        } else {\n          remove();\n        }\n      };\n    });\n  }\n  ngOnDestroy() {\n    const data = this.renderer?.data || {\n      [TUI_LEAVE]: []\n    };\n    setTimeout(() => {\n      data[TUI_LEAVE] = data[TUI_LEAVE].filter(e => e !== this.el);\n    });\n  }\n  remove() {\n    if (this.el.isConnected && !this.el.getAnimations?.().length) {\n      this.el.classList.remove(TUI_ENTER);\n    }\n  }\n  static {\n    this.ɵfac = function TuiAnimated_Factory(t) {\n      return new (t || TuiAnimated)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiAnimated,\n      selectors: [[\"\", \"tuiAnimated\", \"\"]],\n      hostAttrs: [1, \"tui-enter\"],\n      hostBindings: function TuiAnimated_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"animationend.self\", function TuiAnimated_animationend_self_HostBindingHandler() {\n            return ctx.remove();\n          })(\"animationcancel.self\", function TuiAnimated_animationcancel_self_HostBindingHandler() {\n            return ctx.remove();\n          });\n        }\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAnimated, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiAnimated]',\n      host: {\n        class: TUI_ENTER,\n        '(animationend.self)': 'remove()',\n        '(animationcancel.self)': 'remove()'\n      }\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nclass TuiAnimatedParent {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.renderer = inject(Renderer2);\n  }\n  handle() {\n    this.el.classList.remove(TUI_ENTER);\n    this.renderer.data[TUI_LEAVE] = Array.from(this.el.children);\n  }\n  static {\n    this.ɵfac = function TuiAnimatedParent_Factory(t) {\n      return new (t || TuiAnimatedParent)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiAnimatedParent,\n      selectors: [[\"\", \"tuiAnimatedParent\", \"\"]],\n      hostBindings: function TuiAnimatedParent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"waMutationObserver\", function TuiAnimatedParent_waMutationObserver_HostBindingHandler() {\n            return ctx.handle();\n          });\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([provideMutationObserverInit({\n        childList: true\n      })]), i0.ɵɵHostDirectivesFeature([TuiAnimated, {\n        directive: i2.WaMutationObserver,\n        outputs: [\"waMutationObserver\", \"waMutationObserver\"]\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAnimatedParent, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiAnimatedParent]',\n      providers: [provideMutationObserverInit({\n        childList: true\n      })],\n      hostDirectives: [TuiAnimated, {\n        directive: WaMutationObserver,\n        outputs: ['waMutationObserver']\n      }],\n      host: {\n        '(waMutationObserver)': 'handle()'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_ENTER, TUI_LEAVE, TuiAnimated, TuiAnimatedParent };", "map": {"version": 3, "names": ["i0", "inject", "ViewContainerRef", "ApplicationRef", "afterNextRender", "Directive", "Renderer2", "tuiInjectElement", "i2", "provideMutationObserverInit", "WaMutationObserver", "TUI_ENTER", "TUI_LEAVE", "TuiAnimated", "constructor", "renderer", "_host<PERSON><PERSON><PERSON><PERSON>", "el", "app", "remove", "delegate", "<PERSON><PERSON><PERSON><PERSON>", "data", "push", "parent", "host", "call", "elements", "element", "find", "leave", "contains", "length", "getAnimations", "splice", "indexOf", "classList", "add", "animations", "last", "finish", "tick", "onfinish", "oncancel", "ngOnDestroy", "setTimeout", "filter", "e", "isConnected", "ɵfac", "TuiAnimated_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostBindings", "TuiAnimated_HostBindings", "rf", "ctx", "ɵɵlistener", "TuiAnimated_animationend_self_HostBindingHandler", "TuiAnimated_animationcancel_self_HostBindingHandler", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "class", "TuiAnimatedParent", "handle", "Array", "from", "children", "TuiAnimatedParent_Factory", "TuiAnimatedParent_HostBindings", "TuiAnimatedParent_waMutationObserver_HostBindingHandler", "features", "ɵɵProvidersFeature", "childList", "ɵɵHostDirectivesFeature", "directive", "outputs", "providers", "hostDirectives"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-animated.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, ViewContainerRef, ApplicationRef, afterNextRender, Directive, Renderer2 } from '@angular/core';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport * as i2 from '@ng-web-apis/mutation-observer';\nimport { provideMutationObserverInit, WaMutationObserver } from '@ng-web-apis/mutation-observer';\n\nconst TUI_ENTER = 'tui-enter';\nconst TUI_LEAVE = 'tui-leave';\nclass TuiAnimated {\n    constructor() {\n        // @ts-ignore https://github.com/angular/angular/blob/main/packages/core/src/render3/interfaces/view.ts#L56\n        this.renderer = inject(ViewContainerRef)._hostLView?.[11];\n        this.el = tuiInjectElement();\n        this.app = inject(ApplicationRef);\n        afterNextRender(() => this.remove());\n        if (!this.renderer) {\n            return;\n        }\n        // delegate is used in Angular Animations renderer\n        const renderer = this.renderer.delegate || this.renderer;\n        const { removeChild, data } = renderer;\n        if (data[TUI_LEAVE]) {\n            data[TUI_LEAVE].push(this.el);\n            return;\n        }\n        data[TUI_LEAVE] = [this.el];\n        afterNextRender(() => {\n            renderer.removeChild = (parent, el, host) => {\n                const remove = () => removeChild.call(renderer, parent, el, host);\n                const elements = data[TUI_LEAVE];\n                const element = elements.find((leave) => el.contains(leave));\n                const { length } = element?.getAnimations?.() || [];\n                if (!element) {\n                    remove();\n                    return;\n                }\n                elements.splice(elements.indexOf(element), 1);\n                element.classList.add(TUI_LEAVE);\n                const animations = element.getAnimations?.() ?? [];\n                const last = animations[animations.length - 1];\n                const finish = () => {\n                    if (!parent || parent.contains(el)) {\n                        remove();\n                        this.app.tick();\n                    }\n                };\n                if (animations.length > length && last) {\n                    last.onfinish = finish;\n                    last.oncancel = finish;\n                }\n                else {\n                    remove();\n                }\n            };\n        });\n    }\n    ngOnDestroy() {\n        const data = this.renderer?.data || { [TUI_LEAVE]: [] };\n        setTimeout(() => {\n            data[TUI_LEAVE] = data[TUI_LEAVE].filter((e) => e !== this.el);\n        });\n    }\n    remove() {\n        if (this.el.isConnected && !this.el.getAnimations?.().length) {\n            this.el.classList.remove(TUI_ENTER);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAnimated, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAnimated, isStandalone: true, selector: \"[tuiAnimated]\", host: { listeners: { \"animationend.self\": \"remove()\", \"animationcancel.self\": \"remove()\" }, classAttribute: \"tui-enter\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAnimated, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiAnimated]',\n                    host: {\n                        class: TUI_ENTER,\n                        '(animationend.self)': 'remove()',\n                        '(animationcancel.self)': 'remove()',\n                    },\n                }]\n        }], ctorParameters: function () { return []; } });\n\nclass TuiAnimatedParent {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.renderer = inject(Renderer2);\n    }\n    handle() {\n        this.el.classList.remove(TUI_ENTER);\n        this.renderer.data[TUI_LEAVE] = Array.from(this.el.children);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAnimatedParent, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAnimatedParent, isStandalone: true, selector: \"[tuiAnimatedParent]\", host: { listeners: { \"waMutationObserver\": \"handle()\" } }, providers: [provideMutationObserverInit({ childList: true })], hostDirectives: [{ directive: TuiAnimated }, { directive: i2.WaMutationObserver, outputs: [\"waMutationObserver\", \"waMutationObserver\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAnimatedParent, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiAnimatedParent]',\n                    providers: [provideMutationObserverInit({ childList: true })],\n                    hostDirectives: [\n                        TuiAnimated,\n                        {\n                            directive: WaMutationObserver,\n                            outputs: ['waMutationObserver'],\n                        },\n                    ],\n                    host: {\n                        '(waMutationObserver)': 'handle()',\n                    },\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_ENTER, TUI_LEAVE, TuiAnimated, TuiAnimatedParent };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,SAAS,EAAEC,SAAS,QAAQ,eAAe;AAC/G,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,OAAO,KAAKC,EAAE,MAAM,gCAAgC;AACpD,SAASC,2BAA2B,EAAEC,kBAAkB,QAAQ,gCAAgC;AAEhG,MAAMC,SAAS,GAAG,WAAW;AAC7B,MAAMC,SAAS,GAAG,WAAW;AAC7B,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,QAAQ,GAAGd,MAAM,CAACC,gBAAgB,CAAC,CAACc,UAAU,GAAG,EAAE,CAAC;IACzD,IAAI,CAACC,EAAE,GAAGV,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACW,GAAG,GAAGjB,MAAM,CAACE,cAAc,CAAC;IACjCC,eAAe,CAAC,MAAM,IAAI,CAACe,MAAM,CAAC,CAAC,CAAC;IACpC,IAAI,CAAC,IAAI,CAACJ,QAAQ,EAAE;MAChB;IACJ;IACA;IACA,MAAMA,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACK,QAAQ,IAAI,IAAI,CAACL,QAAQ;IACxD,MAAM;MAAEM,WAAW;MAAEC;IAAK,CAAC,GAAGP,QAAQ;IACtC,IAAIO,IAAI,CAACV,SAAS,CAAC,EAAE;MACjBU,IAAI,CAACV,SAAS,CAAC,CAACW,IAAI,CAAC,IAAI,CAACN,EAAE,CAAC;MAC7B;IACJ;IACAK,IAAI,CAACV,SAAS,CAAC,GAAG,CAAC,IAAI,CAACK,EAAE,CAAC;IAC3Bb,eAAe,CAAC,MAAM;MAClBW,QAAQ,CAACM,WAAW,GAAG,CAACG,MAAM,EAAEP,EAAE,EAAEQ,IAAI,KAAK;QACzC,MAAMN,MAAM,GAAGA,CAAA,KAAME,WAAW,CAACK,IAAI,CAACX,QAAQ,EAAES,MAAM,EAAEP,EAAE,EAAEQ,IAAI,CAAC;QACjE,MAAME,QAAQ,GAAGL,IAAI,CAACV,SAAS,CAAC;QAChC,MAAMgB,OAAO,GAAGD,QAAQ,CAACE,IAAI,CAAEC,KAAK,IAAKb,EAAE,CAACc,QAAQ,CAACD,KAAK,CAAC,CAAC;QAC5D,MAAM;UAAEE;QAAO,CAAC,GAAGJ,OAAO,EAAEK,aAAa,GAAG,CAAC,IAAI,EAAE;QACnD,IAAI,CAACL,OAAO,EAAE;UACVT,MAAM,CAAC,CAAC;UACR;QACJ;QACAQ,QAAQ,CAACO,MAAM,CAACP,QAAQ,CAACQ,OAAO,CAACP,OAAO,CAAC,EAAE,CAAC,CAAC;QAC7CA,OAAO,CAACQ,SAAS,CAACC,GAAG,CAACzB,SAAS,CAAC;QAChC,MAAM0B,UAAU,GAAGV,OAAO,CAACK,aAAa,GAAG,CAAC,IAAI,EAAE;QAClD,MAAMM,IAAI,GAAGD,UAAU,CAACA,UAAU,CAACN,MAAM,GAAG,CAAC,CAAC;QAC9C,MAAMQ,MAAM,GAAGA,CAAA,KAAM;UACjB,IAAI,CAAChB,MAAM,IAAIA,MAAM,CAACO,QAAQ,CAACd,EAAE,CAAC,EAAE;YAChCE,MAAM,CAAC,CAAC;YACR,IAAI,CAACD,GAAG,CAACuB,IAAI,CAAC,CAAC;UACnB;QACJ,CAAC;QACD,IAAIH,UAAU,CAACN,MAAM,GAAGA,MAAM,IAAIO,IAAI,EAAE;UACpCA,IAAI,CAACG,QAAQ,GAAGF,MAAM;UACtBD,IAAI,CAACI,QAAQ,GAAGH,MAAM;QAC1B,CAAC,MACI;UACDrB,MAAM,CAAC,CAAC;QACZ;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACAyB,WAAWA,CAAA,EAAG;IACV,MAAMtB,IAAI,GAAG,IAAI,CAACP,QAAQ,EAAEO,IAAI,IAAI;MAAE,CAACV,SAAS,GAAG;IAAG,CAAC;IACvDiC,UAAU,CAAC,MAAM;MACbvB,IAAI,CAACV,SAAS,CAAC,GAAGU,IAAI,CAACV,SAAS,CAAC,CAACkC,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAK,IAAI,CAAC9B,EAAE,CAAC;IAClE,CAAC,CAAC;EACN;EACAE,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACF,EAAE,CAAC+B,WAAW,IAAI,CAAC,IAAI,CAAC/B,EAAE,CAACgB,aAAa,GAAG,CAAC,CAACD,MAAM,EAAE;MAC1D,IAAI,CAACf,EAAE,CAACmB,SAAS,CAACjB,MAAM,CAACR,SAAS,CAAC;IACvC;EACJ;EACA;IAAS,IAAI,CAACsC,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFtC,WAAW;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACuC,IAAI,kBAD+EpD,EAAE,CAAAqD,iBAAA;MAAAC,IAAA,EACJzC,WAAW;MAAA0C,SAAA;MAAAC,SAAA;MAAAC,YAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADT3D,EAAE,CAAA6D,UAAA,+BAAAC,iDAAA;YAAA,OACJF,GAAA,CAAAzC,MAAA,CAAO,CAAC;UAAA,CAAE,CAAC,kCAAA4C,oDAAA;YAAA,OAAXH,GAAA,CAAAzC,MAAA,CAAO,CAAC;UAAA,CAAE,CAAC;QAAA;MAAA;MAAA6C,UAAA;IAAA,EAA2L;EAAE;AAC3S;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGjE,EAAE,CAAAkE,iBAAA,CAGXrD,WAAW,EAAc,CAAC;IAC1GyC,IAAI,EAAEjD,SAAS;IACf8D,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,eAAe;MACzB3C,IAAI,EAAE;QACF4C,KAAK,EAAE1D,SAAS;QAChB,qBAAqB,EAAE,UAAU;QACjC,wBAAwB,EAAE;MAC9B;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAM2D,iBAAiB,CAAC;EACpBxD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACG,EAAE,GAAGV,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACQ,QAAQ,GAAGd,MAAM,CAACK,SAAS,CAAC;EACrC;EACAiE,MAAMA,CAAA,EAAG;IACL,IAAI,CAACtD,EAAE,CAACmB,SAAS,CAACjB,MAAM,CAACR,SAAS,CAAC;IACnC,IAAI,CAACI,QAAQ,CAACO,IAAI,CAACV,SAAS,CAAC,GAAG4D,KAAK,CAACC,IAAI,CAAC,IAAI,CAACxD,EAAE,CAACyD,QAAQ,CAAC;EAChE;EACA;IAAS,IAAI,CAACzB,IAAI,YAAA0B,0BAAAxB,CAAA;MAAA,YAAAA,CAAA,IAAyFmB,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAAClB,IAAI,kBA1B+EpD,EAAE,CAAAqD,iBAAA;MAAAC,IAAA,EA0BJgB,iBAAiB;MAAAf,SAAA;MAAAE,YAAA,WAAAmB,+BAAAjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1Bf3D,EAAE,CAAA6D,UAAA,gCAAAgB,wDAAA;YAAA,OA0BJjB,GAAA,CAAAW,MAAA,CAAO,CAAC;UAAA,CAAQ,CAAC;QAAA;MAAA;MAAAP,UAAA;MAAAc,QAAA,GA1Bf9E,EAAE,CAAA+E,kBAAA,CA0B0I,CAACtE,2BAA2B,CAAC;QAAEuE,SAAS,EAAE;MAAK,CAAC,CAAC,CAAC,GA1B9LhF,EAAE,CAAAiF,uBAAA,EA0B4NpE,WAAW;QAAAqE,SAAA,EAAiB1E,EAAE,CAACE,kBAAkB;QAAAyE,OAAA;MAAA;IAAA,EAA2E;EAAE;AACjc;AACA;EAAA,QAAAlB,SAAA,oBAAAA,SAAA,KA5BqGjE,EAAE,CAAAkE,iBAAA,CA4BXI,iBAAiB,EAAc,CAAC;IAChHhB,IAAI,EAAEjD,SAAS;IACf8D,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,qBAAqB;MAC/BgB,SAAS,EAAE,CAAC3E,2BAA2B,CAAC;QAAEuE,SAAS,EAAE;MAAK,CAAC,CAAC,CAAC;MAC7DK,cAAc,EAAE,CACZxE,WAAW,EACX;QACIqE,SAAS,EAAExE,kBAAkB;QAC7ByE,OAAO,EAAE,CAAC,oBAAoB;MAClC,CAAC,CACJ;MACD1D,IAAI,EAAE;QACF,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASd,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAEyD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}