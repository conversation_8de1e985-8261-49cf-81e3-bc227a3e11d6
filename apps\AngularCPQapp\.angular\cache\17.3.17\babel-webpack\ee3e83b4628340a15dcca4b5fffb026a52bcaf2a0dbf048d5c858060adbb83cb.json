{"ast": null, "code": "import { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nfunction tuiCreateOptions(defaults) {\n  const token = tuiCreateToken(defaults);\n  return [token, options => tuiProvideOptions(token, options, defaults)];\n}\n\n// TODO: Move all DI utils into this entry point in v.5\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiCreateOptions };", "map": {"version": 3, "names": ["tuiCreateToken", "tuiProvideOptions", "tuiCreateOptions", "defaults", "token", "options"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-utils-di.mjs"], "sourcesContent": ["import { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\n\nfunction tuiCreateOptions(defaults) {\n    const token = tuiCreateToken(defaults);\n    return [token, (options) => tuiProvideOptions(token, options, defaults)];\n}\n\n// TODO: Move all DI utils into this entry point in v.5\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiCreateOptions };\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,iBAAiB,QAAQ,mCAAmC;AAErF,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EAChC,MAAMC,KAAK,GAAGJ,cAAc,CAACG,QAAQ,CAAC;EACtC,OAAO,CAACC,KAAK,EAAGC,OAAO,IAAKJ,iBAAiB,CAACG,KAAK,EAAEC,OAAO,EAAEF,QAAQ,CAAC,CAAC;AAC5E;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}