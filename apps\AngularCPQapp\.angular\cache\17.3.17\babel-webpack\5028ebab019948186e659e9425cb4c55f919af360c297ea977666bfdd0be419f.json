{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Input } from '@angular/core';\nimport { NG_VALIDATORS } from '@angular/forms';\nimport { tuiZonefree, tuiTakeUntilDestroyed } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement, tuiProvide } from '@taiga-ui/cdk/utils';\nimport { BehaviorSubject, switchMap, of, delay } from 'rxjs';\nclass TuiNativeValidator {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.control$ = new BehaviorSubject(null);\n    this.sub = this.control$.pipe(switchMap(control => control?.events || of(null)), delay(0), tuiZonefree(), tuiTakeUntilDestroyed()).subscribe(() => this.handleValidation());\n    this.tuiNativeValidator = 'Invalid';\n  }\n  validate(control) {\n    this.control$.next(control);\n    return null;\n  }\n  handleValidation() {\n    const invalid = this.control$.value?.touched && this.control$.value?.invalid;\n    // TODO: Replace with :has(:invalid) when supported\n    this.el.closest('tui-textfield')?.classList.toggle('tui-invalid', invalid);\n    this.el.setCustomValidity?.(invalid ? this.tuiNativeValidator : '');\n  }\n  static {\n    this.ɵfac = function TuiNativeValidator_Factory(t) {\n      return new (t || TuiNativeValidator)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiNativeValidator,\n      selectors: [[\"\", \"tuiNativeValidator\", \"\"]],\n      hostBindings: function TuiNativeValidator_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focusout\", function TuiNativeValidator_focusout_HostBindingHandler() {\n            return ctx.handleValidation();\n          });\n        }\n      },\n      inputs: {\n        tuiNativeValidator: \"tuiNativeValidator\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(NG_VALIDATORS, TuiNativeValidator, true)])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiNativeValidator, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiNativeValidator]',\n      providers: [tuiProvide(NG_VALIDATORS, TuiNativeValidator, true)],\n      host: {\n        '(focusout)': 'handleValidation()'\n      }\n    }]\n  }], null, {\n    tuiNativeValidator: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiNativeValidator };", "map": {"version": 3, "names": ["i0", "Directive", "Input", "NG_VALIDATORS", "tuiZonefree", "tuiTakeUntilDestroyed", "tuiInjectElement", "tui<PERSON><PERSON><PERSON>", "BehaviorSubject", "switchMap", "of", "delay", "TuiNativeValidator", "constructor", "el", "control$", "sub", "pipe", "control", "events", "subscribe", "handleValidation", "tuiNativeValidator", "validate", "next", "invalid", "value", "touched", "closest", "classList", "toggle", "setCustomValidity", "ɵfac", "TuiNativeValidator_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostBindings", "TuiNativeValidator_HostBindings", "rf", "ctx", "ɵɵlistener", "TuiNativeValidator_focusout_HostBindingHandler", "inputs", "standalone", "features", "ɵɵProvidersFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-native-validator.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Input } from '@angular/core';\nimport { NG_VALIDATORS } from '@angular/forms';\nimport { tuiZonefree, tuiTakeUntilDestroyed } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement, tuiProvide } from '@taiga-ui/cdk/utils';\nimport { BehaviorSubject, switchMap, of, delay } from 'rxjs';\n\nclass TuiNativeValidator {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.control$ = new BehaviorSubject(null);\n        this.sub = this.control$\n            .pipe(switchMap((control) => control?.events || of(null)), delay(0), tuiZonefree(), tuiTakeUntilDestroyed())\n            .subscribe(() => this.handleValidation());\n        this.tuiNativeValidator = 'Invalid';\n    }\n    validate(control) {\n        this.control$.next(control);\n        return null;\n    }\n    handleValidation() {\n        const invalid = this.control$.value?.touched && this.control$.value?.invalid;\n        // TODO: Replace with :has(:invalid) when supported\n        this.el.closest('tui-textfield')?.classList.toggle('tui-invalid', invalid);\n        this.el.setCustomValidity?.(invalid ? this.tuiNativeValidator : '');\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiNativeValidator, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiNativeValidator, isStandalone: true, selector: \"[tuiNativeValidator]\", inputs: { tuiNativeValidator: \"tuiNativeValidator\" }, host: { listeners: { \"focusout\": \"handleValidation()\" } }, providers: [tuiProvide(NG_VALIDATORS, TuiNativeValidator, true)], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiNativeValidator, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiNativeValidator]',\n                    providers: [tuiProvide(NG_VALIDATORS, TuiNativeValidator, true)],\n                    host: {\n                        '(focusout)': 'handleValidation()',\n                    },\n                }]\n        }], propDecorators: { tuiNativeValidator: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiNativeValidator };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,WAAW,EAAEC,qBAAqB,QAAQ,2BAA2B;AAC9E,SAASC,gBAAgB,EAAEC,UAAU,QAAQ,qBAAqB;AAClE,SAASC,eAAe,EAAEC,SAAS,EAAEC,EAAE,EAAEC,KAAK,QAAQ,MAAM;AAE5D,MAAMC,kBAAkB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAGR,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACS,QAAQ,GAAG,IAAIP,eAAe,CAAC,IAAI,CAAC;IACzC,IAAI,CAACQ,GAAG,GAAG,IAAI,CAACD,QAAQ,CACnBE,IAAI,CAACR,SAAS,CAAES,OAAO,IAAKA,OAAO,EAAEC,MAAM,IAAIT,EAAE,CAAC,IAAI,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,EAAEP,WAAW,CAAC,CAAC,EAAEC,qBAAqB,CAAC,CAAC,CAAC,CAC3Ge,SAAS,CAAC,MAAM,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC;IAC7C,IAAI,CAACC,kBAAkB,GAAG,SAAS;EACvC;EACAC,QAAQA,CAACL,OAAO,EAAE;IACd,IAAI,CAACH,QAAQ,CAACS,IAAI,CAACN,OAAO,CAAC;IAC3B,OAAO,IAAI;EACf;EACAG,gBAAgBA,CAAA,EAAG;IACf,MAAMI,OAAO,GAAG,IAAI,CAACV,QAAQ,CAACW,KAAK,EAAEC,OAAO,IAAI,IAAI,CAACZ,QAAQ,CAACW,KAAK,EAAED,OAAO;IAC5E;IACA,IAAI,CAACX,EAAE,CAACc,OAAO,CAAC,eAAe,CAAC,EAAEC,SAAS,CAACC,MAAM,CAAC,aAAa,EAAEL,OAAO,CAAC;IAC1E,IAAI,CAACX,EAAE,CAACiB,iBAAiB,GAAGN,OAAO,GAAG,IAAI,CAACH,kBAAkB,GAAG,EAAE,CAAC;EACvE;EACA;IAAS,IAAI,CAACU,IAAI,YAAAC,2BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFtB,kBAAkB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAACuB,IAAI,kBAD+EnC,EAAE,CAAAoC,iBAAA;MAAAC,IAAA,EACJzB,kBAAkB;MAAA0B,SAAA;MAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADhBzC,EAAE,CAAA2C,UAAA,sBAAAC,+CAAA;YAAA,OACJF,GAAA,CAAArB,gBAAA,CAAiB,CAAC;UAAA,CAAD,CAAC;QAAA;MAAA;MAAAwB,MAAA;QAAAvB,kBAAA;MAAA;MAAAwB,UAAA;MAAAC,QAAA,GADhB/C,EAAE,CAAAgD,kBAAA,CACkM,CAACzC,UAAU,CAACJ,aAAa,EAAES,kBAAkB,EAAE,IAAI,CAAC,CAAC;IAAA,EAAiB;EAAE;AACjX;AACA;EAAA,QAAAqC,SAAA,oBAAAA,SAAA,KAHqGjD,EAAE,CAAAkD,iBAAA,CAGXtC,kBAAkB,EAAc,CAAC;IACjHyB,IAAI,EAAEpC,SAAS;IACfkD,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBM,QAAQ,EAAE,sBAAsB;MAChCC,SAAS,EAAE,CAAC9C,UAAU,CAACJ,aAAa,EAAES,kBAAkB,EAAE,IAAI,CAAC,CAAC;MAChE0C,IAAI,EAAE;QACF,YAAY,EAAE;MAClB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEhC,kBAAkB,EAAE,CAAC;MACnCe,IAAI,EAAEnC;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASU,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}