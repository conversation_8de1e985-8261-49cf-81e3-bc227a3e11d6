{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { CpqConfiguratorComponent } from './cpq-configurator.component';\nimport { ProductTreeModule } from '../product-tree/product-tree.module';\nimport { TuiTreeModule } from '@taiga-ui/kit';\nlet CpqConfiguratorModule = class CpqConfiguratorModule {};\nCpqConfiguratorModule = __decorate([NgModule({\n  declarations: [CpqConfiguratorComponent],\n  imports: [CommonModule, FormsModule, HttpClientModule, ProductTreeModule, TuiTreeModule],\n  exports: [CpqConfiguratorComponent],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA]\n})], CpqConfiguratorModule);\nexport { CpqConfiguratorModule };", "map": {"version": 3, "names": ["NgModule", "CUSTOM_ELEMENTS_SCHEMA", "CommonModule", "FormsModule", "HttpClientModule", "CpqConfiguratorComponent", "ProductTreeModule", "TuiTreeModule", "CpqConfiguratorModule", "__decorate", "declarations", "imports", "exports", "schemas"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet_CRM\\apps\\AngularCPQapp\\src\\app\\components\\cpq-configurator\\cpq-configurator.module.ts"], "sourcesContent": ["import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { CpqConfiguratorComponent } from './cpq-configurator.component';\nimport { ProductTreeModule } from '../product-tree/product-tree.module';\nimport { TuiTreeModule } from '@taiga-ui/kit';\nimport { TreeContentComponent } from './tree-content.component';\nimport { TuiTreeModule } from '@taiga-ui/kit';\nimport { TreeContentComponent } from './tree-content.component';\n\n@NgModule({\n  declarations: [CpqConfiguratorComponent],\n  imports: [\n    CommonModule,\n    FormsModule,\n    HttpClientModule,\n    ProductTreeModule,\n    TuiTreeModule\n  ],\n  exports: [CpqConfiguratorComponent],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA]\n})\nexport class CpqConfiguratorModule { }"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,sBAAsB,QAAQ,eAAe;AAChE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,aAAa,QAAQ,eAAe;AAiBtC,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB,GAAI;AAAzBA,qBAAqB,GAAAC,UAAA,EAZjCT,QAAQ,CAAC;EACRU,YAAY,EAAE,CAACL,wBAAwB,CAAC;EACxCM,OAAO,EAAE,CACPT,YAAY,EACZC,WAAW,EACXC,gBAAgB,EAChBE,iBAAiB,EACjBC,aAAa,CACd;EACDK,OAAO,EAAE,CAACP,wBAAwB,CAAC;EACnCQ,OAAO,EAAE,CAACZ,sBAAsB;CACjC,CAAC,C,EACWO,qBAAqB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}