{"ast": null, "code": "export * from '@taiga-ui/core/utils/dom';\nexport * from '@taiga-ui/core/utils/format';\nexport * from '@taiga-ui/core/utils/miscellaneous';\n\n/**\n * Generated bundle index. Do not edit.\n */", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-utils.mjs"], "sourcesContent": ["export * from '@taiga-ui/core/utils/dom';\nexport * from '@taiga-ui/core/utils/format';\nexport * from '@taiga-ui/core/utils/miscellaneous';\n\n/**\n * Generated bundle index. Do not edit.\n */\n"], "mappings": "AAAA,cAAc,0BAA0B;AACxC,cAAc,6BAA6B;AAC3C,cAAc,oCAAoC;;AAElD;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}