{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Directive, Optional, Self, SkipSelf, inject, Input, EventEmitter, Output, ChangeDetectorRef, INJECTOR, signal, TemplateRef, computed, Component, ChangeDetectionStrategy, ElementRef, ContentChild, ViewContainerRef } from '@angular/core';\nimport { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';\nimport { EMPTY_CLIENT_RECT, TUI_TRUE_HANDLER, CHAR_ZERO_WIDTH_SPACE, CHAR_NO_BREAK_SPACE } from '@taiga-ui/cdk/constants';\nimport * as i1 from '@taiga-ui/cdk/directives/active-zone';\nimport { TuiActiveZone } from '@taiga-ui/cdk/directives/active-zone';\nimport * as i2 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { tuiInjectElement, tuiGetActualTarget, tuiPointToClientRect, tuiIsElement, tuiIsHTMLElement, tuiIsElementEditable, tuiIsTextNode, tuiIsTextfield } from '@taiga-ui/cdk/utils/dom';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { tuiCreateTokenFromFactory, tuiCreateToken, tuiProvide, tuiPure, tuiPx, tuiProvideOptions, tuiIsString, tuiDirectiveBinding } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiDriverDirective, TuiPositionAccessor, tuiFallbackAccessor, TuiRectAccessor, tuiAsRectAccessor, tuiAsVehicle, tuiPositionAccessorFor, tuiRectAccessorFor, tuiAsDriver, TuiDriver, tuiAsPositionAccessor } from '@taiga-ui/core/classes';\nimport { TuiScrollbar } from '@taiga-ui/core/components/scrollbar';\nimport { TuiVisualViewportService, TuiPositionService } from '@taiga-ui/core/services';\nimport { TUI_VIEWPORT, TUI_DARK_MODE, TUI_SELECTION_STREAM } from '@taiga-ui/core/tokens';\nimport { PolymorpheusComponent, PolymorpheusTemplate, PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { BehaviorSubject, Subject, throttleTime, takeWhile, map, merge, filter, fromEvent, switchMap, delay, startWith, takeUntil, distinctUntilChanged, of, tap, share, combineLatest } from 'rxjs';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { tuiZonefreeScheduler, tuiIfMap, tuiCloseWatcher, tuiZonefull, tuiWatch, tuiTypedFromEvent, tuiZoneOptimized } from '@taiga-ui/cdk/observables';\nimport { tuiOverrideOptions, tuiCheckFixedPosition, tuiGetWordRange } from '@taiga-ui/core/utils';\nimport { TuiPortalService, TuiPortals, tuiAsPortal } from '@taiga-ui/cdk/classes';\nimport { __decorate } from 'tslib';\nimport { TUI_IS_TOUCH, TUI_RANGE } from '@taiga-ui/cdk/tokens';\nimport { shouldCall } from '@taiga-ui/event-plugins';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1$1 from '@taiga-ui/cdk/directives/obscured';\nimport { TuiObscured } from '@taiga-ui/cdk/directives/obscured';\nimport { tuiIsNativeKeyboardFocusable, tuiGetClosestFocusable, tuiIsNativeFocusedIn, tuiGetNativeFocused } from '@taiga-ui/cdk/utils/focus';\nimport { tuiIsEditingKey, tuiOverrideOptions as tuiOverrideOptions$1 } from '@taiga-ui/core/utils/miscellaneous';\nconst _c0 = a0 => ({\n  $implicit: a0\n});\nfunction TuiDropdownComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r1, \" \");\n  }\n}\nconst _c1 = [\"tuiDropdownHost\"];\nclass TuiDropdownDriver extends BehaviorSubject {\n  constructor() {\n    super(false);\n    this.type = 'dropdown';\n  }\n  static {\n    this.ɵfac = function TuiDropdownDriver_Factory(t) {\n      return new (t || TuiDropdownDriver)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiDropdownDriver,\n      factory: TuiDropdownDriver.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownDriver, [{\n    type: Injectable\n  }], function () {\n    return [];\n  }, null);\n})();\nclass TuiDropdownDriverDirective extends TuiDriverDirective {\n  constructor() {\n    super(...arguments);\n    this.type = 'dropdown';\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiDropdownDriverDirective_BaseFactory;\n      return function TuiDropdownDriverDirective_Factory(t) {\n        return (ɵTuiDropdownDriverDirective_BaseFactory || (ɵTuiDropdownDriverDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiDropdownDriverDirective)))(t || TuiDropdownDriverDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownDriverDirective,\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownDriverDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * A component to display a dropdown\n */\nconst TUI_DROPDOWN_COMPONENT = tuiCreateTokenFromFactory(() => TuiDropdownComponent);\nconst TUI_DROPDOWN_CONTEXT = tuiCreateToken();\nclass TuiDropdownService extends TuiPortalService {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiDropdownService_BaseFactory;\n      return function TuiDropdownService_Factory(t) {\n        return (ɵTuiDropdownService_BaseFactory || (ɵTuiDropdownService_BaseFactory = i0.ɵɵgetInheritedFactory(TuiDropdownService)))(t || TuiDropdownService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiDropdownService,\n      factory: TuiDropdownService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/** Default values for dropdown options */\nconst TUI_DROPDOWN_DEFAULT_OPTIONS = {\n  align: 'left',\n  direction: null,\n  limitWidth: 'auto',\n  maxHeight: 400,\n  minHeight: 80,\n  offset: 4,\n  appearance: ''\n};\n/**\n * Default parameters for dropdown directive\n */\nconst TUI_DROPDOWN_OPTIONS = tuiCreateToken(TUI_DROPDOWN_DEFAULT_OPTIONS);\nconst tuiDropdownOptionsProvider = override => ({\n  provide: TUI_DROPDOWN_OPTIONS,\n  deps: [[new Optional(), new Self(), TuiDropdownOptionsDirective], [new Optional(), new SkipSelf(), TUI_DROPDOWN_OPTIONS]],\n  useFactory: tuiOverrideOptions(override, TUI_DROPDOWN_DEFAULT_OPTIONS)\n});\nclass TuiDropdownOptionsDirective {\n  constructor() {\n    this.options = inject(TUI_DROPDOWN_OPTIONS, {\n      skipSelf: true\n    });\n    this.align = this.options.align;\n    this.appearance = this.options.appearance;\n    this.direction = this.options.direction;\n    this.limitWidth = this.options.limitWidth;\n    this.minHeight = this.options.minHeight;\n    this.maxHeight = this.options.maxHeight;\n    this.offset = this.options.offset;\n  }\n  static {\n    this.ɵfac = function TuiDropdownOptionsDirective_Factory(t) {\n      return new (t || TuiDropdownOptionsDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownOptionsDirective,\n      selectors: [[\"\", \"tuiDropdownAlign\", \"\"], [\"\", \"tuiDropdownAppearance\", \"\"], [\"\", \"tuiDropdownDirection\", \"\"], [\"\", \"tuiDropdownLimitWidth\", \"\"], [\"\", \"tuiDropdownMinHeight\", \"\"], [\"\", \"tuiDropdownMaxHeight\", \"\"], [\"\", \"tuiDropdownOffset\", \"\"]],\n      inputs: {\n        align: [i0.ɵɵInputFlags.None, \"tuiDropdownAlign\", \"align\"],\n        appearance: [i0.ɵɵInputFlags.None, \"tuiDropdownAppearance\", \"appearance\"],\n        direction: [i0.ɵɵInputFlags.None, \"tuiDropdownDirection\", \"direction\"],\n        limitWidth: [i0.ɵɵInputFlags.None, \"tuiDropdownLimitWidth\", \"limitWidth\"],\n        minHeight: [i0.ɵɵInputFlags.None, \"tuiDropdownMinHeight\", \"minHeight\"],\n        maxHeight: [i0.ɵɵInputFlags.None, \"tuiDropdownMaxHeight\", \"maxHeight\"],\n        offset: [i0.ɵɵInputFlags.None, \"tuiDropdownOffset\", \"offset\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TUI_DROPDOWN_OPTIONS, TuiDropdownOptionsDirective)])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownOptionsDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiDropdownAlign], [tuiDropdownAppearance], [tuiDropdownDirection], [tuiDropdownLimitWidth], [tuiDropdownMinHeight], [tuiDropdownMaxHeight], [tuiDropdownOffset]',\n      providers: [tuiProvide(TUI_DROPDOWN_OPTIONS, TuiDropdownOptionsDirective)]\n    }]\n  }], null, {\n    align: [{\n      type: Input,\n      args: ['tuiDropdownAlign']\n    }],\n    appearance: [{\n      type: Input,\n      args: ['tuiDropdownAppearance']\n    }],\n    direction: [{\n      type: Input,\n      args: ['tuiDropdownDirection']\n    }],\n    limitWidth: [{\n      type: Input,\n      args: ['tuiDropdownLimitWidth']\n    }],\n    minHeight: [{\n      type: Input,\n      args: ['tuiDropdownMinHeight']\n    }],\n    maxHeight: [{\n      type: Input,\n      args: ['tuiDropdownMaxHeight']\n    }],\n    offset: [{\n      type: Input,\n      args: ['tuiDropdownOffset']\n    }]\n  });\n})();\nclass TuiDropdownPosition extends TuiPositionAccessor {\n  constructor() {\n    super(...arguments);\n    this.options = inject(TUI_DROPDOWN_OPTIONS);\n    this.viewport = inject(TUI_VIEWPORT);\n    this.directionChange = new EventEmitter();\n    this.type = 'dropdown';\n    this.accessor = tuiFallbackAccessor('dropdown')(inject(TuiRectAccessor), inject(TuiDropdownDirective, {\n      optional: true\n    }));\n  }\n  emitDirection(direction) {\n    this.directionChange.emit(direction);\n  }\n  getPosition({\n    width,\n    height\n  }) {\n    if (!width && !height) {\n      this.previous = undefined;\n    }\n    const hostRect = this.accessor?.getClientRect() ?? EMPTY_CLIENT_RECT;\n    const viewportRect = this.viewport.getClientRect();\n    const {\n      minHeight,\n      align,\n      direction,\n      offset,\n      limitWidth\n    } = this.options;\n    const viewport = {\n      top: viewportRect.top - offset,\n      bottom: viewportRect.bottom + offset,\n      right: viewportRect.right - offset,\n      left: viewportRect.left + offset\n    };\n    const previous = this.previous || direction || 'bottom';\n    const available = {\n      top: hostRect.top - 2 * offset - viewport.top,\n      bottom: viewport.bottom - hostRect.bottom - 2 * offset\n    };\n    const rectWidth = limitWidth === 'fixed' ? hostRect.width : width;\n    const right = Math.max(hostRect.right - rectWidth, offset);\n    const left = hostRect.left + width < viewport.right ? hostRect.left : right;\n    const position = {\n      top: hostRect.top - offset - height,\n      bottom: hostRect.bottom + offset,\n      right: Math.max(viewport.left, right),\n      center: hostRect.left + hostRect.width / 2 + width / 2 < viewport.right ? hostRect.left + hostRect.width / 2 - width / 2 : right,\n      left: Math.max(viewport.left, left)\n    };\n    const better = available.top > available.bottom ? 'top' : 'bottom';\n    if (available[previous] > minHeight && direction || available[previous] > height) {\n      this.emitDirection(previous);\n      return [position[previous], position[align]];\n    }\n    this.previous = better;\n    this.emitDirection(better);\n    return [position[better], position[align]];\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiDropdownPosition_BaseFactory;\n      return function TuiDropdownPosition_Factory(t) {\n        return (ɵTuiDropdownPosition_BaseFactory || (ɵTuiDropdownPosition_BaseFactory = i0.ɵɵgetInheritedFactory(TuiDropdownPosition)))(t || TuiDropdownPosition);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownPosition,\n      outputs: {\n        directionChange: \"tuiDropdownDirectionChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n__decorate([tuiPure], TuiDropdownPosition.prototype, \"emitDirection\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownPosition, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], null, {\n    directionChange: [{\n      type: Output,\n      args: ['tuiDropdownDirectionChange']\n    }],\n    emitDirection: []\n  });\n})();\nclass TuiDropdownDirective {\n  constructor() {\n    this.refresh$ = new Subject();\n    this.service = inject(TuiDropdownService);\n    this.cdr = inject(ChangeDetectorRef);\n    // TODO: think of a better solution later\n    this.drivers = coerceArray(inject(TuiDropdownDriver, {\n      self: true,\n      optional: true\n    }));\n    this.sub = this.refresh$.pipe(throttleTime(0, tuiZonefreeScheduler()), takeUntilDestroyed()).subscribe(() => {\n      this.ref()?.changeDetectorRef.detectChanges();\n      this.ref()?.changeDetectorRef.markForCheck();\n    });\n    this.el = tuiInjectElement();\n    this.type = 'dropdown';\n    this.component = new PolymorpheusComponent(inject(TUI_DROPDOWN_COMPONENT), inject(INJECTOR));\n    this.ref = signal(null);\n    // TODO(v5): rename to `content`\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    this._content = signal(null);\n  }\n  set tuiDropdown(content) {\n    this._content.set(content instanceof TemplateRef ? new PolymorpheusTemplate(content, this.cdr) : content);\n    if (!this._content()) {\n      this.toggle(false);\n    }\n  }\n  get position() {\n    return tuiCheckFixedPosition(this.el) ? 'fixed' : 'absolute';\n  }\n  // TODO(v5): delete\n  get content() {\n    return this._content();\n  }\n  // TODO(v5): delete\n  set content(x) {\n    this._content.set(x);\n  }\n  ngAfterViewChecked() {\n    this.refresh$.next();\n  }\n  ngOnDestroy() {\n    this.toggle(false);\n  }\n  getClientRect() {\n    return this.el.getBoundingClientRect();\n  }\n  toggle(show) {\n    const ref = this.ref();\n    if (show && this._content() && !ref) {\n      this.ref.set(this.service.add(this.component));\n    } else if (!show && ref) {\n      this.ref.set(null);\n      this.service.remove(ref);\n    }\n    this.drivers.forEach(driver => driver?.next(show));\n    // TODO: Remove in v5, only needed in Angular 16\n    this.cdr.markForCheck();\n  }\n  static {\n    this.ɵfac = function TuiDropdownDirective_Factory(t) {\n      return new (t || TuiDropdownDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownDirective,\n      selectors: [[\"\", \"tuiDropdown\", \"\", 5, \"ng-container\", 5, \"ng-template\"]],\n      hostVars: 2,\n      hostBindings: function TuiDropdownDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tui-dropdown-open\", ctx.ref());\n        }\n      },\n      inputs: {\n        tuiDropdown: \"tuiDropdown\"\n      },\n      exportAs: [\"tuiDropdown\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsRectAccessor(TuiDropdownDirective), tuiAsVehicle(TuiDropdownDirective)]), i0.ɵɵHostDirectivesFeature([TuiDropdownDriverDirective, {\n        directive: TuiDropdownPosition,\n        outputs: [\"tuiDropdownDirectionChange\", \"tuiDropdownDirectionChange\"]\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiDropdown]:not(ng-container):not(ng-template)',\n      providers: [tuiAsRectAccessor(TuiDropdownDirective), tuiAsVehicle(TuiDropdownDirective)],\n      exportAs: 'tuiDropdown',\n      hostDirectives: [TuiDropdownDriverDirective, {\n        directive: TuiDropdownPosition,\n        outputs: ['tuiDropdownDirectionChange']\n      }],\n      host: {\n        '[class.tui-dropdown-open]': 'ref()'\n      }\n    }]\n  }], null, {\n    tuiDropdown: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @description:\n * This component is used to show template in a portal\n * using default style of white rounded box with a shadow\n */\nclass TuiDropdownComponent {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.accessor = inject(TuiRectAccessor);\n    this.viewport = inject(TUI_VIEWPORT);\n    this.vvs = inject(TuiVisualViewportService);\n    this.styles$ = inject(TuiPositionService).pipe(takeWhile(() => this.directive.el.isConnected && !!this.directive.el.getBoundingClientRect().height), map(v => this.position === 'fixed' ? this.vvs.correct(v) : v), map(([top, left]) => this.getStyles(left, top)), takeUntilDestroyed());\n    this.options = inject(TUI_DROPDOWN_OPTIONS);\n    this.directive = inject(TuiDropdownDirective);\n    this.context = inject(TUI_DROPDOWN_CONTEXT, {\n      optional: true\n    });\n    this.darkMode = inject(TUI_DARK_MODE);\n    this.position = this.directive.position;\n    this.theme = computed((_ = this.darkMode()) => this.directive.el.closest('[tuiTheme]')?.getAttribute('tuiTheme'));\n    this.close = () => this.directive.toggle(false);\n  }\n  ngAfterViewInit() {\n    this.styles$.subscribe({\n      next: styles => Object.assign(this.el.style, styles),\n      complete: () => this.close?.()\n    });\n  }\n  getStyles(x, y) {\n    const {\n      maxHeight,\n      minHeight,\n      offset,\n      limitWidth\n    } = this.options;\n    const parent = this.el.offsetParent?.getBoundingClientRect() || EMPTY_CLIENT_RECT;\n    const {\n      left = 0,\n      top = 0\n    } = this.position === 'fixed' ? {} : parent;\n    const rect = this.accessor.getClientRect();\n    const viewport = this.viewport.getClientRect();\n    const above = rect.top - viewport.top - 2 * offset;\n    const below = viewport.top + viewport.height - y - offset;\n    const available = y > rect.bottom ? below : above;\n    const height = this.el.getBoundingClientRect().right <= rect.left || x >= rect.right ? maxHeight : tuiClamp(available, minHeight, maxHeight);\n    y -= top;\n    x -= left;\n    return {\n      position: this.position,\n      top: tuiPx(Math.round(Math.max(y, offset - top))),\n      left: tuiPx(Math.round(x)),\n      maxHeight: tuiPx(Math.round(height)),\n      width: limitWidth === 'fixed' ? tuiPx(Math.round(rect.width)) : '',\n      minWidth: limitWidth === 'min' ? tuiPx(Math.round(rect.width)) : '',\n      maxWidth: tuiPx(Math.round(viewport.width) - 16) // 8px min gap from each side\n    };\n  }\n  static {\n    this.ɵfac = function TuiDropdownComponent_Factory(t) {\n      return new (t || TuiDropdownComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiDropdownComponent,\n      selectors: [[\"tui-dropdown\"]],\n      hostVars: 2,\n      hostBindings: function TuiDropdownComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-appearance\", ctx.options.appearance)(\"tuiTheme\", ctx.theme());\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiPositionService, tuiPositionAccessorFor('dropdown', TuiDropdownPosition), tuiRectAccessorFor('dropdown', TuiDropdownDirective)]), i0.ɵɵHostDirectivesFeature([i1.TuiActiveZone, i2.TuiAnimated]), i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 4,\n      consts: [[1, \"t-scroll\"], [\"class\", \"t-primitive\", 4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [1, \"t-primitive\"]],\n      template: function TuiDropdownComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"tui-scrollbar\", 0);\n          i0.ɵɵtemplate(1, TuiDropdownComponent_div_1_Template, 2, 1, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.directive._content())(\"polymorpheusOutletContext\", i0.ɵɵpureFunction1(2, _c0, ctx.close));\n        }\n      },\n      dependencies: [PolymorpheusOutlet, TuiScrollbar],\n      styles: [\"[_nghost-%COMP%]{position:absolute;display:flex;box-shadow:var(--tui-shadow-medium);color:var(--tui-text-primary);background:var(--tui-background-elevation-3);border-radius:var(--tui-radius-m);overflow:hidden;border:1px solid var(--tui-border-normal);box-sizing:border-box;isolation:isolate;pointer-events:auto;--tui-from: translateY(-1rem)}.tui-enter[_nghost-%COMP%], .tui-leave[_nghost-%COMP%]{animation-name:tuiFade,tuiSlide}[_nghost-%COMP%]:not([style*=top]){visibility:hidden}.t-scroll[_ngcontent-%COMP%]{flex-grow:1;max-inline-size:100%;inline-size:-webkit-max-content;inline-size:max-content;overscroll-behavior:none}.t-primitive[_ngcontent-%COMP%]{padding:1rem}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-dropdown',\n      imports: [PolymorpheusOutlet, TuiScrollbar],\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [TuiPositionService, tuiPositionAccessorFor('dropdown', TuiDropdownPosition), tuiRectAccessorFor('dropdown', TuiDropdownDirective)],\n      hostDirectives: [TuiActiveZone, TuiAnimated],\n      host: {\n        '[attr.data-appearance]': 'options.appearance',\n        '[attr.tuiTheme]': 'theme()'\n      },\n      template: \"<tui-scrollbar class=\\\"t-scroll\\\">\\n    <div\\n        *polymorpheusOutlet=\\\"directive._content() as text; context: {$implicit: close}\\\"\\n        class=\\\"t-primitive\\\"\\n    >\\n        {{ text }}\\n    </div>\\n</tui-scrollbar>\\n\",\n      styles: [\":host{position:absolute;display:flex;box-shadow:var(--tui-shadow-medium);color:var(--tui-text-primary);background:var(--tui-background-elevation-3);border-radius:var(--tui-radius-m);overflow:hidden;border:1px solid var(--tui-border-normal);box-sizing:border-box;isolation:isolate;pointer-events:auto;--tui-from: translateY(-1rem)}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide}:host:not([style*=top]){visibility:hidden}.t-scroll{flex-grow:1;max-inline-size:100%;inline-size:-webkit-max-content;inline-size:max-content;overscroll-behavior:none}.t-primitive{padding:1rem}\\n\"]\n    }]\n  }], null, null);\n})();\nfunction activeZoneFilter(event) {\n  return !event || this.driver.value && !this.activeZone.contains(tuiGetActualTarget(event));\n}\nclass TuiDropdownContext extends TuiRectAccessor {\n  constructor() {\n    super(...arguments);\n    this.isTouch = inject(TUI_IS_TOUCH);\n    this.currentRect = EMPTY_CLIENT_RECT;\n    this.userSelect = computed(() => this.isTouch() ? 'none' : null);\n    this.activeZone = inject(TuiActiveZone);\n    this.driver = inject(TuiDropdownDriver);\n    this.type = 'dropdown';\n  }\n  getClientRect() {\n    return this.currentRect;\n  }\n  closeDropdown(_event) {\n    this.driver.next(false);\n    this.currentRect = EMPTY_CLIENT_RECT;\n  }\n  onContextMenu(x, y) {\n    this.currentRect = tuiPointToClientRect(x, y);\n    this.driver.next(true);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiDropdownContext_BaseFactory;\n      return function TuiDropdownContext_Factory(t) {\n        return (ɵTuiDropdownContext_BaseFactory || (ɵTuiDropdownContext_BaseFactory = i0.ɵɵgetInheritedFactory(TuiDropdownContext)))(t || TuiDropdownContext);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownContext,\n      selectors: [[\"\", \"tuiDropdownContext\", \"\"]],\n      hostVars: 6,\n      hostBindings: function TuiDropdownContext_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"pointerdown.zoneless\", function TuiDropdownContext_pointerdown_zoneless_HostBindingHandler($event) {\n            return ctx.closeDropdown($event);\n          }, false, i0.ɵɵresolveDocument)(\"contextmenu.capture.zoneless\", function TuiDropdownContext_contextmenu_capture_zoneless_HostBindingHandler($event) {\n            return ctx.closeDropdown($event);\n          }, false, i0.ɵɵresolveDocument)(\"keydown.esc\", function TuiDropdownContext_keydown_esc_HostBindingHandler() {\n            return ctx.closeDropdown();\n          }, false, i0.ɵɵresolveDocument)(\"longtap\", function TuiDropdownContext_longtap_HostBindingHandler($event) {\n            return ctx.onContextMenu($event.detail.clientX, $event.detail.clientY);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"user-select\", ctx.userSelect())(\"-webkit-user-select\", ctx.userSelect())(\"-webkit-touch-callout\", ctx.userSelect());\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiActiveZone, TuiDropdownDriver, tuiAsDriver(TuiDropdownDriver), tuiAsRectAccessor(TuiDropdownContext)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n__decorate([shouldCall(activeZoneFilter)], TuiDropdownContext.prototype, \"closeDropdown\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownContext, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiDropdownContext]',\n      providers: [TuiActiveZone, TuiDropdownDriver, tuiAsDriver(TuiDropdownDriver), tuiAsRectAccessor(TuiDropdownContext)],\n      host: {\n        '[style.user-select]': 'userSelect()',\n        '[style.-webkit-user-select]': 'userSelect()',\n        '[style.-webkit-touch-callout]': 'userSelect()',\n        '(document:pointerdown.zoneless)': 'closeDropdown($event)',\n        '(document:contextmenu.capture.zoneless)': 'closeDropdown($event)',\n        '(document:keydown.esc)': 'closeDropdown()',\n        '(longtap)': 'onContextMenu($event.detail.clientX, $event.detail.clientY)'\n      }\n    }]\n  }], null, {\n    closeDropdown: []\n  });\n})();\n\n/** Default values for hint options */\nconst TUI_DROPDOWN_HOVER_DEFAULT_OPTIONS = {\n  showDelay: 200,\n  hideDelay: 500\n};\n/**\n * Default parameters for dropdown hover directive\n */\nconst TUI_DROPDOWN_HOVER_OPTIONS = tuiCreateToken(TUI_DROPDOWN_HOVER_DEFAULT_OPTIONS);\nfunction tuiDropdownHoverOptionsProvider(options) {\n  return tuiProvideOptions(TUI_DROPDOWN_HOVER_OPTIONS, options, TUI_DROPDOWN_HOVER_DEFAULT_OPTIONS);\n}\nfunction shouldClose(event) {\n  return (\n    // @ts-ignore\n    typeof CloseWatcher === 'undefined' &&\n    // ?. for auto fill events\n    event.key?.toLowerCase() === 'escape' && this.tuiDropdownEnabled && !!this.tuiDropdownOpen && !this['dropdown']()?.nextElementSibling\n  );\n}\nclass TuiDropdownOpen {\n  constructor() {\n    this.directive = inject(TuiDropdownDirective);\n    this.el = tuiInjectElement();\n    this.obscured = inject(TuiObscured);\n    this.activeZone = inject(TuiActiveZone);\n    this.dropdown = computed(() => this.directive.ref()?.location.nativeElement);\n    this.tuiDropdownEnabled = true;\n    this.tuiDropdownOpen = false;\n    this.tuiDropdownOpenChange = new EventEmitter();\n    // TODO: make it private when all legacy controls will be deleted from @taiga-ui/legacy (5.0)\n    this.driver = inject(TuiDropdownDriver);\n    this.sub = this.driver.pipe(tuiIfMap(() => merge(tuiCloseWatcher(), this.obscured.tuiObscured.pipe(filter(Boolean)), this.activeZone.tuiActiveZoneChange.pipe(filter(a => !a)), fromEvent(this.el, 'focusin').pipe(filter(event => !this.host.contains(tuiGetActualTarget(event)) || !this.directive.ref())))), tuiZonefull(), tuiWatch(), takeUntilDestroyed()).subscribe(() => this.toggle(false));\n    this.sync = this.driver.pipe(takeUntilDestroyed()).subscribe(open => {\n      if (open !== this.tuiDropdownOpen) {\n        this.update(open);\n      }\n    });\n  }\n  ngOnChanges() {\n    this.drive(!!this.tuiDropdownOpen);\n    this.tuiDropdownOpenChange.emit(!!this.tuiDropdownOpen);\n  }\n  toggle(open) {\n    if (this.focused && !open) {\n      this.host.focus({\n        preventScroll: true\n      });\n    }\n    this.update(open);\n  }\n  onEsc(event) {\n    event.preventDefault();\n    this.toggle(false);\n  }\n  onClick(target) {\n    if (!this.editable && this.host.contains(target)) {\n      this.update(!this.tuiDropdownOpen);\n    }\n  }\n  onArrow(event, up) {\n    if (!tuiIsElement(event.target) || !this.host.contains(event.target) || !this.tuiDropdownEnabled || !this.directive._content()) {\n      return;\n    }\n    event.preventDefault();\n    this.focusDropdown(up);\n  }\n  onKeydown(event) {\n    const target = tuiGetActualTarget(event);\n    if (!event.defaultPrevented && tuiIsEditingKey(event.key) && this.editable && this.focused && tuiIsHTMLElement(target) && !tuiIsElementEditable(target)) {\n      this.host.focus({\n        preventScroll: true\n      });\n    }\n  }\n  get host() {\n    const initial = this.dropdownHost?.nativeElement || this.el;\n    const focusable = tuiIsNativeKeyboardFocusable(initial) ? initial : tuiGetClosestFocusable({\n      initial,\n      root: this.el\n    });\n    return this.dropdownHost?.nativeElement || focusable || this.el;\n  }\n  get editable() {\n    return tuiIsElementEditable(this.host);\n  }\n  get focused() {\n    return tuiIsNativeFocusedIn(this.host) || tuiIsNativeFocusedIn(this.dropdown());\n  }\n  update(open) {\n    if (open && !this.tuiDropdownEnabled) {\n      return this.drive();\n    }\n    this.tuiDropdownOpen = open;\n    this.tuiDropdownOpenChange.emit(open);\n    this.drive();\n  }\n  drive(open = !!this.tuiDropdownOpen && this.tuiDropdownEnabled) {\n    this.obscured.tuiObscuredEnabled = open;\n    this.driver.next(open);\n  }\n  focusDropdown(previous) {\n    const root = this.dropdown();\n    if (!root) {\n      this.update(true);\n      return;\n    }\n    const doc = this.el.ownerDocument;\n    const child = root.appendChild(doc.createElement('div'));\n    const initial = previous ? child : root;\n    const focusable = tuiGetClosestFocusable({\n      initial,\n      previous,\n      root\n    });\n    child.remove();\n    focusable?.focus();\n  }\n  static {\n    this.ɵfac = function TuiDropdownOpen_Factory(t) {\n      return new (t || TuiDropdownOpen)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownOpen,\n      selectors: [[\"\", \"tuiDropdown\", \"\", \"tuiDropdownOpen\", \"\"], [\"\", \"tuiDropdown\", \"\", \"tuiDropdownOpenChange\", \"\"]],\n      contentQueries: function TuiDropdownOpen_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, _c1, 5, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dropdownHost = _t.first);\n        }\n      },\n      hostBindings: function TuiDropdownOpen_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TuiDropdownOpen_click_HostBindingHandler($event) {\n            return ctx.onClick($event.target);\n          })(\"keydown.arrowDown\", function TuiDropdownOpen_keydown_arrowDown_HostBindingHandler($event) {\n            return ctx.onArrow($event, false);\n          })(\"keydown.arrowUp\", function TuiDropdownOpen_keydown_arrowUp_HostBindingHandler($event) {\n            return ctx.onArrow($event, true);\n          })(\"keydown.zoneless.capture\", function TuiDropdownOpen_keydown_zoneless_capture_HostBindingHandler($event) {\n            return ctx.onEsc($event);\n          }, false, i0.ɵɵresolveDocument)(\"keydown.zoneless\", function TuiDropdownOpen_keydown_zoneless_HostBindingHandler($event) {\n            return ctx.onKeydown($event);\n          }, false, i0.ɵɵresolveDocument)(\"tuiActiveZoneChange\", function TuiDropdownOpen_tuiActiveZoneChange_HostBindingHandler() {\n            return 0;\n          });\n        }\n      },\n      inputs: {\n        tuiDropdownEnabled: \"tuiDropdownEnabled\",\n        tuiDropdownOpen: \"tuiDropdownOpen\"\n      },\n      outputs: {\n        tuiDropdownOpenChange: \"tuiDropdownOpenChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiDropdownDriver, tuiAsDriver(TuiDropdownDriver)]), i0.ɵɵHostDirectivesFeature([i1$1.TuiObscured, {\n        directive: i1.TuiActiveZone,\n        inputs: [\"tuiActiveZoneParent\", \"tuiActiveZoneParent\"],\n        outputs: [\"tuiActiveZoneChange\", \"tuiActiveZoneChange\"]\n      }]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([shouldCall(shouldClose)], TuiDropdownOpen.prototype, \"onEsc\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownOpen, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiDropdown][tuiDropdownOpen],[tuiDropdown][tuiDropdownOpenChange]',\n      providers: [TuiDropdownDriver, tuiAsDriver(TuiDropdownDriver)],\n      hostDirectives: [TuiObscured, {\n        directive: TuiActiveZone,\n        inputs: ['tuiActiveZoneParent'],\n        outputs: ['tuiActiveZoneChange']\n      }],\n      host: {\n        '(click)': 'onClick($event.target)',\n        '(keydown.arrowDown)': 'onArrow($event, false)',\n        '(keydown.arrowUp)': 'onArrow($event, true)',\n        '(document:keydown.zoneless.capture)': 'onEsc($event)',\n        '(document:keydown.zoneless)': 'onKeydown($event)',\n        // TODO: Necessary because startWith(false) + distinctUntilChanged() in TuiActiveZone, think of better solution\n        '(tuiActiveZoneChange)': '0'\n      }\n    }]\n  }], null, {\n    dropdownHost: [{\n      type: ContentChild,\n      args: ['tuiDropdownHost', {\n        descendants: true,\n        read: ElementRef\n      }]\n    }],\n    tuiDropdownEnabled: [{\n      type: Input\n    }],\n    tuiDropdownOpen: [{\n      type: Input\n    }],\n    tuiDropdownOpenChange: [{\n      type: Output\n    }],\n    onEsc: []\n  });\n})();\nclass TuiDropdownHover extends TuiDriver {\n  constructor() {\n    super(subscriber => this.stream$.subscribe(subscriber));\n    this.el = tuiInjectElement();\n    this.doc = inject(DOCUMENT);\n    this.options = inject(TUI_DROPDOWN_HOVER_OPTIONS);\n    this.activeZone = inject(TuiActiveZone);\n    this.open = inject(TuiDropdownOpen, {\n      optional: true\n    });\n    /**\n     * Dropdown can be removed not only via click/touch –\n     * swipe on mobile devices removes dropdown sheet without triggering new mouseover / mouseout events.\n     */\n    this.dropdownExternalRemoval$ = toObservable(inject(TuiDropdownDirective).ref).pipe(filter(x => !x && this.hovered));\n    this.stream$ = merge(this.dropdownExternalRemoval$.pipe(switchMap(() => tuiTypedFromEvent(this.doc, 'pointerdown').pipe(map(tuiGetActualTarget), delay(this.hideDelay), startWith(null), takeUntil(fromEvent(this.doc, 'mouseover'))))), tuiTypedFromEvent(this.doc, 'mouseover').pipe(map(tuiGetActualTarget)), tuiTypedFromEvent(this.doc, 'mouseout').pipe(map(e => e.relatedTarget))).pipe(map(element => tuiIsElement(element) && this.isHovered(element)), distinctUntilChanged(), switchMap(v => of(v).pipe(delay(v ? this.showDelay : this.hideDelay))), tuiZoneOptimized(), tap(hovered => {\n      this.hovered = hovered;\n      this.open?.toggle(hovered);\n    }), share());\n    this.showDelay = this.options.showDelay;\n    this.hideDelay = this.options.hideDelay;\n    this.hovered = false;\n    this.type = 'dropdown';\n  }\n  onClick(event) {\n    if (this.hovered && this.open) {\n      event.preventDefault();\n    }\n  }\n  isHovered(element) {\n    const host = this.dropdownHost?.nativeElement || this.el;\n    const hovered = host.contains(element);\n    const child = !this.el.contains(element) && this.activeZone.contains(element);\n    return hovered || child;\n  }\n  static {\n    this.ɵfac = function TuiDropdownHover_Factory(t) {\n      return new (t || TuiDropdownHover)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownHover,\n      selectors: [[\"\", \"tuiDropdownHover\", \"\"]],\n      contentQueries: function TuiDropdownHover_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, _c1, 5, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dropdownHost = _t.first);\n        }\n      },\n      hostBindings: function TuiDropdownHover_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click.capture\", function TuiDropdownHover_click_capture_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          });\n        }\n      },\n      inputs: {\n        showDelay: [i0.ɵɵInputFlags.None, \"tuiDropdownShowDelay\", \"showDelay\"],\n        hideDelay: [i0.ɵɵInputFlags.None, \"tuiDropdownHideDelay\", \"hideDelay\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiActiveZone, tuiAsDriver(TuiDropdownHover)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownHover, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiDropdownHover]',\n      providers: [TuiActiveZone, tuiAsDriver(TuiDropdownHover)],\n      host: {\n        '(click.capture)': 'onClick($event)'\n      }\n    }]\n  }], function () {\n    return [];\n  }, {\n    dropdownHost: [{\n      type: ContentChild,\n      args: ['tuiDropdownHost', {\n        descendants: true,\n        read: ElementRef\n      }]\n    }],\n    showDelay: [{\n      type: Input,\n      args: ['tuiDropdownShowDelay']\n    }],\n    hideDelay: [{\n      type: Input,\n      args: ['tuiDropdownHideDelay']\n    }]\n  });\n})();\nclass TuiDropdownManual {\n  constructor() {\n    this.driver = inject(TuiDropdownDriver);\n    this.tuiDropdownManual = false;\n  }\n  ngOnChanges() {\n    this.driver.next(!!this.tuiDropdownManual);\n  }\n  static {\n    this.ɵfac = function TuiDropdownManual_Factory(t) {\n      return new (t || TuiDropdownManual)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownManual,\n      selectors: [[\"\", \"tuiDropdownManual\", \"\"]],\n      inputs: {\n        tuiDropdownManual: \"tuiDropdownManual\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiDropdownDriver, tuiAsDriver(TuiDropdownDriver)]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownManual, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiDropdownManual]',\n      providers: [TuiDropdownDriver, tuiAsDriver(TuiDropdownDriver)]\n    }]\n  }], null, {\n    tuiDropdownManual: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @deprecated TODO: remove in v.5 when legacy controls are dropped\n */\nclass TuiDropdownOpenLegacy {\n  constructor() {\n    this.openStateSub = new Subject();\n    this.tuiDropdownOpenChange = this.openStateSub.pipe(distinctUntilChanged());\n  }\n  set tuiDropdownOpen(open) {\n    this.emitOpenChange(open);\n  }\n  emitOpenChange(open) {\n    this.openStateSub.next(open);\n  }\n  static {\n    this.ɵfac = function TuiDropdownOpenLegacy_Factory(t) {\n      return new (t || TuiDropdownOpenLegacy)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownOpenLegacy,\n      selectors: [[\"\", \"tuiDropdownOpen\", \"\", 3, \"tuiDropdown\", \"\"], [\"\", \"tuiDropdownOpenChange\", \"\", 3, \"tuiDropdown\", \"\"]],\n      inputs: {\n        tuiDropdownOpen: \"tuiDropdownOpen\"\n      },\n      outputs: {\n        tuiDropdownOpenChange: \"tuiDropdownOpenChange\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownOpenLegacy, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiDropdownOpen]:not([tuiDropdown]),[tuiDropdownOpenChange]:not([tuiDropdown])'\n    }]\n  }], null, {\n    tuiDropdownOpenChange: [{\n      type: Output\n    }],\n    tuiDropdownOpen: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @deprecated use {@link TuiPopup} directive instead\n */\nclass TuiDropdownPortal {\n  constructor() {\n    this.template = inject(TemplateRef);\n    this.service = inject(TuiDropdownService);\n  }\n  set tuiDropdown(show) {\n    this.viewRef?.destroy();\n    if (show) {\n      this.viewRef = this.service.addTemplate(this.template);\n    }\n  }\n  ngOnDestroy() {\n    this.viewRef?.destroy();\n  }\n  static {\n    this.ɵfac = function TuiDropdownPortal_Factory(t) {\n      return new (t || TuiDropdownPortal)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownPortal,\n      selectors: [[\"ng-template\", \"tuiDropdown\", \"\"]],\n      inputs: {\n        tuiDropdown: \"tuiDropdown\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownPortal, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-template[tuiDropdown]'\n    }]\n  }], null, {\n    tuiDropdown: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiDropdownPositionSided extends TuiPositionAccessor {\n  constructor() {\n    super(...arguments);\n    this.options = inject(TUI_DROPDOWN_OPTIONS);\n    this.viewport = inject(TUI_VIEWPORT);\n    this.vertical = inject(TuiDropdownPosition);\n    this.previous = this.options.direction || 'bottom';\n    this.tuiDropdownSided = '';\n    this.tuiDropdownSidedOffset = 4;\n    this.type = 'dropdown';\n  }\n  getPosition(rect) {\n    if (this.tuiDropdownSided === false) {\n      return this.vertical.getPosition(rect);\n    }\n    const {\n      height,\n      width\n    } = rect;\n    const hostRect = this.vertical.accessor?.getClientRect() ?? EMPTY_CLIENT_RECT;\n    const viewport = this.viewport.getClientRect();\n    const {\n      direction,\n      minHeight,\n      offset\n    } = this.options;\n    const align = this.options.align === 'center' ? 'left' : this.options.align;\n    const available = {\n      top: hostRect.bottom - viewport.top,\n      left: hostRect.left - offset - viewport.left,\n      right: viewport.right - hostRect.right - offset,\n      bottom: viewport.bottom - hostRect.top\n    };\n    const position = {\n      top: hostRect.bottom - height + this.tuiDropdownSidedOffset + 1,\n      left: hostRect.left - width - offset,\n      right: hostRect.right + offset,\n      bottom: hostRect.top - this.tuiDropdownSidedOffset - 1 // 1 for border\n    };\n    const better = available.top > available.bottom ? 'top' : 'bottom';\n    const maxLeft = available.left > available.right ? position.left : position.right;\n    const left = available[align] > width ? position[align] : maxLeft;\n    if (available[this.previous] > minHeight && direction || this.previous === better) {\n      this.vertical.emitDirection(this.previous);\n      return [position[this.previous], left];\n    }\n    this.previous = better;\n    this.vertical.emitDirection(better);\n    return [position[better], left];\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiDropdownPositionSided_BaseFactory;\n      return function TuiDropdownPositionSided_Factory(t) {\n        return (ɵTuiDropdownPositionSided_BaseFactory || (ɵTuiDropdownPositionSided_BaseFactory = i0.ɵɵgetInheritedFactory(TuiDropdownPositionSided)))(t || TuiDropdownPositionSided);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownPositionSided,\n      selectors: [[\"\", \"tuiDropdownSided\", \"\"]],\n      inputs: {\n        tuiDropdownSided: \"tuiDropdownSided\",\n        tuiDropdownSidedOffset: \"tuiDropdownSidedOffset\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiDropdownPosition, tuiAsPositionAccessor(TuiDropdownPositionSided)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownPositionSided, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiDropdownSided]',\n      providers: [TuiDropdownPosition, tuiAsPositionAccessor(TuiDropdownPositionSided)]\n    }]\n  }], null, {\n    tuiDropdownSided: [{\n      type: Input\n    }],\n    tuiDropdownSidedOffset: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiDropdownSelection extends TuiDriver {\n  constructor() {\n    super(subscriber => this.stream$.subscribe(subscriber));\n    this.doc = inject(DOCUMENT);\n    this.vcr = inject(ViewContainerRef);\n    this.dropdown = inject(TuiDropdownDirective);\n    this.el = tuiInjectElement();\n    this.handler$ = new BehaviorSubject(TUI_TRUE_HANDLER);\n    this.stream$ = combineLatest([this.handler$, inject(TUI_SELECTION_STREAM).pipe(map(() => this.getRange()), filter(range => this.isValid(range)), distinctUntilChanged((x, y) => x.startOffset === y.startOffset && x.endOffset === y.endOffset && x.commonAncestorContainer === y.commonAncestorContainer))]).pipe(map(([handler, range]) => {\n      const contained = this.el.contains(range.commonAncestorContainer);\n      this.range = contained && tuiIsTextNode(range.commonAncestorContainer) ? range : this.range;\n      return contained && handler(this.range) || this.inDropdown(range);\n    }));\n    this.range = inject(TUI_RANGE);\n    this.position = 'selection';\n    this.type = 'dropdown';\n  }\n  set tuiDropdownSelection(visible) {\n    if (!tuiIsString(visible)) {\n      this.handler$.next(visible);\n    }\n  }\n  getClientRect() {\n    switch (this.position) {\n      case 'tag':\n        {\n          const {\n            commonAncestorContainer\n          } = this.range;\n          const element = tuiIsElement(commonAncestorContainer) ? commonAncestorContainer : commonAncestorContainer.parentNode;\n          return element && tuiIsElement(element) ? element.getBoundingClientRect() : EMPTY_CLIENT_RECT;\n        }\n      case 'word':\n        return tuiGetWordRange(this.range).getBoundingClientRect();\n      default:\n        return this.range.getBoundingClientRect();\n    }\n  }\n  ngOnDestroy() {\n    if (this.ghost) {\n      this.vcr.element.nativeElement.removeChild(this.ghost);\n    }\n  }\n  getRange() {\n    const active = tuiGetNativeFocused(this.doc);\n    const selection = this.doc.getSelection();\n    const range = active && tuiIsTextfield(active) && this.el.contains(active) ? this.veryVerySadInputFix(active) : selection?.rangeCount && selection.getRangeAt(0) || this.range;\n    return range.cloneRange();\n  }\n  /**\n   * Check if given range is at least partially inside dropdown\n   */\n  inDropdown(range) {\n    const {\n      startContainer,\n      endContainer\n    } = range;\n    const inDropdown = this.boxContains(range.commonAncestorContainer);\n    const hostToDropdown = this.boxContains(endContainer) && this.el.contains(startContainer);\n    const dropdownToHost = this.boxContains(startContainer) && this.el.contains(endContainer);\n    return inDropdown || hostToDropdown || dropdownToHost;\n  }\n  /**\n   * Check if Node is inside dropdown\n   */\n  boxContains(node) {\n    return !!this.dropdown.ref()?.location.nativeElement.contains(node);\n  }\n  /**\n   * Check if range is not inside tui-textfield's DOM elements\n   */\n  isValid(range) {\n    return !this.el.contains(range.commonAncestorContainer) || !this.el.closest('tui-textfield') || range.intersectsNode(this.ghost || this.el);\n  }\n  veryVerySadInputFix(element) {\n    const {\n      ghost = this.initGhost(element)\n    } = this;\n    const {\n      top,\n      left,\n      width,\n      height\n    } = element.getBoundingClientRect();\n    const {\n      selectionStart,\n      selectionEnd,\n      value\n    } = element;\n    const range = this.doc.createRange();\n    const hostRect = this.el.getBoundingClientRect();\n    ghost.style.top = tuiPx(top - hostRect.top);\n    ghost.style.left = tuiPx(left - hostRect.left);\n    ghost.style.width = tuiPx(width);\n    ghost.style.height = tuiPx(height);\n    ghost.textContent = CHAR_ZERO_WIDTH_SPACE + value + CHAR_NO_BREAK_SPACE;\n    range.setStart(ghost.firstChild, selectionStart || 0);\n    range.setEnd(ghost.firstChild, selectionEnd || 0);\n    return range;\n  }\n  /**\n   * Create an invisible DIV styled exactly like input/textarea element inside directive\n   */\n  initGhost(element) {\n    const ghost = this.doc.createElement('div');\n    const {\n      font,\n      letterSpacing,\n      textTransform,\n      padding,\n      borderTop\n    } = getComputedStyle(element);\n    ghost.style.position = 'absolute';\n    ghost.style.pointerEvents = 'none';\n    ghost.style.opacity = '0';\n    ghost.style.whiteSpace = 'pre-wrap';\n    ghost.style.boxSizing = 'border-box';\n    ghost.style.borderTop = borderTop;\n    ghost.style.font = font;\n    ghost.style.letterSpacing = letterSpacing;\n    ghost.style.textTransform = textTransform;\n    ghost.style.padding = padding;\n    this.vcr.element.nativeElement.appendChild(ghost);\n    this.ghost = ghost;\n    return ghost;\n  }\n  static {\n    this.ɵfac = function TuiDropdownSelection_Factory(t) {\n      return new (t || TuiDropdownSelection)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownSelection,\n      selectors: [[\"\", \"tuiDropdownSelection\", \"\"]],\n      inputs: {\n        position: [i0.ɵɵInputFlags.None, \"tuiDropdownSelectionPosition\", \"position\"],\n        tuiDropdownSelection: \"tuiDropdownSelection\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsDriver(TuiDropdownSelection), tuiAsRectAccessor(TuiDropdownSelection)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownSelection, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiDropdownSelection]',\n      providers: [tuiAsDriver(TuiDropdownSelection), tuiAsRectAccessor(TuiDropdownSelection)]\n    }]\n  }], function () {\n    return [];\n  }, {\n    position: [{\n      type: Input,\n      args: ['tuiDropdownSelectionPosition']\n    }],\n    tuiDropdownSelection: [{\n      type: Input\n    }]\n  });\n})();\nconst TuiDropdown = [TuiDropdownOptionsDirective, TuiDropdownDriverDirective, TuiDropdownDirective, TuiDropdownComponent, TuiDropdownOpen, TuiDropdownOpenLegacy, TuiDropdownPortal, TuiDropdownManual, TuiDropdownHover, TuiDropdownContext, TuiDropdownPosition, TuiDropdownPositionSided, TuiDropdownSelection];\nfunction tuiDropdown(value) {\n  return tuiDirectiveBinding(TuiDropdownDirective, 'tuiDropdown', value, {});\n}\nfunction tuiDropdownEnabled(value) {\n  return tuiDirectiveBinding(TuiDropdownOpen, 'tuiDropdownEnabled', value, {});\n}\nfunction tuiDropdownOpen() {\n  const open = tuiDirectiveBinding(TuiDropdownOpen, 'tuiDropdownOpen', false, {});\n  inject(TuiDropdownOpen).tuiDropdownOpenChange.pipe(takeUntilDestroyed()).subscribe(value => open.set(value));\n  return open;\n}\nclass TuiDropdownFixed {\n  constructor() {\n    const override = tuiOverrideOptions$1({\n      limitWidth: 'fixed'\n    }, TUI_DROPDOWN_DEFAULT_OPTIONS);\n    override(inject(TUI_DROPDOWN_OPTIONS, {\n      self: true,\n      optional: true\n    }), null);\n  }\n  static {\n    this.ɵfac = function TuiDropdownFixed_Factory(t) {\n      return new (t || TuiDropdownFixed)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownFixed,\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiDropdownOptionsProvider({})])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownFixed, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      providers: [tuiDropdownOptionsProvider({})]\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nclass TuiDropdownAuto {\n  constructor() {\n    /**\n     * Update directive props with new defaults before inputs are processed\n     * TODO: find better way to override TuiDropdownFixed host directive from parent component\n     */\n    inject(TUI_DROPDOWN_OPTIONS).limitWidth = 'auto';\n  }\n  static {\n    this.ɵfac = function TuiDropdownAuto_Factory(t) {\n      return new (t || TuiDropdownAuto)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDropdownAuto,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdownAuto, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n\n/**\n * Host element for dynamically created portals, for example using {@link TuiDropdownDirective}.\n */\nclass TuiDropdowns extends TuiPortals {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiDropdowns_BaseFactory;\n      return function TuiDropdowns_Factory(t) {\n        return (ɵTuiDropdowns_BaseFactory || (ɵTuiDropdowns_BaseFactory = i0.ɵɵgetInheritedFactory(TuiDropdowns)))(t || TuiDropdowns);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiDropdowns,\n      selectors: [[\"tui-dropdowns\"]],\n      hostAttrs: [2, \"position\", \"absolute\", \"width\", \"100%\", \"top\", \"0\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsPortal(TuiDropdownService)]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"viewContainer\", \"\"]],\n      template: function TuiDropdowns_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, null, 0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDropdowns, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-dropdowns',\n      template: '<ng-container #viewContainer />',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAsPortal(TuiDropdownService)],\n      host: {\n        style: 'position: absolute; width: 100%; top: 0'\n      }\n    }]\n  }], null, null);\n})();\nclass TuiWithDropdownOpen {\n  static {\n    this.ɵfac = function TuiWithDropdownOpen_Factory(t) {\n      return new (t || TuiWithDropdownOpen)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiWithDropdownOpen,\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([{\n        directive: TuiDropdownOpen,\n        inputs: [\"tuiDropdownOpen\", \"open\", \"tuiDropdownEnabled\", \"tuiDropdownEnabled\"],\n        outputs: [\"tuiDropdownOpenChange\", \"openChange\"]\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiWithDropdownOpen, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      hostDirectives: [{\n        directive: TuiDropdownOpen,\n        inputs: ['tuiDropdownOpen: open', 'tuiDropdownEnabled'],\n        outputs: ['tuiDropdownOpenChange: openChange']\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DROPDOWN_COMPONENT, TUI_DROPDOWN_CONTEXT, TUI_DROPDOWN_DEFAULT_OPTIONS, TUI_DROPDOWN_HOVER_DEFAULT_OPTIONS, TUI_DROPDOWN_HOVER_OPTIONS, TUI_DROPDOWN_OPTIONS, TuiDropdown, TuiDropdownAuto, TuiDropdownComponent, TuiDropdownContext, TuiDropdownDirective, TuiDropdownDriver, TuiDropdownDriverDirective, TuiDropdownFixed, TuiDropdownHover, TuiDropdownManual, TuiDropdownOpen, TuiDropdownOpenLegacy, TuiDropdownOptionsDirective, TuiDropdownPortal, TuiDropdownPosition, TuiDropdownPositionSided, TuiDropdownSelection, TuiDropdownService, TuiDropdowns, TuiWithDropdownOpen, tuiDropdown, tuiDropdownEnabled, tuiDropdownHoverOptionsProvider, tuiDropdownOpen, tuiDropdownOptionsProvider };", "map": {"version": 3, "names": ["i0", "Injectable", "Directive", "Optional", "Self", "SkipSelf", "inject", "Input", "EventEmitter", "Output", "ChangeDetectorRef", "INJECTOR", "signal", "TemplateRef", "computed", "Component", "ChangeDetectionStrategy", "ElementRef", "ContentChild", "ViewContainerRef", "takeUntilDestroyed", "toObservable", "EMPTY_CLIENT_RECT", "TUI_TRUE_HANDLER", "CHAR_ZERO_WIDTH_SPACE", "CHAR_NO_BREAK_SPACE", "i1", "TuiActiveZone", "i2", "TuiAnimated", "tuiInjectElement", "tuiGetActualTarget", "tuiPointToClientRect", "tuiIsElement", "tuiIsHTMLElement", "tuiIsElementEditable", "tuiIsTextNode", "tuiIsTextfield", "tui<PERSON><PERSON>", "tuiCreateTokenFromFactory", "tuiCreateToken", "tui<PERSON><PERSON><PERSON>", "tuiPure", "tuiPx", "tuiProvideOptions", "tuiIsString", "tuiDirectiveBinding", "TuiDriverDirective", "TuiPositionAccessor", "tui<PERSON>allbackAccessor", "TuiRectAccessor", "tuiAsRectAccessor", "tuiAsVehicle", "tuiPositionAccessorFor", "tuiRectAccessorFor", "tuiAsDriver", "TuiDriver", "tuiAsPositionAccessor", "TuiScrollbar", "TuiVisualViewportService", "TuiPositionService", "TUI_VIEWPORT", "TUI_DARK_MODE", "TUI_SELECTION_STREAM", "PolymorpheusComponent", "PolymorpheusTemplate", "Polymorpheus<PERSON><PERSON>let", "BehaviorSubject", "Subject", "throttleTime", "<PERSON><PERSON><PERSON><PERSON>", "map", "merge", "filter", "fromEvent", "switchMap", "delay", "startWith", "takeUntil", "distinctUntilChanged", "of", "tap", "share", "combineLatest", "coerce<PERSON><PERSON><PERSON>", "tuiZonefreeScheduler", "tuiIfMap", "tuiCloseWatcher", "tui<PERSON>onefull", "tuiWatch", "tuiTypedFromEvent", "tuiZoneOptimized", "tuiOverrideOptions", "tuiCheckFixedPosition", "tuiGetWordRange", "TuiPortalService", "TuiPortals", "tui<PERSON><PERSON><PERSON><PERSON>", "__decorate", "TUI_IS_TOUCH", "TUI_RANGE", "shouldCall", "DOCUMENT", "i1$1", "TuiObscured", "tuiIsNativeKeyboardFocusable", "tuiGetClosestFocusable", "tuiIsNativeFocusedIn", "tuiGetNativeFocused", "tuiIsEditingKey", "tuiOverrideOptions$1", "_c0", "a0", "$implicit", "TuiDropdownComponent_div_1_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "text_r1", "polymorpheusOutlet", "ɵɵadvance", "ɵɵtextInterpolate1", "_c1", "TuiDropdownDriver", "constructor", "type", "ɵfac", "TuiDropdownDriver_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "TuiDropdownDriverDirective", "arguments", "ɵTuiDropdownDriverDirective_BaseFactory", "TuiDropdownDriverDirective_Factory", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "standalone", "features", "ɵɵInheritDefinitionFeature", "args", "TUI_DROPDOWN_COMPONENT", "TuiDropdownComponent", "TUI_DROPDOWN_CONTEXT", "TuiDropdownService", "ɵTuiDropdownService_BaseFactory", "TuiDropdownService_Factory", "providedIn", "TUI_DROPDOWN_DEFAULT_OPTIONS", "align", "direction", "limitWidth", "maxHeight", "minHeight", "offset", "appearance", "TUI_DROPDOWN_OPTIONS", "tuiDropdownOptionsProvider", "override", "provide", "deps", "TuiDropdownOptionsDirective", "useFactory", "options", "skipSelf", "TuiDropdownOptionsDirective_Factory", "selectors", "inputs", "ɵɵInputFlags", "None", "ɵɵProvidersFeature", "selector", "providers", "TuiDropdownPosition", "viewport", "directionChange", "accessor", "TuiDropdownDirective", "optional", "emitDirection", "emit", "getPosition", "width", "height", "previous", "undefined", "hostRect", "getClientRect", "viewportRect", "top", "bottom", "right", "left", "available", "rectWidth", "Math", "max", "position", "center", "better", "ɵTuiDropdownPosition_BaseFactory", "TuiDropdownPosition_Factory", "outputs", "prototype", "refresh$", "service", "cdr", "drivers", "self", "sub", "pipe", "subscribe", "ref", "changeDetectorRef", "detectChanges", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "el", "component", "_content", "tuiDropdown", "content", "set", "toggle", "x", "ngAfterViewChecked", "next", "ngOnDestroy", "getBoundingClientRect", "show", "add", "remove", "for<PERSON>ach", "driver", "TuiDropdownDirective_Factory", "hostVars", "hostBindings", "TuiDropdownDirective_HostBindings", "ɵɵclassProp", "exportAs", "ɵɵHostDirectivesFeature", "directive", "hostDirectives", "host", "vvs", "styles$", "isConnected", "v", "correct", "getStyles", "context", "darkMode", "theme", "_", "closest", "getAttribute", "close", "ngAfterViewInit", "styles", "Object", "assign", "style", "complete", "y", "parent", "offsetParent", "rect", "above", "below", "round", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "TuiDropdownComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "TuiDropdownComponent_HostBindings", "ɵɵattribute", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiDropdownComponent_Template", "ɵɵtemplate", "ɵɵproperty", "ɵɵpureFunction1", "dependencies", "imports", "changeDetection", "<PERSON><PERSON><PERSON>", "activeZoneFilter", "event", "value", "activeZone", "contains", "TuiDropdownContext", "is<PERSON><PERSON>ch", "currentRect", "userSelect", "closeDropdown", "_event", "onContextMenu", "ɵTuiDropdownContext_BaseFactory", "TuiDropdownContext_Factory", "TuiDropdownContext_HostBindings", "ɵɵlistener", "TuiDropdownContext_pointerdown_zoneless_HostBindingHandler", "$event", "ɵɵresolveDocument", "TuiDropdownContext_contextmenu_capture_zoneless_HostBindingHandler", "TuiDropdownContext_keydown_esc_HostBindingHandler", "TuiDropdownContext_longtap_HostBindingHandler", "detail", "clientX", "clientY", "ɵɵstyleProp", "TUI_DROPDOWN_HOVER_DEFAULT_OPTIONS", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "TUI_DROPDOWN_HOVER_OPTIONS", "tuiDropdownHoverOptionsProvider", "shouldClose", "CloseWatcher", "key", "toLowerCase", "tuiDropdownEnabled", "tuiDropdownOpen", "nextElement<PERSON><PERSON>ling", "TuiDropdownOpen", "obscured", "dropdown", "location", "nativeElement", "tuiDropdownOpenChange", "tuiObscured", "Boolean", "tuiActiveZoneChange", "a", "sync", "open", "update", "ngOnChanges", "drive", "focused", "focus", "preventScroll", "onEsc", "preventDefault", "onClick", "target", "editable", "onArrow", "up", "focusDropdown", "onKeydown", "defaultPrevented", "initial", "dropdownHost", "focusable", "root", "tuiObscuredEnabled", "doc", "ownerDocument", "child", "append<PERSON><PERSON><PERSON>", "createElement", "TuiDropdownOpen_Factory", "contentQueries", "TuiDropdownOpen_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "TuiDropdownOpen_HostBindings", "TuiDropdownOpen_click_HostBindingHandler", "TuiDropdownOpen_keydown_arrowDown_HostBindingHandler", "TuiDropdownOpen_keydown_arrowUp_HostBindingHandler", "TuiDropdownOpen_keydown_zoneless_capture_HostBindingHandler", "TuiDropdownOpen_keydown_zoneless_HostBindingHandler", "TuiDropdownOpen_tuiActiveZoneChange_HostBindingHandler", "ɵɵNgOnChangesFeature", "descendants", "read", "TuiDropdownHover", "subscriber", "stream$", "dropdownExternalRemoval$", "hovered", "e", "relatedTarget", "element", "isHovered", "TuiDropdownHover_Factory", "TuiDropdownHover_ContentQueries", "TuiDropdownHover_HostBindings", "TuiDropdownHover_click_capture_HostBindingHandler", "TuiDropdownManual", "tui<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TuiDropdownManual_Factory", "TuiDropdownOpenLegacy", "openStateSub", "emitOpenChange", "TuiDropdownOpenLegacy_Factory", "TuiDropdownPortal", "viewRef", "destroy", "addTemplate", "TuiDropdownPortal_Factory", "TuiDropdownPositionSided", "vertical", "tuiDropdownSided", "tuiDropdownSidedOffset", "maxLeft", "ɵTuiDropdownPositionSided_BaseFactory", "TuiDropdownPositionSided_Factory", "TuiDropdownSelection", "vcr", "handler$", "getRange", "range", "<PERSON><PERSON><PERSON><PERSON>", "startOffset", "endOffset", "commonAncestorContainer", "handler", "contained", "inDropdown", "tuiDropdownSelection", "visible", "parentNode", "ghost", "<PERSON><PERSON><PERSON><PERSON>", "active", "selection", "getSelection", "veryVerySadInputFix", "rangeCount", "getRangeAt", "cloneRange", "startContainer", "endContainer", "boxContains", "hostToDropdown", "dropdownToHost", "node", "intersectsNode", "initGhost", "selectionStart", "selectionEnd", "createRange", "textContent", "setStart", "<PERSON><PERSON><PERSON><PERSON>", "setEnd", "font", "letterSpacing", "textTransform", "padding", "borderTop", "getComputedStyle", "pointerEvents", "opacity", "whiteSpace", "boxSizing", "TuiDropdownSelection_Factory", "TuiDropdown", "TuiDropdownFixed", "TuiDropdownFixed_Factory", "TuiDropdownAuto", "TuiDropdownAuto_Factory", "TuiDropdowns", "ɵTuiDropdowns_BaseFactory", "TuiDropdowns_Factory", "hostAttrs", "TuiDropdowns_Template", "ɵɵelementContainer", "encapsulation", "OnPush", "TuiWithDropdownOpen", "TuiWithDropdownOpen_Factory"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-directives-dropdown.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Directive, Optional, Self, SkipSelf, inject, Input, EventEmitter, Output, ChangeDetectorRef, INJECTOR, signal, TemplateRef, computed, Component, ChangeDetectionStrategy, ElementRef, ContentChild, ViewContainerRef } from '@angular/core';\nimport { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';\nimport { EMPTY_CLIENT_RECT, TUI_TRUE_HANDLER, CHAR_ZERO_WIDTH_SPACE, CHAR_NO_BREAK_SPACE } from '@taiga-ui/cdk/constants';\nimport * as i1 from '@taiga-ui/cdk/directives/active-zone';\nimport { TuiActiveZone } from '@taiga-ui/cdk/directives/active-zone';\nimport * as i2 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { tuiInjectElement, tuiGetActualTarget, tuiPointToClientRect, tuiIsElement, tuiIsHTMLElement, tuiIsElementEditable, tuiIsTextNode, tuiIsTextfield } from '@taiga-ui/cdk/utils/dom';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { tuiCreateTokenFromFactory, tuiCreateToken, tuiProvide, tuiPure, tuiPx, tuiProvideOptions, tuiIsString, tuiDirectiveBinding } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiDriverDirective, TuiPositionAccessor, tuiFallbackAccessor, TuiRectAccessor, tuiAsRectAccessor, tuiAsVehicle, tuiPositionAccessorFor, tuiRectAccessorFor, tuiAsDriver, TuiDriver, tuiAsPositionAccessor } from '@taiga-ui/core/classes';\nimport { TuiScrollbar } from '@taiga-ui/core/components/scrollbar';\nimport { TuiVisualViewportService, TuiPositionService } from '@taiga-ui/core/services';\nimport { TUI_VIEWPORT, TUI_DARK_MODE, TUI_SELECTION_STREAM } from '@taiga-ui/core/tokens';\nimport { PolymorpheusComponent, PolymorpheusTemplate, PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { BehaviorSubject, Subject, throttleTime, takeWhile, map, merge, filter, fromEvent, switchMap, delay, startWith, takeUntil, distinctUntilChanged, of, tap, share, combineLatest } from 'rxjs';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { tuiZonefreeScheduler, tuiIfMap, tuiCloseWatcher, tuiZonefull, tuiWatch, tuiTypedFromEvent, tuiZoneOptimized } from '@taiga-ui/cdk/observables';\nimport { tuiOverrideOptions, tuiCheckFixedPosition, tuiGetWordRange } from '@taiga-ui/core/utils';\nimport { TuiPortalService, TuiPortals, tuiAsPortal } from '@taiga-ui/cdk/classes';\nimport { __decorate } from 'tslib';\nimport { TUI_IS_TOUCH, TUI_RANGE } from '@taiga-ui/cdk/tokens';\nimport { shouldCall } from '@taiga-ui/event-plugins';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1$1 from '@taiga-ui/cdk/directives/obscured';\nimport { TuiObscured } from '@taiga-ui/cdk/directives/obscured';\nimport { tuiIsNativeKeyboardFocusable, tuiGetClosestFocusable, tuiIsNativeFocusedIn, tuiGetNativeFocused } from '@taiga-ui/cdk/utils/focus';\nimport { tuiIsEditingKey, tuiOverrideOptions as tuiOverrideOptions$1 } from '@taiga-ui/core/utils/miscellaneous';\n\nclass TuiDropdownDriver extends BehaviorSubject {\n    constructor() {\n        super(false);\n        this.type = 'dropdown';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownDriver, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownDriver }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownDriver, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return []; } });\nclass TuiDropdownDriverDirective extends TuiDriverDirective {\n    constructor() {\n        super(...arguments);\n        this.type = 'dropdown';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownDriverDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownDriverDirective, isStandalone: true, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownDriverDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                }]\n        }] });\n\n/**\n * A component to display a dropdown\n */\nconst TUI_DROPDOWN_COMPONENT = tuiCreateTokenFromFactory(() => TuiDropdownComponent);\nconst TUI_DROPDOWN_CONTEXT = tuiCreateToken();\n\nclass TuiDropdownService extends TuiPortalService {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownService, deps: null, target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\n/** Default values for dropdown options */\nconst TUI_DROPDOWN_DEFAULT_OPTIONS = {\n    align: 'left',\n    direction: null,\n    limitWidth: 'auto',\n    maxHeight: 400,\n    minHeight: 80,\n    offset: 4,\n    appearance: '',\n};\n/**\n * Default parameters for dropdown directive\n */\nconst TUI_DROPDOWN_OPTIONS = tuiCreateToken(TUI_DROPDOWN_DEFAULT_OPTIONS);\nconst tuiDropdownOptionsProvider = (override) => ({\n    provide: TUI_DROPDOWN_OPTIONS,\n    deps: [\n        [new Optional(), new Self(), TuiDropdownOptionsDirective],\n        [new Optional(), new SkipSelf(), TUI_DROPDOWN_OPTIONS],\n    ],\n    useFactory: tuiOverrideOptions(override, TUI_DROPDOWN_DEFAULT_OPTIONS),\n});\nclass TuiDropdownOptionsDirective {\n    constructor() {\n        this.options = inject(TUI_DROPDOWN_OPTIONS, { skipSelf: true });\n        this.align = this.options.align;\n        this.appearance = this.options.appearance;\n        this.direction = this.options.direction;\n        this.limitWidth = this.options.limitWidth;\n        this.minHeight = this.options.minHeight;\n        this.maxHeight = this.options.maxHeight;\n        this.offset = this.options.offset;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownOptionsDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownOptionsDirective, isStandalone: true, selector: \"[tuiDropdownAlign], [tuiDropdownAppearance], [tuiDropdownDirection], [tuiDropdownLimitWidth], [tuiDropdownMinHeight], [tuiDropdownMaxHeight], [tuiDropdownOffset]\", inputs: { align: [\"tuiDropdownAlign\", \"align\"], appearance: [\"tuiDropdownAppearance\", \"appearance\"], direction: [\"tuiDropdownDirection\", \"direction\"], limitWidth: [\"tuiDropdownLimitWidth\", \"limitWidth\"], minHeight: [\"tuiDropdownMinHeight\", \"minHeight\"], maxHeight: [\"tuiDropdownMaxHeight\", \"maxHeight\"], offset: [\"tuiDropdownOffset\", \"offset\"] }, providers: [tuiProvide(TUI_DROPDOWN_OPTIONS, TuiDropdownOptionsDirective)], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownOptionsDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiDropdownAlign], [tuiDropdownAppearance], [tuiDropdownDirection], [tuiDropdownLimitWidth], [tuiDropdownMinHeight], [tuiDropdownMaxHeight], [tuiDropdownOffset]',\n                    providers: [tuiProvide(TUI_DROPDOWN_OPTIONS, TuiDropdownOptionsDirective)],\n                }]\n        }], propDecorators: { align: [{\n                type: Input,\n                args: ['tuiDropdownAlign']\n            }], appearance: [{\n                type: Input,\n                args: ['tuiDropdownAppearance']\n            }], direction: [{\n                type: Input,\n                args: ['tuiDropdownDirection']\n            }], limitWidth: [{\n                type: Input,\n                args: ['tuiDropdownLimitWidth']\n            }], minHeight: [{\n                type: Input,\n                args: ['tuiDropdownMinHeight']\n            }], maxHeight: [{\n                type: Input,\n                args: ['tuiDropdownMaxHeight']\n            }], offset: [{\n                type: Input,\n                args: ['tuiDropdownOffset']\n            }] } });\n\nclass TuiDropdownPosition extends TuiPositionAccessor {\n    constructor() {\n        super(...arguments);\n        this.options = inject(TUI_DROPDOWN_OPTIONS);\n        this.viewport = inject(TUI_VIEWPORT);\n        this.directionChange = new EventEmitter();\n        this.type = 'dropdown';\n        this.accessor = tuiFallbackAccessor('dropdown')(inject(TuiRectAccessor), inject(TuiDropdownDirective, { optional: true }));\n    }\n    emitDirection(direction) {\n        this.directionChange.emit(direction);\n    }\n    getPosition({ width, height }) {\n        if (!width && !height) {\n            this.previous = undefined;\n        }\n        const hostRect = this.accessor?.getClientRect() ?? EMPTY_CLIENT_RECT;\n        const viewportRect = this.viewport.getClientRect();\n        const { minHeight, align, direction, offset, limitWidth } = this.options;\n        const viewport = {\n            top: viewportRect.top - offset,\n            bottom: viewportRect.bottom + offset,\n            right: viewportRect.right - offset,\n            left: viewportRect.left + offset,\n        };\n        const previous = this.previous || direction || 'bottom';\n        const available = {\n            top: hostRect.top - 2 * offset - viewport.top,\n            bottom: viewport.bottom - hostRect.bottom - 2 * offset,\n        };\n        const rectWidth = limitWidth === 'fixed' ? hostRect.width : width;\n        const right = Math.max(hostRect.right - rectWidth, offset);\n        const left = hostRect.left + width < viewport.right ? hostRect.left : right;\n        const position = {\n            top: hostRect.top - offset - height,\n            bottom: hostRect.bottom + offset,\n            right: Math.max(viewport.left, right),\n            center: hostRect.left + hostRect.width / 2 + width / 2 < viewport.right\n                ? hostRect.left + hostRect.width / 2 - width / 2\n                : right,\n            left: Math.max(viewport.left, left),\n        };\n        const better = available.top > available.bottom ? 'top' : 'bottom';\n        if ((available[previous] > minHeight && direction) ||\n            available[previous] > height) {\n            this.emitDirection(previous);\n            return [position[previous], position[align]];\n        }\n        this.previous = better;\n        this.emitDirection(better);\n        return [position[better], position[align]];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownPosition, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownPosition, isStandalone: true, outputs: { directionChange: \"tuiDropdownDirectionChange\" }, usesInheritance: true, ngImport: i0 }); }\n}\n__decorate([\n    tuiPure\n], TuiDropdownPosition.prototype, \"emitDirection\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownPosition, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                }]\n        }], propDecorators: { directionChange: [{\n                type: Output,\n                args: ['tuiDropdownDirectionChange']\n            }], emitDirection: [] } });\n\nclass TuiDropdownDirective {\n    constructor() {\n        this.refresh$ = new Subject();\n        this.service = inject(TuiDropdownService);\n        this.cdr = inject(ChangeDetectorRef);\n        // TODO: think of a better solution later\n        this.drivers = coerceArray(inject(TuiDropdownDriver, { self: true, optional: true }));\n        this.sub = this.refresh$\n            .pipe(throttleTime(0, tuiZonefreeScheduler()), takeUntilDestroyed())\n            .subscribe(() => {\n            this.ref()?.changeDetectorRef.detectChanges();\n            this.ref()?.changeDetectorRef.markForCheck();\n        });\n        this.el = tuiInjectElement();\n        this.type = 'dropdown';\n        this.component = new PolymorpheusComponent(inject(TUI_DROPDOWN_COMPONENT), inject(INJECTOR));\n        this.ref = signal(null);\n        // TODO(v5): rename to `content`\n        // eslint-disable-next-line @typescript-eslint/naming-convention\n        this._content = signal(null);\n    }\n    set tuiDropdown(content) {\n        this._content.set(content instanceof TemplateRef\n            ? new PolymorpheusTemplate(content, this.cdr)\n            : content);\n        if (!this._content()) {\n            this.toggle(false);\n        }\n    }\n    get position() {\n        return tuiCheckFixedPosition(this.el) ? 'fixed' : 'absolute';\n    }\n    // TODO(v5): delete\n    get content() {\n        return this._content();\n    }\n    // TODO(v5): delete\n    set content(x) {\n        this._content.set(x);\n    }\n    ngAfterViewChecked() {\n        this.refresh$.next();\n    }\n    ngOnDestroy() {\n        this.toggle(false);\n    }\n    getClientRect() {\n        return this.el.getBoundingClientRect();\n    }\n    toggle(show) {\n        const ref = this.ref();\n        if (show && this._content() && !ref) {\n            this.ref.set(this.service.add(this.component));\n        }\n        else if (!show && ref) {\n            this.ref.set(null);\n            this.service.remove(ref);\n        }\n        this.drivers.forEach((driver) => driver?.next(show));\n        // TODO: Remove in v5, only needed in Angular 16\n        this.cdr.markForCheck();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownDirective, isStandalone: true, selector: \"[tuiDropdown]:not(ng-container):not(ng-template)\", inputs: { tuiDropdown: \"tuiDropdown\" }, host: { properties: { \"class.tui-dropdown-open\": \"ref()\" } }, providers: [\n            tuiAsRectAccessor(TuiDropdownDirective),\n            tuiAsVehicle(TuiDropdownDirective),\n        ], exportAs: [\"tuiDropdown\"], hostDirectives: [{ directive: TuiDropdownDriverDirective }, { directive: TuiDropdownPosition, outputs: [\"tuiDropdownDirectionChange\", \"tuiDropdownDirectionChange\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiDropdown]:not(ng-container):not(ng-template)',\n                    providers: [\n                        tuiAsRectAccessor(TuiDropdownDirective),\n                        tuiAsVehicle(TuiDropdownDirective),\n                    ],\n                    exportAs: 'tuiDropdown',\n                    hostDirectives: [\n                        TuiDropdownDriverDirective,\n                        {\n                            directive: TuiDropdownPosition,\n                            outputs: ['tuiDropdownDirectionChange'],\n                        },\n                    ],\n                    host: {\n                        '[class.tui-dropdown-open]': 'ref()',\n                    },\n                }]\n        }], propDecorators: { tuiDropdown: [{\n                type: Input\n            }] } });\n\n/**\n * @description:\n * This component is used to show template in a portal\n * using default style of white rounded box with a shadow\n */\nclass TuiDropdownComponent {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.accessor = inject(TuiRectAccessor);\n        this.viewport = inject(TUI_VIEWPORT);\n        this.vvs = inject(TuiVisualViewportService);\n        this.styles$ = inject(TuiPositionService).pipe(takeWhile(() => this.directive.el.isConnected &&\n            !!this.directive.el.getBoundingClientRect().height), map((v) => (this.position === 'fixed' ? this.vvs.correct(v) : v)), map(([top, left]) => this.getStyles(left, top)), takeUntilDestroyed());\n        this.options = inject(TUI_DROPDOWN_OPTIONS);\n        this.directive = inject(TuiDropdownDirective);\n        this.context = inject(TUI_DROPDOWN_CONTEXT, { optional: true });\n        this.darkMode = inject(TUI_DARK_MODE);\n        this.position = this.directive.position;\n        this.theme = computed((_ = this.darkMode()) => this.directive.el.closest('[tuiTheme]')?.getAttribute('tuiTheme'));\n        this.close = () => this.directive.toggle(false);\n    }\n    ngAfterViewInit() {\n        this.styles$.subscribe({\n            next: (styles) => Object.assign(this.el.style, styles),\n            complete: () => this.close?.(),\n        });\n    }\n    getStyles(x, y) {\n        const { maxHeight, minHeight, offset, limitWidth } = this.options;\n        const parent = this.el.offsetParent?.getBoundingClientRect() || EMPTY_CLIENT_RECT;\n        const { left = 0, top = 0 } = this.position === 'fixed' ? {} : parent;\n        const rect = this.accessor.getClientRect();\n        const viewport = this.viewport.getClientRect();\n        const above = rect.top - viewport.top - 2 * offset;\n        const below = viewport.top + viewport.height - y - offset;\n        const available = y > rect.bottom ? below : above;\n        const height = this.el.getBoundingClientRect().right <= rect.left || x >= rect.right\n            ? maxHeight\n            : tuiClamp(available, minHeight, maxHeight);\n        y -= top;\n        x -= left;\n        return {\n            position: this.position,\n            top: tuiPx(Math.round(Math.max(y, offset - top))),\n            left: tuiPx(Math.round(x)),\n            maxHeight: tuiPx(Math.round(height)),\n            width: limitWidth === 'fixed' ? tuiPx(Math.round(rect.width)) : '',\n            minWidth: limitWidth === 'min' ? tuiPx(Math.round(rect.width)) : '',\n            maxWidth: tuiPx(Math.round(viewport.width) - 16), // 8px min gap from each side\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownComponent, isStandalone: true, selector: \"tui-dropdown\", host: { properties: { \"attr.data-appearance\": \"options.appearance\", \"attr.tuiTheme\": \"theme()\" } }, providers: [\n            TuiPositionService,\n            tuiPositionAccessorFor('dropdown', TuiDropdownPosition),\n            tuiRectAccessorFor('dropdown', TuiDropdownDirective),\n        ], hostDirectives: [{ directive: i1.TuiActiveZone }, { directive: i2.TuiAnimated }], ngImport: i0, template: \"<tui-scrollbar class=\\\"t-scroll\\\">\\n    <div\\n        *polymorpheusOutlet=\\\"directive._content() as text; context: {$implicit: close}\\\"\\n        class=\\\"t-primitive\\\"\\n    >\\n        {{ text }}\\n    </div>\\n</tui-scrollbar>\\n\", styles: [\":host{position:absolute;display:flex;box-shadow:var(--tui-shadow-medium);color:var(--tui-text-primary);background:var(--tui-background-elevation-3);border-radius:var(--tui-radius-m);overflow:hidden;border:1px solid var(--tui-border-normal);box-sizing:border-box;isolation:isolate;pointer-events:auto;--tui-from: translateY(-1rem)}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide}:host:not([style*=top]){visibility:hidden}.t-scroll{flex-grow:1;max-inline-size:100%;inline-size:-webkit-max-content;inline-size:max-content;overscroll-behavior:none}.t-primitive{padding:1rem}\\n\"], dependencies: [{ kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"component\", type: TuiScrollbar, selector: \"tui-scrollbar\", inputs: [\"hidden\"] }], changeDetection: i0.ChangeDetectionStrategy.Default }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-dropdown', imports: [PolymorpheusOutlet, TuiScrollbar], changeDetection: ChangeDetectionStrategy.Default, providers: [\n                        TuiPositionService,\n                        tuiPositionAccessorFor('dropdown', TuiDropdownPosition),\n                        tuiRectAccessorFor('dropdown', TuiDropdownDirective),\n                    ], hostDirectives: [TuiActiveZone, TuiAnimated], host: {\n                        '[attr.data-appearance]': 'options.appearance',\n                        '[attr.tuiTheme]': 'theme()',\n                    }, template: \"<tui-scrollbar class=\\\"t-scroll\\\">\\n    <div\\n        *polymorpheusOutlet=\\\"directive._content() as text; context: {$implicit: close}\\\"\\n        class=\\\"t-primitive\\\"\\n    >\\n        {{ text }}\\n    </div>\\n</tui-scrollbar>\\n\", styles: [\":host{position:absolute;display:flex;box-shadow:var(--tui-shadow-medium);color:var(--tui-text-primary);background:var(--tui-background-elevation-3);border-radius:var(--tui-radius-m);overflow:hidden;border:1px solid var(--tui-border-normal);box-sizing:border-box;isolation:isolate;pointer-events:auto;--tui-from: translateY(-1rem)}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide}:host:not([style*=top]){visibility:hidden}.t-scroll{flex-grow:1;max-inline-size:100%;inline-size:-webkit-max-content;inline-size:max-content;overscroll-behavior:none}.t-primitive{padding:1rem}\\n\"] }]\n        }] });\n\nfunction activeZoneFilter(event) {\n    return (!event ||\n        (this.driver.value && !this.activeZone.contains(tuiGetActualTarget(event))));\n}\nclass TuiDropdownContext extends TuiRectAccessor {\n    constructor() {\n        super(...arguments);\n        this.isTouch = inject(TUI_IS_TOUCH);\n        this.currentRect = EMPTY_CLIENT_RECT;\n        this.userSelect = computed(() => (this.isTouch() ? 'none' : null));\n        this.activeZone = inject(TuiActiveZone);\n        this.driver = inject(TuiDropdownDriver);\n        this.type = 'dropdown';\n    }\n    getClientRect() {\n        return this.currentRect;\n    }\n    closeDropdown(_event) {\n        this.driver.next(false);\n        this.currentRect = EMPTY_CLIENT_RECT;\n    }\n    onContextMenu(x, y) {\n        this.currentRect = tuiPointToClientRect(x, y);\n        this.driver.next(true);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownContext, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownContext, isStandalone: true, selector: \"[tuiDropdownContext]\", host: { listeners: { \"document:pointerdown.zoneless\": \"closeDropdown($event)\", \"document:contextmenu.capture.zoneless\": \"closeDropdown($event)\", \"document:keydown.esc\": \"closeDropdown()\", \"longtap\": \"onContextMenu($event.detail.clientX, $event.detail.clientY)\" }, properties: { \"style.user-select\": \"userSelect()\", \"style.-webkit-user-select\": \"userSelect()\", \"style.-webkit-touch-callout\": \"userSelect()\" } }, providers: [\n            TuiActiveZone,\n            TuiDropdownDriver,\n            tuiAsDriver(TuiDropdownDriver),\n            tuiAsRectAccessor(TuiDropdownContext),\n        ], usesInheritance: true, ngImport: i0 }); }\n}\n__decorate([\n    shouldCall(activeZoneFilter)\n], TuiDropdownContext.prototype, \"closeDropdown\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownContext, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiDropdownContext]',\n                    providers: [\n                        TuiActiveZone,\n                        TuiDropdownDriver,\n                        tuiAsDriver(TuiDropdownDriver),\n                        tuiAsRectAccessor(TuiDropdownContext),\n                    ],\n                    host: {\n                        '[style.user-select]': 'userSelect()',\n                        '[style.-webkit-user-select]': 'userSelect()',\n                        '[style.-webkit-touch-callout]': 'userSelect()',\n                        '(document:pointerdown.zoneless)': 'closeDropdown($event)',\n                        '(document:contextmenu.capture.zoneless)': 'closeDropdown($event)',\n                        '(document:keydown.esc)': 'closeDropdown()',\n                        '(longtap)': 'onContextMenu($event.detail.clientX, $event.detail.clientY)',\n                    },\n                }]\n        }], propDecorators: { closeDropdown: [] } });\n\n/** Default values for hint options */\nconst TUI_DROPDOWN_HOVER_DEFAULT_OPTIONS = {\n    showDelay: 200,\n    hideDelay: 500,\n};\n/**\n * Default parameters for dropdown hover directive\n */\nconst TUI_DROPDOWN_HOVER_OPTIONS = tuiCreateToken(TUI_DROPDOWN_HOVER_DEFAULT_OPTIONS);\nfunction tuiDropdownHoverOptionsProvider(options) {\n    return tuiProvideOptions(TUI_DROPDOWN_HOVER_OPTIONS, options, TUI_DROPDOWN_HOVER_DEFAULT_OPTIONS);\n}\n\nfunction shouldClose(event) {\n    return (\n    // @ts-ignore\n    typeof CloseWatcher === 'undefined' &&\n        // ?. for auto fill events\n        event.key?.toLowerCase() === 'escape' &&\n        this.tuiDropdownEnabled &&\n        !!this.tuiDropdownOpen &&\n        !this['dropdown']()?.nextElementSibling);\n}\nclass TuiDropdownOpen {\n    constructor() {\n        this.directive = inject(TuiDropdownDirective);\n        this.el = tuiInjectElement();\n        this.obscured = inject(TuiObscured);\n        this.activeZone = inject(TuiActiveZone);\n        this.dropdown = computed(() => this.directive.ref()?.location.nativeElement);\n        this.tuiDropdownEnabled = true;\n        this.tuiDropdownOpen = false;\n        this.tuiDropdownOpenChange = new EventEmitter();\n        // TODO: make it private when all legacy controls will be deleted from @taiga-ui/legacy (5.0)\n        this.driver = inject(TuiDropdownDriver);\n        this.sub = this.driver\n            .pipe(tuiIfMap(() => merge(tuiCloseWatcher(), this.obscured.tuiObscured.pipe(filter(Boolean)), this.activeZone.tuiActiveZoneChange.pipe(filter((a) => !a)), fromEvent(this.el, 'focusin').pipe(filter((event) => !this.host.contains(tuiGetActualTarget(event)) ||\n            !this.directive.ref())))), tuiZonefull(), tuiWatch(), takeUntilDestroyed())\n            .subscribe(() => this.toggle(false));\n        this.sync = this.driver.pipe(takeUntilDestroyed()).subscribe((open) => {\n            if (open !== this.tuiDropdownOpen) {\n                this.update(open);\n            }\n        });\n    }\n    ngOnChanges() {\n        this.drive(!!this.tuiDropdownOpen);\n        this.tuiDropdownOpenChange.emit(!!this.tuiDropdownOpen);\n    }\n    toggle(open) {\n        if (this.focused && !open) {\n            this.host.focus({ preventScroll: true });\n        }\n        this.update(open);\n    }\n    onEsc(event) {\n        event.preventDefault();\n        this.toggle(false);\n    }\n    onClick(target) {\n        if (!this.editable && this.host.contains(target)) {\n            this.update(!this.tuiDropdownOpen);\n        }\n    }\n    onArrow(event, up) {\n        if (!tuiIsElement(event.target) ||\n            !this.host.contains(event.target) ||\n            !this.tuiDropdownEnabled ||\n            !this.directive._content()) {\n            return;\n        }\n        event.preventDefault();\n        this.focusDropdown(up);\n    }\n    onKeydown(event) {\n        const target = tuiGetActualTarget(event);\n        if (!event.defaultPrevented &&\n            tuiIsEditingKey(event.key) &&\n            this.editable &&\n            this.focused &&\n            tuiIsHTMLElement(target) &&\n            !tuiIsElementEditable(target)) {\n            this.host.focus({ preventScroll: true });\n        }\n    }\n    get host() {\n        const initial = this.dropdownHost?.nativeElement || this.el;\n        const focusable = tuiIsNativeKeyboardFocusable(initial)\n            ? initial\n            : tuiGetClosestFocusable({ initial, root: this.el });\n        return this.dropdownHost?.nativeElement || focusable || this.el;\n    }\n    get editable() {\n        return tuiIsElementEditable(this.host);\n    }\n    get focused() {\n        return tuiIsNativeFocusedIn(this.host) || tuiIsNativeFocusedIn(this.dropdown());\n    }\n    update(open) {\n        if (open && !this.tuiDropdownEnabled) {\n            return this.drive();\n        }\n        this.tuiDropdownOpen = open;\n        this.tuiDropdownOpenChange.emit(open);\n        this.drive();\n    }\n    drive(open = !!this.tuiDropdownOpen && this.tuiDropdownEnabled) {\n        this.obscured.tuiObscuredEnabled = open;\n        this.driver.next(open);\n    }\n    focusDropdown(previous) {\n        const root = this.dropdown();\n        if (!root) {\n            this.update(true);\n            return;\n        }\n        const doc = this.el.ownerDocument;\n        const child = root.appendChild(doc.createElement('div'));\n        const initial = previous ? child : root;\n        const focusable = tuiGetClosestFocusable({ initial, previous, root });\n        child.remove();\n        focusable?.focus();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownOpen, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownOpen, isStandalone: true, selector: \"[tuiDropdown][tuiDropdownOpen],[tuiDropdown][tuiDropdownOpenChange]\", inputs: { tuiDropdownEnabled: \"tuiDropdownEnabled\", tuiDropdownOpen: \"tuiDropdownOpen\" }, outputs: { tuiDropdownOpenChange: \"tuiDropdownOpenChange\" }, host: { listeners: { \"click\": \"onClick($event.target)\", \"keydown.arrowDown\": \"onArrow($event, false)\", \"keydown.arrowUp\": \"onArrow($event, true)\", \"document:keydown.zoneless.capture\": \"onEsc($event)\", \"document:keydown.zoneless\": \"onKeydown($event)\", \"tuiActiveZoneChange\": \"0\" } }, providers: [TuiDropdownDriver, tuiAsDriver(TuiDropdownDriver)], queries: [{ propertyName: \"dropdownHost\", first: true, predicate: [\"tuiDropdownHost\"], descendants: true, read: ElementRef }], usesOnChanges: true, hostDirectives: [{ directive: i1$1.TuiObscured }, { directive: i1.TuiActiveZone, inputs: [\"tuiActiveZoneParent\", \"tuiActiveZoneParent\"], outputs: [\"tuiActiveZoneChange\", \"tuiActiveZoneChange\"] }], ngImport: i0 }); }\n}\n__decorate([\n    shouldCall(shouldClose)\n], TuiDropdownOpen.prototype, \"onEsc\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownOpen, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiDropdown][tuiDropdownOpen],[tuiDropdown][tuiDropdownOpenChange]',\n                    providers: [TuiDropdownDriver, tuiAsDriver(TuiDropdownDriver)],\n                    hostDirectives: [\n                        TuiObscured,\n                        {\n                            directive: TuiActiveZone,\n                            inputs: ['tuiActiveZoneParent'],\n                            outputs: ['tuiActiveZoneChange'],\n                        },\n                    ],\n                    host: {\n                        '(click)': 'onClick($event.target)',\n                        '(keydown.arrowDown)': 'onArrow($event, false)',\n                        '(keydown.arrowUp)': 'onArrow($event, true)',\n                        '(document:keydown.zoneless.capture)': 'onEsc($event)',\n                        '(document:keydown.zoneless)': 'onKeydown($event)',\n                        // TODO: Necessary because startWith(false) + distinctUntilChanged() in TuiActiveZone, think of better solution\n                        '(tuiActiveZoneChange)': '0',\n                    },\n                }]\n        }], propDecorators: { dropdownHost: [{\n                type: ContentChild,\n                args: ['tuiDropdownHost', { descendants: true, read: ElementRef }]\n            }], tuiDropdownEnabled: [{\n                type: Input\n            }], tuiDropdownOpen: [{\n                type: Input\n            }], tuiDropdownOpenChange: [{\n                type: Output\n            }], onEsc: [] } });\n\nclass TuiDropdownHover extends TuiDriver {\n    constructor() {\n        super((subscriber) => this.stream$.subscribe(subscriber));\n        this.el = tuiInjectElement();\n        this.doc = inject(DOCUMENT);\n        this.options = inject(TUI_DROPDOWN_HOVER_OPTIONS);\n        this.activeZone = inject(TuiActiveZone);\n        this.open = inject(TuiDropdownOpen, { optional: true });\n        /**\n         * Dropdown can be removed not only via click/touch –\n         * swipe on mobile devices removes dropdown sheet without triggering new mouseover / mouseout events.\n         */\n        this.dropdownExternalRemoval$ = toObservable(inject(TuiDropdownDirective).ref).pipe(filter((x) => !x && this.hovered));\n        this.stream$ = merge(this.dropdownExternalRemoval$.pipe(switchMap(() => tuiTypedFromEvent(this.doc, 'pointerdown').pipe(map(tuiGetActualTarget), delay(this.hideDelay), startWith(null), takeUntil(fromEvent(this.doc, 'mouseover'))))), tuiTypedFromEvent(this.doc, 'mouseover').pipe(map(tuiGetActualTarget)), tuiTypedFromEvent(this.doc, 'mouseout').pipe(map((e) => e.relatedTarget))).pipe(map((element) => tuiIsElement(element) && this.isHovered(element)), distinctUntilChanged(), switchMap((v) => of(v).pipe(delay(v ? this.showDelay : this.hideDelay))), tuiZoneOptimized(), tap((hovered) => {\n            this.hovered = hovered;\n            this.open?.toggle(hovered);\n        }), share());\n        this.showDelay = this.options.showDelay;\n        this.hideDelay = this.options.hideDelay;\n        this.hovered = false;\n        this.type = 'dropdown';\n    }\n    onClick(event) {\n        if (this.hovered && this.open) {\n            event.preventDefault();\n        }\n    }\n    isHovered(element) {\n        const host = this.dropdownHost?.nativeElement || this.el;\n        const hovered = host.contains(element);\n        const child = !this.el.contains(element) && this.activeZone.contains(element);\n        return hovered || child;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownHover, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownHover, isStandalone: true, selector: \"[tuiDropdownHover]\", inputs: { showDelay: [\"tuiDropdownShowDelay\", \"showDelay\"], hideDelay: [\"tuiDropdownHideDelay\", \"hideDelay\"] }, host: { listeners: { \"click.capture\": \"onClick($event)\" } }, providers: [TuiActiveZone, tuiAsDriver(TuiDropdownHover)], queries: [{ propertyName: \"dropdownHost\", first: true, predicate: [\"tuiDropdownHost\"], descendants: true, read: ElementRef }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownHover, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiDropdownHover]',\n                    providers: [TuiActiveZone, tuiAsDriver(TuiDropdownHover)],\n                    host: {\n                        '(click.capture)': 'onClick($event)',\n                    },\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { dropdownHost: [{\n                type: ContentChild,\n                args: ['tuiDropdownHost', { descendants: true, read: ElementRef }]\n            }], showDelay: [{\n                type: Input,\n                args: ['tuiDropdownShowDelay']\n            }], hideDelay: [{\n                type: Input,\n                args: ['tuiDropdownHideDelay']\n            }] } });\n\nclass TuiDropdownManual {\n    constructor() {\n        this.driver = inject(TuiDropdownDriver);\n        this.tuiDropdownManual = false;\n    }\n    ngOnChanges() {\n        this.driver.next(!!this.tuiDropdownManual);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownManual, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownManual, isStandalone: true, selector: \"[tuiDropdownManual]\", inputs: { tuiDropdownManual: \"tuiDropdownManual\" }, providers: [TuiDropdownDriver, tuiAsDriver(TuiDropdownDriver)], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownManual, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiDropdownManual]',\n                    providers: [TuiDropdownDriver, tuiAsDriver(TuiDropdownDriver)],\n                }]\n        }], propDecorators: { tuiDropdownManual: [{\n                type: Input\n            }] } });\n\n/**\n * @deprecated TODO: remove in v.5 when legacy controls are dropped\n */\nclass TuiDropdownOpenLegacy {\n    constructor() {\n        this.openStateSub = new Subject();\n        this.tuiDropdownOpenChange = this.openStateSub.pipe(distinctUntilChanged());\n    }\n    set tuiDropdownOpen(open) {\n        this.emitOpenChange(open);\n    }\n    emitOpenChange(open) {\n        this.openStateSub.next(open);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownOpenLegacy, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownOpenLegacy, isStandalone: true, selector: \"[tuiDropdownOpen]:not([tuiDropdown]),[tuiDropdownOpenChange]:not([tuiDropdown])\", inputs: { tuiDropdownOpen: \"tuiDropdownOpen\" }, outputs: { tuiDropdownOpenChange: \"tuiDropdownOpenChange\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownOpenLegacy, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiDropdownOpen]:not([tuiDropdown]),[tuiDropdownOpenChange]:not([tuiDropdown])',\n                }]\n        }], propDecorators: { tuiDropdownOpenChange: [{\n                type: Output\n            }], tuiDropdownOpen: [{\n                type: Input\n            }] } });\n\n/**\n * @deprecated use {@link TuiPopup} directive instead\n */\nclass TuiDropdownPortal {\n    constructor() {\n        this.template = inject(TemplateRef);\n        this.service = inject(TuiDropdownService);\n    }\n    set tuiDropdown(show) {\n        this.viewRef?.destroy();\n        if (show) {\n            this.viewRef = this.service.addTemplate(this.template);\n        }\n    }\n    ngOnDestroy() {\n        this.viewRef?.destroy();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownPortal, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownPortal, isStandalone: true, selector: \"ng-template[tuiDropdown]\", inputs: { tuiDropdown: \"tuiDropdown\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownPortal, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-template[tuiDropdown]',\n                }]\n        }], propDecorators: { tuiDropdown: [{\n                type: Input\n            }] } });\n\nclass TuiDropdownPositionSided extends TuiPositionAccessor {\n    constructor() {\n        super(...arguments);\n        this.options = inject(TUI_DROPDOWN_OPTIONS);\n        this.viewport = inject(TUI_VIEWPORT);\n        this.vertical = inject(TuiDropdownPosition);\n        this.previous = this.options.direction || 'bottom';\n        this.tuiDropdownSided = '';\n        this.tuiDropdownSidedOffset = 4;\n        this.type = 'dropdown';\n    }\n    getPosition(rect) {\n        if (this.tuiDropdownSided === false) {\n            return this.vertical.getPosition(rect);\n        }\n        const { height, width } = rect;\n        const hostRect = this.vertical.accessor?.getClientRect() ?? EMPTY_CLIENT_RECT;\n        const viewport = this.viewport.getClientRect();\n        const { direction, minHeight, offset } = this.options;\n        const align = this.options.align === 'center' ? 'left' : this.options.align;\n        const available = {\n            top: hostRect.bottom - viewport.top,\n            left: hostRect.left - offset - viewport.left,\n            right: viewport.right - hostRect.right - offset,\n            bottom: viewport.bottom - hostRect.top,\n        };\n        const position = {\n            top: hostRect.bottom - height + this.tuiDropdownSidedOffset + 1,\n            left: hostRect.left - width - offset,\n            right: hostRect.right + offset,\n            bottom: hostRect.top - this.tuiDropdownSidedOffset - 1, // 1 for border\n        };\n        const better = available.top > available.bottom ? 'top' : 'bottom';\n        const maxLeft = available.left > available.right ? position.left : position.right;\n        const left = available[align] > width ? position[align] : maxLeft;\n        if ((available[this.previous] > minHeight && direction) ||\n            this.previous === better) {\n            this.vertical.emitDirection(this.previous);\n            return [position[this.previous], left];\n        }\n        this.previous = better;\n        this.vertical.emitDirection(better);\n        return [position[better], left];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownPositionSided, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownPositionSided, isStandalone: true, selector: \"[tuiDropdownSided]\", inputs: { tuiDropdownSided: \"tuiDropdownSided\", tuiDropdownSidedOffset: \"tuiDropdownSidedOffset\" }, providers: [TuiDropdownPosition, tuiAsPositionAccessor(TuiDropdownPositionSided)], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownPositionSided, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiDropdownSided]',\n                    providers: [TuiDropdownPosition, tuiAsPositionAccessor(TuiDropdownPositionSided)],\n                }]\n        }], propDecorators: { tuiDropdownSided: [{\n                type: Input\n            }], tuiDropdownSidedOffset: [{\n                type: Input\n            }] } });\n\nclass TuiDropdownSelection extends TuiDriver {\n    constructor() {\n        super((subscriber) => this.stream$.subscribe(subscriber));\n        this.doc = inject(DOCUMENT);\n        this.vcr = inject(ViewContainerRef);\n        this.dropdown = inject(TuiDropdownDirective);\n        this.el = tuiInjectElement();\n        this.handler$ = new BehaviorSubject(TUI_TRUE_HANDLER);\n        this.stream$ = combineLatest([\n            this.handler$,\n            inject(TUI_SELECTION_STREAM).pipe(map(() => this.getRange()), filter((range) => this.isValid(range)), distinctUntilChanged((x, y) => x.startOffset === y.startOffset &&\n                x.endOffset === y.endOffset &&\n                x.commonAncestorContainer === y.commonAncestorContainer)),\n        ]).pipe(map(([handler, range]) => {\n            const contained = this.el.contains(range.commonAncestorContainer);\n            this.range =\n                contained && tuiIsTextNode(range.commonAncestorContainer)\n                    ? range\n                    : this.range;\n            return (contained && handler(this.range)) || this.inDropdown(range);\n        }));\n        this.range = inject(TUI_RANGE);\n        this.position = 'selection';\n        this.type = 'dropdown';\n    }\n    set tuiDropdownSelection(visible) {\n        if (!tuiIsString(visible)) {\n            this.handler$.next(visible);\n        }\n    }\n    getClientRect() {\n        switch (this.position) {\n            case 'tag': {\n                const { commonAncestorContainer } = this.range;\n                const element = tuiIsElement(commonAncestorContainer)\n                    ? commonAncestorContainer\n                    : commonAncestorContainer.parentNode;\n                return element && tuiIsElement(element)\n                    ? element.getBoundingClientRect()\n                    : EMPTY_CLIENT_RECT;\n            }\n            case 'word':\n                return tuiGetWordRange(this.range).getBoundingClientRect();\n            default:\n                return this.range.getBoundingClientRect();\n        }\n    }\n    ngOnDestroy() {\n        if (this.ghost) {\n            this.vcr.element.nativeElement.removeChild(this.ghost);\n        }\n    }\n    getRange() {\n        const active = tuiGetNativeFocused(this.doc);\n        const selection = this.doc.getSelection();\n        const range = active && tuiIsTextfield(active) && this.el.contains(active)\n            ? this.veryVerySadInputFix(active)\n            : (selection?.rangeCount && selection.getRangeAt(0)) || this.range;\n        return range.cloneRange();\n    }\n    /**\n     * Check if given range is at least partially inside dropdown\n     */\n    inDropdown(range) {\n        const { startContainer, endContainer } = range;\n        const inDropdown = this.boxContains(range.commonAncestorContainer);\n        const hostToDropdown = this.boxContains(endContainer) && this.el.contains(startContainer);\n        const dropdownToHost = this.boxContains(startContainer) && this.el.contains(endContainer);\n        return inDropdown || hostToDropdown || dropdownToHost;\n    }\n    /**\n     * Check if Node is inside dropdown\n     */\n    boxContains(node) {\n        return !!this.dropdown.ref()?.location.nativeElement.contains(node);\n    }\n    /**\n     * Check if range is not inside tui-textfield's DOM elements\n     */\n    isValid(range) {\n        return (!this.el.contains(range.commonAncestorContainer) ||\n            !this.el.closest('tui-textfield') ||\n            range.intersectsNode(this.ghost || this.el));\n    }\n    veryVerySadInputFix(element) {\n        const { ghost = this.initGhost(element) } = this;\n        const { top, left, width, height } = element.getBoundingClientRect();\n        const { selectionStart, selectionEnd, value } = element;\n        const range = this.doc.createRange();\n        const hostRect = this.el.getBoundingClientRect();\n        ghost.style.top = tuiPx(top - hostRect.top);\n        ghost.style.left = tuiPx(left - hostRect.left);\n        ghost.style.width = tuiPx(width);\n        ghost.style.height = tuiPx(height);\n        ghost.textContent = CHAR_ZERO_WIDTH_SPACE + value + CHAR_NO_BREAK_SPACE;\n        range.setStart(ghost.firstChild, selectionStart || 0);\n        range.setEnd(ghost.firstChild, selectionEnd || 0);\n        return range;\n    }\n    /**\n     * Create an invisible DIV styled exactly like input/textarea element inside directive\n     */\n    initGhost(element) {\n        const ghost = this.doc.createElement('div');\n        const { font, letterSpacing, textTransform, padding, borderTop } = getComputedStyle(element);\n        ghost.style.position = 'absolute';\n        ghost.style.pointerEvents = 'none';\n        ghost.style.opacity = '0';\n        ghost.style.whiteSpace = 'pre-wrap';\n        ghost.style.boxSizing = 'border-box';\n        ghost.style.borderTop = borderTop;\n        ghost.style.font = font;\n        ghost.style.letterSpacing = letterSpacing;\n        ghost.style.textTransform = textTransform;\n        ghost.style.padding = padding;\n        this.vcr.element.nativeElement.appendChild(ghost);\n        this.ghost = ghost;\n        return ghost;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownSelection, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownSelection, isStandalone: true, selector: \"[tuiDropdownSelection]\", inputs: { position: [\"tuiDropdownSelectionPosition\", \"position\"], tuiDropdownSelection: \"tuiDropdownSelection\" }, providers: [\n            tuiAsDriver(TuiDropdownSelection),\n            tuiAsRectAccessor(TuiDropdownSelection),\n        ], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownSelection, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiDropdownSelection]',\n                    providers: [\n                        tuiAsDriver(TuiDropdownSelection),\n                        tuiAsRectAccessor(TuiDropdownSelection),\n                    ],\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { position: [{\n                type: Input,\n                args: ['tuiDropdownSelectionPosition']\n            }], tuiDropdownSelection: [{\n                type: Input\n            }] } });\n\nconst TuiDropdown = [\n    TuiDropdownOptionsDirective,\n    TuiDropdownDriverDirective,\n    TuiDropdownDirective,\n    TuiDropdownComponent,\n    TuiDropdownOpen,\n    TuiDropdownOpenLegacy,\n    TuiDropdownPortal,\n    TuiDropdownManual,\n    TuiDropdownHover,\n    TuiDropdownContext,\n    TuiDropdownPosition,\n    TuiDropdownPositionSided,\n    TuiDropdownSelection,\n];\n\nfunction tuiDropdown(value) {\n    return tuiDirectiveBinding(TuiDropdownDirective, 'tuiDropdown', value, {});\n}\nfunction tuiDropdownEnabled(value) {\n    return tuiDirectiveBinding(TuiDropdownOpen, 'tuiDropdownEnabled', value, {});\n}\nfunction tuiDropdownOpen() {\n    const open = tuiDirectiveBinding(TuiDropdownOpen, 'tuiDropdownOpen', false, {});\n    inject(TuiDropdownOpen)\n        .tuiDropdownOpenChange.pipe(takeUntilDestroyed())\n        .subscribe((value) => open.set(value));\n    return open;\n}\n\nclass TuiDropdownFixed {\n    constructor() {\n        const override = tuiOverrideOptions$1({ limitWidth: 'fixed' }, TUI_DROPDOWN_DEFAULT_OPTIONS);\n        override(inject(TUI_DROPDOWN_OPTIONS, { self: true, optional: true }), null);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownFixed, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownFixed, isStandalone: true, providers: [tuiDropdownOptionsProvider({})], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownFixed, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    providers: [tuiDropdownOptionsProvider({})],\n                }]\n        }], ctorParameters: function () { return []; } });\nclass TuiDropdownAuto {\n    constructor() {\n        /**\n         * Update directive props with new defaults before inputs are processed\n         * TODO: find better way to override TuiDropdownFixed host directive from parent component\n         */\n        inject(TUI_DROPDOWN_OPTIONS).limitWidth = 'auto';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownAuto, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdownAuto, isStandalone: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdownAuto, decorators: [{\n            type: Directive,\n            args: [{ standalone: true }]\n        }], ctorParameters: function () { return []; } });\n\n/**\n * Host element for dynamically created portals, for example using {@link TuiDropdownDirective}.\n */\nclass TuiDropdowns extends TuiPortals {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdowns, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDropdowns, isStandalone: true, selector: \"tui-dropdowns\", host: { styleAttribute: \"position: absolute; width: 100%; top: 0\" }, providers: [tuiAsPortal(TuiDropdownService)], usesInheritance: true, ngImport: i0, template: '<ng-container #viewContainer />', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDropdowns, decorators: [{\n            type: Component,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-dropdowns',\n                    template: '<ng-container #viewContainer />',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    providers: [tuiAsPortal(TuiDropdownService)],\n                    host: { style: 'position: absolute; width: 100%; top: 0' },\n                }]\n        }] });\n\nclass TuiWithDropdownOpen {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiWithDropdownOpen, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiWithDropdownOpen, isStandalone: true, hostDirectives: [{ directive: TuiDropdownOpen, inputs: [\"tuiDropdownOpen\", \"open\", \"tuiDropdownEnabled\", \"tuiDropdownEnabled\"], outputs: [\"tuiDropdownOpenChange\", \"openChange\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiWithDropdownOpen, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    hostDirectives: [\n                        {\n                            directive: TuiDropdownOpen,\n                            inputs: ['tuiDropdownOpen: open', 'tuiDropdownEnabled'],\n                            outputs: ['tuiDropdownOpenChange: openChange'],\n                        },\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DROPDOWN_COMPONENT, TUI_DROPDOWN_CONTEXT, TUI_DROPDOWN_DEFAULT_OPTIONS, TUI_DROPDOWN_HOVER_DEFAULT_OPTIONS, TUI_DROPDOWN_HOVER_OPTIONS, TUI_DROPDOWN_OPTIONS, TuiDropdown, TuiDropdownAuto, TuiDropdownComponent, TuiDropdownContext, TuiDropdownDirective, TuiDropdownDriver, TuiDropdownDriverDirective, TuiDropdownFixed, TuiDropdownHover, TuiDropdownManual, TuiDropdownOpen, TuiDropdownOpenLegacy, TuiDropdownOptionsDirective, TuiDropdownPortal, TuiDropdownPosition, TuiDropdownPositionSided, TuiDropdownSelection, TuiDropdownService, TuiDropdowns, TuiWithDropdownOpen, tuiDropdown, tuiDropdownEnabled, tuiDropdownHoverOptionsProvider, tuiDropdownOpen, tuiDropdownOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,YAAY,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,UAAU,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,eAAe;AAChQ,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,4BAA4B;AAC7E,SAASC,iBAAiB,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,mBAAmB,QAAQ,yBAAyB;AACzH,OAAO,KAAKC,EAAE,MAAM,sCAAsC;AAC1D,SAASC,aAAa,QAAQ,sCAAsC;AACpE,OAAO,KAAKC,EAAE,MAAM,mCAAmC;AACvD,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,gBAAgB,EAAEC,kBAAkB,EAAEC,oBAAoB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,cAAc,QAAQ,yBAAyB;AACzL,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,yBAAyB,EAAEC,cAAc,EAAEC,UAAU,EAAEC,OAAO,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,mCAAmC;AAC9K,SAASC,kBAAkB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,sBAAsB,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,qBAAqB,QAAQ,wBAAwB;AAClP,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,wBAAwB,EAAEC,kBAAkB,QAAQ,yBAAyB;AACtF,SAASC,YAAY,EAAEC,aAAa,EAAEC,oBAAoB,QAAQ,uBAAuB;AACzF,SAASC,qBAAqB,EAAEC,oBAAoB,EAAEC,kBAAkB,QAAQ,wBAAwB;AACxG,SAASC,eAAe,EAAEC,OAAO,EAAEC,YAAY,EAAEC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,oBAAoB,EAAEC,EAAE,EAAEC,GAAG,EAAEC,KAAK,EAAEC,aAAa,QAAQ,MAAM;AACpM,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,oBAAoB,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,gBAAgB,QAAQ,2BAA2B;AACvJ,SAASC,kBAAkB,EAAEC,qBAAqB,EAAEC,eAAe,QAAQ,sBAAsB;AACjG,SAASC,gBAAgB,EAAEC,UAAU,EAAEC,WAAW,QAAQ,uBAAuB;AACjF,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,YAAY,EAAEC,SAAS,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,IAAI,MAAM,mCAAmC;AACzD,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,4BAA4B,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,mBAAmB,QAAQ,2BAA2B;AAC3I,SAASC,eAAe,EAAEjB,kBAAkB,IAAIkB,oBAAoB,QAAQ,oCAAoC;AAAC,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,SAAAE,oCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAOZnH,EAAE,CAAAqH,cAAA,YAiU2L,CAAC;IAjU9LrH,EAAE,CAAAsH,MAAA,EAiUqN,CAAC;IAjUxNtH,EAAE,CAAAuH,YAAA,CAiU2N,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAjU9NzH,EAAE,CAAA0H,SAAA,CAiUqN,CAAC;IAjUxN1H,EAAE,CAAA2H,kBAAA,MAAAH,OAAA,KAiUqN,CAAC;EAAA;AAAA;AAAA,MAAAI,GAAA;AAtU7T,MAAMC,iBAAiB,SAAS1D,eAAe,CAAC;EAC5C2D,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,KAAK,CAAC;IACZ,IAAI,CAACC,IAAI,GAAG,UAAU;EAC1B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,0BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFL,iBAAiB;IAAA,CAAoD;EAAE;EAClL;IAAS,IAAI,CAACM,KAAK,kBAD8EnI,EAAE,CAAAoI,kBAAA;MAAAC,KAAA,EACYR,iBAAiB;MAAAS,OAAA,EAAjBT,iBAAiB,CAAAG;IAAA,EAAG;EAAE;AACzI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAHqGvI,EAAE,CAAAwI,iBAAA,CAGXX,iBAAiB,EAAc,CAAC;IAChHE,IAAI,EAAE9H;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AACtD,MAAMwI,0BAA0B,SAAS1F,kBAAkB,CAAC;EACxD+E,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGY,SAAS,CAAC;IACnB,IAAI,CAACX,IAAI,GAAG,UAAU;EAC1B;EACA;IAAS,IAAI,CAACC,IAAI;MAAA,IAAAW,uCAAA;MAAA,gBAAAC,mCAAAV,CAAA;QAAA,QAAAS,uCAAA,KAAAA,uCAAA,GAX+E3I,EAAE,CAAA6I,qBAAA,CAWQJ,0BAA0B,IAAAP,CAAA,IAA1BO,0BAA0B;MAAA;IAAA,IAAqD;EAAE;EAC5L;IAAS,IAAI,CAACK,IAAI,kBAZ+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EAYJU,0BAA0B;MAAAO,UAAA;MAAAC,QAAA,GAZxBjJ,EAAE,CAAAkJ,0BAAA;IAAA,EAYkF;EAAE;AAC3L;AACA;EAAA,QAAAX,SAAA,oBAAAA,SAAA,KAdqGvI,EAAE,CAAAwI,iBAAA,CAcXC,0BAA0B,EAAc,CAAC;IACzHV,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAMI,sBAAsB,GAAG7G,yBAAyB,CAAC,MAAM8G,oBAAoB,CAAC;AACpF,MAAMC,oBAAoB,GAAG9G,cAAc,CAAC,CAAC;AAE7C,MAAM+G,kBAAkB,SAASxD,gBAAgB,CAAC;EAC9C;IAAS,IAAI,CAACiC,IAAI;MAAA,IAAAwB,+BAAA;MAAA,gBAAAC,2BAAAvB,CAAA;QAAA,QAAAsB,+BAAA,KAAAA,+BAAA,GA5B+ExJ,EAAE,CAAA6I,qBAAA,CA4BQU,kBAAkB,IAAArB,CAAA,IAAlBqB,kBAAkB;MAAA;IAAA,IAAsD;EAAE;EACrL;IAAS,IAAI,CAACpB,KAAK,kBA7B8EnI,EAAE,CAAAoI,kBAAA;MAAAC,KAAA,EA6BYkB,kBAAkB;MAAAjB,OAAA,EAAlBiB,kBAAkB,CAAAvB,IAAA;MAAA0B,UAAA,EAAc;IAAM,EAAG;EAAE;AAC9J;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KA/BqGvI,EAAE,CAAAwI,iBAAA,CA+BXe,kBAAkB,EAAc,CAAC;IACjHxB,IAAI,EAAE9H,UAAU;IAChBkJ,IAAI,EAAE,CAAC;MACCO,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMC,4BAA4B,GAAG;EACjCC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,MAAM;EAClBC,SAAS,EAAE,GAAG;EACdC,SAAS,EAAE,EAAE;EACbC,MAAM,EAAE,CAAC;EACTC,UAAU,EAAE;AAChB,CAAC;AACD;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG3H,cAAc,CAACmH,4BAA4B,CAAC;AACzE,MAAMS,0BAA0B,GAAIC,QAAQ,KAAM;EAC9CC,OAAO,EAAEH,oBAAoB;EAC7BI,IAAI,EAAE,CACF,CAAC,IAAIpK,QAAQ,CAAC,CAAC,EAAE,IAAIC,IAAI,CAAC,CAAC,EAAEoK,2BAA2B,CAAC,EACzD,CAAC,IAAIrK,QAAQ,CAAC,CAAC,EAAE,IAAIE,QAAQ,CAAC,CAAC,EAAE8J,oBAAoB,CAAC,CACzD;EACDM,UAAU,EAAE7E,kBAAkB,CAACyE,QAAQ,EAAEV,4BAA4B;AACzE,CAAC,CAAC;AACF,MAAMa,2BAA2B,CAAC;EAC9B1C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4C,OAAO,GAAGpK,MAAM,CAAC6J,oBAAoB,EAAE;MAAEQ,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC/D,IAAI,CAACf,KAAK,GAAG,IAAI,CAACc,OAAO,CAACd,KAAK;IAC/B,IAAI,CAACM,UAAU,GAAG,IAAI,CAACQ,OAAO,CAACR,UAAU;IACzC,IAAI,CAACL,SAAS,GAAG,IAAI,CAACa,OAAO,CAACb,SAAS;IACvC,IAAI,CAACC,UAAU,GAAG,IAAI,CAACY,OAAO,CAACZ,UAAU;IACzC,IAAI,CAACE,SAAS,GAAG,IAAI,CAACU,OAAO,CAACV,SAAS;IACvC,IAAI,CAACD,SAAS,GAAG,IAAI,CAACW,OAAO,CAACX,SAAS;IACvC,IAAI,CAACE,MAAM,GAAG,IAAI,CAACS,OAAO,CAACT,MAAM;EACrC;EACA;IAAS,IAAI,CAACjC,IAAI,YAAA4C,oCAAA1C,CAAA;MAAA,YAAAA,CAAA,IAAyFsC,2BAA2B;IAAA,CAAmD;EAAE;EAC3L;IAAS,IAAI,CAAC1B,IAAI,kBAxE+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EAwEJyC,2BAA2B;MAAAK,SAAA;MAAAC,MAAA;QAAAlB,KAAA,GAxEzB5J,EAAE,CAAA+K,YAAA,CAAAC,IAAA;QAAAd,UAAA,GAAFlK,EAAE,CAAA+K,YAAA,CAAAC,IAAA;QAAAnB,SAAA,GAAF7J,EAAE,CAAA+K,YAAA,CAAAC,IAAA;QAAAlB,UAAA,GAAF9J,EAAE,CAAA+K,YAAA,CAAAC,IAAA;QAAAhB,SAAA,GAAFhK,EAAE,CAAA+K,YAAA,CAAAC,IAAA;QAAAjB,SAAA,GAAF/J,EAAE,CAAA+K,YAAA,CAAAC,IAAA;QAAAf,MAAA,GAAFjK,EAAE,CAAA+K,YAAA,CAAAC,IAAA;MAAA;MAAAhC,UAAA;MAAAC,QAAA,GAAFjJ,EAAE,CAAAiL,kBAAA,CAwEkkB,CAACxI,UAAU,CAAC0H,oBAAoB,EAAEK,2BAA2B,CAAC,CAAC;IAAA,EAAiB;EAAE;AAC3vB;AACA;EAAA,QAAAjC,SAAA,oBAAAA,SAAA,KA1EqGvI,EAAE,CAAAwI,iBAAA,CA0EXgC,2BAA2B,EAAc,CAAC;IAC1HzC,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBkC,QAAQ,EAAE,mKAAmK;MAC7KC,SAAS,EAAE,CAAC1I,UAAU,CAAC0H,oBAAoB,EAAEK,2BAA2B,CAAC;IAC7E,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEZ,KAAK,EAAE,CAAC;MACtB7B,IAAI,EAAExH,KAAK;MACX4I,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEe,UAAU,EAAE,CAAC;MACbnC,IAAI,EAAExH,KAAK;MACX4I,IAAI,EAAE,CAAC,uBAAuB;IAClC,CAAC,CAAC;IAAEU,SAAS,EAAE,CAAC;MACZ9B,IAAI,EAAExH,KAAK;MACX4I,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC,CAAC;IAAEW,UAAU,EAAE,CAAC;MACb/B,IAAI,EAAExH,KAAK;MACX4I,IAAI,EAAE,CAAC,uBAAuB;IAClC,CAAC,CAAC;IAAEa,SAAS,EAAE,CAAC;MACZjC,IAAI,EAAExH,KAAK;MACX4I,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC,CAAC;IAAEY,SAAS,EAAE,CAAC;MACZhC,IAAI,EAAExH,KAAK;MACX4I,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC,CAAC;IAAEc,MAAM,EAAE,CAAC;MACTlC,IAAI,EAAExH,KAAK;MACX4I,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiC,mBAAmB,SAASpI,mBAAmB,CAAC;EAClD8E,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGY,SAAS,CAAC;IACnB,IAAI,CAACgC,OAAO,GAAGpK,MAAM,CAAC6J,oBAAoB,CAAC;IAC3C,IAAI,CAACkB,QAAQ,GAAG/K,MAAM,CAACuD,YAAY,CAAC;IACpC,IAAI,CAACyH,eAAe,GAAG,IAAI9K,YAAY,CAAC,CAAC;IACzC,IAAI,CAACuH,IAAI,GAAG,UAAU;IACtB,IAAI,CAACwD,QAAQ,GAAGtI,mBAAmB,CAAC,UAAU,CAAC,CAAC3C,MAAM,CAAC4C,eAAe,CAAC,EAAE5C,MAAM,CAACkL,oBAAoB,EAAE;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;EAC9H;EACAC,aAAaA,CAAC7B,SAAS,EAAE;IACrB,IAAI,CAACyB,eAAe,CAACK,IAAI,CAAC9B,SAAS,CAAC;EACxC;EACA+B,WAAWA,CAAC;IAAEC,KAAK;IAAEC;EAAO,CAAC,EAAE;IAC3B,IAAI,CAACD,KAAK,IAAI,CAACC,MAAM,EAAE;MACnB,IAAI,CAACC,QAAQ,GAAGC,SAAS;IAC7B;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACV,QAAQ,EAAEW,aAAa,CAAC,CAAC,IAAI5K,iBAAiB;IACpE,MAAM6K,YAAY,GAAG,IAAI,CAACd,QAAQ,CAACa,aAAa,CAAC,CAAC;IAClD,MAAM;MAAElC,SAAS;MAAEJ,KAAK;MAAEC,SAAS;MAAEI,MAAM;MAAEH;IAAW,CAAC,GAAG,IAAI,CAACY,OAAO;IACxE,MAAMW,QAAQ,GAAG;MACbe,GAAG,EAAED,YAAY,CAACC,GAAG,GAAGnC,MAAM;MAC9BoC,MAAM,EAAEF,YAAY,CAACE,MAAM,GAAGpC,MAAM;MACpCqC,KAAK,EAAEH,YAAY,CAACG,KAAK,GAAGrC,MAAM;MAClCsC,IAAI,EAAEJ,YAAY,CAACI,IAAI,GAAGtC;IAC9B,CAAC;IACD,MAAM8B,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAIlC,SAAS,IAAI,QAAQ;IACvD,MAAM2C,SAAS,GAAG;MACdJ,GAAG,EAAEH,QAAQ,CAACG,GAAG,GAAG,CAAC,GAAGnC,MAAM,GAAGoB,QAAQ,CAACe,GAAG;MAC7CC,MAAM,EAAEhB,QAAQ,CAACgB,MAAM,GAAGJ,QAAQ,CAACI,MAAM,GAAG,CAAC,GAAGpC;IACpD,CAAC;IACD,MAAMwC,SAAS,GAAG3C,UAAU,KAAK,OAAO,GAAGmC,QAAQ,CAACJ,KAAK,GAAGA,KAAK;IACjE,MAAMS,KAAK,GAAGI,IAAI,CAACC,GAAG,CAACV,QAAQ,CAACK,KAAK,GAAGG,SAAS,EAAExC,MAAM,CAAC;IAC1D,MAAMsC,IAAI,GAAGN,QAAQ,CAACM,IAAI,GAAGV,KAAK,GAAGR,QAAQ,CAACiB,KAAK,GAAGL,QAAQ,CAACM,IAAI,GAAGD,KAAK;IAC3E,MAAMM,QAAQ,GAAG;MACbR,GAAG,EAAEH,QAAQ,CAACG,GAAG,GAAGnC,MAAM,GAAG6B,MAAM;MACnCO,MAAM,EAAEJ,QAAQ,CAACI,MAAM,GAAGpC,MAAM;MAChCqC,KAAK,EAAEI,IAAI,CAACC,GAAG,CAACtB,QAAQ,CAACkB,IAAI,EAAED,KAAK,CAAC;MACrCO,MAAM,EAAEZ,QAAQ,CAACM,IAAI,GAAGN,QAAQ,CAACJ,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAGR,QAAQ,CAACiB,KAAK,GACjEL,QAAQ,CAACM,IAAI,GAAGN,QAAQ,CAACJ,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAC9CS,KAAK;MACXC,IAAI,EAAEG,IAAI,CAACC,GAAG,CAACtB,QAAQ,CAACkB,IAAI,EAAEA,IAAI;IACtC,CAAC;IACD,MAAMO,MAAM,GAAGN,SAAS,CAACJ,GAAG,GAAGI,SAAS,CAACH,MAAM,GAAG,KAAK,GAAG,QAAQ;IAClE,IAAKG,SAAS,CAACT,QAAQ,CAAC,GAAG/B,SAAS,IAAIH,SAAS,IAC7C2C,SAAS,CAACT,QAAQ,CAAC,GAAGD,MAAM,EAAE;MAC9B,IAAI,CAACJ,aAAa,CAACK,QAAQ,CAAC;MAC5B,OAAO,CAACa,QAAQ,CAACb,QAAQ,CAAC,EAAEa,QAAQ,CAAChD,KAAK,CAAC,CAAC;IAChD;IACA,IAAI,CAACmC,QAAQ,GAAGe,MAAM;IACtB,IAAI,CAACpB,aAAa,CAACoB,MAAM,CAAC;IAC1B,OAAO,CAACF,QAAQ,CAACE,MAAM,CAAC,EAAEF,QAAQ,CAAChD,KAAK,CAAC,CAAC;EAC9C;EACA;IAAS,IAAI,CAAC5B,IAAI;MAAA,IAAA+E,gCAAA;MAAA,gBAAAC,4BAAA9E,CAAA;QAAA,QAAA6E,gCAAA,KAAAA,gCAAA,GA5J+E/M,EAAE,CAAA6I,qBAAA,CA4JQuC,mBAAmB,IAAAlD,CAAA,IAAnBkD,mBAAmB;MAAA;IAAA,IAAqD;EAAE;EACrL;IAAS,IAAI,CAACtC,IAAI,kBA7J+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EA6JJqD,mBAAmB;MAAA6B,OAAA;QAAA3B,eAAA;MAAA;MAAAtC,UAAA;MAAAC,QAAA,GA7JjBjJ,EAAE,CAAAkJ,0BAAA;IAAA,EA6JuI;EAAE;AAChP;AACAhD,UAAU,CAAC,CACPxD,OAAO,CACV,EAAE0I,mBAAmB,CAAC8B,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC;AACxD;EAAA,QAAA3E,SAAA,oBAAAA,SAAA,KAlKqGvI,EAAE,CAAAwI,iBAAA,CAkKX4C,mBAAmB,EAAc,CAAC;IAClHrD,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEsC,eAAe,EAAE,CAAC;MAChCvD,IAAI,EAAEtH,MAAM;MACZ0I,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEuC,aAAa,EAAE;EAAG,CAAC;AAAA;AAEnC,MAAMF,oBAAoB,CAAC;EACvB1D,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqF,QAAQ,GAAG,IAAI/I,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACgJ,OAAO,GAAG9M,MAAM,CAACiJ,kBAAkB,CAAC;IACzC,IAAI,CAAC8D,GAAG,GAAG/M,MAAM,CAACI,iBAAiB,CAAC;IACpC;IACA,IAAI,CAAC4M,OAAO,GAAGlI,WAAW,CAAC9E,MAAM,CAACuH,iBAAiB,EAAE;MAAE0F,IAAI,EAAE,IAAI;MAAE9B,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;IACrF,IAAI,CAAC+B,GAAG,GAAG,IAAI,CAACL,QAAQ,CACnBM,IAAI,CAACpJ,YAAY,CAAC,CAAC,EAAEgB,oBAAoB,CAAC,CAAC,CAAC,EAAEjE,kBAAkB,CAAC,CAAC,CAAC,CACnEsM,SAAS,CAAC,MAAM;MACjB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEC,iBAAiB,CAACC,aAAa,CAAC,CAAC;MAC7C,IAAI,CAACF,GAAG,CAAC,CAAC,EAAEC,iBAAiB,CAACE,YAAY,CAAC,CAAC;IAChD,CAAC,CAAC;IACF,IAAI,CAACC,EAAE,GAAGjM,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACiG,IAAI,GAAG,UAAU;IACtB,IAAI,CAACiG,SAAS,GAAG,IAAIhK,qBAAqB,CAAC1D,MAAM,CAAC8I,sBAAsB,CAAC,EAAE9I,MAAM,CAACK,QAAQ,CAAC,CAAC;IAC5F,IAAI,CAACgN,GAAG,GAAG/M,MAAM,CAAC,IAAI,CAAC;IACvB;IACA;IACA,IAAI,CAACqN,QAAQ,GAAGrN,MAAM,CAAC,IAAI,CAAC;EAChC;EACA,IAAIsN,WAAWA,CAACC,OAAO,EAAE;IACrB,IAAI,CAACF,QAAQ,CAACG,GAAG,CAACD,OAAO,YAAYtN,WAAW,GAC1C,IAAIoD,oBAAoB,CAACkK,OAAO,EAAE,IAAI,CAACd,GAAG,CAAC,GAC3Cc,OAAO,CAAC;IACd,IAAI,CAAC,IAAI,CAACF,QAAQ,CAAC,CAAC,EAAE;MAClB,IAAI,CAACI,MAAM,CAAC,KAAK,CAAC;IACtB;EACJ;EACA,IAAIzB,QAAQA,CAAA,EAAG;IACX,OAAO/G,qBAAqB,CAAC,IAAI,CAACkI,EAAE,CAAC,GAAG,OAAO,GAAG,UAAU;EAChE;EACA;EACA,IAAII,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACF,QAAQ,CAAC,CAAC;EAC1B;EACA;EACA,IAAIE,OAAOA,CAACG,CAAC,EAAE;IACX,IAAI,CAACL,QAAQ,CAACG,GAAG,CAACE,CAAC,CAAC;EACxB;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACpB,QAAQ,CAACqB,IAAI,CAAC,CAAC;EACxB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACJ,MAAM,CAAC,KAAK,CAAC;EACtB;EACAnC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC6B,EAAE,CAACW,qBAAqB,CAAC,CAAC;EAC1C;EACAL,MAAMA,CAACM,IAAI,EAAE;IACT,MAAMhB,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACtB,IAAIgB,IAAI,IAAI,IAAI,CAACV,QAAQ,CAAC,CAAC,IAAI,CAACN,GAAG,EAAE;MACjC,IAAI,CAACA,GAAG,CAACS,GAAG,CAAC,IAAI,CAAChB,OAAO,CAACwB,GAAG,CAAC,IAAI,CAACZ,SAAS,CAAC,CAAC;IAClD,CAAC,MACI,IAAI,CAACW,IAAI,IAAIhB,GAAG,EAAE;MACnB,IAAI,CAACA,GAAG,CAACS,GAAG,CAAC,IAAI,CAAC;MAClB,IAAI,CAAChB,OAAO,CAACyB,MAAM,CAAClB,GAAG,CAAC;IAC5B;IACA,IAAI,CAACL,OAAO,CAACwB,OAAO,CAAEC,MAAM,IAAKA,MAAM,EAAEP,IAAI,CAACG,IAAI,CAAC,CAAC;IACpD;IACA,IAAI,CAACtB,GAAG,CAACS,YAAY,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAAC9F,IAAI,YAAAgH,6BAAA9G,CAAA;MAAA,YAAAA,CAAA,IAAyFsD,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAAC1C,IAAI,kBA3O+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EA2OJyD,oBAAoB;MAAAX,SAAA;MAAAoE,QAAA;MAAAC,YAAA,WAAAC,kCAAAhI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3OlBnH,EAAE,CAAAoP,WAAA,sBA2OJhI,GAAA,CAAAuG,GAAA,CAAI,CAAe,CAAC;QAAA;MAAA;MAAA7C,MAAA;QAAAoD,WAAA;MAAA;MAAAmB,QAAA;MAAArG,UAAA;MAAAC,QAAA,GA3OlBjJ,EAAE,CAAAiL,kBAAA,CA2OqN,CAChT9H,iBAAiB,CAACqI,oBAAoB,CAAC,EACvCpI,YAAY,CAACoI,oBAAoB,CAAC,CACrC,GA9O4FxL,EAAE,CAAAsP,uBAAA,EA8OnC7G,0BAA0B;QAAA8G,SAAA,EAAiBnE,mBAAmB;QAAA6B,OAAA;MAAA;IAAA,EAA2F;EAAE;AAC/N;AACA;EAAA,QAAA1E,SAAA,oBAAAA,SAAA,KAhPqGvI,EAAE,CAAAwI,iBAAA,CAgPXgD,oBAAoB,EAAc,CAAC;IACnHzD,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBkC,QAAQ,EAAE,kDAAkD;MAC5DC,SAAS,EAAE,CACPhI,iBAAiB,CAACqI,oBAAoB,CAAC,EACvCpI,YAAY,CAACoI,oBAAoB,CAAC,CACrC;MACD6D,QAAQ,EAAE,aAAa;MACvBG,cAAc,EAAE,CACZ/G,0BAA0B,EAC1B;QACI8G,SAAS,EAAEnE,mBAAmB;QAC9B6B,OAAO,EAAE,CAAC,4BAA4B;MAC1C,CAAC,CACJ;MACDwC,IAAI,EAAE;QACF,2BAA2B,EAAE;MACjC;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEvB,WAAW,EAAE,CAAC;MAC5BnG,IAAI,EAAExH;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAM8I,oBAAoB,CAAC;EACvBvB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiG,EAAE,GAAGjM,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACyJ,QAAQ,GAAGjL,MAAM,CAAC4C,eAAe,CAAC;IACvC,IAAI,CAACmI,QAAQ,GAAG/K,MAAM,CAACuD,YAAY,CAAC;IACpC,IAAI,CAAC6L,GAAG,GAAGpP,MAAM,CAACqD,wBAAwB,CAAC;IAC3C,IAAI,CAACgM,OAAO,GAAGrP,MAAM,CAACsD,kBAAkB,CAAC,CAAC6J,IAAI,CAACnJ,SAAS,CAAC,MAAM,IAAI,CAACiL,SAAS,CAACxB,EAAE,CAAC6B,WAAW,IACxF,CAAC,CAAC,IAAI,CAACL,SAAS,CAACxB,EAAE,CAACW,qBAAqB,CAAC,CAAC,CAAC5C,MAAM,CAAC,EAAEvH,GAAG,CAAEsL,CAAC,IAAM,IAAI,CAACjD,QAAQ,KAAK,OAAO,GAAG,IAAI,CAAC8C,GAAG,CAACI,OAAO,CAACD,CAAC,CAAC,GAAGA,CAAE,CAAC,EAAEtL,GAAG,CAAC,CAAC,CAAC6H,GAAG,EAAEG,IAAI,CAAC,KAAK,IAAI,CAACwD,SAAS,CAACxD,IAAI,EAAEH,GAAG,CAAC,CAAC,EAAEhL,kBAAkB,CAAC,CAAC,CAAC;IAClM,IAAI,CAACsJ,OAAO,GAAGpK,MAAM,CAAC6J,oBAAoB,CAAC;IAC3C,IAAI,CAACoF,SAAS,GAAGjP,MAAM,CAACkL,oBAAoB,CAAC;IAC7C,IAAI,CAACwE,OAAO,GAAG1P,MAAM,CAACgJ,oBAAoB,EAAE;MAAEmC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC/D,IAAI,CAACwE,QAAQ,GAAG3P,MAAM,CAACwD,aAAa,CAAC;IACrC,IAAI,CAAC8I,QAAQ,GAAG,IAAI,CAAC2C,SAAS,CAAC3C,QAAQ;IACvC,IAAI,CAACsD,KAAK,GAAGpP,QAAQ,CAAC,CAACqP,CAAC,GAAG,IAAI,CAACF,QAAQ,CAAC,CAAC,KAAK,IAAI,CAACV,SAAS,CAACxB,EAAE,CAACqC,OAAO,CAAC,YAAY,CAAC,EAAEC,YAAY,CAAC,UAAU,CAAC,CAAC;IACjH,IAAI,CAACC,KAAK,GAAG,MAAM,IAAI,CAACf,SAAS,CAAClB,MAAM,CAAC,KAAK,CAAC;EACnD;EACAkC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACZ,OAAO,CAACjC,SAAS,CAAC;MACnBc,IAAI,EAAGgC,MAAM,IAAKC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC3C,EAAE,CAAC4C,KAAK,EAAEH,MAAM,CAAC;MACtDI,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACN,KAAK,GAAG;IACjC,CAAC,CAAC;EACN;EACAP,SAASA,CAACzB,CAAC,EAAEuC,CAAC,EAAE;IACZ,MAAM;MAAE9G,SAAS;MAAEC,SAAS;MAAEC,MAAM;MAAEH;IAAW,CAAC,GAAG,IAAI,CAACY,OAAO;IACjE,MAAMoG,MAAM,GAAG,IAAI,CAAC/C,EAAE,CAACgD,YAAY,EAAErC,qBAAqB,CAAC,CAAC,IAAIpN,iBAAiB;IACjF,MAAM;MAAEiL,IAAI,GAAG,CAAC;MAAEH,GAAG,GAAG;IAAE,CAAC,GAAG,IAAI,CAACQ,QAAQ,KAAK,OAAO,GAAG,CAAC,CAAC,GAAGkE,MAAM;IACrE,MAAME,IAAI,GAAG,IAAI,CAACzF,QAAQ,CAACW,aAAa,CAAC,CAAC;IAC1C,MAAMb,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACa,aAAa,CAAC,CAAC;IAC9C,MAAM+E,KAAK,GAAGD,IAAI,CAAC5E,GAAG,GAAGf,QAAQ,CAACe,GAAG,GAAG,CAAC,GAAGnC,MAAM;IAClD,MAAMiH,KAAK,GAAG7F,QAAQ,CAACe,GAAG,GAAGf,QAAQ,CAACS,MAAM,GAAG+E,CAAC,GAAG5G,MAAM;IACzD,MAAMuC,SAAS,GAAGqE,CAAC,GAAGG,IAAI,CAAC3E,MAAM,GAAG6E,KAAK,GAAGD,KAAK;IACjD,MAAMnF,MAAM,GAAG,IAAI,CAACiC,EAAE,CAACW,qBAAqB,CAAC,CAAC,CAACpC,KAAK,IAAI0E,IAAI,CAACzE,IAAI,IAAI+B,CAAC,IAAI0C,IAAI,CAAC1E,KAAK,GAC9EvC,SAAS,GACTzH,QAAQ,CAACkK,SAAS,EAAExC,SAAS,EAAED,SAAS,CAAC;IAC/C8G,CAAC,IAAIzE,GAAG;IACRkC,CAAC,IAAI/B,IAAI;IACT,OAAO;MACHK,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBR,GAAG,EAAEzJ,KAAK,CAAC+J,IAAI,CAACyE,KAAK,CAACzE,IAAI,CAACC,GAAG,CAACkE,CAAC,EAAE5G,MAAM,GAAGmC,GAAG,CAAC,CAAC,CAAC;MACjDG,IAAI,EAAE5J,KAAK,CAAC+J,IAAI,CAACyE,KAAK,CAAC7C,CAAC,CAAC,CAAC;MAC1BvE,SAAS,EAAEpH,KAAK,CAAC+J,IAAI,CAACyE,KAAK,CAACrF,MAAM,CAAC,CAAC;MACpCD,KAAK,EAAE/B,UAAU,KAAK,OAAO,GAAGnH,KAAK,CAAC+J,IAAI,CAACyE,KAAK,CAACH,IAAI,CAACnF,KAAK,CAAC,CAAC,GAAG,EAAE;MAClEuF,QAAQ,EAAEtH,UAAU,KAAK,KAAK,GAAGnH,KAAK,CAAC+J,IAAI,CAACyE,KAAK,CAACH,IAAI,CAACnF,KAAK,CAAC,CAAC,GAAG,EAAE;MACnEwF,QAAQ,EAAE1O,KAAK,CAAC+J,IAAI,CAACyE,KAAK,CAAC9F,QAAQ,CAACQ,KAAK,CAAC,GAAG,EAAE,CAAC,CAAE;IACtD,CAAC;EACL;EACA;IAAS,IAAI,CAAC7D,IAAI,YAAAsJ,6BAAApJ,CAAA;MAAA,YAAAA,CAAA,IAAyFmB,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACkI,IAAI,kBA7T+EvR,EAAE,CAAAwR,iBAAA;MAAAzJ,IAAA,EA6TJsB,oBAAoB;MAAAwB,SAAA;MAAAoE,QAAA;MAAAC,YAAA,WAAAuC,kCAAAtK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7TlBnH,EAAE,CAAA0R,WAAA,oBAAAtK,GAAA,CAAAsD,OAAA,CAAAR,UAAA,cA6TJ9C,GAAA,CAAA8I,KAAA,CAAM,CAAC;QAAA;MAAA;MAAAlH,UAAA;MAAAC,QAAA,GA7TLjJ,EAAE,CAAAiL,kBAAA,CA6T+K,CAC1QrH,kBAAkB,EAClBP,sBAAsB,CAAC,UAAU,EAAE+H,mBAAmB,CAAC,EACvD9H,kBAAkB,CAAC,UAAU,EAAEkI,oBAAoB,CAAC,CACvD,GAjU4FxL,EAAE,CAAAsP,uBAAA,EAiU9D5N,EAAE,CAACC,aAAa,EAAiBC,EAAE,CAACC,WAAW,IAjUa7B,EAAE,CAAA2R,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAA7K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnH,EAAE,CAAAqH,cAAA,sBAiUgD,CAAC;UAjUnDrH,EAAE,CAAAiS,UAAA,IAAA/K,mCAAA,gBAiU2L,CAAC;UAjU9LlH,EAAE,CAAAuH,YAAA,CAiU6O,CAAC;QAAA;QAAA,IAAAJ,EAAA;UAjUhPnH,EAAE,CAAA0H,SAAA,CAiU+G,CAAC;UAjUlH1H,EAAE,CAAAkS,UAAA,uBAAA9K,GAAA,CAAAmI,SAAA,CAAAtB,QAAA,EAiU+G,CAAC,8BAjUlHjO,EAAE,CAAAmS,eAAA,IAAApL,GAAA,EAAAK,GAAA,CAAAkJ,KAAA,CAiUmJ,CAAC;QAAA;MAAA;MAAA8B,YAAA,GAAmuBlO,kBAAkB,EAA8HR,YAAY;MAAA8M,MAAA;IAAA,EAA0G;EAAE;AACtuC;AACA;EAAA,QAAAjI,SAAA,oBAAAA,SAAA,KAnUqGvI,EAAE,CAAAwI,iBAAA,CAmUXa,oBAAoB,EAAc,CAAC;IACnHtB,IAAI,EAAEhH,SAAS;IACfoI,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,IAAI;MAAEkC,QAAQ,EAAE,cAAc;MAAEmH,OAAO,EAAE,CAACnO,kBAAkB,EAAER,YAAY,CAAC;MAAE4O,eAAe,EAAEtR,uBAAuB,CAACuR,OAAO;MAAEpH,SAAS,EAAE,CACnJvH,kBAAkB,EAClBP,sBAAsB,CAAC,UAAU,EAAE+H,mBAAmB,CAAC,EACvD9H,kBAAkB,CAAC,UAAU,EAAEkI,oBAAoB,CAAC,CACvD;MAAEgE,cAAc,EAAE,CAAC7N,aAAa,EAAEE,WAAW,CAAC;MAAE4N,IAAI,EAAE;QACnD,wBAAwB,EAAE,oBAAoB;QAC9C,iBAAiB,EAAE;MACvB,CAAC;MAAEsC,QAAQ,EAAE,mOAAmO;MAAEvB,MAAM,EAAE,CAAC,8kBAA8kB;IAAE,CAAC;EACx1B,CAAC,CAAC;AAAA;AAEV,SAASgC,gBAAgBA,CAACC,KAAK,EAAE;EAC7B,OAAQ,CAACA,KAAK,IACT,IAAI,CAAC1D,MAAM,CAAC2D,KAAK,IAAI,CAAC,IAAI,CAACC,UAAU,CAACC,QAAQ,CAAC7Q,kBAAkB,CAAC0Q,KAAK,CAAC,CAAE;AACnF;AACA,MAAMI,kBAAkB,SAAS3P,eAAe,CAAC;EAC7C4E,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGY,SAAS,CAAC;IACnB,IAAI,CAACoK,OAAO,GAAGxS,MAAM,CAAC6F,YAAY,CAAC;IACnC,IAAI,CAAC4M,WAAW,GAAGzR,iBAAiB;IACpC,IAAI,CAAC0R,UAAU,GAAGlS,QAAQ,CAAC,MAAO,IAAI,CAACgS,OAAO,CAAC,CAAC,GAAG,MAAM,GAAG,IAAK,CAAC;IAClE,IAAI,CAACH,UAAU,GAAGrS,MAAM,CAACqB,aAAa,CAAC;IACvC,IAAI,CAACoN,MAAM,GAAGzO,MAAM,CAACuH,iBAAiB,CAAC;IACvC,IAAI,CAACE,IAAI,GAAG,UAAU;EAC1B;EACAmE,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC6G,WAAW;EAC3B;EACAE,aAAaA,CAACC,MAAM,EAAE;IAClB,IAAI,CAACnE,MAAM,CAACP,IAAI,CAAC,KAAK,CAAC;IACvB,IAAI,CAACuE,WAAW,GAAGzR,iBAAiB;EACxC;EACA6R,aAAaA,CAAC7E,CAAC,EAAEuC,CAAC,EAAE;IAChB,IAAI,CAACkC,WAAW,GAAG/Q,oBAAoB,CAACsM,CAAC,EAAEuC,CAAC,CAAC;IAC7C,IAAI,CAAC9B,MAAM,CAACP,IAAI,CAAC,IAAI,CAAC;EAC1B;EACA;IAAS,IAAI,CAACxG,IAAI;MAAA,IAAAoL,+BAAA;MAAA,gBAAAC,2BAAAnL,CAAA;QAAA,QAAAkL,+BAAA,KAAAA,+BAAA,GAxW+EpT,EAAE,CAAA6I,qBAAA,CAwWQgK,kBAAkB,IAAA3K,CAAA,IAAlB2K,kBAAkB;MAAA;IAAA,IAAqD;EAAE;EACpL;IAAS,IAAI,CAAC/J,IAAI,kBAzW+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EAyWJ8K,kBAAkB;MAAAhI,SAAA;MAAAoE,QAAA;MAAAC,YAAA,WAAAoE,gCAAAnM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzWhBnH,EAAE,CAAAuT,UAAA,kCAAAC,2DAAAC,MAAA;YAAA,OAyWJrM,GAAA,CAAA6L,aAAA,CAAAQ,MAAoB,CAAC;UAAA,UAzWnBzT,EAAE,CAAA0T,iBAyWa,CAAC,0CAAAC,mEAAAF,MAAA;YAAA,OAAlBrM,GAAA,CAAA6L,aAAA,CAAAQ,MAAoB,CAAC;UAAA,UAzWnBzT,EAAE,CAAA0T,iBAyWa,CAAC,yBAAAE,kDAAA;YAAA,OAAlBxM,GAAA,CAAA6L,aAAA,CAAc,CAAC;UAAA,UAzWbjT,EAAE,CAAA0T,iBAyWa,CAAC,qBAAAG,8CAAAJ,MAAA;YAAA,OAAlBrM,GAAA,CAAA+L,aAAA,CAAAM,MAAA,CAAAK,MAAA,CAAAC,OAAA,EAAAN,MAAA,CAAAK,MAAA,CAAAE,OAA0D,CAAC;UAAA,CAA1C,CAAC;QAAA;QAAA,IAAA7M,EAAA;UAzWhBnH,EAAE,CAAAiU,WAAA,gBAyWJ7M,GAAA,CAAA4L,UAAA,CAAW,CAAM,CAAC,wBAAlB5L,GAAA,CAAA4L,UAAA,CAAW,CAAM,CAAC,0BAAlB5L,GAAA,CAAA4L,UAAA,CAAW,CAAM,CAAC;QAAA;MAAA;MAAAhK,UAAA;MAAAC,QAAA,GAzWhBjJ,EAAE,CAAAiL,kBAAA,CAyW4e,CACvkBtJ,aAAa,EACbkG,iBAAiB,EACjBtE,WAAW,CAACsE,iBAAiB,CAAC,EAC9B1E,iBAAiB,CAAC0P,kBAAkB,CAAC,CACxC,GA9W4F7S,EAAE,CAAAkJ,0BAAA;IAAA,EA8WtD;EAAE;AACnD;AACAhD,UAAU,CAAC,CACPG,UAAU,CAACmM,gBAAgB,CAAC,CAC/B,EAAEK,kBAAkB,CAAC3F,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC;AACvD;EAAA,QAAA3E,SAAA,oBAAAA,SAAA,KAnXqGvI,EAAE,CAAAwI,iBAAA,CAmXXqK,kBAAkB,EAAc,CAAC;IACjH9K,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBkC,QAAQ,EAAE,sBAAsB;MAChCC,SAAS,EAAE,CACPxJ,aAAa,EACbkG,iBAAiB,EACjBtE,WAAW,CAACsE,iBAAiB,CAAC,EAC9B1E,iBAAiB,CAAC0P,kBAAkB,CAAC,CACxC;MACDpD,IAAI,EAAE;QACF,qBAAqB,EAAE,cAAc;QACrC,6BAA6B,EAAE,cAAc;QAC7C,+BAA+B,EAAE,cAAc;QAC/C,iCAAiC,EAAE,uBAAuB;QAC1D,yCAAyC,EAAE,uBAAuB;QAClE,wBAAwB,EAAE,iBAAiB;QAC3C,WAAW,EAAE;MACjB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEwD,aAAa,EAAE;EAAG,CAAC;AAAA;;AAEjD;AACA,MAAMiB,kCAAkC,GAAG;EACvCC,SAAS,EAAE,GAAG;EACdC,SAAS,EAAE;AACf,CAAC;AACD;AACA;AACA;AACA,MAAMC,0BAA0B,GAAG7R,cAAc,CAAC0R,kCAAkC,CAAC;AACrF,SAASI,+BAA+BA,CAAC5J,OAAO,EAAE;EAC9C,OAAO9H,iBAAiB,CAACyR,0BAA0B,EAAE3J,OAAO,EAAEwJ,kCAAkC,CAAC;AACrG;AAEA,SAASK,WAAWA,CAAC9B,KAAK,EAAE;EACxB;IACA;IACA,OAAO+B,YAAY,KAAK,WAAW;IAC/B;IACA/B,KAAK,CAACgC,GAAG,EAAEC,WAAW,CAAC,CAAC,KAAK,QAAQ,IACrC,IAAI,CAACC,kBAAkB,IACvB,CAAC,CAAC,IAAI,CAACC,eAAe,IACtB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAEC;EAAkB;AAC/C;AACA,MAAMC,eAAe,CAAC;EAClBhN,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyH,SAAS,GAAGjP,MAAM,CAACkL,oBAAoB,CAAC;IAC7C,IAAI,CAACuC,EAAE,GAAGjM,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACiT,QAAQ,GAAGzU,MAAM,CAACkG,WAAW,CAAC;IACnC,IAAI,CAACmM,UAAU,GAAGrS,MAAM,CAACqB,aAAa,CAAC;IACvC,IAAI,CAACqT,QAAQ,GAAGlU,QAAQ,CAAC,MAAM,IAAI,CAACyO,SAAS,CAAC5B,GAAG,CAAC,CAAC,EAAEsH,QAAQ,CAACC,aAAa,CAAC;IAC5E,IAAI,CAACP,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACO,qBAAqB,GAAG,IAAI3U,YAAY,CAAC,CAAC;IAC/C;IACA,IAAI,CAACuO,MAAM,GAAGzO,MAAM,CAACuH,iBAAiB,CAAC;IACvC,IAAI,CAAC2F,GAAG,GAAG,IAAI,CAACuB,MAAM,CACjBtB,IAAI,CAACnI,QAAQ,CAAC,MAAMd,KAAK,CAACe,eAAe,CAAC,CAAC,EAAE,IAAI,CAACwP,QAAQ,CAACK,WAAW,CAAC3H,IAAI,CAAChJ,MAAM,CAAC4Q,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC1C,UAAU,CAAC2C,mBAAmB,CAAC7H,IAAI,CAAChJ,MAAM,CAAE8Q,CAAC,IAAK,CAACA,CAAC,CAAC,CAAC,EAAE7Q,SAAS,CAAC,IAAI,CAACqJ,EAAE,EAAE,SAAS,CAAC,CAACN,IAAI,CAAChJ,MAAM,CAAEgO,KAAK,IAAK,CAAC,IAAI,CAAChD,IAAI,CAACmD,QAAQ,CAAC7Q,kBAAkB,CAAC0Q,KAAK,CAAC,CAAC,IAC/P,CAAC,IAAI,CAAClD,SAAS,CAAC5B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEnI,WAAW,CAAC,CAAC,EAAEC,QAAQ,CAAC,CAAC,EAAErE,kBAAkB,CAAC,CAAC,CAAC,CAC1EsM,SAAS,CAAC,MAAM,IAAI,CAACW,MAAM,CAAC,KAAK,CAAC,CAAC;IACxC,IAAI,CAACmH,IAAI,GAAG,IAAI,CAACzG,MAAM,CAACtB,IAAI,CAACrM,kBAAkB,CAAC,CAAC,CAAC,CAACsM,SAAS,CAAE+H,IAAI,IAAK;MACnE,IAAIA,IAAI,KAAK,IAAI,CAACb,eAAe,EAAE;QAC/B,IAAI,CAACc,MAAM,CAACD,IAAI,CAAC;MACrB;IACJ,CAAC,CAAC;EACN;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAChB,eAAe,CAAC;IAClC,IAAI,CAACO,qBAAqB,CAACxJ,IAAI,CAAC,CAAC,CAAC,IAAI,CAACiJ,eAAe,CAAC;EAC3D;EACAvG,MAAMA,CAACoH,IAAI,EAAE;IACT,IAAI,IAAI,CAACI,OAAO,IAAI,CAACJ,IAAI,EAAE;MACvB,IAAI,CAAChG,IAAI,CAACqG,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;IAC5C;IACA,IAAI,CAACL,MAAM,CAACD,IAAI,CAAC;EACrB;EACAO,KAAKA,CAACvD,KAAK,EAAE;IACTA,KAAK,CAACwD,cAAc,CAAC,CAAC;IACtB,IAAI,CAAC5H,MAAM,CAAC,KAAK,CAAC;EACtB;EACA6H,OAAOA,CAACC,MAAM,EAAE;IACZ,IAAI,CAAC,IAAI,CAACC,QAAQ,IAAI,IAAI,CAAC3G,IAAI,CAACmD,QAAQ,CAACuD,MAAM,CAAC,EAAE;MAC9C,IAAI,CAACT,MAAM,CAAC,CAAC,IAAI,CAACd,eAAe,CAAC;IACtC;EACJ;EACAyB,OAAOA,CAAC5D,KAAK,EAAE6D,EAAE,EAAE;IACf,IAAI,CAACrU,YAAY,CAACwQ,KAAK,CAAC0D,MAAM,CAAC,IAC3B,CAAC,IAAI,CAAC1G,IAAI,CAACmD,QAAQ,CAACH,KAAK,CAAC0D,MAAM,CAAC,IACjC,CAAC,IAAI,CAACxB,kBAAkB,IACxB,CAAC,IAAI,CAACpF,SAAS,CAACtB,QAAQ,CAAC,CAAC,EAAE;MAC5B;IACJ;IACAwE,KAAK,CAACwD,cAAc,CAAC,CAAC;IACtB,IAAI,CAACM,aAAa,CAACD,EAAE,CAAC;EAC1B;EACAE,SAASA,CAAC/D,KAAK,EAAE;IACb,MAAM0D,MAAM,GAAGpU,kBAAkB,CAAC0Q,KAAK,CAAC;IACxC,IAAI,CAACA,KAAK,CAACgE,gBAAgB,IACvB5P,eAAe,CAAC4L,KAAK,CAACgC,GAAG,CAAC,IAC1B,IAAI,CAAC2B,QAAQ,IACb,IAAI,CAACP,OAAO,IACZ3T,gBAAgB,CAACiU,MAAM,CAAC,IACxB,CAAChU,oBAAoB,CAACgU,MAAM,CAAC,EAAE;MAC/B,IAAI,CAAC1G,IAAI,CAACqG,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;IAC5C;EACJ;EACA,IAAItG,IAAIA,CAAA,EAAG;IACP,MAAMiH,OAAO,GAAG,IAAI,CAACC,YAAY,EAAEzB,aAAa,IAAI,IAAI,CAACnH,EAAE;IAC3D,MAAM6I,SAAS,GAAGnQ,4BAA4B,CAACiQ,OAAO,CAAC,GACjDA,OAAO,GACPhQ,sBAAsB,CAAC;MAAEgQ,OAAO;MAAEG,IAAI,EAAE,IAAI,CAAC9I;IAAG,CAAC,CAAC;IACxD,OAAO,IAAI,CAAC4I,YAAY,EAAEzB,aAAa,IAAI0B,SAAS,IAAI,IAAI,CAAC7I,EAAE;EACnE;EACA,IAAIqI,QAAQA,CAAA,EAAG;IACX,OAAOjU,oBAAoB,CAAC,IAAI,CAACsN,IAAI,CAAC;EAC1C;EACA,IAAIoG,OAAOA,CAAA,EAAG;IACV,OAAOlP,oBAAoB,CAAC,IAAI,CAAC8I,IAAI,CAAC,IAAI9I,oBAAoB,CAAC,IAAI,CAACqO,QAAQ,CAAC,CAAC,CAAC;EACnF;EACAU,MAAMA,CAACD,IAAI,EAAE;IACT,IAAIA,IAAI,IAAI,CAAC,IAAI,CAACd,kBAAkB,EAAE;MAClC,OAAO,IAAI,CAACiB,KAAK,CAAC,CAAC;IACvB;IACA,IAAI,CAAChB,eAAe,GAAGa,IAAI;IAC3B,IAAI,CAACN,qBAAqB,CAACxJ,IAAI,CAAC8J,IAAI,CAAC;IACrC,IAAI,CAACG,KAAK,CAAC,CAAC;EAChB;EACAA,KAAKA,CAACH,IAAI,GAAG,CAAC,CAAC,IAAI,CAACb,eAAe,IAAI,IAAI,CAACD,kBAAkB,EAAE;IAC5D,IAAI,CAACI,QAAQ,CAAC+B,kBAAkB,GAAGrB,IAAI;IACvC,IAAI,CAAC1G,MAAM,CAACP,IAAI,CAACiH,IAAI,CAAC;EAC1B;EACAc,aAAaA,CAACxK,QAAQ,EAAE;IACpB,MAAM8K,IAAI,GAAG,IAAI,CAAC7B,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAAC6B,IAAI,EAAE;MACP,IAAI,CAACnB,MAAM,CAAC,IAAI,CAAC;MACjB;IACJ;IACA,MAAMqB,GAAG,GAAG,IAAI,CAAChJ,EAAE,CAACiJ,aAAa;IACjC,MAAMC,KAAK,GAAGJ,IAAI,CAACK,WAAW,CAACH,GAAG,CAACI,aAAa,CAAC,KAAK,CAAC,CAAC;IACxD,MAAMT,OAAO,GAAG3K,QAAQ,GAAGkL,KAAK,GAAGJ,IAAI;IACvC,MAAMD,SAAS,GAAGlQ,sBAAsB,CAAC;MAAEgQ,OAAO;MAAE3K,QAAQ;MAAE8K;IAAK,CAAC,CAAC;IACrEI,KAAK,CAACpI,MAAM,CAAC,CAAC;IACd+H,SAAS,EAAEd,KAAK,CAAC,CAAC;EACtB;EACA;IAAS,IAAI,CAAC9N,IAAI,YAAAoP,wBAAAlP,CAAA;MAAA,YAAAA,CAAA,IAAyF4M,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAAChM,IAAI,kBAtgB+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EAsgBJ+M,eAAe;MAAAjK,SAAA;MAAAwM,cAAA,WAAAC,+BAAAnQ,EAAA,EAAAC,GAAA,EAAAmQ,QAAA;QAAA,IAAApQ,EAAA;UAtgBbnH,EAAE,CAAAwX,cAAA,CAAAD,QAAA,EAAA3P,GAAA,KAsgBotB3G,UAAU;QAAA;QAAA,IAAAkG,EAAA;UAAA,IAAAsQ,EAAA;UAtgBhuBzX,EAAE,CAAA0X,cAAA,CAAAD,EAAA,GAAFzX,EAAE,CAAA2X,WAAA,QAAAvQ,GAAA,CAAAuP,YAAA,GAAAc,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAA1I,YAAA,WAAA2I,6BAAA1Q,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnH,EAAE,CAAAuT,UAAA,mBAAAuE,yCAAArE,MAAA;YAAA,OAsgBJrM,GAAA,CAAA8O,OAAA,CAAAzC,MAAA,CAAA0C,MAAqB,CAAC;UAAA,CAAR,CAAC,+BAAA4B,qDAAAtE,MAAA;YAAA,OAAfrM,GAAA,CAAAiP,OAAA,CAAA5C,MAAA,EAAgB,KAAK,CAAC;UAAA,CAAR,CAAC,6BAAAuE,mDAAAvE,MAAA;YAAA,OAAfrM,GAAA,CAAAiP,OAAA,CAAA5C,MAAA,EAAgB,IAAI,CAAC;UAAA,CAAP,CAAC,sCAAAwE,4DAAAxE,MAAA;YAAA,OAAfrM,GAAA,CAAA4O,KAAA,CAAAvC,MAAY,CAAC;UAAA,UAtgBXzT,EAAE,CAAA0T,iBAsgBU,CAAC,8BAAAwE,oDAAAzE,MAAA;YAAA,OAAfrM,GAAA,CAAAoP,SAAA,CAAA/C,MAAgB,CAAC;UAAA,UAtgBfzT,EAAE,CAAA0T,iBAsgBU,CAAC,iCAAAyE,uDAAA;YAAA,OAAf,CAAC;UAAA,CAAa,CAAC;QAAA;MAAA;MAAArN,MAAA;QAAA6J,kBAAA;QAAAC,eAAA;MAAA;MAAA3H,OAAA;QAAAkI,qBAAA;MAAA;MAAAnM,UAAA;MAAAC,QAAA,GAtgBbjJ,EAAE,CAAAiL,kBAAA,CAsgB+iB,CAACpD,iBAAiB,EAAEtE,WAAW,CAACsE,iBAAiB,CAAC,CAAC,GAtgBpmB7H,EAAE,CAAAsP,uBAAA,EAsgBsxB/I,IAAI,CAACC,WAAW;QAAA+I,SAAA,EAAiB7N,EAAE,CAACC,aAAa;QAAAmJ,MAAA;QAAAmC,OAAA;MAAA,KAtgBz0BjN,EAAE,CAAAoY,oBAAA;IAAA,EAsgB48B;EAAE;AACrjC;AACAlS,UAAU,CAAC,CACPG,UAAU,CAACkO,WAAW,CAAC,CAC1B,EAAEO,eAAe,CAAC5H,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC;AAC5C;EAAA,QAAA3E,SAAA,oBAAAA,SAAA,KA3gBqGvI,EAAE,CAAAwI,iBAAA,CA2gBXsM,eAAe,EAAc,CAAC;IAC9G/M,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBkC,QAAQ,EAAE,qEAAqE;MAC/EC,SAAS,EAAE,CAACtD,iBAAiB,EAAEtE,WAAW,CAACsE,iBAAiB,CAAC,CAAC;MAC9D2H,cAAc,EAAE,CACZhJ,WAAW,EACX;QACI+I,SAAS,EAAE5N,aAAa;QACxBmJ,MAAM,EAAE,CAAC,qBAAqB,CAAC;QAC/BmC,OAAO,EAAE,CAAC,qBAAqB;MACnC,CAAC,CACJ;MACDwC,IAAI,EAAE;QACF,SAAS,EAAE,wBAAwB;QACnC,qBAAqB,EAAE,wBAAwB;QAC/C,mBAAmB,EAAE,uBAAuB;QAC5C,qCAAqC,EAAE,eAAe;QACtD,6BAA6B,EAAE,mBAAmB;QAClD;QACA,uBAAuB,EAAE;MAC7B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEkH,YAAY,EAAE,CAAC;MAC7B5O,IAAI,EAAE7G,YAAY;MAClBiI,IAAI,EAAE,CAAC,iBAAiB,EAAE;QAAEkP,WAAW,EAAE,IAAI;QAAEC,IAAI,EAAErX;MAAW,CAAC;IACrE,CAAC,CAAC;IAAE0T,kBAAkB,EAAE,CAAC;MACrB5M,IAAI,EAAExH;IACV,CAAC,CAAC;IAAEqU,eAAe,EAAE,CAAC;MAClB7M,IAAI,EAAExH;IACV,CAAC,CAAC;IAAE4U,qBAAqB,EAAE,CAAC;MACxBpN,IAAI,EAAEtH;IACV,CAAC,CAAC;IAAEuV,KAAK,EAAE;EAAG,CAAC;AAAA;AAE3B,MAAMuC,gBAAgB,SAAS/U,SAAS,CAAC;EACrCsE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAE0Q,UAAU,IAAK,IAAI,CAACC,OAAO,CAAC/K,SAAS,CAAC8K,UAAU,CAAC,CAAC;IACzD,IAAI,CAACzK,EAAE,GAAGjM,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACiV,GAAG,GAAGzW,MAAM,CAACgG,QAAQ,CAAC;IAC3B,IAAI,CAACoE,OAAO,GAAGpK,MAAM,CAAC+T,0BAA0B,CAAC;IACjD,IAAI,CAAC1B,UAAU,GAAGrS,MAAM,CAACqB,aAAa,CAAC;IACvC,IAAI,CAAC8T,IAAI,GAAGnV,MAAM,CAACwU,eAAe,EAAE;MAAErJ,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvD;AACR;AACA;AACA;IACQ,IAAI,CAACiN,wBAAwB,GAAGrX,YAAY,CAACf,MAAM,CAACkL,oBAAoB,CAAC,CAACmC,GAAG,CAAC,CAACF,IAAI,CAAChJ,MAAM,CAAE6J,CAAC,IAAK,CAACA,CAAC,IAAI,IAAI,CAACqK,OAAO,CAAC,CAAC;IACtH,IAAI,CAACF,OAAO,GAAGjU,KAAK,CAAC,IAAI,CAACkU,wBAAwB,CAACjL,IAAI,CAAC9I,SAAS,CAAC,MAAMe,iBAAiB,CAAC,IAAI,CAACqR,GAAG,EAAE,aAAa,CAAC,CAACtJ,IAAI,CAAClJ,GAAG,CAACxC,kBAAkB,CAAC,EAAE6C,KAAK,CAAC,IAAI,CAACwP,SAAS,CAAC,EAAEvP,SAAS,CAAC,IAAI,CAAC,EAAEC,SAAS,CAACJ,SAAS,CAAC,IAAI,CAACqS,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAErR,iBAAiB,CAAC,IAAI,CAACqR,GAAG,EAAE,WAAW,CAAC,CAACtJ,IAAI,CAAClJ,GAAG,CAACxC,kBAAkB,CAAC,CAAC,EAAE2D,iBAAiB,CAAC,IAAI,CAACqR,GAAG,EAAE,UAAU,CAAC,CAACtJ,IAAI,CAAClJ,GAAG,CAAEqU,CAAC,IAAKA,CAAC,CAACC,aAAa,CAAC,CAAC,CAAC,CAACpL,IAAI,CAAClJ,GAAG,CAAEuU,OAAO,IAAK7W,YAAY,CAAC6W,OAAO,CAAC,IAAI,IAAI,CAACC,SAAS,CAACD,OAAO,CAAC,CAAC,EAAE/T,oBAAoB,CAAC,CAAC,EAAEJ,SAAS,CAAEkL,CAAC,IAAK7K,EAAE,CAAC6K,CAAC,CAAC,CAACpC,IAAI,CAAC7I,KAAK,CAACiL,CAAC,GAAG,IAAI,CAACsE,SAAS,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,EAAEzO,gBAAgB,CAAC,CAAC,EAAEV,GAAG,CAAE0T,OAAO,IAAK;MACxkB,IAAI,CAACA,OAAO,GAAGA,OAAO;MACtB,IAAI,CAAClD,IAAI,EAAEpH,MAAM,CAACsK,OAAO,CAAC;IAC9B,CAAC,CAAC,EAAEzT,KAAK,CAAC,CAAC,CAAC;IACZ,IAAI,CAACiP,SAAS,GAAG,IAAI,CAACzJ,OAAO,CAACyJ,SAAS;IACvC,IAAI,CAACC,SAAS,GAAG,IAAI,CAAC1J,OAAO,CAAC0J,SAAS;IACvC,IAAI,CAACuE,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC5Q,IAAI,GAAG,UAAU;EAC1B;EACAmO,OAAOA,CAACzD,KAAK,EAAE;IACX,IAAI,IAAI,CAACkG,OAAO,IAAI,IAAI,CAAClD,IAAI,EAAE;MAC3BhD,KAAK,CAACwD,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA8C,SAASA,CAACD,OAAO,EAAE;IACf,MAAMrJ,IAAI,GAAG,IAAI,CAACkH,YAAY,EAAEzB,aAAa,IAAI,IAAI,CAACnH,EAAE;IACxD,MAAM4K,OAAO,GAAGlJ,IAAI,CAACmD,QAAQ,CAACkG,OAAO,CAAC;IACtC,MAAM7B,KAAK,GAAG,CAAC,IAAI,CAAClJ,EAAE,CAAC6E,QAAQ,CAACkG,OAAO,CAAC,IAAI,IAAI,CAACnG,UAAU,CAACC,QAAQ,CAACkG,OAAO,CAAC;IAC7E,OAAOH,OAAO,IAAI1B,KAAK;EAC3B;EACA;IAAS,IAAI,CAACjP,IAAI,YAAAgR,yBAAA9Q,CAAA;MAAA,YAAAA,CAAA,IAAyFqQ,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACzP,IAAI,kBAhlB+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EAglBJwQ,gBAAgB;MAAA1N,SAAA;MAAAwM,cAAA,WAAA4B,gCAAA9R,EAAA,EAAAC,GAAA,EAAAmQ,QAAA;QAAA,IAAApQ,EAAA;UAhlBdnH,EAAE,CAAAwX,cAAA,CAAAD,QAAA,EAAA3P,GAAA,KAglB0Z3G,UAAU;QAAA;QAAA,IAAAkG,EAAA;UAAA,IAAAsQ,EAAA;UAhlBtazX,EAAE,CAAA0X,cAAA,CAAAD,EAAA,GAAFzX,EAAE,CAAA2X,WAAA,QAAAvQ,GAAA,CAAAuP,YAAA,GAAAc,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAA1I,YAAA,WAAAgK,8BAAA/R,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnH,EAAE,CAAAuT,UAAA,2BAAA4F,kDAAA1F,MAAA;YAAA,OAglBJrM,GAAA,CAAA8O,OAAA,CAAAzC,MAAc,CAAC;UAAA,EAAC;QAAA;MAAA;MAAA3I,MAAA;QAAAqJ,SAAA,GAhlBdnU,EAAE,CAAA+K,YAAA,CAAAC,IAAA;QAAAoJ,SAAA,GAAFpU,EAAE,CAAA+K,YAAA,CAAAC,IAAA;MAAA;MAAAhC,UAAA;MAAAC,QAAA,GAAFjJ,EAAE,CAAAiL,kBAAA,CAglB0P,CAACtJ,aAAa,EAAE4B,WAAW,CAACgV,gBAAgB,CAAC,CAAC,GAhlB1SvY,EAAE,CAAAkJ,0BAAA;IAAA,EAglB+c;EAAE;AACxjB;AACA;EAAA,QAAAX,SAAA,oBAAAA,SAAA,KAllBqGvI,EAAE,CAAAwI,iBAAA,CAklBX+P,gBAAgB,EAAc,CAAC;IAC/GxQ,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBkC,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CAACxJ,aAAa,EAAE4B,WAAW,CAACgV,gBAAgB,CAAC,CAAC;MACzD9I,IAAI,EAAE;QACF,iBAAiB,EAAE;MACvB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEkH,YAAY,EAAE,CAAC;MACzE5O,IAAI,EAAE7G,YAAY;MAClBiI,IAAI,EAAE,CAAC,iBAAiB,EAAE;QAAEkP,WAAW,EAAE,IAAI;QAAEC,IAAI,EAAErX;MAAW,CAAC;IACrE,CAAC,CAAC;IAAEkT,SAAS,EAAE,CAAC;MACZpM,IAAI,EAAExH,KAAK;MACX4I,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC,CAAC;IAAEiL,SAAS,EAAE,CAAC;MACZrM,IAAI,EAAExH,KAAK;MACX4I,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiQ,iBAAiB,CAAC;EACpBtR,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiH,MAAM,GAAGzO,MAAM,CAACuH,iBAAiB,CAAC;IACvC,IAAI,CAACwR,iBAAiB,GAAG,KAAK;EAClC;EACA1D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC5G,MAAM,CAACP,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC6K,iBAAiB,CAAC;EAC9C;EACA;IAAS,IAAI,CAACrR,IAAI,YAAAsR,0BAAApR,CAAA;MAAA,YAAAA,CAAA,IAAyFkR,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAACtQ,IAAI,kBAhnB+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EAgnBJqR,iBAAiB;MAAAvO,SAAA;MAAAC,MAAA;QAAAuO,iBAAA;MAAA;MAAArQ,UAAA;MAAAC,QAAA,GAhnBfjJ,EAAE,CAAAiL,kBAAA,CAgnBmI,CAACpD,iBAAiB,EAAEtE,WAAW,CAACsE,iBAAiB,CAAC,CAAC,GAhnBxL7H,EAAE,CAAAoY,oBAAA;IAAA,EAgnB4N;EAAE;AACrU;AACA;EAAA,QAAA7P,SAAA,oBAAAA,SAAA,KAlnBqGvI,EAAE,CAAAwI,iBAAA,CAknBX4Q,iBAAiB,EAAc,CAAC;IAChHrR,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBkC,QAAQ,EAAE,qBAAqB;MAC/BC,SAAS,EAAE,CAACtD,iBAAiB,EAAEtE,WAAW,CAACsE,iBAAiB,CAAC;IACjE,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEwR,iBAAiB,EAAE,CAAC;MAClCtR,IAAI,EAAExH;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMgZ,qBAAqB,CAAC;EACxBzR,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0R,YAAY,GAAG,IAAIpV,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC+Q,qBAAqB,GAAG,IAAI,CAACqE,YAAY,CAAC/L,IAAI,CAAC1I,oBAAoB,CAAC,CAAC,CAAC;EAC/E;EACA,IAAI6P,eAAeA,CAACa,IAAI,EAAE;IACtB,IAAI,CAACgE,cAAc,CAAChE,IAAI,CAAC;EAC7B;EACAgE,cAAcA,CAAChE,IAAI,EAAE;IACjB,IAAI,CAAC+D,YAAY,CAAChL,IAAI,CAACiH,IAAI,CAAC;EAChC;EACA;IAAS,IAAI,CAACzN,IAAI,YAAA0R,8BAAAxR,CAAA;MAAA,YAAAA,CAAA,IAAyFqR,qBAAqB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAACzQ,IAAI,kBA5oB+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EA4oBJwR,qBAAqB;MAAA1O,SAAA;MAAAC,MAAA;QAAA8J,eAAA;MAAA;MAAA3H,OAAA;QAAAkI,qBAAA;MAAA;MAAAnM,UAAA;IAAA,EAA+O;EAAE;AACzW;AACA;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KA9oBqGvI,EAAE,CAAAwI,iBAAA,CA8oBX+Q,qBAAqB,EAAc,CAAC;IACpHxR,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBkC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEiK,qBAAqB,EAAE,CAAC;MACtCpN,IAAI,EAAEtH;IACV,CAAC,CAAC;IAAEmU,eAAe,EAAE,CAAC;MAClB7M,IAAI,EAAExH;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMoZ,iBAAiB,CAAC;EACpB7R,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiK,QAAQ,GAAGzR,MAAM,CAACO,WAAW,CAAC;IACnC,IAAI,CAACuM,OAAO,GAAG9M,MAAM,CAACiJ,kBAAkB,CAAC;EAC7C;EACA,IAAI2E,WAAWA,CAACS,IAAI,EAAE;IAClB,IAAI,CAACiL,OAAO,EAAEC,OAAO,CAAC,CAAC;IACvB,IAAIlL,IAAI,EAAE;MACN,IAAI,CAACiL,OAAO,GAAG,IAAI,CAACxM,OAAO,CAAC0M,WAAW,CAAC,IAAI,CAAC/H,QAAQ,CAAC;IAC1D;EACJ;EACAtD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmL,OAAO,EAAEC,OAAO,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAAC7R,IAAI,YAAA+R,0BAAA7R,CAAA;MAAA,YAAAA,CAAA,IAAyFyR,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAAC7Q,IAAI,kBA5qB+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EA4qBJ4R,iBAAiB;MAAA9O,SAAA;MAAAC,MAAA;QAAAoD,WAAA;MAAA;MAAAlF,UAAA;IAAA,EAAmH;EAAE;AACzO;AACA;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KA9qBqGvI,EAAE,CAAAwI,iBAAA,CA8qBXmR,iBAAiB,EAAc,CAAC;IAChH5R,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBkC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEgD,WAAW,EAAE,CAAC;MAC5BnG,IAAI,EAAExH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyZ,wBAAwB,SAAShX,mBAAmB,CAAC;EACvD8E,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGY,SAAS,CAAC;IACnB,IAAI,CAACgC,OAAO,GAAGpK,MAAM,CAAC6J,oBAAoB,CAAC;IAC3C,IAAI,CAACkB,QAAQ,GAAG/K,MAAM,CAACuD,YAAY,CAAC;IACpC,IAAI,CAACoW,QAAQ,GAAG3Z,MAAM,CAAC8K,mBAAmB,CAAC;IAC3C,IAAI,CAACW,QAAQ,GAAG,IAAI,CAACrB,OAAO,CAACb,SAAS,IAAI,QAAQ;IAClD,IAAI,CAACqQ,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACpS,IAAI,GAAG,UAAU;EAC1B;EACA6D,WAAWA,CAACoF,IAAI,EAAE;IACd,IAAI,IAAI,CAACkJ,gBAAgB,KAAK,KAAK,EAAE;MACjC,OAAO,IAAI,CAACD,QAAQ,CAACrO,WAAW,CAACoF,IAAI,CAAC;IAC1C;IACA,MAAM;MAAElF,MAAM;MAAED;IAAM,CAAC,GAAGmF,IAAI;IAC9B,MAAM/E,QAAQ,GAAG,IAAI,CAACgO,QAAQ,CAAC1O,QAAQ,EAAEW,aAAa,CAAC,CAAC,IAAI5K,iBAAiB;IAC7E,MAAM+J,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACa,aAAa,CAAC,CAAC;IAC9C,MAAM;MAAErC,SAAS;MAAEG,SAAS;MAAEC;IAAO,CAAC,GAAG,IAAI,CAACS,OAAO;IACrD,MAAMd,KAAK,GAAG,IAAI,CAACc,OAAO,CAACd,KAAK,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI,CAACc,OAAO,CAACd,KAAK;IAC3E,MAAM4C,SAAS,GAAG;MACdJ,GAAG,EAAEH,QAAQ,CAACI,MAAM,GAAGhB,QAAQ,CAACe,GAAG;MACnCG,IAAI,EAAEN,QAAQ,CAACM,IAAI,GAAGtC,MAAM,GAAGoB,QAAQ,CAACkB,IAAI;MAC5CD,KAAK,EAAEjB,QAAQ,CAACiB,KAAK,GAAGL,QAAQ,CAACK,KAAK,GAAGrC,MAAM;MAC/CoC,MAAM,EAAEhB,QAAQ,CAACgB,MAAM,GAAGJ,QAAQ,CAACG;IACvC,CAAC;IACD,MAAMQ,QAAQ,GAAG;MACbR,GAAG,EAAEH,QAAQ,CAACI,MAAM,GAAGP,MAAM,GAAG,IAAI,CAACqO,sBAAsB,GAAG,CAAC;MAC/D5N,IAAI,EAAEN,QAAQ,CAACM,IAAI,GAAGV,KAAK,GAAG5B,MAAM;MACpCqC,KAAK,EAAEL,QAAQ,CAACK,KAAK,GAAGrC,MAAM;MAC9BoC,MAAM,EAAEJ,QAAQ,CAACG,GAAG,GAAG,IAAI,CAAC+N,sBAAsB,GAAG,CAAC,CAAE;IAC5D,CAAC;IACD,MAAMrN,MAAM,GAAGN,SAAS,CAACJ,GAAG,GAAGI,SAAS,CAACH,MAAM,GAAG,KAAK,GAAG,QAAQ;IAClE,MAAM+N,OAAO,GAAG5N,SAAS,CAACD,IAAI,GAAGC,SAAS,CAACF,KAAK,GAAGM,QAAQ,CAACL,IAAI,GAAGK,QAAQ,CAACN,KAAK;IACjF,MAAMC,IAAI,GAAGC,SAAS,CAAC5C,KAAK,CAAC,GAAGiC,KAAK,GAAGe,QAAQ,CAAChD,KAAK,CAAC,GAAGwQ,OAAO;IACjE,IAAK5N,SAAS,CAAC,IAAI,CAACT,QAAQ,CAAC,GAAG/B,SAAS,IAAIH,SAAS,IAClD,IAAI,CAACkC,QAAQ,KAAKe,MAAM,EAAE;MAC1B,IAAI,CAACmN,QAAQ,CAACvO,aAAa,CAAC,IAAI,CAACK,QAAQ,CAAC;MAC1C,OAAO,CAACa,QAAQ,CAAC,IAAI,CAACb,QAAQ,CAAC,EAAEQ,IAAI,CAAC;IAC1C;IACA,IAAI,CAACR,QAAQ,GAAGe,MAAM;IACtB,IAAI,CAACmN,QAAQ,CAACvO,aAAa,CAACoB,MAAM,CAAC;IACnC,OAAO,CAACF,QAAQ,CAACE,MAAM,CAAC,EAAEP,IAAI,CAAC;EACnC;EACA;IAAS,IAAI,CAACvE,IAAI;MAAA,IAAAqS,qCAAA;MAAA,gBAAAC,iCAAApS,CAAA;QAAA,QAAAmS,qCAAA,KAAAA,qCAAA,GApuB+Era,EAAE,CAAA6I,qBAAA,CAouBQmR,wBAAwB,IAAA9R,CAAA,IAAxB8R,wBAAwB;MAAA;IAAA,IAAqD;EAAE;EAC1L;IAAS,IAAI,CAAClR,IAAI,kBAruB+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EAquBJiS,wBAAwB;MAAAnP,SAAA;MAAAC,MAAA;QAAAoP,gBAAA;QAAAC,sBAAA;MAAA;MAAAnR,UAAA;MAAAC,QAAA,GAruBtBjJ,EAAE,CAAAiL,kBAAA,CAquByL,CAACG,mBAAmB,EAAE3H,qBAAqB,CAACuW,wBAAwB,CAAC,CAAC,GAruBjQha,EAAE,CAAAkJ,0BAAA;IAAA,EAquBuS;EAAE;AAChZ;AACA;EAAA,QAAAX,SAAA,oBAAAA,SAAA,KAvuBqGvI,EAAE,CAAAwI,iBAAA,CAuuBXwR,wBAAwB,EAAc,CAAC;IACvHjS,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBkC,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CAACC,mBAAmB,EAAE3H,qBAAqB,CAACuW,wBAAwB,CAAC;IACpF,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEE,gBAAgB,EAAE,CAAC;MACjCnS,IAAI,EAAExH;IACV,CAAC,CAAC;IAAE4Z,sBAAsB,EAAE,CAAC;MACzBpS,IAAI,EAAExH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMga,oBAAoB,SAAS/W,SAAS,CAAC;EACzCsE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAE0Q,UAAU,IAAK,IAAI,CAACC,OAAO,CAAC/K,SAAS,CAAC8K,UAAU,CAAC,CAAC;IACzD,IAAI,CAACzB,GAAG,GAAGzW,MAAM,CAACgG,QAAQ,CAAC;IAC3B,IAAI,CAACkU,GAAG,GAAGla,MAAM,CAACa,gBAAgB,CAAC;IACnC,IAAI,CAAC6T,QAAQ,GAAG1U,MAAM,CAACkL,oBAAoB,CAAC;IAC5C,IAAI,CAACuC,EAAE,GAAGjM,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAAC2Y,QAAQ,GAAG,IAAItW,eAAe,CAAC5C,gBAAgB,CAAC;IACrD,IAAI,CAACkX,OAAO,GAAGtT,aAAa,CAAC,CACzB,IAAI,CAACsV,QAAQ,EACbna,MAAM,CAACyD,oBAAoB,CAAC,CAAC0J,IAAI,CAAClJ,GAAG,CAAC,MAAM,IAAI,CAACmW,QAAQ,CAAC,CAAC,CAAC,EAAEjW,MAAM,CAAEkW,KAAK,IAAK,IAAI,CAACC,OAAO,CAACD,KAAK,CAAC,CAAC,EAAE5V,oBAAoB,CAAC,CAACuJ,CAAC,EAAEuC,CAAC,KAAKvC,CAAC,CAACuM,WAAW,KAAKhK,CAAC,CAACgK,WAAW,IAChKvM,CAAC,CAACwM,SAAS,KAAKjK,CAAC,CAACiK,SAAS,IAC3BxM,CAAC,CAACyM,uBAAuB,KAAKlK,CAAC,CAACkK,uBAAuB,CAAC,CAAC,CAChE,CAAC,CAACtN,IAAI,CAAClJ,GAAG,CAAC,CAAC,CAACyW,OAAO,EAAEL,KAAK,CAAC,KAAK;MAC9B,MAAMM,SAAS,GAAG,IAAI,CAAClN,EAAE,CAAC6E,QAAQ,CAAC+H,KAAK,CAACI,uBAAuB,CAAC;MACjE,IAAI,CAACJ,KAAK,GACNM,SAAS,IAAI7Y,aAAa,CAACuY,KAAK,CAACI,uBAAuB,CAAC,GACnDJ,KAAK,GACL,IAAI,CAACA,KAAK;MACpB,OAAQM,SAAS,IAAID,OAAO,CAAC,IAAI,CAACL,KAAK,CAAC,IAAK,IAAI,CAACO,UAAU,CAACP,KAAK,CAAC;IACvE,CAAC,CAAC,CAAC;IACH,IAAI,CAACA,KAAK,GAAGra,MAAM,CAAC8F,SAAS,CAAC;IAC9B,IAAI,CAACwG,QAAQ,GAAG,WAAW;IAC3B,IAAI,CAAC7E,IAAI,GAAG,UAAU;EAC1B;EACA,IAAIoT,oBAAoBA,CAACC,OAAO,EAAE;IAC9B,IAAI,CAACvY,WAAW,CAACuY,OAAO,CAAC,EAAE;MACvB,IAAI,CAACX,QAAQ,CAACjM,IAAI,CAAC4M,OAAO,CAAC;IAC/B;EACJ;EACAlP,aAAaA,CAAA,EAAG;IACZ,QAAQ,IAAI,CAACU,QAAQ;MACjB,KAAK,KAAK;QAAE;UACR,MAAM;YAAEmO;UAAwB,CAAC,GAAG,IAAI,CAACJ,KAAK;UAC9C,MAAM7B,OAAO,GAAG7W,YAAY,CAAC8Y,uBAAuB,CAAC,GAC/CA,uBAAuB,GACvBA,uBAAuB,CAACM,UAAU;UACxC,OAAOvC,OAAO,IAAI7W,YAAY,CAAC6W,OAAO,CAAC,GACjCA,OAAO,CAACpK,qBAAqB,CAAC,CAAC,GAC/BpN,iBAAiB;QAC3B;MACA,KAAK,MAAM;QACP,OAAOwE,eAAe,CAAC,IAAI,CAAC6U,KAAK,CAAC,CAACjM,qBAAqB,CAAC,CAAC;MAC9D;QACI,OAAO,IAAI,CAACiM,KAAK,CAACjM,qBAAqB,CAAC,CAAC;IACjD;EACJ;EACAD,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC6M,KAAK,EAAE;MACZ,IAAI,CAACd,GAAG,CAAC1B,OAAO,CAAC5D,aAAa,CAACqG,WAAW,CAAC,IAAI,CAACD,KAAK,CAAC;IAC1D;EACJ;EACAZ,QAAQA,CAAA,EAAG;IACP,MAAMc,MAAM,GAAG5U,mBAAmB,CAAC,IAAI,CAACmQ,GAAG,CAAC;IAC5C,MAAM0E,SAAS,GAAG,IAAI,CAAC1E,GAAG,CAAC2E,YAAY,CAAC,CAAC;IACzC,MAAMf,KAAK,GAAGa,MAAM,IAAInZ,cAAc,CAACmZ,MAAM,CAAC,IAAI,IAAI,CAACzN,EAAE,CAAC6E,QAAQ,CAAC4I,MAAM,CAAC,GACpE,IAAI,CAACG,mBAAmB,CAACH,MAAM,CAAC,GAC/BC,SAAS,EAAEG,UAAU,IAAIH,SAAS,CAACI,UAAU,CAAC,CAAC,CAAC,IAAK,IAAI,CAAClB,KAAK;IACtE,OAAOA,KAAK,CAACmB,UAAU,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;EACIZ,UAAUA,CAACP,KAAK,EAAE;IACd,MAAM;MAAEoB,cAAc;MAAEC;IAAa,CAAC,GAAGrB,KAAK;IAC9C,MAAMO,UAAU,GAAG,IAAI,CAACe,WAAW,CAACtB,KAAK,CAACI,uBAAuB,CAAC;IAClE,MAAMmB,cAAc,GAAG,IAAI,CAACD,WAAW,CAACD,YAAY,CAAC,IAAI,IAAI,CAACjO,EAAE,CAAC6E,QAAQ,CAACmJ,cAAc,CAAC;IACzF,MAAMI,cAAc,GAAG,IAAI,CAACF,WAAW,CAACF,cAAc,CAAC,IAAI,IAAI,CAAChO,EAAE,CAAC6E,QAAQ,CAACoJ,YAAY,CAAC;IACzF,OAAOd,UAAU,IAAIgB,cAAc,IAAIC,cAAc;EACzD;EACA;AACJ;AACA;EACIF,WAAWA,CAACG,IAAI,EAAE;IACd,OAAO,CAAC,CAAC,IAAI,CAACpH,QAAQ,CAACrH,GAAG,CAAC,CAAC,EAAEsH,QAAQ,CAACC,aAAa,CAACtC,QAAQ,CAACwJ,IAAI,CAAC;EACvE;EACA;AACJ;AACA;EACIxB,OAAOA,CAACD,KAAK,EAAE;IACX,OAAQ,CAAC,IAAI,CAAC5M,EAAE,CAAC6E,QAAQ,CAAC+H,KAAK,CAACI,uBAAuB,CAAC,IACpD,CAAC,IAAI,CAAChN,EAAE,CAACqC,OAAO,CAAC,eAAe,CAAC,IACjCuK,KAAK,CAAC0B,cAAc,CAAC,IAAI,CAACf,KAAK,IAAI,IAAI,CAACvN,EAAE,CAAC;EACnD;EACA4N,mBAAmBA,CAAC7C,OAAO,EAAE;IACzB,MAAM;MAAEwC,KAAK,GAAG,IAAI,CAACgB,SAAS,CAACxD,OAAO;IAAE,CAAC,GAAG,IAAI;IAChD,MAAM;MAAE1M,GAAG;MAAEG,IAAI;MAAEV,KAAK;MAAEC;IAAO,CAAC,GAAGgN,OAAO,CAACpK,qBAAqB,CAAC,CAAC;IACpE,MAAM;MAAE6N,cAAc;MAAEC,YAAY;MAAE9J;IAAM,CAAC,GAAGoG,OAAO;IACvD,MAAM6B,KAAK,GAAG,IAAI,CAAC5D,GAAG,CAAC0F,WAAW,CAAC,CAAC;IACpC,MAAMxQ,QAAQ,GAAG,IAAI,CAAC8B,EAAE,CAACW,qBAAqB,CAAC,CAAC;IAChD4M,KAAK,CAAC3K,KAAK,CAACvE,GAAG,GAAGzJ,KAAK,CAACyJ,GAAG,GAAGH,QAAQ,CAACG,GAAG,CAAC;IAC3CkP,KAAK,CAAC3K,KAAK,CAACpE,IAAI,GAAG5J,KAAK,CAAC4J,IAAI,GAAGN,QAAQ,CAACM,IAAI,CAAC;IAC9C+O,KAAK,CAAC3K,KAAK,CAAC9E,KAAK,GAAGlJ,KAAK,CAACkJ,KAAK,CAAC;IAChCyP,KAAK,CAAC3K,KAAK,CAAC7E,MAAM,GAAGnJ,KAAK,CAACmJ,MAAM,CAAC;IAClCwP,KAAK,CAACoB,WAAW,GAAGlb,qBAAqB,GAAGkR,KAAK,GAAGjR,mBAAmB;IACvEkZ,KAAK,CAACgC,QAAQ,CAACrB,KAAK,CAACsB,UAAU,EAAEL,cAAc,IAAI,CAAC,CAAC;IACrD5B,KAAK,CAACkC,MAAM,CAACvB,KAAK,CAACsB,UAAU,EAAEJ,YAAY,IAAI,CAAC,CAAC;IACjD,OAAO7B,KAAK;EAChB;EACA;AACJ;AACA;EACI2B,SAASA,CAACxD,OAAO,EAAE;IACf,MAAMwC,KAAK,GAAG,IAAI,CAACvE,GAAG,CAACI,aAAa,CAAC,KAAK,CAAC;IAC3C,MAAM;MAAE2F,IAAI;MAAEC,aAAa;MAAEC,aAAa;MAAEC,OAAO;MAAEC;IAAU,CAAC,GAAGC,gBAAgB,CAACrE,OAAO,CAAC;IAC5FwC,KAAK,CAAC3K,KAAK,CAAC/D,QAAQ,GAAG,UAAU;IACjC0O,KAAK,CAAC3K,KAAK,CAACyM,aAAa,GAAG,MAAM;IAClC9B,KAAK,CAAC3K,KAAK,CAAC0M,OAAO,GAAG,GAAG;IACzB/B,KAAK,CAAC3K,KAAK,CAAC2M,UAAU,GAAG,UAAU;IACnChC,KAAK,CAAC3K,KAAK,CAAC4M,SAAS,GAAG,YAAY;IACpCjC,KAAK,CAAC3K,KAAK,CAACuM,SAAS,GAAGA,SAAS;IACjC5B,KAAK,CAAC3K,KAAK,CAACmM,IAAI,GAAGA,IAAI;IACvBxB,KAAK,CAAC3K,KAAK,CAACoM,aAAa,GAAGA,aAAa;IACzCzB,KAAK,CAAC3K,KAAK,CAACqM,aAAa,GAAGA,aAAa;IACzC1B,KAAK,CAAC3K,KAAK,CAACsM,OAAO,GAAGA,OAAO;IAC7B,IAAI,CAACzC,GAAG,CAAC1B,OAAO,CAAC5D,aAAa,CAACgC,WAAW,CAACoE,KAAK,CAAC;IACjD,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,OAAOA,KAAK;EAChB;EACA;IAAS,IAAI,CAACtT,IAAI,YAAAwV,6BAAAtV,CAAA;MAAA,YAAAA,CAAA,IAAyFqS,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACzR,IAAI,kBA52B+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EA42BJwS,oBAAoB;MAAA1P,SAAA;MAAAC,MAAA;QAAA8B,QAAA,GA52BlB5M,EAAE,CAAA+K,YAAA,CAAAC,IAAA;QAAAmQ,oBAAA;MAAA;MAAAnS,UAAA;MAAAC,QAAA,GAAFjJ,EAAE,CAAAiL,kBAAA,CA42BuM,CAClS1H,WAAW,CAACgX,oBAAoB,CAAC,EACjCpX,iBAAiB,CAACoX,oBAAoB,CAAC,CAC1C,GA/2B4Fva,EAAE,CAAAkJ,0BAAA;IAAA,EA+2BtD;EAAE;AACnD;AACA;EAAA,QAAAX,SAAA,oBAAAA,SAAA,KAj3BqGvI,EAAE,CAAAwI,iBAAA,CAi3BX+R,oBAAoB,EAAc,CAAC;IACnHxS,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBkC,QAAQ,EAAE,wBAAwB;MAClCC,SAAS,EAAE,CACP5H,WAAW,CAACgX,oBAAoB,CAAC,EACjCpX,iBAAiB,CAACoX,oBAAoB,CAAC;IAE/C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAE3N,QAAQ,EAAE,CAAC;MACrE7E,IAAI,EAAExH,KAAK;MACX4I,IAAI,EAAE,CAAC,8BAA8B;IACzC,CAAC,CAAC;IAAEgS,oBAAoB,EAAE,CAAC;MACvBpT,IAAI,EAAExH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkd,WAAW,GAAG,CAChBjT,2BAA2B,EAC3B/B,0BAA0B,EAC1B+C,oBAAoB,EACpBnC,oBAAoB,EACpByL,eAAe,EACfyE,qBAAqB,EACrBI,iBAAiB,EACjBP,iBAAiB,EACjBb,gBAAgB,EAChB1F,kBAAkB,EAClBzH,mBAAmB,EACnB4O,wBAAwB,EACxBO,oBAAoB,CACvB;AAED,SAASrM,WAAWA,CAACwE,KAAK,EAAE;EACxB,OAAO5P,mBAAmB,CAAC0I,oBAAoB,EAAE,aAAa,EAAEkH,KAAK,EAAE,CAAC,CAAC,CAAC;AAC9E;AACA,SAASiC,kBAAkBA,CAACjC,KAAK,EAAE;EAC/B,OAAO5P,mBAAmB,CAACgS,eAAe,EAAE,oBAAoB,EAAEpC,KAAK,EAAE,CAAC,CAAC,CAAC;AAChF;AACA,SAASkC,eAAeA,CAAA,EAAG;EACvB,MAAMa,IAAI,GAAG3S,mBAAmB,CAACgS,eAAe,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;EAC/ExU,MAAM,CAACwU,eAAe,CAAC,CAClBK,qBAAqB,CAAC1H,IAAI,CAACrM,kBAAkB,CAAC,CAAC,CAAC,CAChDsM,SAAS,CAAEgF,KAAK,IAAK+C,IAAI,CAACrH,GAAG,CAACsE,KAAK,CAAC,CAAC;EAC1C,OAAO+C,IAAI;AACf;AAEA,MAAMiI,gBAAgB,CAAC;EACnB5V,WAAWA,CAAA,EAAG;IACV,MAAMuC,QAAQ,GAAGvD,oBAAoB,CAAC;MAAEgD,UAAU,EAAE;IAAQ,CAAC,EAAEH,4BAA4B,CAAC;IAC5FU,QAAQ,CAAC/J,MAAM,CAAC6J,oBAAoB,EAAE;MAAEoD,IAAI,EAAE,IAAI;MAAE9B,QAAQ,EAAE;IAAK,CAAC,CAAC,EAAE,IAAI,CAAC;EAChF;EACA;IAAS,IAAI,CAACzD,IAAI,YAAA2V,yBAAAzV,CAAA;MAAA,YAAAA,CAAA,IAAyFwV,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAAC5U,IAAI,kBAt6B+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EAs6BJ2V,gBAAgB;MAAA1U,UAAA;MAAAC,QAAA,GAt6BdjJ,EAAE,CAAAiL,kBAAA,CAs6B6C,CAACb,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC;IAAA,EAAiB;EAAE;AACvM;AACA;EAAA,QAAA7B,SAAA,oBAAAA,SAAA,KAx6BqGvI,EAAE,CAAAwI,iBAAA,CAw6BXkV,gBAAgB,EAAc,CAAC;IAC/G3V,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBmC,SAAS,EAAE,CAACf,0BAA0B,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AACtD,MAAMwT,eAAe,CAAC;EAClB9V,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQxH,MAAM,CAAC6J,oBAAoB,CAAC,CAACL,UAAU,GAAG,MAAM;EACpD;EACA;IAAS,IAAI,CAAC9B,IAAI,YAAA6V,wBAAA3V,CAAA;MAAA,YAAAA,CAAA,IAAyF0V,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAAC9U,IAAI,kBAx7B+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EAw7BJ6V,eAAe;MAAA5U,UAAA;IAAA,EAAqC;EAAE;AACzJ;AACA;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KA17BqGvI,EAAE,CAAAwI,iBAAA,CA07BXoV,eAAe,EAAc,CAAC;IAC9G7V,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAK,CAAC;EAC/B,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;;AAEtD;AACA;AACA;AACA,MAAM8U,YAAY,SAAS9X,UAAU,CAAC;EAClC;IAAS,IAAI,CAACgC,IAAI;MAAA,IAAA+V,yBAAA;MAAA,gBAAAC,qBAAA9V,CAAA;QAAA,QAAA6V,yBAAA,KAAAA,yBAAA,GAn8B+E/d,EAAE,CAAA6I,qBAAA,CAm8BQiV,YAAY,IAAA5V,CAAA,IAAZ4V,YAAY;MAAA;IAAA,IAAqD;EAAE;EAC9K;IAAS,IAAI,CAACvM,IAAI,kBAp8B+EvR,EAAE,CAAAwR,iBAAA;MAAAzJ,IAAA,EAo8BJ+V,YAAY;MAAAjT,SAAA;MAAAoT,SAAA;MAAAjV,UAAA;MAAAC,QAAA,GAp8BVjJ,EAAE,CAAAiL,kBAAA,CAo8ByI,CAAChF,WAAW,CAACsD,kBAAkB,CAAC,CAAC,GAp8B5KvJ,EAAE,CAAAkJ,0BAAA,EAAFlJ,EAAE,CAAA2R,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAmM,sBAAA/W,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnH,EAAE,CAAAme,kBAAA,WAo8B0P,CAAC;QAAA;MAAA;MAAAC,aAAA;MAAA9L,eAAA;IAAA,EAAwE;EAAE;AAC5a;AACA;EAAA,QAAA/J,SAAA,oBAAAA,SAAA,KAt8BqGvI,EAAE,CAAAwI,iBAAA,CAs8BXsV,YAAY,EAAc,CAAC;IAC3G/V,IAAI,EAAEhH,SAAS;IACfoI,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBkC,QAAQ,EAAE,eAAe;MACzB6G,QAAQ,EAAE,iCAAiC;MAC3CO,eAAe,EAAEtR,uBAAuB,CAACqd,MAAM;MAC/ClT,SAAS,EAAE,CAAClF,WAAW,CAACsD,kBAAkB,CAAC,CAAC;MAC5CkG,IAAI,EAAE;QAAEkB,KAAK,EAAE;MAA0C;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM2N,mBAAmB,CAAC;EACtB;IAAS,IAAI,CAACtW,IAAI,YAAAuW,4BAAArW,CAAA;MAAA,YAAAA,CAAA,IAAyFoW,mBAAmB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAACxV,IAAI,kBAp9B+E9I,EAAE,CAAA+I,iBAAA;MAAAhB,IAAA,EAo9BJuW,mBAAmB;MAAAtV,UAAA;MAAAC,QAAA,GAp9BjBjJ,EAAE,CAAAsP,uBAAA;QAAAC,SAAA,EAo9BmEuF,eAAe;QAAAhK,MAAA;QAAAmC,OAAA;MAAA;IAAA,EAAuJ;EAAE;AAClV;AACA;EAAA,QAAA1E,SAAA,oBAAAA,SAAA,KAt9BqGvI,EAAE,CAAAwI,iBAAA,CAs9BX8V,mBAAmB,EAAc,CAAC;IAClHvW,IAAI,EAAE7H,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBwG,cAAc,EAAE,CACZ;QACID,SAAS,EAAEuF,eAAe;QAC1BhK,MAAM,EAAE,CAAC,uBAAuB,EAAE,oBAAoB,CAAC;QACvDmC,OAAO,EAAE,CAAC,mCAAmC;MACjD,CAAC;IAET,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS7D,sBAAsB,EAAEE,oBAAoB,EAAEK,4BAA4B,EAAEuK,kCAAkC,EAAEG,0BAA0B,EAAElK,oBAAoB,EAAEsT,WAAW,EAAEG,eAAe,EAAEvU,oBAAoB,EAAEwJ,kBAAkB,EAAErH,oBAAoB,EAAE3D,iBAAiB,EAAEY,0BAA0B,EAAEiV,gBAAgB,EAAEnF,gBAAgB,EAAEa,iBAAiB,EAAEtE,eAAe,EAAEyE,qBAAqB,EAAE/O,2BAA2B,EAAEmP,iBAAiB,EAAEvO,mBAAmB,EAAE4O,wBAAwB,EAAEO,oBAAoB,EAAEhR,kBAAkB,EAAEuU,YAAY,EAAEQ,mBAAmB,EAAEpQ,WAAW,EAAEyG,kBAAkB,EAAEL,+BAA+B,EAAEM,eAAe,EAAExK,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}