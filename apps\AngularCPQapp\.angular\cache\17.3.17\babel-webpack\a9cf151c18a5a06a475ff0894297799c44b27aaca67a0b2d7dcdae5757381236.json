{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive, inject, Input } from '@angular/core';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiWithStyles, tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiZonefull, tuiWatch } from '@taiga-ui/cdk/observables';\nimport { BehaviorSubject, combineLatest, map, distinctUntilChanged, of, delay } from 'rxjs';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nconst _c0 = [\"tuiProgressBar\", \"\"];\nconst _c1 = [\"tuiProgressLabel\", \"\"];\nconst _c2 = [[[\"progress\"]], [[\"tui-progress-circle\"]], \"*\"];\nconst _c3 = [\"progress\", \"tui-progress-circle\", \"*\"];\nclass TuiProgressFixedGradientStyles {\n  static {\n    this.ɵfac = function TuiProgressFixedGradientStyles_Factory(t) {\n      return new (t || TuiProgressFixedGradientStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiProgressFixedGradientStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-fixed-gradient\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiProgressFixedGradientStyles_Template(rf, ctx) {},\n      styles: [\"[tuiProgressFixedGradient]::-moz-progress-bar{inline-size:100%!important;clip-path:inset(0 calc(100% - var(--tui-progress-percent)) 0 0 round var(--tui-radius-m));-moz-transition:clip-path var(--tui-duration) linear;transition:clip-path var(--tui-duration) linear;margin-right:calc(-100% + var(--tui-progress-percent))}[tuiProgressFixedGradient]::-webkit-progress-value{inline-size:100%!important;clip-path:inset(0 calc(100% - var(--tui-progress-percent)) 0 0 round var(--tui-radius-m));-webkit-transition:clip-path var(--tui-duration) linear;transition:clip-path var(--tui-duration) linear}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiProgressFixedGradientStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-fixed-gradient'\n      },\n      styles: [\"[tuiProgressFixedGradient]::-moz-progress-bar{inline-size:100%!important;clip-path:inset(0 calc(100% - var(--tui-progress-percent)) 0 0 round var(--tui-radius-m));-moz-transition:clip-path var(--tui-duration) linear;transition:clip-path var(--tui-duration) linear;margin-right:calc(-100% + var(--tui-progress-percent))}[tuiProgressFixedGradient]::-webkit-progress-value{inline-size:100%!important;clip-path:inset(0 calc(100% - var(--tui-progress-percent)) 0 0 round var(--tui-radius-m));-webkit-transition:clip-path var(--tui-duration) linear;transition:clip-path var(--tui-duration) linear}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiProgressFixedGradientDirective {\n  constructor() {\n    this.nativeProgress = tuiInjectElement();\n    this.nothing = tuiWithStyles(TuiProgressFixedGradientStyles);\n  }\n  get progressPercent() {\n    const {\n      value\n    } = this.nativeProgress;\n    const max = this.nativeProgress.max ?? 1;\n    return Math.min(value / max * 100, 100);\n  }\n  static {\n    this.ɵfac = function TuiProgressFixedGradientDirective_Factory(t) {\n      return new (t || TuiProgressFixedGradientDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiProgressFixedGradientDirective,\n      selectors: [[\"progress\", \"tuiProgressBar\", \"\", \"tuiProgressFixedGradient\", \"\"]],\n      hostVars: 2,\n      hostBindings: function TuiProgressFixedGradientDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--tui-progress-percent\", ctx.progressPercent, \"%\");\n        }\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiProgressFixedGradientDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'progress[tuiProgressBar][tuiProgressFixedGradient]',\n      host: {\n        '[style.--tui-progress-percent.%]': 'progressPercent'\n      }\n    }]\n  }], null, null);\n})();\nconst TUI_PROGRESS_DEFAULT_OPTIONS = {\n  color: null,\n  size: 'm'\n};\nconst TUI_PROGRESS_OPTIONS = tuiCreateToken(TUI_PROGRESS_DEFAULT_OPTIONS);\nfunction tuiProgressOptionsProvider(options) {\n  return tuiProvideOptions(TUI_PROGRESS_OPTIONS, options, TUI_PROGRESS_DEFAULT_OPTIONS);\n}\nclass TuiProgressBar {\n  constructor() {\n    this.options = inject(TUI_PROGRESS_OPTIONS);\n    this.color = this.options.color;\n    this.size = this.options.size;\n  }\n  static {\n    this.ɵfac = function TuiProgressBar_Factory(t) {\n      return new (t || TuiProgressBar)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiProgressBar,\n      selectors: [[\"progress\", \"tuiProgressBar\", \"\"]],\n      hostVars: 3,\n      hostBindings: function TuiProgressBar_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n          i0.ɵɵstyleProp(\"--tui-progress-color\", ctx.color);\n        }\n      },\n      inputs: {\n        color: \"color\",\n        size: \"size\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 0,\n      vars: 0,\n      template: function TuiProgressBar_Template(rf, ctx) {},\n      styles: [\"@keyframes tuiIndeterminateAnimation{50%{background-position:left}}[tuiProgressBar]{-webkit-appearance:none;appearance:none;border:none;--t-height: .75rem;display:block;inline-size:100%;block-size:var(--t-height);color:var(--tui-background-accent-1);background:var(--tui-background-neutral-2);clip-path:inset(0 .5px round var(--tui-radius-m));overflow:hidden;border-radius:1rem;flex-shrink:0}[tuiProgressBar]::-webkit-progress-value{-webkit-transition:width var(--tui-duration) linear;transition:width var(--tui-duration) linear}[tuiProgressBar]::-webkit-progress-value{background:var(--tui-progress-color, currentColor);border-radius:inherit}[tuiProgressBar]::-moz-progress-bar{background:var(--tui-progress-color, currentColor);border-radius:inherit}[tuiProgressBar][data-size=xxs]{--t-height: .125rem}[tuiProgressBar][data-size=xs]{--t-height: .25rem}[tuiProgressBar][data-size=s]{--t-height: .5rem}[tuiProgressBar][data-size=l]{--t-height: 1rem}[tuiProgressBar][data-size=xl]{--t-height: 1.25rem}[tuiProgressBar][data-size=xxl]{--t-height: 1.5rem}[tuiProgressBar]:indeterminate{background:linear-gradient(to right,var(--tui-background-neutral-2) 0 45%,var(--tui-progress-color, currentColor) 45% 55%,var(--tui-background-neutral-2) 55% 100%) right;background-size:225%;animation:tuiIndeterminateAnimation 3s infinite ease-in-out}[tuiProgressBar]:indeterminate::-webkit-progress-value{background:transparent}[tuiProgressBar]:indeterminate::-moz-progress-bar{background:transparent}[tuiProgressBar]::-webkit-progress-inner-element{border-radius:inherit}[tuiProgressBar]::-webkit-progress-bar{background:transparent;border-radius:inherit}label[tuiProgressLabel] [tuiProgressBar]:not(:first-child){position:absolute;top:0;left:0;inline-size:100%;block-size:100%;background:transparent}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiProgressBar, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'progress[tuiProgressBar]',\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[style.--tui-progress-color]': 'color',\n        '[attr.data-size]': 'size'\n      },\n      styles: [\"@keyframes tuiIndeterminateAnimation{50%{background-position:left}}[tuiProgressBar]{-webkit-appearance:none;appearance:none;border:none;--t-height: .75rem;display:block;inline-size:100%;block-size:var(--t-height);color:var(--tui-background-accent-1);background:var(--tui-background-neutral-2);clip-path:inset(0 .5px round var(--tui-radius-m));overflow:hidden;border-radius:1rem;flex-shrink:0}[tuiProgressBar]::-webkit-progress-value{-webkit-transition:width var(--tui-duration) linear;transition:width var(--tui-duration) linear}[tuiProgressBar]::-webkit-progress-value{background:var(--tui-progress-color, currentColor);border-radius:inherit}[tuiProgressBar]::-moz-progress-bar{background:var(--tui-progress-color, currentColor);border-radius:inherit}[tuiProgressBar][data-size=xxs]{--t-height: .125rem}[tuiProgressBar][data-size=xs]{--t-height: .25rem}[tuiProgressBar][data-size=s]{--t-height: .5rem}[tuiProgressBar][data-size=l]{--t-height: 1rem}[tuiProgressBar][data-size=xl]{--t-height: 1.25rem}[tuiProgressBar][data-size=xxl]{--t-height: 1.5rem}[tuiProgressBar]:indeterminate{background:linear-gradient(to right,var(--tui-background-neutral-2) 0 45%,var(--tui-progress-color, currentColor) 45% 55%,var(--tui-background-neutral-2) 55% 100%) right;background-size:225%;animation:tuiIndeterminateAnimation 3s infinite ease-in-out}[tuiProgressBar]:indeterminate::-webkit-progress-value{background:transparent}[tuiProgressBar]:indeterminate::-moz-progress-bar{background:transparent}[tuiProgressBar]::-webkit-progress-inner-element{border-radius:inherit}[tuiProgressBar]::-webkit-progress-bar{background:transparent;border-radius:inherit}label[tuiProgressLabel] [tuiProgressBar]:not(:first-child){position:absolute;top:0;left:0;inline-size:100%;block-size:100%;background:transparent}\\n\"]\n    }]\n  }], null, {\n    color: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiProgressColorSegments {\n  constructor() {\n    this.colors$ = new BehaviorSubject([]);\n    this.el = tuiInjectElement();\n    this.color = toSignal(combineLatest([this.colors$, inject(ResizeObserverService, {\n      self: true\n    }).pipe(map(() => this.el.offsetWidth), distinctUntilChanged())]).pipe(map(([colors, width]) => {\n      const segmentWidth = Math.ceil(width / colors.length);\n      const colorsString = colors.reduce((acc, color, i) => `${acc}, ${color} ${i * segmentWidth}px ${(i + 1) * segmentWidth}px`, '');\n      return `linear-gradient(to right ${colorsString})`;\n    }), tuiZonefull(), tuiWatch()));\n  }\n  set colors(colors) {\n    this.colors$.next(colors);\n  }\n  static {\n    this.ɵfac = function TuiProgressColorSegments_Factory(t) {\n      return new (t || TuiProgressColorSegments)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiProgressColorSegments,\n      selectors: [[\"progress\", \"tuiProgressBar\", \"\", \"tuiProgressColorSegments\", \"\"]],\n      hostVars: 2,\n      hostBindings: function TuiProgressColorSegments_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--tui-progress-color\", ctx.color());\n        }\n      },\n      inputs: {\n        colors: [i0.ɵɵInputFlags.None, \"tuiProgressColorSegments\", \"colors\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([ResizeObserverService])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiProgressColorSegments, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'progress[tuiProgressBar][tuiProgressColorSegments]',\n      providers: [ResizeObserverService],\n      host: {\n        '[style.--tui-progress-color]': 'color()'\n      }\n    }]\n  }], null, {\n    colors: [{\n      type: Input,\n      args: ['tuiProgressColorSegments']\n    }]\n  });\n})();\nclass TuiProgressCircle {\n  constructor() {\n    this.options = inject(TUI_PROGRESS_OPTIONS);\n    this.animationDelay = toSignal(of(true).pipe(delay(0)));\n    this.value = 0;\n    this.max = 1;\n    this.color = this.options.color;\n    this.size = this.options.size;\n    this.arc = false;\n  }\n  get progressRatio() {\n    const ratio = this.value / this.max;\n    return Number.isFinite(ratio) ? ratio : 0;\n  }\n  static {\n    this.ɵfac = function TuiProgressCircle_Factory(t) {\n      return new (t || TuiProgressCircle)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiProgressCircle,\n      selectors: [[\"tui-progress-circle\"]],\n      hostVars: 7,\n      hostBindings: function TuiProgressCircle_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n          i0.ɵɵstyleProp(\"--tui-progress-color\", ctx.color)(\"--t-progress-ratio\", ctx.progressRatio);\n          i0.ɵɵclassProp(\"_arc\", ctx.arc);\n        }\n      },\n      inputs: {\n        value: \"value\",\n        max: \"max\",\n        color: \"color\",\n        size: \"size\",\n        arc: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"arc\", \"arc\", coerceBooleanProperty]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 4,\n      consts: [[1, \"t-hidden-progress\", 3, \"max\", \"value\"], [\"aria-hidden\", \"true\", \"height\", \"100%\", \"width\", \"100%\", 1, \"t-svg\"], [\"cx\", \"50%\", \"cy\", \"50%\", 1, \"t-track\"], [\"cx\", \"50%\", \"cy\", \"50%\", 1, \"t-progress\"]],\n      template: function TuiProgressCircle_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"progress\", 0);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 1);\n          i0.ɵɵelement(2, \"circle\", 2)(3, \"circle\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"max\", ctx.max)(\"value\", ctx.value);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"t-progress_filled\", ctx.animationDelay());\n        }\n      },\n      styles: [\"[_nghost-%COMP%]{--t-stroke-width: var(--tui-thickness, .375em);position:relative;display:block;color:var(--tui-background-accent-1);transform:rotate(-90deg);transform-origin:center;font-size:1rem;inline-size:var(--t-diameter);min-inline-size:var(--t-diameter);block-size:var(--t-diameter);border-radius:100%;-webkit-mask:radial-gradient(closest-side,transparent calc(100% - var(--t-stroke-width)),#000 calc(100% - var(--t-stroke-width) + .5px));mask:radial-gradient(closest-side,transparent calc(100% - var(--t-stroke-width)),#000 calc(100% - var(--t-stroke-width) + .5px))}._arc[_nghost-%COMP%]{transform:rotate(135deg)}[data-size=xxs][_nghost-%COMP%]{--t-diameter: 2em}[data-size=xs][_nghost-%COMP%]{--t-diameter: 2.5em}[data-size=s][_nghost-%COMP%]{--t-diameter: 3.5em}[data-size=m][_nghost-%COMP%]{--t-diameter: 4em}[data-size=l][_nghost-%COMP%]{--t-diameter: 5em}[data-size=xl][_nghost-%COMP%]{--t-diameter: 6em}[data-size=xxl][_nghost-%COMP%]{--t-diameter: 8em}.t-track[_ngcontent-%COMP%], .t-progress[_ngcontent-%COMP%]{fill:transparent;stroke-linecap:round;stroke-width:var(--t-stroke-width);r:calc((var(--t-diameter) - var(--t-stroke-width)) / 2)}.t-track[_ngcontent-%COMP%]{stroke:var(--tui-background-neutral-1)}._arc[_nghost-%COMP%]   .t-track[_ngcontent-%COMP%]{stroke-dasharray:calc(.75 * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2))) calc(.25 * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)))}.t-progress[_ngcontent-%COMP%]{stroke:var(--tui-progress-color, currentColor);stroke-dasharray:calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2));stroke-dashoffset:calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2))}.t-progress_filled[_ngcontent-%COMP%]{transition-property:stroke-dashoffset,stroke-dasharray;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;transition-timing-function:linear;stroke-dashoffset:calc(calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)) - var(--t-progress-ratio) * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)))}._arc[_nghost-%COMP%]   .t-progress[_ngcontent-%COMP%]{stroke-dashoffset:.1px;stroke-dasharray:calc(.75 * var(--t-progress-ratio) * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2))) calc(calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)))}.t-hidden-progress[_ngcontent-%COMP%]{position:absolute;clip:rect(1px,1px,1px,1px);clip-path:inset(50%);block-size:1px;inline-size:1px;margin:-1px;overflow:hidden;padding:0}.t-svg[_ngcontent-%COMP%]{overflow:unset}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiProgressCircle, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-progress-circle',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[attr.data-size]': 'size',\n        '[style.--tui-progress-color]': 'color',\n        '[style.--t-progress-ratio]': 'progressRatio',\n        '[class._arc]': 'arc'\n      },\n      template: \"<progress\\n    class=\\\"t-hidden-progress\\\"\\n    [max]=\\\"max\\\"\\n    [value]=\\\"value\\\"\\n></progress>\\n\\n<svg\\n    aria-hidden=\\\"true\\\"\\n    height=\\\"100%\\\"\\n    width=\\\"100%\\\"\\n    class=\\\"t-svg\\\"\\n>\\n    <circle\\n        cx=\\\"50%\\\"\\n        cy=\\\"50%\\\"\\n        class=\\\"t-track\\\"\\n    />\\n\\n    <circle\\n        cx=\\\"50%\\\"\\n        cy=\\\"50%\\\"\\n        class=\\\"t-progress\\\"\\n        [class.t-progress_filled]=\\\"animationDelay()\\\"\\n    />\\n</svg>\\n\",\n      styles: [\":host{--t-stroke-width: var(--tui-thickness, .375em);position:relative;display:block;color:var(--tui-background-accent-1);transform:rotate(-90deg);transform-origin:center;font-size:1rem;inline-size:var(--t-diameter);min-inline-size:var(--t-diameter);block-size:var(--t-diameter);border-radius:100%;-webkit-mask:radial-gradient(closest-side,transparent calc(100% - var(--t-stroke-width)),#000 calc(100% - var(--t-stroke-width) + .5px));mask:radial-gradient(closest-side,transparent calc(100% - var(--t-stroke-width)),#000 calc(100% - var(--t-stroke-width) + .5px))}:host._arc{transform:rotate(135deg)}:host[data-size=xxs]{--t-diameter: 2em}:host[data-size=xs]{--t-diameter: 2.5em}:host[data-size=s]{--t-diameter: 3.5em}:host[data-size=m]{--t-diameter: 4em}:host[data-size=l]{--t-diameter: 5em}:host[data-size=xl]{--t-diameter: 6em}:host[data-size=xxl]{--t-diameter: 8em}.t-track,.t-progress{fill:transparent;stroke-linecap:round;stroke-width:var(--t-stroke-width);r:calc((var(--t-diameter) - var(--t-stroke-width)) / 2)}.t-track{stroke:var(--tui-background-neutral-1)}:host._arc .t-track{stroke-dasharray:calc(.75 * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2))) calc(.25 * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)))}.t-progress{stroke:var(--tui-progress-color, currentColor);stroke-dasharray:calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2));stroke-dashoffset:calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2))}.t-progress_filled{transition-property:stroke-dashoffset,stroke-dasharray;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;transition-timing-function:linear;stroke-dashoffset:calc(calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)) - var(--t-progress-ratio) * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)))}:host._arc .t-progress{stroke-dashoffset:.1px;stroke-dasharray:calc(.75 * var(--t-progress-ratio) * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2))) calc(calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)))}.t-hidden-progress{position:absolute;clip:rect(1px,1px,1px,1px);clip-path:inset(50%);block-size:1px;inline-size:1px;margin:-1px;overflow:hidden;padding:0}.t-svg{overflow:unset}\\n\"]\n    }]\n  }], null, {\n    value: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    arc: [{\n      type: Input,\n      args: [{\n        transform: coerceBooleanProperty\n      }]\n    }]\n  });\n})();\nclass TuiProgressLabel {\n  static {\n    this.ɵfac = function TuiProgressLabel_Factory(t) {\n      return new (t || TuiProgressLabel)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiProgressLabel,\n      selectors: [[\"label\", \"tuiProgressLabel\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c1,\n      ngContentSelectors: _c3,\n      decls: 4,\n      vars: 0,\n      consts: [[1, \"t-label\"]],\n      template: function TuiProgressLabel_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵprojection(0);\n          i0.ɵɵprojection(1, 1);\n          i0.ɵɵelementStart(2, \"span\", 0);\n          i0.ɵɵprojection(3, 2);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"[_nghost-%COMP%]{position:relative;display:inline-block;color:var(--tui-text-primary)}.t-label[_ngcontent-%COMP%]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;display:flex;font:var(--tui-font-text-s);flex-direction:column;justify-content:center;align-items:center}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiProgressLabel, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'label[tuiProgressLabel]',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-content select=\\\"progress\\\" />\\n<ng-content select=\\\"tui-progress-circle\\\" />\\n<span class=\\\"t-label\\\">\\n    <ng-content />\\n</span>\\n\",\n      styles: [\":host{position:relative;display:inline-block;color:var(--tui-text-primary)}.t-label{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;display:flex;font:var(--tui-font-text-s);flex-direction:column;justify-content:center;align-items:center}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiProgressSegmentedStyles {\n  static {\n    this.ɵfac = function TuiProgressSegmentedStyles_Factory(t) {\n      return new (t || TuiProgressSegmentedStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiProgressSegmentedStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-progress-segmented\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiProgressSegmentedStyles_Template(rf, ctx) {},\n      styles: [\"[tuiProgressBar]._segmented{--tui-segment-gap: .5rem;-webkit-mask-image:radial-gradient(circle closest-side at calc(var(--t-height) / 2) center,#999 0 99%,transparent calc(99% + .6px) 100%),radial-gradient(circle closest-side at calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)) center,#999 0 99%,transparent calc(99% + .6px) 100%),linear-gradient(to right,transparent 0 calc(var(--t-height) / 2),#999 calc(var(--t-height) / 2) calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)),transparent calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)));mask-image:radial-gradient(circle closest-side at calc(var(--t-height) / 2) center,#999 0 99%,transparent calc(99% + .6px) 100%),radial-gradient(circle closest-side at calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)) center,#999 0 99%,transparent calc(99% + .6px) 100%),linear-gradient(to right,transparent 0 calc(var(--t-height) / 2),#999 calc(var(--t-height) / 2) calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)),transparent calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)));-webkit-mask-size:calc(100% * var(--t-segment-width) + var(--tui-segment-gap) * var(--t-segment-width));mask-size:calc(100% * var(--t-segment-width) + var(--tui-segment-gap) * var(--t-segment-width))}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiProgressSegmentedStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-progress-segmented'\n      },\n      styles: [\"[tuiProgressBar]._segmented{--tui-segment-gap: .5rem;-webkit-mask-image:radial-gradient(circle closest-side at calc(var(--t-height) / 2) center,#999 0 99%,transparent calc(99% + .6px) 100%),radial-gradient(circle closest-side at calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)) center,#999 0 99%,transparent calc(99% + .6px) 100%),linear-gradient(to right,transparent 0 calc(var(--t-height) / 2),#999 calc(var(--t-height) / 2) calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)),transparent calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)));mask-image:radial-gradient(circle closest-side at calc(var(--t-height) / 2) center,#999 0 99%,transparent calc(99% + .6px) 100%),radial-gradient(circle closest-side at calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)) center,#999 0 99%,transparent calc(99% + .6px) 100%),linear-gradient(to right,transparent 0 calc(var(--t-height) / 2),#999 calc(var(--t-height) / 2) calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)),transparent calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)));-webkit-mask-size:calc(100% * var(--t-segment-width) + var(--tui-segment-gap) * var(--t-segment-width));mask-size:calc(100% * var(--t-segment-width) + var(--tui-segment-gap) * var(--t-segment-width))}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiProgressSegmented {\n  constructor() {\n    this.nothing = tuiWithStyles(TuiProgressSegmentedStyles);\n    this.segments = 1;\n  }\n  static {\n    this.ɵfac = function TuiProgressSegmented_Factory(t) {\n      return new (t || TuiProgressSegmented)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiProgressSegmented,\n      selectors: [[\"\", \"tuiProgressBar\", \"\", \"segments\", \"\"]],\n      hostAttrs: [1, \"_segmented\"],\n      hostVars: 2,\n      hostBindings: function TuiProgressSegmented_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-segment-width\", 1 / ctx.segments);\n        }\n      },\n      inputs: {\n        segments: \"segments\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiProgressSegmented, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiProgressBar][segments]',\n      host: {\n        class: '_segmented',\n        '[style.--t-segment-width]': '1 / segments'\n      }\n    }]\n  }], null, {\n    segments: [{\n      type: Input\n    }]\n  });\n})();\nconst TuiProgress = [TuiProgressBar, TuiProgressCircle, TuiProgressColorSegments, TuiProgressFixedGradientDirective, TuiProgressLabel, TuiProgressSegmented];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_PROGRESS_DEFAULT_OPTIONS, TUI_PROGRESS_OPTIONS, TuiProgress, TuiProgressBar, TuiProgressCircle, TuiProgressColorSegments, TuiProgressFixedGradientDirective, TuiProgressLabel, TuiProgressSegmented, tuiProgressOptionsProvider };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Directive", "inject", "Input", "tuiInjectElement", "tuiWithStyles", "tuiCreateToken", "tuiProvideOptions", "toSignal", "ResizeObserverService", "tui<PERSON>onefull", "tuiWatch", "BehaviorSubject", "combineLatest", "map", "distinctUntilChanged", "of", "delay", "coerceBooleanProperty", "_c0", "_c1", "_c2", "_c3", "TuiProgressFixedGradientStyles", "ɵfac", "TuiProgressFixedGradientStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiProgressFixedGradientStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiProgressFixedGradientDirective", "constructor", "nativeProgress", "nothing", "progressPercent", "value", "max", "Math", "min", "TuiProgressFixedGradientDirective_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiProgressFixedGradientDirective_HostBindings", "ɵɵstyleProp", "selector", "TUI_PROGRESS_DEFAULT_OPTIONS", "color", "size", "TUI_PROGRESS_OPTIONS", "tuiProgressOptionsProvider", "options", "TuiProgressBar", "TuiProgressBar_Factory", "TuiProgressBar_HostBindings", "ɵɵattribute", "inputs", "attrs", "TuiProgressBar_Template", "TuiProgressColorSegments", "colors$", "el", "self", "pipe", "offsetWidth", "colors", "width", "segmentWidth", "ceil", "length", "colorsString", "reduce", "acc", "i", "next", "TuiProgressColorSegments_Factory", "TuiProgressColorSegments_HostBindings", "ɵɵInputFlags", "ɵɵProvidersFeature", "providers", "TuiProgressCircle", "animationDelay", "arc", "progressRatio", "ratio", "Number", "isFinite", "TuiProgressCircle_Factory", "TuiProgressCircle_HostBindings", "ɵɵclassProp", "HasDecoratorInputTransform", "ɵɵInputTransformsFeature", "consts", "TuiProgressCircle_Template", "ɵɵelement", "ɵɵnamespaceSVG", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance", "transform", "TuiProgressLabel", "TuiProgressLabel_Factory", "ngContentSelectors", "TuiProgressLabel_Template", "ɵɵprojectionDef", "ɵɵprojection", "TuiProgressSegmentedStyles", "TuiProgressSegmentedStyles_Factory", "TuiProgressSegmentedStyles_Template", "TuiProgressSegmented", "segments", "TuiProgressSegmented_Factory", "TuiProgressSegmented_HostBindings", "TuiProgress"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-progress.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive, inject, Input } from '@angular/core';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiWithStyles, tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiZonefull, tuiWatch } from '@taiga-ui/cdk/observables';\nimport { BehaviorSubject, combineLatest, map, distinctUntilChanged, of, delay } from 'rxjs';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\n\nclass TuiProgressFixedGradientStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressFixedGradientStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiProgressFixedGradientStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-fixed-gradient\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiProgressFixedGradient]::-moz-progress-bar{inline-size:100%!important;clip-path:inset(0 calc(100% - var(--tui-progress-percent)) 0 0 round var(--tui-radius-m));-moz-transition:clip-path var(--tui-duration) linear;transition:clip-path var(--tui-duration) linear;margin-right:calc(-100% + var(--tui-progress-percent))}[tuiProgressFixedGradient]::-webkit-progress-value{inline-size:100%!important;clip-path:inset(0 calc(100% - var(--tui-progress-percent)) 0 0 round var(--tui-radius-m));-webkit-transition:clip-path var(--tui-duration) linear;transition:clip-path var(--tui-duration) linear}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressFixedGradientStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-fixed-gradient',\n                    }, styles: [\"[tuiProgressFixedGradient]::-moz-progress-bar{inline-size:100%!important;clip-path:inset(0 calc(100% - var(--tui-progress-percent)) 0 0 round var(--tui-radius-m));-moz-transition:clip-path var(--tui-duration) linear;transition:clip-path var(--tui-duration) linear;margin-right:calc(-100% + var(--tui-progress-percent))}[tuiProgressFixedGradient]::-webkit-progress-value{inline-size:100%!important;clip-path:inset(0 calc(100% - var(--tui-progress-percent)) 0 0 round var(--tui-radius-m));-webkit-transition:clip-path var(--tui-duration) linear;transition:clip-path var(--tui-duration) linear}\\n\"] }]\n        }] });\nclass TuiProgressFixedGradientDirective {\n    constructor() {\n        this.nativeProgress = tuiInjectElement();\n        this.nothing = tuiWithStyles(TuiProgressFixedGradientStyles);\n    }\n    get progressPercent() {\n        const { value } = this.nativeProgress;\n        const max = this.nativeProgress.max ?? 1;\n        return Math.min((value / max) * 100, 100);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressFixedGradientDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiProgressFixedGradientDirective, isStandalone: true, selector: \"progress[tuiProgressBar][tuiProgressFixedGradient]\", host: { properties: { \"style.--tui-progress-percent.%\": \"progressPercent\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressFixedGradientDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'progress[tuiProgressBar][tuiProgressFixedGradient]',\n                    host: {\n                        '[style.--tui-progress-percent.%]': 'progressPercent',\n                    },\n                }]\n        }] });\n\nconst TUI_PROGRESS_DEFAULT_OPTIONS = {\n    color: null,\n    size: 'm',\n};\nconst TUI_PROGRESS_OPTIONS = tuiCreateToken(TUI_PROGRESS_DEFAULT_OPTIONS);\nfunction tuiProgressOptionsProvider(options) {\n    return tuiProvideOptions(TUI_PROGRESS_OPTIONS, options, TUI_PROGRESS_DEFAULT_OPTIONS);\n}\n\nclass TuiProgressBar {\n    constructor() {\n        this.options = inject(TUI_PROGRESS_OPTIONS);\n        this.color = this.options.color;\n        this.size = this.options.size;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressBar, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiProgressBar, isStandalone: true, selector: \"progress[tuiProgressBar]\", inputs: { color: \"color\", size: \"size\" }, host: { properties: { \"style.--tui-progress-color\": \"color\", \"attr.data-size\": \"size\" } }, ngImport: i0, template: '', isInline: true, styles: [\"@keyframes tuiIndeterminateAnimation{50%{background-position:left}}[tuiProgressBar]{-webkit-appearance:none;appearance:none;border:none;--t-height: .75rem;display:block;inline-size:100%;block-size:var(--t-height);color:var(--tui-background-accent-1);background:var(--tui-background-neutral-2);clip-path:inset(0 .5px round var(--tui-radius-m));overflow:hidden;border-radius:1rem;flex-shrink:0}[tuiProgressBar]::-webkit-progress-value{-webkit-transition:width var(--tui-duration) linear;transition:width var(--tui-duration) linear}[tuiProgressBar]::-webkit-progress-value{background:var(--tui-progress-color, currentColor);border-radius:inherit}[tuiProgressBar]::-moz-progress-bar{background:var(--tui-progress-color, currentColor);border-radius:inherit}[tuiProgressBar][data-size=xxs]{--t-height: .125rem}[tuiProgressBar][data-size=xs]{--t-height: .25rem}[tuiProgressBar][data-size=s]{--t-height: .5rem}[tuiProgressBar][data-size=l]{--t-height: 1rem}[tuiProgressBar][data-size=xl]{--t-height: 1.25rem}[tuiProgressBar][data-size=xxl]{--t-height: 1.5rem}[tuiProgressBar]:indeterminate{background:linear-gradient(to right,var(--tui-background-neutral-2) 0 45%,var(--tui-progress-color, currentColor) 45% 55%,var(--tui-background-neutral-2) 55% 100%) right;background-size:225%;animation:tuiIndeterminateAnimation 3s infinite ease-in-out}[tuiProgressBar]:indeterminate::-webkit-progress-value{background:transparent}[tuiProgressBar]:indeterminate::-moz-progress-bar{background:transparent}[tuiProgressBar]::-webkit-progress-inner-element{border-radius:inherit}[tuiProgressBar]::-webkit-progress-bar{background:transparent;border-radius:inherit}label[tuiProgressLabel] [tuiProgressBar]:not(:first-child){position:absolute;top:0;left:0;inline-size:100%;block-size:100%;background:transparent}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressBar, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'progress[tuiProgressBar]', template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[style.--tui-progress-color]': 'color',\n                        '[attr.data-size]': 'size',\n                    }, styles: [\"@keyframes tuiIndeterminateAnimation{50%{background-position:left}}[tuiProgressBar]{-webkit-appearance:none;appearance:none;border:none;--t-height: .75rem;display:block;inline-size:100%;block-size:var(--t-height);color:var(--tui-background-accent-1);background:var(--tui-background-neutral-2);clip-path:inset(0 .5px round var(--tui-radius-m));overflow:hidden;border-radius:1rem;flex-shrink:0}[tuiProgressBar]::-webkit-progress-value{-webkit-transition:width var(--tui-duration) linear;transition:width var(--tui-duration) linear}[tuiProgressBar]::-webkit-progress-value{background:var(--tui-progress-color, currentColor);border-radius:inherit}[tuiProgressBar]::-moz-progress-bar{background:var(--tui-progress-color, currentColor);border-radius:inherit}[tuiProgressBar][data-size=xxs]{--t-height: .125rem}[tuiProgressBar][data-size=xs]{--t-height: .25rem}[tuiProgressBar][data-size=s]{--t-height: .5rem}[tuiProgressBar][data-size=l]{--t-height: 1rem}[tuiProgressBar][data-size=xl]{--t-height: 1.25rem}[tuiProgressBar][data-size=xxl]{--t-height: 1.5rem}[tuiProgressBar]:indeterminate{background:linear-gradient(to right,var(--tui-background-neutral-2) 0 45%,var(--tui-progress-color, currentColor) 45% 55%,var(--tui-background-neutral-2) 55% 100%) right;background-size:225%;animation:tuiIndeterminateAnimation 3s infinite ease-in-out}[tuiProgressBar]:indeterminate::-webkit-progress-value{background:transparent}[tuiProgressBar]:indeterminate::-moz-progress-bar{background:transparent}[tuiProgressBar]::-webkit-progress-inner-element{border-radius:inherit}[tuiProgressBar]::-webkit-progress-bar{background:transparent;border-radius:inherit}label[tuiProgressLabel] [tuiProgressBar]:not(:first-child){position:absolute;top:0;left:0;inline-size:100%;block-size:100%;background:transparent}\\n\"] }]\n        }], propDecorators: { color: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }] } });\n\nclass TuiProgressColorSegments {\n    constructor() {\n        this.colors$ = new BehaviorSubject([]);\n        this.el = tuiInjectElement();\n        this.color = toSignal(combineLatest([\n            this.colors$,\n            inject(ResizeObserverService, { self: true }).pipe(map(() => this.el.offsetWidth), distinctUntilChanged()),\n        ]).pipe(map(([colors, width]) => {\n            const segmentWidth = Math.ceil(width / colors.length);\n            const colorsString = colors.reduce((acc, color, i) => `${acc}, ${color} ${i * segmentWidth}px ${(i + 1) * segmentWidth}px`, '');\n            return `linear-gradient(to right ${colorsString})`;\n        }), tuiZonefull(), tuiWatch()));\n    }\n    set colors(colors) {\n        this.colors$.next(colors);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressColorSegments, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiProgressColorSegments, isStandalone: true, selector: \"progress[tuiProgressBar][tuiProgressColorSegments]\", inputs: { colors: [\"tuiProgressColorSegments\", \"colors\"] }, host: { properties: { \"style.--tui-progress-color\": \"color()\" } }, providers: [ResizeObserverService], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressColorSegments, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'progress[tuiProgressBar][tuiProgressColorSegments]',\n                    providers: [ResizeObserverService],\n                    host: { '[style.--tui-progress-color]': 'color()' },\n                }]\n        }], propDecorators: { colors: [{\n                type: Input,\n                args: ['tuiProgressColorSegments']\n            }] } });\n\nclass TuiProgressCircle {\n    constructor() {\n        this.options = inject(TUI_PROGRESS_OPTIONS);\n        this.animationDelay = toSignal(of(true).pipe(delay(0)));\n        this.value = 0;\n        this.max = 1;\n        this.color = this.options.color;\n        this.size = this.options.size;\n        this.arc = false;\n    }\n    get progressRatio() {\n        const ratio = this.value / this.max;\n        return Number.isFinite(ratio) ? ratio : 0;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressCircle, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"16.2.12\", type: TuiProgressCircle, isStandalone: true, selector: \"tui-progress-circle\", inputs: { value: \"value\", max: \"max\", color: \"color\", size: \"size\", arc: [\"arc\", \"arc\", coerceBooleanProperty] }, host: { properties: { \"attr.data-size\": \"size\", \"style.--tui-progress-color\": \"color\", \"style.--t-progress-ratio\": \"progressRatio\", \"class._arc\": \"arc\" } }, ngImport: i0, template: \"<progress\\n    class=\\\"t-hidden-progress\\\"\\n    [max]=\\\"max\\\"\\n    [value]=\\\"value\\\"\\n></progress>\\n\\n<svg\\n    aria-hidden=\\\"true\\\"\\n    height=\\\"100%\\\"\\n    width=\\\"100%\\\"\\n    class=\\\"t-svg\\\"\\n>\\n    <circle\\n        cx=\\\"50%\\\"\\n        cy=\\\"50%\\\"\\n        class=\\\"t-track\\\"\\n    />\\n\\n    <circle\\n        cx=\\\"50%\\\"\\n        cy=\\\"50%\\\"\\n        class=\\\"t-progress\\\"\\n        [class.t-progress_filled]=\\\"animationDelay()\\\"\\n    />\\n</svg>\\n\", styles: [\":host{--t-stroke-width: var(--tui-thickness, .375em);position:relative;display:block;color:var(--tui-background-accent-1);transform:rotate(-90deg);transform-origin:center;font-size:1rem;inline-size:var(--t-diameter);min-inline-size:var(--t-diameter);block-size:var(--t-diameter);border-radius:100%;-webkit-mask:radial-gradient(closest-side,transparent calc(100% - var(--t-stroke-width)),#000 calc(100% - var(--t-stroke-width) + .5px));mask:radial-gradient(closest-side,transparent calc(100% - var(--t-stroke-width)),#000 calc(100% - var(--t-stroke-width) + .5px))}:host._arc{transform:rotate(135deg)}:host[data-size=xxs]{--t-diameter: 2em}:host[data-size=xs]{--t-diameter: 2.5em}:host[data-size=s]{--t-diameter: 3.5em}:host[data-size=m]{--t-diameter: 4em}:host[data-size=l]{--t-diameter: 5em}:host[data-size=xl]{--t-diameter: 6em}:host[data-size=xxl]{--t-diameter: 8em}.t-track,.t-progress{fill:transparent;stroke-linecap:round;stroke-width:var(--t-stroke-width);r:calc((var(--t-diameter) - var(--t-stroke-width)) / 2)}.t-track{stroke:var(--tui-background-neutral-1)}:host._arc .t-track{stroke-dasharray:calc(.75 * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2))) calc(.25 * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)))}.t-progress{stroke:var(--tui-progress-color, currentColor);stroke-dasharray:calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2));stroke-dashoffset:calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2))}.t-progress_filled{transition-property:stroke-dashoffset,stroke-dasharray;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;transition-timing-function:linear;stroke-dashoffset:calc(calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)) - var(--t-progress-ratio) * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)))}:host._arc .t-progress{stroke-dashoffset:.1px;stroke-dasharray:calc(.75 * var(--t-progress-ratio) * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2))) calc(calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)))}.t-hidden-progress{position:absolute;clip:rect(1px,1px,1px,1px);clip-path:inset(50%);block-size:1px;inline-size:1px;margin:-1px;overflow:hidden;padding:0}.t-svg{overflow:unset}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressCircle, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-progress-circle', changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[attr.data-size]': 'size',\n                        '[style.--tui-progress-color]': 'color',\n                        '[style.--t-progress-ratio]': 'progressRatio',\n                        '[class._arc]': 'arc',\n                    }, template: \"<progress\\n    class=\\\"t-hidden-progress\\\"\\n    [max]=\\\"max\\\"\\n    [value]=\\\"value\\\"\\n></progress>\\n\\n<svg\\n    aria-hidden=\\\"true\\\"\\n    height=\\\"100%\\\"\\n    width=\\\"100%\\\"\\n    class=\\\"t-svg\\\"\\n>\\n    <circle\\n        cx=\\\"50%\\\"\\n        cy=\\\"50%\\\"\\n        class=\\\"t-track\\\"\\n    />\\n\\n    <circle\\n        cx=\\\"50%\\\"\\n        cy=\\\"50%\\\"\\n        class=\\\"t-progress\\\"\\n        [class.t-progress_filled]=\\\"animationDelay()\\\"\\n    />\\n</svg>\\n\", styles: [\":host{--t-stroke-width: var(--tui-thickness, .375em);position:relative;display:block;color:var(--tui-background-accent-1);transform:rotate(-90deg);transform-origin:center;font-size:1rem;inline-size:var(--t-diameter);min-inline-size:var(--t-diameter);block-size:var(--t-diameter);border-radius:100%;-webkit-mask:radial-gradient(closest-side,transparent calc(100% - var(--t-stroke-width)),#000 calc(100% - var(--t-stroke-width) + .5px));mask:radial-gradient(closest-side,transparent calc(100% - var(--t-stroke-width)),#000 calc(100% - var(--t-stroke-width) + .5px))}:host._arc{transform:rotate(135deg)}:host[data-size=xxs]{--t-diameter: 2em}:host[data-size=xs]{--t-diameter: 2.5em}:host[data-size=s]{--t-diameter: 3.5em}:host[data-size=m]{--t-diameter: 4em}:host[data-size=l]{--t-diameter: 5em}:host[data-size=xl]{--t-diameter: 6em}:host[data-size=xxl]{--t-diameter: 8em}.t-track,.t-progress{fill:transparent;stroke-linecap:round;stroke-width:var(--t-stroke-width);r:calc((var(--t-diameter) - var(--t-stroke-width)) / 2)}.t-track{stroke:var(--tui-background-neutral-1)}:host._arc .t-track{stroke-dasharray:calc(.75 * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2))) calc(.25 * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)))}.t-progress{stroke:var(--tui-progress-color, currentColor);stroke-dasharray:calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2));stroke-dashoffset:calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2))}.t-progress_filled{transition-property:stroke-dashoffset,stroke-dasharray;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;transition-timing-function:linear;stroke-dashoffset:calc(calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)) - var(--t-progress-ratio) * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)))}:host._arc .t-progress{stroke-dashoffset:.1px;stroke-dasharray:calc(.75 * var(--t-progress-ratio) * calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2))) calc(calc(2 * 3.14159265 * calc((var(--t-diameter) - var(--t-stroke-width)) / 2)))}.t-hidden-progress{position:absolute;clip:rect(1px,1px,1px,1px);clip-path:inset(50%);block-size:1px;inline-size:1px;margin:-1px;overflow:hidden;padding:0}.t-svg{overflow:unset}\\n\"] }]\n        }], propDecorators: { value: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], arc: [{\n                type: Input,\n                args: [{ transform: coerceBooleanProperty }]\n            }] } });\n\nclass TuiProgressLabel {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressLabel, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiProgressLabel, isStandalone: true, selector: \"label[tuiProgressLabel]\", ngImport: i0, template: \"<ng-content select=\\\"progress\\\" />\\n<ng-content select=\\\"tui-progress-circle\\\" />\\n<span class=\\\"t-label\\\">\\n    <ng-content />\\n</span>\\n\", styles: [\":host{position:relative;display:inline-block;color:var(--tui-text-primary)}.t-label{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;display:flex;font:var(--tui-font-text-s);flex-direction:column;justify-content:center;align-items:center}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressLabel, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'label[tuiProgressLabel]', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-content select=\\\"progress\\\" />\\n<ng-content select=\\\"tui-progress-circle\\\" />\\n<span class=\\\"t-label\\\">\\n    <ng-content />\\n</span>\\n\", styles: [\":host{position:relative;display:inline-block;color:var(--tui-text-primary)}.t-label{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;display:flex;font:var(--tui-font-text-s);flex-direction:column;justify-content:center;align-items:center}\\n\"] }]\n        }] });\n\nclass TuiProgressSegmentedStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressSegmentedStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiProgressSegmentedStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-progress-segmented\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiProgressBar]._segmented{--tui-segment-gap: .5rem;-webkit-mask-image:radial-gradient(circle closest-side at calc(var(--t-height) / 2) center,#999 0 99%,transparent calc(99% + .6px) 100%),radial-gradient(circle closest-side at calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)) center,#999 0 99%,transparent calc(99% + .6px) 100%),linear-gradient(to right,transparent 0 calc(var(--t-height) / 2),#999 calc(var(--t-height) / 2) calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)),transparent calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)));mask-image:radial-gradient(circle closest-side at calc(var(--t-height) / 2) center,#999 0 99%,transparent calc(99% + .6px) 100%),radial-gradient(circle closest-side at calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)) center,#999 0 99%,transparent calc(99% + .6px) 100%),linear-gradient(to right,transparent 0 calc(var(--t-height) / 2),#999 calc(var(--t-height) / 2) calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)),transparent calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)));-webkit-mask-size:calc(100% * var(--t-segment-width) + var(--tui-segment-gap) * var(--t-segment-width));mask-size:calc(100% * var(--t-segment-width) + var(--tui-segment-gap) * var(--t-segment-width))}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressSegmentedStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { class: 'tui-progress-segmented' }, styles: [\"[tuiProgressBar]._segmented{--tui-segment-gap: .5rem;-webkit-mask-image:radial-gradient(circle closest-side at calc(var(--t-height) / 2) center,#999 0 99%,transparent calc(99% + .6px) 100%),radial-gradient(circle closest-side at calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)) center,#999 0 99%,transparent calc(99% + .6px) 100%),linear-gradient(to right,transparent 0 calc(var(--t-height) / 2),#999 calc(var(--t-height) / 2) calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)),transparent calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)));mask-image:radial-gradient(circle closest-side at calc(var(--t-height) / 2) center,#999 0 99%,transparent calc(99% + .6px) 100%),radial-gradient(circle closest-side at calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)) center,#999 0 99%,transparent calc(99% + .6px) 100%),linear-gradient(to right,transparent 0 calc(var(--t-height) / 2),#999 calc(var(--t-height) / 2) calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)),transparent calc(100% - calc(var(--t-height) / 2) - var(--tui-segment-gap)));-webkit-mask-size:calc(100% * var(--t-segment-width) + var(--tui-segment-gap) * var(--t-segment-width));mask-size:calc(100% * var(--t-segment-width) + var(--tui-segment-gap) * var(--t-segment-width))}\\n\"] }]\n        }] });\nclass TuiProgressSegmented {\n    constructor() {\n        this.nothing = tuiWithStyles(TuiProgressSegmentedStyles);\n        this.segments = 1;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressSegmented, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiProgressSegmented, isStandalone: true, selector: \"[tuiProgressBar][segments]\", inputs: { segments: \"segments\" }, host: { properties: { \"style.--t-segment-width\": \"1 / segments\" }, classAttribute: \"_segmented\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiProgressSegmented, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiProgressBar][segments]',\n                    host: {\n                        class: '_segmented',\n                        '[style.--t-segment-width]': '1 / segments',\n                    },\n                }]\n        }], propDecorators: { segments: [{\n                type: Input\n            }] } });\n\nconst TuiProgress = [\n    TuiProgressBar,\n    TuiProgressCircle,\n    TuiProgressColorSegments,\n    TuiProgressFixedGradientDirective,\n    TuiProgressLabel,\n    TuiProgressSegmented,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_PROGRESS_DEFAULT_OPTIONS, TUI_PROGRESS_OPTIONS, TuiProgress, TuiProgressBar, TuiProgressCircle, TuiProgressColorSegments, TuiProgressFixedGradientDirective, TuiProgressLabel, TuiProgressSegmented, tuiProgressOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,QAAQ,eAAe;AAC/G,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,aAAa,EAAEC,cAAc,EAAEC,iBAAiB,QAAQ,mCAAmC;AACpG,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,WAAW,EAAEC,QAAQ,QAAQ,2BAA2B;AACjE,SAASC,eAAe,EAAEC,aAAa,EAAEC,GAAG,EAAEC,oBAAoB,EAAEC,EAAE,EAAEC,KAAK,QAAQ,MAAM;AAC3F,SAASC,qBAAqB,QAAQ,uBAAuB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAE9D,MAAMC,8BAA8B,CAAC;EACjC;IAAS,IAAI,CAACC,IAAI,YAAAC,uCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,8BAA8B;IAAA,CAAmD;EAAE;EAC9L;IAAS,IAAI,CAACI,IAAI,kBAD+E9B,EAAE,CAAA+B,iBAAA;MAAAC,IAAA,EACJN,8BAA8B;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAD5BpC,EAAE,CAAAqC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACo2B;EAAE;AAC78B;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG/C,EAAE,CAAAgD,iBAAA,CAGXtB,8BAA8B,EAAc,CAAC;IAC7HM,IAAI,EAAE/B,SAAS;IACfgD,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAE3C,iBAAiB,CAACgD,IAAI;MAAEJ,eAAe,EAAE3C,uBAAuB,CAACgD,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,mlBAAmlB;IAAE,CAAC;EAC9mB,CAAC,CAAC;AAAA;AACV,MAAMU,iCAAiC,CAAC;EACpCC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,cAAc,GAAGjD,gBAAgB,CAAC,CAAC;IACxC,IAAI,CAACkD,OAAO,GAAGjD,aAAa,CAACkB,8BAA8B,CAAC;EAChE;EACA,IAAIgC,eAAeA,CAAA,EAAG;IAClB,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI,CAACH,cAAc;IACrC,MAAMI,GAAG,GAAG,IAAI,CAACJ,cAAc,CAACI,GAAG,IAAI,CAAC;IACxC,OAAOC,IAAI,CAACC,GAAG,CAAEH,KAAK,GAAGC,GAAG,GAAI,GAAG,EAAE,GAAG,CAAC;EAC7C;EACA;IAAS,IAAI,CAACjC,IAAI,YAAAoC,0CAAAlC,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,iCAAiC;IAAA,CAAmD;EAAE;EACjM;IAAS,IAAI,CAACU,IAAI,kBApB+EhE,EAAE,CAAAiE,iBAAA;MAAAjC,IAAA,EAoBJsB,iCAAiC;MAAArB,SAAA;MAAAiC,QAAA;MAAAC,YAAA,WAAAC,+CAAA1B,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApB/B1C,EAAE,CAAAqE,WAAA,2BAAA1B,GAAA,CAAAe,eAAA,KAoB4B,CAAC;QAAA;MAAA;MAAAvB,UAAA;IAAA,EAAoL;EAAE;AAC1T;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAtBqG/C,EAAE,CAAAgD,iBAAA,CAsBXM,iCAAiC,EAAc,CAAC;IAChItB,IAAI,EAAE5B,SAAS;IACf6C,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBmC,QAAQ,EAAE,oDAAoD;MAC9DlB,IAAI,EAAE;QACF,kCAAkC,EAAE;MACxC;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMmB,4BAA4B,GAAG;EACjCC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACV,CAAC;AACD,MAAMC,oBAAoB,GAAGjE,cAAc,CAAC8D,4BAA4B,CAAC;AACzE,SAASI,0BAA0BA,CAACC,OAAO,EAAE;EACzC,OAAOlE,iBAAiB,CAACgE,oBAAoB,EAAEE,OAAO,EAAEL,4BAA4B,CAAC;AACzF;AAEA,MAAMM,cAAc,CAAC;EACjBtB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqB,OAAO,GAAGvE,MAAM,CAACqE,oBAAoB,CAAC;IAC3C,IAAI,CAACF,KAAK,GAAG,IAAI,CAACI,OAAO,CAACJ,KAAK;IAC/B,IAAI,CAACC,IAAI,GAAG,IAAI,CAACG,OAAO,CAACH,IAAI;EACjC;EACA;IAAS,IAAI,CAAC9C,IAAI,YAAAmD,uBAAAjD,CAAA;MAAA,YAAAA,CAAA,IAAyFgD,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAAC/C,IAAI,kBAjD+E9B,EAAE,CAAA+B,iBAAA;MAAAC,IAAA,EAiDJ6C,cAAc;MAAA5C,SAAA;MAAAiC,QAAA;MAAAC,YAAA,WAAAY,4BAAArC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjDZ1C,EAAE,CAAAgF,WAAA,cAAArC,GAAA,CAAA8B,IAAA;UAAFzE,EAAE,CAAAqE,WAAA,yBAAA1B,GAAA,CAAA6B,KAiDS,CAAC;QAAA;MAAA;MAAAS,MAAA;QAAAT,KAAA;QAAAC,IAAA;MAAA;MAAAtC,UAAA;MAAAC,QAAA,GAjDZpC,EAAE,CAAAqC,mBAAA;MAAA6C,KAAA,EAAA5D,GAAA;MAAAgB,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAA2C,wBAAAzC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAiD+lE;EAAE;AACxsE;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAnDqG/C,EAAE,CAAAgD,iBAAA,CAmDX6B,cAAc,EAAc,CAAC;IAC7G7C,IAAI,EAAE/B,SAAS;IACfgD,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEmC,QAAQ,EAAE,0BAA0B;MAAE9B,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAE3C,iBAAiB,CAACgD,IAAI;MAAEJ,eAAe,EAAE3C,uBAAuB,CAACgD,MAAM;MAAEC,IAAI,EAAE;QACjK,8BAA8B,EAAE,OAAO;QACvC,kBAAkB,EAAE;MACxB,CAAC;MAAER,MAAM,EAAE,CAAC,6vDAA6vD;IAAE,CAAC;EACxxD,CAAC,CAAC,QAAkB;IAAE4B,KAAK,EAAE,CAAC;MACtBxC,IAAI,EAAE1B;IACV,CAAC,CAAC;IAAEmE,IAAI,EAAE,CAAC;MACPzC,IAAI,EAAE1B;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8E,wBAAwB,CAAC;EAC3B7B,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8B,OAAO,GAAG,IAAItE,eAAe,CAAC,EAAE,CAAC;IACtC,IAAI,CAACuE,EAAE,GAAG/E,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACiE,KAAK,GAAG7D,QAAQ,CAACK,aAAa,CAAC,CAChC,IAAI,CAACqE,OAAO,EACZhF,MAAM,CAACO,qBAAqB,EAAE;MAAE2E,IAAI,EAAE;IAAK,CAAC,CAAC,CAACC,IAAI,CAACvE,GAAG,CAAC,MAAM,IAAI,CAACqE,EAAE,CAACG,WAAW,CAAC,EAAEvE,oBAAoB,CAAC,CAAC,CAAC,CAC7G,CAAC,CAACsE,IAAI,CAACvE,GAAG,CAAC,CAAC,CAACyE,MAAM,EAAEC,KAAK,CAAC,KAAK;MAC7B,MAAMC,YAAY,GAAG/B,IAAI,CAACgC,IAAI,CAACF,KAAK,GAAGD,MAAM,CAACI,MAAM,CAAC;MACrD,MAAMC,YAAY,GAAGL,MAAM,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEzB,KAAK,EAAE0B,CAAC,KAAK,GAAGD,GAAG,KAAKzB,KAAK,IAAI0B,CAAC,GAAGN,YAAY,MAAM,CAACM,CAAC,GAAG,CAAC,IAAIN,YAAY,IAAI,EAAE,EAAE,CAAC;MAC/H,OAAO,4BAA4BG,YAAY,GAAG;IACtD,CAAC,CAAC,EAAElF,WAAW,CAAC,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACnC;EACA,IAAI4E,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,CAACL,OAAO,CAACc,IAAI,CAACT,MAAM,CAAC;EAC7B;EACA;IAAS,IAAI,CAAC/D,IAAI,YAAAyE,iCAAAvE,CAAA;MAAA,YAAAA,CAAA,IAAyFuD,wBAAwB;IAAA,CAAmD;EAAE;EACxL;IAAS,IAAI,CAACpB,IAAI,kBAhF+EhE,EAAE,CAAAiE,iBAAA;MAAAjC,IAAA,EAgFJoD,wBAAwB;MAAAnD,SAAA;MAAAiC,QAAA;MAAAC,YAAA,WAAAkC,sCAAA3D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhFtB1C,EAAE,CAAAqE,WAAA,yBAgFJ1B,GAAA,CAAA6B,KAAA,CAAM,CAAiB,CAAC;QAAA;MAAA;MAAAS,MAAA;QAAAS,MAAA,GAhFtB1F,EAAE,CAAAsG,YAAA,CAAApD,IAAA;MAAA;MAAAf,UAAA;MAAAC,QAAA,GAAFpC,EAAE,CAAAuG,kBAAA,CAgFoP,CAAC3F,qBAAqB,CAAC;IAAA,EAAiB;EAAE;AACrY;AACA;EAAA,QAAAmC,SAAA,oBAAAA,SAAA,KAlFqG/C,EAAE,CAAAgD,iBAAA,CAkFXoC,wBAAwB,EAAc,CAAC;IACvHpD,IAAI,EAAE5B,SAAS;IACf6C,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBmC,QAAQ,EAAE,oDAAoD;MAC9DkC,SAAS,EAAE,CAAC5F,qBAAqB,CAAC;MAClCwC,IAAI,EAAE;QAAE,8BAA8B,EAAE;MAAU;IACtD,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEsC,MAAM,EAAE,CAAC;MACvB1D,IAAI,EAAE1B,KAAK;MACX2C,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMwD,iBAAiB,CAAC;EACpBlD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqB,OAAO,GAAGvE,MAAM,CAACqE,oBAAoB,CAAC;IAC3C,IAAI,CAACgC,cAAc,GAAG/F,QAAQ,CAACQ,EAAE,CAAC,IAAI,CAAC,CAACqE,IAAI,CAACpE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,IAAI,CAACuC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACY,KAAK,GAAG,IAAI,CAACI,OAAO,CAACJ,KAAK;IAC/B,IAAI,CAACC,IAAI,GAAG,IAAI,CAACG,OAAO,CAACH,IAAI;IAC7B,IAAI,CAACkC,GAAG,GAAG,KAAK;EACpB;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,MAAMC,KAAK,GAAG,IAAI,CAAClD,KAAK,GAAG,IAAI,CAACC,GAAG;IACnC,OAAOkD,MAAM,CAACC,QAAQ,CAACF,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC;EAC7C;EACA;IAAS,IAAI,CAAClF,IAAI,YAAAqF,0BAAAnF,CAAA;MAAA,YAAAA,CAAA,IAAyF4E,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAAC3E,IAAI,kBA9G+E9B,EAAE,CAAA+B,iBAAA;MAAAC,IAAA,EA8GJyE,iBAAiB;MAAAxE,SAAA;MAAAiC,QAAA;MAAAC,YAAA,WAAA8C,+BAAAvE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9Gf1C,EAAE,CAAAgF,WAAA,cAAArC,GAAA,CAAA8B,IAAA;UAAFzE,EAAE,CAAAqE,WAAA,yBAAA1B,GAAA,CAAA6B,KA8GY,CAAC,uBAAA7B,GAAA,CAAAiE,aAAD,CAAC;UA9Gf5G,EAAE,CAAAkH,WAAA,SAAAvE,GAAA,CAAAgE,GA8GY,CAAC;QAAA;MAAA;MAAA1B,MAAA;QAAAtB,KAAA;QAAAC,GAAA;QAAAY,KAAA;QAAAC,IAAA;QAAAkC,GAAA,GA9Gf3G,EAAE,CAAAsG,YAAA,CAAAa,0BAAA,gBA8G4J9F,qBAAqB;MAAA;MAAAc,UAAA;MAAAC,QAAA,GA9GnLpC,EAAE,CAAAoH,wBAAA,EAAFpH,EAAE,CAAAqC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAA8E,MAAA;MAAA7E,QAAA,WAAA8E,2BAAA5E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1C,EAAE,CAAAuH,SAAA,iBA8G6c,CAAC;UA9GhdvH,EAAE,CAAAwH,cAAA;UAAFxH,EAAE,CAAAyH,cAAA,YA8GgjB,CAAC;UA9GnjBzH,EAAE,CAAAuH,SAAA,eA8GwoB,CAAC,eAAoJ,CAAC;UA9GhyBvH,EAAE,CAAA0H,YAAA,CA8GqyB,CAAC;QAAA;QAAA,IAAAhF,EAAA;UA9GxyB1C,EAAE,CAAA2H,UAAA,QAAAhF,GAAA,CAAAiB,GA8Gwa,CAAC,UAAAjB,GAAA,CAAAgB,KAAsB,CAAC;UA9Glc3D,EAAE,CAAA4H,SAAA,EA8GqxB,CAAC;UA9GxxB5H,EAAE,CAAAkH,WAAA,sBAAAvE,GAAA,CAAA+D,cAAA,EA8GqxB,CAAC;QAAA;MAAA;MAAA9D,MAAA;MAAAE,eAAA;IAAA,EAAu5E;EAAE;AACtxG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhHqG/C,EAAE,CAAAgD,iBAAA,CAgHXyD,iBAAiB,EAAc,CAAC;IAChHzE,IAAI,EAAE/B,SAAS;IACfgD,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEmC,QAAQ,EAAE,qBAAqB;MAAExB,eAAe,EAAE3C,uBAAuB,CAACgD,MAAM;MAAEC,IAAI,EAAE;QACvG,kBAAkB,EAAE,MAAM;QAC1B,8BAA8B,EAAE,OAAO;QACvC,4BAA4B,EAAE,eAAe;QAC7C,cAAc,EAAE;MACpB,CAAC;MAAEZ,QAAQ,EAAE,8bAA8b;MAAEI,MAAM,EAAE,CAAC,i0EAAi0E;IAAE,CAAC;EACtyF,CAAC,CAAC,QAAkB;IAAEe,KAAK,EAAE,CAAC;MACtB3B,IAAI,EAAE1B;IACV,CAAC,CAAC;IAAEsD,GAAG,EAAE,CAAC;MACN5B,IAAI,EAAE1B;IACV,CAAC,CAAC;IAAEkE,KAAK,EAAE,CAAC;MACRxC,IAAI,EAAE1B;IACV,CAAC,CAAC;IAAEmE,IAAI,EAAE,CAAC;MACPzC,IAAI,EAAE1B;IACV,CAAC,CAAC;IAAEqG,GAAG,EAAE,CAAC;MACN3E,IAAI,EAAE1B,KAAK;MACX2C,IAAI,EAAE,CAAC;QAAE4E,SAAS,EAAExG;MAAsB,CAAC;IAC/C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyG,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACnG,IAAI,YAAAoG,yBAAAlG,CAAA;MAAA,YAAAA,CAAA,IAAyFiG,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAAChG,IAAI,kBAvI+E9B,EAAE,CAAA+B,iBAAA;MAAAC,IAAA,EAuIJ8F,gBAAgB;MAAA7F,SAAA;MAAAE,UAAA;MAAAC,QAAA,GAvIdpC,EAAE,CAAAqC,mBAAA;MAAA6C,KAAA,EAAA3D,GAAA;MAAAyG,kBAAA,EAAAvG,GAAA;MAAAa,KAAA;MAAAC,IAAA;MAAA8E,MAAA;MAAA7E,QAAA,WAAAyF,0BAAAvF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1C,EAAE,CAAAkI,eAAA,CAAA1G,GAAA;UAAFxB,EAAE,CAAAmI,YAAA,EAuIiI,CAAC;UAvIpInI,EAAE,CAAAmI,YAAA,KAuIgL,CAAC;UAvInLnI,EAAE,CAAAyH,cAAA,aAuI0M,CAAC;UAvI7MzH,EAAE,CAAAmI,YAAA,KAuI8N,CAAC;UAvIjOnI,EAAE,CAAA0H,YAAA,CAuIuO,CAAC;QAAA;MAAA;MAAA9E,MAAA;MAAAE,eAAA;IAAA,EAAuU;EAAE;AACxpB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAzIqG/C,EAAE,CAAAgD,iBAAA,CAyIX8E,gBAAgB,EAAc,CAAC;IAC/G9F,IAAI,EAAE/B,SAAS;IACfgD,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEmC,QAAQ,EAAE,yBAAyB;MAAExB,eAAe,EAAE3C,uBAAuB,CAACgD,MAAM;MAAEX,QAAQ,EAAE,4IAA4I;MAAEI,MAAM,EAAE,CAAC,iQAAiQ;IAAE,CAAC;EAC1hB,CAAC,CAAC;AAAA;AAEV,MAAMwF,0BAA0B,CAAC;EAC7B;IAAS,IAAI,CAACzG,IAAI,YAAA0G,mCAAAxG,CAAA;MAAA,YAAAA,CAAA,IAAyFuG,0BAA0B;IAAA,CAAmD;EAAE;EAC1L;IAAS,IAAI,CAACtG,IAAI,kBAhJ+E9B,EAAE,CAAA+B,iBAAA;MAAAC,IAAA,EAgJJoG,0BAA0B;MAAAnG,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAhJxBpC,EAAE,CAAAqC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAA8F,oCAAA5F,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAgJ8iD;EAAE;AACvpD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlJqG/C,EAAE,CAAAgD,iBAAA,CAkJXoF,0BAA0B,EAAc,CAAC;IACzHpG,IAAI,EAAE/B,SAAS;IACfgD,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAE3C,iBAAiB,CAACgD,IAAI;MAAEJ,eAAe,EAAE3C,uBAAuB,CAACgD,MAAM;MAAEC,IAAI,EAAE;QAAEC,KAAK,EAAE;MAAyB,CAAC;MAAET,MAAM,EAAE,CAAC,6xCAA6xC;IAAE,CAAC;EACz9C,CAAC,CAAC;AAAA;AACV,MAAM2F,oBAAoB,CAAC;EACvBhF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACE,OAAO,GAAGjD,aAAa,CAAC4H,0BAA0B,CAAC;IACxD,IAAI,CAACI,QAAQ,GAAG,CAAC;EACrB;EACA;IAAS,IAAI,CAAC7G,IAAI,YAAA8G,6BAAA5G,CAAA;MAAA,YAAAA,CAAA,IAAyF0G,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACvE,IAAI,kBA5J+EhE,EAAE,CAAAiE,iBAAA;MAAAjC,IAAA,EA4JJuG,oBAAoB;MAAAtG,SAAA;MAAAC,SAAA;MAAAgC,QAAA;MAAAC,YAAA,WAAAuE,kCAAAhG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5JlB1C,EAAE,CAAAqE,WAAA,sBA4JJ,CAAC,GAAA1B,GAAA,CAAA6F,QAAkB,CAAC;QAAA;MAAA;MAAAvD,MAAA;QAAAuD,QAAA;MAAA;MAAArG,UAAA;IAAA,EAAkN;EAAE;AAC3U;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KA9JqG/C,EAAE,CAAAgD,iBAAA,CA8JXuF,oBAAoB,EAAc,CAAC;IACnHvG,IAAI,EAAE5B,SAAS;IACf6C,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBmC,QAAQ,EAAE,4BAA4B;MACtClB,IAAI,EAAE;QACFC,KAAK,EAAE,YAAY;QACnB,2BAA2B,EAAE;MACjC;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEmF,QAAQ,EAAE,CAAC;MACzBxG,IAAI,EAAE1B;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMqI,WAAW,GAAG,CAChB9D,cAAc,EACd4B,iBAAiB,EACjBrB,wBAAwB,EACxB9B,iCAAiC,EACjCwE,gBAAgB,EAChBS,oBAAoB,CACvB;;AAED;AACA;AACA;;AAEA,SAAShE,4BAA4B,EAAEG,oBAAoB,EAAEiE,WAAW,EAAE9D,cAAc,EAAE4B,iBAAiB,EAAErB,wBAAwB,EAAE9B,iCAAiC,EAAEwE,gBAAgB,EAAES,oBAAoB,EAAE5D,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}