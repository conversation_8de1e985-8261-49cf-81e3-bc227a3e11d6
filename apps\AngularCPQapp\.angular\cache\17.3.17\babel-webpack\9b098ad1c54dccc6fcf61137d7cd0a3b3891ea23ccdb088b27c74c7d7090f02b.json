{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearanceOptionsProvider, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport { tuiInjectIconResolver } from '@taiga-ui/core/tokens';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nconst _c0 = [\"tuiLike\", \"\", \"type\", \"checkbox\"];\nconst [TUI_LIKE_OPTIONS, tuiLikeOptionsProvider] = tuiCreateOptions({\n  size: 'm',\n  appearance: 'secondary',\n  icons: {\n    unchecked: '@tui.heart',\n    checked: '@tui.heart-filled'\n  }\n});\nclass TuiLike {\n  constructor() {\n    this.options = inject(TUI_LIKE_OPTIONS);\n    this.resolver = tuiInjectIconResolver();\n    this.color = '';\n    this.uncheckedIcon = this.options.icons.unchecked;\n    this.checkedIcon = this.options.icons.checked;\n    this.size = this.options.size;\n  }\n  getIcon(state) {\n    const option = state === 'checked' ? this.checkedIcon : this.uncheckedIcon;\n    const icon = tuiIsString(option) ? option : option(this.size);\n    return icon && `url(${this.resolver(icon)})`;\n  }\n  static {\n    this.ɵfac = function TuiLike_Factory(t) {\n      return new (t || TuiLike)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiLike,\n      selectors: [[\"input\", \"tuiLike\", \"\", \"type\", \"checkbox\"]],\n      hostAttrs: [\"tuiLike\", \"\"],\n      hostVars: 8,\n      hostBindings: function TuiLike_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size)(\"data-mode\", \"\");\n          i0.ɵɵstyleProp(\"--t-icon-color\", ctx.color)(\"--t-unchecked-icon\", ctx.getIcon(\"unchecked\"))(\"--t-checked-icon\", ctx.getIcon(\"checked\"));\n        }\n      },\n      inputs: {\n        color: [i0.ɵɵInputFlags.None, \"tuiLike\", \"color\"],\n        uncheckedIcon: \"uncheckedIcon\",\n        checkedIcon: \"checkedIcon\",\n        size: \"size\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAppearanceOptionsProvider(TUI_LIKE_OPTIONS)]), i0.ɵɵHostDirectivesFeature([i1.TuiWithAppearance]), i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 0,\n      vars: 0,\n      template: function TuiLike_Template(rf, ctx) {},\n      styles: [\"[tuiLike]{--t-size: var(--tui-height-m);--t-border-width: .75rem;inline-size:var(--t-size);block-size:var(--t-size);border:var(--t-border-width) transparent solid;border-radius:100%;cursor:pointer;margin:0;flex-shrink:0}[tuiLike]:before,[tuiLike]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;transition-property:transform,opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";background:currentColor;-webkit-mask:var(--t-unchecked-icon) no-repeat center / contain;mask:var(--t-unchecked-icon) no-repeat center / contain}[tuiLike]:after{-webkit-mask-image:var(--t-checked-icon);mask-image:var(--t-checked-icon);opacity:0;color:var(--t-icon-color, inherit);transform:scale(0)}[tuiLike]:checked:before{opacity:0}[tuiLike]:checked:after{opacity:1;transform:scale(1);transition-timing-function:cubic-bezier(.2,.6,.5,1.8),ease-in-out}[tuiLike][data-size=s]{--t-size: var(--tui-height-s);--t-border-width: .5rem}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiLike, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiLike][type=checkbox]',\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAppearanceOptionsProvider(TUI_LIKE_OPTIONS)],\n      hostDirectives: [TuiWithAppearance],\n      host: {\n        tuiLike: '',\n        '[attr.data-size]': 'size',\n        '[attr.data-mode]': '\"\"',\n        '[style.--t-icon-color]': 'color',\n        '[style.--t-unchecked-icon]': 'getIcon(\"unchecked\")',\n        '[style.--t-checked-icon]': 'getIcon(\"checked\")'\n      },\n      styles: [\"[tuiLike]{--t-size: var(--tui-height-m);--t-border-width: .75rem;inline-size:var(--t-size);block-size:var(--t-size);border:var(--t-border-width) transparent solid;border-radius:100%;cursor:pointer;margin:0;flex-shrink:0}[tuiLike]:before,[tuiLike]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;transition-property:transform,opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";background:currentColor;-webkit-mask:var(--t-unchecked-icon) no-repeat center / contain;mask:var(--t-unchecked-icon) no-repeat center / contain}[tuiLike]:after{-webkit-mask-image:var(--t-checked-icon);mask-image:var(--t-checked-icon);opacity:0;color:var(--t-icon-color, inherit);transform:scale(0)}[tuiLike]:checked:before{opacity:0}[tuiLike]:checked:after{opacity:1;transform:scale(1);transition-timing-function:cubic-bezier(.2,.6,.5,1.8),ease-in-out}[tuiLike][data-size=s]{--t-size: var(--tui-height-s);--t-border-width: .5rem}\\n\"]\n    }]\n  }], null, {\n    color: [{\n      type: Input,\n      args: ['tuiLike']\n    }],\n    uncheckedIcon: [{\n      type: Input\n    }],\n    checkedIcon: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_LIKE_OPTIONS, TuiLike, tuiLikeOptionsProvider };", "map": {"version": 3, "names": ["i0", "inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "tuiIsString", "i1", "tuiAppearanceOptionsProvider", "TuiWithAppearance", "tuiInjectIconResolver", "tuiCreateOptions", "_c0", "TUI_LIKE_OPTIONS", "tuiLikeOptionsProvider", "size", "appearance", "icons", "unchecked", "checked", "TuiLike", "constructor", "options", "resolver", "color", "uncheckedIcon", "checkedIcon", "getIcon", "state", "option", "icon", "ɵfac", "TuiLike_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "TuiLike_HostBindings", "rf", "ctx", "ɵɵattribute", "ɵɵstyleProp", "inputs", "ɵɵInputFlags", "None", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "template", "TuiLike_Template", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "providers", "hostDirectives", "host", "tui<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-like.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearanceOptionsProvider, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport { tuiInjectIconResolver } from '@taiga-ui/core/tokens';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\n\nconst [TUI_LIKE_OPTIONS, tuiLikeOptionsProvider] = tuiCreateOptions({\n    size: 'm',\n    appearance: 'secondary',\n    icons: {\n        unchecked: '@tui.heart',\n        checked: '@tui.heart-filled',\n    },\n});\n\nclass TuiLike {\n    constructor() {\n        this.options = inject(TUI_LIKE_OPTIONS);\n        this.resolver = tuiInjectIconResolver();\n        this.color = '';\n        this.uncheckedIcon = this.options.icons.unchecked;\n        this.checkedIcon = this.options.icons.checked;\n        this.size = this.options.size;\n    }\n    getIcon(state) {\n        const option = state === 'checked' ? this.checkedIcon : this.uncheckedIcon;\n        const icon = tuiIsString(option) ? option : option(this.size);\n        return icon && `url(${this.resolver(icon)})`;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLike, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiLike, isStandalone: true, selector: \"input[tuiLike][type=checkbox]\", inputs: { color: [\"tuiLike\", \"color\"], uncheckedIcon: \"uncheckedIcon\", checkedIcon: \"checkedIcon\", size: \"size\" }, host: { attributes: { \"tuiLike\": \"\" }, properties: { \"attr.data-size\": \"size\", \"attr.data-mode\": \"\\\"\\\"\", \"style.--t-icon-color\": \"color\", \"style.--t-unchecked-icon\": \"getIcon(\\\"unchecked\\\")\", \"style.--t-checked-icon\": \"getIcon(\\\"checked\\\")\" } }, providers: [tuiAppearanceOptionsProvider(TUI_LIKE_OPTIONS)], hostDirectives: [{ directive: i1.TuiWithAppearance }], ngImport: i0, template: '', isInline: true, styles: [\"[tuiLike]{--t-size: var(--tui-height-m);--t-border-width: .75rem;inline-size:var(--t-size);block-size:var(--t-size);border:var(--t-border-width) transparent solid;border-radius:100%;cursor:pointer;margin:0;flex-shrink:0}[tuiLike]:before,[tuiLike]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;transition-property:transform,opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";background:currentColor;-webkit-mask:var(--t-unchecked-icon) no-repeat center / contain;mask:var(--t-unchecked-icon) no-repeat center / contain}[tuiLike]:after{-webkit-mask-image:var(--t-checked-icon);mask-image:var(--t-checked-icon);opacity:0;color:var(--t-icon-color, inherit);transform:scale(0)}[tuiLike]:checked:before{opacity:0}[tuiLike]:checked:after{opacity:1;transform:scale(1);transition-timing-function:cubic-bezier(.2,.6,.5,1.8),ease-in-out}[tuiLike][data-size=s]{--t-size: var(--tui-height-s);--t-border-width: .5rem}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLike, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'input[tuiLike][type=checkbox]', template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiAppearanceOptionsProvider(TUI_LIKE_OPTIONS)], hostDirectives: [TuiWithAppearance], host: {\n                        tuiLike: '',\n                        '[attr.data-size]': 'size',\n                        '[attr.data-mode]': '\"\"',\n                        '[style.--t-icon-color]': 'color',\n                        '[style.--t-unchecked-icon]': 'getIcon(\"unchecked\")',\n                        '[style.--t-checked-icon]': 'getIcon(\"checked\")',\n                    }, styles: [\"[tuiLike]{--t-size: var(--tui-height-m);--t-border-width: .75rem;inline-size:var(--t-size);block-size:var(--t-size);border:var(--t-border-width) transparent solid;border-radius:100%;cursor:pointer;margin:0;flex-shrink:0}[tuiLike]:before,[tuiLike]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;transition-property:transform,opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";background:currentColor;-webkit-mask:var(--t-unchecked-icon) no-repeat center / contain;mask:var(--t-unchecked-icon) no-repeat center / contain}[tuiLike]:after{-webkit-mask-image:var(--t-checked-icon);mask-image:var(--t-checked-icon);opacity:0;color:var(--t-icon-color, inherit);transform:scale(0)}[tuiLike]:checked:before{opacity:0}[tuiLike]:checked:after{opacity:1;transform:scale(1);transition-timing-function:cubic-bezier(.2,.6,.5,1.8),ease-in-out}[tuiLike][data-size=s]{--t-size: var(--tui-height-s);--t-border-width: .5rem}\\n\"] }]\n        }], propDecorators: { color: [{\n                type: Input,\n                args: ['tuiLike']\n            }], uncheckedIcon: [{\n                type: Input\n            }], checkedIcon: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_LIKE_OPTIONS, TuiLike, tuiLikeOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,QAAQ,eAAe;AACpG,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,OAAO,KAAKC,EAAE,MAAM,sCAAsC;AAC1D,SAASC,4BAA4B,EAAEC,iBAAiB,QAAQ,sCAAsC;AACtG,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,gBAAgB,QAAQ,wBAAwB;AAAC,MAAAC,GAAA;AAE1D,MAAM,CAACC,gBAAgB,EAAEC,sBAAsB,CAAC,GAAGH,gBAAgB,CAAC;EAChEI,IAAI,EAAE,GAAG;EACTC,UAAU,EAAE,WAAW;EACvBC,KAAK,EAAE;IACHC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE;EACb;AACJ,CAAC,CAAC;AAEF,MAAMC,OAAO,CAAC;EACVC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGrB,MAAM,CAACY,gBAAgB,CAAC;IACvC,IAAI,CAACU,QAAQ,GAAGb,qBAAqB,CAAC,CAAC;IACvC,IAAI,CAACc,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,aAAa,GAAG,IAAI,CAACH,OAAO,CAACL,KAAK,CAACC,SAAS;IACjD,IAAI,CAACQ,WAAW,GAAG,IAAI,CAACJ,OAAO,CAACL,KAAK,CAACE,OAAO;IAC7C,IAAI,CAACJ,IAAI,GAAG,IAAI,CAACO,OAAO,CAACP,IAAI;EACjC;EACAY,OAAOA,CAACC,KAAK,EAAE;IACX,MAAMC,MAAM,GAAGD,KAAK,KAAK,SAAS,GAAG,IAAI,CAACF,WAAW,GAAG,IAAI,CAACD,aAAa;IAC1E,MAAMK,IAAI,GAAGxB,WAAW,CAACuB,MAAM,CAAC,GAAGA,MAAM,GAAGA,MAAM,CAAC,IAAI,CAACd,IAAI,CAAC;IAC7D,OAAOe,IAAI,IAAI,OAAO,IAAI,CAACP,QAAQ,CAACO,IAAI,CAAC,GAAG;EAChD;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,gBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFb,OAAO;IAAA,CAAmD;EAAE;EACvK;IAAS,IAAI,CAACc,IAAI,kBAD+ElC,EAAE,CAAAmC,iBAAA;MAAAC,IAAA,EACJhB,OAAO;MAAAiB,SAAA;MAAAC,SAAA,cAAqN,EAAE;MAAAC,QAAA;MAAAC,YAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD5N1C,EAAE,CAAA4C,WAAA,cAAAD,GAAA,CAAA5B,IAAA,eACJ,EAAE;UADAf,EAAE,CAAA6C,WAAA,mBAAAF,GAAA,CAAAnB,KACE,CAAC,uBAAPmB,GAAA,CAAAhB,OAAA,CAAQ,WAAW,CAAb,CAAC,qBAAPgB,GAAA,CAAAhB,OAAA,CAAQ,SAAS,CAAX,CAAC;QAAA;MAAA;MAAAmB,MAAA;QAAAtB,KAAA,GADLxB,EAAE,CAAA+C,YAAA,CAAAC,IAAA;QAAAvB,aAAA;QAAAC,WAAA;QAAAX,IAAA;MAAA;MAAAkC,UAAA;MAAAC,QAAA,GAAFlD,EAAE,CAAAmD,kBAAA,CACwb,CAAC3C,4BAA4B,CAACK,gBAAgB,CAAC,CAAC,GAD1eb,EAAE,CAAAoD,uBAAA,EACwgB7C,EAAE,CAACE,iBAAiB,IAD9hBT,EAAE,CAAAqD,mBAAA;MAAAC,KAAA,EAAA1C,GAAA;MAAA2C,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,iBAAAhB,EAAA,EAAAC,GAAA;MAAAgB,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACipD;EAAE;AAC1vD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG9D,EAAE,CAAA+D,iBAAA,CAGX3C,OAAO,EAAc,CAAC;IACtGgB,IAAI,EAAElC,SAAS;IACf8D,IAAI,EAAE,CAAC;MAAEf,UAAU,EAAE,IAAI;MAAEgB,QAAQ,EAAE,+BAA+B;MAAER,QAAQ,EAAE,EAAE;MAAEG,aAAa,EAAEzD,iBAAiB,CAAC6C,IAAI;MAAEa,eAAe,EAAEzD,uBAAuB,CAAC8D,MAAM;MAAEC,SAAS,EAAE,CAAC3D,4BAA4B,CAACK,gBAAgB,CAAC,CAAC;MAAEuD,cAAc,EAAE,CAAC3D,iBAAiB,CAAC;MAAE4D,IAAI,EAAE;QACxQC,OAAO,EAAE,EAAE;QACX,kBAAkB,EAAE,MAAM;QAC1B,kBAAkB,EAAE,IAAI;QACxB,wBAAwB,EAAE,OAAO;QACjC,4BAA4B,EAAE,sBAAsB;QACpD,0BAA0B,EAAE;MAChC,CAAC;MAAEX,MAAM,EAAE,CAAC,y9BAAy9B;IAAE,CAAC;EACp/B,CAAC,CAAC,QAAkB;IAAEnC,KAAK,EAAE,CAAC;MACtBY,IAAI,EAAE/B,KAAK;MACX2D,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEvC,aAAa,EAAE,CAAC;MAChBW,IAAI,EAAE/B;IACV,CAAC,CAAC;IAAEqB,WAAW,EAAE,CAAC;MACdU,IAAI,EAAE/B;IACV,CAAC,CAAC;IAAEU,IAAI,EAAE,CAAC;MACPqB,IAAI,EAAE/B;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASQ,gBAAgB,EAAEO,OAAO,EAAEN,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}