{"ast": null, "code": "import { isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, DestroyRef, PLATFORM_ID, Directive } from '@angular/core';\nimport { WA_WINDOW } from '@ng-web-apis/common';\nimport { EMPTY_FUNCTION } from '@taiga-ui/cdk/constants';\nimport { tuiCreateToken, tuiFontSizeWatcher } from '@taiga-ui/cdk/utils/miscellaneous';\nconst TUI_FONT_SIZE_HANDLER = tuiCreateToken();\nclass TuiFontSize {\n  constructor() {\n    this.handler = inject(TUI_FONT_SIZE_HANDLER, {\n      optional: true\n    });\n    this.nothing = inject(DestroyRef).onDestroy(this.handler && isPlatformBrowser(inject(PLATFORM_ID)) && typeof ResizeObserver !== 'undefined' ? tuiFontSizeWatcher(this.handler, inject(WA_WINDOW)) : EMPTY_FUNCTION);\n  }\n  static {\n    this.ɵfac = function TuiFontSize_Factory(t) {\n      return new (t || TuiFontSize)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiFontSize,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFontSize, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_FONT_SIZE_HANDLER, TuiFontSize };", "map": {"version": 3, "names": ["isPlatformBrowser", "i0", "inject", "DestroyRef", "PLATFORM_ID", "Directive", "WA_WINDOW", "EMPTY_FUNCTION", "tuiCreateToken", "tuiFontSizeWatcher", "TUI_FONT_SIZE_HANDLER", "TuiFontSize", "constructor", "handler", "optional", "nothing", "onDestroy", "ResizeObserver", "ɵfac", "TuiFontSize_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "standalone", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-font-size.mjs"], "sourcesContent": ["import { isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, DestroyRef, PLATFORM_ID, Directive } from '@angular/core';\nimport { WA_WINDOW } from '@ng-web-apis/common';\nimport { EMPTY_FUNCTION } from '@taiga-ui/cdk/constants';\nimport { tuiCreateToken, tuiFontSizeWatcher } from '@taiga-ui/cdk/utils/miscellaneous';\n\nconst TUI_FONT_SIZE_HANDLER = tuiCreateToken();\nclass TuiFontSize {\n    constructor() {\n        this.handler = inject(TUI_FONT_SIZE_HANDLER, { optional: true });\n        this.nothing = inject(DestroyRef).onDestroy(this.handler &&\n            isPlatformBrowser(inject(PLATFORM_ID)) &&\n            typeof ResizeObserver !== 'undefined'\n            ? tuiFontSizeWatcher(this.handler, inject(WA_WINDOW))\n            : EMPTY_FUNCTION);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFontSize, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiFontSize, isStandalone: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFontSize, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_FONT_SIZE_HANDLER, TuiFontSize };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,iBAAiB;AACnD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,QAAQ,eAAe;AAC1E,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,EAAEC,kBAAkB,QAAQ,mCAAmC;AAEtF,MAAMC,qBAAqB,GAAGF,cAAc,CAAC,CAAC;AAC9C,MAAMG,WAAW,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGX,MAAM,CAACQ,qBAAqB,EAAE;MAAEI,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChE,IAAI,CAACC,OAAO,GAAGb,MAAM,CAACC,UAAU,CAAC,CAACa,SAAS,CAAC,IAAI,CAACH,OAAO,IACpDb,iBAAiB,CAACE,MAAM,CAACE,WAAW,CAAC,CAAC,IACtC,OAAOa,cAAc,KAAK,WAAW,GACnCR,kBAAkB,CAAC,IAAI,CAACI,OAAO,EAAEX,MAAM,CAACI,SAAS,CAAC,CAAC,GACnDC,cAAc,CAAC;EACzB;EACA;IAAS,IAAI,CAACW,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFT,WAAW;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACU,IAAI,kBAD+EpB,EAAE,CAAAqB,iBAAA;MAAAC,IAAA,EACJZ,WAAW;MAAAa,UAAA;IAAA,EAAqC;EAAE;AACrJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGxB,EAAE,CAAAyB,iBAAA,CAGXf,WAAW,EAAc,CAAC;IAC1GY,IAAI,EAAElB,SAAS;IACfsB,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASd,qBAAqB,EAAEC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}