{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Input } from '@angular/core';\nimport { maskitoTransform, MASKITO_DEFAULT_OPTIONS } from '@maskito/core';\nimport { TuiValueTransformer } from '@taiga-ui/cdk/classes';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { identity } from 'rxjs';\nclass TuiUnmaskHandler extends TuiValueTransformer {\n  constructor() {\n    super(...arguments);\n    this.tuiUnmaskHandler = identity;\n    this.maskito = null;\n  }\n  fromControlValue(controlValue) {\n    return maskitoTransform(String(controlValue ?? ''), this.maskito || MASKITO_DEFAULT_OPTIONS);\n  }\n  toControlValue(value) {\n    return this.tuiUnmaskHandler(value);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiUnmaskHandler_BaseFactory;\n      return function TuiUnmaskHandler_Factory(t) {\n        return (ɵTuiUnmaskHandler_BaseFactory || (ɵTuiUnmaskHandler_BaseFactory = i0.ɵɵgetInheritedFactory(TuiUnmaskHandler)))(t || TuiUnmaskHandler);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiUnmaskHandler,\n      selectors: [[\"\", \"maskito\", \"\", \"tuiUnmaskHandler\", \"\"]],\n      inputs: {\n        tuiUnmaskHandler: \"tuiUnmaskHandler\",\n        maskito: \"maskito\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TuiValueTransformer, TuiUnmaskHandler)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiUnmaskHandler, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[maskito][tuiUnmaskHandler]',\n      providers: [tuiProvide(TuiValueTransformer, TuiUnmaskHandler)]\n    }]\n  }], null, {\n    tuiUnmaskHandler: [{\n      type: Input\n    }],\n    maskito: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiUnmaskHandler };", "map": {"version": 3, "names": ["i0", "Directive", "Input", "maskitoTransform", "MASKITO_DEFAULT_OPTIONS", "TuiValueTransformer", "tui<PERSON><PERSON><PERSON>", "identity", "TuiUnmaskHandler", "constructor", "arguments", "tuiUnmaskHandler", "maskito", "fromControlValue", "controlValue", "String", "toControlValue", "value", "ɵfac", "ɵTuiUnmaskHandler_BaseFactory", "TuiUnmaskHandler_Factory", "t", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-unmask-handler.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Input } from '@angular/core';\nimport { maskitoTransform, MASKITO_DEFAULT_OPTIONS } from '@maskito/core';\nimport { TuiValueTransformer } from '@taiga-ui/cdk/classes';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { identity } from 'rxjs';\n\nclass TuiUnmaskHandler extends TuiValueTransformer {\n    constructor() {\n        super(...arguments);\n        this.tuiUnmaskHandler = identity;\n        this.maskito = null;\n    }\n    fromControlValue(controlValue) {\n        return maskitoTransform(String(controlValue ?? ''), this.maskito || MASKITO_DEFAULT_OPTIONS);\n    }\n    toControlValue(value) {\n        return this.tuiUnmaskHandler(value);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiUnmaskHandler, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiUnmaskHandler, isStandalone: true, selector: \"[maskito][tuiUnmaskHandler]\", inputs: { tuiUnmaskHandler: \"tuiUnmaskHandler\", maskito: \"maskito\" }, providers: [tuiProvide(TuiValueTransformer, TuiUnmaskHandler)], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiUnmaskHandler, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[maskito][tuiUnmaskHandler]',\n                    providers: [tuiProvide(TuiValueTransformer, TuiUnmaskHandler)],\n                }]\n        }], propDecorators: { tuiUnmaskHandler: [{\n                type: Input\n            }], maskito: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiUnmaskHandler };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,SAASC,gBAAgB,EAAEC,uBAAuB,QAAQ,eAAe;AACzE,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,QAAQ,QAAQ,MAAM;AAE/B,MAAMC,gBAAgB,SAASH,mBAAmB,CAAC;EAC/CI,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,gBAAgB,GAAGJ,QAAQ;IAChC,IAAI,CAACK,OAAO,GAAG,IAAI;EACvB;EACAC,gBAAgBA,CAACC,YAAY,EAAE;IAC3B,OAAOX,gBAAgB,CAACY,MAAM,CAACD,YAAY,IAAI,EAAE,CAAC,EAAE,IAAI,CAACF,OAAO,IAAIR,uBAAuB,CAAC;EAChG;EACAY,cAAcA,CAACC,KAAK,EAAE;IAClB,OAAO,IAAI,CAACN,gBAAgB,CAACM,KAAK,CAAC;EACvC;EACA;IAAS,IAAI,CAACC,IAAI;MAAA,IAAAC,6BAAA;MAAA,gBAAAC,yBAAAC,CAAA;QAAA,QAAAF,6BAAA,KAAAA,6BAAA,GAA+EnB,EAAE,CAAAsB,qBAAA,CAAQd,gBAAgB,IAAAa,CAAA,IAAhBb,gBAAgB;MAAA;IAAA,IAAqD;EAAE;EAClL;IAAS,IAAI,CAACe,IAAI,kBAD+EvB,EAAE,CAAAwB,iBAAA;MAAAC,IAAA,EACJjB,gBAAgB;MAAAkB,SAAA;MAAAC,MAAA;QAAAhB,gBAAA;QAAAC,OAAA;MAAA;MAAAgB,UAAA;MAAAC,QAAA,GADd7B,EAAE,CAAA8B,kBAAA,CAC4J,CAACxB,UAAU,CAACD,mBAAmB,EAAEG,gBAAgB,CAAC,CAAC,GADjNR,EAAE,CAAA+B,0BAAA;IAAA,EACuP;EAAE;AAChW;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGhC,EAAE,CAAAiC,iBAAA,CAGXzB,gBAAgB,EAAc,CAAC;IAC/GiB,IAAI,EAAExB,SAAS;IACfiC,IAAI,EAAE,CAAC;MACCN,UAAU,EAAE,IAAI;MAChBO,QAAQ,EAAE,6BAA6B;MACvCC,SAAS,EAAE,CAAC9B,UAAU,CAACD,mBAAmB,EAAEG,gBAAgB,CAAC;IACjE,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEG,gBAAgB,EAAE,CAAC;MACjCc,IAAI,EAAEvB;IACV,CAAC,CAAC;IAAEU,OAAO,EAAE,CAAC;MACVa,IAAI,EAAEvB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASM,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}