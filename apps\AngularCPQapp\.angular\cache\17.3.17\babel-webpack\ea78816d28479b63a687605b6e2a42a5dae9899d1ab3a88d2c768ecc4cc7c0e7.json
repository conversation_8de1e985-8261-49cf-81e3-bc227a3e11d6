{"ast": null, "code": "import { Async<PERSON><PERSON><PERSON>, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Injectable, Directive, Input, Component, ChangeDetectionStrategy, ElementRef, DestroyRef } from '@angular/core';\nimport { WA_ANIMATION_FRAME } from '@ng-web-apis/common';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { tuiTypedFromEvent, tuiZonefree, tuiZonefreeScheduler, tuiScrollFrom, tuiZoneOptimized } from '@taiga-ui/cdk/observables';\nimport { TUI_SCROLL_REF } from '@taiga-ui/core/tokens';\nimport { Observable, merge, filter, map, switchMap, takeUntil, throttleTime, startWith, distinctUntilChanged, timer } from 'rxjs';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { tuiInjectElement, tuiGetElementOffset } from '@taiga-ui/cdk/utils/dom';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { TUI_IS_IOS } from '@taiga-ui/cdk/tokens';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nfunction TuiScrollControls_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiScrollControls_ng_template_1_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵlistener(\"mousedown.capture.prevent\", function TuiScrollControls_ng_template_1_ng_container_0_div_1_Template_div_mousedown_capture_prevent_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      return i0.ɵɵresetView(0);\n    });\n    i0.ɵɵelement(1, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const bars_r2 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵclassProp(\"t-bar_has-horizontal\", bars_r2[1]);\n  }\n}\nfunction TuiScrollControls_ng_template_1_ng_container_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵlistener(\"mousedown.capture.prevent\", function TuiScrollControls_ng_template_1_ng_container_0_div_2_Template_div_mousedown_capture_prevent_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      return i0.ɵɵresetView(0);\n    });\n    i0.ɵɵelement(1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const bars_r2 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵclassProp(\"t-bar_has-vertical\", bars_r2[0]);\n  }\n}\nfunction TuiScrollControls_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiScrollControls_ng_template_1_ng_container_0_div_1_Template, 2, 2, \"div\", 3)(2, TuiScrollControls_ng_template_1_ng_container_0_div_2_Template, 2, 2, \"div\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const bars_r2 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", bars_r2[0]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", bars_r2[1]);\n  }\n}\nfunction TuiScrollControls_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiScrollControls_ng_template_1_ng_container_0_Template, 3, 2, \"ng-container\", 2);\n    i0.ɵɵpipe(1, \"async\");\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx_r3.refresh$));\n  }\n}\nconst _c0 = [\"*\"];\nfunction TuiScrollbar_tui_scroll_controls_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-scroll-controls\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"t-hover-mode\", ctx_r0.options.mode === \"hover\");\n  }\n}\nclass TuiScrollbarService extends Observable {\n  constructor() {\n    super(subscriber => this.scroll$.subscribe(subscriber));\n    this.el = tuiInjectElement();\n    this.element = inject(TUI_SCROLL_REF).nativeElement;\n    this.scroll$ = merge(tuiTypedFromEvent(this.el.parentElement, 'mousedown').pipe(filter(({\n      target\n    }) => target !== this.el), map(event => this.getScrolled(event, 0.5, 0.5))), tuiTypedFromEvent(this.el, 'mousedown').pipe(tuiZonefree(), switchMap(event => {\n      const {\n        ownerDocument\n      } = this.el;\n      const rect = this.el.getBoundingClientRect();\n      const vertical = getOffsetVertical(event, rect);\n      const horizontal = getOffsetHorizontal(event, rect);\n      return tuiTypedFromEvent(ownerDocument, 'mousemove').pipe(map(event => this.getScrolled(event, vertical, horizontal)), takeUntil(tuiTypedFromEvent(ownerDocument, 'mouseup')));\n    })));\n  }\n  getScrolled({\n    clientY,\n    clientX\n  }, offsetY, offsetX) {\n    const {\n      offsetHeight,\n      offsetWidth\n    } = this.el;\n    const {\n      top,\n      left,\n      width,\n      height\n    } = this.el.parentElement.getBoundingClientRect();\n    const maxTop = this.element.scrollHeight - height;\n    const maxLeft = this.element.scrollWidth - width;\n    const scrolledTop = (clientY - top - offsetHeight * offsetY) / (height - offsetHeight);\n    const scrolledLeft = (clientX - left - offsetWidth * offsetX) / (width - offsetWidth);\n    return [maxTop * scrolledTop, maxLeft * scrolledLeft];\n  }\n  static {\n    this.ɵfac = function TuiScrollbarService_Factory(t) {\n      return new (t || TuiScrollbarService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiScrollbarService,\n      factory: TuiScrollbarService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiScrollbarService, [{\n    type: Injectable\n  }], function () {\n    return [];\n  }, null);\n})();\nfunction getOffsetVertical({\n  clientY\n}, {\n  top,\n  height\n}) {\n  return (clientY - top) / height;\n}\nfunction getOffsetHorizontal({\n  clientX\n}, {\n  left,\n  width\n}) {\n  return (clientX - left) / width;\n}\nconst MIN_WIDTH = 24;\nclass TuiScrollbarDirective {\n  constructor() {\n    this.el = inject(TUI_SCROLL_REF).nativeElement;\n    this.style = tuiInjectElement().style;\n    this.scrollSub = inject(TuiScrollbarService).pipe(takeUntilDestroyed()).subscribe(([top, left]) => {\n      this.el.style.scrollBehavior = 'auto';\n      if (this.tuiScrollbar === 'horizontal') {\n        this.el.scrollLeft = left;\n      } else {\n        this.el.scrollTop = top;\n      }\n      this.el.style.scrollBehavior = '';\n    });\n    this.styleSub = merge(inject(WA_ANIMATION_FRAME).pipe(throttleTime(100, tuiZonefreeScheduler())), tuiScrollFrom(this.el)).pipe(tuiZonefree(), takeUntilDestroyed()).subscribe(() => {\n      const dimension = {\n        scrollTop: this.el.scrollTop,\n        scrollHeight: this.el.scrollHeight,\n        clientHeight: this.el.clientHeight,\n        scrollLeft: this.el.scrollLeft,\n        scrollWidth: this.el.scrollWidth,\n        clientWidth: this.el.clientWidth\n      };\n      const thumb = `${this.getThumb(dimension) * 100}%`;\n      const view = `${this.getView(dimension) * 100}%`;\n      if (this.tuiScrollbar === 'vertical') {\n        this.style.top = thumb;\n        this.style.height = view;\n      } else {\n        this.style.left = thumb;\n        this.style.width = view;\n      }\n    });\n    this.tuiScrollbar = 'vertical';\n  }\n  getScrolled(dimension) {\n    return this.tuiScrollbar === 'vertical' ? dimension.scrollTop / (dimension.scrollHeight - dimension.clientHeight) : dimension.scrollLeft / (dimension.scrollWidth - dimension.clientWidth);\n  }\n  getCompensation(dimension) {\n    if (dimension.clientHeight * dimension.clientHeight / dimension.scrollHeight > MIN_WIDTH && this.tuiScrollbar === 'vertical' || dimension.clientWidth * dimension.clientWidth / dimension.scrollWidth > MIN_WIDTH && this.tuiScrollbar === 'horizontal') {\n      return 0;\n    }\n    return this.tuiScrollbar === 'vertical' ? MIN_WIDTH / dimension.clientHeight : MIN_WIDTH / dimension.clientWidth;\n  }\n  getThumb(dimension) {\n    const compensation = this.getCompensation(dimension) || this.getView(dimension);\n    return this.getScrolled(dimension) * (1 - compensation);\n  }\n  getView(dimension) {\n    return this.tuiScrollbar === 'vertical' ? Math.ceil(dimension.clientHeight / dimension.scrollHeight * 100) / 100 : Math.ceil(dimension.clientWidth / dimension.scrollWidth * 100) / 100;\n  }\n  static {\n    this.ɵfac = function TuiScrollbarDirective_Factory(t) {\n      return new (t || TuiScrollbarDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiScrollbarDirective,\n      selectors: [[\"\", \"tuiScrollbar\", \"\"]],\n      inputs: {\n        tuiScrollbar: \"tuiScrollbar\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiScrollbarService])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiScrollbarDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiScrollbar]',\n      providers: [TuiScrollbarService]\n    }]\n  }], null, {\n    tuiScrollbar: [{\n      type: Input\n    }]\n  });\n})();\nconst TUI_DEFAULT_SCROLLBAR_OPTIONS = {\n  mode: 'always'\n};\nconst [TUI_SCROLLBAR_OPTIONS, tuiScrollbarOptionsProvider] = tuiCreateOptions(TUI_DEFAULT_SCROLLBAR_OPTIONS);\nclass TuiScrollControls {\n  constructor() {\n    this.scrollRef = inject(TUI_SCROLL_REF).nativeElement;\n    this.nativeScrollbar = inject(TUI_SCROLLBAR_OPTIONS).mode === 'native';\n    this.refresh$ = inject(WA_ANIMATION_FRAME).pipe(throttleTime(300, tuiZonefreeScheduler()), map(() => this.scrollbars), startWith([false, false]), distinctUntilChanged((a, b) => a[0] === b[0] && a[1] === b[1]), tuiZoneOptimized());\n  }\n  get scrollbars() {\n    const {\n      clientHeight,\n      scrollHeight,\n      clientWidth,\n      scrollWidth\n    } = this.scrollRef;\n    return [Math.ceil(clientHeight / scrollHeight * 100) < 100, Math.ceil(clientWidth / scrollWidth * 100) < 100];\n  }\n  static {\n    this.ɵfac = function TuiScrollControls_Factory(t) {\n      return new (t || TuiScrollControls)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiScrollControls,\n      selectors: [[\"tui-scroll-controls\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[\"custom\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [4, \"ngIf\"], [\"tuiAnimated\", \"\", \"class\", \"t-bar t-bar_vertical\", 3, \"t-bar_has-horizontal\", \"mousedown.capture.prevent\", 4, \"ngIf\"], [\"tuiAnimated\", \"\", \"class\", \"t-bar t-bar_horizontal\", 3, \"t-bar_has-vertical\", \"mousedown.capture.prevent\", 4, \"ngIf\"], [\"tuiAnimated\", \"\", 1, \"t-bar\", \"t-bar_vertical\", 3, \"mousedown.capture.prevent\"], [\"tuiScrollbar\", \"vertical\", 1, \"t-thumb\"], [\"tuiAnimated\", \"\", 1, \"t-bar\", \"t-bar_horizontal\", 3, \"mousedown.capture.prevent\"], [\"tuiScrollbar\", \"horizontal\", 1, \"t-thumb\"]],\n      template: function TuiScrollControls_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiScrollControls_ng_container_0_Template, 1, 0, \"ng-container\", 1)(1, TuiScrollControls_ng_template_1_Template, 2, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const custom_r5 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.nativeScrollbar)(\"ngIfElse\", custom_r5);\n        }\n      },\n      dependencies: [AsyncPipe, NgIf, TuiAnimated, TuiScrollbarDirective],\n      styles: [\"[_nghost-%COMP%]{position:sticky;top:0;left:0;z-index:1;min-inline-size:calc(100% - 1px);min-block-size:calc(100% - 1px);max-inline-size:calc(100% - 1px);max-block-size:calc(100% - 1px);float:inline-start;margin-inline-end:calc(-100% + 1px);pointer-events:none}.t-bar[_ngcontent-%COMP%]{position:absolute;right:0;bottom:0;pointer-events:auto}.t-bar.tui-enter[_ngcontent-%COMP%], .t-bar.tui-leave[_ngcontent-%COMP%]{animation-name:tuiFade}.t-bar_vertical[_ngcontent-%COMP%]{top:0;inline-size:.875rem}.t-bar_horizontal[_ngcontent-%COMP%]{left:0;block-size:.875rem}.t-bar_has-horizontal[_ngcontent-%COMP%]{bottom:.5rem}.t-bar_has-vertical[_ngcontent-%COMP%]{right:.5rem}.t-thumb[_ngcontent-%COMP%]{transition-property:all;transition-duration:.15s;transition-timing-function:ease-in-out;position:absolute;border-radius:6.25rem;border:.25rem solid transparent;cursor:pointer;pointer-events:auto;-webkit-user-select:none;user-select:none;background:currentColor;background-clip:content-box;box-sizing:border-box;transition-property:width,height,opacity;opacity:.2}.t-thumb[_ngcontent-%COMP%]:hover{opacity:.24}.t-thumb[_ngcontent-%COMP%]:active{opacity:.48}.t-bar_vertical[_ngcontent-%COMP%]   .t-thumb[_ngcontent-%COMP%]{right:0;inline-size:.75rem;min-block-size:1.25rem}.t-bar_vertical[_ngcontent-%COMP%]:hover   .t-thumb[_ngcontent-%COMP%], .t-bar_vertical[_ngcontent-%COMP%]   .t-thumb[_ngcontent-%COMP%]:active{inline-size:.875rem}.t-bar_horizontal[_ngcontent-%COMP%]   .t-thumb[_ngcontent-%COMP%]{bottom:0;block-size:.75rem;min-inline-size:1.25rem}.t-bar_horizontal[_ngcontent-%COMP%]:hover   .t-thumb[_ngcontent-%COMP%], .t-bar_horizontal[_ngcontent-%COMP%]   .t-thumb[_ngcontent-%COMP%]:active{block-size:.875rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiScrollControls, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-scroll-controls',\n      imports: [AsyncPipe, NgIf, TuiAnimated, TuiScrollbarDirective],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-container *ngIf=\\\"nativeScrollbar; else custom\\\" />\\n<ng-template #custom>\\n    <ng-container *ngIf=\\\"refresh$ | async as bars\\\">\\n        <div\\n            *ngIf=\\\"bars[0]\\\"\\n            tuiAnimated\\n            class=\\\"t-bar t-bar_vertical\\\"\\n            [class.t-bar_has-horizontal]=\\\"bars[1]\\\"\\n            (mousedown.capture.prevent)=\\\"(0)\\\"\\n        >\\n            <div\\n                tuiScrollbar=\\\"vertical\\\"\\n                class=\\\"t-thumb\\\"\\n            ></div>\\n        </div>\\n        <div\\n            *ngIf=\\\"bars[1]\\\"\\n            tuiAnimated\\n            class=\\\"t-bar t-bar_horizontal\\\"\\n            [class.t-bar_has-vertical]=\\\"bars[0]\\\"\\n            (mousedown.capture.prevent)=\\\"(0)\\\"\\n        >\\n            <div\\n                tuiScrollbar=\\\"horizontal\\\"\\n                class=\\\"t-thumb\\\"\\n            ></div>\\n        </div>\\n    </ng-container>\\n</ng-template>\\n\",\n      styles: [\":host{position:sticky;top:0;left:0;z-index:1;min-inline-size:calc(100% - 1px);min-block-size:calc(100% - 1px);max-inline-size:calc(100% - 1px);max-block-size:calc(100% - 1px);float:inline-start;margin-inline-end:calc(-100% + 1px);pointer-events:none}.t-bar{position:absolute;right:0;bottom:0;pointer-events:auto}.t-bar.tui-enter,.t-bar.tui-leave{animation-name:tuiFade}.t-bar_vertical{top:0;inline-size:.875rem}.t-bar_horizontal{left:0;block-size:.875rem}.t-bar_has-horizontal{bottom:.5rem}.t-bar_has-vertical{right:.5rem}.t-thumb{transition-property:all;transition-duration:.15s;transition-timing-function:ease-in-out;position:absolute;border-radius:6.25rem;border:.25rem solid transparent;cursor:pointer;pointer-events:auto;-webkit-user-select:none;user-select:none;background:currentColor;background-clip:content-box;box-sizing:border-box;transition-property:width,height,opacity;opacity:.2}.t-thumb:hover{opacity:.24}.t-thumb:active{opacity:.48}.t-bar_vertical .t-thumb{right:0;inline-size:.75rem;min-block-size:1.25rem}.t-bar_vertical:hover .t-thumb,.t-bar_vertical .t-thumb:active{inline-size:.875rem}.t-bar_horizontal .t-thumb{bottom:0;block-size:.75rem;min-inline-size:1.25rem}.t-bar_horizontal:hover .t-thumb,.t-bar_horizontal .t-thumb:active{block-size:.875rem}\\n\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * An event for scrolling an element into view within {@link TuiScrollbar}.\n */\nconst TUI_SCROLL_INTO_VIEW = 'tui-scroll-into-view';\n/**\n * An event to notify {@link TuiScrollbar} that\n * it should control a nested element.\n */\nconst TUI_SCROLLABLE = 'tui-scrollable';\nclass TuiScrollbar {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.options = inject(TUI_SCROLLBAR_OPTIONS);\n    this.isIOS = inject(TUI_IS_IOS);\n    this.browserScrollRef = new ElementRef(this.el);\n    /**\n     * @deprecated: use tuiScrollbarOptionsProvider({ mode: 'hidden' })\n     */\n    this.hidden = this.options.mode === 'hidden';\n  }\n  get delegated() {\n    return this.scrollRef !== this.el || this.options.mode === 'native';\n  }\n  get scrollRef() {\n    return this.browserScrollRef.nativeElement;\n  }\n  set scrollRef(element) {\n    this.browserScrollRef.nativeElement = element;\n  }\n  scrollIntoView(detail) {\n    if (this.delegated) {\n      return;\n    }\n    const {\n      offsetHeight,\n      offsetWidth\n    } = detail;\n    const {\n      offsetTop,\n      offsetLeft\n    } = tuiGetElementOffset(this.scrollRef, detail);\n    const scrollTop = offsetTop + offsetHeight / 2 - this.scrollRef.clientHeight / 2;\n    const scrollLeft = offsetLeft + offsetWidth / 2 - this.scrollRef.clientWidth / 2;\n    this.scrollRef.scrollTo?.(scrollLeft, scrollTop);\n  }\n  static {\n    this.ɵfac = function TuiScrollbar_Factory(t) {\n      return new (t || TuiScrollbar)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiScrollbar,\n      selectors: [[\"tui-scrollbar\"]],\n      hostVars: 2,\n      hostBindings: function TuiScrollbar_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"tui-scrollable.stop\", function TuiScrollbar_tui_scrollable_stop_HostBindingHandler($event) {\n            return ctx.scrollRef = $event.detail;\n          })(\"tui-scroll-into-view.stop\", function TuiScrollbar_tui_scroll_into_view_stop_HostBindingHandler($event) {\n            return ctx.scrollIntoView($event.detail);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_native-hidden\", ctx.options.mode !== \"native\" && (!ctx.isIOS || ctx.hidden));\n        }\n      },\n      inputs: {\n        hidden: \"hidden\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: TUI_SCROLL_REF,\n        useFactory: () => inject(TuiScrollbar).browserScrollRef\n      }]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 3,\n      consts: [[\"class\", \"t-bars\", 3, \"t-hover-mode\", 4, \"ngIf\"], [1, \"t-content\"], [1, \"t-bars\"]],\n      template: function TuiScrollbar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, TuiScrollbar_tui_scroll_controls_0_Template, 1, 2, \"tui-scroll-controls\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.hidden && !ctx.isIOS && ctx.options.mode !== \"native\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"t-content_delegated\", ctx.delegated);\n        }\n      },\n      dependencies: [NgIf, TuiScrollControls],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:flex;max-block-size:100%;isolation:isolate;overflow:auto}._native-hidden[_nghost-%COMP%]{scrollbar-width:none;-ms-overflow-style:none}._native-hidden[_nghost-%COMP%]::-webkit-scrollbar, ._native-hidden[_nghost-%COMP%]::-webkit-scrollbar-thumb{display:none}[_nghost-%COMP%]   .t-hover-mode[_ngcontent-%COMP%]:not(:active){transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;opacity:0}[_nghost-%COMP%]:hover   .t-hover-mode[_ngcontent-%COMP%]{transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;opacity:1}.t-content[_ngcontent-%COMP%]{isolation:isolate;flex:1;flex-basis:auto;inline-size:100%;block-size:-webkit-max-content;block-size:max-content}.t-content_delegated[_ngcontent-%COMP%]{block-size:100%}.t-bars[_ngcontent-%COMP%]{color:var(--tui-text-primary)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiScrollbar, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-scrollbar',\n      imports: [NgIf, TuiScrollControls],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: TUI_SCROLL_REF,\n        useFactory: () => inject(TuiScrollbar).browserScrollRef\n      }],\n      host: {\n        '[class._native-hidden]': 'options.mode !== \"native\" && (!isIOS || hidden)',\n        [`(${TUI_SCROLLABLE}.stop)`]: 'scrollRef = $event.detail',\n        [`(${TUI_SCROLL_INTO_VIEW}.stop)`]: 'scrollIntoView($event.detail)'\n      },\n      template: \"<tui-scroll-controls\\n    *ngIf=\\\"!hidden && !isIOS && options.mode !== 'native'\\\"\\n    class=\\\"t-bars\\\"\\n    [class.t-hover-mode]=\\\"options.mode === 'hover'\\\"\\n/>\\n<div\\n    class=\\\"t-content\\\"\\n    [class.t-content_delegated]=\\\"delegated\\\"\\n>\\n    <ng-content />\\n</div>\\n\",\n      styles: [\":host{position:relative;display:flex;max-block-size:100%;isolation:isolate;overflow:auto}:host._native-hidden{scrollbar-width:none;-ms-overflow-style:none}:host._native-hidden::-webkit-scrollbar,:host._native-hidden::-webkit-scrollbar-thumb{display:none}:host .t-hover-mode:not(:active){transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;opacity:0}:host:hover .t-hover-mode{transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;opacity:1}.t-content{isolation:isolate;flex:1;flex-basis:auto;inline-size:100%;block-size:-webkit-max-content;block-size:max-content}.t-content_delegated{block-size:100%}.t-bars{color:var(--tui-text-primary)}\\n\"]\n    }]\n  }], null, {\n    hidden: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Directive scrolls element into view inside tui-scrollbar\n */\nclass TuiScrollIntoView {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.destroyRef = inject(DestroyRef);\n  }\n  set tuiScrollIntoView(scroll) {\n    if (!scroll) {\n      return;\n    }\n    // Timeout is necessary in order to give element render cycle to get into its final spot\n    // (for example if it is inside dropdown box which has to be positioned first)\n    timer(0).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {\n      this.el.dispatchEvent(new CustomEvent(TUI_SCROLL_INTO_VIEW, {\n        bubbles: true,\n        detail: this.el\n      }));\n    });\n  }\n  static {\n    this.ɵfac = function TuiScrollIntoView_Factory(t) {\n      return new (t || TuiScrollIntoView)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiScrollIntoView,\n      selectors: [[\"\", \"tuiScrollIntoView\", \"\"]],\n      inputs: {\n        tuiScrollIntoView: \"tuiScrollIntoView\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiScrollIntoView, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiScrollIntoView]'\n    }]\n  }], null, {\n    tuiScrollIntoView: [{\n      type: Input\n    }]\n  });\n})();\nconst SCROLL_REF_SELECTOR = '[tuiScrollRef]';\nclass TuiScrollRef {\n  static {\n    this.ɵfac = function TuiScrollRef_Factory(t) {\n      return new (t || TuiScrollRef)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiScrollRef,\n      selectors: [[\"\", \"tuiScrollRef\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TUI_SCROLL_REF, ElementRef)])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiScrollRef, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiScrollRef]',\n      providers: [tuiProvide(TUI_SCROLL_REF, ElementRef)]\n    }]\n  }], null, null);\n})();\nclass TuiScrollable {\n  constructor() {\n    this.el = tuiInjectElement();\n  }\n  ngOnInit() {\n    this.el.dispatchEvent(new CustomEvent(TUI_SCROLLABLE, {\n      bubbles: true,\n      detail: this.el\n    }));\n  }\n  static {\n    this.ɵfac = function TuiScrollable_Factory(t) {\n      return new (t || TuiScrollable)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiScrollable,\n      selectors: [[\"\", \"tuiScrollable\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiScrollable, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiScrollable]'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { SCROLL_REF_SELECTOR, TUI_DEFAULT_SCROLLBAR_OPTIONS, TUI_SCROLLABLE, TUI_SCROLLBAR_OPTIONS, TUI_SCROLL_INTO_VIEW, TuiScrollControls, TuiScrollIntoView, TuiScrollRef, TuiScrollable, TuiScrollbar, TuiScrollbarDirective, TuiScrollbarService, tuiScrollbarOptionsProvider };", "map": {"version": 3, "names": ["AsyncPipe", "NgIf", "i0", "inject", "Injectable", "Directive", "Input", "Component", "ChangeDetectionStrategy", "ElementRef", "DestroyRef", "WA_ANIMATION_FRAME", "TuiAnimated", "tuiTypedFromEvent", "tuiZonefree", "tuiZonefreeScheduler", "tuiScrollFrom", "tuiZoneOptimized", "TUI_SCROLL_REF", "Observable", "merge", "filter", "map", "switchMap", "takeUntil", "throttleTime", "startWith", "distinctUntilChanged", "timer", "takeUntilDestroyed", "tuiInjectElement", "tuiGetElementOffset", "tuiCreateOptions", "TUI_IS_IOS", "tui<PERSON><PERSON><PERSON>", "TuiScrollControls_ng_container_0_Template", "rf", "ctx", "ɵɵelementContainer", "TuiScrollControls_ng_template_1_ng_container_0_div_1_Template", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiScrollControls_ng_template_1_ng_container_0_div_1_Template_div_mousedown_capture_prevent_0_listener", "ɵɵrestoreView", "ɵɵresetView", "ɵɵelement", "ɵɵelementEnd", "bars_r2", "ɵɵnextContext", "ngIf", "ɵɵclassProp", "TuiScrollControls_ng_template_1_ng_container_0_div_2_Template", "_r3", "TuiScrollControls_ng_template_1_ng_container_0_div_2_Template_div_mousedown_capture_prevent_0_listener", "TuiScrollControls_ng_template_1_ng_container_0_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵproperty", "TuiScrollControls_ng_template_1_Template", "ɵɵpipe", "ctx_r3", "ɵɵpipeBind1", "refresh$", "_c0", "TuiScrollbar_tui_scroll_controls_0_Template", "ctx_r0", "options", "mode", "TuiScrollbarService", "constructor", "subscriber", "scroll$", "subscribe", "el", "element", "nativeElement", "parentElement", "pipe", "target", "event", "getScrolled", "ownerDocument", "rect", "getBoundingClientRect", "vertical", "getOffsetVertical", "horizontal", "getOffsetHorizontal", "clientY", "clientX", "offsetY", "offsetX", "offsetHeight", "offsetWidth", "top", "left", "width", "height", "maxTop", "scrollHeight", "maxLeft", "scrollWidth", "scrolledTop", "scrolledLeft", "ɵfac", "TuiScrollbarService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "MIN_WIDTH", "TuiScrollbarDirective", "style", "scrollSub", "scroll<PERSON>eh<PERSON>or", "tuiS<PERSON><PERSON><PERSON>", "scrollLeft", "scrollTop", "styleSub", "dimension", "clientHeight", "clientWidth", "thumb", "getThumb", "view", "get<PERSON>iew", "getCompensation", "compensation", "Math", "ceil", "TuiScrollbarDirective_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "standalone", "features", "ɵɵProvidersFeature", "args", "selector", "providers", "TUI_DEFAULT_SCROLLBAR_OPTIONS", "TUI_SCROLLBAR_OPTIONS", "tuiScrollbarOptionsProvider", "TuiScrollControls", "scrollRef", "nativeScrollbar", "scrollbars", "a", "b", "TuiScrollControls_Factory", "ɵcmp", "ɵɵdefineComponent", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiScrollControls_Template", "ɵɵtemplateRefExtractor", "custom_r5", "ɵɵreference", "dependencies", "styles", "changeDetection", "imports", "OnPush", "TUI_SCROLL_INTO_VIEW", "TUI_SCROLLABLE", "TuiScrollbar", "isIOS", "browserScrollRef", "hidden", "delegated", "scrollIntoView", "detail", "offsetTop", "offsetLeft", "scrollTo", "TuiScrollbar_Factory", "hostVars", "hostBindings", "TuiScrollbar_HostBindings", "TuiScrollbar_tui_scrollable_stop_HostBindingHandler", "$event", "TuiScrollbar_tui_scroll_into_view_stop_HostBindingHandler", "provide", "useFactory", "ngContentSelectors", "TuiScrollbar_Template", "ɵɵprojectionDef", "ɵɵprojection", "host", "TuiScrollIntoView", "destroyRef", "tuiScrollIntoView", "scroll", "dispatchEvent", "CustomEvent", "bubbles", "TuiScrollIntoView_Factory", "SCROLL_REF_SELECTOR", "TuiScrollRef", "TuiScrollRef_Factory", "TuiScrollable", "ngOnInit", "TuiScrollable_Factory"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-components-scrollbar.mjs"], "sourcesContent": ["import { Async<PERSON><PERSON><PERSON>, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Injectable, Directive, Input, Component, ChangeDetectionStrategy, ElementRef, DestroyRef } from '@angular/core';\nimport { WA_ANIMATION_FRAME } from '@ng-web-apis/common';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { tuiTypedFromEvent, tuiZonefree, tuiZonefreeScheduler, tuiScrollFrom, tuiZoneOptimized } from '@taiga-ui/cdk/observables';\nimport { TUI_SCROLL_REF } from '@taiga-ui/core/tokens';\nimport { Observable, merge, filter, map, switchMap, takeUntil, throttleTime, startWith, distinctUntilChanged, timer } from 'rxjs';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { tuiInjectElement, tuiGetElementOffset } from '@taiga-ui/cdk/utils/dom';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { TUI_IS_IOS } from '@taiga-ui/cdk/tokens';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\n\nclass TuiScrollbarService extends Observable {\n    constructor() {\n        super((subscriber) => this.scroll$.subscribe(subscriber));\n        this.el = tuiInjectElement();\n        this.element = inject(TUI_SCROLL_REF).nativeElement;\n        this.scroll$ = merge(tuiTypedFromEvent(this.el.parentElement, 'mousedown').pipe(filter(({ target }) => target !== this.el), map((event) => this.getScrolled(event, 0.5, 0.5))), tuiTypedFromEvent(this.el, 'mousedown').pipe(tuiZonefree(), switchMap((event) => {\n            const { ownerDocument } = this.el;\n            const rect = this.el.getBoundingClientRect();\n            const vertical = getOffsetVertical(event, rect);\n            const horizontal = getOffsetHorizontal(event, rect);\n            return tuiTypedFromEvent(ownerDocument, 'mousemove').pipe(map((event) => this.getScrolled(event, vertical, horizontal)), takeUntil(tuiTypedFromEvent(ownerDocument, 'mouseup')));\n        })));\n    }\n    getScrolled({ clientY, clientX }, offsetY, offsetX) {\n        const { offsetHeight, offsetWidth } = this.el;\n        const { top, left, width, height } = this.el.parentElement.getBoundingClientRect();\n        const maxTop = this.element.scrollHeight - height;\n        const maxLeft = this.element.scrollWidth - width;\n        const scrolledTop = (clientY - top - offsetHeight * offsetY) / (height - offsetHeight);\n        const scrolledLeft = (clientX - left - offsetWidth * offsetX) / (width - offsetWidth);\n        return [maxTop * scrolledTop, maxLeft * scrolledLeft];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollbarService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollbarService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollbarService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return []; } });\nfunction getOffsetVertical({ clientY }, { top, height }) {\n    return (clientY - top) / height;\n}\nfunction getOffsetHorizontal({ clientX }, { left, width }) {\n    return (clientX - left) / width;\n}\n\nconst MIN_WIDTH = 24;\nclass TuiScrollbarDirective {\n    constructor() {\n        this.el = inject(TUI_SCROLL_REF).nativeElement;\n        this.style = tuiInjectElement().style;\n        this.scrollSub = inject(TuiScrollbarService)\n            .pipe(takeUntilDestroyed())\n            .subscribe(([top, left]) => {\n            this.el.style.scrollBehavior = 'auto';\n            if (this.tuiScrollbar === 'horizontal') {\n                this.el.scrollLeft = left;\n            }\n            else {\n                this.el.scrollTop = top;\n            }\n            this.el.style.scrollBehavior = '';\n        });\n        this.styleSub = merge(inject(WA_ANIMATION_FRAME).pipe(throttleTime(100, tuiZonefreeScheduler())), tuiScrollFrom(this.el))\n            .pipe(tuiZonefree(), takeUntilDestroyed())\n            .subscribe(() => {\n            const dimension = {\n                scrollTop: this.el.scrollTop,\n                scrollHeight: this.el.scrollHeight,\n                clientHeight: this.el.clientHeight,\n                scrollLeft: this.el.scrollLeft,\n                scrollWidth: this.el.scrollWidth,\n                clientWidth: this.el.clientWidth,\n            };\n            const thumb = `${this.getThumb(dimension) * 100}%`;\n            const view = `${this.getView(dimension) * 100}%`;\n            if (this.tuiScrollbar === 'vertical') {\n                this.style.top = thumb;\n                this.style.height = view;\n            }\n            else {\n                this.style.left = thumb;\n                this.style.width = view;\n            }\n        });\n        this.tuiScrollbar = 'vertical';\n    }\n    getScrolled(dimension) {\n        return this.tuiScrollbar === 'vertical'\n            ? dimension.scrollTop / (dimension.scrollHeight - dimension.clientHeight)\n            : dimension.scrollLeft / (dimension.scrollWidth - dimension.clientWidth);\n    }\n    getCompensation(dimension) {\n        if (((dimension.clientHeight * dimension.clientHeight) / dimension.scrollHeight >\n            MIN_WIDTH &&\n            this.tuiScrollbar === 'vertical') ||\n            ((dimension.clientWidth * dimension.clientWidth) / dimension.scrollWidth >\n                MIN_WIDTH &&\n                this.tuiScrollbar === 'horizontal')) {\n            return 0;\n        }\n        return this.tuiScrollbar === 'vertical'\n            ? MIN_WIDTH / dimension.clientHeight\n            : MIN_WIDTH / dimension.clientWidth;\n    }\n    getThumb(dimension) {\n        const compensation = this.getCompensation(dimension) || this.getView(dimension);\n        return this.getScrolled(dimension) * (1 - compensation);\n    }\n    getView(dimension) {\n        return this.tuiScrollbar === 'vertical'\n            ? Math.ceil((dimension.clientHeight / dimension.scrollHeight) * 100) / 100\n            : Math.ceil((dimension.clientWidth / dimension.scrollWidth) * 100) / 100;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollbarDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiScrollbarDirective, isStandalone: true, selector: \"[tuiScrollbar]\", inputs: { tuiScrollbar: \"tuiScrollbar\" }, providers: [TuiScrollbarService], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollbarDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiScrollbar]',\n                    providers: [TuiScrollbarService],\n                }]\n        }], propDecorators: { tuiScrollbar: [{\n                type: Input\n            }] } });\n\nconst TUI_DEFAULT_SCROLLBAR_OPTIONS = {\n    mode: 'always',\n};\nconst [TUI_SCROLLBAR_OPTIONS, tuiScrollbarOptionsProvider] = tuiCreateOptions(TUI_DEFAULT_SCROLLBAR_OPTIONS);\n\nclass TuiScrollControls {\n    constructor() {\n        this.scrollRef = inject(TUI_SCROLL_REF).nativeElement;\n        this.nativeScrollbar = inject(TUI_SCROLLBAR_OPTIONS).mode === 'native';\n        this.refresh$ = inject(WA_ANIMATION_FRAME).pipe(throttleTime(300, tuiZonefreeScheduler()), map(() => this.scrollbars), startWith([false, false]), distinctUntilChanged((a, b) => a[0] === b[0] && a[1] === b[1]), tuiZoneOptimized());\n    }\n    get scrollbars() {\n        const { clientHeight, scrollHeight, clientWidth, scrollWidth } = this.scrollRef;\n        return [\n            Math.ceil((clientHeight / scrollHeight) * 100) < 100,\n            Math.ceil((clientWidth / scrollWidth) * 100) < 100,\n        ];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollControls, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiScrollControls, isStandalone: true, selector: \"tui-scroll-controls\", ngImport: i0, template: \"<ng-container *ngIf=\\\"nativeScrollbar; else custom\\\" />\\n<ng-template #custom>\\n    <ng-container *ngIf=\\\"refresh$ | async as bars\\\">\\n        <div\\n            *ngIf=\\\"bars[0]\\\"\\n            tuiAnimated\\n            class=\\\"t-bar t-bar_vertical\\\"\\n            [class.t-bar_has-horizontal]=\\\"bars[1]\\\"\\n            (mousedown.capture.prevent)=\\\"(0)\\\"\\n        >\\n            <div\\n                tuiScrollbar=\\\"vertical\\\"\\n                class=\\\"t-thumb\\\"\\n            ></div>\\n        </div>\\n        <div\\n            *ngIf=\\\"bars[1]\\\"\\n            tuiAnimated\\n            class=\\\"t-bar t-bar_horizontal\\\"\\n            [class.t-bar_has-vertical]=\\\"bars[0]\\\"\\n            (mousedown.capture.prevent)=\\\"(0)\\\"\\n        >\\n            <div\\n                tuiScrollbar=\\\"horizontal\\\"\\n                class=\\\"t-thumb\\\"\\n            ></div>\\n        </div>\\n    </ng-container>\\n</ng-template>\\n\", styles: [\":host{position:sticky;top:0;left:0;z-index:1;min-inline-size:calc(100% - 1px);min-block-size:calc(100% - 1px);max-inline-size:calc(100% - 1px);max-block-size:calc(100% - 1px);float:inline-start;margin-inline-end:calc(-100% + 1px);pointer-events:none}.t-bar{position:absolute;right:0;bottom:0;pointer-events:auto}.t-bar.tui-enter,.t-bar.tui-leave{animation-name:tuiFade}.t-bar_vertical{top:0;inline-size:.875rem}.t-bar_horizontal{left:0;block-size:.875rem}.t-bar_has-horizontal{bottom:.5rem}.t-bar_has-vertical{right:.5rem}.t-thumb{transition-property:all;transition-duration:.15s;transition-timing-function:ease-in-out;position:absolute;border-radius:6.25rem;border:.25rem solid transparent;cursor:pointer;pointer-events:auto;-webkit-user-select:none;user-select:none;background:currentColor;background-clip:content-box;box-sizing:border-box;transition-property:width,height,opacity;opacity:.2}.t-thumb:hover{opacity:.24}.t-thumb:active{opacity:.48}.t-bar_vertical .t-thumb{right:0;inline-size:.75rem;min-block-size:1.25rem}.t-bar_vertical:hover .t-thumb,.t-bar_vertical .t-thumb:active{inline-size:.875rem}.t-bar_horizontal .t-thumb{bottom:0;block-size:.75rem;min-inline-size:1.25rem}.t-bar_horizontal:hover .t-thumb,.t-bar_horizontal .t-thumb:active{block-size:.875rem}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: TuiAnimated, selector: \"[tuiAnimated]\" }, { kind: \"directive\", type: TuiScrollbarDirective, selector: \"[tuiScrollbar]\", inputs: [\"tuiScrollbar\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollControls, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-scroll-controls', imports: [AsyncPipe, NgIf, TuiAnimated, TuiScrollbarDirective], changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-container *ngIf=\\\"nativeScrollbar; else custom\\\" />\\n<ng-template #custom>\\n    <ng-container *ngIf=\\\"refresh$ | async as bars\\\">\\n        <div\\n            *ngIf=\\\"bars[0]\\\"\\n            tuiAnimated\\n            class=\\\"t-bar t-bar_vertical\\\"\\n            [class.t-bar_has-horizontal]=\\\"bars[1]\\\"\\n            (mousedown.capture.prevent)=\\\"(0)\\\"\\n        >\\n            <div\\n                tuiScrollbar=\\\"vertical\\\"\\n                class=\\\"t-thumb\\\"\\n            ></div>\\n        </div>\\n        <div\\n            *ngIf=\\\"bars[1]\\\"\\n            tuiAnimated\\n            class=\\\"t-bar t-bar_horizontal\\\"\\n            [class.t-bar_has-vertical]=\\\"bars[0]\\\"\\n            (mousedown.capture.prevent)=\\\"(0)\\\"\\n        >\\n            <div\\n                tuiScrollbar=\\\"horizontal\\\"\\n                class=\\\"t-thumb\\\"\\n            ></div>\\n        </div>\\n    </ng-container>\\n</ng-template>\\n\", styles: [\":host{position:sticky;top:0;left:0;z-index:1;min-inline-size:calc(100% - 1px);min-block-size:calc(100% - 1px);max-inline-size:calc(100% - 1px);max-block-size:calc(100% - 1px);float:inline-start;margin-inline-end:calc(-100% + 1px);pointer-events:none}.t-bar{position:absolute;right:0;bottom:0;pointer-events:auto}.t-bar.tui-enter,.t-bar.tui-leave{animation-name:tuiFade}.t-bar_vertical{top:0;inline-size:.875rem}.t-bar_horizontal{left:0;block-size:.875rem}.t-bar_has-horizontal{bottom:.5rem}.t-bar_has-vertical{right:.5rem}.t-thumb{transition-property:all;transition-duration:.15s;transition-timing-function:ease-in-out;position:absolute;border-radius:6.25rem;border:.25rem solid transparent;cursor:pointer;pointer-events:auto;-webkit-user-select:none;user-select:none;background:currentColor;background-clip:content-box;box-sizing:border-box;transition-property:width,height,opacity;opacity:.2}.t-thumb:hover{opacity:.24}.t-thumb:active{opacity:.48}.t-bar_vertical .t-thumb{right:0;inline-size:.75rem;min-block-size:1.25rem}.t-bar_vertical:hover .t-thumb,.t-bar_vertical .t-thumb:active{inline-size:.875rem}.t-bar_horizontal .t-thumb{bottom:0;block-size:.75rem;min-inline-size:1.25rem}.t-bar_horizontal:hover .t-thumb,.t-bar_horizontal .t-thumb:active{block-size:.875rem}\\n\"] }]\n        }] });\n\n/**\n * An event for scrolling an element into view within {@link TuiScrollbar}.\n */\nconst TUI_SCROLL_INTO_VIEW = 'tui-scroll-into-view';\n/**\n * An event to notify {@link TuiScrollbar} that\n * it should control a nested element.\n */\nconst TUI_SCROLLABLE = 'tui-scrollable';\nclass TuiScrollbar {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.options = inject(TUI_SCROLLBAR_OPTIONS);\n        this.isIOS = inject(TUI_IS_IOS);\n        this.browserScrollRef = new ElementRef(this.el);\n        /**\n         * @deprecated: use tuiScrollbarOptionsProvider({ mode: 'hidden' })\n         */\n        this.hidden = this.options.mode === 'hidden';\n    }\n    get delegated() {\n        return this.scrollRef !== this.el || this.options.mode === 'native';\n    }\n    get scrollRef() {\n        return this.browserScrollRef.nativeElement;\n    }\n    set scrollRef(element) {\n        this.browserScrollRef.nativeElement = element;\n    }\n    scrollIntoView(detail) {\n        if (this.delegated) {\n            return;\n        }\n        const { offsetHeight, offsetWidth } = detail;\n        const { offsetTop, offsetLeft } = tuiGetElementOffset(this.scrollRef, detail);\n        const scrollTop = offsetTop + offsetHeight / 2 - this.scrollRef.clientHeight / 2;\n        const scrollLeft = offsetLeft + offsetWidth / 2 - this.scrollRef.clientWidth / 2;\n        this.scrollRef.scrollTo?.(scrollLeft, scrollTop);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollbar, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiScrollbar, isStandalone: true, selector: \"tui-scrollbar\", inputs: { hidden: \"hidden\" }, host: { listeners: { \"tui-scrollable.stop\": \"scrollRef = $event.detail\", \"tui-scroll-into-view.stop\": \"scrollIntoView($event.detail)\" }, properties: { \"class._native-hidden\": \"options.mode !== \\\"native\\\" && (!isIOS || hidden)\" } }, providers: [\n            {\n                provide: TUI_SCROLL_REF,\n                useFactory: () => inject(TuiScrollbar).browserScrollRef,\n            },\n        ], ngImport: i0, template: \"<tui-scroll-controls\\n    *ngIf=\\\"!hidden && !isIOS && options.mode !== 'native'\\\"\\n    class=\\\"t-bars\\\"\\n    [class.t-hover-mode]=\\\"options.mode === 'hover'\\\"\\n/>\\n<div\\n    class=\\\"t-content\\\"\\n    [class.t-content_delegated]=\\\"delegated\\\"\\n>\\n    <ng-content />\\n</div>\\n\", styles: [\":host{position:relative;display:flex;max-block-size:100%;isolation:isolate;overflow:auto}:host._native-hidden{scrollbar-width:none;-ms-overflow-style:none}:host._native-hidden::-webkit-scrollbar,:host._native-hidden::-webkit-scrollbar-thumb{display:none}:host .t-hover-mode:not(:active){transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;opacity:0}:host:hover .t-hover-mode{transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;opacity:1}.t-content{isolation:isolate;flex:1;flex-basis:auto;inline-size:100%;block-size:-webkit-max-content;block-size:max-content}.t-content_delegated{block-size:100%}.t-bars{color:var(--tui-text-primary)}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: TuiScrollControls, selector: \"tui-scroll-controls\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollbar, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-scrollbar', imports: [NgIf, TuiScrollControls], changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        {\n                            provide: TUI_SCROLL_REF,\n                            useFactory: () => inject(TuiScrollbar).browserScrollRef,\n                        },\n                    ], host: {\n                        '[class._native-hidden]': 'options.mode !== \"native\" && (!isIOS || hidden)',\n                        [`(${TUI_SCROLLABLE}.stop)`]: 'scrollRef = $event.detail',\n                        [`(${TUI_SCROLL_INTO_VIEW}.stop)`]: 'scrollIntoView($event.detail)',\n                    }, template: \"<tui-scroll-controls\\n    *ngIf=\\\"!hidden && !isIOS && options.mode !== 'native'\\\"\\n    class=\\\"t-bars\\\"\\n    [class.t-hover-mode]=\\\"options.mode === 'hover'\\\"\\n/>\\n<div\\n    class=\\\"t-content\\\"\\n    [class.t-content_delegated]=\\\"delegated\\\"\\n>\\n    <ng-content />\\n</div>\\n\", styles: [\":host{position:relative;display:flex;max-block-size:100%;isolation:isolate;overflow:auto}:host._native-hidden{scrollbar-width:none;-ms-overflow-style:none}:host._native-hidden::-webkit-scrollbar,:host._native-hidden::-webkit-scrollbar-thumb{display:none}:host .t-hover-mode:not(:active){transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;opacity:0}:host:hover .t-hover-mode{transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;opacity:1}.t-content{isolation:isolate;flex:1;flex-basis:auto;inline-size:100%;block-size:-webkit-max-content;block-size:max-content}.t-content_delegated{block-size:100%}.t-bars{color:var(--tui-text-primary)}\\n\"] }]\n        }], propDecorators: { hidden: [{\n                type: Input\n            }] } });\n\n/**\n * Directive scrolls element into view inside tui-scrollbar\n */\nclass TuiScrollIntoView {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.destroyRef = inject(DestroyRef);\n    }\n    set tuiScrollIntoView(scroll) {\n        if (!scroll) {\n            return;\n        }\n        // Timeout is necessary in order to give element render cycle to get into its final spot\n        // (for example if it is inside dropdown box which has to be positioned first)\n        timer(0)\n            .pipe(takeUntilDestroyed(this.destroyRef))\n            .subscribe(() => {\n            this.el.dispatchEvent(new CustomEvent(TUI_SCROLL_INTO_VIEW, {\n                bubbles: true,\n                detail: this.el,\n            }));\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollIntoView, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiScrollIntoView, isStandalone: true, selector: \"[tuiScrollIntoView]\", inputs: { tuiScrollIntoView: \"tuiScrollIntoView\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollIntoView, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiScrollIntoView]',\n                }]\n        }], propDecorators: { tuiScrollIntoView: [{\n                type: Input\n            }] } });\n\nconst SCROLL_REF_SELECTOR = '[tuiScrollRef]';\nclass TuiScrollRef {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollRef, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiScrollRef, isStandalone: true, selector: \"[tuiScrollRef]\", providers: [tuiProvide(TUI_SCROLL_REF, ElementRef)], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollRef, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiScrollRef]',\n                    providers: [tuiProvide(TUI_SCROLL_REF, ElementRef)],\n                }]\n        }] });\n\nclass TuiScrollable {\n    constructor() {\n        this.el = tuiInjectElement();\n    }\n    ngOnInit() {\n        this.el.dispatchEvent(new CustomEvent(TUI_SCROLLABLE, {\n            bubbles: true,\n            detail: this.el,\n        }));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollable, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiScrollable, isStandalone: true, selector: \"[tuiScrollable]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollable, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiScrollable]',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { SCROLL_REF_SELECTOR, TUI_DEFAULT_SCROLLBAR_OPTIONS, TUI_SCROLLABLE, TUI_SCROLLBAR_OPTIONS, TUI_SCROLL_INTO_VIEW, TuiScrollControls, TuiScrollIntoView, TuiScrollRef, TuiScrollable, TuiScrollbar, TuiScrollbarDirective, TuiScrollbarService, tuiScrollbarOptionsProvider };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,IAAI,QAAQ,iBAAiB;AACjD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,UAAU,EAAEC,UAAU,QAAQ,eAAe;AAChI,SAASC,kBAAkB,QAAQ,qBAAqB;AACxD,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,gBAAgB,QAAQ,2BAA2B;AACjI,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,SAAS,EAAEC,oBAAoB,EAAEC,KAAK,QAAQ,MAAM;AACjI,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,yBAAyB;AAC/E,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,UAAU,QAAQ,mCAAmC;AAAC,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAwBsClC,EAAE,CAAAoC,kBAAA,EAkHmJ,CAAC;EAAA;AAAA;AAAA,SAAAC,8DAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAI,GAAA,GAlHtJtC,EAAE,CAAAuC,gBAAA;IAAFvC,EAAE,CAAAwC,cAAA,YAkHqc,CAAC;IAlHxcxC,EAAE,CAAAyC,UAAA,uCAAAC,uGAAA;MAAF1C,EAAE,CAAA2C,aAAA,CAAAL,GAAA;MAAA,OAAFtC,EAAE,CAAA4C,WAAA,CAkHub,CAAC;IAAA,CAAE,CAAC;IAlH7b5C,EAAE,CAAA6C,SAAA,YAkH0jB,CAAC;IAlH7jB7C,EAAE,CAAA8C,YAAA,CAkH0kB,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAa,OAAA,GAlH7kB/C,EAAE,CAAAgD,aAAA,GAAAC,IAAA;IAAFjD,EAAE,CAAAkD,WAAA,yBAAAH,OAAA,GAkHyY,CAAC;EAAA;AAAA;AAAA,SAAAI,8DAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkB,GAAA,GAlH5YpD,EAAE,CAAAuC,gBAAA;IAAFvC,EAAE,CAAAwC,cAAA,YAkH8yB,CAAC;IAlHjzBxC,EAAE,CAAAyC,UAAA,uCAAAY,uGAAA;MAAFrD,EAAE,CAAA2C,aAAA,CAAAS,GAAA;MAAA,OAAFpD,EAAE,CAAA4C,WAAA,CAkHgyB,CAAC;IAAA,CAAE,CAAC;IAlHtyB5C,EAAE,CAAA6C,SAAA,YAkHq6B,CAAC;IAlHx6B7C,EAAE,CAAA8C,YAAA,CAkHq7B,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAa,OAAA,GAlHx7B/C,EAAE,CAAAgD,aAAA,GAAAC,IAAA;IAAFjD,EAAE,CAAAkD,WAAA,uBAAAH,OAAA,GAkHkvB,CAAC;EAAA;AAAA;AAAA,SAAAO,wDAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlHrvBlC,EAAE,CAAAuD,uBAAA,EAkHiO,CAAC;IAlHpOvD,EAAE,CAAAwD,UAAA,IAAAnB,6DAAA,gBAkHqc,CAAC,IAAAc,6DAAA,gBAAwW,CAAC;IAlHjzBnD,EAAE,CAAAyD,qBAAA;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAa,OAAA,GAAAZ,GAAA,CAAAc,IAAA;IAAFjD,EAAE,CAAA0D,SAAA,CAkH4Q,CAAC;IAlH/Q1D,EAAE,CAAA2D,UAAA,SAAAZ,OAAA,GAkH4Q,CAAC;IAlH/Q/C,EAAE,CAAA0D,SAAA,CAkHqnB,CAAC;IAlHxnB1D,EAAE,CAAA2D,UAAA,SAAAZ,OAAA,GAkHqnB,CAAC;EAAA;AAAA;AAAA,SAAAa,yCAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlHxnBlC,EAAE,CAAAwD,UAAA,IAAAF,uDAAA,yBAkHiO,CAAC;IAlHpOtD,EAAE,CAAA6D,MAAA;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAA4B,MAAA,GAAF9D,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAA2D,UAAA,SAAF3D,EAAE,CAAA+D,WAAA,OAAAD,MAAA,CAAAE,QAAA,CAkHuN,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,4CAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlH1NlC,EAAE,CAAA6C,SAAA,4BAsK+F,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAiC,MAAA,GAtKlGnE,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAkD,WAAA,iBAAAiB,MAAA,CAAAC,OAAA,CAAAC,IAAA,YAsK2F,CAAC;EAAA;AAAA;AA5LnM,MAAMC,mBAAmB,SAASrD,UAAU,CAAC;EACzCsD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAEC,UAAU,IAAK,IAAI,CAACC,OAAO,CAACC,SAAS,CAACF,UAAU,CAAC,CAAC;IACzD,IAAI,CAACG,EAAE,GAAG/C,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACgD,OAAO,GAAG3E,MAAM,CAACe,cAAc,CAAC,CAAC6D,aAAa;IACnD,IAAI,CAACJ,OAAO,GAAGvD,KAAK,CAACP,iBAAiB,CAAC,IAAI,CAACgE,EAAE,CAACG,aAAa,EAAE,WAAW,CAAC,CAACC,IAAI,CAAC5D,MAAM,CAAC,CAAC;MAAE6D;IAAO,CAAC,KAAKA,MAAM,KAAK,IAAI,CAACL,EAAE,CAAC,EAAEvD,GAAG,CAAE6D,KAAK,IAAK,IAAI,CAACC,WAAW,CAACD,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,EAAEtE,iBAAiB,CAAC,IAAI,CAACgE,EAAE,EAAE,WAAW,CAAC,CAACI,IAAI,CAACnE,WAAW,CAAC,CAAC,EAAES,SAAS,CAAE4D,KAAK,IAAK;MAC7P,MAAM;QAAEE;MAAc,CAAC,GAAG,IAAI,CAACR,EAAE;MACjC,MAAMS,IAAI,GAAG,IAAI,CAACT,EAAE,CAACU,qBAAqB,CAAC,CAAC;MAC5C,MAAMC,QAAQ,GAAGC,iBAAiB,CAACN,KAAK,EAAEG,IAAI,CAAC;MAC/C,MAAMI,UAAU,GAAGC,mBAAmB,CAACR,KAAK,EAAEG,IAAI,CAAC;MACnD,OAAOzE,iBAAiB,CAACwE,aAAa,EAAE,WAAW,CAAC,CAACJ,IAAI,CAAC3D,GAAG,CAAE6D,KAAK,IAAK,IAAI,CAACC,WAAW,CAACD,KAAK,EAAEK,QAAQ,EAAEE,UAAU,CAAC,CAAC,EAAElE,SAAS,CAACX,iBAAiB,CAACwE,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;IACpL,CAAC,CAAC,CAAC,CAAC;EACR;EACAD,WAAWA,CAAC;IAAEQ,OAAO;IAAEC;EAAQ,CAAC,EAAEC,OAAO,EAAEC,OAAO,EAAE;IAChD,MAAM;MAAEC,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI,CAACpB,EAAE;IAC7C,MAAM;MAAEqB,GAAG;MAAEC,IAAI;MAAEC,KAAK;MAAEC;IAAO,CAAC,GAAG,IAAI,CAACxB,EAAE,CAACG,aAAa,CAACO,qBAAqB,CAAC,CAAC;IAClF,MAAMe,MAAM,GAAG,IAAI,CAACxB,OAAO,CAACyB,YAAY,GAAGF,MAAM;IACjD,MAAMG,OAAO,GAAG,IAAI,CAAC1B,OAAO,CAAC2B,WAAW,GAAGL,KAAK;IAChD,MAAMM,WAAW,GAAG,CAACd,OAAO,GAAGM,GAAG,GAAGF,YAAY,GAAGF,OAAO,KAAKO,MAAM,GAAGL,YAAY,CAAC;IACtF,MAAMW,YAAY,GAAG,CAACd,OAAO,GAAGM,IAAI,GAAGF,WAAW,GAAGF,OAAO,KAAKK,KAAK,GAAGH,WAAW,CAAC;IACrF,OAAO,CAACK,MAAM,GAAGI,WAAW,EAAEF,OAAO,GAAGG,YAAY,CAAC;EACzD;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,4BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFtC,mBAAmB;IAAA,CAAoD;EAAE;EACpL;IAAS,IAAI,CAACuC,KAAK,kBAD8E7G,EAAE,CAAA8G,kBAAA;MAAAC,KAAA,EACYzC,mBAAmB;MAAA0C,OAAA,EAAnB1C,mBAAmB,CAAAoC;IAAA,EAAG;EAAE;AAC3I;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAHqGjH,EAAE,CAAAkH,iBAAA,CAGX5C,mBAAmB,EAAc,CAAC;IAClH6C,IAAI,EAAEjH;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AACtD,SAASqF,iBAAiBA,CAAC;EAAEG;AAAQ,CAAC,EAAE;EAAEM,GAAG;EAAEG;AAAO,CAAC,EAAE;EACrD,OAAO,CAACT,OAAO,GAAGM,GAAG,IAAIG,MAAM;AACnC;AACA,SAASV,mBAAmBA,CAAC;EAAEE;AAAQ,CAAC,EAAE;EAAEM,IAAI;EAAEC;AAAM,CAAC,EAAE;EACvD,OAAO,CAACP,OAAO,GAAGM,IAAI,IAAIC,KAAK;AACnC;AAEA,MAAMkB,SAAS,GAAG,EAAE;AACpB,MAAMC,qBAAqB,CAAC;EACxB9C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACI,EAAE,GAAG1E,MAAM,CAACe,cAAc,CAAC,CAAC6D,aAAa;IAC9C,IAAI,CAACyC,KAAK,GAAG1F,gBAAgB,CAAC,CAAC,CAAC0F,KAAK;IACrC,IAAI,CAACC,SAAS,GAAGtH,MAAM,CAACqE,mBAAmB,CAAC,CACvCS,IAAI,CAACpD,kBAAkB,CAAC,CAAC,CAAC,CAC1B+C,SAAS,CAAC,CAAC,CAACsB,GAAG,EAAEC,IAAI,CAAC,KAAK;MAC5B,IAAI,CAACtB,EAAE,CAAC2C,KAAK,CAACE,cAAc,GAAG,MAAM;MACrC,IAAI,IAAI,CAACC,YAAY,KAAK,YAAY,EAAE;QACpC,IAAI,CAAC9C,EAAE,CAAC+C,UAAU,GAAGzB,IAAI;MAC7B,CAAC,MACI;QACD,IAAI,CAACtB,EAAE,CAACgD,SAAS,GAAG3B,GAAG;MAC3B;MACA,IAAI,CAACrB,EAAE,CAAC2C,KAAK,CAACE,cAAc,GAAG,EAAE;IACrC,CAAC,CAAC;IACF,IAAI,CAACI,QAAQ,GAAG1G,KAAK,CAACjB,MAAM,CAACQ,kBAAkB,CAAC,CAACsE,IAAI,CAACxD,YAAY,CAAC,GAAG,EAAEV,oBAAoB,CAAC,CAAC,CAAC,CAAC,EAAEC,aAAa,CAAC,IAAI,CAAC6D,EAAE,CAAC,CAAC,CACpHI,IAAI,CAACnE,WAAW,CAAC,CAAC,EAAEe,kBAAkB,CAAC,CAAC,CAAC,CACzC+C,SAAS,CAAC,MAAM;MACjB,MAAMmD,SAAS,GAAG;QACdF,SAAS,EAAE,IAAI,CAAChD,EAAE,CAACgD,SAAS;QAC5BtB,YAAY,EAAE,IAAI,CAAC1B,EAAE,CAAC0B,YAAY;QAClCyB,YAAY,EAAE,IAAI,CAACnD,EAAE,CAACmD,YAAY;QAClCJ,UAAU,EAAE,IAAI,CAAC/C,EAAE,CAAC+C,UAAU;QAC9BnB,WAAW,EAAE,IAAI,CAAC5B,EAAE,CAAC4B,WAAW;QAChCwB,WAAW,EAAE,IAAI,CAACpD,EAAE,CAACoD;MACzB,CAAC;MACD,MAAMC,KAAK,GAAG,GAAG,IAAI,CAACC,QAAQ,CAACJ,SAAS,CAAC,GAAG,GAAG,GAAG;MAClD,MAAMK,IAAI,GAAG,GAAG,IAAI,CAACC,OAAO,CAACN,SAAS,CAAC,GAAG,GAAG,GAAG;MAChD,IAAI,IAAI,CAACJ,YAAY,KAAK,UAAU,EAAE;QAClC,IAAI,CAACH,KAAK,CAACtB,GAAG,GAAGgC,KAAK;QACtB,IAAI,CAACV,KAAK,CAACnB,MAAM,GAAG+B,IAAI;MAC5B,CAAC,MACI;QACD,IAAI,CAACZ,KAAK,CAACrB,IAAI,GAAG+B,KAAK;QACvB,IAAI,CAACV,KAAK,CAACpB,KAAK,GAAGgC,IAAI;MAC3B;IACJ,CAAC,CAAC;IACF,IAAI,CAACT,YAAY,GAAG,UAAU;EAClC;EACAvC,WAAWA,CAAC2C,SAAS,EAAE;IACnB,OAAO,IAAI,CAACJ,YAAY,KAAK,UAAU,GACjCI,SAAS,CAACF,SAAS,IAAIE,SAAS,CAACxB,YAAY,GAAGwB,SAAS,CAACC,YAAY,CAAC,GACvED,SAAS,CAACH,UAAU,IAAIG,SAAS,CAACtB,WAAW,GAAGsB,SAAS,CAACE,WAAW,CAAC;EAChF;EACAK,eAAeA,CAACP,SAAS,EAAE;IACvB,IAAMA,SAAS,CAACC,YAAY,GAAGD,SAAS,CAACC,YAAY,GAAID,SAAS,CAACxB,YAAY,GAC3Ee,SAAS,IACT,IAAI,CAACK,YAAY,KAAK,UAAU,IAC9BI,SAAS,CAACE,WAAW,GAAGF,SAAS,CAACE,WAAW,GAAIF,SAAS,CAACtB,WAAW,GACpEa,SAAS,IACT,IAAI,CAACK,YAAY,KAAK,YAAa,EAAE;MACzC,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACA,YAAY,KAAK,UAAU,GACjCL,SAAS,GAAGS,SAAS,CAACC,YAAY,GAClCV,SAAS,GAAGS,SAAS,CAACE,WAAW;EAC3C;EACAE,QAAQA,CAACJ,SAAS,EAAE;IAChB,MAAMQ,YAAY,GAAG,IAAI,CAACD,eAAe,CAACP,SAAS,CAAC,IAAI,IAAI,CAACM,OAAO,CAACN,SAAS,CAAC;IAC/E,OAAO,IAAI,CAAC3C,WAAW,CAAC2C,SAAS,CAAC,IAAI,CAAC,GAAGQ,YAAY,CAAC;EAC3D;EACAF,OAAOA,CAACN,SAAS,EAAE;IACf,OAAO,IAAI,CAACJ,YAAY,KAAK,UAAU,GACjCa,IAAI,CAACC,IAAI,CAAEV,SAAS,CAACC,YAAY,GAAGD,SAAS,CAACxB,YAAY,GAAI,GAAG,CAAC,GAAG,GAAG,GACxEiC,IAAI,CAACC,IAAI,CAAEV,SAAS,CAACE,WAAW,GAAGF,SAAS,CAACtB,WAAW,GAAI,GAAG,CAAC,GAAG,GAAG;EAChF;EACA;IAAS,IAAI,CAACG,IAAI,YAAA8B,8BAAA5B,CAAA;MAAA,YAAAA,CAAA,IAAyFS,qBAAqB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAACoB,IAAI,kBAlF+EzI,EAAE,CAAA0I,iBAAA;MAAAvB,IAAA,EAkFJE,qBAAqB;MAAAsB,SAAA;MAAAC,MAAA;QAAAnB,YAAA;MAAA;MAAAoB,UAAA;MAAAC,QAAA,GAlFnB9I,EAAE,CAAA+I,kBAAA,CAkFwH,CAACzE,mBAAmB,CAAC;IAAA,EAAiB;EAAE;AACvQ;AACA;EAAA,QAAA2C,SAAA,oBAAAA,SAAA,KApFqGjH,EAAE,CAAAkH,iBAAA,CAoFXG,qBAAqB,EAAc,CAAC;IACpHF,IAAI,EAAEhH,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,CAAC5E,mBAAmB;IACnC,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEmD,YAAY,EAAE,CAAC;MAC7BN,IAAI,EAAE/G;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+I,6BAA6B,GAAG;EAClC9E,IAAI,EAAE;AACV,CAAC;AACD,MAAM,CAAC+E,qBAAqB,EAAEC,2BAA2B,CAAC,GAAGvH,gBAAgB,CAACqH,6BAA6B,CAAC;AAE5G,MAAMG,iBAAiB,CAAC;EACpB/E,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgF,SAAS,GAAGtJ,MAAM,CAACe,cAAc,CAAC,CAAC6D,aAAa;IACrD,IAAI,CAAC2E,eAAe,GAAGvJ,MAAM,CAACmJ,qBAAqB,CAAC,CAAC/E,IAAI,KAAK,QAAQ;IACtE,IAAI,CAACL,QAAQ,GAAG/D,MAAM,CAACQ,kBAAkB,CAAC,CAACsE,IAAI,CAACxD,YAAY,CAAC,GAAG,EAAEV,oBAAoB,CAAC,CAAC,CAAC,EAAEO,GAAG,CAAC,MAAM,IAAI,CAACqI,UAAU,CAAC,EAAEjI,SAAS,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEC,oBAAoB,CAAC,CAACiI,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,KAAKC,CAAC,CAAC,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC,KAAKC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE5I,gBAAgB,CAAC,CAAC,CAAC;EACzO;EACA,IAAI0I,UAAUA,CAAA,EAAG;IACb,MAAM;MAAE3B,YAAY;MAAEzB,YAAY;MAAE0B,WAAW;MAAExB;IAAY,CAAC,GAAG,IAAI,CAACgD,SAAS;IAC/E,OAAO,CACHjB,IAAI,CAACC,IAAI,CAAET,YAAY,GAAGzB,YAAY,GAAI,GAAG,CAAC,GAAG,GAAG,EACpDiC,IAAI,CAACC,IAAI,CAAER,WAAW,GAAGxB,WAAW,GAAI,GAAG,CAAC,GAAG,GAAG,CACrD;EACL;EACA;IAAS,IAAI,CAACG,IAAI,YAAAkD,0BAAAhD,CAAA;MAAA,YAAAA,CAAA,IAAyF0C,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAACO,IAAI,kBAlH+E7J,EAAE,CAAA8J,iBAAA;MAAA3C,IAAA,EAkHJmC,iBAAiB;MAAAX,SAAA;MAAAE,UAAA;MAAAC,QAAA,GAlHf9I,EAAE,CAAA+J,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAlI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlC,EAAE,CAAAwD,UAAA,IAAAvB,yCAAA,yBAkHmJ,CAAC,IAAA2B,wCAAA,gCAlHtJ5D,EAAE,CAAAqK,sBAkH0K,CAAC;QAAA;QAAA,IAAAnI,EAAA;UAAA,MAAAoI,SAAA,GAlH7KtK,EAAE,CAAAuK,WAAA;UAAFvK,EAAE,CAAA2D,UAAA,SAAAxB,GAAA,CAAAqH,eAkHmI,CAAC,aAAAc,SAAU,CAAC;QAAA;MAAA;MAAAE,YAAA,GAA6nE1K,SAAS,EAA8CC,IAAI,EAA6FW,WAAW,EAA0D2G,qBAAqB;MAAAoD,MAAA;MAAAC,eAAA;IAAA,EAAgH;EAAE;AACvtF;AACA;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KApHqGjH,EAAE,CAAAkH,iBAAA,CAoHXoC,iBAAiB,EAAc,CAAC;IAChHnC,IAAI,EAAE9G,SAAS;IACf2I,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,IAAI;MAAEI,QAAQ,EAAE,qBAAqB;MAAE0B,OAAO,EAAE,CAAC7K,SAAS,EAAEC,IAAI,EAAEW,WAAW,EAAE2G,qBAAqB,CAAC;MAAEqD,eAAe,EAAEpK,uBAAuB,CAACsK,MAAM;MAAET,QAAQ,EAAE,k4BAAk4B;MAAEM,MAAM,EAAE,CAAC,2vCAA2vC;IAAE,CAAC;EACt0E,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAMI,oBAAoB,GAAG,sBAAsB;AACnD;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG,gBAAgB;AACvC,MAAMC,YAAY,CAAC;EACfxG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACI,EAAE,GAAG/C,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACwC,OAAO,GAAGnE,MAAM,CAACmJ,qBAAqB,CAAC;IAC5C,IAAI,CAAC4B,KAAK,GAAG/K,MAAM,CAAC8B,UAAU,CAAC;IAC/B,IAAI,CAACkJ,gBAAgB,GAAG,IAAI1K,UAAU,CAAC,IAAI,CAACoE,EAAE,CAAC;IAC/C;AACR;AACA;IACQ,IAAI,CAACuG,MAAM,GAAG,IAAI,CAAC9G,OAAO,CAACC,IAAI,KAAK,QAAQ;EAChD;EACA,IAAI8G,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC5B,SAAS,KAAK,IAAI,CAAC5E,EAAE,IAAI,IAAI,CAACP,OAAO,CAACC,IAAI,KAAK,QAAQ;EACvE;EACA,IAAIkF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC0B,gBAAgB,CAACpG,aAAa;EAC9C;EACA,IAAI0E,SAASA,CAAC3E,OAAO,EAAE;IACnB,IAAI,CAACqG,gBAAgB,CAACpG,aAAa,GAAGD,OAAO;EACjD;EACAwG,cAAcA,CAACC,MAAM,EAAE;IACnB,IAAI,IAAI,CAACF,SAAS,EAAE;MAChB;IACJ;IACA,MAAM;MAAErF,YAAY;MAAEC;IAAY,CAAC,GAAGsF,MAAM;IAC5C,MAAM;MAAEC,SAAS;MAAEC;IAAW,CAAC,GAAG1J,mBAAmB,CAAC,IAAI,CAAC0H,SAAS,EAAE8B,MAAM,CAAC;IAC7E,MAAM1D,SAAS,GAAG2D,SAAS,GAAGxF,YAAY,GAAG,CAAC,GAAG,IAAI,CAACyD,SAAS,CAACzB,YAAY,GAAG,CAAC;IAChF,MAAMJ,UAAU,GAAG6D,UAAU,GAAGxF,WAAW,GAAG,CAAC,GAAG,IAAI,CAACwD,SAAS,CAACxB,WAAW,GAAG,CAAC;IAChF,IAAI,CAACwB,SAAS,CAACiC,QAAQ,GAAG9D,UAAU,EAAEC,SAAS,CAAC;EACpD;EACA;IAAS,IAAI,CAACjB,IAAI,YAAA+E,qBAAA7E,CAAA;MAAA,YAAAA,CAAA,IAAyFmE,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAAClB,IAAI,kBAjK+E7J,EAAE,CAAA8J,iBAAA;MAAA3C,IAAA,EAiKJ4D,YAAY;MAAApC,SAAA;MAAA+C,QAAA;MAAAC,YAAA,WAAAC,0BAAA1J,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjKVlC,EAAE,CAAAyC,UAAA,iCAAAoJ,oDAAAC,MAAA;YAAA,OAAA3J,GAAA,CAAAoH,SAAA,GAAAuC,MAAA,CAAAT,MAAA;UAAA,CAiKO,CAAC,uCAAAU,0DAAAD,MAAA;YAAA,OAAZ3J,GAAA,CAAAiJ,cAAA,CAAAU,MAAA,CAAAT,MAA4B,CAAC;UAAA,CAAlB,CAAC;QAAA;QAAA,IAAAnJ,EAAA;UAjKVlC,EAAE,CAAAkD,WAAA,mBAAAf,GAAA,CAAAiC,OAAA,CAAAC,IAAA,KAiKa,QAAQ,MAAAlC,GAAA,CAAA6I,KAAA,IAAA7I,GAAA,CAAA+I,MAAA,CAAd,CAAC;QAAA;MAAA;MAAAtC,MAAA;QAAAsC,MAAA;MAAA;MAAArC,UAAA;MAAAC,QAAA,GAjKV9I,EAAE,CAAA+I,kBAAA,CAiK0U,CACra;QACIiD,OAAO,EAAEhL,cAAc;QACvBiL,UAAU,EAAEA,CAAA,KAAMhM,MAAM,CAAC8K,YAAY,CAAC,CAACE;MAC3C,CAAC,CACJ,GAtK4FjL,EAAE,CAAA+J,mBAAA;MAAAmC,kBAAA,EAAAjI,GAAA;MAAA+F,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAgC,sBAAAjK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlC,EAAE,CAAAoM,eAAA;UAAFpM,EAAE,CAAAwD,UAAA,IAAAU,2CAAA,gCAsK+F,CAAC;UAtKlGlE,EAAE,CAAAwC,cAAA,YAsKgL,CAAC;UAtKnLxC,EAAE,CAAAqM,YAAA,EAsKoM,CAAC;UAtKvMrM,EAAE,CAAA8C,YAAA,CAsK4M,CAAC;QAAA;QAAA,IAAAZ,EAAA;UAtK/MlC,EAAE,CAAA2D,UAAA,UAAAxB,GAAA,CAAA+I,MAAA,KAAA/I,GAAA,CAAA6I,KAAA,IAAA7I,GAAA,CAAAiC,OAAA,CAAAC,IAAA,aAsKY,CAAC;UAtKfrE,EAAE,CAAA0D,SAAA,CAsK6K,CAAC;UAtKhL1D,EAAE,CAAAkD,WAAA,wBAAAf,GAAA,CAAAgJ,SAsK6K,CAAC;QAAA;MAAA;MAAAX,YAAA,GAAi1BzK,IAAI,EAA6FuJ,iBAAiB;MAAAmB,MAAA;MAAAC,eAAA;IAAA,EAA2F;EAAE;AACrzC;AACA;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KAxKqGjH,EAAE,CAAAkH,iBAAA,CAwKX6D,YAAY,EAAc,CAAC;IAC3G5D,IAAI,EAAE9G,SAAS;IACf2I,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,IAAI;MAAEI,QAAQ,EAAE,eAAe;MAAE0B,OAAO,EAAE,CAAC5K,IAAI,EAAEuJ,iBAAiB,CAAC;MAAEoB,eAAe,EAAEpK,uBAAuB,CAACsK,MAAM;MAAE1B,SAAS,EAAE,CAC1I;QACI8C,OAAO,EAAEhL,cAAc;QACvBiL,UAAU,EAAEA,CAAA,KAAMhM,MAAM,CAAC8K,YAAY,CAAC,CAACE;MAC3C,CAAC,CACJ;MAAEqB,IAAI,EAAE;QACL,wBAAwB,EAAE,iDAAiD;QAC3E,CAAC,IAAIxB,cAAc,QAAQ,GAAG,2BAA2B;QACzD,CAAC,IAAID,oBAAoB,QAAQ,GAAG;MACxC,CAAC;MAAEV,QAAQ,EAAE,oRAAoR;MAAEM,MAAM,EAAE,CAAC,uvBAAuvB;IAAE,CAAC;EACljC,CAAC,CAAC,QAAkB;IAAES,MAAM,EAAE,CAAC;MACvB/D,IAAI,EAAE/G;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMmM,iBAAiB,CAAC;EACpBhI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACI,EAAE,GAAG/C,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAAC4K,UAAU,GAAGvM,MAAM,CAACO,UAAU,CAAC;EACxC;EACA,IAAIiM,iBAAiBA,CAACC,MAAM,EAAE;IAC1B,IAAI,CAACA,MAAM,EAAE;MACT;IACJ;IACA;IACA;IACAhL,KAAK,CAAC,CAAC,CAAC,CACHqD,IAAI,CAACpD,kBAAkB,CAAC,IAAI,CAAC6K,UAAU,CAAC,CAAC,CACzC9H,SAAS,CAAC,MAAM;MACjB,IAAI,CAACC,EAAE,CAACgI,aAAa,CAAC,IAAIC,WAAW,CAAC/B,oBAAoB,EAAE;QACxDgC,OAAO,EAAE,IAAI;QACbxB,MAAM,EAAE,IAAI,CAAC1G;MACjB,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC+B,IAAI,YAAAoG,0BAAAlG,CAAA;MAAA,YAAAA,CAAA,IAAyF2F,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAAC9D,IAAI,kBAhN+EzI,EAAE,CAAA0I,iBAAA;MAAAvB,IAAA,EAgNJoF,iBAAiB;MAAA5D,SAAA;MAAAC,MAAA;QAAA6D,iBAAA;MAAA;MAAA5D,UAAA;IAAA,EAA0H;EAAE;AAChP;AACA;EAAA,QAAA5B,SAAA,oBAAAA,SAAA,KAlNqGjH,EAAE,CAAAkH,iBAAA,CAkNXqF,iBAAiB,EAAc,CAAC;IAChHpF,IAAI,EAAEhH,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEwD,iBAAiB,EAAE,CAAC;MAClCtF,IAAI,EAAE/G;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2M,mBAAmB,GAAG,gBAAgB;AAC5C,MAAMC,YAAY,CAAC;EACf;IAAS,IAAI,CAACtG,IAAI,YAAAuG,qBAAArG,CAAA;MAAA,YAAAA,CAAA,IAAyFoG,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACvE,IAAI,kBA/N+EzI,EAAE,CAAA0I,iBAAA;MAAAvB,IAAA,EA+NJ6F,YAAY;MAAArE,SAAA;MAAAE,UAAA;MAAAC,QAAA,GA/NV9I,EAAE,CAAA+I,kBAAA,CA+NqE,CAAC/G,UAAU,CAAChB,cAAc,EAAET,UAAU,CAAC,CAAC;IAAA,EAAiB;EAAE;AACvO;AACA;EAAA,QAAA0G,SAAA,oBAAAA,SAAA,KAjOqGjH,EAAE,CAAAkH,iBAAA,CAiOX8F,YAAY,EAAc,CAAC;IAC3G7F,IAAI,EAAEhH,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,CAAClH,UAAU,CAAChB,cAAc,EAAET,UAAU,CAAC;IACtD,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM2M,aAAa,CAAC;EAChB3I,WAAWA,CAAA,EAAG;IACV,IAAI,CAACI,EAAE,GAAG/C,gBAAgB,CAAC,CAAC;EAChC;EACAuL,QAAQA,CAAA,EAAG;IACP,IAAI,CAACxI,EAAE,CAACgI,aAAa,CAAC,IAAIC,WAAW,CAAC9B,cAAc,EAAE;MAClD+B,OAAO,EAAE,IAAI;MACbxB,MAAM,EAAE,IAAI,CAAC1G;IACjB,CAAC,CAAC,CAAC;EACP;EACA;IAAS,IAAI,CAAC+B,IAAI,YAAA0G,sBAAAxG,CAAA;MAAA,YAAAA,CAAA,IAAyFsG,aAAa;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACzE,IAAI,kBArP+EzI,EAAE,CAAA0I,iBAAA;MAAAvB,IAAA,EAqPJ+F,aAAa;MAAAvE,SAAA;MAAAE,UAAA;IAAA,EAAkE;EAAE;AACpL;AACA;EAAA,QAAA5B,SAAA,oBAAAA,SAAA,KAvPqGjH,EAAE,CAAAkH,iBAAA,CAuPXgG,aAAa,EAAc,CAAC;IAC5G/F,IAAI,EAAEhH,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS8D,mBAAmB,EAAE5D,6BAA6B,EAAE2B,cAAc,EAAE1B,qBAAqB,EAAEyB,oBAAoB,EAAEvB,iBAAiB,EAAEiD,iBAAiB,EAAES,YAAY,EAAEE,aAAa,EAAEnC,YAAY,EAAE1D,qBAAqB,EAAE/C,mBAAmB,EAAE+E,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}