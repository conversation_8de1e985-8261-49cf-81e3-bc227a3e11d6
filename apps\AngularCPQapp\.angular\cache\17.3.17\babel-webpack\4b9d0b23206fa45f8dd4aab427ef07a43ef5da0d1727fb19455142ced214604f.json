{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, signal, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, PLATFORM_ID, Injectable, ViewChild, Directive } from '@angular/core';\nimport { tuiInjectElement, tuiGetActualTarget, tuiIsElement } from '@taiga-ui/cdk/utils/dom';\nimport { isPlatformBrowser } from '@angular/common';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiZonefreeScheduler } from '@taiga-ui/cdk/observables';\nimport { tuiCreateToken, tuiArrayShallowEquals, tuiPx } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { Subject, debounce, timer, filter, map, BehaviorSubject, Subscription, combineLatest, distinctUntilChanged, startWith, debounceTime } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { shouldCall } from '@taiga-ui/event-plugins';\nconst _c0 = [\"*\"];\nconst _c1 = [\"wrapper\"];\nconst tuiTilesSwap = (order, currentIndex, newIndex) => {\n  if (!order.has(currentIndex) || !order.has(newIndex)) {\n    return order;\n  }\n  const dragged = order.get(currentIndex) ?? currentIndex;\n  const placement = order.get(newIndex) ?? newIndex;\n  const newOrder = new Map(order);\n  newOrder.set(currentIndex, placement);\n  newOrder.set(newIndex, dragged);\n  return newOrder;\n};\nconst tuiTilesShift = (order, currentIndex, newIndex) => {\n  if (!order.has(currentIndex) || !order.has(newIndex)) {\n    return order;\n  }\n  const dragged = order.get(currentIndex) ?? currentIndex;\n  const placement = order.get(newIndex) ?? newIndex;\n  const newOrder = new Map(order);\n  const flipped = new Map(Array.from(order).map(([a, b]) => [b, a]));\n  if ((placement - dragged) / Math.abs(placement - dragged) > 0) {\n    for (let i = placement; i > dragged; i--) {\n      newOrder.set(flipped.get(i) ?? i, i - 1);\n    }\n  } else {\n    for (let i = placement; i < dragged; i++) {\n      newOrder.set(flipped.get(i) ?? i, i + 1);\n    }\n  }\n  newOrder.set(currentIndex, placement);\n  return newOrder;\n};\nconst TUI_TILES_REORDER = tuiCreateToken(tuiTilesSwap);\nclass TuiTilesComponent {\n  constructor() {\n    this.el$ = new Subject();\n    this.handler = inject(TUI_TILES_REORDER);\n    this.debounce = 0;\n    this.orderChange = this.el$.pipe(debounce(() => timer(this.debounce)), filter(this.filter.bind(this)), map(element => this.reorder(element)));\n    this.element = signal(null);\n    this.el = tuiInjectElement();\n    this.order$ = new BehaviorSubject(new Map());\n  }\n  set order(map) {\n    this.order$.next(map);\n  }\n  get order() {\n    return this.order$.value;\n  }\n  rearrange(element) {\n    this.el$.next(element);\n  }\n  filter(element) {\n    return !!this.element() && !!element && this.element() !== element;\n  }\n  reorder(element) {\n    const elements = Array.from(this.el.children);\n    const currentIndex = elements.indexOf(this.element() || element);\n    const newIndex = elements.indexOf(element);\n    const order = this.order.size ? new Map(this.order) : new Map(elements.map((_, index) => [index, index]));\n    this.order$.next(this.handler(order, currentIndex, newIndex));\n    return this.order$.value;\n  }\n  static {\n    this.ɵfac = function TuiTilesComponent_Factory(t) {\n      return new (t || TuiTilesComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTilesComponent,\n      selectors: [[\"tui-tiles\"]],\n      hostBindings: function TuiTilesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"pointerleave.zoneless\", function TuiTilesComponent_pointerleave_zoneless_HostBindingHandler() {\n            return ctx.rearrange();\n          });\n        }\n      },\n      inputs: {\n        debounce: \"debounce\",\n        order: \"order\"\n      },\n      outputs: {\n        orderChange: \"orderChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([ResizeObserverService, MutationObserverService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          childList: true\n        }\n      }]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function TuiTilesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\"tui-tiles{position:relative;z-index:0;display:grid;grid-auto-flow:dense;justify-items:stretch}tui-tiles._dragged tui-tile>.t-wrapper{pointer-events:none}tui-tiles._dragged tui-tile:not(._dragged)>.t-wrapper,tui-tiles:not(._dragged) tui-tile._dragged>.t-wrapper{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;transition-delay:1ms}tui-tile>.t-wrapper{position:absolute;z-index:0;border-radius:inherit}tui-tile._dragged>.t-wrapper,tui-tile:has(tui-tile._dragged)>.t-wrapper{z-index:1}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTilesComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-tiles',\n      template: '<ng-content />',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [ResizeObserverService, MutationObserverService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          childList: true\n        }\n      }],\n      host: {\n        '(pointerleave.zoneless)': 'rearrange()'\n      },\n      styles: [\"tui-tiles{position:relative;z-index:0;display:grid;grid-auto-flow:dense;justify-items:stretch}tui-tiles._dragged tui-tile>.t-wrapper{pointer-events:none}tui-tiles._dragged tui-tile:not(._dragged)>.t-wrapper,tui-tiles:not(._dragged) tui-tile._dragged>.t-wrapper{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;transition-delay:1ms}tui-tile>.t-wrapper{position:absolute;z-index:0;border-radius:inherit}tui-tile._dragged>.t-wrapper,tui-tile:has(tui-tile._dragged)>.t-wrapper{z-index:1}\\n\"]\n    }]\n  }], null, {\n    debounce: [{\n      type: Input\n    }],\n    orderChange: [{\n      type: Output\n    }],\n    order: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiTileService {\n  constructor() {\n    this.isBrowser = isPlatformBrowser(inject(PLATFORM_ID));\n    this.el = tuiInjectElement();\n    this.tiles = inject(TuiTilesComponent);\n    this.sub = new Subscription();\n    this.offset$ = new BehaviorSubject([NaN, NaN]);\n    this.position$ = combineLatest([this.offset$.pipe(distinctUntilChanged(tuiArrayShallowEquals)), inject(ResizeObserverService).pipe(startWith(null)), inject(MutationObserverService).pipe(startWith(null)), this.tiles.order$.pipe(debounceTime(0, tuiZonefreeScheduler()))]).pipe(map(([offset]) => offset));\n  }\n  init(element) {\n    if (this.isBrowser) {\n      this.sub.add(this.position$.subscribe(offset => {\n        this.setPosition(element, offset);\n        this.setRect(element, offset);\n      }));\n    } else {\n      this.el.style.setProperty('position', 'relative');\n    }\n  }\n  setOffset(offset) {\n    this.offset$.next(offset);\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n  }\n  getRect([left, top]) {\n    const elTop = Number.isNaN(top) ? this.el.offsetTop : top;\n    const elLeft = Number.isNaN(left) ? this.el.offsetLeft : left;\n    const rect = {\n      top: elTop,\n      left: elLeft,\n      width: this.el.clientWidth,\n      height: this.el.clientHeight,\n      right: NaN,\n      bottom: NaN,\n      y: elTop,\n      x: elLeft\n    };\n    return {\n      ...rect,\n      toJSON: () => JSON.stringify(rect)\n    };\n  }\n  setRect({\n    style\n  }, offset) {\n    const {\n      top,\n      left,\n      width,\n      height\n    } = this.getRect(offset);\n    style.top = tuiPx(top);\n    style.left = tuiPx(left);\n    style.width = tuiPx(width);\n    style.height = tuiPx(height);\n  }\n  setPosition(element, [left]) {\n    if (!Number.isNaN(left)) {\n      element.style.setProperty('position', 'fixed');\n      element.style.setProperty('transition', 'none');\n      return;\n    }\n    const {\n      style\n    } = element;\n    const rect = element.getBoundingClientRect();\n    const host = this.el.getBoundingClientRect();\n    style.removeProperty('position');\n    style.removeProperty('transition');\n    style.removeProperty('top');\n    style.removeProperty('left');\n    style.top = tuiPx(rect.top - host.top + this.el.offsetTop);\n    style.left = tuiPx(rect.left - host.left + this.el.offsetLeft);\n  }\n  static {\n    this.ɵfac = function TuiTileService_Factory(t) {\n      return new (t || TuiTileService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiTileService,\n      factory: TuiTileService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTileService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass TuiTile {\n  constructor() {\n    this.service = inject(TuiTileService);\n    this.tiles = inject(TuiTilesComponent);\n    this.dragged = signal(false);\n    this.width = 1;\n    this.height = 1;\n    this.element = tuiInjectElement();\n  }\n  onDrag(offset) {\n    const dragged = !Number.isNaN(offset[0]);\n    this.dragged.set(this.dragged() || dragged);\n    this.tiles.element.set(dragged ? this.element : null);\n    this.service.setOffset(offset);\n    if (dragged) {\n      this.tiles.el.classList.add('_dragged');\n    } else {\n      this.tiles.el.classList.remove('_dragged');\n    }\n  }\n  ngAfterViewInit() {\n    if (this.wrapper) {\n      this.service.init(this.wrapper.nativeElement);\n    }\n  }\n  ngOnDestroy() {\n    if (this.tiles.element() === this.element) {\n      this.tiles.element.set(null);\n    }\n  }\n  get column() {\n    return `span var(--tui-width, ${this.width})`;\n  }\n  get row() {\n    return `span var(--tui-height, ${this.height})`;\n  }\n  onEnter() {\n    this.tiles.rearrange(this.element);\n  }\n  onTransitionEnd() {\n    this.dragged.set(false);\n  }\n  static {\n    this.ɵfac = function TuiTile_Factory(t) {\n      return new (t || TuiTile)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTile,\n      selectors: [[\"tui-tile\"]],\n      viewQuery: function TuiTile_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.wrapper = _t.first);\n        }\n      },\n      hostVars: 6,\n      hostBindings: function TuiTile_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"pointerenter\", function TuiTile_pointerenter_HostBindingHandler() {\n            return ctx.onEnter();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"grid-column\", ctx.column)(\"grid-row\", ctx.row);\n          i0.ɵɵclassProp(\"_dragged\", ctx.dragged());\n        }\n      },\n      inputs: {\n        width: \"width\",\n        height: \"height\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiTileService]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 0,\n      consts: [[\"wrapper\", \"\"], [1, \"t-wrapper\", 3, \"transitionend.self\"]],\n      template: function TuiTile_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 1, 0);\n          i0.ɵɵlistener(\"transitionend.self\", function TuiTile_Template_div_transitionend_self_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTransitionEnd());\n          });\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTile, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-tile',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [TuiTileService],\n      host: {\n        '[class._dragged]': 'dragged()',\n        '[style.gridColumn]': 'column',\n        '[style.gridRow]': 'row',\n        '(pointerenter)': 'onEnter()'\n      },\n      template: \"<div\\n    #wrapper\\n    class=\\\"t-wrapper\\\"\\n    (transitionend.self)=\\\"onTransitionEnd()\\\"\\n>\\n    <ng-content />\\n</div>\\n\"\n    }]\n  }], null, {\n    wrapper: [{\n      type: ViewChild,\n      args: ['wrapper']\n    }],\n    width: [{\n      type: Input\n    }],\n    height: [{\n      type: Input\n    }]\n  });\n})();\nfunction isInteracting(x = NaN) {\n  return !Number.isNaN(x) || !Number.isNaN(this['x']);\n}\nfunction isDragging() {\n  return !Number.isNaN(this['x']);\n}\nclass TuiTileHandle {\n  constructor() {\n    this.tile = inject(TuiTile);\n    this.x = NaN;\n    this.y = NaN;\n  }\n  onPointer(x = NaN, y = NaN) {\n    const {\n      left,\n      top\n    } = this.tile.element.getBoundingClientRect();\n    this.x = x - left;\n    this.y = y - top;\n    this.tile.onDrag([NaN, NaN]);\n  }\n  onMove(x, y) {\n    this.tile.onDrag([x - this.x, y - this.y]);\n  }\n  onStart(event) {\n    const target = tuiGetActualTarget(event);\n    const {\n      x,\n      y,\n      pointerId\n    } = event;\n    if (tuiIsElement(target)) {\n      target.releasePointerCapture(pointerId);\n    }\n    this.onPointer(x, y);\n  }\n  static {\n    this.ɵfac = function TuiTileHandle_Factory(t) {\n      return new (t || TuiTileHandle)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTileHandle,\n      selectors: [[\"\", \"tuiTileHandle\", \"\"]],\n      hostVars: 4,\n      hostBindings: function TuiTileHandle_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"pointerdown.zoneless.prevent\", function TuiTileHandle_pointerdown_zoneless_prevent_HostBindingHandler($event) {\n            return ctx.onStart($event);\n          })(\"pointerup.zoneless\", function TuiTileHandle_pointerup_zoneless_HostBindingHandler() {\n            return ctx.onPointer();\n          }, false, i0.ɵɵresolveDocument)(\"pointermove.zoneless\", function TuiTileHandle_pointermove_zoneless_HostBindingHandler($event) {\n            return ctx.onMove($event.x, $event.y);\n          }, false, i0.ɵɵresolveDocument);\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"touch-action\", \"none\")(\"user-select\", \"none\");\n        }\n      },\n      standalone: true\n    });\n  }\n}\n__decorate([shouldCall(isInteracting)], TuiTileHandle.prototype, \"onPointer\", null);\n__decorate([shouldCall(isDragging)], TuiTileHandle.prototype, \"onMove\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTileHandle, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiTileHandle]',\n      host: {\n        '[style.touchAction]': '\"none\"',\n        '[style.userSelect]': '\"none\"',\n        '(pointerdown.zoneless.prevent)': 'onStart($event)',\n        '(document:pointerup.zoneless)': 'onPointer()',\n        '(document:pointermove.zoneless)': 'onMove($event.x, $event.y)'\n      }\n    }]\n  }], null, {\n    onPointer: [],\n    onMove: []\n  });\n})();\nconst TuiTiles = [TuiTilesComponent, TuiTile, TuiTileHandle];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_TILES_REORDER, TuiTile, TuiTileHandle, TuiTileService, TuiTiles, TuiTilesComponent, tuiTilesShift, tuiTilesSwap };", "map": {"version": 3, "names": ["i0", "inject", "signal", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "Output", "PLATFORM_ID", "Injectable", "ViewChild", "Directive", "tuiInjectElement", "tuiGetActualTarget", "tuiIsElement", "isPlatformBrowser", "MutationObserverService", "WA_MUTATION_OBSERVER_INIT", "ResizeObserverService", "tuiZonefreeScheduler", "tuiCreateToken", "tuiArrayShallowEquals", "tuiPx", "Subject", "debounce", "timer", "filter", "map", "BehaviorSubject", "Subscription", "combineLatest", "distinctUntilChanged", "startWith", "debounceTime", "__decorate", "shouldCall", "_c0", "_c1", "tuiTilesSwap", "order", "currentIndex", "newIndex", "has", "dragged", "get", "placement", "newOrder", "Map", "set", "tuiTilesShift", "flipped", "Array", "from", "a", "b", "Math", "abs", "i", "TUI_TILES_REORDER", "TuiTilesComponent", "constructor", "el$", "handler", "orderChange", "pipe", "bind", "element", "reorder", "el", "order$", "next", "value", "rearrange", "elements", "children", "indexOf", "size", "_", "index", "ɵfac", "TuiTilesComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostBindings", "TuiTilesComponent_HostBindings", "rf", "ctx", "ɵɵlistener", "TuiTilesComponent_pointerleave_zoneless_HostBindingHandler", "inputs", "outputs", "standalone", "features", "ɵɵProvidersFeature", "provide", "useValue", "childList", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "template", "TuiTilesComponent_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "providers", "host", "TuiTileService", "<PERSON><PERSON><PERSON><PERSON>", "tiles", "sub", "offset$", "NaN", "position$", "offset", "init", "add", "subscribe", "setPosition", "setRect", "style", "setProperty", "setOffset", "ngOnDestroy", "unsubscribe", "getRect", "left", "top", "elTop", "Number", "isNaN", "offsetTop", "elLeft", "offsetLeft", "rect", "width", "clientWidth", "height", "clientHeight", "right", "bottom", "y", "x", "toJSON", "JSON", "stringify", "getBoundingClientRect", "removeProperty", "TuiTileService_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "TuiTile", "service", "onDrag", "classList", "remove", "ngAfterViewInit", "wrapper", "nativeElement", "column", "row", "onEnter", "onTransitionEnd", "TuiTile_Factory", "viewQuery", "TuiTile_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostVars", "TuiTile_HostBindings", "TuiTile_pointerenter_HostBindingHandler", "ɵɵstyleProp", "ɵɵclassProp", "consts", "TuiTile_Template", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "TuiTile_Template_div_transitionend_self_0_listener", "ɵɵrestoreView", "ɵɵresetView", "ɵɵelementEnd", "isInteracting", "isDragging", "TuiTileHandle", "tile", "onPointer", "onMove", "onStart", "event", "target", "pointerId", "releasePointerCapture", "TuiTileHandle_Factory", "ɵdir", "ɵɵdefineDirective", "TuiTileHandle_HostBindings", "TuiTileHandle_pointerdown_zoneless_prevent_HostBindingHandler", "$event", "TuiTileHandle_pointerup_zoneless_HostBindingHandler", "ɵɵresolveDocument", "TuiTileHandle_pointermove_zoneless_HostBindingHandler", "prototype", "TuiTiles"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-tiles.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, signal, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, PLATFORM_ID, Injectable, ViewChild, Directive } from '@angular/core';\nimport { tuiInjectElement, tuiGetActualTarget, tuiIsElement } from '@taiga-ui/cdk/utils/dom';\nimport { isPlatformBrowser } from '@angular/common';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiZonefreeScheduler } from '@taiga-ui/cdk/observables';\nimport { tuiCreateToken, tuiArrayShallowEquals, tuiPx } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { Subject, debounce, timer, filter, map, BehaviorSubject, Subscription, combineLatest, distinctUntilChanged, startWith, debounceTime } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { shouldCall } from '@taiga-ui/event-plugins';\n\nconst tuiTilesSwap = (order, currentIndex, newIndex) => {\n    if (!order.has(currentIndex) || !order.has(newIndex)) {\n        return order;\n    }\n    const dragged = order.get(currentIndex) ?? currentIndex;\n    const placement = order.get(newIndex) ?? newIndex;\n    const newOrder = new Map(order);\n    newOrder.set(currentIndex, placement);\n    newOrder.set(newIndex, dragged);\n    return newOrder;\n};\nconst tuiTilesShift = (order, currentIndex, newIndex) => {\n    if (!order.has(currentIndex) || !order.has(newIndex)) {\n        return order;\n    }\n    const dragged = order.get(currentIndex) ?? currentIndex;\n    const placement = order.get(newIndex) ?? newIndex;\n    const newOrder = new Map(order);\n    const flipped = new Map(Array.from(order).map(([a, b]) => [b, a]));\n    if ((placement - dragged) / Math.abs(placement - dragged) > 0) {\n        for (let i = placement; i > dragged; i--) {\n            newOrder.set(flipped.get(i) ?? i, i - 1);\n        }\n    }\n    else {\n        for (let i = placement; i < dragged; i++) {\n            newOrder.set(flipped.get(i) ?? i, i + 1);\n        }\n    }\n    newOrder.set(currentIndex, placement);\n    return newOrder;\n};\nconst TUI_TILES_REORDER = tuiCreateToken(tuiTilesSwap);\n\nclass TuiTilesComponent {\n    constructor() {\n        this.el$ = new Subject();\n        this.handler = inject(TUI_TILES_REORDER);\n        this.debounce = 0;\n        this.orderChange = this.el$.pipe(debounce(() => timer(this.debounce)), filter(this.filter.bind(this)), map((element) => this.reorder(element)));\n        this.element = signal(null);\n        this.el = tuiInjectElement();\n        this.order$ = new BehaviorSubject(new Map());\n    }\n    set order(map) {\n        this.order$.next(map);\n    }\n    get order() {\n        return this.order$.value;\n    }\n    rearrange(element) {\n        this.el$.next(element);\n    }\n    filter(element) {\n        return !!this.element() && !!element && this.element() !== element;\n    }\n    reorder(element) {\n        const elements = Array.from(this.el.children);\n        const currentIndex = elements.indexOf(this.element() || element);\n        const newIndex = elements.indexOf(element);\n        const order = this.order.size\n            ? new Map(this.order)\n            : new Map(elements.map((_, index) => [index, index]));\n        this.order$.next(this.handler(order, currentIndex, newIndex));\n        return this.order$.value;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTilesComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTilesComponent, isStandalone: true, selector: \"tui-tiles\", inputs: { debounce: \"debounce\", order: \"order\" }, outputs: { orderChange: \"orderChange\" }, host: { listeners: { \"pointerleave.zoneless\": \"rearrange()\" } }, providers: [\n            ResizeObserverService,\n            MutationObserverService,\n            {\n                provide: WA_MUTATION_OBSERVER_INIT,\n                useValue: { childList: true },\n            },\n        ], ngImport: i0, template: '<ng-content />', isInline: true, styles: [\"tui-tiles{position:relative;z-index:0;display:grid;grid-auto-flow:dense;justify-items:stretch}tui-tiles._dragged tui-tile>.t-wrapper{pointer-events:none}tui-tiles._dragged tui-tile:not(._dragged)>.t-wrapper,tui-tiles:not(._dragged) tui-tile._dragged>.t-wrapper{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;transition-delay:1ms}tui-tile>.t-wrapper{position:absolute;z-index:0;border-radius:inherit}tui-tile._dragged>.t-wrapper,tui-tile:has(tui-tile._dragged)>.t-wrapper{z-index:1}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTilesComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-tiles', template: '<ng-content />', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        ResizeObserverService,\n                        MutationObserverService,\n                        {\n                            provide: WA_MUTATION_OBSERVER_INIT,\n                            useValue: { childList: true },\n                        },\n                    ], host: {\n                        '(pointerleave.zoneless)': 'rearrange()',\n                    }, styles: [\"tui-tiles{position:relative;z-index:0;display:grid;grid-auto-flow:dense;justify-items:stretch}tui-tiles._dragged tui-tile>.t-wrapper{pointer-events:none}tui-tiles._dragged tui-tile:not(._dragged)>.t-wrapper,tui-tiles:not(._dragged) tui-tile._dragged>.t-wrapper{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;transition-delay:1ms}tui-tile>.t-wrapper{position:absolute;z-index:0;border-radius:inherit}tui-tile._dragged>.t-wrapper,tui-tile:has(tui-tile._dragged)>.t-wrapper{z-index:1}\\n\"] }]\n        }], propDecorators: { debounce: [{\n                type: Input\n            }], orderChange: [{\n                type: Output\n            }], order: [{\n                type: Input\n            }] } });\n\nclass TuiTileService {\n    constructor() {\n        this.isBrowser = isPlatformBrowser(inject(PLATFORM_ID));\n        this.el = tuiInjectElement();\n        this.tiles = inject(TuiTilesComponent);\n        this.sub = new Subscription();\n        this.offset$ = new BehaviorSubject([NaN, NaN]);\n        this.position$ = combineLatest([\n            this.offset$.pipe(distinctUntilChanged(tuiArrayShallowEquals)),\n            inject(ResizeObserverService).pipe(startWith(null)),\n            inject(MutationObserverService).pipe(startWith(null)),\n            this.tiles.order$.pipe(debounceTime(0, tuiZonefreeScheduler())),\n        ]).pipe(map(([offset]) => offset));\n    }\n    init(element) {\n        if (this.isBrowser) {\n            this.sub.add(this.position$.subscribe((offset) => {\n                this.setPosition(element, offset);\n                this.setRect(element, offset);\n            }));\n        }\n        else {\n            this.el.style.setProperty('position', 'relative');\n        }\n    }\n    setOffset(offset) {\n        this.offset$.next(offset);\n    }\n    ngOnDestroy() {\n        this.sub.unsubscribe();\n    }\n    getRect([left, top]) {\n        const elTop = Number.isNaN(top) ? this.el.offsetTop : top;\n        const elLeft = Number.isNaN(left) ? this.el.offsetLeft : left;\n        const rect = {\n            top: elTop,\n            left: elLeft,\n            width: this.el.clientWidth,\n            height: this.el.clientHeight,\n            right: NaN,\n            bottom: NaN,\n            y: elTop,\n            x: elLeft,\n        };\n        return {\n            ...rect,\n            toJSON: () => JSON.stringify(rect),\n        };\n    }\n    setRect({ style }, offset) {\n        const { top, left, width, height } = this.getRect(offset);\n        style.top = tuiPx(top);\n        style.left = tuiPx(left);\n        style.width = tuiPx(width);\n        style.height = tuiPx(height);\n    }\n    setPosition(element, [left]) {\n        if (!Number.isNaN(left)) {\n            element.style.setProperty('position', 'fixed');\n            element.style.setProperty('transition', 'none');\n            return;\n        }\n        const { style } = element;\n        const rect = element.getBoundingClientRect();\n        const host = this.el.getBoundingClientRect();\n        style.removeProperty('position');\n        style.removeProperty('transition');\n        style.removeProperty('top');\n        style.removeProperty('left');\n        style.top = tuiPx(rect.top - host.top + this.el.offsetTop);\n        style.left = tuiPx(rect.left - host.left + this.el.offsetLeft);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTileService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTileService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTileService, decorators: [{\n            type: Injectable\n        }] });\n\nclass TuiTile {\n    constructor() {\n        this.service = inject(TuiTileService);\n        this.tiles = inject(TuiTilesComponent);\n        this.dragged = signal(false);\n        this.width = 1;\n        this.height = 1;\n        this.element = tuiInjectElement();\n    }\n    onDrag(offset) {\n        const dragged = !Number.isNaN(offset[0]);\n        this.dragged.set(this.dragged() || dragged);\n        this.tiles.element.set(dragged ? this.element : null);\n        this.service.setOffset(offset);\n        if (dragged) {\n            this.tiles.el.classList.add('_dragged');\n        }\n        else {\n            this.tiles.el.classList.remove('_dragged');\n        }\n    }\n    ngAfterViewInit() {\n        if (this.wrapper) {\n            this.service.init(this.wrapper.nativeElement);\n        }\n    }\n    ngOnDestroy() {\n        if (this.tiles.element() === this.element) {\n            this.tiles.element.set(null);\n        }\n    }\n    get column() {\n        return `span var(--tui-width, ${this.width})`;\n    }\n    get row() {\n        return `span var(--tui-height, ${this.height})`;\n    }\n    onEnter() {\n        this.tiles.rearrange(this.element);\n    }\n    onTransitionEnd() {\n        this.dragged.set(false);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTile, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTile, isStandalone: true, selector: \"tui-tile\", inputs: { width: \"width\", height: \"height\" }, host: { listeners: { \"pointerenter\": \"onEnter()\" }, properties: { \"class._dragged\": \"dragged()\", \"style.gridColumn\": \"column\", \"style.gridRow\": \"row\" } }, providers: [TuiTileService], viewQueries: [{ propertyName: \"wrapper\", first: true, predicate: [\"wrapper\"], descendants: true }], ngImport: i0, template: \"<div\\n    #wrapper\\n    class=\\\"t-wrapper\\\"\\n    (transitionend.self)=\\\"onTransitionEnd()\\\"\\n>\\n    <ng-content />\\n</div>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTile, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-tile', changeDetection: ChangeDetectionStrategy.OnPush, providers: [TuiTileService], host: {\n                        '[class._dragged]': 'dragged()',\n                        '[style.gridColumn]': 'column',\n                        '[style.gridRow]': 'row',\n                        '(pointerenter)': 'onEnter()',\n                    }, template: \"<div\\n    #wrapper\\n    class=\\\"t-wrapper\\\"\\n    (transitionend.self)=\\\"onTransitionEnd()\\\"\\n>\\n    <ng-content />\\n</div>\\n\" }]\n        }], propDecorators: { wrapper: [{\n                type: ViewChild,\n                args: ['wrapper']\n            }], width: [{\n                type: Input\n            }], height: [{\n                type: Input\n            }] } });\n\nfunction isInteracting(x = NaN) {\n    return !Number.isNaN(x) || !Number.isNaN(this['x']);\n}\nfunction isDragging() {\n    return !Number.isNaN(this['x']);\n}\nclass TuiTileHandle {\n    constructor() {\n        this.tile = inject(TuiTile);\n        this.x = NaN;\n        this.y = NaN;\n    }\n    onPointer(x = NaN, y = NaN) {\n        const { left, top } = this.tile.element.getBoundingClientRect();\n        this.x = x - left;\n        this.y = y - top;\n        this.tile.onDrag([NaN, NaN]);\n    }\n    onMove(x, y) {\n        this.tile.onDrag([x - this.x, y - this.y]);\n    }\n    onStart(event) {\n        const target = tuiGetActualTarget(event);\n        const { x, y, pointerId } = event;\n        if (tuiIsElement(target)) {\n            target.releasePointerCapture(pointerId);\n        }\n        this.onPointer(x, y);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTileHandle, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTileHandle, isStandalone: true, selector: \"[tuiTileHandle]\", host: { listeners: { \"pointerdown.zoneless.prevent\": \"onStart($event)\", \"document:pointerup.zoneless\": \"onPointer()\", \"document:pointermove.zoneless\": \"onMove($event.x, $event.y)\" }, properties: { \"style.touchAction\": \"\\\"none\\\"\", \"style.userSelect\": \"\\\"none\\\"\" } }, ngImport: i0 }); }\n}\n__decorate([\n    shouldCall(isInteracting)\n], TuiTileHandle.prototype, \"onPointer\", null);\n__decorate([\n    shouldCall(isDragging)\n], TuiTileHandle.prototype, \"onMove\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTileHandle, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiTileHandle]',\n                    host: {\n                        '[style.touchAction]': '\"none\"',\n                        '[style.userSelect]': '\"none\"',\n                        '(pointerdown.zoneless.prevent)': 'onStart($event)',\n                        '(document:pointerup.zoneless)': 'onPointer()',\n                        '(document:pointermove.zoneless)': 'onMove($event.x, $event.y)',\n                    },\n                }]\n        }], propDecorators: { onPointer: [], onMove: [] } });\n\nconst TuiTiles = [TuiTilesComponent, TuiTile, TuiTileHandle];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_TILES_REORDER, TuiTile, TuiTileHandle, TuiTileService, TuiTiles, TuiTilesComponent, tuiTilesShift, tuiTilesSwap };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,eAAe;AACnK,SAASC,gBAAgB,EAAEC,kBAAkB,EAAEC,YAAY,QAAQ,yBAAyB;AAC5F,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,uBAAuB,EAAEC,yBAAyB,QAAQ,gCAAgC;AACnG,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,cAAc,EAAEC,qBAAqB,EAAEC,KAAK,QAAQ,mCAAmC;AAChG,SAASC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,GAAG,EAAEC,eAAe,EAAEC,YAAY,EAAEC,aAAa,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,YAAY,QAAQ,MAAM;AACzJ,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,QAAQ,yBAAyB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAErD,MAAMC,YAAY,GAAGA,CAACC,KAAK,EAAEC,YAAY,EAAEC,QAAQ,KAAK;EACpD,IAAI,CAACF,KAAK,CAACG,GAAG,CAACF,YAAY,CAAC,IAAI,CAACD,KAAK,CAACG,GAAG,CAACD,QAAQ,CAAC,EAAE;IAClD,OAAOF,KAAK;EAChB;EACA,MAAMI,OAAO,GAAGJ,KAAK,CAACK,GAAG,CAACJ,YAAY,CAAC,IAAIA,YAAY;EACvD,MAAMK,SAAS,GAAGN,KAAK,CAACK,GAAG,CAACH,QAAQ,CAAC,IAAIA,QAAQ;EACjD,MAAMK,QAAQ,GAAG,IAAIC,GAAG,CAACR,KAAK,CAAC;EAC/BO,QAAQ,CAACE,GAAG,CAACR,YAAY,EAAEK,SAAS,CAAC;EACrCC,QAAQ,CAACE,GAAG,CAACP,QAAQ,EAAEE,OAAO,CAAC;EAC/B,OAAOG,QAAQ;AACnB,CAAC;AACD,MAAMG,aAAa,GAAGA,CAACV,KAAK,EAAEC,YAAY,EAAEC,QAAQ,KAAK;EACrD,IAAI,CAACF,KAAK,CAACG,GAAG,CAACF,YAAY,CAAC,IAAI,CAACD,KAAK,CAACG,GAAG,CAACD,QAAQ,CAAC,EAAE;IAClD,OAAOF,KAAK;EAChB;EACA,MAAMI,OAAO,GAAGJ,KAAK,CAACK,GAAG,CAACJ,YAAY,CAAC,IAAIA,YAAY;EACvD,MAAMK,SAAS,GAAGN,KAAK,CAACK,GAAG,CAACH,QAAQ,CAAC,IAAIA,QAAQ;EACjD,MAAMK,QAAQ,GAAG,IAAIC,GAAG,CAACR,KAAK,CAAC;EAC/B,MAAMW,OAAO,GAAG,IAAIH,GAAG,CAACI,KAAK,CAACC,IAAI,CAACb,KAAK,CAAC,CAACZ,GAAG,CAAC,CAAC,CAAC0B,CAAC,EAAEC,CAAC,CAAC,KAAK,CAACA,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC;EAClE,IAAI,CAACR,SAAS,GAAGF,OAAO,IAAIY,IAAI,CAACC,GAAG,CAACX,SAAS,GAAGF,OAAO,CAAC,GAAG,CAAC,EAAE;IAC3D,KAAK,IAAIc,CAAC,GAAGZ,SAAS,EAAEY,CAAC,GAAGd,OAAO,EAAEc,CAAC,EAAE,EAAE;MACtCX,QAAQ,CAACE,GAAG,CAACE,OAAO,CAACN,GAAG,CAACa,CAAC,CAAC,IAAIA,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;IAC5C;EACJ,CAAC,MACI;IACD,KAAK,IAAIA,CAAC,GAAGZ,SAAS,EAAEY,CAAC,GAAGd,OAAO,EAAEc,CAAC,EAAE,EAAE;MACtCX,QAAQ,CAACE,GAAG,CAACE,OAAO,CAACN,GAAG,CAACa,CAAC,CAAC,IAAIA,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;IAC5C;EACJ;EACAX,QAAQ,CAACE,GAAG,CAACR,YAAY,EAAEK,SAAS,CAAC;EACrC,OAAOC,QAAQ;AACnB,CAAC;AACD,MAAMY,iBAAiB,GAAGtC,cAAc,CAACkB,YAAY,CAAC;AAEtD,MAAMqB,iBAAiB,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,GAAG,GAAG,IAAItC,OAAO,CAAC,CAAC;IACxB,IAAI,CAACuC,OAAO,GAAG7D,MAAM,CAACyD,iBAAiB,CAAC;IACxC,IAAI,CAAClC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACuC,WAAW,GAAG,IAAI,CAACF,GAAG,CAACG,IAAI,CAACxC,QAAQ,CAAC,MAAMC,KAAK,CAAC,IAAI,CAACD,QAAQ,CAAC,CAAC,EAAEE,MAAM,CAAC,IAAI,CAACA,MAAM,CAACuC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEtC,GAAG,CAAEuC,OAAO,IAAK,IAAI,CAACC,OAAO,CAACD,OAAO,CAAC,CAAC,CAAC;IAC/I,IAAI,CAACA,OAAO,GAAGhE,MAAM,CAAC,IAAI,CAAC;IAC3B,IAAI,CAACkE,EAAE,GAAGxD,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACyD,MAAM,GAAG,IAAIzC,eAAe,CAAC,IAAImB,GAAG,CAAC,CAAC,CAAC;EAChD;EACA,IAAIR,KAAKA,CAACZ,GAAG,EAAE;IACX,IAAI,CAAC0C,MAAM,CAACC,IAAI,CAAC3C,GAAG,CAAC;EACzB;EACA,IAAIY,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC8B,MAAM,CAACE,KAAK;EAC5B;EACAC,SAASA,CAACN,OAAO,EAAE;IACf,IAAI,CAACL,GAAG,CAACS,IAAI,CAACJ,OAAO,CAAC;EAC1B;EACAxC,MAAMA,CAACwC,OAAO,EAAE;IACZ,OAAO,CAAC,CAAC,IAAI,CAACA,OAAO,CAAC,CAAC,IAAI,CAAC,CAACA,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC,CAAC,KAAKA,OAAO;EACtE;EACAC,OAAOA,CAACD,OAAO,EAAE;IACb,MAAMO,QAAQ,GAAGtB,KAAK,CAACC,IAAI,CAAC,IAAI,CAACgB,EAAE,CAACM,QAAQ,CAAC;IAC7C,MAAMlC,YAAY,GAAGiC,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACT,OAAO,CAAC,CAAC,IAAIA,OAAO,CAAC;IAChE,MAAMzB,QAAQ,GAAGgC,QAAQ,CAACE,OAAO,CAACT,OAAO,CAAC;IAC1C,MAAM3B,KAAK,GAAG,IAAI,CAACA,KAAK,CAACqC,IAAI,GACvB,IAAI7B,GAAG,CAAC,IAAI,CAACR,KAAK,CAAC,GACnB,IAAIQ,GAAG,CAAC0B,QAAQ,CAAC9C,GAAG,CAAC,CAACkD,CAAC,EAAEC,KAAK,KAAK,CAACA,KAAK,EAAEA,KAAK,CAAC,CAAC,CAAC;IACzD,IAAI,CAACT,MAAM,CAACC,IAAI,CAAC,IAAI,CAACR,OAAO,CAACvB,KAAK,EAAEC,YAAY,EAAEC,QAAQ,CAAC,CAAC;IAC7D,OAAO,IAAI,CAAC4B,MAAM,CAACE,KAAK;EAC5B;EACA;IAAS,IAAI,CAACQ,IAAI,YAAAC,0BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFtB,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAACuB,IAAI,kBAD+ElF,EAAE,CAAAmF,iBAAA;MAAAC,IAAA,EACJzB,iBAAiB;MAAA0B,SAAA;MAAAC,YAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADfxF,EAAE,CAAA0F,UAAA,mCAAAC,2DAAA;YAAA,OACJF,GAAA,CAAAjB,SAAA,CAAU,CAAC;UAAA,CAAK,CAAC;QAAA;MAAA;MAAAoB,MAAA;QAAApE,QAAA;QAAAe,KAAA;MAAA;MAAAsD,OAAA;QAAA9B,WAAA;MAAA;MAAA+B,UAAA;MAAAC,QAAA,GADf/F,EAAE,CAAAgG,kBAAA,CACiO,CAC5T9E,qBAAqB,EACrBF,uBAAuB,EACvB;QACIiF,OAAO,EAAEhF,yBAAyB;QAClCiF,QAAQ,EAAE;UAAEC,SAAS,EAAE;QAAK;MAChC,CAAC,CACJ,GAR4FnG,EAAE,CAAAoG,mBAAA;MAAAC,kBAAA,EAAAjE,GAAA;MAAAkE,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,2BAAAjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFxF,EAAE,CAAA0G,eAAA;UAAF1G,EAAE,CAAA2G,YAAA,EAQtD,CAAC;QAAA;MAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAgqB;EAAE;AACptB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAVqG/G,EAAE,CAAAgH,iBAAA,CAUXrD,iBAAiB,EAAc,CAAC;IAChHyB,IAAI,EAAEjF,SAAS;IACf8G,IAAI,EAAE,CAAC;MAAEnB,UAAU,EAAE,IAAI;MAAEoB,QAAQ,EAAE,WAAW;MAAEV,QAAQ,EAAE,gBAAgB;MAAEK,aAAa,EAAEzG,iBAAiB,CAAC+G,IAAI;MAAEL,eAAe,EAAEzG,uBAAuB,CAAC+G,MAAM;MAAEC,SAAS,EAAE,CACrKnG,qBAAqB,EACrBF,uBAAuB,EACvB;QACIiF,OAAO,EAAEhF,yBAAyB;QAClCiF,QAAQ,EAAE;UAAEC,SAAS,EAAE;QAAK;MAChC,CAAC,CACJ;MAAEmB,IAAI,EAAE;QACL,yBAAyB,EAAE;MAC/B,CAAC;MAAEV,MAAM,EAAE,CAAC,kiBAAkiB;IAAE,CAAC;EAC7jB,CAAC,CAAC,QAAkB;IAAEpF,QAAQ,EAAE,CAAC;MACzB4D,IAAI,EAAE9E;IACV,CAAC,CAAC;IAAEyD,WAAW,EAAE,CAAC;MACdqB,IAAI,EAAE7E;IACV,CAAC,CAAC;IAAEgC,KAAK,EAAE,CAAC;MACR6C,IAAI,EAAE9E;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiH,cAAc,CAAC;EACjB3D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4D,SAAS,GAAGzG,iBAAiB,CAACd,MAAM,CAACO,WAAW,CAAC,CAAC;IACvD,IAAI,CAAC4D,EAAE,GAAGxD,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAAC6G,KAAK,GAAGxH,MAAM,CAAC0D,iBAAiB,CAAC;IACtC,IAAI,CAAC+D,GAAG,GAAG,IAAI7F,YAAY,CAAC,CAAC;IAC7B,IAAI,CAAC8F,OAAO,GAAG,IAAI/F,eAAe,CAAC,CAACgG,GAAG,EAAEA,GAAG,CAAC,CAAC;IAC9C,IAAI,CAACC,SAAS,GAAG/F,aAAa,CAAC,CAC3B,IAAI,CAAC6F,OAAO,CAAC3D,IAAI,CAACjC,oBAAoB,CAACV,qBAAqB,CAAC,CAAC,EAC9DpB,MAAM,CAACiB,qBAAqB,CAAC,CAAC8C,IAAI,CAAChC,SAAS,CAAC,IAAI,CAAC,CAAC,EACnD/B,MAAM,CAACe,uBAAuB,CAAC,CAACgD,IAAI,CAAChC,SAAS,CAAC,IAAI,CAAC,CAAC,EACrD,IAAI,CAACyF,KAAK,CAACpD,MAAM,CAACL,IAAI,CAAC/B,YAAY,CAAC,CAAC,EAAEd,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAClE,CAAC,CAAC6C,IAAI,CAACrC,GAAG,CAAC,CAAC,CAACmG,MAAM,CAAC,KAAKA,MAAM,CAAC,CAAC;EACtC;EACAC,IAAIA,CAAC7D,OAAO,EAAE;IACV,IAAI,IAAI,CAACsD,SAAS,EAAE;MAChB,IAAI,CAACE,GAAG,CAACM,GAAG,CAAC,IAAI,CAACH,SAAS,CAACI,SAAS,CAAEH,MAAM,IAAK;QAC9C,IAAI,CAACI,WAAW,CAAChE,OAAO,EAAE4D,MAAM,CAAC;QACjC,IAAI,CAACK,OAAO,CAACjE,OAAO,EAAE4D,MAAM,CAAC;MACjC,CAAC,CAAC,CAAC;IACP,CAAC,MACI;MACD,IAAI,CAAC1D,EAAE,CAACgE,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC;IACrD;EACJ;EACAC,SAASA,CAACR,MAAM,EAAE;IACd,IAAI,CAACH,OAAO,CAACrD,IAAI,CAACwD,MAAM,CAAC;EAC7B;EACAS,WAAWA,CAAA,EAAG;IACV,IAAI,CAACb,GAAG,CAACc,WAAW,CAAC,CAAC;EAC1B;EACAC,OAAOA,CAAC,CAACC,IAAI,EAAEC,GAAG,CAAC,EAAE;IACjB,MAAMC,KAAK,GAAGC,MAAM,CAACC,KAAK,CAACH,GAAG,CAAC,GAAG,IAAI,CAACvE,EAAE,CAAC2E,SAAS,GAAGJ,GAAG;IACzD,MAAMK,MAAM,GAAGH,MAAM,CAACC,KAAK,CAACJ,IAAI,CAAC,GAAG,IAAI,CAACtE,EAAE,CAAC6E,UAAU,GAAGP,IAAI;IAC7D,MAAMQ,IAAI,GAAG;MACTP,GAAG,EAAEC,KAAK;MACVF,IAAI,EAAEM,MAAM;MACZG,KAAK,EAAE,IAAI,CAAC/E,EAAE,CAACgF,WAAW;MAC1BC,MAAM,EAAE,IAAI,CAACjF,EAAE,CAACkF,YAAY;MAC5BC,KAAK,EAAE3B,GAAG;MACV4B,MAAM,EAAE5B,GAAG;MACX6B,CAAC,EAAEb,KAAK;MACRc,CAAC,EAAEV;IACP,CAAC;IACD,OAAO;MACH,GAAGE,IAAI;MACPS,MAAM,EAAEA,CAAA,KAAMC,IAAI,CAACC,SAAS,CAACX,IAAI;IACrC,CAAC;EACL;EACAf,OAAOA,CAAC;IAAEC;EAAM,CAAC,EAAEN,MAAM,EAAE;IACvB,MAAM;MAAEa,GAAG;MAAED,IAAI;MAAES,KAAK;MAAEE;IAAO,CAAC,GAAG,IAAI,CAACZ,OAAO,CAACX,MAAM,CAAC;IACzDM,KAAK,CAACO,GAAG,GAAGrH,KAAK,CAACqH,GAAG,CAAC;IACtBP,KAAK,CAACM,IAAI,GAAGpH,KAAK,CAACoH,IAAI,CAAC;IACxBN,KAAK,CAACe,KAAK,GAAG7H,KAAK,CAAC6H,KAAK,CAAC;IAC1Bf,KAAK,CAACiB,MAAM,GAAG/H,KAAK,CAAC+H,MAAM,CAAC;EAChC;EACAnB,WAAWA,CAAChE,OAAO,EAAE,CAACwE,IAAI,CAAC,EAAE;IACzB,IAAI,CAACG,MAAM,CAACC,KAAK,CAACJ,IAAI,CAAC,EAAE;MACrBxE,OAAO,CAACkE,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC;MAC9CnE,OAAO,CAACkE,KAAK,CAACC,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC;MAC/C;IACJ;IACA,MAAM;MAAED;IAAM,CAAC,GAAGlE,OAAO;IACzB,MAAMgF,IAAI,GAAGhF,OAAO,CAAC4F,qBAAqB,CAAC,CAAC;IAC5C,MAAMxC,IAAI,GAAG,IAAI,CAAClD,EAAE,CAAC0F,qBAAqB,CAAC,CAAC;IAC5C1B,KAAK,CAAC2B,cAAc,CAAC,UAAU,CAAC;IAChC3B,KAAK,CAAC2B,cAAc,CAAC,YAAY,CAAC;IAClC3B,KAAK,CAAC2B,cAAc,CAAC,KAAK,CAAC;IAC3B3B,KAAK,CAAC2B,cAAc,CAAC,MAAM,CAAC;IAC5B3B,KAAK,CAACO,GAAG,GAAGrH,KAAK,CAAC4H,IAAI,CAACP,GAAG,GAAGrB,IAAI,CAACqB,GAAG,GAAG,IAAI,CAACvE,EAAE,CAAC2E,SAAS,CAAC;IAC1DX,KAAK,CAACM,IAAI,GAAGpH,KAAK,CAAC4H,IAAI,CAACR,IAAI,GAAGpB,IAAI,CAACoB,IAAI,GAAG,IAAI,CAACtE,EAAE,CAAC6E,UAAU,CAAC;EAClE;EACA;IAAS,IAAI,CAAClE,IAAI,YAAAiF,uBAAA/E,CAAA;MAAA,YAAAA,CAAA,IAAyFsC,cAAc;IAAA,CAAoD;EAAE;EAC/K;IAAS,IAAI,CAAC0C,KAAK,kBAvG8EjK,EAAE,CAAAkK,kBAAA;MAAAC,KAAA,EAuGY5C,cAAc;MAAA6C,OAAA,EAAd7C,cAAc,CAAAxC;IAAA,EAAG;EAAE;AACtI;AACA;EAAA,QAAAgC,SAAA,oBAAAA,SAAA,KAzGqG/G,EAAE,CAAAgH,iBAAA,CAyGXO,cAAc,EAAc,CAAC;IAC7GnC,IAAI,EAAE3E;EACV,CAAC,CAAC;AAAA;AAEV,MAAM4J,OAAO,CAAC;EACVzG,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0G,OAAO,GAAGrK,MAAM,CAACsH,cAAc,CAAC;IACrC,IAAI,CAACE,KAAK,GAAGxH,MAAM,CAAC0D,iBAAiB,CAAC;IACtC,IAAI,CAAChB,OAAO,GAAGzC,MAAM,CAAC,KAAK,CAAC;IAC5B,IAAI,CAACiJ,KAAK,GAAG,CAAC;IACd,IAAI,CAACE,MAAM,GAAG,CAAC;IACf,IAAI,CAACnF,OAAO,GAAGtD,gBAAgB,CAAC,CAAC;EACrC;EACA2J,MAAMA,CAACzC,MAAM,EAAE;IACX,MAAMnF,OAAO,GAAG,CAACkG,MAAM,CAACC,KAAK,CAAChB,MAAM,CAAC,CAAC,CAAC,CAAC;IACxC,IAAI,CAACnF,OAAO,CAACK,GAAG,CAAC,IAAI,CAACL,OAAO,CAAC,CAAC,IAAIA,OAAO,CAAC;IAC3C,IAAI,CAAC8E,KAAK,CAACvD,OAAO,CAAClB,GAAG,CAACL,OAAO,GAAG,IAAI,CAACuB,OAAO,GAAG,IAAI,CAAC;IACrD,IAAI,CAACoG,OAAO,CAAChC,SAAS,CAACR,MAAM,CAAC;IAC9B,IAAInF,OAAO,EAAE;MACT,IAAI,CAAC8E,KAAK,CAACrD,EAAE,CAACoG,SAAS,CAACxC,GAAG,CAAC,UAAU,CAAC;IAC3C,CAAC,MACI;MACD,IAAI,CAACP,KAAK,CAACrD,EAAE,CAACoG,SAAS,CAACC,MAAM,CAAC,UAAU,CAAC;IAC9C;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACL,OAAO,CAACvC,IAAI,CAAC,IAAI,CAAC4C,OAAO,CAACC,aAAa,CAAC;IACjD;EACJ;EACArC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACd,KAAK,CAACvD,OAAO,CAAC,CAAC,KAAK,IAAI,CAACA,OAAO,EAAE;MACvC,IAAI,CAACuD,KAAK,CAACvD,OAAO,CAAClB,GAAG,CAAC,IAAI,CAAC;IAChC;EACJ;EACA,IAAI6H,MAAMA,CAAA,EAAG;IACT,OAAO,yBAAyB,IAAI,CAAC1B,KAAK,GAAG;EACjD;EACA,IAAI2B,GAAGA,CAAA,EAAG;IACN,OAAO,0BAA0B,IAAI,CAACzB,MAAM,GAAG;EACnD;EACA0B,OAAOA,CAAA,EAAG;IACN,IAAI,CAACtD,KAAK,CAACjD,SAAS,CAAC,IAAI,CAACN,OAAO,CAAC;EACtC;EACA8G,eAAeA,CAAA,EAAG;IACd,IAAI,CAACrI,OAAO,CAACK,GAAG,CAAC,KAAK,CAAC;EAC3B;EACA;IAAS,IAAI,CAAC+B,IAAI,YAAAkG,gBAAAhG,CAAA;MAAA,YAAAA,CAAA,IAAyFoF,OAAO;IAAA,CAAmD;EAAE;EACvK;IAAS,IAAI,CAACnF,IAAI,kBAzJ+ElF,EAAE,CAAAmF,iBAAA;MAAAC,IAAA,EAyJJiF,OAAO;MAAAhF,SAAA;MAAA6F,SAAA,WAAAC,cAAA3F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzJLxF,EAAE,CAAAoL,WAAA,CAAA/I,GAAA;QAAA;QAAA,IAAAmD,EAAA;UAAA,IAAA6F,EAAA;UAAFrL,EAAE,CAAAsL,cAAA,CAAAD,EAAA,GAAFrL,EAAE,CAAAuL,WAAA,QAAA9F,GAAA,CAAAkF,OAAA,GAAAU,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,QAAA;MAAAnG,YAAA,WAAAoG,qBAAAlG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFxF,EAAE,CAAA0F,UAAA,0BAAAiG,wCAAA;YAAA,OAyJJlG,GAAA,CAAAsF,OAAA,CAAQ,CAAC;UAAA,CAAH,CAAC;QAAA;QAAA,IAAAvF,EAAA;UAzJLxF,EAAE,CAAA4L,WAAA,gBAAAnG,GAAA,CAAAoF,MAyJE,CAAC,aAAApF,GAAA,CAAAqF,GAAD,CAAC;UAzJL9K,EAAE,CAAA6L,WAAA,aAyJJpG,GAAA,CAAA9C,OAAA,CAAQ,CAAF,CAAC;QAAA;MAAA;MAAAiD,MAAA;QAAAuD,KAAA;QAAAE,MAAA;MAAA;MAAAvD,UAAA;MAAAC,QAAA,GAzJL/F,EAAE,CAAAgG,kBAAA,CAyJmQ,CAACuB,cAAc,CAAC,GAzJrRvH,EAAE,CAAAoG,mBAAA;MAAAC,kBAAA,EAAAjE,GAAA;MAAAkE,KAAA;MAAAC,IAAA;MAAAuF,MAAA;MAAAtF,QAAA,WAAAuF,iBAAAvG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAwG,GAAA,GAAFhM,EAAE,CAAAiM,gBAAA;UAAFjM,EAAE,CAAA0G,eAAA;UAAF1G,EAAE,CAAAkM,cAAA,eAyJ+e,CAAC;UAzJlflM,EAAE,CAAA0F,UAAA,gCAAAyG,mDAAA;YAAFnM,EAAE,CAAAoM,aAAA,CAAAJ,GAAA;YAAA,OAAFhM,EAAE,CAAAqM,WAAA,CAyJ0d5G,GAAA,CAAAuF,eAAA,CAAgB,CAAC;UAAA,CAAC,CAAC;UAzJ/ehL,EAAE,CAAA2G,YAAA,EAyJmgB,CAAC;UAzJtgB3G,EAAE,CAAAsM,YAAA,CAyJ2gB,CAAC;QAAA;MAAA;MAAAzF,aAAA;MAAAC,eAAA;IAAA,EAA0D;EAAE;AAC/qB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3JqG/G,EAAE,CAAAgH,iBAAA,CA2JXqD,OAAO,EAAc,CAAC;IACtGjF,IAAI,EAAEjF,SAAS;IACf8G,IAAI,EAAE,CAAC;MAAEnB,UAAU,EAAE,IAAI;MAAEoB,QAAQ,EAAE,UAAU;MAAEJ,eAAe,EAAEzG,uBAAuB,CAAC+G,MAAM;MAAEC,SAAS,EAAE,CAACE,cAAc,CAAC;MAAED,IAAI,EAAE;QACzH,kBAAkB,EAAE,WAAW;QAC/B,oBAAoB,EAAE,QAAQ;QAC9B,iBAAiB,EAAE,KAAK;QACxB,gBAAgB,EAAE;MACtB,CAAC;MAAEd,QAAQ,EAAE;IAA+H,CAAC;EACzJ,CAAC,CAAC,QAAkB;IAAEmE,OAAO,EAAE,CAAC;MACxBvF,IAAI,EAAE1E,SAAS;MACfuG,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEkC,KAAK,EAAE,CAAC;MACR/D,IAAI,EAAE9E;IACV,CAAC,CAAC;IAAE+I,MAAM,EAAE,CAAC;MACTjE,IAAI,EAAE9E;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,SAASiM,aAAaA,CAAC7C,CAAC,GAAG9B,GAAG,EAAE;EAC5B,OAAO,CAACiB,MAAM,CAACC,KAAK,CAACY,CAAC,CAAC,IAAI,CAACb,MAAM,CAACC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvD;AACA,SAAS0D,UAAUA,CAAA,EAAG;EAClB,OAAO,CAAC3D,MAAM,CAACC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC;AACA,MAAM2D,aAAa,CAAC;EAChB7I,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8I,IAAI,GAAGzM,MAAM,CAACoK,OAAO,CAAC;IAC3B,IAAI,CAACX,CAAC,GAAG9B,GAAG;IACZ,IAAI,CAAC6B,CAAC,GAAG7B,GAAG;EAChB;EACA+E,SAASA,CAACjD,CAAC,GAAG9B,GAAG,EAAE6B,CAAC,GAAG7B,GAAG,EAAE;IACxB,MAAM;MAAEc,IAAI;MAAEC;IAAI,CAAC,GAAG,IAAI,CAAC+D,IAAI,CAACxI,OAAO,CAAC4F,qBAAqB,CAAC,CAAC;IAC/D,IAAI,CAACJ,CAAC,GAAGA,CAAC,GAAGhB,IAAI;IACjB,IAAI,CAACe,CAAC,GAAGA,CAAC,GAAGd,GAAG;IAChB,IAAI,CAAC+D,IAAI,CAACnC,MAAM,CAAC,CAAC3C,GAAG,EAAEA,GAAG,CAAC,CAAC;EAChC;EACAgF,MAAMA,CAAClD,CAAC,EAAED,CAAC,EAAE;IACT,IAAI,CAACiD,IAAI,CAACnC,MAAM,CAAC,CAACb,CAAC,GAAG,IAAI,CAACA,CAAC,EAAED,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC,CAAC;EAC9C;EACAoD,OAAOA,CAACC,KAAK,EAAE;IACX,MAAMC,MAAM,GAAGlM,kBAAkB,CAACiM,KAAK,CAAC;IACxC,MAAM;MAAEpD,CAAC;MAAED,CAAC;MAAEuD;IAAU,CAAC,GAAGF,KAAK;IACjC,IAAIhM,YAAY,CAACiM,MAAM,CAAC,EAAE;MACtBA,MAAM,CAACE,qBAAqB,CAACD,SAAS,CAAC;IAC3C;IACA,IAAI,CAACL,SAAS,CAACjD,CAAC,EAAED,CAAC,CAAC;EACxB;EACA;IAAS,IAAI,CAAC1E,IAAI,YAAAmI,sBAAAjI,CAAA;MAAA,YAAAA,CAAA,IAAyFwH,aAAa;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACU,IAAI,kBA1M+EnN,EAAE,CAAAoN,iBAAA;MAAAhI,IAAA,EA0MJqH,aAAa;MAAApH,SAAA;MAAAoG,QAAA;MAAAnG,YAAA,WAAA+H,2BAAA7H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1MXxF,EAAE,CAAA0F,UAAA,0CAAA4H,8DAAAC,MAAA;YAAA,OA0MJ9H,GAAA,CAAAoH,OAAA,CAAAU,MAAc,CAAC;UAAA,CAAH,CAAC,gCAAAC,oDAAA;YAAA,OAAb/H,GAAA,CAAAkH,SAAA,CAAU,CAAC;UAAA,UA1MT3M,EAAE,CAAAyN,iBA0MQ,CAAC,kCAAAC,sDAAAH,MAAA;YAAA,OAAb9H,GAAA,CAAAmH,MAAA,CAAAW,MAAA,CAAA7D,CAAA,EAAA6D,MAAA,CAAA9D,CAAyB,CAAC;UAAA,UA1MxBzJ,EAAE,CAAAyN,iBA0MQ,CAAC;QAAA;QAAA,IAAAjI,EAAA;UA1MXxF,EAAE,CAAA4L,WAAA,iBA0MJ,MAAY,CAAC,gBAAb,MAAY,CAAC;QAAA;MAAA;MAAA9F,UAAA;IAAA,EAA4U;EAAE;AAC9b;AACA5D,UAAU,CAAC,CACPC,UAAU,CAACoK,aAAa,CAAC,CAC5B,EAAEE,aAAa,CAACkB,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC;AAC9CzL,UAAU,CAAC,CACPC,UAAU,CAACqK,UAAU,CAAC,CACzB,EAAEC,aAAa,CAACkB,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;AAC3C;EAAA,QAAA5G,SAAA,oBAAAA,SAAA,KAlNqG/G,EAAE,CAAAgH,iBAAA,CAkNXyF,aAAa,EAAc,CAAC;IAC5GrH,IAAI,EAAEzE,SAAS;IACfsG,IAAI,EAAE,CAAC;MACCnB,UAAU,EAAE,IAAI;MAChBoB,QAAQ,EAAE,iBAAiB;MAC3BI,IAAI,EAAE;QACF,qBAAqB,EAAE,QAAQ;QAC/B,oBAAoB,EAAE,QAAQ;QAC9B,gCAAgC,EAAE,iBAAiB;QACnD,+BAA+B,EAAE,aAAa;QAC9C,iCAAiC,EAAE;MACvC;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEqF,SAAS,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAG,CAAC;AAAA;AAEzD,MAAMgB,QAAQ,GAAG,CAACjK,iBAAiB,EAAE0G,OAAO,EAAEoC,aAAa,CAAC;;AAE5D;AACA;AACA;;AAEA,SAAS/I,iBAAiB,EAAE2G,OAAO,EAAEoC,aAAa,EAAElF,cAAc,EAAEqG,QAAQ,EAAEjK,iBAAiB,EAAEV,aAAa,EAAEX,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}