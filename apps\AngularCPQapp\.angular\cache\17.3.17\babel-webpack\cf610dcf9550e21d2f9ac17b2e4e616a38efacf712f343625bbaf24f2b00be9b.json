{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Input } from '@angular/core';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nconst _c0 = [\"*\"];\nconst TUI_BADGE_NOTIFICATION_DEFAULT_OPTIONS = {\n  size: 'm'\n};\nconst TUI_BADGE_NOTIFICATION_OPTIONS = tuiCreateToken(TUI_BADGE_NOTIFICATION_DEFAULT_OPTIONS);\nfunction tuiBadgeNotificationOptionsProvider(options) {\n  return tuiProvideOptions(TUI_BADGE_NOTIFICATION_OPTIONS, options, TUI_BADGE_NOTIFICATION_DEFAULT_OPTIONS);\n}\nclass TuiBadgeNotification {\n  constructor() {\n    this.size = inject(TUI_BADGE_NOTIFICATION_OPTIONS).size;\n  }\n  static {\n    this.ɵfac = function TuiBadgeNotification_Factory(t) {\n      return new (t || TuiBadgeNotification)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiBadgeNotification,\n      selectors: [[\"tui-badge-notification\"]],\n      hostVars: 1,\n      hostBindings: function TuiBadgeNotification_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n        }\n      },\n      inputs: {\n        size: \"size\"\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([i1.TuiAnimated]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function TuiBadgeNotification_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\"[_nghost-%COMP%]{--t-size: 1.5rem;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;color:#fff;border-radius:2rem;white-space:nowrap;overflow:hidden;vertical-align:middle;font:var(--tui-font-text-s);max-inline-size:100%;padding:0 .25rem;background:#f52222;block-size:var(--t-size);min-inline-size:var(--t-size)}.tui-enter[_nghost-%COMP%]{animation:tuiScale var(--tui-duration) cubic-bezier(.34,1.56,.64,1)}.tui-leave[_nghost-%COMP%]{animation:tuiScale var(--tui-duration) ease-in-out reverse}[data-size=m][_nghost-%COMP%]{--t-size: 1.25rem}[data-size=s][_nghost-%COMP%]{--t-size: 1rem;padding:0 .125rem;font:var(--tui-font-text-xs)}[data-size=xs][_nghost-%COMP%]{--t-size: .375rem;padding:0;font-size:0}[_nghost-%COMP%]   [tuiIconButton][_nghost-%COMP%], [tuiIconButton]   [_nghost-%COMP%]{position:absolute;right:25%;top:25%}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiBadgeNotification, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-badge-notification',\n      template: '<ng-content />',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      hostDirectives: [TuiAnimated],\n      host: {\n        '[attr.data-size]': 'size'\n      },\n      styles: [\":host{--t-size: 1.5rem;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;color:#fff;border-radius:2rem;white-space:nowrap;overflow:hidden;vertical-align:middle;font:var(--tui-font-text-s);max-inline-size:100%;padding:0 .25rem;background:#f52222;block-size:var(--t-size);min-inline-size:var(--t-size)}:host.tui-enter{animation:tuiScale var(--tui-duration) cubic-bezier(.34,1.56,.64,1)}:host.tui-leave{animation:tuiScale var(--tui-duration) ease-in-out reverse}:host[data-size=m]{--t-size: 1.25rem}:host[data-size=s]{--t-size: 1rem;padding:0 .125rem;font:var(--tui-font-text-xs)}:host[data-size=xs]{--t-size: .375rem;padding:0;font-size:0}:host :host-context([tuiIconButton]){position:absolute;right:25%;top:25%}\\n\"]\n    }]\n  }], null, {\n    size: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_BADGE_NOTIFICATION_DEFAULT_OPTIONS, TUI_BADGE_NOTIFICATION_OPTIONS, TuiBadgeNotification, tuiBadgeNotificationOptionsProvider };", "map": {"version": 3, "names": ["i0", "inject", "Component", "ChangeDetectionStrategy", "Input", "i1", "TuiAnimated", "tuiCreateToken", "tuiProvideOptions", "_c0", "TUI_BADGE_NOTIFICATION_DEFAULT_OPTIONS", "size", "TUI_BADGE_NOTIFICATION_OPTIONS", "tuiBadgeNotificationOptionsProvider", "options", "TuiBadgeNotification", "constructor", "ɵfac", "TuiBadgeNotification_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "TuiBadgeNotification_HostBindings", "rf", "ctx", "ɵɵattribute", "inputs", "standalone", "features", "ɵɵHostDirectivesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "template", "TuiBadgeNotification_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "hostDirectives", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-badge-notification.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Input } from '@angular/core';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\n\nconst TUI_BADGE_NOTIFICATION_DEFAULT_OPTIONS = {\n    size: 'm',\n};\nconst TUI_BADGE_NOTIFICATION_OPTIONS = tuiCreateToken(TUI_BADGE_NOTIFICATION_DEFAULT_OPTIONS);\nfunction tuiBadgeNotificationOptionsProvider(options) {\n    return tuiProvideOptions(TUI_BADGE_NOTIFICATION_OPTIONS, options, TUI_BADGE_NOTIFICATION_DEFAULT_OPTIONS);\n}\n\nclass TuiBadgeNotification {\n    constructor() {\n        this.size = inject(TUI_BADGE_NOTIFICATION_OPTIONS).size;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBadgeNotification, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiBadgeNotification, isStandalone: true, selector: \"tui-badge-notification\", inputs: { size: \"size\" }, host: { properties: { \"attr.data-size\": \"size\" } }, hostDirectives: [{ directive: i1.TuiAnimated }], ngImport: i0, template: '<ng-content />', isInline: true, styles: [\":host{--t-size: 1.5rem;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;color:#fff;border-radius:2rem;white-space:nowrap;overflow:hidden;vertical-align:middle;font:var(--tui-font-text-s);max-inline-size:100%;padding:0 .25rem;background:#f52222;block-size:var(--t-size);min-inline-size:var(--t-size)}:host.tui-enter{animation:tuiScale var(--tui-duration) cubic-bezier(.34,1.56,.64,1)}:host.tui-leave{animation:tuiScale var(--tui-duration) ease-in-out reverse}:host[data-size=m]{--t-size: 1.25rem}:host[data-size=s]{--t-size: 1rem;padding:0 .125rem;font:var(--tui-font-text-xs)}:host[data-size=xs]{--t-size: .375rem;padding:0;font-size:0}:host :host-context([tuiIconButton]){position:absolute;right:25%;top:25%}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBadgeNotification, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-badge-notification', template: '<ng-content />', changeDetection: ChangeDetectionStrategy.OnPush, hostDirectives: [TuiAnimated], host: {\n                        '[attr.data-size]': 'size',\n                    }, styles: [\":host{--t-size: 1.5rem;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;color:#fff;border-radius:2rem;white-space:nowrap;overflow:hidden;vertical-align:middle;font:var(--tui-font-text-s);max-inline-size:100%;padding:0 .25rem;background:#f52222;block-size:var(--t-size);min-inline-size:var(--t-size)}:host.tui-enter{animation:tuiScale var(--tui-duration) cubic-bezier(.34,1.56,.64,1)}:host.tui-leave{animation:tuiScale var(--tui-duration) ease-in-out reverse}:host[data-size=m]{--t-size: 1.25rem}:host[data-size=s]{--t-size: 1rem;padding:0 .125rem;font:var(--tui-font-text-xs)}:host[data-size=xs]{--t-size: .375rem;padding:0;font-size:0}:host :host-context([tuiIconButton]){position:absolute;right:25%;top:25%}\\n\"] }]\n        }], propDecorators: { size: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_BADGE_NOTIFICATION_DEFAULT_OPTIONS, TUI_BADGE_NOTIFICATION_OPTIONS, TuiBadgeNotification, tuiBadgeNotificationOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,QAAQ,eAAe;AACjF,OAAO,KAAKC,EAAE,MAAM,mCAAmC;AACvD,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,mCAAmC;AAAC,MAAAC,GAAA;AAEtF,MAAMC,sCAAsC,GAAG;EAC3CC,IAAI,EAAE;AACV,CAAC;AACD,MAAMC,8BAA8B,GAAGL,cAAc,CAACG,sCAAsC,CAAC;AAC7F,SAASG,mCAAmCA,CAACC,OAAO,EAAE;EAClD,OAAON,iBAAiB,CAACI,8BAA8B,EAAEE,OAAO,EAAEJ,sCAAsC,CAAC;AAC7G;AAEA,MAAMK,oBAAoB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACL,IAAI,GAAGV,MAAM,CAACW,8BAA8B,CAAC,CAACD,IAAI;EAC3D;EACA;IAAS,IAAI,CAACM,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFJ,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACK,IAAI,kBAD+EpB,EAAE,CAAAqB,iBAAA;MAAAC,IAAA,EACJP,oBAAoB;MAAAQ,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADlB3B,EAAE,CAAA6B,WAAA,cAAAD,GAAA,CAAAjB,IAAA;QAAA;MAAA;MAAAmB,MAAA;QAAAnB,IAAA;MAAA;MAAAoB,UAAA;MAAAC,QAAA,GAAFhC,EAAE,CAAAiC,uBAAA,EACsL5B,EAAE,CAACC,WAAW,IADtMN,EAAE,CAAAkC,mBAAA;MAAAC,kBAAA,EAAA1B,GAAA;MAAA2B,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,8BAAAZ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3B,EAAE,CAAAwC,eAAA;UAAFxC,EAAE,CAAAyC,YAAA,EAC+O,CAAC;QAAA;MAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA,EAAk1B;EAAE;AAC3qC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG5C,EAAE,CAAA6C,iBAAA,CAGX9B,oBAAoB,EAAc,CAAC;IACnHO,IAAI,EAAEpB,SAAS;IACf4C,IAAI,EAAE,CAAC;MAAEf,UAAU,EAAE,IAAI;MAAEgB,QAAQ,EAAE,wBAAwB;MAAET,QAAQ,EAAE,gBAAgB;MAAEK,eAAe,EAAExC,uBAAuB,CAAC6C,MAAM;MAAEC,cAAc,EAAE,CAAC3C,WAAW,CAAC;MAAE4C,IAAI,EAAE;QACrK,kBAAkB,EAAE;MACxB,CAAC;MAAER,MAAM,EAAE,CAAC,8vBAA8vB;IAAE,CAAC;EACzxB,CAAC,CAAC,QAAkB;IAAE/B,IAAI,EAAE,CAAC;MACrBW,IAAI,EAAElB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASM,sCAAsC,EAAEE,8BAA8B,EAAEG,oBAAoB,EAAEF,mCAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}