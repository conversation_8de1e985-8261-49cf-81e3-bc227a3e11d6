{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive } from '@angular/core';\n\n/**\n * Blank directive for queries via `@ContentChildren` / `@ViewChildren` / `querySelector`\n */\nclass TuiItem {\n  static {\n    this.ɵfac = function TuiItem_Factory(t) {\n      return new (t || TuiItem)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiItem,\n      selectors: [[\"\", \"tuiItem\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiItem, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiItem]'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiItem };", "map": {"version": 3, "names": ["i0", "Directive", "TuiItem", "ɵfac", "TuiItem_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-item.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive } from '@angular/core';\n\n/**\n * Blank directive for queries via `@ContentChildren` / `@ViewChildren` / `querySelector`\n */\nclass TuiItem {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiItem, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiItem, isStandalone: true, selector: \"[tuiItem]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiItem, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiItem]',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiItem };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,QAAQ,eAAe;;AAEzC;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACV;IAAS,IAAI,CAACC,IAAI,YAAAC,gBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,OAAO;IAAA,CAAmD;EAAE;EACvK;IAAS,IAAI,CAACI,IAAI,kBAD+EN,EAAE,CAAAO,iBAAA;MAAAC,IAAA,EACJN,OAAO;MAAAO,SAAA;MAAAC,UAAA;IAAA,EAA4D;EAAE;AACxK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGX,EAAE,CAAAY,iBAAA,CAGXV,OAAO,EAAc,CAAC;IACtGM,IAAI,EAAEP,SAAS;IACfY,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASZ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}