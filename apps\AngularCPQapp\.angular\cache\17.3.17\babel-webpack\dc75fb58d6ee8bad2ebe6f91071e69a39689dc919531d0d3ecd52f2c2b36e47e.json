{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport { inject } from '@angular/core';\nimport { TUI_FONT_SIZE_HANDLER } from '@taiga-ui/cdk/directives/font-size';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { tuiGetElementObscures } from '@taiga-ui/cdk/utils/dom';\nimport { TUI_ANIMATIONS_SPEED } from '@taiga-ui/core/tokens';\nfunction tuiEnableFontScaling() {\n  return {\n    provide: TUI_FONT_SIZE_HANDLER,\n    useFactory: ({\n      documentElement\n    } = inject(DOCUMENT)) => size => documentElement.style.setProperty('--tui-font-offset', `${tuiClamp(size, 17, 28) - 17}px`)\n  };\n}\nconst KEYS = ['Spacebar', 'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Left', 'Right', 'End', 'Home'];\n/**\n * Check if pressed key is interactive in terms of input field\n */\nfunction tuiIsEditingKey(key = '') {\n  return key.length === 1 || KEYS.includes(key);\n}\n\n/**\n * @internal\n */\nfunction tuiIsObscured(el, exceptSelector = 'tui-hints') {\n  return !!tuiGetElementObscures(el)?.some(el => !el.closest(exceptSelector));\n}\nfunction tuiOverrideOptions(override, fallback) {\n  return (directive, options) => {\n    const result = directive || {\n      ...(options || fallback)\n    };\n    Object.keys(override).forEach(key => {\n      // Update directive props with new defaults before inputs are processed\n      result[key] = override[key];\n    });\n    return result;\n  };\n}\nconst SIZES = {\n  xxs: 0,\n  xs: 1,\n  s: 2,\n  m: 3,\n  l: 4,\n  xl: 5,\n  xxl: 6\n};\n/**\n * Compares size constants to determine if first size is bigger than the second\n *\n * @param size size that we need to compare\n * @param biggerThanSize size to compare with, 's' by default\n */\nfunction tuiSizeBigger(size, biggerThanSize = 's') {\n  return SIZES[size] > SIZES[biggerThanSize];\n}\nconst TUI_ANIMATIONS_DEFAULT_DURATION = 300;\nfunction tuiToAnimationOptions(speed = inject(TUI_ANIMATIONS_SPEED), easing) {\n  return {\n    value: '',\n    params: {\n      duration: tuiGetDuration(speed),\n      easing\n    }\n  };\n}\nfunction tuiGetDuration(speed) {\n  return speed && TUI_ANIMATIONS_DEFAULT_DURATION / speed;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_ANIMATIONS_DEFAULT_DURATION, tuiEnableFontScaling, tuiGetDuration, tuiIsEditingKey, tuiIsObscured, tuiOverrideOptions, tuiSizeBigger, tuiToAnimationOptions };", "map": {"version": 3, "names": ["DOCUMENT", "inject", "TUI_FONT_SIZE_HANDLER", "tui<PERSON><PERSON>", "tuiGetElementObscures", "TUI_ANIMATIONS_SPEED", "tuiEnableFontScaling", "provide", "useFactory", "documentElement", "size", "style", "setProperty", "KEYS", "tuiIsEditingKey", "key", "length", "includes", "tuiIsObscured", "el", "except<PERSON><PERSON><PERSON>", "some", "closest", "tuiOverrideOptions", "override", "fallback", "directive", "options", "result", "Object", "keys", "for<PERSON>ach", "SIZES", "xxs", "xs", "s", "m", "l", "xl", "xxl", "tui<PERSON>izeBigger", "biggerThanSize", "TUI_ANIMATIONS_DEFAULT_DURATION", "tuiToAnimationOptions", "speed", "easing", "value", "params", "duration", "tuiGetDuration"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-utils-miscellaneous.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport { inject } from '@angular/core';\nimport { TUI_FONT_SIZE_HANDLER } from '@taiga-ui/cdk/directives/font-size';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { tuiGetElementObscures } from '@taiga-ui/cdk/utils/dom';\nimport { TUI_ANIMATIONS_SPEED } from '@taiga-ui/core/tokens';\n\nfunction tuiEnableFontScaling() {\n    return {\n        provide: TUI_FONT_SIZE_HANDLER,\n        useFactory: ({ documentElement } = inject(DOCUMENT)) => (size) => documentElement.style.setProperty('--tui-font-offset', `${tuiClamp(size, 17, 28) - 17}px`),\n    };\n}\n\nconst KEYS = [\n    'Spacebar',\n    'Backspace',\n    'Delete',\n    'ArrowLeft',\n    'ArrowRight',\n    'Left',\n    'Right',\n    'End',\n    'Home',\n];\n/**\n * Check if pressed key is interactive in terms of input field\n */\nfunction tuiIsEditingKey(key = '') {\n    return key.length === 1 || KEYS.includes(key);\n}\n\n/**\n * @internal\n */\nfunction tuiIsObscured(el, exceptSelector = 'tui-hints') {\n    return !!tuiGetElementObscures(el)?.some((el) => !el.closest(exceptSelector));\n}\n\nfunction tuiOverrideOptions(override, fallback) {\n    return (directive, options) => {\n        const result = directive || { ...(options || fallback) };\n        Object.keys(override).forEach((key) => {\n            // Update directive props with new defaults before inputs are processed\n            result[key] = override[key];\n        });\n        return result;\n    };\n}\n\nconst SIZES = {\n    xxs: 0,\n    xs: 1,\n    s: 2,\n    m: 3,\n    l: 4,\n    xl: 5,\n    xxl: 6,\n};\n/**\n * Compares size constants to determine if first size is bigger than the second\n *\n * @param size size that we need to compare\n * @param biggerThanSize size to compare with, 's' by default\n */\nfunction tuiSizeBigger(size, biggerThanSize = 's') {\n    return SIZES[size] > SIZES[biggerThanSize];\n}\n\nconst TUI_ANIMATIONS_DEFAULT_DURATION = 300;\nfunction tuiToAnimationOptions(speed = inject(TUI_ANIMATIONS_SPEED), easing) {\n    return {\n        value: '',\n        params: {\n            duration: tuiGetDuration(speed),\n            easing,\n        },\n    };\n}\nfunction tuiGetDuration(speed) {\n    return speed && TUI_ANIMATIONS_DEFAULT_DURATION / speed;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_ANIMATIONS_DEFAULT_DURATION, tuiEnableFontScaling, tuiGetDuration, tuiIsEditingKey, tuiIsObscured, tuiOverrideOptions, tuiSizeBigger, tuiToAnimationOptions };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,oBAAoB,QAAQ,uBAAuB;AAE5D,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,OAAO;IACHC,OAAO,EAAEL,qBAAqB;IAC9BM,UAAU,EAAEA,CAAC;MAAEC;IAAgB,CAAC,GAAGR,MAAM,CAACD,QAAQ,CAAC,KAAMU,IAAI,IAAKD,eAAe,CAACE,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAE,GAAGT,QAAQ,CAACO,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI;EAC/J,CAAC;AACL;AAEA,MAAMG,IAAI,GAAG,CACT,UAAU,EACV,WAAW,EACX,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,CACT;AACD;AACA;AACA;AACA,SAASC,eAAeA,CAACC,GAAG,GAAG,EAAE,EAAE;EAC/B,OAAOA,GAAG,CAACC,MAAM,KAAK,CAAC,IAAIH,IAAI,CAACI,QAAQ,CAACF,GAAG,CAAC;AACjD;;AAEA;AACA;AACA;AACA,SAASG,aAAaA,CAACC,EAAE,EAAEC,cAAc,GAAG,WAAW,EAAE;EACrD,OAAO,CAAC,CAAChB,qBAAqB,CAACe,EAAE,CAAC,EAAEE,IAAI,CAAEF,EAAE,IAAK,CAACA,EAAE,CAACG,OAAO,CAACF,cAAc,CAAC,CAAC;AACjF;AAEA,SAASG,kBAAkBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EAC5C,OAAO,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC3B,MAAMC,MAAM,GAAGF,SAAS,IAAI;MAAE,IAAIC,OAAO,IAAIF,QAAQ;IAAE,CAAC;IACxDI,MAAM,CAACC,IAAI,CAACN,QAAQ,CAAC,CAACO,OAAO,CAAEhB,GAAG,IAAK;MACnC;MACAa,MAAM,CAACb,GAAG,CAAC,GAAGS,QAAQ,CAACT,GAAG,CAAC;IAC/B,CAAC,CAAC;IACF,OAAOa,MAAM;EACjB,CAAC;AACL;AAEA,MAAMI,KAAK,GAAG;EACVC,GAAG,EAAE,CAAC;EACNC,EAAE,EAAE,CAAC;EACLC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,EAAE,EAAE,CAAC;EACLC,GAAG,EAAE;AACT,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAC9B,IAAI,EAAE+B,cAAc,GAAG,GAAG,EAAE;EAC/C,OAAOT,KAAK,CAACtB,IAAI,CAAC,GAAGsB,KAAK,CAACS,cAAc,CAAC;AAC9C;AAEA,MAAMC,+BAA+B,GAAG,GAAG;AAC3C,SAASC,qBAAqBA,CAACC,KAAK,GAAG3C,MAAM,CAACI,oBAAoB,CAAC,EAAEwC,MAAM,EAAE;EACzE,OAAO;IACHC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;MACJC,QAAQ,EAAEC,cAAc,CAACL,KAAK,CAAC;MAC/BC;IACJ;EACJ,CAAC;AACL;AACA,SAASI,cAAcA,CAACL,KAAK,EAAE;EAC3B,OAAOA,KAAK,IAAIF,+BAA+B,GAAGE,KAAK;AAC3D;;AAEA;AACA;AACA;;AAEA,SAASF,+BAA+B,EAAEpC,oBAAoB,EAAE2C,cAAc,EAAEnC,eAAe,EAAEI,aAAa,EAAEK,kBAAkB,EAAEiB,aAAa,EAAEG,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}