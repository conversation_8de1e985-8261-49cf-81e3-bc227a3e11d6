{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, INJECTOR, EventEmitter, afterNextRender, Input, Output, ContentChildren, forwardRef, ElementRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { RouterLinkActive } from '@angular/router';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { tuiTypedFromEvent, tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement, tuiIsElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiIsNativeFocused, tuiMoveFocus, tuiGetClosestFocusable } from '@taiga-ui/cdk/utils/focus';\nimport * as i1 from '@taiga-ui/core/directives/icons';\nimport { TuiWithIcons } from '@taiga-ui/core/directives/icons';\nimport { filter, merge, EMPTY, switchMap, take, tap, debounceTime, startWith, map } from 'rxjs';\nimport { TuiItem } from '@taiga-ui/cdk/directives/item';\nimport { tuiCreateToken, tuiProvideOptions, tuiWithStyles, tuiPx, tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { __decorate } from 'tslib';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport * as i1$1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { tuiClamp, tuiToInt } from '@taiga-ui/cdk/utils/math';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { tuiDropdownOptionsProvider, TuiDropdown } from '@taiga-ui/core/directives/dropdown';\nimport { TuiChevron } from '@taiga-ui/kit/directives/chevron';\nimport { TUI_MORE_WORD } from '@taiga-ui/kit/tokens';\nimport { PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nfunction TuiTabsWithMore_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiTabsWithMore_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 9);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngTemplateOutlet\", item_r2);\n  }\n}\nfunction TuiTabsWithMore_ng_container_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelementContainer(1, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    const item_r2 = ctx_r2.$implicit;\n    const index_r4 = ctx_r2.index;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"t-overflown\", ctx_r4.isOverflown(index_r4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", item_r2);\n  }\n}\nfunction TuiTabsWithMore_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiTabsWithMore_ng_container_3_ng_container_1_Template, 1, 1, \"ng-container\", 8)(2, TuiTabsWithMore_ng_container_3_ng_template_2_Template, 2, 3, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const index_r4 = ctx.index;\n    const hidden_r6 = i0.ɵɵreference(3);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", index_r4 <= ctx_r4.lastVisibleIndex)(\"ngIfElse\", hidden_r6);\n  }\n}\nfunction TuiTabsWithMore_button_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r8 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r8, \" \");\n  }\n}\nfunction TuiTabsWithMore_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11);\n    i0.ɵɵtwoWayListener(\"tuiDropdownOpenChange\", function TuiTabsWithMore_button_4_Template_button_tuiDropdownOpenChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.open, $event) || (ctx_r4.open = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown.arrowLeft.prevent\", function TuiTabsWithMore_button_4_Template_button_keydown_arrowLeft_prevent_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onArrowLeft());\n    });\n    i0.ɵɵtemplate(1, TuiTabsWithMore_button_4_ng_container_1_Template, 2, 1, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    const dropdown_r9 = i0.ɵɵreference(8);\n    i0.ɵɵclassProp(\"_active\", ctx_r4.isMoreActive)(\"t-no-margin\", ctx_r4.isMoreAlone)(\"t-overflown\", !ctx_r4.isMoreVisible);\n    i0.ɵɵproperty(\"tabIndex\", ctx_r4.isMoreFocusable ? 0 : -1)(\"tuiDropdown\", ctx_r4.dropdownContent || dropdown_r9);\n    i0.ɵɵtwoWayProperty(\"tuiDropdownOpen\", ctx_r4.open);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r4.moreContent);\n  }\n}\nfunction TuiTabsWithMore_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵtwoWayListener(\"tuiDropdownOpenChange\", function TuiTabsWithMore_ng_template_5_Template_button_tuiDropdownOpenChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.open, $event) || (ctx_r4.open = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown.arrowLeft.prevent\", function TuiTabsWithMore_ng_template_5_Template_button_keydown_arrowLeft_prevent_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onArrowLeft());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    const dropdown_r9 = i0.ɵɵreference(8);\n    i0.ɵɵclassProp(\"_active\", ctx_r4.isMoreActive)(\"t-no-margin\", ctx_r4.isMoreAlone)(\"t-overflown\", !ctx_r4.isMoreVisible);\n    i0.ɵɵproperty(\"tabIndex\", ctx_r4.isMoreFocusable ? 0 : -1)(\"tuiDropdown\", ctx_r4.dropdownContent || dropdown_r9);\n    i0.ɵɵtwoWayProperty(\"tuiDropdownOpen\", ctx_r4.open);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 10, ctx_r4.moreWord$), \" \");\n  }\n}\nfunction TuiTabsWithMore_ng_template_7_div_2_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiTabsWithMore_ng_template_7_div_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiTabsWithMore_ng_template_7_div_2_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", item_r15);\n  }\n}\nfunction TuiTabsWithMore_ng_template_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵlistener(\"tui-tab-activate\", function TuiTabsWithMore_ng_template_7_div_2_Template_div_tui_tab_activate_0_listener() {\n      const index_r14 = i0.ɵɵrestoreView(_r13).index;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onClick(index_r14));\n    });\n    i0.ɵɵtemplate(1, TuiTabsWithMore_ng_template_7_div_2_ng_container_1_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const index_r14 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.shouldShow(index_r14));\n  }\n}\nfunction TuiTabsWithMore_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14, 3);\n    i0.ɵɵlistener(\"keydown.arrowDown.prevent\", function TuiTabsWithMore_ng_template_7_Template_div_keydown_arrowDown_prevent_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const element_r12 = i0.ɵɵreference(1);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onWrapperArrow($event, element_r12, false));\n    })(\"keydown.arrowUp.prevent\", function TuiTabsWithMore_ng_template_7_Template_div_keydown_arrowUp_prevent_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const element_r12 = i0.ɵɵreference(1);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onWrapperArrow($event, element_r12, true));\n    });\n    i0.ɵɵtemplate(2, TuiTabsWithMore_ng_template_7_div_2_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-size\", ctx_r4.size);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.items);\n  }\n}\nconst TUI_TAB_ACTIVATE = 'tui-tab-activate';\nclass TuiTab {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.rla = inject(RouterLinkActive, {\n      optional: true\n    });\n    this.observer = this.rla && inject(MutationObserverService, {\n      optional: true\n    })?.pipe(filter(() => !!this.rla?.isActive));\n    this.sub = merge(this.observer || EMPTY, this.rla?.isActiveChange.pipe(filter(Boolean)) || EMPTY, this.el.matches('button') ? tuiTypedFromEvent(this.el, 'click').pipe(\n    // Delaying execution until after all other click callbacks\n    switchMap(() => tuiTypedFromEvent(this.el.parentElement, 'click').pipe(take(1)))) : EMPTY).pipe(takeUntilDestroyed()).subscribe(() => this.el.dispatchEvent(new CustomEvent(TUI_TAB_ACTIVATE, {\n      bubbles: true\n    })));\n  }\n  ngOnDestroy() {\n    if (tuiIsNativeFocused(this.el)) {\n      this.el.blur();\n    }\n  }\n  static {\n    this.ɵfac = function TuiTab_Factory(t) {\n      return new (t || TuiTab)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTab,\n      selectors: [[\"a\", \"tuiTab\", \"\", 3, \"routerLink\", \"\"], [\"a\", \"tuiTab\", \"\", \"routerLink\", \"\", \"routerLinkActive\", \"\"], [\"button\", \"tuiTab\", \"\"]],\n      hostAttrs: [\"type\", \"button\"],\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([i1.TuiWithIcons])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTab, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'a[tuiTab]:not([routerLink]), a[tuiTab][routerLink][routerLinkActive], button[tuiTab]',\n      hostDirectives: [TuiWithIcons],\n      host: {\n        type: 'button'\n      }\n    }]\n  }], null, null);\n})();\nconst TUI_TABS_DEFAULT_OPTIONS = {\n  underline: true,\n  exposeActive: true,\n  itemsLimit: Infinity,\n  minMoreWidth: 0,\n  size: 'l'\n};\n/**\n * Default parameters for Tabs component\n */\nconst TUI_TABS_OPTIONS = tuiCreateToken(TUI_TABS_DEFAULT_OPTIONS);\nfunction tuiTabsOptionsProvider(options) {\n  return tuiProvideOptions(TUI_TABS_OPTIONS, options, TUI_TABS_DEFAULT_OPTIONS);\n}\nclass TuiTabsStyles {\n  static {\n    this.ɵfac = function TuiTabsStyles_Factory(t) {\n      return new (t || TuiTabsStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTabsStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-tabs\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiTabsStyles_Template(rf, ctx) {},\n      styles: [\"[tuiTab]{transition-property:color,box-shadow,opacity,background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;position:relative;display:flex;flex-shrink:0;box-sizing:border-box;justify-content:space-between;line-height:1.5rem;align-items:center;white-space:nowrap;cursor:pointer;outline:none;color:inherit;margin-inline-start:var(--tui-tab-margin, 24px)}[tuiTab]:disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}[tuiTab]._active{color:var(--tui-text-primary);box-shadow:none}[tuiTab]:focus-visible{outline:.125rem solid var(--tui-border-focus);outline-offset:-.125rem}tui-tabs._underline [tuiTab]:hover:not(._active),[tuiTabs]._underline [tuiTab]:hover:not(._active){box-shadow:inset 0 -.125rem var(--tui-border-normal)}tui-tabs>[tuiTab]:first-child,[tuiTabs]>[tuiTab]:first-child,tui-tabs>:not(.t-overflown)>[tuiTab]:first-child{margin-inline-start:0}tui-tabs>[tuiTab]~:not(.t-overflown)>[tuiTab]:first-child{margin-inline-start:var(--tui-tab-margin, 24px)}[tuiTab][tuiIcons]:before{font-size:1rem;margin-inline-end:.5rem}[tuiTab][tuiIcons]:after{font-size:1rem;margin-inline-start:.5rem}[tuiTab]:empty:after,[tuiTab]:empty:before{margin:.5rem}@media (hover: hover) and (pointer: fine){[tuiTab]:hover{color:var(--tui-text-primary)}}[tuiTabs],tui-tabs{scrollbar-width:none;-ms-overflow-style:none;position:relative;display:flex;font:var(--tui-font-text-m);color:var(--tui-text-secondary);box-shadow:inset 0 -1px var(--tui-border-normal);overflow:auto;isolation:isolate}[tuiTabs]::-webkit-scrollbar,tui-tabs::-webkit-scrollbar,[tuiTabs]::-webkit-scrollbar-thumb,tui-tabs::-webkit-scrollbar-thumb{display:none}[tuiTabs][data-size=m],tui-tabs[data-size=m]{font:var(--tui-font-text-s);--tui-tab-margin: 16px}[tuiTabs][data-size=l]:not([data-vertical]),tui-tabs[data-size=l]:not([data-vertical]){block-size:var(--tui-height-l)}[tuiTabs][data-size=m]:not([data-vertical]),tui-tabs[data-size=m]:not([data-vertical]){block-size:var(--tui-height-m)}[tuiTabs]:before,tui-tabs:before{transition-property:width,left;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;left:var(--t-left);bottom:0;block-size:.125rem;inline-size:var(--t-width);background:var(--t-color);animation:tuiPresent 1ms}[tuiTabs]._underline:before,tui-tabs._underline:before{content:\\\"\\\"}tui-tabs[data-vertical],[tuiTabs][data-vertical]{flex-direction:column;box-shadow:inset -1px 0 var(--tui-border-normal)}tui-tabs[data-vertical] [tuiTab],[tuiTabs][data-vertical] [tuiTab]{min-block-size:2.75rem;block-size:auto;white-space:normal;margin:0;text-align:start;padding:.25rem 1.25rem .25rem 0}tui-tabs[data-vertical] [tuiTab]:after,[tuiTabs][data-vertical] [tuiTab]:after{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;top:0;right:0;display:block;block-size:100%;inline-size:.125rem;background:var(--tui-background-accent-1);transform:scaleX(0);transform-origin:right;margin:0}tui-tabs[data-vertical] [tuiTab]:hover,[tuiTabs][data-vertical] [tuiTab]:hover{box-shadow:inset -.125rem 0 var(--tui-border-normal)}tui-tabs[data-vertical] [tuiTab]._active:after,[tuiTabs][data-vertical] [tuiTab]._active:after{transform:none}tui-tabs[data-vertical][data-size=m] [tuiTab],[tuiTabs][data-vertical][data-size=m] [tuiTab]{min-block-size:2.25rem;font:var(--tui-font-text-s)}tui-tabs[data-vertical][data-vertical=right],[tuiTabs][data-vertical][data-vertical=right]{box-shadow:inset 1px 0 var(--tui-border-normal)}tui-tabs[data-vertical][data-vertical=right] [tuiTab],[tuiTabs][data-vertical][data-vertical=right] [tuiTab]{text-align:end;padding:.25rem 0 .25rem 1.25rem}tui-tabs[data-vertical][data-vertical=right] [tuiTab]:after,[tuiTabs][data-vertical][data-vertical=right] [tuiTab]:after{right:auto;left:0;transform-origin:left}tui-tabs[data-vertical][data-vertical=right] [tuiTab]:hover,[tuiTabs][data-vertical][data-vertical=right] [tuiTab]:hover{box-shadow:inset .125rem 0 var(--tui-border-normal)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTabsStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-tabs'\n      },\n      styles: [\"[tuiTab]{transition-property:color,box-shadow,opacity,background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;position:relative;display:flex;flex-shrink:0;box-sizing:border-box;justify-content:space-between;line-height:1.5rem;align-items:center;white-space:nowrap;cursor:pointer;outline:none;color:inherit;margin-inline-start:var(--tui-tab-margin, 24px)}[tuiTab]:disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}[tuiTab]._active{color:var(--tui-text-primary);box-shadow:none}[tuiTab]:focus-visible{outline:.125rem solid var(--tui-border-focus);outline-offset:-.125rem}tui-tabs._underline [tuiTab]:hover:not(._active),[tuiTabs]._underline [tuiTab]:hover:not(._active){box-shadow:inset 0 -.125rem var(--tui-border-normal)}tui-tabs>[tuiTab]:first-child,[tuiTabs]>[tuiTab]:first-child,tui-tabs>:not(.t-overflown)>[tuiTab]:first-child{margin-inline-start:0}tui-tabs>[tuiTab]~:not(.t-overflown)>[tuiTab]:first-child{margin-inline-start:var(--tui-tab-margin, 24px)}[tuiTab][tuiIcons]:before{font-size:1rem;margin-inline-end:.5rem}[tuiTab][tuiIcons]:after{font-size:1rem;margin-inline-start:.5rem}[tuiTab]:empty:after,[tuiTab]:empty:before{margin:.5rem}@media (hover: hover) and (pointer: fine){[tuiTab]:hover{color:var(--tui-text-primary)}}[tuiTabs],tui-tabs{scrollbar-width:none;-ms-overflow-style:none;position:relative;display:flex;font:var(--tui-font-text-m);color:var(--tui-text-secondary);box-shadow:inset 0 -1px var(--tui-border-normal);overflow:auto;isolation:isolate}[tuiTabs]::-webkit-scrollbar,tui-tabs::-webkit-scrollbar,[tuiTabs]::-webkit-scrollbar-thumb,tui-tabs::-webkit-scrollbar-thumb{display:none}[tuiTabs][data-size=m],tui-tabs[data-size=m]{font:var(--tui-font-text-s);--tui-tab-margin: 16px}[tuiTabs][data-size=l]:not([data-vertical]),tui-tabs[data-size=l]:not([data-vertical]){block-size:var(--tui-height-l)}[tuiTabs][data-size=m]:not([data-vertical]),tui-tabs[data-size=m]:not([data-vertical]){block-size:var(--tui-height-m)}[tuiTabs]:before,tui-tabs:before{transition-property:width,left;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;left:var(--t-left);bottom:0;block-size:.125rem;inline-size:var(--t-width);background:var(--t-color);animation:tuiPresent 1ms}[tuiTabs]._underline:before,tui-tabs._underline:before{content:\\\"\\\"}tui-tabs[data-vertical],[tuiTabs][data-vertical]{flex-direction:column;box-shadow:inset -1px 0 var(--tui-border-normal)}tui-tabs[data-vertical] [tuiTab],[tuiTabs][data-vertical] [tuiTab]{min-block-size:2.75rem;block-size:auto;white-space:normal;margin:0;text-align:start;padding:.25rem 1.25rem .25rem 0}tui-tabs[data-vertical] [tuiTab]:after,[tuiTabs][data-vertical] [tuiTab]:after{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;top:0;right:0;display:block;block-size:100%;inline-size:.125rem;background:var(--tui-background-accent-1);transform:scaleX(0);transform-origin:right;margin:0}tui-tabs[data-vertical] [tuiTab]:hover,[tuiTabs][data-vertical] [tuiTab]:hover{box-shadow:inset -.125rem 0 var(--tui-border-normal)}tui-tabs[data-vertical] [tuiTab]._active:after,[tuiTabs][data-vertical] [tuiTab]._active:after{transform:none}tui-tabs[data-vertical][data-size=m] [tuiTab],[tuiTabs][data-vertical][data-size=m] [tuiTab]{min-block-size:2.25rem;font:var(--tui-font-text-s)}tui-tabs[data-vertical][data-vertical=right],[tuiTabs][data-vertical][data-vertical=right]{box-shadow:inset 1px 0 var(--tui-border-normal)}tui-tabs[data-vertical][data-vertical=right] [tuiTab],[tuiTabs][data-vertical][data-vertical=right] [tuiTab]{text-align:end;padding:.25rem 0 .25rem 1.25rem}tui-tabs[data-vertical][data-vertical=right] [tuiTab]:after,[tuiTabs][data-vertical][data-vertical=right] [tuiTab]:after{right:auto;left:0;transform-origin:left}tui-tabs[data-vertical][data-vertical=right] [tuiTab]:hover,[tuiTabs][data-vertical][data-vertical=right] [tuiTab]:hover{box-shadow:inset .125rem 0 var(--tui-border-normal)}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiTabsDirective {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.injector = inject(INJECTOR);\n    this.nothing = tuiWithStyles(TuiTabsStyles);\n    this.size = inject(TUI_TABS_OPTIONS).size;\n    this.activeItemIndex = 0;\n    this.activeItemIndexChange = new EventEmitter();\n  }\n  get tabs() {\n    return Array.from(this.el.querySelectorAll('[tuiTab]'));\n  }\n  get activeElement() {\n    return this.tabs[this.activeItemIndex] || null;\n  }\n  moveFocus(current, step) {\n    const {\n      tabs\n    } = this;\n    tuiMoveFocus(tabs.indexOf(current), tabs, step);\n  }\n  ngAfterViewChecked() {\n    afterNextRender(() => {\n      this.markTabAsActive();\n    }, {\n      injector: this.injector\n    });\n  }\n  onActivate(event, element) {\n    const index = this.tabs.findIndex(tab => tab === element);\n    event.stopPropagation();\n    if (index === this.activeItemIndex) {\n      return;\n    }\n    this.activeItemIndexChange.emit(index);\n    this.activeItemIndex = index;\n  }\n  markTabAsActive() {\n    const {\n      tabs,\n      activeElement\n    } = this;\n    tabs.forEach(nativeElement => {\n      const active = nativeElement === activeElement;\n      nativeElement.classList.toggle('_active', active);\n      nativeElement.setAttribute('tabIndex', active ? '0' : '-1');\n    });\n  }\n  static {\n    this.ɵfac = function TuiTabsDirective_Factory(t) {\n      return new (t || TuiTabsDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTabsDirective,\n      hostVars: 1,\n      hostBindings: function TuiTabsDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"tui-tab-activate\", function TuiTabsDirective_tui_tab_activate_HostBindingHandler($event) {\n            return ctx.onActivate($event, $event.target);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n        }\n      },\n      inputs: {\n        size: \"size\",\n        activeItemIndex: \"activeItemIndex\"\n      },\n      outputs: {\n        activeItemIndexChange: \"activeItemIndexChange\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTabsDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      host: {\n        '[attr.data-size]': 'size',\n        [`(${TUI_TAB_ACTIVATE})`]: 'onActivate($event, $event.target)'\n      }\n    }]\n  }], null, {\n    size: [{\n      type: Input\n    }],\n    activeItemIndex: [{\n      type: Input\n    }],\n    activeItemIndexChange: [{\n      type: Output\n    }]\n  });\n})();\nclass TuiTabsHorizontal {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.options = inject(TUI_TABS_OPTIONS);\n    this.tabs = inject(TuiTabsDirective);\n    this.children = EMPTY_QUERY;\n    this.sub = inject(MutationObserverService, {\n      self: true\n    }).pipe(tuiZonefree(), takeUntilDestroyed()).subscribe(() => this.refresh());\n    this.underline = this.options.underline;\n  }\n  ngAfterViewChecked() {\n    this.scrollTo(this.tabs.activeItemIndex);\n    this.refresh();\n  }\n  onKeyDownArrow(current, step) {\n    this.tabs.moveFocus(current, step);\n  }\n  refresh() {\n    const {\n      activeElement\n    } = this.tabs;\n    if (activeElement && !activeElement.isConnected) {\n      return;\n    }\n    const {\n      offsetLeft = 0,\n      offsetWidth = 0\n    } = activeElement || {};\n    this.el.style.setProperty('--t-left', tuiPx(offsetLeft));\n    this.el.style.setProperty('--t-width', tuiPx(offsetWidth));\n  }\n  scrollTo(index) {\n    const element = this.tabs.tabs[index];\n    if (!element) {\n      return;\n    }\n    const {\n      offsetLeft,\n      offsetWidth\n    } = element;\n    if (offsetLeft < this.el.scrollLeft) {\n      this.el.scrollLeft = offsetLeft;\n    }\n    if (offsetLeft + offsetWidth > this.el.scrollLeft + this.el.offsetWidth) {\n      this.el.scrollLeft = offsetLeft + offsetWidth - this.el.offsetWidth;\n    }\n  }\n  static {\n    this.ɵfac = function TuiTabsHorizontal_Factory(t) {\n      return new (t || TuiTabsHorizontal)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTabsHorizontal,\n      selectors: [[\"tui-tabs\", 3, \"vertical\", \"\"], [\"nav\", \"tuiTabs\", \"\", 3, \"vertical\", \"\"]],\n      contentQueries: function TuiTabsHorizontal_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiTab, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.children = _t);\n        }\n      },\n      hostVars: 4,\n      hostBindings: function TuiTabsHorizontal_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"animationend\", function TuiTabsHorizontal_animationend_HostBindingHandler() {\n            return ctx.refresh();\n          })(\"keydown.arrowRight.prevent\", function TuiTabsHorizontal_keydown_arrowRight_prevent_HostBindingHandler($event) {\n            return ctx.onKeyDownArrow($event.target, 1);\n          })(\"keydown.arrowLeft.prevent\", function TuiTabsHorizontal_keydown_arrowLeft_prevent_HostBindingHandler($event) {\n            return ctx.onKeyDownArrow($event.target, -1);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-color\", ctx.underline === true ? \"var(--tui-background-accent-1)\" : ctx.underline);\n          i0.ɵɵclassProp(\"_underline\", ctx.underline);\n        }\n      },\n      inputs: {\n        underline: \"underline\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MutationObserverService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          childList: true,\n          characterData: true,\n          subtree: true\n        }\n      }]), i0.ɵɵHostDirectivesFeature([{\n        directive: TuiTabsDirective,\n        inputs: [\"activeItemIndex\", \"activeItemIndex\", \"size\", \"size\"],\n        outputs: [\"activeItemIndexChange\", \"activeItemIndexChange\"]\n      }])]\n    });\n  }\n}\n__decorate([tuiPure], TuiTabsHorizontal.prototype, \"scrollTo\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTabsHorizontal, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'tui-tabs:not([vertical]), nav[tuiTabs]:not([vertical])',\n      providers: [MutationObserverService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          childList: true,\n          characterData: true,\n          subtree: true\n        }\n      }],\n      hostDirectives: [{\n        directive: TuiTabsDirective,\n        inputs: ['activeItemIndex', 'size'],\n        outputs: ['activeItemIndexChange']\n      }],\n      host: {\n        '[class._underline]': 'underline',\n        '[style.--t-color]': \"underline === true ? 'var(--tui-background-accent-1)' : underline\",\n        '(animationend)': 'refresh()',\n        '(keydown.arrowRight.prevent)': 'onKeyDownArrow($event.target, 1)',\n        '(keydown.arrowLeft.prevent)': 'onKeyDownArrow($event.target, -1)'\n      }\n    }]\n  }], null, {\n    children: [{\n      type: ContentChildren,\n      args: [forwardRef(() => TuiTab)]\n    }],\n    underline: [{\n      type: Input\n    }],\n    scrollTo: []\n  });\n})();\nclass TuiTabsVertical {\n  constructor() {\n    this.tabs = inject(TuiTabsDirective);\n    this.vertical = 'left';\n  }\n  onKeyDownArrow(current, step) {\n    this.tabs.moveFocus(current, step);\n  }\n  static {\n    this.ɵfac = function TuiTabsVertical_Factory(t) {\n      return new (t || TuiTabsVertical)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTabsVertical,\n      selectors: [[\"tui-tabs\", \"vertical\", \"\"], [\"nav\", \"tuiTabs\", \"\", \"vertical\", \"\"]],\n      hostVars: 1,\n      hostBindings: function TuiTabsVertical_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.arrowDown.prevent\", function TuiTabsVertical_keydown_arrowDown_prevent_HostBindingHandler($event) {\n            return ctx.onKeyDownArrow($event.target, 1);\n          })(\"keydown.arrowUp.prevent\", function TuiTabsVertical_keydown_arrowUp_prevent_HostBindingHandler($event) {\n            return ctx.onKeyDownArrow($event.target, -1);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-vertical\", ctx.vertical);\n        }\n      },\n      inputs: {\n        vertical: \"vertical\"\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([{\n        directive: TuiTabsDirective,\n        inputs: [\"activeItemIndex\", \"activeItemIndex\", \"size\", \"size\"],\n        outputs: [\"activeItemIndexChange\", \"activeItemIndexChange\"]\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTabsVertical, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'tui-tabs[vertical], nav[tuiTabs][vertical]',\n      hostDirectives: [{\n        directive: TuiTabsDirective,\n        inputs: ['activeItemIndex', 'size'],\n        outputs: ['activeItemIndexChange']\n      }],\n      host: {\n        '[attr.data-vertical]': 'vertical',\n        '(keydown.arrowDown.prevent)': 'onKeyDownArrow($event.target, 1)',\n        '(keydown.arrowUp.prevent)': 'onKeyDownArrow($event.target, -1)'\n      }\n    }]\n  }], null, {\n    vertical: [{\n      type: Input\n    }]\n  });\n})();\nconst TUI_TABS_REFRESH = tuiCreateToken();\nconst TUI_TABS_PROVIDERS = [ResizeObserverService, MutationObserverService, tuiDropdownOptionsProvider({\n  align: 'right'\n}), {\n  provide: WA_MUTATION_OBSERVER_INIT,\n  useValue: {\n    childList: true,\n    subtree: true,\n    characterData: true\n  }\n}, {\n  provide: TUI_TABS_REFRESH,\n  deps: [ResizeObserverService, MutationObserverService, DOCUMENT, ElementRef, ChangeDetectorRef],\n  useFactory: (resize$, mutations$, {\n    body\n  }, {\n    nativeElement\n  }, cdr) => merge(resize$, mutations$.pipe(tap(() => cdr.detectChanges()))).pipe(\n  // Ignoring cases when host is detached from DOM\n  filter(() => body.contains(nativeElement)), debounceTime(0), startWith(null), takeUntilDestroyed())\n}];\nclass TuiTabsWithMore {\n  constructor() {\n    this.options = inject(TUI_TABS_OPTIONS);\n    this.refresh$ = inject(TUI_TABS_REFRESH);\n    this.el = tuiInjectElement();\n    this.cdr = inject(ChangeDetectorRef);\n    this.maxIndex = Infinity;\n    this.items = EMPTY_QUERY;\n    this.moreWord$ = inject(TUI_MORE_WORD);\n    this.open = false;\n    this.size = this.options.size;\n    this.underline = this.options.underline;\n    this.itemsLimit = this.options.itemsLimit;\n    this.activeItemIndexChange = new EventEmitter();\n    this.activeItemIndex = 0;\n  }\n  set itemIndex(activeItemIndex) {\n    this.activeItemIndex = activeItemIndex;\n    this.maxIndex = this.getMaxIndex();\n  }\n  get lastVisibleIndex() {\n    if (this.itemsLimit + 1 >= this.items.length) {\n      return this.maxIndex;\n    }\n    const offset = this.itemsLimit - 1 > this.activeItemIndex || !this.options.exposeActive ? 1 : 2;\n    return Math.min(this.itemsLimit - offset, this.maxIndex);\n  }\n  ngAfterViewInit() {\n    this.refresh$.pipe(map(() => this.getMaxIndex()), tap(() => this.refresh()), filter(maxIndex => this.maxIndex !== maxIndex)).subscribe(maxIndex => {\n      this.maxIndex = maxIndex;\n      this.cdr.detectChanges();\n    });\n  }\n  ngAfterViewChecked() {\n    this.refresh();\n  }\n  // TODO: Improve performance\n  get tabs() {\n    return Array.from(this.el.querySelectorAll('[tuiTab]'));\n  }\n  get activeElement() {\n    const {\n      tabs\n    } = this;\n    const safeActiveIndex = tuiClamp(this.activeItemIndex || 0, 0, tabs.length - 2);\n    return this.options.exposeActive || this.lastVisibleIndex >= safeActiveIndex ? tabs[safeActiveIndex] || null : this.moreButton?.nativeElement || null;\n  }\n  get isMoreAlone() {\n    return this.lastVisibleIndex < 0 && !this.options.exposeActive;\n  }\n  get isMoreVisible() {\n    return this.lastVisibleIndex < this.items.length - 1;\n  }\n  get isMoreFocusable() {\n    return !!this.moreButton && tuiIsNativeFocused(this.moreButton.nativeElement);\n  }\n  get isMoreActive() {\n    return this.open || !this.options.exposeActive && this.lastVisibleIndex < this.activeItemIndex;\n  }\n  onActiveItemIndexChange(activeItemIndex) {\n    this.updateActiveItemIndex(activeItemIndex);\n  }\n  onClick(index) {\n    this.open = false;\n    this.focusMore();\n    this.updateActiveItemIndex(index);\n  }\n  onArrowRight(event) {\n    if (tuiIsElement(event.target) && tuiIsNativeFocused(event.target)) {\n      this.focusMore();\n    }\n  }\n  onArrowLeft() {\n    const {\n      tabs\n    } = this;\n    let index = tabs.length - 2;\n    while (index >= 0) {\n      tabs[index]?.focus();\n      if (tuiIsNativeFocused(tabs[index])) {\n        return;\n      }\n      index--;\n    }\n  }\n  onWrapperArrow(event, wrapper, previous) {\n    const button = event.target;\n    const target = tuiGetClosestFocusable({\n      initial: button,\n      root: wrapper,\n      previous\n    });\n    if (target) {\n      target.focus();\n    }\n  }\n  isOverflown(index) {\n    return index !== this.activeItemIndex || !this.options.exposeActive;\n  }\n  shouldShow(index) {\n    return index > this.lastVisibleIndex && this.isOverflown(index);\n  }\n  get margin() {\n    return this.size === 'l' ? 24 : 16;\n  }\n  focusMore() {\n    if (this.moreButton) {\n      this.moreButton.nativeElement.focus();\n    }\n  }\n  getMaxIndex() {\n    const {\n      tabs,\n      activeItemIndex,\n      margin\n    } = this;\n    if (tabs.length < 2) {\n      return 0;\n    }\n    const {\n      exposeActive,\n      minMoreWidth\n    } = this.options;\n    const {\n      clientWidth\n    } = this.el;\n    const active = tabs[activeItemIndex];\n    const activeWidth = active?.scrollWidth ?? 0;\n    const moreWidth = Math.max(tabs[tabs.length - 1]?.scrollWidth ?? 0, minMoreWidth);\n    let maxIndex = tabs.length - 2;\n    let total = tabs.reduce((acc, {\n      scrollWidth\n    }) => acc + scrollWidth, 0) + maxIndex * margin - (tabs[tabs.length - 1]?.scrollWidth ?? 0);\n    if (Number.isNaN(total) || total <= clientWidth) {\n      return Infinity;\n    }\n    while (maxIndex) {\n      total -= (tabs[maxIndex]?.scrollWidth ?? 0) + margin;\n      maxIndex--;\n      const activeDisplaced = exposeActive && activeItemIndex > maxIndex;\n      const activeOffset = activeDisplaced ? activeWidth + margin : 0;\n      const currentWidth = total + activeOffset + moreWidth + margin;\n      // Needed for different rounding of visible and hidden elements scrollWidth\n      const safetyOffset = tuiToInt(this.maxIndex === maxIndex - 1);\n      if (currentWidth + safetyOffset < clientWidth) {\n        return maxIndex;\n      }\n    }\n    return -1;\n  }\n  updateActiveItemIndex(activeItemIndex) {\n    this.itemIndex = activeItemIndex;\n    this.activeItemIndexChange.emit(activeItemIndex);\n  }\n  refresh() {\n    const {\n      offsetLeft = 0,\n      offsetWidth = 0\n    } = this.activeElement || {};\n    this.dir?.nativeElement.style.setProperty('--t-left', tuiPx(offsetLeft));\n    this.dir?.nativeElement.style.setProperty('--t-width', tuiPx(offsetWidth));\n  }\n  static {\n    this.ɵfac = function TuiTabsWithMore_Factory(t) {\n      return new (t || TuiTabsWithMore)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTabsWithMore,\n      selectors: [[\"tui-tabs-with-more\"], [\"nav\", \"tuiTabsWithMore\", \"\"]],\n      contentQueries: function TuiTabsWithMore_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiItem, 4, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      viewQuery: function TuiTabsWithMore_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TuiTab, 5, ElementRef);\n          i0.ɵɵviewQuery(TuiTabsHorizontal, 5, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.moreButton = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dir = _t.first);\n        }\n      },\n      hostVars: 1,\n      hostBindings: function TuiTabsWithMore_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n        }\n      },\n      inputs: {\n        size: \"size\",\n        moreContent: \"moreContent\",\n        dropdownContent: \"dropdownContent\",\n        underline: \"underline\",\n        itemsLimit: \"itemsLimit\",\n        itemIndex: [i0.ɵɵInputFlags.None, \"activeItemIndex\", \"itemIndex\"]\n      },\n      outputs: {\n        activeItemIndexChange: \"activeItemIndexChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature(TUI_TABS_PROVIDERS), i0.ɵɵStandaloneFeature],\n      decls: 9,\n      vars: 9,\n      consts: [[\"chevron\", \"\"], [\"dropdown\", \"\"], [\"hidden\", \"\"], [\"element\", \"\"], [4, \"ngIf\"], [1, \"t-tabs\", 3, \"activeItemIndexChange\", \"keydown.arrowRight\", \"activeItemIndex\", \"size\", \"underline\"], [4, \"ngFor\", \"ngForOf\"], [\"tuiTab\", \"\", \"type\", \"button\", \"class\", \"t-more\", 3, \"_active\", \"t-no-margin\", \"t-overflown\", \"tabIndex\", \"tuiDropdown\", \"tuiDropdownOpen\", \"tuiDropdownOpenChange\", \"keydown.arrowLeft.prevent\", 4, \"ngIf\", \"ngIfElse\"], [3, \"ngTemplateOutlet\", 4, \"ngIf\", \"ngIfElse\"], [3, \"ngTemplateOutlet\"], [1, \"t-flex\"], [\"tuiTab\", \"\", \"type\", \"button\", 1, \"t-more\", 3, \"tuiDropdownOpenChange\", \"keydown.arrowLeft.prevent\", \"tabIndex\", \"tuiDropdown\", \"tuiDropdownOpen\"], [4, \"polymorpheusOutlet\"], [\"tuiChevron\", \"\", \"tuiTab\", \"\", \"type\", \"button\", 1, \"t-more\", 3, \"tuiDropdownOpenChange\", \"keydown.arrowLeft.prevent\", \"tabIndex\", \"tuiDropdown\", \"tuiDropdownOpen\"], [1, \"t-dropdown\", 3, \"keydown.arrowDown.prevent\", \"keydown.arrowUp.prevent\"], [\"class\", \"t-dropdown-item\", 3, \"tui-tab-activate\", 4, \"ngFor\", \"ngForOf\"], [1, \"t-dropdown-item\", 3, \"tui-tab-activate\"]],\n      template: function TuiTabsWithMore_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, TuiTabsWithMore_ng_container_0_Template, 1, 0, \"ng-container\", 4);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelementStart(2, \"tui-tabs\", 5);\n          i0.ɵɵlistener(\"activeItemIndexChange\", function TuiTabsWithMore_Template_tui_tabs_activeItemIndexChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onActiveItemIndexChange($event));\n          })(\"keydown.arrowRight\", function TuiTabsWithMore_Template_tui_tabs_keydown_arrowRight_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onArrowRight($event));\n          });\n          i0.ɵɵtemplate(3, TuiTabsWithMore_ng_container_3_Template, 4, 2, \"ng-container\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, TuiTabsWithMore_button_4_Template, 2, 10, \"button\", 7)(5, TuiTabsWithMore_ng_template_5_Template, 3, 12, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(7, TuiTabsWithMore_ng_template_7_Template, 3, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const chevron_r16 = i0.ɵɵreference(6);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 7, ctx.items.changes));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"activeItemIndex\", ctx.activeItemIndex)(\"size\", ctx.size)(\"underline\", ctx.underline);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.moreContent)(\"ngIfElse\", chevron_r16);\n        }\n      },\n      dependencies: [CommonModule, i1$1.NgForOf, i1$1.NgIf, i1$1.NgTemplateOutlet, i1$1.AsyncPipe, PolymorpheusOutlet, TuiChevron, i2.TuiDropdownDirective, i2.TuiDropdownOpen, TuiTab, TuiTabsHorizontal],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:flex;font:var(--tui-font-text-m);box-sizing:border-box;color:var(--tui-text-secondary);box-shadow:inset 0 -1px var(--tui-border-normal);overflow:hidden}[data-size=m][_nghost-%COMP%]{font:var(--tui-font-text-s)}.t-tabs[_ngcontent-%COMP%]{block-size:inherit;font:inherit;overflow:visible;box-shadow:none;color:inherit}.t-flex[_ngcontent-%COMP%]{display:flex}.t-overflown[_ngcontent-%COMP%]{margin:0;inline-size:0;max-inline-size:0;overflow:hidden;visibility:hidden}.t-icon[_ngcontent-%COMP%]{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-right:-.25rem;vertical-align:bottom}.t-icon_rotated[_ngcontent-%COMP%]{transform:rotate(180deg)}.t-dropdown[_ngcontent-%COMP%]{padding:.25rem 0}.t-dropdown[_ngcontent-%COMP%]     *[tuiTab]{inline-size:calc(100% - .75rem);block-size:2.75rem;justify-content:flex-start;margin:.125rem .375rem;padding:0 .625rem;line-height:1.5rem;border-radius:var(--tui-radius-s);font:var(--tui-font-text-m);color:var(--tui-text-primary)}.t-dropdown[_ngcontent-%COMP%]     *[tuiTab]:before{display:none}.t-dropdown[_ngcontent-%COMP%]     *[tuiTab]:hover, .t-dropdown[_ngcontent-%COMP%]     *[tuiTab]:focus, .t-dropdown[_ngcontent-%COMP%]     *[tuiTab]._active{box-shadow:none;outline:none;background:var(--tui-background-neutral-1)}.t-dropdown[data-size=m][_ngcontent-%COMP%]     *[tuiTab]{block-size:2.25rem;font:var(--tui-font-text-s)}.t-dropdown-item[_ngcontent-%COMP%]{display:flex;flex-direction:column}.t-no-margin[_ngcontent-%COMP%]{margin-left:0}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTabsWithMore, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-tabs-with-more, nav[tuiTabsWithMore]',\n      imports: [CommonModule, PolymorpheusOutlet, TuiChevron, TuiDropdown, TuiTab, TuiTabsHorizontal],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: TUI_TABS_PROVIDERS,\n      host: {\n        '[attr.data-size]': 'size'\n      },\n      template: \"<ng-container *ngIf=\\\"items.changes | async\\\" />\\n<tui-tabs\\n    class=\\\"t-tabs\\\"\\n    [activeItemIndex]=\\\"activeItemIndex\\\"\\n    [size]=\\\"size\\\"\\n    [underline]=\\\"underline\\\"\\n    (activeItemIndexChange)=\\\"onActiveItemIndexChange($event)\\\"\\n    (keydown.arrowRight)=\\\"onArrowRight($event)\\\"\\n>\\n    <ng-container *ngFor=\\\"let item of items; let index = index\\\">\\n        <ng-container\\n            *ngIf=\\\"index <= lastVisibleIndex; else hidden\\\"\\n            [ngTemplateOutlet]=\\\"item\\\"\\n        />\\n        <ng-template #hidden>\\n            <div\\n                class=\\\"t-flex\\\"\\n                [class.t-overflown]=\\\"isOverflown(index)\\\"\\n            >\\n                <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n            </div>\\n        </ng-template>\\n    </ng-container>\\n</tui-tabs>\\n\\n<button\\n    *ngIf=\\\"moreContent; else chevron\\\"\\n    tuiTab\\n    type=\\\"button\\\"\\n    class=\\\"t-more\\\"\\n    [class._active]=\\\"isMoreActive\\\"\\n    [class.t-no-margin]=\\\"isMoreAlone\\\"\\n    [class.t-overflown]=\\\"!isMoreVisible\\\"\\n    [tabIndex]=\\\"isMoreFocusable ? 0 : -1\\\"\\n    [tuiDropdown]=\\\"dropdownContent || dropdown\\\"\\n    [(tuiDropdownOpen)]=\\\"open\\\"\\n    (keydown.arrowLeft.prevent)=\\\"onArrowLeft()\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"moreContent as text\\\">\\n        {{ text }}\\n    </ng-container>\\n</button>\\n<ng-template #chevron>\\n    <button\\n        tuiChevron\\n        tuiTab\\n        type=\\\"button\\\"\\n        class=\\\"t-more\\\"\\n        [class._active]=\\\"isMoreActive\\\"\\n        [class.t-no-margin]=\\\"isMoreAlone\\\"\\n        [class.t-overflown]=\\\"!isMoreVisible\\\"\\n        [tabIndex]=\\\"isMoreFocusable ? 0 : -1\\\"\\n        [tuiDropdown]=\\\"dropdownContent || dropdown\\\"\\n        [(tuiDropdownOpen)]=\\\"open\\\"\\n        (keydown.arrowLeft.prevent)=\\\"onArrowLeft()\\\"\\n    >\\n        {{ moreWord$ | async }}\\n    </button>\\n</ng-template>\\n<ng-template #dropdown>\\n    <div\\n        #element\\n        class=\\\"t-dropdown\\\"\\n        [attr.data-size]=\\\"size\\\"\\n        (keydown.arrowDown.prevent)=\\\"onWrapperArrow($event, element, false)\\\"\\n        (keydown.arrowUp.prevent)=\\\"onWrapperArrow($event, element, true)\\\"\\n    >\\n        <div\\n            *ngFor=\\\"let item of items; let index = index\\\"\\n            class=\\\"t-dropdown-item\\\"\\n            (tui-tab-activate)=\\\"onClick(index)\\\"\\n        >\\n            <ng-container *ngIf=\\\"shouldShow(index)\\\">\\n                <ng-container *polymorpheusOutlet=\\\"item\\\" />\\n            </ng-container>\\n        </div>\\n    </div>\\n</ng-template>\\n\",\n      styles: [\":host{position:relative;display:flex;font:var(--tui-font-text-m);box-sizing:border-box;color:var(--tui-text-secondary);box-shadow:inset 0 -1px var(--tui-border-normal);overflow:hidden}:host[data-size=m]{font:var(--tui-font-text-s)}.t-tabs{block-size:inherit;font:inherit;overflow:visible;box-shadow:none;color:inherit}.t-flex{display:flex}.t-overflown{margin:0;inline-size:0;max-inline-size:0;overflow:hidden;visibility:hidden}.t-icon{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-right:-.25rem;vertical-align:bottom}.t-icon_rotated{transform:rotate(180deg)}.t-dropdown{padding:.25rem 0}.t-dropdown ::ng-deep *[tuiTab]{inline-size:calc(100% - .75rem);block-size:2.75rem;justify-content:flex-start;margin:.125rem .375rem;padding:0 .625rem;line-height:1.5rem;border-radius:var(--tui-radius-s);font:var(--tui-font-text-m);color:var(--tui-text-primary)}.t-dropdown ::ng-deep *[tuiTab]:before{display:none}.t-dropdown ::ng-deep *[tuiTab]:hover,.t-dropdown ::ng-deep *[tuiTab]:focus,.t-dropdown ::ng-deep *[tuiTab]._active{box-shadow:none;outline:none;background:var(--tui-background-neutral-1)}.t-dropdown[data-size=m] ::ng-deep *[tuiTab]{block-size:2.25rem;font:var(--tui-font-text-s)}.t-dropdown-item{display:flex;flex-direction:column}.t-no-margin{margin-left:0}\\n\"]\n    }]\n  }], null, {\n    moreButton: [{\n      type: ViewChild,\n      args: [TuiTab, {\n        read: ElementRef\n      }]\n    }],\n    dir: [{\n      type: ViewChild,\n      args: [TuiTabsHorizontal, {\n        read: ElementRef\n      }]\n    }],\n    items: [{\n      type: ContentChildren,\n      args: [TuiItem, {\n        read: TemplateRef\n      }]\n    }],\n    size: [{\n      type: Input\n    }],\n    moreContent: [{\n      type: Input\n    }],\n    dropdownContent: [{\n      type: Input\n    }],\n    underline: [{\n      type: Input\n    }],\n    itemsLimit: [{\n      type: Input\n    }],\n    activeItemIndexChange: [{\n      type: Output\n    }],\n    itemIndex: [{\n      type: Input,\n      args: ['activeItemIndex']\n    }]\n  });\n})();\nconst TuiTabs = [TuiItem, TuiTab, TuiTabsDirective, TuiTabsHorizontal, TuiTabsVertical, TuiTabsWithMore];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_TABS_DEFAULT_OPTIONS, TUI_TABS_OPTIONS, TUI_TABS_PROVIDERS, TUI_TABS_REFRESH, TUI_TAB_ACTIVATE, TuiTab, TuiTabs, TuiTabsDirective, TuiTabsHorizontal, TuiTabsVertical, TuiTabsWithMore, tuiTabsOptionsProvider };", "map": {"version": 3, "names": ["i0", "inject", "Directive", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "INJECTOR", "EventEmitter", "afterNextRender", "Input", "Output", "ContentChildren", "forwardRef", "ElementRef", "ChangeDetectorRef", "TemplateRef", "ViewChild", "takeUntilDestroyed", "RouterLinkActive", "MutationObserverService", "WA_MUTATION_OBSERVER_INIT", "tuiTypedFromEvent", "tuiZonefree", "tuiInjectElement", "tuiIsElement", "tuiIsNativeFocused", "tuiMoveFocus", "tuiGetClosestFocusable", "i1", "TuiWithIcons", "filter", "merge", "EMPTY", "switchMap", "take", "tap", "debounceTime", "startWith", "map", "TuiItem", "tuiCreateToken", "tuiProvideOptions", "tuiWithStyles", "tuiPx", "tuiPure", "__decorate", "EMPTY_QUERY", "i1$1", "DOCUMENT", "CommonModule", "tui<PERSON><PERSON>", "tuiToInt", "i2", "tuiDropdownOptionsProvider", "TuiDropdown", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TUI_MORE_WORD", "Polymorpheus<PERSON><PERSON>let", "ResizeObserverService", "TuiTabsWithMore_ng_container_0_Template", "rf", "ctx", "ɵɵelementContainer", "TuiTabsWithMore_ng_container_3_ng_container_1_Template", "item_r2", "ɵɵnextContext", "$implicit", "ɵɵproperty", "TuiTabsWithMore_ng_container_3_ng_template_2_Template", "ɵɵelementStart", "ɵɵelementEnd", "ctx_r2", "index_r4", "index", "ctx_r4", "ɵɵclassProp", "isOverflown", "ɵɵadvance", "TuiTabsWithMore_ng_container_3_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵtemplateRefExtractor", "ɵɵelementContainerEnd", "hidden_r6", "ɵɵreference", "lastVisibleIndex", "TuiTabsWithMore_button_4_ng_container_1_Template", "ɵɵtext", "text_r8", "polymorpheusOutlet", "ɵɵtextInterpolate1", "TuiTabsWithMore_button_4_Template", "_r7", "ɵɵgetCurrentView", "ɵɵtwoWayListener", "TuiTabsWithMore_button_4_Template_button_tuiDropdownOpenChange_0_listener", "$event", "ɵɵrestoreView", "ɵɵtwoWayBindingSet", "open", "ɵɵresetView", "ɵɵlistener", "TuiTabsWithMore_button_4_Template_button_keydown_arrowLeft_prevent_0_listener", "onArrowLeft", "dropdown_r9", "isMoreActive", "isMoreAlone", "isMoreVisible", "isMoreFocusable", "dropdownContent", "ɵɵtwoWayProperty", "moreContent", "TuiTabsWithMore_ng_template_5_Template", "_r10", "TuiTabsWithMore_ng_template_5_Template_button_tuiDropdownOpenChange_0_listener", "TuiTabsWithMore_ng_template_5_Template_button_keydown_arrowLeft_prevent_0_listener", "ɵɵpipe", "ɵɵpipeBind1", "moreWord$", "TuiTabsWithMore_ng_template_7_div_2_ng_container_1_ng_container_1_Template", "TuiTabsWithMore_ng_template_7_div_2_ng_container_1_Template", "item_r15", "TuiTabsWithMore_ng_template_7_div_2_Template", "_r13", "TuiTabsWithMore_ng_template_7_div_2_Template_div_tui_tab_activate_0_listener", "index_r14", "onClick", "shouldShow", "TuiTabsWithMore_ng_template_7_Template", "_r11", "TuiTabsWithMore_ng_template_7_Template_div_keydown_arrowDown_prevent_0_listener", "element_r12", "onWrapperArrow", "TuiTabsWithMore_ng_template_7_Template_div_keydown_arrowUp_prevent_0_listener", "ɵɵattribute", "size", "items", "TUI_TAB_ACTIVATE", "TuiTab", "constructor", "el", "rla", "optional", "observer", "pipe", "isActive", "sub", "isActiveChange", "Boolean", "matches", "parentElement", "subscribe", "dispatchEvent", "CustomEvent", "bubbles", "ngOnDestroy", "blur", "ɵfac", "TuiTab_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵHostDirectivesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "hostDirectives", "host", "TUI_TABS_DEFAULT_OPTIONS", "underline", "exposeActive", "itemsLimit", "Infinity", "minMore<PERSON>idth", "TUI_TABS_OPTIONS", "tuiTabsOptionsProvider", "options", "TuiTabsStyles", "TuiTabsStyles_Factory", "ɵcmp", "ɵɵdefineComponent", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiTabsStyles_Template", "styles", "encapsulation", "changeDetection", "None", "OnPush", "class", "TuiTabsDirective", "injector", "nothing", "activeItemIndex", "activeItemIndexChange", "tabs", "Array", "from", "querySelectorAll", "activeElement", "moveFocus", "current", "step", "indexOf", "ngAfterViewChecked", "markTabAsActive", "onActivate", "event", "element", "findIndex", "tab", "stopPropagation", "emit", "for<PERSON>ach", "nativeElement", "active", "classList", "toggle", "setAttribute", "TuiTabsDirective_Factory", "hostVars", "hostBindings", "TuiTabsDirective_HostBindings", "TuiTabsDirective_tui_tab_activate_HostBindingHandler", "target", "inputs", "outputs", "TuiTabsHorizontal", "children", "self", "refresh", "scrollTo", "onKeyDownArrow", "isConnected", "offsetLeft", "offsetWidth", "style", "setProperty", "scrollLeft", "TuiTabsHorizontal_Factory", "contentQueries", "TuiTabsHorizontal_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "TuiTabsHorizontal_HostBindings", "TuiTabsHorizontal_animationend_HostBindingHandler", "TuiTabsHorizontal_keydown_arrowRight_prevent_HostBindingHandler", "TuiTabsHorizontal_keydown_arrowLeft_prevent_HostBindingHandler", "ɵɵstyleProp", "ɵɵProvidersFeature", "provide", "useValue", "childList", "characterData", "subtree", "directive", "prototype", "providers", "TuiTabsVertical", "vertical", "TuiTabsVertical_Factory", "TuiTabsVertical_HostBindings", "TuiTabsVertical_keydown_arrowDown_prevent_HostBindingHandler", "TuiTabsVertical_keydown_arrowUp_prevent_HostBindingHandler", "TUI_TABS_REFRESH", "TUI_TABS_PROVIDERS", "align", "deps", "useFactory", "resize$", "mutations$", "body", "cdr", "detectChanges", "contains", "TuiTabsWithMore", "refresh$", "maxIndex", "itemIndex", "getMaxIndex", "length", "offset", "Math", "min", "ngAfterViewInit", "safeActiveIndex", "moreButton", "onActiveItemIndexChange", "updateActiveItemIndex", "focusMore", "onArrowRight", "focus", "wrapper", "previous", "button", "initial", "root", "margin", "clientWidth", "activeWidth", "scrollWidth", "moreWidth", "max", "total", "reduce", "acc", "Number", "isNaN", "activeDisplaced", "activeOffset", "currentWidth", "safetyOffset", "dir", "TuiTabsWithMore_Factory", "TuiTabsWithMore_ContentQueries", "viewQuery", "TuiTabsWithMore_Query", "ɵɵviewQuery", "first", "TuiTabsWithMore_HostBindings", "ɵɵInputFlags", "consts", "TuiTabsWithMore_Template", "_r1", "TuiTabsWithMore_Template_tui_tabs_activeItemIndexChange_2_listener", "TuiTabsWithMore_Template_tui_tabs_keydown_arrowRight_2_listener", "chevron_r16", "changes", "dependencies", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "AsyncPipe", "TuiDropdownDirective", "TuiDropdownOpen", "imports", "read", "TuiTabs"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-tabs.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, INJECTOR, EventEmitter, afterNextRender, Input, Output, ContentChildren, forwardRef, ElementRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { RouterLinkActive } from '@angular/router';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { tuiTypedFromEvent, tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement, tuiIsElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiIsNativeFocused, tuiMoveFocus, tuiGetClosestFocusable } from '@taiga-ui/cdk/utils/focus';\nimport * as i1 from '@taiga-ui/core/directives/icons';\nimport { TuiWithIcons } from '@taiga-ui/core/directives/icons';\nimport { filter, merge, EMPTY, switchMap, take, tap, debounceTime, startWith, map } from 'rxjs';\nimport { TuiItem } from '@taiga-ui/cdk/directives/item';\nimport { tuiCreateToken, tuiProvideOptions, tuiWithStyles, tuiPx, tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { __decorate } from 'tslib';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport * as i1$1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { tuiClamp, tuiToInt } from '@taiga-ui/cdk/utils/math';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { tuiDropdownOptionsProvider, TuiDropdown } from '@taiga-ui/core/directives/dropdown';\nimport { TuiChevron } from '@taiga-ui/kit/directives/chevron';\nimport { TUI_MORE_WORD } from '@taiga-ui/kit/tokens';\nimport { PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\n\nconst TUI_TAB_ACTIVATE = 'tui-tab-activate';\nclass TuiTab {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.rla = inject(RouterLinkActive, { optional: true });\n        this.observer = this.rla &&\n            inject(MutationObserverService, { optional: true })?.pipe(filter(() => !!this.rla?.isActive));\n        this.sub = merge(this.observer || EMPTY, this.rla?.isActiveChange.pipe(filter(Boolean)) || EMPTY, this.el.matches('button')\n            ? tuiTypedFromEvent(this.el, 'click').pipe(\n            // Delaying execution until after all other click callbacks\n            switchMap(() => tuiTypedFromEvent(this.el.parentElement, 'click').pipe(take(1))))\n            : EMPTY)\n            .pipe(takeUntilDestroyed())\n            .subscribe(() => this.el.dispatchEvent(new CustomEvent(TUI_TAB_ACTIVATE, { bubbles: true })));\n    }\n    ngOnDestroy() {\n        if (tuiIsNativeFocused(this.el)) {\n            this.el.blur();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTab, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTab, isStandalone: true, selector: \"a[tuiTab]:not([routerLink]), a[tuiTab][routerLink][routerLinkActive], button[tuiTab]\", host: { attributes: { \"type\": \"button\" } }, hostDirectives: [{ directive: i1.TuiWithIcons }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTab, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'a[tuiTab]:not([routerLink]), a[tuiTab][routerLink][routerLinkActive], button[tuiTab]',\n                    hostDirectives: [TuiWithIcons],\n                    host: {\n                        type: 'button',\n                    },\n                }]\n        }] });\n\nconst TUI_TABS_DEFAULT_OPTIONS = {\n    underline: true,\n    exposeActive: true,\n    itemsLimit: Infinity,\n    minMoreWidth: 0,\n    size: 'l',\n};\n/**\n * Default parameters for Tabs component\n */\nconst TUI_TABS_OPTIONS = tuiCreateToken(TUI_TABS_DEFAULT_OPTIONS);\nfunction tuiTabsOptionsProvider(options) {\n    return tuiProvideOptions(TUI_TABS_OPTIONS, options, TUI_TABS_DEFAULT_OPTIONS);\n}\n\nclass TuiTabsStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTabsStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTabsStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-tabs\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiTab]{transition-property:color,box-shadow,opacity,background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;position:relative;display:flex;flex-shrink:0;box-sizing:border-box;justify-content:space-between;line-height:1.5rem;align-items:center;white-space:nowrap;cursor:pointer;outline:none;color:inherit;margin-inline-start:var(--tui-tab-margin, 24px)}[tuiTab]:disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}[tuiTab]._active{color:var(--tui-text-primary);box-shadow:none}[tuiTab]:focus-visible{outline:.125rem solid var(--tui-border-focus);outline-offset:-.125rem}tui-tabs._underline [tuiTab]:hover:not(._active),[tuiTabs]._underline [tuiTab]:hover:not(._active){box-shadow:inset 0 -.125rem var(--tui-border-normal)}tui-tabs>[tuiTab]:first-child,[tuiTabs]>[tuiTab]:first-child,tui-tabs>:not(.t-overflown)>[tuiTab]:first-child{margin-inline-start:0}tui-tabs>[tuiTab]~:not(.t-overflown)>[tuiTab]:first-child{margin-inline-start:var(--tui-tab-margin, 24px)}[tuiTab][tuiIcons]:before{font-size:1rem;margin-inline-end:.5rem}[tuiTab][tuiIcons]:after{font-size:1rem;margin-inline-start:.5rem}[tuiTab]:empty:after,[tuiTab]:empty:before{margin:.5rem}@media (hover: hover) and (pointer: fine){[tuiTab]:hover{color:var(--tui-text-primary)}}[tuiTabs],tui-tabs{scrollbar-width:none;-ms-overflow-style:none;position:relative;display:flex;font:var(--tui-font-text-m);color:var(--tui-text-secondary);box-shadow:inset 0 -1px var(--tui-border-normal);overflow:auto;isolation:isolate}[tuiTabs]::-webkit-scrollbar,tui-tabs::-webkit-scrollbar,[tuiTabs]::-webkit-scrollbar-thumb,tui-tabs::-webkit-scrollbar-thumb{display:none}[tuiTabs][data-size=m],tui-tabs[data-size=m]{font:var(--tui-font-text-s);--tui-tab-margin: 16px}[tuiTabs][data-size=l]:not([data-vertical]),tui-tabs[data-size=l]:not([data-vertical]){block-size:var(--tui-height-l)}[tuiTabs][data-size=m]:not([data-vertical]),tui-tabs[data-size=m]:not([data-vertical]){block-size:var(--tui-height-m)}[tuiTabs]:before,tui-tabs:before{transition-property:width,left;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;left:var(--t-left);bottom:0;block-size:.125rem;inline-size:var(--t-width);background:var(--t-color);animation:tuiPresent 1ms}[tuiTabs]._underline:before,tui-tabs._underline:before{content:\\\"\\\"}tui-tabs[data-vertical],[tuiTabs][data-vertical]{flex-direction:column;box-shadow:inset -1px 0 var(--tui-border-normal)}tui-tabs[data-vertical] [tuiTab],[tuiTabs][data-vertical] [tuiTab]{min-block-size:2.75rem;block-size:auto;white-space:normal;margin:0;text-align:start;padding:.25rem 1.25rem .25rem 0}tui-tabs[data-vertical] [tuiTab]:after,[tuiTabs][data-vertical] [tuiTab]:after{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;top:0;right:0;display:block;block-size:100%;inline-size:.125rem;background:var(--tui-background-accent-1);transform:scaleX(0);transform-origin:right;margin:0}tui-tabs[data-vertical] [tuiTab]:hover,[tuiTabs][data-vertical] [tuiTab]:hover{box-shadow:inset -.125rem 0 var(--tui-border-normal)}tui-tabs[data-vertical] [tuiTab]._active:after,[tuiTabs][data-vertical] [tuiTab]._active:after{transform:none}tui-tabs[data-vertical][data-size=m] [tuiTab],[tuiTabs][data-vertical][data-size=m] [tuiTab]{min-block-size:2.25rem;font:var(--tui-font-text-s)}tui-tabs[data-vertical][data-vertical=right],[tuiTabs][data-vertical][data-vertical=right]{box-shadow:inset 1px 0 var(--tui-border-normal)}tui-tabs[data-vertical][data-vertical=right] [tuiTab],[tuiTabs][data-vertical][data-vertical=right] [tuiTab]{text-align:end;padding:.25rem 0 .25rem 1.25rem}tui-tabs[data-vertical][data-vertical=right] [tuiTab]:after,[tuiTabs][data-vertical][data-vertical=right] [tuiTab]:after{right:auto;left:0;transform-origin:left}tui-tabs[data-vertical][data-vertical=right] [tuiTab]:hover,[tuiTabs][data-vertical][data-vertical=right] [tuiTab]:hover{box-shadow:inset .125rem 0 var(--tui-border-normal)}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTabsStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-tabs',\n                    }, styles: [\"[tuiTab]{transition-property:color,box-shadow,opacity,background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;position:relative;display:flex;flex-shrink:0;box-sizing:border-box;justify-content:space-between;line-height:1.5rem;align-items:center;white-space:nowrap;cursor:pointer;outline:none;color:inherit;margin-inline-start:var(--tui-tab-margin, 24px)}[tuiTab]:disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}[tuiTab]._active{color:var(--tui-text-primary);box-shadow:none}[tuiTab]:focus-visible{outline:.125rem solid var(--tui-border-focus);outline-offset:-.125rem}tui-tabs._underline [tuiTab]:hover:not(._active),[tuiTabs]._underline [tuiTab]:hover:not(._active){box-shadow:inset 0 -.125rem var(--tui-border-normal)}tui-tabs>[tuiTab]:first-child,[tuiTabs]>[tuiTab]:first-child,tui-tabs>:not(.t-overflown)>[tuiTab]:first-child{margin-inline-start:0}tui-tabs>[tuiTab]~:not(.t-overflown)>[tuiTab]:first-child{margin-inline-start:var(--tui-tab-margin, 24px)}[tuiTab][tuiIcons]:before{font-size:1rem;margin-inline-end:.5rem}[tuiTab][tuiIcons]:after{font-size:1rem;margin-inline-start:.5rem}[tuiTab]:empty:after,[tuiTab]:empty:before{margin:.5rem}@media (hover: hover) and (pointer: fine){[tuiTab]:hover{color:var(--tui-text-primary)}}[tuiTabs],tui-tabs{scrollbar-width:none;-ms-overflow-style:none;position:relative;display:flex;font:var(--tui-font-text-m);color:var(--tui-text-secondary);box-shadow:inset 0 -1px var(--tui-border-normal);overflow:auto;isolation:isolate}[tuiTabs]::-webkit-scrollbar,tui-tabs::-webkit-scrollbar,[tuiTabs]::-webkit-scrollbar-thumb,tui-tabs::-webkit-scrollbar-thumb{display:none}[tuiTabs][data-size=m],tui-tabs[data-size=m]{font:var(--tui-font-text-s);--tui-tab-margin: 16px}[tuiTabs][data-size=l]:not([data-vertical]),tui-tabs[data-size=l]:not([data-vertical]){block-size:var(--tui-height-l)}[tuiTabs][data-size=m]:not([data-vertical]),tui-tabs[data-size=m]:not([data-vertical]){block-size:var(--tui-height-m)}[tuiTabs]:before,tui-tabs:before{transition-property:width,left;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;left:var(--t-left);bottom:0;block-size:.125rem;inline-size:var(--t-width);background:var(--t-color);animation:tuiPresent 1ms}[tuiTabs]._underline:before,tui-tabs._underline:before{content:\\\"\\\"}tui-tabs[data-vertical],[tuiTabs][data-vertical]{flex-direction:column;box-shadow:inset -1px 0 var(--tui-border-normal)}tui-tabs[data-vertical] [tuiTab],[tuiTabs][data-vertical] [tuiTab]{min-block-size:2.75rem;block-size:auto;white-space:normal;margin:0;text-align:start;padding:.25rem 1.25rem .25rem 0}tui-tabs[data-vertical] [tuiTab]:after,[tuiTabs][data-vertical] [tuiTab]:after{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;top:0;right:0;display:block;block-size:100%;inline-size:.125rem;background:var(--tui-background-accent-1);transform:scaleX(0);transform-origin:right;margin:0}tui-tabs[data-vertical] [tuiTab]:hover,[tuiTabs][data-vertical] [tuiTab]:hover{box-shadow:inset -.125rem 0 var(--tui-border-normal)}tui-tabs[data-vertical] [tuiTab]._active:after,[tuiTabs][data-vertical] [tuiTab]._active:after{transform:none}tui-tabs[data-vertical][data-size=m] [tuiTab],[tuiTabs][data-vertical][data-size=m] [tuiTab]{min-block-size:2.25rem;font:var(--tui-font-text-s)}tui-tabs[data-vertical][data-vertical=right],[tuiTabs][data-vertical][data-vertical=right]{box-shadow:inset 1px 0 var(--tui-border-normal)}tui-tabs[data-vertical][data-vertical=right] [tuiTab],[tuiTabs][data-vertical][data-vertical=right] [tuiTab]{text-align:end;padding:.25rem 0 .25rem 1.25rem}tui-tabs[data-vertical][data-vertical=right] [tuiTab]:after,[tuiTabs][data-vertical][data-vertical=right] [tuiTab]:after{right:auto;left:0;transform-origin:left}tui-tabs[data-vertical][data-vertical=right] [tuiTab]:hover,[tuiTabs][data-vertical][data-vertical=right] [tuiTab]:hover{box-shadow:inset .125rem 0 var(--tui-border-normal)}\\n\"] }]\n        }] });\nclass TuiTabsDirective {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.injector = inject(INJECTOR);\n        this.nothing = tuiWithStyles(TuiTabsStyles);\n        this.size = inject(TUI_TABS_OPTIONS).size;\n        this.activeItemIndex = 0;\n        this.activeItemIndexChange = new EventEmitter();\n    }\n    get tabs() {\n        return Array.from(this.el.querySelectorAll('[tuiTab]'));\n    }\n    get activeElement() {\n        return this.tabs[this.activeItemIndex] || null;\n    }\n    moveFocus(current, step) {\n        const { tabs } = this;\n        tuiMoveFocus(tabs.indexOf(current), tabs, step);\n    }\n    ngAfterViewChecked() {\n        afterNextRender(() => {\n            this.markTabAsActive();\n        }, { injector: this.injector });\n    }\n    onActivate(event, element) {\n        const index = this.tabs.findIndex((tab) => tab === element);\n        event.stopPropagation();\n        if (index === this.activeItemIndex) {\n            return;\n        }\n        this.activeItemIndexChange.emit(index);\n        this.activeItemIndex = index;\n    }\n    markTabAsActive() {\n        const { tabs, activeElement } = this;\n        tabs.forEach((nativeElement) => {\n            const active = nativeElement === activeElement;\n            nativeElement.classList.toggle('_active', active);\n            nativeElement.setAttribute('tabIndex', active ? '0' : '-1');\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTabsDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTabsDirective, isStandalone: true, inputs: { size: \"size\", activeItemIndex: \"activeItemIndex\" }, outputs: { activeItemIndexChange: \"activeItemIndexChange\" }, host: { listeners: { \"tui-tab-activate\": \"onActivate($event, $event.target)\" }, properties: { \"attr.data-size\": \"size\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTabsDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    host: {\n                        '[attr.data-size]': 'size',\n                        [`(${TUI_TAB_ACTIVATE})`]: 'onActivate($event, $event.target)',\n                    },\n                }]\n        }], propDecorators: { size: [{\n                type: Input\n            }], activeItemIndex: [{\n                type: Input\n            }], activeItemIndexChange: [{\n                type: Output\n            }] } });\n\nclass TuiTabsHorizontal {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.options = inject(TUI_TABS_OPTIONS);\n        this.tabs = inject(TuiTabsDirective);\n        this.children = EMPTY_QUERY;\n        this.sub = inject(MutationObserverService, { self: true })\n            .pipe(tuiZonefree(), takeUntilDestroyed())\n            .subscribe(() => this.refresh());\n        this.underline = this.options.underline;\n    }\n    ngAfterViewChecked() {\n        this.scrollTo(this.tabs.activeItemIndex);\n        this.refresh();\n    }\n    onKeyDownArrow(current, step) {\n        this.tabs.moveFocus(current, step);\n    }\n    refresh() {\n        const { activeElement } = this.tabs;\n        if (activeElement && !activeElement.isConnected) {\n            return;\n        }\n        const { offsetLeft = 0, offsetWidth = 0 } = activeElement || {};\n        this.el.style.setProperty('--t-left', tuiPx(offsetLeft));\n        this.el.style.setProperty('--t-width', tuiPx(offsetWidth));\n    }\n    scrollTo(index) {\n        const element = this.tabs.tabs[index];\n        if (!element) {\n            return;\n        }\n        const { offsetLeft, offsetWidth } = element;\n        if (offsetLeft < this.el.scrollLeft) {\n            this.el.scrollLeft = offsetLeft;\n        }\n        if (offsetLeft + offsetWidth > this.el.scrollLeft + this.el.offsetWidth) {\n            this.el.scrollLeft = offsetLeft + offsetWidth - this.el.offsetWidth;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTabsHorizontal, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTabsHorizontal, isStandalone: true, selector: \"tui-tabs:not([vertical]), nav[tuiTabs]:not([vertical])\", inputs: { underline: \"underline\" }, host: { listeners: { \"animationend\": \"refresh()\", \"keydown.arrowRight.prevent\": \"onKeyDownArrow($event.target, 1)\", \"keydown.arrowLeft.prevent\": \"onKeyDownArrow($event.target, -1)\" }, properties: { \"class._underline\": \"underline\", \"style.--t-color\": \"underline === true ? 'var(--tui-background-accent-1)' : underline\" } }, providers: [\n            MutationObserverService,\n            {\n                provide: WA_MUTATION_OBSERVER_INIT,\n                useValue: {\n                    childList: true,\n                    characterData: true,\n                    subtree: true,\n                },\n            },\n        ], queries: [{ propertyName: \"children\", predicate: i0.forwardRef(function () { return TuiTab; }) }], hostDirectives: [{ directive: TuiTabsDirective, inputs: [\"activeItemIndex\", \"activeItemIndex\", \"size\", \"size\"], outputs: [\"activeItemIndexChange\", \"activeItemIndexChange\"] }], ngImport: i0 }); }\n}\n__decorate([\n    tuiPure\n], TuiTabsHorizontal.prototype, \"scrollTo\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTabsHorizontal, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-tabs:not([vertical]), nav[tuiTabs]:not([vertical])',\n                    providers: [\n                        MutationObserverService,\n                        {\n                            provide: WA_MUTATION_OBSERVER_INIT,\n                            useValue: {\n                                childList: true,\n                                characterData: true,\n                                subtree: true,\n                            },\n                        },\n                    ],\n                    hostDirectives: [\n                        {\n                            directive: TuiTabsDirective,\n                            inputs: ['activeItemIndex', 'size'],\n                            outputs: ['activeItemIndexChange'],\n                        },\n                    ],\n                    host: {\n                        '[class._underline]': 'underline',\n                        '[style.--t-color]': \"underline === true ? 'var(--tui-background-accent-1)' : underline\",\n                        '(animationend)': 'refresh()',\n                        '(keydown.arrowRight.prevent)': 'onKeyDownArrow($event.target, 1)',\n                        '(keydown.arrowLeft.prevent)': 'onKeyDownArrow($event.target, -1)',\n                    },\n                }]\n        }], propDecorators: { children: [{\n                type: ContentChildren,\n                args: [forwardRef(() => TuiTab)]\n            }], underline: [{\n                type: Input\n            }], scrollTo: [] } });\n\nclass TuiTabsVertical {\n    constructor() {\n        this.tabs = inject(TuiTabsDirective);\n        this.vertical = 'left';\n    }\n    onKeyDownArrow(current, step) {\n        this.tabs.moveFocus(current, step);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTabsVertical, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTabsVertical, isStandalone: true, selector: \"tui-tabs[vertical], nav[tuiTabs][vertical]\", inputs: { vertical: \"vertical\" }, host: { listeners: { \"keydown.arrowDown.prevent\": \"onKeyDownArrow($event.target, 1)\", \"keydown.arrowUp.prevent\": \"onKeyDownArrow($event.target, -1)\" }, properties: { \"attr.data-vertical\": \"vertical\" } }, hostDirectives: [{ directive: TuiTabsDirective, inputs: [\"activeItemIndex\", \"activeItemIndex\", \"size\", \"size\"], outputs: [\"activeItemIndexChange\", \"activeItemIndexChange\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTabsVertical, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-tabs[vertical], nav[tuiTabs][vertical]',\n                    hostDirectives: [\n                        {\n                            directive: TuiTabsDirective,\n                            inputs: ['activeItemIndex', 'size'],\n                            outputs: ['activeItemIndexChange'],\n                        },\n                    ],\n                    host: {\n                        '[attr.data-vertical]': 'vertical',\n                        '(keydown.arrowDown.prevent)': 'onKeyDownArrow($event.target, 1)',\n                        '(keydown.arrowUp.prevent)': 'onKeyDownArrow($event.target, -1)',\n                    },\n                }]\n        }], propDecorators: { vertical: [{\n                type: Input\n            }] } });\n\nconst TUI_TABS_REFRESH = tuiCreateToken();\nconst TUI_TABS_PROVIDERS = [\n    ResizeObserverService,\n    MutationObserverService,\n    tuiDropdownOptionsProvider({ align: 'right' }),\n    {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n            childList: true,\n            subtree: true,\n            characterData: true,\n        },\n    },\n    {\n        provide: TUI_TABS_REFRESH,\n        deps: [\n            ResizeObserverService,\n            MutationObserverService,\n            DOCUMENT,\n            ElementRef,\n            ChangeDetectorRef,\n        ],\n        useFactory: (resize$, mutations$, { body }, { nativeElement }, cdr) => merge(resize$, mutations$.pipe(tap(() => cdr.detectChanges()))).pipe(\n        // Ignoring cases when host is detached from DOM\n        filter(() => body.contains(nativeElement)), debounceTime(0), startWith(null), takeUntilDestroyed()),\n    },\n];\n\nclass TuiTabsWithMore {\n    constructor() {\n        this.options = inject(TUI_TABS_OPTIONS);\n        this.refresh$ = inject(TUI_TABS_REFRESH);\n        this.el = tuiInjectElement();\n        this.cdr = inject(ChangeDetectorRef);\n        this.maxIndex = Infinity;\n        this.items = EMPTY_QUERY;\n        this.moreWord$ = inject(TUI_MORE_WORD);\n        this.open = false;\n        this.size = this.options.size;\n        this.underline = this.options.underline;\n        this.itemsLimit = this.options.itemsLimit;\n        this.activeItemIndexChange = new EventEmitter();\n        this.activeItemIndex = 0;\n    }\n    set itemIndex(activeItemIndex) {\n        this.activeItemIndex = activeItemIndex;\n        this.maxIndex = this.getMaxIndex();\n    }\n    get lastVisibleIndex() {\n        if (this.itemsLimit + 1 >= this.items.length) {\n            return this.maxIndex;\n        }\n        const offset = this.itemsLimit - 1 > this.activeItemIndex || !this.options.exposeActive\n            ? 1\n            : 2;\n        return Math.min(this.itemsLimit - offset, this.maxIndex);\n    }\n    ngAfterViewInit() {\n        this.refresh$\n            .pipe(map(() => this.getMaxIndex()), tap(() => this.refresh()), filter((maxIndex) => this.maxIndex !== maxIndex))\n            .subscribe((maxIndex) => {\n            this.maxIndex = maxIndex;\n            this.cdr.detectChanges();\n        });\n    }\n    ngAfterViewChecked() {\n        this.refresh();\n    }\n    // TODO: Improve performance\n    get tabs() {\n        return Array.from(this.el.querySelectorAll('[tuiTab]'));\n    }\n    get activeElement() {\n        const { tabs } = this;\n        const safeActiveIndex = tuiClamp(this.activeItemIndex || 0, 0, tabs.length - 2);\n        return this.options.exposeActive || this.lastVisibleIndex >= safeActiveIndex\n            ? tabs[safeActiveIndex] || null\n            : this.moreButton?.nativeElement || null;\n    }\n    get isMoreAlone() {\n        return this.lastVisibleIndex < 0 && !this.options.exposeActive;\n    }\n    get isMoreVisible() {\n        return this.lastVisibleIndex < this.items.length - 1;\n    }\n    get isMoreFocusable() {\n        return !!this.moreButton && tuiIsNativeFocused(this.moreButton.nativeElement);\n    }\n    get isMoreActive() {\n        return (this.open ||\n            (!this.options.exposeActive && this.lastVisibleIndex < this.activeItemIndex));\n    }\n    onActiveItemIndexChange(activeItemIndex) {\n        this.updateActiveItemIndex(activeItemIndex);\n    }\n    onClick(index) {\n        this.open = false;\n        this.focusMore();\n        this.updateActiveItemIndex(index);\n    }\n    onArrowRight(event) {\n        if (tuiIsElement(event.target) && tuiIsNativeFocused(event.target)) {\n            this.focusMore();\n        }\n    }\n    onArrowLeft() {\n        const { tabs } = this;\n        let index = tabs.length - 2;\n        while (index >= 0) {\n            tabs[index]?.focus();\n            if (tuiIsNativeFocused(tabs[index])) {\n                return;\n            }\n            index--;\n        }\n    }\n    onWrapperArrow(event, wrapper, previous) {\n        const button = event.target;\n        const target = tuiGetClosestFocusable({ initial: button, root: wrapper, previous });\n        if (target) {\n            target.focus();\n        }\n    }\n    isOverflown(index) {\n        return index !== this.activeItemIndex || !this.options.exposeActive;\n    }\n    shouldShow(index) {\n        return index > this.lastVisibleIndex && this.isOverflown(index);\n    }\n    get margin() {\n        return this.size === 'l' ? 24 : 16;\n    }\n    focusMore() {\n        if (this.moreButton) {\n            this.moreButton.nativeElement.focus();\n        }\n    }\n    getMaxIndex() {\n        const { tabs, activeItemIndex, margin } = this;\n        if (tabs.length < 2) {\n            return 0;\n        }\n        const { exposeActive, minMoreWidth } = this.options;\n        const { clientWidth } = this.el;\n        const active = tabs[activeItemIndex];\n        const activeWidth = active?.scrollWidth ?? 0;\n        const moreWidth = Math.max(tabs[tabs.length - 1]?.scrollWidth ?? 0, minMoreWidth);\n        let maxIndex = tabs.length - 2;\n        let total = tabs.reduce((acc, { scrollWidth }) => acc + scrollWidth, 0) +\n            maxIndex * margin -\n            (tabs[tabs.length - 1]?.scrollWidth ?? 0);\n        if (Number.isNaN(total) || total <= clientWidth) {\n            return Infinity;\n        }\n        while (maxIndex) {\n            total -= (tabs[maxIndex]?.scrollWidth ?? 0) + margin;\n            maxIndex--;\n            const activeDisplaced = exposeActive && activeItemIndex > maxIndex;\n            const activeOffset = activeDisplaced ? activeWidth + margin : 0;\n            const currentWidth = total + activeOffset + moreWidth + margin;\n            // Needed for different rounding of visible and hidden elements scrollWidth\n            const safetyOffset = tuiToInt(this.maxIndex === maxIndex - 1);\n            if (currentWidth + safetyOffset < clientWidth) {\n                return maxIndex;\n            }\n        }\n        return -1;\n    }\n    updateActiveItemIndex(activeItemIndex) {\n        this.itemIndex = activeItemIndex;\n        this.activeItemIndexChange.emit(activeItemIndex);\n    }\n    refresh() {\n        const { offsetLeft = 0, offsetWidth = 0 } = this.activeElement || {};\n        this.dir?.nativeElement.style.setProperty('--t-left', tuiPx(offsetLeft));\n        this.dir?.nativeElement.style.setProperty('--t-width', tuiPx(offsetWidth));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTabsWithMore, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTabsWithMore, isStandalone: true, selector: \"tui-tabs-with-more, nav[tuiTabsWithMore]\", inputs: { size: \"size\", moreContent: \"moreContent\", dropdownContent: \"dropdownContent\", underline: \"underline\", itemsLimit: \"itemsLimit\", itemIndex: [\"activeItemIndex\", \"itemIndex\"] }, outputs: { activeItemIndexChange: \"activeItemIndexChange\" }, host: { properties: { \"attr.data-size\": \"size\" } }, providers: TUI_TABS_PROVIDERS, queries: [{ propertyName: \"items\", predicate: TuiItem, read: TemplateRef }], viewQueries: [{ propertyName: \"moreButton\", first: true, predicate: TuiTab, descendants: true, read: ElementRef }, { propertyName: \"dir\", first: true, predicate: TuiTabsHorizontal, descendants: true, read: ElementRef }], ngImport: i0, template: \"<ng-container *ngIf=\\\"items.changes | async\\\" />\\n<tui-tabs\\n    class=\\\"t-tabs\\\"\\n    [activeItemIndex]=\\\"activeItemIndex\\\"\\n    [size]=\\\"size\\\"\\n    [underline]=\\\"underline\\\"\\n    (activeItemIndexChange)=\\\"onActiveItemIndexChange($event)\\\"\\n    (keydown.arrowRight)=\\\"onArrowRight($event)\\\"\\n>\\n    <ng-container *ngFor=\\\"let item of items; let index = index\\\">\\n        <ng-container\\n            *ngIf=\\\"index <= lastVisibleIndex; else hidden\\\"\\n            [ngTemplateOutlet]=\\\"item\\\"\\n        />\\n        <ng-template #hidden>\\n            <div\\n                class=\\\"t-flex\\\"\\n                [class.t-overflown]=\\\"isOverflown(index)\\\"\\n            >\\n                <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n            </div>\\n        </ng-template>\\n    </ng-container>\\n</tui-tabs>\\n\\n<button\\n    *ngIf=\\\"moreContent; else chevron\\\"\\n    tuiTab\\n    type=\\\"button\\\"\\n    class=\\\"t-more\\\"\\n    [class._active]=\\\"isMoreActive\\\"\\n    [class.t-no-margin]=\\\"isMoreAlone\\\"\\n    [class.t-overflown]=\\\"!isMoreVisible\\\"\\n    [tabIndex]=\\\"isMoreFocusable ? 0 : -1\\\"\\n    [tuiDropdown]=\\\"dropdownContent || dropdown\\\"\\n    [(tuiDropdownOpen)]=\\\"open\\\"\\n    (keydown.arrowLeft.prevent)=\\\"onArrowLeft()\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"moreContent as text\\\">\\n        {{ text }}\\n    </ng-container>\\n</button>\\n<ng-template #chevron>\\n    <button\\n        tuiChevron\\n        tuiTab\\n        type=\\\"button\\\"\\n        class=\\\"t-more\\\"\\n        [class._active]=\\\"isMoreActive\\\"\\n        [class.t-no-margin]=\\\"isMoreAlone\\\"\\n        [class.t-overflown]=\\\"!isMoreVisible\\\"\\n        [tabIndex]=\\\"isMoreFocusable ? 0 : -1\\\"\\n        [tuiDropdown]=\\\"dropdownContent || dropdown\\\"\\n        [(tuiDropdownOpen)]=\\\"open\\\"\\n        (keydown.arrowLeft.prevent)=\\\"onArrowLeft()\\\"\\n    >\\n        {{ moreWord$ | async }}\\n    </button>\\n</ng-template>\\n<ng-template #dropdown>\\n    <div\\n        #element\\n        class=\\\"t-dropdown\\\"\\n        [attr.data-size]=\\\"size\\\"\\n        (keydown.arrowDown.prevent)=\\\"onWrapperArrow($event, element, false)\\\"\\n        (keydown.arrowUp.prevent)=\\\"onWrapperArrow($event, element, true)\\\"\\n    >\\n        <div\\n            *ngFor=\\\"let item of items; let index = index\\\"\\n            class=\\\"t-dropdown-item\\\"\\n            (tui-tab-activate)=\\\"onClick(index)\\\"\\n        >\\n            <ng-container *ngIf=\\\"shouldShow(index)\\\">\\n                <ng-container *polymorpheusOutlet=\\\"item\\\" />\\n            </ng-container>\\n        </div>\\n    </div>\\n</ng-template>\\n\", styles: [\":host{position:relative;display:flex;font:var(--tui-font-text-m);box-sizing:border-box;color:var(--tui-text-secondary);box-shadow:inset 0 -1px var(--tui-border-normal);overflow:hidden}:host[data-size=m]{font:var(--tui-font-text-s)}.t-tabs{block-size:inherit;font:inherit;overflow:visible;box-shadow:none;color:inherit}.t-flex{display:flex}.t-overflown{margin:0;inline-size:0;max-inline-size:0;overflow:hidden;visibility:hidden}.t-icon{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-right:-.25rem;vertical-align:bottom}.t-icon_rotated{transform:rotate(180deg)}.t-dropdown{padding:.25rem 0}.t-dropdown ::ng-deep *[tuiTab]{inline-size:calc(100% - .75rem);block-size:2.75rem;justify-content:flex-start;margin:.125rem .375rem;padding:0 .625rem;line-height:1.5rem;border-radius:var(--tui-radius-s);font:var(--tui-font-text-m);color:var(--tui-text-primary)}.t-dropdown ::ng-deep *[tuiTab]:before{display:none}.t-dropdown ::ng-deep *[tuiTab]:hover,.t-dropdown ::ng-deep *[tuiTab]:focus,.t-dropdown ::ng-deep *[tuiTab]._active{box-shadow:none;outline:none;background:var(--tui-background-neutral-1)}.t-dropdown[data-size=m] ::ng-deep *[tuiTab]{block-size:2.25rem;font:var(--tui-font-text-s)}.t-dropdown-item{display:flex;flex-direction:column}.t-no-margin{margin-left:0}\\n\"], dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i1$1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1$1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1$1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"pipe\", type: i1$1.AsyncPipe, name: \"async\" }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: TuiChevron, selector: \"[tuiChevron]\", inputs: [\"tuiChevron\"] }, { kind: \"directive\", type: i2.TuiDropdownDirective, selector: \"[tuiDropdown]:not(ng-container):not(ng-template)\", inputs: [\"tuiDropdown\"], exportAs: [\"tuiDropdown\"] }, { kind: \"directive\", type: i2.TuiDropdownOpen, selector: \"[tuiDropdown][tuiDropdownOpen],[tuiDropdown][tuiDropdownOpenChange]\", inputs: [\"tuiDropdownEnabled\", \"tuiDropdownOpen\"], outputs: [\"tuiDropdownOpenChange\"] }, { kind: \"directive\", type: TuiTab, selector: \"a[tuiTab]:not([routerLink]), a[tuiTab][routerLink][routerLinkActive], button[tuiTab]\" }, { kind: \"directive\", type: TuiTabsHorizontal, selector: \"tui-tabs:not([vertical]), nav[tuiTabs]:not([vertical])\", inputs: [\"underline\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTabsWithMore, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-tabs-with-more, nav[tuiTabsWithMore]', imports: [\n                        CommonModule,\n                        PolymorpheusOutlet,\n                        TuiChevron,\n                        TuiDropdown,\n                        TuiTab,\n                        TuiTabsHorizontal,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, providers: TUI_TABS_PROVIDERS, host: {\n                        '[attr.data-size]': 'size',\n                    }, template: \"<ng-container *ngIf=\\\"items.changes | async\\\" />\\n<tui-tabs\\n    class=\\\"t-tabs\\\"\\n    [activeItemIndex]=\\\"activeItemIndex\\\"\\n    [size]=\\\"size\\\"\\n    [underline]=\\\"underline\\\"\\n    (activeItemIndexChange)=\\\"onActiveItemIndexChange($event)\\\"\\n    (keydown.arrowRight)=\\\"onArrowRight($event)\\\"\\n>\\n    <ng-container *ngFor=\\\"let item of items; let index = index\\\">\\n        <ng-container\\n            *ngIf=\\\"index <= lastVisibleIndex; else hidden\\\"\\n            [ngTemplateOutlet]=\\\"item\\\"\\n        />\\n        <ng-template #hidden>\\n            <div\\n                class=\\\"t-flex\\\"\\n                [class.t-overflown]=\\\"isOverflown(index)\\\"\\n            >\\n                <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n            </div>\\n        </ng-template>\\n    </ng-container>\\n</tui-tabs>\\n\\n<button\\n    *ngIf=\\\"moreContent; else chevron\\\"\\n    tuiTab\\n    type=\\\"button\\\"\\n    class=\\\"t-more\\\"\\n    [class._active]=\\\"isMoreActive\\\"\\n    [class.t-no-margin]=\\\"isMoreAlone\\\"\\n    [class.t-overflown]=\\\"!isMoreVisible\\\"\\n    [tabIndex]=\\\"isMoreFocusable ? 0 : -1\\\"\\n    [tuiDropdown]=\\\"dropdownContent || dropdown\\\"\\n    [(tuiDropdownOpen)]=\\\"open\\\"\\n    (keydown.arrowLeft.prevent)=\\\"onArrowLeft()\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"moreContent as text\\\">\\n        {{ text }}\\n    </ng-container>\\n</button>\\n<ng-template #chevron>\\n    <button\\n        tuiChevron\\n        tuiTab\\n        type=\\\"button\\\"\\n        class=\\\"t-more\\\"\\n        [class._active]=\\\"isMoreActive\\\"\\n        [class.t-no-margin]=\\\"isMoreAlone\\\"\\n        [class.t-overflown]=\\\"!isMoreVisible\\\"\\n        [tabIndex]=\\\"isMoreFocusable ? 0 : -1\\\"\\n        [tuiDropdown]=\\\"dropdownContent || dropdown\\\"\\n        [(tuiDropdownOpen)]=\\\"open\\\"\\n        (keydown.arrowLeft.prevent)=\\\"onArrowLeft()\\\"\\n    >\\n        {{ moreWord$ | async }}\\n    </button>\\n</ng-template>\\n<ng-template #dropdown>\\n    <div\\n        #element\\n        class=\\\"t-dropdown\\\"\\n        [attr.data-size]=\\\"size\\\"\\n        (keydown.arrowDown.prevent)=\\\"onWrapperArrow($event, element, false)\\\"\\n        (keydown.arrowUp.prevent)=\\\"onWrapperArrow($event, element, true)\\\"\\n    >\\n        <div\\n            *ngFor=\\\"let item of items; let index = index\\\"\\n            class=\\\"t-dropdown-item\\\"\\n            (tui-tab-activate)=\\\"onClick(index)\\\"\\n        >\\n            <ng-container *ngIf=\\\"shouldShow(index)\\\">\\n                <ng-container *polymorpheusOutlet=\\\"item\\\" />\\n            </ng-container>\\n        </div>\\n    </div>\\n</ng-template>\\n\", styles: [\":host{position:relative;display:flex;font:var(--tui-font-text-m);box-sizing:border-box;color:var(--tui-text-secondary);box-shadow:inset 0 -1px var(--tui-border-normal);overflow:hidden}:host[data-size=m]{font:var(--tui-font-text-s)}.t-tabs{block-size:inherit;font:inherit;overflow:visible;box-shadow:none;color:inherit}.t-flex{display:flex}.t-overflown{margin:0;inline-size:0;max-inline-size:0;overflow:hidden;visibility:hidden}.t-icon{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-right:-.25rem;vertical-align:bottom}.t-icon_rotated{transform:rotate(180deg)}.t-dropdown{padding:.25rem 0}.t-dropdown ::ng-deep *[tuiTab]{inline-size:calc(100% - .75rem);block-size:2.75rem;justify-content:flex-start;margin:.125rem .375rem;padding:0 .625rem;line-height:1.5rem;border-radius:var(--tui-radius-s);font:var(--tui-font-text-m);color:var(--tui-text-primary)}.t-dropdown ::ng-deep *[tuiTab]:before{display:none}.t-dropdown ::ng-deep *[tuiTab]:hover,.t-dropdown ::ng-deep *[tuiTab]:focus,.t-dropdown ::ng-deep *[tuiTab]._active{box-shadow:none;outline:none;background:var(--tui-background-neutral-1)}.t-dropdown[data-size=m] ::ng-deep *[tuiTab]{block-size:2.25rem;font:var(--tui-font-text-s)}.t-dropdown-item{display:flex;flex-direction:column}.t-no-margin{margin-left:0}\\n\"] }]\n        }], propDecorators: { moreButton: [{\n                type: ViewChild,\n                args: [TuiTab, { read: ElementRef }]\n            }], dir: [{\n                type: ViewChild,\n                args: [TuiTabsHorizontal, { read: ElementRef }]\n            }], items: [{\n                type: ContentChildren,\n                args: [TuiItem, { read: TemplateRef }]\n            }], size: [{\n                type: Input\n            }], moreContent: [{\n                type: Input\n            }], dropdownContent: [{\n                type: Input\n            }], underline: [{\n                type: Input\n            }], itemsLimit: [{\n                type: Input\n            }], activeItemIndexChange: [{\n                type: Output\n            }], itemIndex: [{\n                type: Input,\n                args: ['activeItemIndex']\n            }] } });\n\nconst TuiTabs = [\n    TuiItem,\n    TuiTab,\n    TuiTabsDirective,\n    TuiTabsHorizontal,\n    TuiTabsVertical,\n    TuiTabsWithMore,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_TABS_DEFAULT_OPTIONS, TUI_TABS_OPTIONS, TUI_TABS_PROVIDERS, TUI_TABS_REFRESH, TUI_TAB_ACTIVATE, TuiTab, TuiTabs, TuiTabsDirective, TuiTabsHorizontal, TuiTabsVertical, TuiTabsWithMore, tuiTabsOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,eAAe,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,SAAS,QAAQ,eAAe;AACpP,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,uBAAuB,EAAEC,yBAAyB,QAAQ,gCAAgC;AACnG,SAASC,iBAAiB,EAAEC,WAAW,QAAQ,2BAA2B;AAC1E,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AACxE,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,sBAAsB,QAAQ,2BAA2B;AACpG,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,IAAI,EAAEC,GAAG,EAAEC,YAAY,EAAEC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AAC/F,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,KAAK,EAAEC,OAAO,QAAQ,mCAAmC;AACpH,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,KAAKC,IAAI,MAAM,iBAAiB;AACvC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,0BAA0B;AAC7D,OAAO,KAAKC,EAAE,MAAM,oCAAoC;AACxD,SAASC,0BAA0B,EAAEC,WAAW,QAAQ,oCAAoC;AAC5F,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,qBAAqB,QAAQ,8BAA8B;AAAC,SAAAC,wCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAsBgC5D,EAAE,CAAA8D,kBAAA,EAsZkxB,CAAC;EAAA;AAAA;AAAA,SAAAC,uDAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtZrxB5D,EAAE,CAAA8D,kBAAA,KAsZutC,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAI,OAAA,GAtZ1tChE,EAAE,CAAAiE,aAAA,GAAAC,SAAA;IAAFlE,EAAE,CAAAmE,UAAA,qBAAAH,OAsZ2sC,CAAC;EAAA;AAAA;AAAA,SAAAI,sDAAAR,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtZ9sC5D,EAAE,CAAAqE,cAAA,aAsZq3C,CAAC;IAtZx3CrE,EAAE,CAAA8D,kBAAA,KAsZm7C,CAAC;IAtZt7C9D,EAAE,CAAAsE,YAAA,CAsZu8C,CAAC;EAAA;EAAA,IAAAV,EAAA;IAAA,MAAAW,MAAA,GAtZ18CvE,EAAE,CAAAiE,aAAA;IAAA,MAAAD,OAAA,GAAAO,MAAA,CAAAL,SAAA;IAAA,MAAAM,QAAA,GAAAD,MAAA,CAAAE,KAAA;IAAA,MAAAC,MAAA,GAAF1E,EAAE,CAAAiE,aAAA;IAAFjE,EAAE,CAAA2E,WAAA,gBAAAD,MAAA,CAAAE,WAAA,CAAAJ,QAAA,CAsZs2C,CAAC;IAtZz2CxE,EAAE,CAAA6E,SAAA,CAsZg7C,CAAC;IAtZn7C7E,EAAE,CAAAmE,UAAA,qBAAAH,OAsZg7C,CAAC;EAAA;AAAA;AAAA,SAAAc,wCAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtZn7C5D,EAAE,CAAA+E,uBAAA,EAsZ6kC,CAAC;IAtZhlC/E,EAAE,CAAAgF,UAAA,IAAAjB,sDAAA,yBAsZutC,CAAC,IAAAK,qDAAA,gCAtZ1tCpE,EAAE,CAAAiF,sBAsZsvC,CAAC;IAtZzvCjF,EAAE,CAAAkF,qBAAA;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAAY,QAAA,GAAAX,GAAA,CAAAY,KAAA;IAAA,MAAAU,SAAA,GAAFnF,EAAE,CAAAoF,WAAA;IAAA,MAAAV,MAAA,GAAF1E,EAAE,CAAAiE,aAAA;IAAFjE,EAAE,CAAA6E,SAAA,CAsZqpC,CAAC;IAtZxpC7E,EAAE,CAAAmE,UAAA,SAAAK,QAAA,IAAAE,MAAA,CAAAW,gBAsZqpC,CAAC,aAAAF,SAAU,CAAC;EAAA;AAAA;AAAA,SAAAG,iDAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtZnqC5D,EAAE,CAAA+E,uBAAA,EAsZ+9D,CAAC;IAtZl+D/E,EAAE,CAAAuF,MAAA,EAsZy/D,CAAC;IAtZ5/DvF,EAAE,CAAAkF,qBAAA;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAA4B,OAAA,GAAA3B,GAAA,CAAA4B,kBAAA;IAAFzF,EAAE,CAAA6E,SAAA,CAsZy/D,CAAC;IAtZ5/D7E,EAAE,CAAA0F,kBAAA,MAAAF,OAAA,KAsZy/D,CAAC;EAAA;AAAA;AAAA,SAAAG,kCAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAgC,GAAA,GAtZ5/D5F,EAAE,CAAA6F,gBAAA;IAAF7F,EAAE,CAAAqE,cAAA,gBAsZ+5D,CAAC;IAtZl6DrE,EAAE,CAAA8F,gBAAA,mCAAAC,0EAAAC,MAAA;MAAFhG,EAAE,CAAAiG,aAAA,CAAAL,GAAA;MAAA,MAAAlB,MAAA,GAAF1E,EAAE,CAAAiE,aAAA;MAAFjE,EAAE,CAAAkG,kBAAA,CAAAxB,MAAA,CAAAyB,IAAA,EAAAH,MAAA,MAAAtB,MAAA,CAAAyB,IAAA,GAAAH,MAAA;MAAA,OAAFhG,EAAE,CAAAoG,WAAA,CAAAJ,MAAA;IAAA,CAsZy2D,CAAC;IAtZ52DhG,EAAE,CAAAqG,UAAA,uCAAAC,8EAAA;MAAFtG,EAAE,CAAAiG,aAAA,CAAAL,GAAA;MAAA,MAAAlB,MAAA,GAAF1E,EAAE,CAAAiE,aAAA;MAAA,OAAFjE,EAAE,CAAAoG,WAAA,CAsZ84D1B,MAAA,CAAA6B,WAAA,CAAY,CAAC;IAAA,CAAC,CAAC;IAtZ/5DvG,EAAE,CAAAgF,UAAA,IAAAM,gDAAA,0BAsZ+9D,CAAC;IAtZl+DtF,EAAE,CAAAsE,YAAA,CAsZmhE,CAAC;EAAA;EAAA,IAAAV,EAAA;IAAA,MAAAc,MAAA,GAtZthE1E,EAAE,CAAAiE,aAAA;IAAA,MAAAuC,WAAA,GAAFxG,EAAE,CAAAoF,WAAA;IAAFpF,EAAE,CAAA2E,WAAA,YAAAD,MAAA,CAAA+B,YAsZkpD,CAAC,gBAAA/B,MAAA,CAAAgC,WAAwC,CAAC,iBAAAhC,MAAA,CAAAiC,aAA2C,CAAC;IAtZ1uD3G,EAAE,CAAAmE,UAAA,aAAAO,MAAA,CAAAkC,eAAA,SAsZoxD,CAAC,gBAAAlC,MAAA,CAAAmC,eAAA,IAAAL,WAAkD,CAAC;IAtZ10DxG,EAAE,CAAA8G,gBAAA,oBAAApC,MAAA,CAAAyB,IAsZy2D,CAAC;IAtZ52DnG,EAAE,CAAA6E,SAAA,CAsZq9D,CAAC;IAtZx9D7E,EAAE,CAAAmE,UAAA,uBAAAO,MAAA,CAAAqC,WAsZq9D,CAAC;EAAA;AAAA;AAAA,SAAAC,uCAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqD,IAAA,GAtZx9DjH,EAAE,CAAA6F,gBAAA;IAAF7F,EAAE,CAAAqE,cAAA,gBAsZk+E,CAAC;IAtZr+ErE,EAAE,CAAA8F,gBAAA,mCAAAoB,+EAAAlB,MAAA;MAAFhG,EAAE,CAAAiG,aAAA,CAAAgB,IAAA;MAAA,MAAAvC,MAAA,GAAF1E,EAAE,CAAAiE,aAAA;MAAFjE,EAAE,CAAAkG,kBAAA,CAAAxB,MAAA,CAAAyB,IAAA,EAAAH,MAAA,MAAAtB,MAAA,CAAAyB,IAAA,GAAAH,MAAA;MAAA,OAAFhG,EAAE,CAAAoG,WAAA,CAAAJ,MAAA;IAAA,CAsZo6E,CAAC;IAtZv6EhG,EAAE,CAAAqG,UAAA,uCAAAc,mFAAA;MAAFnH,EAAE,CAAAiG,aAAA,CAAAgB,IAAA;MAAA,MAAAvC,MAAA,GAAF1E,EAAE,CAAAiE,aAAA;MAAA,OAAFjE,EAAE,CAAAoG,WAAA,CAsZ68E1B,MAAA,CAAA6B,WAAA,CAAY,CAAC;IAAA,CAAC,CAAC;IAtZ99EvG,EAAE,CAAAuF,MAAA,EAsZygF,CAAC;IAtZ5gFvF,EAAE,CAAAoH,MAAA;IAAFpH,EAAE,CAAAsE,YAAA,CAsZkhF,CAAC;EAAA;EAAA,IAAAV,EAAA;IAAA,MAAAc,MAAA,GAtZrhF1E,EAAE,CAAAiE,aAAA;IAAA,MAAAuC,WAAA,GAAFxG,EAAE,CAAAoF,WAAA;IAAFpF,EAAE,CAAA2E,WAAA,YAAAD,MAAA,CAAA+B,YAsZyrE,CAAC,gBAAA/B,MAAA,CAAAgC,WAA4C,CAAC,iBAAAhC,MAAA,CAAAiC,aAA+C,CAAC;IAtZzxE3G,EAAE,CAAAmE,UAAA,aAAAO,MAAA,CAAAkC,eAAA,SAsZu0E,CAAC,gBAAAlC,MAAA,CAAAmC,eAAA,IAAAL,WAAsD,CAAC;IAtZj4ExG,EAAE,CAAA8G,gBAAA,oBAAApC,MAAA,CAAAyB,IAsZo6E,CAAC;IAtZv6EnG,EAAE,CAAA6E,SAAA,CAsZygF,CAAC;IAtZ5gF7E,EAAE,CAAA0F,kBAAA,MAAF1F,EAAE,CAAAqH,WAAA,QAAA3C,MAAA,CAAA4C,SAAA,MAsZygF,CAAC;EAAA;AAAA;AAAA,SAAAC,2EAAA3D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtZ5gF5D,EAAE,CAAA8D,kBAAA,EAsZmmG,CAAC;EAAA;AAAA;AAAA,SAAA0D,4DAAA5D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtZtmG5D,EAAE,CAAA+E,uBAAA,EAsZoiG,CAAC;IAtZviG/E,EAAE,CAAAgF,UAAA,IAAAuC,0EAAA,0BAsZmmG,CAAC;IAtZtmGvH,EAAE,CAAAkF,qBAAA;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAA6D,QAAA,GAAFzH,EAAE,CAAAiE,aAAA,GAAAC,SAAA;IAAFlE,EAAE,CAAA6E,SAAA,CAsZ8lG,CAAC;IAtZjmG7E,EAAE,CAAAmE,UAAA,uBAAAsD,QAsZ8lG,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAA9D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+D,IAAA,GAtZjmG3H,EAAE,CAAA6F,gBAAA;IAAF7F,EAAE,CAAAqE,cAAA,aAsZ4+F,CAAC;IAtZ/+FrE,EAAE,CAAAqG,UAAA,8BAAAuB,6EAAA;MAAA,MAAAC,SAAA,GAAF7H,EAAE,CAAAiG,aAAA,CAAA0B,IAAA,EAAAlD,KAAA;MAAA,MAAAC,MAAA,GAAF1E,EAAE,CAAAiE,aAAA;MAAA,OAAFjE,EAAE,CAAAoG,WAAA,CAsZk9F1B,MAAA,CAAAoD,OAAA,CAAAD,SAAa,CAAC;IAAA,CAAC,CAAC;IAtZp+F7H,EAAE,CAAAgF,UAAA,IAAAwC,2DAAA,yBAsZoiG,CAAC;IAtZviGxH,EAAE,CAAAsE,YAAA,CAsZgpG,CAAC;EAAA;EAAA,IAAAV,EAAA;IAAA,MAAAiE,SAAA,GAAAhE,GAAA,CAAAY,KAAA;IAAA,MAAAC,MAAA,GAtZnpG1E,EAAE,CAAAiE,aAAA;IAAFjE,EAAE,CAAA6E,SAAA,CAsZiiG,CAAC;IAtZpiG7E,EAAE,CAAAmE,UAAA,SAAAO,MAAA,CAAAqD,UAAA,CAAAF,SAAA,CAsZiiG,CAAC;EAAA;AAAA;AAAA,SAAAG,uCAAApE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqE,IAAA,GAtZpiGjI,EAAE,CAAA6F,gBAAA;IAAF7F,EAAE,CAAAqE,cAAA,gBAsZ4zF,CAAC;IAtZ/zFrE,EAAE,CAAAqG,UAAA,uCAAA6B,gFAAAlC,MAAA;MAAFhG,EAAE,CAAAiG,aAAA,CAAAgC,IAAA;MAAA,MAAAE,WAAA,GAAFnI,EAAE,CAAAoF,WAAA;MAAA,MAAAV,MAAA,GAAF1E,EAAE,CAAAiE,aAAA;MAAA,OAAFjE,EAAE,CAAAoG,WAAA,CAsZisF1B,MAAA,CAAA0D,cAAA,CAAApC,MAAA,EAAAmC,WAAA,EAAgC,KAAK,CAAC;IAAA,CAAC,CAAC,qCAAAE,8EAAArC,MAAA;MAtZ3uFhG,EAAE,CAAAiG,aAAA,CAAAgC,IAAA;MAAA,MAAAE,WAAA,GAAFnI,EAAE,CAAAoF,WAAA;MAAA,MAAAV,MAAA,GAAF1E,EAAE,CAAAiE,aAAA;MAAA,OAAFjE,EAAE,CAAAoG,WAAA,CAsZ+wF1B,MAAA,CAAA0D,cAAA,CAAApC,MAAA,EAAAmC,WAAA,EAAgC,IAAI,CAAC;IAAA,CAAC,CAAC;IAtZxzFnI,EAAE,CAAAgF,UAAA,IAAA0C,4CAAA,iBAsZ4+F,CAAC;IAtZ/+F1H,EAAE,CAAAsE,YAAA,CAsZ4pG,CAAC;EAAA;EAAA,IAAAV,EAAA;IAAA,MAAAc,MAAA,GAtZ/pG1E,EAAE,CAAAiE,aAAA;IAAFjE,EAAE,CAAAsI,WAAA,cAAA5D,MAAA,CAAA6D,IAAA;IAAFvI,EAAE,CAAA6E,SAAA,EAsZo3F,CAAC;IAtZv3F7E,EAAE,CAAAmE,UAAA,YAAAO,MAAA,CAAA8D,KAsZo3F,CAAC;EAAA;AAAA;AA1a59F,MAAMC,gBAAgB,GAAG,kBAAkB;AAC3C,MAAMC,MAAM,CAAC;EACTC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAGrH,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACsH,GAAG,GAAG5I,MAAM,CAACiB,gBAAgB,EAAE;MAAE4H,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvD,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACF,GAAG,IACpB5I,MAAM,CAACkB,uBAAuB,EAAE;MAAE2H,QAAQ,EAAE;IAAK,CAAC,CAAC,EAAEE,IAAI,CAAClH,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC+G,GAAG,EAAEI,QAAQ,CAAC,CAAC;IACjG,IAAI,CAACC,GAAG,GAAGnH,KAAK,CAAC,IAAI,CAACgH,QAAQ,IAAI/G,KAAK,EAAE,IAAI,CAAC6G,GAAG,EAAEM,cAAc,CAACH,IAAI,CAAClH,MAAM,CAACsH,OAAO,CAAC,CAAC,IAAIpH,KAAK,EAAE,IAAI,CAAC4G,EAAE,CAACS,OAAO,CAAC,QAAQ,CAAC,GACrHhI,iBAAiB,CAAC,IAAI,CAACuH,EAAE,EAAE,OAAO,CAAC,CAACI,IAAI;IAC1C;IACA/G,SAAS,CAAC,MAAMZ,iBAAiB,CAAC,IAAI,CAACuH,EAAE,CAACU,aAAa,EAAE,OAAO,CAAC,CAACN,IAAI,CAAC9G,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAC/EF,KAAK,CAAC,CACPgH,IAAI,CAAC/H,kBAAkB,CAAC,CAAC,CAAC,CAC1BsI,SAAS,CAAC,MAAM,IAAI,CAACX,EAAE,CAACY,aAAa,CAAC,IAAIC,WAAW,CAAChB,gBAAgB,EAAE;MAAEiB,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,CAAC;EACrG;EACAC,WAAWA,CAAA,EAAG;IACV,IAAIlI,kBAAkB,CAAC,IAAI,CAACmH,EAAE,CAAC,EAAE;MAC7B,IAAI,CAACA,EAAE,CAACgB,IAAI,CAAC,CAAC;IAClB;EACJ;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,eAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFrB,MAAM;IAAA,CAAmD;EAAE;EACtK;IAAS,IAAI,CAACsB,IAAI,kBAD+EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EACJxB,MAAM;MAAAyB,SAAA;MAAAC,SAAA,WAAsJ,QAAQ;MAAAC,UAAA;MAAAC,QAAA,GADlKtK,EAAE,CAAAuK,uBAAA,EACoM3I,EAAE,CAACC,YAAY;IAAA,EAAoB;EAAE;AAChV;AACA;EAAA,QAAA2I,SAAA,oBAAAA,SAAA,KAHqGxK,EAAE,CAAAyK,iBAAA,CAGX/B,MAAM,EAAc,CAAC;IACrGwB,IAAI,EAAEhK,SAAS;IACfwK,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBM,QAAQ,EAAE,sFAAsF;MAChGC,cAAc,EAAE,CAAC/I,YAAY,CAAC;MAC9BgJ,IAAI,EAAE;QACFX,IAAI,EAAE;MACV;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMY,wBAAwB,GAAG;EAC7BC,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAEC,QAAQ;EACpBC,YAAY,EAAE,CAAC;EACf5C,IAAI,EAAE;AACV,CAAC;AACD;AACA;AACA;AACA,MAAM6C,gBAAgB,GAAG5I,cAAc,CAACsI,wBAAwB,CAAC;AACjE,SAASO,sBAAsBA,CAACC,OAAO,EAAE;EACrC,OAAO7I,iBAAiB,CAAC2I,gBAAgB,EAAEE,OAAO,EAAER,wBAAwB,CAAC;AACjF;AAEA,MAAMS,aAAa,CAAC;EAChB;IAAS,IAAI,CAAC1B,IAAI,YAAA2B,sBAAAzB,CAAA;MAAA,YAAAA,CAAA,IAAyFwB,aAAa;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACE,IAAI,kBAhC+EzL,EAAE,CAAA0L,iBAAA;MAAAxB,IAAA,EAgCJqB,aAAa;MAAApB,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAhCXtK,EAAE,CAAA2L,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,uBAAAnI,EAAA,EAAAC,GAAA;MAAAmI,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAgCi1I;EAAE;AAC17I;AACA;EAAA,QAAA1B,SAAA,oBAAAA,SAAA,KAlCqGxK,EAAE,CAAAyK,iBAAA,CAkCXc,aAAa,EAAc,CAAC;IAC5GrB,IAAI,EAAE/J,SAAS;IACfuK,IAAI,EAAE,CAAC;MAAEL,UAAU,EAAE,IAAI;MAAEyB,QAAQ,EAAE,EAAE;MAAEG,aAAa,EAAE7L,iBAAiB,CAAC+L,IAAI;MAAED,eAAe,EAAE7L,uBAAuB,CAAC+L,MAAM;MAAEvB,IAAI,EAAE;QAC3HwB,KAAK,EAAE;MACX,CAAC;MAAEL,MAAM,EAAE,CAAC,2lIAA2lI;IAAE,CAAC;EACtnI,CAAC,CAAC;AAAA;AACV,MAAMM,gBAAgB,CAAC;EACnB3D,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAGrH,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACgL,QAAQ,GAAGtM,MAAM,CAACK,QAAQ,CAAC;IAChC,IAAI,CAACkM,OAAO,GAAG9J,aAAa,CAAC6I,aAAa,CAAC;IAC3C,IAAI,CAAChD,IAAI,GAAGtI,MAAM,CAACmL,gBAAgB,CAAC,CAAC7C,IAAI;IACzC,IAAI,CAACkE,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,qBAAqB,GAAG,IAAInM,YAAY,CAAC,CAAC;EACnD;EACA,IAAIoM,IAAIA,CAAA,EAAG;IACP,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACjE,EAAE,CAACkE,gBAAgB,CAAC,UAAU,CAAC,CAAC;EAC3D;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACJ,IAAI,CAAC,IAAI,CAACF,eAAe,CAAC,IAAI,IAAI;EAClD;EACAO,SAASA,CAACC,OAAO,EAAEC,IAAI,EAAE;IACrB,MAAM;MAAEP;IAAK,CAAC,GAAG,IAAI;IACrBjL,YAAY,CAACiL,IAAI,CAACQ,OAAO,CAACF,OAAO,CAAC,EAAEN,IAAI,EAAEO,IAAI,CAAC;EACnD;EACAE,kBAAkBA,CAAA,EAAG;IACjB5M,eAAe,CAAC,MAAM;MAClB,IAAI,CAAC6M,eAAe,CAAC,CAAC;IAC1B,CAAC,EAAE;MAAEd,QAAQ,EAAE,IAAI,CAACA;IAAS,CAAC,CAAC;EACnC;EACAe,UAAUA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACvB,MAAM/I,KAAK,GAAG,IAAI,CAACkI,IAAI,CAACc,SAAS,CAAEC,GAAG,IAAKA,GAAG,KAAKF,OAAO,CAAC;IAC3DD,KAAK,CAACI,eAAe,CAAC,CAAC;IACvB,IAAIlJ,KAAK,KAAK,IAAI,CAACgI,eAAe,EAAE;MAChC;IACJ;IACA,IAAI,CAACC,qBAAqB,CAACkB,IAAI,CAACnJ,KAAK,CAAC;IACtC,IAAI,CAACgI,eAAe,GAAGhI,KAAK;EAChC;EACA4I,eAAeA,CAAA,EAAG;IACd,MAAM;MAAEV,IAAI;MAAEI;IAAc,CAAC,GAAG,IAAI;IACpCJ,IAAI,CAACkB,OAAO,CAAEC,aAAa,IAAK;MAC5B,MAAMC,MAAM,GAAGD,aAAa,KAAKf,aAAa;MAC9Ce,aAAa,CAACE,SAAS,CAACC,MAAM,CAAC,SAAS,EAAEF,MAAM,CAAC;MACjDD,aAAa,CAACI,YAAY,CAAC,UAAU,EAAEH,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC;IAC/D,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAClE,IAAI,YAAAsE,yBAAApE,CAAA;MAAA,YAAAA,CAAA,IAAyFuC,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACtC,IAAI,kBAlF+EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EAkFJoC,gBAAgB;MAAA8B,QAAA;MAAAC,YAAA,WAAAC,8BAAA1K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlFd5D,EAAE,CAAAqG,UAAA,8BAAAkI,qDAAAvI,MAAA;YAAA,OAkFJnC,GAAA,CAAAyJ,UAAA,CAAAtH,MAAA,EAAAA,MAAA,CAAAwI,MAAgC,CAAC;UAAA,CAAlB,CAAC;QAAA;QAAA,IAAA5K,EAAA;UAlFd5D,EAAE,CAAAsI,WAAA,cAAAzE,GAAA,CAAA0E,IAAA;QAAA;MAAA;MAAAkG,MAAA;QAAAlG,IAAA;QAAAkE,eAAA;MAAA;MAAAiC,OAAA;QAAAhC,qBAAA;MAAA;MAAArC,UAAA;IAAA,EAkFwS;EAAE;AACjZ;AACA;EAAA,QAAAG,SAAA,oBAAAA,SAAA,KApFqGxK,EAAE,CAAAyK,iBAAA,CAoFX6B,gBAAgB,EAAc,CAAC;IAC/GpC,IAAI,EAAEhK,SAAS;IACfwK,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBQ,IAAI,EAAE;QACF,kBAAkB,EAAE,MAAM;QAC1B,CAAC,IAAIpC,gBAAgB,GAAG,GAAG;MAC/B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEF,IAAI,EAAE,CAAC;MACrB2B,IAAI,EAAEzJ;IACV,CAAC,CAAC;IAAEgM,eAAe,EAAE,CAAC;MAClBvC,IAAI,EAAEzJ;IACV,CAAC,CAAC;IAAEiM,qBAAqB,EAAE,CAAC;MACxBxC,IAAI,EAAExJ;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiO,iBAAiB,CAAC;EACpBhG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAGrH,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAAC+J,OAAO,GAAGrL,MAAM,CAACmL,gBAAgB,CAAC;IACvC,IAAI,CAACuB,IAAI,GAAG1M,MAAM,CAACqM,gBAAgB,CAAC;IACpC,IAAI,CAACsC,QAAQ,GAAG9L,WAAW;IAC3B,IAAI,CAACoG,GAAG,GAAGjJ,MAAM,CAACkB,uBAAuB,EAAE;MAAE0N,IAAI,EAAE;IAAK,CAAC,CAAC,CACrD7F,IAAI,CAAC1H,WAAW,CAAC,CAAC,EAAEL,kBAAkB,CAAC,CAAC,CAAC,CACzCsI,SAAS,CAAC,MAAM,IAAI,CAACuF,OAAO,CAAC,CAAC,CAAC;IACpC,IAAI,CAAC/D,SAAS,GAAG,IAAI,CAACO,OAAO,CAACP,SAAS;EAC3C;EACAqC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC2B,QAAQ,CAAC,IAAI,CAACpC,IAAI,CAACF,eAAe,CAAC;IACxC,IAAI,CAACqC,OAAO,CAAC,CAAC;EAClB;EACAE,cAAcA,CAAC/B,OAAO,EAAEC,IAAI,EAAE;IAC1B,IAAI,CAACP,IAAI,CAACK,SAAS,CAACC,OAAO,EAAEC,IAAI,CAAC;EACtC;EACA4B,OAAOA,CAAA,EAAG;IACN,MAAM;MAAE/B;IAAc,CAAC,GAAG,IAAI,CAACJ,IAAI;IACnC,IAAII,aAAa,IAAI,CAACA,aAAa,CAACkC,WAAW,EAAE;MAC7C;IACJ;IACA,MAAM;MAAEC,UAAU,GAAG,CAAC;MAAEC,WAAW,GAAG;IAAE,CAAC,GAAGpC,aAAa,IAAI,CAAC,CAAC;IAC/D,IAAI,CAACnE,EAAE,CAACwG,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE1M,KAAK,CAACuM,UAAU,CAAC,CAAC;IACxD,IAAI,CAACtG,EAAE,CAACwG,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE1M,KAAK,CAACwM,WAAW,CAAC,CAAC;EAC9D;EACAJ,QAAQA,CAACtK,KAAK,EAAE;IACZ,MAAM+I,OAAO,GAAG,IAAI,CAACb,IAAI,CAACA,IAAI,CAAClI,KAAK,CAAC;IACrC,IAAI,CAAC+I,OAAO,EAAE;MACV;IACJ;IACA,MAAM;MAAE0B,UAAU;MAAEC;IAAY,CAAC,GAAG3B,OAAO;IAC3C,IAAI0B,UAAU,GAAG,IAAI,CAACtG,EAAE,CAAC0G,UAAU,EAAE;MACjC,IAAI,CAAC1G,EAAE,CAAC0G,UAAU,GAAGJ,UAAU;IACnC;IACA,IAAIA,UAAU,GAAGC,WAAW,GAAG,IAAI,CAACvG,EAAE,CAAC0G,UAAU,GAAG,IAAI,CAAC1G,EAAE,CAACuG,WAAW,EAAE;MACrE,IAAI,CAACvG,EAAE,CAAC0G,UAAU,GAAGJ,UAAU,GAAGC,WAAW,GAAG,IAAI,CAACvG,EAAE,CAACuG,WAAW;IACvE;EACJ;EACA;IAAS,IAAI,CAACtF,IAAI,YAAA0F,0BAAAxF,CAAA;MAAA,YAAAA,CAAA,IAAyF4E,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAAC3E,IAAI,kBA9I+EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EA8IJyE,iBAAiB;MAAAxE,SAAA;MAAAqF,cAAA,WAAAC,iCAAA7L,EAAA,EAAAC,GAAA,EAAA6L,QAAA;QAAA,IAAA9L,EAAA;UA9If5D,EAAE,CAAA2P,cAAA,CAAAD,QAAA,EAwJRhH,MAAM;QAAA;QAAA,IAAA9E,EAAA;UAAA,IAAAgM,EAAA;UAxJA5P,EAAE,CAAA6P,cAAA,CAAAD,EAAA,GAAF5P,EAAE,CAAA8P,WAAA,QAAAjM,GAAA,CAAA+K,QAAA,GAAAgB,EAAA;QAAA;MAAA;MAAAxB,QAAA;MAAAC,YAAA,WAAA0B,+BAAAnM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5D,EAAE,CAAAqG,UAAA,0BAAA2J,kDAAA;YAAA,OA8IJnM,GAAA,CAAAiL,OAAA,CAAQ,CAAC;UAAA,CAAO,CAAC,wCAAAmB,gEAAAjK,MAAA;YAAA,OAAjBnC,GAAA,CAAAmL,cAAA,CAAAhJ,MAAA,CAAAwI,MAAA,EAA8B,CAAC,CAAC;UAAA,CAAhB,CAAC,uCAAA0B,+DAAAlK,MAAA;YAAA,OAAjBnC,GAAA,CAAAmL,cAAA,CAAAhJ,MAAA,CAAAwI,MAAA,GAA+B,CAAC,CAAC;UAAA,CAAjB,CAAC;QAAA;QAAA,IAAA5K,EAAA;UA9If5D,EAAE,CAAAmQ,WAAA,cAAAtM,GAAA,CAAAkH,SAAA,KA8IU,IAAI,GAAG,gCAAgC,GAAAlH,GAAA,CAAAkH,SAArC,CAAC;UA9If/K,EAAE,CAAA2E,WAAA,eAAAd,GAAA,CAAAkH,SA8IY,CAAC;QAAA;MAAA;MAAA0D,MAAA;QAAA1D,SAAA;MAAA;MAAAV,UAAA;MAAAC,QAAA,GA9IftK,EAAE,CAAAoQ,kBAAA,CA8Iyd,CACpjBjP,uBAAuB,EACvB;QACIkP,OAAO,EAAEjP,yBAAyB;QAClCkP,QAAQ,EAAE;UACNC,SAAS,EAAE,IAAI;UACfC,aAAa,EAAE,IAAI;UACnBC,OAAO,EAAE;QACb;MACJ,CAAC,CACJ,GAxJ4FzQ,EAAE,CAAAuK,uBAAA;QAAAmG,SAAA,EAwJqCpE,gBAAgB;QAAAmC,MAAA;QAAAC,OAAA;MAAA;IAAA,EAAiJ;EAAE;AAC/S;AACA7L,UAAU,CAAC,CACPD,OAAO,CACV,EAAE+L,iBAAiB,CAACgC,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC;AACjD;EAAA,QAAAnG,SAAA,oBAAAA,SAAA,KA7JqGxK,EAAE,CAAAyK,iBAAA,CA6JXkE,iBAAiB,EAAc,CAAC;IAChHzE,IAAI,EAAEhK,SAAS;IACfwK,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBM,QAAQ,EAAE,wDAAwD;MAClEiG,SAAS,EAAE,CACPzP,uBAAuB,EACvB;QACIkP,OAAO,EAAEjP,yBAAyB;QAClCkP,QAAQ,EAAE;UACNC,SAAS,EAAE,IAAI;UACfC,aAAa,EAAE,IAAI;UACnBC,OAAO,EAAE;QACb;MACJ,CAAC,CACJ;MACD7F,cAAc,EAAE,CACZ;QACI8F,SAAS,EAAEpE,gBAAgB;QAC3BmC,MAAM,EAAE,CAAC,iBAAiB,EAAE,MAAM,CAAC;QACnCC,OAAO,EAAE,CAAC,uBAAuB;MACrC,CAAC,CACJ;MACD7D,IAAI,EAAE;QACF,oBAAoB,EAAE,WAAW;QACjC,mBAAmB,EAAE,mEAAmE;QACxF,gBAAgB,EAAE,WAAW;QAC7B,8BAA8B,EAAE,kCAAkC;QAClE,6BAA6B,EAAE;MACnC;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE+D,QAAQ,EAAE,CAAC;MACzB1E,IAAI,EAAEvJ,eAAe;MACrB+J,IAAI,EAAE,CAAC9J,UAAU,CAAC,MAAM8H,MAAM,CAAC;IACnC,CAAC,CAAC;IAAEqC,SAAS,EAAE,CAAC;MACZb,IAAI,EAAEzJ;IACV,CAAC,CAAC;IAAEsO,QAAQ,EAAE;EAAG,CAAC;AAAA;AAE9B,MAAM8B,eAAe,CAAC;EAClBlI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgE,IAAI,GAAG1M,MAAM,CAACqM,gBAAgB,CAAC;IACpC,IAAI,CAACwE,QAAQ,GAAG,MAAM;EAC1B;EACA9B,cAAcA,CAAC/B,OAAO,EAAEC,IAAI,EAAE;IAC1B,IAAI,CAACP,IAAI,CAACK,SAAS,CAACC,OAAO,EAAEC,IAAI,CAAC;EACtC;EACA;IAAS,IAAI,CAACrD,IAAI,YAAAkH,wBAAAhH,CAAA;MAAA,YAAAA,CAAA,IAAyF8G,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAAC7G,IAAI,kBA5M+EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EA4MJ2G,eAAe;MAAA1G,SAAA;MAAAiE,QAAA;MAAAC,YAAA,WAAA2C,6BAAApN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5Mb5D,EAAE,CAAAqG,UAAA,uCAAA4K,6DAAAjL,MAAA;YAAA,OA4MJnC,GAAA,CAAAmL,cAAA,CAAAhJ,MAAA,CAAAwI,MAAA,EAA8B,CAAC,CAAC;UAAA,CAAlB,CAAC,qCAAA0C,2DAAAlL,MAAA;YAAA,OAAfnC,GAAA,CAAAmL,cAAA,CAAAhJ,MAAA,CAAAwI,MAAA,GAA+B,CAAC,CAAC;UAAA,CAAnB,CAAC;QAAA;QAAA,IAAA5K,EAAA;UA5Mb5D,EAAE,CAAAsI,WAAA,kBAAAzE,GAAA,CAAAiN,QAAA;QAAA;MAAA;MAAArC,MAAA;QAAAqC,QAAA;MAAA;MAAAzG,UAAA;MAAAC,QAAA,GAAFtK,EAAE,CAAAuK,uBAAA;QAAAmG,SAAA,EA4MqWpE,gBAAgB;QAAAmC,MAAA;QAAAC,OAAA;MAAA;IAAA,EAAiJ;EAAE;AAC/mB;AACA;EAAA,QAAAlE,SAAA,oBAAAA,SAAA,KA9MqGxK,EAAE,CAAAyK,iBAAA,CA8MXoG,eAAe,EAAc,CAAC;IAC9G3G,IAAI,EAAEhK,SAAS;IACfwK,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBM,QAAQ,EAAE,4CAA4C;MACtDC,cAAc,EAAE,CACZ;QACI8F,SAAS,EAAEpE,gBAAgB;QAC3BmC,MAAM,EAAE,CAAC,iBAAiB,EAAE,MAAM,CAAC;QACnCC,OAAO,EAAE,CAAC,uBAAuB;MACrC,CAAC,CACJ;MACD7D,IAAI,EAAE;QACF,sBAAsB,EAAE,UAAU;QAClC,6BAA6B,EAAE,kCAAkC;QACjE,2BAA2B,EAAE;MACjC;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEiG,QAAQ,EAAE,CAAC;MACzB5G,IAAI,EAAEzJ;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM0Q,gBAAgB,GAAG3O,cAAc,CAAC,CAAC;AACzC,MAAM4O,kBAAkB,GAAG,CACvB1N,qBAAqB,EACrBvC,uBAAuB,EACvBkC,0BAA0B,CAAC;EAAEgO,KAAK,EAAE;AAAQ,CAAC,CAAC,EAC9C;EACIhB,OAAO,EAAEjP,yBAAyB;EAClCkP,QAAQ,EAAE;IACNC,SAAS,EAAE,IAAI;IACfE,OAAO,EAAE,IAAI;IACbD,aAAa,EAAE;EACnB;AACJ,CAAC,EACD;EACIH,OAAO,EAAEc,gBAAgB;EACzBG,IAAI,EAAE,CACF5N,qBAAqB,EACrBvC,uBAAuB,EACvB6B,QAAQ,EACRnC,UAAU,EACVC,iBAAiB,CACpB;EACDyQ,UAAU,EAAEA,CAACC,OAAO,EAAEC,UAAU,EAAE;IAAEC;EAAK,CAAC,EAAE;IAAE5D;EAAc,CAAC,EAAE6D,GAAG,KAAK5P,KAAK,CAACyP,OAAO,EAAEC,UAAU,CAACzI,IAAI,CAAC7G,GAAG,CAAC,MAAMwP,GAAG,CAACC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC5I,IAAI;EAC3I;EACAlH,MAAM,CAAC,MAAM4P,IAAI,CAACG,QAAQ,CAAC/D,aAAa,CAAC,CAAC,EAAE1L,YAAY,CAAC,CAAC,CAAC,EAAEC,SAAS,CAAC,IAAI,CAAC,EAAEpB,kBAAkB,CAAC,CAAC;AACtG,CAAC,CACJ;AAED,MAAM6Q,eAAe,CAAC;EAClBnJ,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2C,OAAO,GAAGrL,MAAM,CAACmL,gBAAgB,CAAC;IACvC,IAAI,CAAC2G,QAAQ,GAAG9R,MAAM,CAACkR,gBAAgB,CAAC;IACxC,IAAI,CAACvI,EAAE,GAAGrH,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACoQ,GAAG,GAAG1R,MAAM,CAACa,iBAAiB,CAAC;IACpC,IAAI,CAACkR,QAAQ,GAAG9G,QAAQ;IACxB,IAAI,CAAC1C,KAAK,GAAG1F,WAAW;IACxB,IAAI,CAACwE,SAAS,GAAGrH,MAAM,CAACuD,aAAa,CAAC;IACtC,IAAI,CAAC2C,IAAI,GAAG,KAAK;IACjB,IAAI,CAACoC,IAAI,GAAG,IAAI,CAAC+C,OAAO,CAAC/C,IAAI;IAC7B,IAAI,CAACwC,SAAS,GAAG,IAAI,CAACO,OAAO,CAACP,SAAS;IACvC,IAAI,CAACE,UAAU,GAAG,IAAI,CAACK,OAAO,CAACL,UAAU;IACzC,IAAI,CAACyB,qBAAqB,GAAG,IAAInM,YAAY,CAAC,CAAC;IAC/C,IAAI,CAACkM,eAAe,GAAG,CAAC;EAC5B;EACA,IAAIwF,SAASA,CAACxF,eAAe,EAAE;IAC3B,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACuF,QAAQ,GAAG,IAAI,CAACE,WAAW,CAAC,CAAC;EACtC;EACA,IAAI7M,gBAAgBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC4F,UAAU,GAAG,CAAC,IAAI,IAAI,CAACzC,KAAK,CAAC2J,MAAM,EAAE;MAC1C,OAAO,IAAI,CAACH,QAAQ;IACxB;IACA,MAAMI,MAAM,GAAG,IAAI,CAACnH,UAAU,GAAG,CAAC,GAAG,IAAI,CAACwB,eAAe,IAAI,CAAC,IAAI,CAACnB,OAAO,CAACN,YAAY,GACjF,CAAC,GACD,CAAC;IACP,OAAOqH,IAAI,CAACC,GAAG,CAAC,IAAI,CAACrH,UAAU,GAAGmH,MAAM,EAAE,IAAI,CAACJ,QAAQ,CAAC;EAC5D;EACAO,eAAeA,CAAA,EAAG;IACd,IAAI,CAACR,QAAQ,CACR/I,IAAI,CAAC1G,GAAG,CAAC,MAAM,IAAI,CAAC4P,WAAW,CAAC,CAAC,CAAC,EAAE/P,GAAG,CAAC,MAAM,IAAI,CAAC2M,OAAO,CAAC,CAAC,CAAC,EAAEhN,MAAM,CAAEkQ,QAAQ,IAAK,IAAI,CAACA,QAAQ,KAAKA,QAAQ,CAAC,CAAC,CAChHzI,SAAS,CAAEyI,QAAQ,IAAK;MACzB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACL,GAAG,CAACC,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACAxE,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC0B,OAAO,CAAC,CAAC;EAClB;EACA;EACA,IAAInC,IAAIA,CAAA,EAAG;IACP,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACjE,EAAE,CAACkE,gBAAgB,CAAC,UAAU,CAAC,CAAC;EAC3D;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,MAAM;MAAEJ;IAAK,CAAC,GAAG,IAAI;IACrB,MAAM6F,eAAe,GAAGtP,QAAQ,CAAC,IAAI,CAACuJ,eAAe,IAAI,CAAC,EAAE,CAAC,EAAEE,IAAI,CAACwF,MAAM,GAAG,CAAC,CAAC;IAC/E,OAAO,IAAI,CAAC7G,OAAO,CAACN,YAAY,IAAI,IAAI,CAAC3F,gBAAgB,IAAImN,eAAe,GACtE7F,IAAI,CAAC6F,eAAe,CAAC,IAAI,IAAI,GAC7B,IAAI,CAACC,UAAU,EAAE3E,aAAa,IAAI,IAAI;EAChD;EACA,IAAIpH,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACrB,gBAAgB,GAAG,CAAC,IAAI,CAAC,IAAI,CAACiG,OAAO,CAACN,YAAY;EAClE;EACA,IAAIrE,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACtB,gBAAgB,GAAG,IAAI,CAACmD,KAAK,CAAC2J,MAAM,GAAG,CAAC;EACxD;EACA,IAAIvL,eAAeA,CAAA,EAAG;IAClB,OAAO,CAAC,CAAC,IAAI,CAAC6L,UAAU,IAAIhR,kBAAkB,CAAC,IAAI,CAACgR,UAAU,CAAC3E,aAAa,CAAC;EACjF;EACA,IAAIrH,YAAYA,CAAA,EAAG;IACf,OAAQ,IAAI,CAACN,IAAI,IACZ,CAAC,IAAI,CAACmF,OAAO,CAACN,YAAY,IAAI,IAAI,CAAC3F,gBAAgB,GAAG,IAAI,CAACoH,eAAgB;EACpF;EACAiG,uBAAuBA,CAACjG,eAAe,EAAE;IACrC,IAAI,CAACkG,qBAAqB,CAAClG,eAAe,CAAC;EAC/C;EACA3E,OAAOA,CAACrD,KAAK,EAAE;IACX,IAAI,CAAC0B,IAAI,GAAG,KAAK;IACjB,IAAI,CAACyM,SAAS,CAAC,CAAC;IAChB,IAAI,CAACD,qBAAqB,CAAClO,KAAK,CAAC;EACrC;EACAoO,YAAYA,CAACtF,KAAK,EAAE;IAChB,IAAI/L,YAAY,CAAC+L,KAAK,CAACiB,MAAM,CAAC,IAAI/M,kBAAkB,CAAC8L,KAAK,CAACiB,MAAM,CAAC,EAAE;MAChE,IAAI,CAACoE,SAAS,CAAC,CAAC;IACpB;EACJ;EACArM,WAAWA,CAAA,EAAG;IACV,MAAM;MAAEoG;IAAK,CAAC,GAAG,IAAI;IACrB,IAAIlI,KAAK,GAAGkI,IAAI,CAACwF,MAAM,GAAG,CAAC;IAC3B,OAAO1N,KAAK,IAAI,CAAC,EAAE;MACfkI,IAAI,CAAClI,KAAK,CAAC,EAAEqO,KAAK,CAAC,CAAC;MACpB,IAAIrR,kBAAkB,CAACkL,IAAI,CAAClI,KAAK,CAAC,CAAC,EAAE;QACjC;MACJ;MACAA,KAAK,EAAE;IACX;EACJ;EACA2D,cAAcA,CAACmF,KAAK,EAAEwF,OAAO,EAAEC,QAAQ,EAAE;IACrC,MAAMC,MAAM,GAAG1F,KAAK,CAACiB,MAAM;IAC3B,MAAMA,MAAM,GAAG7M,sBAAsB,CAAC;MAAEuR,OAAO,EAAED,MAAM;MAAEE,IAAI,EAAEJ,OAAO;MAAEC;IAAS,CAAC,CAAC;IACnF,IAAIxE,MAAM,EAAE;MACRA,MAAM,CAACsE,KAAK,CAAC,CAAC;IAClB;EACJ;EACAlO,WAAWA,CAACH,KAAK,EAAE;IACf,OAAOA,KAAK,KAAK,IAAI,CAACgI,eAAe,IAAI,CAAC,IAAI,CAACnB,OAAO,CAACN,YAAY;EACvE;EACAjD,UAAUA,CAACtD,KAAK,EAAE;IACd,OAAOA,KAAK,GAAG,IAAI,CAACY,gBAAgB,IAAI,IAAI,CAACT,WAAW,CAACH,KAAK,CAAC;EACnE;EACA,IAAI2O,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC7K,IAAI,KAAK,GAAG,GAAG,EAAE,GAAG,EAAE;EACtC;EACAqK,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACH,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC3E,aAAa,CAACgF,KAAK,CAAC,CAAC;IACzC;EACJ;EACAZ,WAAWA,CAAA,EAAG;IACV,MAAM;MAAEvF,IAAI;MAAEF,eAAe;MAAE2G;IAAO,CAAC,GAAG,IAAI;IAC9C,IAAIzG,IAAI,CAACwF,MAAM,GAAG,CAAC,EAAE;MACjB,OAAO,CAAC;IACZ;IACA,MAAM;MAAEnH,YAAY;MAAEG;IAAa,CAAC,GAAG,IAAI,CAACG,OAAO;IACnD,MAAM;MAAE+H;IAAY,CAAC,GAAG,IAAI,CAACzK,EAAE;IAC/B,MAAMmF,MAAM,GAAGpB,IAAI,CAACF,eAAe,CAAC;IACpC,MAAM6G,WAAW,GAAGvF,MAAM,EAAEwF,WAAW,IAAI,CAAC;IAC5C,MAAMC,SAAS,GAAGnB,IAAI,CAACoB,GAAG,CAAC9G,IAAI,CAACA,IAAI,CAACwF,MAAM,GAAG,CAAC,CAAC,EAAEoB,WAAW,IAAI,CAAC,EAAEpI,YAAY,CAAC;IACjF,IAAI6G,QAAQ,GAAGrF,IAAI,CAACwF,MAAM,GAAG,CAAC;IAC9B,IAAIuB,KAAK,GAAG/G,IAAI,CAACgH,MAAM,CAAC,CAACC,GAAG,EAAE;MAAEL;IAAY,CAAC,KAAKK,GAAG,GAAGL,WAAW,EAAE,CAAC,CAAC,GACnEvB,QAAQ,GAAGoB,MAAM,IAChBzG,IAAI,CAACA,IAAI,CAACwF,MAAM,GAAG,CAAC,CAAC,EAAEoB,WAAW,IAAI,CAAC,CAAC;IAC7C,IAAIM,MAAM,CAACC,KAAK,CAACJ,KAAK,CAAC,IAAIA,KAAK,IAAIL,WAAW,EAAE;MAC7C,OAAOnI,QAAQ;IACnB;IACA,OAAO8G,QAAQ,EAAE;MACb0B,KAAK,IAAI,CAAC/G,IAAI,CAACqF,QAAQ,CAAC,EAAEuB,WAAW,IAAI,CAAC,IAAIH,MAAM;MACpDpB,QAAQ,EAAE;MACV,MAAM+B,eAAe,GAAG/I,YAAY,IAAIyB,eAAe,GAAGuF,QAAQ;MAClE,MAAMgC,YAAY,GAAGD,eAAe,GAAGT,WAAW,GAAGF,MAAM,GAAG,CAAC;MAC/D,MAAMa,YAAY,GAAGP,KAAK,GAAGM,YAAY,GAAGR,SAAS,GAAGJ,MAAM;MAC9D;MACA,MAAMc,YAAY,GAAG/Q,QAAQ,CAAC,IAAI,CAAC6O,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC;MAC7D,IAAIiC,YAAY,GAAGC,YAAY,GAAGb,WAAW,EAAE;QAC3C,OAAOrB,QAAQ;MACnB;IACJ;IACA,OAAO,CAAC,CAAC;EACb;EACAW,qBAAqBA,CAAClG,eAAe,EAAE;IACnC,IAAI,CAACwF,SAAS,GAAGxF,eAAe;IAChC,IAAI,CAACC,qBAAqB,CAACkB,IAAI,CAACnB,eAAe,CAAC;EACpD;EACAqC,OAAOA,CAAA,EAAG;IACN,MAAM;MAAEI,UAAU,GAAG,CAAC;MAAEC,WAAW,GAAG;IAAE,CAAC,GAAG,IAAI,CAACpC,aAAa,IAAI,CAAC,CAAC;IACpE,IAAI,CAACoH,GAAG,EAAErG,aAAa,CAACsB,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE1M,KAAK,CAACuM,UAAU,CAAC,CAAC;IACxE,IAAI,CAACiF,GAAG,EAAErG,aAAa,CAACsB,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE1M,KAAK,CAACwM,WAAW,CAAC,CAAC;EAC9E;EACA;IAAS,IAAI,CAACtF,IAAI,YAAAuK,wBAAArK,CAAA;MAAA,YAAAA,CAAA,IAAyF+H,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACrG,IAAI,kBAtZ+EzL,EAAE,CAAA0L,iBAAA;MAAAxB,IAAA,EAsZJ4H,eAAe;MAAA3H,SAAA;MAAAqF,cAAA,WAAA6E,+BAAAzQ,EAAA,EAAAC,GAAA,EAAA6L,QAAA;QAAA,IAAA9L,EAAA;UAtZb5D,EAAE,CAAA2P,cAAA,CAAAD,QAAA,EAsZ8cnN,OAAO,KAAQxB,WAAW;QAAA;QAAA,IAAA6C,EAAA;UAAA,IAAAgM,EAAA;UAtZ1e5P,EAAE,CAAA6P,cAAA,CAAAD,EAAA,GAAF5P,EAAE,CAAA8P,WAAA,QAAAjM,GAAA,CAAA2E,KAAA,GAAAoH,EAAA;QAAA;MAAA;MAAA0E,SAAA,WAAAC,sBAAA3Q,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5D,EAAE,CAAAwU,WAAA,CAsZijB9L,MAAM,KAA2B7H,UAAU;UAtZ9lBb,EAAE,CAAAwU,WAAA,CAsZ+oB7F,iBAAiB,KAA2B9N,UAAU;QAAA;QAAA,IAAA+C,EAAA;UAAA,IAAAgM,EAAA;UAtZvsB5P,EAAE,CAAA6P,cAAA,CAAAD,EAAA,GAAF5P,EAAE,CAAA8P,WAAA,QAAAjM,GAAA,CAAA4O,UAAA,GAAA7C,EAAA,CAAA6E,KAAA;UAAFzU,EAAE,CAAA6P,cAAA,CAAAD,EAAA,GAAF5P,EAAE,CAAA8P,WAAA,QAAAjM,GAAA,CAAAsQ,GAAA,GAAAvE,EAAA,CAAA6E,KAAA;QAAA;MAAA;MAAArG,QAAA;MAAAC,YAAA,WAAAqG,6BAAA9Q,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5D,EAAE,CAAAsI,WAAA,cAAAzE,GAAA,CAAA0E,IAAA;QAAA;MAAA;MAAAkG,MAAA;QAAAlG,IAAA;QAAAxB,WAAA;QAAAF,eAAA;QAAAkE,SAAA;QAAAE,UAAA;QAAAgH,SAAA,GAAFjS,EAAE,CAAA2U,YAAA,CAAAxI,IAAA;MAAA;MAAAuC,OAAA;QAAAhC,qBAAA;MAAA;MAAArC,UAAA;MAAAC,QAAA,GAAFtK,EAAE,CAAAoQ,kBAAA,CAsZ4YgB,kBAAkB,GAtZhapR,EAAE,CAAA2L,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAA+I,MAAA;MAAA9I,QAAA,WAAA+I,yBAAAjR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAkR,GAAA,GAAF9U,EAAE,CAAA6F,gBAAA;UAAF7F,EAAE,CAAAgF,UAAA,IAAArB,uCAAA,yBAsZkxB,CAAC;UAtZrxB3D,EAAE,CAAAoH,MAAA;UAAFpH,EAAE,CAAAqE,cAAA,iBAsZygC,CAAC;UAtZ5gCrE,EAAE,CAAAqG,UAAA,mCAAA0O,mEAAA/O,MAAA;YAAFhG,EAAE,CAAAiG,aAAA,CAAA6O,GAAA;YAAA,OAAF9U,EAAE,CAAAoG,WAAA,CAsZm7BvC,GAAA,CAAA6O,uBAAA,CAAA1M,MAA8B,CAAC;UAAA,CAAC,CAAC,gCAAAgP,gEAAAhP,MAAA;YAtZt9BhG,EAAE,CAAAiG,aAAA,CAAA6O,GAAA;YAAA,OAAF9U,EAAE,CAAAoG,WAAA,CAsZi/BvC,GAAA,CAAAgP,YAAA,CAAA7M,MAAmB,CAAC;UAAA,CAAC,CAAC;UAtZzgChG,EAAE,CAAAgF,UAAA,IAAAF,uCAAA,yBAsZ6kC,CAAC;UAtZhlC9E,EAAE,CAAAsE,YAAA,CAsZigD,CAAC;UAtZpgDtE,EAAE,CAAAgF,UAAA,IAAAW,iCAAA,oBAsZ+5D,CAAC,IAAAqB,sCAAA,iCAtZl6DhH,EAAE,CAAAiF,sBAsZ2iE,CAAC,IAAA+C,sCAAA,gCAtZ9iEhI,EAAE,CAAAiF,sBAsZ2jF,CAAC;QAAA;QAAA,IAAArB,EAAA;UAAA,MAAAqR,WAAA,GAtZ9jFjV,EAAE,CAAAoF,WAAA;UAAFpF,EAAE,CAAAmE,UAAA,SAAFnE,EAAE,CAAAqH,WAAA,OAAAxD,GAAA,CAAA2E,KAAA,CAAA0M,OAAA,CAsZ6wB,CAAC;UAtZhxBlV,EAAE,CAAA6E,SAAA,EAsZ81B,CAAC;UAtZj2B7E,EAAE,CAAAmE,UAAA,oBAAAN,GAAA,CAAA4I,eAsZ81B,CAAC,SAAA5I,GAAA,CAAA0E,IAAoB,CAAC,cAAA1E,GAAA,CAAAkH,SAA8B,CAAC;UAtZr5B/K,EAAE,CAAA6E,SAAA,CAsZyjC,CAAC;UAtZ5jC7E,EAAE,CAAAmE,UAAA,YAAAN,GAAA,CAAA2E,KAsZyjC,CAAC;UAtZ5jCxI,EAAE,CAAA6E,SAAA,CAsZuiD,CAAC;UAtZ1iD7E,EAAE,CAAAmE,UAAA,SAAAN,GAAA,CAAAkD,WAsZuiD,CAAC,aAAAkO,WAAW,CAAC;QAAA;MAAA;MAAAE,YAAA,GAA8+FlS,YAAY,EAA+BF,IAAI,CAACqS,OAAO,EAAmHrS,IAAI,CAACsS,IAAI,EAA6FtS,IAAI,CAACuS,gBAAgB,EAA+IvS,IAAI,CAACwS,SAAS,EAA8C9R,kBAAkB,EAA8HF,UAAU,EAAiFH,EAAE,CAACoS,oBAAoB,EAAiJpS,EAAE,CAACqS,eAAe,EAAuM/M,MAAM,EAAiIiG,iBAAiB;MAAA3C,MAAA;MAAAE,eAAA;IAAA,EAAqJ;EAAE;AACphM;AACA;EAAA,QAAA1B,SAAA,oBAAAA,SAAA,KAxZqGxK,EAAE,CAAAyK,iBAAA,CAwZXqH,eAAe,EAAc,CAAC;IAC9G5H,IAAI,EAAE/J,SAAS;IACfuK,IAAI,EAAE,CAAC;MAAEL,UAAU,EAAE,IAAI;MAAEM,QAAQ,EAAE,0CAA0C;MAAE+K,OAAO,EAAE,CAC9EzS,YAAY,EACZQ,kBAAkB,EAClBF,UAAU,EACVD,WAAW,EACXoF,MAAM,EACNiG,iBAAiB,CACpB;MAAEzC,eAAe,EAAE7L,uBAAuB,CAAC+L,MAAM;MAAEwE,SAAS,EAAEQ,kBAAkB;MAAEvG,IAAI,EAAE;QACrF,kBAAkB,EAAE;MACxB,CAAC;MAAEiB,QAAQ,EAAE,88EAA88E;MAAEE,MAAM,EAAE,CAAC,2zCAA2zC;IAAE,CAAC;EAChzH,CAAC,CAAC,QAAkB;IAAEyG,UAAU,EAAE,CAAC;MAC3BvI,IAAI,EAAElJ,SAAS;MACf0J,IAAI,EAAE,CAAChC,MAAM,EAAE;QAAEiN,IAAI,EAAE9U;MAAW,CAAC;IACvC,CAAC,CAAC;IAAEsT,GAAG,EAAE,CAAC;MACNjK,IAAI,EAAElJ,SAAS;MACf0J,IAAI,EAAE,CAACiE,iBAAiB,EAAE;QAAEgH,IAAI,EAAE9U;MAAW,CAAC;IAClD,CAAC,CAAC;IAAE2H,KAAK,EAAE,CAAC;MACR0B,IAAI,EAAEvJ,eAAe;MACrB+J,IAAI,EAAE,CAACnI,OAAO,EAAE;QAAEoT,IAAI,EAAE5U;MAAY,CAAC;IACzC,CAAC,CAAC;IAAEwH,IAAI,EAAE,CAAC;MACP2B,IAAI,EAAEzJ;IACV,CAAC,CAAC;IAAEsG,WAAW,EAAE,CAAC;MACdmD,IAAI,EAAEzJ;IACV,CAAC,CAAC;IAAEoG,eAAe,EAAE,CAAC;MAClBqD,IAAI,EAAEzJ;IACV,CAAC,CAAC;IAAEsK,SAAS,EAAE,CAAC;MACZb,IAAI,EAAEzJ;IACV,CAAC,CAAC;IAAEwK,UAAU,EAAE,CAAC;MACbf,IAAI,EAAEzJ;IACV,CAAC,CAAC;IAAEiM,qBAAqB,EAAE,CAAC;MACxBxC,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEuR,SAAS,EAAE,CAAC;MACZ/H,IAAI,EAAEzJ,KAAK;MACXiK,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkL,OAAO,GAAG,CACZrT,OAAO,EACPmG,MAAM,EACN4D,gBAAgB,EAChBqC,iBAAiB,EACjBkC,eAAe,EACfiB,eAAe,CAClB;;AAED;AACA;AACA;;AAEA,SAAShH,wBAAwB,EAAEM,gBAAgB,EAAEgG,kBAAkB,EAAED,gBAAgB,EAAE1I,gBAAgB,EAAEC,MAAM,EAAEkN,OAAO,EAAEtJ,gBAAgB,EAAEqC,iBAAiB,EAAEkC,eAAe,EAAEiB,eAAe,EAAEzG,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}