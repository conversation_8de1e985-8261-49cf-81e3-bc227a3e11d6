{"ast": null, "code": "import { TuiTreeItemContent } from '@taiga-ui/kit';\nimport * as i0 from \"@angular/core\";\nexport class TreeContentComponent extends TuiTreeItemContent {\n  get icon() {\n    return this.isExpandable ? 'bi-folder-fill text-warning' : 'bi-file-earmark text-primary';\n  }\n  onCheckboxChange(event) {\n    const checked = event.target.checked;\n    this.context.toggle(checked);\n  }\n  static {\n    this.ɵfac = /*@__PURE__*/(() => {\n      let ɵTreeContentComponent_BaseFactory;\n      return function TreeContentComponent_Factory(t) {\n        return (ɵTreeContentComponent_BaseFactory || (ɵTreeContentComponent_BaseFactory = i0.ɵɵgetInheritedFactory(TreeContentComponent)))(t || TreeContentComponent);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TreeContentComponent,\n      selectors: [[\"app-tree-content\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 9,\n      vars: 7,\n      consts: [[1, \"tui-tree-content\"], [1, \"bi\", \"me-2\", 3, \"ngClass\"], [1, \"tui-tree-content-label\"], [1, \"tui-tree-content-price\", \"ms-auto\"], [1, \"tui-tree-content-checkbox\", \"ms-2\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"checked\"]],\n      template: function TreeContentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"i\", 1);\n          i0.ɵɵelementStart(2, \"span\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"span\", 3);\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"currency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"input\", 5);\n          i0.ɵɵlistener(\"change\", function TreeContentComponent_Template_input_change_8_listener($event) {\n            return ctx.onCheckboxChange($event);\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", ctx.icon);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.context.node.text);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 4, ctx.context.node.price, \"EUR\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"checked\", ctx.context.node.selected);\n        }\n      },\n      styles: [\".tui-tree-content[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      padding: 8px 12px;\\n      border-radius: 4px;\\n      transition: background-color 0.2s;\\n    }\\n    \\n    .tui-tree-content[_ngcontent-%COMP%]:hover {\\n      background-color: #f8f9fa;\\n    }\\n    \\n    .tui-tree-content-label[_ngcontent-%COMP%] {\\n      flex: 1;\\n    }\\n    \\n    .tui-tree-content-price[_ngcontent-%COMP%] {\\n      font-weight: bold;\\n      color: #28a745;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9jcHEtY29uZmlndXJhdG9yL3RyZWUtY29udGVudC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJO01BQ0UsYUFBYTtNQUNiLG1CQUFtQjtNQUNuQixpQkFBaUI7TUFDakIsa0JBQWtCO01BQ2xCLGlDQUFpQztJQUNuQzs7SUFFQTtNQUNFLHlCQUF5QjtJQUMzQjs7SUFFQTtNQUNFLE9BQU87SUFDVDs7SUFFQTtNQUNFLGlCQUFpQjtNQUNqQixjQUFjO0lBQ2hCIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLnR1aS10cmVlLWNvbnRlbnQge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBwYWRkaW5nOiA4cHggMTJweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4ycztcbiAgICB9XG4gICAgXG4gICAgLnR1aS10cmVlLWNvbnRlbnQ6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcbiAgICB9XG4gICAgXG4gICAgLnR1aS10cmVlLWNvbnRlbnQtbGFiZWwge1xuICAgICAgZmxleDogMTtcbiAgICB9XG4gICAgXG4gICAgLnR1aS10cmVlLWNvbnRlbnQtcHJpY2Uge1xuICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gICAgICBjb2xvcjogIzI4YTc0NTtcbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["TuiTreeItemContent", "TreeContentComponent", "icon", "isExpandable", "onCheckboxChange", "event", "checked", "target", "context", "toggle", "t", "selectors", "standalone", "features", "i0", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TreeContentComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TreeContentComponent_Template_input_change_8_listener", "$event", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate", "node", "text", "ɵɵpipeBind2", "price", "selected"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet_CRM\\apps\\AngularCPQapp\\src\\app\\components\\cpq-configurator\\tree-content.component.ts"], "sourcesContent": ["import { NgTemplateOutlet } from '@angular/common';\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\nimport { TuiTreeItemContent } from '@taiga-ui/kit';\n\n@Component({\n  selector: 'app-tree-content',\n  standalone: true,\n  imports: [NgTemplateOutlet],\n  template: `\n    <div class=\"tui-tree-content\">\n      <i \n        class=\"bi me-2\"\n        [ngClass]=\"icon\"\n      ></i>\n      <span class=\"tui-tree-content-label\">{{ context.node.text }}</span>\n      <span class=\"tui-tree-content-price ms-auto\">{{ context.node.price | currency:'EUR' }}</span>\n      <div class=\"tui-tree-content-checkbox ms-2\">\n        <input \n          type=\"checkbox\" \n          class=\"form-check-input\"\n          [checked]=\"context.node.selected\"\n          (change)=\"onCheckboxChange($event)\"\n        >\n      </div>\n    </div>\n  `,\n  styles: [`\n    .tui-tree-content {\n      display: flex;\n      align-items: center;\n      padding: 8px 12px;\n      border-radius: 4px;\n      transition: background-color 0.2s;\n    }\n    \n    .tui-tree-content:hover {\n      background-color: #f8f9fa;\n    }\n    \n    .tui-tree-content-label {\n      flex: 1;\n    }\n    \n    .tui-tree-content-price {\n      font-weight: bold;\n      color: #28a745;\n    }\n  `],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TreeContentComponent extends TuiTreeItemContent {\n  get icon(): string {\n    return this.isExpandable ? 'bi-folder-fill text-warning' : 'bi-file-earmark text-primary';\n  }\n\n  onCheckboxChange(event: Event): void {\n    const checked = (event.target as HTMLInputElement).checked;\n    this.context.toggle(checked);\n  }\n}"], "mappings": "AAEA,SAASA,kBAAkB,QAAQ,eAAe;;AAgDlD,OAAM,MAAOC,oBAAqB,SAAQD,kBAAkB;EAC1D,IAAIE,IAAIA,CAAA;IACN,OAAO,IAAI,CAACC,YAAY,GAAG,6BAA6B,GAAG,8BAA8B;EAC3F;EAEAC,gBAAgBA,CAACC,KAAY;IAC3B,MAAMC,OAAO,GAAID,KAAK,CAACE,MAA2B,CAACD,OAAO;IAC1D,IAAI,CAACE,OAAO,CAACC,MAAM,CAACH,OAAO,CAAC;EAC9B;;;;;mHARWL,oBAAoB,IAAAS,CAAA,IAApBT,oBAAoB;MAAA;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAU,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,0BAAA,EAAAD,EAAA,CAAAE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzC7BR,EAAA,CAAAU,cAAA,aAA8B;UAC5BV,EAAA,CAAAW,SAAA,WAGK;UACLX,EAAA,CAAAU,cAAA,cAAqC;UAAAV,EAAA,CAAAY,MAAA,GAAuB;UAAAZ,EAAA,CAAAa,YAAA,EAAO;UACnEb,EAAA,CAAAU,cAAA,cAA6C;UAAAV,EAAA,CAAAY,MAAA,GAAyC;;UAAAZ,EAAA,CAAAa,YAAA,EAAO;UAE3Fb,EADF,CAAAU,cAAA,aAA4C,eAMzC;UADCV,EAAA,CAAAc,UAAA,oBAAAC,sDAAAC,MAAA;YAAA,OAAUP,GAAA,CAAAnB,gBAAA,CAAA0B,MAAA,CAAwB;UAAA,EAAC;UAGzChB,EAPI,CAAAa,YAAA,EAKC,EACG,EACF;;;UAZFb,EAAA,CAAAiB,SAAA,EAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,YAAAT,GAAA,CAAArB,IAAA,CAAgB;UAEmBY,EAAA,CAAAiB,SAAA,GAAuB;UAAvBjB,EAAA,CAAAmB,iBAAA,CAAAV,GAAA,CAAAf,OAAA,CAAA0B,IAAA,CAAAC,IAAA,CAAuB;UACfrB,EAAA,CAAAiB,SAAA,GAAyC;UAAzCjB,EAAA,CAAAmB,iBAAA,CAAAnB,EAAA,CAAAsB,WAAA,OAAAb,GAAA,CAAAf,OAAA,CAAA0B,IAAA,CAAAG,KAAA,SAAyC;UAKlFvB,EAAA,CAAAiB,SAAA,GAAiC;UAAjCjB,EAAA,CAAAkB,UAAA,YAAAT,GAAA,CAAAf,OAAA,CAAA0B,IAAA,CAAAI,QAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}