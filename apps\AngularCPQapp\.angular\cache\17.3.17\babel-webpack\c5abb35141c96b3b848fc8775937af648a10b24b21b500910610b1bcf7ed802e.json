{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Injectable, Directive, Output, Input } from '@angular/core';\nimport { TuiActiveZone } from '@taiga-ui/cdk/directives/active-zone';\nimport { tuiZonefreeScheduler, tuiZoneOptimized, tuiIfMap } from '@taiga-ui/cdk/observables';\nimport { Observable, throttleTime, map, startWith, distinctUntilChanged, BehaviorSubject } from 'rxjs';\nimport { WA_ANIMATION_FRAME } from '@ng-web-apis/common';\nimport { tuiInjectElement, tuiGetElementObscures } from '@taiga-ui/cdk/utils/dom';\n\n/**\n * Service that monitors element visibility by polling and returning\n * either null or an array of elements that overlap given element edges\n */\nclass TuiObscuredService extends Observable {\n  constructor() {\n    super(subscriber => this.obscured$.subscribe(subscriber));\n    this.el = tuiInjectElement();\n    this.obscured$ = inject(WA_ANIMATION_FRAME).pipe(throttleTime(100, tuiZonefreeScheduler()), map(() => tuiGetElementObscures(this.el)), startWith(null), distinctUntilChanged(), tuiZoneOptimized());\n  }\n  static {\n    this.ɵfac = function TuiObscuredService_Factory(t) {\n      return new (t || TuiObscuredService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiObscuredService,\n      factory: TuiObscuredService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiObscuredService, [{\n    type: Injectable\n  }], function () {\n    return [];\n  }, null);\n})();\n\n/**\n * Directive that monitors element visibility\n */\nclass TuiObscured {\n  constructor() {\n    this.activeZone = inject(TuiActiveZone, {\n      optional: true\n    });\n    this.enabled$ = new BehaviorSubject(false);\n    this.obscured$ = inject(TuiObscuredService, {\n      self: true\n    }).pipe(map(by => !!by?.every(el => !this.activeZone?.contains(el))));\n    this.tuiObscured = this.enabled$.pipe(tuiIfMap(() => this.obscured$));\n  }\n  set tuiObscuredEnabled(enabled) {\n    this.enabled$.next(enabled);\n  }\n  static {\n    this.ɵfac = function TuiObscured_Factory(t) {\n      return new (t || TuiObscured)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiObscured,\n      selectors: [[\"\", \"tuiObscured\", \"\"]],\n      inputs: {\n        tuiObscuredEnabled: \"tuiObscuredEnabled\"\n      },\n      outputs: {\n        tuiObscured: \"tuiObscured\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiObscuredService])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiObscured, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiObscured]',\n      providers: [TuiObscuredService]\n    }]\n  }], null, {\n    tuiObscured: [{\n      type: Output\n    }],\n    tuiObscuredEnabled: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiObscured, TuiObscuredService };", "map": {"version": 3, "names": ["i0", "inject", "Injectable", "Directive", "Output", "Input", "TuiActiveZone", "tuiZonefreeScheduler", "tuiZoneOptimized", "tuiIfMap", "Observable", "throttleTime", "map", "startWith", "distinctUntilChanged", "BehaviorSubject", "WA_ANIMATION_FRAME", "tuiInjectElement", "tuiGetElementObscures", "TuiObscuredService", "constructor", "subscriber", "obscured$", "subscribe", "el", "pipe", "ɵfac", "TuiObscuredService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "TuiObscured", "activeZone", "optional", "enabled$", "self", "by", "every", "contains", "tuiObscured", "tuiObscuredEnabled", "enabled", "next", "TuiObscured_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵProvidersFeature", "args", "selector", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-obscured.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Injectable, Directive, Output, Input } from '@angular/core';\nimport { TuiActiveZone } from '@taiga-ui/cdk/directives/active-zone';\nimport { tuiZonefreeScheduler, tuiZoneOptimized, tuiIfMap } from '@taiga-ui/cdk/observables';\nimport { Observable, throttleTime, map, startWith, distinctUntilChanged, BehaviorSubject } from 'rxjs';\nimport { WA_ANIMATION_FRAME } from '@ng-web-apis/common';\nimport { tuiInjectElement, tuiGetElementObscures } from '@taiga-ui/cdk/utils/dom';\n\n/**\n * Service that monitors element visibility by polling and returning\n * either null or an array of elements that overlap given element edges\n */\nclass TuiObscuredService extends Observable {\n    constructor() {\n        super((subscriber) => this.obscured$.subscribe(subscriber));\n        this.el = tuiInjectElement();\n        this.obscured$ = inject(WA_ANIMATION_FRAME).pipe(throttleTime(100, tuiZonefreeScheduler()), map(() => tuiGetElementObscures(this.el)), startWith(null), distinctUntilChanged(), tuiZoneOptimized());\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiObscuredService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiObscuredService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiObscuredService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return []; } });\n\n/**\n * Directive that monitors element visibility\n */\nclass TuiObscured {\n    constructor() {\n        this.activeZone = inject(TuiActiveZone, { optional: true });\n        this.enabled$ = new BehaviorSubject(false);\n        this.obscured$ = inject(TuiObscuredService, { self: true }).pipe(map((by) => !!by?.every((el) => !this.activeZone?.contains(el))));\n        this.tuiObscured = this.enabled$.pipe(tuiIfMap(() => this.obscured$));\n    }\n    set tuiObscuredEnabled(enabled) {\n        this.enabled$.next(enabled);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiObscured, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiObscured, isStandalone: true, selector: \"[tuiObscured]\", inputs: { tuiObscuredEnabled: \"tuiObscuredEnabled\" }, outputs: { tuiObscured: \"tuiObscured\" }, providers: [TuiObscuredService], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiObscured, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiObscured]',\n                    providers: [TuiObscuredService],\n                }]\n        }], propDecorators: { tuiObscured: [{\n                type: Output\n            }], tuiObscuredEnabled: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiObscured, TuiObscuredService };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,QAAQ,eAAe;AAC5E,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,oBAAoB,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,2BAA2B;AAC5F,SAASC,UAAU,EAAEC,YAAY,EAAEC,GAAG,EAAEC,SAAS,EAAEC,oBAAoB,EAAEC,eAAe,QAAQ,MAAM;AACtG,SAASC,kBAAkB,QAAQ,qBAAqB;AACxD,SAASC,gBAAgB,EAAEC,qBAAqB,QAAQ,yBAAyB;;AAEjF;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAAST,UAAU,CAAC;EACxCU,WAAWA,CAAA,EAAG;IACV,KAAK,CAAEC,UAAU,IAAK,IAAI,CAACC,SAAS,CAACC,SAAS,CAACF,UAAU,CAAC,CAAC;IAC3D,IAAI,CAACG,EAAE,GAAGP,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACK,SAAS,GAAGrB,MAAM,CAACe,kBAAkB,CAAC,CAACS,IAAI,CAACd,YAAY,CAAC,GAAG,EAAEJ,oBAAoB,CAAC,CAAC,CAAC,EAAEK,GAAG,CAAC,MAAMM,qBAAqB,CAAC,IAAI,CAACM,EAAE,CAAC,CAAC,EAAEX,SAAS,CAAC,IAAI,CAAC,EAAEC,oBAAoB,CAAC,CAAC,EAAEN,gBAAgB,CAAC,CAAC,CAAC;EACvM;EACA;IAAS,IAAI,CAACkB,IAAI,YAAAC,2BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFT,kBAAkB;IAAA,CAAoD;EAAE;EACnL;IAAS,IAAI,CAACU,KAAK,kBAD8E7B,EAAE,CAAA8B,kBAAA;MAAAC,KAAA,EACYZ,kBAAkB;MAAAa,OAAA,EAAlBb,kBAAkB,CAAAO;IAAA,EAAG;EAAE;AAC1I;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAHqGjC,EAAE,CAAAkC,iBAAA,CAGXf,kBAAkB,EAAc,CAAC;IACjHgB,IAAI,EAAEjC;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;;AAEtD;AACA;AACA;AACA,MAAMkC,WAAW,CAAC;EACdhB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiB,UAAU,GAAGpC,MAAM,CAACK,aAAa,EAAE;MAAEgC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC3D,IAAI,CAACC,QAAQ,GAAG,IAAIxB,eAAe,CAAC,KAAK,CAAC;IAC1C,IAAI,CAACO,SAAS,GAAGrB,MAAM,CAACkB,kBAAkB,EAAE;MAAEqB,IAAI,EAAE;IAAK,CAAC,CAAC,CAACf,IAAI,CAACb,GAAG,CAAE6B,EAAE,IAAK,CAAC,CAACA,EAAE,EAAEC,KAAK,CAAElB,EAAE,IAAK,CAAC,IAAI,CAACa,UAAU,EAAEM,QAAQ,CAACnB,EAAE,CAAC,CAAC,CAAC,CAAC;IAClI,IAAI,CAACoB,WAAW,GAAG,IAAI,CAACL,QAAQ,CAACd,IAAI,CAAChB,QAAQ,CAAC,MAAM,IAAI,CAACa,SAAS,CAAC,CAAC;EACzE;EACA,IAAIuB,kBAAkBA,CAACC,OAAO,EAAE;IAC5B,IAAI,CAACP,QAAQ,CAACQ,IAAI,CAACD,OAAO,CAAC;EAC/B;EACA;IAAS,IAAI,CAACpB,IAAI,YAAAsB,oBAAApB,CAAA;MAAA,YAAAA,CAAA,IAAyFQ,WAAW;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACa,IAAI,kBArB+EjD,EAAE,CAAAkD,iBAAA;MAAAf,IAAA,EAqBJC,WAAW;MAAAe,SAAA;MAAAC,MAAA;QAAAP,kBAAA;MAAA;MAAAQ,OAAA;QAAAT,WAAA;MAAA;MAAAU,UAAA;MAAAC,QAAA,GArBTvD,EAAE,CAAAwD,kBAAA,CAqBkK,CAACrC,kBAAkB,CAAC;IAAA,EAAiB;EAAE;AAChT;AACA;EAAA,QAAAc,SAAA,oBAAAA,SAAA,KAvBqGjC,EAAE,CAAAkC,iBAAA,CAuBXE,WAAW,EAAc,CAAC;IAC1GD,IAAI,EAAEhC,SAAS;IACfsD,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,eAAe;MACzBC,SAAS,EAAE,CAACxC,kBAAkB;IAClC,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEyB,WAAW,EAAE,CAAC;MAC5BT,IAAI,EAAE/B;IACV,CAAC,CAAC;IAAEyC,kBAAkB,EAAE,CAAC;MACrBV,IAAI,EAAE9B;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAAS+B,WAAW,EAAEjB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}