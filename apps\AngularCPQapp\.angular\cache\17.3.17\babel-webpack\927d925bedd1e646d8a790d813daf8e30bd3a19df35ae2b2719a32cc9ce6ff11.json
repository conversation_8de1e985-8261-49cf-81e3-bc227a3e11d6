{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive, Input } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nclass TuiCommentStyles {\n  static {\n    this.ɵfac = function TuiCommentStyles_Factory(t) {\n      return new (t || TuiCommentStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiCommentStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-comment\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiCommentStyles_Template(rf, ctx) {},\n      styles: [\"[tuiComment]{position:relative;display:inline-flex;font:var(--tui-font-text-m);color:#fff;padding:.5rem .75rem;min-inline-size:2.5rem;border-radius:1rem;line-height:1.125rem;background:var(--tui-background-accent-2);align-items:center;justify-content:center}[tuiComment]:before{content:\\\"\\\";position:absolute;bottom:100%;left:50%;inline-size:1.5625rem;block-size:.5625rem;background:inherit;transform:translate(-50%);-webkit-mask:url(\\\"data:image/svg+xml,%3Csvg width='25' height='9' viewBox='0 0 25 9' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.23309 6.67813L7.56191 3.83178C9.4546 1.5185 10.4009 0.361853 11.5998 0.10488C12.0834 0.00123882 12.5834 0.00123882 13.0669 0.10488C14.2658 0.361853 15.2121 1.5185 17.1048 3.83178L19.4337 6.67813C20.636 8.14771 22.4346 9 24.3334 9H0.333374C2.23217 9 4.0307 8.14772 5.23309 6.67813Z' fill='black'/%3E%3C/svg%3E%0A\\\") no-repeat;mask:url(\\\"data:image/svg+xml,%3Csvg width='25' height='9' viewBox='0 0 25 9' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.23309 6.67813L7.56191 3.83178C9.4546 1.5185 10.4009 0.361853 11.5998 0.10488C12.0834 0.00123882 12.5834 0.00123882 13.0669 0.10488C14.2658 0.361853 15.2121 1.5185 17.1048 3.83178L19.4337 6.67813C20.636 8.14771 22.4346 9 24.3334 9H0.333374C2.23217 9 4.0307 8.14772 5.23309 6.67813Z' fill='black'/%3E%3C/svg%3E%0A\\\") no-repeat}[tuiComment][data-direction=bottom]:before{top:100%;bottom:auto;transform:translate(-50%) rotate(180deg)}[tuiComment][data-direction=left]:before{left:auto;top:50%;right:100%;transform:translate(.785rem,-50%) rotate(-90deg)}[tuiComment][data-direction=right]:before{top:50%;left:100%;transform:translate(-.785rem,-50%) rotate(90deg)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCommentStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-comment'\n      },\n      styles: [\"[tuiComment]{position:relative;display:inline-flex;font:var(--tui-font-text-m);color:#fff;padding:.5rem .75rem;min-inline-size:2.5rem;border-radius:1rem;line-height:1.125rem;background:var(--tui-background-accent-2);align-items:center;justify-content:center}[tuiComment]:before{content:\\\"\\\";position:absolute;bottom:100%;left:50%;inline-size:1.5625rem;block-size:.5625rem;background:inherit;transform:translate(-50%);-webkit-mask:url(\\\"data:image/svg+xml,%3Csvg width='25' height='9' viewBox='0 0 25 9' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.23309 6.67813L7.56191 3.83178C9.4546 1.5185 10.4009 0.361853 11.5998 0.10488C12.0834 0.00123882 12.5834 0.00123882 13.0669 0.10488C14.2658 0.361853 15.2121 1.5185 17.1048 3.83178L19.4337 6.67813C20.636 8.14771 22.4346 9 24.3334 9H0.333374C2.23217 9 4.0307 8.14772 5.23309 6.67813Z' fill='black'/%3E%3C/svg%3E%0A\\\") no-repeat;mask:url(\\\"data:image/svg+xml,%3Csvg width='25' height='9' viewBox='0 0 25 9' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.23309 6.67813L7.56191 3.83178C9.4546 1.5185 10.4009 0.361853 11.5998 0.10488C12.0834 0.00123882 12.5834 0.00123882 13.0669 0.10488C14.2658 0.361853 15.2121 1.5185 17.1048 3.83178L19.4337 6.67813C20.636 8.14771 22.4346 9 24.3334 9H0.333374C2.23217 9 4.0307 8.14772 5.23309 6.67813Z' fill='black'/%3E%3C/svg%3E%0A\\\") no-repeat}[tuiComment][data-direction=bottom]:before{top:100%;bottom:auto;transform:translate(-50%) rotate(180deg)}[tuiComment][data-direction=left]:before{left:auto;top:50%;right:100%;transform:translate(.785rem,-50%) rotate(-90deg)}[tuiComment][data-direction=right]:before{top:50%;left:100%;transform:translate(-.785rem,-50%) rotate(90deg)}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiComment {\n  constructor() {\n    this.nothing = tuiWithStyles(TuiCommentStyles);\n    this.tuiComment = 'top';\n  }\n  static {\n    this.ɵfac = function TuiComment_Factory(t) {\n      return new (t || TuiComment)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiComment,\n      selectors: [[\"\", \"tuiComment\", \"\"]],\n      hostVars: 1,\n      hostBindings: function TuiComment_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-direction\", ctx.tuiComment);\n        }\n      },\n      inputs: {\n        tuiComment: \"tuiComment\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiComment, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiComment]',\n      host: {\n        '[attr.data-direction]': 'tuiComment'\n      }\n    }]\n  }], null, {\n    tuiComment: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiComment };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Directive", "Input", "tuiWithStyles", "TuiCommentStyles", "ɵfac", "TuiCommentStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiCommentStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiComment", "constructor", "nothing", "tuiComment", "TuiComment_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiComment_HostBindings", "ɵɵattribute", "inputs", "selector"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-comment.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive, Input } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\n\nclass TuiCommentStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCommentStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCommentStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-comment\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiComment]{position:relative;display:inline-flex;font:var(--tui-font-text-m);color:#fff;padding:.5rem .75rem;min-inline-size:2.5rem;border-radius:1rem;line-height:1.125rem;background:var(--tui-background-accent-2);align-items:center;justify-content:center}[tuiComment]:before{content:\\\"\\\";position:absolute;bottom:100%;left:50%;inline-size:1.5625rem;block-size:.5625rem;background:inherit;transform:translate(-50%);-webkit-mask:url(\\\"data:image/svg+xml,%3Csvg width='25' height='9' viewBox='0 0 25 9' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.23309 6.67813L7.56191 3.83178C9.4546 1.5185 10.4009 0.361853 11.5998 0.10488C12.0834 0.00123882 12.5834 0.00123882 13.0669 0.10488C14.2658 0.361853 15.2121 1.5185 17.1048 3.83178L19.4337 6.67813C20.636 8.14771 22.4346 9 24.3334 9H0.333374C2.23217 9 4.0307 8.14772 5.23309 6.67813Z' fill='black'/%3E%3C/svg%3E%0A\\\") no-repeat;mask:url(\\\"data:image/svg+xml,%3Csvg width='25' height='9' viewBox='0 0 25 9' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.23309 6.67813L7.56191 3.83178C9.4546 1.5185 10.4009 0.361853 11.5998 0.10488C12.0834 0.00123882 12.5834 0.00123882 13.0669 0.10488C14.2658 0.361853 15.2121 1.5185 17.1048 3.83178L19.4337 6.67813C20.636 8.14771 22.4346 9 24.3334 9H0.333374C2.23217 9 4.0307 8.14772 5.23309 6.67813Z' fill='black'/%3E%3C/svg%3E%0A\\\") no-repeat}[tuiComment][data-direction=bottom]:before{top:100%;bottom:auto;transform:translate(-50%) rotate(180deg)}[tuiComment][data-direction=left]:before{left:auto;top:50%;right:100%;transform:translate(.785rem,-50%) rotate(-90deg)}[tuiComment][data-direction=right]:before{top:50%;left:100%;transform:translate(-.785rem,-50%) rotate(90deg)}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCommentStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-comment',\n                    }, styles: [\"[tuiComment]{position:relative;display:inline-flex;font:var(--tui-font-text-m);color:#fff;padding:.5rem .75rem;min-inline-size:2.5rem;border-radius:1rem;line-height:1.125rem;background:var(--tui-background-accent-2);align-items:center;justify-content:center}[tuiComment]:before{content:\\\"\\\";position:absolute;bottom:100%;left:50%;inline-size:1.5625rem;block-size:.5625rem;background:inherit;transform:translate(-50%);-webkit-mask:url(\\\"data:image/svg+xml,%3Csvg width='25' height='9' viewBox='0 0 25 9' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.23309 6.67813L7.56191 3.83178C9.4546 1.5185 10.4009 0.361853 11.5998 0.10488C12.0834 0.00123882 12.5834 0.00123882 13.0669 0.10488C14.2658 0.361853 15.2121 1.5185 17.1048 3.83178L19.4337 6.67813C20.636 8.14771 22.4346 9 24.3334 9H0.333374C2.23217 9 4.0307 8.14772 5.23309 6.67813Z' fill='black'/%3E%3C/svg%3E%0A\\\") no-repeat;mask:url(\\\"data:image/svg+xml,%3Csvg width='25' height='9' viewBox='0 0 25 9' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.23309 6.67813L7.56191 3.83178C9.4546 1.5185 10.4009 0.361853 11.5998 0.10488C12.0834 0.00123882 12.5834 0.00123882 13.0669 0.10488C14.2658 0.361853 15.2121 1.5185 17.1048 3.83178L19.4337 6.67813C20.636 8.14771 22.4346 9 24.3334 9H0.333374C2.23217 9 4.0307 8.14772 5.23309 6.67813Z' fill='black'/%3E%3C/svg%3E%0A\\\") no-repeat}[tuiComment][data-direction=bottom]:before{top:100%;bottom:auto;transform:translate(-50%) rotate(180deg)}[tuiComment][data-direction=left]:before{left:auto;top:50%;right:100%;transform:translate(.785rem,-50%) rotate(-90deg)}[tuiComment][data-direction=right]:before{top:50%;left:100%;transform:translate(-.785rem,-50%) rotate(90deg)}\\n\"] }]\n        }] });\nclass TuiComment {\n    constructor() {\n        this.nothing = tuiWithStyles(TuiCommentStyles);\n        this.tuiComment = 'top';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiComment, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiComment, isStandalone: true, selector: \"[tuiComment]\", inputs: { tuiComment: \"tuiComment\" }, host: { properties: { \"attr.data-direction\": \"tuiComment\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiComment, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiComment]',\n                    host: {\n                        '[attr.data-direction]': 'tuiComment',\n                    },\n                }]\n        }], propDecorators: { tuiComment: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiComment };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACvG,SAASC,aAAa,QAAQ,mCAAmC;AAEjE,MAAMC,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACI,IAAI,kBAD+EX,EAAE,CAAAY,iBAAA;MAAAC,IAAA,EACJN,gBAAgB;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADdjB,EAAE,CAAAkB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC05D;EAAE;AACngE;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG5B,EAAE,CAAA6B,iBAAA,CAGXtB,gBAAgB,EAAc,CAAC;IAC/GM,IAAI,EAAEZ,SAAS;IACf6B,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAExB,iBAAiB,CAAC6B,IAAI;MAAEJ,eAAe,EAAExB,uBAAuB,CAAC6B,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,8pDAA8pD;IAAE,CAAC;EACzrD,CAAC,CAAC;AAAA;AACV,MAAMU,UAAU,CAAC;EACbC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG/B,aAAa,CAACC,gBAAgB,CAAC;IAC9C,IAAI,CAAC+B,UAAU,GAAG,KAAK;EAC3B;EACA;IAAS,IAAI,CAAC9B,IAAI,YAAA+B,mBAAA7B,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,UAAU;IAAA,CAAmD;EAAE;EAC1K;IAAS,IAAI,CAACK,IAAI,kBAf+ExC,EAAE,CAAAyC,iBAAA;MAAA5B,IAAA,EAeJsB,UAAU;MAAArB,SAAA;MAAA4B,QAAA;MAAAC,YAAA,WAAAC,wBAAArB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAfRvB,EAAE,CAAA6C,WAAA,mBAAArB,GAAA,CAAAc,UAAA;QAAA;MAAA;MAAAQ,MAAA;QAAAR,UAAA;MAAA;MAAAtB,UAAA;IAAA,EAe0K;EAAE;AACnR;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAjBqG5B,EAAE,CAAA6B,iBAAA,CAiBXM,UAAU,EAAc,CAAC;IACzGtB,IAAI,EAAET,SAAS;IACf0B,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChB+B,QAAQ,EAAE,cAAc;MACxBd,IAAI,EAAE;QACF,uBAAuB,EAAE;MAC7B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEK,UAAU,EAAE,CAAC;MAC3BzB,IAAI,EAAER;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAAS8B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}