{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, inject, ChangeDetectorRef, EventEmitter, Component, ChangeDetectionStrategy, ContentChild, Input, Output, DestroyRef, ContentChildren } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiQueryListChanges } from '@taiga-ui/cdk/observables';\nimport { tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1$1 from '@taiga-ui/core/directives/group';\nimport { TuiGroup } from '@taiga-ui/core/directives/group';\nimport { pairwise, map, filter, merge, switchMap, identity } from 'rxjs';\nimport { NgIf } from '@angular/common';\nimport * as i1 from '@taiga-ui/core/components/expand';\nimport { TuiExpand } from '@taiga-ui/core/components/expand';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TuiChevron } from '@taiga-ui/kit/directives';\nimport { PolymorpheusTemplate, PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nconst _c0 = [\"*\", [[\"\", \"tuiAccordionItemContent\", \"\", 5, \"ng-template\"]]];\nconst _c1 = [\"*\", \"[tuiAccordionItemContent]:not(ng-template)\"];\nfunction TuiAccordionItem_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"tui-icon\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tuiChevron\", ctx_r0.open);\n  }\n}\nfunction TuiAccordionItem_ng_template_6_div_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r2 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r2, \" \");\n  }\n}\nfunction TuiAccordionItem_ng_template_6_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, TuiAccordionItem_ng_template_6_div_0_ng_container_1_Template, 2, 1, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r0.lazyContent);\n  }\n}\nfunction TuiAccordionItem_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiAccordionItem_ng_template_6_div_0_Template, 2, 1, \"div\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.lazyContent);\n  }\n}\nfunction TuiAccordionItem_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementEnd();\n  }\n}\nclass TuiAccordionItemContent extends PolymorpheusTemplate {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiAccordionItemContent_BaseFactory;\n      return function TuiAccordionItemContent_Factory(t) {\n        return (ɵTuiAccordionItemContent_BaseFactory || (ɵTuiAccordionItemContent_BaseFactory = i0.ɵɵgetInheritedFactory(TuiAccordionItemContent)))(t || TuiAccordionItemContent);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiAccordionItemContent,\n      selectors: [[\"ng-template\", \"tuiAccordionItemContent\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAccordionItemContent, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-template[tuiAccordionItemContent]'\n    }]\n  }], null, null);\n})();\nclass TuiAccordionItemEagerContent {\n  static {\n    this.ɵfac = function TuiAccordionItemEagerContent_Factory(t) {\n      return new (t || TuiAccordionItemEagerContent)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiAccordionItemEagerContent,\n      selectors: [[\"\", \"tuiAccordionItemContent\", \"\", 5, \"ng-template\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAccordionItemEagerContent, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiAccordionItemContent]:not(ng-template)'\n    }]\n  }], null, null);\n})();\nclass TuiAccordionItem {\n  constructor() {\n    this.cdr = inject(ChangeDetectorRef);\n    this.noPadding = false;\n    this.showArrow = true;\n    this.borders = 'all';\n    this.size = 'm';\n    this.disabled = false;\n    this.disableHover = false;\n    this.open = false;\n    this.async = false;\n    this.openChange = new EventEmitter();\n  }\n  close() {\n    this.updateOpen(false);\n    this.cdr.markForCheck();\n  }\n  onRowToggle() {\n    if (!this.disabled) {\n      this.updateOpen(!this.open);\n    }\n  }\n  onItemKeyDownEsc(event) {\n    if (!this.open) {\n      return;\n    }\n    event.stopPropagation();\n    this.updateOpen(false);\n  }\n  updateOpen(open) {\n    if (this.open === open) {\n      return;\n    }\n    this.open = open;\n    this.openChange.emit(open);\n  }\n  static {\n    this.ɵfac = function TuiAccordionItem_Factory(t) {\n      return new (t || TuiAccordionItem)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiAccordionItem,\n      selectors: [[\"tui-accordion-item\"]],\n      contentQueries: function TuiAccordionItem_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiAccordionItemEagerContent, 5);\n          i0.ɵɵcontentQuery(dirIndex, TuiAccordionItemContent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.eagerContent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lazyContent = _t.first);\n        }\n      },\n      hostVars: 8,\n      hostBindings: function TuiAccordionItem_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-borders\", ctx.borders)(\"data-size\", ctx.size);\n          i0.ɵɵclassProp(\"_no-padding\", ctx.noPadding)(\"_has-arrow\", ctx.showArrow)(\"_disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        noPadding: \"noPadding\",\n        showArrow: \"showArrow\",\n        borders: \"borders\",\n        size: \"size\",\n        disabled: \"disabled\",\n        disableHover: \"disableHover\",\n        open: \"open\",\n        async: \"async\"\n      },\n      outputs: {\n        openChange: \"openChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 8,\n      vars: 9,\n      consts: [[\"automation-id\", \"tui-accordion__item-wrapper\", 1, \"t-wrapper\"], [\"automation-id\", \"tui-accordion__item-header\", \"type\", \"button\", 1, \"t-header\", 3, \"click\", \"keydown.esc\", \"disabled\"], [\"automation-id\", \"tui-accordion__item-title\", 1, \"t-title\"], [4, \"ngIf\"], [3, \"async\", \"expanded\"], [\"tuiExpandContent\", \"\"], [\"class\", \"t-content\", 4, \"ngIf\"], [1, \"t-icon\", 3, \"tuiChevron\"], [\"automation-id\", \"tui-accordion__item-content\", \"class\", \"t-content\", 4, \"ngIf\"], [\"automation-id\", \"tui-accordion__item-content\", 1, \"t-content\"], [4, \"polymorpheusOutlet\"], [1, \"t-content\"]],\n      template: function TuiAccordionItem_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1);\n          i0.ɵɵlistener(\"click\", function TuiAccordionItem_Template_button_click_1_listener() {\n            return ctx.onRowToggle();\n          })(\"keydown.esc\", function TuiAccordionItem_Template_button_keydown_esc_1_listener($event) {\n            return ctx.onItemKeyDownEsc($event);\n          });\n          i0.ɵɵelementStart(2, \"span\", 2);\n          i0.ɵɵprojection(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, TuiAccordionItem_ng_container_4_Template, 2, 1, \"ng-container\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"tui-expand\", 4);\n          i0.ɵɵtemplate(6, TuiAccordionItem_ng_template_6_Template, 1, 1, \"ng-template\", 5)(7, TuiAccordionItem_div_7_Template, 2, 0, \"div\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"t-header_hoverable\", !ctx.disableHover)(\"t-header_open\", ctx.open);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.showArrow);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"async\", ctx.async)(\"expanded\", ctx.open);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.eagerContent);\n        }\n      },\n      dependencies: [NgIf, PolymorpheusOutlet, TuiChevron, i1.TuiExpandComponent, i1.TuiExpandContent, TuiIcon],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:block;overflow:hidden;border-radius:var(--tui-radius-l)}[data-borders=top-bottom][_nghost-%COMP%]{border-radius:0!important}  tui-accordion{inline-size:100%}.t-wrapper[_ngcontent-%COMP%]{position:relative;border-radius:inherit}.t-wrapper[_ngcontent-%COMP%]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";box-sizing:border-box;border-radius:inherit;border:1px solid var(--tui-border-normal);pointer-events:none}[_nghost-%COMP%]:not([data-borders])   .t-wrapper[_ngcontent-%COMP%]:after{border-width:0}[data-borders=all][_nghost-%COMP%]   .t-wrapper[_ngcontent-%COMP%]:after{border-width:1px}[data-borders=top-bottom][_nghost-%COMP%]   .t-wrapper[_ngcontent-%COMP%]:after{border-inline-start-width:0;border-inline-end-width:0}[data-borders=top][_nghost-%COMP%]   .t-wrapper[_ngcontent-%COMP%]:after{border-inline-start-width:0;border-inline-end-width:0;border-block-end-width:0}[data-borders=bottom][_nghost-%COMP%]   .t-wrapper[_ngcontent-%COMP%]:after{border-inline-start-width:0;border-inline-end-width:0;border-block-start-width:0}.t-header[_ngcontent-%COMP%]{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:flex;inline-size:100%;font:var(--tui-font-text-l);align-items:center;box-sizing:border-box;border-block-end:1px solid var(--tui-border-normal);min-block-size:var(--tui-height-l);padding:.75rem 1.25rem;color:var(--tui-text-primary);cursor:pointer;text-align:start;outline:none}.t-header[_ngcontent-%COMP%]:focus-visible{box-shadow:inset 0 0 0 2px var(--tui-border-focus)}[_nghost-%COMP%]:not([data-borders])   .t-header[_ngcontent-%COMP%]{border-block-end-width:0}._has-arrow[_nghost-%COMP%]   .t-header[_ngcontent-%COMP%]{padding-right:.75rem}[tuiTheme=\\\"dark\\\"][_nghost-%COMP%]   .t-header_open[_ngcontent-%COMP%], [tuiTheme=\\\"dark\\\"]   [_nghost-%COMP%]   .t-header_open[_ngcontent-%COMP%]{background:var(--tui-background-neutral-1)}[data-size=s][_nghost-%COMP%]   .t-header[_ngcontent-%COMP%]{font:var(--tui-font-text-m);min-block-size:var(--tui-height-m);padding:.625rem .75rem .625rem 1rem}._no-padding[_nghost-%COMP%]   .t-header[_ngcontent-%COMP%]{padding-left:0;padding-right:0}._disabled[_nghost-%COMP%]   .t-header[_ngcontent-%COMP%]{cursor:default}.t-wrapper[_ngcontent-%COMP%]:hover > .t-header_hoverable[_ngcontent-%COMP%]{background:var(--tui-background-base-alt)}.t-title[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-right:0;flex-grow:1}._has-arrow[_nghost-%COMP%]   .t-title[_ngcontent-%COMP%]{margin-right:.5rem}.t-icon[_ngcontent-%COMP%]{transition-property:color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-left:auto;border-width:.25rem;color:var(--tui-text-tertiary)}[_nghost-%COMP%]:hover   .t-icon[_ngcontent-%COMP%]{color:var(--tui-text-secondary)}.t-content[_ngcontent-%COMP%]{font:var(--tui-font-text-m);padding:1.25rem;overflow-wrap:break-word}[data-size=s][_nghost-%COMP%]   .t-content[_ngcontent-%COMP%]{font:var(--tui-font-text-s);padding:1rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAccordionItem, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-accordion-item',\n      imports: [NgIf, PolymorpheusOutlet, TuiChevron, TuiExpand, TuiIcon],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class._no-padding]': 'noPadding',\n        '[class._has-arrow]': 'showArrow',\n        '[attr.data-borders]': 'borders',\n        '[attr.data-size]': 'size',\n        '[class._disabled]': 'disabled'\n      },\n      template: \"<div\\n    automation-id=\\\"tui-accordion__item-wrapper\\\"\\n    class=\\\"t-wrapper\\\"\\n>\\n    <button\\n        automation-id=\\\"tui-accordion__item-header\\\"\\n        type=\\\"button\\\"\\n        class=\\\"t-header\\\"\\n        [class.t-header_hoverable]=\\\"!disableHover\\\"\\n        [class.t-header_open]=\\\"open\\\"\\n        [disabled]=\\\"disabled\\\"\\n        (click)=\\\"onRowToggle()\\\"\\n        (keydown.esc)=\\\"onItemKeyDownEsc($event)\\\"\\n    >\\n        <span\\n            automation-id=\\\"tui-accordion__item-title\\\"\\n            class=\\\"t-title\\\"\\n        >\\n            <ng-content />\\n        </span>\\n        <ng-container *ngIf=\\\"showArrow\\\">\\n            <tui-icon\\n                class=\\\"t-icon\\\"\\n                [tuiChevron]=\\\"open\\\"\\n            />\\n        </ng-container>\\n    </button>\\n    <tui-expand\\n        [async]=\\\"async\\\"\\n        [expanded]=\\\"open\\\"\\n    >\\n        <ng-template tuiExpandContent>\\n            <div\\n                *ngIf=\\\"lazyContent\\\"\\n                automation-id=\\\"tui-accordion__item-content\\\"\\n                class=\\\"t-content\\\"\\n            >\\n                <ng-container *polymorpheusOutlet=\\\"lazyContent as text\\\">\\n                    {{ text }}\\n                </ng-container>\\n            </div>\\n        </ng-template>\\n        <div\\n            *ngIf=\\\"eagerContent\\\"\\n            class=\\\"t-content\\\"\\n        >\\n            <ng-content select=\\\"[tuiAccordionItemContent]:not(ng-template)\\\" />\\n        </div>\\n    </tui-expand>\\n</div>\\n\",\n      styles: [\":host{position:relative;display:block;overflow:hidden;border-radius:var(--tui-radius-l)}:host[data-borders=top-bottom]{border-radius:0!important}::ng-deep tui-accordion{inline-size:100%}.t-wrapper{position:relative;border-radius:inherit}.t-wrapper:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";box-sizing:border-box;border-radius:inherit;border:1px solid var(--tui-border-normal);pointer-events:none}:host:not([data-borders]) .t-wrapper:after{border-width:0}:host[data-borders=all] .t-wrapper:after{border-width:1px}:host[data-borders=top-bottom] .t-wrapper:after{border-inline-start-width:0;border-inline-end-width:0}:host[data-borders=top] .t-wrapper:after{border-inline-start-width:0;border-inline-end-width:0;border-block-end-width:0}:host[data-borders=bottom] .t-wrapper:after{border-inline-start-width:0;border-inline-end-width:0;border-block-start-width:0}.t-header{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:flex;inline-size:100%;font:var(--tui-font-text-l);align-items:center;box-sizing:border-box;border-block-end:1px solid var(--tui-border-normal);min-block-size:var(--tui-height-l);padding:.75rem 1.25rem;color:var(--tui-text-primary);cursor:pointer;text-align:start;outline:none}.t-header:focus-visible{box-shadow:inset 0 0 0 2px var(--tui-border-focus)}:host:not([data-borders]) .t-header{border-block-end-width:0}:host._has-arrow .t-header{padding-right:.75rem}:host-context([tuiTheme=\\\"dark\\\"]) .t-header_open{background:var(--tui-background-neutral-1)}:host[data-size=s] .t-header{font:var(--tui-font-text-m);min-block-size:var(--tui-height-m);padding:.625rem .75rem .625rem 1rem}:host._no-padding .t-header{padding-left:0;padding-right:0}:host._disabled .t-header{cursor:default}.t-wrapper:hover>.t-header_hoverable{background:var(--tui-background-base-alt)}.t-title{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-right:0;flex-grow:1}:host._has-arrow .t-title{margin-right:.5rem}.t-icon{transition-property:color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-left:auto;border-width:.25rem;color:var(--tui-text-tertiary)}:host:hover .t-icon{color:var(--tui-text-secondary)}.t-content{font:var(--tui-font-text-m);padding:1.25rem;overflow-wrap:break-word}:host[data-size=s] .t-content{font:var(--tui-font-text-s);padding:1rem}\\n\"]\n    }]\n  }], null, {\n    eagerContent: [{\n      type: ContentChild,\n      args: [TuiAccordionItemEagerContent]\n    }],\n    lazyContent: [{\n      type: ContentChild,\n      args: [TuiAccordionItemContent]\n    }],\n    noPadding: [{\n      type: Input\n    }],\n    showArrow: [{\n      type: Input\n    }],\n    borders: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    disableHover: [{\n      type: Input\n    }],\n    open: [{\n      type: Input\n    }],\n    async: [{\n      type: Input\n    }],\n    openChange: [{\n      type: Output\n    }]\n  });\n})();\nclass TuiAccordionDirective {\n  constructor() {\n    this.destroyRef = inject(DestroyRef);\n    this.accordionItems = EMPTY_QUERY;\n    this.closeOthers = true;\n    // Not using DI options to avoid changed defaults spilling to content\n    const group = inject(TuiGroup);\n    group.orientation = 'vertical';\n    group.collapsed = true;\n  }\n  ngAfterContentInit() {\n    const {\n      accordionItems\n    } = this;\n    const rows$ = tuiQueryListChanges(accordionItems);\n    const newOpenRow$ = rows$.pipe(pairwise(), map(([previous, current]) => current.find(item => !previous.includes(item) && item.open)), filter(tuiIsPresent));\n    const rowsOpen$ = merge(rows$.pipe(switchMap(rows => merge(...rows.map(row => row.openChange.pipe(filter(identity), map(() => row)))))), newOpenRow$).pipe(filter(() => this.closeOthers), takeUntilDestroyed(this.destroyRef));\n    rowsOpen$.subscribe(currentRow => {\n      accordionItems.forEach(row => {\n        if (currentRow !== row) {\n          row.close();\n        }\n      });\n    });\n  }\n  static {\n    this.ɵfac = function TuiAccordionDirective_Factory(t) {\n      return new (t || TuiAccordionDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiAccordionDirective,\n      selectors: [[\"tui-accordion\"]],\n      contentQueries: function TuiAccordionDirective_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiAccordionItem, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.accordionItems = _t);\n        }\n      },\n      inputs: {\n        closeOthers: \"closeOthers\"\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([{\n        directive: i1$1.TuiGroup,\n        inputs: [\"rounded\", \"rounded\"]\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAccordionDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'tui-accordion',\n      hostDirectives: [{\n        directive: TuiGroup,\n        inputs: ['rounded']\n      }]\n    }]\n  }], function () {\n    return [];\n  }, {\n    accordionItems: [{\n      type: ContentChildren,\n      args: [TuiAccordionItem]\n    }],\n    closeOthers: [{\n      type: Input\n    }]\n  });\n})();\nconst TuiAccordion = [TuiAccordionItem, TuiAccordionDirective, TuiAccordionItemContent, TuiAccordionItemEagerContent];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiAccordion, TuiAccordionDirective, TuiAccordionItem, TuiAccordionItemContent, TuiAccordionItemEagerContent };", "map": {"version": 3, "names": ["i0", "Directive", "inject", "ChangeDetectorRef", "EventEmitter", "Component", "ChangeDetectionStrategy", "ContentChild", "Input", "Output", "DestroyRef", "ContentChildren", "takeUntilDestroyed", "EMPTY_QUERY", "tui<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tuiIsPresent", "i1$1", "TuiGroup", "pairwise", "map", "filter", "merge", "switchMap", "identity", "NgIf", "i1", "TuiExpand", "TuiIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PolymorpheusTemplate", "Polymorpheus<PERSON><PERSON>let", "_c0", "_c1", "TuiAccordionItem_ng_container_4_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵelementContainerEnd", "ctx_r0", "ɵɵnextContext", "ɵɵadvance", "ɵɵproperty", "open", "TuiAccordionItem_ng_template_6_div_0_ng_container_1_Template", "ɵɵtext", "text_r2", "polymorpheusOutlet", "ɵɵtextInterpolate1", "TuiAccordionItem_ng_template_6_div_0_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "lazyContent", "TuiAccordionItem_ng_template_6_Template", "TuiAccordionItem_div_7_Template", "ɵɵprojection", "TuiAccordionItemContent", "ɵfac", "ɵTuiAccordionItemContent_BaseFactory", "TuiAccordionItemContent_Factory", "t", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "TuiAccordionItemEagerContent", "TuiAccordionItemEagerContent_Factory", "TuiAccordionItem", "constructor", "cdr", "noPadding", "showArrow", "borders", "size", "disabled", "disableHover", "async", "openChange", "close", "updateOpen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onRowToggle", "onItemKeyDownEsc", "event", "stopPropagation", "emit", "TuiAccordionItem_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "TuiAccordionItem_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "eagerContent", "first", "hostVars", "hostBindings", "TuiAccordionItem_HostBindings", "ɵɵattribute", "ɵɵclassProp", "inputs", "outputs", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "TuiAccordionItem_Template", "ɵɵprojectionDef", "ɵɵlistener", "TuiAccordionItem_Template_button_click_1_listener", "TuiAccordionItem_Template_button_keydown_esc_1_listener", "$event", "dependencies", "TuiExpandComponent", "TuiExpandContent", "styles", "changeDetection", "imports", "OnPush", "host", "TuiAccordionDirective", "destroyRef", "accordionItems", "closeOthers", "group", "orientation", "collapsed", "ngAfterContentInit", "rows$", "newOpenRow$", "pipe", "previous", "current", "find", "item", "includes", "rowsOpen$", "rows", "row", "subscribe", "currentRow", "for<PERSON>ach", "TuiAccordionDirective_Factory", "TuiAccordionDirective_ContentQueries", "ɵɵHostDirectivesFeature", "directive", "hostDirectives", "<PERSON>i<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-accordion.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, inject, ChangeDetectorRef, EventEmitter, Component, ChangeDetectionStrategy, ContentChild, Input, Output, DestroyRef, ContentChildren } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiQueryListChanges } from '@taiga-ui/cdk/observables';\nimport { tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1$1 from '@taiga-ui/core/directives/group';\nimport { TuiGroup } from '@taiga-ui/core/directives/group';\nimport { pairwise, map, filter, merge, switchMap, identity } from 'rxjs';\nimport { NgIf } from '@angular/common';\nimport * as i1 from '@taiga-ui/core/components/expand';\nimport { TuiExpand } from '@taiga-ui/core/components/expand';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TuiChevron } from '@taiga-ui/kit/directives';\nimport { PolymorpheusTemplate, PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\n\nclass TuiAccordionItemContent extends PolymorpheusTemplate {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAccordionItemContent, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAccordionItemContent, isStandalone: true, selector: \"ng-template[tuiAccordionItemContent]\", usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAccordionItemContent, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-template[tuiAccordionItemContent]',\n                }]\n        }] });\n\nclass TuiAccordionItemEagerContent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAccordionItemEagerContent, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAccordionItemEagerContent, isStandalone: true, selector: \"[tuiAccordionItemContent]:not(ng-template)\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAccordionItemEagerContent, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiAccordionItemContent]:not(ng-template)',\n                }]\n        }] });\n\nclass TuiAccordionItem {\n    constructor() {\n        this.cdr = inject(ChangeDetectorRef);\n        this.noPadding = false;\n        this.showArrow = true;\n        this.borders = 'all';\n        this.size = 'm';\n        this.disabled = false;\n        this.disableHover = false;\n        this.open = false;\n        this.async = false;\n        this.openChange = new EventEmitter();\n    }\n    close() {\n        this.updateOpen(false);\n        this.cdr.markForCheck();\n    }\n    onRowToggle() {\n        if (!this.disabled) {\n            this.updateOpen(!this.open);\n        }\n    }\n    onItemKeyDownEsc(event) {\n        if (!this.open) {\n            return;\n        }\n        event.stopPropagation();\n        this.updateOpen(false);\n    }\n    updateOpen(open) {\n        if (this.open === open) {\n            return;\n        }\n        this.open = open;\n        this.openChange.emit(open);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAccordionItem, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAccordionItem, isStandalone: true, selector: \"tui-accordion-item\", inputs: { noPadding: \"noPadding\", showArrow: \"showArrow\", borders: \"borders\", size: \"size\", disabled: \"disabled\", disableHover: \"disableHover\", open: \"open\", async: \"async\" }, outputs: { openChange: \"openChange\" }, host: { properties: { \"class._no-padding\": \"noPadding\", \"class._has-arrow\": \"showArrow\", \"attr.data-borders\": \"borders\", \"attr.data-size\": \"size\", \"class._disabled\": \"disabled\" } }, queries: [{ propertyName: \"eagerContent\", first: true, predicate: TuiAccordionItemEagerContent, descendants: true }, { propertyName: \"lazyContent\", first: true, predicate: TuiAccordionItemContent, descendants: true }], ngImport: i0, template: \"<div\\n    automation-id=\\\"tui-accordion__item-wrapper\\\"\\n    class=\\\"t-wrapper\\\"\\n>\\n    <button\\n        automation-id=\\\"tui-accordion__item-header\\\"\\n        type=\\\"button\\\"\\n        class=\\\"t-header\\\"\\n        [class.t-header_hoverable]=\\\"!disableHover\\\"\\n        [class.t-header_open]=\\\"open\\\"\\n        [disabled]=\\\"disabled\\\"\\n        (click)=\\\"onRowToggle()\\\"\\n        (keydown.esc)=\\\"onItemKeyDownEsc($event)\\\"\\n    >\\n        <span\\n            automation-id=\\\"tui-accordion__item-title\\\"\\n            class=\\\"t-title\\\"\\n        >\\n            <ng-content />\\n        </span>\\n        <ng-container *ngIf=\\\"showArrow\\\">\\n            <tui-icon\\n                class=\\\"t-icon\\\"\\n                [tuiChevron]=\\\"open\\\"\\n            />\\n        </ng-container>\\n    </button>\\n    <tui-expand\\n        [async]=\\\"async\\\"\\n        [expanded]=\\\"open\\\"\\n    >\\n        <ng-template tuiExpandContent>\\n            <div\\n                *ngIf=\\\"lazyContent\\\"\\n                automation-id=\\\"tui-accordion__item-content\\\"\\n                class=\\\"t-content\\\"\\n            >\\n                <ng-container *polymorpheusOutlet=\\\"lazyContent as text\\\">\\n                    {{ text }}\\n                </ng-container>\\n            </div>\\n        </ng-template>\\n        <div\\n            *ngIf=\\\"eagerContent\\\"\\n            class=\\\"t-content\\\"\\n        >\\n            <ng-content select=\\\"[tuiAccordionItemContent]:not(ng-template)\\\" />\\n        </div>\\n    </tui-expand>\\n</div>\\n\", styles: [\":host{position:relative;display:block;overflow:hidden;border-radius:var(--tui-radius-l)}:host[data-borders=top-bottom]{border-radius:0!important}::ng-deep tui-accordion{inline-size:100%}.t-wrapper{position:relative;border-radius:inherit}.t-wrapper:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";box-sizing:border-box;border-radius:inherit;border:1px solid var(--tui-border-normal);pointer-events:none}:host:not([data-borders]) .t-wrapper:after{border-width:0}:host[data-borders=all] .t-wrapper:after{border-width:1px}:host[data-borders=top-bottom] .t-wrapper:after{border-inline-start-width:0;border-inline-end-width:0}:host[data-borders=top] .t-wrapper:after{border-inline-start-width:0;border-inline-end-width:0;border-block-end-width:0}:host[data-borders=bottom] .t-wrapper:after{border-inline-start-width:0;border-inline-end-width:0;border-block-start-width:0}.t-header{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:flex;inline-size:100%;font:var(--tui-font-text-l);align-items:center;box-sizing:border-box;border-block-end:1px solid var(--tui-border-normal);min-block-size:var(--tui-height-l);padding:.75rem 1.25rem;color:var(--tui-text-primary);cursor:pointer;text-align:start;outline:none}.t-header:focus-visible{box-shadow:inset 0 0 0 2px var(--tui-border-focus)}:host:not([data-borders]) .t-header{border-block-end-width:0}:host._has-arrow .t-header{padding-right:.75rem}:host-context([tuiTheme=\\\"dark\\\"]) .t-header_open{background:var(--tui-background-neutral-1)}:host[data-size=s] .t-header{font:var(--tui-font-text-m);min-block-size:var(--tui-height-m);padding:.625rem .75rem .625rem 1rem}:host._no-padding .t-header{padding-left:0;padding-right:0}:host._disabled .t-header{cursor:default}.t-wrapper:hover>.t-header_hoverable{background:var(--tui-background-base-alt)}.t-title{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-right:0;flex-grow:1}:host._has-arrow .t-title{margin-right:.5rem}.t-icon{transition-property:color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-left:auto;border-width:.25rem;color:var(--tui-text-tertiary)}:host:hover .t-icon{color:var(--tui-text-secondary)}.t-content{font:var(--tui-font-text-m);padding:1.25rem;overflow-wrap:break-word}:host[data-size=s] .t-content{font:var(--tui-font-text-s);padding:1rem}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: TuiChevron, selector: \"[tuiChevron]\", inputs: [\"tuiChevron\"] }, { kind: \"component\", type: i1.TuiExpandComponent, selector: \"tui-expand\", inputs: [\"async\", \"expanded\"] }, { kind: \"directive\", type: i1.TuiExpandContent, selector: \"[tuiExpandContent]\" }, { kind: \"component\", type: TuiIcon, selector: \"tui-icon\", inputs: [\"icon\", \"background\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAccordionItem, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-accordion-item', imports: [NgIf, PolymorpheusOutlet, TuiChevron, TuiExpand, TuiIcon], changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[class._no-padding]': 'noPadding',\n                        '[class._has-arrow]': 'showArrow',\n                        '[attr.data-borders]': 'borders',\n                        '[attr.data-size]': 'size',\n                        '[class._disabled]': 'disabled',\n                    }, template: \"<div\\n    automation-id=\\\"tui-accordion__item-wrapper\\\"\\n    class=\\\"t-wrapper\\\"\\n>\\n    <button\\n        automation-id=\\\"tui-accordion__item-header\\\"\\n        type=\\\"button\\\"\\n        class=\\\"t-header\\\"\\n        [class.t-header_hoverable]=\\\"!disableHover\\\"\\n        [class.t-header_open]=\\\"open\\\"\\n        [disabled]=\\\"disabled\\\"\\n        (click)=\\\"onRowToggle()\\\"\\n        (keydown.esc)=\\\"onItemKeyDownEsc($event)\\\"\\n    >\\n        <span\\n            automation-id=\\\"tui-accordion__item-title\\\"\\n            class=\\\"t-title\\\"\\n        >\\n            <ng-content />\\n        </span>\\n        <ng-container *ngIf=\\\"showArrow\\\">\\n            <tui-icon\\n                class=\\\"t-icon\\\"\\n                [tuiChevron]=\\\"open\\\"\\n            />\\n        </ng-container>\\n    </button>\\n    <tui-expand\\n        [async]=\\\"async\\\"\\n        [expanded]=\\\"open\\\"\\n    >\\n        <ng-template tuiExpandContent>\\n            <div\\n                *ngIf=\\\"lazyContent\\\"\\n                automation-id=\\\"tui-accordion__item-content\\\"\\n                class=\\\"t-content\\\"\\n            >\\n                <ng-container *polymorpheusOutlet=\\\"lazyContent as text\\\">\\n                    {{ text }}\\n                </ng-container>\\n            </div>\\n        </ng-template>\\n        <div\\n            *ngIf=\\\"eagerContent\\\"\\n            class=\\\"t-content\\\"\\n        >\\n            <ng-content select=\\\"[tuiAccordionItemContent]:not(ng-template)\\\" />\\n        </div>\\n    </tui-expand>\\n</div>\\n\", styles: [\":host{position:relative;display:block;overflow:hidden;border-radius:var(--tui-radius-l)}:host[data-borders=top-bottom]{border-radius:0!important}::ng-deep tui-accordion{inline-size:100%}.t-wrapper{position:relative;border-radius:inherit}.t-wrapper:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";box-sizing:border-box;border-radius:inherit;border:1px solid var(--tui-border-normal);pointer-events:none}:host:not([data-borders]) .t-wrapper:after{border-width:0}:host[data-borders=all] .t-wrapper:after{border-width:1px}:host[data-borders=top-bottom] .t-wrapper:after{border-inline-start-width:0;border-inline-end-width:0}:host[data-borders=top] .t-wrapper:after{border-inline-start-width:0;border-inline-end-width:0;border-block-end-width:0}:host[data-borders=bottom] .t-wrapper:after{border-inline-start-width:0;border-inline-end-width:0;border-block-start-width:0}.t-header{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:flex;inline-size:100%;font:var(--tui-font-text-l);align-items:center;box-sizing:border-box;border-block-end:1px solid var(--tui-border-normal);min-block-size:var(--tui-height-l);padding:.75rem 1.25rem;color:var(--tui-text-primary);cursor:pointer;text-align:start;outline:none}.t-header:focus-visible{box-shadow:inset 0 0 0 2px var(--tui-border-focus)}:host:not([data-borders]) .t-header{border-block-end-width:0}:host._has-arrow .t-header{padding-right:.75rem}:host-context([tuiTheme=\\\"dark\\\"]) .t-header_open{background:var(--tui-background-neutral-1)}:host[data-size=s] .t-header{font:var(--tui-font-text-m);min-block-size:var(--tui-height-m);padding:.625rem .75rem .625rem 1rem}:host._no-padding .t-header{padding-left:0;padding-right:0}:host._disabled .t-header{cursor:default}.t-wrapper:hover>.t-header_hoverable{background:var(--tui-background-base-alt)}.t-title{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-right:0;flex-grow:1}:host._has-arrow .t-title{margin-right:.5rem}.t-icon{transition-property:color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-left:auto;border-width:.25rem;color:var(--tui-text-tertiary)}:host:hover .t-icon{color:var(--tui-text-secondary)}.t-content{font:var(--tui-font-text-m);padding:1.25rem;overflow-wrap:break-word}:host[data-size=s] .t-content{font:var(--tui-font-text-s);padding:1rem}\\n\"] }]\n        }], propDecorators: { eagerContent: [{\n                type: ContentChild,\n                args: [TuiAccordionItemEagerContent]\n            }], lazyContent: [{\n                type: ContentChild,\n                args: [TuiAccordionItemContent]\n            }], noPadding: [{\n                type: Input\n            }], showArrow: [{\n                type: Input\n            }], borders: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], disableHover: [{\n                type: Input\n            }], open: [{\n                type: Input\n            }], async: [{\n                type: Input\n            }], openChange: [{\n                type: Output\n            }] } });\n\nclass TuiAccordionDirective {\n    constructor() {\n        this.destroyRef = inject(DestroyRef);\n        this.accordionItems = EMPTY_QUERY;\n        this.closeOthers = true;\n        // Not using DI options to avoid changed defaults spilling to content\n        const group = inject(TuiGroup);\n        group.orientation = 'vertical';\n        group.collapsed = true;\n    }\n    ngAfterContentInit() {\n        const { accordionItems } = this;\n        const rows$ = tuiQueryListChanges(accordionItems);\n        const newOpenRow$ = rows$.pipe(pairwise(), map(([previous, current]) => current.find((item) => !previous.includes(item) && item.open)), filter(tuiIsPresent));\n        const rowsOpen$ = merge(rows$.pipe(switchMap((rows) => merge(...rows.map((row) => row.openChange.pipe(filter(identity), map(() => row)))))), newOpenRow$).pipe(filter(() => this.closeOthers), takeUntilDestroyed(this.destroyRef));\n        rowsOpen$.subscribe((currentRow) => {\n            accordionItems.forEach((row) => {\n                if (currentRow !== row) {\n                    row.close();\n                }\n            });\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAccordionDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAccordionDirective, isStandalone: true, selector: \"tui-accordion\", inputs: { closeOthers: \"closeOthers\" }, queries: [{ propertyName: \"accordionItems\", predicate: TuiAccordionItem }], hostDirectives: [{ directive: i1$1.TuiGroup, inputs: [\"rounded\", \"rounded\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAccordionDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-accordion',\n                    hostDirectives: [\n                        {\n                            directive: TuiGroup,\n                            inputs: ['rounded'],\n                        },\n                    ],\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { accordionItems: [{\n                type: ContentChildren,\n                args: [TuiAccordionItem]\n            }], closeOthers: [{\n                type: Input\n            }] } });\n\nconst TuiAccordion = [\n    TuiAccordionItem,\n    TuiAccordionDirective,\n    TuiAccordionItemContent,\n    TuiAccordionItemEagerContent,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiAccordion, TuiAccordionDirective, TuiAccordionItem, TuiAccordionItemContent, TuiAccordionItemEagerContent };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,eAAe,QAAQ,eAAe;AAChL,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,YAAY,QAAQ,mCAAmC;AAChE,OAAO,KAAKC,IAAI,MAAM,iCAAiC;AACvD,SAASC,QAAQ,QAAQ,iCAAiC;AAC1D,SAASC,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,MAAM;AACxE,SAASC,IAAI,QAAQ,iBAAiB;AACtC,OAAO,KAAKC,EAAE,MAAM,kCAAkC;AACtD,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,oBAAoB,EAAEC,kBAAkB,QAAQ,wBAAwB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,yCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAGmBlC,EAAE,CAAAoC,uBAAA,EA4DqzC,CAAC;IA5DxzCpC,EAAE,CAAAqC,SAAA,iBA4Dq6C,CAAC;IA5Dx6CrC,EAAE,CAAAsC,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAAFvC,EAAE,CAAAwC,aAAA;IAAFxC,EAAE,CAAAyC,SAAA,CA4Dq5C,CAAC;IA5Dx5CzC,EAAE,CAAA0C,UAAA,eAAAH,MAAA,CAAAI,IA4Dq5C,CAAC;EAAA;AAAA;AAAA,SAAAC,6DAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5Dx5ClC,EAAE,CAAAoC,uBAAA,EA4D6zD,CAAC;IA5Dh0DpC,EAAE,CAAA6C,MAAA,EA4D+2D,CAAC;IA5Dl3D7C,EAAE,CAAAsC,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAY,OAAA,GAAAX,GAAA,CAAAY,kBAAA;IAAF/C,EAAE,CAAAyC,SAAA,CA4D+2D,CAAC;IA5Dl3DzC,EAAE,CAAAgD,kBAAA,MAAAF,OAAA,KA4D+2D,CAAC;EAAA;AAAA;AAAA,SAAAG,8CAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5Dl3DlC,EAAE,CAAAkD,cAAA,YA4DivD,CAAC;IA5DpvDlD,EAAE,CAAAmD,UAAA,IAAAP,4DAAA,0BA4D6zD,CAAC;IA5Dh0D5C,EAAE,CAAAoD,YAAA,CA4Dk5D,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAK,MAAA,GA5Dr5DvC,EAAE,CAAAwC,aAAA;IAAFxC,EAAE,CAAAyC,SAAA,CA4DmzD,CAAC;IA5DtzDzC,EAAE,CAAA0C,UAAA,uBAAAH,MAAA,CAAAc,WA4DmzD,CAAC;EAAA;AAAA;AAAA,SAAAC,wCAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5DtzDlC,EAAE,CAAAmD,UAAA,IAAAF,6CAAA,gBA4DivD,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAK,MAAA,GA5DpvDvC,EAAE,CAAAwC,aAAA;IAAFxC,EAAE,CAAA0C,UAAA,SAAAH,MAAA,CAAAc,WA4D4nD,CAAC;EAAA;AAAA;AAAA,SAAAE,gCAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5D/nDlC,EAAE,CAAAkD,cAAA,aA4DwgE,CAAC;IA5D3gElD,EAAE,CAAAwD,YAAA,KA4D0lE,CAAC;IA5D7lExD,EAAE,CAAAoD,YAAA,CA4D0mE,CAAC;EAAA;AAAA;AA7DltE,MAAMK,uBAAuB,SAAS5B,oBAAoB,CAAC;EACvD;IAAS,IAAI,CAAC6B,IAAI;MAAA,IAAAC,oCAAA;MAAA,gBAAAC,gCAAAC,CAAA;QAAA,QAAAF,oCAAA,KAAAA,oCAAA,GAA+E3D,EAAE,CAAA8D,qBAAA,CAAQL,uBAAuB,IAAAI,CAAA,IAAvBJ,uBAAuB;MAAA;IAAA,IAAqD;EAAE;EACzL;IAAS,IAAI,CAACM,IAAI,kBAD+E/D,EAAE,CAAAgE,iBAAA;MAAAC,IAAA,EACJR,uBAAuB;MAAAS,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADrBpE,EAAE,CAAAqE,0BAAA;IAAA,EACiI;EAAE;AAC1O;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGtE,EAAE,CAAAuE,iBAAA,CAGXd,uBAAuB,EAAc,CAAC;IACtHQ,IAAI,EAAEhE,SAAS;IACfuE,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBM,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMC,4BAA4B,CAAC;EAC/B;IAAS,IAAI,CAAChB,IAAI,YAAAiB,qCAAAd,CAAA;MAAA,YAAAA,CAAA,IAAyFa,4BAA4B;IAAA,CAAmD;EAAE;EAC5L;IAAS,IAAI,CAACX,IAAI,kBAb+E/D,EAAE,CAAAgE,iBAAA;MAAAC,IAAA,EAaJS,4BAA4B;MAAAR,SAAA;MAAAC,UAAA;IAAA,EAA6F;EAAE;AAC9N;AACA;EAAA,QAAAG,SAAA,oBAAAA,SAAA,KAfqGtE,EAAE,CAAAuE,iBAAA,CAeXG,4BAA4B,EAAc,CAAC;IAC3HT,IAAI,EAAEhE,SAAS;IACfuE,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBM,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMG,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,GAAG,GAAG5E,MAAM,CAACC,iBAAiB,CAAC;IACpC,IAAI,CAAC4E,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,IAAI,GAAG,GAAG;IACf,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACzC,IAAI,GAAG,KAAK;IACjB,IAAI,CAAC0C,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,UAAU,GAAG,IAAIlF,YAAY,CAAC,CAAC;EACxC;EACAmF,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,UAAU,CAAC,KAAK,CAAC;IACtB,IAAI,CAACV,GAAG,CAACW,YAAY,CAAC,CAAC;EAC3B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACP,QAAQ,EAAE;MAChB,IAAI,CAACK,UAAU,CAAC,CAAC,IAAI,CAAC7C,IAAI,CAAC;IAC/B;EACJ;EACAgD,gBAAgBA,CAACC,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACjD,IAAI,EAAE;MACZ;IACJ;IACAiD,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,CAACL,UAAU,CAAC,KAAK,CAAC;EAC1B;EACAA,UAAUA,CAAC7C,IAAI,EAAE;IACb,IAAI,IAAI,CAACA,IAAI,KAAKA,IAAI,EAAE;MACpB;IACJ;IACA,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC2C,UAAU,CAACQ,IAAI,CAACnD,IAAI,CAAC;EAC9B;EACA;IAAS,IAAI,CAACe,IAAI,YAAAqC,yBAAAlC,CAAA;MAAA,YAAAA,CAAA,IAAyFe,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACoB,IAAI,kBA5D+EhG,EAAE,CAAAiG,iBAAA;MAAAhC,IAAA,EA4DJW,gBAAgB;MAAAV,SAAA;MAAAgC,cAAA,WAAAC,gCAAAjE,EAAA,EAAAC,GAAA,EAAAiE,QAAA;QAAA,IAAAlE,EAAA;UA5DdlC,EAAE,CAAAqG,cAAA,CAAAD,QAAA,EA4DihB1B,4BAA4B;UA5D/iB1E,EAAE,CAAAqG,cAAA,CAAAD,QAAA,EA4D2nB3C,uBAAuB;QAAA;QAAA,IAAAvB,EAAA;UAAA,IAAAoE,EAAA;UA5DppBtG,EAAE,CAAAuG,cAAA,CAAAD,EAAA,GAAFtG,EAAE,CAAAwG,WAAA,QAAArE,GAAA,CAAAsE,YAAA,GAAAH,EAAA,CAAAI,KAAA;UAAF1G,EAAE,CAAAuG,cAAA,CAAAD,EAAA,GAAFtG,EAAE,CAAAwG,WAAA,QAAArE,GAAA,CAAAkB,WAAA,GAAAiD,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,8BAAA3E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlC,EAAE,CAAA8G,WAAA,iBAAA3E,GAAA,CAAA8C,OAAA,eAAA9C,GAAA,CAAA+C,IAAA;UAAFlF,EAAE,CAAA+G,WAAA,gBAAA5E,GAAA,CAAA4C,SA4DW,CAAC,eAAA5C,GAAA,CAAA6C,SAAD,CAAC,cAAA7C,GAAA,CAAAgD,QAAD,CAAC;QAAA;MAAA;MAAA6B,MAAA;QAAAjC,SAAA;QAAAC,SAAA;QAAAC,OAAA;QAAAC,IAAA;QAAAC,QAAA;QAAAC,YAAA;QAAAzC,IAAA;QAAA0C,KAAA;MAAA;MAAA4B,OAAA;QAAA3B,UAAA;MAAA;MAAAnB,UAAA;MAAAC,QAAA,GA5DdpE,EAAE,CAAAkH,mBAAA;MAAAC,kBAAA,EAAAnF,GAAA;MAAAoF,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAtF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlC,EAAE,CAAAyH,eAAA,CAAA1F,GAAA;UAAF/B,EAAE,CAAAkD,cAAA,YA4DqxB,CAAC,eAAoV,CAAC;UA5D7mClD,EAAE,CAAA0H,UAAA,mBAAAC,kDAAA;YAAA,OA4DiiCxF,GAAA,CAAAuD,WAAA,CAAY,CAAC;UAAA,CAAC,CAAC,yBAAAkC,wDAAAC,MAAA;YAAA,OAA0B1F,GAAA,CAAAwD,gBAAA,CAAAkC,MAAuB,CAAC;UAAA,CAAC,CAAC;UA5DtmC7H,EAAE,CAAAkD,cAAA,aA4D4tC,CAAC;UA5D/tClD,EAAE,CAAAwD,YAAA,EA4DwvC,CAAC;UA5D3vCxD,EAAE,CAAAoD,YAAA,CA4DywC,CAAC;UA5D5wCpD,EAAE,CAAAmD,UAAA,IAAAlB,wCAAA,yBA4DqzC,CAAC;UA5DxzCjC,EAAE,CAAAoD,YAAA,CA4D68C,CAAC;UA5Dh9CpD,EAAE,CAAAkD,cAAA,mBA4D6hD,CAAC;UA5DhiDlD,EAAE,CAAAmD,UAAA,IAAAG,uCAAA,wBA4DqkD,CAAC,IAAAC,+BAAA,gBAAkc,CAAC;UA5D3gEvD,EAAE,CAAAoD,YAAA,CA4D6nE,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAlB,EAAA;UA5DxoElC,EAAE,CAAAyC,SAAA,CA4Dm8B,CAAC;UA5Dt8BzC,EAAE,CAAA+G,WAAA,wBAAA5E,GAAA,CAAAiD,YA4Dm8B,CAAC,kBAAAjD,GAAA,CAAAQ,IAAuC,CAAC;UA5D9+B3C,EAAE,CAAA0C,UAAA,aAAAP,GAAA,CAAAgD,QA4D4gC,CAAC;UA5D/gCnF,EAAE,CAAAyC,SAAA,EA4DkzC,CAAC;UA5DrzCzC,EAAE,CAAA0C,UAAA,SAAAP,GAAA,CAAA6C,SA4DkzC,CAAC;UA5DrzChF,EAAE,CAAAyC,SAAA,CA4Dy/C,CAAC;UA5D5/CzC,EAAE,CAAA0C,UAAA,UAAAP,GAAA,CAAAkD,KA4Dy/C,CAAC,aAAAlD,GAAA,CAAAQ,IAA4B,CAAC;UA5DzhD3C,EAAE,CAAAyC,SAAA,EA4D09D,CAAC;UA5D79DzC,EAAE,CAAA0C,UAAA,SAAAP,GAAA,CAAAsE,YA4D09D,CAAC;QAAA;MAAA;MAAAqB,YAAA,GAAguFtG,IAAI,EAA6FM,kBAAkB,EAA8HF,UAAU,EAAiFH,EAAE,CAACsG,kBAAkB,EAAsFtG,EAAE,CAACuG,gBAAgB,EAA+DrG,OAAO;MAAAsG,MAAA;MAAAC,eAAA;IAAA,EAAgH;EAAE;AACp6K;AACA;EAAA,QAAA5D,SAAA,oBAAAA,SAAA,KA9DqGtE,EAAE,CAAAuE,iBAAA,CA8DXK,gBAAgB,EAAc,CAAC;IAC/GX,IAAI,EAAE5D,SAAS;IACfmE,IAAI,EAAE,CAAC;MAAEL,UAAU,EAAE,IAAI;MAAEM,QAAQ,EAAE,oBAAoB;MAAE0D,OAAO,EAAE,CAAC3G,IAAI,EAAEM,kBAAkB,EAAEF,UAAU,EAAEF,SAAS,EAAEC,OAAO,CAAC;MAAEuG,eAAe,EAAE5H,uBAAuB,CAAC8H,MAAM;MAAEC,IAAI,EAAE;QAC3K,qBAAqB,EAAE,WAAW;QAClC,oBAAoB,EAAE,WAAW;QACjC,qBAAqB,EAAE,SAAS;QAChC,kBAAkB,EAAE,MAAM;QAC1B,mBAAmB,EAAE;MACzB,CAAC;MAAEd,QAAQ,EAAE,u8CAAu8C;MAAEU,MAAM,EAAE,CAAC,0/EAA0/E;IAAE,CAAC;EACx+H,CAAC,CAAC,QAAkB;IAAExB,YAAY,EAAE,CAAC;MAC7BxC,IAAI,EAAE1D,YAAY;MAClBiE,IAAI,EAAE,CAACE,4BAA4B;IACvC,CAAC,CAAC;IAAErB,WAAW,EAAE,CAAC;MACdY,IAAI,EAAE1D,YAAY;MAClBiE,IAAI,EAAE,CAACf,uBAAuB;IAClC,CAAC,CAAC;IAAEsB,SAAS,EAAE,CAAC;MACZd,IAAI,EAAEzD;IACV,CAAC,CAAC;IAAEwE,SAAS,EAAE,CAAC;MACZf,IAAI,EAAEzD;IACV,CAAC,CAAC;IAAEyE,OAAO,EAAE,CAAC;MACVhB,IAAI,EAAEzD;IACV,CAAC,CAAC;IAAE0E,IAAI,EAAE,CAAC;MACPjB,IAAI,EAAEzD;IACV,CAAC,CAAC;IAAE2E,QAAQ,EAAE,CAAC;MACXlB,IAAI,EAAEzD;IACV,CAAC,CAAC;IAAE4E,YAAY,EAAE,CAAC;MACfnB,IAAI,EAAEzD;IACV,CAAC,CAAC;IAAEmC,IAAI,EAAE,CAAC;MACPsB,IAAI,EAAEzD;IACV,CAAC,CAAC;IAAE6E,KAAK,EAAE,CAAC;MACRpB,IAAI,EAAEzD;IACV,CAAC,CAAC;IAAE8E,UAAU,EAAE,CAAC;MACbrB,IAAI,EAAExD;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6H,qBAAqB,CAAC;EACxBzD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0D,UAAU,GAAGrI,MAAM,CAACQ,UAAU,CAAC;IACpC,IAAI,CAAC8H,cAAc,GAAG3H,WAAW;IACjC,IAAI,CAAC4H,WAAW,GAAG,IAAI;IACvB;IACA,MAAMC,KAAK,GAAGxI,MAAM,CAACe,QAAQ,CAAC;IAC9ByH,KAAK,CAACC,WAAW,GAAG,UAAU;IAC9BD,KAAK,CAACE,SAAS,GAAG,IAAI;EAC1B;EACAC,kBAAkBA,CAAA,EAAG;IACjB,MAAM;MAAEL;IAAe,CAAC,GAAG,IAAI;IAC/B,MAAMM,KAAK,GAAGhI,mBAAmB,CAAC0H,cAAc,CAAC;IACjD,MAAMO,WAAW,GAAGD,KAAK,CAACE,IAAI,CAAC9H,QAAQ,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAC8H,QAAQ,EAAEC,OAAO,CAAC,KAAKA,OAAO,CAACC,IAAI,CAAEC,IAAI,IAAK,CAACH,QAAQ,CAACI,QAAQ,CAACD,IAAI,CAAC,IAAIA,IAAI,CAACzG,IAAI,CAAC,CAAC,EAAEvB,MAAM,CAACL,YAAY,CAAC,CAAC;IAC7J,MAAMuI,SAAS,GAAGjI,KAAK,CAACyH,KAAK,CAACE,IAAI,CAAC1H,SAAS,CAAEiI,IAAI,IAAKlI,KAAK,CAAC,GAAGkI,IAAI,CAACpI,GAAG,CAAEqI,GAAG,IAAKA,GAAG,CAAClE,UAAU,CAAC0D,IAAI,CAAC5H,MAAM,CAACG,QAAQ,CAAC,EAAEJ,GAAG,CAAC,MAAMqI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAET,WAAW,CAAC,CAACC,IAAI,CAAC5H,MAAM,CAAC,MAAM,IAAI,CAACqH,WAAW,CAAC,EAAE7H,kBAAkB,CAAC,IAAI,CAAC2H,UAAU,CAAC,CAAC;IACnOe,SAAS,CAACG,SAAS,CAAEC,UAAU,IAAK;MAChClB,cAAc,CAACmB,OAAO,CAAEH,GAAG,IAAK;QAC5B,IAAIE,UAAU,KAAKF,GAAG,EAAE;UACpBA,GAAG,CAACjE,KAAK,CAAC,CAAC;QACf;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC7B,IAAI,YAAAkG,8BAAA/F,CAAA;MAAA,YAAAA,CAAA,IAAyFyE,qBAAqB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAACvE,IAAI,kBAzH+E/D,EAAE,CAAAgE,iBAAA;MAAAC,IAAA,EAyHJqE,qBAAqB;MAAApE,SAAA;MAAAgC,cAAA,WAAA2D,qCAAA3H,EAAA,EAAAC,GAAA,EAAAiE,QAAA;QAAA,IAAAlE,EAAA;UAzHnBlC,EAAE,CAAAqG,cAAA,CAAAD,QAAA,EAyHiKxB,gBAAgB;QAAA;QAAA,IAAA1C,EAAA;UAAA,IAAAoE,EAAA;UAzHnLtG,EAAE,CAAAuG,cAAA,CAAAD,EAAA,GAAFtG,EAAE,CAAAwG,WAAA,QAAArE,GAAA,CAAAqG,cAAA,GAAAlC,EAAA;QAAA;MAAA;MAAAU,MAAA;QAAAyB,WAAA;MAAA;MAAAtE,UAAA;MAAAC,QAAA,GAAFpE,EAAE,CAAA8J,uBAAA;QAAAC,SAAA,EAyHoN/I,IAAI,CAACC,QAAQ;QAAA+F,MAAA;MAAA;IAAA,EAAoD;EAAE;AAC9X;AACA;EAAA,QAAA1C,SAAA,oBAAAA,SAAA,KA3HqGtE,EAAE,CAAAuE,iBAAA,CA2HX+D,qBAAqB,EAAc,CAAC;IACpHrE,IAAI,EAAEhE,SAAS;IACfuE,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBM,QAAQ,EAAE,eAAe;MACzBuF,cAAc,EAAE,CACZ;QACID,SAAS,EAAE9I,QAAQ;QACnB+F,MAAM,EAAE,CAAC,SAAS;MACtB,CAAC;IAET,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEwB,cAAc,EAAE,CAAC;MAC3EvE,IAAI,EAAEtD,eAAe;MACrB6D,IAAI,EAAE,CAACI,gBAAgB;IAC3B,CAAC,CAAC;IAAE6D,WAAW,EAAE,CAAC;MACdxE,IAAI,EAAEzD;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyJ,YAAY,GAAG,CACjBrF,gBAAgB,EAChB0D,qBAAqB,EACrB7E,uBAAuB,EACvBiB,4BAA4B,CAC/B;;AAED;AACA;AACA;;AAEA,SAASuF,YAAY,EAAE3B,qBAAqB,EAAE1D,gBAAgB,EAAEnB,uBAAuB,EAAEiB,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}