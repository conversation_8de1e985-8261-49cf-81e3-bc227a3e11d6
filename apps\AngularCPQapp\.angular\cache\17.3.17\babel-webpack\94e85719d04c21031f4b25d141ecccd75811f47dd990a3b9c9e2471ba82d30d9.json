{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { TUI_APPEARANCE_OPTIONS, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nclass TuiMessageStyles {\n  static {\n    this.ɵfac = function TuiMessageStyles_Factory(t) {\n      return new (t || TuiMessageStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiMessageStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-message\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiMessageStyles_Template(rf, ctx) {},\n      styles: [\"[tuiMessage]{display:inline-flex;padding:.5rem .625rem;min-block-size:2.25rem;block-size:auto;box-sizing:border-box;inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;isolation:isolate;white-space:nowrap;text-align:start;font:var(--tui-font-text-ui-m);border-radius:var(--tui-radius-l)}[tuiMessage]>[tuiLink]{color:inherit!important;-webkit-text-decoration:underline solid!important;text-decoration:underline solid!important}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiMessageStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-message'\n      },\n      styles: [\"[tuiMessage]{display:inline-flex;padding:.5rem .625rem;min-block-size:2.25rem;block-size:auto;box-sizing:border-box;inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;isolation:isolate;white-space:nowrap;text-align:start;font:var(--tui-font-text-ui-m);border-radius:var(--tui-radius-l)}[tuiMessage]>[tuiLink]{color:inherit!important;-webkit-text-decoration:underline solid!important;text-decoration:underline solid!important}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiMessage {\n  constructor() {\n    this.nothing = tuiWithStyles(TuiMessageStyles);\n  }\n  static {\n    this.ɵfac = function TuiMessage_Factory(t) {\n      return new (t || TuiMessage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiMessage,\n      selectors: [[\"\", \"tuiMessage\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: TUI_APPEARANCE_OPTIONS,\n        useValue: {\n          appearance: 'neutral'\n        }\n      }]), i0.ɵɵHostDirectivesFeature([i1.TuiWithAppearance])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiMessage, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiMessage]',\n      providers: [{\n        provide: TUI_APPEARANCE_OPTIONS,\n        useValue: {\n          appearance: 'neutral'\n        }\n      }],\n      hostDirectives: [TuiWithAppearance]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiMessage };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Directive", "tuiWithStyles", "i1", "TUI_APPEARANCE_OPTIONS", "TuiWithAppearance", "TuiMessageStyles", "ɵfac", "TuiMessageStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiMessageStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiMessage", "constructor", "nothing", "TuiMessage_Factory", "ɵdir", "ɵɵdefineDirective", "ɵɵProvidersFeature", "provide", "useValue", "appearance", "ɵɵHostDirectivesFeature", "selector", "providers", "hostDirectives"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-message.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { TUI_APPEARANCE_OPTIONS, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\n\nclass TuiMessageStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMessageStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiMessageStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-message\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiMessage]{display:inline-flex;padding:.5rem .625rem;min-block-size:2.25rem;block-size:auto;box-sizing:border-box;inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;isolation:isolate;white-space:nowrap;text-align:start;font:var(--tui-font-text-ui-m);border-radius:var(--tui-radius-l)}[tuiMessage]>[tuiLink]{color:inherit!important;-webkit-text-decoration:underline solid!important;text-decoration:underline solid!important}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMessageStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-message',\n                    }, styles: [\"[tuiMessage]{display:inline-flex;padding:.5rem .625rem;min-block-size:2.25rem;block-size:auto;box-sizing:border-box;inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;isolation:isolate;white-space:nowrap;text-align:start;font:var(--tui-font-text-ui-m);border-radius:var(--tui-radius-l)}[tuiMessage]>[tuiLink]{color:inherit!important;-webkit-text-decoration:underline solid!important;text-decoration:underline solid!important}\\n\"] }]\n        }] });\nclass TuiMessage {\n    constructor() {\n        this.nothing = tuiWithStyles(TuiMessageStyles);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMessage, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiMessage, isStandalone: true, selector: \"[tuiMessage]\", providers: [\n            {\n                provide: TUI_APPEARANCE_OPTIONS,\n                useValue: { appearance: 'neutral' },\n            },\n        ], hostDirectives: [{ directive: i1.TuiWithAppearance }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMessage, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiMessage]',\n                    providers: [\n                        {\n                            provide: TUI_APPEARANCE_OPTIONS,\n                            useValue: { appearance: 'neutral' },\n                        },\n                    ],\n                    hostDirectives: [TuiWithAppearance],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiMessage };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,QAAQ,eAAe;AAChG,SAASC,aAAa,QAAQ,mCAAmC;AACjE,OAAO,KAAKC,EAAE,MAAM,sCAAsC;AAC1D,SAASC,sBAAsB,EAAEC,iBAAiB,QAAQ,sCAAsC;AAEhG,MAAMC,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACI,IAAI,kBAD+Eb,EAAE,CAAAc,iBAAA;MAAAC,IAAA,EACJN,gBAAgB;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADdnB,EAAE,CAAAoB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC2sB;EAAE;AACpzB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG9B,EAAE,CAAA+B,iBAAA,CAGXtB,gBAAgB,EAAc,CAAC;IAC/GM,IAAI,EAAEd,SAAS;IACf+B,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAE1B,iBAAiB,CAAC+B,IAAI;MAAEJ,eAAe,EAAE1B,uBAAuB,CAAC+B,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,+cAA+c;IAAE,CAAC;EAC1e,CAAC,CAAC;AAAA;AACV,MAAMU,UAAU,CAAC;EACbC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGlC,aAAa,CAACI,gBAAgB,CAAC;EAClD;EACA;IAAS,IAAI,CAACC,IAAI,YAAA8B,mBAAA5B,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,UAAU;IAAA,CAAmD;EAAE;EAC1K;IAAS,IAAI,CAACI,IAAI,kBAd+EzC,EAAE,CAAA0C,iBAAA;MAAA3B,IAAA,EAcJsB,UAAU;MAAArB,SAAA;MAAAE,UAAA;MAAAC,QAAA,GAdRnB,EAAE,CAAA2C,kBAAA,CAciE,CAC5J;QACIC,OAAO,EAAErC,sBAAsB;QAC/BsC,QAAQ,EAAE;UAAEC,UAAU,EAAE;QAAU;MACtC,CAAC,CACJ,GAnB4F9C,EAAE,CAAA+C,uBAAA,EAmB9DzC,EAAE,CAACE,iBAAiB;IAAA,EAAoB;EAAE;AACnF;AACA;EAAA,QAAAsB,SAAA,oBAAAA,SAAA,KArBqG9B,EAAE,CAAA+B,iBAAA,CAqBXM,UAAU,EAAc,CAAC;IACzGtB,IAAI,EAAEX,SAAS;IACf4B,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChB8B,QAAQ,EAAE,cAAc;MACxBC,SAAS,EAAE,CACP;QACIL,OAAO,EAAErC,sBAAsB;QAC/BsC,QAAQ,EAAE;UAAEC,UAAU,EAAE;QAAU;MACtC,CAAC,CACJ;MACDI,cAAc,EAAE,CAAC1C,iBAAiB;IACtC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS6B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}