{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, ViewContainerRef, TemplateRef, Directive, Input } from '@angular/core';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nconst MAX_VALUE = 0x10000;\nclass TuiRepeatTimesContext {\n  constructor($implicit) {\n    this.$implicit = $implicit;\n  }\n}\n/**\n * Directive similar to ngFor but using a number of repetitions rather than an array\n *\n * {@link TuiRepeatTimes.tuiRepeatTimesOf requested number of times}.\n * {@link TuiRepeatTimesContext context} for every instance of the template inherits outer context and stores\n * {@link TuiRepeatTimesContext.$implicit index} of a template instance.\n */\nclass TuiRepeatTimes {\n  constructor() {\n    this.viewContainer = inject(ViewContainerRef);\n    this.templateRef = inject(TemplateRef);\n  }\n  set tuiRepeatTimesOf(count) {\n    const safeCount = Math.floor(tuiClamp(count, 0, MAX_VALUE));\n    const {\n      length\n    } = this.viewContainer;\n    if (count < length) {\n      this.removeContainers(length - count);\n    } else {\n      this.addContainers(safeCount);\n    }\n  }\n  addContainers(count) {\n    for (let index = this.viewContainer.length; index < count; index++) {\n      this.viewContainer.createEmbeddedView(this.templateRef, new TuiRepeatTimesContext(index));\n    }\n  }\n  removeContainers(amount) {\n    for (let index = 0; index < amount; index++) {\n      this.viewContainer.remove();\n    }\n  }\n  static {\n    this.ɵfac = function TuiRepeatTimes_Factory(t) {\n      return new (t || TuiRepeatTimes)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiRepeatTimes,\n      selectors: [[\"\", \"tuiRepeatTimes\", \"\", \"tuiRepeatTimesOf\", \"\"]],\n      inputs: {\n        tuiRepeatTimesOf: \"tuiRepeatTimesOf\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiRepeatTimes, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiRepeatTimes][tuiRepeatTimesOf]'\n    }]\n  }], null, {\n    tuiRepeatTimesOf: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiRepeatTimes, TuiRepeatTimesContext };", "map": {"version": 3, "names": ["i0", "inject", "ViewContainerRef", "TemplateRef", "Directive", "Input", "tui<PERSON><PERSON>", "MAX_VALUE", "TuiRepeatTimesContext", "constructor", "$implicit", "TuiRepeatTimes", "viewContainer", "templateRef", "tuiRepeatTimesOf", "count", "safeCount", "Math", "floor", "length", "removeContainers", "addContainers", "index", "createEmbeddedView", "amount", "remove", "ɵfac", "TuiRepeatTimes_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-repeat-times.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, ViewContainerRef, TemplateRef, Directive, Input } from '@angular/core';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\n\nconst MAX_VALUE = 0x10000;\nclass TuiRepeatTimesContext {\n    constructor($implicit) {\n        this.$implicit = $implicit;\n    }\n}\n/**\n * Directive similar to ngFor but using a number of repetitions rather than an array\n *\n * {@link TuiRepeatTimes.tuiRepeatTimesOf requested number of times}.\n * {@link TuiRepeatTimesContext context} for every instance of the template inherits outer context and stores\n * {@link TuiRepeatTimesContext.$implicit index} of a template instance.\n */\nclass TuiRepeatTimes {\n    constructor() {\n        this.viewContainer = inject(ViewContainerRef);\n        this.templateRef = inject((TemplateRef));\n    }\n    set tuiRepeatTimesOf(count) {\n        const safeCount = Math.floor(tuiClamp(count, 0, MAX_VALUE));\n        const { length } = this.viewContainer;\n        if (count < length) {\n            this.removeContainers(length - count);\n        }\n        else {\n            this.addContainers(safeCount);\n        }\n    }\n    addContainers(count) {\n        for (let index = this.viewContainer.length; index < count; index++) {\n            this.viewContainer.createEmbeddedView(this.templateRef, new TuiRepeatTimesContext(index));\n        }\n    }\n    removeContainers(amount) {\n        for (let index = 0; index < amount; index++) {\n            this.viewContainer.remove();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiRepeatTimes, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiRepeatTimes, isStandalone: true, selector: \"[tuiRepeatTimes][tuiRepeatTimesOf]\", inputs: { tuiRepeatTimesOf: \"tuiRepeatTimesOf\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiRepeatTimes, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiRepeatTimes][tuiRepeatTimesOf]',\n                }]\n        }], propDecorators: { tuiRepeatTimesOf: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiRepeatTimes, TuiRepeatTimesContext };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACvF,SAASC,QAAQ,QAAQ,0BAA0B;AAEnD,MAAMC,SAAS,GAAG,OAAO;AACzB,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACG,aAAa,GAAGX,MAAM,CAACC,gBAAgB,CAAC;IAC7C,IAAI,CAACW,WAAW,GAAGZ,MAAM,CAAEE,WAAY,CAAC;EAC5C;EACA,IAAIW,gBAAgBA,CAACC,KAAK,EAAE;IACxB,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACZ,QAAQ,CAACS,KAAK,EAAE,CAAC,EAAER,SAAS,CAAC,CAAC;IAC3D,MAAM;MAAEY;IAAO,CAAC,GAAG,IAAI,CAACP,aAAa;IACrC,IAAIG,KAAK,GAAGI,MAAM,EAAE;MAChB,IAAI,CAACC,gBAAgB,CAACD,MAAM,GAAGJ,KAAK,CAAC;IACzC,CAAC,MACI;MACD,IAAI,CAACM,aAAa,CAACL,SAAS,CAAC;IACjC;EACJ;EACAK,aAAaA,CAACN,KAAK,EAAE;IACjB,KAAK,IAAIO,KAAK,GAAG,IAAI,CAACV,aAAa,CAACO,MAAM,EAAEG,KAAK,GAAGP,KAAK,EAAEO,KAAK,EAAE,EAAE;MAChE,IAAI,CAACV,aAAa,CAACW,kBAAkB,CAAC,IAAI,CAACV,WAAW,EAAE,IAAIL,qBAAqB,CAACc,KAAK,CAAC,CAAC;IAC7F;EACJ;EACAF,gBAAgBA,CAACI,MAAM,EAAE;IACrB,KAAK,IAAIF,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGE,MAAM,EAAEF,KAAK,EAAE,EAAE;MACzC,IAAI,CAACV,aAAa,CAACa,MAAM,CAAC,CAAC;IAC/B;EACJ;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFjB,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACkB,IAAI,kBAD+E7B,EAAE,CAAA8B,iBAAA;MAAAC,IAAA,EACJpB,cAAc;MAAAqB,SAAA;MAAAC,MAAA;QAAAnB,gBAAA;MAAA;MAAAoB,UAAA;IAAA,EAAuI;EAAE;AAC1P;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGnC,EAAE,CAAAoC,iBAAA,CAGXzB,cAAc,EAAc,CAAC;IAC7GoB,IAAI,EAAE3B,SAAS;IACfiC,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAExB,gBAAgB,EAAE,CAAC;MACjCiB,IAAI,EAAE1B;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASM,cAAc,EAAEH,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}