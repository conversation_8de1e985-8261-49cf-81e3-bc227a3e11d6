{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, ContentChild, Input } from '@angular/core';\nimport { NgControl } from '@angular/forms';\nimport * as i1 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport { tuiCreateToken, tuiProvideOptions, tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i2 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearanceOptionsProvider, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport * as i3 from '@taiga-ui/core/directives/icons';\nimport { TuiWithIcons } from '@taiga-ui/core/directives/icons';\nimport { tuiAvatarOptionsProvider } from '@taiga-ui/kit/components/avatar';\nconst TUI_BLOCK_DEFAULT_OPTIONS = {\n  appearance: 'outline-grayscale',\n  size: 'l'\n};\nconst TUI_BLOCK_OPTIONS = tuiCreateToken(TUI_BLOCK_DEFAULT_OPTIONS);\nfunction tuiBlockOptionsProvider(options) {\n  return tuiProvideOptions(TUI_BLOCK_OPTIONS, options, TUI_BLOCK_DEFAULT_OPTIONS);\n}\nclass TuiBlockStyles {\n  static {\n    this.ɵfac = function TuiBlockStyles_Factory(t) {\n      return new (t || TuiBlockStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiBlockStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-block\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiBlockStyles_Template(rf, ctx) {},\n      styles: [\"[tuiBlock]{--t-height: var(--tui-height-l);--t-radius: var(--tui-radius-l);position:relative;display:inline-flex;gap:.75rem;color:var(--tui-text-primary);border-radius:var(--t-radius);min-block-size:var(--t-height);margin:0;box-sizing:border-box;cursor:pointer;overflow:hidden;font:var(--tui-font-text-m);padding:var(--tui-padding-l);isolation:isolate}[tuiBlock][data-size=s]{gap:.5rem;font:var(--tui-font-text-ui-s);padding:.5rem;--t-height: var(--tui-height-s);--t-radius: var(--tui-radius-m)}[tuiBlock][data-size=s] [tuiSubtitle]{font:var(--tui-font-text-ui-xs)}[tuiBlock][data-size=s] [tuiTooltip]{margin:0 .125rem}[tuiBlock][data-size=m]{gap:.625rem;font:var(--tui-font-text-ui-m);padding:var(--tui-padding-m);--t-height: var(--tui-height-m);--t-radius: var(--tui-radius-m)}[tuiBlock][data-size=m] input:not([tuiBlock]){margin:.125rem}[tuiBlock][data-size=m] [tuiTooltip]{margin:.125rem}[tuiBlock]._disabled{pointer-events:none;opacity:var(--tui-disabled-opacity)}[tuiBlock]._disabled :focus{visibility:hidden}[tuiBlock][data-appearance=\\\"\\\"]{justify-content:center}[tuiBlock] input[tuiBlock]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;z-index:-1;min-block-size:0;pointer-events:none;border-radius:inherit;padding:0}[tuiBlock] tui-avatar{margin:-.25rem}[tuiBlock] [tuiTitle]{flex:1;gap:0;font:inherit;color:var(--tui-text-primary)}[tuiBlock] [tuiSubtitle]{color:var(--tui-text-secondary)}[tuiBlock] [tuiTooltip]{vertical-align:bottom;margin:.25rem;font-size:1rem;border:none}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiBlockStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-block'\n      },\n      styles: [\"[tuiBlock]{--t-height: var(--tui-height-l);--t-radius: var(--tui-radius-l);position:relative;display:inline-flex;gap:.75rem;color:var(--tui-text-primary);border-radius:var(--t-radius);min-block-size:var(--t-height);margin:0;box-sizing:border-box;cursor:pointer;overflow:hidden;font:var(--tui-font-text-m);padding:var(--tui-padding-l);isolation:isolate}[tuiBlock][data-size=s]{gap:.5rem;font:var(--tui-font-text-ui-s);padding:.5rem;--t-height: var(--tui-height-s);--t-radius: var(--tui-radius-m)}[tuiBlock][data-size=s] [tuiSubtitle]{font:var(--tui-font-text-ui-xs)}[tuiBlock][data-size=s] [tuiTooltip]{margin:0 .125rem}[tuiBlock][data-size=m]{gap:.625rem;font:var(--tui-font-text-ui-m);padding:var(--tui-padding-m);--t-height: var(--tui-height-m);--t-radius: var(--tui-radius-m)}[tuiBlock][data-size=m] input:not([tuiBlock]){margin:.125rem}[tuiBlock][data-size=m] [tuiTooltip]{margin:.125rem}[tuiBlock]._disabled{pointer-events:none;opacity:var(--tui-disabled-opacity)}[tuiBlock]._disabled :focus{visibility:hidden}[tuiBlock][data-appearance=\\\"\\\"]{justify-content:center}[tuiBlock] input[tuiBlock]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;z-index:-1;min-block-size:0;pointer-events:none;border-radius:inherit;padding:0}[tuiBlock] tui-avatar{margin:-.25rem}[tuiBlock] [tuiTitle]{flex:1;gap:0;font:inherit;color:var(--tui-text-primary)}[tuiBlock] [tuiSubtitle]{color:var(--tui-text-secondary)}[tuiBlock] [tuiTooltip]{vertical-align:bottom;margin:.25rem;font-size:1rem;border:none}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiBlock {\n  constructor() {\n    this.nothing = tuiWithStyles(TuiBlockStyles);\n    this.size = inject(TUI_BLOCK_OPTIONS).size;\n  }\n  static {\n    this.ɵfac = function TuiBlock_Factory(t) {\n      return new (t || TuiBlock)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiBlock,\n      selectors: [[\"label\", \"tuiBlock\", \"\"], [\"input\", \"tuiBlock\", \"\"]],\n      contentQueries: function TuiBlock_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NgControl, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.control = _t.first);\n        }\n      },\n      hostAttrs: [\"tuiBlock\", \"\"],\n      hostVars: 3,\n      hostBindings: function TuiBlock_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size || \"l\");\n          i0.ɵɵclassProp(\"_disabled\", !!(ctx.control == null ? null : ctx.control.disabled));\n        }\n      },\n      inputs: {\n        size: [i0.ɵɵInputFlags.None, \"tuiBlock\", \"size\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAppearanceOptionsProvider(TUI_BLOCK_OPTIONS), tuiAvatarOptionsProvider({\n        size: 's'\n      })]), i0.ɵɵHostDirectivesFeature([i1.TuiNativeValidator, i2.TuiWithAppearance, i3.TuiWithIcons])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiBlock, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'label[tuiBlock],input[tuiBlock]',\n      providers: [tuiAppearanceOptionsProvider(TUI_BLOCK_OPTIONS), tuiAvatarOptionsProvider({\n        size: 's'\n      })],\n      hostDirectives: [TuiNativeValidator, TuiWithAppearance, TuiWithIcons],\n      host: {\n        tuiBlock: '',\n        '[attr.data-size]': 'size || \"l\"',\n        '[class._disabled]': '!!this.control?.disabled'\n      }\n    }]\n  }], null, {\n    control: [{\n      type: ContentChild,\n      args: [NgControl]\n    }],\n    size: [{\n      type: Input,\n      args: ['tuiBlock']\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_BLOCK_DEFAULT_OPTIONS, TUI_BLOCK_OPTIONS, TuiBlock, tuiBlockOptionsProvider };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "Directive", "ContentChild", "Input", "NgControl", "i1", "TuiNativeValidator", "tuiCreateToken", "tuiProvideOptions", "tuiWithStyles", "i2", "tuiAppearanceOptionsProvider", "TuiWithAppearance", "i3", "TuiWithIcons", "tuiAvatarOptionsProvider", "TUI_BLOCK_DEFAULT_OPTIONS", "appearance", "size", "TUI_BLOCK_OPTIONS", "tuiBlockOptionsProvider", "options", "TuiBlockStyles", "ɵfac", "TuiBlockStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiBlockStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "nothing", "TuiBlock_Factory", "ɵdir", "ɵɵdefineDirective", "contentQueries", "TuiBlock_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "control", "first", "hostVars", "hostBindings", "TuiBlock_HostBindings", "ɵɵattribute", "ɵɵclassProp", "disabled", "inputs", "ɵɵInputFlags", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "selector", "providers", "hostDirectives", "tui<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-block.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, ContentChild, Input } from '@angular/core';\nimport { NgControl } from '@angular/forms';\nimport * as i1 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport { tuiCreateToken, tuiProvideOptions, tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i2 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearanceOptionsProvider, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport * as i3 from '@taiga-ui/core/directives/icons';\nimport { TuiWithIcons } from '@taiga-ui/core/directives/icons';\nimport { tuiAvatarOptionsProvider } from '@taiga-ui/kit/components/avatar';\n\nconst TUI_BLOCK_DEFAULT_OPTIONS = {\n    appearance: 'outline-grayscale',\n    size: 'l',\n};\nconst TUI_BLOCK_OPTIONS = tuiCreateToken(TUI_BLOCK_DEFAULT_OPTIONS);\nfunction tuiBlockOptionsProvider(options) {\n    return tuiProvideOptions(TUI_BLOCK_OPTIONS, options, TUI_BLOCK_DEFAULT_OPTIONS);\n}\n\nclass TuiBlockStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBlockStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiBlockStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-block\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiBlock]{--t-height: var(--tui-height-l);--t-radius: var(--tui-radius-l);position:relative;display:inline-flex;gap:.75rem;color:var(--tui-text-primary);border-radius:var(--t-radius);min-block-size:var(--t-height);margin:0;box-sizing:border-box;cursor:pointer;overflow:hidden;font:var(--tui-font-text-m);padding:var(--tui-padding-l);isolation:isolate}[tuiBlock][data-size=s]{gap:.5rem;font:var(--tui-font-text-ui-s);padding:.5rem;--t-height: var(--tui-height-s);--t-radius: var(--tui-radius-m)}[tuiBlock][data-size=s] [tuiSubtitle]{font:var(--tui-font-text-ui-xs)}[tuiBlock][data-size=s] [tuiTooltip]{margin:0 .125rem}[tuiBlock][data-size=m]{gap:.625rem;font:var(--tui-font-text-ui-m);padding:var(--tui-padding-m);--t-height: var(--tui-height-m);--t-radius: var(--tui-radius-m)}[tuiBlock][data-size=m] input:not([tuiBlock]){margin:.125rem}[tuiBlock][data-size=m] [tuiTooltip]{margin:.125rem}[tuiBlock]._disabled{pointer-events:none;opacity:var(--tui-disabled-opacity)}[tuiBlock]._disabled :focus{visibility:hidden}[tuiBlock][data-appearance=\\\"\\\"]{justify-content:center}[tuiBlock] input[tuiBlock]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;z-index:-1;min-block-size:0;pointer-events:none;border-radius:inherit;padding:0}[tuiBlock] tui-avatar{margin:-.25rem}[tuiBlock] [tuiTitle]{flex:1;gap:0;font:inherit;color:var(--tui-text-primary)}[tuiBlock] [tuiSubtitle]{color:var(--tui-text-secondary)}[tuiBlock] [tuiTooltip]{vertical-align:bottom;margin:.25rem;font-size:1rem;border:none}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBlockStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-block',\n                    }, styles: [\"[tuiBlock]{--t-height: var(--tui-height-l);--t-radius: var(--tui-radius-l);position:relative;display:inline-flex;gap:.75rem;color:var(--tui-text-primary);border-radius:var(--t-radius);min-block-size:var(--t-height);margin:0;box-sizing:border-box;cursor:pointer;overflow:hidden;font:var(--tui-font-text-m);padding:var(--tui-padding-l);isolation:isolate}[tuiBlock][data-size=s]{gap:.5rem;font:var(--tui-font-text-ui-s);padding:.5rem;--t-height: var(--tui-height-s);--t-radius: var(--tui-radius-m)}[tuiBlock][data-size=s] [tuiSubtitle]{font:var(--tui-font-text-ui-xs)}[tuiBlock][data-size=s] [tuiTooltip]{margin:0 .125rem}[tuiBlock][data-size=m]{gap:.625rem;font:var(--tui-font-text-ui-m);padding:var(--tui-padding-m);--t-height: var(--tui-height-m);--t-radius: var(--tui-radius-m)}[tuiBlock][data-size=m] input:not([tuiBlock]){margin:.125rem}[tuiBlock][data-size=m] [tuiTooltip]{margin:.125rem}[tuiBlock]._disabled{pointer-events:none;opacity:var(--tui-disabled-opacity)}[tuiBlock]._disabled :focus{visibility:hidden}[tuiBlock][data-appearance=\\\"\\\"]{justify-content:center}[tuiBlock] input[tuiBlock]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;z-index:-1;min-block-size:0;pointer-events:none;border-radius:inherit;padding:0}[tuiBlock] tui-avatar{margin:-.25rem}[tuiBlock] [tuiTitle]{flex:1;gap:0;font:inherit;color:var(--tui-text-primary)}[tuiBlock] [tuiSubtitle]{color:var(--tui-text-secondary)}[tuiBlock] [tuiTooltip]{vertical-align:bottom;margin:.25rem;font-size:1rem;border:none}\\n\"] }]\n        }] });\nclass TuiBlock {\n    constructor() {\n        this.nothing = tuiWithStyles(TuiBlockStyles);\n        this.size = inject(TUI_BLOCK_OPTIONS).size;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBlock, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiBlock, isStandalone: true, selector: \"label[tuiBlock],input[tuiBlock]\", inputs: { size: [\"tuiBlock\", \"size\"] }, host: { attributes: { \"tuiBlock\": \"\" }, properties: { \"attr.data-size\": \"size || \\\"l\\\"\", \"class._disabled\": \"!!this.control?.disabled\" } }, providers: [\n            tuiAppearanceOptionsProvider(TUI_BLOCK_OPTIONS),\n            tuiAvatarOptionsProvider({ size: 's' }),\n        ], queries: [{ propertyName: \"control\", first: true, predicate: NgControl, descendants: true }], hostDirectives: [{ directive: i1.TuiNativeValidator }, { directive: i2.TuiWithAppearance }, { directive: i3.TuiWithIcons }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBlock, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'label[tuiBlock],input[tuiBlock]',\n                    providers: [\n                        tuiAppearanceOptionsProvider(TUI_BLOCK_OPTIONS),\n                        tuiAvatarOptionsProvider({ size: 's' }),\n                    ],\n                    hostDirectives: [TuiNativeValidator, TuiWithAppearance, TuiWithIcons],\n                    host: {\n                        tuiBlock: '',\n                        '[attr.data-size]': 'size || \"l\"',\n                        '[class._disabled]': '!!this.control?.disabled',\n                    },\n                }]\n        }], propDecorators: { control: [{\n                type: ContentChild,\n                args: [NgControl]\n            }], size: [{\n                type: Input,\n                args: ['tuiBlock']\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_BLOCK_DEFAULT_OPTIONS, TUI_BLOCK_OPTIONS, TuiBlock, tuiBlockOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAEC,KAAK,QAAQ,eAAe;AAC7H,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,KAAKC,EAAE,MAAM,2CAA2C;AAC/D,SAASC,kBAAkB,QAAQ,2CAA2C;AAC9E,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,aAAa,QAAQ,mCAAmC;AACpG,OAAO,KAAKC,EAAE,MAAM,sCAAsC;AAC1D,SAASC,4BAA4B,EAAEC,iBAAiB,QAAQ,sCAAsC;AACtG,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,iCAAiC;AAE1E,MAAMC,yBAAyB,GAAG;EAC9BC,UAAU,EAAE,mBAAmB;EAC/BC,IAAI,EAAE;AACV,CAAC;AACD,MAAMC,iBAAiB,GAAGZ,cAAc,CAACS,yBAAyB,CAAC;AACnE,SAASI,uBAAuBA,CAACC,OAAO,EAAE;EACtC,OAAOb,iBAAiB,CAACW,iBAAiB,EAAEE,OAAO,EAAEL,yBAAyB,CAAC;AACnF;AAEA,MAAMM,cAAc,CAAC;EACjB;IAAS,IAAI,CAACC,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACI,IAAI,kBAD+E9B,EAAE,CAAA+B,iBAAA;MAAAC,IAAA,EACJN,cAAc;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADZpC,EAAE,CAAAqC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACytD;EAAE;AACl0D;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG/C,EAAE,CAAAgD,iBAAA,CAGXtB,cAAc,EAAc,CAAC;IAC7GM,IAAI,EAAE/B,SAAS;IACfgD,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAE3C,iBAAiB,CAACgD,IAAI;MAAEJ,eAAe,EAAE3C,uBAAuB,CAACgD,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,i+CAAi+C;IAAE,CAAC;EAC5/C,CAAC,CAAC;AAAA;AACV,MAAMU,QAAQ,CAAC;EACXC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG3C,aAAa,CAACa,cAAc,CAAC;IAC5C,IAAI,CAACJ,IAAI,GAAGlB,MAAM,CAACmB,iBAAiB,CAAC,CAACD,IAAI;EAC9C;EACA;IAAS,IAAI,CAACK,IAAI,YAAA8B,iBAAA5B,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,QAAQ;IAAA,CAAmD;EAAE;EACxK;IAAS,IAAI,CAACI,IAAI,kBAf+E1D,EAAE,CAAA2D,iBAAA;MAAA3B,IAAA,EAeJsB,QAAQ;MAAArB,SAAA;MAAA2B,cAAA,WAAAC,wBAAAnB,EAAA,EAAAC,GAAA,EAAAmB,QAAA;QAAA,IAAApB,EAAA;UAfN1C,EAAE,CAAA+D,cAAA,CAAAD,QAAA,EAkB/BtD,SAAS;QAAA;QAAA,IAAAkC,EAAA;UAAA,IAAAsB,EAAA;UAlBoBhE,EAAE,CAAAiE,cAAA,CAAAD,EAAA,GAAFhE,EAAE,CAAAkE,WAAA,QAAAvB,GAAA,CAAAwB,OAAA,GAAAH,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAlC,SAAA,eAeiJ,EAAE;MAAAmC,QAAA;MAAAC,YAAA,WAAAC,sBAAA7B,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAfrJ1C,EAAE,CAAAwE,WAAA,cAAA7B,GAAA,CAAArB,IAAA,IAeI,GAAG;UAfTtB,EAAE,CAAAyE,WAAA,iBAAA9B,GAAA,CAAAwB,OAAA,kBAAAxB,GAAA,CAAAwB,OAAA,CAAAO,QAAA,CAeG,CAAC;QAAA;MAAA;MAAAC,MAAA;QAAArD,IAAA,GAfNtB,EAAE,CAAA4E,YAAA,CAAA1B,IAAA;MAAA;MAAAf,UAAA;MAAAC,QAAA,GAAFpC,EAAE,CAAA6E,kBAAA,CAesQ,CACjW9D,4BAA4B,CAACQ,iBAAiB,CAAC,EAC/CJ,wBAAwB,CAAC;QAAEG,IAAI,EAAE;MAAI,CAAC,CAAC,CAC1C,GAlB4FtB,EAAE,CAAA8E,uBAAA,EAkBgCrE,EAAE,CAACC,kBAAkB,EAAiBI,EAAE,CAACE,iBAAiB,EAAiBC,EAAE,CAACC,YAAY;IAAA,EAAoB;EAAE;AACvP;AACA;EAAA,QAAA6B,SAAA,oBAAAA,SAAA,KApBqG/C,EAAE,CAAAgD,iBAAA,CAoBXM,QAAQ,EAAc,CAAC;IACvGtB,IAAI,EAAE3B,SAAS;IACf4C,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChB4C,QAAQ,EAAE,iCAAiC;MAC3CC,SAAS,EAAE,CACPjE,4BAA4B,CAACQ,iBAAiB,CAAC,EAC/CJ,wBAAwB,CAAC;QAAEG,IAAI,EAAE;MAAI,CAAC,CAAC,CAC1C;MACD2D,cAAc,EAAE,CAACvE,kBAAkB,EAAEM,iBAAiB,EAAEE,YAAY,CAAC;MACrEkC,IAAI,EAAE;QACF8B,QAAQ,EAAE,EAAE;QACZ,kBAAkB,EAAE,aAAa;QACjC,mBAAmB,EAAE;MACzB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEf,OAAO,EAAE,CAAC;MACxBnC,IAAI,EAAE1B,YAAY;MAClB2C,IAAI,EAAE,CAACzC,SAAS;IACpB,CAAC,CAAC;IAAEc,IAAI,EAAE,CAAC;MACPU,IAAI,EAAEzB,KAAK;MACX0C,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAAS7B,yBAAyB,EAAEG,iBAAiB,EAAE+B,QAAQ,EAAE9B,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}