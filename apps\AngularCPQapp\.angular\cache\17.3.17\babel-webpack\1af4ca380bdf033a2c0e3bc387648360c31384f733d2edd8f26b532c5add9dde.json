{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, signal, Optional, SkipSelf, Input, TemplateRef, computed, ElementRef, ViewContainerRef, ViewChild, ContentChild, forwardRef, ContentChildren } from '@angular/core';\nimport { TUI_IS_ANDROID } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement, tuiValue, tuiIsElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiWithStyles, tuiCreateToken, tuiProvide, tuiPx, tuiArrayToggle, tuiDirectiveBinding } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiItem } from '@taiga-ui/cdk/directives/item';\nimport { TuiLabel } from '@taiga-ui/core/components/label';\nimport * as i3$1 from '@angular/common';\nimport { NgIf, DOCUMENT, CommonModule, NgForOf } from '@angular/common';\nimport { WA_NAVIGATOR } from '@ng-web-apis/common';\nimport * as i1$1 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport * as i2$1 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearance, tuiAppearanceState, tuiAppearanceMode, tuiAppearanceFocus, TuiAppearance } from '@taiga-ui/core/directives/appearance';\nimport { NgControl } from '@angular/forms';\nimport * as i3 from '@taiga-ui/core/directives/items-handlers';\nimport { TuiWithItemsHandlers, TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { WaResizeObserver } from '@ng-web-apis/resize-observer';\nimport { TuiControl } from '@taiga-ui/cdk/classes';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiQueryListChanges, tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { tuiInjectId } from '@taiga-ui/cdk/services';\nimport { tuiFocusedIn } from '@taiga-ui/cdk/utils/focus';\nimport { tuiButtonOptionsProvider, TuiButton } from '@taiga-ui/core/components/button';\nimport * as i4 from '@taiga-ui/core/components/data-list';\nimport { tuiAsDataListHost, TuiWithOptionContent } from '@taiga-ui/core/components/data-list';\nimport * as i1 from '@taiga-ui/core/directives/dropdown';\nimport { TuiDropdownDirective, tuiDropdownOpen, TuiDropdownOpen, TuiDropdownFixed, TuiWithDropdownOpen } from '@taiga-ui/core/directives/dropdown';\nimport * as i2 from '@taiga-ui/core/directives/icons';\nimport { TuiWithIcons, TuiIcons } from '@taiga-ui/core/directives/icons';\nimport { TUI_COMMON_ICONS, TUI_CLEAR_WORD, TUI_AUXILIARY, TUI_SCROLL_REF } from '@taiga-ui/core/tokens';\nimport { PolymorpheusOutlet, injectContext, PolymorpheusComponent } from '@taiga-ui/polymorpheus';\nimport { ReplaySubject, switchMap, startWith, fromEvent, filter } from 'rxjs';\nimport { TuiScrollControls } from '@taiga-ui/core/components/scrollbar';\nimport * as i4$1 from '@taiga-ui/core/directives';\nimport { TUI_ITEMS_HANDLERS as TUI_ITEMS_HANDLERS$1, TuiWithItemsHandlers as TuiWithItemsHandlers$1, TuiWithAppearance } from '@taiga-ui/core/directives';\nconst _c0 = [\"ghost\"];\nconst _c1 = [\"vcr\"];\nconst _c2 = [[[\"input\"]], [[\"select\"]], [[\"textarea\"]], [[\"label\"]], \"*\", [[\"tui-icon\"]]];\nconst _c3 = [\"input\", \"select\", \"textarea\", \"label\", \"*\", \"tui-icon\"];\nconst _c4 = a0 => ({\n  $implicit: a0\n});\nfunction TuiTextfieldComponent_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function TuiTextfieldComponent_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.accessor == null ? null : ctx_r2.accessor.setValue(null));\n    })(\"pointerdown.zoneless.prevent\", function TuiTextfieldComponent_button_6_Template_button_pointerdown_zoneless_prevent_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.input == null ? null : ctx_r2.input.nativeElement == null ? null : ctx_r2.input.nativeElement.focus());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"iconStart\", ctx_r2.icons.close);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.clear(), \" \");\n  }\n}\nfunction TuiTextfieldComponent_span_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r4 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r4, \" \");\n  }\n}\nfunction TuiTextfieldComponent_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵtemplate(1, TuiTextfieldComponent_span_10_ng_container_1_Template, 2, 1, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r2.content)(\"polymorpheusOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r2.control == null ? null : ctx_r2.control.value));\n  }\n}\nfunction TuiTextfieldComponent_input_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 9, 1);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r2.computedFiller());\n  }\n}\nconst _c5 = [\"tuiTextfield\", \"\"];\nconst _c6 = a0 => [a0];\nfunction TuiSelect_option_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 2);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.placeholder, \"\\n\");\n  }\n}\nfunction TuiSelect_ng_template_1_option_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r2, \" \");\n  }\n}\nfunction TuiSelect_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiSelect_ng_template_1_option_0_Template, 2, 2, \"option\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction1(1, _c6, ctx_r0.stringified));\n  }\n}\nfunction TuiTextfieldItemComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(text_r1);\n  }\n}\nconst _c7 = [\"multi\", \"\"];\nconst _c8 = [[[\"label\"]], [[\"input\"]], \"*\", [[\"tui-icon\"]]];\nconst _c9 = [\"label\", \"input\", \"*\", \"tui-icon\"];\nconst _c10 = (a0, a1) => ({\n  item: a0,\n  index: a1\n});\nfunction TuiTextfieldMultiComponent_tui_scroll_controls_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-scroll-controls\", 10);\n  }\n}\nfunction TuiTextfieldMultiComponent_3_ng_template_0_Template(rf, ctx) {}\nfunction TuiTextfieldMultiComponent_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiTextfieldMultiComponent_3_ng_template_0_Template, 0, 0, \"ng-template\", 11);\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const index_r3 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r3.component)(\"polymorpheusOutletContext\", i0.ɵɵpureFunction1(5, _c4, i0.ɵɵpureFunction2(2, _c10, item_r2, index_r3)));\n  }\n}\nfunction TuiTextfieldMultiComponent_input_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r3.computedFiller());\n  }\n}\nfunction TuiTextfieldMultiComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function TuiTextfieldMultiComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.accessor == null ? null : ctx_r3.accessor.setValue([]));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"iconStart\", ctx_r3.icons.close);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.clear(), \" \");\n  }\n}\nfunction TuiTextfieldMultiComponent_span_14_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r6 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r6, \" \");\n  }\n}\nfunction TuiTextfieldMultiComponent_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtemplate(1, TuiTextfieldMultiComponent_span_14_ng_container_1_Template, 2, 1, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r3.content)(\"polymorpheusOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r3.control == null ? null : ctx_r3.control.value));\n  }\n}\nclass TuiSelectLikeStyles {\n  static {\n    this.ɵfac = function TuiSelectLikeStyles_Factory(t) {\n      return new (t || TuiSelectLikeStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiSelectLikeStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-select-like\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiSelectLikeStyles_Template(rf, ctx) {},\n      styles: [\".t-select-like:not(:read-only){cursor:pointer}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSelectLikeStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-select-like'\n      },\n      styles: [\".t-select-like:not(:read-only){cursor:pointer}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiSelectLike {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.isAndroid = inject(TUI_IS_ANDROID);\n    this.nothing = tuiWithStyles(TuiSelectLikeStyles);\n  }\n  clear() {\n    this.el.value = '';\n  }\n  prevent(event) {\n    if (!this.isAndroid) {\n      return;\n    }\n    event.preventDefault();\n    this.el.focus();\n  }\n  static {\n    this.ɵfac = function TuiSelectLike_Factory(t) {\n      return new (t || TuiSelectLike)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiSelectLike,\n      selectors: [[\"\", \"tuiSelectLike\", \"\"]],\n      hostAttrs: [\"inputmode\", \"none\", \"spellcheck\", \"false\", \"autocomplete\", \"off\", 1, \"t-select-like\"],\n      hostBindings: function TuiSelectLike_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"beforeinput\", function TuiSelectLike_beforeinput_HostBindingHandler($event) {\n            return $event.inputType.includes(\"delete\") || $event.preventDefault();\n          })(\"input.capture\", function TuiSelectLike_input_capture_HostBindingHandler($event) {\n            return ($event.inputType == null ? null : $event.inputType.includes(\"delete\")) && ctx.clear();\n          })(\"mousedown\", function TuiSelectLike_mousedown_HostBindingHandler($event) {\n            return ctx.prevent($event);\n          });\n        }\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSelectLike, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiSelectLike]',\n      host: {\n        class: 't-select-like',\n        inputmode: 'none',\n        spellcheck: 'false',\n        autocomplete: 'off',\n        // Click on cleaner icon does not trigger `beforeinput` event --> handle all kind of deletion in input event\n        '(beforeinput)': '$event.inputType.includes(\"delete\") || $event.preventDefault()',\n        '(input.capture)': '$event.inputType?.includes(\"delete\") && clear()',\n        // Hide Android text select handle (bubble marker below transparent caret)\n        '(mousedown)': 'prevent($event)'\n      }\n    }]\n  }], null, null);\n})();\nconst DEFAULT = {\n  appearance: 'textfield',\n  size: 'l',\n  cleaner: true\n};\nconst TUI_TEXTFIELD_OPTIONS = tuiCreateToken({\n  appearance: signal(DEFAULT.appearance),\n  size: signal(DEFAULT.size),\n  cleaner: signal(DEFAULT.cleaner)\n});\nfunction tuiTextfieldOptionsProvider(options) {\n  return {\n    provide: TUI_TEXTFIELD_OPTIONS,\n    deps: [[new Optional(), new SkipSelf(), TUI_TEXTFIELD_OPTIONS]],\n    useFactory: parent => ({\n      appearance: signal(parent?.appearance() ?? DEFAULT.appearance),\n      size: signal(parent?.size() ?? DEFAULT.size),\n      cleaner: signal(parent?.cleaner() ?? DEFAULT.cleaner),\n      ...options\n    })\n  };\n}\nclass TuiTextfieldOptionsDirective {\n  constructor() {\n    this.options = inject(TUI_TEXTFIELD_OPTIONS, {\n      skipSelf: true\n    });\n    // TODO: refactor to signal inputs after Angular update\n    this.appearance = signal(this.options.appearance());\n    this.size = signal(this.options.size());\n    this.cleaner = signal(this.options.cleaner());\n  }\n  set tuiTextfieldAppearance(appearance) {\n    this.appearance.set(appearance);\n  }\n  set tuiTextfieldSize(size) {\n    this.size.set(size);\n  }\n  set tuiTextfieldCleaner(enabled) {\n    this.cleaner.set(enabled);\n  }\n  static {\n    this.ɵfac = function TuiTextfieldOptionsDirective_Factory(t) {\n      return new (t || TuiTextfieldOptionsDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTextfieldOptionsDirective,\n      selectors: [[\"\", \"tuiTextfieldAppearance\", \"\"], [\"\", \"tuiTextfieldSize\", \"\"], [\"\", \"tuiTextfieldCleaner\", \"\"]],\n      inputs: {\n        tuiTextfieldAppearance: \"tuiTextfieldAppearance\",\n        tuiTextfieldSize: \"tuiTextfieldSize\",\n        tuiTextfieldCleaner: \"tuiTextfieldCleaner\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TUI_TEXTFIELD_OPTIONS, TuiTextfieldOptionsDirective)])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTextfieldOptionsDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiTextfieldAppearance],[tuiTextfieldSize],[tuiTextfieldCleaner]',\n      providers: [tuiProvide(TUI_TEXTFIELD_OPTIONS, TuiTextfieldOptionsDirective)]\n    }]\n  }], null, {\n    tuiTextfieldAppearance: [{\n      type: Input\n    }],\n    tuiTextfieldSize: [{\n      type: Input\n    }],\n    tuiTextfieldCleaner: [{\n      type: Input\n    }]\n  });\n})();\nconst TUI_TEXTFIELD_ACCESSOR = tuiCreateToken();\nfunction tuiAsTextfieldAccessor(accessor) {\n  return tuiProvide(TUI_TEXTFIELD_ACCESSOR, accessor);\n}\n\n// TODO: Change selector to tuiDropdown in v5 and move to TuiDropdown\nclass TuiTextfieldDropdownDirective {\n  constructor() {\n    this.directive = inject(TuiDropdownDirective);\n    this.directive.tuiDropdown = inject(TemplateRef);\n  }\n  ngOnDestroy() {\n    this.directive.tuiDropdown = null;\n  }\n  static {\n    this.ɵfac = function TuiTextfieldDropdownDirective_Factory(t) {\n      return new (t || TuiTextfieldDropdownDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTextfieldDropdownDirective,\n      selectors: [[\"ng-template\", \"tuiTextfieldDropdown\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTextfieldDropdownDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-template[tuiTextfieldDropdown]'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n/**\n * @deprecated remove in v5\n */\nclass TuiWithTextfieldDropdown {\n  static {\n    this.ɵfac = function TuiWithTextfieldDropdown_Factory(t) {\n      return new (t || TuiWithTextfieldDropdown)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiWithTextfieldDropdown,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiWithTextfieldDropdown, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n// TODO: Remove base class in v5\nclass TuiTextfieldBaseComponent {\n  constructor() {\n    // TODO: refactor to signal inputs after Angular update\n    this.filler = signal('');\n    this.autoId = tuiInjectId();\n    this.focusedIn = tuiFocusedIn(tuiInjectElement());\n    this.contentReady$ = new ReplaySubject(1);\n    this.inputQuery = signal(undefined);\n    this.auxiliaryQuery = EMPTY_QUERY;\n    this.open = tuiDropdownOpen();\n    this.dropdown = inject(TuiDropdownDirective);\n    this.dropdownOpen = inject(TuiDropdownOpen);\n    this.icons = inject(TUI_COMMON_ICONS);\n    this.clear = toSignal(inject(TUI_CLEAR_WORD));\n    this.computedFiller = computed((value = this.value()) => {\n      const filledValue = value + this.filler().slice(value.length);\n      return filledValue.length > value.length ? filledValue : '';\n    });\n    this.showFiller = computed(() => this.focused() && !!this.computedFiller() && (!!this.value() || !this.input?.nativeElement.placeholder));\n    this.focused = computed(() => this.open() || this.focusedIn());\n    this.options = inject(TUI_TEXTFIELD_OPTIONS);\n    this.el = tuiInjectElement();\n    this.value = tuiValue(this.inputQuery);\n    // TODO: Refactor to signal queries when Angular is updated\n    this.auxiliaries = toSignal(this.contentReady$.pipe(switchMap(() => tuiQueryListChanges(this.auxiliaryQuery)), startWith([])), {\n      requireSync: true\n    });\n  }\n  set fillerSetter(filler) {\n    this.filler.set(filler);\n  }\n  get id() {\n    return this.input?.nativeElement.id || this.autoId;\n  }\n  get size() {\n    return this.options.size();\n  }\n  ngAfterContentInit() {\n    this.contentReady$.next(true);\n    this.inputQuery.set(this.input);\n  }\n  handleOption(option) {\n    this.accessor?.setValue(option);\n    this.open.set(false);\n  }\n  get hasLabel() {\n    return Boolean(this.label?.nativeElement?.childNodes.length);\n  }\n  onResize({\n    contentRect\n  }) {\n    this.el.style.setProperty('--t-side', tuiPx(contentRect.width));\n  }\n  // Click on ::before,::after pseudo-elements ([iconStart] / [iconEnd])\n  onIconClick() {\n    this.input?.nativeElement.focus();\n    if (this.dropdownOpen.tuiDropdownEnabled && this.dropdown._content() && !this.input?.nativeElement.matches(':read-only')) {\n      this.open.update(x => !x);\n    }\n  }\n  onScroll(element) {\n    if (this.input?.nativeElement === element) {\n      this.ghost?.nativeElement.scrollTo({\n        left: this.input.nativeElement.scrollLeft\n      });\n    }\n  }\n  static {\n    this.ɵfac = function TuiTextfieldBaseComponent_Factory(t) {\n      return new (t || TuiTextfieldBaseComponent)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTextfieldBaseComponent,\n      contentQueries: function TuiTextfieldBaseComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiLabel, 5, ElementRef);\n          i0.ɵɵcontentQuery(dirIndex, TUI_TEXTFIELD_ACCESSOR, 5);\n          i0.ɵɵcontentQuery(dirIndex, NgControl, 5);\n          i0.ɵɵcontentQuery(dirIndex, TuiControl, 5);\n          i0.ɵɵcontentQuery(dirIndex, TuiTextfieldBase, 7, ElementRef);\n          i0.ɵɵcontentQuery(dirIndex, TUI_AUXILIARY, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.label = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.accessor = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.control = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cva = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.auxiliaryQuery = _t);\n        }\n      },\n      viewQuery: function TuiTextfieldBaseComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 7, ViewContainerRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ghost = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.vcr = _t.first);\n        }\n      },\n      inputs: {\n        content: \"content\",\n        fillerSetter: [i0.ɵɵInputFlags.None, \"filler\", \"fillerSetter\"]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTextfieldBaseComponent, [{\n    type: Directive\n  }], null, {\n    ghost: [{\n      type: ViewChild,\n      args: ['ghost']\n    }],\n    label: [{\n      type: ContentChild,\n      args: [forwardRef(() => TuiLabel), {\n        read: ElementRef\n      }]\n    }],\n    auxiliaryQuery: [{\n      type: ContentChildren,\n      args: [TUI_AUXILIARY, {\n        descendants: true\n      }]\n    }],\n    vcr: [{\n      type: ViewChild,\n      args: ['vcr', {\n        read: ViewContainerRef,\n        static: true\n      }]\n    }],\n    accessor: [{\n      type: ContentChild,\n      args: [TUI_TEXTFIELD_ACCESSOR, {\n        descendants: true\n      }]\n    }],\n    control: [{\n      type: ContentChild,\n      args: [NgControl]\n    }],\n    cva: [{\n      type: ContentChild,\n      args: [TuiControl]\n    }],\n    input: [{\n      type: ContentChild,\n      args: [forwardRef(() => TuiTextfieldBase), {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    content: [{\n      type: Input\n    }],\n    fillerSetter: [{\n      type: Input,\n      args: ['filler']\n    }]\n  });\n})();\nclass TuiTextfieldComponent extends TuiTextfieldBaseComponent {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiTextfieldComponent_BaseFactory;\n      return function TuiTextfieldComponent_Factory(t) {\n        return (ɵTuiTextfieldComponent_BaseFactory || (ɵTuiTextfieldComponent_BaseFactory = i0.ɵɵgetInheritedFactory(TuiTextfieldComponent)))(t || TuiTextfieldComponent);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTextfieldComponent,\n      selectors: [[\"tui-textfield\", 3, \"multi\", \"\"]],\n      hostVars: 7,\n      hostBindings: function TuiTextfieldComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click.self.prevent\", function TuiTextfieldComponent_click_self_prevent_HostBindingHandler() {\n            return 0;\n          })(\"pointerdown.self.prevent\", function TuiTextfieldComponent_pointerdown_self_prevent_HostBindingHandler() {\n            return ctx.onIconClick();\n          })(\"scroll.capture.zoneless\", function TuiTextfieldComponent_scroll_capture_zoneless_HostBindingHandler($event) {\n            return ctx.onScroll($event.target);\n          })(\"tuiActiveZoneChange\", function TuiTextfieldComponent_tuiActiveZoneChange_HostBindingHandler($event) {\n            return !$event && (ctx.cva == null ? null : ctx.cva.onTouched());\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.options.size());\n          i0.ɵɵclassProp(\"_with-label\", ctx.hasLabel)(\"_with-template\", ctx.content && (ctx.control == null ? null : ctx.control.value) != null)(\"_disabled\", ctx.input == null ? null : ctx.input.nativeElement == null ? null : ctx.input.nativeElement.disabled);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiButtonOptionsProvider({\n        size: 'xs',\n        appearance: 'icon'\n      }), tuiAsDataListHost(TuiTextfieldComponent)]), i0.ɵɵHostDirectivesFeature([i1.TuiDropdownDirective, i1.TuiDropdownFixed, i1.TuiWithDropdownOpen, i2.TuiWithIcons, i3.TuiWithItemsHandlers, i4.TuiWithOptionContent, TuiWithTextfieldDropdown]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c3,\n      decls: 12,\n      vars: 3,\n      consts: [[\"vcr\", \"\"], [\"ghost\", \"\"], [1, \"t-content\", 3, \"pointerdown.prevent\", \"waResizeObserver\"], [\"appearance\", \"icon\", \"size\", \"xs\", \"tabindex\", \"-1\", \"tuiIconButton\", \"\", \"type\", \"button\", \"class\", \"t-clear\", 3, \"iconStart\", \"click\", \"pointerdown.zoneless.prevent\", 4, \"ngIf\"], [\"class\", \"t-template\", 4, \"ngIf\"], [\"aria-hidden\", \"true\", \"disabled\", \"\", \"class\", \"t-filler\", 3, \"value\", 4, \"ngIf\"], [\"appearance\", \"icon\", \"size\", \"xs\", \"tabindex\", \"-1\", \"tuiIconButton\", \"\", \"type\", \"button\", 1, \"t-clear\", 3, \"click\", \"pointerdown.zoneless.prevent\", \"iconStart\"], [1, \"t-template\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [\"aria-hidden\", \"true\", \"disabled\", \"\", 1, \"t-filler\", 3, \"value\"]],\n      template: function TuiTextfieldComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵprojection(0);\n          i0.ɵɵprojection(1, 1);\n          i0.ɵɵprojection(2, 2);\n          i0.ɵɵprojection(3, 3);\n          i0.ɵɵelementStart(4, \"span\", 2);\n          i0.ɵɵlistener(\"pointerdown.prevent\", function TuiTextfieldComponent_Template_span_pointerdown_prevent_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.input == null ? null : ctx.input.nativeElement == null ? null : ctx.input.nativeElement.focus());\n          })(\"waResizeObserver\", function TuiTextfieldComponent_Template_span_waResizeObserver_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView($event[0] && ctx.onResize($event[0]));\n          });\n          i0.ɵɵprojection(5, 4);\n          i0.ɵɵtemplate(6, TuiTextfieldComponent_button_6_Template, 2, 2, \"button\", 3);\n          i0.ɵɵelementContainer(7, null, 0);\n          i0.ɵɵprojection(9, 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, TuiTextfieldComponent_span_10_Template, 2, 4, \"span\", 4)(11, TuiTextfieldComponent_input_11_Template, 2, 1, \"input\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.options.cleaner());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", (ctx.control == null ? null : ctx.control.value) != null);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showFiller());\n        }\n      },\n      dependencies: [NgIf, PolymorpheusOutlet, TuiButton, WaResizeObserver],\n      styles: [\"tui-textfield{scrollbar-width:none;-ms-overflow-style:none;transition-property:color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;--t-height: var(--tui-height-l);--t-padding: var(--tui-padding-l);--t-label: -.7em;--t-end: 0rem;--t-start: 0rem;position:relative;display:flex;flex-wrap:wrap;align-items:center;cursor:pointer;min-block-size:var(--t-height);color:var(--tui-text-tertiary);padding:0 var(--t-padding);border-radius:var(--tui-radius-l);font:var(--tui-font-text-ui-m);box-sizing:border-box;gap:0 .25rem;isolation:isolate}tui-textfield::-webkit-scrollbar,tui-textfield::-webkit-scrollbar-thumb{display:none}tui-textfield[style*=\\\"--t-icon-start:\\\"]{--t-start: 2.25rem}tui-textfield[style*=\\\"--t-icon-end:\\\"]{--t-end: 2.25rem}tui-textfield[tuiIcons]:after{position:relative;block-size:auto;align-self:stretch;border-inline-start:var(--t-padding) solid transparent;border-inline-end:var(--t-padding) solid transparent;margin:0 calc(-1 * var(--t-padding))}tui-textfield::-webkit-resizer{border:.25rem solid transparent;inline-size:.5rem;block-size:.5rem;box-sizing:content-box;color:var(--tui-text-tertiary);background:linear-gradient(-45deg,transparent,transparent .125rem,currentColor .125rem,currentColor .1875rem,transparent .1875rem,transparent .25rem,currentColor .25rem,currentColor .3125rem,transparent .35rem);background-clip:content-box}tui-textfield label,tui-textfield>.t-content,tui-textfield>.t-template{pointer-events:none}tui-textfield input,tui-textfield select,tui-textfield textarea{font:var(--tui-font-text-ui-m);resize:none;outline:none;padding-block-start:1.125rem;padding-block-end:1.125rem}tui-textfield input[inputmode=none],tui-textfield select[inputmode=none],tui-textfield textarea[inputmode=none]{caret-color:transparent}tui-textfield[data-size=s]{--t-height: var(--tui-height-s);--t-padding: var(--tui-padding-s);border-radius:var(--tui-radius-m);gap:0;font:var(--tui-font-text-ui-s)}tui-textfield[data-size=s][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.25rem}tui-textfield[data-size=s][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.25rem}tui-textfield[data-size=s]:before{margin-inline-end:.5rem;margin-inline-start:-.125rem;font-size:1rem}tui-textfield[data-size=s]:after{margin-inline-end:calc(-.125rem - var(--t-padding));margin-inline-start:calc(.625rem - var(--t-padding));font-size:1rem}tui-textfield[data-size=s] input,tui-textfield[data-size=s] select,tui-textfield[data-size=s] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.5rem;padding-block-end:.5rem}tui-textfield[data-size=s]>.t-content{margin-inline-end:-.375rem}tui-textfield[data-size=m]{--t-height: var(--tui-height-m);--t-padding: var(--tui-padding-m);border-radius:var(--tui-radius-m);font:var(--tui-font-text-ui-s)}tui-textfield[data-size=m][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.75rem}tui-textfield[data-size=m][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.75rem}tui-textfield[data-size=m]:before{margin-inline-start:-.125rem;margin-inline-end:.125rem}tui-textfield[data-size=m]:after{margin-inline-start:calc(.25rem - var(--t-padding));margin-inline-end:calc(-.125rem - var(--t-padding))}tui-textfield[data-size=m] input,tui-textfield[data-size=m] select,tui-textfield[data-size=m] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.875rem;padding-block-end:.875rem}tui-textfield[data-size=m]>.t-content{margin-inline-end:-.125rem}tui-textfield[data-size=l]{--t-label: -.7rem}tui-textfield:hover{color:var(--tui-text-secondary)}tui-textfield:hover:has(input:read-only):not([multi]),tui-textfield:hover:has(textarea:read-only),tui-textfield:hover:has(select[data-mode~=readonly]){color:var(--tui-text-tertiary)}tui-textfield:before{z-index:1;margin-inline-end:.5rem}tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):before,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):after,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip]))>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield._disabled:before,tui-textfield._disabled:after,tui-textfield._disabled>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield:has(label:not(:empty))>.t-template,tui-textfield:has(label:not(:empty)) input:not([type=range]),tui-textfield:has(label:not(:empty)) select:defined,tui-textfield:has(label:not(:empty)) textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined._empty{color:transparent}tui-textfield._with-label>.t-template,tui-textfield._with-label input:not([type=range]),tui-textfield._with-label select:defined,tui-textfield._with-label textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true])._with-label>.t-template::placeholder,tui-textfield:not([data-focus=true])._with-label input:not([type=range])::placeholder,tui-textfield:not([data-focus=true])._with-label select:defined::placeholder,tui-textfield:not([data-focus=true])._with-label textarea:defined::placeholder,tui-textfield:not([data-focus=true])._with-label>.t-template._empty,tui-textfield:not([data-focus=true])._with-label input:not([type=range])._empty,tui-textfield:not([data-focus=true])._with-label select:defined._empty,tui-textfield:not([data-focus=true])._with-label textarea:defined._empty{color:transparent}tui-textfield>.t-template,tui-textfield input:defined,tui-textfield select:defined,tui-textfield textarea:defined{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;-webkit-appearance:none;appearance:none;box-sizing:border-box;border-radius:inherit;border-width:0;overscroll-behavior:none;padding-inline-start:calc(var(--t-start, 0rem) + var(--t-padding));padding-inline-end:calc(var(--t-end, 0rem) + var(--t-side) + var(--t-padding))}tui-textfield>.t-template{display:flex;align-items:center;color:var(--tui-text-primary)}tui-textfield._with-template input:not([type=range]),tui-textfield._with-template select,tui-textfield._with-template textarea{color:transparent!important}tui-textfield input:not([type=range]),tui-textfield select:defined,tui-textfield textarea:defined{pointer-events:auto;background:transparent}tui-textfield input:not([type=range]):read-only~.t-filler,tui-textfield select:defined:read-only~.t-filler,tui-textfield textarea:defined:read-only~.t-filler{display:none}tui-textfield input:not([type=range]):disabled~label,tui-textfield select:defined:disabled~label,tui-textfield textarea:defined:disabled~label,tui-textfield input:not([type=range]):disabled~.t-content,tui-textfield select:defined:disabled~.t-content,tui-textfield textarea:defined:disabled~.t-content{opacity:var(--tui-disabled-opacity)}tui-textfield input:not([type=range]):disabled~label>tui-icon,tui-textfield select:defined:disabled~label>tui-icon,tui-textfield textarea:defined:disabled~label>tui-icon,tui-textfield input:not([type=range]):disabled~.t-content>tui-icon,tui-textfield select:defined:disabled~.t-content>tui-icon,tui-textfield textarea:defined:disabled~.t-content>tui-icon{display:none}tui-textfield input:not([type=range]):-webkit-autofill~label,tui-textfield select:defined:-webkit-autofill~label,tui-textfield textarea:defined:-webkit-autofill~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown)~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown)~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown)~label{font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label{color:var(--tui-text-negative)}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear{display:flex}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]~label,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]~label,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}@supports (-webkit-touch-callout: none){tui-textfield input:not([type=range])._ios-fix,tui-textfield select:defined._ios-fix,tui-textfield textarea:defined._ios-fix{position:fixed;left:1000rem}}tui-textfield [tuiLabel][tuiLabel][tuiLabel]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;position:relative;display:block;flex:1;align-self:flex-start;font-size:inherit;line-height:var(--t-height)}tui-textfield label:defined,tui-textfield input:defined::placeholder,tui-textfield textarea:defined::placeholder,tui-textfield select:defined._empty{color:var(--tui-text-secondary)}tui-textfield select:not([data-mode~=readonly]){cursor:pointer}tui-textfield select option[value=\\\"\\\"]:disabled{color:transparent}tui-textfield select optgroup,tui-textfield select option{background-color:var(--tui-background-elevation-3)}tui-textfield select optgroup,tui-textfield select option:not(:disabled){color:var(--tui-text-primary)}tui-textfield button,tui-textfield a{pointer-events:auto}tui-textfield>.t-content{z-index:1;display:flex;block-size:var(--t-height);align-items:center;gap:inherit;margin-inline-start:auto;isolation:isolate;border-radius:inherit}tui-textfield>.t-content>tui-icon{pointer-events:auto}tui-textfield textarea~.t-content{min-inline-size:.5rem}tui-textfield .t-clear{z-index:1;display:none;pointer-events:auto}tui-textfield>.t-filler:defined{pointer-events:none;background:none;color:var(--tui-text-tertiary);opacity:1}tui-textfield [tuiFluidTypography]{font-weight:700}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTextfieldComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-textfield:not([multi])',\n      imports: [NgIf, PolymorpheusOutlet, TuiButton, WaResizeObserver],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiButtonOptionsProvider({\n        size: 'xs',\n        appearance: 'icon'\n      }), tuiAsDataListHost(TuiTextfieldComponent)],\n      hostDirectives: [TuiDropdownDirective, TuiDropdownFixed, TuiWithDropdownOpen, TuiWithIcons, TuiWithItemsHandlers, TuiWithOptionContent, TuiWithTextfieldDropdown],\n      host: {\n        '[attr.data-size]': 'options.size()',\n        '[class._with-label]': 'hasLabel',\n        '[class._with-template]': 'content && control?.value != null',\n        '[class._disabled]': 'input?.nativeElement?.disabled',\n        '(click.self.prevent)': '0',\n        '(pointerdown.self.prevent)': 'onIconClick()',\n        '(scroll.capture.zoneless)': 'onScroll($event.target)',\n        '(tuiActiveZoneChange)': '!$event && cva?.onTouched()'\n      },\n      template: \"<ng-content select=\\\"input\\\" />\\n<ng-content select=\\\"select\\\" />\\n<ng-content select=\\\"textarea\\\" />\\n<ng-content select=\\\"label\\\" />\\n<span\\n    class=\\\"t-content\\\"\\n    (pointerdown.prevent)=\\\"input?.nativeElement?.focus()\\\"\\n    (waResizeObserver)=\\\"$event[0] && onResize($event[0])\\\"\\n>\\n    <ng-content />\\n    <button\\n        *ngIf=\\\"options.cleaner()\\\"\\n        appearance=\\\"icon\\\"\\n        size=\\\"xs\\\"\\n        tabindex=\\\"-1\\\"\\n        tuiIconButton\\n        type=\\\"button\\\"\\n        class=\\\"t-clear\\\"\\n        [iconStart]=\\\"icons.close\\\"\\n        (click)=\\\"accessor?.setValue(null)\\\"\\n        (pointerdown.zoneless.prevent)=\\\"input?.nativeElement?.focus()\\\"\\n    >\\n        {{ clear() }}\\n    </button>\\n    <ng-container #vcr />\\n    <ng-content select=\\\"tui-icon\\\" />\\n</span>\\n<span\\n    *ngIf=\\\"control?.value != null\\\"\\n    class=\\\"t-template\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: control?.value}\\\">\\n        {{ text }}\\n    </ng-container>\\n</span>\\n<input\\n    *ngIf=\\\"showFiller()\\\"\\n    #ghost\\n    aria-hidden=\\\"true\\\"\\n    disabled\\n    class=\\\"t-filler\\\"\\n    [value]=\\\"computedFiller()\\\"\\n/>\\n\",\n      styles: [\"tui-textfield{scrollbar-width:none;-ms-overflow-style:none;transition-property:color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;--t-height: var(--tui-height-l);--t-padding: var(--tui-padding-l);--t-label: -.7em;--t-end: 0rem;--t-start: 0rem;position:relative;display:flex;flex-wrap:wrap;align-items:center;cursor:pointer;min-block-size:var(--t-height);color:var(--tui-text-tertiary);padding:0 var(--t-padding);border-radius:var(--tui-radius-l);font:var(--tui-font-text-ui-m);box-sizing:border-box;gap:0 .25rem;isolation:isolate}tui-textfield::-webkit-scrollbar,tui-textfield::-webkit-scrollbar-thumb{display:none}tui-textfield[style*=\\\"--t-icon-start:\\\"]{--t-start: 2.25rem}tui-textfield[style*=\\\"--t-icon-end:\\\"]{--t-end: 2.25rem}tui-textfield[tuiIcons]:after{position:relative;block-size:auto;align-self:stretch;border-inline-start:var(--t-padding) solid transparent;border-inline-end:var(--t-padding) solid transparent;margin:0 calc(-1 * var(--t-padding))}tui-textfield::-webkit-resizer{border:.25rem solid transparent;inline-size:.5rem;block-size:.5rem;box-sizing:content-box;color:var(--tui-text-tertiary);background:linear-gradient(-45deg,transparent,transparent .125rem,currentColor .125rem,currentColor .1875rem,transparent .1875rem,transparent .25rem,currentColor .25rem,currentColor .3125rem,transparent .35rem);background-clip:content-box}tui-textfield label,tui-textfield>.t-content,tui-textfield>.t-template{pointer-events:none}tui-textfield input,tui-textfield select,tui-textfield textarea{font:var(--tui-font-text-ui-m);resize:none;outline:none;padding-block-start:1.125rem;padding-block-end:1.125rem}tui-textfield input[inputmode=none],tui-textfield select[inputmode=none],tui-textfield textarea[inputmode=none]{caret-color:transparent}tui-textfield[data-size=s]{--t-height: var(--tui-height-s);--t-padding: var(--tui-padding-s);border-radius:var(--tui-radius-m);gap:0;font:var(--tui-font-text-ui-s)}tui-textfield[data-size=s][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.25rem}tui-textfield[data-size=s][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.25rem}tui-textfield[data-size=s]:before{margin-inline-end:.5rem;margin-inline-start:-.125rem;font-size:1rem}tui-textfield[data-size=s]:after{margin-inline-end:calc(-.125rem - var(--t-padding));margin-inline-start:calc(.625rem - var(--t-padding));font-size:1rem}tui-textfield[data-size=s] input,tui-textfield[data-size=s] select,tui-textfield[data-size=s] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.5rem;padding-block-end:.5rem}tui-textfield[data-size=s]>.t-content{margin-inline-end:-.375rem}tui-textfield[data-size=m]{--t-height: var(--tui-height-m);--t-padding: var(--tui-padding-m);border-radius:var(--tui-radius-m);font:var(--tui-font-text-ui-s)}tui-textfield[data-size=m][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.75rem}tui-textfield[data-size=m][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.75rem}tui-textfield[data-size=m]:before{margin-inline-start:-.125rem;margin-inline-end:.125rem}tui-textfield[data-size=m]:after{margin-inline-start:calc(.25rem - var(--t-padding));margin-inline-end:calc(-.125rem - var(--t-padding))}tui-textfield[data-size=m] input,tui-textfield[data-size=m] select,tui-textfield[data-size=m] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.875rem;padding-block-end:.875rem}tui-textfield[data-size=m]>.t-content{margin-inline-end:-.125rem}tui-textfield[data-size=l]{--t-label: -.7rem}tui-textfield:hover{color:var(--tui-text-secondary)}tui-textfield:hover:has(input:read-only):not([multi]),tui-textfield:hover:has(textarea:read-only),tui-textfield:hover:has(select[data-mode~=readonly]){color:var(--tui-text-tertiary)}tui-textfield:before{z-index:1;margin-inline-end:.5rem}tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):before,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):after,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip]))>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield._disabled:before,tui-textfield._disabled:after,tui-textfield._disabled>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield:has(label:not(:empty))>.t-template,tui-textfield:has(label:not(:empty)) input:not([type=range]),tui-textfield:has(label:not(:empty)) select:defined,tui-textfield:has(label:not(:empty)) textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined._empty{color:transparent}tui-textfield._with-label>.t-template,tui-textfield._with-label input:not([type=range]),tui-textfield._with-label select:defined,tui-textfield._with-label textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true])._with-label>.t-template::placeholder,tui-textfield:not([data-focus=true])._with-label input:not([type=range])::placeholder,tui-textfield:not([data-focus=true])._with-label select:defined::placeholder,tui-textfield:not([data-focus=true])._with-label textarea:defined::placeholder,tui-textfield:not([data-focus=true])._with-label>.t-template._empty,tui-textfield:not([data-focus=true])._with-label input:not([type=range])._empty,tui-textfield:not([data-focus=true])._with-label select:defined._empty,tui-textfield:not([data-focus=true])._with-label textarea:defined._empty{color:transparent}tui-textfield>.t-template,tui-textfield input:defined,tui-textfield select:defined,tui-textfield textarea:defined{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;-webkit-appearance:none;appearance:none;box-sizing:border-box;border-radius:inherit;border-width:0;overscroll-behavior:none;padding-inline-start:calc(var(--t-start, 0rem) + var(--t-padding));padding-inline-end:calc(var(--t-end, 0rem) + var(--t-side) + var(--t-padding))}tui-textfield>.t-template{display:flex;align-items:center;color:var(--tui-text-primary)}tui-textfield._with-template input:not([type=range]),tui-textfield._with-template select,tui-textfield._with-template textarea{color:transparent!important}tui-textfield input:not([type=range]),tui-textfield select:defined,tui-textfield textarea:defined{pointer-events:auto;background:transparent}tui-textfield input:not([type=range]):read-only~.t-filler,tui-textfield select:defined:read-only~.t-filler,tui-textfield textarea:defined:read-only~.t-filler{display:none}tui-textfield input:not([type=range]):disabled~label,tui-textfield select:defined:disabled~label,tui-textfield textarea:defined:disabled~label,tui-textfield input:not([type=range]):disabled~.t-content,tui-textfield select:defined:disabled~.t-content,tui-textfield textarea:defined:disabled~.t-content{opacity:var(--tui-disabled-opacity)}tui-textfield input:not([type=range]):disabled~label>tui-icon,tui-textfield select:defined:disabled~label>tui-icon,tui-textfield textarea:defined:disabled~label>tui-icon,tui-textfield input:not([type=range]):disabled~.t-content>tui-icon,tui-textfield select:defined:disabled~.t-content>tui-icon,tui-textfield textarea:defined:disabled~.t-content>tui-icon{display:none}tui-textfield input:not([type=range]):-webkit-autofill~label,tui-textfield select:defined:-webkit-autofill~label,tui-textfield textarea:defined:-webkit-autofill~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown)~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown)~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown)~label{font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label{color:var(--tui-text-negative)}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear{display:flex}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]~label,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]~label,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}@supports (-webkit-touch-callout: none){tui-textfield input:not([type=range])._ios-fix,tui-textfield select:defined._ios-fix,tui-textfield textarea:defined._ios-fix{position:fixed;left:1000rem}}tui-textfield [tuiLabel][tuiLabel][tuiLabel]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;position:relative;display:block;flex:1;align-self:flex-start;font-size:inherit;line-height:var(--t-height)}tui-textfield label:defined,tui-textfield input:defined::placeholder,tui-textfield textarea:defined::placeholder,tui-textfield select:defined._empty{color:var(--tui-text-secondary)}tui-textfield select:not([data-mode~=readonly]){cursor:pointer}tui-textfield select option[value=\\\"\\\"]:disabled{color:transparent}tui-textfield select optgroup,tui-textfield select option{background-color:var(--tui-background-elevation-3)}tui-textfield select optgroup,tui-textfield select option:not(:disabled){color:var(--tui-text-primary)}tui-textfield button,tui-textfield a{pointer-events:auto}tui-textfield>.t-content{z-index:1;display:flex;block-size:var(--t-height);align-items:center;gap:inherit;margin-inline-start:auto;isolation:isolate;border-radius:inherit}tui-textfield>.t-content>tui-icon{pointer-events:auto}tui-textfield textarea~.t-content{min-inline-size:.5rem}tui-textfield .t-clear{z-index:1;display:none;pointer-events:auto}tui-textfield>.t-filler:defined{pointer-events:none;background:none;color:var(--tui-text-tertiary);opacity:1}tui-textfield [tuiFluidTypography]{font-weight:700}\\n\"]\n    }]\n  }], null, null);\n})();\n\n// TODO: Drop in v5 after updated Angular and hostDirectives inherit\nclass TuiTextfieldBase {\n  constructor() {\n    // TODO: refactor to signal inputs after Angular update\n    this.focused = signal(null);\n    this.control = inject(NgControl, {\n      optional: true\n    });\n    this.a = tuiAppearance(inject(TUI_TEXTFIELD_OPTIONS).appearance, {});\n    this.s = tuiAppearanceState(null, {});\n    this.m = tuiAppearanceMode(this.mode, {});\n    this.f = tuiAppearanceFocus(computed(() => this.focused() ?? this.textfield.focused()), {});\n    this.el = tuiInjectElement();\n    this.handlers = inject(TUI_ITEMS_HANDLERS);\n    this.textfield = inject(TuiTextfieldComponent);\n    this.readOnly = false;\n    this.invalid = null;\n    this.value = tuiValue(this.el);\n  }\n  set focusedSetter(focused) {\n    this.focused.set(focused);\n  }\n  set stateSetter(state) {\n    this.s.set(state);\n  }\n  get mode() {\n    if (this.readOnly) {\n      return 'readonly';\n    }\n    if (this.invalid === false) {\n      return 'valid';\n    }\n    if (this.invalid) {\n      return 'invalid';\n    }\n    return null;\n  }\n  // TODO: refactor to signal inputs after Angular update\n  ngOnChanges() {\n    this.m.set(this.mode);\n  }\n  setValue(value) {\n    this.el.focus();\n    this.el.select();\n    if (value == null) {\n      this.el.ownerDocument.execCommand('delete');\n    } else {\n      this.el.ownerDocument.execCommand('insertText', false, this.handlers.stringify()(value));\n    }\n  }\n  static {\n    this.ɵfac = function TuiTextfieldBase_Factory(t) {\n      return new (t || TuiTextfieldBase)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTextfieldBase,\n      hostVars: 4,\n      hostBindings: function TuiTextfieldBase_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function TuiTextfieldBase_input_HostBindingHandler() {\n            return 0;\n          })(\"focusin\", function TuiTextfieldBase_focusin_HostBindingHandler() {\n            return 0;\n          })(\"focusout\", function TuiTextfieldBase_focusout_HostBindingHandler() {\n            return 0;\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.textfield.id)(\"readOnly\", ctx.readOnly);\n          i0.ɵɵclassProp(\"_empty\", ctx.value() === \"\");\n        }\n      },\n      inputs: {\n        readOnly: \"readOnly\",\n        invalid: \"invalid\",\n        focusedSetter: [i0.ɵɵInputFlags.None, \"focused\", \"focusedSetter\"],\n        stateSetter: [i0.ɵɵInputFlags.None, \"state\", \"stateSetter\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsTextfieldAccessor(TuiTextfieldBase)]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTextfieldBase, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      providers: [tuiAsTextfieldAccessor(TuiTextfieldBase)],\n      host: {\n        '[id]': 'textfield.id',\n        '[readOnly]': 'readOnly',\n        '[class._empty]': 'value() === \"\"',\n        '(input)': '0',\n        '(focusin)': '0',\n        '(focusout)': '0'\n      }\n    }]\n  }], null, {\n    readOnly: [{\n      type: Input\n    }],\n    invalid: [{\n      type: Input\n    }],\n    focusedSetter: [{\n      type: Input,\n      args: ['focused']\n    }],\n    stateSetter: [{\n      type: Input,\n      args: ['state']\n    }]\n  });\n})();\nclass TuiTextfieldDirective extends TuiTextfieldBase {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiTextfieldDirective_BaseFactory;\n      return function TuiTextfieldDirective_Factory(t) {\n        return (ɵTuiTextfieldDirective_BaseFactory || (ɵTuiTextfieldDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiTextfieldDirective)))(t || TuiTextfieldDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTextfieldDirective,\n      selectors: [[\"input\", \"tuiTextfield\", \"\", 3, \"tuiInputCard\", \"\", 3, \"tuiInputExpire\", \"\", 3, \"tuiInputCVC\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsTextfieldAccessor(TuiTextfieldDirective), tuiProvide(TuiTextfieldBase, TuiTextfieldDirective)]), i0.ɵɵHostDirectivesFeature([i1$1.TuiNativeValidator, i2$1.TuiAppearance]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTextfieldDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      // TODO: Remove :not in v.5\n      selector: 'input[tuiTextfield]:not([tuiInputCard]):not([tuiInputExpire]):not([tuiInputCVC])',\n      providers: [tuiAsTextfieldAccessor(TuiTextfieldDirective), tuiProvide(TuiTextfieldBase, TuiTextfieldDirective)],\n      hostDirectives: [TuiNativeValidator, TuiAppearance]\n    }]\n  }], null, null);\n})();\nclass TuiWithTextfield {\n  static {\n    this.ɵfac = function TuiWithTextfield_Factory(t) {\n      return new (t || TuiWithTextfield)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiWithTextfield,\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([{\n        directive: TuiTextfieldDirective,\n        inputs: [\"invalid\", \"invalid\", \"focused\", \"focused\", \"readOnly\", \"readOnly\", \"state\", \"state\"]\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiWithTextfield, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      hostDirectives: [{\n        directive: TuiTextfieldDirective,\n        inputs: ['invalid', 'focused', 'readOnly', 'state']\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * @deprecated use {@link TuiSelect} from @taiga-ui/kit instead, drop in v5\n */\nclass TuiSelect extends TuiTextfieldBase {\n  constructor() {\n    super(...arguments);\n    this.nav = inject(WA_NAVIGATOR);\n    this.doc = inject(DOCUMENT);\n    this.placeholder = '';\n  }\n  setValue(value) {\n    this.control?.control?.setValue(value);\n    this.el.dispatchEvent(new Event('input', {\n      bubbles: true\n    }));\n  }\n  focus() {\n    this.el.classList.add('_ios-fix');\n    this.el.focus();\n    this.el.classList.remove('_ios-fix');\n  }\n  get ariaLabel() {\n    return this.doc.querySelector(`label[for=\"${this.el.id}\"]`) ? null : this.el.getAttribute('aria-label') || this.placeholder;\n  }\n  get stringified() {\n    return this.handlers.stringify()(this.control?.value ?? '');\n  }\n  onCopy() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.nav.clipboard.writeText(_this.stringified);\n    })();\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiSelect_BaseFactory;\n      return function TuiSelect_Factory(t) {\n        return (ɵTuiSelect_BaseFactory || (ɵTuiSelect_BaseFactory = i0.ɵɵgetInheritedFactory(TuiSelect)))(t || TuiSelect);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiSelect,\n      selectors: [[\"select\", \"tuiTextfield\", \"\"]],\n      hostVars: 4,\n      hostBindings: function TuiSelect_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function TuiSelect_input_HostBindingHandler() {\n            return 0;\n          })(\"focusin\", function TuiSelect_focusin_HostBindingHandler() {\n            return 0;\n          })(\"focusout\", function TuiSelect_focusout_HostBindingHandler() {\n            return 0;\n          })(\"keydown.space.prevent\", function TuiSelect_keydown_space_prevent_HostBindingHandler() {\n            return 0;\n          })(\"keydown.enter.prevent\", function TuiSelect_keydown_enter_prevent_HostBindingHandler() {\n            return 0;\n          })(\"keydown.backspace\", function TuiSelect_keydown_backspace_HostBindingHandler() {\n            return ctx.setValue(\"\");\n          })(\"mousedown.prevent\", function TuiSelect_mousedown_prevent_HostBindingHandler() {\n            return ctx.focus();\n          })(\"keydown.control.c\", function TuiSelect_keydown_control_c_HostBindingHandler() {\n            return ctx.onCopy();\n          })(\"keydown.meta.c\", function TuiSelect_keydown_meta_c_HostBindingHandler() {\n            return ctx.onCopy();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.textfield.id);\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel);\n          i0.ɵɵclassProp(\"_empty\", ctx.stringified === \"\");\n        }\n      },\n      inputs: {\n        placeholder: \"placeholder\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsTextfieldAccessor(TuiSelect)]), i0.ɵɵHostDirectivesFeature([i1$1.TuiNativeValidator, i2$1.TuiAppearance]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c5,\n      decls: 3,\n      vars: 2,\n      consts: [[\"selected\", \"\"], [\"disabled\", \"\", \"selected\", \"\", \"value\", \"\", 4, \"ngIf\", \"ngIfElse\"], [\"disabled\", \"\", \"selected\", \"\", \"value\", \"\"], [\"selected\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"selected\", \"\", 3, \"value\"]],\n      template: function TuiSelect_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiSelect_option_0_Template, 2, 1, \"option\", 1)(1, TuiSelect_ng_template_1_Template, 1, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const selected_r3 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.placeholder && !ctx.stringified)(\"ngIfElse\", selected_r3);\n        }\n      },\n      dependencies: [CommonModule, i3$1.NgForOf, i3$1.NgIf],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSelect, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'select[tuiTextfield]',\n      imports: [CommonModule],\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [tuiAsTextfieldAccessor(TuiSelect)],\n      hostDirectives: [TuiNativeValidator, TuiAppearance],\n      host: {\n        '[id]': 'textfield.id',\n        '[class._empty]': 'stringified === \"\"',\n        '[attr.aria-label]': 'ariaLabel',\n        '(input)': '0',\n        '(focusin)': '0',\n        '(focusout)': '0',\n        '(keydown.space.prevent)': '0',\n        '(keydown.enter.prevent)': '0',\n        '(keydown.backspace)': 'setValue(\"\")',\n        '(mousedown.prevent)': 'focus()',\n        '(keydown.control.c)': 'onCopy()',\n        '(keydown.meta.c)': 'onCopy()'\n      },\n      template: \"<option\\n    *ngIf=\\\"placeholder && !stringified; else selected\\\"\\n    disabled\\n    selected\\n    value=\\\"\\\"\\n>\\n    {{ placeholder }}\\n</option>\\n<ng-template #selected>\\n    <option\\n        *ngFor=\\\"let item of [stringified]\\\"\\n        selected\\n        [value]=\\\"item\\\"\\n    >\\n        {{ item }}\\n    </option>\\n</ng-template>\\n\"\n    }]\n  }], null, {\n    placeholder: [{\n      type: Input\n    }]\n  });\n})();\n\n/*\n * Internal wrapper for polymorpheus-context\n */\nclass TuiTextfieldItemComponent {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.context = injectContext();\n    this.textfield = inject(TuiTextfieldMultiComponent);\n  }\n  get content() {\n    return this.textfield.item ?? String(this.context.$implicit.item);\n  }\n  static {\n    this.ɵfac = function TuiTextfieldItemComponent_Factory(t) {\n      return new (t || TuiTextfieldItemComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTextfieldItemComponent,\n      selectors: [[\"tui-textfield-item\"]],\n      hostVars: 2,\n      hostBindings: function TuiTextfieldItemComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"pointerdown.self.prevent\", function TuiTextfieldItemComponent_pointerdown_self_prevent_HostBindingHandler() {\n            return 0;\n          })(\"keydown.arrowLeft.prevent\", function TuiTextfieldItemComponent_keydown_arrowLeft_prevent_HostBindingHandler() {\n            return ctx.el.previousElementSibling == null ? null : ctx.el.previousElementSibling.firstChild == null ? null : ctx.el.previousElementSibling.firstChild.focus();\n          })(\"keydown.arrowRight.prevent\", function TuiTextfieldItemComponent_keydown_arrowRight_prevent_HostBindingHandler() {\n            return ctx.el.nextElementSibling == null ? null : ctx.el.nextElementSibling.firstChild == null ? null : ctx.el.nextElementSibling.firstChild.focus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_string\", !ctx.textfield.item);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 2,\n      consts: [[4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"]],\n      template: function TuiTextfieldItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiTextfieldItemComponent_ng_container_0_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.content)(\"polymorpheusOutletContext\", ctx.context);\n        }\n      },\n      dependencies: [PolymorpheusOutlet],\n      styles: [\"[_nghost-%COMP%]{max-inline-size:100%;flex-shrink:0;white-space:pre-wrap;text-overflow:ellipsis}._string[_nghost-%COMP%]{overflow:hidden}._string[_nghost-%COMP%]:after{content:\\\", \\\"}[_nghost-%COMP%]:last-of-type{max-inline-size:80%}tui-textfield:not([data-focus=\\\"true\\\"])[_nghost-%COMP%]:last-of-type:after, tui-textfield:not([data-focus=\\\"true\\\"])   [_nghost-%COMP%]:last-of-type:after{display:none}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTextfieldItemComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-textfield-item',\n      imports: [PolymorpheusOutlet],\n      template: '<ng-container *polymorpheusOutlet=\"content as text; context: context\">{{ text }}</ng-container>',\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        '[class._string]': '!textfield.item',\n        '(pointerdown.self.prevent)': '0',\n        '(keydown.arrowLeft.prevent)': 'el.previousElementSibling?.firstChild?.focus()',\n        '(keydown.arrowRight.prevent)': 'el.nextElementSibling?.firstChild?.focus()'\n      },\n      styles: [\":host{max-inline-size:100%;flex-shrink:0;white-space:pre-wrap;text-overflow:ellipsis}:host._string{overflow:hidden}:host._string:after{content:\\\", \\\"}:host:last-of-type{max-inline-size:80%}:host-context(tui-textfield:not([data-focus=\\\"true\\\"])):last-of-type:after{display:none}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiTextfieldMultiComponent extends TuiTextfieldBaseComponent {\n  constructor() {\n    super(...arguments);\n    this.height = signal(null);\n    this.handlers = inject(TUI_ITEMS_HANDLERS$1);\n    this.component = new PolymorpheusComponent(TuiTextfieldItemComponent);\n    this.sub = fromEvent(this.el, 'scroll').pipe(filter(() => this.rows === 1), tuiZonefree(), takeUntilDestroyed()).subscribe(() => {\n      this.el.style.setProperty('--t-scroll', tuiPx(-1 * this.el.scrollLeft));\n    });\n    this.rows = 100;\n  }\n  handleOption(option) {\n    this.accessor?.setValue(tuiArrayToggle(this.control?.value ?? [], option, this.handlers.identityMatcher()));\n  }\n  onItems({\n    target\n  }) {\n    const height = this.rows > 1 && this.control?.value?.length ? target.querySelector('tui-textfield-item')?.clientHeight ?? 0 : null;\n    if (height !== 0) {\n      this.height.set(height);\n    }\n  }\n  onLeft(event) {\n    if (this.value() || !tuiIsElement(event.currentTarget)) {\n      return;\n    }\n    event.preventDefault();\n    event.currentTarget.previousElementSibling?.firstElementChild?.focus();\n  }\n  onClick(target) {\n    if (target !== this.el && this.el.matches('[tuiChevron]') && !target.matches('input:read-only')) {\n      this.open.update(open => !open);\n    }\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiTextfieldMultiComponent_BaseFactory;\n      return function TuiTextfieldMultiComponent_Factory(t) {\n        return (ɵTuiTextfieldMultiComponent_BaseFactory || (ɵTuiTextfieldMultiComponent_BaseFactory = i0.ɵɵgetInheritedFactory(TuiTextfieldMultiComponent)))(t || TuiTextfieldMultiComponent);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTextfieldMultiComponent,\n      selectors: [[\"tui-textfield\", \"multi\", \"\"]],\n      contentQueries: function TuiTextfieldMultiComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiItem, 5, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.item = _t.first);\n        }\n      },\n      hostAttrs: [1, \"tui-interactive\"],\n      hostVars: 14,\n      hostBindings: function TuiTextfieldMultiComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TuiTextfieldMultiComponent_click_HostBindingHandler($event) {\n            return ctx.onClick($event.target);\n          })(\"tuiActiveZoneChange\", function TuiTextfieldMultiComponent_tuiActiveZoneChange_HostBindingHandler($event) {\n            return !$event && (ctx.el.scrollTo({\n              left: 0\n            }) || (ctx.cva == null ? null : ctx.cva.onTouched()));\n          })(\"click.self.prevent\", function TuiTextfieldMultiComponent_click_self_prevent_HostBindingHandler() {\n            return 0;\n          })(\"pointerdown.self.prevent\", function TuiTextfieldMultiComponent_pointerdown_self_prevent_HostBindingHandler() {\n            return ctx.onIconClick();\n          })(\"scroll.capture.zoneless\", function TuiTextfieldMultiComponent_scroll_capture_zoneless_HostBindingHandler($event) {\n            return ctx.onScroll($event.target);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-state\", (ctx.control == null ? null : ctx.control.disabled) ? \"disabled\" : null)(\"data-size\", ctx.options.size());\n          i0.ɵɵstyleProp(\"--t-item-height\", ctx.height(), \"px\")(\"--t-rows\", ctx.rows);\n          i0.ɵɵclassProp(\"_empty\", !(ctx.control == null ? null : ctx.control.value == null ? null : ctx.control.value.length))(\"_with-label\", ctx.hasLabel)(\"_with-template\", ctx.content && (ctx.control == null ? null : ctx.control.value) != null)(\"_disabled\", ctx.input == null ? null : ctx.input.nativeElement == null ? null : ctx.input.nativeElement.disabled);\n        }\n      },\n      inputs: {\n        rows: \"rows\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiButtonOptionsProvider({\n        size: 'xs',\n        appearance: 'icon'\n      }), tuiAsDataListHost(TuiTextfieldMultiComponent), tuiProvide(TuiTextfieldComponent, TuiTextfieldMultiComponent), tuiProvide(TUI_SCROLL_REF, ElementRef)]), i0.ɵɵHostDirectivesFeature([i1.TuiDropdownFixed, i1.TuiDropdownDirective, i1.TuiWithDropdownOpen, TuiWithTextfieldDropdown, i2.TuiWithIcons, i4$1.TuiWithItemsHandlers, i4.TuiWithOptionContent, i4$1.TuiWithAppearance]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c7,\n      ngContentSelectors: _c9,\n      decls: 15,\n      vars: 9,\n      consts: [[\"vcr\", \"\"], [\"class\", \"t-scrollbar\", 4, \"ngIf\"], [1, \"t-items\", 3, \"click\", \"pointerdown.self.zoneless.prevent\", \"waResizeObserver\"], [4, \"ngFor\", \"ngForOf\"], [1, \"t-input\", 3, \"keydown.arrowLeft\"], [1, \"t-ghost\", 3, \"textContent\"], [\"aria-hidden\", \"true\", \"disabled\", \"\", \"class\", \"t-filler\", 3, \"value\", 4, \"ngIf\"], [1, \"t-content\", 3, \"click.stop\", \"pointerdown.zoneless.prevent\", \"waResizeObserver\"], [\"appearance\", \"icon\", \"size\", \"xs\", \"tabindex\", \"-1\", \"tuiIconButton\", \"\", \"type\", \"button\", \"class\", \"t-clear\", 3, \"iconStart\", \"click\", 4, \"ngIf\"], [\"class\", \"t-template\", 4, \"ngIf\"], [1, \"t-scrollbar\"], [3, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [\"aria-hidden\", \"true\", \"disabled\", \"\", 1, \"t-filler\", 3, \"value\"], [\"appearance\", \"icon\", \"size\", \"xs\", \"tabindex\", \"-1\", \"tuiIconButton\", \"\", \"type\", \"button\", 1, \"t-clear\", 3, \"click\", \"iconStart\"], [1, \"t-template\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"]],\n      template: function TuiTextfieldMultiComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c8);\n          i0.ɵɵtemplate(0, TuiTextfieldMultiComponent_tui_scroll_controls_0_Template, 1, 0, \"tui-scroll-controls\", 1);\n          i0.ɵɵelementStart(1, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function TuiTextfieldMultiComponent_Template_div_click_1_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.input == null ? null : ctx.input.nativeElement == null ? null : ctx.input.nativeElement.focus());\n          })(\"pointerdown.self.zoneless.prevent\", function TuiTextfieldMultiComponent_Template_div_pointerdown_self_zoneless_prevent_1_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(0);\n          })(\"waResizeObserver\", function TuiTextfieldMultiComponent_Template_div_waResizeObserver_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView($event[0] && ctx.onItems($event[0]));\n          });\n          i0.ɵɵprojection(2);\n          i0.ɵɵtemplate(3, TuiTextfieldMultiComponent_3_Template, 1, 7, null, 3);\n          i0.ɵɵelementStart(4, \"span\", 4);\n          i0.ɵɵlistener(\"keydown.arrowLeft\", function TuiTextfieldMultiComponent_Template_span_keydown_arrowLeft_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onLeft($event));\n          });\n          i0.ɵɵprojection(5, 1);\n          i0.ɵɵelement(6, \"span\", 5);\n          i0.ɵɵtemplate(7, TuiTextfieldMultiComponent_input_7_Template, 1, 1, \"input\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"span\", 7);\n          i0.ɵɵlistener(\"click.stop\", function TuiTextfieldMultiComponent_Template_span_click_stop_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.input == null ? null : ctx.input.nativeElement == null ? null : ctx.input.nativeElement.focus());\n          })(\"pointerdown.zoneless.prevent\", function TuiTextfieldMultiComponent_Template_span_pointerdown_zoneless_prevent_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(0);\n          })(\"waResizeObserver\", function TuiTextfieldMultiComponent_Template_span_waResizeObserver_8_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView($event[0] && ctx.onResize($event[0]));\n          });\n          i0.ɵɵprojection(9, 2);\n          i0.ɵɵtemplate(10, TuiTextfieldMultiComponent_button_10_Template, 2, 2, \"button\", 8);\n          i0.ɵɵelementContainer(11, null, 0);\n          i0.ɵɵprojection(13, 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(14, TuiTextfieldMultiComponent_span_14_Template, 2, 4, \"span\", 9);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.rows > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"t-items_horizontal\", ctx.rows === 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.control == null ? null : ctx.control.value);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"textContent\", ctx.value() && ctx.value().length < ((ctx.input == null ? null : ctx.input.nativeElement == null ? null : ctx.input.nativeElement.placeholder == null ? null : ctx.input.nativeElement.placeholder.length) || 0) ? ctx.input == null ? null : ctx.input.nativeElement == null ? null : ctx.input.nativeElement.placeholder : ctx.value());\n          i0.ɵɵattribute(\"data-placeholder\", ctx.input == null ? null : ctx.input.nativeElement == null ? null : ctx.input.nativeElement.placeholder);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showFiller());\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.options.cleaner());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", (ctx.control == null ? null : ctx.control.value) != null);\n        }\n      },\n      dependencies: [NgForOf, NgIf, PolymorpheusOutlet, TuiButton, TuiScrollControls, WaResizeObserver],\n      styles: [\"tui-textfield{scrollbar-width:none;-ms-overflow-style:none;transition-property:color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;--t-height: var(--tui-height-l);--t-padding: var(--tui-padding-l);--t-label: -.7em;--t-end: 0rem;--t-start: 0rem;position:relative;display:flex;flex-wrap:wrap;align-items:center;cursor:pointer;min-block-size:var(--t-height);color:var(--tui-text-tertiary);padding:0 var(--t-padding);border-radius:var(--tui-radius-l);font:var(--tui-font-text-ui-m);box-sizing:border-box;gap:0 .25rem;isolation:isolate}tui-textfield::-webkit-scrollbar,tui-textfield::-webkit-scrollbar-thumb{display:none}tui-textfield[style*=\\\"--t-icon-start:\\\"]{--t-start: 2.25rem}tui-textfield[style*=\\\"--t-icon-end:\\\"]{--t-end: 2.25rem}tui-textfield[tuiIcons]:after{position:relative;block-size:auto;align-self:stretch;border-inline-start:var(--t-padding) solid transparent;border-inline-end:var(--t-padding) solid transparent;margin:0 calc(-1 * var(--t-padding))}tui-textfield::-webkit-resizer{border:.25rem solid transparent;inline-size:.5rem;block-size:.5rem;box-sizing:content-box;color:var(--tui-text-tertiary);background:linear-gradient(-45deg,transparent,transparent .125rem,currentColor .125rem,currentColor .1875rem,transparent .1875rem,transparent .25rem,currentColor .25rem,currentColor .3125rem,transparent .35rem);background-clip:content-box}tui-textfield label,tui-textfield>.t-content,tui-textfield>.t-template{pointer-events:none}tui-textfield input,tui-textfield select,tui-textfield textarea{font:var(--tui-font-text-ui-m);resize:none;outline:none;padding-block-start:1.125rem;padding-block-end:1.125rem}tui-textfield input[inputmode=none],tui-textfield select[inputmode=none],tui-textfield textarea[inputmode=none]{caret-color:transparent}tui-textfield[data-size=s]{--t-height: var(--tui-height-s);--t-padding: var(--tui-padding-s);border-radius:var(--tui-radius-m);gap:0;font:var(--tui-font-text-ui-s)}tui-textfield[data-size=s][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.25rem}tui-textfield[data-size=s][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.25rem}tui-textfield[data-size=s]:before{margin-inline-end:.5rem;margin-inline-start:-.125rem;font-size:1rem}tui-textfield[data-size=s]:after{margin-inline-end:calc(-.125rem - var(--t-padding));margin-inline-start:calc(.625rem - var(--t-padding));font-size:1rem}tui-textfield[data-size=s] input,tui-textfield[data-size=s] select,tui-textfield[data-size=s] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.5rem;padding-block-end:.5rem}tui-textfield[data-size=s]>.t-content{margin-inline-end:-.375rem}tui-textfield[data-size=m]{--t-height: var(--tui-height-m);--t-padding: var(--tui-padding-m);border-radius:var(--tui-radius-m);font:var(--tui-font-text-ui-s)}tui-textfield[data-size=m][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.75rem}tui-textfield[data-size=m][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.75rem}tui-textfield[data-size=m]:before{margin-inline-start:-.125rem;margin-inline-end:.125rem}tui-textfield[data-size=m]:after{margin-inline-start:calc(.25rem - var(--t-padding));margin-inline-end:calc(-.125rem - var(--t-padding))}tui-textfield[data-size=m] input,tui-textfield[data-size=m] select,tui-textfield[data-size=m] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.875rem;padding-block-end:.875rem}tui-textfield[data-size=m]>.t-content{margin-inline-end:-.125rem}tui-textfield[data-size=l]{--t-label: -.7rem}tui-textfield:hover{color:var(--tui-text-secondary)}tui-textfield:hover:has(input:read-only):not([multi]),tui-textfield:hover:has(textarea:read-only),tui-textfield:hover:has(select[data-mode~=readonly]){color:var(--tui-text-tertiary)}tui-textfield:before{z-index:1;margin-inline-end:.5rem}tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):before,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):after,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip]))>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield._disabled:before,tui-textfield._disabled:after,tui-textfield._disabled>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield:has(label:not(:empty))>.t-template,tui-textfield:has(label:not(:empty)) input:not([type=range]),tui-textfield:has(label:not(:empty)) select:defined,tui-textfield:has(label:not(:empty)) textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined._empty{color:transparent}tui-textfield._with-label>.t-template,tui-textfield._with-label input:not([type=range]),tui-textfield._with-label select:defined,tui-textfield._with-label textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true])._with-label>.t-template::placeholder,tui-textfield:not([data-focus=true])._with-label input:not([type=range])::placeholder,tui-textfield:not([data-focus=true])._with-label select:defined::placeholder,tui-textfield:not([data-focus=true])._with-label textarea:defined::placeholder,tui-textfield:not([data-focus=true])._with-label>.t-template._empty,tui-textfield:not([data-focus=true])._with-label input:not([type=range])._empty,tui-textfield:not([data-focus=true])._with-label select:defined._empty,tui-textfield:not([data-focus=true])._with-label textarea:defined._empty{color:transparent}tui-textfield>.t-template,tui-textfield input:defined,tui-textfield select:defined,tui-textfield textarea:defined{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;-webkit-appearance:none;appearance:none;box-sizing:border-box;border-radius:inherit;border-width:0;overscroll-behavior:none;padding-inline-start:calc(var(--t-start, 0rem) + var(--t-padding));padding-inline-end:calc(var(--t-end, 0rem) + var(--t-side) + var(--t-padding))}tui-textfield>.t-template{display:flex;align-items:center;color:var(--tui-text-primary)}tui-textfield._with-template input:not([type=range]),tui-textfield._with-template select,tui-textfield._with-template textarea{color:transparent!important}tui-textfield input:not([type=range]),tui-textfield select:defined,tui-textfield textarea:defined{pointer-events:auto;background:transparent}tui-textfield input:not([type=range]):read-only~.t-filler,tui-textfield select:defined:read-only~.t-filler,tui-textfield textarea:defined:read-only~.t-filler{display:none}tui-textfield input:not([type=range]):disabled~label,tui-textfield select:defined:disabled~label,tui-textfield textarea:defined:disabled~label,tui-textfield input:not([type=range]):disabled~.t-content,tui-textfield select:defined:disabled~.t-content,tui-textfield textarea:defined:disabled~.t-content{opacity:var(--tui-disabled-opacity)}tui-textfield input:not([type=range]):disabled~label>tui-icon,tui-textfield select:defined:disabled~label>tui-icon,tui-textfield textarea:defined:disabled~label>tui-icon,tui-textfield input:not([type=range]):disabled~.t-content>tui-icon,tui-textfield select:defined:disabled~.t-content>tui-icon,tui-textfield textarea:defined:disabled~.t-content>tui-icon{display:none}tui-textfield input:not([type=range]):-webkit-autofill~label,tui-textfield select:defined:-webkit-autofill~label,tui-textfield textarea:defined:-webkit-autofill~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown)~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown)~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown)~label{font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label{color:var(--tui-text-negative)}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear{display:flex}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]~label,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]~label,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}@supports (-webkit-touch-callout: none){tui-textfield input:not([type=range])._ios-fix,tui-textfield select:defined._ios-fix,tui-textfield textarea:defined._ios-fix{position:fixed;left:1000rem}}tui-textfield [tuiLabel][tuiLabel][tuiLabel]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;position:relative;display:block;flex:1;align-self:flex-start;font-size:inherit;line-height:var(--t-height)}tui-textfield label:defined,tui-textfield input:defined::placeholder,tui-textfield textarea:defined::placeholder,tui-textfield select:defined._empty{color:var(--tui-text-secondary)}tui-textfield select:not([data-mode~=readonly]){cursor:pointer}tui-textfield select option[value=\\\"\\\"]:disabled{color:transparent}tui-textfield select optgroup,tui-textfield select option{background-color:var(--tui-background-elevation-3)}tui-textfield select optgroup,tui-textfield select option:not(:disabled){color:var(--tui-text-primary)}tui-textfield button,tui-textfield a{pointer-events:auto}tui-textfield>.t-content{z-index:1;display:flex;block-size:var(--t-height);align-items:center;gap:inherit;margin-inline-start:auto;isolation:isolate;border-radius:inherit}tui-textfield>.t-content>tui-icon{pointer-events:auto}tui-textfield textarea~.t-content{min-inline-size:.5rem}tui-textfield .t-clear{z-index:1;display:none;pointer-events:auto}tui-textfield>.t-filler:defined{pointer-events:none;background:none;color:var(--tui-text-tertiary);opacity:1}tui-textfield [tuiFluidTypography]{font-weight:700}tui-textfield[multi][multi]{flex-wrap:nowrap;overflow:scroll;align-items:stretch;cursor:text;gap:0;max-block-size:calc(var(--t-vertical) * 2 + var(--t-item-height) * var(--t-rows));overscroll-behavior:none;scroll-behavior:var(--tui-scroll-behavior)}tui-textfield[multi][multi]:before,tui-textfield[multi][multi]:after{position:sticky;top:0;left:0;block-size:var(--t-height)}@supports (inset-inline-start: 0){tui-textfield[multi][multi]:before,tui-textfield[multi][multi]:after{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]:has([tuiSelectLike]){cursor:pointer}tui-textfield[multi][multi][tuiChevron]:after{top:.375rem;block-size:calc(var(--t-height) - .75rem)}tui-textfield[multi][multi]>.t-scrollbar{transform:translate(var(--t-padding));margin-inline-start:calc(-1 * var(--t-start));margin-inline-end:calc(1px - 100% + var(--t-start))}tui-textfield[multi][multi]>.t-scrollbar .t-bar_horizontal{display:none}tui-textfield[multi][multi]>.t-items{position:sticky;left:var(--t-start);display:flex;min-inline-size:0;min-block-size:-webkit-fit-content;min-block-size:-moz-fit-content;min-block-size:fit-content;flex:1;align-items:center;flex-wrap:wrap;padding:var(--t-vertical) 0}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-items{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]>.t-items:after{content:\\\"\\\";min-inline-size:1px;min-block-size:1px}tui-textfield[multi][multi]>.t-items_horizontal{clip-path:inset(0 0 0 calc(var(--t-start) / 2 - var(--t-padding) - .25rem));flex-wrap:nowrap}tui-textfield[multi][multi]>.t-items_horizontal:dir(rtl){clip-path:inset(0 calc(var(--t-start) / 2 - var(--t-padding) - .25rem) 0 0)}tui-textfield[multi][multi]>.t-items_horizontal>.t-input{padding-inline-end:calc(var(--t-side) + var(--t-end) + var(--t-padding))}tui-textfield[multi][multi]>.t-items:not(tui-textfield[multi][multi]>.t-items_horizontal){--t-scroll: 0}tui-textfield[multi][multi]>.t-items>label[tuiLabel]{position:absolute;top:0;inline-size:100%}tui-textfield[multi][multi]>.t-items>.t-input{position:relative;display:flex;align-items:center;flex:1;max-inline-size:100%;transform:translate(var(--t-scroll))}tui-textfield[multi][multi]>.t-items>.t-input input{position:absolute;left:0;inline-size:100%;padding:0}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-items>.t-input input{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]>.t-items>.t-input .t-ghost{visibility:hidden;white-space:pre;text-overflow:clip;padding-inline-end:.125rem;min-block-size:var(--t-item-height, 1em)}tui-textfield[multi][multi]>.t-items>.t-input .t-ghost:empty:before{content:attr(data-placeholder)}tui-textfield[multi][multi]>.t-items>.t-input .t-filler{position:absolute;left:0;color:var(--tui-text-tertiary)}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-items>.t-input .t-filler{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]>.t-content{position:sticky;top:0;left:calc(100% - var(--t-side) - var(--t-end) + var(--t-padding) - var(--t-offset));margin:0;gap:.25rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-content{left:unset;inset-inline-start:calc(100% - var(--t-side) - var(--t-end) + var(--t-padding) - var(--t-offset))}}tui-textfield[multi][multi]>.t-content .t-clear{display:flex}tui-textfield[multi][multi][data-mode~=invalid]:not([data-mode~=readonly])>.t-items>[tuiLabel]{color:var(--tui-text-negative)}tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly])>.t-items>[tuiLabel]{color:var(--tui-text-primary)}tui-textfield[multi][multi][data-mode~=readonly]>.t-content .t-clear,tui-textfield[multi][multi]._disabled>.t-content .t-clear,tui-textfield[multi][multi]._empty>.t-content .t-clear{display:none}tui-textfield[multi][multi]>.t-items input:not(:focus)::placeholder,tui-textfield[multi][multi][data-mode~=readonly]>.t-items input::placeholder{opacity:0}tui-textfield[multi][multi]>.t-items input:not(:focus)~.t-ghost:before,tui-textfield[multi][multi][data-mode~=readonly]>.t-items input~.t-ghost:before{display:none}tui-textfield[multi][multi]:not([data-mode~=readonly])._empty>.t-items input::placeholder,tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly]):not(:focus-within)>.t-items input::placeholder{opacity:1}tui-textfield[multi][multi]:not([data-mode~=readonly])._empty>.t-items .t-ghost:before,tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly]):not(:focus-within)>.t-items .t-ghost:before{display:inline}tui-textfield[multi][multi]:not(._empty)>.t-items [tuiLabel],tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly])>.t-items [tuiLabel]{font-size:.83em;transform:translateY(var(--t-label))}tui-textfield[multi][multi][data-size=l]{--t-vertical: .625rem;--t-offset: calc(1rem - var(--t-end) / 4.5)}tui-textfield[multi][multi][data-size=l]:before{margin-inline-end:.75rem}tui-textfield[multi][multi][data-size=l]:after{left:calc(100% - var(--t-end) - .375rem + .25 * (1em - 1rem));margin-inline-end:calc(.5 * (1.5rem - 1em) - var(--t-padding));margin-inline-start:-.75rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=l]:after{left:unset;inset-inline-start:calc(100% - var(--t-end) - .375rem + .25 * (1em - 1rem))}}tui-textfield[multi][multi][data-size=l]._with-label{--t-vertical: 1.125rem}tui-textfield[multi][multi][data-size=l]._with-label>.t-items{padding:1.75rem 0 .5rem}tui-textfield[multi][multi][data-size=m]{--t-vertical: .5rem;--t-offset: calc(.75rem + var(--t-end) / 14)}tui-textfield[multi][multi][data-size=m]:before{left:-.125rem;margin-inline-end:.375rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=m]:before{left:unset;inset-inline-start:-.125rem}}tui-textfield[multi][multi][data-size=m]:after{left:calc(100% - var(--t-end) - .25rem);margin-inline-start:-.125rem;border-width:.625rem;margin-inline-end:calc(.5 * (1.5rem - 1em) - var(--t-padding))}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=m]:after{left:unset;inset-inline-start:calc(100% - var(--t-end) - .25rem)}}tui-textfield[multi][multi][data-size=m]._with-label{--t-vertical: .875rem}tui-textfield[multi][multi][data-size=m]._with-label>.t-items{padding:1.375rem 0 .375rem}tui-textfield[multi][multi][data-size=m]>.t-content{transform:translate(.125rem)}tui-textfield[multi][multi][data-size=s]{--t-vertical: .125rem;--t-offset: calc(.625rem + var(--t-end) / 10)}tui-textfield[multi][multi][data-size=s]:before{left:-.125rem;margin-inline-end:.375rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=s]:before{left:unset;inset-inline-start:-.125rem}}tui-textfield[multi][multi][data-size=s]:after{left:calc(100% - var(--t-end) - .125rem);border-width:.5rem;margin-inline-end:calc(-1 * var(--t-padding))}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=s]:after{left:unset;inset-inline-start:calc(100% - var(--t-end) - .125rem)}}tui-textfield[multi][multi][data-size=s]>.t-content{gap:0;transform:translate(calc(.375rem - var(--t-end) / 10))}tui-textfield[multi][multi]:focus-visible:not([data-focus=false]) input::placeholder,tui-textfield[multi][multi]:focus-visible:not([data-focus=false]) input._empty{color:var(--tui-text-tertiary)}tui-textfield[multi][multi][data-focus=true] input::placeholder,tui-textfield[multi][multi][data-focus=true] input._empty{color:var(--tui-text-tertiary)}tui-textfield[multi][multi][tuiWrapper]:not(._focused):has(:focus-visible) input::placeholder,tui-textfield[multi][multi][tuiWrapper]._focused input::placeholder,tui-textfield[multi][multi][tuiWrapper]:not(._focused):has(:focus-visible) input._empty,tui-textfield[multi][multi][tuiWrapper]._focused input._empty{color:var(--tui-text-tertiary)}tui-textfield[multi][multi] tui-textfield-item{transform:translate(var(--t-scroll))}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTextfieldMultiComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-textfield[multi]',\n      imports: [NgForOf, NgIf, PolymorpheusOutlet, TuiButton, TuiScrollControls, WaResizeObserver],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiButtonOptionsProvider({\n        size: 'xs',\n        appearance: 'icon'\n      }), tuiAsDataListHost(TuiTextfieldMultiComponent), tuiProvide(TuiTextfieldComponent, TuiTextfieldMultiComponent), tuiProvide(TUI_SCROLL_REF, ElementRef)],\n      hostDirectives: [TuiDropdownFixed, TuiDropdownDirective, TuiWithDropdownOpen, TuiWithTextfieldDropdown, TuiWithIcons, TuiWithItemsHandlers$1, TuiWithOptionContent, TuiWithAppearance],\n      host: {\n        class: 'tui-interactive',\n        '[attr.data-state]': 'control?.disabled ? \"disabled\" : null',\n        '[class._empty]': '!control?.value?.length',\n        '[style.--t-item-height.px]': 'height()',\n        '[style.--t-rows]': 'rows',\n        '(click)': 'onClick($event.target)',\n        '(tuiActiveZoneChange)': '!$event && (el.scrollTo({left: 0}) || cva?.onTouched())',\n        // TODO: Remove in v5\n        '[attr.data-size]': 'options.size()',\n        '[class._with-label]': 'hasLabel',\n        '[class._with-template]': 'content && control?.value != null',\n        '[class._disabled]': 'input?.nativeElement?.disabled',\n        '(click.self.prevent)': '0',\n        '(pointerdown.self.prevent)': 'onIconClick()',\n        '(scroll.capture.zoneless)': 'onScroll($event.target)'\n      },\n      template: \"<tui-scroll-controls\\n    *ngIf=\\\"rows > 1\\\"\\n    class=\\\"t-scrollbar\\\"\\n/>\\n\\n<div\\n    class=\\\"t-items\\\"\\n    [class.t-items_horizontal]=\\\"rows === 1\\\"\\n    (click)=\\\"input?.nativeElement?.focus()\\\"\\n    (pointerdown.self.zoneless.prevent)=\\\"(0)\\\"\\n    (waResizeObserver)=\\\"$event[0] && onItems($event[0])\\\"\\n>\\n    <ng-content select=\\\"label\\\" />\\n    <ng-template\\n        *ngFor=\\\"let item of control?.value; let index = index\\\"\\n        [polymorpheusOutlet]=\\\"component\\\"\\n        [polymorpheusOutletContext]=\\\"{$implicit: {item, index}}\\\"\\n    />\\n    <span\\n        class=\\\"t-input\\\"\\n        (keydown.arrowLeft)=\\\"onLeft($event)\\\"\\n    >\\n        <ng-content select=\\\"input\\\" />\\n        <span\\n            class=\\\"t-ghost\\\"\\n            [attr.data-placeholder]=\\\"input?.nativeElement?.placeholder\\\"\\n            [textContent]=\\\"\\n                value() && value().length < (input?.nativeElement?.placeholder?.length || 0)\\n                    ? input?.nativeElement?.placeholder\\n                    : value()\\n            \\\"\\n        ></span>\\n        <input\\n            *ngIf=\\\"showFiller()\\\"\\n            aria-hidden=\\\"true\\\"\\n            disabled\\n            class=\\\"t-filler\\\"\\n            [value]=\\\"computedFiller()\\\"\\n        />\\n    </span>\\n</div>\\n\\n<span\\n    class=\\\"t-content\\\"\\n    (click.stop)=\\\"input?.nativeElement?.focus()\\\"\\n    (pointerdown.zoneless.prevent)=\\\"(0)\\\"\\n    (waResizeObserver)=\\\"$event[0] && onResize($event[0])\\\"\\n>\\n    <ng-content />\\n    <button\\n        *ngIf=\\\"options.cleaner()\\\"\\n        appearance=\\\"icon\\\"\\n        size=\\\"xs\\\"\\n        tabindex=\\\"-1\\\"\\n        tuiIconButton\\n        type=\\\"button\\\"\\n        class=\\\"t-clear\\\"\\n        [iconStart]=\\\"icons.close\\\"\\n        (click)=\\\"accessor?.setValue([])\\\"\\n    >\\n        {{ clear() }}\\n    </button>\\n    <ng-container #vcr />\\n    <ng-content select=\\\"tui-icon\\\" />\\n</span>\\n\\n<span\\n    *ngIf=\\\"control?.value != null\\\"\\n    class=\\\"t-template\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: control?.value}\\\">\\n        {{ text }}\\n    </ng-container>\\n</span>\\n\",\n      styles: [\"tui-textfield{scrollbar-width:none;-ms-overflow-style:none;transition-property:color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;--t-height: var(--tui-height-l);--t-padding: var(--tui-padding-l);--t-label: -.7em;--t-end: 0rem;--t-start: 0rem;position:relative;display:flex;flex-wrap:wrap;align-items:center;cursor:pointer;min-block-size:var(--t-height);color:var(--tui-text-tertiary);padding:0 var(--t-padding);border-radius:var(--tui-radius-l);font:var(--tui-font-text-ui-m);box-sizing:border-box;gap:0 .25rem;isolation:isolate}tui-textfield::-webkit-scrollbar,tui-textfield::-webkit-scrollbar-thumb{display:none}tui-textfield[style*=\\\"--t-icon-start:\\\"]{--t-start: 2.25rem}tui-textfield[style*=\\\"--t-icon-end:\\\"]{--t-end: 2.25rem}tui-textfield[tuiIcons]:after{position:relative;block-size:auto;align-self:stretch;border-inline-start:var(--t-padding) solid transparent;border-inline-end:var(--t-padding) solid transparent;margin:0 calc(-1 * var(--t-padding))}tui-textfield::-webkit-resizer{border:.25rem solid transparent;inline-size:.5rem;block-size:.5rem;box-sizing:content-box;color:var(--tui-text-tertiary);background:linear-gradient(-45deg,transparent,transparent .125rem,currentColor .125rem,currentColor .1875rem,transparent .1875rem,transparent .25rem,currentColor .25rem,currentColor .3125rem,transparent .35rem);background-clip:content-box}tui-textfield label,tui-textfield>.t-content,tui-textfield>.t-template{pointer-events:none}tui-textfield input,tui-textfield select,tui-textfield textarea{font:var(--tui-font-text-ui-m);resize:none;outline:none;padding-block-start:1.125rem;padding-block-end:1.125rem}tui-textfield input[inputmode=none],tui-textfield select[inputmode=none],tui-textfield textarea[inputmode=none]{caret-color:transparent}tui-textfield[data-size=s]{--t-height: var(--tui-height-s);--t-padding: var(--tui-padding-s);border-radius:var(--tui-radius-m);gap:0;font:var(--tui-font-text-ui-s)}tui-textfield[data-size=s][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.25rem}tui-textfield[data-size=s][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.25rem}tui-textfield[data-size=s]:before{margin-inline-end:.5rem;margin-inline-start:-.125rem;font-size:1rem}tui-textfield[data-size=s]:after{margin-inline-end:calc(-.125rem - var(--t-padding));margin-inline-start:calc(.625rem - var(--t-padding));font-size:1rem}tui-textfield[data-size=s] input,tui-textfield[data-size=s] select,tui-textfield[data-size=s] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.5rem;padding-block-end:.5rem}tui-textfield[data-size=s]>.t-content{margin-inline-end:-.375rem}tui-textfield[data-size=m]{--t-height: var(--tui-height-m);--t-padding: var(--tui-padding-m);border-radius:var(--tui-radius-m);font:var(--tui-font-text-ui-s)}tui-textfield[data-size=m][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.75rem}tui-textfield[data-size=m][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.75rem}tui-textfield[data-size=m]:before{margin-inline-start:-.125rem;margin-inline-end:.125rem}tui-textfield[data-size=m]:after{margin-inline-start:calc(.25rem - var(--t-padding));margin-inline-end:calc(-.125rem - var(--t-padding))}tui-textfield[data-size=m] input,tui-textfield[data-size=m] select,tui-textfield[data-size=m] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.875rem;padding-block-end:.875rem}tui-textfield[data-size=m]>.t-content{margin-inline-end:-.125rem}tui-textfield[data-size=l]{--t-label: -.7rem}tui-textfield:hover{color:var(--tui-text-secondary)}tui-textfield:hover:has(input:read-only):not([multi]),tui-textfield:hover:has(textarea:read-only),tui-textfield:hover:has(select[data-mode~=readonly]){color:var(--tui-text-tertiary)}tui-textfield:before{z-index:1;margin-inline-end:.5rem}tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):before,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):after,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip]))>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield._disabled:before,tui-textfield._disabled:after,tui-textfield._disabled>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield:has(label:not(:empty))>.t-template,tui-textfield:has(label:not(:empty)) input:not([type=range]),tui-textfield:has(label:not(:empty)) select:defined,tui-textfield:has(label:not(:empty)) textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined._empty{color:transparent}tui-textfield._with-label>.t-template,tui-textfield._with-label input:not([type=range]),tui-textfield._with-label select:defined,tui-textfield._with-label textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true])._with-label>.t-template::placeholder,tui-textfield:not([data-focus=true])._with-label input:not([type=range])::placeholder,tui-textfield:not([data-focus=true])._with-label select:defined::placeholder,tui-textfield:not([data-focus=true])._with-label textarea:defined::placeholder,tui-textfield:not([data-focus=true])._with-label>.t-template._empty,tui-textfield:not([data-focus=true])._with-label input:not([type=range])._empty,tui-textfield:not([data-focus=true])._with-label select:defined._empty,tui-textfield:not([data-focus=true])._with-label textarea:defined._empty{color:transparent}tui-textfield>.t-template,tui-textfield input:defined,tui-textfield select:defined,tui-textfield textarea:defined{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;-webkit-appearance:none;appearance:none;box-sizing:border-box;border-radius:inherit;border-width:0;overscroll-behavior:none;padding-inline-start:calc(var(--t-start, 0rem) + var(--t-padding));padding-inline-end:calc(var(--t-end, 0rem) + var(--t-side) + var(--t-padding))}tui-textfield>.t-template{display:flex;align-items:center;color:var(--tui-text-primary)}tui-textfield._with-template input:not([type=range]),tui-textfield._with-template select,tui-textfield._with-template textarea{color:transparent!important}tui-textfield input:not([type=range]),tui-textfield select:defined,tui-textfield textarea:defined{pointer-events:auto;background:transparent}tui-textfield input:not([type=range]):read-only~.t-filler,tui-textfield select:defined:read-only~.t-filler,tui-textfield textarea:defined:read-only~.t-filler{display:none}tui-textfield input:not([type=range]):disabled~label,tui-textfield select:defined:disabled~label,tui-textfield textarea:defined:disabled~label,tui-textfield input:not([type=range]):disabled~.t-content,tui-textfield select:defined:disabled~.t-content,tui-textfield textarea:defined:disabled~.t-content{opacity:var(--tui-disabled-opacity)}tui-textfield input:not([type=range]):disabled~label>tui-icon,tui-textfield select:defined:disabled~label>tui-icon,tui-textfield textarea:defined:disabled~label>tui-icon,tui-textfield input:not([type=range]):disabled~.t-content>tui-icon,tui-textfield select:defined:disabled~.t-content>tui-icon,tui-textfield textarea:defined:disabled~.t-content>tui-icon{display:none}tui-textfield input:not([type=range]):-webkit-autofill~label,tui-textfield select:defined:-webkit-autofill~label,tui-textfield textarea:defined:-webkit-autofill~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown)~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown)~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown)~label{font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label{color:var(--tui-text-negative)}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear{display:flex}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]~label,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]~label,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}@supports (-webkit-touch-callout: none){tui-textfield input:not([type=range])._ios-fix,tui-textfield select:defined._ios-fix,tui-textfield textarea:defined._ios-fix{position:fixed;left:1000rem}}tui-textfield [tuiLabel][tuiLabel][tuiLabel]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;position:relative;display:block;flex:1;align-self:flex-start;font-size:inherit;line-height:var(--t-height)}tui-textfield label:defined,tui-textfield input:defined::placeholder,tui-textfield textarea:defined::placeholder,tui-textfield select:defined._empty{color:var(--tui-text-secondary)}tui-textfield select:not([data-mode~=readonly]){cursor:pointer}tui-textfield select option[value=\\\"\\\"]:disabled{color:transparent}tui-textfield select optgroup,tui-textfield select option{background-color:var(--tui-background-elevation-3)}tui-textfield select optgroup,tui-textfield select option:not(:disabled){color:var(--tui-text-primary)}tui-textfield button,tui-textfield a{pointer-events:auto}tui-textfield>.t-content{z-index:1;display:flex;block-size:var(--t-height);align-items:center;gap:inherit;margin-inline-start:auto;isolation:isolate;border-radius:inherit}tui-textfield>.t-content>tui-icon{pointer-events:auto}tui-textfield textarea~.t-content{min-inline-size:.5rem}tui-textfield .t-clear{z-index:1;display:none;pointer-events:auto}tui-textfield>.t-filler:defined{pointer-events:none;background:none;color:var(--tui-text-tertiary);opacity:1}tui-textfield [tuiFluidTypography]{font-weight:700}tui-textfield[multi][multi]{flex-wrap:nowrap;overflow:scroll;align-items:stretch;cursor:text;gap:0;max-block-size:calc(var(--t-vertical) * 2 + var(--t-item-height) * var(--t-rows));overscroll-behavior:none;scroll-behavior:var(--tui-scroll-behavior)}tui-textfield[multi][multi]:before,tui-textfield[multi][multi]:after{position:sticky;top:0;left:0;block-size:var(--t-height)}@supports (inset-inline-start: 0){tui-textfield[multi][multi]:before,tui-textfield[multi][multi]:after{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]:has([tuiSelectLike]){cursor:pointer}tui-textfield[multi][multi][tuiChevron]:after{top:.375rem;block-size:calc(var(--t-height) - .75rem)}tui-textfield[multi][multi]>.t-scrollbar{transform:translate(var(--t-padding));margin-inline-start:calc(-1 * var(--t-start));margin-inline-end:calc(1px - 100% + var(--t-start))}tui-textfield[multi][multi]>.t-scrollbar .t-bar_horizontal{display:none}tui-textfield[multi][multi]>.t-items{position:sticky;left:var(--t-start);display:flex;min-inline-size:0;min-block-size:-webkit-fit-content;min-block-size:-moz-fit-content;min-block-size:fit-content;flex:1;align-items:center;flex-wrap:wrap;padding:var(--t-vertical) 0}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-items{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]>.t-items:after{content:\\\"\\\";min-inline-size:1px;min-block-size:1px}tui-textfield[multi][multi]>.t-items_horizontal{clip-path:inset(0 0 0 calc(var(--t-start) / 2 - var(--t-padding) - .25rem));flex-wrap:nowrap}tui-textfield[multi][multi]>.t-items_horizontal:dir(rtl){clip-path:inset(0 calc(var(--t-start) / 2 - var(--t-padding) - .25rem) 0 0)}tui-textfield[multi][multi]>.t-items_horizontal>.t-input{padding-inline-end:calc(var(--t-side) + var(--t-end) + var(--t-padding))}tui-textfield[multi][multi]>.t-items:not(tui-textfield[multi][multi]>.t-items_horizontal){--t-scroll: 0}tui-textfield[multi][multi]>.t-items>label[tuiLabel]{position:absolute;top:0;inline-size:100%}tui-textfield[multi][multi]>.t-items>.t-input{position:relative;display:flex;align-items:center;flex:1;max-inline-size:100%;transform:translate(var(--t-scroll))}tui-textfield[multi][multi]>.t-items>.t-input input{position:absolute;left:0;inline-size:100%;padding:0}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-items>.t-input input{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]>.t-items>.t-input .t-ghost{visibility:hidden;white-space:pre;text-overflow:clip;padding-inline-end:.125rem;min-block-size:var(--t-item-height, 1em)}tui-textfield[multi][multi]>.t-items>.t-input .t-ghost:empty:before{content:attr(data-placeholder)}tui-textfield[multi][multi]>.t-items>.t-input .t-filler{position:absolute;left:0;color:var(--tui-text-tertiary)}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-items>.t-input .t-filler{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]>.t-content{position:sticky;top:0;left:calc(100% - var(--t-side) - var(--t-end) + var(--t-padding) - var(--t-offset));margin:0;gap:.25rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-content{left:unset;inset-inline-start:calc(100% - var(--t-side) - var(--t-end) + var(--t-padding) - var(--t-offset))}}tui-textfield[multi][multi]>.t-content .t-clear{display:flex}tui-textfield[multi][multi][data-mode~=invalid]:not([data-mode~=readonly])>.t-items>[tuiLabel]{color:var(--tui-text-negative)}tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly])>.t-items>[tuiLabel]{color:var(--tui-text-primary)}tui-textfield[multi][multi][data-mode~=readonly]>.t-content .t-clear,tui-textfield[multi][multi]._disabled>.t-content .t-clear,tui-textfield[multi][multi]._empty>.t-content .t-clear{display:none}tui-textfield[multi][multi]>.t-items input:not(:focus)::placeholder,tui-textfield[multi][multi][data-mode~=readonly]>.t-items input::placeholder{opacity:0}tui-textfield[multi][multi]>.t-items input:not(:focus)~.t-ghost:before,tui-textfield[multi][multi][data-mode~=readonly]>.t-items input~.t-ghost:before{display:none}tui-textfield[multi][multi]:not([data-mode~=readonly])._empty>.t-items input::placeholder,tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly]):not(:focus-within)>.t-items input::placeholder{opacity:1}tui-textfield[multi][multi]:not([data-mode~=readonly])._empty>.t-items .t-ghost:before,tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly]):not(:focus-within)>.t-items .t-ghost:before{display:inline}tui-textfield[multi][multi]:not(._empty)>.t-items [tuiLabel],tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly])>.t-items [tuiLabel]{font-size:.83em;transform:translateY(var(--t-label))}tui-textfield[multi][multi][data-size=l]{--t-vertical: .625rem;--t-offset: calc(1rem - var(--t-end) / 4.5)}tui-textfield[multi][multi][data-size=l]:before{margin-inline-end:.75rem}tui-textfield[multi][multi][data-size=l]:after{left:calc(100% - var(--t-end) - .375rem + .25 * (1em - 1rem));margin-inline-end:calc(.5 * (1.5rem - 1em) - var(--t-padding));margin-inline-start:-.75rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=l]:after{left:unset;inset-inline-start:calc(100% - var(--t-end) - .375rem + .25 * (1em - 1rem))}}tui-textfield[multi][multi][data-size=l]._with-label{--t-vertical: 1.125rem}tui-textfield[multi][multi][data-size=l]._with-label>.t-items{padding:1.75rem 0 .5rem}tui-textfield[multi][multi][data-size=m]{--t-vertical: .5rem;--t-offset: calc(.75rem + var(--t-end) / 14)}tui-textfield[multi][multi][data-size=m]:before{left:-.125rem;margin-inline-end:.375rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=m]:before{left:unset;inset-inline-start:-.125rem}}tui-textfield[multi][multi][data-size=m]:after{left:calc(100% - var(--t-end) - .25rem);margin-inline-start:-.125rem;border-width:.625rem;margin-inline-end:calc(.5 * (1.5rem - 1em) - var(--t-padding))}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=m]:after{left:unset;inset-inline-start:calc(100% - var(--t-end) - .25rem)}}tui-textfield[multi][multi][data-size=m]._with-label{--t-vertical: .875rem}tui-textfield[multi][multi][data-size=m]._with-label>.t-items{padding:1.375rem 0 .375rem}tui-textfield[multi][multi][data-size=m]>.t-content{transform:translate(.125rem)}tui-textfield[multi][multi][data-size=s]{--t-vertical: .125rem;--t-offset: calc(.625rem + var(--t-end) / 10)}tui-textfield[multi][multi][data-size=s]:before{left:-.125rem;margin-inline-end:.375rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=s]:before{left:unset;inset-inline-start:-.125rem}}tui-textfield[multi][multi][data-size=s]:after{left:calc(100% - var(--t-end) - .125rem);border-width:.5rem;margin-inline-end:calc(-1 * var(--t-padding))}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=s]:after{left:unset;inset-inline-start:calc(100% - var(--t-end) - .125rem)}}tui-textfield[multi][multi][data-size=s]>.t-content{gap:0;transform:translate(calc(.375rem - var(--t-end) / 10))}tui-textfield[multi][multi]:focus-visible:not([data-focus=false]) input::placeholder,tui-textfield[multi][multi]:focus-visible:not([data-focus=false]) input._empty{color:var(--tui-text-tertiary)}tui-textfield[multi][multi][data-focus=true] input::placeholder,tui-textfield[multi][multi][data-focus=true] input._empty{color:var(--tui-text-tertiary)}tui-textfield[multi][multi][tuiWrapper]:not(._focused):has(:focus-visible) input::placeholder,tui-textfield[multi][multi][tuiWrapper]._focused input::placeholder,tui-textfield[multi][multi][tuiWrapper]:not(._focused):has(:focus-visible) input._empty,tui-textfield[multi][multi][tuiWrapper]._focused input._empty{color:var(--tui-text-tertiary)}tui-textfield[multi][multi] tui-textfield-item{transform:translate(var(--t-scroll))}\\n\"]\n    }]\n  }], null, {\n    item: [{\n      type: ContentChild,\n      args: [TuiItem, {\n        read: TemplateRef,\n        descendants: true\n      }]\n    }],\n    rows: [{\n      type: Input\n    }]\n  });\n})();\nconst TuiTextfield = [TuiItem, TuiLabel, TuiSelect, TuiTextfieldComponent, TuiTextfieldDirective, TuiTextfieldOptionsDirective, TuiTextfieldDropdownDirective, TuiTextfieldMultiComponent];\nfunction tuiInjectAuxiliary(predicate) {\n  const {\n    auxiliaries\n  } = inject(TuiTextfieldComponent);\n  return computed(() => auxiliaries().find(predicate) ?? null);\n}\nclass TuiTextfieldContent {\n  constructor() {\n    this.ref = inject(TuiTextfieldComponent).vcr?.createEmbeddedView(inject(TemplateRef));\n  }\n  ngDoCheck() {\n    this.ref?.detectChanges();\n  }\n  static {\n    this.ɵfac = function TuiTextfieldContent_Factory(t) {\n      return new (t || TuiTextfieldContent)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTextfieldContent,\n      selectors: [[\"ng-template\", \"tuiTextfieldContent\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTextfieldContent, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-template[tuiTextfieldContent]'\n    }]\n  }], null, null);\n})();\nfunction tuiTextfieldIconBinding(token) {\n  const textfield = inject(TUI_TEXTFIELD_OPTIONS);\n  const options = inject(token);\n  return tuiDirectiveBinding(TuiIcons, 'iconEnd', computed(() => options.icon(textfield.size())), {});\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_TEXTFIELD_ACCESSOR, TUI_TEXTFIELD_OPTIONS, TuiSelect, TuiSelectLike, TuiTextfield, TuiTextfieldBase, TuiTextfieldBaseComponent, TuiTextfieldComponent, TuiTextfieldContent, TuiTextfieldDirective, TuiTextfieldDropdownDirective, TuiTextfieldItemComponent, TuiTextfieldMultiComponent, TuiTextfieldOptionsDirective, TuiWithTextfield, TuiWithTextfieldDropdown, tuiAsTextfieldAccessor, tuiInjectAuxiliary, tuiTextfieldIconBinding, tuiTextfieldOptionsProvider };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "Directive", "signal", "Optional", "SkipSelf", "Input", "TemplateRef", "computed", "ElementRef", "ViewContainerRef", "ViewChild", "ContentChild", "forwardRef", "ContentChildren", "TUI_IS_ANDROID", "tuiInjectElement", "tuiValue", "tuiIsElement", "tuiWithStyles", "tuiCreateToken", "tui<PERSON><PERSON><PERSON>", "tuiPx", "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tuiDirectiveBinding", "TuiItem", "Tui<PERSON><PERSON>l", "i3$1", "NgIf", "DOCUMENT", "CommonModule", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WA_NAVIGATOR", "i1$1", "TuiNativeValidator", "i2$1", "tui<PERSON><PERSON><PERSON><PERSON>", "tuiAppearanceState", "tuiAppearanceMode", "tui<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TuiAppearan<PERSON>", "NgControl", "i3", "TuiWithItemsHandlers", "TUI_ITEMS_HANDLERS", "toSignal", "takeUntilDestroyed", "WaResizeObserver", "TuiControl", "EMPTY_QUERY", "tui<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tuiZonefree", "tuiInjectId", "tuiFocusedIn", "tuiButtonOptionsProvider", "TuiButton", "i4", "tuiAsDataListHost", "TuiWithOptionContent", "i1", "TuiDropdownDirective", "tuiDropdownOpen", "TuiDropdownOpen", "TuiDropdownFixed", "TuiWithDropdownOpen", "i2", "TuiWithIcons", "TuiIcons", "TUI_COMMON_ICONS", "TUI_CLEAR_WORD", "TUI_AUXILIARY", "TUI_SCROLL_REF", "Polymorpheus<PERSON><PERSON>let", "injectContext", "PolymorpheusComponent", "ReplaySubject", "switchMap", "startWith", "fromEvent", "filter", "TuiScrollControls", "i4$1", "TUI_ITEMS_HANDLERS$1", "TuiWithItemsHandlers$1", "TuiWithAppearance", "_c0", "_c1", "_c2", "_c3", "_c4", "a0", "$implicit", "TuiTextfieldComponent_button_6_Template", "rf", "ctx", "_r2", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiTextfieldComponent_button_6_Template_button_click_0_listener", "ɵɵrestoreView", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "accessor", "setValue", "TuiTextfieldComponent_button_6_Template_button_pointerdown_zoneless_prevent_0_listener", "input", "nativeElement", "focus", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "icons", "close", "ɵɵadvance", "ɵɵtextInterpolate1", "clear", "TuiTextfieldComponent_span_10_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "text_r4", "polymorpheusOutlet", "TuiTextfieldComponent_span_10_Template", "ɵɵtemplate", "content", "ɵɵpureFunction1", "control", "value", "TuiTextfieldComponent_input_11_Template", "ɵɵelement", "computedFiller", "_c5", "_c6", "TuiSelect_option_0_Template", "ctx_r0", "placeholder", "TuiSelect_ng_template_1_option_0_Template", "item_r2", "TuiSelect_ng_template_1_Template", "stringified", "TuiTextfieldItemComponent_ng_container_0_Template", "text_r1", "ɵɵtextInterpolate", "_c7", "_c8", "_c9", "_c10", "a1", "item", "index", "TuiTextfieldMultiComponent_tui_scroll_controls_0_Template", "TuiTextfieldMultiComponent_3_ng_template_0_Template", "TuiTextfieldMultiComponent_3_Template", "index_r3", "ctx_r3", "component", "ɵɵpureFunction2", "TuiTextfieldMultiComponent_input_7_Template", "TuiTextfieldMultiComponent_button_10_Template", "_r5", "TuiTextfieldMultiComponent_button_10_Template_button_click_0_listener", "TuiTextfieldMultiComponent_span_14_ng_container_1_Template", "text_r6", "TuiTextfieldMultiComponent_span_14_Template", "TuiSelectLikeStyles", "ɵfac", "TuiSelectLikeStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiSelectLikeStyles_Template", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiSelectLike", "constructor", "el", "isAndroid", "nothing", "prevent", "event", "preventDefault", "TuiSelectLike_Factory", "ɵdir", "ɵɵdefineDirective", "hostBindings", "TuiSelectLike_HostBindings", "TuiSelectLike_beforeinput_HostBindingHandler", "$event", "inputType", "includes", "TuiSelectLike_input_capture_HostBindingHandler", "TuiSelectLike_mousedown_HostBindingHandler", "selector", "inputmode", "spellcheck", "autocomplete", "DEFAULT", "appearance", "size", "cleaner", "TUI_TEXTFIELD_OPTIONS", "tuiTextfieldOptionsProvider", "options", "provide", "deps", "useFactory", "parent", "TuiTextfieldOptionsDirective", "skipSelf", "tuiTextfieldAppearance", "set", "tuiTextfieldSize", "tui<PERSON><PERSON><PERSON>fieldCleaner", "enabled", "TuiTextfieldOptionsDirective_Factory", "inputs", "ɵɵProvidersFeature", "providers", "TUI_TEXTFIELD_ACCESSOR", "tuiAsTextfieldAccessor", "TuiTextfieldDropdownDirective", "directive", "tuiDropdown", "ngOnDestroy", "TuiTextfieldDropdownDirective_Factory", "TuiWithTextfieldDropdown", "TuiWithTextfieldDropdown_Factory", "TuiTextfieldBaseComponent", "filler", "autoId", "focusedIn", "contentReady$", "inputQuery", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "open", "dropdown", "dropdownOpen", "filledValue", "slice", "length", "showFiller", "focused", "auxiliaries", "pipe", "requireSync", "fillerSetter", "id", "ngAfterContentInit", "next", "handleOption", "option", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "label", "childNodes", "onResize", "contentRect", "style", "setProperty", "width", "onIconClick", "tuiDropdownEnabled", "_content", "matches", "update", "x", "onScroll", "element", "ghost", "scrollTo", "left", "scrollLeft", "TuiTextfieldBaseComponent_Factory", "contentQueries", "TuiTextfieldBaseComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "TuiTextfieldBase", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "cva", "viewQuery", "TuiTextfieldBaseComponent_Query", "ɵɵviewQuery", "vcr", "ɵɵInputFlags", "read", "descendants", "static", "TuiTextfieldComponent", "ɵTuiTextfieldComponent_BaseFactory", "TuiTextfieldComponent_Factory", "ɵɵgetInheritedFactory", "hostVars", "TuiTextfieldComponent_HostBindings", "TuiTextfieldComponent_click_self_prevent_HostBindingHandler", "TuiTextfieldComponent_pointerdown_self_prevent_HostBindingHandler", "TuiTextfieldComponent_scroll_capture_zoneless_HostBindingHandler", "target", "TuiTextfieldComponent_tuiActiveZoneChange_HostBindingHandler", "onTouched", "ɵɵattribute", "ɵɵclassProp", "disabled", "ɵɵHostDirectivesFeature", "ɵɵInheritDefinitionFeature", "ngContentSelectors", "consts", "TuiTextfieldComponent_Template", "_r1", "ɵɵprojectionDef", "ɵɵprojection", "TuiTextfieldComponent_Template_span_pointerdown_prevent_4_listener", "TuiTextfieldComponent_Template_span_waResizeObserver_4_listener", "ɵɵelementContainer", "dependencies", "imports", "hostDirectives", "optional", "a", "s", "m", "mode", "f", "textfield", "handlers", "readOnly", "invalid", "focusedSetter", "stateSetter", "state", "ngOnChanges", "select", "ownerDocument", "execCommand", "stringify", "TuiTextfieldBase_Factory", "TuiTextfieldBase_HostBindings", "TuiTextfieldBase_input_HostBindingHandler", "TuiTextfieldBase_focusin_HostBindingHandler", "TuiTextfieldBase_focusout_HostBindingHandler", "ɵɵhostProperty", "ɵɵNgOnChangesFeature", "TuiTextfieldDirective", "ɵTuiTextfieldDirective_BaseFactory", "TuiTextfieldDirective_Factory", "TuiWithTextfield", "TuiWithTextfield_Factory", "TuiSelect", "arguments", "nav", "doc", "dispatchEvent", "Event", "bubbles", "classList", "add", "remove", "aria<PERSON><PERSON><PERSON>", "querySelector", "getAttribute", "onCopy", "_this", "_asyncToGenerator", "clipboard", "writeText", "ɵTuiSelect_BaseFactory", "TuiSelect_Factory", "TuiSelect_HostBindings", "TuiSelect_input_HostBindingHandler", "TuiSelect_focusin_HostBindingHandler", "TuiSelect_focusout_HostBindingHandler", "TuiSelect_keydown_space_prevent_HostBindingHandler", "TuiSelect_keydown_enter_prevent_HostBindingHandler", "TuiSelect_keydown_backspace_HostBindingHandler", "TuiSelect_mousedown_prevent_HostBindingHandler", "TuiSelect_keydown_control_c_HostBindingHandler", "TuiSelect_keydown_meta_c_HostBindingHandler", "attrs", "TuiSelect_Template", "ɵɵtemplateRefExtractor", "selected_r3", "ɵɵreference", "<PERSON><PERSON><PERSON>", "TuiTextfieldItemComponent", "context", "TuiTextfieldMultiComponent", "String", "TuiTextfieldItemComponent_Factory", "TuiTextfieldItemComponent_HostBindings", "TuiTextfieldItemComponent_pointerdown_self_prevent_HostBindingHandler", "TuiTextfieldItemComponent_keydown_arrowLeft_prevent_HostBindingHandler", "previousElementSibling", "<PERSON><PERSON><PERSON><PERSON>", "TuiTextfieldItemComponent_keydown_arrowRight_prevent_HostBindingHandler", "nextElement<PERSON><PERSON>ling", "TuiTextfieldItemComponent_Template", "height", "sub", "rows", "subscribe", "identityMatcher", "onItems", "clientHeight", "onLeft", "currentTarget", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "onClick", "ɵTuiTextfieldMultiComponent_BaseFactory", "TuiTextfieldMultiComponent_Factory", "TuiTextfieldMultiComponent_ContentQueries", "TuiTextfieldMultiComponent_HostBindings", "TuiTextfieldMultiComponent_click_HostBindingHandler", "TuiTextfieldMultiComponent_tuiActiveZoneChange_HostBindingHandler", "TuiTextfieldMultiComponent_click_self_prevent_HostBindingHandler", "TuiTextfieldMultiComponent_pointerdown_self_prevent_HostBindingHandler", "TuiTextfieldMultiComponent_scroll_capture_zoneless_HostBindingHandler", "ɵɵstyleProp", "TuiTextfieldMultiComponent_Template", "TuiTextfieldMultiComponent_Template_div_click_1_listener", "TuiTextfieldMultiComponent_Template_div_pointerdown_self_zoneless_prevent_1_listener", "TuiTextfieldMultiComponent_Template_div_waResizeObserver_1_listener", "TuiTextfieldMultiComponent_Template_span_keydown_arrowLeft_4_listener", "TuiTextfieldMultiComponent_Template_span_click_stop_8_listener", "TuiTextfieldMultiComponent_Template_span_pointerdown_zoneless_prevent_8_listener", "TuiTextfieldMultiComponent_Template_span_waResizeObserver_8_listener", "TuiTextfield", "tuiInjectAuxiliary", "predicate", "find", "TuiTextfieldContent", "ref", "createEmbeddedView", "ngDoCheck", "detectChanges", "TuiTextfieldContent_Factory", "tuiTextfieldIconBinding", "token", "icon"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-components-textfield.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, signal, Optional, SkipSelf, Input, TemplateRef, computed, ElementRef, ViewContainerRef, ViewChild, ContentChild, forwardRef, ContentChildren } from '@angular/core';\nimport { TUI_IS_ANDROID } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement, tuiValue, tuiIsElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiWithStyles, tuiCreateToken, tuiProvide, tuiPx, tuiArrayToggle, tuiDirectiveBinding } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiItem } from '@taiga-ui/cdk/directives/item';\nimport { TuiLabel } from '@taiga-ui/core/components/label';\nimport * as i3$1 from '@angular/common';\nimport { NgIf, DOCUMENT, CommonModule, NgForOf } from '@angular/common';\nimport { WA_NAVIGATOR } from '@ng-web-apis/common';\nimport * as i1$1 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport * as i2$1 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearance, tuiAppearanceState, tuiAppearanceMode, tuiAppearanceFocus, TuiAppearance } from '@taiga-ui/core/directives/appearance';\nimport { NgControl } from '@angular/forms';\nimport * as i3 from '@taiga-ui/core/directives/items-handlers';\nimport { TuiWithItemsHandlers, TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { WaResizeObserver } from '@ng-web-apis/resize-observer';\nimport { TuiControl } from '@taiga-ui/cdk/classes';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiQueryListChanges, tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { tuiInjectId } from '@taiga-ui/cdk/services';\nimport { tuiFocusedIn } from '@taiga-ui/cdk/utils/focus';\nimport { tuiButtonOptionsProvider, TuiButton } from '@taiga-ui/core/components/button';\nimport * as i4 from '@taiga-ui/core/components/data-list';\nimport { tuiAsDataListHost, TuiWithOptionContent } from '@taiga-ui/core/components/data-list';\nimport * as i1 from '@taiga-ui/core/directives/dropdown';\nimport { TuiDropdownDirective, tuiDropdownOpen, TuiDropdownOpen, TuiDropdownFixed, TuiWithDropdownOpen } from '@taiga-ui/core/directives/dropdown';\nimport * as i2 from '@taiga-ui/core/directives/icons';\nimport { TuiWithIcons, TuiIcons } from '@taiga-ui/core/directives/icons';\nimport { TUI_COMMON_ICONS, TUI_CLEAR_WORD, TUI_AUXILIARY, TUI_SCROLL_REF } from '@taiga-ui/core/tokens';\nimport { PolymorpheusOutlet, injectContext, PolymorpheusComponent } from '@taiga-ui/polymorpheus';\nimport { ReplaySubject, switchMap, startWith, fromEvent, filter } from 'rxjs';\nimport { TuiScrollControls } from '@taiga-ui/core/components/scrollbar';\nimport * as i4$1 from '@taiga-ui/core/directives';\nimport { TUI_ITEMS_HANDLERS as TUI_ITEMS_HANDLERS$1, TuiWithItemsHandlers as TuiWithItemsHandlers$1, TuiWithAppearance } from '@taiga-ui/core/directives';\n\nclass TuiSelectLikeStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSelectLikeStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSelectLikeStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-select-like\" }, ngImport: i0, template: '', isInline: true, styles: [\".t-select-like:not(:read-only){cursor:pointer}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSelectLikeStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-select-like',\n                    }, styles: [\".t-select-like:not(:read-only){cursor:pointer}\\n\"] }]\n        }] });\nclass TuiSelectLike {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.isAndroid = inject(TUI_IS_ANDROID);\n        this.nothing = tuiWithStyles(TuiSelectLikeStyles);\n    }\n    clear() {\n        this.el.value = '';\n    }\n    prevent(event) {\n        if (!this.isAndroid) {\n            return;\n        }\n        event.preventDefault();\n        this.el.focus();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSelectLike, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSelectLike, isStandalone: true, selector: \"[tuiSelectLike]\", host: { attributes: { \"inputmode\": \"none\", \"spellcheck\": \"false\", \"autocomplete\": \"off\" }, listeners: { \"beforeinput\": \"$event.inputType.includes(\\\"delete\\\") || $event.preventDefault()\", \"input.capture\": \"$event.inputType?.includes(\\\"delete\\\") && clear()\", \"mousedown\": \"prevent($event)\" }, classAttribute: \"t-select-like\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSelectLike, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiSelectLike]',\n                    host: {\n                        class: 't-select-like',\n                        inputmode: 'none',\n                        spellcheck: 'false',\n                        autocomplete: 'off',\n                        // Click on cleaner icon does not trigger `beforeinput` event --> handle all kind of deletion in input event\n                        '(beforeinput)': '$event.inputType.includes(\"delete\") || $event.preventDefault()',\n                        '(input.capture)': '$event.inputType?.includes(\"delete\") && clear()',\n                        // Hide Android text select handle (bubble marker below transparent caret)\n                        '(mousedown)': 'prevent($event)',\n                    },\n                }]\n        }] });\n\nconst DEFAULT = {\n    appearance: 'textfield',\n    size: 'l',\n    cleaner: true,\n};\nconst TUI_TEXTFIELD_OPTIONS = tuiCreateToken({\n    appearance: signal(DEFAULT.appearance),\n    size: signal(DEFAULT.size),\n    cleaner: signal(DEFAULT.cleaner),\n});\nfunction tuiTextfieldOptionsProvider(options) {\n    return {\n        provide: TUI_TEXTFIELD_OPTIONS,\n        deps: [[new Optional(), new SkipSelf(), TUI_TEXTFIELD_OPTIONS]],\n        useFactory: (parent) => ({\n            appearance: signal(parent?.appearance() ?? DEFAULT.appearance),\n            size: signal(parent?.size() ?? DEFAULT.size),\n            cleaner: signal(parent?.cleaner() ?? DEFAULT.cleaner),\n            ...options,\n        }),\n    };\n}\nclass TuiTextfieldOptionsDirective {\n    constructor() {\n        this.options = inject(TUI_TEXTFIELD_OPTIONS, { skipSelf: true });\n        // TODO: refactor to signal inputs after Angular update\n        this.appearance = signal(this.options.appearance());\n        this.size = signal(this.options.size());\n        this.cleaner = signal(this.options.cleaner());\n    }\n    set tuiTextfieldAppearance(appearance) {\n        this.appearance.set(appearance);\n    }\n    set tuiTextfieldSize(size) {\n        this.size.set(size);\n    }\n    set tuiTextfieldCleaner(enabled) {\n        this.cleaner.set(enabled);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldOptionsDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTextfieldOptionsDirective, isStandalone: true, selector: \"[tuiTextfieldAppearance],[tuiTextfieldSize],[tuiTextfieldCleaner]\", inputs: { tuiTextfieldAppearance: \"tuiTextfieldAppearance\", tuiTextfieldSize: \"tuiTextfieldSize\", tuiTextfieldCleaner: \"tuiTextfieldCleaner\" }, providers: [tuiProvide(TUI_TEXTFIELD_OPTIONS, TuiTextfieldOptionsDirective)], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldOptionsDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiTextfieldAppearance],[tuiTextfieldSize],[tuiTextfieldCleaner]',\n                    providers: [tuiProvide(TUI_TEXTFIELD_OPTIONS, TuiTextfieldOptionsDirective)],\n                }]\n        }], propDecorators: { tuiTextfieldAppearance: [{\n                type: Input\n            }], tuiTextfieldSize: [{\n                type: Input\n            }], tuiTextfieldCleaner: [{\n                type: Input\n            }] } });\n\nconst TUI_TEXTFIELD_ACCESSOR = tuiCreateToken();\nfunction tuiAsTextfieldAccessor(accessor) {\n    return tuiProvide(TUI_TEXTFIELD_ACCESSOR, accessor);\n}\n\n// TODO: Change selector to tuiDropdown in v5 and move to TuiDropdown\nclass TuiTextfieldDropdownDirective {\n    constructor() {\n        this.directive = inject(TuiDropdownDirective);\n        this.directive.tuiDropdown = inject(TemplateRef);\n    }\n    ngOnDestroy() {\n        this.directive.tuiDropdown = null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldDropdownDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTextfieldDropdownDirective, isStandalone: true, selector: \"ng-template[tuiTextfieldDropdown]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldDropdownDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-template[tuiTextfieldDropdown]',\n                }]\n        }], ctorParameters: function () { return []; } });\n/**\n * @deprecated remove in v5\n */\nclass TuiWithTextfieldDropdown {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiWithTextfieldDropdown, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiWithTextfieldDropdown, isStandalone: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiWithTextfieldDropdown, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                }]\n        }] });\n\n// TODO: Remove base class in v5\nclass TuiTextfieldBaseComponent {\n    constructor() {\n        // TODO: refactor to signal inputs after Angular update\n        this.filler = signal('');\n        this.autoId = tuiInjectId();\n        this.focusedIn = tuiFocusedIn(tuiInjectElement());\n        this.contentReady$ = new ReplaySubject(1);\n        this.inputQuery = signal(undefined);\n        this.auxiliaryQuery = EMPTY_QUERY;\n        this.open = tuiDropdownOpen();\n        this.dropdown = inject(TuiDropdownDirective);\n        this.dropdownOpen = inject(TuiDropdownOpen);\n        this.icons = inject(TUI_COMMON_ICONS);\n        this.clear = toSignal(inject(TUI_CLEAR_WORD));\n        this.computedFiller = computed((value = this.value()) => {\n            const filledValue = value + this.filler().slice(value.length);\n            return filledValue.length > value.length ? filledValue : '';\n        });\n        this.showFiller = computed(() => this.focused() &&\n            !!this.computedFiller() &&\n            (!!this.value() || !this.input?.nativeElement.placeholder));\n        this.focused = computed(() => this.open() || this.focusedIn());\n        this.options = inject(TUI_TEXTFIELD_OPTIONS);\n        this.el = tuiInjectElement();\n        this.value = tuiValue(this.inputQuery);\n        // TODO: Refactor to signal queries when Angular is updated\n        this.auxiliaries = toSignal(this.contentReady$.pipe(switchMap(() => tuiQueryListChanges(this.auxiliaryQuery)), startWith([])), { requireSync: true });\n    }\n    set fillerSetter(filler) {\n        this.filler.set(filler);\n    }\n    get id() {\n        return this.input?.nativeElement.id || this.autoId;\n    }\n    get size() {\n        return this.options.size();\n    }\n    ngAfterContentInit() {\n        this.contentReady$.next(true);\n        this.inputQuery.set(this.input);\n    }\n    handleOption(option) {\n        this.accessor?.setValue(option);\n        this.open.set(false);\n    }\n    get hasLabel() {\n        return Boolean(this.label?.nativeElement?.childNodes.length);\n    }\n    onResize({ contentRect }) {\n        this.el.style.setProperty('--t-side', tuiPx(contentRect.width));\n    }\n    // Click on ::before,::after pseudo-elements ([iconStart] / [iconEnd])\n    onIconClick() {\n        this.input?.nativeElement.focus();\n        if (this.dropdownOpen.tuiDropdownEnabled &&\n            this.dropdown._content() &&\n            !this.input?.nativeElement.matches(':read-only')) {\n            this.open.update((x) => !x);\n        }\n    }\n    onScroll(element) {\n        if (this.input?.nativeElement === element) {\n            this.ghost?.nativeElement.scrollTo({\n                left: this.input.nativeElement.scrollLeft,\n            });\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldBaseComponent, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTextfieldBaseComponent, inputs: { content: \"content\", fillerSetter: [\"filler\", \"fillerSetter\"] }, queries: [{ propertyName: \"label\", first: true, predicate: i0.forwardRef(function () { return TuiLabel; }), descendants: true, read: ElementRef }, { propertyName: \"accessor\", first: true, predicate: TUI_TEXTFIELD_ACCESSOR, descendants: true }, { propertyName: \"control\", first: true, predicate: NgControl, descendants: true }, { propertyName: \"cva\", first: true, predicate: TuiControl, descendants: true }, { propertyName: \"input\", first: true, predicate: i0.forwardRef(function () { return TuiTextfieldBase; }), descendants: true, read: ElementRef, static: true }, { propertyName: \"auxiliaryQuery\", predicate: TUI_AUXILIARY, descendants: true }], viewQueries: [{ propertyName: \"ghost\", first: true, predicate: [\"ghost\"], descendants: true }, { propertyName: \"vcr\", first: true, predicate: [\"vcr\"], descendants: true, read: ViewContainerRef, static: true }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldBaseComponent, decorators: [{\n            type: Directive\n        }], propDecorators: { ghost: [{\n                type: ViewChild,\n                args: ['ghost']\n            }], label: [{\n                type: ContentChild,\n                args: [forwardRef(() => TuiLabel), { read: ElementRef }]\n            }], auxiliaryQuery: [{\n                type: ContentChildren,\n                args: [TUI_AUXILIARY, { descendants: true }]\n            }], vcr: [{\n                type: ViewChild,\n                args: ['vcr', { read: ViewContainerRef, static: true }]\n            }], accessor: [{\n                type: ContentChild,\n                args: [TUI_TEXTFIELD_ACCESSOR, { descendants: true }]\n            }], control: [{\n                type: ContentChild,\n                args: [NgControl]\n            }], cva: [{\n                type: ContentChild,\n                args: [TuiControl]\n            }], input: [{\n                type: ContentChild,\n                args: [forwardRef(() => TuiTextfieldBase), {\n                        read: ElementRef,\n                        static: true,\n                    }]\n            }], content: [{\n                type: Input\n            }], fillerSetter: [{\n                type: Input,\n                args: ['filler']\n            }] } });\nclass TuiTextfieldComponent extends TuiTextfieldBaseComponent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldComponent, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTextfieldComponent, isStandalone: true, selector: \"tui-textfield:not([multi])\", host: { listeners: { \"click.self.prevent\": \"0\", \"pointerdown.self.prevent\": \"onIconClick()\", \"scroll.capture.zoneless\": \"onScroll($event.target)\", \"tuiActiveZoneChange\": \"!$event && cva?.onTouched()\" }, properties: { \"attr.data-size\": \"options.size()\", \"class._with-label\": \"hasLabel\", \"class._with-template\": \"content && control?.value != null\", \"class._disabled\": \"input?.nativeElement?.disabled\" } }, providers: [\n            tuiButtonOptionsProvider({ size: 'xs', appearance: 'icon' }),\n            tuiAsDataListHost(TuiTextfieldComponent),\n        ], usesInheritance: true, hostDirectives: [{ directive: i1.TuiDropdownDirective }, { directive: i1.TuiDropdownFixed }, { directive: i1.TuiWithDropdownOpen }, { directive: i2.TuiWithIcons }, { directive: i3.TuiWithItemsHandlers }, { directive: i4.TuiWithOptionContent }, { directive: TuiWithTextfieldDropdown }], ngImport: i0, template: \"<ng-content select=\\\"input\\\" />\\n<ng-content select=\\\"select\\\" />\\n<ng-content select=\\\"textarea\\\" />\\n<ng-content select=\\\"label\\\" />\\n<span\\n    class=\\\"t-content\\\"\\n    (pointerdown.prevent)=\\\"input?.nativeElement?.focus()\\\"\\n    (waResizeObserver)=\\\"$event[0] && onResize($event[0])\\\"\\n>\\n    <ng-content />\\n    <button\\n        *ngIf=\\\"options.cleaner()\\\"\\n        appearance=\\\"icon\\\"\\n        size=\\\"xs\\\"\\n        tabindex=\\\"-1\\\"\\n        tuiIconButton\\n        type=\\\"button\\\"\\n        class=\\\"t-clear\\\"\\n        [iconStart]=\\\"icons.close\\\"\\n        (click)=\\\"accessor?.setValue(null)\\\"\\n        (pointerdown.zoneless.prevent)=\\\"input?.nativeElement?.focus()\\\"\\n    >\\n        {{ clear() }}\\n    </button>\\n    <ng-container #vcr />\\n    <ng-content select=\\\"tui-icon\\\" />\\n</span>\\n<span\\n    *ngIf=\\\"control?.value != null\\\"\\n    class=\\\"t-template\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: control?.value}\\\">\\n        {{ text }}\\n    </ng-container>\\n</span>\\n<input\\n    *ngIf=\\\"showFiller()\\\"\\n    #ghost\\n    aria-hidden=\\\"true\\\"\\n    disabled\\n    class=\\\"t-filler\\\"\\n    [value]=\\\"computedFiller()\\\"\\n/>\\n\", styles: [\"tui-textfield{scrollbar-width:none;-ms-overflow-style:none;transition-property:color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;--t-height: var(--tui-height-l);--t-padding: var(--tui-padding-l);--t-label: -.7em;--t-end: 0rem;--t-start: 0rem;position:relative;display:flex;flex-wrap:wrap;align-items:center;cursor:pointer;min-block-size:var(--t-height);color:var(--tui-text-tertiary);padding:0 var(--t-padding);border-radius:var(--tui-radius-l);font:var(--tui-font-text-ui-m);box-sizing:border-box;gap:0 .25rem;isolation:isolate}tui-textfield::-webkit-scrollbar,tui-textfield::-webkit-scrollbar-thumb{display:none}tui-textfield[style*=\\\"--t-icon-start:\\\"]{--t-start: 2.25rem}tui-textfield[style*=\\\"--t-icon-end:\\\"]{--t-end: 2.25rem}tui-textfield[tuiIcons]:after{position:relative;block-size:auto;align-self:stretch;border-inline-start:var(--t-padding) solid transparent;border-inline-end:var(--t-padding) solid transparent;margin:0 calc(-1 * var(--t-padding))}tui-textfield::-webkit-resizer{border:.25rem solid transparent;inline-size:.5rem;block-size:.5rem;box-sizing:content-box;color:var(--tui-text-tertiary);background:linear-gradient(-45deg,transparent,transparent .125rem,currentColor .125rem,currentColor .1875rem,transparent .1875rem,transparent .25rem,currentColor .25rem,currentColor .3125rem,transparent .35rem);background-clip:content-box}tui-textfield label,tui-textfield>.t-content,tui-textfield>.t-template{pointer-events:none}tui-textfield input,tui-textfield select,tui-textfield textarea{font:var(--tui-font-text-ui-m);resize:none;outline:none;padding-block-start:1.125rem;padding-block-end:1.125rem}tui-textfield input[inputmode=none],tui-textfield select[inputmode=none],tui-textfield textarea[inputmode=none]{caret-color:transparent}tui-textfield[data-size=s]{--t-height: var(--tui-height-s);--t-padding: var(--tui-padding-s);border-radius:var(--tui-radius-m);gap:0;font:var(--tui-font-text-ui-s)}tui-textfield[data-size=s][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.25rem}tui-textfield[data-size=s][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.25rem}tui-textfield[data-size=s]:before{margin-inline-end:.5rem;margin-inline-start:-.125rem;font-size:1rem}tui-textfield[data-size=s]:after{margin-inline-end:calc(-.125rem - var(--t-padding));margin-inline-start:calc(.625rem - var(--t-padding));font-size:1rem}tui-textfield[data-size=s] input,tui-textfield[data-size=s] select,tui-textfield[data-size=s] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.5rem;padding-block-end:.5rem}tui-textfield[data-size=s]>.t-content{margin-inline-end:-.375rem}tui-textfield[data-size=m]{--t-height: var(--tui-height-m);--t-padding: var(--tui-padding-m);border-radius:var(--tui-radius-m);font:var(--tui-font-text-ui-s)}tui-textfield[data-size=m][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.75rem}tui-textfield[data-size=m][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.75rem}tui-textfield[data-size=m]:before{margin-inline-start:-.125rem;margin-inline-end:.125rem}tui-textfield[data-size=m]:after{margin-inline-start:calc(.25rem - var(--t-padding));margin-inline-end:calc(-.125rem - var(--t-padding))}tui-textfield[data-size=m] input,tui-textfield[data-size=m] select,tui-textfield[data-size=m] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.875rem;padding-block-end:.875rem}tui-textfield[data-size=m]>.t-content{margin-inline-end:-.125rem}tui-textfield[data-size=l]{--t-label: -.7rem}tui-textfield:hover{color:var(--tui-text-secondary)}tui-textfield:hover:has(input:read-only):not([multi]),tui-textfield:hover:has(textarea:read-only),tui-textfield:hover:has(select[data-mode~=readonly]){color:var(--tui-text-tertiary)}tui-textfield:before{z-index:1;margin-inline-end:.5rem}tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):before,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):after,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip]))>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield._disabled:before,tui-textfield._disabled:after,tui-textfield._disabled>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield:has(label:not(:empty))>.t-template,tui-textfield:has(label:not(:empty)) input:not([type=range]),tui-textfield:has(label:not(:empty)) select:defined,tui-textfield:has(label:not(:empty)) textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined._empty{color:transparent}tui-textfield._with-label>.t-template,tui-textfield._with-label input:not([type=range]),tui-textfield._with-label select:defined,tui-textfield._with-label textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true])._with-label>.t-template::placeholder,tui-textfield:not([data-focus=true])._with-label input:not([type=range])::placeholder,tui-textfield:not([data-focus=true])._with-label select:defined::placeholder,tui-textfield:not([data-focus=true])._with-label textarea:defined::placeholder,tui-textfield:not([data-focus=true])._with-label>.t-template._empty,tui-textfield:not([data-focus=true])._with-label input:not([type=range])._empty,tui-textfield:not([data-focus=true])._with-label select:defined._empty,tui-textfield:not([data-focus=true])._with-label textarea:defined._empty{color:transparent}tui-textfield>.t-template,tui-textfield input:defined,tui-textfield select:defined,tui-textfield textarea:defined{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;-webkit-appearance:none;appearance:none;box-sizing:border-box;border-radius:inherit;border-width:0;overscroll-behavior:none;padding-inline-start:calc(var(--t-start, 0rem) + var(--t-padding));padding-inline-end:calc(var(--t-end, 0rem) + var(--t-side) + var(--t-padding))}tui-textfield>.t-template{display:flex;align-items:center;color:var(--tui-text-primary)}tui-textfield._with-template input:not([type=range]),tui-textfield._with-template select,tui-textfield._with-template textarea{color:transparent!important}tui-textfield input:not([type=range]),tui-textfield select:defined,tui-textfield textarea:defined{pointer-events:auto;background:transparent}tui-textfield input:not([type=range]):read-only~.t-filler,tui-textfield select:defined:read-only~.t-filler,tui-textfield textarea:defined:read-only~.t-filler{display:none}tui-textfield input:not([type=range]):disabled~label,tui-textfield select:defined:disabled~label,tui-textfield textarea:defined:disabled~label,tui-textfield input:not([type=range]):disabled~.t-content,tui-textfield select:defined:disabled~.t-content,tui-textfield textarea:defined:disabled~.t-content{opacity:var(--tui-disabled-opacity)}tui-textfield input:not([type=range]):disabled~label>tui-icon,tui-textfield select:defined:disabled~label>tui-icon,tui-textfield textarea:defined:disabled~label>tui-icon,tui-textfield input:not([type=range]):disabled~.t-content>tui-icon,tui-textfield select:defined:disabled~.t-content>tui-icon,tui-textfield textarea:defined:disabled~.t-content>tui-icon{display:none}tui-textfield input:not([type=range]):-webkit-autofill~label,tui-textfield select:defined:-webkit-autofill~label,tui-textfield textarea:defined:-webkit-autofill~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown)~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown)~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown)~label{font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label{color:var(--tui-text-negative)}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear{display:flex}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]~label,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]~label,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}@supports (-webkit-touch-callout: none){tui-textfield input:not([type=range])._ios-fix,tui-textfield select:defined._ios-fix,tui-textfield textarea:defined._ios-fix{position:fixed;left:1000rem}}tui-textfield [tuiLabel][tuiLabel][tuiLabel]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;position:relative;display:block;flex:1;align-self:flex-start;font-size:inherit;line-height:var(--t-height)}tui-textfield label:defined,tui-textfield input:defined::placeholder,tui-textfield textarea:defined::placeholder,tui-textfield select:defined._empty{color:var(--tui-text-secondary)}tui-textfield select:not([data-mode~=readonly]){cursor:pointer}tui-textfield select option[value=\\\"\\\"]:disabled{color:transparent}tui-textfield select optgroup,tui-textfield select option{background-color:var(--tui-background-elevation-3)}tui-textfield select optgroup,tui-textfield select option:not(:disabled){color:var(--tui-text-primary)}tui-textfield button,tui-textfield a{pointer-events:auto}tui-textfield>.t-content{z-index:1;display:flex;block-size:var(--t-height);align-items:center;gap:inherit;margin-inline-start:auto;isolation:isolate;border-radius:inherit}tui-textfield>.t-content>tui-icon{pointer-events:auto}tui-textfield textarea~.t-content{min-inline-size:.5rem}tui-textfield .t-clear{z-index:1;display:none;pointer-events:auto}tui-textfield>.t-filler:defined{pointer-events:none;background:none;color:var(--tui-text-tertiary);opacity:1}tui-textfield [tuiFluidTypography]{font-weight:700}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"directive\", type: WaResizeObserver, selector: \"[waResizeObserver]\", inputs: [\"box\"], outputs: [\"waResizeObserver\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-textfield:not([multi])', imports: [NgIf, PolymorpheusOutlet, TuiButton, WaResizeObserver], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        tuiButtonOptionsProvider({ size: 'xs', appearance: 'icon' }),\n                        tuiAsDataListHost(TuiTextfieldComponent),\n                    ], hostDirectives: [\n                        TuiDropdownDirective,\n                        TuiDropdownFixed,\n                        TuiWithDropdownOpen,\n                        TuiWithIcons,\n                        TuiWithItemsHandlers,\n                        TuiWithOptionContent,\n                        TuiWithTextfieldDropdown,\n                    ], host: {\n                        '[attr.data-size]': 'options.size()',\n                        '[class._with-label]': 'hasLabel',\n                        '[class._with-template]': 'content && control?.value != null',\n                        '[class._disabled]': 'input?.nativeElement?.disabled',\n                        '(click.self.prevent)': '0',\n                        '(pointerdown.self.prevent)': 'onIconClick()',\n                        '(scroll.capture.zoneless)': 'onScroll($event.target)',\n                        '(tuiActiveZoneChange)': '!$event && cva?.onTouched()',\n                    }, template: \"<ng-content select=\\\"input\\\" />\\n<ng-content select=\\\"select\\\" />\\n<ng-content select=\\\"textarea\\\" />\\n<ng-content select=\\\"label\\\" />\\n<span\\n    class=\\\"t-content\\\"\\n    (pointerdown.prevent)=\\\"input?.nativeElement?.focus()\\\"\\n    (waResizeObserver)=\\\"$event[0] && onResize($event[0])\\\"\\n>\\n    <ng-content />\\n    <button\\n        *ngIf=\\\"options.cleaner()\\\"\\n        appearance=\\\"icon\\\"\\n        size=\\\"xs\\\"\\n        tabindex=\\\"-1\\\"\\n        tuiIconButton\\n        type=\\\"button\\\"\\n        class=\\\"t-clear\\\"\\n        [iconStart]=\\\"icons.close\\\"\\n        (click)=\\\"accessor?.setValue(null)\\\"\\n        (pointerdown.zoneless.prevent)=\\\"input?.nativeElement?.focus()\\\"\\n    >\\n        {{ clear() }}\\n    </button>\\n    <ng-container #vcr />\\n    <ng-content select=\\\"tui-icon\\\" />\\n</span>\\n<span\\n    *ngIf=\\\"control?.value != null\\\"\\n    class=\\\"t-template\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: control?.value}\\\">\\n        {{ text }}\\n    </ng-container>\\n</span>\\n<input\\n    *ngIf=\\\"showFiller()\\\"\\n    #ghost\\n    aria-hidden=\\\"true\\\"\\n    disabled\\n    class=\\\"t-filler\\\"\\n    [value]=\\\"computedFiller()\\\"\\n/>\\n\", styles: [\"tui-textfield{scrollbar-width:none;-ms-overflow-style:none;transition-property:color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;--t-height: var(--tui-height-l);--t-padding: var(--tui-padding-l);--t-label: -.7em;--t-end: 0rem;--t-start: 0rem;position:relative;display:flex;flex-wrap:wrap;align-items:center;cursor:pointer;min-block-size:var(--t-height);color:var(--tui-text-tertiary);padding:0 var(--t-padding);border-radius:var(--tui-radius-l);font:var(--tui-font-text-ui-m);box-sizing:border-box;gap:0 .25rem;isolation:isolate}tui-textfield::-webkit-scrollbar,tui-textfield::-webkit-scrollbar-thumb{display:none}tui-textfield[style*=\\\"--t-icon-start:\\\"]{--t-start: 2.25rem}tui-textfield[style*=\\\"--t-icon-end:\\\"]{--t-end: 2.25rem}tui-textfield[tuiIcons]:after{position:relative;block-size:auto;align-self:stretch;border-inline-start:var(--t-padding) solid transparent;border-inline-end:var(--t-padding) solid transparent;margin:0 calc(-1 * var(--t-padding))}tui-textfield::-webkit-resizer{border:.25rem solid transparent;inline-size:.5rem;block-size:.5rem;box-sizing:content-box;color:var(--tui-text-tertiary);background:linear-gradient(-45deg,transparent,transparent .125rem,currentColor .125rem,currentColor .1875rem,transparent .1875rem,transparent .25rem,currentColor .25rem,currentColor .3125rem,transparent .35rem);background-clip:content-box}tui-textfield label,tui-textfield>.t-content,tui-textfield>.t-template{pointer-events:none}tui-textfield input,tui-textfield select,tui-textfield textarea{font:var(--tui-font-text-ui-m);resize:none;outline:none;padding-block-start:1.125rem;padding-block-end:1.125rem}tui-textfield input[inputmode=none],tui-textfield select[inputmode=none],tui-textfield textarea[inputmode=none]{caret-color:transparent}tui-textfield[data-size=s]{--t-height: var(--tui-height-s);--t-padding: var(--tui-padding-s);border-radius:var(--tui-radius-m);gap:0;font:var(--tui-font-text-ui-s)}tui-textfield[data-size=s][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.25rem}tui-textfield[data-size=s][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.25rem}tui-textfield[data-size=s]:before{margin-inline-end:.5rem;margin-inline-start:-.125rem;font-size:1rem}tui-textfield[data-size=s]:after{margin-inline-end:calc(-.125rem - var(--t-padding));margin-inline-start:calc(.625rem - var(--t-padding));font-size:1rem}tui-textfield[data-size=s] input,tui-textfield[data-size=s] select,tui-textfield[data-size=s] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.5rem;padding-block-end:.5rem}tui-textfield[data-size=s]>.t-content{margin-inline-end:-.375rem}tui-textfield[data-size=m]{--t-height: var(--tui-height-m);--t-padding: var(--tui-padding-m);border-radius:var(--tui-radius-m);font:var(--tui-font-text-ui-s)}tui-textfield[data-size=m][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.75rem}tui-textfield[data-size=m][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.75rem}tui-textfield[data-size=m]:before{margin-inline-start:-.125rem;margin-inline-end:.125rem}tui-textfield[data-size=m]:after{margin-inline-start:calc(.25rem - var(--t-padding));margin-inline-end:calc(-.125rem - var(--t-padding))}tui-textfield[data-size=m] input,tui-textfield[data-size=m] select,tui-textfield[data-size=m] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.875rem;padding-block-end:.875rem}tui-textfield[data-size=m]>.t-content{margin-inline-end:-.125rem}tui-textfield[data-size=l]{--t-label: -.7rem}tui-textfield:hover{color:var(--tui-text-secondary)}tui-textfield:hover:has(input:read-only):not([multi]),tui-textfield:hover:has(textarea:read-only),tui-textfield:hover:has(select[data-mode~=readonly]){color:var(--tui-text-tertiary)}tui-textfield:before{z-index:1;margin-inline-end:.5rem}tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):before,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):after,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip]))>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield._disabled:before,tui-textfield._disabled:after,tui-textfield._disabled>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield:has(label:not(:empty))>.t-template,tui-textfield:has(label:not(:empty)) input:not([type=range]),tui-textfield:has(label:not(:empty)) select:defined,tui-textfield:has(label:not(:empty)) textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined._empty{color:transparent}tui-textfield._with-label>.t-template,tui-textfield._with-label input:not([type=range]),tui-textfield._with-label select:defined,tui-textfield._with-label textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true])._with-label>.t-template::placeholder,tui-textfield:not([data-focus=true])._with-label input:not([type=range])::placeholder,tui-textfield:not([data-focus=true])._with-label select:defined::placeholder,tui-textfield:not([data-focus=true])._with-label textarea:defined::placeholder,tui-textfield:not([data-focus=true])._with-label>.t-template._empty,tui-textfield:not([data-focus=true])._with-label input:not([type=range])._empty,tui-textfield:not([data-focus=true])._with-label select:defined._empty,tui-textfield:not([data-focus=true])._with-label textarea:defined._empty{color:transparent}tui-textfield>.t-template,tui-textfield input:defined,tui-textfield select:defined,tui-textfield textarea:defined{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;-webkit-appearance:none;appearance:none;box-sizing:border-box;border-radius:inherit;border-width:0;overscroll-behavior:none;padding-inline-start:calc(var(--t-start, 0rem) + var(--t-padding));padding-inline-end:calc(var(--t-end, 0rem) + var(--t-side) + var(--t-padding))}tui-textfield>.t-template{display:flex;align-items:center;color:var(--tui-text-primary)}tui-textfield._with-template input:not([type=range]),tui-textfield._with-template select,tui-textfield._with-template textarea{color:transparent!important}tui-textfield input:not([type=range]),tui-textfield select:defined,tui-textfield textarea:defined{pointer-events:auto;background:transparent}tui-textfield input:not([type=range]):read-only~.t-filler,tui-textfield select:defined:read-only~.t-filler,tui-textfield textarea:defined:read-only~.t-filler{display:none}tui-textfield input:not([type=range]):disabled~label,tui-textfield select:defined:disabled~label,tui-textfield textarea:defined:disabled~label,tui-textfield input:not([type=range]):disabled~.t-content,tui-textfield select:defined:disabled~.t-content,tui-textfield textarea:defined:disabled~.t-content{opacity:var(--tui-disabled-opacity)}tui-textfield input:not([type=range]):disabled~label>tui-icon,tui-textfield select:defined:disabled~label>tui-icon,tui-textfield textarea:defined:disabled~label>tui-icon,tui-textfield input:not([type=range]):disabled~.t-content>tui-icon,tui-textfield select:defined:disabled~.t-content>tui-icon,tui-textfield textarea:defined:disabled~.t-content>tui-icon{display:none}tui-textfield input:not([type=range]):-webkit-autofill~label,tui-textfield select:defined:-webkit-autofill~label,tui-textfield textarea:defined:-webkit-autofill~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown)~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown)~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown)~label{font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label{color:var(--tui-text-negative)}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear{display:flex}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]~label,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]~label,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}@supports (-webkit-touch-callout: none){tui-textfield input:not([type=range])._ios-fix,tui-textfield select:defined._ios-fix,tui-textfield textarea:defined._ios-fix{position:fixed;left:1000rem}}tui-textfield [tuiLabel][tuiLabel][tuiLabel]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;position:relative;display:block;flex:1;align-self:flex-start;font-size:inherit;line-height:var(--t-height)}tui-textfield label:defined,tui-textfield input:defined::placeholder,tui-textfield textarea:defined::placeholder,tui-textfield select:defined._empty{color:var(--tui-text-secondary)}tui-textfield select:not([data-mode~=readonly]){cursor:pointer}tui-textfield select option[value=\\\"\\\"]:disabled{color:transparent}tui-textfield select optgroup,tui-textfield select option{background-color:var(--tui-background-elevation-3)}tui-textfield select optgroup,tui-textfield select option:not(:disabled){color:var(--tui-text-primary)}tui-textfield button,tui-textfield a{pointer-events:auto}tui-textfield>.t-content{z-index:1;display:flex;block-size:var(--t-height);align-items:center;gap:inherit;margin-inline-start:auto;isolation:isolate;border-radius:inherit}tui-textfield>.t-content>tui-icon{pointer-events:auto}tui-textfield textarea~.t-content{min-inline-size:.5rem}tui-textfield .t-clear{z-index:1;display:none;pointer-events:auto}tui-textfield>.t-filler:defined{pointer-events:none;background:none;color:var(--tui-text-tertiary);opacity:1}tui-textfield [tuiFluidTypography]{font-weight:700}\\n\"] }]\n        }] });\n\n// TODO: Drop in v5 after updated Angular and hostDirectives inherit\nclass TuiTextfieldBase {\n    constructor() {\n        // TODO: refactor to signal inputs after Angular update\n        this.focused = signal(null);\n        this.control = inject(NgControl, { optional: true });\n        this.a = tuiAppearance(inject(TUI_TEXTFIELD_OPTIONS).appearance, {});\n        this.s = tuiAppearanceState(null, {});\n        this.m = tuiAppearanceMode(this.mode, {});\n        this.f = tuiAppearanceFocus(computed(() => this.focused() ?? this.textfield.focused()), {});\n        this.el = tuiInjectElement();\n        this.handlers = inject(TUI_ITEMS_HANDLERS);\n        this.textfield = inject(TuiTextfieldComponent);\n        this.readOnly = false;\n        this.invalid = null;\n        this.value = tuiValue(this.el);\n    }\n    set focusedSetter(focused) {\n        this.focused.set(focused);\n    }\n    set stateSetter(state) {\n        this.s.set(state);\n    }\n    get mode() {\n        if (this.readOnly) {\n            return 'readonly';\n        }\n        if (this.invalid === false) {\n            return 'valid';\n        }\n        if (this.invalid) {\n            return 'invalid';\n        }\n        return null;\n    }\n    // TODO: refactor to signal inputs after Angular update\n    ngOnChanges() {\n        this.m.set(this.mode);\n    }\n    setValue(value) {\n        this.el.focus();\n        this.el.select();\n        if (value == null) {\n            this.el.ownerDocument.execCommand('delete');\n        }\n        else {\n            this.el.ownerDocument.execCommand('insertText', false, this.handlers.stringify()(value));\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldBase, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTextfieldBase, isStandalone: true, inputs: { readOnly: \"readOnly\", invalid: \"invalid\", focusedSetter: [\"focused\", \"focusedSetter\"], stateSetter: [\"state\", \"stateSetter\"] }, host: { listeners: { \"input\": \"0\", \"focusin\": \"0\", \"focusout\": \"0\" }, properties: { \"id\": \"textfield.id\", \"readOnly\": \"readOnly\", \"class._empty\": \"value() === \\\"\\\"\" } }, providers: [tuiAsTextfieldAccessor(TuiTextfieldBase)], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldBase, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    providers: [tuiAsTextfieldAccessor(TuiTextfieldBase)],\n                    host: {\n                        '[id]': 'textfield.id',\n                        '[readOnly]': 'readOnly',\n                        '[class._empty]': 'value() === \"\"',\n                        '(input)': '0',\n                        '(focusin)': '0',\n                        '(focusout)': '0',\n                    },\n                }]\n        }], propDecorators: { readOnly: [{\n                type: Input\n            }], invalid: [{\n                type: Input\n            }], focusedSetter: [{\n                type: Input,\n                args: ['focused']\n            }], stateSetter: [{\n                type: Input,\n                args: ['state']\n            }] } });\nclass TuiTextfieldDirective extends TuiTextfieldBase {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTextfieldDirective, isStandalone: true, selector: \"input[tuiTextfield]:not([tuiInputCard]):not([tuiInputExpire]):not([tuiInputCVC])\", providers: [\n            tuiAsTextfieldAccessor(TuiTextfieldDirective),\n            tuiProvide(TuiTextfieldBase, TuiTextfieldDirective),\n        ], usesInheritance: true, hostDirectives: [{ directive: i1$1.TuiNativeValidator }, { directive: i2$1.TuiAppearance }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    // TODO: Remove :not in v.5\n                    selector: 'input[tuiTextfield]:not([tuiInputCard]):not([tuiInputExpire]):not([tuiInputCVC])',\n                    providers: [\n                        tuiAsTextfieldAccessor(TuiTextfieldDirective),\n                        tuiProvide(TuiTextfieldBase, TuiTextfieldDirective),\n                    ],\n                    hostDirectives: [TuiNativeValidator, TuiAppearance],\n                }]\n        }] });\nclass TuiWithTextfield {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiWithTextfield, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiWithTextfield, isStandalone: true, hostDirectives: [{ directive: TuiTextfieldDirective, inputs: [\"invalid\", \"invalid\", \"focused\", \"focused\", \"readOnly\", \"readOnly\", \"state\", \"state\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiWithTextfield, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    hostDirectives: [\n                        {\n                            directive: TuiTextfieldDirective,\n                            inputs: ['invalid', 'focused', 'readOnly', 'state'],\n                        },\n                    ],\n                }]\n        }] });\n\n/**\n * @deprecated use {@link TuiSelect} from @taiga-ui/kit instead, drop in v5\n */\nclass TuiSelect extends TuiTextfieldBase {\n    constructor() {\n        super(...arguments);\n        this.nav = inject(WA_NAVIGATOR);\n        this.doc = inject(DOCUMENT);\n        this.placeholder = '';\n    }\n    setValue(value) {\n        this.control?.control?.setValue(value);\n        this.el.dispatchEvent(new Event('input', { bubbles: true }));\n    }\n    focus() {\n        this.el.classList.add('_ios-fix');\n        this.el.focus();\n        this.el.classList.remove('_ios-fix');\n    }\n    get ariaLabel() {\n        return this.doc.querySelector(`label[for=\"${this.el.id}\"]`)\n            ? null\n            : this.el.getAttribute('aria-label') || this.placeholder;\n    }\n    get stringified() {\n        return this.handlers.stringify()(this.control?.value ?? '');\n    }\n    async onCopy() {\n        await this.nav.clipboard.writeText(this.stringified);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSelect, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSelect, isStandalone: true, selector: \"select[tuiTextfield]\", inputs: { placeholder: \"placeholder\" }, host: { listeners: { \"input\": \"0\", \"focusin\": \"0\", \"focusout\": \"0\", \"keydown.space.prevent\": \"0\", \"keydown.enter.prevent\": \"0\", \"keydown.backspace\": \"setValue(\\\"\\\")\", \"mousedown.prevent\": \"focus()\", \"keydown.control.c\": \"onCopy()\", \"keydown.meta.c\": \"onCopy()\" }, properties: { \"id\": \"textfield.id\", \"class._empty\": \"stringified === \\\"\\\"\", \"attr.aria-label\": \"ariaLabel\" } }, providers: [tuiAsTextfieldAccessor(TuiSelect)], usesInheritance: true, hostDirectives: [{ directive: i1$1.TuiNativeValidator }, { directive: i2$1.TuiAppearance }], ngImport: i0, template: \"<option\\n    *ngIf=\\\"placeholder && !stringified; else selected\\\"\\n    disabled\\n    selected\\n    value=\\\"\\\"\\n>\\n    {{ placeholder }}\\n</option>\\n<ng-template #selected>\\n    <option\\n        *ngFor=\\\"let item of [stringified]\\\"\\n        selected\\n        [value]=\\\"item\\\"\\n    >\\n        {{ item }}\\n    </option>\\n</ng-template>\\n\", dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i3$1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i3$1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], changeDetection: i0.ChangeDetectionStrategy.Default }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSelect, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'select[tuiTextfield]', imports: [CommonModule], changeDetection: ChangeDetectionStrategy.Default, providers: [tuiAsTextfieldAccessor(TuiSelect)], hostDirectives: [TuiNativeValidator, TuiAppearance], host: {\n                        '[id]': 'textfield.id',\n                        '[class._empty]': 'stringified === \"\"',\n                        '[attr.aria-label]': 'ariaLabel',\n                        '(input)': '0',\n                        '(focusin)': '0',\n                        '(focusout)': '0',\n                        '(keydown.space.prevent)': '0',\n                        '(keydown.enter.prevent)': '0',\n                        '(keydown.backspace)': 'setValue(\"\")',\n                        '(mousedown.prevent)': 'focus()',\n                        '(keydown.control.c)': 'onCopy()',\n                        '(keydown.meta.c)': 'onCopy()',\n                    }, template: \"<option\\n    *ngIf=\\\"placeholder && !stringified; else selected\\\"\\n    disabled\\n    selected\\n    value=\\\"\\\"\\n>\\n    {{ placeholder }}\\n</option>\\n<ng-template #selected>\\n    <option\\n        *ngFor=\\\"let item of [stringified]\\\"\\n        selected\\n        [value]=\\\"item\\\"\\n    >\\n        {{ item }}\\n    </option>\\n</ng-template>\\n\" }]\n        }], propDecorators: { placeholder: [{\n                type: Input\n            }] } });\n\n/*\n * Internal wrapper for polymorpheus-context\n */\nclass TuiTextfieldItemComponent {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.context = injectContext();\n        this.textfield = inject(TuiTextfieldMultiComponent);\n    }\n    get content() {\n        return this.textfield.item ?? String(this.context.$implicit.item);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldItemComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTextfieldItemComponent, isStandalone: true, selector: \"tui-textfield-item\", host: { listeners: { \"pointerdown.self.prevent\": \"0\", \"keydown.arrowLeft.prevent\": \"el.previousElementSibling?.firstChild?.focus()\", \"keydown.arrowRight.prevent\": \"el.nextElementSibling?.firstChild?.focus()\" }, properties: { \"class._string\": \"!textfield.item\" } }, ngImport: i0, template: '<ng-container *polymorpheusOutlet=\"content as text; context: context\">{{ text }}</ng-container>', isInline: true, styles: [\":host{max-inline-size:100%;flex-shrink:0;white-space:pre-wrap;text-overflow:ellipsis}:host._string{overflow:hidden}:host._string:after{content:\\\", \\\"}:host:last-of-type{max-inline-size:80%}:host-context(tui-textfield:not([data-focus=\\\"true\\\"])):last-of-type:after{display:none}\\n\"], dependencies: [{ kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }], changeDetection: i0.ChangeDetectionStrategy.Default }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldItemComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-textfield-item', imports: [PolymorpheusOutlet], template: '<ng-container *polymorpheusOutlet=\"content as text; context: context\">{{ text }}</ng-container>', changeDetection: ChangeDetectionStrategy.Default, host: {\n                        '[class._string]': '!textfield.item',\n                        '(pointerdown.self.prevent)': '0',\n                        '(keydown.arrowLeft.prevent)': 'el.previousElementSibling?.firstChild?.focus()',\n                        '(keydown.arrowRight.prevent)': 'el.nextElementSibling?.firstChild?.focus()',\n                    }, styles: [\":host{max-inline-size:100%;flex-shrink:0;white-space:pre-wrap;text-overflow:ellipsis}:host._string{overflow:hidden}:host._string:after{content:\\\", \\\"}:host:last-of-type{max-inline-size:80%}:host-context(tui-textfield:not([data-focus=\\\"true\\\"])):last-of-type:after{display:none}\\n\"] }]\n        }] });\n\nclass TuiTextfieldMultiComponent extends TuiTextfieldBaseComponent {\n    constructor() {\n        super(...arguments);\n        this.height = signal(null);\n        this.handlers = inject(TUI_ITEMS_HANDLERS$1);\n        this.component = new PolymorpheusComponent(TuiTextfieldItemComponent);\n        this.sub = fromEvent(this.el, 'scroll')\n            .pipe(filter(() => this.rows === 1), tuiZonefree(), takeUntilDestroyed())\n            .subscribe(() => {\n            this.el.style.setProperty('--t-scroll', tuiPx(-1 * this.el.scrollLeft));\n        });\n        this.rows = 100;\n    }\n    handleOption(option) {\n        this.accessor?.setValue(tuiArrayToggle(this.control?.value ?? [], option, this.handlers.identityMatcher()));\n    }\n    onItems({ target }) {\n        const height = this.rows > 1 && this.control?.value?.length\n            ? (target.querySelector('tui-textfield-item')?.clientHeight ?? 0)\n            : null;\n        if (height !== 0) {\n            this.height.set(height);\n        }\n    }\n    onLeft(event) {\n        if (this.value() || !tuiIsElement(event.currentTarget)) {\n            return;\n        }\n        event.preventDefault();\n        event.currentTarget.previousElementSibling?.firstElementChild?.focus();\n    }\n    onClick(target) {\n        if (target !== this.el &&\n            this.el.matches('[tuiChevron]') &&\n            !target.matches('input:read-only')) {\n            this.open.update((open) => !open);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldMultiComponent, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTextfieldMultiComponent, isStandalone: true, selector: \"tui-textfield[multi]\", inputs: { rows: \"rows\" }, host: { listeners: { \"click\": \"onClick($event.target)\", \"tuiActiveZoneChange\": \"!$event && (el.scrollTo({left: 0}) || cva?.onTouched())\", \"click.self.prevent\": \"0\", \"pointerdown.self.prevent\": \"onIconClick()\", \"scroll.capture.zoneless\": \"onScroll($event.target)\" }, properties: { \"attr.data-state\": \"control?.disabled ? \\\"disabled\\\" : null\", \"class._empty\": \"!control?.value?.length\", \"style.--t-item-height.px\": \"height()\", \"style.--t-rows\": \"rows\", \"attr.data-size\": \"options.size()\", \"class._with-label\": \"hasLabel\", \"class._with-template\": \"content && control?.value != null\", \"class._disabled\": \"input?.nativeElement?.disabled\" }, classAttribute: \"tui-interactive\" }, providers: [\n            tuiButtonOptionsProvider({ size: 'xs', appearance: 'icon' }),\n            tuiAsDataListHost(TuiTextfieldMultiComponent),\n            tuiProvide(TuiTextfieldComponent, TuiTextfieldMultiComponent),\n            tuiProvide(TUI_SCROLL_REF, ElementRef),\n        ], queries: [{ propertyName: \"item\", first: true, predicate: TuiItem, descendants: true, read: TemplateRef }], usesInheritance: true, hostDirectives: [{ directive: i1.TuiDropdownFixed }, { directive: i1.TuiDropdownDirective }, { directive: i1.TuiWithDropdownOpen }, { directive: TuiWithTextfieldDropdown }, { directive: i2.TuiWithIcons }, { directive: i4$1.TuiWithItemsHandlers }, { directive: i4.TuiWithOptionContent }, { directive: i4$1.TuiWithAppearance }], ngImport: i0, template: \"<tui-scroll-controls\\n    *ngIf=\\\"rows > 1\\\"\\n    class=\\\"t-scrollbar\\\"\\n/>\\n\\n<div\\n    class=\\\"t-items\\\"\\n    [class.t-items_horizontal]=\\\"rows === 1\\\"\\n    (click)=\\\"input?.nativeElement?.focus()\\\"\\n    (pointerdown.self.zoneless.prevent)=\\\"(0)\\\"\\n    (waResizeObserver)=\\\"$event[0] && onItems($event[0])\\\"\\n>\\n    <ng-content select=\\\"label\\\" />\\n    <ng-template\\n        *ngFor=\\\"let item of control?.value; let index = index\\\"\\n        [polymorpheusOutlet]=\\\"component\\\"\\n        [polymorpheusOutletContext]=\\\"{$implicit: {item, index}}\\\"\\n    />\\n    <span\\n        class=\\\"t-input\\\"\\n        (keydown.arrowLeft)=\\\"onLeft($event)\\\"\\n    >\\n        <ng-content select=\\\"input\\\" />\\n        <span\\n            class=\\\"t-ghost\\\"\\n            [attr.data-placeholder]=\\\"input?.nativeElement?.placeholder\\\"\\n            [textContent]=\\\"\\n                value() && value().length < (input?.nativeElement?.placeholder?.length || 0)\\n                    ? input?.nativeElement?.placeholder\\n                    : value()\\n            \\\"\\n        ></span>\\n        <input\\n            *ngIf=\\\"showFiller()\\\"\\n            aria-hidden=\\\"true\\\"\\n            disabled\\n            class=\\\"t-filler\\\"\\n            [value]=\\\"computedFiller()\\\"\\n        />\\n    </span>\\n</div>\\n\\n<span\\n    class=\\\"t-content\\\"\\n    (click.stop)=\\\"input?.nativeElement?.focus()\\\"\\n    (pointerdown.zoneless.prevent)=\\\"(0)\\\"\\n    (waResizeObserver)=\\\"$event[0] && onResize($event[0])\\\"\\n>\\n    <ng-content />\\n    <button\\n        *ngIf=\\\"options.cleaner()\\\"\\n        appearance=\\\"icon\\\"\\n        size=\\\"xs\\\"\\n        tabindex=\\\"-1\\\"\\n        tuiIconButton\\n        type=\\\"button\\\"\\n        class=\\\"t-clear\\\"\\n        [iconStart]=\\\"icons.close\\\"\\n        (click)=\\\"accessor?.setValue([])\\\"\\n    >\\n        {{ clear() }}\\n    </button>\\n    <ng-container #vcr />\\n    <ng-content select=\\\"tui-icon\\\" />\\n</span>\\n\\n<span\\n    *ngIf=\\\"control?.value != null\\\"\\n    class=\\\"t-template\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: control?.value}\\\">\\n        {{ text }}\\n    </ng-container>\\n</span>\\n\", styles: [\"tui-textfield{scrollbar-width:none;-ms-overflow-style:none;transition-property:color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;--t-height: var(--tui-height-l);--t-padding: var(--tui-padding-l);--t-label: -.7em;--t-end: 0rem;--t-start: 0rem;position:relative;display:flex;flex-wrap:wrap;align-items:center;cursor:pointer;min-block-size:var(--t-height);color:var(--tui-text-tertiary);padding:0 var(--t-padding);border-radius:var(--tui-radius-l);font:var(--tui-font-text-ui-m);box-sizing:border-box;gap:0 .25rem;isolation:isolate}tui-textfield::-webkit-scrollbar,tui-textfield::-webkit-scrollbar-thumb{display:none}tui-textfield[style*=\\\"--t-icon-start:\\\"]{--t-start: 2.25rem}tui-textfield[style*=\\\"--t-icon-end:\\\"]{--t-end: 2.25rem}tui-textfield[tuiIcons]:after{position:relative;block-size:auto;align-self:stretch;border-inline-start:var(--t-padding) solid transparent;border-inline-end:var(--t-padding) solid transparent;margin:0 calc(-1 * var(--t-padding))}tui-textfield::-webkit-resizer{border:.25rem solid transparent;inline-size:.5rem;block-size:.5rem;box-sizing:content-box;color:var(--tui-text-tertiary);background:linear-gradient(-45deg,transparent,transparent .125rem,currentColor .125rem,currentColor .1875rem,transparent .1875rem,transparent .25rem,currentColor .25rem,currentColor .3125rem,transparent .35rem);background-clip:content-box}tui-textfield label,tui-textfield>.t-content,tui-textfield>.t-template{pointer-events:none}tui-textfield input,tui-textfield select,tui-textfield textarea{font:var(--tui-font-text-ui-m);resize:none;outline:none;padding-block-start:1.125rem;padding-block-end:1.125rem}tui-textfield input[inputmode=none],tui-textfield select[inputmode=none],tui-textfield textarea[inputmode=none]{caret-color:transparent}tui-textfield[data-size=s]{--t-height: var(--tui-height-s);--t-padding: var(--tui-padding-s);border-radius:var(--tui-radius-m);gap:0;font:var(--tui-font-text-ui-s)}tui-textfield[data-size=s][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.25rem}tui-textfield[data-size=s][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.25rem}tui-textfield[data-size=s]:before{margin-inline-end:.5rem;margin-inline-start:-.125rem;font-size:1rem}tui-textfield[data-size=s]:after{margin-inline-end:calc(-.125rem - var(--t-padding));margin-inline-start:calc(.625rem - var(--t-padding));font-size:1rem}tui-textfield[data-size=s] input,tui-textfield[data-size=s] select,tui-textfield[data-size=s] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.5rem;padding-block-end:.5rem}tui-textfield[data-size=s]>.t-content{margin-inline-end:-.375rem}tui-textfield[data-size=m]{--t-height: var(--tui-height-m);--t-padding: var(--tui-padding-m);border-radius:var(--tui-radius-m);font:var(--tui-font-text-ui-s)}tui-textfield[data-size=m][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.75rem}tui-textfield[data-size=m][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.75rem}tui-textfield[data-size=m]:before{margin-inline-start:-.125rem;margin-inline-end:.125rem}tui-textfield[data-size=m]:after{margin-inline-start:calc(.25rem - var(--t-padding));margin-inline-end:calc(-.125rem - var(--t-padding))}tui-textfield[data-size=m] input,tui-textfield[data-size=m] select,tui-textfield[data-size=m] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.875rem;padding-block-end:.875rem}tui-textfield[data-size=m]>.t-content{margin-inline-end:-.125rem}tui-textfield[data-size=l]{--t-label: -.7rem}tui-textfield:hover{color:var(--tui-text-secondary)}tui-textfield:hover:has(input:read-only):not([multi]),tui-textfield:hover:has(textarea:read-only),tui-textfield:hover:has(select[data-mode~=readonly]){color:var(--tui-text-tertiary)}tui-textfield:before{z-index:1;margin-inline-end:.5rem}tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):before,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):after,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip]))>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield._disabled:before,tui-textfield._disabled:after,tui-textfield._disabled>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield:has(label:not(:empty))>.t-template,tui-textfield:has(label:not(:empty)) input:not([type=range]),tui-textfield:has(label:not(:empty)) select:defined,tui-textfield:has(label:not(:empty)) textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined._empty{color:transparent}tui-textfield._with-label>.t-template,tui-textfield._with-label input:not([type=range]),tui-textfield._with-label select:defined,tui-textfield._with-label textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true])._with-label>.t-template::placeholder,tui-textfield:not([data-focus=true])._with-label input:not([type=range])::placeholder,tui-textfield:not([data-focus=true])._with-label select:defined::placeholder,tui-textfield:not([data-focus=true])._with-label textarea:defined::placeholder,tui-textfield:not([data-focus=true])._with-label>.t-template._empty,tui-textfield:not([data-focus=true])._with-label input:not([type=range])._empty,tui-textfield:not([data-focus=true])._with-label select:defined._empty,tui-textfield:not([data-focus=true])._with-label textarea:defined._empty{color:transparent}tui-textfield>.t-template,tui-textfield input:defined,tui-textfield select:defined,tui-textfield textarea:defined{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;-webkit-appearance:none;appearance:none;box-sizing:border-box;border-radius:inherit;border-width:0;overscroll-behavior:none;padding-inline-start:calc(var(--t-start, 0rem) + var(--t-padding));padding-inline-end:calc(var(--t-end, 0rem) + var(--t-side) + var(--t-padding))}tui-textfield>.t-template{display:flex;align-items:center;color:var(--tui-text-primary)}tui-textfield._with-template input:not([type=range]),tui-textfield._with-template select,tui-textfield._with-template textarea{color:transparent!important}tui-textfield input:not([type=range]),tui-textfield select:defined,tui-textfield textarea:defined{pointer-events:auto;background:transparent}tui-textfield input:not([type=range]):read-only~.t-filler,tui-textfield select:defined:read-only~.t-filler,tui-textfield textarea:defined:read-only~.t-filler{display:none}tui-textfield input:not([type=range]):disabled~label,tui-textfield select:defined:disabled~label,tui-textfield textarea:defined:disabled~label,tui-textfield input:not([type=range]):disabled~.t-content,tui-textfield select:defined:disabled~.t-content,tui-textfield textarea:defined:disabled~.t-content{opacity:var(--tui-disabled-opacity)}tui-textfield input:not([type=range]):disabled~label>tui-icon,tui-textfield select:defined:disabled~label>tui-icon,tui-textfield textarea:defined:disabled~label>tui-icon,tui-textfield input:not([type=range]):disabled~.t-content>tui-icon,tui-textfield select:defined:disabled~.t-content>tui-icon,tui-textfield textarea:defined:disabled~.t-content>tui-icon{display:none}tui-textfield input:not([type=range]):-webkit-autofill~label,tui-textfield select:defined:-webkit-autofill~label,tui-textfield textarea:defined:-webkit-autofill~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown)~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown)~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown)~label{font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label{color:var(--tui-text-negative)}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear{display:flex}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]~label,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]~label,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}@supports (-webkit-touch-callout: none){tui-textfield input:not([type=range])._ios-fix,tui-textfield select:defined._ios-fix,tui-textfield textarea:defined._ios-fix{position:fixed;left:1000rem}}tui-textfield [tuiLabel][tuiLabel][tuiLabel]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;position:relative;display:block;flex:1;align-self:flex-start;font-size:inherit;line-height:var(--t-height)}tui-textfield label:defined,tui-textfield input:defined::placeholder,tui-textfield textarea:defined::placeholder,tui-textfield select:defined._empty{color:var(--tui-text-secondary)}tui-textfield select:not([data-mode~=readonly]){cursor:pointer}tui-textfield select option[value=\\\"\\\"]:disabled{color:transparent}tui-textfield select optgroup,tui-textfield select option{background-color:var(--tui-background-elevation-3)}tui-textfield select optgroup,tui-textfield select option:not(:disabled){color:var(--tui-text-primary)}tui-textfield button,tui-textfield a{pointer-events:auto}tui-textfield>.t-content{z-index:1;display:flex;block-size:var(--t-height);align-items:center;gap:inherit;margin-inline-start:auto;isolation:isolate;border-radius:inherit}tui-textfield>.t-content>tui-icon{pointer-events:auto}tui-textfield textarea~.t-content{min-inline-size:.5rem}tui-textfield .t-clear{z-index:1;display:none;pointer-events:auto}tui-textfield>.t-filler:defined{pointer-events:none;background:none;color:var(--tui-text-tertiary);opacity:1}tui-textfield [tuiFluidTypography]{font-weight:700}tui-textfield[multi][multi]{flex-wrap:nowrap;overflow:scroll;align-items:stretch;cursor:text;gap:0;max-block-size:calc(var(--t-vertical) * 2 + var(--t-item-height) * var(--t-rows));overscroll-behavior:none;scroll-behavior:var(--tui-scroll-behavior)}tui-textfield[multi][multi]:before,tui-textfield[multi][multi]:after{position:sticky;top:0;left:0;block-size:var(--t-height)}@supports (inset-inline-start: 0){tui-textfield[multi][multi]:before,tui-textfield[multi][multi]:after{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]:has([tuiSelectLike]){cursor:pointer}tui-textfield[multi][multi][tuiChevron]:after{top:.375rem;block-size:calc(var(--t-height) - .75rem)}tui-textfield[multi][multi]>.t-scrollbar{transform:translate(var(--t-padding));margin-inline-start:calc(-1 * var(--t-start));margin-inline-end:calc(1px - 100% + var(--t-start))}tui-textfield[multi][multi]>.t-scrollbar .t-bar_horizontal{display:none}tui-textfield[multi][multi]>.t-items{position:sticky;left:var(--t-start);display:flex;min-inline-size:0;min-block-size:-webkit-fit-content;min-block-size:-moz-fit-content;min-block-size:fit-content;flex:1;align-items:center;flex-wrap:wrap;padding:var(--t-vertical) 0}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-items{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]>.t-items:after{content:\\\"\\\";min-inline-size:1px;min-block-size:1px}tui-textfield[multi][multi]>.t-items_horizontal{clip-path:inset(0 0 0 calc(var(--t-start) / 2 - var(--t-padding) - .25rem));flex-wrap:nowrap}tui-textfield[multi][multi]>.t-items_horizontal:dir(rtl){clip-path:inset(0 calc(var(--t-start) / 2 - var(--t-padding) - .25rem) 0 0)}tui-textfield[multi][multi]>.t-items_horizontal>.t-input{padding-inline-end:calc(var(--t-side) + var(--t-end) + var(--t-padding))}tui-textfield[multi][multi]>.t-items:not(tui-textfield[multi][multi]>.t-items_horizontal){--t-scroll: 0}tui-textfield[multi][multi]>.t-items>label[tuiLabel]{position:absolute;top:0;inline-size:100%}tui-textfield[multi][multi]>.t-items>.t-input{position:relative;display:flex;align-items:center;flex:1;max-inline-size:100%;transform:translate(var(--t-scroll))}tui-textfield[multi][multi]>.t-items>.t-input input{position:absolute;left:0;inline-size:100%;padding:0}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-items>.t-input input{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]>.t-items>.t-input .t-ghost{visibility:hidden;white-space:pre;text-overflow:clip;padding-inline-end:.125rem;min-block-size:var(--t-item-height, 1em)}tui-textfield[multi][multi]>.t-items>.t-input .t-ghost:empty:before{content:attr(data-placeholder)}tui-textfield[multi][multi]>.t-items>.t-input .t-filler{position:absolute;left:0;color:var(--tui-text-tertiary)}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-items>.t-input .t-filler{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]>.t-content{position:sticky;top:0;left:calc(100% - var(--t-side) - var(--t-end) + var(--t-padding) - var(--t-offset));margin:0;gap:.25rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-content{left:unset;inset-inline-start:calc(100% - var(--t-side) - var(--t-end) + var(--t-padding) - var(--t-offset))}}tui-textfield[multi][multi]>.t-content .t-clear{display:flex}tui-textfield[multi][multi][data-mode~=invalid]:not([data-mode~=readonly])>.t-items>[tuiLabel]{color:var(--tui-text-negative)}tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly])>.t-items>[tuiLabel]{color:var(--tui-text-primary)}tui-textfield[multi][multi][data-mode~=readonly]>.t-content .t-clear,tui-textfield[multi][multi]._disabled>.t-content .t-clear,tui-textfield[multi][multi]._empty>.t-content .t-clear{display:none}tui-textfield[multi][multi]>.t-items input:not(:focus)::placeholder,tui-textfield[multi][multi][data-mode~=readonly]>.t-items input::placeholder{opacity:0}tui-textfield[multi][multi]>.t-items input:not(:focus)~.t-ghost:before,tui-textfield[multi][multi][data-mode~=readonly]>.t-items input~.t-ghost:before{display:none}tui-textfield[multi][multi]:not([data-mode~=readonly])._empty>.t-items input::placeholder,tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly]):not(:focus-within)>.t-items input::placeholder{opacity:1}tui-textfield[multi][multi]:not([data-mode~=readonly])._empty>.t-items .t-ghost:before,tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly]):not(:focus-within)>.t-items .t-ghost:before{display:inline}tui-textfield[multi][multi]:not(._empty)>.t-items [tuiLabel],tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly])>.t-items [tuiLabel]{font-size:.83em;transform:translateY(var(--t-label))}tui-textfield[multi][multi][data-size=l]{--t-vertical: .625rem;--t-offset: calc(1rem - var(--t-end) / 4.5)}tui-textfield[multi][multi][data-size=l]:before{margin-inline-end:.75rem}tui-textfield[multi][multi][data-size=l]:after{left:calc(100% - var(--t-end) - .375rem + .25 * (1em - 1rem));margin-inline-end:calc(.5 * (1.5rem - 1em) - var(--t-padding));margin-inline-start:-.75rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=l]:after{left:unset;inset-inline-start:calc(100% - var(--t-end) - .375rem + .25 * (1em - 1rem))}}tui-textfield[multi][multi][data-size=l]._with-label{--t-vertical: 1.125rem}tui-textfield[multi][multi][data-size=l]._with-label>.t-items{padding:1.75rem 0 .5rem}tui-textfield[multi][multi][data-size=m]{--t-vertical: .5rem;--t-offset: calc(.75rem + var(--t-end) / 14)}tui-textfield[multi][multi][data-size=m]:before{left:-.125rem;margin-inline-end:.375rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=m]:before{left:unset;inset-inline-start:-.125rem}}tui-textfield[multi][multi][data-size=m]:after{left:calc(100% - var(--t-end) - .25rem);margin-inline-start:-.125rem;border-width:.625rem;margin-inline-end:calc(.5 * (1.5rem - 1em) - var(--t-padding))}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=m]:after{left:unset;inset-inline-start:calc(100% - var(--t-end) - .25rem)}}tui-textfield[multi][multi][data-size=m]._with-label{--t-vertical: .875rem}tui-textfield[multi][multi][data-size=m]._with-label>.t-items{padding:1.375rem 0 .375rem}tui-textfield[multi][multi][data-size=m]>.t-content{transform:translate(.125rem)}tui-textfield[multi][multi][data-size=s]{--t-vertical: .125rem;--t-offset: calc(.625rem + var(--t-end) / 10)}tui-textfield[multi][multi][data-size=s]:before{left:-.125rem;margin-inline-end:.375rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=s]:before{left:unset;inset-inline-start:-.125rem}}tui-textfield[multi][multi][data-size=s]:after{left:calc(100% - var(--t-end) - .125rem);border-width:.5rem;margin-inline-end:calc(-1 * var(--t-padding))}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=s]:after{left:unset;inset-inline-start:calc(100% - var(--t-end) - .125rem)}}tui-textfield[multi][multi][data-size=s]>.t-content{gap:0;transform:translate(calc(.375rem - var(--t-end) / 10))}tui-textfield[multi][multi]:focus-visible:not([data-focus=false]) input::placeholder,tui-textfield[multi][multi]:focus-visible:not([data-focus=false]) input._empty{color:var(--tui-text-tertiary)}tui-textfield[multi][multi][data-focus=true] input::placeholder,tui-textfield[multi][multi][data-focus=true] input._empty{color:var(--tui-text-tertiary)}tui-textfield[multi][multi][tuiWrapper]:not(._focused):has(:focus-visible) input::placeholder,tui-textfield[multi][multi][tuiWrapper]._focused input::placeholder,tui-textfield[multi][multi][tuiWrapper]:not(._focused):has(:focus-visible) input._empty,tui-textfield[multi][multi][tuiWrapper]._focused input._empty{color:var(--tui-text-tertiary)}tui-textfield[multi][multi] tui-textfield-item{transform:translate(var(--t-scroll))}\\n\"], dependencies: [{ kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"component\", type: TuiScrollControls, selector: \"tui-scroll-controls\" }, { kind: \"directive\", type: WaResizeObserver, selector: \"[waResizeObserver]\", inputs: [\"box\"], outputs: [\"waResizeObserver\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldMultiComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-textfield[multi]', imports: [\n                        NgForOf,\n                        NgIf,\n                        PolymorpheusOutlet,\n                        TuiButton,\n                        TuiScrollControls,\n                        WaResizeObserver,\n                    ], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        tuiButtonOptionsProvider({ size: 'xs', appearance: 'icon' }),\n                        tuiAsDataListHost(TuiTextfieldMultiComponent),\n                        tuiProvide(TuiTextfieldComponent, TuiTextfieldMultiComponent),\n                        tuiProvide(TUI_SCROLL_REF, ElementRef),\n                    ], hostDirectives: [\n                        TuiDropdownFixed,\n                        TuiDropdownDirective,\n                        TuiWithDropdownOpen,\n                        TuiWithTextfieldDropdown,\n                        TuiWithIcons,\n                        TuiWithItemsHandlers$1,\n                        TuiWithOptionContent,\n                        TuiWithAppearance,\n                    ], host: {\n                        class: 'tui-interactive',\n                        '[attr.data-state]': 'control?.disabled ? \"disabled\" : null',\n                        '[class._empty]': '!control?.value?.length',\n                        '[style.--t-item-height.px]': 'height()',\n                        '[style.--t-rows]': 'rows',\n                        '(click)': 'onClick($event.target)',\n                        '(tuiActiveZoneChange)': '!$event && (el.scrollTo({left: 0}) || cva?.onTouched())',\n                        // TODO: Remove in v5\n                        '[attr.data-size]': 'options.size()',\n                        '[class._with-label]': 'hasLabel',\n                        '[class._with-template]': 'content && control?.value != null',\n                        '[class._disabled]': 'input?.nativeElement?.disabled',\n                        '(click.self.prevent)': '0',\n                        '(pointerdown.self.prevent)': 'onIconClick()',\n                        '(scroll.capture.zoneless)': 'onScroll($event.target)',\n                    }, template: \"<tui-scroll-controls\\n    *ngIf=\\\"rows > 1\\\"\\n    class=\\\"t-scrollbar\\\"\\n/>\\n\\n<div\\n    class=\\\"t-items\\\"\\n    [class.t-items_horizontal]=\\\"rows === 1\\\"\\n    (click)=\\\"input?.nativeElement?.focus()\\\"\\n    (pointerdown.self.zoneless.prevent)=\\\"(0)\\\"\\n    (waResizeObserver)=\\\"$event[0] && onItems($event[0])\\\"\\n>\\n    <ng-content select=\\\"label\\\" />\\n    <ng-template\\n        *ngFor=\\\"let item of control?.value; let index = index\\\"\\n        [polymorpheusOutlet]=\\\"component\\\"\\n        [polymorpheusOutletContext]=\\\"{$implicit: {item, index}}\\\"\\n    />\\n    <span\\n        class=\\\"t-input\\\"\\n        (keydown.arrowLeft)=\\\"onLeft($event)\\\"\\n    >\\n        <ng-content select=\\\"input\\\" />\\n        <span\\n            class=\\\"t-ghost\\\"\\n            [attr.data-placeholder]=\\\"input?.nativeElement?.placeholder\\\"\\n            [textContent]=\\\"\\n                value() && value().length < (input?.nativeElement?.placeholder?.length || 0)\\n                    ? input?.nativeElement?.placeholder\\n                    : value()\\n            \\\"\\n        ></span>\\n        <input\\n            *ngIf=\\\"showFiller()\\\"\\n            aria-hidden=\\\"true\\\"\\n            disabled\\n            class=\\\"t-filler\\\"\\n            [value]=\\\"computedFiller()\\\"\\n        />\\n    </span>\\n</div>\\n\\n<span\\n    class=\\\"t-content\\\"\\n    (click.stop)=\\\"input?.nativeElement?.focus()\\\"\\n    (pointerdown.zoneless.prevent)=\\\"(0)\\\"\\n    (waResizeObserver)=\\\"$event[0] && onResize($event[0])\\\"\\n>\\n    <ng-content />\\n    <button\\n        *ngIf=\\\"options.cleaner()\\\"\\n        appearance=\\\"icon\\\"\\n        size=\\\"xs\\\"\\n        tabindex=\\\"-1\\\"\\n        tuiIconButton\\n        type=\\\"button\\\"\\n        class=\\\"t-clear\\\"\\n        [iconStart]=\\\"icons.close\\\"\\n        (click)=\\\"accessor?.setValue([])\\\"\\n    >\\n        {{ clear() }}\\n    </button>\\n    <ng-container #vcr />\\n    <ng-content select=\\\"tui-icon\\\" />\\n</span>\\n\\n<span\\n    *ngIf=\\\"control?.value != null\\\"\\n    class=\\\"t-template\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: control?.value}\\\">\\n        {{ text }}\\n    </ng-container>\\n</span>\\n\", styles: [\"tui-textfield{scrollbar-width:none;-ms-overflow-style:none;transition-property:color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;--t-height: var(--tui-height-l);--t-padding: var(--tui-padding-l);--t-label: -.7em;--t-end: 0rem;--t-start: 0rem;position:relative;display:flex;flex-wrap:wrap;align-items:center;cursor:pointer;min-block-size:var(--t-height);color:var(--tui-text-tertiary);padding:0 var(--t-padding);border-radius:var(--tui-radius-l);font:var(--tui-font-text-ui-m);box-sizing:border-box;gap:0 .25rem;isolation:isolate}tui-textfield::-webkit-scrollbar,tui-textfield::-webkit-scrollbar-thumb{display:none}tui-textfield[style*=\\\"--t-icon-start:\\\"]{--t-start: 2.25rem}tui-textfield[style*=\\\"--t-icon-end:\\\"]{--t-end: 2.25rem}tui-textfield[tuiIcons]:after{position:relative;block-size:auto;align-self:stretch;border-inline-start:var(--t-padding) solid transparent;border-inline-end:var(--t-padding) solid transparent;margin:0 calc(-1 * var(--t-padding))}tui-textfield::-webkit-resizer{border:.25rem solid transparent;inline-size:.5rem;block-size:.5rem;box-sizing:content-box;color:var(--tui-text-tertiary);background:linear-gradient(-45deg,transparent,transparent .125rem,currentColor .125rem,currentColor .1875rem,transparent .1875rem,transparent .25rem,currentColor .25rem,currentColor .3125rem,transparent .35rem);background-clip:content-box}tui-textfield label,tui-textfield>.t-content,tui-textfield>.t-template{pointer-events:none}tui-textfield input,tui-textfield select,tui-textfield textarea{font:var(--tui-font-text-ui-m);resize:none;outline:none;padding-block-start:1.125rem;padding-block-end:1.125rem}tui-textfield input[inputmode=none],tui-textfield select[inputmode=none],tui-textfield textarea[inputmode=none]{caret-color:transparent}tui-textfield[data-size=s]{--t-height: var(--tui-height-s);--t-padding: var(--tui-padding-s);border-radius:var(--tui-radius-m);gap:0;font:var(--tui-font-text-ui-s)}tui-textfield[data-size=s][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.25rem}tui-textfield[data-size=s][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.25rem}tui-textfield[data-size=s]:before{margin-inline-end:.5rem;margin-inline-start:-.125rem;font-size:1rem}tui-textfield[data-size=s]:after{margin-inline-end:calc(-.125rem - var(--t-padding));margin-inline-start:calc(.625rem - var(--t-padding));font-size:1rem}tui-textfield[data-size=s] input,tui-textfield[data-size=s] select,tui-textfield[data-size=s] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.5rem;padding-block-end:.5rem}tui-textfield[data-size=s]>.t-content{margin-inline-end:-.375rem}tui-textfield[data-size=m]{--t-height: var(--tui-height-m);--t-padding: var(--tui-padding-m);border-radius:var(--tui-radius-m);font:var(--tui-font-text-ui-s)}tui-textfield[data-size=m][style*=\\\"--t-icon-start:\\\"]{--t-start: 1.75rem}tui-textfield[data-size=m][style*=\\\"--t-icon-end:\\\"]{--t-end: 1.75rem}tui-textfield[data-size=m]:before{margin-inline-start:-.125rem;margin-inline-end:.125rem}tui-textfield[data-size=m]:after{margin-inline-start:calc(.25rem - var(--t-padding));margin-inline-end:calc(-.125rem - var(--t-padding))}tui-textfield[data-size=m] input,tui-textfield[data-size=m] select,tui-textfield[data-size=m] textarea{font:var(--tui-font-text-ui-s);padding-block-start:.875rem;padding-block-end:.875rem}tui-textfield[data-size=m]>.t-content{margin-inline-end:-.125rem}tui-textfield[data-size=l]{--t-label: -.7rem}tui-textfield:hover{color:var(--tui-text-secondary)}tui-textfield:hover:has(input:read-only):not([multi]),tui-textfield:hover:has(textarea:read-only),tui-textfield:hover:has(select[data-mode~=readonly]){color:var(--tui-text-tertiary)}tui-textfield:before{z-index:1;margin-inline-end:.5rem}tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):before,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip])):after,tui-textfield:has(:disabled:not(.t-filler,button,option,[tuiChip]))>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield._disabled:before,tui-textfield._disabled:after,tui-textfield._disabled>.t-template{opacity:var(--tui-disabled-opacity)}tui-textfield:has(label:not(:empty))>.t-template,tui-textfield:has(label:not(:empty)) input:not([type=range]),tui-textfield:has(label:not(:empty)) select:defined,tui-textfield:has(label:not(:empty)) textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined::placeholder,tui-textfield:not([data-focus=true]):has(label:not(:empty))>.t-template._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) input:not([type=range])._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) select:defined._empty,tui-textfield:not([data-focus=true]):has(label:not(:empty)) textarea:defined._empty{color:transparent}tui-textfield._with-label>.t-template,tui-textfield._with-label input:not([type=range]),tui-textfield._with-label select:defined,tui-textfield._with-label textarea:defined{padding-block-start:calc(var(--t-height) / 3);padding-block-end:0}tui-textfield:not([data-focus=true])._with-label>.t-template::placeholder,tui-textfield:not([data-focus=true])._with-label input:not([type=range])::placeholder,tui-textfield:not([data-focus=true])._with-label select:defined::placeholder,tui-textfield:not([data-focus=true])._with-label textarea:defined::placeholder,tui-textfield:not([data-focus=true])._with-label>.t-template._empty,tui-textfield:not([data-focus=true])._with-label input:not([type=range])._empty,tui-textfield:not([data-focus=true])._with-label select:defined._empty,tui-textfield:not([data-focus=true])._with-label textarea:defined._empty{color:transparent}tui-textfield>.t-template,tui-textfield input:defined,tui-textfield select:defined,tui-textfield textarea:defined{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;-webkit-appearance:none;appearance:none;box-sizing:border-box;border-radius:inherit;border-width:0;overscroll-behavior:none;padding-inline-start:calc(var(--t-start, 0rem) + var(--t-padding));padding-inline-end:calc(var(--t-end, 0rem) + var(--t-side) + var(--t-padding))}tui-textfield>.t-template{display:flex;align-items:center;color:var(--tui-text-primary)}tui-textfield._with-template input:not([type=range]),tui-textfield._with-template select,tui-textfield._with-template textarea{color:transparent!important}tui-textfield input:not([type=range]),tui-textfield select:defined,tui-textfield textarea:defined{pointer-events:auto;background:transparent}tui-textfield input:not([type=range]):read-only~.t-filler,tui-textfield select:defined:read-only~.t-filler,tui-textfield textarea:defined:read-only~.t-filler{display:none}tui-textfield input:not([type=range]):disabled~label,tui-textfield select:defined:disabled~label,tui-textfield textarea:defined:disabled~label,tui-textfield input:not([type=range]):disabled~.t-content,tui-textfield select:defined:disabled~.t-content,tui-textfield textarea:defined:disabled~.t-content{opacity:var(--tui-disabled-opacity)}tui-textfield input:not([type=range]):disabled~label>tui-icon,tui-textfield select:defined:disabled~label>tui-icon,tui-textfield textarea:defined:disabled~label>tui-icon,tui-textfield input:not([type=range]):disabled~.t-content>tui-icon,tui-textfield select:defined:disabled~.t-content>tui-icon,tui-textfield textarea:defined:disabled~.t-content>tui-icon{display:none}tui-textfield input:not([type=range]):-webkit-autofill~label,tui-textfield select:defined:-webkit-autofill~label,tui-textfield textarea:defined:-webkit-autofill~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown)~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown)~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown)~label{font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:-webkit-autofill:not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled)[data-mode~=invalid]~label,tui-textfield input:not([type=range]):-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:-webkit-autofill:invalid:not(:disabled):not([data-mode])~label,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield select:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):invalid:not(:disabled):not([data-mode])~label{color:var(--tui-text-negative)}tui-textfield input:not([type=range]):-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:-webkit-autofill:not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield input:not([type=range]):not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield select:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear,tui-textfield textarea:defined:not(._empty):not(:placeholder-shown):not(:disabled):not([data-mode~=readonly])~.t-content .t-clear{display:flex}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield select:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label,tui-textfield textarea:defined:not([data-mode~=readonly]):focus-visible:not([data-focus=false])~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[data-focus=true]~label,tui-textfield select:defined:not([data-mode~=readonly])[data-focus=true]~label,tui-textfield textarea:defined:not([data-mode~=readonly])[data-focus=true]~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused::placeholder,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)._empty,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused._empty{color:var(--tui-text-tertiary)}tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]:not(._focused):has(:focus-visible)~label,tui-textfield input:not([type=range]):not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield select:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label,tui-textfield textarea:defined:not([data-mode~=readonly])[tuiWrapper]._focused~label{color:var(--tui-text-primary)!important;font-size:.83em;transform:translateY(var(--t-label))}@supports (-webkit-touch-callout: none){tui-textfield input:not([type=range])._ios-fix,tui-textfield select:defined._ios-fix,tui-textfield textarea:defined._ios-fix{position:fixed;left:1000rem}}tui-textfield [tuiLabel][tuiLabel][tuiLabel]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;position:relative;display:block;flex:1;align-self:flex-start;font-size:inherit;line-height:var(--t-height)}tui-textfield label:defined,tui-textfield input:defined::placeholder,tui-textfield textarea:defined::placeholder,tui-textfield select:defined._empty{color:var(--tui-text-secondary)}tui-textfield select:not([data-mode~=readonly]){cursor:pointer}tui-textfield select option[value=\\\"\\\"]:disabled{color:transparent}tui-textfield select optgroup,tui-textfield select option{background-color:var(--tui-background-elevation-3)}tui-textfield select optgroup,tui-textfield select option:not(:disabled){color:var(--tui-text-primary)}tui-textfield button,tui-textfield a{pointer-events:auto}tui-textfield>.t-content{z-index:1;display:flex;block-size:var(--t-height);align-items:center;gap:inherit;margin-inline-start:auto;isolation:isolate;border-radius:inherit}tui-textfield>.t-content>tui-icon{pointer-events:auto}tui-textfield textarea~.t-content{min-inline-size:.5rem}tui-textfield .t-clear{z-index:1;display:none;pointer-events:auto}tui-textfield>.t-filler:defined{pointer-events:none;background:none;color:var(--tui-text-tertiary);opacity:1}tui-textfield [tuiFluidTypography]{font-weight:700}tui-textfield[multi][multi]{flex-wrap:nowrap;overflow:scroll;align-items:stretch;cursor:text;gap:0;max-block-size:calc(var(--t-vertical) * 2 + var(--t-item-height) * var(--t-rows));overscroll-behavior:none;scroll-behavior:var(--tui-scroll-behavior)}tui-textfield[multi][multi]:before,tui-textfield[multi][multi]:after{position:sticky;top:0;left:0;block-size:var(--t-height)}@supports (inset-inline-start: 0){tui-textfield[multi][multi]:before,tui-textfield[multi][multi]:after{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]:has([tuiSelectLike]){cursor:pointer}tui-textfield[multi][multi][tuiChevron]:after{top:.375rem;block-size:calc(var(--t-height) - .75rem)}tui-textfield[multi][multi]>.t-scrollbar{transform:translate(var(--t-padding));margin-inline-start:calc(-1 * var(--t-start));margin-inline-end:calc(1px - 100% + var(--t-start))}tui-textfield[multi][multi]>.t-scrollbar .t-bar_horizontal{display:none}tui-textfield[multi][multi]>.t-items{position:sticky;left:var(--t-start);display:flex;min-inline-size:0;min-block-size:-webkit-fit-content;min-block-size:-moz-fit-content;min-block-size:fit-content;flex:1;align-items:center;flex-wrap:wrap;padding:var(--t-vertical) 0}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-items{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]>.t-items:after{content:\\\"\\\";min-inline-size:1px;min-block-size:1px}tui-textfield[multi][multi]>.t-items_horizontal{clip-path:inset(0 0 0 calc(var(--t-start) / 2 - var(--t-padding) - .25rem));flex-wrap:nowrap}tui-textfield[multi][multi]>.t-items_horizontal:dir(rtl){clip-path:inset(0 calc(var(--t-start) / 2 - var(--t-padding) - .25rem) 0 0)}tui-textfield[multi][multi]>.t-items_horizontal>.t-input{padding-inline-end:calc(var(--t-side) + var(--t-end) + var(--t-padding))}tui-textfield[multi][multi]>.t-items:not(tui-textfield[multi][multi]>.t-items_horizontal){--t-scroll: 0}tui-textfield[multi][multi]>.t-items>label[tuiLabel]{position:absolute;top:0;inline-size:100%}tui-textfield[multi][multi]>.t-items>.t-input{position:relative;display:flex;align-items:center;flex:1;max-inline-size:100%;transform:translate(var(--t-scroll))}tui-textfield[multi][multi]>.t-items>.t-input input{position:absolute;left:0;inline-size:100%;padding:0}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-items>.t-input input{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]>.t-items>.t-input .t-ghost{visibility:hidden;white-space:pre;text-overflow:clip;padding-inline-end:.125rem;min-block-size:var(--t-item-height, 1em)}tui-textfield[multi][multi]>.t-items>.t-input .t-ghost:empty:before{content:attr(data-placeholder)}tui-textfield[multi][multi]>.t-items>.t-input .t-filler{position:absolute;left:0;color:var(--tui-text-tertiary)}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-items>.t-input .t-filler{left:unset;inset-inline-start:0}}tui-textfield[multi][multi]>.t-content{position:sticky;top:0;left:calc(100% - var(--t-side) - var(--t-end) + var(--t-padding) - var(--t-offset));margin:0;gap:.25rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi]>.t-content{left:unset;inset-inline-start:calc(100% - var(--t-side) - var(--t-end) + var(--t-padding) - var(--t-offset))}}tui-textfield[multi][multi]>.t-content .t-clear{display:flex}tui-textfield[multi][multi][data-mode~=invalid]:not([data-mode~=readonly])>.t-items>[tuiLabel]{color:var(--tui-text-negative)}tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly])>.t-items>[tuiLabel]{color:var(--tui-text-primary)}tui-textfield[multi][multi][data-mode~=readonly]>.t-content .t-clear,tui-textfield[multi][multi]._disabled>.t-content .t-clear,tui-textfield[multi][multi]._empty>.t-content .t-clear{display:none}tui-textfield[multi][multi]>.t-items input:not(:focus)::placeholder,tui-textfield[multi][multi][data-mode~=readonly]>.t-items input::placeholder{opacity:0}tui-textfield[multi][multi]>.t-items input:not(:focus)~.t-ghost:before,tui-textfield[multi][multi][data-mode~=readonly]>.t-items input~.t-ghost:before{display:none}tui-textfield[multi][multi]:not([data-mode~=readonly])._empty>.t-items input::placeholder,tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly]):not(:focus-within)>.t-items input::placeholder{opacity:1}tui-textfield[multi][multi]:not([data-mode~=readonly])._empty>.t-items .t-ghost:before,tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly]):not(:focus-within)>.t-items .t-ghost:before{display:inline}tui-textfield[multi][multi]:not(._empty)>.t-items [tuiLabel],tui-textfield[multi][multi][data-focus=true]:not([data-mode~=readonly])>.t-items [tuiLabel]{font-size:.83em;transform:translateY(var(--t-label))}tui-textfield[multi][multi][data-size=l]{--t-vertical: .625rem;--t-offset: calc(1rem - var(--t-end) / 4.5)}tui-textfield[multi][multi][data-size=l]:before{margin-inline-end:.75rem}tui-textfield[multi][multi][data-size=l]:after{left:calc(100% - var(--t-end) - .375rem + .25 * (1em - 1rem));margin-inline-end:calc(.5 * (1.5rem - 1em) - var(--t-padding));margin-inline-start:-.75rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=l]:after{left:unset;inset-inline-start:calc(100% - var(--t-end) - .375rem + .25 * (1em - 1rem))}}tui-textfield[multi][multi][data-size=l]._with-label{--t-vertical: 1.125rem}tui-textfield[multi][multi][data-size=l]._with-label>.t-items{padding:1.75rem 0 .5rem}tui-textfield[multi][multi][data-size=m]{--t-vertical: .5rem;--t-offset: calc(.75rem + var(--t-end) / 14)}tui-textfield[multi][multi][data-size=m]:before{left:-.125rem;margin-inline-end:.375rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=m]:before{left:unset;inset-inline-start:-.125rem}}tui-textfield[multi][multi][data-size=m]:after{left:calc(100% - var(--t-end) - .25rem);margin-inline-start:-.125rem;border-width:.625rem;margin-inline-end:calc(.5 * (1.5rem - 1em) - var(--t-padding))}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=m]:after{left:unset;inset-inline-start:calc(100% - var(--t-end) - .25rem)}}tui-textfield[multi][multi][data-size=m]._with-label{--t-vertical: .875rem}tui-textfield[multi][multi][data-size=m]._with-label>.t-items{padding:1.375rem 0 .375rem}tui-textfield[multi][multi][data-size=m]>.t-content{transform:translate(.125rem)}tui-textfield[multi][multi][data-size=s]{--t-vertical: .125rem;--t-offset: calc(.625rem + var(--t-end) / 10)}tui-textfield[multi][multi][data-size=s]:before{left:-.125rem;margin-inline-end:.375rem}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=s]:before{left:unset;inset-inline-start:-.125rem}}tui-textfield[multi][multi][data-size=s]:after{left:calc(100% - var(--t-end) - .125rem);border-width:.5rem;margin-inline-end:calc(-1 * var(--t-padding))}@supports (inset-inline-start: 0){tui-textfield[multi][multi][data-size=s]:after{left:unset;inset-inline-start:calc(100% - var(--t-end) - .125rem)}}tui-textfield[multi][multi][data-size=s]>.t-content{gap:0;transform:translate(calc(.375rem - var(--t-end) / 10))}tui-textfield[multi][multi]:focus-visible:not([data-focus=false]) input::placeholder,tui-textfield[multi][multi]:focus-visible:not([data-focus=false]) input._empty{color:var(--tui-text-tertiary)}tui-textfield[multi][multi][data-focus=true] input::placeholder,tui-textfield[multi][multi][data-focus=true] input._empty{color:var(--tui-text-tertiary)}tui-textfield[multi][multi][tuiWrapper]:not(._focused):has(:focus-visible) input::placeholder,tui-textfield[multi][multi][tuiWrapper]._focused input::placeholder,tui-textfield[multi][multi][tuiWrapper]:not(._focused):has(:focus-visible) input._empty,tui-textfield[multi][multi][tuiWrapper]._focused input._empty{color:var(--tui-text-tertiary)}tui-textfield[multi][multi] tui-textfield-item{transform:translate(var(--t-scroll))}\\n\"] }]\n        }], propDecorators: { item: [{\n                type: ContentChild,\n                args: [TuiItem, { read: TemplateRef, descendants: true }]\n            }], rows: [{\n                type: Input\n            }] } });\n\nconst TuiTextfield = [\n    TuiItem,\n    TuiLabel,\n    TuiSelect,\n    TuiTextfieldComponent,\n    TuiTextfieldDirective,\n    TuiTextfieldOptionsDirective,\n    TuiTextfieldDropdownDirective,\n    TuiTextfieldMultiComponent,\n];\n\nfunction tuiInjectAuxiliary(predicate) {\n    const { auxiliaries } = inject(TuiTextfieldComponent);\n    return computed(() => (auxiliaries().find(predicate) ?? null));\n}\n\nclass TuiTextfieldContent {\n    constructor() {\n        this.ref = inject(TuiTextfieldComponent).vcr?.createEmbeddedView(inject(TemplateRef));\n    }\n    ngDoCheck() {\n        this.ref?.detectChanges();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldContent, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTextfieldContent, isStandalone: true, selector: \"ng-template[tuiTextfieldContent]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextfieldContent, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-template[tuiTextfieldContent]',\n                }]\n        }] });\n\nfunction tuiTextfieldIconBinding(token) {\n    const textfield = inject(TUI_TEXTFIELD_OPTIONS);\n    const options = inject(token);\n    return tuiDirectiveBinding(TuiIcons, 'iconEnd', computed(() => options.icon(textfield.size())), {});\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_TEXTFIELD_ACCESSOR, TUI_TEXTFIELD_OPTIONS, TuiSelect, TuiSelectLike, TuiTextfield, TuiTextfieldBase, TuiTextfieldBaseComponent, TuiTextfieldComponent, TuiTextfieldContent, TuiTextfieldDirective, TuiTextfieldDropdownDirective, TuiTextfieldItemComponent, TuiTextfieldMultiComponent, TuiTextfieldOptionsDirective, TuiWithTextfield, TuiWithTextfieldDropdown, tuiAsTextfieldAccessor, tuiInjectAuxiliary, tuiTextfieldIconBinding, tuiTextfieldOptionsProvider };\n"], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,UAAU,EAAEC,eAAe,QAAQ,eAAe;AACtP,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,gBAAgB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,yBAAyB;AAClF,SAASC,aAAa,EAAEC,cAAc,EAAEC,UAAU,EAAEC,KAAK,EAAEC,cAAc,EAAEC,mBAAmB,QAAQ,mCAAmC;AACzI,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,QAAQ,QAAQ,iCAAiC;AAC1D,OAAO,KAAKC,IAAI,MAAM,iBAAiB;AACvC,SAASC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,OAAO,QAAQ,iBAAiB;AACvE,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAO,KAAKC,IAAI,MAAM,2CAA2C;AACjE,SAASC,kBAAkB,QAAQ,2CAA2C;AAC9E,OAAO,KAAKC,IAAI,MAAM,sCAAsC;AAC5D,SAASC,aAAa,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,aAAa,QAAQ,sCAAsC;AAC9I,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,KAAKC,EAAE,MAAM,0CAA0C;AAC9D,SAASC,oBAAoB,EAAEC,kBAAkB,QAAQ,0CAA0C;AACnG,SAASC,QAAQ,EAAEC,kBAAkB,QAAQ,4BAA4B;AACzE,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,2BAA2B;AAC5E,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,wBAAwB,EAAEC,SAAS,QAAQ,kCAAkC;AACtF,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,iBAAiB,EAAEC,oBAAoB,QAAQ,qCAAqC;AAC7F,OAAO,KAAKC,EAAE,MAAM,oCAAoC;AACxD,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,mBAAmB,QAAQ,oCAAoC;AAClJ,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,YAAY,EAAEC,QAAQ,QAAQ,iCAAiC;AACxE,SAASC,gBAAgB,EAAEC,cAAc,EAAEC,aAAa,EAAEC,cAAc,QAAQ,uBAAuB;AACvG,SAASC,kBAAkB,EAAEC,aAAa,EAAEC,qBAAqB,QAAQ,wBAAwB;AACjG,SAASC,aAAa,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,MAAM,QAAQ,MAAM;AAC7E,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,OAAO,KAAKC,IAAI,MAAM,2BAA2B;AACjD,SAASrC,kBAAkB,IAAIsC,oBAAoB,EAAEvC,oBAAoB,IAAIwC,sBAAsB,EAAEC,iBAAiB,QAAQ,2BAA2B;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,SAAAE,wCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAGrDlG,EAAE,CAAAmG,gBAAA;IAAFnG,EAAE,CAAAoG,cAAA,eA6Po5B,CAAC;IA7Pv5BpG,EAAE,CAAAqG,UAAA,mBAAAC,gEAAA;MAAFtG,EAAE,CAAAuG,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFxG,EAAE,CAAAyG,aAAA;MAAA,OAAFzG,EAAE,CAAA0G,WAAA,CAAAF,MAAA,CAAAG,QAAA,kBAAAH,MAAA,CAAAG,QAAA,CAAAC,QAAA,CA6P6zB,IAAI;IAAA,CAAE,CAAC,0CAAAC,uFAAA;MA7Pt0B7G,EAAE,CAAAuG,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFxG,EAAE,CAAAyG,aAAA;MAAA,OAAFzG,EAAE,CAAA0G,WAAA,CAAAF,MAAA,CAAAM,KAAA,kBAAAN,MAAA,CAAAM,KAAA,CAAAC,aAAA,kBAAAP,MAAA,CAAAM,KAAA,CAAAC,aAAA,CAAAC,KAAA;IAAA,CA6P64B,CAAC;IA7Ph5BhH,EAAE,CAAAiH,MAAA,EA6Pi7B,CAAC;IA7Pp7BjH,EAAE,CAAAkH,YAAA,CA6P07B,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAQ,MAAA,GA7P77BxG,EAAE,CAAAyG,aAAA;IAAFzG,EAAE,CAAAmH,UAAA,cAAAX,MAAA,CAAAY,KAAA,CAAAC,KA6PqxB,CAAC;IA7PxxBrH,EAAE,CAAAsH,SAAA,CA6Pi7B,CAAC;IA7Pp7BtH,EAAE,CAAAuH,kBAAA,MAAAf,MAAA,CAAAgB,KAAA,OA6Pi7B,CAAC;EAAA;AAAA;AAAA,SAAAC,sDAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7Pp7BhG,EAAE,CAAA0H,uBAAA,EA6PkrC,CAAC;IA7PrrC1H,EAAE,CAAAiH,MAAA,EA6P4sC,CAAC;IA7P/sCjH,EAAE,CAAA2H,qBAAA;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAA4B,OAAA,GAAA3B,GAAA,CAAA4B,kBAAA;IAAF7H,EAAE,CAAAsH,SAAA,CA6P4sC,CAAC;IA7P/sCtH,EAAE,CAAAuH,kBAAA,MAAAK,OAAA,KA6P4sC,CAAC;EAAA;AAAA;AAAA,SAAAE,uCAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7P/sChG,EAAE,CAAAoG,cAAA,aA6PglC,CAAC;IA7PnlCpG,EAAE,CAAA+H,UAAA,IAAAN,qDAAA,yBA6PkrC,CAAC;IA7PrrCzH,EAAE,CAAAkH,YAAA,CA6PouC,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAQ,MAAA,GA7PvuCxG,EAAE,CAAAyG,aAAA;IAAFzG,EAAE,CAAAsH,SAAA,CA6PkoC,CAAC;IA7ProCtH,EAAE,CAAAmH,UAAA,uBAAAX,MAAA,CAAAwB,OA6PkoC,CAAC,8BA7ProChI,EAAE,CAAAiI,eAAA,IAAArC,GAAA,EAAAY,MAAA,CAAA0B,OAAA,kBAAA1B,MAAA,CAAA0B,OAAA,CAAAC,KAAA,CA6P+qC,CAAC;EAAA;AAAA;AAAA,SAAAC,wCAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7PlrChG,EAAE,CAAAqI,SAAA,iBA6P03C,CAAC;EAAA;EAAA,IAAArC,EAAA;IAAA,MAAAQ,MAAA,GA7P73CxG,EAAE,CAAAyG,aAAA;IAAFzG,EAAE,CAAAmH,UAAA,UAAAX,MAAA,CAAA8B,cAAA,EA6Ps3C,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAA3C,EAAA,KAAAA,EAAA;AAAA,SAAA4C,4BAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7Pz3ChG,EAAE,CAAAoG,cAAA,eAyaywB,CAAC;IAza5wBpG,EAAE,CAAAiH,MAAA,EAyakyB,CAAC;IAzaryBjH,EAAE,CAAAkH,YAAA,CAya2yB,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAA0C,MAAA,GAza9yB1I,EAAE,CAAAyG,aAAA;IAAFzG,EAAE,CAAAsH,SAAA,CAyakyB,CAAC;IAzaryBtH,EAAE,CAAAuH,kBAAA,MAAAmB,MAAA,CAAAC,WAAA,MAyakyB,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAA5C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzaryBhG,EAAE,CAAAoG,cAAA,eAyak7B,CAAC;IAzar7BpG,EAAE,CAAAiH,MAAA,EAya48B,CAAC;IAza/8BjH,EAAE,CAAAkH,YAAA,CAyaq9B,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAA6C,OAAA,GAAA5C,GAAA,CAAAH,SAAA;IAzax9B9F,EAAE,CAAAmH,UAAA,UAAA0B,OAya26B,CAAC;IAza96B7I,EAAE,CAAAsH,SAAA,CAya48B,CAAC;IAza/8BtH,EAAE,CAAAuH,kBAAA,MAAAsB,OAAA,KAya48B,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAza/8BhG,EAAE,CAAA+H,UAAA,IAAAa,yCAAA,mBAyak7B,CAAC;EAAA;EAAA,IAAA5C,EAAA;IAAA,MAAA0C,MAAA,GAzar7B1I,EAAE,CAAAyG,aAAA;IAAFzG,EAAE,CAAAmH,UAAA,YAAFnH,EAAE,CAAAiI,eAAA,IAAAO,GAAA,EAAAE,MAAA,CAAAK,WAAA,CAya63B,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAAhD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzah4BhG,EAAE,CAAA0H,uBAAA,EA4ckb,CAAC;IA5crb1H,EAAE,CAAAiH,MAAA,EA4c4b,CAAC;IA5c/bjH,EAAE,CAAA2H,qBAAA;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAiD,OAAA,GAAAhD,GAAA,CAAA4B,kBAAA;IAAF7H,EAAE,CAAAsH,SAAA,CA4c4b,CAAC;IA5c/btH,EAAE,CAAAkJ,iBAAA,CAAAD,OA4c4b,CAAC;EAAA;AAAA;AAAA,MAAAE,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,IAAA,GAAAA,CAAAzD,EAAA,EAAA0D,EAAA;EAAAC,IAAA,EAAA3D,EAAA;EAAA4D,KAAA,EAAAF;AAAA;AAAA,SAAAG,0DAAA1D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5c/bhG,EAAE,CAAAqI,SAAA,6BAogBid,CAAC;EAAA;AAAA;AAAA,SAAAsB,oDAAA3D,EAAA,EAAAC,GAAA;AAAA,SAAA2D,sCAAA5D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApgBpdhG,EAAE,CAAA+H,UAAA,IAAA4B,mDAAA,yBAogB+6B,CAAC;EAAA;EAAA,IAAA3D,EAAA;IAAA,MAAA6C,OAAA,GAAA5C,GAAA,CAAAH,SAAA;IAAA,MAAA+D,QAAA,GAAA5D,GAAA,CAAAwD,KAAA;IAAA,MAAAK,MAAA,GApgBl7B9J,EAAE,CAAAyG,aAAA;IAAFzG,EAAE,CAAAmH,UAAA,uBAAA2C,MAAA,CAAAC,SAogBm2B,CAAC,8BApgBt2B/J,EAAE,CAAAiI,eAAA,IAAArC,GAAA,EAAF5F,EAAE,CAAAgK,eAAA,IAAAV,IAAA,EAAAT,OAAA,EAAAgB,QAAA,EAogBu6B,CAAC;EAAA;AAAA;AAAA,SAAAI,4CAAAjE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApgB16BhG,EAAE,CAAAqI,SAAA,eAogBsmD,CAAC;EAAA;EAAA,IAAArC,EAAA;IAAA,MAAA8D,MAAA,GApgBzmD9J,EAAE,CAAAyG,aAAA;IAAFzG,EAAE,CAAAmH,UAAA,UAAA2C,MAAA,CAAAxB,cAAA,EAogB0lD,CAAC;EAAA;AAAA;AAAA,SAAA4B,8CAAAlE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAmE,GAAA,GApgB7lDnK,EAAE,CAAAmG,gBAAA;IAAFnG,EAAE,CAAAoG,cAAA,gBAogBinE,CAAC;IApgBpnEpG,EAAE,CAAAqG,UAAA,mBAAA+D,sEAAA;MAAFpK,EAAE,CAAAuG,aAAA,CAAA4D,GAAA;MAAA,MAAAL,MAAA,GAAF9J,EAAE,CAAAyG,aAAA;MAAA,OAAFzG,EAAE,CAAA0G,WAAA,CAAAoD,MAAA,CAAAnD,QAAA,kBAAAmD,MAAA,CAAAnD,QAAA,CAAAC,QAAA;IAAA,CAogB0mE,CAAC;IApgB7mE5G,EAAE,CAAAiH,MAAA,EAogB8oE,CAAC;IApgBjpEjH,EAAE,CAAAkH,YAAA,CAogBupE,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAA8D,MAAA,GApgB1pE9J,EAAE,CAAAyG,aAAA;IAAFzG,EAAE,CAAAmH,UAAA,cAAA2C,MAAA,CAAA1C,KAAA,CAAAC,KAogB8jE,CAAC;IApgBjkErH,EAAE,CAAAsH,SAAA,CAogB8oE,CAAC;IApgBjpEtH,EAAE,CAAAuH,kBAAA,MAAAuC,MAAA,CAAAtC,KAAA,OAogB8oE,CAAC;EAAA;AAAA;AAAA,SAAA6C,2DAAArE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApgBjpEhG,EAAE,CAAA0H,uBAAA,EAogBi5E,CAAC;IApgBp5E1H,EAAE,CAAAiH,MAAA,EAogB26E,CAAC;IApgB96EjH,EAAE,CAAA2H,qBAAA;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAsE,OAAA,GAAArE,GAAA,CAAA4B,kBAAA;IAAF7H,EAAE,CAAAsH,SAAA,CAogB26E,CAAC;IApgB96EtH,EAAE,CAAAuH,kBAAA,MAAA+C,OAAA,KAogB26E,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAAvE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApgB96EhG,EAAE,CAAAoG,cAAA,cAogB+yE,CAAC;IApgBlzEpG,EAAE,CAAA+H,UAAA,IAAAsC,0DAAA,0BAogBi5E,CAAC;IApgBp5ErK,EAAE,CAAAkH,YAAA,CAogBm8E,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAA8D,MAAA,GApgBt8E9J,EAAE,CAAAyG,aAAA;IAAFzG,EAAE,CAAAsH,SAAA,CAogBi2E,CAAC;IApgBp2EtH,EAAE,CAAAmH,UAAA,uBAAA2C,MAAA,CAAA9B,OAogBi2E,CAAC,8BApgBp2EhI,EAAE,CAAAiI,eAAA,IAAArC,GAAA,EAAAkE,MAAA,CAAA5B,OAAA,kBAAA4B,MAAA,CAAA5B,OAAA,CAAAC,KAAA,CAogB84E,CAAC;EAAA;AAAA;AArgBt/E,MAAMqC,mBAAmB,CAAC;EACtB;IAAS,IAAI,CAACC,IAAI,YAAAC,4BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,mBAAmB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAACI,IAAI,kBAD+E5K,EAAE,CAAA6K,iBAAA;MAAAC,IAAA,EACJN,mBAAmB;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADjBlL,EAAE,CAAAmL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,6BAAAvF,EAAA,EAAAC,GAAA;MAAAuF,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACqT;EAAE;AAC9Z;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG3L,EAAE,CAAA4L,iBAAA,CAGXpB,mBAAmB,EAAc,CAAC;IAClHM,IAAI,EAAE7K,SAAS;IACf4L,IAAI,EAAE,CAAC;MAAEZ,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEG,aAAa,EAAEvL,iBAAiB,CAAC4L,IAAI;MAAEJ,eAAe,EAAEvL,uBAAuB,CAAC4L,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,kDAAkD;IAAE,CAAC;EAC7E,CAAC,CAAC;AAAA;AACV,MAAMU,aAAa,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAGjL,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACkL,SAAS,GAAGjM,MAAM,CAACc,cAAc,CAAC;IACvC,IAAI,CAACoL,OAAO,GAAGhL,aAAa,CAACkJ,mBAAmB,CAAC;EACrD;EACAhD,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC4E,EAAE,CAACjE,KAAK,GAAG,EAAE;EACtB;EACAoE,OAAOA,CAACC,KAAK,EAAE;IACX,IAAI,CAAC,IAAI,CAACH,SAAS,EAAE;MACjB;IACJ;IACAG,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI,CAACL,EAAE,CAACpF,KAAK,CAAC,CAAC;EACnB;EACA;IAAS,IAAI,CAACyD,IAAI,YAAAiC,sBAAA/B,CAAA;MAAA,YAAAA,CAAA,IAAyFuB,aAAa;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACS,IAAI,kBA1B+E3M,EAAE,CAAA4M,iBAAA;MAAA9B,IAAA,EA0BJoB,aAAa;MAAAnB,SAAA;MAAAC,SAAA,gBAAsF,MAAM,gBAAgB,OAAO,kBAAkB,KAAK;MAAA6B,YAAA,WAAAC,2BAAA9G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1BrJhG,EAAE,CAAAqG,UAAA,yBAAA0G,6CAAAC,MAAA;YAAA,OA0BJA,MAAA,CAAAC,SAAA,CAAAC,QAAA,CAA0B,QAAQ,CAAC,IAAIF,MAAA,CAAAP,cAAA,CAAsB,CAAC;UAAA,CAAlD,CAAC,2BAAAU,+CAAAH,MAAA;YAAA,QAAAA,MAAA,CAAAC,SAAA,kBAAAD,MAAA,CAAAC,SAAA,CAAAC,QAAA,CAAc,QAAQ,MAAKjH,GAAA,CAAAuB,KAAA,CAAM,CAAC;UAAA,CAAnC,CAAC,uBAAA4F,2CAAAJ,MAAA;YAAA,OAAb/G,GAAA,CAAAsG,OAAA,CAAAS,MAAc,CAAC;UAAA,CAAH,CAAC;QAAA;MAAA;MAAA/B,UAAA;IAAA,EAAwY;EAAE;AAC1f;AACA;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KA5BqG3L,EAAE,CAAA4L,iBAAA,CA4BXM,aAAa,EAAc,CAAC;IAC5GpB,IAAI,EAAEzK,SAAS;IACfwL,IAAI,EAAE,CAAC;MACCZ,UAAU,EAAE,IAAI;MAChBoC,QAAQ,EAAE,iBAAiB;MAC3BrB,IAAI,EAAE;QACFC,KAAK,EAAE,eAAe;QACtBqB,SAAS,EAAE,MAAM;QACjBC,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,KAAK;QACnB;QACA,eAAe,EAAE,gEAAgE;QACjF,iBAAiB,EAAE,iDAAiD;QACpE;QACA,aAAa,EAAE;MACnB;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMC,OAAO,GAAG;EACZC,UAAU,EAAE,WAAW;EACvBC,IAAI,EAAE,GAAG;EACTC,OAAO,EAAE;AACb,CAAC;AACD,MAAMC,qBAAqB,GAAGtM,cAAc,CAAC;EACzCmM,UAAU,EAAEpN,MAAM,CAACmN,OAAO,CAACC,UAAU,CAAC;EACtCC,IAAI,EAAErN,MAAM,CAACmN,OAAO,CAACE,IAAI,CAAC;EAC1BC,OAAO,EAAEtN,MAAM,CAACmN,OAAO,CAACG,OAAO;AACnC,CAAC,CAAC;AACF,SAASE,2BAA2BA,CAACC,OAAO,EAAE;EAC1C,OAAO;IACHC,OAAO,EAAEH,qBAAqB;IAC9BI,IAAI,EAAE,CAAC,CAAC,IAAI1N,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAEqN,qBAAqB,CAAC,CAAC;IAC/DK,UAAU,EAAGC,MAAM,KAAM;MACrBT,UAAU,EAAEpN,MAAM,CAAC6N,MAAM,EAAET,UAAU,CAAC,CAAC,IAAID,OAAO,CAACC,UAAU,CAAC;MAC9DC,IAAI,EAAErN,MAAM,CAAC6N,MAAM,EAAER,IAAI,CAAC,CAAC,IAAIF,OAAO,CAACE,IAAI,CAAC;MAC5CC,OAAO,EAAEtN,MAAM,CAAC6N,MAAM,EAAEP,OAAO,CAAC,CAAC,IAAIH,OAAO,CAACG,OAAO,CAAC;MACrD,GAAGG;IACP,CAAC;EACL,CAAC;AACL;AACA,MAAMK,4BAA4B,CAAC;EAC/BjC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4B,OAAO,GAAG3N,MAAM,CAACyN,qBAAqB,EAAE;MAAEQ,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChE;IACA,IAAI,CAACX,UAAU,GAAGpN,MAAM,CAAC,IAAI,CAACyN,OAAO,CAACL,UAAU,CAAC,CAAC,CAAC;IACnD,IAAI,CAACC,IAAI,GAAGrN,MAAM,CAAC,IAAI,CAACyN,OAAO,CAACJ,IAAI,CAAC,CAAC,CAAC;IACvC,IAAI,CAACC,OAAO,GAAGtN,MAAM,CAAC,IAAI,CAACyN,OAAO,CAACH,OAAO,CAAC,CAAC,CAAC;EACjD;EACA,IAAIU,sBAAsBA,CAACZ,UAAU,EAAE;IACnC,IAAI,CAACA,UAAU,CAACa,GAAG,CAACb,UAAU,CAAC;EACnC;EACA,IAAIc,gBAAgBA,CAACb,IAAI,EAAE;IACvB,IAAI,CAACA,IAAI,CAACY,GAAG,CAACZ,IAAI,CAAC;EACvB;EACA,IAAIc,mBAAmBA,CAACC,OAAO,EAAE;IAC7B,IAAI,CAACd,OAAO,CAACW,GAAG,CAACG,OAAO,CAAC;EAC7B;EACA;IAAS,IAAI,CAACjE,IAAI,YAAAkE,qCAAAhE,CAAA;MAAA,YAAAA,CAAA,IAAyFyD,4BAA4B;IAAA,CAAmD;EAAE;EAC5L;IAAS,IAAI,CAACzB,IAAI,kBAvF+E3M,EAAE,CAAA4M,iBAAA;MAAA9B,IAAA,EAuFJsD,4BAA4B;MAAArD,SAAA;MAAA6D,MAAA;QAAAN,sBAAA;QAAAE,gBAAA;QAAAC,mBAAA;MAAA;MAAAxD,UAAA;MAAAC,QAAA,GAvF1BlL,EAAE,CAAA6O,kBAAA,CAuFwR,CAACrN,UAAU,CAACqM,qBAAqB,EAAEO,4BAA4B,CAAC,CAAC;IAAA,EAAiB;EAAE;AACnd;AACA;EAAA,QAAAzC,SAAA,oBAAAA,SAAA,KAzFqG3L,EAAE,CAAA4L,iBAAA,CAyFXwC,4BAA4B,EAAc,CAAC;IAC3HtD,IAAI,EAAEzK,SAAS;IACfwL,IAAI,EAAE,CAAC;MACCZ,UAAU,EAAE,IAAI;MAChBoC,QAAQ,EAAE,mEAAmE;MAC7EyB,SAAS,EAAE,CAACtN,UAAU,CAACqM,qBAAqB,EAAEO,4BAA4B,CAAC;IAC/E,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEE,sBAAsB,EAAE,CAAC;MACvCxD,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE+N,gBAAgB,EAAE,CAAC;MACnB1D,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEgO,mBAAmB,EAAE,CAAC;MACtB3D,IAAI,EAAErK;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsO,sBAAsB,GAAGxN,cAAc,CAAC,CAAC;AAC/C,SAASyN,sBAAsBA,CAACrI,QAAQ,EAAE;EACtC,OAAOnF,UAAU,CAACuN,sBAAsB,EAAEpI,QAAQ,CAAC;AACvD;;AAEA;AACA,MAAMsI,6BAA6B,CAAC;EAChC9C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+C,SAAS,GAAG9O,MAAM,CAAC2D,oBAAoB,CAAC;IAC7C,IAAI,CAACmL,SAAS,CAACC,WAAW,GAAG/O,MAAM,CAACM,WAAW,CAAC;EACpD;EACA0O,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,SAAS,CAACC,WAAW,GAAG,IAAI;EACrC;EACA;IAAS,IAAI,CAAC1E,IAAI,YAAA4E,sCAAA1E,CAAA;MAAA,YAAAA,CAAA,IAAyFsE,6BAA6B;IAAA,CAAmD;EAAE;EAC7L;IAAS,IAAI,CAACtC,IAAI,kBAvH+E3M,EAAE,CAAA4M,iBAAA;MAAA9B,IAAA,EAuHJmE,6BAA6B;MAAAlE,SAAA;MAAAE,UAAA;IAAA,EAAoF;EAAE;AACtN;AACA;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KAzHqG3L,EAAE,CAAA4L,iBAAA,CAyHXqD,6BAA6B,EAAc,CAAC;IAC5HnE,IAAI,EAAEzK,SAAS;IACfwL,IAAI,EAAE,CAAC;MACCZ,UAAU,EAAE,IAAI;MAChBoC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AACtD;AACA;AACA;AACA,MAAMiC,wBAAwB,CAAC;EAC3B;IAAS,IAAI,CAAC7E,IAAI,YAAA8E,iCAAA5E,CAAA;MAAA,YAAAA,CAAA,IAAyF2E,wBAAwB;IAAA,CAAmD;EAAE;EACxL;IAAS,IAAI,CAAC3C,IAAI,kBArI+E3M,EAAE,CAAA4M,iBAAA;MAAA9B,IAAA,EAqIJwE,wBAAwB;MAAArE,UAAA;IAAA,EAAqC;EAAE;AAClK;AACA;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KAvIqG3L,EAAE,CAAA4L,iBAAA,CAuIX0D,wBAAwB,EAAc,CAAC;IACvHxE,IAAI,EAAEzK,SAAS;IACfwL,IAAI,EAAE,CAAC;MACCZ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMuE,yBAAyB,CAAC;EAC5BrD,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACsD,MAAM,GAAGnP,MAAM,CAAC,EAAE,CAAC;IACxB,IAAI,CAACoP,MAAM,GAAGnM,WAAW,CAAC,CAAC;IAC3B,IAAI,CAACoM,SAAS,GAAGnM,YAAY,CAACrC,gBAAgB,CAAC,CAAC,CAAC;IACjD,IAAI,CAACyO,aAAa,GAAG,IAAI9K,aAAa,CAAC,CAAC,CAAC;IACzC,IAAI,CAAC+K,UAAU,GAAGvP,MAAM,CAACwP,SAAS,CAAC;IACnC,IAAI,CAACC,cAAc,GAAG3M,WAAW;IACjC,IAAI,CAAC4M,IAAI,GAAGhM,eAAe,CAAC,CAAC;IAC7B,IAAI,CAACiM,QAAQ,GAAG7P,MAAM,CAAC2D,oBAAoB,CAAC;IAC5C,IAAI,CAACmM,YAAY,GAAG9P,MAAM,CAAC6D,eAAe,CAAC;IAC3C,IAAI,CAACmD,KAAK,GAAGhH,MAAM,CAACmE,gBAAgB,CAAC;IACrC,IAAI,CAACiD,KAAK,GAAGxE,QAAQ,CAAC5C,MAAM,CAACoE,cAAc,CAAC,CAAC;IAC7C,IAAI,CAAC8D,cAAc,GAAG3H,QAAQ,CAAC,CAACwH,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,KAAK;MACrD,MAAMgI,WAAW,GAAGhI,KAAK,GAAG,IAAI,CAACsH,MAAM,CAAC,CAAC,CAACW,KAAK,CAACjI,KAAK,CAACkI,MAAM,CAAC;MAC7D,OAAOF,WAAW,CAACE,MAAM,GAAGlI,KAAK,CAACkI,MAAM,GAAGF,WAAW,GAAG,EAAE;IAC/D,CAAC,CAAC;IACF,IAAI,CAACG,UAAU,GAAG3P,QAAQ,CAAC,MAAM,IAAI,CAAC4P,OAAO,CAAC,CAAC,IAC3C,CAAC,CAAC,IAAI,CAACjI,cAAc,CAAC,CAAC,KACtB,CAAC,CAAC,IAAI,CAACH,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAACrB,KAAK,EAAEC,aAAa,CAAC4B,WAAW,CAAC,CAAC;IAC/D,IAAI,CAAC4H,OAAO,GAAG5P,QAAQ,CAAC,MAAM,IAAI,CAACqP,IAAI,CAAC,CAAC,IAAI,IAAI,CAACL,SAAS,CAAC,CAAC,CAAC;IAC9D,IAAI,CAAC5B,OAAO,GAAG3N,MAAM,CAACyN,qBAAqB,CAAC;IAC5C,IAAI,CAACzB,EAAE,GAAGjL,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACgH,KAAK,GAAG/G,QAAQ,CAAC,IAAI,CAACyO,UAAU,CAAC;IACtC;IACA,IAAI,CAACW,WAAW,GAAGxN,QAAQ,CAAC,IAAI,CAAC4M,aAAa,CAACa,IAAI,CAAC1L,SAAS,CAAC,MAAM1B,mBAAmB,CAAC,IAAI,CAAC0M,cAAc,CAAC,CAAC,EAAE/K,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MAAE0L,WAAW,EAAE;IAAK,CAAC,CAAC;EACzJ;EACA,IAAIC,YAAYA,CAAClB,MAAM,EAAE;IACrB,IAAI,CAACA,MAAM,CAAClB,GAAG,CAACkB,MAAM,CAAC;EAC3B;EACA,IAAImB,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAAC9J,KAAK,EAAEC,aAAa,CAAC6J,EAAE,IAAI,IAAI,CAAClB,MAAM;EACtD;EACA,IAAI/B,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACI,OAAO,CAACJ,IAAI,CAAC,CAAC;EAC9B;EACAkD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACjB,aAAa,CAACkB,IAAI,CAAC,IAAI,CAAC;IAC7B,IAAI,CAACjB,UAAU,CAACtB,GAAG,CAAC,IAAI,CAACzH,KAAK,CAAC;EACnC;EACAiK,YAAYA,CAACC,MAAM,EAAE;IACjB,IAAI,CAACrK,QAAQ,EAAEC,QAAQ,CAACoK,MAAM,CAAC;IAC/B,IAAI,CAAChB,IAAI,CAACzB,GAAG,CAAC,KAAK,CAAC;EACxB;EACA,IAAI0C,QAAQA,CAAA,EAAG;IACX,OAAOC,OAAO,CAAC,IAAI,CAACC,KAAK,EAAEpK,aAAa,EAAEqK,UAAU,CAACf,MAAM,CAAC;EAChE;EACAgB,QAAQA,CAAC;IAAEC;EAAY,CAAC,EAAE;IACtB,IAAI,CAAClF,EAAE,CAACmF,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE/P,KAAK,CAAC6P,WAAW,CAACG,KAAK,CAAC,CAAC;EACnE;EACA;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC5K,KAAK,EAAEC,aAAa,CAACC,KAAK,CAAC,CAAC;IACjC,IAAI,IAAI,CAACkJ,YAAY,CAACyB,kBAAkB,IACpC,IAAI,CAAC1B,QAAQ,CAAC2B,QAAQ,CAAC,CAAC,IACxB,CAAC,IAAI,CAAC9K,KAAK,EAAEC,aAAa,CAAC8K,OAAO,CAAC,YAAY,CAAC,EAAE;MAClD,IAAI,CAAC7B,IAAI,CAAC8B,MAAM,CAAEC,CAAC,IAAK,CAACA,CAAC,CAAC;IAC/B;EACJ;EACAC,QAAQA,CAACC,OAAO,EAAE;IACd,IAAI,IAAI,CAACnL,KAAK,EAAEC,aAAa,KAAKkL,OAAO,EAAE;MACvC,IAAI,CAACC,KAAK,EAAEnL,aAAa,CAACoL,QAAQ,CAAC;QAC/BC,IAAI,EAAE,IAAI,CAACtL,KAAK,CAACC,aAAa,CAACsL;MACnC,CAAC,CAAC;IACN;EACJ;EACA;IAAS,IAAI,CAAC5H,IAAI,YAAA6H,kCAAA3H,CAAA;MAAA,YAAAA,CAAA,IAAyF6E,yBAAyB;IAAA,CAAmD;EAAE;EACzL;IAAS,IAAI,CAAC7C,IAAI,kBAnN+E3M,EAAE,CAAA4M,iBAAA;MAAA9B,IAAA,EAmNJ0E,yBAAyB;MAAA+C,cAAA,WAAAC,yCAAAxM,EAAA,EAAAC,GAAA,EAAAwM,QAAA;QAAA,IAAAzM,EAAA;UAnNvBhG,EAAE,CAAA0S,cAAA,CAAAD,QAAA,EAmN+L5Q,QAAQ,KAA+BjB,UAAU;UAnNlPZ,EAAE,CAAA0S,cAAA,CAAAD,QAAA,EAmNwS1D,sBAAsB;UAnNhU/O,EAAE,CAAA0S,cAAA,CAAAD,QAAA,EAmNwY7P,SAAS;UAnNnZ5C,EAAE,CAAA0S,cAAA,CAAAD,QAAA,EAmNudtP,UAAU;UAnNnenD,EAAE,CAAA0S,cAAA,CAAAD,QAAA,EAmN4kBE,gBAAgB,KAA+B/R,UAAU;UAnNvoBZ,EAAE,CAAA0S,cAAA,CAAAD,QAAA,EAmNosBhO,aAAa;QAAA;QAAA,IAAAuB,EAAA;UAAA,IAAA4M,EAAA;UAnNntB5S,EAAE,CAAA6S,cAAA,CAAAD,EAAA,GAAF5S,EAAE,CAAA8S,WAAA,QAAA7M,GAAA,CAAAkL,KAAA,GAAAyB,EAAA,CAAAG,KAAA;UAAF/S,EAAE,CAAA6S,cAAA,CAAAD,EAAA,GAAF5S,EAAE,CAAA8S,WAAA,QAAA7M,GAAA,CAAAU,QAAA,GAAAiM,EAAA,CAAAG,KAAA;UAAF/S,EAAE,CAAA6S,cAAA,CAAAD,EAAA,GAAF5S,EAAE,CAAA8S,WAAA,QAAA7M,GAAA,CAAAiC,OAAA,GAAA0K,EAAA,CAAAG,KAAA;UAAF/S,EAAE,CAAA6S,cAAA,CAAAD,EAAA,GAAF5S,EAAE,CAAA8S,WAAA,QAAA7M,GAAA,CAAA+M,GAAA,GAAAJ,EAAA,CAAAG,KAAA;UAAF/S,EAAE,CAAA6S,cAAA,CAAAD,EAAA,GAAF5S,EAAE,CAAA8S,WAAA,QAAA7M,GAAA,CAAAa,KAAA,GAAA8L,EAAA,CAAAG,KAAA;UAAF/S,EAAE,CAAA6S,cAAA,CAAAD,EAAA,GAAF5S,EAAE,CAAA8S,WAAA,QAAA7M,GAAA,CAAA8J,cAAA,GAAA6C,EAAA;QAAA;MAAA;MAAAK,SAAA,WAAAC,gCAAAlN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhG,EAAE,CAAAmT,WAAA,CAAA3N,GAAA;UAAFxF,EAAE,CAAAmT,WAAA,CAAA1N,GAAA,KAmNy5B5E,gBAAgB;QAAA;QAAA,IAAAmF,EAAA;UAAA,IAAA4M,EAAA;UAnN36B5S,EAAE,CAAA6S,cAAA,CAAAD,EAAA,GAAF5S,EAAE,CAAA8S,WAAA,QAAA7M,GAAA,CAAAiM,KAAA,GAAAU,EAAA,CAAAG,KAAA;UAAF/S,EAAE,CAAA6S,cAAA,CAAAD,EAAA,GAAF5S,EAAE,CAAA8S,WAAA,QAAA7M,GAAA,CAAAmN,GAAA,GAAAR,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAnE,MAAA;QAAA5G,OAAA;QAAA2I,YAAA,GAAF3Q,EAAE,CAAAqT,YAAA,CAAAvH,IAAA;MAAA;IAAA,EAmN28B;EAAE;AACpjC;AACA;EAAA,QAAAH,SAAA,oBAAAA,SAAA,KArNqG3L,EAAE,CAAA4L,iBAAA,CAqNX4D,yBAAyB,EAAc,CAAC;IACxH1E,IAAI,EAAEzK;EACV,CAAC,CAAC,QAAkB;IAAE6R,KAAK,EAAE,CAAC;MACtBpH,IAAI,EAAEhK,SAAS;MACf+K,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEsF,KAAK,EAAE,CAAC;MACRrG,IAAI,EAAE/J,YAAY;MAClB8K,IAAI,EAAE,CAAC7K,UAAU,CAAC,MAAMa,QAAQ,CAAC,EAAE;QAAEyR,IAAI,EAAE1S;MAAW,CAAC;IAC3D,CAAC,CAAC;IAAEmP,cAAc,EAAE,CAAC;MACjBjF,IAAI,EAAE7J,eAAe;MACrB4K,IAAI,EAAE,CAACpH,aAAa,EAAE;QAAE8O,WAAW,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEH,GAAG,EAAE,CAAC;MACNtI,IAAI,EAAEhK,SAAS;MACf+K,IAAI,EAAE,CAAC,KAAK,EAAE;QAAEyH,IAAI,EAAEzS,gBAAgB;QAAE2S,MAAM,EAAE;MAAK,CAAC;IAC1D,CAAC,CAAC;IAAE7M,QAAQ,EAAE,CAAC;MACXmE,IAAI,EAAE/J,YAAY;MAClB8K,IAAI,EAAE,CAACkD,sBAAsB,EAAE;QAAEwE,WAAW,EAAE;MAAK,CAAC;IACxD,CAAC,CAAC;IAAErL,OAAO,EAAE,CAAC;MACV4C,IAAI,EAAE/J,YAAY;MAClB8K,IAAI,EAAE,CAACjJ,SAAS;IACpB,CAAC,CAAC;IAAEoQ,GAAG,EAAE,CAAC;MACNlI,IAAI,EAAE/J,YAAY;MAClB8K,IAAI,EAAE,CAAC1I,UAAU;IACrB,CAAC,CAAC;IAAE2D,KAAK,EAAE,CAAC;MACRgE,IAAI,EAAE/J,YAAY;MAClB8K,IAAI,EAAE,CAAC7K,UAAU,CAAC,MAAM2R,gBAAgB,CAAC,EAAE;QACnCW,IAAI,EAAE1S,UAAU;QAChB4S,MAAM,EAAE;MACZ,CAAC;IACT,CAAC,CAAC;IAAExL,OAAO,EAAE,CAAC;MACV8C,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEkQ,YAAY,EAAE,CAAC;MACf7F,IAAI,EAAErK,KAAK;MACXoL,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM4H,qBAAqB,SAASjE,yBAAyB,CAAC;EAC1D;IAAS,IAAI,CAAC/E,IAAI;MAAA,IAAAiJ,kCAAA;MAAA,gBAAAC,8BAAAhJ,CAAA;QAAA,QAAA+I,kCAAA,KAAAA,kCAAA,GAzP+E1T,EAAE,CAAA4T,qBAAA,CAyPQH,qBAAqB,IAAA9I,CAAA,IAArB8I,qBAAqB;MAAA;IAAA,IAAqD;EAAE;EACvL;IAAS,IAAI,CAAC7I,IAAI,kBA1P+E5K,EAAE,CAAA6K,iBAAA;MAAAC,IAAA,EA0PJ2I,qBAAqB;MAAA1I,SAAA;MAAA8I,QAAA;MAAAhH,YAAA,WAAAiH,mCAAA9N,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1PnBhG,EAAE,CAAAqG,UAAA,gCAAA0N,4DAAA;YAAA,OA0PJ,CAAC;UAAA,CAAmB,CAAC,sCAAAC,kEAAA;YAAA,OAArB/N,GAAA,CAAAyL,WAAA,CAAY,CAAC;UAAA,CAAO,CAAC,qCAAAuC,iEAAAjH,MAAA;YAAA,OAArB/G,GAAA,CAAA+L,QAAA,CAAAhF,MAAA,CAAAkH,MAAsB,CAAC;UAAA,CAAH,CAAC,iCAAAC,6DAAAnH,MAAA;YAAA,QAAAA,MAAA,KAAA/G,GAAA,CAAA+M,GAAA,kBAAA/M,GAAA,CAAA+M,GAAA,CAAAoB,SAAA;UAAA,CAAD,CAAC;QAAA;QAAA,IAAApO,EAAA;UA1PnBhG,EAAE,CAAAqU,WAAA,cA0PJpO,GAAA,CAAA8H,OAAA,CAAAJ,IAAA,CAAa,CAAC;UA1PZ3N,EAAE,CAAAsU,WAAA,gBAAArO,GAAA,CAAAgL,QA0PgB,CAAC,mBAAAhL,GAAA,CAAA+B,OAAA,KAAA/B,GAAA,CAAAiC,OAAA,kBAAAjC,GAAA,CAAAiC,OAAA,CAAAC,KAAA,KAAQ,IAAT,CAAC,cAAAlC,GAAA,CAAAa,KAAA,kBAAAb,GAAA,CAAAa,KAAA,CAAAC,aAAA,kBAAAd,GAAA,CAAAa,KAAA,CAAAC,aAAA,CAAAwN,QAAD,CAAC;QAAA;MAAA;MAAAtJ,UAAA;MAAAC,QAAA,GA1PnBlL,EAAE,CAAA6O,kBAAA,CA0P8e,CACzkBpL,wBAAwB,CAAC;QAAEkK,IAAI,EAAE,IAAI;QAAED,UAAU,EAAE;MAAO,CAAC,CAAC,EAC5D9J,iBAAiB,CAAC6P,qBAAqB,CAAC,CAC3C,GA7P4FzT,EAAE,CAAAwU,uBAAA,EA6PvC1Q,EAAE,CAACC,oBAAoB,EAAiBD,EAAE,CAACI,gBAAgB,EAAiBJ,EAAE,CAACK,mBAAmB,EAAiBC,EAAE,CAACC,YAAY,EAAiBxB,EAAE,CAACC,oBAAoB,EAAiBa,EAAE,CAACE,oBAAoB,EAAiByL,wBAAwB,IA7PtNtP,EAAE,CAAAyU,0BAAA,EAAFzU,EAAE,CAAAmL,mBAAA;MAAAuJ,kBAAA,EAAA/O,GAAA;MAAAyF,KAAA;MAAAC,IAAA;MAAAsJ,MAAA;MAAArJ,QAAA,WAAAsJ,+BAAA5O,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA6O,GAAA,GAAF7U,EAAE,CAAAmG,gBAAA;UAAFnG,EAAE,CAAA8U,eAAA,CAAApP,GAAA;UAAF1F,EAAE,CAAA+U,YAAA,EA6PgR,CAAC;UA7PnR/U,EAAE,CAAA+U,YAAA,KA6PkT,CAAC;UA7PrT/U,EAAE,CAAA+U,YAAA,KA6PsV,CAAC;UA7PzV/U,EAAE,CAAA+U,YAAA,KA6PuX,CAAC;UA7P1X/U,EAAE,CAAAoG,cAAA,aA6PohB,CAAC;UA7PvhBpG,EAAE,CAAAqG,UAAA,iCAAA2O,mEAAA;YAAFhV,EAAE,CAAAuG,aAAA,CAAAsO,GAAA;YAAA,OAAF7U,EAAE,CAAA0G,WAAA,CAAAT,GAAA,CAAAa,KAAA,kBAAAb,GAAA,CAAAa,KAAA,CAAAC,aAAA,kBAAAd,GAAA,CAAAa,KAAA,CAAAC,aAAA,CAAAC,KAAA;UAAA,CA6Pod,CAAC,8BAAAiO,gEAAAjI,MAAA;YA7PvdhN,EAAE,CAAAuG,aAAA,CAAAsO,GAAA;YAAA,OAAF7U,EAAE,CAAA0G,WAAA,CAAAsG,MAAA,CA6Puf,CAAC,KAAK/G,GAAA,CAAAoL,QAAA,CAAArE,MAAA,CAAgB,CAAC,CAAC,CAAC;UAAA,CAAC,CAAC;UA7PphBhN,EAAE,CAAA+U,YAAA,KA6PwiB,CAAC;UA7P3iB/U,EAAE,CAAA+H,UAAA,IAAAhC,uCAAA,mBA6Po5B,CAAC;UA7Pv5B/F,EAAE,CAAAkV,kBAAA,WA6Pq9B,CAAC;UA7Px9BlV,EAAE,CAAA+U,YAAA,KA6P6/B,CAAC;UA7PhgC/U,EAAE,CAAAkH,YAAA,CA6PsgC,CAAC;UA7PzgClH,EAAE,CAAA+H,UAAA,KAAAD,sCAAA,iBA6PglC,CAAC,KAAAM,uCAAA,kBAAyS,CAAC;QAAA;QAAA,IAAApC,EAAA;UA7P73ChG,EAAE,CAAAsH,SAAA,EA6PwlB,CAAC;UA7P3lBtH,EAAE,CAAAmH,UAAA,SAAAlB,GAAA,CAAA8H,OAAA,CAAAH,OAAA,EA6PwlB,CAAC;UA7P3lB5N,EAAE,CAAAsH,SAAA,EA6PijC,CAAC;UA7PpjCtH,EAAE,CAAAmH,UAAA,UAAAlB,GAAA,CAAAiC,OAAA,kBAAAjC,GAAA,CAAAiC,OAAA,CAAAC,KAAA,SA6PijC,CAAC;UA7PpjCnI,EAAE,CAAAsH,SAAA,CA6PswC,CAAC;UA7PzwCtH,EAAE,CAAAmH,UAAA,SAAAlB,GAAA,CAAAqK,UAAA,EA6PswC,CAAC;QAAA;MAAA;MAAA6E,YAAA,GAAu/epT,IAAI,EAA6F4C,kBAAkB,EAA8HjB,SAAS,EAAoIR,gBAAgB;MAAAsI,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAoL;EAAE;AACz6iB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/PqG3L,EAAE,CAAA4L,iBAAA,CA+PX6H,qBAAqB,EAAc,CAAC;IACpH3I,IAAI,EAAE7K,SAAS;IACf4L,IAAI,EAAE,CAAC;MAAEZ,UAAU,EAAE,IAAI;MAAEoC,QAAQ,EAAE,4BAA4B;MAAE+H,OAAO,EAAE,CAACrT,IAAI,EAAE4C,kBAAkB,EAAEjB,SAAS,EAAER,gBAAgB,CAAC;MAAEuI,aAAa,EAAEvL,iBAAiB,CAAC4L,IAAI;MAAEJ,eAAe,EAAEvL,uBAAuB,CAAC4L,MAAM;MAAE+C,SAAS,EAAE,CAC5NrL,wBAAwB,CAAC;QAAEkK,IAAI,EAAE,IAAI;QAAED,UAAU,EAAE;MAAO,CAAC,CAAC,EAC5D9J,iBAAiB,CAAC6P,qBAAqB,CAAC,CAC3C;MAAE4B,cAAc,EAAE,CACftR,oBAAoB,EACpBG,gBAAgB,EAChBC,mBAAmB,EACnBE,YAAY,EACZvB,oBAAoB,EACpBe,oBAAoB,EACpByL,wBAAwB,CAC3B;MAAEtD,IAAI,EAAE;QACL,kBAAkB,EAAE,gBAAgB;QACpC,qBAAqB,EAAE,UAAU;QACjC,wBAAwB,EAAE,mCAAmC;QAC7D,mBAAmB,EAAE,gCAAgC;QACrD,sBAAsB,EAAE,GAAG;QAC3B,4BAA4B,EAAE,eAAe;QAC7C,2BAA2B,EAAE,yBAAyB;QACtD,uBAAuB,EAAE;MAC7B,CAAC;MAAEV,QAAQ,EAAE,6oCAA6oC;MAAEE,MAAM,EAAE,CAAC,w0eAAw0e;IAAE,CAAC;EAC5/gB,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMmH,gBAAgB,CAAC;EACnBxG,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACoE,OAAO,GAAGjQ,MAAM,CAAC,IAAI,CAAC;IAC3B,IAAI,CAAC4H,OAAO,GAAG9H,MAAM,CAACwC,SAAS,EAAE;MAAE0S,QAAQ,EAAE;IAAK,CAAC,CAAC;IACpD,IAAI,CAACC,CAAC,GAAGhT,aAAa,CAACnC,MAAM,CAACyN,qBAAqB,CAAC,CAACH,UAAU,EAAE,CAAC,CAAC,CAAC;IACpE,IAAI,CAAC8H,CAAC,GAAGhT,kBAAkB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACrC,IAAI,CAACiT,CAAC,GAAGhT,iBAAiB,CAAC,IAAI,CAACiT,IAAI,EAAE,CAAC,CAAC,CAAC;IACzC,IAAI,CAACC,CAAC,GAAGjT,kBAAkB,CAAC/B,QAAQ,CAAC,MAAM,IAAI,CAAC4P,OAAO,CAAC,CAAC,IAAI,IAAI,CAACqF,SAAS,CAACrF,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3F,IAAI,CAACnE,EAAE,GAAGjL,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAAC0U,QAAQ,GAAGzV,MAAM,CAAC2C,kBAAkB,CAAC;IAC1C,IAAI,CAAC6S,SAAS,GAAGxV,MAAM,CAACqT,qBAAqB,CAAC;IAC9C,IAAI,CAACqC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC5N,KAAK,GAAG/G,QAAQ,CAAC,IAAI,CAACgL,EAAE,CAAC;EAClC;EACA,IAAI4J,aAAaA,CAACzF,OAAO,EAAE;IACvB,IAAI,CAACA,OAAO,CAAChC,GAAG,CAACgC,OAAO,CAAC;EAC7B;EACA,IAAI0F,WAAWA,CAACC,KAAK,EAAE;IACnB,IAAI,CAACV,CAAC,CAACjH,GAAG,CAAC2H,KAAK,CAAC;EACrB;EACA,IAAIR,IAAIA,CAAA,EAAG;IACP,IAAI,IAAI,CAACI,QAAQ,EAAE;MACf,OAAO,UAAU;IACrB;IACA,IAAI,IAAI,CAACC,OAAO,KAAK,KAAK,EAAE;MACxB,OAAO,OAAO;IAClB;IACA,IAAI,IAAI,CAACA,OAAO,EAAE;MACd,OAAO,SAAS;IACpB;IACA,OAAO,IAAI;EACf;EACA;EACAI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACV,CAAC,CAAClH,GAAG,CAAC,IAAI,CAACmH,IAAI,CAAC;EACzB;EACA9O,QAAQA,CAACuB,KAAK,EAAE;IACZ,IAAI,CAACiE,EAAE,CAACpF,KAAK,CAAC,CAAC;IACf,IAAI,CAACoF,EAAE,CAACgK,MAAM,CAAC,CAAC;IAChB,IAAIjO,KAAK,IAAI,IAAI,EAAE;MACf,IAAI,CAACiE,EAAE,CAACiK,aAAa,CAACC,WAAW,CAAC,QAAQ,CAAC;IAC/C,CAAC,MACI;MACD,IAAI,CAAClK,EAAE,CAACiK,aAAa,CAACC,WAAW,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAACT,QAAQ,CAACU,SAAS,CAAC,CAAC,CAACpO,KAAK,CAAC,CAAC;IAC5F;EACJ;EACA;IAAS,IAAI,CAACsC,IAAI,YAAA+L,yBAAA7L,CAAA;MAAA,YAAAA,CAAA,IAAyFgI,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAAChG,IAAI,kBA1U+E3M,EAAE,CAAA4M,iBAAA;MAAA9B,IAAA,EA0UJ6H,gBAAgB;MAAAkB,QAAA;MAAAhH,YAAA,WAAA4J,8BAAAzQ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1UdhG,EAAE,CAAAqG,UAAA,mBAAAqQ,0CAAA;YAAA,OA0UJ,CAAC;UAAA,CAAc,CAAC,qBAAAC,4CAAA;YAAA,OAAhB,CAAC;UAAA,CAAc,CAAC,sBAAAC,6CAAA;YAAA,OAAhB,CAAC;UAAA,CAAc,CAAC;QAAA;QAAA,IAAA5Q,EAAA;UA1UdhG,EAAE,CAAA6W,cAAA,OAAA5Q,GAAA,CAAA2P,SAAA,CAAAhF,EA0UW,CAAC,aAAA3K,GAAA,CAAA6P,QAAD,CAAC;UA1Ud9V,EAAE,CAAAsU,WAAA,WA0UJrO,GAAA,CAAAkC,KAAA,CAAM,CAAC,KAAK,EAAG,CAAC;QAAA;MAAA;MAAAyG,MAAA;QAAAkH,QAAA;QAAAC,OAAA;QAAAC,aAAA,GA1UdhW,EAAE,CAAAqT,YAAA,CAAAvH,IAAA;QAAAmK,WAAA,GAAFjW,EAAE,CAAAqT,YAAA,CAAAvH,IAAA;MAAA;MAAAb,UAAA;MAAAC,QAAA,GAAFlL,EAAE,CAAA6O,kBAAA,CA0UiW,CAACG,sBAAsB,CAAC2D,gBAAgB,CAAC,CAAC,GA1U7Y3S,EAAE,CAAA8W,oBAAA;IAAA,EA0Uib;EAAE;AAC1hB;AACA;EAAA,QAAAnL,SAAA,oBAAAA,SAAA,KA5UqG3L,EAAE,CAAA4L,iBAAA,CA4UX+G,gBAAgB,EAAc,CAAC;IAC/G7H,IAAI,EAAEzK,SAAS;IACfwL,IAAI,EAAE,CAAC;MACCZ,UAAU,EAAE,IAAI;MAChB6D,SAAS,EAAE,CAACE,sBAAsB,CAAC2D,gBAAgB,CAAC,CAAC;MACrD3G,IAAI,EAAE;QACF,MAAM,EAAE,cAAc;QACtB,YAAY,EAAE,UAAU;QACxB,gBAAgB,EAAE,gBAAgB;QAClC,SAAS,EAAE,GAAG;QACd,WAAW,EAAE,GAAG;QAChB,YAAY,EAAE;MAClB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE8J,QAAQ,EAAE,CAAC;MACzBhL,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEsV,OAAO,EAAE,CAAC;MACVjL,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEuV,aAAa,EAAE,CAAC;MAChBlL,IAAI,EAAErK,KAAK;MACXoL,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEoK,WAAW,EAAE,CAAC;MACdnL,IAAI,EAAErK,KAAK;MACXoL,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMkL,qBAAqB,SAASpE,gBAAgB,CAAC;EACjD;IAAS,IAAI,CAAClI,IAAI;MAAA,IAAAuM,kCAAA;MAAA,gBAAAC,8BAAAtM,CAAA;QAAA,QAAAqM,kCAAA,KAAAA,kCAAA,GAtW+EhX,EAAE,CAAA4T,qBAAA,CAsWQmD,qBAAqB,IAAApM,CAAA,IAArBoM,qBAAqB;MAAA;IAAA,IAAqD;EAAE;EACvL;IAAS,IAAI,CAACpK,IAAI,kBAvW+E3M,EAAE,CAAA4M,iBAAA;MAAA9B,IAAA,EAuWJiM,qBAAqB;MAAAhM,SAAA;MAAAE,UAAA;MAAAC,QAAA,GAvWnBlL,EAAE,CAAA6O,kBAAA,CAuWgJ,CAC3OG,sBAAsB,CAAC+H,qBAAqB,CAAC,EAC7CvV,UAAU,CAACmR,gBAAgB,EAAEoE,qBAAqB,CAAC,CACtD,GA1W4F/W,EAAE,CAAAwU,uBAAA,EA0WvCpS,IAAI,CAACC,kBAAkB,EAAiBC,IAAI,CAACK,aAAa,IA1WrB3C,EAAE,CAAAyU,0BAAA;IAAA,EA0WuC;EAAE;AAChJ;AACA;EAAA,QAAA9I,SAAA,oBAAAA,SAAA,KA5WqG3L,EAAE,CAAA4L,iBAAA,CA4WXmL,qBAAqB,EAAc,CAAC;IACpHjM,IAAI,EAAEzK,SAAS;IACfwL,IAAI,EAAE,CAAC;MACCZ,UAAU,EAAE,IAAI;MAChB;MACAoC,QAAQ,EAAE,kFAAkF;MAC5FyB,SAAS,EAAE,CACPE,sBAAsB,CAAC+H,qBAAqB,CAAC,EAC7CvV,UAAU,CAACmR,gBAAgB,EAAEoE,qBAAqB,CAAC,CACtD;MACD1B,cAAc,EAAE,CAAChT,kBAAkB,EAAEM,aAAa;IACtD,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMuU,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACzM,IAAI,YAAA0M,yBAAAxM,CAAA;MAAA,YAAAA,CAAA,IAAyFuM,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACvK,IAAI,kBA3X+E3M,EAAE,CAAA4M,iBAAA;MAAA9B,IAAA,EA2XJoM,gBAAgB;MAAAjM,UAAA;MAAAC,QAAA,GA3XdlL,EAAE,CAAAwU,uBAAA;QAAAtF,SAAA,EA2XgE6H,qBAAqB;QAAAnI,MAAA;MAAA;IAAA,EAAoH;EAAE;AAClT;AACA;EAAA,QAAAjD,SAAA,oBAAAA,SAAA,KA7XqG3L,EAAE,CAAA4L,iBAAA,CA6XXsL,gBAAgB,EAAc,CAAC;IAC/GpM,IAAI,EAAEzK,SAAS;IACfwL,IAAI,EAAE,CAAC;MACCZ,UAAU,EAAE,IAAI;MAChBoK,cAAc,EAAE,CACZ;QACInG,SAAS,EAAE6H,qBAAqB;QAChCnI,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO;MACtD,CAAC;IAET,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAMwI,SAAS,SAASzE,gBAAgB,CAAC;EACrCxG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGkL,SAAS,CAAC;IACnB,IAAI,CAACC,GAAG,GAAGlX,MAAM,CAAC+B,YAAY,CAAC;IAC/B,IAAI,CAACoV,GAAG,GAAGnX,MAAM,CAAC4B,QAAQ,CAAC;IAC3B,IAAI,CAAC2G,WAAW,GAAG,EAAE;EACzB;EACA/B,QAAQA,CAACuB,KAAK,EAAE;IACZ,IAAI,CAACD,OAAO,EAAEA,OAAO,EAAEtB,QAAQ,CAACuB,KAAK,CAAC;IACtC,IAAI,CAACiE,EAAE,CAACoL,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,EAAE;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC;EAChE;EACA1Q,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACoF,EAAE,CAACuL,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;IACjC,IAAI,CAACxL,EAAE,CAACpF,KAAK,CAAC,CAAC;IACf,IAAI,CAACoF,EAAE,CAACuL,SAAS,CAACE,MAAM,CAAC,UAAU,CAAC;EACxC;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACP,GAAG,CAACQ,aAAa,CAAC,cAAc,IAAI,CAAC3L,EAAE,CAACwE,EAAE,IAAI,CAAC,GACrD,IAAI,GACJ,IAAI,CAACxE,EAAE,CAAC4L,YAAY,CAAC,YAAY,CAAC,IAAI,IAAI,CAACrP,WAAW;EAChE;EACA,IAAII,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC8M,QAAQ,CAACU,SAAS,CAAC,CAAC,CAAC,IAAI,CAACrO,OAAO,EAAEC,KAAK,IAAI,EAAE,CAAC;EAC/D;EACM8P,MAAMA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACX,MAAMD,KAAI,CAACZ,GAAG,CAACc,SAAS,CAACC,SAAS,CAACH,KAAI,CAACnP,WAAW,CAAC;IAAC;EACzD;EACA;IAAS,IAAI,CAAC0B,IAAI;MAAA,IAAA6N,sBAAA;MAAA,gBAAAC,kBAAA5N,CAAA;QAAA,QAAA2N,sBAAA,KAAAA,sBAAA,GAxa+EtY,EAAE,CAAA4T,qBAAA,CAwaQwD,SAAS,IAAAzM,CAAA,IAATyM,SAAS;MAAA;IAAA,IAAqD;EAAE;EAC3K;IAAS,IAAI,CAACxM,IAAI,kBAza+E5K,EAAE,CAAA6K,iBAAA;MAAAC,IAAA,EAyaJsM,SAAS;MAAArM,SAAA;MAAA8I,QAAA;MAAAhH,YAAA,WAAA2L,uBAAAxS,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzaPhG,EAAE,CAAAqG,UAAA,mBAAAoS,mCAAA;YAAA,OAyaJ,CAAC;UAAA,CAAO,CAAC,qBAAAC,qCAAA;YAAA,OAAT,CAAC;UAAA,CAAO,CAAC,sBAAAC,sCAAA;YAAA,OAAT,CAAC;UAAA,CAAO,CAAC,mCAAAC,mDAAA;YAAA,OAAT,CAAC;UAAA,CAAO,CAAC,mCAAAC,mDAAA;YAAA,OAAT,CAAC;UAAA,CAAO,CAAC,+BAAAC,+CAAA;YAAA,OAAT7S,GAAA,CAAAW,QAAA,CAAS,EAAE,CAAC;UAAA,CAAJ,CAAC,+BAAAmS,+CAAA;YAAA,OAAT9S,GAAA,CAAAe,KAAA,CAAM,CAAC;UAAA,CAAC,CAAC,+BAAAgS,+CAAA;YAAA,OAAT/S,GAAA,CAAAgS,MAAA,CAAO,CAAC;UAAA,EAAC,4BAAAgB,4CAAA;YAAA,OAAThT,GAAA,CAAAgS,MAAA,CAAO,CAAC;UAAA,EAAC;QAAA;QAAA,IAAAjS,EAAA;UAzaPhG,EAAE,CAAA6W,cAAA,OAAA5Q,GAAA,CAAA2P,SAAA,CAAAhF,EAyaI,CAAC;UAzaP5Q,EAAE,CAAAqU,WAAA,eAAApO,GAAA,CAAA6R,SAAA;UAAF9X,EAAE,CAAAsU,WAAA,WAAArO,GAAA,CAAA8C,WAAA,KAyaY,EAAR,CAAC;QAAA;MAAA;MAAA6F,MAAA;QAAAjG,WAAA;MAAA;MAAAsC,UAAA;MAAAC,QAAA,GAzaPlL,EAAE,CAAA6O,kBAAA,CAyawe,CAACG,sBAAsB,CAACoI,SAAS,CAAC,CAAC,GAza7gBpX,EAAE,CAAAwU,uBAAA,EAyakkBpS,IAAI,CAACC,kBAAkB,EAAiBC,IAAI,CAACK,aAAa,IAza9nB3C,EAAE,CAAAyU,0BAAA,EAAFzU,EAAE,CAAAmL,mBAAA;MAAA+N,KAAA,EAAA3Q,GAAA;MAAA6C,KAAA;MAAAC,IAAA;MAAAsJ,MAAA;MAAArJ,QAAA,WAAA6N,mBAAAnT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhG,EAAE,CAAA+H,UAAA,IAAAU,2BAAA,mBAyaywB,CAAC,IAAAK,gCAAA,gCAza5wB9I,EAAE,CAAAoZ,sBAyao0B,CAAC;QAAA;QAAA,IAAApT,EAAA;UAAA,MAAAqT,WAAA,GAzav0BrZ,EAAE,CAAAsZ,WAAA;UAAFtZ,EAAE,CAAAmH,UAAA,SAAAlB,GAAA,CAAA0C,WAAA,KAAA1C,GAAA,CAAA8C,WAya2sB,CAAC,aAAAsQ,WAAY,CAAC;QAAA;MAAA;MAAAlE,YAAA,GAA2TlT,YAAY,EAA+BH,IAAI,CAACI,OAAO,EAAmHJ,IAAI,CAACC,IAAI;MAAA0J,aAAA;IAAA,EAAyH;EAAE;AACz6C;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KA3aqG3L,EAAE,CAAA4L,iBAAA,CA2aXwL,SAAS,EAAc,CAAC;IACxGtM,IAAI,EAAE7K,SAAS;IACf4L,IAAI,EAAE,CAAC;MAAEZ,UAAU,EAAE,IAAI;MAAEoC,QAAQ,EAAE,sBAAsB;MAAE+H,OAAO,EAAE,CAACnT,YAAY,CAAC;MAAEyJ,eAAe,EAAEvL,uBAAuB,CAACoZ,OAAO;MAAEzK,SAAS,EAAE,CAACE,sBAAsB,CAACoI,SAAS,CAAC,CAAC;MAAE/B,cAAc,EAAE,CAAChT,kBAAkB,EAAEM,aAAa,CAAC;MAAEqJ,IAAI,EAAE;QACvO,MAAM,EAAE,cAAc;QACtB,gBAAgB,EAAE,oBAAoB;QACtC,mBAAmB,EAAE,WAAW;QAChC,SAAS,EAAE,GAAG;QACd,WAAW,EAAE,GAAG;QAChB,YAAY,EAAE,GAAG;QACjB,yBAAyB,EAAE,GAAG;QAC9B,yBAAyB,EAAE,GAAG;QAC9B,qBAAqB,EAAE,cAAc;QACrC,qBAAqB,EAAE,SAAS;QAChC,qBAAqB,EAAE,UAAU;QACjC,kBAAkB,EAAE;MACxB,CAAC;MAAEV,QAAQ,EAAE;IAAiV,CAAC;EAC3W,CAAC,CAAC,QAAkB;IAAE3C,WAAW,EAAE,CAAC;MAC5BmC,IAAI,EAAErK;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAM+Y,yBAAyB,CAAC;EAC5BrN,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAGjL,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACsY,OAAO,GAAG7U,aAAa,CAAC,CAAC;IAC9B,IAAI,CAACgR,SAAS,GAAGxV,MAAM,CAACsZ,0BAA0B,CAAC;EACvD;EACA,IAAI1R,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC4N,SAAS,CAACpM,IAAI,IAAImQ,MAAM,CAAC,IAAI,CAACF,OAAO,CAAC3T,SAAS,CAAC0D,IAAI,CAAC;EACrE;EACA;IAAS,IAAI,CAACiB,IAAI,YAAAmP,kCAAAjP,CAAA;MAAA,YAAAA,CAAA,IAAyF6O,yBAAyB;IAAA,CAAmD;EAAE;EACzL;IAAS,IAAI,CAAC5O,IAAI,kBA5c+E5K,EAAE,CAAA6K,iBAAA;MAAAC,IAAA,EA4cJ0O,yBAAyB;MAAAzO,SAAA;MAAA8I,QAAA;MAAAhH,YAAA,WAAAgN,uCAAA7T,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5cvBhG,EAAE,CAAAqG,UAAA,sCAAAyT,sEAAA;YAAA,OA4cJ,CAAC;UAAA,CAAuB,CAAC,uCAAAC,uEAAA;YAAA,OAAA9T,GAAA,CAAAmG,EAAA,CAAA4N,sBAAA,kBAAA/T,GAAA,CAAAmG,EAAA,CAAA4N,sBAAA,CAAAC,UAAA,kBAAAhU,GAAA,CAAAmG,EAAA,CAAA4N,sBAAA,CAAAC,UAAA,CAAAjT,KAAA;UAAA,CAAD,CAAC,wCAAAkT,wEAAA;YAAA,OAAAjU,GAAA,CAAAmG,EAAA,CAAA+N,kBAAA,kBAAAlU,GAAA,CAAAmG,EAAA,CAAA+N,kBAAA,CAAAF,UAAA,kBAAAhU,GAAA,CAAAmG,EAAA,CAAA+N,kBAAA,CAAAF,UAAA,CAAAjT,KAAA;UAAA,CAAD,CAAC;QAAA;QAAA,IAAAhB,EAAA;UA5cvBhG,EAAE,CAAAsU,WAAA,aAAArO,GAAA,CAAA2P,SAAA,CAAApM,IA4coB,CAAC;QAAA;MAAA;MAAAyB,UAAA;MAAAC,QAAA,GA5cvBlL,EAAE,CAAAmL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAsJ,MAAA;MAAArJ,QAAA,WAAA8O,mCAAApU,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhG,EAAE,CAAA+H,UAAA,IAAAiB,iDAAA,yBA4ckb,CAAC;QAAA;QAAA,IAAAhD,EAAA;UA5crbhG,EAAE,CAAAmH,UAAA,uBAAAlB,GAAA,CAAA+B,OA4cuZ,CAAC,8BAAA/B,GAAA,CAAAwT,OAAwB,CAAC;QAAA;MAAA;MAAAtE,YAAA,GAA6XxQ,kBAAkB;MAAA6G,MAAA;IAAA,EAA0J;EAAE;AACnkC;AACA;EAAA,QAAAG,SAAA,oBAAAA,SAAA,KA9cqG3L,EAAE,CAAA4L,iBAAA,CA8cX4N,yBAAyB,EAAc,CAAC;IACxH1O,IAAI,EAAE7K,SAAS;IACf4L,IAAI,EAAE,CAAC;MAAEZ,UAAU,EAAE,IAAI;MAAEoC,QAAQ,EAAE,oBAAoB;MAAE+H,OAAO,EAAE,CAACzQ,kBAAkB,CAAC;MAAE2G,QAAQ,EAAE,iGAAiG;MAAEI,eAAe,EAAEvL,uBAAuB,CAACoZ,OAAO;MAAEvN,IAAI,EAAE;QACnP,iBAAiB,EAAE,iBAAiB;QACpC,4BAA4B,EAAE,GAAG;QACjC,6BAA6B,EAAE,gDAAgD;QAC/E,8BAA8B,EAAE;MACpC,CAAC;MAAER,MAAM,EAAE,CAAC,yRAAyR;IAAE,CAAC;EACpT,CAAC,CAAC;AAAA;AAEV,MAAMkO,0BAA0B,SAASlK,yBAAyB,CAAC;EAC/DrD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGkL,SAAS,CAAC;IACnB,IAAI,CAACgD,MAAM,GAAG/Z,MAAM,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACuV,QAAQ,GAAGzV,MAAM,CAACiF,oBAAoB,CAAC;IAC5C,IAAI,CAAC0E,SAAS,GAAG,IAAIlF,qBAAqB,CAAC2U,yBAAyB,CAAC;IACrE,IAAI,CAACc,GAAG,GAAGrV,SAAS,CAAC,IAAI,CAACmH,EAAE,EAAE,QAAQ,CAAC,CAClCqE,IAAI,CAACvL,MAAM,CAAC,MAAM,IAAI,CAACqV,IAAI,KAAK,CAAC,CAAC,EAAEjX,WAAW,CAAC,CAAC,EAAEL,kBAAkB,CAAC,CAAC,CAAC,CACxEuX,SAAS,CAAC,MAAM;MACjB,IAAI,CAACpO,EAAE,CAACmF,KAAK,CAACC,WAAW,CAAC,YAAY,EAAE/P,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC2K,EAAE,CAACiG,UAAU,CAAC,CAAC;IAC3E,CAAC,CAAC;IACF,IAAI,CAACkI,IAAI,GAAG,GAAG;EACnB;EACAxJ,YAAYA,CAACC,MAAM,EAAE;IACjB,IAAI,CAACrK,QAAQ,EAAEC,QAAQ,CAAClF,cAAc,CAAC,IAAI,CAACwG,OAAO,EAAEC,KAAK,IAAI,EAAE,EAAE6I,MAAM,EAAE,IAAI,CAAC6E,QAAQ,CAAC4E,eAAe,CAAC,CAAC,CAAC,CAAC;EAC/G;EACAC,OAAOA,CAAC;IAAExG;EAAO,CAAC,EAAE;IAChB,MAAMmG,MAAM,GAAG,IAAI,CAACE,IAAI,GAAG,CAAC,IAAI,IAAI,CAACrS,OAAO,EAAEC,KAAK,EAAEkI,MAAM,GACpD6D,MAAM,CAAC6D,aAAa,CAAC,oBAAoB,CAAC,EAAE4C,YAAY,IAAI,CAAC,GAC9D,IAAI;IACV,IAAIN,MAAM,KAAK,CAAC,EAAE;MACd,IAAI,CAACA,MAAM,CAAC9L,GAAG,CAAC8L,MAAM,CAAC;IAC3B;EACJ;EACAO,MAAMA,CAACpO,KAAK,EAAE;IACV,IAAI,IAAI,CAACrE,KAAK,CAAC,CAAC,IAAI,CAAC9G,YAAY,CAACmL,KAAK,CAACqO,aAAa,CAAC,EAAE;MACpD;IACJ;IACArO,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACqO,aAAa,CAACb,sBAAsB,EAAEc,iBAAiB,EAAE9T,KAAK,CAAC,CAAC;EAC1E;EACA+T,OAAOA,CAAC7G,MAAM,EAAE;IACZ,IAAIA,MAAM,KAAK,IAAI,CAAC9H,EAAE,IAClB,IAAI,CAACA,EAAE,CAACyF,OAAO,CAAC,cAAc,CAAC,IAC/B,CAACqC,MAAM,CAACrC,OAAO,CAAC,iBAAiB,CAAC,EAAE;MACpC,IAAI,CAAC7B,IAAI,CAAC8B,MAAM,CAAE9B,IAAI,IAAK,CAACA,IAAI,CAAC;IACrC;EACJ;EACA;IAAS,IAAI,CAACvF,IAAI;MAAA,IAAAuQ,uCAAA;MAAA,gBAAAC,mCAAAtQ,CAAA;QAAA,QAAAqQ,uCAAA,KAAAA,uCAAA,GA9f+Ehb,EAAE,CAAA4T,qBAAA,CA8fQ8F,0BAA0B,IAAA/O,CAAA,IAA1B+O,0BAA0B;MAAA;IAAA,IAAqD;EAAE;EAC5L;IAAS,IAAI,CAAC9O,IAAI,kBA/f+E5K,EAAE,CAAA6K,iBAAA;MAAAC,IAAA,EA+fJ4O,0BAA0B;MAAA3O,SAAA;MAAAwH,cAAA,WAAA2I,0CAAAlV,EAAA,EAAAC,GAAA,EAAAwM,QAAA;QAAA,IAAAzM,EAAA;UA/fxBhG,EAAE,CAAA0S,cAAA,CAAAD,QAAA,EAogBlC7Q,OAAO,KAA2BlB,WAAW;QAAA;QAAA,IAAAsF,EAAA;UAAA,IAAA4M,EAAA;UApgBb5S,EAAE,CAAA6S,cAAA,CAAAD,EAAA,GAAF5S,EAAE,CAAA8S,WAAA,QAAA7M,GAAA,CAAAuD,IAAA,GAAAoJ,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAA/H,SAAA;MAAA6I,QAAA;MAAAhH,YAAA,WAAAsO,wCAAAnV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhG,EAAE,CAAAqG,UAAA,mBAAA+U,oDAAApO,MAAA;YAAA,OA+fJ/G,GAAA,CAAA8U,OAAA,CAAA/N,MAAA,CAAAkH,MAAqB,CAAC;UAAA,CAAG,CAAC,iCAAAmH,kEAAArO,MAAA;YAAA,QAAAA,MAAA,KAAd/G,GAAA,CAAAmG,EAAA,CAAA+F,QAAA,CAAY;cAAAC,IAAA,EAAO;YAAC,CAAC,CAAC,KAAAnM,GAAA,CAAA+M,GAAA,kBAAA/M,GAAA,CAAA+M,GAAA,CAAAoB,SAAA;UAAA,CAAT,CAAC,gCAAAkH,iEAAA;YAAA,OAA1B,CAAC;UAAA,CAAwB,CAAC,sCAAAC,uEAAA;YAAA,OAA1BtV,GAAA,CAAAyL,WAAA,CAAY,CAAC;UAAA,CAAY,CAAC,qCAAA8J,sEAAAxO,MAAA;YAAA,OAA1B/G,GAAA,CAAA+L,QAAA,CAAAhF,MAAA,CAAAkH,MAAsB,CAAC;UAAA,CAAE,CAAC;QAAA;QAAA,IAAAlO,EAAA;UA/fxBhG,EAAE,CAAAqU,WAAA,gBAAApO,GAAA,CAAAiC,OAAA,kBAAAjC,GAAA,CAAAiC,OAAA,CAAAqM,QAAA,IA+fgB,UAAU,GAAG,IAAI,eAArCtO,GAAA,CAAA8H,OAAA,CAAAJ,IAAA,CAAa,CAAC;UA/fZ3N,EAAE,CAAAyb,WAAA,oBA+fJxV,GAAA,CAAAoU,MAAA,CAAO,CAAC,MAAiB,CAAC,aAAApU,GAAA,CAAAsU,IAAD,CAAC;UA/fxBva,EAAE,CAAAsU,WAAA,aAAArO,GAAA,CAAAiC,OAAA,kBAAAjC,GAAA,CAAAiC,OAAA,CAAAC,KAAA,kBAAAlC,GAAA,CAAAiC,OAAA,CAAAC,KAAA,CAAAkI,MAAA,CA+fqB,CAAC,gBAAApK,GAAA,CAAAgL,QAAD,CAAC,mBAAAhL,GAAA,CAAA+B,OAAA,KAAA/B,GAAA,CAAAiC,OAAA,kBAAAjC,GAAA,CAAAiC,OAAA,CAAAC,KAAA,KAAG,IAAJ,CAAC,cAAAlC,GAAA,CAAAa,KAAA,kBAAAb,GAAA,CAAAa,KAAA,CAAAC,aAAA,kBAAAd,GAAA,CAAAa,KAAA,CAAAC,aAAA,CAAAwN,QAAD,CAAC;QAAA;MAAA;MAAA3F,MAAA;QAAA2L,IAAA;MAAA;MAAAtP,UAAA;MAAAC,QAAA,GA/fxBlL,EAAE,CAAA6O,kBAAA,CA+foxB,CAC/2BpL,wBAAwB,CAAC;QAAEkK,IAAI,EAAE,IAAI;QAAED,UAAU,EAAE;MAAO,CAAC,CAAC,EAC5D9J,iBAAiB,CAAC8V,0BAA0B,CAAC,EAC7ClY,UAAU,CAACiS,qBAAqB,EAAEiG,0BAA0B,CAAC,EAC7DlY,UAAU,CAACkD,cAAc,EAAE9D,UAAU,CAAC,CACzC,GApgB4FZ,EAAE,CAAAwU,uBAAA,EAogBqE1Q,EAAE,CAACI,gBAAgB,EAAiBJ,EAAE,CAACC,oBAAoB,EAAiBD,EAAE,CAACK,mBAAmB,EAAiBmL,wBAAwB,EAAiBlL,EAAE,CAACC,YAAY,EAAiBe,IAAI,CAACtC,oBAAoB,EAAiBa,EAAE,CAACE,oBAAoB,EAAiBuB,IAAI,CAACG,iBAAiB,IApgB3WvF,EAAE,CAAAyU,0BAAA,EAAFzU,EAAE,CAAAmL,mBAAA;MAAA+N,KAAA,EAAA/P,GAAA;MAAAuL,kBAAA,EAAArL,GAAA;MAAA+B,KAAA;MAAAC,IAAA;MAAAsJ,MAAA;MAAArJ,QAAA,WAAAoQ,oCAAA1V,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA6O,GAAA,GAAF7U,EAAE,CAAAmG,gBAAA;UAAFnG,EAAE,CAAA8U,eAAA,CAAA1L,GAAA;UAAFpJ,EAAE,CAAA+H,UAAA,IAAA2B,yDAAA,gCAogBid,CAAC;UApgBpd1J,EAAE,CAAAoG,cAAA,YAogB8rB,CAAC;UApgBjsBpG,EAAE,CAAAqG,UAAA,mBAAAsV,yDAAA;YAAF3b,EAAE,CAAAuG,aAAA,CAAAsO,GAAA;YAAA,OAAF7U,EAAE,CAAA0G,WAAA,CAAAT,GAAA,CAAAa,KAAA,kBAAAb,GAAA,CAAAa,KAAA,CAAAC,aAAA,kBAAAd,GAAA,CAAAa,KAAA,CAAAC,aAAA,CAAAC,KAAA;UAAA,CAogB8kB,CAAC,+CAAA4U,qFAAA;YApgBjlB5b,EAAE,CAAAuG,aAAA,CAAAsO,GAAA;YAAA,OAAF7U,EAAE,CAAA0G,WAAA,CAogB4nB,CAAC;UAAA,CAAE,CAAC,8BAAAmV,oEAAA7O,MAAA;YApgBloBhN,EAAE,CAAAuG,aAAA,CAAAsO,GAAA;YAAA,OAAF7U,EAAE,CAAA0G,WAAA,CAAAsG,MAAA,CAogBkqB,CAAC,KAAK/G,GAAA,CAAAyU,OAAA,CAAA1N,MAAA,CAAe,CAAC,CAAC,CAAC;UAAA,CAAC,CAAC;UApgB9rBhN,EAAE,CAAA+U,YAAA,EAogBmuB,CAAC;UApgBtuB/U,EAAE,CAAA+H,UAAA,IAAA6B,qCAAA,eAogB+6B,CAAC;UApgBl7B5J,EAAE,CAAAoG,cAAA,aAogB4gC,CAAC;UApgB/gCpG,EAAE,CAAAqG,UAAA,+BAAAyV,sEAAA9O,MAAA;YAAFhN,EAAE,CAAAuG,aAAA,CAAAsO,GAAA;YAAA,OAAF7U,EAAE,CAAA0G,WAAA,CAogBs/BT,GAAA,CAAA2U,MAAA,CAAA5N,MAAa,CAAC;UAAA,CAAC,CAAC;UApgBxgChN,EAAE,CAAA+U,YAAA,KAogBqjC,CAAC;UApgBxjC/U,EAAE,CAAAqI,SAAA,aAogBo6C,CAAC;UApgBv6CrI,EAAE,CAAA+H,UAAA,IAAAkC,2CAAA,kBAogBsmD,CAAC;UApgBzmDjK,EAAE,CAAAkH,YAAA,CAogBmnD,CAAC,CAAO,CAAC;UApgB9nDlH,EAAE,CAAAoG,cAAA,aAogB6zD,CAAC;UApgBh0DpG,EAAE,CAAAqG,UAAA,wBAAA0V,+DAAA;YAAF/b,EAAE,CAAAuG,aAAA,CAAAsO,GAAA;YAAA,OAAF7U,EAAE,CAAA0G,WAAA,CAAAT,GAAA,CAAAa,KAAA,kBAAAb,GAAA,CAAAa,KAAA,CAAAC,aAAA,kBAAAd,GAAA,CAAAa,KAAA,CAAAC,aAAA,CAAAC,KAAA;UAAA,CAogBitD,CAAC,0CAAAgV,iFAAA;YApgBptDhc,EAAE,CAAAuG,aAAA,CAAAsO,GAAA;YAAA,OAAF7U,EAAE,CAAA0G,WAAA,CAogB0vD,CAAC;UAAA,CAAE,CAAC,8BAAAuV,qEAAAjP,MAAA;YApgBhwDhN,EAAE,CAAAuG,aAAA,CAAAsO,GAAA;YAAA,OAAF7U,EAAE,CAAA0G,WAAA,CAAAsG,MAAA,CAogBgyD,CAAC,KAAK/G,GAAA,CAAAoL,QAAA,CAAArE,MAAA,CAAgB,CAAC,CAAC,CAAC;UAAA,CAAC,CAAC;UApgB7zDhN,EAAE,CAAA+U,YAAA,KAogBi1D,CAAC;UApgBp1D/U,EAAE,CAAA+H,UAAA,KAAAmC,6CAAA,mBAogBinE,CAAC;UApgBpnElK,EAAE,CAAAkV,kBAAA,YAogBkrE,CAAC;UApgBrrElV,EAAE,CAAA+U,YAAA,MAogB0tE,CAAC;UApgB7tE/U,EAAE,CAAAkH,YAAA,CAogBmuE,CAAC;UApgBtuElH,EAAE,CAAA+H,UAAA,KAAAwC,2CAAA,iBAogB+yE,CAAC;QAAA;QAAA,IAAAvE,EAAA;UApgBlzEhG,EAAE,CAAAmH,UAAA,SAAAlB,GAAA,CAAAsU,IAAA,IAogBgb,CAAC;UApgBnbva,EAAE,CAAAsH,SAAA,CAogB+hB,CAAC;UApgBliBtH,EAAE,CAAAsU,WAAA,uBAAArO,GAAA,CAAAsU,IAAA,MAogB+hB,CAAC;UApgBliBva,EAAE,CAAAsH,SAAA,EAogBoyB,CAAC;UApgBvyBtH,EAAE,CAAAmH,UAAA,YAAAlB,GAAA,CAAAiC,OAAA,kBAAAjC,GAAA,CAAAiC,OAAA,CAAAC,KAogBoyB,CAAC;UApgBvyBnI,EAAE,CAAAsH,SAAA,EAogBk5C,CAAC;UApgBr5CtH,EAAE,CAAAmH,UAAA,gBAAAlB,GAAA,CAAAkC,KAAA,MAAAlC,GAAA,CAAAkC,KAAA,GAAAkI,MAAA,KAAApK,GAAA,CAAAa,KAAA,kBAAAb,GAAA,CAAAa,KAAA,CAAAC,aAAA,kBAAAd,GAAA,CAAAa,KAAA,CAAAC,aAAA,CAAA4B,WAAA,kBAAA1C,GAAA,CAAAa,KAAA,CAAAC,aAAA,CAAA4B,WAAA,CAAA0H,MAAA,UAAApK,GAAA,CAAAa,KAAA,kBAAAb,GAAA,CAAAa,KAAA,CAAAC,aAAA,kBAAAd,GAAA,CAAAa,KAAA,CAAAC,aAAA,CAAA4B,WAAA,GAAA1C,GAAA,CAAAkC,KAAA,EAogBk5C,CAAC;UApgBr5CnI,EAAE,CAAAqU,WAAA,qBAAApO,GAAA,CAAAa,KAAA,kBAAAb,GAAA,CAAAa,KAAA,CAAAC,aAAA,kBAAAd,GAAA,CAAAa,KAAA,CAAAC,aAAA,CAAA4B,WAAA;UAAF3I,EAAE,CAAAsH,SAAA,CAogBs9C,CAAC;UApgBz9CtH,EAAE,CAAAmH,UAAA,SAAAlB,GAAA,CAAAqK,UAAA,EAogBs9C,CAAC;UApgBz9CtQ,EAAE,CAAAsH,SAAA,EAogBi4D,CAAC;UApgBp4DtH,EAAE,CAAAmH,UAAA,SAAAlB,GAAA,CAAA8H,OAAA,CAAAH,OAAA,EAogBi4D,CAAC;UApgBp4D5N,EAAE,CAAAsH,SAAA,EAogBgxE,CAAC;UApgBnxEtH,EAAE,CAAAmH,UAAA,UAAAlB,GAAA,CAAAiC,OAAA,kBAAAjC,GAAA,CAAAiC,OAAA,CAAAC,KAAA,SAogBgxE,CAAC;QAAA;MAAA;MAAAgN,YAAA,GAAwsuBjT,OAAO,EAAmHH,IAAI,EAA6F4C,kBAAkB,EAA8HjB,SAAS,EAAoIyB,iBAAiB,EAAgEjC,gBAAgB;MAAAsI,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAoL;EAAE;AAC/00B;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAtgBqG3L,EAAE,CAAA4L,iBAAA,CAsgBX8N,0BAA0B,EAAc,CAAC;IACzH5O,IAAI,EAAE7K,SAAS;IACf4L,IAAI,EAAE,CAAC;MAAEZ,UAAU,EAAE,IAAI;MAAEoC,QAAQ,EAAE,sBAAsB;MAAE+H,OAAO,EAAE,CAC1DlT,OAAO,EACPH,IAAI,EACJ4C,kBAAkB,EAClBjB,SAAS,EACTyB,iBAAiB,EACjBjC,gBAAgB,CACnB;MAAEuI,aAAa,EAAEvL,iBAAiB,CAAC4L,IAAI;MAAEJ,eAAe,EAAEvL,uBAAuB,CAAC4L,MAAM;MAAE+C,SAAS,EAAE,CAClGrL,wBAAwB,CAAC;QAAEkK,IAAI,EAAE,IAAI;QAAED,UAAU,EAAE;MAAO,CAAC,CAAC,EAC5D9J,iBAAiB,CAAC8V,0BAA0B,CAAC,EAC7ClY,UAAU,CAACiS,qBAAqB,EAAEiG,0BAA0B,CAAC,EAC7DlY,UAAU,CAACkD,cAAc,EAAE9D,UAAU,CAAC,CACzC;MAAEyU,cAAc,EAAE,CACfnR,gBAAgB,EAChBH,oBAAoB,EACpBI,mBAAmB,EACnBmL,wBAAwB,EACxBjL,YAAY,EACZiB,sBAAsB,EACtBzB,oBAAoB,EACpB0B,iBAAiB,CACpB;MAAEyG,IAAI,EAAE;QACLC,KAAK,EAAE,iBAAiB;QACxB,mBAAmB,EAAE,uCAAuC;QAC5D,gBAAgB,EAAE,yBAAyB;QAC3C,4BAA4B,EAAE,UAAU;QACxC,kBAAkB,EAAE,MAAM;QAC1B,SAAS,EAAE,wBAAwB;QACnC,uBAAuB,EAAE,yDAAyD;QAClF;QACA,kBAAkB,EAAE,gBAAgB;QACpC,qBAAqB,EAAE,UAAU;QACjC,wBAAwB,EAAE,mCAAmC;QAC7D,mBAAmB,EAAE,gCAAgC;QACrD,sBAAsB,EAAE,GAAG;QAC3B,4BAA4B,EAAE,eAAe;QAC7C,2BAA2B,EAAE;MACjC,CAAC;MAAEX,QAAQ,EAAE,ikEAAikE;MAAEE,MAAM,EAAE,CAAC,09tBAA09tB;IAAE,CAAC;EAClkyB,CAAC,CAAC,QAAkB;IAAEhC,IAAI,EAAE,CAAC;MACrBsB,IAAI,EAAE/J,YAAY;MAClB8K,IAAI,EAAE,CAACjK,OAAO,EAAE;QAAE0R,IAAI,EAAE5S,WAAW;QAAE6S,WAAW,EAAE;MAAK,CAAC;IAC5D,CAAC,CAAC;IAAEgH,IAAI,EAAE,CAAC;MACPzP,IAAI,EAAErK;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyb,YAAY,GAAG,CACjBta,OAAO,EACPC,QAAQ,EACRuV,SAAS,EACT3D,qBAAqB,EACrBsD,qBAAqB,EACrB3I,4BAA4B,EAC5Ba,6BAA6B,EAC7ByK,0BAA0B,CAC7B;AAED,SAASyC,kBAAkBA,CAACC,SAAS,EAAE;EACnC,MAAM;IAAE5L;EAAY,CAAC,GAAGpQ,MAAM,CAACqT,qBAAqB,CAAC;EACrD,OAAO9S,QAAQ,CAAC,MAAO6P,WAAW,CAAC,CAAC,CAAC6L,IAAI,CAACD,SAAS,CAAC,IAAI,IAAK,CAAC;AAClE;AAEA,MAAME,mBAAmB,CAAC;EACtBnQ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoQ,GAAG,GAAGnc,MAAM,CAACqT,qBAAqB,CAAC,CAACL,GAAG,EAAEoJ,kBAAkB,CAACpc,MAAM,CAACM,WAAW,CAAC,CAAC;EACzF;EACA+b,SAASA,CAAA,EAAG;IACR,IAAI,CAACF,GAAG,EAAEG,aAAa,CAAC,CAAC;EAC7B;EACA;IAAS,IAAI,CAACjS,IAAI,YAAAkS,4BAAAhS,CAAA;MAAA,YAAAA,CAAA,IAAyF2R,mBAAmB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAAC3P,IAAI,kBA7kB+E3M,EAAE,CAAA4M,iBAAA;MAAA9B,IAAA,EA6kBJwR,mBAAmB;MAAAvR,SAAA;MAAAE,UAAA;IAAA,EAAmF;EAAE;AAC3M;AACA;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KA/kBqG3L,EAAE,CAAA4L,iBAAA,CA+kBX0Q,mBAAmB,EAAc,CAAC;IAClHxR,IAAI,EAAEzK,SAAS;IACfwL,IAAI,EAAE,CAAC;MACCZ,UAAU,EAAE,IAAI;MAChBoC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASuP,uBAAuBA,CAACC,KAAK,EAAE;EACpC,MAAMjH,SAAS,GAAGxV,MAAM,CAACyN,qBAAqB,CAAC;EAC/C,MAAME,OAAO,GAAG3N,MAAM,CAACyc,KAAK,CAAC;EAC7B,OAAOlb,mBAAmB,CAAC2C,QAAQ,EAAE,SAAS,EAAE3D,QAAQ,CAAC,MAAMoN,OAAO,CAAC+O,IAAI,CAAClH,SAAS,CAACjI,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvG;;AAEA;AACA;AACA;;AAEA,SAASoB,sBAAsB,EAAElB,qBAAqB,EAAEuJ,SAAS,EAAElL,aAAa,EAAEgQ,YAAY,EAAEvJ,gBAAgB,EAAEnD,yBAAyB,EAAEiE,qBAAqB,EAAE6I,mBAAmB,EAAEvF,qBAAqB,EAAE9H,6BAA6B,EAAEuK,yBAAyB,EAAEE,0BAA0B,EAAEtL,4BAA4B,EAAE8I,gBAAgB,EAAE5H,wBAAwB,EAAEN,sBAAsB,EAAEmN,kBAAkB,EAAES,uBAAuB,EAAE9O,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}