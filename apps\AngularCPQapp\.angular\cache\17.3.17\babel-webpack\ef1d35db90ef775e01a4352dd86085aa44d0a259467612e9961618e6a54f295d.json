{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { TUI_NUMBER_FORMAT } from '@taiga-ui/core/tokens';\nimport { tuiFormatNumber } from '@taiga-ui/core/utils/format';\nimport { map } from 'rxjs';\nclass TuiFormatNumberPipe {\n  constructor() {\n    this.numberFormat = inject(TUI_NUMBER_FORMAT);\n  }\n  /**\n   * Formats number adding thousand separators and correct decimal separator\n   * padding decimal part with zeroes to given length\n   * @param value number\n   * @param settings See {@link TuiNumberFormatSettings}\n   */\n  transform(value, settings = {}) {\n    return this.numberFormat.pipe(map(format => tuiFormatNumber(value, {\n      ...format,\n      precision: Number.isNaN(format.precision) ? Infinity : format.precision,\n      ...settings\n    })));\n  }\n  static {\n    this.ɵfac = function TuiFormatNumberPipe_Factory(t) {\n      return new (t || TuiFormatNumberPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiFormatNumber\",\n      type: TuiFormatNumberPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFormatNumberPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiFormatNumber'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFormatNumberPipe };", "map": {"version": 3, "names": ["i0", "inject", "<PERSON><PERSON>", "TUI_NUMBER_FORMAT", "tuiFormatNumber", "map", "TuiFormatNumberPipe", "constructor", "numberFormat", "transform", "value", "settings", "pipe", "format", "precision", "Number", "isNaN", "Infinity", "ɵfac", "TuiFormatNumberPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-pipes-format-number.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { TUI_NUMBER_FORMAT } from '@taiga-ui/core/tokens';\nimport { tuiFormatNumber } from '@taiga-ui/core/utils/format';\nimport { map } from 'rxjs';\n\nclass TuiFormatNumberPipe {\n    constructor() {\n        this.numberFormat = inject(TUI_NUMBER_FORMAT);\n    }\n    /**\n     * Formats number adding thousand separators and correct decimal separator\n     * padding decimal part with zeroes to given length\n     * @param value number\n     * @param settings See {@link TuiNumberFormatSettings}\n     */\n    transform(value, settings = {}) {\n        return this.numberFormat.pipe(map((format) => tuiFormatNumber(value, {\n            ...format,\n            precision: Number.isNaN(format.precision)\n                ? Infinity\n                : format.precision,\n            ...settings,\n        })));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFormatNumberPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFormatNumberPipe, isStandalone: true, name: \"tuiFormatNumber\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFormatNumberPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiFormatNumber',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFormatNumberPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,IAAI,QAAQ,eAAe;AAC5C,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,GAAG,QAAQ,MAAM;AAE1B,MAAMC,mBAAmB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,YAAY,GAAGP,MAAM,CAACE,iBAAiB,CAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,SAASA,CAACC,KAAK,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAE;IAC5B,OAAO,IAAI,CAACH,YAAY,CAACI,IAAI,CAACP,GAAG,CAAEQ,MAAM,IAAKT,eAAe,CAACM,KAAK,EAAE;MACjE,GAAGG,MAAM;MACTC,SAAS,EAAEC,MAAM,CAACC,KAAK,CAACH,MAAM,CAACC,SAAS,CAAC,GACnCG,QAAQ,GACRJ,MAAM,CAACC,SAAS;MACtB,GAAGH;IACP,CAAC,CAAC,CAAC,CAAC;EACR;EACA;IAAS,IAAI,CAACO,IAAI,YAAAC,4BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFd,mBAAmB;IAAA,CAA8C;EAAE;EAC9K;IAAS,IAAI,CAACe,KAAK,kBAD8ErB,EAAE,CAAAsB,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMlB,mBAAmB;MAAAmB,IAAA;MAAAC,UAAA;IAAA,EAAgD;EAAE;AAClL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG3B,EAAE,CAAA4B,iBAAA,CAGXtB,mBAAmB,EAAc,CAAC;IAClHkB,IAAI,EAAEtB,IAAI;IACV2B,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASjB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}