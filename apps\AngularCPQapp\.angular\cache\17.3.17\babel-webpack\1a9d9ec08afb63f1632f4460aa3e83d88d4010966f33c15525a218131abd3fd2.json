{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { merge, fromEvent, filter } from 'rxjs';\nconst BUFFER = 1; // buffer for rounding issues\nclass TuiFadeStyles {\n  static {\n    this.ɵfac = function TuiFadeStyles_Factory(t) {\n      return new (t || TuiFadeStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiFadeStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-fade-styles\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiFadeStyles_Template(rf, ctx) {},\n      styles: [\"[tuiFade]{scrollbar-width:none;-ms-overflow-style:none;transition-property:-webkit-mask-position;transition-property:mask-position;transition-property:mask-position,-webkit-mask-position;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;overflow:auto;text-overflow:unset!important;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}[tuiFade]::-webkit-scrollbar,[tuiFade]::-webkit-scrollbar-thumb{display:none}[tuiFade]:not([data-orientation=vertical]){overflow-y:hidden;-webkit-mask-image:linear-gradient(to right,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to left,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(#000,#000);mask-image:linear-gradient(to right,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to left,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(#000,#000);-webkit-mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top;mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top;-webkit-mask-size:calc(51% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),calc(50% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),100% calc(100% - var(--t-line-height, 100%));mask-size:calc(51% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),calc(50% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),100% calc(100% - var(--t-line-height, 100%))}[tuiFade]:not([data-orientation=vertical])._start{-webkit-mask-position:left bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top;mask-position:left bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top}[tuiFade]:not([data-orientation=vertical])._end{-webkit-mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,right bottom,top;mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,right bottom,top}[tuiFade]:not([data-orientation=vertical])._start._end{-webkit-mask-position:left bottom,right bottom,top;mask-position:left bottom,right bottom,top}[tuiFade][data-orientation=vertical]{overflow-x:hidden;-webkit-mask-image:linear-gradient(to bottom,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to top,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset)));mask-image:linear-gradient(to bottom,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to top,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset)));-webkit-mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left calc(100% + var(--t-fade-size) + var(--t-fade-offset));mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left calc(100% + var(--t-fade-size) + var(--t-fade-offset));-webkit-mask-size:100% calc(51% + var(--t-fade-size) + var(--t-fade-offset));mask-size:100% calc(51% + var(--t-fade-size) + var(--t-fade-offset))}[tuiFade][data-orientation=vertical]._start{-webkit-mask-position:left top,left calc(100% + var(--t-fade-size) + var(--t-fade-offset));mask-position:left top,left calc(100% + var(--t-fade-size) + var(--t-fade-offset))}[tuiFade][data-orientation=vertical]._end{-webkit-mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left bottom;mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left bottom}[tuiFade][data-orientation=vertical]._start._end{-webkit-mask-position:left top,left bottom;mask-position:left top,left bottom}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFadeStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-fade-styles'\n      },\n      styles: [\"[tuiFade]{scrollbar-width:none;-ms-overflow-style:none;transition-property:-webkit-mask-position;transition-property:mask-position;transition-property:mask-position,-webkit-mask-position;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;overflow:auto;text-overflow:unset!important;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}[tuiFade]::-webkit-scrollbar,[tuiFade]::-webkit-scrollbar-thumb{display:none}[tuiFade]:not([data-orientation=vertical]){overflow-y:hidden;-webkit-mask-image:linear-gradient(to right,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to left,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(#000,#000);mask-image:linear-gradient(to right,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to left,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(#000,#000);-webkit-mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top;mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top;-webkit-mask-size:calc(51% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),calc(50% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),100% calc(100% - var(--t-line-height, 100%));mask-size:calc(51% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),calc(50% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),100% calc(100% - var(--t-line-height, 100%))}[tuiFade]:not([data-orientation=vertical])._start{-webkit-mask-position:left bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top;mask-position:left bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top}[tuiFade]:not([data-orientation=vertical])._end{-webkit-mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,right bottom,top;mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,right bottom,top}[tuiFade]:not([data-orientation=vertical])._start._end{-webkit-mask-position:left bottom,right bottom,top;mask-position:left bottom,right bottom,top}[tuiFade][data-orientation=vertical]{overflow-x:hidden;-webkit-mask-image:linear-gradient(to bottom,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to top,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset)));mask-image:linear-gradient(to bottom,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to top,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset)));-webkit-mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left calc(100% + var(--t-fade-size) + var(--t-fade-offset));mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left calc(100% + var(--t-fade-size) + var(--t-fade-offset));-webkit-mask-size:100% calc(51% + var(--t-fade-size) + var(--t-fade-offset));mask-size:100% calc(51% + var(--t-fade-size) + var(--t-fade-offset))}[tuiFade][data-orientation=vertical]._start{-webkit-mask-position:left top,left calc(100% + var(--t-fade-size) + var(--t-fade-offset));mask-position:left top,left calc(100% + var(--t-fade-size) + var(--t-fade-offset))}[tuiFade][data-orientation=vertical]._end{-webkit-mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left bottom;mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left bottom}[tuiFade][data-orientation=vertical]._start._end{-webkit-mask-position:left top,left bottom;mask-position:left top,left bottom}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiFade {\n  constructor() {\n    // TODO: Remove when lh CSS units are supported: https://caniuse.com/mdn-css_types_length_lh\n    this.lineHeight = null;\n    this.size = '1.5em';\n    this.offset = '0em';\n    this.orientation = 'horizontal';\n    const el = tuiInjectElement();\n    tuiWithStyles(TuiFadeStyles);\n    merge(inject(ResizeObserverService, {\n      self: true\n    }), inject(MutationObserverService, {\n      self: true\n    }), fromEvent(el, 'scroll')).pipe(filter(() => !!el.scrollWidth), tuiZonefree(), takeUntilDestroyed()).subscribe(() => {\n      el.classList.toggle('_start', !!el.scrollLeft || !!el.scrollTop);\n      el.classList.toggle('_end', this.isEnd(el));\n    });\n  }\n  isEnd(el) {\n    if (this.orientation === 'vertical') {\n      return Math.round(el.scrollTop) < el.scrollHeight - el.clientHeight - BUFFER;\n    }\n    return el.clientWidth && Math.round(el.scrollLeft) < el.scrollWidth - el.clientWidth - BUFFER ||\n    // horizontal multiline fade can kick in early due to hanging elements of fonts so using bigger buffer\n    el.scrollHeight > el.clientHeight + 4 * BUFFER;\n  }\n  static {\n    this.ɵfac = function TuiFade_Factory(t) {\n      return new (t || TuiFade)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiFade,\n      selectors: [[\"\", \"tuiFade\", \"\"]],\n      hostVars: 9,\n      hostBindings: function TuiFade_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-orientation\", ctx.orientation);\n          i0.ɵɵstyleProp(\"line-height\", ctx.lineHeight)(\"--t-line-height\", ctx.lineHeight)(\"--t-fade-size\", ctx.size)(\"--t-fade-offset\", ctx.offset);\n        }\n      },\n      inputs: {\n        lineHeight: [i0.ɵɵInputFlags.None, \"tuiFadeHeight\", \"lineHeight\"],\n        size: [i0.ɵɵInputFlags.None, \"tuiFadeSize\", \"size\"],\n        offset: [i0.ɵɵInputFlags.None, \"tuiFadeOffset\", \"offset\"],\n        orientation: [i0.ɵɵInputFlags.None, \"tuiFade\", \"orientation\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([ResizeObserverService, MutationObserverService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          characterData: true,\n          subtree: true\n        }\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFade, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiFade]',\n      providers: [ResizeObserverService, MutationObserverService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          characterData: true,\n          subtree: true\n        }\n      }],\n      host: {\n        '[style.line-height]': 'lineHeight',\n        '[style.--t-line-height]': 'lineHeight',\n        '[style.--t-fade-size]': 'size',\n        '[style.--t-fade-offset]': 'offset',\n        '[attr.data-orientation]': 'orientation'\n      }\n    }]\n  }], function () {\n    return [];\n  }, {\n    lineHeight: [{\n      type: Input,\n      args: ['tuiFadeHeight']\n    }],\n    size: [{\n      type: Input,\n      args: ['tuiFadeSize']\n    }],\n    offset: [{\n      type: Input,\n      args: ['tuiFadeOffset']\n    }],\n    orientation: [{\n      type: Input,\n      args: ['tuiFade']\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFade };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "Directive", "Input", "takeUntilDestroyed", "MutationObserverService", "WA_MUTATION_OBSERVER_INIT", "ResizeObserverService", "tuiZonefree", "tuiInjectElement", "tuiWithStyles", "merge", "fromEvent", "filter", "BUFFER", "TuiFadeStyles", "ɵfac", "TuiFadeStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiFadeStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiFade", "constructor", "lineHeight", "size", "offset", "orientation", "el", "self", "pipe", "scrollWidth", "subscribe", "classList", "toggle", "scrollLeft", "scrollTop", "isEnd", "Math", "round", "scrollHeight", "clientHeight", "clientWidth", "TuiFade_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiFade_HostBindings", "ɵɵattribute", "ɵɵstyleProp", "inputs", "ɵɵInputFlags", "ɵɵProvidersFeature", "provide", "useValue", "characterData", "subtree", "selector", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-fade.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { merge, fromEvent, filter } from 'rxjs';\n\nconst BUFFER = 1; // buffer for rounding issues\nclass TuiFadeStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFadeStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiFadeStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-fade-styles\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiFade]{scrollbar-width:none;-ms-overflow-style:none;transition-property:-webkit-mask-position;transition-property:mask-position;transition-property:mask-position,-webkit-mask-position;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;overflow:auto;text-overflow:unset!important;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}[tuiFade]::-webkit-scrollbar,[tuiFade]::-webkit-scrollbar-thumb{display:none}[tuiFade]:not([data-orientation=vertical]){overflow-y:hidden;-webkit-mask-image:linear-gradient(to right,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to left,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(#000,#000);mask-image:linear-gradient(to right,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to left,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(#000,#000);-webkit-mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top;mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top;-webkit-mask-size:calc(51% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),calc(50% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),100% calc(100% - var(--t-line-height, 100%));mask-size:calc(51% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),calc(50% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),100% calc(100% - var(--t-line-height, 100%))}[tuiFade]:not([data-orientation=vertical])._start{-webkit-mask-position:left bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top;mask-position:left bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top}[tuiFade]:not([data-orientation=vertical])._end{-webkit-mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,right bottom,top;mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,right bottom,top}[tuiFade]:not([data-orientation=vertical])._start._end{-webkit-mask-position:left bottom,right bottom,top;mask-position:left bottom,right bottom,top}[tuiFade][data-orientation=vertical]{overflow-x:hidden;-webkit-mask-image:linear-gradient(to bottom,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to top,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset)));mask-image:linear-gradient(to bottom,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to top,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset)));-webkit-mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left calc(100% + var(--t-fade-size) + var(--t-fade-offset));mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left calc(100% + var(--t-fade-size) + var(--t-fade-offset));-webkit-mask-size:100% calc(51% + var(--t-fade-size) + var(--t-fade-offset));mask-size:100% calc(51% + var(--t-fade-size) + var(--t-fade-offset))}[tuiFade][data-orientation=vertical]._start{-webkit-mask-position:left top,left calc(100% + var(--t-fade-size) + var(--t-fade-offset));mask-position:left top,left calc(100% + var(--t-fade-size) + var(--t-fade-offset))}[tuiFade][data-orientation=vertical]._end{-webkit-mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left bottom;mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left bottom}[tuiFade][data-orientation=vertical]._start._end{-webkit-mask-position:left top,left bottom;mask-position:left top,left bottom}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFadeStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-fade-styles',\n                    }, styles: [\"[tuiFade]{scrollbar-width:none;-ms-overflow-style:none;transition-property:-webkit-mask-position;transition-property:mask-position;transition-property:mask-position,-webkit-mask-position;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;overflow:auto;text-overflow:unset!important;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}[tuiFade]::-webkit-scrollbar,[tuiFade]::-webkit-scrollbar-thumb{display:none}[tuiFade]:not([data-orientation=vertical]){overflow-y:hidden;-webkit-mask-image:linear-gradient(to right,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to left,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(#000,#000);mask-image:linear-gradient(to right,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to left,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(#000,#000);-webkit-mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top;mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top;-webkit-mask-size:calc(51% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),calc(50% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),100% calc(100% - var(--t-line-height, 100%));mask-size:calc(51% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),calc(50% + var(--t-fade-size) + var(--t-fade-offset)) var(--t-line-height, 100%),100% calc(100% - var(--t-line-height, 100%))}[tuiFade]:not([data-orientation=vertical])._start{-webkit-mask-position:left bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top;mask-position:left bottom,calc(100% + var(--t-fade-size) + var(--t-fade-offset)) bottom,top}[tuiFade]:not([data-orientation=vertical])._end{-webkit-mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,right bottom,top;mask-position:calc(-1 * var(--t-fade-size) - var(--t-fade-offset)) bottom,right bottom,top}[tuiFade]:not([data-orientation=vertical])._start._end{-webkit-mask-position:left bottom,right bottom,top;mask-position:left bottom,right bottom,top}[tuiFade][data-orientation=vertical]{overflow-x:hidden;-webkit-mask-image:linear-gradient(to bottom,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to top,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset)));mask-image:linear-gradient(to bottom,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset))),linear-gradient(to top,transparent var(--t-fade-offset),#000 calc(var(--t-fade-size) + var(--t-fade-offset)));-webkit-mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left calc(100% + var(--t-fade-size) + var(--t-fade-offset));mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left calc(100% + var(--t-fade-size) + var(--t-fade-offset));-webkit-mask-size:100% calc(51% + var(--t-fade-size) + var(--t-fade-offset));mask-size:100% calc(51% + var(--t-fade-size) + var(--t-fade-offset))}[tuiFade][data-orientation=vertical]._start{-webkit-mask-position:left top,left calc(100% + var(--t-fade-size) + var(--t-fade-offset));mask-position:left top,left calc(100% + var(--t-fade-size) + var(--t-fade-offset))}[tuiFade][data-orientation=vertical]._end{-webkit-mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left bottom;mask-position:left calc(-1 * var(--t-fade-size) - var(--t-fade-offset)),left bottom}[tuiFade][data-orientation=vertical]._start._end{-webkit-mask-position:left top,left bottom;mask-position:left top,left bottom}\\n\"] }]\n        }] });\nclass TuiFade {\n    constructor() {\n        // TODO: Remove when lh CSS units are supported: https://caniuse.com/mdn-css_types_length_lh\n        this.lineHeight = null;\n        this.size = '1.5em';\n        this.offset = '0em';\n        this.orientation = 'horizontal';\n        const el = tuiInjectElement();\n        tuiWithStyles(TuiFadeStyles);\n        merge(inject(ResizeObserverService, { self: true }), inject(MutationObserverService, { self: true }), fromEvent(el, 'scroll'))\n            .pipe(filter(() => !!el.scrollWidth), tuiZonefree(), takeUntilDestroyed())\n            .subscribe(() => {\n            el.classList.toggle('_start', !!el.scrollLeft || !!el.scrollTop);\n            el.classList.toggle('_end', this.isEnd(el));\n        });\n    }\n    isEnd(el) {\n        if (this.orientation === 'vertical') {\n            return Math.round(el.scrollTop) < el.scrollHeight - el.clientHeight - BUFFER;\n        }\n        return ((el.clientWidth &&\n            Math.round(el.scrollLeft) < el.scrollWidth - el.clientWidth - BUFFER) ||\n            // horizontal multiline fade can kick in early due to hanging elements of fonts so using bigger buffer\n            el.scrollHeight > el.clientHeight + 4 * BUFFER);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFade, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiFade, isStandalone: true, selector: \"[tuiFade]\", inputs: { lineHeight: [\"tuiFadeHeight\", \"lineHeight\"], size: [\"tuiFadeSize\", \"size\"], offset: [\"tuiFadeOffset\", \"offset\"], orientation: [\"tuiFade\", \"orientation\"] }, host: { properties: { \"style.line-height\": \"lineHeight\", \"style.--t-line-height\": \"lineHeight\", \"style.--t-fade-size\": \"size\", \"style.--t-fade-offset\": \"offset\", \"attr.data-orientation\": \"orientation\" } }, providers: [\n            ResizeObserverService,\n            MutationObserverService,\n            {\n                provide: WA_MUTATION_OBSERVER_INIT,\n                useValue: { characterData: true, subtree: true },\n            },\n        ], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFade, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiFade]',\n                    providers: [\n                        ResizeObserverService,\n                        MutationObserverService,\n                        {\n                            provide: WA_MUTATION_OBSERVER_INIT,\n                            useValue: { characterData: true, subtree: true },\n                        },\n                    ],\n                    host: {\n                        '[style.line-height]': 'lineHeight',\n                        '[style.--t-line-height]': 'lineHeight',\n                        '[style.--t-fade-size]': 'size',\n                        '[style.--t-fade-offset]': 'offset',\n                        '[attr.data-orientation]': 'orientation',\n                    },\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { lineHeight: [{\n                type: Input,\n                args: ['tuiFadeHeight']\n            }], size: [{\n                type: Input,\n                args: ['tuiFadeSize']\n            }], offset: [{\n                type: Input,\n                args: ['tuiFadeOffset']\n            }], orientation: [{\n                type: Input,\n                args: ['tuiFade']\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFade };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAC/G,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,uBAAuB,EAAEC,yBAAyB,QAAQ,gCAAgC;AACnG,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,KAAK,EAAEC,SAAS,EAAEC,MAAM,QAAQ,MAAM;AAE/C,MAAMC,MAAM,GAAG,CAAC,CAAC,CAAC;AAClB,MAAMC,aAAa,CAAC;EAChB;IAAS,IAAI,CAACC,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,aAAa;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACI,IAAI,kBAD+EtB,EAAE,CAAAuB,iBAAA;MAAAC,IAAA,EACJN,aAAa;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADX5B,EAAE,CAAA6B,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACmkI;EAAE;AAC5qI;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGvC,EAAE,CAAAwC,iBAAA,CAGXtB,aAAa,EAAc,CAAC;IAC5GM,IAAI,EAAEvB,SAAS;IACfwC,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAEnC,iBAAiB,CAACwC,IAAI;MAAEJ,eAAe,EAAEnC,uBAAuB,CAACwC,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,s0HAAs0H;IAAE,CAAC;EACj2H,CAAC,CAAC;AAAA;AACV,MAAMU,OAAO,CAAC;EACVC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,IAAI,GAAG,OAAO;IACnB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,WAAW,GAAG,YAAY;IAC/B,MAAMC,EAAE,GAAGxC,gBAAgB,CAAC,CAAC;IAC7BC,aAAa,CAACK,aAAa,CAAC;IAC5BJ,KAAK,CAACV,MAAM,CAACM,qBAAqB,EAAE;MAAE2C,IAAI,EAAE;IAAK,CAAC,CAAC,EAAEjD,MAAM,CAACI,uBAAuB,EAAE;MAAE6C,IAAI,EAAE;IAAK,CAAC,CAAC,EAAEtC,SAAS,CAACqC,EAAE,EAAE,QAAQ,CAAC,CAAC,CACzHE,IAAI,CAACtC,MAAM,CAAC,MAAM,CAAC,CAACoC,EAAE,CAACG,WAAW,CAAC,EAAE5C,WAAW,CAAC,CAAC,EAAEJ,kBAAkB,CAAC,CAAC,CAAC,CACzEiD,SAAS,CAAC,MAAM;MACjBJ,EAAE,CAACK,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAACN,EAAE,CAACO,UAAU,IAAI,CAAC,CAACP,EAAE,CAACQ,SAAS,CAAC;MAChER,EAAE,CAACK,SAAS,CAACC,MAAM,CAAC,MAAM,EAAE,IAAI,CAACG,KAAK,CAACT,EAAE,CAAC,CAAC;IAC/C,CAAC,CAAC;EACN;EACAS,KAAKA,CAACT,EAAE,EAAE;IACN,IAAI,IAAI,CAACD,WAAW,KAAK,UAAU,EAAE;MACjC,OAAOW,IAAI,CAACC,KAAK,CAACX,EAAE,CAACQ,SAAS,CAAC,GAAGR,EAAE,CAACY,YAAY,GAAGZ,EAAE,CAACa,YAAY,GAAGhD,MAAM;IAChF;IACA,OAASmC,EAAE,CAACc,WAAW,IACnBJ,IAAI,CAACC,KAAK,CAACX,EAAE,CAACO,UAAU,CAAC,GAAGP,EAAE,CAACG,WAAW,GAAGH,EAAE,CAACc,WAAW,GAAGjD,MAAM;IACpE;IACAmC,EAAE,CAACY,YAAY,GAAGZ,EAAE,CAACa,YAAY,GAAG,CAAC,GAAGhD,MAAM;EACtD;EACA;IAAS,IAAI,CAACE,IAAI,YAAAgD,gBAAA9C,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,OAAO;IAAA,CAAmD;EAAE;EACvK;IAAS,IAAI,CAACsB,IAAI,kBAnC+EpE,EAAE,CAAAqE,iBAAA;MAAA7C,IAAA,EAmCJsB,OAAO;MAAArB,SAAA;MAAA6C,QAAA;MAAAC,YAAA,WAAAC,qBAAAtC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnCLlC,EAAE,CAAAyE,WAAA,qBAAAtC,GAAA,CAAAgB,WAAA;UAAFnD,EAAE,CAAA0E,WAAA,gBAAAvC,GAAA,CAAAa,UAmCE,CAAC,oBAAAb,GAAA,CAAAa,UAAD,CAAC,kBAAAb,GAAA,CAAAc,IAAD,CAAC,oBAAAd,GAAA,CAAAe,MAAD,CAAC;QAAA;MAAA;MAAAyB,MAAA;QAAA3B,UAAA,GAnCLhD,EAAE,CAAA4E,YAAA,CAAAlC,IAAA;QAAAO,IAAA,GAAFjD,EAAE,CAAA4E,YAAA,CAAAlC,IAAA;QAAAQ,MAAA,GAAFlD,EAAE,CAAA4E,YAAA,CAAAlC,IAAA;QAAAS,WAAA,GAAFnD,EAAE,CAAA4E,YAAA,CAAAlC,IAAA;MAAA;MAAAf,UAAA;MAAAC,QAAA,GAAF5B,EAAE,CAAA6E,kBAAA,CAmC+a,CAC1gBnE,qBAAqB,EACrBF,uBAAuB,EACvB;QACIsE,OAAO,EAAErE,yBAAyB;QAClCsE,QAAQ,EAAE;UAAEC,aAAa,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAK;MACnD,CAAC,CACJ;IAAA,EAAiB;EAAE;AAC5B;AACA;EAAA,QAAA1C,SAAA,oBAAAA,SAAA,KA5CqGvC,EAAE,CAAAwC,iBAAA,CA4CXM,OAAO,EAAc,CAAC;IACtGtB,IAAI,EAAEnB,SAAS;IACfoC,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBuD,QAAQ,EAAE,WAAW;MACrBC,SAAS,EAAE,CACPzE,qBAAqB,EACrBF,uBAAuB,EACvB;QACIsE,OAAO,EAAErE,yBAAyB;QAClCsE,QAAQ,EAAE;UAAEC,aAAa,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAK;MACnD,CAAC,CACJ;MACDrC,IAAI,EAAE;QACF,qBAAqB,EAAE,YAAY;QACnC,yBAAyB,EAAE,YAAY;QACvC,uBAAuB,EAAE,MAAM;QAC/B,yBAAyB,EAAE,QAAQ;QACnC,yBAAyB,EAAE;MAC/B;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEI,UAAU,EAAE,CAAC;MACvExB,IAAI,EAAElB,KAAK;MACXmC,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEQ,IAAI,EAAE,CAAC;MACPzB,IAAI,EAAElB,KAAK;MACXmC,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAES,MAAM,EAAE,CAAC;MACT1B,IAAI,EAAElB,KAAK;MACXmC,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEU,WAAW,EAAE,CAAC;MACd3B,IAAI,EAAElB,KAAK;MACXmC,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASK,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}