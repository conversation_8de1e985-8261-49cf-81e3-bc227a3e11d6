{"ast": null, "code": "import { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { TUI_IS_IOS } from '@taiga-ui/cdk/tokens';\nimport { tuiIsSafari } from '@taiga-ui/cdk/utils/browser';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiSizeBigger } from '@taiga-ui/core/utils/miscellaneous';\nimport { PolymorpheusOutlet, PolymorpheusTemplate } from '@taiga-ui/polymorpheus';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\n\n/** Default values for the loader options. */\nconst _c0 = [\"*\"];\nfunction TuiLoader_div_2_div_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r1, \" \");\n  }\n}\nfunction TuiLoader_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, TuiLoader_div_2_div_3_ng_container_1_Template, 2, 1, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"t-text_horizontal\", ctx_r1.isHorizontal);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r1.textContent);\n  }\n}\nfunction TuiLoader_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 3);\n    i0.ɵɵelement(2, \"circle\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TuiLoader_div_2_div_3_Template, 2, 3, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"t-loader_horizontal\", ctx_r1.isHorizontal)(\"t-loader_inherit-color\", ctx_r1.inheritColor);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.textContent);\n  }\n}\nconst TUI_LOADER_DEFAULT_OPTIONS = {\n  size: 'm',\n  inheritColor: false,\n  overlay: false\n};\n/**\n * Default parameters for loader component\n */\nconst TUI_LOADER_OPTIONS = tuiCreateToken(TUI_LOADER_DEFAULT_OPTIONS);\nfunction tuiLoaderOptionsProvider(options) {\n  return tuiProvideOptions(TUI_LOADER_OPTIONS, options, TUI_LOADER_DEFAULT_OPTIONS);\n}\nclass TuiLoader {\n  constructor() {\n    this.isIOS = inject(TUI_IS_IOS);\n    this.options = inject(TUI_LOADER_OPTIONS);\n    this.isApple = tuiIsSafari(tuiInjectElement()) || this.isIOS;\n    this.size = this.options.size;\n    this.inheritColor = this.options.inheritColor;\n    this.overlay = this.options.overlay;\n    // TODO: Drop alias in v5\n    this.loading = true;\n  }\n  get isHorizontal() {\n    return !tuiSizeBigger(this.size);\n  }\n  static {\n    this.ɵfac = function TuiLoader_Factory(t) {\n      return new (t || TuiLoader)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiLoader,\n      selectors: [[\"tui-loader\"]],\n      hostVars: 3,\n      hostBindings: function TuiLoader_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n          i0.ɵɵclassProp(\"_loading\", ctx.loading);\n        }\n      },\n      inputs: {\n        size: \"size\",\n        inheritColor: \"inheritColor\",\n        overlay: \"overlay\",\n        textContent: \"textContent\",\n        loading: [i0.ɵɵInputFlags.None, \"showLoader\", \"loading\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 7,\n      consts: [[1, \"t-content\", 3, \"disabled\"], [\"class\", \"t-loader\", 3, \"t-loader_horizontal\", \"t-loader_inherit-color\", 4, \"ngIf\"], [1, \"t-loader\"], [\"automation-id\", \"tui-loader__loader\", \"focusable\", \"false\", \"height\", \"100%\", \"width\", \"100%\", 1, \"t-icon\"], [\"cx\", \"50%\", \"cy\", \"50%\", 1, \"t-circle\"], [\"automation-id\", \"tui-loader__text\", \"class\", \"t-text\", 3, \"t-text_horizontal\", 4, \"ngIf\"], [\"automation-id\", \"tui-loader__text\", 1, \"t-text\"], [4, \"polymorpheusOutlet\"]],\n      template: function TuiLoader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"fieldset\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, TuiLoader_div_2_Template, 4, 5, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"t-content_has-overlay\", ctx.overlay && ctx.loading)(\"t-content_loading\", ctx.loading);\n          i0.ɵɵproperty(\"disabled\", ctx.loading && !ctx.isApple);\n          i0.ɵɵattribute(\"inert\", ctx.loading || null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [NgIf, PolymorpheusOutlet],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:flex;min-inline-size:1.5rem;--tui-thickness: calc(var(--t-diameter) / 12)}._loading[_nghost-%COMP%]{overflow:hidden}[data-size=xs][_nghost-%COMP%]{--t-diameter: .75em}[data-size=s][_nghost-%COMP%]{--t-diameter: 1em}[data-size=m][_nghost-%COMP%]{--t-diameter: 1.5em}[data-size=l][_nghost-%COMP%]{--t-diameter: 2.5em}[data-size=xl][_nghost-%COMP%]{--t-diameter: 3.5em}[data-size=xxl][_nghost-%COMP%]{--t-diameter: 5em}.t-content[_ngcontent-%COMP%]{z-index:0;min-inline-size:100%;block-size:100%;padding:0;margin:0;border:none}.t-content_has-overlay[_ngcontent-%COMP%]{opacity:.3}.t-content_loading[_ngcontent-%COMP%]{pointer-events:none}.t-loader[_ngcontent-%COMP%]{position:relative;left:-100%;display:flex;flex-direction:column;align-items:center;justify-content:center;min-inline-size:100%;min-block-size:var(--t-diameter);flex-shrink:0;align-self:center;color:var(--tui-text-primary);stroke:var(--tui-background-accent-1);animation:tuiFadeIn var(--tui-duration);font-size:1rem}@supports (inset-inline-start: -100%){.t-loader[_ngcontent-%COMP%]{left:unset;inset-inline-start:-100%}}.t-loader.t-loader_horizontal[_ngcontent-%COMP%]{flex-direction:row}.t-loader.t-loader_inherit-color[_ngcontent-%COMP%]{color:inherit;stroke:currentColor}.t-text[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font:var(--tui-font-text-s);margin-block-start:1rem;color:inherit;max-inline-size:100%;text-align:center;stroke-width:0}.t-text_horizontal[_ngcontent-%COMP%]{margin:0;margin-inline-start:1rem}@keyframes _ngcontent-%COMP%_tuiLoaderRotate{0%{transform:rotate(-90deg)}50%{transform:rotate(-90deg) rotate(1turn)}to{transform:rotate(-90deg) rotate(3turn)}}.t-icon[_ngcontent-%COMP%]{display:block;inline-size:var(--t-diameter);block-size:var(--t-diameter);margin:.25rem calc(var(--t-diameter) / -2);border-radius:100%;overflow:hidden;animation:_ngcontent-%COMP%_tuiLoaderRotate 4s linear infinite}@supports (-webkit-hyphens: none){.t-icon[_ngcontent-%COMP%]{overflow:visible}}@keyframes _ngcontent-%COMP%_tuiLoaderDashOffset{0%{stroke-dashoffset:calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness)))}50%{stroke-dashoffset:calc(.05 * calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness))))}to{stroke-dashoffset:calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness)))}}.t-circle[_ngcontent-%COMP%]{r:calc(var(--t-diameter) / 2 - var(--tui-thickness));stroke-dasharray:calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness)));fill:none;stroke:inherit;stroke-width:max(var(--tui-thickness),1.5px);animation:_ngcontent-%COMP%_tuiLoaderDashOffset 4s linear infinite}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiLoader, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-loader',\n      imports: [NgIf, PolymorpheusOutlet, PolymorpheusTemplate],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class._loading]': 'loading',\n        '[attr.data-size]': 'size'\n      },\n      template: \"<fieldset\\n    class=\\\"t-content\\\"\\n    [attr.inert]=\\\"loading || null\\\"\\n    [class.t-content_has-overlay]=\\\"overlay && loading\\\"\\n    [class.t-content_loading]=\\\"loading\\\"\\n    [disabled]=\\\"loading && !isApple\\\"\\n>\\n    <ng-content />\\n</fieldset>\\n\\n<div\\n    *ngIf=\\\"loading\\\"\\n    class=\\\"t-loader\\\"\\n    [class.t-loader_horizontal]=\\\"isHorizontal\\\"\\n    [class.t-loader_inherit-color]=\\\"inheritColor\\\"\\n>\\n    <svg\\n        automation-id=\\\"tui-loader__loader\\\"\\n        focusable=\\\"false\\\"\\n        height=\\\"100%\\\"\\n        width=\\\"100%\\\"\\n        class=\\\"t-icon\\\"\\n    >\\n        <circle\\n            cx=\\\"50%\\\"\\n            cy=\\\"50%\\\"\\n            class=\\\"t-circle\\\"\\n        />\\n    </svg>\\n\\n    <div\\n        *ngIf=\\\"textContent\\\"\\n        automation-id=\\\"tui-loader__text\\\"\\n        class=\\\"t-text\\\"\\n        [class.t-text_horizontal]=\\\"isHorizontal\\\"\\n    >\\n        <ng-container *polymorpheusOutlet=\\\"textContent as text\\\">\\n            {{ text }}\\n        </ng-container>\\n    </div>\\n</div>\\n\",\n      styles: [\":host{position:relative;display:flex;min-inline-size:1.5rem;--tui-thickness: calc(var(--t-diameter) / 12)}:host._loading{overflow:hidden}:host[data-size=xs]{--t-diameter: .75em}:host[data-size=s]{--t-diameter: 1em}:host[data-size=m]{--t-diameter: 1.5em}:host[data-size=l]{--t-diameter: 2.5em}:host[data-size=xl]{--t-diameter: 3.5em}:host[data-size=xxl]{--t-diameter: 5em}.t-content{z-index:0;min-inline-size:100%;block-size:100%;padding:0;margin:0;border:none}.t-content_has-overlay{opacity:.3}.t-content_loading{pointer-events:none}.t-loader{position:relative;left:-100%;display:flex;flex-direction:column;align-items:center;justify-content:center;min-inline-size:100%;min-block-size:var(--t-diameter);flex-shrink:0;align-self:center;color:var(--tui-text-primary);stroke:var(--tui-background-accent-1);animation:tuiFadeIn var(--tui-duration);font-size:1rem}@supports (inset-inline-start: -100%){.t-loader{left:unset;inset-inline-start:-100%}}.t-loader.t-loader_horizontal{flex-direction:row}.t-loader.t-loader_inherit-color{color:inherit;stroke:currentColor}.t-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font:var(--tui-font-text-s);margin-block-start:1rem;color:inherit;max-inline-size:100%;text-align:center;stroke-width:0}.t-text_horizontal{margin:0;margin-inline-start:1rem}@keyframes tuiLoaderRotate{0%{transform:rotate(-90deg)}50%{transform:rotate(-90deg) rotate(1turn)}to{transform:rotate(-90deg) rotate(3turn)}}.t-icon{display:block;inline-size:var(--t-diameter);block-size:var(--t-diameter);margin:.25rem calc(var(--t-diameter) / -2);border-radius:100%;overflow:hidden;animation:tuiLoaderRotate 4s linear infinite}@supports (-webkit-hyphens: none){.t-icon{overflow:visible}}@keyframes tuiLoaderDashOffset{0%{stroke-dashoffset:calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness)))}50%{stroke-dashoffset:calc(.05 * calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness))))}to{stroke-dashoffset:calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness)))}}.t-circle{r:calc(var(--t-diameter) / 2 - var(--tui-thickness));stroke-dasharray:calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness)));fill:none;stroke:inherit;stroke-width:max(var(--tui-thickness),1.5px);animation:tuiLoaderDashOffset 4s linear infinite}\\n\"]\n    }]\n  }], null, {\n    size: [{\n      type: Input\n    }],\n    inheritColor: [{\n      type: Input\n    }],\n    overlay: [{\n      type: Input\n    }],\n    textContent: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input,\n      args: ['showLoader']\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_LOADER_DEFAULT_OPTIONS, TUI_LOADER_OPTIONS, TuiLoader, tuiLoaderOptionsProvider };", "map": {"version": 3, "names": ["NgIf", "i0", "inject", "Component", "ChangeDetectionStrategy", "Input", "TUI_IS_IOS", "tui<PERSON>s<PERSON><PERSON><PERSON>", "tuiInjectElement", "tui<PERSON>izeBigger", "Polymorpheus<PERSON><PERSON>let", "PolymorpheusTemplate", "tuiCreateToken", "tuiProvideOptions", "_c0", "Tui<PERSON><PERSON>der_div_2_div_3_ng_container_1_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "text_r1", "polymorpheusOutlet", "ɵɵadvance", "ɵɵtextInterpolate1", "Tui<PERSON><PERSON>der_div_2_div_3_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "ctx_r1", "ɵɵnextContext", "ɵɵclassProp", "isHorizontal", "ɵɵproperty", "textContent", "Tui<PERSON><PERSON>der_div_2_Template", "ɵɵnamespaceSVG", "ɵɵelement", "inheritColor", "TUI_LOADER_DEFAULT_OPTIONS", "size", "overlay", "TUI_LOADER_OPTIONS", "tuiLoaderOptionsProvider", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "isIOS", "isApple", "loading", "ɵfac", "TuiLoader_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "TuiLoader_HostBindings", "ɵɵattribute", "inputs", "ɵɵInputFlags", "None", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "Tui<PERSON><PERSON>der_Template", "ɵɵprojectionDef", "ɵɵprojection", "dependencies", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-components-loader.mjs"], "sourcesContent": ["import { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { TUI_IS_IOS } from '@taiga-ui/cdk/tokens';\nimport { tuiIsSafari } from '@taiga-ui/cdk/utils/browser';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiSizeBigger } from '@taiga-ui/core/utils/miscellaneous';\nimport { PolymorpheusOutlet, PolymorpheusTemplate } from '@taiga-ui/polymorpheus';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\n\n/** Default values for the loader options. */\nconst TUI_LOADER_DEFAULT_OPTIONS = {\n    size: 'm',\n    inheritColor: false,\n    overlay: false,\n};\n/**\n * Default parameters for loader component\n */\nconst TUI_LOADER_OPTIONS = tuiCreateToken(TUI_LOADER_DEFAULT_OPTIONS);\nfunction tuiLoaderOptionsProvider(options) {\n    return tuiProvideOptions(TUI_LOADER_OPTIONS, options, TUI_LOADER_DEFAULT_OPTIONS);\n}\n\nclass TuiLoader {\n    constructor() {\n        this.isIOS = inject(TUI_IS_IOS);\n        this.options = inject(TUI_LOADER_OPTIONS);\n        this.isApple = tuiIsSafari(tuiInjectElement()) || this.isIOS;\n        this.size = this.options.size;\n        this.inheritColor = this.options.inheritColor;\n        this.overlay = this.options.overlay;\n        // TODO: Drop alias in v5\n        this.loading = true;\n    }\n    get isHorizontal() {\n        return !tuiSizeBigger(this.size);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLoader, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiLoader, isStandalone: true, selector: \"tui-loader\", inputs: { size: \"size\", inheritColor: \"inheritColor\", overlay: \"overlay\", textContent: \"textContent\", loading: [\"showLoader\", \"loading\"] }, host: { properties: { \"class._loading\": \"loading\", \"attr.data-size\": \"size\" } }, ngImport: i0, template: \"<fieldset\\n    class=\\\"t-content\\\"\\n    [attr.inert]=\\\"loading || null\\\"\\n    [class.t-content_has-overlay]=\\\"overlay && loading\\\"\\n    [class.t-content_loading]=\\\"loading\\\"\\n    [disabled]=\\\"loading && !isApple\\\"\\n>\\n    <ng-content />\\n</fieldset>\\n\\n<div\\n    *ngIf=\\\"loading\\\"\\n    class=\\\"t-loader\\\"\\n    [class.t-loader_horizontal]=\\\"isHorizontal\\\"\\n    [class.t-loader_inherit-color]=\\\"inheritColor\\\"\\n>\\n    <svg\\n        automation-id=\\\"tui-loader__loader\\\"\\n        focusable=\\\"false\\\"\\n        height=\\\"100%\\\"\\n        width=\\\"100%\\\"\\n        class=\\\"t-icon\\\"\\n    >\\n        <circle\\n            cx=\\\"50%\\\"\\n            cy=\\\"50%\\\"\\n            class=\\\"t-circle\\\"\\n        />\\n    </svg>\\n\\n    <div\\n        *ngIf=\\\"textContent\\\"\\n        automation-id=\\\"tui-loader__text\\\"\\n        class=\\\"t-text\\\"\\n        [class.t-text_horizontal]=\\\"isHorizontal\\\"\\n    >\\n        <ng-container *polymorpheusOutlet=\\\"textContent as text\\\">\\n            {{ text }}\\n        </ng-container>\\n    </div>\\n</div>\\n\", styles: [\":host{position:relative;display:flex;min-inline-size:1.5rem;--tui-thickness: calc(var(--t-diameter) / 12)}:host._loading{overflow:hidden}:host[data-size=xs]{--t-diameter: .75em}:host[data-size=s]{--t-diameter: 1em}:host[data-size=m]{--t-diameter: 1.5em}:host[data-size=l]{--t-diameter: 2.5em}:host[data-size=xl]{--t-diameter: 3.5em}:host[data-size=xxl]{--t-diameter: 5em}.t-content{z-index:0;min-inline-size:100%;block-size:100%;padding:0;margin:0;border:none}.t-content_has-overlay{opacity:.3}.t-content_loading{pointer-events:none}.t-loader{position:relative;left:-100%;display:flex;flex-direction:column;align-items:center;justify-content:center;min-inline-size:100%;min-block-size:var(--t-diameter);flex-shrink:0;align-self:center;color:var(--tui-text-primary);stroke:var(--tui-background-accent-1);animation:tuiFadeIn var(--tui-duration);font-size:1rem}@supports (inset-inline-start: -100%){.t-loader{left:unset;inset-inline-start:-100%}}.t-loader.t-loader_horizontal{flex-direction:row}.t-loader.t-loader_inherit-color{color:inherit;stroke:currentColor}.t-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font:var(--tui-font-text-s);margin-block-start:1rem;color:inherit;max-inline-size:100%;text-align:center;stroke-width:0}.t-text_horizontal{margin:0;margin-inline-start:1rem}@keyframes tuiLoaderRotate{0%{transform:rotate(-90deg)}50%{transform:rotate(-90deg) rotate(1turn)}to{transform:rotate(-90deg) rotate(3turn)}}.t-icon{display:block;inline-size:var(--t-diameter);block-size:var(--t-diameter);margin:.25rem calc(var(--t-diameter) / -2);border-radius:100%;overflow:hidden;animation:tuiLoaderRotate 4s linear infinite}@supports (-webkit-hyphens: none){.t-icon{overflow:visible}}@keyframes tuiLoaderDashOffset{0%{stroke-dashoffset:calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness)))}50%{stroke-dashoffset:calc(.05 * calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness))))}to{stroke-dashoffset:calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness)))}}.t-circle{r:calc(var(--t-diameter) / 2 - var(--tui-thickness));stroke-dasharray:calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness)));fill:none;stroke:inherit;stroke-width:max(var(--tui-thickness),1.5px);animation:tuiLoaderDashOffset 4s linear infinite}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLoader, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-loader', imports: [NgIf, PolymorpheusOutlet, PolymorpheusTemplate], changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[class._loading]': 'loading',\n                        '[attr.data-size]': 'size',\n                    }, template: \"<fieldset\\n    class=\\\"t-content\\\"\\n    [attr.inert]=\\\"loading || null\\\"\\n    [class.t-content_has-overlay]=\\\"overlay && loading\\\"\\n    [class.t-content_loading]=\\\"loading\\\"\\n    [disabled]=\\\"loading && !isApple\\\"\\n>\\n    <ng-content />\\n</fieldset>\\n\\n<div\\n    *ngIf=\\\"loading\\\"\\n    class=\\\"t-loader\\\"\\n    [class.t-loader_horizontal]=\\\"isHorizontal\\\"\\n    [class.t-loader_inherit-color]=\\\"inheritColor\\\"\\n>\\n    <svg\\n        automation-id=\\\"tui-loader__loader\\\"\\n        focusable=\\\"false\\\"\\n        height=\\\"100%\\\"\\n        width=\\\"100%\\\"\\n        class=\\\"t-icon\\\"\\n    >\\n        <circle\\n            cx=\\\"50%\\\"\\n            cy=\\\"50%\\\"\\n            class=\\\"t-circle\\\"\\n        />\\n    </svg>\\n\\n    <div\\n        *ngIf=\\\"textContent\\\"\\n        automation-id=\\\"tui-loader__text\\\"\\n        class=\\\"t-text\\\"\\n        [class.t-text_horizontal]=\\\"isHorizontal\\\"\\n    >\\n        <ng-container *polymorpheusOutlet=\\\"textContent as text\\\">\\n            {{ text }}\\n        </ng-container>\\n    </div>\\n</div>\\n\", styles: [\":host{position:relative;display:flex;min-inline-size:1.5rem;--tui-thickness: calc(var(--t-diameter) / 12)}:host._loading{overflow:hidden}:host[data-size=xs]{--t-diameter: .75em}:host[data-size=s]{--t-diameter: 1em}:host[data-size=m]{--t-diameter: 1.5em}:host[data-size=l]{--t-diameter: 2.5em}:host[data-size=xl]{--t-diameter: 3.5em}:host[data-size=xxl]{--t-diameter: 5em}.t-content{z-index:0;min-inline-size:100%;block-size:100%;padding:0;margin:0;border:none}.t-content_has-overlay{opacity:.3}.t-content_loading{pointer-events:none}.t-loader{position:relative;left:-100%;display:flex;flex-direction:column;align-items:center;justify-content:center;min-inline-size:100%;min-block-size:var(--t-diameter);flex-shrink:0;align-self:center;color:var(--tui-text-primary);stroke:var(--tui-background-accent-1);animation:tuiFadeIn var(--tui-duration);font-size:1rem}@supports (inset-inline-start: -100%){.t-loader{left:unset;inset-inline-start:-100%}}.t-loader.t-loader_horizontal{flex-direction:row}.t-loader.t-loader_inherit-color{color:inherit;stroke:currentColor}.t-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font:var(--tui-font-text-s);margin-block-start:1rem;color:inherit;max-inline-size:100%;text-align:center;stroke-width:0}.t-text_horizontal{margin:0;margin-inline-start:1rem}@keyframes tuiLoaderRotate{0%{transform:rotate(-90deg)}50%{transform:rotate(-90deg) rotate(1turn)}to{transform:rotate(-90deg) rotate(3turn)}}.t-icon{display:block;inline-size:var(--t-diameter);block-size:var(--t-diameter);margin:.25rem calc(var(--t-diameter) / -2);border-radius:100%;overflow:hidden;animation:tuiLoaderRotate 4s linear infinite}@supports (-webkit-hyphens: none){.t-icon{overflow:visible}}@keyframes tuiLoaderDashOffset{0%{stroke-dashoffset:calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness)))}50%{stroke-dashoffset:calc(.05 * calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness))))}to{stroke-dashoffset:calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness)))}}.t-circle{r:calc(var(--t-diameter) / 2 - var(--tui-thickness));stroke-dasharray:calc(2 * 3.14159265 * calc(var(--t-diameter) / 2 - var(--tui-thickness)));fill:none;stroke:inherit;stroke-width:max(var(--tui-thickness),1.5px);animation:tuiLoaderDashOffset 4s linear infinite}\\n\"] }]\n        }], propDecorators: { size: [{\n                type: Input\n            }], inheritColor: [{\n                type: Input\n            }], overlay: [{\n                type: Input\n            }], textContent: [{\n                type: Input\n            }], loading: [{\n                type: Input,\n                args: ['showLoader']\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_LOADER_DEFAULT_OPTIONS, TUI_LOADER_OPTIONS, TuiLoader, tuiLoaderOptionsProvider };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,iBAAiB;AACtC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,QAAQ,eAAe;AACjF,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,kBAAkB,EAAEC,oBAAoB,QAAQ,wBAAwB;AACjF,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,mCAAmC;;AAErF;AAAA,MAAAC,GAAA;AAAA,SAAAC,8CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA4BqGf,EAAE,CAAAiB,uBAAA,EACktC,CAAC;IADrtCjB,EAAE,CAAAkB,MAAA,EACovC,CAAC;IADvvClB,EAAE,CAAAmB,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAAFrB,EAAE,CAAAsB,SAAA,CACovC,CAAC;IADvvCtB,EAAE,CAAAuB,kBAAA,MAAAH,OAAA,KACovC,CAAC;EAAA;AAAA;AAAA,SAAAI,+BAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADvvCf,EAAE,CAAAyB,cAAA,YAC8oC,CAAC;IADjpCzB,EAAE,CAAA0B,UAAA,IAAAZ,6CAAA,yBACktC,CAAC;IADrtCd,EAAE,CAAA2B,YAAA,CAC+wC,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAa,MAAA,GADlxC5B,EAAE,CAAA6B,aAAA;IAAF7B,EAAE,CAAA8B,WAAA,sBAAAF,MAAA,CAAAG,YACuoC,CAAC;IAD1oC/B,EAAE,CAAAsB,SAAA,CACwsC,CAAC;IAD3sCtB,EAAE,CAAAgC,UAAA,uBAAAJ,MAAA,CAAAK,WACwsC,CAAC;EAAA;AAAA;AAAA,SAAAC,yBAAAnB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD3sCf,EAAE,CAAAyB,cAAA,YACksB,CAAC;IADrsBzB,EAAE,CAAAmC,cAAA;IAAFnC,EAAE,CAAAyB,cAAA,YACy2B,CAAC;IAD52BzB,EAAE,CAAAoC,SAAA,eACs9B,CAAC;IADz9BpC,EAAE,CAAA2B,YAAA,CACk+B,CAAC;IADr+B3B,EAAE,CAAA0B,UAAA,IAAAF,8BAAA,gBAC8oC,CAAC;IADjpCxB,EAAE,CAAA2B,YAAA,CACuxC,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAa,MAAA,GAD1xC5B,EAAE,CAAA6B,aAAA;IAAF7B,EAAE,CAAA8B,WAAA,wBAAAF,MAAA,CAAAG,YAC0oB,CAAC,2BAAAH,MAAA,CAAAS,YAAoD,CAAC;IADlsBrC,EAAE,CAAAsB,SAAA,EAC2gC,CAAC;IAD9gCtB,EAAE,CAAAgC,UAAA,SAAAJ,MAAA,CAAAK,WAC2gC,CAAC;EAAA;AAAA;AA5BnnC,MAAMK,0BAA0B,GAAG;EAC/BC,IAAI,EAAE,GAAG;EACTF,YAAY,EAAE,KAAK;EACnBG,OAAO,EAAE;AACb,CAAC;AACD;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG9B,cAAc,CAAC2B,0BAA0B,CAAC;AACrE,SAASI,wBAAwBA,CAACC,OAAO,EAAE;EACvC,OAAO/B,iBAAiB,CAAC6B,kBAAkB,EAAEE,OAAO,EAAEL,0BAA0B,CAAC;AACrF;AAEA,MAAMM,SAAS,CAAC;EACZC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG7C,MAAM,CAACI,UAAU,CAAC;IAC/B,IAAI,CAACsC,OAAO,GAAG1C,MAAM,CAACwC,kBAAkB,CAAC;IACzC,IAAI,CAACM,OAAO,GAAGzC,WAAW,CAACC,gBAAgB,CAAC,CAAC,CAAC,IAAI,IAAI,CAACuC,KAAK;IAC5D,IAAI,CAACP,IAAI,GAAG,IAAI,CAACI,OAAO,CAACJ,IAAI;IAC7B,IAAI,CAACF,YAAY,GAAG,IAAI,CAACM,OAAO,CAACN,YAAY;IAC7C,IAAI,CAACG,OAAO,GAAG,IAAI,CAACG,OAAO,CAACH,OAAO;IACnC;IACA,IAAI,CAACQ,OAAO,GAAG,IAAI;EACvB;EACA,IAAIjB,YAAYA,CAAA,EAAG;IACf,OAAO,CAACvB,aAAa,CAAC,IAAI,CAAC+B,IAAI,CAAC;EACpC;EACA;IAAS,IAAI,CAACU,IAAI,YAAAC,kBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFP,SAAS;IAAA,CAAmD;EAAE;EACzK;IAAS,IAAI,CAACQ,IAAI,kBAD+EpD,EAAE,CAAAqD,iBAAA;MAAAC,IAAA,EACJV,SAAS;MAAAW,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,uBAAA3C,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADPf,EAAE,CAAA2D,WAAA,cAAA3C,GAAA,CAAAuB,IAAA;UAAFvC,EAAE,CAAA8B,WAAA,aAAAd,GAAA,CAAAgC,OACI,CAAC;QAAA;MAAA;MAAAY,MAAA;QAAArB,IAAA;QAAAF,YAAA;QAAAG,OAAA;QAAAP,WAAA;QAAAe,OAAA,GADPhD,EAAE,CAAA6D,YAAA,CAAAC,IAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFhE,EAAE,CAAAiE,mBAAA;MAAAC,kBAAA,EAAArD,GAAA;MAAAsD,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mBAAAxD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFf,EAAE,CAAAwE,eAAA;UAAFxE,EAAE,CAAAyB,cAAA,iBACggB,CAAC;UADngBzB,EAAE,CAAAyE,YAAA,EACohB,CAAC;UADvhBzE,EAAE,CAAA2B,YAAA,CACiiB,CAAC;UADpiB3B,EAAE,CAAA0B,UAAA,IAAAQ,wBAAA,gBACksB,CAAC;QAAA;QAAA,IAAAnB,EAAA;UADrsBf,EAAE,CAAA8B,WAAA,0BAAAd,GAAA,CAAAwB,OAAA,IAAAxB,GAAA,CAAAgC,OAC0a,CAAC,sBAAAhC,GAAA,CAAAgC,OAA0C,CAAC;UADxdhD,EAAE,CAAAgC,UAAA,aAAAhB,GAAA,CAAAgC,OAAA,KAAAhC,GAAA,CAAA+B,OAC6f,CAAC;UADhgB/C,EAAE,CAAA2D,WAAA,UAAA3C,GAAA,CAAAgC,OAAA;UAAFhD,EAAE,CAAAsB,SAAA,EAC8jB,CAAC;UADjkBtB,EAAE,CAAAgC,UAAA,SAAAhB,GAAA,CAAAgC,OAC8jB,CAAC;QAAA;MAAA;MAAA0B,YAAA,GAA0hG3E,IAAI,EAA6FU,kBAAkB;MAAAkE,MAAA;MAAAC,eAAA;IAAA,EAAyJ;EAAE;AAC98H;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG7E,EAAE,CAAA8E,iBAAA,CAGXlC,SAAS,EAAc,CAAC;IACxGU,IAAI,EAAEpD,SAAS;IACf6E,IAAI,EAAE,CAAC;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,QAAQ,EAAE,YAAY;MAAEC,OAAO,EAAE,CAAClF,IAAI,EAAEU,kBAAkB,EAAEC,oBAAoB,CAAC;MAAEkE,eAAe,EAAEzE,uBAAuB,CAAC+E,MAAM;MAAEC,IAAI,EAAE;QACzJ,kBAAkB,EAAE,SAAS;QAC7B,kBAAkB,EAAE;MACxB,CAAC;MAAEb,QAAQ,EAAE,m/BAAm/B;MAAEK,MAAM,EAAE,CAAC,swEAAswE;IAAE,CAAC;EAChyG,CAAC,CAAC,QAAkB;IAAEpC,IAAI,EAAE,CAAC;MACrBe,IAAI,EAAElD;IACV,CAAC,CAAC;IAAEiC,YAAY,EAAE,CAAC;MACfiB,IAAI,EAAElD;IACV,CAAC,CAAC;IAAEoC,OAAO,EAAE,CAAC;MACVc,IAAI,EAAElD;IACV,CAAC,CAAC;IAAE6B,WAAW,EAAE,CAAC;MACdqB,IAAI,EAAElD;IACV,CAAC,CAAC;IAAE4C,OAAO,EAAE,CAAC;MACVM,IAAI,EAAElD,KAAK;MACX2E,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASzC,0BAA0B,EAAEG,kBAAkB,EAAEG,SAAS,EAAEF,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}