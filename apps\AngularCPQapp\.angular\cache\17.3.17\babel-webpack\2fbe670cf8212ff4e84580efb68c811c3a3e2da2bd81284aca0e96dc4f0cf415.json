{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Injectable, Directive, Output } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { Observable, merge, switchMap, filter, map, pairwise, takeUntil, repeat } from 'rxjs';\nclass TuiPanService extends Observable {\n  constructor() {\n    const el = tuiInjectElement();\n    const doc = inject(DOCUMENT);\n    super(subscriber => merge(tuiTypedFromEvent(el, 'touchstart', {\n      passive: true\n    }), tuiTypedFromEvent(el, 'mousedown')).pipe(switchMap(() => merge(tuiTypedFromEvent(doc, 'touchmove', {\n      passive: true\n    }).pipe(filter(({\n      touches\n    }) => touches.length < 2), map(({\n      touches\n    }) => touches[0])), tuiTypedFromEvent(doc, 'mousemove'))), pairwise(), map(([first, second]) => {\n      const deltaX = (second?.clientX ?? 0) - (first?.clientX ?? 0);\n      const deltaY = (second?.clientY ?? 0) - (first?.clientY ?? 0);\n      return [deltaX, deltaY];\n    }), takeUntil(merge(tuiTypedFromEvent(doc, 'touchend'), tuiTypedFromEvent(doc, 'mouseup'))), repeat()).subscribe(subscriber));\n  }\n  static {\n    this.ɵfac = function TuiPanService_Factory(t) {\n      return new (t || TuiPanService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiPanService,\n      factory: TuiPanService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPanService, [{\n    type: Injectable\n  }], function () {\n    return [];\n  }, null);\n})();\nclass TuiPan {\n  constructor() {\n    this.tuiPan = inject(TuiPanService);\n  }\n  static {\n    this.ɵfac = function TuiPan_Factory(t) {\n      return new (t || TuiPan)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiPan,\n      selectors: [[\"\", \"tuiPan\", \"\"]],\n      outputs: {\n        tuiPan: \"tuiPan\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiPanService])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPan, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiPan]',\n      providers: [TuiPanService]\n    }]\n  }], null, {\n    tuiPan: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPan, TuiPanService };", "map": {"version": 3, "names": ["i0", "inject", "Injectable", "Directive", "Output", "DOCUMENT", "tuiTypedFromEvent", "tuiInjectElement", "Observable", "merge", "switchMap", "filter", "map", "pairwise", "takeUntil", "repeat", "TuiPanService", "constructor", "el", "doc", "subscriber", "passive", "pipe", "touches", "length", "first", "second", "deltaX", "clientX", "deltaY", "clientY", "subscribe", "ɵfac", "TuiPanService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "<PERSON><PERSON><PERSON><PERSON>", "tui<PERSON>an", "TuiPan_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "outputs", "standalone", "features", "ɵɵProvidersFeature", "args", "selector", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-pan.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Injectable, Directive, Output } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { Observable, merge, switchMap, filter, map, pairwise, takeUntil, repeat } from 'rxjs';\n\nclass TuiPanService extends Observable {\n    constructor() {\n        const el = tuiInjectElement();\n        const doc = inject(DOCUMENT);\n        super((subscriber) => merge(tuiTypedFromEvent(el, 'touchstart', { passive: true }), tuiTypedFromEvent(el, 'mousedown'))\n            .pipe(switchMap(() => merge(tuiTypedFromEvent(doc, 'touchmove', {\n            passive: true,\n        }).pipe(filter(({ touches }) => touches.length < 2), map(({ touches }) => touches[0])), tuiTypedFromEvent(doc, 'mousemove'))), pairwise(), map(([first, second]) => {\n            const deltaX = (second?.clientX ?? 0) - (first?.clientX ?? 0);\n            const deltaY = (second?.clientY ?? 0) - (first?.clientY ?? 0);\n            return [deltaX, deltaY];\n        }), takeUntil(merge(tuiTypedFromEvent(doc, 'touchend'), tuiTypedFromEvent(doc, 'mouseup'))), repeat())\n            .subscribe(subscriber));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPanService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPanService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPanService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return []; } });\n\nclass TuiPan {\n    constructor() {\n        this.tuiPan = inject(TuiPanService);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPan, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPan, isStandalone: true, selector: \"[tuiPan]\", outputs: { tuiPan: \"tuiPan\" }, providers: [TuiPanService], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPan, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiPan]',\n                    providers: [TuiPanService],\n                }]\n        }], propDecorators: { tuiPan: [{\n                type: Output\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPan, TuiPanService };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACrE,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,MAAM;AAE7F,MAAMC,aAAa,SAASR,UAAU,CAAC;EACnCS,WAAWA,CAAA,EAAG;IACV,MAAMC,EAAE,GAAGX,gBAAgB,CAAC,CAAC;IAC7B,MAAMY,GAAG,GAAGlB,MAAM,CAACI,QAAQ,CAAC;IAC5B,KAAK,CAAEe,UAAU,IAAKX,KAAK,CAACH,iBAAiB,CAACY,EAAE,EAAE,YAAY,EAAE;MAAEG,OAAO,EAAE;IAAK,CAAC,CAAC,EAAEf,iBAAiB,CAACY,EAAE,EAAE,WAAW,CAAC,CAAC,CAClHI,IAAI,CAACZ,SAAS,CAAC,MAAMD,KAAK,CAACH,iBAAiB,CAACa,GAAG,EAAE,WAAW,EAAE;MAChEE,OAAO,EAAE;IACb,CAAC,CAAC,CAACC,IAAI,CAACX,MAAM,CAAC,CAAC;MAAEY;IAAQ,CAAC,KAAKA,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC,EAAEZ,GAAG,CAAC,CAAC;MAAEW;IAAQ,CAAC,KAAKA,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEjB,iBAAiB,CAACa,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,EAAEN,QAAQ,CAAC,CAAC,EAAED,GAAG,CAAC,CAAC,CAACa,KAAK,EAAEC,MAAM,CAAC,KAAK;MAChK,MAAMC,MAAM,GAAG,CAACD,MAAM,EAAEE,OAAO,IAAI,CAAC,KAAKH,KAAK,EAAEG,OAAO,IAAI,CAAC,CAAC;MAC7D,MAAMC,MAAM,GAAG,CAACH,MAAM,EAAEI,OAAO,IAAI,CAAC,KAAKL,KAAK,EAAEK,OAAO,IAAI,CAAC,CAAC;MAC7D,OAAO,CAACH,MAAM,EAAEE,MAAM,CAAC;IAC3B,CAAC,CAAC,EAAEf,SAAS,CAACL,KAAK,CAACH,iBAAiB,CAACa,GAAG,EAAE,UAAU,CAAC,EAAEb,iBAAiB,CAACa,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAAC,CAAC,CAAC,CACjGgB,SAAS,CAACX,UAAU,CAAC,CAAC;EAC/B;EACA;IAAS,IAAI,CAACY,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFlB,aAAa;IAAA,CAAoD;EAAE;EAC9K;IAAS,IAAI,CAACmB,KAAK,kBAD8EnC,EAAE,CAAAoC,kBAAA;MAAAC,KAAA,EACYrB,aAAa;MAAAsB,OAAA,EAAbtB,aAAa,CAAAgB;IAAA,EAAG;EAAE;AACrI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAHqGvC,EAAE,CAAAwC,iBAAA,CAGXxB,aAAa,EAAc,CAAC;IAC5GyB,IAAI,EAAEvC;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAMwC,MAAM,CAAC;EACTzB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0B,MAAM,GAAG1C,MAAM,CAACe,aAAa,CAAC;EACvC;EACA;IAAS,IAAI,CAACgB,IAAI,YAAAY,eAAAV,CAAA;MAAA,YAAAA,CAAA,IAAyFQ,MAAM;IAAA,CAAmD;EAAE;EACtK;IAAS,IAAI,CAACG,IAAI,kBAZ+E7C,EAAE,CAAA8C,iBAAA;MAAAL,IAAA,EAYJC,MAAM;MAAAK,SAAA;MAAAC,OAAA;QAAAL,MAAA;MAAA;MAAAM,UAAA;MAAAC,QAAA,GAZJlD,EAAE,CAAAmD,kBAAA,CAYwF,CAACnC,aAAa,CAAC;IAAA,EAAiB;EAAE;AACjO;AACA;EAAA,QAAAuB,SAAA,oBAAAA,SAAA,KAdqGvC,EAAE,CAAAwC,iBAAA,CAcXE,MAAM,EAAc,CAAC;IACrGD,IAAI,EAAEtC,SAAS;IACfiD,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,UAAU;MACpBC,SAAS,EAAE,CAACtC,aAAa;IAC7B,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE2B,MAAM,EAAE,CAAC;MACvBF,IAAI,EAAErC;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASsC,MAAM,EAAE1B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}