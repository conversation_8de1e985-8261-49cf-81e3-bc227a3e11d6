{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nclass TuiStatusStyles {\n  static {\n    this.ɵfac = function TuiStatusStyles_Factory(t) {\n      return new (t || TuiStatusStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiStatusStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-status\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiStatusStyles_Template(rf, ctx) {},\n      styles: [\"[tuiStatus]{display:inline-flex;align-items:center;gap:.5rem}[tuiStatus]:before{content:\\\"\\\";display:var(--t-status, none);inline-size:.5rem;block-size:.5rem;border-radius:100%;background:var(--t-status)}[tuiStatus][data-size=s]{gap:.125rem}[tuiStatus][data-size=m],[tuiStatus][data-size=l]{gap:.25rem}[tuiStatus][data-size=xl]{gap:.375rem}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiStatusStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-status'\n      },\n      styles: [\"[tuiStatus]{display:inline-flex;align-items:center;gap:.5rem}[tuiStatus]:before{content:\\\"\\\";display:var(--t-status, none);inline-size:.5rem;block-size:.5rem;border-radius:100%;background:var(--t-status)}[tuiStatus][data-size=s]{gap:.125rem}[tuiStatus][data-size=m],[tuiStatus][data-size=l]{gap:.25rem}[tuiStatus][data-size=xl]{gap:.375rem}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiStatus {\n  constructor() {\n    this.nothing = tuiWithStyles(TuiStatusStyles);\n    this.tuiStatus = '';\n  }\n  static {\n    this.ɵfac = function TuiStatus_Factory(t) {\n      return new (t || TuiStatus)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiStatus,\n      selectors: [[\"\", \"tuiStatus\", \"\"]],\n      hostAttrs: [\"tuiStatus\", \"\"],\n      hostVars: 2,\n      hostBindings: function TuiStatus_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-status\", ctx.tuiStatus || null);\n        }\n      },\n      inputs: {\n        tuiStatus: \"tuiStatus\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiStatus, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiStatus]',\n      inputs: ['tuiStatus'],\n      host: {\n        tuiStatus: '',\n        '[style.--t-status]': 'tuiStatus || null'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiStatus };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Directive", "tuiWithStyles", "TuiStatusStyles", "ɵfac", "TuiStatusStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiStatusStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiStatus", "constructor", "nothing", "tui<PERSON><PERSON>us", "TuiStatus_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiStatus_HostBindings", "ɵɵstyleProp", "inputs", "selector"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-status.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\n\nclass TuiStatusStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStatusStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiStatusStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-status\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiStatus]{display:inline-flex;align-items:center;gap:.5rem}[tuiStatus]:before{content:\\\"\\\";display:var(--t-status, none);inline-size:.5rem;block-size:.5rem;border-radius:100%;background:var(--t-status)}[tuiStatus][data-size=s]{gap:.125rem}[tuiStatus][data-size=m],[tuiStatus][data-size=l]{gap:.25rem}[tuiStatus][data-size=xl]{gap:.375rem}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStatusStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-status',\n                    }, styles: [\"[tuiStatus]{display:inline-flex;align-items:center;gap:.5rem}[tuiStatus]:before{content:\\\"\\\";display:var(--t-status, none);inline-size:.5rem;block-size:.5rem;border-radius:100%;background:var(--t-status)}[tuiStatus][data-size=s]{gap:.125rem}[tuiStatus][data-size=m],[tuiStatus][data-size=l]{gap:.25rem}[tuiStatus][data-size=xl]{gap:.375rem}\\n\"] }]\n        }] });\nclass TuiStatus {\n    constructor() {\n        this.nothing = tuiWithStyles(TuiStatusStyles);\n        this.tuiStatus = '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStatus, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiStatus, isStandalone: true, selector: \"[tuiStatus]\", inputs: { tuiStatus: \"tuiStatus\" }, host: { attributes: { \"tuiStatus\": \"\" }, properties: { \"style.--t-status\": \"tuiStatus || null\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStatus, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiStatus]',\n                    inputs: ['tuiStatus'],\n                    host: {\n                        tuiStatus: '',\n                        '[style.--t-status]': 'tuiStatus || null',\n                    },\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiStatus };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,QAAQ,eAAe;AAChG,SAASC,aAAa,QAAQ,mCAAmC;AAEjE,MAAMC,eAAe,CAAC;EAClB;IAAS,IAAI,CAACC,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACI,IAAI,kBAD+EV,EAAE,CAAAW,iBAAA;MAAAC,IAAA,EACJN,eAAe;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADbhB,EAAE,CAAAiB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACklB;EAAE;AAC3rB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG3B,EAAE,CAAA4B,iBAAA,CAGXtB,eAAe,EAAc,CAAC;IAC9GM,IAAI,EAAEX,SAAS;IACf4B,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAEvB,iBAAiB,CAAC4B,IAAI;MAAEJ,eAAe,EAAEvB,uBAAuB,CAAC4B,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,wVAAwV;IAAE,CAAC;EACnX,CAAC,CAAC;AAAA;AACV,MAAMU,SAAS,CAAC;EACZC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG/B,aAAa,CAACC,eAAe,CAAC;IAC7C,IAAI,CAAC+B,SAAS,GAAG,EAAE;EACvB;EACA;IAAS,IAAI,CAAC9B,IAAI,YAAA+B,kBAAA7B,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,SAAS;IAAA,CAAmD;EAAE;EACzK;IAAS,IAAI,CAACK,IAAI,kBAf+EvC,EAAE,CAAAwC,iBAAA;MAAA5B,IAAA,EAeJsB,SAAS;MAAArB,SAAA;MAAAC,SAAA,gBAAsH,EAAE;MAAA2B,QAAA;MAAAC,YAAA,WAAAC,uBAAArB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAf/HtB,EAAE,CAAA4C,WAAA,eAAArB,GAAA,CAAAc,SAAA,IAeS,IAAL,CAAC;QAAA;MAAA;MAAAQ,MAAA;QAAAR,SAAA;MAAA;MAAAtB,UAAA;IAAA,EAAsM;EAAE;AACpT;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAjBqG3B,EAAE,CAAA4B,iBAAA,CAiBXM,SAAS,EAAc,CAAC;IACxGtB,IAAI,EAAER,SAAS;IACfyB,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChB+B,QAAQ,EAAE,aAAa;MACvBD,MAAM,EAAE,CAAC,WAAW,CAAC;MACrBb,IAAI,EAAE;QACFK,SAAS,EAAE,EAAE;QACb,oBAAoB,EAAE;MAC1B;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}