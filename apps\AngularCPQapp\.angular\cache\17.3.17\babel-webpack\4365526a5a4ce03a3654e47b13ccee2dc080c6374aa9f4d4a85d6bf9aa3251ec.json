{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Pi<PERSON> } from '@angular/core';\nclass TuiInitialsPipe {\n  transform(text) {\n    return text.toUpperCase().split(' ').map(([char]) => char).join('').slice(0, 2);\n  }\n  static {\n    this.ɵfac = function TuiInitialsPipe_Factory(t) {\n      return new (t || TuiInitialsPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiInitials\",\n      type: TuiInitialsPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInitialsPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiInitials'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiInitialsPipe };", "map": {"version": 3, "names": ["i0", "<PERSON><PERSON>", "TuiInitialsPipe", "transform", "text", "toUpperCase", "split", "map", "char", "join", "slice", "ɵfac", "TuiInitialsPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-pipes-initials.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Pi<PERSON> } from '@angular/core';\n\nclass TuiInitialsPipe {\n    transform(text) {\n        return text\n            .toUpperCase()\n            .split(' ')\n            .map(([char]) => char)\n            .join('')\n            .slice(0, 2);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInitialsPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInitialsPipe, isStandalone: true, name: \"tuiInitials\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInitialsPipe, decorators: [{\n            type: <PERSON><PERSON>,\n            args: [{\n                    standalone: true,\n                    name: 'tuiInitials',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiInitialsPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,IAAI,QAAQ,eAAe;AAEpC,MAAMC,eAAe,CAAC;EAClBC,SAASA,CAACC,IAAI,EAAE;IACZ,OAAOA,IAAI,CACNC,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,KAAKA,IAAI,CAAC,CACrBC,IAAI,CAAC,EAAE,CAAC,CACRC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACpB;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFX,eAAe;IAAA,CAA8C;EAAE;EAC1K;IAAS,IAAI,CAACY,KAAK,kBAD8Ed,EAAE,CAAAe,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMf,eAAe;MAAAgB,IAAA;MAAAC,UAAA;IAAA,EAA4C;EAAE;AAC1K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGpB,EAAE,CAAAqB,iBAAA,CAGXnB,eAAe,EAAc,CAAC;IAC9Ge,IAAI,EAAEhB,IAAI;IACVqB,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASd,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}