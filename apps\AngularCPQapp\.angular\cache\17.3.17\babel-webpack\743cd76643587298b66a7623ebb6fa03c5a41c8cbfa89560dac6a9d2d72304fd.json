{"ast": null, "code": "import { TuiItem } from '@taiga-ui/cdk/directives/item';\nimport { __decorate } from 'tslib';\nimport { isPlatformServer, AsyncPipe, NgForOf, NgIf, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, NgZone, Directive, Input, Output, ChangeDetectorRef, EventEmitter, TemplateRef, Component, ChangeDetectionStrategy, ContentChildren } from '@angular/core';\nimport * as i2 from '@ng-web-apis/intersection-observer';\nimport { WaIntersectionObserver } from '@ng-web-apis/intersection-observer';\nimport { TUI_FALSE_HANDLER, TUI_TRUE_HANDLER, EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { TuiPan } from '@taiga-ui/cdk/directives/pan';\nimport { TUI_SWIPE_OPTIONS, TuiSwipe } from '@taiga-ui/cdk/directives/swipe';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { WA_PAGE_VISIBILITY } from '@ng-web-apis/common';\nimport { tuiTypedFromEvent, tuiIfMap, tuiZoneOptimized, tuiZonefreeScheduler } from '@taiga-ui/cdk/observables';\nimport { Observable, BehaviorSubject, merge, map, EMPTY, combineLatest, interval, filter, throttleTime, tap } from 'rxjs';\nimport { tuiButtonOptionsProvider } from '@taiga-ui/core/components/button';\nfunction TuiCarouselComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiCarouselComponent_fieldset_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"fieldset\", 5);\n    i0.ɵɵlistener(\"waIntersectionObservee\", function TuiCarouselComponent_fieldset_5_Template_fieldset_waIntersectionObservee_0_listener($event) {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView($event[0] && ctx_r2.onIntersection($event[0], i_r2));\n    });\n    i0.ɵɵelementContainer(1, 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r2.getStyle(ctx_r2.itemsCount));\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isDisabled(i_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", item_r4);\n  }\n}\nclass TuiCarouselDirective extends Observable {\n  constructor() {\n    super(subscriber => this.output$.subscribe(subscriber));\n    this.el = tuiInjectElement();\n    this.platform = inject(PLATFORM_ID);\n    this.visible$ = inject(WA_PAGE_VISIBILITY);\n    this.zone = inject(NgZone);\n    this.duration$ = new BehaviorSubject(0);\n    this.running$ = merge(tuiTypedFromEvent(this.el, 'mouseenter').pipe(map(TUI_FALSE_HANDLER)), tuiTypedFromEvent(this.el, 'touchstart').pipe(map(TUI_FALSE_HANDLER)), tuiTypedFromEvent(this.el, 'touchend').pipe(map(TUI_TRUE_HANDLER)), tuiTypedFromEvent(this.el, 'mouseleave').pipe(map(TUI_TRUE_HANDLER)), this.visible$);\n    this.output$ = isPlatformServer(this.platform) ? EMPTY : combineLatest([this.duration$, this.running$]).pipe(tuiIfMap(([duration]) => interval(duration).pipe(tuiZoneOptimized(this.zone)), values => values.every(Boolean)));\n  }\n  set duration(duration) {\n    this.duration$.next(Number.isNaN(duration) ? this.duration$.value : duration);\n  }\n  static {\n    this.ɵfac = function TuiCarouselDirective_Factory(t) {\n      return new (t || TuiCarouselDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiCarouselDirective,\n      inputs: {\n        duration: \"duration\"\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCarouselDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], function () {\n    return [];\n  }, {\n    duration: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiCarouselAutoscroll {\n  constructor() {\n    this.tuiCarouselAutoscroll = inject(TuiCarouselDirective);\n  }\n  static {\n    this.ɵfac = function TuiCarouselAutoscroll_Factory(t) {\n      return new (t || TuiCarouselAutoscroll)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiCarouselAutoscroll,\n      selectors: [[\"\", \"tuiCarouselAutoscroll\", \"\"]],\n      outputs: {\n        tuiCarouselAutoscroll: \"tuiCarouselAutoscroll\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCarouselAutoscroll, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiCarouselAutoscroll]'\n    }]\n  }], null, {\n    tuiCarouselAutoscroll: [{\n      type: Output\n    }]\n  });\n})();\nclass TuiCarouselScroll {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.tuiCarouselScroll = tuiTypedFromEvent(this.el, 'wheel').pipe(filter(({\n      deltaX\n    }) => Math.abs(deltaX) > 20), throttleTime(500, tuiZonefreeScheduler()), map(({\n      deltaX\n    }) => Math.sign(deltaX)), tap(() => {\n      // So we always have space to scroll and overflow-behavior saves us from back nav\n      this.el.scrollLeft = 10;\n    }));\n  }\n  static {\n    this.ɵfac = function TuiCarouselScroll_Factory(t) {\n      return new (t || TuiCarouselScroll)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiCarouselScroll,\n      selectors: [[\"\", \"tuiCarouselScroll\", \"\"]],\n      outputs: {\n        tuiCarouselScroll: \"tuiCarouselScroll\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCarouselScroll, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiCarouselScroll]'\n    }]\n  }], null, {\n    tuiCarouselScroll: [{\n      type: Output\n    }]\n  });\n})();\nclass TuiCarouselComponent {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.cdr = inject(ChangeDetectorRef);\n    this.isMobile = inject(TUI_IS_MOBILE);\n    this.directive = inject(TuiCarouselDirective);\n    this.translate = 0;\n    this.items = EMPTY_QUERY;\n    this.transitioned = true;\n    this.index = 0;\n    this.draggable = false;\n    this.itemsCount = 1;\n    this.indexChange = new EventEmitter();\n    this.shift = new EventEmitter();\n  }\n  set indexSetter(index) {\n    this.index = index;\n    this.directive.duration = NaN;\n  }\n  next() {\n    if (this.items && this.index === this.items.length - this.itemsCount) {\n      return;\n    }\n    this.updateIndex(this.index + 1);\n  }\n  prev() {\n    this.updateIndex(this.index - 1);\n  }\n  get transform() {\n    return `translateX(${100 * this.x}%)`;\n  }\n  getStyle(itemsCount) {\n    const percent = `${100 / itemsCount}%`;\n    return {\n      flexBasis: percent,\n      minWidth: percent,\n      maxWidth: percent\n    };\n  }\n  onTransitioned(transitioned) {\n    this.transitioned = transitioned;\n    if (!transitioned) {\n      this.translate = this.computedTranslate;\n    }\n    this.onShift();\n  }\n  isDisabled(index) {\n    return index < this.index || index >= this.index + this.itemsCount;\n  }\n  onIntersection({\n    intersectionRatio\n  }, index) {\n    if (intersectionRatio && intersectionRatio >= 0.5 && !this.transitioned) {\n      this.updateIndex(this.index < index ? index - this.itemsCount + 1 : index);\n    }\n  }\n  onScroll(delta) {\n    if (!this.isMobile) {\n      if (delta > 0) {\n        this.next();\n      } else {\n        this.prev();\n      }\n    }\n  }\n  onPan(x) {\n    if (!this.computedDraggable) {\n      return;\n    }\n    const min = 1 - this.items.length / this.itemsCount;\n    this.translate = tuiClamp(x / this.el.clientWidth + this.translate, min, 0);\n    this.onShift();\n  }\n  onSwipe(direction) {\n    if (direction === 'left') {\n      this.next();\n    } else if (direction === 'right') {\n      this.prev();\n    }\n  }\n  onAutoscroll() {\n    this.updateIndex(this.index === this.items.length - 1 ? 0 : this.index + 1);\n  }\n  onShift() {\n    this.shift.emit(Math.abs(this.x % 1 + 0.5) * 2);\n  }\n  get x() {\n    return this.transitioned ? this.computedTranslate : this.translate;\n  }\n  get computedTranslate() {\n    return -this.index / this.itemsCount;\n  }\n  get computedDraggable() {\n    return this.isMobile || this.draggable;\n  }\n  updateIndex(index) {\n    this.index = tuiClamp(index, 0, this.items.length - 1);\n    this.indexChange.emit(this.index);\n    this.cdr.markForCheck();\n  }\n  static {\n    this.ɵfac = function TuiCarouselComponent_Factory(t) {\n      return new (t || TuiCarouselComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiCarouselComponent,\n      selectors: [[\"tui-carousel\"]],\n      contentQueries: function TuiCarouselComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiItem, 4, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      hostVars: 4,\n      hostBindings: function TuiCarouselComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"touchstart\", function TuiCarouselComponent_touchstart_HostBindingHandler() {\n            return ctx.onTransitioned(false);\n          })(\"touchend\", function TuiCarouselComponent_touchend_HostBindingHandler() {\n            return ctx.onTransitioned(true);\n          })(\"mousedown\", function TuiCarouselComponent_mousedown_HostBindingHandler() {\n            return ctx.onTransitioned(false);\n          })(\"mouseup.zoneless\", function TuiCarouselComponent_mouseup_zoneless_HostBindingHandler() {\n            return ctx.onTransitioned(true);\n          }, false, i0.ɵɵresolveDocument);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_transitioned\", ctx.transitioned)(\"_draggable\", ctx.draggable);\n        }\n      },\n      inputs: {\n        draggable: \"draggable\",\n        itemsCount: \"itemsCount\",\n        indexSetter: [i0.ɵɵInputFlags.None, \"index\", \"indexSetter\"]\n      },\n      outputs: {\n        indexChange: \"indexChange\",\n        shift: \"shift\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: TUI_SWIPE_OPTIONS,\n        useValue: {\n          timeout: 200,\n          threshold: 30\n        }\n      }]), i0.ɵɵHostDirectivesFeature([{\n        directive: TuiCarouselDirective,\n        inputs: [\"duration\", \"duration\"]\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 6,\n      consts: [[4, \"ngIf\"], [1, \"t-scroller\", 3, \"tuiCarouselScroll\"], [\"waIntersectionObserver\", \"\", \"waIntersectionThreshold\", \"0.5\", 1, \"t-wrapper\"], [1, \"t-items\", 3, \"tuiCarouselAutoscroll\", \"tuiPan\", \"tuiSwipe\"], [\"class\", \"t-item\", 3, \"disabled\", \"style\", \"waIntersectionObservee\", 4, \"ngFor\", \"ngForOf\"], [1, \"t-item\", 3, \"waIntersectionObservee\", \"disabled\"], [3, \"ngTemplateOutlet\"]],\n      template: function TuiCarouselComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiCarouselComponent_ng_container_0_Template, 1, 0, \"ng-container\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵlistener(\"tuiCarouselScroll\", function TuiCarouselComponent_Template_div_tuiCarouselScroll_2_listener($event) {\n            return ctx.onScroll($event);\n          });\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3);\n          i0.ɵɵlistener(\"tuiCarouselAutoscroll\", function TuiCarouselComponent_Template_div_tuiCarouselAutoscroll_4_listener() {\n            return ctx.onAutoscroll();\n          })(\"tuiPan\", function TuiCarouselComponent_Template_div_tuiPan_4_listener($event) {\n            return ctx.onPan($event[0]);\n          })(\"tuiSwipe\", function TuiCarouselComponent_Template_div_tuiSwipe_4_listener($event) {\n            return ctx.onSwipe($event.direction);\n          });\n          i0.ɵɵtemplate(5, TuiCarouselComponent_fieldset_5_Template, 2, 4, \"fieldset\", 4);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 4, ctx.items.changes));\n          i0.ɵɵadvance(4);\n          i0.ɵɵstyleProp(\"transform\", ctx.transform);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n        }\n      },\n      dependencies: [AsyncPipe, NgForOf, NgIf, NgTemplateOutlet, TuiCarouselAutoscroll, TuiCarouselScroll, TuiPan, TuiSwipe, i2.WaIntersectionObserverDirective, i2.WaIntersectionObservee],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:block;overflow:hidden}._draggable[_nghost-%COMP%]{-webkit-user-select:none;user-select:none}._draggable[_nghost-%COMP%]:hover{cursor:grab}._draggable[_nghost-%COMP%]:active{cursor:grabbing}.t-items[_ngcontent-%COMP%]{display:flex}._transitioned[_nghost-%COMP%]   .t-items[_ngcontent-%COMP%]{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out}  [tuiCarouselButtons] [tuiIconButton]{border-radius:100%}.t-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;padding:var(--tui-carousel-padding, 0 1.25rem);flex:1;min-inline-size:100%;max-inline-size:100%;box-sizing:border-box;border:none;margin:0}.t-wrapper[_ngcontent-%COMP%]{position:sticky;left:0;right:0;min-inline-size:100%;overflow:hidden}.t-scroller[_ngcontent-%COMP%]{scrollbar-width:none;-ms-overflow-style:none;display:flex;overflow:auto;overscroll-behavior-x:none;touch-action:pan-y}.t-scroller[_ngcontent-%COMP%]::-webkit-scrollbar, .t-scroller[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{display:none}.t-scroller[_ngcontent-%COMP%]:before, .t-scroller[_ngcontent-%COMP%]:after{content:\\\"\\\";display:block;min-inline-size:1rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n__decorate([tuiPure], TuiCarouselComponent.prototype, \"getStyle\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCarouselComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-carousel',\n      imports: [AsyncPipe, NgForOf, NgIf, NgTemplateOutlet, TuiCarouselAutoscroll, TuiCarouselScroll, TuiPan, TuiSwipe, WaIntersectionObserver],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: TUI_SWIPE_OPTIONS,\n        useValue: {\n          timeout: 200,\n          threshold: 30\n        }\n      }],\n      hostDirectives: [{\n        directive: TuiCarouselDirective,\n        inputs: ['duration']\n      }],\n      host: {\n        '[class._transitioned]': 'transitioned',\n        '[class._draggable]': 'draggable',\n        '(touchstart)': 'onTransitioned(false)',\n        '(touchend)': 'onTransitioned(true)',\n        '(mousedown)': 'onTransitioned(false)',\n        '(document:mouseup.zoneless)': 'onTransitioned(true)'\n      },\n      template: \"<ng-container *ngIf=\\\"items.changes | async\\\" />\\n<div\\n    class=\\\"t-scroller\\\"\\n    (tuiCarouselScroll)=\\\"onScroll($event)\\\"\\n>\\n    <div\\n        waIntersectionObserver\\n        waIntersectionThreshold=\\\"0.5\\\"\\n        class=\\\"t-wrapper\\\"\\n    >\\n        <div\\n            class=\\\"t-items\\\"\\n            [style.transform]=\\\"transform\\\"\\n            (tuiCarouselAutoscroll)=\\\"onAutoscroll()\\\"\\n            (tuiPan)=\\\"onPan($event[0])\\\"\\n            (tuiSwipe)=\\\"onSwipe($event.direction)\\\"\\n        >\\n            <fieldset\\n                *ngFor=\\\"let item of items; let i = index\\\"\\n                class=\\\"t-item\\\"\\n                [disabled]=\\\"isDisabled(i)\\\"\\n                [style]=\\\"getStyle(itemsCount)\\\"\\n                (waIntersectionObservee)=\\\"$event[0] && onIntersection($event[0], i)\\\"\\n            >\\n                <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n            </fieldset>\\n        </div>\\n    </div>\\n</div>\\n\",\n      styles: [\":host{position:relative;display:block;overflow:hidden}:host._draggable{-webkit-user-select:none;user-select:none}:host._draggable:hover{cursor:grab}:host._draggable:active{cursor:grabbing}.t-items{display:flex}:host._transitioned .t-items{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out}::ng-deep [tuiCarouselButtons] [tuiIconButton]{border-radius:100%}.t-item{display:flex;flex-direction:column;justify-content:center;padding:var(--tui-carousel-padding, 0 1.25rem);flex:1;min-inline-size:100%;max-inline-size:100%;box-sizing:border-box;border:none;margin:0}.t-wrapper{position:sticky;left:0;right:0;min-inline-size:100%;overflow:hidden}.t-scroller{scrollbar-width:none;-ms-overflow-style:none;display:flex;overflow:auto;overscroll-behavior-x:none;touch-action:pan-y}.t-scroller::-webkit-scrollbar,.t-scroller::-webkit-scrollbar-thumb{display:none}.t-scroller:before,.t-scroller:after{content:\\\"\\\";display:block;min-inline-size:1rem}\\n\"]\n    }]\n  }], null, {\n    items: [{\n      type: ContentChildren,\n      args: [TuiItem, {\n        read: TemplateRef\n      }]\n    }],\n    draggable: [{\n      type: Input\n    }],\n    itemsCount: [{\n      type: Input\n    }],\n    indexChange: [{\n      type: Output\n    }],\n    shift: [{\n      type: Output\n    }],\n    indexSetter: [{\n      type: Input,\n      args: ['index']\n    }],\n    getStyle: []\n  });\n})();\nclass TuiCarouselButtons {\n  static {\n    this.ɵfac = function TuiCarouselButtons_Factory(t) {\n      return new (t || TuiCarouselButtons)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiCarouselButtons,\n      selectors: [[\"\", \"tuiCarouselButtons\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiButtonOptionsProvider({\n        appearance: 'secondary',\n        size: 'm'\n      })])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCarouselButtons, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiCarouselButtons]',\n      providers: [tuiButtonOptionsProvider({\n        appearance: 'secondary',\n        size: 'm'\n      })]\n    }]\n  }], null, null);\n})();\nconst TuiCarousel = [TuiItem, TuiCarouselComponent, TuiCarouselDirective, TuiCarouselAutoscroll, TuiCarouselButtons, TuiCarouselScroll];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiCarousel, TuiCarouselAutoscroll, TuiCarouselButtons, TuiCarouselComponent, TuiCarouselDirective, TuiCarouselScroll };", "map": {"version": 3, "names": ["TuiItem", "__decorate", "isPlatformServer", "AsyncPipe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "i0", "inject", "PLATFORM_ID", "NgZone", "Directive", "Input", "Output", "ChangeDetectorRef", "EventEmitter", "TemplateRef", "Component", "ChangeDetectionStrategy", "ContentChildren", "i2", "WaIntersectionObserver", "TUI_FALSE_HANDLER", "TUI_TRUE_HANDLER", "EMPTY_QUERY", "<PERSON><PERSON><PERSON><PERSON>", "TUI_SWIPE_OPTIONS", "TuiSwipe", "TUI_IS_MOBILE", "tuiInjectElement", "tui<PERSON><PERSON>", "tuiPure", "WA_PAGE_VISIBILITY", "tuiTypedFromEvent", "tuiIfMap", "tuiZoneOptimized", "tuiZonefreeScheduler", "Observable", "BehaviorSubject", "merge", "map", "EMPTY", "combineLatest", "interval", "filter", "throttleTime", "tap", "tuiButtonOptionsProvider", "TuiCarouselComponent_ng_container_0_Template", "rf", "ctx", "ɵɵelementContainer", "TuiCarouselComponent_fieldset_5_Template", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiCarouselComponent_fieldset_5_Template_fieldset_waIntersectionObservee_0_listener", "$event", "i_r2", "ɵɵrestoreView", "index", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onIntersection", "ɵɵelementEnd", "item_r4", "$implicit", "ɵɵstyleMap", "getStyle", "itemsCount", "ɵɵproperty", "isDisabled", "ɵɵadvance", "TuiCarouselDirective", "constructor", "subscriber", "output$", "subscribe", "el", "platform", "visible$", "zone", "duration$", "running$", "pipe", "duration", "values", "every", "Boolean", "next", "Number", "isNaN", "value", "ɵfac", "TuiCarouselDirective_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "inputs", "standalone", "features", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "TuiCarouselAutoscroll", "tuiCarouselAutoscroll", "TuiCarouselAutoscroll_Factory", "selectors", "outputs", "selector", "TuiCarouselScroll", "tuiCarouselScroll", "deltaX", "Math", "abs", "sign", "scrollLeft", "TuiCarouselScroll_Factory", "TuiCarouselComponent", "cdr", "isMobile", "directive", "translate", "items", "transitioned", "draggable", "indexChange", "shift", "indexSetter", "NaN", "length", "updateIndex", "prev", "transform", "x", "percent", "flexBasis", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "onTransitioned", "computedTranslate", "onShift", "intersectionRatio", "onScroll", "delta", "onPan", "computedDraggable", "min", "clientWidth", "onSwipe", "direction", "onAutoscroll", "emit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TuiCarouselComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "TuiCarouselComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostVars", "hostBindings", "TuiCarouselComponent_HostBindings", "TuiCarouselComponent_touchstart_HostBindingHandler", "TuiCarouselComponent_touchend_HostBindingHandler", "TuiCarouselComponent_mousedown_HostBindingHandler", "TuiCarouselComponent_mouseup_zoneless_HostBindingHandler", "ɵɵresolveDocument", "ɵɵclassProp", "ɵɵInputFlags", "None", "ɵɵProvidersFeature", "provide", "useValue", "timeout", "threshold", "ɵɵHostDirectivesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiCarouselComponent_Template", "ɵɵtemplate", "ɵɵpipe", "TuiCarouselComponent_Template_div_tuiCarouselScroll_2_listener", "TuiCarouselComponent_Template_div_tuiCarouselAutoscroll_4_listener", "TuiCarouselComponent_Template_div_tuiPan_4_listener", "TuiCarouselComponent_Template_div_tuiSwipe_4_listener", "ɵɵpipeBind1", "changes", "ɵɵstyleProp", "dependencies", "WaIntersectionObserverDirective", "WaIntersectionObservee", "styles", "changeDetection", "prototype", "imports", "OnPush", "providers", "hostDirectives", "host", "read", "TuiCarouselButtons", "TuiCarouselButtons_Factory", "appearance", "size", "TuiCarousel"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-carousel.mjs"], "sourcesContent": ["import { TuiItem } from '@taiga-ui/cdk/directives/item';\nimport { __decorate } from 'tslib';\nimport { isPlatformServer, AsyncPipe, NgForOf, NgIf, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, NgZone, Directive, Input, Output, ChangeDetectorRef, EventEmitter, TemplateRef, Component, ChangeDetectionStrategy, ContentChildren } from '@angular/core';\nimport * as i2 from '@ng-web-apis/intersection-observer';\nimport { WaIntersectionObserver } from '@ng-web-apis/intersection-observer';\nimport { TUI_FALSE_HANDLER, TUI_TRUE_HANDLER, EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { TuiPan } from '@taiga-ui/cdk/directives/pan';\nimport { TUI_SWIPE_OPTIONS, TuiSwipe } from '@taiga-ui/cdk/directives/swipe';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { WA_PAGE_VISIBILITY } from '@ng-web-apis/common';\nimport { tuiTypedFromEvent, tuiIfMap, tuiZoneOptimized, tuiZonefreeScheduler } from '@taiga-ui/cdk/observables';\nimport { Observable, BehaviorSubject, merge, map, EMPTY, combineLatest, interval, filter, throttleTime, tap } from 'rxjs';\nimport { tuiButtonOptionsProvider } from '@taiga-ui/core/components/button';\n\nclass TuiCarouselDirective extends Observable {\n    constructor() {\n        super((subscriber) => this.output$.subscribe(subscriber));\n        this.el = tuiInjectElement();\n        this.platform = inject(PLATFORM_ID);\n        this.visible$ = inject(WA_PAGE_VISIBILITY);\n        this.zone = inject(NgZone);\n        this.duration$ = new BehaviorSubject(0);\n        this.running$ = merge(tuiTypedFromEvent(this.el, 'mouseenter').pipe(map(TUI_FALSE_HANDLER)), tuiTypedFromEvent(this.el, 'touchstart').pipe(map(TUI_FALSE_HANDLER)), tuiTypedFromEvent(this.el, 'touchend').pipe(map(TUI_TRUE_HANDLER)), tuiTypedFromEvent(this.el, 'mouseleave').pipe(map(TUI_TRUE_HANDLER)), this.visible$);\n        this.output$ = isPlatformServer(this.platform)\n            ? EMPTY\n            : combineLatest([this.duration$, this.running$]).pipe(tuiIfMap(([duration]) => interval(duration).pipe(tuiZoneOptimized(this.zone)), (values) => values.every(Boolean)));\n    }\n    set duration(duration) {\n        this.duration$.next(Number.isNaN(duration) ? this.duration$.value : duration);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCarouselDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCarouselDirective, isStandalone: true, inputs: { duration: \"duration\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCarouselDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { duration: [{\n                type: Input\n            }] } });\n\nclass TuiCarouselAutoscroll {\n    constructor() {\n        this.tuiCarouselAutoscroll = inject(TuiCarouselDirective);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCarouselAutoscroll, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCarouselAutoscroll, isStandalone: true, selector: \"[tuiCarouselAutoscroll]\", outputs: { tuiCarouselAutoscroll: \"tuiCarouselAutoscroll\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCarouselAutoscroll, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiCarouselAutoscroll]',\n                }]\n        }], propDecorators: { tuiCarouselAutoscroll: [{\n                type: Output\n            }] } });\n\nclass TuiCarouselScroll {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.tuiCarouselScroll = tuiTypedFromEvent(this.el, 'wheel').pipe(filter(({ deltaX }) => Math.abs(deltaX) > 20), throttleTime(500, tuiZonefreeScheduler()), map(({ deltaX }) => Math.sign(deltaX)), tap(() => {\n            // So we always have space to scroll and overflow-behavior saves us from back nav\n            this.el.scrollLeft = 10;\n        }));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCarouselScroll, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCarouselScroll, isStandalone: true, selector: \"[tuiCarouselScroll]\", outputs: { tuiCarouselScroll: \"tuiCarouselScroll\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCarouselScroll, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiCarouselScroll]',\n                }]\n        }], propDecorators: { tuiCarouselScroll: [{\n                type: Output\n            }] } });\n\nclass TuiCarouselComponent {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.cdr = inject(ChangeDetectorRef);\n        this.isMobile = inject(TUI_IS_MOBILE);\n        this.directive = inject(TuiCarouselDirective);\n        this.translate = 0;\n        this.items = EMPTY_QUERY;\n        this.transitioned = true;\n        this.index = 0;\n        this.draggable = false;\n        this.itemsCount = 1;\n        this.indexChange = new EventEmitter();\n        this.shift = new EventEmitter();\n    }\n    set indexSetter(index) {\n        this.index = index;\n        this.directive.duration = NaN;\n    }\n    next() {\n        if (this.items && this.index === this.items.length - this.itemsCount) {\n            return;\n        }\n        this.updateIndex(this.index + 1);\n    }\n    prev() {\n        this.updateIndex(this.index - 1);\n    }\n    get transform() {\n        return `translateX(${100 * this.x}%)`;\n    }\n    getStyle(itemsCount) {\n        const percent = `${100 / itemsCount}%`;\n        return {\n            flexBasis: percent,\n            minWidth: percent,\n            maxWidth: percent,\n        };\n    }\n    onTransitioned(transitioned) {\n        this.transitioned = transitioned;\n        if (!transitioned) {\n            this.translate = this.computedTranslate;\n        }\n        this.onShift();\n    }\n    isDisabled(index) {\n        return index < this.index || index >= this.index + this.itemsCount;\n    }\n    onIntersection({ intersectionRatio }, index) {\n        if (intersectionRatio && intersectionRatio >= 0.5 && !this.transitioned) {\n            this.updateIndex(this.index < index ? index - this.itemsCount + 1 : index);\n        }\n    }\n    onScroll(delta) {\n        if (!this.isMobile) {\n            if (delta > 0) {\n                this.next();\n            }\n            else {\n                this.prev();\n            }\n        }\n    }\n    onPan(x) {\n        if (!this.computedDraggable) {\n            return;\n        }\n        const min = 1 - this.items.length / this.itemsCount;\n        this.translate = tuiClamp(x / this.el.clientWidth + this.translate, min, 0);\n        this.onShift();\n    }\n    onSwipe(direction) {\n        if (direction === 'left') {\n            this.next();\n        }\n        else if (direction === 'right') {\n            this.prev();\n        }\n    }\n    onAutoscroll() {\n        this.updateIndex(this.index === this.items.length - 1 ? 0 : this.index + 1);\n    }\n    onShift() {\n        this.shift.emit(Math.abs((this.x % 1) + 0.5) * 2);\n    }\n    get x() {\n        return this.transitioned ? this.computedTranslate : this.translate;\n    }\n    get computedTranslate() {\n        return -this.index / this.itemsCount;\n    }\n    get computedDraggable() {\n        return this.isMobile || this.draggable;\n    }\n    updateIndex(index) {\n        this.index = tuiClamp(index, 0, this.items.length - 1);\n        this.indexChange.emit(this.index);\n        this.cdr.markForCheck();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCarouselComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCarouselComponent, isStandalone: true, selector: \"tui-carousel\", inputs: { draggable: \"draggable\", itemsCount: \"itemsCount\", indexSetter: [\"index\", \"indexSetter\"] }, outputs: { indexChange: \"indexChange\", shift: \"shift\" }, host: { listeners: { \"touchstart\": \"onTransitioned(false)\", \"touchend\": \"onTransitioned(true)\", \"mousedown\": \"onTransitioned(false)\", \"document:mouseup.zoneless\": \"onTransitioned(true)\" }, properties: { \"class._transitioned\": \"transitioned\", \"class._draggable\": \"draggable\" } }, providers: [{ provide: TUI_SWIPE_OPTIONS, useValue: { timeout: 200, threshold: 30 } }], queries: [{ propertyName: \"items\", predicate: TuiItem, read: TemplateRef }], hostDirectives: [{ directive: TuiCarouselDirective, inputs: [\"duration\", \"duration\"] }], ngImport: i0, template: \"<ng-container *ngIf=\\\"items.changes | async\\\" />\\n<div\\n    class=\\\"t-scroller\\\"\\n    (tuiCarouselScroll)=\\\"onScroll($event)\\\"\\n>\\n    <div\\n        waIntersectionObserver\\n        waIntersectionThreshold=\\\"0.5\\\"\\n        class=\\\"t-wrapper\\\"\\n    >\\n        <div\\n            class=\\\"t-items\\\"\\n            [style.transform]=\\\"transform\\\"\\n            (tuiCarouselAutoscroll)=\\\"onAutoscroll()\\\"\\n            (tuiPan)=\\\"onPan($event[0])\\\"\\n            (tuiSwipe)=\\\"onSwipe($event.direction)\\\"\\n        >\\n            <fieldset\\n                *ngFor=\\\"let item of items; let i = index\\\"\\n                class=\\\"t-item\\\"\\n                [disabled]=\\\"isDisabled(i)\\\"\\n                [style]=\\\"getStyle(itemsCount)\\\"\\n                (waIntersectionObservee)=\\\"$event[0] && onIntersection($event[0], i)\\\"\\n            >\\n                <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n            </fieldset>\\n        </div>\\n    </div>\\n</div>\\n\", styles: [\":host{position:relative;display:block;overflow:hidden}:host._draggable{-webkit-user-select:none;user-select:none}:host._draggable:hover{cursor:grab}:host._draggable:active{cursor:grabbing}.t-items{display:flex}:host._transitioned .t-items{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out}::ng-deep [tuiCarouselButtons] [tuiIconButton]{border-radius:100%}.t-item{display:flex;flex-direction:column;justify-content:center;padding:var(--tui-carousel-padding, 0 1.25rem);flex:1;min-inline-size:100%;max-inline-size:100%;box-sizing:border-box;border:none;margin:0}.t-wrapper{position:sticky;left:0;right:0;min-inline-size:100%;overflow:hidden}.t-scroller{scrollbar-width:none;-ms-overflow-style:none;display:flex;overflow:auto;overscroll-behavior-x:none;touch-action:pan-y}.t-scroller::-webkit-scrollbar,.t-scroller::-webkit-scrollbar-thumb{display:none}.t-scroller:before,.t-scroller:after{content:\\\"\\\";display:block;min-inline-size:1rem}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: TuiCarouselAutoscroll, selector: \"[tuiCarouselAutoscroll]\", outputs: [\"tuiCarouselAutoscroll\"] }, { kind: \"directive\", type: TuiCarouselScroll, selector: \"[tuiCarouselScroll]\", outputs: [\"tuiCarouselScroll\"] }, { kind: \"directive\", type: TuiPan, selector: \"[tuiPan]\", outputs: [\"tuiPan\"] }, { kind: \"directive\", type: TuiSwipe, selector: \"[tuiSwipe]\", outputs: [\"tuiSwipe\"] }, { kind: \"directive\", type: i2.WaIntersectionObserverDirective, selector: \"[waIntersectionObserver]\", inputs: [\"waIntersectionRootMargin\", \"waIntersectionThreshold\"], exportAs: [\"IntersectionObserver\"] }, { kind: \"directive\", type: i2.WaIntersectionObservee, selector: \"[waIntersectionObservee]\", outputs: [\"waIntersectionObservee\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\n__decorate([\n    tuiPure\n], TuiCarouselComponent.prototype, \"getStyle\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCarouselComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-carousel', imports: [\n                        AsyncPipe,\n                        NgForOf,\n                        NgIf,\n                        NgTemplateOutlet,\n                        TuiCarouselAutoscroll,\n                        TuiCarouselScroll,\n                        TuiPan,\n                        TuiSwipe,\n                        WaIntersectionObserver,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, providers: [{ provide: TUI_SWIPE_OPTIONS, useValue: { timeout: 200, threshold: 30 } }], hostDirectives: [\n                        {\n                            directive: TuiCarouselDirective,\n                            inputs: ['duration'],\n                        },\n                    ], host: {\n                        '[class._transitioned]': 'transitioned',\n                        '[class._draggable]': 'draggable',\n                        '(touchstart)': 'onTransitioned(false)',\n                        '(touchend)': 'onTransitioned(true)',\n                        '(mousedown)': 'onTransitioned(false)',\n                        '(document:mouseup.zoneless)': 'onTransitioned(true)',\n                    }, template: \"<ng-container *ngIf=\\\"items.changes | async\\\" />\\n<div\\n    class=\\\"t-scroller\\\"\\n    (tuiCarouselScroll)=\\\"onScroll($event)\\\"\\n>\\n    <div\\n        waIntersectionObserver\\n        waIntersectionThreshold=\\\"0.5\\\"\\n        class=\\\"t-wrapper\\\"\\n    >\\n        <div\\n            class=\\\"t-items\\\"\\n            [style.transform]=\\\"transform\\\"\\n            (tuiCarouselAutoscroll)=\\\"onAutoscroll()\\\"\\n            (tuiPan)=\\\"onPan($event[0])\\\"\\n            (tuiSwipe)=\\\"onSwipe($event.direction)\\\"\\n        >\\n            <fieldset\\n                *ngFor=\\\"let item of items; let i = index\\\"\\n                class=\\\"t-item\\\"\\n                [disabled]=\\\"isDisabled(i)\\\"\\n                [style]=\\\"getStyle(itemsCount)\\\"\\n                (waIntersectionObservee)=\\\"$event[0] && onIntersection($event[0], i)\\\"\\n            >\\n                <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n            </fieldset>\\n        </div>\\n    </div>\\n</div>\\n\", styles: [\":host{position:relative;display:block;overflow:hidden}:host._draggable{-webkit-user-select:none;user-select:none}:host._draggable:hover{cursor:grab}:host._draggable:active{cursor:grabbing}.t-items{display:flex}:host._transitioned .t-items{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out}::ng-deep [tuiCarouselButtons] [tuiIconButton]{border-radius:100%}.t-item{display:flex;flex-direction:column;justify-content:center;padding:var(--tui-carousel-padding, 0 1.25rem);flex:1;min-inline-size:100%;max-inline-size:100%;box-sizing:border-box;border:none;margin:0}.t-wrapper{position:sticky;left:0;right:0;min-inline-size:100%;overflow:hidden}.t-scroller{scrollbar-width:none;-ms-overflow-style:none;display:flex;overflow:auto;overscroll-behavior-x:none;touch-action:pan-y}.t-scroller::-webkit-scrollbar,.t-scroller::-webkit-scrollbar-thumb{display:none}.t-scroller:before,.t-scroller:after{content:\\\"\\\";display:block;min-inline-size:1rem}\\n\"] }]\n        }], propDecorators: { items: [{\n                type: ContentChildren,\n                args: [TuiItem, { read: TemplateRef }]\n            }], draggable: [{\n                type: Input\n            }], itemsCount: [{\n                type: Input\n            }], indexChange: [{\n                type: Output\n            }], shift: [{\n                type: Output\n            }], indexSetter: [{\n                type: Input,\n                args: ['index']\n            }], getStyle: [] } });\n\nclass TuiCarouselButtons {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCarouselButtons, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCarouselButtons, isStandalone: true, selector: \"[tuiCarouselButtons]\", providers: [\n            tuiButtonOptionsProvider({\n                appearance: 'secondary',\n                size: 'm',\n            }),\n        ], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCarouselButtons, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiCarouselButtons]',\n                    providers: [\n                        tuiButtonOptionsProvider({\n                            appearance: 'secondary',\n                            size: 'm',\n                        }),\n                    ],\n                }]\n        }] });\n\nconst TuiCarousel = [\n    TuiItem,\n    TuiCarouselComponent,\n    TuiCarouselDirective,\n    TuiCarouselAutoscroll,\n    TuiCarouselButtons,\n    TuiCarouselScroll,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiCarousel, TuiCarouselAutoscroll, TuiCarouselButtons, TuiCarouselComponent, TuiCarouselDirective, TuiCarouselScroll };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,+BAA+B;AACvD,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,gBAAgB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,IAAI,EAAEC,gBAAgB,QAAQ,iBAAiB;AAC9F,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,eAAe,QAAQ,eAAe;AACxL,OAAO,KAAKC,EAAE,MAAM,oCAAoC;AACxD,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,iBAAiB,EAAEC,gBAAgB,EAAEC,WAAW,QAAQ,yBAAyB;AAC1F,SAASC,MAAM,QAAQ,8BAA8B;AACrD,SAASC,iBAAiB,EAAEC,QAAQ,QAAQ,gCAAgC;AAC5E,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,kBAAkB,QAAQ,qBAAqB;AACxD,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/G,SAASC,UAAU,EAAEC,eAAe,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,YAAY,EAAEC,GAAG,QAAQ,MAAM;AACzH,SAASC,wBAAwB,QAAQ,kCAAkC;AAAC,SAAAC,6CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAkByB1C,EAAE,CAAA4C,kBAAA,EAuJ2zB,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAI,GAAA,GAvJ9zB9C,EAAE,CAAA+C,gBAAA;IAAF/C,EAAE,CAAAgD,cAAA,iBAuJ8jD,CAAC;IAvJjkDhD,EAAE,CAAAiD,UAAA,oCAAAC,oFAAAC,MAAA;MAAA,MAAAC,IAAA,GAAFpD,EAAE,CAAAqD,aAAA,CAAAP,GAAA,EAAAQ,KAAA;MAAA,MAAAC,MAAA,GAAFvD,EAAE,CAAAwD,aAAA;MAAA,OAAFxD,EAAE,CAAAyD,WAAA,CAAAN,MAAA,CAuJ4gD,CAAC,KAAKI,MAAA,CAAAG,cAAA,CAAAP,MAAA,CAAsB,CAAC,GAAAC,IAAI,CAAC;IAAA,CAAC,CAAC;IAvJljDpD,EAAE,CAAA4C,kBAAA,KAuJ4nD,CAAC;IAvJ/nD5C,EAAE,CAAA2D,YAAA,CAuJqpD,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAkB,OAAA,GAAAjB,GAAA,CAAAkB,SAAA;IAAA,MAAAT,IAAA,GAAAT,GAAA,CAAAW,KAAA;IAAA,MAAAC,MAAA,GAvJxpDvD,EAAE,CAAAwD,aAAA;IAAFxD,EAAE,CAAA8D,UAAA,CAAAP,MAAA,CAAAQ,QAAA,CAAAR,MAAA,CAAAS,UAAA,CAuJu9C,CAAC;IAvJ19ChE,EAAE,CAAAiE,UAAA,aAAAV,MAAA,CAAAW,UAAA,CAAAd,IAAA,CAuJq6C,CAAC;IAvJx6CpD,EAAE,CAAAmE,SAAA,CAuJynD,CAAC;IAvJ5nDnE,EAAE,CAAAiE,UAAA,qBAAAL,OAuJynD,CAAC;EAAA;AAAA;AAvKjuD,MAAMQ,oBAAoB,SAAStC,UAAU,CAAC;EAC1CuC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAEC,UAAU,IAAK,IAAI,CAACC,OAAO,CAACC,SAAS,CAACF,UAAU,CAAC,CAAC;IACzD,IAAI,CAACG,EAAE,GAAGnD,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACoD,QAAQ,GAAGzE,MAAM,CAACC,WAAW,CAAC;IACnC,IAAI,CAACyE,QAAQ,GAAG1E,MAAM,CAACwB,kBAAkB,CAAC;IAC1C,IAAI,CAACmD,IAAI,GAAG3E,MAAM,CAACE,MAAM,CAAC;IAC1B,IAAI,CAAC0E,SAAS,GAAG,IAAI9C,eAAe,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC+C,QAAQ,GAAG9C,KAAK,CAACN,iBAAiB,CAAC,IAAI,CAAC+C,EAAE,EAAE,YAAY,CAAC,CAACM,IAAI,CAAC9C,GAAG,CAAClB,iBAAiB,CAAC,CAAC,EAAEW,iBAAiB,CAAC,IAAI,CAAC+C,EAAE,EAAE,YAAY,CAAC,CAACM,IAAI,CAAC9C,GAAG,CAAClB,iBAAiB,CAAC,CAAC,EAAEW,iBAAiB,CAAC,IAAI,CAAC+C,EAAE,EAAE,UAAU,CAAC,CAACM,IAAI,CAAC9C,GAAG,CAACjB,gBAAgB,CAAC,CAAC,EAAEU,iBAAiB,CAAC,IAAI,CAAC+C,EAAE,EAAE,YAAY,CAAC,CAACM,IAAI,CAAC9C,GAAG,CAACjB,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC2D,QAAQ,CAAC;IAC5T,IAAI,CAACJ,OAAO,GAAG5E,gBAAgB,CAAC,IAAI,CAAC+E,QAAQ,CAAC,GACxCxC,KAAK,GACLC,aAAa,CAAC,CAAC,IAAI,CAAC0C,SAAS,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAACpD,QAAQ,CAAC,CAAC,CAACqD,QAAQ,CAAC,KAAK5C,QAAQ,CAAC4C,QAAQ,CAAC,CAACD,IAAI,CAACnD,gBAAgB,CAAC,IAAI,CAACgD,IAAI,CAAC,CAAC,EAAGK,MAAM,IAAKA,MAAM,CAACC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;EAChL;EACA,IAAIH,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACH,SAAS,CAACO,IAAI,CAACC,MAAM,CAACC,KAAK,CAACN,QAAQ,CAAC,GAAG,IAAI,CAACH,SAAS,CAACU,KAAK,GAAGP,QAAQ,CAAC;EACjF;EACA;IAAS,IAAI,CAACQ,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFtB,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACuB,IAAI,kBAD+E3F,EAAE,CAAA4F,iBAAA;MAAAC,IAAA,EACJzB,oBAAoB;MAAA0B,MAAA;QAAAd,QAAA;MAAA;MAAAe,UAAA;MAAAC,QAAA,GADlBhG,EAAE,CAAAiG,0BAAA;IAAA,EAC8G;EAAE;AACvN;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGlG,EAAE,CAAAmG,iBAAA,CAGX/B,oBAAoB,EAAc,CAAC;IACnHyB,IAAI,EAAEzF,SAAS;IACfgG,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEf,QAAQ,EAAE,CAAC;MACrEa,IAAI,EAAExF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgG,qBAAqB,CAAC;EACxBhC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiC,qBAAqB,GAAGrG,MAAM,CAACmE,oBAAoB,CAAC;EAC7D;EACA;IAAS,IAAI,CAACoB,IAAI,YAAAe,8BAAAb,CAAA;MAAA,YAAAA,CAAA,IAAyFW,qBAAqB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAACV,IAAI,kBAjB+E3F,EAAE,CAAA4F,iBAAA;MAAAC,IAAA,EAiBJQ,qBAAqB;MAAAG,SAAA;MAAAC,OAAA;QAAAH,qBAAA;MAAA;MAAAP,UAAA;IAAA,EAAuI;EAAE;AACjQ;AACA;EAAA,QAAAG,SAAA,oBAAAA,SAAA,KAnBqGlG,EAAE,CAAAmG,iBAAA,CAmBXE,qBAAqB,EAAc,CAAC;IACpHR,IAAI,EAAEzF,SAAS;IACfgG,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBW,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEJ,qBAAqB,EAAE,CAAC;MACtCT,IAAI,EAAEvF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMqG,iBAAiB,CAAC;EACpBtC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACI,EAAE,GAAGnD,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACsF,iBAAiB,GAAGlF,iBAAiB,CAAC,IAAI,CAAC+C,EAAE,EAAE,OAAO,CAAC,CAACM,IAAI,CAAC1C,MAAM,CAAC,CAAC;MAAEwE;IAAO,CAAC,KAAKC,IAAI,CAACC,GAAG,CAACF,MAAM,CAAC,GAAG,EAAE,CAAC,EAAEvE,YAAY,CAAC,GAAG,EAAET,oBAAoB,CAAC,CAAC,CAAC,EAAEI,GAAG,CAAC,CAAC;MAAE4E;IAAO,CAAC,KAAKC,IAAI,CAACE,IAAI,CAACH,MAAM,CAAC,CAAC,EAAEtE,GAAG,CAAC,MAAM;MAC1M;MACA,IAAI,CAACkC,EAAE,CAACwC,UAAU,GAAG,EAAE;IAC3B,CAAC,CAAC,CAAC;EACP;EACA;IAAS,IAAI,CAACzB,IAAI,YAAA0B,0BAAAxB,CAAA;MAAA,YAAAA,CAAA,IAAyFiB,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAAChB,IAAI,kBAtC+E3F,EAAE,CAAA4F,iBAAA;MAAAC,IAAA,EAsCJc,iBAAiB;MAAAH,SAAA;MAAAC,OAAA;QAAAG,iBAAA;MAAA;MAAAb,UAAA;IAAA,EAA2H;EAAE;AACjP;AACA;EAAA,QAAAG,SAAA,oBAAAA,SAAA,KAxCqGlG,EAAE,CAAAmG,iBAAA,CAwCXQ,iBAAiB,EAAc,CAAC;IAChHd,IAAI,EAAEzF,SAAS;IACfgG,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBW,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEE,iBAAiB,EAAE,CAAC;MAClCf,IAAI,EAAEvF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6G,oBAAoB,CAAC;EACvB9C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACI,EAAE,GAAGnD,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAAC8F,GAAG,GAAGnH,MAAM,CAACM,iBAAiB,CAAC;IACpC,IAAI,CAAC8G,QAAQ,GAAGpH,MAAM,CAACoB,aAAa,CAAC;IACrC,IAAI,CAACiG,SAAS,GAAGrH,MAAM,CAACmE,oBAAoB,CAAC;IAC7C,IAAI,CAACmD,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,KAAK,GAAGvG,WAAW;IACxB,IAAI,CAACwG,YAAY,GAAG,IAAI;IACxB,IAAI,CAACnE,KAAK,GAAG,CAAC;IACd,IAAI,CAACoE,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC1D,UAAU,GAAG,CAAC;IACnB,IAAI,CAAC2D,WAAW,GAAG,IAAInH,YAAY,CAAC,CAAC;IACrC,IAAI,CAACoH,KAAK,GAAG,IAAIpH,YAAY,CAAC,CAAC;EACnC;EACA,IAAIqH,WAAWA,CAACvE,KAAK,EAAE;IACnB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACgE,SAAS,CAACtC,QAAQ,GAAG8C,GAAG;EACjC;EACA1C,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACoC,KAAK,IAAI,IAAI,CAAClE,KAAK,KAAK,IAAI,CAACkE,KAAK,CAACO,MAAM,GAAG,IAAI,CAAC/D,UAAU,EAAE;MAClE;IACJ;IACA,IAAI,CAACgE,WAAW,CAAC,IAAI,CAAC1E,KAAK,GAAG,CAAC,CAAC;EACpC;EACA2E,IAAIA,CAAA,EAAG;IACH,IAAI,CAACD,WAAW,CAAC,IAAI,CAAC1E,KAAK,GAAG,CAAC,CAAC;EACpC;EACA,IAAI4E,SAASA,CAAA,EAAG;IACZ,OAAO,cAAc,GAAG,GAAG,IAAI,CAACC,CAAC,IAAI;EACzC;EACApE,QAAQA,CAACC,UAAU,EAAE;IACjB,MAAMoE,OAAO,GAAG,GAAG,GAAG,GAAGpE,UAAU,GAAG;IACtC,OAAO;MACHqE,SAAS,EAAED,OAAO;MAClBE,QAAQ,EAAEF,OAAO;MACjBG,QAAQ,EAAEH;IACd,CAAC;EACL;EACAI,cAAcA,CAACf,YAAY,EAAE;IACzB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACA,YAAY,EAAE;MACf,IAAI,CAACF,SAAS,GAAG,IAAI,CAACkB,iBAAiB;IAC3C;IACA,IAAI,CAACC,OAAO,CAAC,CAAC;EAClB;EACAxE,UAAUA,CAACZ,KAAK,EAAE;IACd,OAAOA,KAAK,GAAG,IAAI,CAACA,KAAK,IAAIA,KAAK,IAAI,IAAI,CAACA,KAAK,GAAG,IAAI,CAACU,UAAU;EACtE;EACAN,cAAcA,CAAC;IAAEiF;EAAkB,CAAC,EAAErF,KAAK,EAAE;IACzC,IAAIqF,iBAAiB,IAAIA,iBAAiB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAClB,YAAY,EAAE;MACrE,IAAI,CAACO,WAAW,CAAC,IAAI,CAAC1E,KAAK,GAAGA,KAAK,GAAGA,KAAK,GAAG,IAAI,CAACU,UAAU,GAAG,CAAC,GAAGV,KAAK,CAAC;IAC9E;EACJ;EACAsF,QAAQA,CAACC,KAAK,EAAE;IACZ,IAAI,CAAC,IAAI,CAACxB,QAAQ,EAAE;MAChB,IAAIwB,KAAK,GAAG,CAAC,EAAE;QACX,IAAI,CAACzD,IAAI,CAAC,CAAC;MACf,CAAC,MACI;QACD,IAAI,CAAC6C,IAAI,CAAC,CAAC;MACf;IACJ;EACJ;EACAa,KAAKA,CAACX,CAAC,EAAE;IACL,IAAI,CAAC,IAAI,CAACY,iBAAiB,EAAE;MACzB;IACJ;IACA,MAAMC,GAAG,GAAG,CAAC,GAAG,IAAI,CAACxB,KAAK,CAACO,MAAM,GAAG,IAAI,CAAC/D,UAAU;IACnD,IAAI,CAACuD,SAAS,GAAGhG,QAAQ,CAAC4G,CAAC,GAAG,IAAI,CAAC1D,EAAE,CAACwE,WAAW,GAAG,IAAI,CAAC1B,SAAS,EAAEyB,GAAG,EAAE,CAAC,CAAC;IAC3E,IAAI,CAACN,OAAO,CAAC,CAAC;EAClB;EACAQ,OAAOA,CAACC,SAAS,EAAE;IACf,IAAIA,SAAS,KAAK,MAAM,EAAE;MACtB,IAAI,CAAC/D,IAAI,CAAC,CAAC;IACf,CAAC,MACI,IAAI+D,SAAS,KAAK,OAAO,EAAE;MAC5B,IAAI,CAAClB,IAAI,CAAC,CAAC;IACf;EACJ;EACAmB,YAAYA,CAAA,EAAG;IACX,IAAI,CAACpB,WAAW,CAAC,IAAI,CAAC1E,KAAK,KAAK,IAAI,CAACkE,KAAK,CAACO,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAACzE,KAAK,GAAG,CAAC,CAAC;EAC/E;EACAoF,OAAOA,CAAA,EAAG;IACN,IAAI,CAACd,KAAK,CAACyB,IAAI,CAACvC,IAAI,CAACC,GAAG,CAAE,IAAI,CAACoB,CAAC,GAAG,CAAC,GAAI,GAAG,CAAC,GAAG,CAAC,CAAC;EACrD;EACA,IAAIA,CAACA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACV,YAAY,GAAG,IAAI,CAACgB,iBAAiB,GAAG,IAAI,CAAClB,SAAS;EACtE;EACA,IAAIkB,iBAAiBA,CAAA,EAAG;IACpB,OAAO,CAAC,IAAI,CAACnF,KAAK,GAAG,IAAI,CAACU,UAAU;EACxC;EACA,IAAI+E,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC1B,QAAQ,IAAI,IAAI,CAACK,SAAS;EAC1C;EACAM,WAAWA,CAAC1E,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAG/B,QAAQ,CAAC+B,KAAK,EAAE,CAAC,EAAE,IAAI,CAACkE,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;IACtD,IAAI,CAACJ,WAAW,CAAC0B,IAAI,CAAC,IAAI,CAAC/F,KAAK,CAAC;IACjC,IAAI,CAAC8D,GAAG,CAACkC,YAAY,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAAC9D,IAAI,YAAA+D,6BAAA7D,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACqC,IAAI,kBAvJ+ExJ,EAAE,CAAAyJ,iBAAA;MAAA5D,IAAA,EAuJJsB,oBAAoB;MAAAX,SAAA;MAAAkD,cAAA,WAAAC,oCAAAjH,EAAA,EAAAC,GAAA,EAAAiH,QAAA;QAAA,IAAAlH,EAAA;UAvJlB1C,EAAE,CAAA6J,cAAA,CAAAD,QAAA,EAuJ2nBnK,OAAO,KAAQgB,WAAW;QAAA;QAAA,IAAAiC,EAAA;UAAA,IAAAoH,EAAA;UAvJvpB9J,EAAE,CAAA+J,cAAA,CAAAD,EAAA,GAAF9J,EAAE,CAAAgK,WAAA,QAAArH,GAAA,CAAA6E,KAAA,GAAAsC,EAAA;QAAA;MAAA;MAAAG,QAAA;MAAAC,YAAA,WAAAC,kCAAAzH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1C,EAAE,CAAAiD,UAAA,wBAAAmH,mDAAA;YAAA,OAuJJzH,GAAA,CAAA6F,cAAA,CAAe,KAAK,CAAC;UAAA,CAAF,CAAC,sBAAA6B,iDAAA;YAAA,OAApB1H,GAAA,CAAA6F,cAAA,CAAe,IAAI,CAAC;UAAA,CAAD,CAAC,uBAAA8B,kDAAA;YAAA,OAApB3H,GAAA,CAAA6F,cAAA,CAAe,KAAK,CAAC;UAAA,CAAF,CAAC,8BAAA+B,yDAAA;YAAA,OAApB5H,GAAA,CAAA6F,cAAA,CAAe,IAAI,CAAC;UAAA,UAvJlBxI,EAAE,CAAAwK,iBAuJe,CAAC;QAAA;QAAA,IAAA9H,EAAA;UAvJlB1C,EAAE,CAAAyK,WAAA,kBAAA9H,GAAA,CAAA8E,YAuJe,CAAC,eAAA9E,GAAA,CAAA+E,SAAD,CAAC;QAAA;MAAA;MAAA5B,MAAA;QAAA4B,SAAA;QAAA1D,UAAA;QAAA6D,WAAA,GAvJlB7H,EAAE,CAAA0K,YAAA,CAAAC,IAAA;MAAA;MAAAlE,OAAA;QAAAkB,WAAA;QAAAC,KAAA;MAAA;MAAA7B,UAAA;MAAAC,QAAA,GAAFhG,EAAE,CAAA4K,kBAAA,CAuJggB,CAAC;QAAEC,OAAO,EAAE1J,iBAAiB;QAAE2J,QAAQ,EAAE;UAAEC,OAAO,EAAE,GAAG;UAAEC,SAAS,EAAE;QAAG;MAAE,CAAC,CAAC,GAvJ7kBhL,EAAE,CAAAiL,uBAAA;QAAA3D,SAAA,EAuJwrBlD,oBAAoB;QAAA0B,MAAA;MAAA,KAvJ9sB9F,EAAE,CAAAkL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAA7I,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1C,EAAE,CAAAwL,UAAA,IAAA/I,4CAAA,yBAuJ2zB,CAAC;UAvJ9zBzC,EAAE,CAAAyL,MAAA;UAAFzL,EAAE,CAAAgD,cAAA,YAuJ44B,CAAC;UAvJ/4BhD,EAAE,CAAAiD,UAAA,+BAAAyI,+DAAAvI,MAAA;YAAA,OAuJw3BR,GAAA,CAAAiG,QAAA,CAAAzF,MAAe,CAAC;UAAA,CAAC,CAAC;UAvJ54BnD,EAAE,CAAAgD,cAAA,YAuJmgC,CAAC,YAA6P,CAAC;UAvJpwChD,EAAE,CAAAiD,UAAA,mCAAA0I,mEAAA;YAAA,OAuJsoChJ,GAAA,CAAAyG,YAAA,CAAa,CAAC;UAAA,CAAC,CAAC,oBAAAwC,oDAAAzI,MAAA;YAAA,OAAyBR,GAAA,CAAAmG,KAAA,CAAA3F,MAAA,CAAa,CAAC,CAAC,CAAC;UAAA,CAAC,CAAC,sBAAA0I,sDAAA1I,MAAA;YAAA,OAA2BR,GAAA,CAAAuG,OAAA,CAAA/F,MAAA,CAAAgG,SAAwB,CAAC;UAAA,CAAC,CAAC;UAvJzvCnJ,EAAE,CAAAwL,UAAA,IAAA3I,wCAAA,qBAuJ8jD,CAAC;UAvJjkD7C,EAAE,CAAA2D,YAAA,CAuJqqD,CAAC,CAAW,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAjB,EAAA;UAvJ5rD1C,EAAE,CAAAiE,UAAA,SAAFjE,EAAE,CAAA8L,WAAA,OAAAnJ,GAAA,CAAA6E,KAAA,CAAAuE,OAAA,CAuJszB,CAAC;UAvJzzB/L,EAAE,CAAAmE,SAAA,EAuJ6lC,CAAC;UAvJhmCnE,EAAE,CAAAgM,WAAA,cAAArJ,GAAA,CAAAuF,SAuJ6lC,CAAC;UAvJhmClI,EAAE,CAAAmE,SAAA,CAuJs0C,CAAC;UAvJz0CnE,EAAE,CAAAiE,UAAA,YAAAtB,GAAA,CAAA6E,KAuJs0C,CAAC;QAAA;MAAA;MAAAyE,YAAA,GAAo5CrM,SAAS,EAA8CC,OAAO,EAAmHC,IAAI,EAA6FC,gBAAgB,EAAoJsG,qBAAqB,EAAwGM,iBAAiB,EAAgGzF,MAAM,EAA0EE,QAAQ,EAA8EP,EAAE,CAACqL,+BAA+B,EAA0KrL,EAAE,CAACsL,sBAAsB;MAAAC,MAAA;MAAAC,eAAA;IAAA,EAAqI;EAAE;AACx/H;AACA3M,UAAU,CAAC,CACP8B,OAAO,CACV,EAAE2F,oBAAoB,CAACmF,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC;AACpD;EAAA,QAAApG,SAAA,oBAAAA,SAAA,KA5JqGlG,EAAE,CAAAmG,iBAAA,CA4JXgB,oBAAoB,EAAc,CAAC;IACnHtB,IAAI,EAAEnF,SAAS;IACf0F,IAAI,EAAE,CAAC;MAAEL,UAAU,EAAE,IAAI;MAAEW,QAAQ,EAAE,cAAc;MAAE6F,OAAO,EAAE,CAClD3M,SAAS,EACTC,OAAO,EACPC,IAAI,EACJC,gBAAgB,EAChBsG,qBAAqB,EACrBM,iBAAiB,EACjBzF,MAAM,EACNE,QAAQ,EACRN,sBAAsB,CACzB;MAAEuL,eAAe,EAAE1L,uBAAuB,CAAC6L,MAAM;MAAEC,SAAS,EAAE,CAAC;QAAE5B,OAAO,EAAE1J,iBAAiB;QAAE2J,QAAQ,EAAE;UAAEC,OAAO,EAAE,GAAG;UAAEC,SAAS,EAAE;QAAG;MAAE,CAAC,CAAC;MAAE0B,cAAc,EAAE,CACxJ;QACIpF,SAAS,EAAElD,oBAAoB;QAC/B0B,MAAM,EAAE,CAAC,UAAU;MACvB,CAAC,CACJ;MAAE6G,IAAI,EAAE;QACL,uBAAuB,EAAE,cAAc;QACvC,oBAAoB,EAAE,WAAW;QACjC,cAAc,EAAE,uBAAuB;QACvC,YAAY,EAAE,sBAAsB;QACpC,aAAa,EAAE,uBAAuB;QACtC,6BAA6B,EAAE;MACnC,CAAC;MAAErB,QAAQ,EAAE,k7BAAk7B;MAAEc,MAAM,EAAE,CAAC,2+BAA2+B;IAAE,CAAC;EACp8D,CAAC,CAAC,QAAkB;IAAE5E,KAAK,EAAE,CAAC;MACtB3B,IAAI,EAAEjF,eAAe;MACrBwF,IAAI,EAAE,CAAC3G,OAAO,EAAE;QAAEmN,IAAI,EAAEnM;MAAY,CAAC;IACzC,CAAC,CAAC;IAAEiH,SAAS,EAAE,CAAC;MACZ7B,IAAI,EAAExF;IACV,CAAC,CAAC;IAAE2D,UAAU,EAAE,CAAC;MACb6B,IAAI,EAAExF;IACV,CAAC,CAAC;IAAEsH,WAAW,EAAE,CAAC;MACd9B,IAAI,EAAEvF;IACV,CAAC,CAAC;IAAEsH,KAAK,EAAE,CAAC;MACR/B,IAAI,EAAEvF;IACV,CAAC,CAAC;IAAEuH,WAAW,EAAE,CAAC;MACdhC,IAAI,EAAExF,KAAK;MACX+F,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAErC,QAAQ,EAAE;EAAG,CAAC;AAAA;AAE9B,MAAM8I,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACrH,IAAI,YAAAsH,2BAAApH,CAAA;MAAA,YAAAA,CAAA,IAAyFmH,kBAAkB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAAClH,IAAI,kBAvM+E3F,EAAE,CAAA4F,iBAAA;MAAAC,IAAA,EAuMJgH,kBAAkB;MAAArG,SAAA;MAAAT,UAAA;MAAAC,QAAA,GAvMhBhG,EAAE,CAAA4K,kBAAA,CAuMiF,CAC5KpI,wBAAwB,CAAC;QACrBuK,UAAU,EAAE,WAAW;QACvBC,IAAI,EAAE;MACV,CAAC,CAAC,CACL;IAAA,EAAiB;EAAE;AAC5B;AACA;EAAA,QAAA9G,SAAA,oBAAAA,SAAA,KA9MqGlG,EAAE,CAAAmG,iBAAA,CA8MX0G,kBAAkB,EAAc,CAAC;IACjHhH,IAAI,EAAEzF,SAAS;IACfgG,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBW,QAAQ,EAAE,sBAAsB;MAChC+F,SAAS,EAAE,CACPjK,wBAAwB,CAAC;QACrBuK,UAAU,EAAE,WAAW;QACvBC,IAAI,EAAE;MACV,CAAC,CAAC;IAEV,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMC,WAAW,GAAG,CAChBxN,OAAO,EACP0H,oBAAoB,EACpB/C,oBAAoB,EACpBiC,qBAAqB,EACrBwG,kBAAkB,EAClBlG,iBAAiB,CACpB;;AAED;AACA;AACA;;AAEA,SAASsG,WAAW,EAAE5G,qBAAqB,EAAEwG,kBAAkB,EAAE1F,oBAAoB,EAAE/C,oBAAoB,EAAEuC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}