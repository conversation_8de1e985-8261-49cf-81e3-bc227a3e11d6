{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Directive } from '@angular/core';\nimport { tuiButtonOptionsProvider } from '@taiga-ui/core/components/button';\nimport { TUI_ICON_START, TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nclass TuiButtonClose {\n  static {\n    this.ɵfac = function TuiButtonClose_Factory(t) {\n      return new (t || TuiButtonClose)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiButtonClose,\n      selectors: [[\"\", \"tuiIconButton\", \"\", \"tuiButtonClose\", \"\"]],\n      hostVars: 2,\n      hostBindings: function TuiButtonClose_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-radius\", 100, \"%\");\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiButtonOptionsProvider({\n        appearance: 'neutral',\n        size: 's'\n      }), {\n        provide: TUI_ICON_START,\n        useFactory: () => inject(TUI_COMMON_ICONS).close\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiButtonClose, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiIconButton][tuiButtonClose]',\n      providers: [tuiButtonOptionsProvider({\n        appearance: 'neutral',\n        size: 's'\n      }), {\n        provide: TUI_ICON_START,\n        useFactory: () => inject(TUI_COMMON_ICONS).close\n      }],\n      host: {\n        '[style.--t-radius.%]': '100'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiButtonClose };", "map": {"version": 3, "names": ["i0", "inject", "Directive", "tuiButtonOptionsProvider", "TUI_ICON_START", "TUI_COMMON_ICONS", "TuiButtonClose", "ɵfac", "TuiButtonClose_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostVars", "hostBindings", "TuiButtonClose_HostBindings", "rf", "ctx", "ɵɵstyleProp", "standalone", "features", "ɵɵProvidersFeature", "appearance", "size", "provide", "useFactory", "close", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-button-close.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Directive } from '@angular/core';\nimport { tuiButtonOptionsProvider } from '@taiga-ui/core/components/button';\nimport { TUI_ICON_START, TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\n\nclass TuiButtonClose {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiButtonClose, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiButtonClose, isStandalone: true, selector: \"[tuiIconButton][tuiButtonClose]\", host: { properties: { \"style.--t-radius.%\": \"100\" } }, providers: [\n            tuiButtonOptionsProvider({ appearance: 'neutral', size: 's' }),\n            {\n                provide: TUI_ICON_START,\n                useFactory: () => inject(TUI_COMMON_ICONS).close,\n            },\n        ], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiButtonClose, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiIconButton][tuiButtonClose]',\n                    providers: [\n                        tuiButtonOptionsProvider({ appearance: 'neutral', size: 's' }),\n                        {\n                            provide: TUI_ICON_START,\n                            useFactory: () => inject(TUI_COMMON_ICONS).close,\n                        },\n                    ],\n                    host: {\n                        '[style.--t-radius.%]': '100',\n                    },\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiButtonClose };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACjD,SAASC,wBAAwB,QAAQ,kCAAkC;AAC3E,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,uBAAuB;AAExE,MAAMC,cAAc,CAAC;EACjB;IAAS,IAAI,CAACC,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACI,IAAI,kBAD+EV,EAAE,CAAAW,iBAAA;MAAAC,IAAA,EACJN,cAAc;MAAAO,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADZjB,EAAE,CAAAmB,WAAA,eACJ,GAAG,KAAU,CAAC;QAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GADZrB,EAAE,CAAAsB,kBAAA,CAC+I,CAC1OnB,wBAAwB,CAAC;QAAEoB,UAAU,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAI,CAAC,CAAC,EAC9D;QACIC,OAAO,EAAErB,cAAc;QACvBsB,UAAU,EAAEA,CAAA,KAAMzB,MAAM,CAACI,gBAAgB,CAAC,CAACsB;MAC/C,CAAC,CACJ;IAAA,EAAiB;EAAE;AAC5B;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KATqG5B,EAAE,CAAA6B,iBAAA,CASXvB,cAAc,EAAc,CAAC;IAC7GM,IAAI,EAAEV,SAAS;IACf4B,IAAI,EAAE,CAAC;MACCV,UAAU,EAAE,IAAI;MAChBW,QAAQ,EAAE,iCAAiC;MAC3CC,SAAS,EAAE,CACP7B,wBAAwB,CAAC;QAAEoB,UAAU,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAI,CAAC,CAAC,EAC9D;QACIC,OAAO,EAAErB,cAAc;QACvBsB,UAAU,EAAEA,CAAA,KAAMzB,MAAM,CAACI,gBAAgB,CAAC,CAACsB;MAC/C,CAAC,CACJ;MACDM,IAAI,EAAE;QACF,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS3B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}