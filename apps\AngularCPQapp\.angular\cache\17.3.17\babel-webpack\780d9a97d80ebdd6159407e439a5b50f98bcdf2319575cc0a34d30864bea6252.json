{"ast": null, "code": "import * as i1$1 from '@angular/common';\nimport { NgIf, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Injectable, Directive, INJECTOR, Injector, ViewEncapsulation } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated, TuiAnimatedParent } from '@taiga-ui/cdk/directives/animated';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { TUI_NOTIFICATION_OPTIONS, TuiNotification } from '@taiga-ui/core/components/notification';\nimport { TuiTitle } from '@taiga-ui/core/directives/title';\nimport { TUI_COMMON_ICONS, TUI_CLOSE_WORD } from '@taiga-ui/core/tokens';\nimport { injectContext, PolymorpheusOutlet, POLYMORPHEUS_CONTEXT } from '@taiga-ui/polymorpheus';\nimport { BehaviorSubject, combineLatest, of, map, switchMap, timer, EMPTY, takeUntil, fromEvent, repeat, identity } from 'rxjs';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiCreateTokenFromFactory, tuiCreateToken } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiPopoverDirective } from '@taiga-ui/cdk/directives/popover';\nimport { TuiPopoverService, tuiAsPopover } from '@taiga-ui/cdk/services';\nimport { TuiMapperPipe } from '@taiga-ui/cdk/pipes/mapper';\nfunction TuiAlertComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r1, \" \");\n  }\n}\nfunction TuiAlertComponent_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 7);\n  }\n  if (rf & 2) {\n    const text_r2 = ctx.polymorpheusOutlet;\n    i0.ɵɵproperty(\"innerHTML\", text_r2, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction TuiAlertComponent_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function TuiAlertComponent_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.item.$implicit.complete());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"iconStart\", ctx_r3.icons.close);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.close(), \" \");\n  }\n}\nfunction TuiAlerts_div_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 3);\n    i0.ɵɵpipe(1, \"tuiMapper\");\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngComponentOutlet\", item_r1.component.component)(\"ngComponentOutletInjector\", i0.ɵɵpipeBind2(1, 2, item_r1, ctx_r1.mapper));\n  }\n}\nfunction TuiAlerts_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, TuiAlerts_div_0_ng_container_1_Template, 2, 5, \"ng-container\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", group_r3);\n  }\n}\nconst TUI_ALERT_DEFAULT_OPTIONS = {\n  autoClose: 3000,\n  label: '',\n  closeable: true,\n  data: undefined\n};\nconst TUI_ALERT_OPTIONS = tuiCreateTokenFromFactory(() => ({\n  ...TUI_ALERT_DEFAULT_OPTIONS,\n  ...inject(TUI_NOTIFICATION_OPTIONS)\n}));\nconst TUI_ALERT_POSITION = tuiCreateTokenFromFactory(() => inject(TUI_IS_MOBILE) ? '1rem 1rem 0 auto' : '2rem 3rem 0 auto');\nconst TUI_ALERTS = tuiCreateToken(new BehaviorSubject([]));\n/**\n * Grouping alerts by their component\n */\nconst TUI_ALERTS_GROUPED = tuiCreateTokenFromFactory(() => combineLatest([of(new Map()), inject(TUI_ALERTS)]).pipe(map(([map, alerts]) => {\n  map.forEach((_, key) => map.set(key, []));\n  alerts.forEach(alert => {\n    const key = alert.component.component;\n    const value = map.get(key) || [];\n    map.set(key, [...value, alert]);\n  });\n  return Array.from(map.values());\n})));\nfunction tuiAlertOptionsProvider(options) {\n  return {\n    provide: TUI_ALERT_OPTIONS,\n    useFactory: () => ({\n      ...TUI_ALERT_DEFAULT_OPTIONS,\n      ...(inject(TUI_ALERT_OPTIONS, {\n        optional: true,\n        skipSelf: true\n      }) || inject(TUI_NOTIFICATION_OPTIONS)),\n      ...options\n    })\n  };\n}\nclass TuiAlertComponent {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.icons = inject(TUI_COMMON_ICONS);\n    this.close = toSignal(inject(TUI_CLOSE_WORD));\n    this.position = inject(TUI_ALERT_POSITION);\n    this.item = injectContext();\n    this.sub = of(typeof this.item.autoClose === 'function' ? this.item.autoClose(this.item.appearance) : this.item.autoClose).pipe(switchMap(autoClose => autoClose ? timer(autoClose) : EMPTY), takeUntil(fromEvent(this.el, 'mouseenter')), repeat({\n      delay: () => fromEvent(this.el, 'mouseleave')\n    }), takeUntilDestroyed()).subscribe(() => this.item.$implicit.complete());\n  }\n  get from() {\n    return this.position.endsWith('auto') ? 'translateX(100%)' : 'translateX(-100%)';\n  }\n  static {\n    this.ɵfac = function TuiAlertComponent_Factory(t) {\n      return new (t || TuiAlertComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiAlertComponent,\n      selectors: [[\"tui-alert\"]],\n      hostAttrs: [\"role\", \"alert\"],\n      hostVars: 4,\n      hostBindings: function TuiAlertComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"margin\", ctx.position)(\"--tui-from\", ctx.from);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([i1.TuiAnimated]), i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 9,\n      consts: [[1, \"t-wrapper\"], [\"size\", \"m\", 3, \"appearance\", \"icon\"], [\"tuiTitle\", \"\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [\"tuiSubtitle\", \"\"], [3, \"innerHTML\", 4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [\"tuiIconButton\", \"\", \"type\", \"button\", 3, \"iconStart\", \"click\", 4, \"ngIf\"], [3, \"innerHTML\"], [\"tuiIconButton\", \"\", \"type\", \"button\", 3, \"click\", \"iconStart\"]],\n      template: function TuiAlertComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"tui-notification\", 1)(2, \"span\", 2);\n          i0.ɵɵtemplate(3, TuiAlertComponent_ng_container_3_Template, 2, 1, \"ng-container\", 3);\n          i0.ɵɵelementStart(4, \"span\", 4);\n          i0.ɵɵtemplate(5, TuiAlertComponent_span_5_Template, 1, 1, \"span\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, TuiAlertComponent_button_6_Template, 2, 2, \"button\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"t-closeable\", ctx.item.closeable);\n          i0.ɵɵproperty(\"appearance\", ctx.item.appearance)(\"icon\", ctx.item.icon);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.item.label)(\"polymorpheusOutletContext\", ctx.item);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.item.content)(\"polymorpheusOutletContext\", ctx.item);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.item.closeable);\n        }\n      },\n      dependencies: [NgIf, PolymorpheusOutlet, TuiButton, TuiNotification, TuiTitle],\n      styles: [\"[_nghost-%COMP%]{display:grid;inline-size:18rem;flex-shrink:0;word-break:break-word}.tui-enter[_nghost-%COMP%], .tui-leave[_nghost-%COMP%]{animation-name:tuiFade,tuiSlide,tuiCollapse}[_nghost-%COMP%]:not(:first-child){margin-top:0!important}[_nghost-%COMP%]:not(:last-child){margin-bottom:0!important}.t-wrapper[_ngcontent-%COMP%]{transition-property:margin;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;grid-row:1 / span 2;overflow:hidden;margin-bottom:.75rem;background:var(--tui-background-base);border-radius:var(--tui-radius-m);box-shadow:var(--tui-shadow-medium)}.tui-leave[_nghost-%COMP%]   .t-wrapper[_ngcontent-%COMP%]{margin:0}.t-closeable[_ngcontent-%COMP%]{padding-inline-end:2.5rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAlertComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-alert',\n      imports: [NgIf, PolymorpheusOutlet, TuiButton, TuiNotification, TuiTitle],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      hostDirectives: [TuiAnimated],\n      host: {\n        role: 'alert',\n        '[style.margin]': 'position',\n        '[style.--tui-from]': 'from'\n      },\n      template: \"<div class=\\\"t-wrapper\\\">\\n    <tui-notification\\n        size=\\\"m\\\"\\n        [appearance]=\\\"item.appearance\\\"\\n        [class.t-closeable]=\\\"item.closeable\\\"\\n        [icon]=\\\"item.icon\\\"\\n    >\\n        <span tuiTitle>\\n            <ng-container *polymorpheusOutlet=\\\"item.label as text; context: item\\\">\\n                {{ text }}\\n            </ng-container>\\n            <span tuiSubtitle>\\n                <span\\n                    *polymorpheusOutlet=\\\"item.content as text; context: item\\\"\\n                    [innerHTML]=\\\"text\\\"\\n                ></span>\\n            </span>\\n        </span>\\n        <button\\n            *ngIf=\\\"item.closeable\\\"\\n            tuiIconButton\\n            type=\\\"button\\\"\\n            [iconStart]=\\\"icons.close\\\"\\n            (click)=\\\"item.$implicit.complete()\\\"\\n        >\\n            {{ close() }}\\n        </button>\\n    </tui-notification>\\n</div>\\n\",\n      styles: [\":host{display:grid;inline-size:18rem;flex-shrink:0;word-break:break-word}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide,tuiCollapse}:host:not(:first-child){margin-top:0!important}:host:not(:last-child){margin-bottom:0!important}.t-wrapper{transition-property:margin;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;grid-row:1 / span 2;overflow:hidden;margin-bottom:.75rem;background:var(--tui-background-base);border-radius:var(--tui-radius-m);box-shadow:var(--tui-shadow-medium)}:host.tui-leave .t-wrapper{margin:0}.t-closeable{padding-inline-end:2.5rem}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiAlertService extends TuiPopoverService {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiAlertService_BaseFactory;\n      return function TuiAlertService_Factory(t) {\n        return (ɵTuiAlertService_BaseFactory || (ɵTuiAlertService_BaseFactory = i0.ɵɵgetInheritedFactory(TuiAlertService)))(t || TuiAlertService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiAlertService,\n      factory: () => (() => new TuiAlertService(TUI_ALERTS, TuiAlertComponent, inject(TUI_ALERT_OPTIONS)))(),\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAlertService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => new TuiAlertService(TUI_ALERTS, TuiAlertComponent, inject(TUI_ALERT_OPTIONS))\n    }]\n  }], null, null);\n})();\nclass TuiAlert extends TuiPopoverDirective {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiAlert_BaseFactory;\n      return function TuiAlert_Factory(t) {\n        return (ɵTuiAlert_BaseFactory || (ɵTuiAlert_BaseFactory = i0.ɵɵgetInheritedFactory(TuiAlert)))(t || TuiAlert);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiAlert,\n      selectors: [[\"ng-template\", \"tuiAlert\", \"\"]],\n      inputs: {\n        options: [i0.ɵɵInputFlags.None, \"tuiAlertOptions\", \"options\"],\n        open: [i0.ɵɵInputFlags.None, \"tuiAlert\", \"open\"]\n      },\n      outputs: {\n        openChange: \"tuiAlertChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsPopover(TuiAlertService)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAlert, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-template[tuiAlert]',\n      inputs: ['options: tuiAlertOptions', 'open: tuiAlert'],\n      outputs: ['openChange: tuiAlertChange'],\n      providers: [tuiAsPopover(TuiAlertService)]\n    }]\n  }], null, null);\n})();\nclass TuiAlerts {\n  constructor() {\n    this.injector = inject(INJECTOR);\n    this.alerts$ = inject(TUI_ALERTS_GROUPED);\n    this.trackBy = identity;\n    this.mapper = useValue => Injector.create({\n      providers: [{\n        provide: POLYMORPHEUS_CONTEXT,\n        useValue\n      }],\n      parent: this.injector\n    });\n  }\n  static {\n    this.ɵfac = function TuiAlerts_Factory(t) {\n      return new (t || TuiAlerts)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiAlerts,\n      selectors: [[\"tui-alerts\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 4,\n      consts: [[\"tuiAnimatedParent\", \"\", \"class\", \"t-wrapper\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"tuiAnimatedParent\", \"\", 1, \"t-wrapper\"], [3, \"ngComponentOutlet\", \"ngComponentOutletInjector\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngComponentOutlet\", \"ngComponentOutletInjector\"]],\n      template: function TuiAlerts_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiAlerts_div_0_Template, 2, 1, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(1, 2, ctx.alerts$))(\"ngForTrackBy\", ctx.trackBy);\n        }\n      },\n      dependencies: [CommonModule, i1$1.NgComponentOutlet, i1$1.NgForOf, i1$1.AsyncPipe, TuiAnimatedParent, TuiMapperPipe],\n      styles: [\"tui-alerts>.t-wrapper{position:fixed;top:0;left:0;inline-size:100%;display:flex;flex-direction:column;pointer-events:none;box-sizing:border-box;block-size:100%;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom)}tui-alerts>.t-wrapper>*{pointer-events:auto}\\n\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAlerts, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-alerts',\n      imports: [CommonModule, TuiAnimatedParent, TuiMapperPipe],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      template: \"<div\\n    *ngFor=\\\"let group of alerts$ | async; trackBy: trackBy\\\"\\n    tuiAnimatedParent\\n    class=\\\"t-wrapper\\\"\\n>\\n    <ng-container\\n        *ngFor=\\\"let item of group\\\"\\n        [ngComponentOutlet]=\\\"item.component.component\\\"\\n        [ngComponentOutletInjector]=\\\"item | tuiMapper: mapper\\\"\\n    />\\n</div>\\n\",\n      styles: [\"tui-alerts>.t-wrapper{position:fixed;top:0;left:0;inline-size:100%;display:flex;flex-direction:column;pointer-events:none;box-sizing:border-box;block-size:100%;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom)}tui-alerts>.t-wrapper>*{pointer-events:auto}\\n\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_ALERTS, TUI_ALERTS_GROUPED, TUI_ALERT_DEFAULT_OPTIONS, TUI_ALERT_OPTIONS, TUI_ALERT_POSITION, TuiAlert, TuiAlertComponent, TuiAlertService, TuiAlerts, tuiAlertOptionsProvider };", "map": {"version": 3, "names": ["i1$1", "NgIf", "CommonModule", "i0", "inject", "Component", "ChangeDetectionStrategy", "Injectable", "Directive", "INJECTOR", "Injector", "ViewEncapsulation", "toSignal", "takeUntilDestroyed", "i1", "TuiAnimated", "TuiAnimatedParent", "tuiInjectElement", "TuiButton", "TUI_NOTIFICATION_OPTIONS", "TuiNotification", "Tui<PERSON>it<PERSON>", "TUI_COMMON_ICONS", "TUI_CLOSE_WORD", "injectContext", "Polymorpheus<PERSON><PERSON>let", "POLYMORPHEUS_CONTEXT", "BehaviorSubject", "combineLatest", "of", "map", "switchMap", "timer", "EMPTY", "takeUntil", "fromEvent", "repeat", "identity", "TUI_IS_MOBILE", "tuiCreateTokenFromFactory", "tuiCreateToken", "TuiPopoverDirective", "TuiPopoverService", "tuiAsPopover", "TuiMapperPipe", "TuiAlertComponent_ng_container_3_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "text_r1", "polymorpheusOutlet", "ɵɵadvance", "ɵɵtextInterpolate1", "TuiAlertComponent_span_5_Template", "ɵɵelement", "text_r2", "ɵɵproperty", "ɵɵsanitizeHtml", "TuiAlertComponent_button_6_Template", "_r3", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiAlertComponent_button_6_Template_button_click_0_listener", "ɵɵrestoreView", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "item", "$implicit", "complete", "ɵɵelementEnd", "icons", "close", "TuiAlerts_div_0_ng_container_1_Template", "ɵɵelementContainer", "ɵɵpipe", "item_r1", "ctx_r1", "component", "ɵɵpipeBind2", "mapper", "TuiAlerts_div_0_Template", "ɵɵtemplate", "group_r3", "TUI_ALERT_DEFAULT_OPTIONS", "autoClose", "label", "closeable", "data", "undefined", "TUI_ALERT_OPTIONS", "TUI_ALERT_POSITION", "TUI_ALERTS", "TUI_ALERTS_GROUPED", "Map", "pipe", "alerts", "for<PERSON>ach", "_", "key", "set", "alert", "value", "get", "Array", "from", "values", "tuiAlertOptionsProvider", "options", "provide", "useFactory", "optional", "skipSelf", "TuiAlertComponent", "constructor", "el", "position", "sub", "appearance", "delay", "subscribe", "endsWith", "ɵfac", "TuiAlertComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "TuiAlertComponent_HostBindings", "ɵɵstyleProp", "standalone", "features", "ɵɵHostDirectivesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiAlertComponent_Template", "ɵɵclassProp", "icon", "content", "dependencies", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "hostDirectives", "host", "role", "TuiAlertService", "ɵTuiAlertService_BaseFactory", "TuiAlertService_Factory", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵTuiAlert_BaseFactory", "TuiAlert_Factory", "ɵdir", "ɵɵdefineDirective", "inputs", "ɵɵInputFlags", "None", "open", "outputs", "openChange", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "providers", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "injector", "alerts$", "trackBy", "useValue", "create", "parent", "TuiAlerts_Factory", "TuiAlerts_Template", "ɵɵpipeBind1", "NgComponentOutlet", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AsyncPipe", "encapsulation", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-components-alert.mjs"], "sourcesContent": ["import * as i1$1 from '@angular/common';\nimport { NgIf, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Injectable, Directive, INJECTOR, Injector, ViewEncapsulation } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated, TuiAnimatedParent } from '@taiga-ui/cdk/directives/animated';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { TUI_NOTIFICATION_OPTIONS, TuiNotification } from '@taiga-ui/core/components/notification';\nimport { TuiTitle } from '@taiga-ui/core/directives/title';\nimport { TUI_COMMON_ICONS, TUI_CLOSE_WORD } from '@taiga-ui/core/tokens';\nimport { injectContext, PolymorpheusOutlet, POLYMORPHEUS_CONTEXT } from '@taiga-ui/polymorpheus';\nimport { BehaviorSubject, combineLatest, of, map, switchMap, timer, EMPTY, takeUntil, fromEvent, repeat, identity } from 'rxjs';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiCreateTokenFromFactory, tuiCreateToken } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiPopoverDirective } from '@taiga-ui/cdk/directives/popover';\nimport { TuiPopoverService, tuiAsPopover } from '@taiga-ui/cdk/services';\nimport { TuiMapperPipe } from '@taiga-ui/cdk/pipes/mapper';\n\nconst TUI_ALERT_DEFAULT_OPTIONS = {\n    autoClose: 3000,\n    label: '',\n    closeable: true,\n    data: undefined,\n};\nconst TUI_ALERT_OPTIONS = tuiCreateTokenFromFactory(() => ({\n    ...TUI_ALERT_DEFAULT_OPTIONS,\n    ...inject(TUI_NOTIFICATION_OPTIONS),\n}));\nconst TUI_ALERT_POSITION = tuiCreateTokenFromFactory(() => inject(TUI_IS_MOBILE) ? '1rem 1rem 0 auto' : '2rem 3rem 0 auto');\nconst TUI_ALERTS = tuiCreateToken(new BehaviorSubject([]));\n/**\n * Grouping alerts by their component\n */\nconst TUI_ALERTS_GROUPED = tuiCreateTokenFromFactory(() => combineLatest([\n    of(new Map()),\n    inject(TUI_ALERTS),\n]).pipe(map(([map, alerts]) => {\n    map.forEach((_, key) => map.set(key, []));\n    alerts.forEach((alert) => {\n        const key = alert.component.component;\n        const value = map.get(key) || [];\n        map.set(key, [...value, alert]);\n    });\n    return Array.from(map.values());\n})));\nfunction tuiAlertOptionsProvider(options) {\n    return {\n        provide: TUI_ALERT_OPTIONS,\n        useFactory: () => ({\n            ...TUI_ALERT_DEFAULT_OPTIONS,\n            ...(inject(TUI_ALERT_OPTIONS, { optional: true, skipSelf: true }) ||\n                inject(TUI_NOTIFICATION_OPTIONS)),\n            ...options,\n        }),\n    };\n}\n\nclass TuiAlertComponent {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.icons = inject(TUI_COMMON_ICONS);\n        this.close = toSignal(inject(TUI_CLOSE_WORD));\n        this.position = inject(TUI_ALERT_POSITION);\n        this.item = injectContext();\n        this.sub = of(typeof this.item.autoClose === 'function'\n            ? this.item.autoClose(this.item.appearance)\n            : this.item.autoClose)\n            .pipe(switchMap((autoClose) => (autoClose ? timer(autoClose) : EMPTY)), takeUntil(fromEvent(this.el, 'mouseenter')), repeat({ delay: () => fromEvent(this.el, 'mouseleave') }), takeUntilDestroyed())\n            .subscribe(() => this.item.$implicit.complete());\n    }\n    get from() {\n        return this.position.endsWith('auto') ? 'translateX(100%)' : 'translateX(-100%)';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAlertComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAlertComponent, isStandalone: true, selector: \"tui-alert\", host: { attributes: { \"role\": \"alert\" }, properties: { \"style.margin\": \"position\", \"style.--tui-from\": \"from\" } }, hostDirectives: [{ directive: i1.TuiAnimated }], ngImport: i0, template: \"<div class=\\\"t-wrapper\\\">\\n    <tui-notification\\n        size=\\\"m\\\"\\n        [appearance]=\\\"item.appearance\\\"\\n        [class.t-closeable]=\\\"item.closeable\\\"\\n        [icon]=\\\"item.icon\\\"\\n    >\\n        <span tuiTitle>\\n            <ng-container *polymorpheusOutlet=\\\"item.label as text; context: item\\\">\\n                {{ text }}\\n            </ng-container>\\n            <span tuiSubtitle>\\n                <span\\n                    *polymorpheusOutlet=\\\"item.content as text; context: item\\\"\\n                    [innerHTML]=\\\"text\\\"\\n                ></span>\\n            </span>\\n        </span>\\n        <button\\n            *ngIf=\\\"item.closeable\\\"\\n            tuiIconButton\\n            type=\\\"button\\\"\\n            [iconStart]=\\\"icons.close\\\"\\n            (click)=\\\"item.$implicit.complete()\\\"\\n        >\\n            {{ close() }}\\n        </button>\\n    </tui-notification>\\n</div>\\n\", styles: [\":host{display:grid;inline-size:18rem;flex-shrink:0;word-break:break-word}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide,tuiCollapse}:host:not(:first-child){margin-top:0!important}:host:not(:last-child){margin-bottom:0!important}.t-wrapper{transition-property:margin;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;grid-row:1 / span 2;overflow:hidden;margin-bottom:.75rem;background:var(--tui-background-base);border-radius:var(--tui-radius-m);box-shadow:var(--tui-shadow-medium)}:host.tui-leave .t-wrapper{margin:0}.t-closeable{padding-inline-end:2.5rem}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"directive\", type: TuiNotification, selector: \"tui-notification,a[tuiNotification],button[tuiNotification]\", inputs: [\"appearance\", \"icon\", \"size\"] }, { kind: \"directive\", type: TuiTitle, selector: \"[tuiTitle]\", inputs: [\"tuiTitle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAlertComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-alert', imports: [NgIf, PolymorpheusOutlet, TuiButton, TuiNotification, TuiTitle], changeDetection: ChangeDetectionStrategy.OnPush, hostDirectives: [TuiAnimated], host: {\n                        role: 'alert',\n                        '[style.margin]': 'position',\n                        '[style.--tui-from]': 'from',\n                    }, template: \"<div class=\\\"t-wrapper\\\">\\n    <tui-notification\\n        size=\\\"m\\\"\\n        [appearance]=\\\"item.appearance\\\"\\n        [class.t-closeable]=\\\"item.closeable\\\"\\n        [icon]=\\\"item.icon\\\"\\n    >\\n        <span tuiTitle>\\n            <ng-container *polymorpheusOutlet=\\\"item.label as text; context: item\\\">\\n                {{ text }}\\n            </ng-container>\\n            <span tuiSubtitle>\\n                <span\\n                    *polymorpheusOutlet=\\\"item.content as text; context: item\\\"\\n                    [innerHTML]=\\\"text\\\"\\n                ></span>\\n            </span>\\n        </span>\\n        <button\\n            *ngIf=\\\"item.closeable\\\"\\n            tuiIconButton\\n            type=\\\"button\\\"\\n            [iconStart]=\\\"icons.close\\\"\\n            (click)=\\\"item.$implicit.complete()\\\"\\n        >\\n            {{ close() }}\\n        </button>\\n    </tui-notification>\\n</div>\\n\", styles: [\":host{display:grid;inline-size:18rem;flex-shrink:0;word-break:break-word}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide,tuiCollapse}:host:not(:first-child){margin-top:0!important}:host:not(:last-child){margin-bottom:0!important}.t-wrapper{transition-property:margin;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;grid-row:1 / span 2;overflow:hidden;margin-bottom:.75rem;background:var(--tui-background-base);border-radius:var(--tui-radius-m);box-shadow:var(--tui-shadow-medium)}:host.tui-leave .t-wrapper{margin:0}.t-closeable{padding-inline-end:2.5rem}\\n\"] }]\n        }] });\n\nclass TuiAlertService extends TuiPopoverService {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAlertService, deps: null, target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAlertService, providedIn: 'root', useFactory: () => new TuiAlertService(TUI_ALERTS, TuiAlertComponent, inject(TUI_ALERT_OPTIONS)) }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAlertService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                    useFactory: () => new TuiAlertService(TUI_ALERTS, TuiAlertComponent, inject(TUI_ALERT_OPTIONS)),\n                }]\n        }] });\n\nclass TuiAlert extends TuiPopoverDirective {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAlert, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAlert, isStandalone: true, selector: \"ng-template[tuiAlert]\", inputs: { options: [\"tuiAlertOptions\", \"options\"], open: [\"tuiAlert\", \"open\"] }, outputs: { openChange: \"tuiAlertChange\" }, providers: [tuiAsPopover(TuiAlertService)], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAlert, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-template[tuiAlert]',\n                    inputs: ['options: tuiAlertOptions', 'open: tuiAlert'],\n                    outputs: ['openChange: tuiAlertChange'],\n                    providers: [tuiAsPopover(TuiAlertService)],\n                }]\n        }] });\n\nclass TuiAlerts {\n    constructor() {\n        this.injector = inject(INJECTOR);\n        this.alerts$ = inject(TUI_ALERTS_GROUPED);\n        this.trackBy = identity;\n        this.mapper = (useValue) => Injector.create({\n            providers: [\n                {\n                    provide: POLYMORPHEUS_CONTEXT,\n                    useValue,\n                },\n            ],\n            parent: this.injector,\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAlerts, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiAlerts, isStandalone: true, selector: \"tui-alerts\", ngImport: i0, template: \"<div\\n    *ngFor=\\\"let group of alerts$ | async; trackBy: trackBy\\\"\\n    tuiAnimatedParent\\n    class=\\\"t-wrapper\\\"\\n>\\n    <ng-container\\n        *ngFor=\\\"let item of group\\\"\\n        [ngComponentOutlet]=\\\"item.component.component\\\"\\n        [ngComponentOutletInjector]=\\\"item | tuiMapper: mapper\\\"\\n    />\\n</div>\\n\", styles: [\"tui-alerts>.t-wrapper{position:fixed;top:0;left:0;inline-size:100%;display:flex;flex-direction:column;pointer-events:none;box-sizing:border-box;block-size:100%;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom)}tui-alerts>.t-wrapper>*{pointer-events:auto}\\n\"], dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i1$1.NgComponentOutlet, selector: \"[ngComponentOutlet]\", inputs: [\"ngComponentOutlet\", \"ngComponentOutletInputs\", \"ngComponentOutletInjector\", \"ngComponentOutletContent\", \"ngComponentOutletNgModule\", \"ngComponentOutletNgModuleFactory\"] }, { kind: \"directive\", type: i1$1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"pipe\", type: i1$1.AsyncPipe, name: \"async\" }, { kind: \"directive\", type: TuiAnimatedParent, selector: \"[tuiAnimatedParent]\" }, { kind: \"pipe\", type: TuiMapperPipe, name: \"tuiMapper\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAlerts, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-alerts', imports: [CommonModule, TuiAnimatedParent, TuiMapperPipe], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, template: \"<div\\n    *ngFor=\\\"let group of alerts$ | async; trackBy: trackBy\\\"\\n    tuiAnimatedParent\\n    class=\\\"t-wrapper\\\"\\n>\\n    <ng-container\\n        *ngFor=\\\"let item of group\\\"\\n        [ngComponentOutlet]=\\\"item.component.component\\\"\\n        [ngComponentOutletInjector]=\\\"item | tuiMapper: mapper\\\"\\n    />\\n</div>\\n\", styles: [\"tui-alerts>.t-wrapper{position:fixed;top:0;left:0;inline-size:100%;display:flex;flex-direction:column;pointer-events:none;box-sizing:border-box;block-size:100%;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom)}tui-alerts>.t-wrapper>*{pointer-events:auto}\\n\"] }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_ALERTS, TUI_ALERTS_GROUPED, TUI_ALERT_DEFAULT_OPTIONS, TUI_ALERT_OPTIONS, TUI_ALERT_POSITION, TuiAlert, TuiAlertComponent, TuiAlertService, TuiAlerts, tuiAlertOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,IAAI,MAAM,iBAAiB;AACvC,SAASC,IAAI,EAAEC,YAAY,QAAQ,iBAAiB;AACpD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,iBAAiB,QAAQ,eAAe;AACxI,SAASC,QAAQ,EAAEC,kBAAkB,QAAQ,4BAA4B;AACzE,OAAO,KAAKC,EAAE,MAAM,mCAAmC;AACvD,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,mCAAmC;AAClF,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,wBAAwB,EAAEC,eAAe,QAAQ,wCAAwC;AAClG,SAASC,QAAQ,QAAQ,iCAAiC;AAC1D,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,uBAAuB;AACxE,SAASC,aAAa,EAAEC,kBAAkB,EAAEC,oBAAoB,QAAQ,wBAAwB;AAChG,SAASC,eAAe,EAAEC,aAAa,EAAEC,EAAE,EAAEC,GAAG,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,MAAM;AAC/H,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,yBAAyB,EAAEC,cAAc,QAAQ,mCAAmC;AAC7F,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,wBAAwB;AACxE,SAASC,aAAa,QAAQ,4BAA4B;AAAC,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAyD0C3C,EAAE,CAAA6C,uBAAA,EACwiB,CAAC;IAD3iB7C,EAAE,CAAA8C,MAAA,EACklB,CAAC;IADrlB9C,EAAE,CAAA+C,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAAFjD,EAAE,CAAAkD,SAAA,CACklB,CAAC;IADrlBlD,EAAE,CAAAmD,kBAAA,MAAAH,OAAA,KACklB,CAAC;EAAA;AAAA;AAAA,SAAAI,kCAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADrlB3C,EAAE,CAAAqD,SAAA,aAC6yB,CAAC;EAAA;EAAA,IAAAV,EAAA;IAAA,MAAAW,OAAA,GAAAV,GAAA,CAAAK,kBAAA;IADhzBjD,EAAE,CAAAuD,UAAA,cAAAD,OAAA,EAAFtD,EAAE,CAAAwD,cACmxB,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAe,GAAA,GADtxB1D,EAAE,CAAA2D,gBAAA;IAAF3D,EAAE,CAAA4D,cAAA,eACyiC,CAAC;IAD5iC5D,EAAE,CAAA6D,UAAA,mBAAAC,4DAAA;MAAF9D,EAAE,CAAA+D,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFhE,EAAE,CAAAiE,aAAA;MAAA,OAAFjE,EAAE,CAAAkE,WAAA,CACogCF,MAAA,CAAAG,IAAA,CAAAC,SAAA,CAAAC,QAAA,CAAwB,CAAC;IAAA,CAAC,CAAC;IADjiCrE,EAAE,CAAA8C,MAAA,EAC8kC,CAAC;IADjlC9C,EAAE,CAAAsE,YAAA,CACulC,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAqB,MAAA,GAD1lChE,EAAE,CAAAiE,aAAA;IAAFjE,EAAE,CAAAuD,UAAA,cAAAS,MAAA,CAAAO,KAAA,CAAAC,KAC2+B,CAAC;IAD9+BxE,EAAE,CAAAkD,SAAA,CAC8kC,CAAC;IADjlClD,EAAE,CAAAmD,kBAAA,MAAAa,MAAA,CAAAQ,KAAA,OAC8kC,CAAC;EAAA;AAAA;AAAA,SAAAC,wCAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADjlC3C,EAAE,CAAA0E,kBAAA,KAuD8X,CAAC;IAvDjY1E,EAAE,CAAA2E,MAAA;EAAA;EAAA,IAAAhC,EAAA;IAAA,MAAAiC,OAAA,GAAAhC,GAAA,CAAAwB,SAAA;IAAA,MAAAS,MAAA,GAAF7E,EAAE,CAAAiE,aAAA;IAAFjE,EAAE,CAAAuD,UAAA,sBAAAqB,OAAA,CAAAE,SAAA,CAAAA,SAuDoT,CAAC,8BAvDvT9E,EAAE,CAAA+E,WAAA,OAAAH,OAAA,EAAAC,MAAA,CAAAG,MAAA,CAuDsX,CAAC;EAAA;AAAA;AAAA,SAAAC,yBAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvDzX3C,EAAE,CAAA4D,cAAA,YAuDiM,CAAC;IAvDpM5D,EAAE,CAAAkF,UAAA,IAAAT,uCAAA,yBAuD8X,CAAC;IAvDjYzE,EAAE,CAAAsE,YAAA,CAuDsY,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAwC,QAAA,GAAAvC,GAAA,CAAAwB,SAAA;IAvDzYpE,EAAE,CAAAkD,SAAA,CAuDwP,CAAC;IAvD3PlD,EAAE,CAAAuD,UAAA,YAAA4B,QAuDwP,CAAC;EAAA;AAAA;AA9GhW,MAAMC,yBAAyB,GAAG;EAC9BC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE,EAAE;EACTC,SAAS,EAAE,IAAI;EACfC,IAAI,EAAEC;AACV,CAAC;AACD,MAAMC,iBAAiB,GAAGtD,yBAAyB,CAAC,OAAO;EACvD,GAAGgD,yBAAyB;EAC5B,GAAGnF,MAAM,CAACe,wBAAwB;AACtC,CAAC,CAAC,CAAC;AACH,MAAM2E,kBAAkB,GAAGvD,yBAAyB,CAAC,MAAMnC,MAAM,CAACkC,aAAa,CAAC,GAAG,kBAAkB,GAAG,kBAAkB,CAAC;AAC3H,MAAMyD,UAAU,GAAGvD,cAAc,CAAC,IAAIb,eAAe,CAAC,EAAE,CAAC,CAAC;AAC1D;AACA;AACA;AACA,MAAMqE,kBAAkB,GAAGzD,yBAAyB,CAAC,MAAMX,aAAa,CAAC,CACrEC,EAAE,CAAC,IAAIoE,GAAG,CAAC,CAAC,CAAC,EACb7F,MAAM,CAAC2F,UAAU,CAAC,CACrB,CAAC,CAACG,IAAI,CAACpE,GAAG,CAAC,CAAC,CAACA,GAAG,EAAEqE,MAAM,CAAC,KAAK;EAC3BrE,GAAG,CAACsE,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAKxE,GAAG,CAACyE,GAAG,CAACD,GAAG,EAAE,EAAE,CAAC,CAAC;EACzCH,MAAM,CAACC,OAAO,CAAEI,KAAK,IAAK;IACtB,MAAMF,GAAG,GAAGE,KAAK,CAACvB,SAAS,CAACA,SAAS;IACrC,MAAMwB,KAAK,GAAG3E,GAAG,CAAC4E,GAAG,CAACJ,GAAG,CAAC,IAAI,EAAE;IAChCxE,GAAG,CAACyE,GAAG,CAACD,GAAG,EAAE,CAAC,GAAGG,KAAK,EAAED,KAAK,CAAC,CAAC;EACnC,CAAC,CAAC;EACF,OAAOG,KAAK,CAACC,IAAI,CAAC9E,GAAG,CAAC+E,MAAM,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC,CAAC;AACJ,SAASC,uBAAuBA,CAACC,OAAO,EAAE;EACtC,OAAO;IACHC,OAAO,EAAEnB,iBAAiB;IAC1BoB,UAAU,EAAEA,CAAA,MAAO;MACf,GAAG1B,yBAAyB;MAC5B,IAAInF,MAAM,CAACyF,iBAAiB,EAAE;QAAEqB,QAAQ,EAAE,IAAI;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC,IAC7D/G,MAAM,CAACe,wBAAwB,CAAC,CAAC;MACrC,GAAG4F;IACP,CAAC;EACL,CAAC;AACL;AAEA,MAAMK,iBAAiB,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAGrG,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACyD,KAAK,GAAGtE,MAAM,CAACkB,gBAAgB,CAAC;IACrC,IAAI,CAACqD,KAAK,GAAG/D,QAAQ,CAACR,MAAM,CAACmB,cAAc,CAAC,CAAC;IAC7C,IAAI,CAACgG,QAAQ,GAAGnH,MAAM,CAAC0F,kBAAkB,CAAC;IAC1C,IAAI,CAACxB,IAAI,GAAG9C,aAAa,CAAC,CAAC;IAC3B,IAAI,CAACgG,GAAG,GAAG3F,EAAE,CAAC,OAAO,IAAI,CAACyC,IAAI,CAACkB,SAAS,KAAK,UAAU,GACjD,IAAI,CAAClB,IAAI,CAACkB,SAAS,CAAC,IAAI,CAAClB,IAAI,CAACmD,UAAU,CAAC,GACzC,IAAI,CAACnD,IAAI,CAACkB,SAAS,CAAC,CACrBU,IAAI,CAACnE,SAAS,CAAEyD,SAAS,IAAMA,SAAS,GAAGxD,KAAK,CAACwD,SAAS,CAAC,GAAGvD,KAAM,CAAC,EAAEC,SAAS,CAACC,SAAS,CAAC,IAAI,CAACmF,EAAE,EAAE,YAAY,CAAC,CAAC,EAAElF,MAAM,CAAC;MAAEsF,KAAK,EAAEA,CAAA,KAAMvF,SAAS,CAAC,IAAI,CAACmF,EAAE,EAAE,YAAY;IAAE,CAAC,CAAC,EAAEzG,kBAAkB,CAAC,CAAC,CAAC,CACpM8G,SAAS,CAAC,MAAM,IAAI,CAACrD,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,CAAC,CAAC;EACxD;EACA,IAAIoC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACW,QAAQ,CAACK,QAAQ,CAAC,MAAM,CAAC,GAAG,kBAAkB,GAAG,mBAAmB;EACpF;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,0BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFX,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAACY,IAAI,kBAD+E7H,EAAE,CAAA8H,iBAAA;MAAAC,IAAA,EACJd,iBAAiB;MAAAe,SAAA;MAAAC,SAAA,WAA2E,OAAO;MAAAC,QAAA;MAAAC,YAAA,WAAAC,+BAAAzF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADjG3C,EAAE,CAAAqI,WAAA,WAAAzF,GAAA,CAAAwE,QACY,CAAC,eAAAxE,GAAA,CAAA6D,IAAD,CAAC;QAAA;MAAA;MAAA6B,UAAA;MAAAC,QAAA,GADfvI,EAAE,CAAAwI,uBAAA,EAC2M7H,EAAE,CAACC,WAAW,IAD3NZ,EAAE,CAAAyI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAnG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3C,EAAE,CAAA4D,cAAA,YAC+Q,CAAC,yBAAyK,CAAC,aAAwB,CAAC;UADrd5D,EAAE,CAAAkF,UAAA,IAAAxC,yCAAA,yBACwiB,CAAC;UAD3iB1C,EAAE,CAAA4D,cAAA,aACioB,CAAC;UADpoB5D,EAAE,CAAAkF,UAAA,IAAA9B,iCAAA,iBACsyB,CAAC;UADzyBpD,EAAE,CAAAsE,YAAA,CACk0B,CAAC,CAAgB,CAAC;UADt1BtE,EAAE,CAAAkF,UAAA,IAAAzB,mCAAA,mBACyiC,CAAC;UAD5iCzD,EAAE,CAAAsE,YAAA,CACgnC,CAAC,CAAO,CAAC;QAAA;QAAA,IAAA3B,EAAA;UAD3nC3C,EAAE,CAAAkD,SAAA,CACoZ,CAAC;UADvZlD,EAAE,CAAA+I,WAAA,gBAAAnG,GAAA,CAAAuB,IAAA,CAAAoB,SACoZ,CAAC;UADvZvF,EAAE,CAAAuD,UAAA,eAAAX,GAAA,CAAAuB,IAAA,CAAAmD,UACoW,CAAC,SAAA1E,GAAA,CAAAuB,IAAA,CAAA6E,IAA6E,CAAC;UADrbhJ,EAAE,CAAAkD,SAAA,EAC+gB,CAAC;UADlhBlD,EAAE,CAAAuD,UAAA,uBAAAX,GAAA,CAAAuB,IAAA,CAAAmB,KAC+gB,CAAC,8BAAA1C,GAAA,CAAAuB,IAAqB,CAAC;UADxiBnE,EAAE,CAAAkD,SAAA,EACitB,CAAC;UADptBlD,EAAE,CAAAuD,UAAA,uBAAAX,GAAA,CAAAuB,IAAA,CAAA8E,OACitB,CAAC,8BAAArG,GAAA,CAAAuB,IAAqB,CAAC;UAD1uBnE,EAAE,CAAAkD,SAAA,CACw4B,CAAC;UAD34BlD,EAAE,CAAAuD,UAAA,SAAAX,GAAA,CAAAuB,IAAA,CAAAoB,SACw4B,CAAC;QAAA;MAAA;MAAA2D,YAAA,GAA84BpJ,IAAI,EAA6FwB,kBAAkB,EAA8HP,SAAS,EAAoIE,eAAe,EAAgJC,QAAQ;MAAAiI,MAAA;MAAAC,eAAA;IAAA,EAAwG;EAAE;AAC7gF;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGrJ,EAAE,CAAAsJ,iBAAA,CAGXrC,iBAAiB,EAAc,CAAC;IAChHc,IAAI,EAAE7H,SAAS;IACfqJ,IAAI,EAAE,CAAC;MAAEjB,UAAU,EAAE,IAAI;MAAEkB,QAAQ,EAAE,WAAW;MAAEC,OAAO,EAAE,CAAC3J,IAAI,EAAEwB,kBAAkB,EAAEP,SAAS,EAAEE,eAAe,EAAEC,QAAQ,CAAC;MAAEkI,eAAe,EAAEjJ,uBAAuB,CAACuJ,MAAM;MAAEC,cAAc,EAAE,CAAC/I,WAAW,CAAC;MAAEgJ,IAAI,EAAE;QACvMC,IAAI,EAAE,OAAO;QACb,gBAAgB,EAAE,UAAU;QAC5B,oBAAoB,EAAE;MAC1B,CAAC;MAAEhB,QAAQ,EAAE,s4BAAs4B;MAAEM,MAAM,EAAE,CAAC,mmBAAmmB;IAAE,CAAC;EAChhD,CAAC,CAAC;AAAA;AAEV,MAAMW,eAAe,SAASvH,iBAAiB,CAAC;EAC5C;IAAS,IAAI,CAACmF,IAAI;MAAA,IAAAqC,4BAAA;MAAA,gBAAAC,wBAAApC,CAAA;QAAA,QAAAmC,4BAAA,KAAAA,4BAAA,GAb+E/J,EAAE,CAAAiK,qBAAA,CAaQH,eAAe,IAAAlC,CAAA,IAAfkC,eAAe;MAAA;IAAA,IAAsD;EAAE;EAClL;IAAS,IAAI,CAACI,KAAK,kBAd8ElK,EAAE,CAAAmK,kBAAA;MAAAC,KAAA,EAcYN,eAAe;MAAAO,OAAA,EAAAA,CAAA,MAAkC,MAAM,IAAIP,eAAe,CAAClE,UAAU,EAAEqB,iBAAiB,EAAEhH,MAAM,CAACyF,iBAAiB,CAAC,CAAC;MAAA4E,UAAA,EAAvG;IAAM,EAAoG;EAAE;AAC5P;AACA;EAAA,QAAAjB,SAAA,oBAAAA,SAAA,KAhBqGrJ,EAAE,CAAAsJ,iBAAA,CAgBXQ,eAAe,EAAc,CAAC;IAC9G/B,IAAI,EAAE3H,UAAU;IAChBmJ,IAAI,EAAE,CAAC;MACCe,UAAU,EAAE,MAAM;MAClBxD,UAAU,EAAEA,CAAA,KAAM,IAAIgD,eAAe,CAAClE,UAAU,EAAEqB,iBAAiB,EAAEhH,MAAM,CAACyF,iBAAiB,CAAC;IAClG,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM6E,QAAQ,SAASjI,mBAAmB,CAAC;EACvC;IAAS,IAAI,CAACoF,IAAI;MAAA,IAAA8C,qBAAA;MAAA,gBAAAC,iBAAA7C,CAAA;QAAA,QAAA4C,qBAAA,KAAAA,qBAAA,GAzB+ExK,EAAE,CAAAiK,qBAAA,CAyBQM,QAAQ,IAAA3C,CAAA,IAAR2C,QAAQ;MAAA;IAAA,IAAqD;EAAE;EAC1K;IAAS,IAAI,CAACG,IAAI,kBA1B+E1K,EAAE,CAAA2K,iBAAA;MAAA5C,IAAA,EA0BJwC,QAAQ;MAAAvC,SAAA;MAAA4C,MAAA;QAAAhE,OAAA,GA1BN5G,EAAE,CAAA6K,YAAA,CAAAC,IAAA;QAAAC,IAAA,GAAF/K,EAAE,CAAA6K,YAAA,CAAAC,IAAA;MAAA;MAAAE,OAAA;QAAAC,UAAA;MAAA;MAAA3C,UAAA;MAAAC,QAAA,GAAFvI,EAAE,CAAAkL,kBAAA,CA0BoM,CAAC1I,YAAY,CAACsH,eAAe,CAAC,CAAC,GA1BrO9J,EAAE,CAAAmL,0BAAA;IAAA,EA0B2Q;EAAE;AACpX;AACA;EAAA,QAAA9B,SAAA,oBAAAA,SAAA,KA5BqGrJ,EAAE,CAAAsJ,iBAAA,CA4BXiB,QAAQ,EAAc,CAAC;IACvGxC,IAAI,EAAE1H,SAAS;IACfkJ,IAAI,EAAE,CAAC;MACCjB,UAAU,EAAE,IAAI;MAChBkB,QAAQ,EAAE,uBAAuB;MACjCoB,MAAM,EAAE,CAAC,0BAA0B,EAAE,gBAAgB,CAAC;MACtDI,OAAO,EAAE,CAAC,4BAA4B,CAAC;MACvCI,SAAS,EAAE,CAAC5I,YAAY,CAACsH,eAAe,CAAC;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMuB,SAAS,CAAC;EACZnE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoE,QAAQ,GAAGrL,MAAM,CAACK,QAAQ,CAAC;IAChC,IAAI,CAACiL,OAAO,GAAGtL,MAAM,CAAC4F,kBAAkB,CAAC;IACzC,IAAI,CAAC2F,OAAO,GAAGtJ,QAAQ;IACvB,IAAI,CAAC8C,MAAM,GAAIyG,QAAQ,IAAKlL,QAAQ,CAACmL,MAAM,CAAC;MACxCN,SAAS,EAAE,CACP;QACIvE,OAAO,EAAEtF,oBAAoB;QAC7BkK;MACJ,CAAC,CACJ;MACDE,MAAM,EAAE,IAAI,CAACL;IACjB,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC5D,IAAI,YAAAkE,kBAAAhE,CAAA;MAAA,YAAAA,CAAA,IAAyFyD,SAAS;IAAA,CAAmD;EAAE;EACzK;IAAS,IAAI,CAACxD,IAAI,kBAvD+E7H,EAAE,CAAA8H,iBAAA;MAAAC,IAAA,EAuDJsD,SAAS;MAAArD,SAAA;MAAAM,UAAA;MAAAC,QAAA,GAvDPvI,EAAE,CAAAyI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAgD,mBAAAlJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3C,EAAE,CAAAkF,UAAA,IAAAD,wBAAA,gBAuDiM,CAAC;UAvDpMjF,EAAE,CAAA2E,MAAA;QAAA;QAAA,IAAAhC,EAAA;UAAF3C,EAAE,CAAAuD,UAAA,YAAFvD,EAAE,CAAA8L,WAAA,OAAAlJ,GAAA,CAAA2I,OAAA,CAuD4H,CAAC,iBAAA3I,GAAA,CAAA4I,OAAe,CAAC;QAAA;MAAA;MAAAtC,YAAA,GAAmkBnJ,YAAY,EAA+BF,IAAI,CAACkM,iBAAiB,EAAoPlM,IAAI,CAACmM,OAAO,EAA8GnM,IAAI,CAACoM,SAAS,EAA8CpL,iBAAiB,EAA2D4B,aAAa;MAAA0G,MAAA;MAAA+C,aAAA;IAAA,EAAwH;EAAE;AACr/C;AACA;EAAA,QAAA7C,SAAA,oBAAAA,SAAA,KAzDqGrJ,EAAE,CAAAsJ,iBAAA,CAyDX+B,SAAS,EAAc,CAAC;IACxGtD,IAAI,EAAE7H,SAAS;IACfqJ,IAAI,EAAE,CAAC;MAAEjB,UAAU,EAAE,IAAI;MAAEkB,QAAQ,EAAE,YAAY;MAAEC,OAAO,EAAE,CAAC1J,YAAY,EAAEc,iBAAiB,EAAE4B,aAAa,CAAC;MAAEyJ,aAAa,EAAE1L,iBAAiB,CAACsK,IAAI;MAAE1B,eAAe,EAAEjJ,uBAAuB,CAACgM,OAAO;MAAEtD,QAAQ,EAAE,+TAA+T;MAAEM,MAAM,EAAE,CAAC,+QAA+Q;IAAE,CAAC;EACjzB,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASvD,UAAU,EAAEC,kBAAkB,EAAET,yBAAyB,EAAEM,iBAAiB,EAAEC,kBAAkB,EAAE4E,QAAQ,EAAEtD,iBAAiB,EAAE6C,eAAe,EAAEuB,SAAS,EAAE1E,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}