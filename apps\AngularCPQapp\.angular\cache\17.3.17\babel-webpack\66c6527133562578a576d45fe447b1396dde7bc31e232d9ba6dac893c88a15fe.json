{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Input, inject, ChangeDetectorRef, Component, ChangeDetectionStrategy } from '@angular/core';\nimport { WaResizeObserver } from '@ng-web-apis/resize-observer';\nconst _c0 = [\"*\", [[\"tui-badge-notification\", \"tuiSlot\", \"top\"]], [[\"\", \"tuiSlot\", \"top\"]], [[\"tui-badge-notification\", \"tuiSlot\", \"bottom\"]], [[\"\", \"tuiSlot\", \"bottom\"]]];\nconst _c1 = [\"*\", \"tui-badge-notification[tuiSlot='top']\", \"[tuiSlot='top']\", \"tui-badge-notification[tuiSlot='bottom']\", \"[tuiSlot='bottom']\"];\nclass TuiBadgedContentDirective {\n  constructor() {\n    this.tuiSlot = 'top';\n  }\n  static {\n    this.ɵfac = function TuiBadgedContentDirective_Factory(t) {\n      return new (t || TuiBadgedContentDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiBadgedContentDirective,\n      selectors: [[\"\", \"tuiSlot\", \"\"]],\n      inputs: {\n        tuiSlot: \"tuiSlot\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiBadgedContentDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiSlot]'\n    }]\n  }], null, {\n    tuiSlot: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiBadgedContentComponent {\n  constructor() {\n    this.cdr = inject(ChangeDetectorRef);\n  }\n  onResize() {\n    this.cdr.detectChanges();\n  }\n  static {\n    this.ɵfac = function TuiBadgedContentComponent_Factory(t) {\n      return new (t || TuiBadgedContentComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiBadgedContentComponent,\n      selectors: [[\"tui-badged-content\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 11,\n      vars: 4,\n      consts: [[\"badgeTop\", \"\"], [\"badgeBottom\", \"\"], [1, \"t-badge\", \"t-badge_top\", 3, \"waResizeObserver\"], [1, \"t-border\"], [1, \"t-badge\", \"t-badge_bottom\", 3, \"waResizeObserver\"]],\n      template: function TuiBadgedContentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 2, 0);\n          i0.ɵɵlistener(\"waResizeObserver\", function TuiBadgedContentComponent_Template_div_waResizeObserver_1_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onResize());\n          });\n          i0.ɵɵprojection(3, 1);\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵprojection(5, 2);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 4, 1);\n          i0.ɵɵlistener(\"waResizeObserver\", function TuiBadgedContentComponent_Template_div_waResizeObserver_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onResize());\n          });\n          i0.ɵɵprojection(8, 3);\n          i0.ɵɵelementStart(9, \"div\", 3);\n          i0.ɵɵprojection(10, 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const badgeTop_r2 = i0.ɵɵreference(2);\n          const badgeBottom_r3 = i0.ɵɵreference(7);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"--t-badge-height\", badgeTop_r2.offsetHeight, \"px\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleProp(\"--t-badge-height\", badgeBottom_r3.offsetHeight, \"px\");\n        }\n      },\n      dependencies: [WaResizeObserver],\n      styles: [\"[_nghost-%COMP%]{--tui-radius: var(--tui-radius-l);--t-badge-height: 0;--t-corner-offset: calc((var(--tui-radius) * 1.4142 - var(--tui-radius)) * 1 / 1.4142);position:relative;display:inline-block;block-size:-webkit-fit-content;block-size:-moz-fit-content;block-size:fit-content;inline-size:-webkit-max-content;inline-size:max-content;color:var(--tui-background-base)}.t-badge[_ngcontent-%COMP%]{--t-offset: calc(var(--t-badge-height) * -.5 + var(--t-corner-offset));position:absolute;display:flex}.t-badge_top[_ngcontent-%COMP%]{top:var(--t-offset);right:var(--t-offset)}.t-badge_bottom[_ngcontent-%COMP%]{bottom:var(--t-offset);right:var(--t-offset)}.t-border[_ngcontent-%COMP%]:not(:empty){display:flex;border-radius:6rem;box-shadow:0 0 0 1px currentColor;background:currentColor}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiBadgedContentComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-badged-content',\n      imports: [TuiBadgedContentDirective, WaResizeObserver],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-content />\\n\\n<div\\n    #badgeTop\\n    class=\\\"t-badge t-badge_top\\\"\\n    [style.--t-badge-height.px]=\\\"badgeTop.offsetHeight\\\"\\n    (waResizeObserver)=\\\"onResize()\\\"\\n>\\n    <ng-content select=\\\"tui-badge-notification[tuiSlot='top']\\\" />\\n    <div class=\\\"t-border\\\">\\n        <ng-content select=\\\"[tuiSlot='top']\\\" />\\n    </div>\\n</div>\\n\\n<div\\n    #badgeBottom\\n    class=\\\"t-badge t-badge_bottom\\\"\\n    [style.--t-badge-height.px]=\\\"badgeBottom.offsetHeight\\\"\\n    (waResizeObserver)=\\\"onResize()\\\"\\n>\\n    <ng-content select=\\\"tui-badge-notification[tuiSlot='bottom']\\\" />\\n    <div class=\\\"t-border\\\">\\n        <ng-content select=\\\"[tuiSlot='bottom']\\\" />\\n    </div>\\n</div>\\n\",\n      styles: [\":host{--tui-radius: var(--tui-radius-l);--t-badge-height: 0;--t-corner-offset: calc((var(--tui-radius) * 1.4142 - var(--tui-radius)) * 1 / 1.4142);position:relative;display:inline-block;block-size:-webkit-fit-content;block-size:-moz-fit-content;block-size:fit-content;inline-size:-webkit-max-content;inline-size:max-content;color:var(--tui-background-base)}.t-badge{--t-offset: calc(var(--t-badge-height) * -.5 + var(--t-corner-offset));position:absolute;display:flex}.t-badge_top{top:var(--t-offset);right:var(--t-offset)}.t-badge_bottom{bottom:var(--t-offset);right:var(--t-offset)}.t-border:not(:empty){display:flex;border-radius:6rem;box-shadow:0 0 0 1px currentColor;background:currentColor}\\n\"]\n    }]\n  }], null, null);\n})();\nconst TuiBadgedContent = [TuiBadgedContentDirective, TuiBadgedContentComponent];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiBadgedContent, TuiBadgedContentComponent, TuiBadgedContentDirective };", "map": {"version": 3, "names": ["i0", "Directive", "Input", "inject", "ChangeDetectorRef", "Component", "ChangeDetectionStrategy", "WaResizeObserver", "_c0", "_c1", "TuiBadgedContentDirective", "constructor", "tuiSlot", "ɵfac", "TuiBadgedContentDirective_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "TuiBadgedContentComponent", "cdr", "onResize", "detectChanges", "TuiBadgedContentComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "TuiBadgedContentComponent_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵprojectionDef", "ɵɵprojection", "ɵɵelementStart", "ɵɵlistener", "TuiBadgedContentComponent_Template_div_waResizeObserver_1_listener", "ɵɵrestoreView", "ɵɵresetView", "ɵɵelementEnd", "TuiBadgedContentComponent_Template_div_waResizeObserver_6_listener", "badgeTop_r2", "ɵɵreference", "badgeBottom_r3", "ɵɵadvance", "ɵɵstyleProp", "offsetHeight", "dependencies", "styles", "changeDetection", "imports", "OnPush", "TuiBadgedContent"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-badged-content.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Input, inject, ChangeDetectorRef, Component, ChangeDetectionStrategy } from '@angular/core';\nimport { WaResizeObserver } from '@ng-web-apis/resize-observer';\n\nclass TuiBadgedContentDirective {\n    constructor() {\n        this.tuiSlot = 'top';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBadgedContentDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiBadgedContentDirective, isStandalone: true, selector: \"[tuiSlot]\", inputs: { tuiSlot: \"tuiSlot\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBadgedContentDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiSlot]',\n                }]\n        }], propDecorators: { tuiSlot: [{\n                type: Input\n            }] } });\n\nclass TuiBadgedContentComponent {\n    constructor() {\n        this.cdr = inject(ChangeDetectorRef);\n    }\n    onResize() {\n        this.cdr.detectChanges();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBadgedContentComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiBadgedContentComponent, isStandalone: true, selector: \"tui-badged-content\", ngImport: i0, template: \"<ng-content />\\n\\n<div\\n    #badgeTop\\n    class=\\\"t-badge t-badge_top\\\"\\n    [style.--t-badge-height.px]=\\\"badgeTop.offsetHeight\\\"\\n    (waResizeObserver)=\\\"onResize()\\\"\\n>\\n    <ng-content select=\\\"tui-badge-notification[tuiSlot='top']\\\" />\\n    <div class=\\\"t-border\\\">\\n        <ng-content select=\\\"[tuiSlot='top']\\\" />\\n    </div>\\n</div>\\n\\n<div\\n    #badgeBottom\\n    class=\\\"t-badge t-badge_bottom\\\"\\n    [style.--t-badge-height.px]=\\\"badgeBottom.offsetHeight\\\"\\n    (waResizeObserver)=\\\"onResize()\\\"\\n>\\n    <ng-content select=\\\"tui-badge-notification[tuiSlot='bottom']\\\" />\\n    <div class=\\\"t-border\\\">\\n        <ng-content select=\\\"[tuiSlot='bottom']\\\" />\\n    </div>\\n</div>\\n\", styles: [\":host{--tui-radius: var(--tui-radius-l);--t-badge-height: 0;--t-corner-offset: calc((var(--tui-radius) * 1.4142 - var(--tui-radius)) * 1 / 1.4142);position:relative;display:inline-block;block-size:-webkit-fit-content;block-size:-moz-fit-content;block-size:fit-content;inline-size:-webkit-max-content;inline-size:max-content;color:var(--tui-background-base)}.t-badge{--t-offset: calc(var(--t-badge-height) * -.5 + var(--t-corner-offset));position:absolute;display:flex}.t-badge_top{top:var(--t-offset);right:var(--t-offset)}.t-badge_bottom{bottom:var(--t-offset);right:var(--t-offset)}.t-border:not(:empty){display:flex;border-radius:6rem;box-shadow:0 0 0 1px currentColor;background:currentColor}\\n\"], dependencies: [{ kind: \"directive\", type: WaResizeObserver, selector: \"[waResizeObserver]\", inputs: [\"box\"], outputs: [\"waResizeObserver\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBadgedContentComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-badged-content', imports: [TuiBadgedContentDirective, WaResizeObserver], changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-content />\\n\\n<div\\n    #badgeTop\\n    class=\\\"t-badge t-badge_top\\\"\\n    [style.--t-badge-height.px]=\\\"badgeTop.offsetHeight\\\"\\n    (waResizeObserver)=\\\"onResize()\\\"\\n>\\n    <ng-content select=\\\"tui-badge-notification[tuiSlot='top']\\\" />\\n    <div class=\\\"t-border\\\">\\n        <ng-content select=\\\"[tuiSlot='top']\\\" />\\n    </div>\\n</div>\\n\\n<div\\n    #badgeBottom\\n    class=\\\"t-badge t-badge_bottom\\\"\\n    [style.--t-badge-height.px]=\\\"badgeBottom.offsetHeight\\\"\\n    (waResizeObserver)=\\\"onResize()\\\"\\n>\\n    <ng-content select=\\\"tui-badge-notification[tuiSlot='bottom']\\\" />\\n    <div class=\\\"t-border\\\">\\n        <ng-content select=\\\"[tuiSlot='bottom']\\\" />\\n    </div>\\n</div>\\n\", styles: [\":host{--tui-radius: var(--tui-radius-l);--t-badge-height: 0;--t-corner-offset: calc((var(--tui-radius) * 1.4142 - var(--tui-radius)) * 1 / 1.4142);position:relative;display:inline-block;block-size:-webkit-fit-content;block-size:-moz-fit-content;block-size:fit-content;inline-size:-webkit-max-content;inline-size:max-content;color:var(--tui-background-base)}.t-badge{--t-offset: calc(var(--t-badge-height) * -.5 + var(--t-corner-offset));position:absolute;display:flex}.t-badge_top{top:var(--t-offset);right:var(--t-offset)}.t-badge_bottom{bottom:var(--t-offset);right:var(--t-offset)}.t-border:not(:empty){display:flex;border-radius:6rem;box-shadow:0 0 0 1px currentColor;background:currentColor}\\n\"] }]\n        }] });\n\nconst TuiBadgedContent = [\n    TuiBadgedContentDirective,\n    TuiBadgedContentComponent,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiBadgedContent, TuiBadgedContentComponent, TuiBadgedContentDirective };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,uBAAuB,QAAQ,eAAe;AAC/G,SAASC,gBAAgB,QAAQ,8BAA8B;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAEhE,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,kCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFL,yBAAyB;IAAA,CAAmD;EAAE;EACzL;IAAS,IAAI,CAACM,IAAI,kBAD+EhB,EAAE,CAAAiB,iBAAA;MAAAC,IAAA,EACJR,yBAAyB;MAAAS,SAAA;MAAAC,MAAA;QAAAR,OAAA;MAAA;MAAAS,UAAA;IAAA,EAA4F;EAAE;AAC1N;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGtB,EAAE,CAAAuB,iBAAA,CAGXb,yBAAyB,EAAc,CAAC;IACxHQ,IAAI,EAAEjB,SAAS;IACfuB,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEb,OAAO,EAAE,CAAC;MACxBM,IAAI,EAAEhB;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMwB,yBAAyB,CAAC;EAC5Bf,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgB,GAAG,GAAGxB,MAAM,CAACC,iBAAiB,CAAC;EACxC;EACAwB,QAAQA,CAAA,EAAG;IACP,IAAI,CAACD,GAAG,CAACE,aAAa,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAAChB,IAAI,YAAAiB,kCAAAf,CAAA;MAAA,YAAAA,CAAA,IAAyFW,yBAAyB;IAAA,CAAmD;EAAE;EACzL;IAAS,IAAI,CAACK,IAAI,kBArB+E/B,EAAE,CAAAgC,iBAAA;MAAAd,IAAA,EAqBJQ,yBAAyB;MAAAP,SAAA;MAAAE,UAAA;MAAAY,QAAA,GArBvBjC,EAAE,CAAAkC,mBAAA;MAAAC,kBAAA,EAAA1B,GAAA;MAAA2B,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAE,GAAA,GAAF3C,EAAE,CAAA4C,gBAAA;UAAF5C,EAAE,CAAA6C,eAAA,CAAArC,GAAA;UAAFR,EAAE,CAAA8C,YAAA,EAqBiH,CAAC;UArBpH9C,EAAE,CAAA+C,cAAA,eAqBgR,CAAC;UArBnR/C,EAAE,CAAAgD,UAAA,8BAAAC,mEAAA;YAAFjD,EAAE,CAAAkD,aAAA,CAAAP,GAAA;YAAA,OAAF3C,EAAE,CAAAmD,WAAA,CAqBkQT,GAAA,CAAAd,QAAA,CAAS,CAAC;UAAA,CAAC,CAAC;UArBhR5B,EAAE,CAAA8C,YAAA,KAqBqV,CAAC;UArBxV9C,EAAE,CAAA+C,cAAA,YAqBmX,CAAC;UArBtX/C,EAAE,CAAA8C,YAAA,KAqBsa,CAAC;UArBza9C,EAAE,CAAAoD,YAAA,CAqBkb,CAAC,CAAO,CAAC;UArB7bpD,EAAE,CAAA+C,cAAA,eAqBkmB,CAAC;UArBrmB/C,EAAE,CAAAgD,UAAA,8BAAAK,mEAAA;YAAFrD,EAAE,CAAAkD,aAAA,CAAAP,GAAA;YAAA,OAAF3C,EAAE,CAAAmD,WAAA,CAqBolBT,GAAA,CAAAd,QAAA,CAAS,CAAC;UAAA,CAAC,CAAC;UArBlmB5B,EAAE,CAAA8C,YAAA,KAqB0qB,CAAC;UArB7qB9C,EAAE,CAAA+C,cAAA,YAqBwsB,CAAC;UArB3sB/C,EAAE,CAAA8C,YAAA,MAqB8vB,CAAC;UArBjwB9C,EAAE,CAAAoD,YAAA,CAqB0wB,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAX,EAAA;UAAA,MAAAa,WAAA,GArBrxBtD,EAAE,CAAAuD,WAAA;UAAA,MAAAC,cAAA,GAAFxD,EAAE,CAAAuD,WAAA;UAAFvD,EAAE,CAAAyD,SAAA,CAqBsO,CAAC;UArBzOzD,EAAE,CAAA0D,WAAA,qBAAAJ,WAAA,CAAAK,YAAA,MAqBsO,CAAC;UArBzO3D,EAAE,CAAAyD,SAAA,EAqBwjB,CAAC;UArB3jBzD,EAAE,CAAA0D,WAAA,qBAAAF,cAAA,CAAAG,YAAA,MAqBwjB,CAAC;QAAA;MAAA;MAAAC,YAAA,GAAi9BrD,gBAAgB;MAAAsD,MAAA;MAAAC,eAAA;IAAA,EAA0I;EAAE;AAC7wD;AACA;EAAA,QAAAxC,SAAA,oBAAAA,SAAA,KAvBqGtB,EAAE,CAAAuB,iBAAA,CAuBXG,yBAAyB,EAAc,CAAC;IACxHR,IAAI,EAAEb,SAAS;IACfmB,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,IAAI;MAAEI,QAAQ,EAAE,oBAAoB;MAAEsC,OAAO,EAAE,CAACrD,yBAAyB,EAAEH,gBAAgB,CAAC;MAAEuD,eAAe,EAAExD,uBAAuB,CAAC0D,MAAM;MAAEzB,QAAQ,EAAE,mrBAAmrB;MAAEsB,MAAM,EAAE,CAAC,4rBAA4rB;IAAE,CAAC;EAC/iD,CAAC,CAAC;AAAA;AAEV,MAAMI,gBAAgB,GAAG,CACrBvD,yBAAyB,EACzBgB,yBAAyB,CAC5B;;AAED;AACA;AACA;;AAEA,SAASuC,gBAAgB,EAAEvC,yBAAyB,EAAEhB,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}