{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, Input } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiZonefull, tuiWatch } from '@taiga-ui/cdk/observables';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { map } from 'rxjs';\nconst rowsInSvg = 3;\nclass TuiSensitiveStyles {\n  static {\n    this.ɵfac = function TuiSensitiveStyles_Factory(t) {\n      return new (t || TuiSensitiveStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiSensitiveStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-sensitive-styles\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiSensitiveStyles_Template(rf, ctx) {},\n      styles: [\".tui-sensitive{position:relative;line-height:1em;clip-path:inset(0 round .25rem)}.tui-sensitive:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";display:block!important;background:currentColor;border-radius:inherit;-webkit-mask:url('data:image/svg+xml,<svg width=\\\"360\\\" height=\\\"72\\\" preserveAspectRatio=\\\"none\\\" fill=\\\"black\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><rect opacity=\\\"0.2\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"336\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"120\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"216\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"312\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"144\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"192\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"288\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"240\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.24\\\" x=\\\"72\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.34\\\" x=\\\"264\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"168\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"336\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"120\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\" /><rect opacity=\\\"0.3\\\" x=\\\"216\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"24\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"312\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"144\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"192\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"48\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"288\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.38\\\" x=\\\"96\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"240\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"72\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"264\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" x=\\\"168\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"240\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"72\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"192\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.34\\\" x=\\\"216\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"24\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"168\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"264\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.31\\\" x=\\\"288\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"312\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.28\\\" x=\\\"144\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"336\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"120\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/></svg>') var(--t-offset, 0) 0 / auto var(--t-mask-height);mask:url('data:image/svg+xml,<svg width=\\\"360\\\" height=\\\"72\\\" preserveAspectRatio=\\\"none\\\" fill=\\\"black\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><rect opacity=\\\"0.2\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"336\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"120\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"216\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"312\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"144\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"192\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"288\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"240\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.24\\\" x=\\\"72\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.34\\\" x=\\\"264\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"168\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"336\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"120\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\" /><rect opacity=\\\"0.3\\\" x=\\\"216\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"24\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"312\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"144\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"192\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"48\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"288\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.38\\\" x=\\\"96\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"240\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"72\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"264\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" x=\\\"168\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"240\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"72\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"192\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.34\\\" x=\\\"216\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"24\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"168\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"264\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.31\\\" x=\\\"288\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"312\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.28\\\" x=\\\"144\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"336\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"120\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/></svg>') var(--t-offset, 0) 0 / auto var(--t-mask-height);margin:0!important}.tui-sensitive.tui-sensitive{-webkit-text-fill-color:transparent}span.tui-sensitive,a.tui-sensitive{display:inline-block}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSensitiveStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-sensitive-styles'\n      },\n      styles: [\".tui-sensitive{position:relative;line-height:1em;clip-path:inset(0 round .25rem)}.tui-sensitive:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";display:block!important;background:currentColor;border-radius:inherit;-webkit-mask:url('data:image/svg+xml,<svg width=\\\"360\\\" height=\\\"72\\\" preserveAspectRatio=\\\"none\\\" fill=\\\"black\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><rect opacity=\\\"0.2\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"336\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"120\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"216\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"312\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"144\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"192\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"288\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"240\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.24\\\" x=\\\"72\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.34\\\" x=\\\"264\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"168\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"336\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"120\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\" /><rect opacity=\\\"0.3\\\" x=\\\"216\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"24\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"312\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"144\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"192\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"48\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"288\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.38\\\" x=\\\"96\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"240\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"72\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"264\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" x=\\\"168\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"240\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"72\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"192\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.34\\\" x=\\\"216\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"24\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"168\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"264\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.31\\\" x=\\\"288\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"312\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.28\\\" x=\\\"144\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"336\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"120\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/></svg>') var(--t-offset, 0) 0 / auto var(--t-mask-height);mask:url('data:image/svg+xml,<svg width=\\\"360\\\" height=\\\"72\\\" preserveAspectRatio=\\\"none\\\" fill=\\\"black\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><rect opacity=\\\"0.2\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"336\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"120\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"216\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"312\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"144\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"192\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"288\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"240\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.24\\\" x=\\\"72\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.34\\\" x=\\\"264\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"168\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"336\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"120\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\" /><rect opacity=\\\"0.3\\\" x=\\\"216\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"24\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"312\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"144\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"192\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"48\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"288\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.38\\\" x=\\\"96\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"240\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"72\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"264\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" x=\\\"168\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"240\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"72\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"192\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.34\\\" x=\\\"216\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"24\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"168\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"264\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.31\\\" x=\\\"288\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"312\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.28\\\" x=\\\"144\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"336\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"120\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/></svg>') var(--t-offset, 0) 0 / auto var(--t-mask-height);margin:0!important}.tui-sensitive.tui-sensitive{-webkit-text-fill-color:transparent}span.tui-sensitive,a.tui-sensitive{display:inline-block}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiSensitive {\n  constructor() {\n    this.nothing = tuiWithStyles(TuiSensitiveStyles);\n    this.offset = Math.round(Math.random() * 10) * 10;\n    this.height = toSignal(inject(ResizeObserverService, {\n      self: true\n    }).pipe(map(entry => {\n      const height = entry[0]?.contentRect.height ?? 0;\n      return [Math.max(2, Math.floor(height / 16) + 1), height];\n    }), map(([rows, height]) => height * (rowsInSvg / rows)), tuiZonefull(), tuiWatch()));\n    this.tuiSensitive = false;\n  }\n  static {\n    this.ɵfac = function TuiSensitive_Factory(t) {\n      return new (t || TuiSensitive)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiSensitive,\n      selectors: [[\"\", \"tuiSensitive\", \"\"]],\n      hostVars: 6,\n      hostBindings: function TuiSensitive_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-offset\", ctx.offset, \"px\")(\"--t-mask-height\", ctx.height(), \"px\");\n          i0.ɵɵclassProp(\"tui-sensitive\", ctx.tuiSensitive);\n        }\n      },\n      inputs: {\n        tuiSensitive: \"tuiSensitive\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([ResizeObserverService])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSensitive, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiSensitive]',\n      providers: [ResizeObserverService],\n      host: {\n        '[style.--t-offset.px]': 'offset',\n        '[style.--t-mask-height.px]': 'height()',\n        '[class.tui-sensitive]': 'tuiSensitive'\n      }\n    }]\n  }], null, {\n    tuiSensitive: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiSensitive };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "Directive", "Input", "toSignal", "ResizeObserverService", "tui<PERSON>onefull", "tuiWatch", "tuiWithStyles", "map", "rowsInSvg", "TuiSensitiveStyles", "ɵfac", "TuiSensitiveStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiSensitiveStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiSensitive", "constructor", "nothing", "offset", "Math", "round", "random", "height", "self", "pipe", "entry", "contentRect", "max", "floor", "rows", "tuiSensitive", "TuiSensitive_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiSensitive_HostBindings", "ɵɵstyleProp", "ɵɵclassProp", "inputs", "ɵɵProvidersFeature", "selector", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-sensitive.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, Input } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiZonefull, tuiWatch } from '@taiga-ui/cdk/observables';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { map } from 'rxjs';\n\nconst rowsInSvg = 3;\nclass TuiSensitiveStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSensitiveStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSensitiveStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-sensitive-styles\" }, ngImport: i0, template: '', isInline: true, styles: [\".tui-sensitive{position:relative;line-height:1em;clip-path:inset(0 round .25rem)}.tui-sensitive:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";display:block!important;background:currentColor;border-radius:inherit;-webkit-mask:url('data:image/svg+xml,<svg width=\\\"360\\\" height=\\\"72\\\" preserveAspectRatio=\\\"none\\\" fill=\\\"black\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><rect opacity=\\\"0.2\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"336\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"120\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"216\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"312\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"144\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"192\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"288\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"240\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.24\\\" x=\\\"72\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.34\\\" x=\\\"264\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"168\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"336\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"120\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\" /><rect opacity=\\\"0.3\\\" x=\\\"216\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"24\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"312\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"144\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"192\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"48\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"288\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.38\\\" x=\\\"96\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"240\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"72\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"264\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" x=\\\"168\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"240\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"72\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"192\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.34\\\" x=\\\"216\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"24\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"168\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"264\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.31\\\" x=\\\"288\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"312\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.28\\\" x=\\\"144\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"336\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"120\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/></svg>') var(--t-offset, 0) 0 / auto var(--t-mask-height);mask:url('data:image/svg+xml,<svg width=\\\"360\\\" height=\\\"72\\\" preserveAspectRatio=\\\"none\\\" fill=\\\"black\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><rect opacity=\\\"0.2\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"336\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"120\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"216\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"312\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"144\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"192\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"288\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"240\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.24\\\" x=\\\"72\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.34\\\" x=\\\"264\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"168\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"336\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"120\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\" /><rect opacity=\\\"0.3\\\" x=\\\"216\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"24\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"312\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"144\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"192\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"48\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"288\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.38\\\" x=\\\"96\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"240\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"72\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"264\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" x=\\\"168\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"240\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"72\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"192\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.34\\\" x=\\\"216\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"24\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"168\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"264\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.31\\\" x=\\\"288\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"312\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.28\\\" x=\\\"144\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"336\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"120\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/></svg>') var(--t-offset, 0) 0 / auto var(--t-mask-height);margin:0!important}.tui-sensitive.tui-sensitive{-webkit-text-fill-color:transparent}span.tui-sensitive,a.tui-sensitive{display:inline-block}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSensitiveStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-sensitive-styles',\n                    }, styles: [\".tui-sensitive{position:relative;line-height:1em;clip-path:inset(0 round .25rem)}.tui-sensitive:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";display:block!important;background:currentColor;border-radius:inherit;-webkit-mask:url('data:image/svg+xml,<svg width=\\\"360\\\" height=\\\"72\\\" preserveAspectRatio=\\\"none\\\" fill=\\\"black\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><rect opacity=\\\"0.2\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"336\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"120\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"216\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"312\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"144\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"192\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"288\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"240\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.24\\\" x=\\\"72\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.34\\\" x=\\\"264\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"168\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"336\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"120\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\" /><rect opacity=\\\"0.3\\\" x=\\\"216\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"24\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"312\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"144\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"192\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"48\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"288\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.38\\\" x=\\\"96\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"240\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"72\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"264\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" x=\\\"168\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"240\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"72\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"192\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.34\\\" x=\\\"216\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"24\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"168\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"264\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.31\\\" x=\\\"288\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"312\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.28\\\" x=\\\"144\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"336\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"120\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/></svg>') var(--t-offset, 0) 0 / auto var(--t-mask-height);mask:url('data:image/svg+xml,<svg width=\\\"360\\\" height=\\\"72\\\" preserveAspectRatio=\\\"none\\\" fill=\\\"black\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><rect opacity=\\\"0.2\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"336\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"120\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"216\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"312\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"144\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"192\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"288\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"240\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.24\\\" x=\\\"72\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.34\\\" x=\\\"264\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"168\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"336\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"120\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\" /><rect opacity=\\\"0.3\\\" x=\\\"216\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"24\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"312\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"144\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"192\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"48\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"288\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.38\\\" x=\\\"96\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"240\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"72\\\" y=\\\"24\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.2\\\" x=\\\"264\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" x=\\\"168\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.35\\\" x=\\\"240\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.4\\\" x=\\\"72\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"192\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"48\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.34\\\" x=\\\"216\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"24\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"168\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.32\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"264\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/> <rect opacity=\\\"0.31\\\" x=\\\"288\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.3\\\" x=\\\"96\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.25\\\" x=\\\"312\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.28\\\" x=\\\"144\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.36\\\" x=\\\"336\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/><rect opacity=\\\"0.21\\\" x=\\\"120\\\" y=\\\"48\\\" width=\\\"24\\\" height=\\\"24\\\"/></svg>') var(--t-offset, 0) 0 / auto var(--t-mask-height);margin:0!important}.tui-sensitive.tui-sensitive{-webkit-text-fill-color:transparent}span.tui-sensitive,a.tui-sensitive{display:inline-block}\\n\"] }]\n        }] });\nclass TuiSensitive {\n    constructor() {\n        this.nothing = tuiWithStyles(TuiSensitiveStyles);\n        this.offset = Math.round(Math.random() * 10) * 10;\n        this.height = toSignal(inject(ResizeObserverService, { self: true }).pipe(map((entry) => {\n            const height = entry[0]?.contentRect.height ?? 0;\n            return [Math.max(2, Math.floor(height / 16) + 1), height];\n        }), map(([rows, height]) => height * (rowsInSvg / rows)), tuiZonefull(), tuiWatch()));\n        this.tuiSensitive = false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSensitive, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSensitive, isStandalone: true, selector: \"[tuiSensitive]\", inputs: { tuiSensitive: \"tuiSensitive\" }, host: { properties: { \"style.--t-offset.px\": \"offset\", \"style.--t-mask-height.px\": \"height()\", \"class.tui-sensitive\": \"tuiSensitive\" } }, providers: [ResizeObserverService], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSensitive, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiSensitive]',\n                    providers: [ResizeObserverService],\n                    host: {\n                        '[style.--t-offset.px]': 'offset',\n                        '[style.--t-mask-height.px]': 'height()',\n                        '[class.tui-sensitive]': 'tuiSensitive',\n                    },\n                }]\n        }], propDecorators: { tuiSensitive: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiSensitive };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAC/G,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,WAAW,EAAEC,QAAQ,QAAQ,2BAA2B;AACjE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,GAAG,QAAQ,MAAM;AAE1B,MAAMC,SAAS,GAAG,CAAC;AACnB,MAAMC,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACC,IAAI,YAAAC,2BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,kBAAkB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAACI,IAAI,kBAD+ElB,EAAE,CAAAmB,iBAAA;MAAAC,IAAA,EACJN,kBAAkB;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADhBxB,EAAE,CAAAyB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACo9N;EAAE;AAC7jO;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGnC,EAAE,CAAAoC,iBAAA,CAGXtB,kBAAkB,EAAc,CAAC;IACjHM,IAAI,EAAEnB,SAAS;IACfoC,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAE/B,iBAAiB,CAACoC,IAAI;MAAEJ,eAAe,EAAE/B,uBAAuB,CAACoC,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,6sNAA6sN;IAAE,CAAC;EACxuN,CAAC,CAAC;AAAA;AACV,MAAMU,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGjC,aAAa,CAACG,kBAAkB,CAAC;IAChD,IAAI,CAAC+B,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;IACjD,IAAI,CAACC,MAAM,GAAG1C,QAAQ,CAACH,MAAM,CAACI,qBAAqB,EAAE;MAAE0C,IAAI,EAAE;IAAK,CAAC,CAAC,CAACC,IAAI,CAACvC,GAAG,CAAEwC,KAAK,IAAK;MACrF,MAAMH,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC,EAAEC,WAAW,CAACJ,MAAM,IAAI,CAAC;MAChD,OAAO,CAACH,IAAI,CAACQ,GAAG,CAAC,CAAC,EAAER,IAAI,CAACS,KAAK,CAACN,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAEA,MAAM,CAAC;IAC7D,CAAC,CAAC,EAAErC,GAAG,CAAC,CAAC,CAAC4C,IAAI,EAAEP,MAAM,CAAC,KAAKA,MAAM,IAAIpC,SAAS,GAAG2C,IAAI,CAAC,CAAC,EAAE/C,WAAW,CAAC,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrF,IAAI,CAAC+C,YAAY,GAAG,KAAK;EAC7B;EACA;IAAS,IAAI,CAAC1C,IAAI,YAAA2C,qBAAAzC,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACiB,IAAI,kBApB+E3D,EAAE,CAAA4D,iBAAA;MAAAxC,IAAA,EAoBJsB,YAAY;MAAArB,SAAA;MAAAwC,QAAA;MAAAC,YAAA,WAAAC,0BAAAjC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApBV9B,EAAE,CAAAgE,WAAA,eAAAjC,GAAA,CAAAc,MAAA,MAoBO,CAAC,oBAAZd,GAAA,CAAAkB,MAAA,CAAO,CAAC,MAAG,CAAC;UApBVjD,EAAE,CAAAiE,WAAA,kBAAAlC,GAAA,CAAA0B,YAoBO,CAAC;QAAA;MAAA;MAAAS,MAAA;QAAAT,YAAA;MAAA;MAAAlC,UAAA;MAAAC,QAAA,GApBVxB,EAAE,CAAAmE,kBAAA,CAoByP,CAAC3D,qBAAqB,CAAC;IAAA,EAAiB;EAAE;AAC1Y;AACA;EAAA,QAAA2B,SAAA,oBAAAA,SAAA,KAtBqGnC,EAAE,CAAAoC,iBAAA,CAsBXM,YAAY,EAAc,CAAC;IAC3GtB,IAAI,EAAEf,SAAS;IACfgC,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChB6C,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,CAAC7D,qBAAqB,CAAC;MAClCgC,IAAI,EAAE;QACF,uBAAuB,EAAE,QAAQ;QACjC,4BAA4B,EAAE,UAAU;QACxC,uBAAuB,EAAE;MAC7B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEiB,YAAY,EAAE,CAAC;MAC7BrC,IAAI,EAAEd;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASoC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}