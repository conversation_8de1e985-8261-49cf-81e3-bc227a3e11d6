{"ast": null, "code": "import { Tui<PERSON>ay<PERSON><PERSON><PERSON>, TUI_FIRST_DAY, TUI_LAST_DAY, TuiDay, TuiMonth } from '@taiga-ui/cdk/date-time';\nimport { __decorate } from 'tslib';\nimport { AsyncPipe, NgForOf, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ChangeDetectorRef, EventEmitter, Component, ChangeDetectionStrategy, Input, Output } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { tuiWatch } from '@taiga-ui/cdk/observables';\nimport { TuiMapperPipe } from '@taiga-ui/cdk/pipes/mapper';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiIsString, tuiNullableSame, tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiCalendarSheetOptionsProvider, TuiCalendar } from '@taiga-ui/core/components/calendar';\nimport * as i1 from '@taiga-ui/core/components/data-list';\nimport { TuiDataList } from '@taiga-ui/core/components/data-list';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TUI_COMMON_ICONS, tuiAsAuxiliary } from '@taiga-ui/core/tokens';\nimport { TUI_OTHER_DATE_TEXT, TUI_CALENDAR_DATE_STREAM } from '@taiga-ui/kit/tokens';\nconst _c0 = (a0, a1, a2, a3) => [a0, a1, a2, a3, false];\nconst _c1 = (a0, a1, a2, a3) => [a0, a1, a2, a3, true];\nconst _c2 = (a0, a1, a2, a3, a4, a5) => [a0, a1, a2, a3, a4, a5];\nfunction TuiCalendarRange_tui_calendar_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tui-calendar\", 3);\n    i0.ɵɵpipe(1, \"tuiMapper\");\n    i0.ɵɵpipe(2, \"tuiMapper\");\n    i0.ɵɵpipe(3, \"tuiMapper\");\n    i0.ɵɵpipe(4, \"tuiMapper\");\n    i0.ɵɵtwoWayListener(\"hoveredItemChange\", function TuiCalendarRange_tui_calendar_4_Template_tui_calendar_hoveredItemChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.hoveredItem, $event) || (ctx_r2.hoveredItem = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"dayClick\", function TuiCalendarRange_tui_calendar_4_Template_tui_calendar_dayClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDayClick($event));\n    })(\"monthChange\", function TuiCalendarRange_tui_calendar_4_Template_tui_calendar_monthChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onMonthChange($event.append({\n        month: -1\n      })));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabledItemHandler\", ctx_r2.calculatedDisabledItemHandler)(\"markerHandler\", ctx_r2.markerHandler)(\"max\", i0.ɵɵpipeBindV(1, 9, i0.ɵɵpureFunction4(29, _c0, ctx_r2.max, ctx_r2.capsMapper, ctx_r2.currentValue, ctx_r2.maxLength)))(\"min\", i0.ɵɵpipeBindV(2, 15, i0.ɵɵpureFunction4(34, _c1, ctx_r2.min, ctx_r2.capsMapper, ctx_r2.currentValue, ctx_r2.maxLength)))(\"minViewedMonth\", i0.ɵɵpipeBind3(3, 21, ctx_r2.defaultViewedMonth, ctx_r2.monthOffset, 1))(\"month\", i0.ɵɵpipeBind3(4, 25, ctx_r2.defaultViewedMonth, ctx_r2.monthOffset, 1))(\"showAdjacent\", false)(\"value\", ctx_r2.currentValue);\n    i0.ɵɵtwoWayProperty(\"hoveredItem\", ctx_r2.hoveredItem);\n  }\n}\nfunction TuiCalendarRange_ng_template_5_tui_data_list_0_button_1_tui_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-icon\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"font-size\", 1, \"rem\");\n    i0.ɵɵproperty(\"icon\", ctx_r2.icons.check);\n  }\n}\nfunction TuiCalendarRange_ng_template_5_tui_data_list_0_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function TuiCalendarRange_ng_template_5_tui_data_list_0_button_1_Template_button_click_0_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onItemSelect(item_r5));\n    })(\"pointerdown.prevent.zoneless\", function TuiCalendarRange_ng_template_5_tui_data_list_0_button_1_Template_button_pointerdown_prevent_zoneless_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      return i0.ɵɵresetView(0);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, TuiCalendarRange_ng_template_5_tui_data_list_0_button_1_tui_icon_2_Template, 1, 3, \"tui-icon\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"aria-checked\", ctx_r2.isItemActive(item_r5));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r5, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemActive(item_r5));\n  }\n}\nfunction TuiCalendarRange_ng_template_5_tui_data_list_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tui-data-list\", 5);\n    i0.ɵɵtemplate(1, TuiCalendarRange_ng_template_5_tui_data_list_0_button_1_Template, 3, 3, \"button\", 6);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵpipe(3, \"tuiMapper\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"flex\", 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBindV(3, 5, i0.ɵɵpureFunction6(12, _c2, ctx_r2.items, ctx_r2.mapper, ctx_r2.min, ctx_r2.max, ctx_r2.minLength, i0.ɵɵpipeBind1(2, 3, ctx_r2.otherDateText$))));\n  }\n}\nfunction TuiCalendarRange_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiCalendarRange_ng_template_5_tui_data_list_0_Template, 4, 19, \"tui-data-list\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.mobile);\n  }\n}\nconst calculateDisabledItemHandler = (disabledItemHandler, value, minLength) => item => {\n  if (!value || value instanceof TuiDayRange || !minLength) {\n    return disabledItemHandler(item);\n  }\n  const negativeMinLength = Object.fromEntries(Object.entries(minLength).map(([key, value]) => [key, -value]));\n  const disabledBefore = value.append(negativeMinLength).append({\n    day: 1\n  });\n  const disabledAfter = value.append(minLength).append({\n    day: -1\n  });\n  const inDisabledRange = disabledBefore.dayBefore(item) && disabledAfter.dayAfter(item);\n  return inDisabledRange || disabledItemHandler(item);\n};\nconst TUI_DAY_CAPS_MAPPER = (current, value, maxLength, backwards) => {\n  if (\n  // TODO(v5): replace with `if (!(value instanceof TuiDay) || !maxLength)` (backward compatibility)\n  value instanceof TuiDayRange && !value.isSingleDay || !value || !maxLength) {\n    return backwards ? current || TUI_FIRST_DAY : current || TUI_LAST_DAY;\n  }\n  const negativeMaxLength = Object.fromEntries(Object.entries(maxLength).map(([key, value]) => [key, -value]));\n  // TODO(v5): `value instanceof TuiDay` always `true`\n  const from = value instanceof TuiDay ? value : value.from;\n  const dateShift = from.append(backwards ? negativeMaxLength : maxLength).append({\n    day: !backwards ? -1 : 1\n  });\n  if (backwards) {\n    return dateShift.dayBefore(current || TUI_FIRST_DAY) ? current || TUI_FIRST_DAY : dateShift;\n  }\n  if (!current) {\n    return dateShift;\n  }\n  return dateShift.dayAfter(current) ? current : dateShift;\n};\nclass TuiCalendarRange {\n  constructor() {\n    /**\n     * @deprecated use `item`\n     */\n    this.selectedPeriod = null;\n    this.cdr = inject(ChangeDetectorRef);\n    this.currentValue = null;\n    this.previousValue = null;\n    this.hoveredItem = null;\n    this.month = TuiMonth.currentLocal();\n    this.otherDateText$ = inject(TUI_OTHER_DATE_TEXT);\n    this.icons = inject(TUI_COMMON_ICONS);\n    this.capsMapper = TUI_DAY_CAPS_MAPPER;\n    this.mobile = inject(TUI_IS_MOBILE);\n    this.disabledItemHandler = TUI_FALSE_HANDLER;\n    this.markerHandler = null;\n    this.items = [];\n    this.min = TUI_FIRST_DAY;\n    this.max = TUI_LAST_DAY;\n    this.minLength = null;\n    this.maxLength = null;\n    this.item = null;\n    this.valueChange = new EventEmitter();\n    this.itemChange = new EventEmitter();\n    this.monthOffset = (value, month) => value.append({\n      month\n    });\n    this.mapper = (items, min, max, minLength, otherDateText) => [...items.filter(item => (minLength === null || item.range.from.append(minLength).append({\n      day: -1\n    }).daySameOrBefore(item.range.to)) && (min === null || item.range.to.daySameOrAfter(min)) && (max === null || item.range.from.daySameOrBefore(max))), otherDateText || ''];\n    inject(TUI_CALENDAR_DATE_STREAM, {\n      optional: true\n    })?.pipe(tuiWatch(), takeUntilDestroyed()).subscribe(value => {\n      this.currentValue = value;\n      this.initDefaultViewedMonth();\n    });\n  }\n  set valueSetter(value) {\n    this.currentValue = value;\n  }\n  set defaultViewedMonth(month) {\n    if (!this.currentValue) {\n      this.month = month;\n    }\n  }\n  set value(value) {\n    this.cdr.markForCheck();\n    this.currentValue = value;\n    this.initDefaultViewedMonth();\n  }\n  get defaultViewedMonth() {\n    return this.month;\n  }\n  /**\n   * @deprecated use `item`\n   */\n  get selectedActivePeriod() {\n    return this.selectedPeriod;\n  }\n  /**\n   * @deprecated use `item`\n   */\n  set selectedActivePeriod(period) {\n    this.selectedPeriod = period;\n  }\n  ngOnChanges() {\n    if (!this.currentValue) {\n      this.initDefaultViewedMonth();\n    }\n  }\n  ngOnInit() {\n    this.initDefaultViewedMonth();\n  }\n  get calculatedDisabledItemHandler() {\n    return this.calculateDisabledItemHandler(this.disabledItemHandler, this.currentValue, this.minLength);\n  }\n  onEsc(event) {\n    if (event.key !== 'Escape' || !(this.currentValue instanceof TuiDay)) {\n      return;\n    }\n    event.stopPropagation();\n    this.currentValue = this.previousValue;\n  }\n  isItemActive(item) {\n    const {\n      activePeriod\n    } = this;\n    return tuiIsString(item) && activePeriod === null || activePeriod === item || activePeriod?.toString() === item.toString();\n  }\n  onItemSelect(item) {\n    if (!tuiIsString(item)) {\n      this.selectedActivePeriod = item;\n      this.itemChange.emit(item);\n      this.updateValue(item.range.dayLimit(this.min, this.max));\n    } else if (this.activePeriod !== null) {\n      this.selectedActivePeriod = null;\n      this.itemChange.emit(null);\n      this.updateValue(null);\n    }\n    this.initDefaultViewedMonth();\n  }\n  onMonthChange(month) {\n    this.month = month;\n  }\n  onDayClick(day) {\n    this.previousValue = this.currentValue;\n    this.selectedActivePeriod = null;\n    if (this.currentValue instanceof TuiDay) {\n      const range = TuiDayRange.sort(this.currentValue, day);\n      this.currentValue = range;\n      this.itemChange.emit(this.findItemByDayRange(range));\n      this.updateValue(range);\n    } else {\n      this.currentValue = day;\n    }\n  }\n  updateValue(value) {\n    this.currentValue = value;\n    this.valueChange.emit(value);\n  }\n  get activePeriod() {\n    return this.item ?? this.selectedActivePeriod ?? (this.items.find(item => tuiNullableSame(this.currentValue instanceof TuiDay ? new TuiDayRange(this.currentValue, this.currentValue) : this.currentValue, item.range, (a, b) => a.from.daySame(b.from.dayLimit(this.min, this.max)) && a.to.daySame(b.to.dayLimit(this.min, this.max)))) || null);\n  }\n  calculateDisabledItemHandler(disabledItemHandler, value, minLength) {\n    return calculateDisabledItemHandler(disabledItemHandler, value, minLength);\n  }\n  initDefaultViewedMonth() {\n    if (this.currentValue instanceof TuiDay) {\n      this.month = this.currentValue;\n    } else if (this.currentValue) {\n      this.month = this.items.length ? this.currentValue.to : this.currentValue.from;\n    } else if (this.max && this.month.monthSameOrAfter(this.max)) {\n      this.month = this.items.length ? this.max : this.max.append({\n        month: -1\n      });\n    } else if (this.min && this.month.monthSameOrBefore(this.min)) {\n      this.month = this.min;\n    }\n  }\n  findItemByDayRange(dayRange) {\n    return this.items.find(item => dayRange.daySame(item.range)) ?? null;\n  }\n  static {\n    this.ɵfac = function TuiCalendarRange_Factory(t) {\n      return new (t || TuiCalendarRange)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiCalendarRange,\n      selectors: [[\"tui-calendar-range\"]],\n      hostVars: 2,\n      hostBindings: function TuiCalendarRange_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.capture\", function TuiCalendarRange_keydown_capture_HostBindingHandler($event) {\n            return ctx.onEsc($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_mobile\", ctx.mobile);\n        }\n      },\n      inputs: {\n        disabledItemHandler: \"disabledItemHandler\",\n        markerHandler: \"markerHandler\",\n        items: \"items\",\n        min: \"min\",\n        max: \"max\",\n        minLength: \"minLength\",\n        maxLength: \"maxLength\",\n        item: \"item\",\n        valueSetter: [i0.ɵɵInputFlags.None, \"value\", \"valueSetter\"],\n        defaultViewedMonth: \"defaultViewedMonth\"\n      },\n      outputs: {\n        valueChange: \"valueChange\",\n        itemChange: \"itemChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsAuxiliary(TuiCalendarRange), tuiCalendarSheetOptionsProvider({\n        rangeMode: true\n      })]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 39,\n      consts: [[\"presets\", \"\"], [\"automation-id\", \"tui-calendar-range__calendar\", 3, \"hoveredItemChange\", \"dayClick\", \"monthChange\", \"disabledItemHandler\", \"markerHandler\", \"max\", \"maxViewedMonth\", \"min\", \"month\", \"showAdjacent\", \"value\", \"hoveredItem\"], [3, \"disabledItemHandler\", \"markerHandler\", \"max\", \"min\", \"minViewedMonth\", \"month\", \"showAdjacent\", \"value\", \"hoveredItem\", \"hoveredItemChange\", \"dayClick\", \"monthChange\", 4, \"ngIf\", \"ngIfElse\"], [3, \"hoveredItemChange\", \"dayClick\", \"monthChange\", \"disabledItemHandler\", \"markerHandler\", \"max\", \"min\", \"minViewedMonth\", \"month\", \"showAdjacent\", \"value\", \"hoveredItem\"], [\"automation-id\", \"tui-calendar-range__menu\", \"role\", \"menu\", 3, \"flex\", 4, \"ngIf\"], [\"automation-id\", \"tui-calendar-range__menu\", \"role\", \"menu\"], [\"automation-id\", \"tui-calendar-range__menu__item\", \"role\", \"menuitemradio\", \"tuiOption\", \"\", \"type\", \"button\", 3, \"click\", \"pointerdown.prevent.zoneless\", 4, \"ngFor\", \"ngForOf\"], [\"automation-id\", \"tui-calendar-range__menu__item\", \"role\", \"menuitemradio\", \"tuiOption\", \"\", \"type\", \"button\", 3, \"click\", \"pointerdown.prevent.zoneless\"], [\"automation-id\", \"tui-calendar-range__checkmark\", 3, \"icon\", \"font-size\", 4, \"ngIf\"], [\"automation-id\", \"tui-calendar-range__checkmark\", 3, \"icon\"]],\n      template: function TuiCalendarRange_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"tui-calendar\", 1);\n          i0.ɵɵpipe(1, \"tuiMapper\");\n          i0.ɵɵpipe(2, \"tuiMapper\");\n          i0.ɵɵpipe(3, \"tuiMapper\");\n          i0.ɵɵtwoWayListener(\"hoveredItemChange\", function TuiCalendarRange_Template_tui_calendar_hoveredItemChange_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.hoveredItem, $event) || (ctx.hoveredItem = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"dayClick\", function TuiCalendarRange_Template_tui_calendar_dayClick_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onDayClick($event));\n          })(\"monthChange\", function TuiCalendarRange_Template_tui_calendar_monthChange_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMonthChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, TuiCalendarRange_tui_calendar_4_Template, 5, 39, \"tui-calendar\", 2)(5, TuiCalendarRange_ng_template_5_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const presets_r6 = i0.ɵɵreference(6);\n          i0.ɵɵclassProp(\"t-calendar\", !ctx.mobile);\n          i0.ɵɵproperty(\"disabledItemHandler\", ctx.calculatedDisabledItemHandler)(\"markerHandler\", ctx.markerHandler)(\"max\", i0.ɵɵpipeBindV(1, 13, i0.ɵɵpureFunction4(29, _c0, ctx.max, ctx.capsMapper, ctx.currentValue, ctx.maxLength)))(\"maxViewedMonth\", ctx.items.length || ctx.mobile ? null : i0.ɵɵpipeBind3(2, 19, ctx.defaultViewedMonth, ctx.monthOffset, -1))(\"min\", i0.ɵɵpipeBindV(3, 23, i0.ɵɵpureFunction4(34, _c1, ctx.min, ctx.capsMapper, ctx.currentValue, ctx.maxLength)))(\"month\", ctx.defaultViewedMonth)(\"showAdjacent\", !!ctx.items.length || ctx.mobile)(\"value\", ctx.currentValue);\n          i0.ɵɵtwoWayProperty(\"hoveredItem\", ctx.hoveredItem);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.items.length && !ctx.mobile)(\"ngIfElse\", presets_r6);\n        }\n      },\n      dependencies: [AsyncPipe, NgForOf, NgIf, TuiCalendar, i1.TuiDataListComponent, i1.TuiOption, TuiIcon, TuiMapperPipe],\n      styles: [\"[_nghost-%COMP%]:not(._mobile){display:flex;min-inline-size:30rem}.t-calendar[_ngcontent-%COMP%]{border-inline-end:1px solid var(--tui-border-normal)}\"],\n      changeDetection: 0\n    });\n  }\n}\n__decorate([tuiPure], TuiCalendarRange.prototype, \"calculateDisabledItemHandler\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCalendarRange, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-calendar-range',\n      imports: [AsyncPipe, NgForOf, NgIf, TuiCalendar, TuiDataList, TuiIcon, TuiMapperPipe],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAsAuxiliary(TuiCalendarRange), tuiCalendarSheetOptionsProvider({\n        rangeMode: true\n      })],\n      host: {\n        '[class._mobile]': 'mobile',\n        '(document:keydown.capture)': 'onEsc($event)'\n      },\n      template: \"<tui-calendar\\n    automation-id=\\\"tui-calendar-range__calendar\\\"\\n    [class.t-calendar]=\\\"!mobile\\\"\\n    [disabledItemHandler]=\\\"calculatedDisabledItemHandler\\\"\\n    [markerHandler]=\\\"markerHandler\\\"\\n    [max]=\\\"max | tuiMapper: capsMapper : currentValue : maxLength : false\\\"\\n    [maxViewedMonth]=\\\"items.length || mobile ? null : (defaultViewedMonth | tuiMapper: monthOffset : -1)\\\"\\n    [min]=\\\"min | tuiMapper: capsMapper : currentValue : maxLength : true\\\"\\n    [month]=\\\"defaultViewedMonth\\\"\\n    [showAdjacent]=\\\"!!items.length || mobile\\\"\\n    [value]=\\\"currentValue\\\"\\n    [(hoveredItem)]=\\\"hoveredItem\\\"\\n    (dayClick)=\\\"onDayClick($event)\\\"\\n    (monthChange)=\\\"onMonthChange($event)\\\"\\n/>\\n<tui-calendar\\n    *ngIf=\\\"!items.length && !mobile; else presets\\\"\\n    [disabledItemHandler]=\\\"calculatedDisabledItemHandler\\\"\\n    [markerHandler]=\\\"markerHandler\\\"\\n    [max]=\\\"max | tuiMapper: capsMapper : currentValue : maxLength : false\\\"\\n    [min]=\\\"min | tuiMapper: capsMapper : currentValue : maxLength : true\\\"\\n    [minViewedMonth]=\\\"defaultViewedMonth | tuiMapper: monthOffset : 1\\\"\\n    [month]=\\\"defaultViewedMonth | tuiMapper: monthOffset : 1\\\"\\n    [showAdjacent]=\\\"false\\\"\\n    [value]=\\\"currentValue\\\"\\n    [(hoveredItem)]=\\\"hoveredItem\\\"\\n    (dayClick)=\\\"onDayClick($event)\\\"\\n    (monthChange)=\\\"onMonthChange($event.append({month: -1}))\\\"\\n/>\\n<ng-template #presets>\\n    <tui-data-list\\n        *ngIf=\\\"!mobile\\\"\\n        automation-id=\\\"tui-calendar-range__menu\\\"\\n        role=\\\"menu\\\"\\n        [style.flex]=\\\"1\\\"\\n    >\\n        <button\\n            *ngFor=\\\"let item of items | tuiMapper: mapper : min : max : minLength : (otherDateText$ | async)\\\"\\n            automation-id=\\\"tui-calendar-range__menu__item\\\"\\n            role=\\\"menuitemradio\\\"\\n            tuiOption\\n            type=\\\"button\\\"\\n            [attr.aria-checked]=\\\"isItemActive(item)\\\"\\n            (click)=\\\"onItemSelect(item)\\\"\\n            (pointerdown.prevent.zoneless)=\\\"(0)\\\"\\n        >\\n            {{ item }}\\n            <tui-icon\\n                *ngIf=\\\"isItemActive(item)\\\"\\n                automation-id=\\\"tui-calendar-range__checkmark\\\"\\n                [icon]=\\\"icons.check\\\"\\n                [style.font-size.rem]=\\\"1\\\"\\n            />\\n        </button>\\n    </tui-data-list>\\n</ng-template>\\n\",\n      styles: [\":host:not(._mobile){display:flex;min-inline-size:30rem}.t-calendar{border-inline-end:1px solid var(--tui-border-normal)}\\n\"]\n    }]\n  }], function () {\n    return [];\n  }, {\n    disabledItemHandler: [{\n      type: Input\n    }],\n    markerHandler: [{\n      type: Input\n    }],\n    items: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    minLength: [{\n      type: Input\n    }],\n    maxLength: [{\n      type: Input\n    }],\n    item: [{\n      type: Input\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    itemChange: [{\n      type: Output\n    }],\n    valueSetter: [{\n      type: Input,\n      args: ['value']\n    }],\n    defaultViewedMonth: [{\n      type: Input\n    }],\n    calculateDisabledItemHandler: []\n  });\n})();\nclass TuiDayRangePeriod {\n  constructor(range, name, content) {\n    this.range = range;\n    this.name = name;\n    this.content = content;\n  }\n  toString() {\n    return this.name;\n  }\n}\nfunction tuiCreateDefaultDayRangePeriods(periodTitles = ['For all the time', 'Today', 'Yesterday', 'Current week', 'Current month', 'Previous month']) {\n  const today = TuiDay.currentLocal();\n  const yesterday = today.append({\n    day: -1\n  });\n  const startOfWeek = today.append({\n    day: -today.dayOfWeek()\n  });\n  const endOfWeek = startOfWeek.append({\n    day: 6\n  });\n  const startOfMonth = today.append({\n    day: 1 - today.day\n  });\n  const endOfMonth = startOfMonth.append({\n    month: 1,\n    day: -1\n  });\n  const startOfLastMonth = startOfMonth.append({\n    month: -1\n  });\n  return [new TuiDayRangePeriod(new TuiDayRange(TUI_FIRST_DAY, today), periodTitles[0]), new TuiDayRangePeriod(new TuiDayRange(today, today), periodTitles[1]), new TuiDayRangePeriod(new TuiDayRange(yesterday, yesterday), periodTitles[2]), new TuiDayRangePeriod(new TuiDayRange(startOfWeek, endOfWeek), periodTitles[3]), new TuiDayRangePeriod(new TuiDayRange(startOfMonth, endOfMonth), periodTitles[4]), new TuiDayRangePeriod(new TuiDayRange(startOfLastMonth, startOfMonth.append({\n    day: -1\n  })), periodTitles[5])];\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DAY_CAPS_MAPPER, TuiCalendarRange, TuiDayRangePeriod, calculateDisabledItemHandler, tuiCreateDefaultDayRangePeriods };", "map": {"version": 3, "names": ["TuiDayRange", "TUI_FIRST_DAY", "TUI_LAST_DAY", "TuiDay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "__decorate", "AsyncPipe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i0", "inject", "ChangeDetectorRef", "EventEmitter", "Component", "ChangeDetectionStrategy", "Input", "Output", "takeUntilDestroyed", "TUI_FALSE_HANDLER", "tuiWatch", "TuiMapperPipe", "TUI_IS_MOBILE", "tuiIsString", "tuiNullable<PERSON>ame", "tuiPure", "tuiCalendarSheetOptionsProvider", "TuiCalendar", "i1", "TuiDataList", "TuiIcon", "TUI_COMMON_ICONS", "tuiAsAuxiliary", "TUI_OTHER_DATE_TEXT", "TUI_CALENDAR_DATE_STREAM", "_c0", "a0", "a1", "a2", "a3", "_c1", "_c2", "a4", "a5", "TuiCalendar<PERSON><PERSON><PERSON>_tui_calendar_4_Template", "rf", "ctx", "_r2", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵpipe", "ɵɵtwoWayListener", "TuiCalendar<PERSON><PERSON><PERSON>_tui_calendar_4_Template_tui_calendar_hoveredItemChange_0_listener", "$event", "ɵɵrestoreView", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "hoveredItem", "ɵɵresetView", "ɵɵlistener", "TuiCalendar<PERSON><PERSON><PERSON>_tui_calendar_4_Template_tui_calendar_dayClick_0_listener", "onDayClick", "TuiCalendar<PERSON><PERSON><PERSON>_tui_calendar_4_Template_tui_calendar_monthChange_0_listener", "onMonthChange", "append", "month", "ɵɵelementEnd", "ɵɵproperty", "calculatedDisabledItemHandler", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵpipeBindV", "ɵɵpureFunction4", "max", "capsMapper", "currentValue", "max<PERSON><PERSON><PERSON>", "min", "ɵɵpipeBind3", "defaultViewedMonth", "monthOffset", "ɵɵtwoWayProperty", "TuiCalendarRange_ng_template_5_tui_data_list_0_button_1_tui_icon_2_Template", "ɵɵelement", "ɵɵstyleProp", "icons", "check", "TuiCalendarRange_ng_template_5_tui_data_list_0_button_1_Template", "_r4", "TuiCalendarRange_ng_template_5_tui_data_list_0_button_1_Template_button_click_0_listener", "item_r5", "$implicit", "onItemSelect", "TuiCalendarRange_ng_template_5_tui_data_list_0_button_1_Template_button_pointerdown_prevent_zoneless_0_listener", "ɵɵtext", "ɵɵtemplate", "ɵɵattribute", "isItemActive", "ɵɵadvance", "ɵɵtextInterpolate1", "TuiCalendarRange_ng_template_5_tui_data_list_0_Template", "ɵɵpureFunction6", "items", "mapper", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵpipeBind1", "otherDateText$", "TuiCalendarRange_ng_template_5_Template", "mobile", "calculateDisabledItemHandler", "disabledItemHandler", "value", "item", "negative<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "fromEntries", "entries", "map", "key", "disabledBefore", "day", "disabledAfter", "inDisabledRange", "dayBefore", "dayAfter", "TUI_DAY_CAPS_MAPPER", "current", "backwards", "isSingleDay", "negativeMaxLength", "from", "dateShift", "TuiCalendarRange", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "cdr", "previousValue", "currentLocal", "valueChange", "itemChange", "otherDateText", "filter", "range", "daySameOrBefore", "to", "daySameOrAfter", "optional", "pipe", "subscribe", "initDefaultViewedMonth", "valueSetter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedActivePeriod", "period", "ngOnChanges", "ngOnInit", "onEsc", "event", "stopPropagation", "activePeriod", "toString", "emit", "updateValue", "dayLimit", "sort", "findItemByDayRange", "find", "a", "b", "daySame", "length", "monthSameOrAfter", "monthSameOrBefore", "<PERSON><PERSON><PERSON><PERSON>", "ɵfac", "TuiCalendarRange_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "TuiCalendarRange_HostBindings", "TuiCalendarRange_keydown_capture_HostBindingHandler", "ɵɵresolveDocument", "ɵɵclassProp", "inputs", "ɵɵInputFlags", "None", "outputs", "standalone", "features", "ɵɵProvidersFeature", "rangeMode", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiCalendarRange_Template", "_r1", "TuiCalendar<PERSON><PERSON><PERSON>_Template_tui_calendar_hoveredItemChange_0_listener", "TuiCalendar<PERSON><PERSON><PERSON>_Template_tui_calendar_dayClick_0_listener", "TuiCalendar<PERSON><PERSON><PERSON>_Template_tui_calendar_monthChange_0_listener", "ɵɵtemplateRefExtractor", "presets_r6", "ɵɵreference", "dependencies", "TuiDataListComponent", "TuiOption", "styles", "changeDetection", "prototype", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "host", "TuiDayRangePeriod", "name", "content", "tuiCreateDefaultDayRangePeriods", "periodTitles", "today", "yesterday", "startOfWeek", "dayOfWeek", "endOfWeek", "startOfMonth", "endOfMonth", "startOfLastMonth"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-calendar-range.mjs"], "sourcesContent": ["import { Tui<PERSON>ay<PERSON><PERSON><PERSON>, TUI_FIRST_DAY, TUI_LAST_DAY, TuiDay, TuiMonth } from '@taiga-ui/cdk/date-time';\nimport { __decorate } from 'tslib';\nimport { AsyncPipe, NgForOf, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ChangeDetectorRef, EventEmitter, Component, ChangeDetectionStrategy, Input, Output } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { tuiWatch } from '@taiga-ui/cdk/observables';\nimport { TuiMapperPipe } from '@taiga-ui/cdk/pipes/mapper';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiIsString, tuiNullableSame, tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiCalendarSheetOptionsProvider, TuiCalendar } from '@taiga-ui/core/components/calendar';\nimport * as i1 from '@taiga-ui/core/components/data-list';\nimport { TuiDataList } from '@taiga-ui/core/components/data-list';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TUI_COMMON_ICONS, tuiAsAuxiliary } from '@taiga-ui/core/tokens';\nimport { TUI_OTHER_DATE_TEXT, TUI_CALENDAR_DATE_STREAM } from '@taiga-ui/kit/tokens';\n\nconst calculateDisabledItemHandler = (disabledItemHandler, value, minLength) => (item) => {\n    if (!value || value instanceof TuiDayRange || !minLength) {\n        return disabledItemHandler(item);\n    }\n    const negativeMinLength = Object.fromEntries(Object.entries(minLength).map(([key, value]) => [key, -value]));\n    const disabledBefore = value.append(negativeMinLength).append({ day: 1 });\n    const disabledAfter = value.append(minLength).append({ day: -1 });\n    const inDisabledRange = disabledBefore.dayBefore(item) && disabledAfter.dayAfter(item);\n    return inDisabledRange || disabledItemHandler(item);\n};\n\nconst TUI_DAY_CAPS_MAPPER = (current, value, maxLength, backwards) => {\n    if (\n    // TODO(v5): replace with `if (!(value instanceof TuiDay) || !maxLength)` (backward compatibility)\n    (value instanceof TuiDayRange && !value.isSingleDay) ||\n        !value ||\n        !maxLength) {\n        return backwards ? current || TUI_FIRST_DAY : current || TUI_LAST_DAY;\n    }\n    const negativeMaxLength = Object.fromEntries(Object.entries(maxLength).map(([key, value]) => [key, -value]));\n    // TODO(v5): `value instanceof TuiDay` always `true`\n    const from = value instanceof TuiDay ? value : value.from;\n    const dateShift = from\n        .append(backwards ? negativeMaxLength : maxLength)\n        .append({ day: !backwards ? -1 : 1 });\n    if (backwards) {\n        return dateShift.dayBefore(current || TUI_FIRST_DAY)\n            ? current || TUI_FIRST_DAY\n            : dateShift;\n    }\n    if (!current) {\n        return dateShift;\n    }\n    return dateShift.dayAfter(current) ? current : dateShift;\n};\n\nclass TuiCalendarRange {\n    constructor() {\n        /**\n         * @deprecated use `item`\n         */\n        this.selectedPeriod = null;\n        this.cdr = inject(ChangeDetectorRef);\n        this.currentValue = null;\n        this.previousValue = null;\n        this.hoveredItem = null;\n        this.month = TuiMonth.currentLocal();\n        this.otherDateText$ = inject(TUI_OTHER_DATE_TEXT);\n        this.icons = inject(TUI_COMMON_ICONS);\n        this.capsMapper = TUI_DAY_CAPS_MAPPER;\n        this.mobile = inject(TUI_IS_MOBILE);\n        this.disabledItemHandler = TUI_FALSE_HANDLER;\n        this.markerHandler = null;\n        this.items = [];\n        this.min = TUI_FIRST_DAY;\n        this.max = TUI_LAST_DAY;\n        this.minLength = null;\n        this.maxLength = null;\n        this.item = null;\n        this.valueChange = new EventEmitter();\n        this.itemChange = new EventEmitter();\n        this.monthOffset = (value, month) => value.append({ month });\n        this.mapper = (items, min, max, minLength, otherDateText) => [\n            ...items.filter((item) => (minLength === null ||\n                item.range.from\n                    .append(minLength)\n                    .append({ day: -1 })\n                    .daySameOrBefore(item.range.to)) &&\n                (min === null || item.range.to.daySameOrAfter(min)) &&\n                (max === null || item.range.from.daySameOrBefore(max))),\n            otherDateText || '',\n        ];\n        inject(TUI_CALENDAR_DATE_STREAM, { optional: true })\n            ?.pipe(tuiWatch(), takeUntilDestroyed())\n            .subscribe((value) => {\n            this.currentValue = value;\n            this.initDefaultViewedMonth();\n        });\n    }\n    set valueSetter(value) {\n        this.currentValue = value;\n    }\n    set defaultViewedMonth(month) {\n        if (!this.currentValue) {\n            this.month = month;\n        }\n    }\n    set value(value) {\n        this.cdr.markForCheck();\n        this.currentValue = value;\n        this.initDefaultViewedMonth();\n    }\n    get defaultViewedMonth() {\n        return this.month;\n    }\n    /**\n     * @deprecated use `item`\n     */\n    get selectedActivePeriod() {\n        return this.selectedPeriod;\n    }\n    /**\n     * @deprecated use `item`\n     */\n    set selectedActivePeriod(period) {\n        this.selectedPeriod = period;\n    }\n    ngOnChanges() {\n        if (!this.currentValue) {\n            this.initDefaultViewedMonth();\n        }\n    }\n    ngOnInit() {\n        this.initDefaultViewedMonth();\n    }\n    get calculatedDisabledItemHandler() {\n        return this.calculateDisabledItemHandler(this.disabledItemHandler, this.currentValue, this.minLength);\n    }\n    onEsc(event) {\n        if (event.key !== 'Escape' || !(this.currentValue instanceof TuiDay)) {\n            return;\n        }\n        event.stopPropagation();\n        this.currentValue = this.previousValue;\n    }\n    isItemActive(item) {\n        const { activePeriod } = this;\n        return ((tuiIsString(item) && activePeriod === null) ||\n            activePeriod === item ||\n            activePeriod?.toString() === item.toString());\n    }\n    onItemSelect(item) {\n        if (!tuiIsString(item)) {\n            this.selectedActivePeriod = item;\n            this.itemChange.emit(item);\n            this.updateValue(item.range.dayLimit(this.min, this.max));\n        }\n        else if (this.activePeriod !== null) {\n            this.selectedActivePeriod = null;\n            this.itemChange.emit(null);\n            this.updateValue(null);\n        }\n        this.initDefaultViewedMonth();\n    }\n    onMonthChange(month) {\n        this.month = month;\n    }\n    onDayClick(day) {\n        this.previousValue = this.currentValue;\n        this.selectedActivePeriod = null;\n        if (this.currentValue instanceof TuiDay) {\n            const range = TuiDayRange.sort(this.currentValue, day);\n            this.currentValue = range;\n            this.itemChange.emit(this.findItemByDayRange(range));\n            this.updateValue(range);\n        }\n        else {\n            this.currentValue = day;\n        }\n    }\n    updateValue(value) {\n        this.currentValue = value;\n        this.valueChange.emit(value);\n    }\n    get activePeriod() {\n        return (this.item ??\n            this.selectedActivePeriod ??\n            (this.items.find((item) => tuiNullableSame(this.currentValue instanceof TuiDay\n                ? new TuiDayRange(this.currentValue, this.currentValue)\n                : this.currentValue, item.range, (a, b) => a.from.daySame(b.from.dayLimit(this.min, this.max)) &&\n                a.to.daySame(b.to.dayLimit(this.min, this.max)))) ||\n                null));\n    }\n    calculateDisabledItemHandler(disabledItemHandler, value, minLength) {\n        return calculateDisabledItemHandler(disabledItemHandler, value, minLength);\n    }\n    initDefaultViewedMonth() {\n        if (this.currentValue instanceof TuiDay) {\n            this.month = this.currentValue;\n        }\n        else if (this.currentValue) {\n            this.month = this.items.length\n                ? this.currentValue.to\n                : this.currentValue.from;\n        }\n        else if (this.max && this.month.monthSameOrAfter(this.max)) {\n            this.month = this.items.length ? this.max : this.max.append({ month: -1 });\n        }\n        else if (this.min && this.month.monthSameOrBefore(this.min)) {\n            this.month = this.min;\n        }\n    }\n    findItemByDayRange(dayRange) {\n        return this.items.find((item) => dayRange.daySame(item.range)) ?? null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendarRange, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCalendarRange, isStandalone: true, selector: \"tui-calendar-range\", inputs: { disabledItemHandler: \"disabledItemHandler\", markerHandler: \"markerHandler\", items: \"items\", min: \"min\", max: \"max\", minLength: \"minLength\", maxLength: \"maxLength\", item: \"item\", valueSetter: [\"value\", \"valueSetter\"], defaultViewedMonth: \"defaultViewedMonth\" }, outputs: { valueChange: \"valueChange\", itemChange: \"itemChange\" }, host: { listeners: { \"document:keydown.capture\": \"onEsc($event)\" }, properties: { \"class._mobile\": \"mobile\" } }, providers: [\n            tuiAsAuxiliary(TuiCalendarRange),\n            tuiCalendarSheetOptionsProvider({ rangeMode: true }),\n        ], usesOnChanges: true, ngImport: i0, template: \"<tui-calendar\\n    automation-id=\\\"tui-calendar-range__calendar\\\"\\n    [class.t-calendar]=\\\"!mobile\\\"\\n    [disabledItemHandler]=\\\"calculatedDisabledItemHandler\\\"\\n    [markerHandler]=\\\"markerHandler\\\"\\n    [max]=\\\"max | tuiMapper: capsMapper : currentValue : maxLength : false\\\"\\n    [maxViewedMonth]=\\\"items.length || mobile ? null : (defaultViewedMonth | tuiMapper: monthOffset : -1)\\\"\\n    [min]=\\\"min | tuiMapper: capsMapper : currentValue : maxLength : true\\\"\\n    [month]=\\\"defaultViewedMonth\\\"\\n    [showAdjacent]=\\\"!!items.length || mobile\\\"\\n    [value]=\\\"currentValue\\\"\\n    [(hoveredItem)]=\\\"hoveredItem\\\"\\n    (dayClick)=\\\"onDayClick($event)\\\"\\n    (monthChange)=\\\"onMonthChange($event)\\\"\\n/>\\n<tui-calendar\\n    *ngIf=\\\"!items.length && !mobile; else presets\\\"\\n    [disabledItemHandler]=\\\"calculatedDisabledItemHandler\\\"\\n    [markerHandler]=\\\"markerHandler\\\"\\n    [max]=\\\"max | tuiMapper: capsMapper : currentValue : maxLength : false\\\"\\n    [min]=\\\"min | tuiMapper: capsMapper : currentValue : maxLength : true\\\"\\n    [minViewedMonth]=\\\"defaultViewedMonth | tuiMapper: monthOffset : 1\\\"\\n    [month]=\\\"defaultViewedMonth | tuiMapper: monthOffset : 1\\\"\\n    [showAdjacent]=\\\"false\\\"\\n    [value]=\\\"currentValue\\\"\\n    [(hoveredItem)]=\\\"hoveredItem\\\"\\n    (dayClick)=\\\"onDayClick($event)\\\"\\n    (monthChange)=\\\"onMonthChange($event.append({month: -1}))\\\"\\n/>\\n<ng-template #presets>\\n    <tui-data-list\\n        *ngIf=\\\"!mobile\\\"\\n        automation-id=\\\"tui-calendar-range__menu\\\"\\n        role=\\\"menu\\\"\\n        [style.flex]=\\\"1\\\"\\n    >\\n        <button\\n            *ngFor=\\\"let item of items | tuiMapper: mapper : min : max : minLength : (otherDateText$ | async)\\\"\\n            automation-id=\\\"tui-calendar-range__menu__item\\\"\\n            role=\\\"menuitemradio\\\"\\n            tuiOption\\n            type=\\\"button\\\"\\n            [attr.aria-checked]=\\\"isItemActive(item)\\\"\\n            (click)=\\\"onItemSelect(item)\\\"\\n            (pointerdown.prevent.zoneless)=\\\"(0)\\\"\\n        >\\n            {{ item }}\\n            <tui-icon\\n                *ngIf=\\\"isItemActive(item)\\\"\\n                automation-id=\\\"tui-calendar-range__checkmark\\\"\\n                [icon]=\\\"icons.check\\\"\\n                [style.font-size.rem]=\\\"1\\\"\\n            />\\n        </button>\\n    </tui-data-list>\\n</ng-template>\\n\", styles: [\":host:not(._mobile){display:flex;min-inline-size:30rem}.t-calendar{border-inline-end:1px solid var(--tui-border-normal)}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: TuiCalendar, selector: \"tui-calendar\", inputs: [\"month\", \"disabledItemHandler\", \"min\", \"max\", \"minViewedMonth\", \"maxViewedMonth\", \"hoveredItem\", \"showAdjacent\", \"markerHandler\", \"value\", \"initialView\"], outputs: [\"dayClick\", \"monthChange\", \"hoveredItemChange\"] }, { kind: \"component\", type: i1.TuiDataListComponent, selector: \"tui-data-list\", inputs: [\"emptyContent\", \"size\"] }, { kind: \"component\", type: i1.TuiOption, selector: \"button[tuiOption]:not([new]), a[tuiOption]:not([new]), label[tuiOption]:not([new])\", inputs: [\"disabled\", \"value\"] }, { kind: \"component\", type: TuiIcon, selector: \"tui-icon\", inputs: [\"icon\", \"background\"] }, { kind: \"pipe\", type: TuiMapperPipe, name: \"tuiMapper\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\n__decorate([\n    tuiPure\n], TuiCalendarRange.prototype, \"calculateDisabledItemHandler\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendarRange, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-calendar-range', imports: [AsyncPipe, NgForOf, NgIf, TuiCalendar, TuiDataList, TuiIcon, TuiMapperPipe], changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        tuiAsAuxiliary(TuiCalendarRange),\n                        tuiCalendarSheetOptionsProvider({ rangeMode: true }),\n                    ], host: {\n                        '[class._mobile]': 'mobile',\n                        '(document:keydown.capture)': 'onEsc($event)',\n                    }, template: \"<tui-calendar\\n    automation-id=\\\"tui-calendar-range__calendar\\\"\\n    [class.t-calendar]=\\\"!mobile\\\"\\n    [disabledItemHandler]=\\\"calculatedDisabledItemHandler\\\"\\n    [markerHandler]=\\\"markerHandler\\\"\\n    [max]=\\\"max | tuiMapper: capsMapper : currentValue : maxLength : false\\\"\\n    [maxViewedMonth]=\\\"items.length || mobile ? null : (defaultViewedMonth | tuiMapper: monthOffset : -1)\\\"\\n    [min]=\\\"min | tuiMapper: capsMapper : currentValue : maxLength : true\\\"\\n    [month]=\\\"defaultViewedMonth\\\"\\n    [showAdjacent]=\\\"!!items.length || mobile\\\"\\n    [value]=\\\"currentValue\\\"\\n    [(hoveredItem)]=\\\"hoveredItem\\\"\\n    (dayClick)=\\\"onDayClick($event)\\\"\\n    (monthChange)=\\\"onMonthChange($event)\\\"\\n/>\\n<tui-calendar\\n    *ngIf=\\\"!items.length && !mobile; else presets\\\"\\n    [disabledItemHandler]=\\\"calculatedDisabledItemHandler\\\"\\n    [markerHandler]=\\\"markerHandler\\\"\\n    [max]=\\\"max | tuiMapper: capsMapper : currentValue : maxLength : false\\\"\\n    [min]=\\\"min | tuiMapper: capsMapper : currentValue : maxLength : true\\\"\\n    [minViewedMonth]=\\\"defaultViewedMonth | tuiMapper: monthOffset : 1\\\"\\n    [month]=\\\"defaultViewedMonth | tuiMapper: monthOffset : 1\\\"\\n    [showAdjacent]=\\\"false\\\"\\n    [value]=\\\"currentValue\\\"\\n    [(hoveredItem)]=\\\"hoveredItem\\\"\\n    (dayClick)=\\\"onDayClick($event)\\\"\\n    (monthChange)=\\\"onMonthChange($event.append({month: -1}))\\\"\\n/>\\n<ng-template #presets>\\n    <tui-data-list\\n        *ngIf=\\\"!mobile\\\"\\n        automation-id=\\\"tui-calendar-range__menu\\\"\\n        role=\\\"menu\\\"\\n        [style.flex]=\\\"1\\\"\\n    >\\n        <button\\n            *ngFor=\\\"let item of items | tuiMapper: mapper : min : max : minLength : (otherDateText$ | async)\\\"\\n            automation-id=\\\"tui-calendar-range__menu__item\\\"\\n            role=\\\"menuitemradio\\\"\\n            tuiOption\\n            type=\\\"button\\\"\\n            [attr.aria-checked]=\\\"isItemActive(item)\\\"\\n            (click)=\\\"onItemSelect(item)\\\"\\n            (pointerdown.prevent.zoneless)=\\\"(0)\\\"\\n        >\\n            {{ item }}\\n            <tui-icon\\n                *ngIf=\\\"isItemActive(item)\\\"\\n                automation-id=\\\"tui-calendar-range__checkmark\\\"\\n                [icon]=\\\"icons.check\\\"\\n                [style.font-size.rem]=\\\"1\\\"\\n            />\\n        </button>\\n    </tui-data-list>\\n</ng-template>\\n\", styles: [\":host:not(._mobile){display:flex;min-inline-size:30rem}.t-calendar{border-inline-end:1px solid var(--tui-border-normal)}\\n\"] }]\n        }], ctorParameters: function () { return []; }, propDecorators: { disabledItemHandler: [{\n                type: Input\n            }], markerHandler: [{\n                type: Input\n            }], items: [{\n                type: Input\n            }], min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], minLength: [{\n                type: Input\n            }], maxLength: [{\n                type: Input\n            }], item: [{\n                type: Input\n            }], valueChange: [{\n                type: Output\n            }], itemChange: [{\n                type: Output\n            }], valueSetter: [{\n                type: Input,\n                args: ['value']\n            }], defaultViewedMonth: [{\n                type: Input\n            }], calculateDisabledItemHandler: [] } });\n\nclass TuiDayRangePeriod {\n    constructor(range, name, content) {\n        this.range = range;\n        this.name = name;\n        this.content = content;\n    }\n    toString() {\n        return this.name;\n    }\n}\nfunction tuiCreateDefaultDayRangePeriods(periodTitles = [\n    'For all the time',\n    'Today',\n    'Yesterday',\n    'Current week',\n    'Current month',\n    'Previous month',\n]) {\n    const today = TuiDay.currentLocal();\n    const yesterday = today.append({ day: -1 });\n    const startOfWeek = today.append({ day: -today.dayOfWeek() });\n    const endOfWeek = startOfWeek.append({ day: 6 });\n    const startOfMonth = today.append({ day: 1 - today.day });\n    const endOfMonth = startOfMonth.append({ month: 1, day: -1 });\n    const startOfLastMonth = startOfMonth.append({ month: -1 });\n    return [\n        new TuiDayRangePeriod(new TuiDayRange(TUI_FIRST_DAY, today), periodTitles[0]),\n        new TuiDayRangePeriod(new TuiDayRange(today, today), periodTitles[1]),\n        new TuiDayRangePeriod(new TuiDayRange(yesterday, yesterday), periodTitles[2]),\n        new TuiDayRangePeriod(new TuiDayRange(startOfWeek, endOfWeek), periodTitles[3]),\n        new TuiDayRangePeriod(new TuiDayRange(startOfMonth, endOfMonth), periodTitles[4]),\n        new TuiDayRangePeriod(new TuiDayRange(startOfLastMonth, startOfMonth.append({ day: -1 })), periodTitles[5]),\n    ];\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DAY_CAPS_MAPPER, TuiCalendarRange, TuiDayRangePeriod, calculateDisabledItemHandler, tuiCreateDefaultDayRangePeriods };\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,aAAa,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,yBAAyB;AACpG,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,EAAEC,OAAO,EAAEC,IAAI,QAAQ,iBAAiB;AAC1D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAC1H,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,WAAW,EAAEC,eAAe,EAAEC,OAAO,QAAQ,mCAAmC;AACzF,SAASC,+BAA+B,EAAEC,WAAW,QAAQ,oCAAoC;AACjG,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,WAAW,QAAQ,qCAAqC;AACjE,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,uBAAuB;AACxE,SAASC,mBAAmB,EAAEC,wBAAwB,QAAQ,sBAAsB;AAAC,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,MAAAH,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAJ,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,MAAAH,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;AAAA,MAAAE,GAAA,GAAAA,CAAAL,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAG,EAAA,EAAAC,EAAA,MAAAP,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAG,EAAA,EAAAC,EAAA;AAAA,SAAAC,yCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAqMgBrC,EAAE,CAAAsC,gBAAA;IAAFtC,EAAE,CAAAuC,cAAA,qBAI8yC,CAAC;IAJjzCvC,EAAE,CAAAwC,MAAA;IAAFxC,EAAE,CAAAwC,MAAA;IAAFxC,EAAE,CAAAwC,MAAA;IAAFxC,EAAE,CAAAwC,MAAA;IAAFxC,EAAE,CAAAyC,gBAAA,+BAAAC,mFAAAC,MAAA;MAAF3C,EAAE,CAAA4C,aAAA,CAAAP,GAAA;MAAA,MAAAQ,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAF9C,EAAE,CAAA+C,kBAAA,CAAAF,MAAA,CAAAG,WAAA,EAAAL,MAAA,MAAAE,MAAA,CAAAG,WAAA,GAAAL,MAAA;MAAA,OAAF3C,EAAE,CAAAiD,WAAA,CAAAN,MAAA;IAAA,CAIksC,CAAC;IAJrsC3C,EAAE,CAAAkD,UAAA,sBAAAC,0EAAAR,MAAA;MAAF3C,EAAE,CAAA4C,aAAA,CAAAP,GAAA;MAAA,MAAAQ,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAAiD,WAAA,CAIstCJ,MAAA,CAAAO,UAAA,CAAAT,MAAiB,CAAC;IAAA,CAAC,CAAC,yBAAAU,6EAAAV,MAAA;MAJ5uC3C,EAAE,CAAA4C,aAAA,CAAAP,GAAA;MAAA,MAAAQ,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAAiD,WAAA,CAIgwCJ,MAAA,CAAAS,aAAA,CAAcX,MAAA,CAAAY,MAAA,CAAc;QAAAC,KAAA,GAAS;MAAC,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAJ7yCxD,EAAE,CAAAyD,YAAA,CAI8yC,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAAU,MAAA,GAJjzC7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAA0D,UAAA,wBAAAb,MAAA,CAAAc,6BAIoxB,CAAC,kBAAAd,MAAA,CAAAe,aAAsC,CAAC,QAJ9zB5D,EAAE,CAAA6D,WAAA,OAAF7D,EAAE,CAAA8D,eAAA,KAAArC,GAAA,EAAAoB,MAAA,CAAAkB,GAAA,EAAAlB,MAAA,CAAAmB,UAAA,EAAAnB,MAAA,CAAAoB,YAAA,EAAApB,MAAA,CAAAqB,SAAA,EAIy4B,CAAC,QAJ54BlE,EAAE,CAAA6D,WAAA,QAAF7D,EAAE,CAAA8D,eAAA,KAAAhC,GAAA,EAAAe,MAAA,CAAAsB,GAAA,EAAAtB,MAAA,CAAAmB,UAAA,EAAAnB,MAAA,CAAAoB,YAAA,EAAApB,MAAA,CAAAqB,SAAA,EAIs9B,CAAC,mBAJz9BlE,EAAE,CAAAoE,WAAA,QAAAvB,MAAA,CAAAwB,kBAAA,EAAAxB,MAAA,CAAAyB,WAAA,IAIgiC,CAAC,UAJniCtE,EAAE,CAAAoE,WAAA,QAAAvB,MAAA,CAAAwB,kBAAA,EAAAxB,MAAA,CAAAyB,WAAA,IAIimC,CAAC,sBAA6B,CAAC,UAAAzB,MAAA,CAAAoB,YAA6B,CAAC;IAJhqCjE,EAAE,CAAAuE,gBAAA,gBAAA1B,MAAA,CAAAG,WAIksC,CAAC;EAAA;AAAA;AAAA,SAAAwB,4EAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJrsCnC,EAAE,CAAAyE,SAAA,iBAIiqE,CAAC;EAAA;EAAA,IAAAtC,EAAA;IAAA,MAAAU,MAAA,GAJpqE7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAA0E,WAAA,sBAIipE,CAAC;IAJppE1E,EAAE,CAAA0D,UAAA,SAAAb,MAAA,CAAA8B,KAAA,CAAAC,KAIomE,CAAC;EAAA;AAAA;AAAA,SAAAC,iEAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA2C,GAAA,GAJvmE9E,EAAE,CAAAsC,gBAAA;IAAFtC,EAAE,CAAAuC,cAAA,eAI85D,CAAC;IAJj6DvC,EAAE,CAAAkD,UAAA,mBAAA6B,yFAAA;MAAA,MAAAC,OAAA,GAAFhF,EAAE,CAAA4C,aAAA,CAAAkC,GAAA,EAAAG,SAAA;MAAA,MAAApC,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAAiD,WAAA,CAI40DJ,MAAA,CAAAqC,YAAA,CAAAF,OAAiB,CAAC;IAAA,CAAC,CAAC,0CAAAG,gHAAA;MAJl2DnF,EAAE,CAAA4C,aAAA,CAAAkC,GAAA;MAAA,OAAF9E,EAAE,CAAAiD,WAAA,CAIg5D,CAAC;IAAA,CAAE,CAAC;IAJt5DjD,EAAE,CAAAoF,MAAA,EAIo8D,CAAC;IAJv8DpF,EAAE,CAAAqF,UAAA,IAAAb,2EAAA,qBAIiqE,CAAC;IAJpqExE,EAAE,CAAAyD,YAAA,CAIorE,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAA6C,OAAA,GAAA5C,GAAA,CAAA6C,SAAA;IAAA,MAAApC,MAAA,GAJvrE7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAsF,WAAA,iBAAAzC,MAAA,CAAA0C,YAAA,CAAAP,OAAA;IAAFhF,EAAE,CAAAwF,SAAA,CAIo8D,CAAC;IAJv8DxF,EAAE,CAAAyF,kBAAA,MAAAT,OAAA,KAIo8D,CAAC;IAJv8DhF,EAAE,CAAAwF,SAAA,CAIy/D,CAAC;IAJ5/DxF,EAAE,CAAA0D,UAAA,SAAAb,MAAA,CAAA0C,YAAA,CAAAP,OAAA,CAIy/D,CAAC;EAAA;AAAA;AAAA,SAAAU,wDAAAvD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJ5/DnC,EAAE,CAAAuC,cAAA,sBAIm+C,CAAC;IAJt+CvC,EAAE,CAAAqF,UAAA,IAAAR,gEAAA,mBAI85D,CAAC;IAJj6D7E,EAAE,CAAAwC,MAAA;IAAFxC,EAAE,CAAAwC,MAAA;IAAFxC,EAAE,CAAAyD,YAAA,CAI0sE,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAAU,MAAA,GAJ7sE7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAA0E,WAAA,UAI49C,CAAC;IAJ/9C1E,EAAE,CAAAwF,SAAA,CAImmD,CAAC;IAJtmDxF,EAAE,CAAA0D,UAAA,YAAF1D,EAAE,CAAA6D,WAAA,OAAF7D,EAAE,CAAA2F,eAAA,KAAA5D,GAAA,EAAAc,MAAA,CAAA+C,KAAA,EAAA/C,MAAA,CAAAgD,MAAA,EAAAhD,MAAA,CAAAsB,GAAA,EAAAtB,MAAA,CAAAkB,GAAA,EAAAlB,MAAA,CAAAiD,SAAA,EAAF9F,EAAE,CAAA+F,WAAA,OAAAlD,MAAA,CAAAmD,cAAA,GAImmD,CAAC;EAAA;AAAA;AAAA,SAAAC,wCAAA9D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJtmDnC,EAAE,CAAAqF,UAAA,IAAAK,uDAAA,2BAIm+C,CAAC;EAAA;EAAA,IAAAvD,EAAA;IAAA,MAAAU,MAAA,GAJt+C7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAA0D,UAAA,UAAAb,MAAA,CAAAqD,MAIm3C,CAAC;EAAA;AAAA;AAvM39C,MAAMC,4BAA4B,GAAGA,CAACC,mBAAmB,EAAEC,KAAK,EAAEP,SAAS,KAAMQ,IAAI,IAAK;EACtF,IAAI,CAACD,KAAK,IAAIA,KAAK,YAAY9G,WAAW,IAAI,CAACuG,SAAS,EAAE;IACtD,OAAOM,mBAAmB,CAACE,IAAI,CAAC;EACpC;EACA,MAAMC,iBAAiB,GAAGC,MAAM,CAACC,WAAW,CAACD,MAAM,CAACE,OAAO,CAACZ,SAAS,CAAC,CAACa,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEP,KAAK,CAAC,KAAK,CAACO,GAAG,EAAE,CAACP,KAAK,CAAC,CAAC,CAAC;EAC5G,MAAMQ,cAAc,GAAGR,KAAK,CAAC9C,MAAM,CAACgD,iBAAiB,CAAC,CAAChD,MAAM,CAAC;IAAEuD,GAAG,EAAE;EAAE,CAAC,CAAC;EACzE,MAAMC,aAAa,GAAGV,KAAK,CAAC9C,MAAM,CAACuC,SAAS,CAAC,CAACvC,MAAM,CAAC;IAAEuD,GAAG,EAAE,CAAC;EAAE,CAAC,CAAC;EACjE,MAAME,eAAe,GAAGH,cAAc,CAACI,SAAS,CAACX,IAAI,CAAC,IAAIS,aAAa,CAACG,QAAQ,CAACZ,IAAI,CAAC;EACtF,OAAOU,eAAe,IAAIZ,mBAAmB,CAACE,IAAI,CAAC;AACvD,CAAC;AAED,MAAMa,mBAAmB,GAAGA,CAACC,OAAO,EAAEf,KAAK,EAAEnC,SAAS,EAAEmD,SAAS,KAAK;EAClE;EACA;EACChB,KAAK,YAAY9G,WAAW,IAAI,CAAC8G,KAAK,CAACiB,WAAW,IAC/C,CAACjB,KAAK,IACN,CAACnC,SAAS,EAAE;IACZ,OAAOmD,SAAS,GAAGD,OAAO,IAAI5H,aAAa,GAAG4H,OAAO,IAAI3H,YAAY;EACzE;EACA,MAAM8H,iBAAiB,GAAGf,MAAM,CAACC,WAAW,CAACD,MAAM,CAACE,OAAO,CAACxC,SAAS,CAAC,CAACyC,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEP,KAAK,CAAC,KAAK,CAACO,GAAG,EAAE,CAACP,KAAK,CAAC,CAAC,CAAC;EAC5G;EACA,MAAMmB,IAAI,GAAGnB,KAAK,YAAY3G,MAAM,GAAG2G,KAAK,GAAGA,KAAK,CAACmB,IAAI;EACzD,MAAMC,SAAS,GAAGD,IAAI,CACjBjE,MAAM,CAAC8D,SAAS,GAAGE,iBAAiB,GAAGrD,SAAS,CAAC,CACjDX,MAAM,CAAC;IAAEuD,GAAG,EAAE,CAACO,SAAS,GAAG,CAAC,CAAC,GAAG;EAAE,CAAC,CAAC;EACzC,IAAIA,SAAS,EAAE;IACX,OAAOI,SAAS,CAACR,SAAS,CAACG,OAAO,IAAI5H,aAAa,CAAC,GAC9C4H,OAAO,IAAI5H,aAAa,GACxBiI,SAAS;EACnB;EACA,IAAI,CAACL,OAAO,EAAE;IACV,OAAOK,SAAS;EACpB;EACA,OAAOA,SAAS,CAACP,QAAQ,CAACE,OAAO,CAAC,GAAGA,OAAO,GAAGK,SAAS;AAC5D,CAAC;AAED,MAAMC,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV;AACR;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,GAAG,GAAG5H,MAAM,CAACC,iBAAiB,CAAC;IACpC,IAAI,CAAC+D,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC6D,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC9E,WAAW,GAAG,IAAI;IACvB,IAAI,CAACQ,KAAK,GAAG7D,QAAQ,CAACoI,YAAY,CAAC,CAAC;IACpC,IAAI,CAAC/B,cAAc,GAAG/F,MAAM,CAACsB,mBAAmB,CAAC;IACjD,IAAI,CAACoD,KAAK,GAAG1E,MAAM,CAACoB,gBAAgB,CAAC;IACrC,IAAI,CAAC2C,UAAU,GAAGmD,mBAAmB;IACrC,IAAI,CAACjB,MAAM,GAAGjG,MAAM,CAACW,aAAa,CAAC;IACnC,IAAI,CAACwF,mBAAmB,GAAG3F,iBAAiB;IAC5C,IAAI,CAACmD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACgC,KAAK,GAAG,EAAE;IACf,IAAI,CAACzB,GAAG,GAAG3E,aAAa;IACxB,IAAI,CAACuE,GAAG,GAAGtE,YAAY;IACvB,IAAI,CAACqG,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC5B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACoC,IAAI,GAAG,IAAI;IAChB,IAAI,CAAC0B,WAAW,GAAG,IAAI7H,YAAY,CAAC,CAAC;IACrC,IAAI,CAAC8H,UAAU,GAAG,IAAI9H,YAAY,CAAC,CAAC;IACpC,IAAI,CAACmE,WAAW,GAAG,CAAC+B,KAAK,EAAE7C,KAAK,KAAK6C,KAAK,CAAC9C,MAAM,CAAC;MAAEC;IAAM,CAAC,CAAC;IAC5D,IAAI,CAACqC,MAAM,GAAG,CAACD,KAAK,EAAEzB,GAAG,EAAEJ,GAAG,EAAE+B,SAAS,EAAEoC,aAAa,KAAK,CACzD,GAAGtC,KAAK,CAACuC,MAAM,CAAE7B,IAAI,IAAK,CAACR,SAAS,KAAK,IAAI,IACzCQ,IAAI,CAAC8B,KAAK,CAACZ,IAAI,CACVjE,MAAM,CAACuC,SAAS,CAAC,CACjBvC,MAAM,CAAC;MAAEuD,GAAG,EAAE,CAAC;IAAE,CAAC,CAAC,CACnBuB,eAAe,CAAC/B,IAAI,CAAC8B,KAAK,CAACE,EAAE,CAAC,MAClCnE,GAAG,KAAK,IAAI,IAAImC,IAAI,CAAC8B,KAAK,CAACE,EAAE,CAACC,cAAc,CAACpE,GAAG,CAAC,CAAC,KAClDJ,GAAG,KAAK,IAAI,IAAIuC,IAAI,CAAC8B,KAAK,CAACZ,IAAI,CAACa,eAAe,CAACtE,GAAG,CAAC,CAAC,CAAC,EAC3DmE,aAAa,IAAI,EAAE,CACtB;IACDjI,MAAM,CAACuB,wBAAwB,EAAE;MAAEgH,QAAQ,EAAE;IAAK,CAAC,CAAC,EAC9CC,IAAI,CAAC/H,QAAQ,CAAC,CAAC,EAAEF,kBAAkB,CAAC,CAAC,CAAC,CACvCkI,SAAS,CAAErC,KAAK,IAAK;MACtB,IAAI,CAACpC,YAAY,GAAGoC,KAAK;MACzB,IAAI,CAACsC,sBAAsB,CAAC,CAAC;IACjC,CAAC,CAAC;EACN;EACA,IAAIC,WAAWA,CAACvC,KAAK,EAAE;IACnB,IAAI,CAACpC,YAAY,GAAGoC,KAAK;EAC7B;EACA,IAAIhC,kBAAkBA,CAACb,KAAK,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACS,YAAY,EAAE;MACpB,IAAI,CAACT,KAAK,GAAGA,KAAK;IACtB;EACJ;EACA,IAAI6C,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACwB,GAAG,CAACgB,YAAY,CAAC,CAAC;IACvB,IAAI,CAAC5E,YAAY,GAAGoC,KAAK;IACzB,IAAI,CAACsC,sBAAsB,CAAC,CAAC;EACjC;EACA,IAAItE,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACb,KAAK;EACrB;EACA;AACJ;AACA;EACI,IAAIsF,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAAClB,cAAc;EAC9B;EACA;AACJ;AACA;EACI,IAAIkB,oBAAoBA,CAACC,MAAM,EAAE;IAC7B,IAAI,CAACnB,cAAc,GAAGmB,MAAM;EAChC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAAC/E,YAAY,EAAE;MACpB,IAAI,CAAC0E,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACAM,QAAQA,CAAA,EAAG;IACP,IAAI,CAACN,sBAAsB,CAAC,CAAC;EACjC;EACA,IAAIhF,6BAA6BA,CAAA,EAAG;IAChC,OAAO,IAAI,CAACwC,4BAA4B,CAAC,IAAI,CAACC,mBAAmB,EAAE,IAAI,CAACnC,YAAY,EAAE,IAAI,CAAC6B,SAAS,CAAC;EACzG;EACAoD,KAAKA,CAACC,KAAK,EAAE;IACT,IAAIA,KAAK,CAACvC,GAAG,KAAK,QAAQ,IAAI,EAAE,IAAI,CAAC3C,YAAY,YAAYvE,MAAM,CAAC,EAAE;MAClE;IACJ;IACAyJ,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,CAACnF,YAAY,GAAG,IAAI,CAAC6D,aAAa;EAC1C;EACAvC,YAAYA,CAACe,IAAI,EAAE;IACf,MAAM;MAAE+C;IAAa,CAAC,GAAG,IAAI;IAC7B,OAASxI,WAAW,CAACyF,IAAI,CAAC,IAAI+C,YAAY,KAAK,IAAI,IAC/CA,YAAY,KAAK/C,IAAI,IACrB+C,YAAY,EAAEC,QAAQ,CAAC,CAAC,KAAKhD,IAAI,CAACgD,QAAQ,CAAC,CAAC;EACpD;EACApE,YAAYA,CAACoB,IAAI,EAAE;IACf,IAAI,CAACzF,WAAW,CAACyF,IAAI,CAAC,EAAE;MACpB,IAAI,CAACwC,oBAAoB,GAAGxC,IAAI;MAChC,IAAI,CAAC2B,UAAU,CAACsB,IAAI,CAACjD,IAAI,CAAC;MAC1B,IAAI,CAACkD,WAAW,CAAClD,IAAI,CAAC8B,KAAK,CAACqB,QAAQ,CAAC,IAAI,CAACtF,GAAG,EAAE,IAAI,CAACJ,GAAG,CAAC,CAAC;IAC7D,CAAC,MACI,IAAI,IAAI,CAACsF,YAAY,KAAK,IAAI,EAAE;MACjC,IAAI,CAACP,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAACb,UAAU,CAACsB,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC;IAC1B;IACA,IAAI,CAACb,sBAAsB,CAAC,CAAC;EACjC;EACArF,aAAaA,CAACE,KAAK,EAAE;IACjB,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACAJ,UAAUA,CAAC0D,GAAG,EAAE;IACZ,IAAI,CAACgB,aAAa,GAAG,IAAI,CAAC7D,YAAY;IACtC,IAAI,CAAC6E,oBAAoB,GAAG,IAAI;IAChC,IAAI,IAAI,CAAC7E,YAAY,YAAYvE,MAAM,EAAE;MACrC,MAAM0I,KAAK,GAAG7I,WAAW,CAACmK,IAAI,CAAC,IAAI,CAACzF,YAAY,EAAE6C,GAAG,CAAC;MACtD,IAAI,CAAC7C,YAAY,GAAGmE,KAAK;MACzB,IAAI,CAACH,UAAU,CAACsB,IAAI,CAAC,IAAI,CAACI,kBAAkB,CAACvB,KAAK,CAAC,CAAC;MACpD,IAAI,CAACoB,WAAW,CAACpB,KAAK,CAAC;IAC3B,CAAC,MACI;MACD,IAAI,CAACnE,YAAY,GAAG6C,GAAG;IAC3B;EACJ;EACA0C,WAAWA,CAACnD,KAAK,EAAE;IACf,IAAI,CAACpC,YAAY,GAAGoC,KAAK;IACzB,IAAI,CAAC2B,WAAW,CAACuB,IAAI,CAAClD,KAAK,CAAC;EAChC;EACA,IAAIgD,YAAYA,CAAA,EAAG;IACf,OAAQ,IAAI,CAAC/C,IAAI,IACb,IAAI,CAACwC,oBAAoB,KACxB,IAAI,CAAClD,KAAK,CAACgE,IAAI,CAAEtD,IAAI,IAAKxF,eAAe,CAAC,IAAI,CAACmD,YAAY,YAAYvE,MAAM,GACxE,IAAIH,WAAW,CAAC,IAAI,CAAC0E,YAAY,EAAE,IAAI,CAACA,YAAY,CAAC,GACrD,IAAI,CAACA,YAAY,EAAEqC,IAAI,CAAC8B,KAAK,EAAE,CAACyB,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACrC,IAAI,CAACuC,OAAO,CAACD,CAAC,CAACtC,IAAI,CAACiC,QAAQ,CAAC,IAAI,CAACtF,GAAG,EAAE,IAAI,CAACJ,GAAG,CAAC,CAAC,IAC9F8F,CAAC,CAACvB,EAAE,CAACyB,OAAO,CAACD,CAAC,CAACxB,EAAE,CAACmB,QAAQ,CAAC,IAAI,CAACtF,GAAG,EAAE,IAAI,CAACJ,GAAG,CAAC,CAAC,CAAC,CAAC,IACjD,IAAI,CAAC;EACjB;EACAoC,4BAA4BA,CAACC,mBAAmB,EAAEC,KAAK,EAAEP,SAAS,EAAE;IAChE,OAAOK,4BAA4B,CAACC,mBAAmB,EAAEC,KAAK,EAAEP,SAAS,CAAC;EAC9E;EACA6C,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAC1E,YAAY,YAAYvE,MAAM,EAAE;MACrC,IAAI,CAAC8D,KAAK,GAAG,IAAI,CAACS,YAAY;IAClC,CAAC,MACI,IAAI,IAAI,CAACA,YAAY,EAAE;MACxB,IAAI,CAACT,KAAK,GAAG,IAAI,CAACoC,KAAK,CAACoE,MAAM,GACxB,IAAI,CAAC/F,YAAY,CAACqE,EAAE,GACpB,IAAI,CAACrE,YAAY,CAACuD,IAAI;IAChC,CAAC,MACI,IAAI,IAAI,CAACzD,GAAG,IAAI,IAAI,CAACP,KAAK,CAACyG,gBAAgB,CAAC,IAAI,CAAClG,GAAG,CAAC,EAAE;MACxD,IAAI,CAACP,KAAK,GAAG,IAAI,CAACoC,KAAK,CAACoE,MAAM,GAAG,IAAI,CAACjG,GAAG,GAAG,IAAI,CAACA,GAAG,CAACR,MAAM,CAAC;QAAEC,KAAK,EAAE,CAAC;MAAE,CAAC,CAAC;IAC9E,CAAC,MACI,IAAI,IAAI,CAACW,GAAG,IAAI,IAAI,CAACX,KAAK,CAAC0G,iBAAiB,CAAC,IAAI,CAAC/F,GAAG,CAAC,EAAE;MACzD,IAAI,CAACX,KAAK,GAAG,IAAI,CAACW,GAAG;IACzB;EACJ;EACAwF,kBAAkBA,CAACQ,QAAQ,EAAE;IACzB,OAAO,IAAI,CAACvE,KAAK,CAACgE,IAAI,CAAEtD,IAAI,IAAK6D,QAAQ,CAACJ,OAAO,CAACzD,IAAI,CAAC8B,KAAK,CAAC,CAAC,IAAI,IAAI;EAC1E;EACA;IAAS,IAAI,CAACgC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyF5C,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAAC6C,IAAI,kBAD+EvK,EAAE,CAAAwK,iBAAA;MAAAC,IAAA,EACJ/C,gBAAgB;MAAAgD,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,8BAAA1I,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADdnC,EAAE,CAAAkD,UAAA,6BAAA4H,oDAAAnI,MAAA;YAAA,OACJP,GAAA,CAAA8G,KAAA,CAAAvG,MAAY,CAAC;UAAA,UADX3C,EAAE,CAAA+K,iBACW,CAAC;QAAA;QAAA,IAAA5I,EAAA;UADdnC,EAAE,CAAAgL,WAAA,YAAA5I,GAAA,CAAA8D,MACW,CAAC;QAAA;MAAA;MAAA+E,MAAA;QAAA7E,mBAAA;QAAAxC,aAAA;QAAAgC,KAAA;QAAAzB,GAAA;QAAAJ,GAAA;QAAA+B,SAAA;QAAA5B,SAAA;QAAAoC,IAAA;QAAAsC,WAAA,GADd5I,EAAE,CAAAkL,YAAA,CAAAC,IAAA;QAAA9G,kBAAA;MAAA;MAAA+G,OAAA;QAAApD,WAAA;QAAAC,UAAA;MAAA;MAAAoD,UAAA;MAAAC,QAAA,GAAFtL,EAAE,CAAAuL,kBAAA,CACghB,CAC3mBjK,cAAc,CAACoG,gBAAgB,CAAC,EAChC1G,+BAA+B,CAAC;QAAEwK,SAAS,EAAE;MAAK,CAAC,CAAC,CACvD,GAJ4FxL,EAAE,CAAAyL,oBAAA,EAAFzL,EAAE,CAAA0L,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAA5J,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA6J,GAAA,GAAFhM,EAAE,CAAAsC,gBAAA;UAAFtC,EAAE,CAAAuC,cAAA,qBAIkpB,CAAC;UAJrpBvC,EAAE,CAAAwC,MAAA;UAAFxC,EAAE,CAAAwC,MAAA;UAAFxC,EAAE,CAAAwC,MAAA;UAAFxC,EAAE,CAAAyC,gBAAA,+BAAAwJ,oEAAAtJ,MAAA;YAAF3C,EAAE,CAAA4C,aAAA,CAAAoJ,GAAA;YAAFhM,EAAE,CAAA+C,kBAAA,CAAAX,GAAA,CAAAY,WAAA,EAAAL,MAAA,MAAAP,GAAA,CAAAY,WAAA,GAAAL,MAAA;YAAA,OAAF3C,EAAE,CAAAiD,WAAA,CAAAN,MAAA;UAAA,CAI0jB,CAAC;UAJ7jB3C,EAAE,CAAAkD,UAAA,sBAAAgJ,2DAAAvJ,MAAA;YAAF3C,EAAE,CAAA4C,aAAA,CAAAoJ,GAAA;YAAA,OAAFhM,EAAE,CAAAiD,WAAA,CAI8kBb,GAAA,CAAAgB,UAAA,CAAAT,MAAiB,CAAC;UAAA,CAAC,CAAC,yBAAAwJ,8DAAAxJ,MAAA;YAJpmB3C,EAAE,CAAA4C,aAAA,CAAAoJ,GAAA;YAAA,OAAFhM,EAAE,CAAAiD,WAAA,CAIwnBb,GAAA,CAAAkB,aAAA,CAAAX,MAAoB,CAAC;UAAA,CAAC,CAAC;UAJjpB3C,EAAE,CAAAyD,YAAA,CAIkpB,CAAC;UAJrpBzD,EAAE,CAAAqF,UAAA,IAAAnD,wCAAA,0BAI8yC,CAAC,IAAA+D,uCAAA,gCAJjzCjG,EAAE,CAAAoM,sBAIs0C,CAAC;QAAA;QAAA,IAAAjK,EAAA;UAAA,MAAAkK,UAAA,GAJz0CrM,EAAE,CAAAsM,WAAA;UAAFtM,EAAE,CAAAgL,WAAA,gBAAA5I,GAAA,CAAA8D,MAIsD,CAAC;UAJzDlG,EAAE,CAAA0D,UAAA,wBAAAtB,GAAA,CAAAuB,6BAImH,CAAC,kBAAAvB,GAAA,CAAAwB,aAAsC,CAAC,QAJ7J5D,EAAE,CAAA6D,WAAA,QAAF7D,EAAE,CAAA8D,eAAA,KAAArC,GAAA,EAAAW,GAAA,CAAA2B,GAAA,EAAA3B,GAAA,CAAA4B,UAAA,EAAA5B,GAAA,CAAA6B,YAAA,EAAA7B,GAAA,CAAA8B,SAAA,EAIwO,CAAC,mBAAA9B,GAAA,CAAAwD,KAAA,CAAAoE,MAAA,IAAA5H,GAAA,CAAA8D,MAAA,UAJ3OlG,EAAE,CAAAoE,WAAA,QAAAhC,GAAA,CAAAiC,kBAAA,EAAAjC,GAAA,CAAAkC,WAAA,KAIqV,CAAC,QAJxVtE,EAAE,CAAA6D,WAAA,QAAF7D,EAAE,CAAA8D,eAAA,KAAAhC,GAAA,EAAAM,GAAA,CAAA+B,GAAA,EAAA/B,GAAA,CAAA4B,UAAA,EAAA5B,GAAA,CAAA6B,YAAA,EAAA7B,GAAA,CAAA8B,SAAA,EAIka,CAAC,UAAA9B,GAAA,CAAAiC,kBAAmC,CAAC,mBAAAjC,GAAA,CAAAwD,KAAA,CAAAoE,MAAA,IAAA5H,GAAA,CAAA8D,MAAgD,CAAC,UAAA9D,GAAA,CAAA6B,YAA6B,CAAC;UAJxhBjE,EAAE,CAAAuE,gBAAA,gBAAAnC,GAAA,CAAAY,WAI0jB,CAAC;UAJ7jBhD,EAAE,CAAAwF,SAAA,EAIysB,CAAC;UAJ5sBxF,EAAE,CAAA0D,UAAA,UAAAtB,GAAA,CAAAwD,KAAA,CAAAoE,MAAA,KAAA5H,GAAA,CAAA8D,MAIysB,CAAC,aAAAmG,UAAW,CAAC;QAAA;MAAA;MAAAE,YAAA,GAAurD1M,SAAS,EAA8CC,OAAO,EAAmHC,IAAI,EAA6FkB,WAAW,EAAwRC,EAAE,CAACsL,oBAAoB,EAA4FtL,EAAE,CAACuL,SAAS,EAA8JrL,OAAO,EAAgFT,aAAa;MAAA+L,MAAA;MAAAC,eAAA;IAAA,EAA6E;EAAE;AACz/G;AACA/M,UAAU,CAAC,CACPmB,OAAO,CACV,EAAE2G,gBAAgB,CAACkF,SAAS,EAAE,8BAA8B,EAAE,IAAI,CAAC;AACpE;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KATqG7M,EAAE,CAAA8M,iBAAA,CASXpF,gBAAgB,EAAc,CAAC;IAC/G+C,IAAI,EAAErK,SAAS;IACf2M,IAAI,EAAE,CAAC;MAAE1B,UAAU,EAAE,IAAI;MAAE2B,QAAQ,EAAE,oBAAoB;MAAEC,OAAO,EAAE,CAACpN,SAAS,EAAEC,OAAO,EAAEC,IAAI,EAAEkB,WAAW,EAAEE,WAAW,EAAEC,OAAO,EAAET,aAAa,CAAC;MAAEgM,eAAe,EAAEtM,uBAAuB,CAAC6M,MAAM;MAAEC,SAAS,EAAE,CAClM7L,cAAc,CAACoG,gBAAgB,CAAC,EAChC1G,+BAA+B,CAAC;QAAEwK,SAAS,EAAE;MAAK,CAAC,CAAC,CACvD;MAAE4B,IAAI,EAAE;QACL,iBAAiB,EAAE,QAAQ;QAC3B,4BAA4B,EAAE;MAClC,CAAC;MAAEtB,QAAQ,EAAE,6wEAA6wE;MAAEY,MAAM,EAAE,CAAC,4HAA4H;IAAE,CAAC;EACh7E,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEtG,mBAAmB,EAAE,CAAC;MAChFqE,IAAI,EAAEnK;IACV,CAAC,CAAC;IAAEsD,aAAa,EAAE,CAAC;MAChB6G,IAAI,EAAEnK;IACV,CAAC,CAAC;IAAEsF,KAAK,EAAE,CAAC;MACR6E,IAAI,EAAEnK;IACV,CAAC,CAAC;IAAE6D,GAAG,EAAE,CAAC;MACNsG,IAAI,EAAEnK;IACV,CAAC,CAAC;IAAEyD,GAAG,EAAE,CAAC;MACN0G,IAAI,EAAEnK;IACV,CAAC,CAAC;IAAEwF,SAAS,EAAE,CAAC;MACZ2E,IAAI,EAAEnK;IACV,CAAC,CAAC;IAAE4D,SAAS,EAAE,CAAC;MACZuG,IAAI,EAAEnK;IACV,CAAC,CAAC;IAAEgG,IAAI,EAAE,CAAC;MACPmE,IAAI,EAAEnK;IACV,CAAC,CAAC;IAAE0H,WAAW,EAAE,CAAC;MACdyC,IAAI,EAAElK;IACV,CAAC,CAAC;IAAE0H,UAAU,EAAE,CAAC;MACbwC,IAAI,EAAElK;IACV,CAAC,CAAC;IAAEqI,WAAW,EAAE,CAAC;MACd6B,IAAI,EAAEnK,KAAK;MACXyM,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE1I,kBAAkB,EAAE,CAAC;MACrBoG,IAAI,EAAEnK;IACV,CAAC,CAAC;IAAE6F,4BAA4B,EAAE;EAAG,CAAC;AAAA;AAElD,MAAMkH,iBAAiB,CAAC;EACpB1F,WAAWA,CAACS,KAAK,EAAEkF,IAAI,EAAEC,OAAO,EAAE;IAC9B,IAAI,CAACnF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACkF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACAjE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACgE,IAAI;EACpB;AACJ;AACA,SAASE,+BAA+BA,CAACC,YAAY,GAAG,CACpD,kBAAkB,EAClB,OAAO,EACP,WAAW,EACX,cAAc,EACd,eAAe,EACf,gBAAgB,CACnB,EAAE;EACC,MAAMC,KAAK,GAAGhO,MAAM,CAACqI,YAAY,CAAC,CAAC;EACnC,MAAM4F,SAAS,GAAGD,KAAK,CAACnK,MAAM,CAAC;IAAEuD,GAAG,EAAE,CAAC;EAAE,CAAC,CAAC;EAC3C,MAAM8G,WAAW,GAAGF,KAAK,CAACnK,MAAM,CAAC;IAAEuD,GAAG,EAAE,CAAC4G,KAAK,CAACG,SAAS,CAAC;EAAE,CAAC,CAAC;EAC7D,MAAMC,SAAS,GAAGF,WAAW,CAACrK,MAAM,CAAC;IAAEuD,GAAG,EAAE;EAAE,CAAC,CAAC;EAChD,MAAMiH,YAAY,GAAGL,KAAK,CAACnK,MAAM,CAAC;IAAEuD,GAAG,EAAE,CAAC,GAAG4G,KAAK,CAAC5G;EAAI,CAAC,CAAC;EACzD,MAAMkH,UAAU,GAAGD,YAAY,CAACxK,MAAM,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEsD,GAAG,EAAE,CAAC;EAAE,CAAC,CAAC;EAC7D,MAAMmH,gBAAgB,GAAGF,YAAY,CAACxK,MAAM,CAAC;IAAEC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC;EAC3D,OAAO,CACH,IAAI6J,iBAAiB,CAAC,IAAI9N,WAAW,CAACC,aAAa,EAAEkO,KAAK,CAAC,EAAED,YAAY,CAAC,CAAC,CAAC,CAAC,EAC7E,IAAIJ,iBAAiB,CAAC,IAAI9N,WAAW,CAACmO,KAAK,EAAEA,KAAK,CAAC,EAAED,YAAY,CAAC,CAAC,CAAC,CAAC,EACrE,IAAIJ,iBAAiB,CAAC,IAAI9N,WAAW,CAACoO,SAAS,EAAEA,SAAS,CAAC,EAAEF,YAAY,CAAC,CAAC,CAAC,CAAC,EAC7E,IAAIJ,iBAAiB,CAAC,IAAI9N,WAAW,CAACqO,WAAW,EAAEE,SAAS,CAAC,EAAEL,YAAY,CAAC,CAAC,CAAC,CAAC,EAC/E,IAAIJ,iBAAiB,CAAC,IAAI9N,WAAW,CAACwO,YAAY,EAAEC,UAAU,CAAC,EAAEP,YAAY,CAAC,CAAC,CAAC,CAAC,EACjF,IAAIJ,iBAAiB,CAAC,IAAI9N,WAAW,CAAC0O,gBAAgB,EAAEF,YAAY,CAACxK,MAAM,CAAC;IAAEuD,GAAG,EAAE,CAAC;EAAE,CAAC,CAAC,CAAC,EAAE2G,YAAY,CAAC,CAAC,CAAC,CAAC,CAC9G;AACL;;AAEA;AACA;AACA;;AAEA,SAAStG,mBAAmB,EAAEO,gBAAgB,EAAE2F,iBAAiB,EAAElH,4BAA4B,EAAEqH,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}