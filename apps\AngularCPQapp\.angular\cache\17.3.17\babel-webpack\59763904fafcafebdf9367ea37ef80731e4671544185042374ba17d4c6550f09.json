{"ast": null, "code": "import * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, signal, computed, ElementRef, TemplateRef, Component, ChangeDetectionStrategy, ViewChild, ViewChildren, Input, Output, forwardRef } from '@angular/core';\nimport { toSignal, toObservable } from '@angular/core/rxjs-interop';\nimport * as i4 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { maskitoTransform, maskitoInitialCalibrationPlugin } from '@maskito/core';\nimport { maskitoRemoveOnBlurPlugin } from '@maskito/kit';\nimport { maskitoGetCountryFromNumber, maskitoPhoneOptionsGenerator } from '@maskito/phone';\nimport { TuiControl, tuiAsControl } from '@taiga-ui/cdk/classes';\nimport { TUI_DEFAULT_MATCHER, CHAR_PLUS } from '@taiga-ui/cdk/constants';\nimport { tuiAutoFocusOptionsProvider, TuiAutoFocus } from '@taiga-ui/cdk/directives/auto-focus';\nimport { TUI_IS_IOS, tuiFallbackValueProvider } from '@taiga-ui/cdk/tokens';\nimport { tuiIsInputEvent } from '@taiga-ui/cdk/utils/dom';\nimport { tuiCreateToken, tuiProvideOptions, tuiDirectiveBinding } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i5 from '@taiga-ui/core/components/data-list';\nimport { TuiOption, TuiDataList } from '@taiga-ui/core/components/data-list';\nimport * as i7 from '@taiga-ui/core/components/textfield';\nimport { TUI_TEXTFIELD_OPTIONS, tuiTextfieldOptionsProvider, TuiTextfieldDropdownDirective, TuiTextfield } from '@taiga-ui/core/components/textfield';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { tuiDropdown, tuiDropdownOpen, TuiDropdownOpen, tuiDropdownOptionsProvider, TuiDropdownDirective, TuiWithDropdownOpen } from '@taiga-ui/core/directives/dropdown';\nimport * as i1 from '@taiga-ui/core/directives/group';\nimport { TuiGroup } from '@taiga-ui/core/directives/group';\nimport { TuiFlagPipe } from '@taiga-ui/core/pipes/flag';\nimport { TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { tuiIsEditingKey } from '@taiga-ui/core/utils/miscellaneous';\nimport { TuiChevron } from '@taiga-ui/kit/directives';\nimport { TUI_COUNTRIES, TUI_INTERNATIONAL_SEARCH } from '@taiga-ui/kit/tokens';\nimport { tuiGetCallingCode } from '@taiga-ui/kit/utils';\nimport { validatePhoneNumberLength } from 'libphonenumber-js';\nimport { getCountryCallingCode } from 'libphonenumber-js/core';\nimport { of, from, skip } from 'rxjs';\nimport * as i6 from '@taiga-ui/core/components/label';\nconst _c0 = [[[\"tui-icon\"], [\"img\"]], \"*\"];\nconst _c1 = [\"tui-icon, img\", \"*\"];\nconst _c2 = () => ({\n  standalone: true\n});\nfunction TuiInputPhoneInternational_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 6);\n    i0.ɵɵpipe(1, \"tuiFlag\");\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"alt\", (tmp_2_0 = ctx_r1.names()) == null ? null : tmp_2_0[ctx_r1.countryIsoCode()])(\"src\", i0.ɵɵpipeBind1(1, 2, ctx_r1.countryIsoCode()), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction TuiInputPhoneInternational_ng_container_9_tui_textfield_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tui-textfield\", 10)(1, \"input\", 11);\n    i0.ɵɵlistener(\"keydown.arrowDown\", function TuiInputPhoneInternational_ng_container_9_tui_textfield_1_Template_input_keydown_arrowDown_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.focusFirstItem());\n    })(\"ngModelChange\", function TuiInputPhoneInternational_ng_container_9_tui_textfield_1_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.search.set($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"iconStart\", ctx_r1.icons.search);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"focused\", true)(\"ngModel\", ctx_r1.search())(\"placeholder\", ctx_r1.label())(\"tuiAutoFocus\", !ctx_r1.isIos);\n  }\n}\nfunction TuiInputPhoneInternational_ng_container_9_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function TuiInputPhoneInternational_ng_container_9_button_3_Template_button_click_0_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onItemClick(item_r6.iso));\n    });\n    i0.ɵɵelement(1, \"img\", 13);\n    i0.ɵɵpipe(2, \"tuiFlag\");\n    i0.ɵɵelementStart(3, \"span\", 14);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 15);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 3, item_r6.iso), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.code);\n  }\n}\nfunction TuiInputPhoneInternational_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiInputPhoneInternational_ng_container_9_tui_textfield_1_Template, 2, 5, \"tui-textfield\", 7);\n    i0.ɵɵelementStart(2, \"tui-data-list\", 8);\n    i0.ɵɵlistener(\"keydown\", function TuiInputPhoneInternational_ng_container_9_Template_tui_data_list_keydown_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onKeyDown($event));\n    });\n    i0.ɵɵtemplate(3, TuiInputPhoneInternational_ng_container_9_button_3_Template, 7, 5, \"button\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.countrySearch);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filtered());\n  }\n}\nconst TUI_INPUT_PHONE_INTERNATIONAL_DEFAULT_OPTIONS = {\n  countries: [],\n  countryIsoCode: 'RU',\n  metadata: of({\n    countries: {},\n    country_calling_codes: {}\n  }),\n  separator: '-'\n};\n/**\n * Default parameters for input phone international component\n */\nconst TUI_INPUT_PHONE_INTERNATIONAL_OPTIONS = tuiCreateToken(TUI_INPUT_PHONE_INTERNATIONAL_DEFAULT_OPTIONS);\nfunction tuiInputPhoneInternationalOptionsProvider(options) {\n  return tuiProvideOptions(TUI_INPUT_PHONE_INTERNATIONAL_OPTIONS, options, TUI_INPUT_PHONE_INTERNATIONAL_DEFAULT_OPTIONS);\n}\nconst NOT_FORM_CONTROL_SYMBOLS = /[^+\\d]/g;\n/** @deprecated use version from experimental package instead */\nclass TuiInputPhoneInternational extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.isIos = inject(TUI_IS_IOS);\n    this.dropdown = tuiDropdown(null);\n    this.options = inject(TUI_INPUT_PHONE_INTERNATIONAL_OPTIONS);\n    this.size = inject(TUI_TEXTFIELD_OPTIONS).size;\n    this.open = tuiDropdownOpen();\n    this.names = toSignal(inject(TUI_COUNTRIES));\n    this.metadata = toSignal(from(this.options.metadata));\n    this.countries = signal(this.options.countries);\n    this.countryIsoCode = signal(this.options.countryIsoCode);\n    this.icons = inject(TUI_COMMON_ICONS);\n    this.label = toSignal(inject(TUI_INTERNATIONAL_SEARCH));\n    this.search = signal('');\n    this.separator = signal(this.options.separator);\n    this.filtered = computed(() => this.countries().map(iso => ({\n      iso,\n      name: this.names()?.[iso] || '',\n      code: tuiGetCallingCode(iso, this.metadata())\n    })).filter(({\n      name,\n      code\n    }) => TUI_DEFAULT_MATCHER(name + code, this.search())));\n    this.mask = computed(() => this.computeMask(this.countryIsoCode(), this.metadata(), this.separator()));\n    this.$ = tuiDirectiveBinding(TuiDropdownOpen, 'tuiDropdownEnabled', this.interactive);\n    this.textfieldValue = '';\n    this.countrySearch = false;\n    this.countryIsoCodeChange = toObservable(this.countryIsoCode).pipe(skip(1));\n  }\n  set countriesValue(value) {\n    this.countries.set(value);\n  }\n  set isoCode(code) {\n    this.countryIsoCode.set(code);\n  }\n  focusFirstItem() {\n    this.listOptions?.get(0)?.nativeElement.focus();\n  }\n  onPaste(event) {\n    const phonesMetadata = this.metadata();\n    if (!tuiIsInputEvent(event) || !phonesMetadata || !event.inputType.includes('Drop') && !event.inputType.includes('Paste')) {\n      return;\n    }\n    const newValue = event.data || '';\n    const prefixedValue = newValue.startsWith(CHAR_PLUS) ? newValue : CHAR_PLUS + newValue;\n    if (validatePhoneNumberLength(prefixedValue) === 'TOO_SHORT') {\n      return;\n    }\n    const countryIsoCode = maskitoGetCountryFromNumber(prefixedValue, phonesMetadata);\n    if (countryIsoCode) {\n      this.countryIsoCode.set(countryIsoCode);\n    }\n  }\n  onItemClick(isoCode) {\n    this.open.set(false);\n    this.countryIsoCode.set(isoCode);\n    this.input?.nativeElement.focus();\n  }\n  writeValue(unmaskedValue) {\n    super.writeValue(unmaskedValue);\n    const maskOptions = this.mask();\n    this.textfieldValue = maskOptions ? maskitoTransform(this.value(), maskOptions) : this.value(); // it will be calibrated later when mask is ready (by maskitoInitialCalibrationPlugin)\n    this.cdr.detectChanges();\n  }\n  set template(template) {\n    this.dropdown.set(template);\n  }\n  onFocus() {\n    const phoneMetadata = this.metadata();\n    if (!this.textfieldValue && phoneMetadata) {\n      this.textfieldValue = `${CHAR_PLUS + getCountryCallingCode(this.countryIsoCode(), phoneMetadata)} `;\n    }\n  }\n  onValueChange(maskedValue) {\n    const unmaskedValue = maskedValue.replaceAll(NOT_FORM_CONTROL_SYMBOLS, '');\n    const phonesMetadata = this.metadata();\n    const countryCallingCode = phonesMetadata ? CHAR_PLUS + getCountryCallingCode(this.countryIsoCode(), phonesMetadata) : '';\n    this.onChange(unmaskedValue === countryCallingCode ? '' : unmaskedValue);\n  }\n  onKeyDown({\n    key\n  }) {\n    if (tuiIsEditingKey(key)) {\n      this.filter?.nativeElement.focus({\n        preventScroll: true\n      });\n    }\n  }\n  computeMask(countryIsoCode, metadata, separator) {\n    if (!metadata) {\n      return null;\n    }\n    const {\n      plugins,\n      ...restOptions\n    } = maskitoPhoneOptionsGenerator({\n      countryIsoCode,\n      metadata,\n      separator\n    });\n    return {\n      ...restOptions,\n      plugins: [...plugins, maskitoRemoveOnBlurPlugin(`${CHAR_PLUS}${getCountryCallingCode(countryIsoCode, metadata)} `), maskitoInitialCalibrationPlugin()]\n    };\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiInputPhoneInternational_BaseFactory;\n      return function TuiInputPhoneInternational_Factory(t) {\n        return (ɵTuiInputPhoneInternational_BaseFactory || (ɵTuiInputPhoneInternational_BaseFactory = i0.ɵɵgetInheritedFactory(TuiInputPhoneInternational)))(t || TuiInputPhoneInternational);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiInputPhoneInternational,\n      selectors: [[\"tui-input-phone-international\"]],\n      viewQuery: function TuiInputPhoneInternational_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MaskitoDirective, 5, ElementRef);\n          i0.ɵɵviewQuery(TuiAutoFocus, 5, ElementRef);\n          i0.ɵɵviewQuery(TuiTextfieldDropdownDirective, 5, TemplateRef);\n          i0.ɵɵviewQuery(TuiOption, 5, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filter = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOptions = _t);\n        }\n      },\n      hostVars: 1,\n      hostBindings: function TuiInputPhoneInternational_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size());\n        }\n      },\n      inputs: {\n        countrySearch: \"countrySearch\",\n        countriesValue: [i0.ɵɵInputFlags.None, \"countries\", \"countriesValue\"],\n        isoCode: [i0.ɵɵInputFlags.None, \"countryIsoCode\", \"isoCode\"]\n      },\n      outputs: {\n        countryIsoCodeChange: \"countryIsoCodeChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsControl(TuiInputPhoneInternational), tuiFallbackValueProvider(''), tuiAutoFocusOptionsProvider({\n        preventScroll: true\n      }), tuiTextfieldOptionsProvider({\n        cleaner: signal(false)\n      }), tuiDropdownOptionsProvider({\n        limitWidth: 'fixed',\n        align: 'right'\n      })]), i0.ɵɵHostDirectivesFeature([i1.TuiGroup, i2.TuiDropdownDirective, i2.TuiWithDropdownOpen]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 10,\n      vars: 14,\n      consts: [[\"flag\", \"\"], [1, \"t-select\", 3, \"content\", \"tuiChevron\"], [\"aria-label\", \"Country\", \"ngModel\", \"\", \"tuiTextfield\", \"\", 3, \"disabled\", \"focused\", \"ngModelOptions\"], [\"autocomplete\", \"new-password\", \"tuiTextfield\", \"\", 3, \"ngModelChange\", \"beforeinput.capture\", \"blur\", \"focus\", \"disabled\", \"maskito\", \"ngModelOptions\", \"ngModel\"], [\"tuiLabel\", \"\"], [4, \"tuiTextfieldDropdown\"], [1, \"t-flag\", 3, \"alt\", \"src\"], [\"tuiTextfieldSize\", \"m\", \"class\", \"t-search\", 3, \"iconStart\", 4, \"ngIf\"], [3, \"keydown\"], [\"tuiOption\", \"\", \"type\", \"button\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"tuiTextfieldSize\", \"m\", 1, \"t-search\", 3, \"iconStart\"], [\"tuiTextfield\", \"\", 3, \"keydown.arrowDown\", \"ngModelChange\", \"focused\", \"ngModel\", \"placeholder\", \"tuiAutoFocus\"], [\"tuiOption\", \"\", \"type\", \"button\", 3, \"click\"], [\"alt\", \"\", 1, \"t-flag\", 3, \"src\"], [1, \"t-name\"], [1, \"t-code\"]],\n      template: function TuiInputPhoneInternational_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"tui-textfield\", 1);\n          i0.ɵɵelement(1, \"select\", 2);\n          i0.ɵɵtemplate(2, TuiInputPhoneInternational_ng_template_2_Template, 2, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"tui-textfield\")(5, \"input\", 3);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TuiInputPhoneInternational_Template_input_ngModelChange_5_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.textfieldValue, $event) || (ctx.textfieldValue = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"beforeinput.capture\", function TuiInputPhoneInternational_Template_input_beforeinput_capture_5_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPaste($event));\n          })(\"blur\", function TuiInputPhoneInternational_Template_input_blur_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouched());\n          })(\"focus\", function TuiInputPhoneInternational_Template_input_focus_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(!ctx.readOnly() && ctx.onFocus());\n          })(\"ngModelChange\", function TuiInputPhoneInternational_Template_input_ngModelChange_5_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onValueChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(6);\n          i0.ɵɵelementStart(7, \"label\", 4);\n          i0.ɵɵprojection(8, 1);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, TuiInputPhoneInternational_ng_container_9_Template, 4, 2, \"ng-container\", 5);\n        }\n        if (rf & 2) {\n          const flag_r7 = i0.ɵɵreference(3);\n          i0.ɵɵproperty(\"content\", flag_r7)(\"tuiChevron\", ctx.open());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.disabled())(\"focused\", ctx.open())(\"ngModelOptions\", i0.ɵɵpureFunction0(12, _c2));\n          i0.ɵɵattribute(\"data-mode\", ctx.mode());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled())(\"maskito\", ctx.mask())(\"ngModelOptions\", i0.ɵɵpureFunction0(13, _c2));\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.textfieldValue);\n          i0.ɵɵattribute(\"data-mode\", ctx.mode())(\"readonly\", ctx.readOnly() || null);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, FormsModule, i4.DefaultValueAccessor, i4.SelectControlValueAccessor, i4.NgControlStatus, i4.NgModel, MaskitoDirective, TuiAutoFocus, TuiChevron, i5.TuiDataListComponent, i5.TuiOption, TuiFlagPipe, i6.TuiLabel, i7.TuiSelect, i7.TuiTextfieldComponent, i7.TuiTextfieldDirective, i7.TuiTextfieldOptionsDirective, i7.TuiTextfieldDropdownDirective],\n      styles: [\".t-select[_ngcontent-%COMP%]{inline-size:5.625rem;flex:none}.t-select[data-size=m][_ngcontent-%COMP%]{inline-size:5rem}.t-select[data-size=s][_ngcontent-%COMP%]{inline-size:4rem}.t-flag[_ngcontent-%COMP%]{inline-size:1.75rem;block-size:1.75rem;border-radius:100%}.t-name[_ngcontent-%COMP%]{margin:0 auto 0 .75rem}.t-code[_ngcontent-%COMP%]{color:var(--tui-text-secondary);margin-inline-end:.25rem}.t-search[_ngcontent-%COMP%]{position:sticky;top:.375rem;background:var(--tui-background-elevation-3);box-shadow:0 -1rem var(--tui-background-elevation-3);margin:.375rem .375rem 0}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputPhoneInternational, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-input-phone-international',\n      imports: [CommonModule, FormsModule, MaskitoDirective, TuiAutoFocus, TuiChevron, TuiDataList, TuiFlagPipe, TuiTextfield],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAsControl(TuiInputPhoneInternational), tuiFallbackValueProvider(''), tuiAutoFocusOptionsProvider({\n        preventScroll: true\n      }), tuiTextfieldOptionsProvider({\n        cleaner: signal(false)\n      }), tuiDropdownOptionsProvider({\n        limitWidth: 'fixed',\n        align: 'right'\n      })],\n      hostDirectives: [TuiGroup, TuiDropdownDirective, TuiWithDropdownOpen],\n      host: {\n        '[attr.data-size]': 'size()'\n      },\n      template: \"<tui-textfield\\n    class=\\\"t-select\\\"\\n    [content]=\\\"flag\\\"\\n    [tuiChevron]=\\\"open()\\\"\\n>\\n    <select\\n        aria-label=\\\"Country\\\"\\n        ngModel=\\\"\\\"\\n        tuiTextfield\\n        [attr.data-mode]=\\\"mode()\\\"\\n        [disabled]=\\\"disabled()\\\"\\n        [focused]=\\\"open()\\\"\\n        [ngModelOptions]=\\\"{standalone: true}\\\"\\n    ></select>\\n\\n    <ng-template #flag>\\n        <img\\n            class=\\\"t-flag\\\"\\n            [alt]=\\\"names()?.[countryIsoCode()]\\\"\\n            [src]=\\\"countryIsoCode() | tuiFlag\\\"\\n        />\\n    </ng-template>\\n</tui-textfield>\\n\\n<tui-textfield>\\n    <!--TODO: Replace attribute bindings with inputs after Angular updated and signal bindings properly update-->\\n    <input\\n        autocomplete=\\\"new-password\\\"\\n        tuiTextfield\\n        [attr.data-mode]=\\\"mode()\\\"\\n        [attr.readonly]=\\\"readOnly() || null\\\"\\n        [disabled]=\\\"disabled()\\\"\\n        [maskito]=\\\"mask()\\\"\\n        [ngModelOptions]=\\\"{standalone: true}\\\"\\n        [(ngModel)]=\\\"textfieldValue\\\"\\n        (beforeinput.capture)=\\\"onPaste($event)\\\"\\n        (blur)=\\\"onTouched()\\\"\\n        (focus)=\\\"!readOnly() && onFocus()\\\"\\n        (ngModelChange)=\\\"onValueChange($event)\\\"\\n    />\\n\\n    <!--\\n    TODO: get rid of built-in input and label and just externalize everything in 5.0\\n    <tui-input-phone-international>\\n      <label tuiLabel>My label</label>\\n      <input tuiTextfield placeholder=\\\"My placeholder\\\" [(ngModel)]=\\\"value\\\" />\\n      <tui-icon icon=\\\"@tui.phone\\\" />\\n    </tui-input-phone-international>\\n    -->\\n    <ng-content select=\\\"tui-icon, img\\\" />\\n\\n    <label tuiLabel>\\n        <ng-content />\\n    </label>\\n</tui-textfield>\\n\\n<ng-container *tuiTextfieldDropdown>\\n    <tui-textfield\\n        *ngIf=\\\"countrySearch\\\"\\n        tuiTextfieldSize=\\\"m\\\"\\n        class=\\\"t-search\\\"\\n        [iconStart]=\\\"icons.search\\\"\\n    >\\n        <input\\n            tuiTextfield\\n            [focused]=\\\"true\\\"\\n            [ngModel]=\\\"search()\\\"\\n            [placeholder]=\\\"label()\\\"\\n            [tuiAutoFocus]=\\\"!isIos\\\"\\n            (keydown.arrowDown)=\\\"focusFirstItem()\\\"\\n            (ngModelChange)=\\\"search.set($event)\\\"\\n        />\\n    </tui-textfield>\\n\\n    <tui-data-list (keydown)=\\\"onKeyDown($event)\\\">\\n        <button\\n            *ngFor=\\\"let item of filtered()\\\"\\n            tuiOption\\n            type=\\\"button\\\"\\n            (click)=\\\"onItemClick(item.iso)\\\"\\n        >\\n            <img\\n                alt=\\\"\\\"\\n                class=\\\"t-flag\\\"\\n                [src]=\\\"item.iso | tuiFlag\\\"\\n            />\\n            <span class=\\\"t-name\\\">{{ item.name }}</span>\\n            <span class=\\\"t-code\\\">{{ item.code }}</span>\\n        </button>\\n    </tui-data-list>\\n</ng-container>\\n\",\n      styles: [\".t-select{inline-size:5.625rem;flex:none}.t-select[data-size=m]{inline-size:5rem}.t-select[data-size=s]{inline-size:4rem}.t-flag{inline-size:1.75rem;block-size:1.75rem;border-radius:100%}.t-name{margin:0 auto 0 .75rem}.t-code{color:var(--tui-text-secondary);margin-inline-end:.25rem}.t-search{position:sticky;top:.375rem;background:var(--tui-background-elevation-3);box-shadow:0 -1rem var(--tui-background-elevation-3);margin:.375rem .375rem 0}\\n\"]\n    }]\n  }], null, {\n    input: [{\n      type: ViewChild,\n      args: [MaskitoDirective, {\n        read: ElementRef\n      }]\n    }],\n    filter: [{\n      type: ViewChild,\n      args: [TuiAutoFocus, {\n        read: ElementRef\n      }]\n    }],\n    listOptions: [{\n      type: ViewChildren,\n      args: [TuiOption, {\n        read: ElementRef\n      }]\n    }],\n    countrySearch: [{\n      type: Input\n    }],\n    countryIsoCodeChange: [{\n      type: Output\n    }],\n    countriesValue: [{\n      type: Input,\n      args: ['countries']\n    }],\n    isoCode: [{\n      type: Input,\n      args: ['countryIsoCode']\n    }],\n    template: [{\n      type: ViewChild,\n      args: [forwardRef(() => TuiTextfieldDropdownDirective), {\n        read: TemplateRef\n      }]\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_PHONE_INTERNATIONAL_DEFAULT_OPTIONS, TUI_INPUT_PHONE_INTERNATIONAL_OPTIONS, TuiInputPhoneInternational, tuiInputPhoneInternationalOptionsProvider };", "map": {"version": 3, "names": ["i3", "CommonModule", "i0", "inject", "signal", "computed", "ElementRef", "TemplateRef", "Component", "ChangeDetectionStrategy", "ViewChild", "ViewChildren", "Input", "Output", "forwardRef", "toSignal", "toObservable", "i4", "FormsModule", "MaskitoDirective", "maskitoTransform", "maskitoInitialCalibrationPlugin", "maskitoRemoveOnBlurPlugin", "maskitoGetCountryFromNumber", "maskitoPhoneOptionsGenerator", "TuiControl", "tuiAsControl", "TUI_DEFAULT_MATCHER", "CHAR_PLUS", "tuiAutoFocusOptionsProvider", "TuiAutoFocus", "TUI_IS_IOS", "tuiFallback<PERSON><PERSON>ue<PERSON>", "tuiIsInputEvent", "tuiCreateToken", "tuiProvideOptions", "tuiDirectiveBinding", "i5", "TuiOption", "TuiDataList", "i7", "TUI_TEXTFIELD_OPTIONS", "tuiTextfieldOptionsProvider", "TuiTextfieldDropdownDirective", "TuiTextfield", "i2", "tuiDropdown", "tuiDropdownOpen", "TuiDropdownOpen", "tuiDropdownOptionsProvider", "TuiDropdownDirective", "TuiWithDropdownOpen", "i1", "TuiGroup", "TuiFlagPipe", "TUI_COMMON_ICONS", "tuiIsEditingKey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TUI_COUNTRIES", "TUI_INTERNATIONAL_SEARCH", "tuiGetCallingCode", "validatePhoneNumberLength", "getCountryCallingCode", "of", "from", "skip", "i6", "_c0", "_c1", "_c2", "standalone", "TuiInputPhoneInternational_ng_template_2_Template", "rf", "ctx", "ɵɵelement", "ɵɵpipe", "tmp_2_0", "ctx_r1", "ɵɵnextContext", "ɵɵproperty", "names", "countryIsoCode", "ɵɵpipeBind1", "ɵɵsanitizeUrl", "TuiInputPhoneInternational_ng_container_9_tui_textfield_1_Template", "_r4", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiInputPhoneInternational_ng_container_9_tui_textfield_1_Template_input_keydown_arrowDown_1_listener", "ɵɵrestoreView", "ɵɵresetView", "focusFirstItem", "TuiInputPhoneInternational_ng_container_9_tui_textfield_1_Template_input_ngModelChange_1_listener", "$event", "search", "set", "ɵɵelementEnd", "icons", "ɵɵadvance", "label", "isIos", "TuiInputPhoneInternational_ng_container_9_button_3_Template", "_r5", "TuiInputPhoneInternational_ng_container_9_button_3_Template_button_click_0_listener", "item_r6", "$implicit", "onItemClick", "iso", "ɵɵtext", "ɵɵtextInterpolate", "name", "code", "TuiInputPhoneInternational_ng_container_9_Template", "_r3", "ɵɵelementContainerStart", "ɵɵtemplate", "TuiInputPhoneInternational_ng_container_9_Template_tui_data_list_keydown_2_listener", "onKeyDown", "ɵɵelementContainerEnd", "countrySearch", "filtered", "TUI_INPUT_PHONE_INTERNATIONAL_DEFAULT_OPTIONS", "countries", "metadata", "country_calling_codes", "separator", "TUI_INPUT_PHONE_INTERNATIONAL_OPTIONS", "tuiInputPhoneInternationalOptionsProvider", "options", "NOT_FORM_CONTROL_SYMBOLS", "TuiInputPhoneInternational", "constructor", "arguments", "dropdown", "size", "open", "map", "filter", "mask", "computeMask", "$", "interactive", "textfieldValue", "countryIsoCodeChange", "pipe", "countriesValue", "value", "isoCode", "listOptions", "get", "nativeElement", "focus", "onPaste", "event", "phonesMetadata", "inputType", "includes", "newValue", "data", "prefixedValue", "startsWith", "input", "writeValue", "unmasked<PERSON><PERSON>ue", "maskOptions", "cdr", "detectChanges", "template", "onFocus", "phoneMetadata", "onValueChange", "maskedValue", "replaceAll", "countryCallingCode", "onChange", "key", "preventScroll", "plugins", "restOptions", "ɵfac", "ɵTuiInputPhoneInternational_BaseFactory", "TuiInputPhoneInternational_Factory", "t", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "TuiInputPhoneInternational_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostVars", "hostBindings", "TuiInputPhoneInternational_HostBindings", "ɵɵattribute", "inputs", "ɵɵInputFlags", "None", "outputs", "features", "ɵɵProvidersFeature", "cleaner", "limitWidth", "align", "ɵɵHostDirectivesFeature", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "TuiInputPhoneInternational_Template", "_r1", "ɵɵprojectionDef", "ɵɵtemplateRefExtractor", "ɵɵtwoWayListener", "TuiInputPhoneInternational_Template_input_ngModelChange_5_listener", "ɵɵtwoWayBindingSet", "TuiInputPhoneInternational_Template_input_beforeinput_capture_5_listener", "TuiInputPhoneInternational_Template_input_blur_5_listener", "onTouched", "TuiInputPhoneInternational_Template_input_focus_5_listener", "readOnly", "ɵɵprojection", "flag_r7", "ɵɵreference", "disabled", "ɵɵpureFunction0", "mode", "ɵɵtwoWayProperty", "dependencies", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DefaultValueAccessor", "SelectControlValueAccessor", "NgControlStatus", "NgModel", "TuiDataListComponent", "Tui<PERSON><PERSON>l", "TuiSelect", "TuiTextfieldComponent", "TuiTextfieldDirective", "TuiTextfieldOptionsDirective", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "hostDirectives", "host", "read"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-input-phone-international.mjs"], "sourcesContent": ["import * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, signal, computed, ElementRef, TemplateRef, Component, ChangeDetectionStrategy, ViewChild, ViewChildren, Input, Output, forwardRef } from '@angular/core';\nimport { toSignal, toObservable } from '@angular/core/rxjs-interop';\nimport * as i4 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { maskitoTransform, maskitoInitialCalibrationPlugin } from '@maskito/core';\nimport { maskitoRemoveOnBlurPlugin } from '@maskito/kit';\nimport { maskitoGetCountryFromNumber, maskitoPhoneOptionsGenerator } from '@maskito/phone';\nimport { TuiControl, tuiAsControl } from '@taiga-ui/cdk/classes';\nimport { TUI_DEFAULT_MATCHER, CHAR_PLUS } from '@taiga-ui/cdk/constants';\nimport { tuiAutoFocusOptionsProvider, TuiAutoFocus } from '@taiga-ui/cdk/directives/auto-focus';\nimport { TUI_IS_IOS, tuiFallbackValueProvider } from '@taiga-ui/cdk/tokens';\nimport { tuiIsInputEvent } from '@taiga-ui/cdk/utils/dom';\nimport { tuiCreateToken, tuiProvideOptions, tuiDirectiveBinding } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i5 from '@taiga-ui/core/components/data-list';\nimport { TuiOption, TuiDataList } from '@taiga-ui/core/components/data-list';\nimport * as i7 from '@taiga-ui/core/components/textfield';\nimport { TUI_TEXTFIELD_OPTIONS, tuiTextfieldOptionsProvider, TuiTextfieldDropdownDirective, TuiTextfield } from '@taiga-ui/core/components/textfield';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { tuiDropdown, tuiDropdownOpen, TuiDropdownOpen, tuiDropdownOptionsProvider, TuiDropdownDirective, TuiWithDropdownOpen } from '@taiga-ui/core/directives/dropdown';\nimport * as i1 from '@taiga-ui/core/directives/group';\nimport { TuiGroup } from '@taiga-ui/core/directives/group';\nimport { TuiFlagPipe } from '@taiga-ui/core/pipes/flag';\nimport { TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { tuiIsEditingKey } from '@taiga-ui/core/utils/miscellaneous';\nimport { TuiChevron } from '@taiga-ui/kit/directives';\nimport { TUI_COUNTRIES, TUI_INTERNATIONAL_SEARCH } from '@taiga-ui/kit/tokens';\nimport { tuiGetCallingCode } from '@taiga-ui/kit/utils';\nimport { validatePhoneNumberLength } from 'libphonenumber-js';\nimport { getCountryCallingCode } from 'libphonenumber-js/core';\nimport { of, from, skip } from 'rxjs';\nimport * as i6 from '@taiga-ui/core/components/label';\n\nconst TUI_INPUT_PHONE_INTERNATIONAL_DEFAULT_OPTIONS = {\n    countries: [],\n    countryIsoCode: 'RU',\n    metadata: of({ countries: {}, country_calling_codes: {} }),\n    separator: '-',\n};\n/**\n * Default parameters for input phone international component\n */\nconst TUI_INPUT_PHONE_INTERNATIONAL_OPTIONS = tuiCreateToken(TUI_INPUT_PHONE_INTERNATIONAL_DEFAULT_OPTIONS);\nfunction tuiInputPhoneInternationalOptionsProvider(options) {\n    return tuiProvideOptions(TUI_INPUT_PHONE_INTERNATIONAL_OPTIONS, options, TUI_INPUT_PHONE_INTERNATIONAL_DEFAULT_OPTIONS);\n}\n\nconst NOT_FORM_CONTROL_SYMBOLS = /[^+\\d]/g;\n/** @deprecated use version from experimental package instead */\nclass TuiInputPhoneInternational extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.isIos = inject(TUI_IS_IOS);\n        this.dropdown = tuiDropdown(null);\n        this.options = inject(TUI_INPUT_PHONE_INTERNATIONAL_OPTIONS);\n        this.size = inject(TUI_TEXTFIELD_OPTIONS).size;\n        this.open = tuiDropdownOpen();\n        this.names = toSignal(inject(TUI_COUNTRIES));\n        this.metadata = toSignal(from(this.options.metadata));\n        this.countries = signal(this.options.countries);\n        this.countryIsoCode = signal(this.options.countryIsoCode);\n        this.icons = inject(TUI_COMMON_ICONS);\n        this.label = toSignal(inject(TUI_INTERNATIONAL_SEARCH));\n        this.search = signal('');\n        this.separator = signal(this.options.separator);\n        this.filtered = computed(() => this.countries()\n            .map((iso) => ({\n            iso,\n            name: this.names()?.[iso] || '',\n            code: tuiGetCallingCode(iso, this.metadata()),\n        }))\n            .filter(({ name, code }) => TUI_DEFAULT_MATCHER(name + code, this.search())));\n        this.mask = computed(() => this.computeMask(this.countryIsoCode(), this.metadata(), this.separator()));\n        this.$ = tuiDirectiveBinding(TuiDropdownOpen, 'tuiDropdownEnabled', this.interactive);\n        this.textfieldValue = '';\n        this.countrySearch = false;\n        this.countryIsoCodeChange = toObservable(this.countryIsoCode).pipe(skip(1));\n    }\n    set countriesValue(value) {\n        this.countries.set(value);\n    }\n    set isoCode(code) {\n        this.countryIsoCode.set(code);\n    }\n    focusFirstItem() {\n        this.listOptions?.get(0)?.nativeElement.focus();\n    }\n    onPaste(event) {\n        const phonesMetadata = this.metadata();\n        if (!tuiIsInputEvent(event) ||\n            !phonesMetadata ||\n            (!event.inputType.includes('Drop') && !event.inputType.includes('Paste'))) {\n            return;\n        }\n        const newValue = event.data || '';\n        const prefixedValue = newValue.startsWith(CHAR_PLUS)\n            ? newValue\n            : CHAR_PLUS + newValue;\n        if (validatePhoneNumberLength(prefixedValue) === 'TOO_SHORT') {\n            return;\n        }\n        const countryIsoCode = maskitoGetCountryFromNumber(prefixedValue, phonesMetadata);\n        if (countryIsoCode) {\n            this.countryIsoCode.set(countryIsoCode);\n        }\n    }\n    onItemClick(isoCode) {\n        this.open.set(false);\n        this.countryIsoCode.set(isoCode);\n        this.input?.nativeElement.focus();\n    }\n    writeValue(unmaskedValue) {\n        super.writeValue(unmaskedValue);\n        const maskOptions = this.mask();\n        this.textfieldValue = maskOptions\n            ? maskitoTransform(this.value(), maskOptions)\n            : this.value(); // it will be calibrated later when mask is ready (by maskitoInitialCalibrationPlugin)\n        this.cdr.detectChanges();\n    }\n    set template(template) {\n        this.dropdown.set(template);\n    }\n    onFocus() {\n        const phoneMetadata = this.metadata();\n        if (!this.textfieldValue && phoneMetadata) {\n            this.textfieldValue = `${CHAR_PLUS + getCountryCallingCode(this.countryIsoCode(), phoneMetadata)} `;\n        }\n    }\n    onValueChange(maskedValue) {\n        const unmaskedValue = maskedValue.replaceAll(NOT_FORM_CONTROL_SYMBOLS, '');\n        const phonesMetadata = this.metadata();\n        const countryCallingCode = phonesMetadata\n            ? CHAR_PLUS + getCountryCallingCode(this.countryIsoCode(), phonesMetadata)\n            : '';\n        this.onChange(unmaskedValue === countryCallingCode ? '' : unmaskedValue);\n    }\n    onKeyDown({ key }) {\n        if (tuiIsEditingKey(key)) {\n            this.filter?.nativeElement.focus({ preventScroll: true });\n        }\n    }\n    computeMask(countryIsoCode, metadata, separator) {\n        if (!metadata) {\n            return null;\n        }\n        const { plugins, ...restOptions } = maskitoPhoneOptionsGenerator({\n            countryIsoCode,\n            metadata,\n            separator,\n        });\n        return {\n            ...restOptions,\n            plugins: [\n                ...plugins,\n                maskitoRemoveOnBlurPlugin(`${CHAR_PLUS}${getCountryCallingCode(countryIsoCode, metadata)} `),\n                maskitoInitialCalibrationPlugin(),\n            ],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputPhoneInternational, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputPhoneInternational, isStandalone: true, selector: \"tui-input-phone-international\", inputs: { countrySearch: \"countrySearch\", countriesValue: [\"countries\", \"countriesValue\"], isoCode: [\"countryIsoCode\", \"isoCode\"] }, outputs: { countryIsoCodeChange: \"countryIsoCodeChange\" }, host: { properties: { \"attr.data-size\": \"size()\" } }, providers: [\n            tuiAsControl(TuiInputPhoneInternational),\n            tuiFallbackValueProvider(''),\n            tuiAutoFocusOptionsProvider({ preventScroll: true }),\n            tuiTextfieldOptionsProvider({ cleaner: signal(false) }),\n            tuiDropdownOptionsProvider({\n                limitWidth: 'fixed',\n                align: 'right',\n            }),\n        ], viewQueries: [{ propertyName: \"input\", first: true, predicate: MaskitoDirective, descendants: true, read: ElementRef }, { propertyName: \"filter\", first: true, predicate: TuiAutoFocus, descendants: true, read: ElementRef }, { propertyName: \"template\", first: true, predicate: i0.forwardRef(function () { return TuiTextfieldDropdownDirective; }), descendants: true, read: TemplateRef }, { propertyName: \"listOptions\", predicate: TuiOption, descendants: true, read: ElementRef }], usesInheritance: true, hostDirectives: [{ directive: i1.TuiGroup }, { directive: i2.TuiDropdownDirective }, { directive: i2.TuiWithDropdownOpen }], ngImport: i0, template: \"<tui-textfield\\n    class=\\\"t-select\\\"\\n    [content]=\\\"flag\\\"\\n    [tuiChevron]=\\\"open()\\\"\\n>\\n    <select\\n        aria-label=\\\"Country\\\"\\n        ngModel=\\\"\\\"\\n        tuiTextfield\\n        [attr.data-mode]=\\\"mode()\\\"\\n        [disabled]=\\\"disabled()\\\"\\n        [focused]=\\\"open()\\\"\\n        [ngModelOptions]=\\\"{standalone: true}\\\"\\n    ></select>\\n\\n    <ng-template #flag>\\n        <img\\n            class=\\\"t-flag\\\"\\n            [alt]=\\\"names()?.[countryIsoCode()]\\\"\\n            [src]=\\\"countryIsoCode() | tuiFlag\\\"\\n        />\\n    </ng-template>\\n</tui-textfield>\\n\\n<tui-textfield>\\n    <!--TODO: Replace attribute bindings with inputs after Angular updated and signal bindings properly update-->\\n    <input\\n        autocomplete=\\\"new-password\\\"\\n        tuiTextfield\\n        [attr.data-mode]=\\\"mode()\\\"\\n        [attr.readonly]=\\\"readOnly() || null\\\"\\n        [disabled]=\\\"disabled()\\\"\\n        [maskito]=\\\"mask()\\\"\\n        [ngModelOptions]=\\\"{standalone: true}\\\"\\n        [(ngModel)]=\\\"textfieldValue\\\"\\n        (beforeinput.capture)=\\\"onPaste($event)\\\"\\n        (blur)=\\\"onTouched()\\\"\\n        (focus)=\\\"!readOnly() && onFocus()\\\"\\n        (ngModelChange)=\\\"onValueChange($event)\\\"\\n    />\\n\\n    <!--\\n    TODO: get rid of built-in input and label and just externalize everything in 5.0\\n    <tui-input-phone-international>\\n      <label tuiLabel>My label</label>\\n      <input tuiTextfield placeholder=\\\"My placeholder\\\" [(ngModel)]=\\\"value\\\" />\\n      <tui-icon icon=\\\"@tui.phone\\\" />\\n    </tui-input-phone-international>\\n    -->\\n    <ng-content select=\\\"tui-icon, img\\\" />\\n\\n    <label tuiLabel>\\n        <ng-content />\\n    </label>\\n</tui-textfield>\\n\\n<ng-container *tuiTextfieldDropdown>\\n    <tui-textfield\\n        *ngIf=\\\"countrySearch\\\"\\n        tuiTextfieldSize=\\\"m\\\"\\n        class=\\\"t-search\\\"\\n        [iconStart]=\\\"icons.search\\\"\\n    >\\n        <input\\n            tuiTextfield\\n            [focused]=\\\"true\\\"\\n            [ngModel]=\\\"search()\\\"\\n            [placeholder]=\\\"label()\\\"\\n            [tuiAutoFocus]=\\\"!isIos\\\"\\n            (keydown.arrowDown)=\\\"focusFirstItem()\\\"\\n            (ngModelChange)=\\\"search.set($event)\\\"\\n        />\\n    </tui-textfield>\\n\\n    <tui-data-list (keydown)=\\\"onKeyDown($event)\\\">\\n        <button\\n            *ngFor=\\\"let item of filtered()\\\"\\n            tuiOption\\n            type=\\\"button\\\"\\n            (click)=\\\"onItemClick(item.iso)\\\"\\n        >\\n            <img\\n                alt=\\\"\\\"\\n                class=\\\"t-flag\\\"\\n                [src]=\\\"item.iso | tuiFlag\\\"\\n            />\\n            <span class=\\\"t-name\\\">{{ item.name }}</span>\\n            <span class=\\\"t-code\\\">{{ item.code }}</span>\\n        </button>\\n    </tui-data-list>\\n</ng-container>\\n\", styles: [\".t-select{inline-size:5.625rem;flex:none}.t-select[data-size=m]{inline-size:5rem}.t-select[data-size=s]{inline-size:4rem}.t-flag{inline-size:1.75rem;block-size:1.75rem;border-radius:100%}.t-name{margin:0 auto 0 .75rem}.t-code{color:var(--tui-text-secondary);margin-inline-end:.25rem}.t-search{position:sticky;top:.375rem;background:var(--tui-background-elevation-3);box-shadow:0 -1rem var(--tui-background-elevation-3);margin:.375rem .375rem 0}\\n\"], dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i3.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"ngmodule\", type: FormsModule }, { kind: \"directive\", type: i4.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { kind: \"directive\", type: i4.SelectControlValueAccessor, selector: \"select:not([multiple])[formControlName],select:not([multiple])[formControl],select:not([multiple])[ngModel]\", inputs: [\"compareWith\"] }, { kind: \"directive\", type: i4.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i4.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }, { kind: \"directive\", type: MaskitoDirective, selector: \"[maskito]\", inputs: [\"maskito\", \"maskitoElement\"] }, { kind: \"directive\", type: TuiAutoFocus, selector: \"[tuiAutoFocus]\", inputs: [\"tuiAutoFocus\"] }, { kind: \"directive\", type: TuiChevron, selector: \"[tuiChevron]\", inputs: [\"tuiChevron\"] }, { kind: \"component\", type: i5.TuiDataListComponent, selector: \"tui-data-list\", inputs: [\"emptyContent\", \"size\"] }, { kind: \"component\", type: i5.TuiOption, selector: \"button[tuiOption]:not([new]), a[tuiOption]:not([new]), label[tuiOption]:not([new])\", inputs: [\"disabled\", \"value\"] }, { kind: \"pipe\", type: TuiFlagPipe, name: \"tuiFlag\" }, { kind: \"directive\", type: i6.TuiLabel, selector: \"label[tuiLabel]\" }, { kind: \"component\", type: i7.TuiSelect, selector: \"select[tuiTextfield]\", inputs: [\"placeholder\"] }, { kind: \"component\", type: i7.TuiTextfieldComponent, selector: \"tui-textfield:not([multi])\" }, { kind: \"directive\", type: i7.TuiTextfieldDirective, selector: \"input[tuiTextfield]:not([tuiInputCard]):not([tuiInputExpire]):not([tuiInputCVC])\" }, { kind: \"directive\", type: i7.TuiTextfieldOptionsDirective, selector: \"[tuiTextfieldAppearance],[tuiTextfieldSize],[tuiTextfieldCleaner]\", inputs: [\"tuiTextfieldAppearance\", \"tuiTextfieldSize\", \"tuiTextfieldCleaner\"] }, { kind: \"directive\", type: i7.TuiTextfieldDropdownDirective, selector: \"ng-template[tuiTextfieldDropdown]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputPhoneInternational, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-input-phone-international', imports: [\n                        CommonModule,\n                        FormsModule,\n                        MaskitoDirective,\n                        TuiAutoFocus,\n                        TuiChevron,\n                        TuiDataList,\n                        TuiFlagPipe,\n                        TuiTextfield,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        tuiAsControl(TuiInputPhoneInternational),\n                        tuiFallbackValueProvider(''),\n                        tuiAutoFocusOptionsProvider({ preventScroll: true }),\n                        tuiTextfieldOptionsProvider({ cleaner: signal(false) }),\n                        tuiDropdownOptionsProvider({\n                            limitWidth: 'fixed',\n                            align: 'right',\n                        }),\n                    ], hostDirectives: [TuiGroup, TuiDropdownDirective, TuiWithDropdownOpen], host: {\n                        '[attr.data-size]': 'size()',\n                    }, template: \"<tui-textfield\\n    class=\\\"t-select\\\"\\n    [content]=\\\"flag\\\"\\n    [tuiChevron]=\\\"open()\\\"\\n>\\n    <select\\n        aria-label=\\\"Country\\\"\\n        ngModel=\\\"\\\"\\n        tuiTextfield\\n        [attr.data-mode]=\\\"mode()\\\"\\n        [disabled]=\\\"disabled()\\\"\\n        [focused]=\\\"open()\\\"\\n        [ngModelOptions]=\\\"{standalone: true}\\\"\\n    ></select>\\n\\n    <ng-template #flag>\\n        <img\\n            class=\\\"t-flag\\\"\\n            [alt]=\\\"names()?.[countryIsoCode()]\\\"\\n            [src]=\\\"countryIsoCode() | tuiFlag\\\"\\n        />\\n    </ng-template>\\n</tui-textfield>\\n\\n<tui-textfield>\\n    <!--TODO: Replace attribute bindings with inputs after Angular updated and signal bindings properly update-->\\n    <input\\n        autocomplete=\\\"new-password\\\"\\n        tuiTextfield\\n        [attr.data-mode]=\\\"mode()\\\"\\n        [attr.readonly]=\\\"readOnly() || null\\\"\\n        [disabled]=\\\"disabled()\\\"\\n        [maskito]=\\\"mask()\\\"\\n        [ngModelOptions]=\\\"{standalone: true}\\\"\\n        [(ngModel)]=\\\"textfieldValue\\\"\\n        (beforeinput.capture)=\\\"onPaste($event)\\\"\\n        (blur)=\\\"onTouched()\\\"\\n        (focus)=\\\"!readOnly() && onFocus()\\\"\\n        (ngModelChange)=\\\"onValueChange($event)\\\"\\n    />\\n\\n    <!--\\n    TODO: get rid of built-in input and label and just externalize everything in 5.0\\n    <tui-input-phone-international>\\n      <label tuiLabel>My label</label>\\n      <input tuiTextfield placeholder=\\\"My placeholder\\\" [(ngModel)]=\\\"value\\\" />\\n      <tui-icon icon=\\\"@tui.phone\\\" />\\n    </tui-input-phone-international>\\n    -->\\n    <ng-content select=\\\"tui-icon, img\\\" />\\n\\n    <label tuiLabel>\\n        <ng-content />\\n    </label>\\n</tui-textfield>\\n\\n<ng-container *tuiTextfieldDropdown>\\n    <tui-textfield\\n        *ngIf=\\\"countrySearch\\\"\\n        tuiTextfieldSize=\\\"m\\\"\\n        class=\\\"t-search\\\"\\n        [iconStart]=\\\"icons.search\\\"\\n    >\\n        <input\\n            tuiTextfield\\n            [focused]=\\\"true\\\"\\n            [ngModel]=\\\"search()\\\"\\n            [placeholder]=\\\"label()\\\"\\n            [tuiAutoFocus]=\\\"!isIos\\\"\\n            (keydown.arrowDown)=\\\"focusFirstItem()\\\"\\n            (ngModelChange)=\\\"search.set($event)\\\"\\n        />\\n    </tui-textfield>\\n\\n    <tui-data-list (keydown)=\\\"onKeyDown($event)\\\">\\n        <button\\n            *ngFor=\\\"let item of filtered()\\\"\\n            tuiOption\\n            type=\\\"button\\\"\\n            (click)=\\\"onItemClick(item.iso)\\\"\\n        >\\n            <img\\n                alt=\\\"\\\"\\n                class=\\\"t-flag\\\"\\n                [src]=\\\"item.iso | tuiFlag\\\"\\n            />\\n            <span class=\\\"t-name\\\">{{ item.name }}</span>\\n            <span class=\\\"t-code\\\">{{ item.code }}</span>\\n        </button>\\n    </tui-data-list>\\n</ng-container>\\n\", styles: [\".t-select{inline-size:5.625rem;flex:none}.t-select[data-size=m]{inline-size:5rem}.t-select[data-size=s]{inline-size:4rem}.t-flag{inline-size:1.75rem;block-size:1.75rem;border-radius:100%}.t-name{margin:0 auto 0 .75rem}.t-code{color:var(--tui-text-secondary);margin-inline-end:.25rem}.t-search{position:sticky;top:.375rem;background:var(--tui-background-elevation-3);box-shadow:0 -1rem var(--tui-background-elevation-3);margin:.375rem .375rem 0}\\n\"] }]\n        }], propDecorators: { input: [{\n                type: ViewChild,\n                args: [MaskitoDirective, { read: ElementRef }]\n            }], filter: [{\n                type: ViewChild,\n                args: [TuiAutoFocus, { read: ElementRef }]\n            }], listOptions: [{\n                type: ViewChildren,\n                args: [TuiOption, { read: ElementRef }]\n            }], countrySearch: [{\n                type: Input\n            }], countryIsoCodeChange: [{\n                type: Output\n            }], countriesValue: [{\n                type: Input,\n                args: ['countries']\n            }], isoCode: [{\n                type: Input,\n                args: ['countryIsoCode']\n            }], template: [{\n                type: ViewChild,\n                args: [forwardRef(() => TuiTextfieldDropdownDirective), { read: TemplateRef }]\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_PHONE_INTERNATIONAL_DEFAULT_OPTIONS, TUI_INPUT_PHONE_INTERNATIONAL_OPTIONS, TuiInputPhoneInternational, tuiInputPhoneInternationalOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,QAAQ,eAAe;AACzK,SAASC,QAAQ,EAAEC,YAAY,QAAQ,4BAA4B;AACnE,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,gBAAgB,EAAEC,+BAA+B,QAAQ,eAAe;AACjF,SAASC,yBAAyB,QAAQ,cAAc;AACxD,SAASC,2BAA2B,EAAEC,4BAA4B,QAAQ,gBAAgB;AAC1F,SAASC,UAAU,EAAEC,YAAY,QAAQ,uBAAuB;AAChE,SAASC,mBAAmB,EAAEC,SAAS,QAAQ,yBAAyB;AACxE,SAASC,2BAA2B,EAAEC,YAAY,QAAQ,qCAAqC;AAC/F,SAASC,UAAU,EAAEC,wBAAwB,QAAQ,sBAAsB;AAC3E,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,mBAAmB,QAAQ,mCAAmC;AAC1G,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,SAAS,EAAEC,WAAW,QAAQ,qCAAqC;AAC5E,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,qBAAqB,EAAEC,2BAA2B,EAAEC,6BAA6B,EAAEC,YAAY,QAAQ,qCAAqC;AACrJ,OAAO,KAAKC,EAAE,MAAM,oCAAoC;AACxD,SAASC,WAAW,EAAEC,eAAe,EAAEC,eAAe,EAAEC,0BAA0B,EAAEC,oBAAoB,EAAEC,mBAAmB,QAAQ,oCAAoC;AACzK,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,QAAQ,QAAQ,iCAAiC;AAC1D,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,aAAa,EAAEC,wBAAwB,QAAQ,sBAAsB;AAC9E,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,yBAAyB,QAAQ,mBAAmB;AAC7D,SAASC,qBAAqB,QAAQ,wBAAwB;AAC9D,SAASC,EAAE,EAAEC,IAAI,EAAEC,IAAI,QAAQ,MAAM;AACrC,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAA;EAAAC,UAAA;AAAA;AAAA,SAAAC,kDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAgI+CtE,EAAE,CAAAwE,SAAA,YAUokC,CAAC;IAVvkCxE,EAAE,CAAAyE,MAAA;EAAA;EAAA,IAAAH,EAAA;IAAA,IAAAI,OAAA;IAAA,MAAAC,MAAA,GAAF3E,EAAE,CAAA4E,aAAA;IAAF5E,EAAE,CAAA6E,UAAA,SAAAH,OAAA,GAAAC,MAAA,CAAAG,KAAA,qBAAAJ,OAAA,CAAAC,MAAA,CAAAI,cAAA,GAUsgC,CAAC,QAVzgC/E,EAAE,CAAAgF,WAAA,OAAAL,MAAA,CAAAI,cAAA,KAAF/E,EAAE,CAAAiF,aAUwjC,CAAC;EAAA;AAAA;AAAA,SAAAC,mEAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAa,GAAA,GAV3jCnF,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAqF,cAAA,uBAUg4E,CAAC,eAAiT,CAAC;IAVrrFrF,EAAE,CAAAsF,UAAA,+BAAAC,sGAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAL,GAAA;MAAA,MAAAR,MAAA,GAAF3E,EAAE,CAAA4E,aAAA;MAAA,OAAF5E,EAAE,CAAAyF,WAAA,CAUimFd,MAAA,CAAAe,cAAA,CAAe,CAAC;IAAA,CAAC,CAAC,2BAAAC,kGAAAC,MAAA;MAVrnF5F,EAAE,CAAAwF,aAAA,CAAAL,GAAA;MAAA,MAAAR,MAAA,GAAF3E,EAAE,CAAA4E,aAAA;MAAA,OAAF5E,EAAE,CAAAyF,WAAA,CAUmpFd,MAAA,CAAAkB,MAAA,CAAAC,GAAA,CAAAF,MAAiB,CAAC;IAAA,CAAC,CAAC;IAVzqF5F,EAAE,CAAA+F,YAAA,CAUkrF,CAAC,CAAqB,CAAC;EAAA;EAAA,IAAAzB,EAAA;IAAA,MAAAK,MAAA,GAV3sF3E,EAAE,CAAA4E,aAAA;IAAF5E,EAAE,CAAA6E,UAAA,cAAAF,MAAA,CAAAqB,KAAA,CAAAH,MAUy3E,CAAC;IAV53E7F,EAAE,CAAAiG,SAAA,CAU08E,CAAC;IAV78EjG,EAAE,CAAA6E,UAAA,gBAU08E,CAAC,YAAAF,MAAA,CAAAkB,MAAA,EAAmC,CAAC,gBAAAlB,MAAA,CAAAuB,KAAA,EAAsC,CAAC,kBAAAvB,MAAA,CAAAwB,KAAsC,CAAC;EAAA;AAAA;AAAA,SAAAC,4DAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+B,GAAA,GAV/jFrG,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAqF,cAAA,gBAU66F,CAAC;IAVh7FrF,EAAE,CAAAsF,UAAA,mBAAAgB,oFAAA;MAAA,MAAAC,OAAA,GAAFvG,EAAE,CAAAwF,aAAA,CAAAa,GAAA,EAAAG,SAAA;MAAA,MAAA7B,MAAA,GAAF3E,EAAE,CAAA4E,aAAA;MAAA,OAAF5E,EAAE,CAAAyF,WAAA,CAU44Fd,MAAA,CAAA8B,WAAA,CAAAF,OAAA,CAAAG,GAAoB,CAAC;IAAA,CAAC,CAAC;IAVr6F1G,EAAE,CAAAwE,SAAA,aAUyjG,CAAC;IAV5jGxE,EAAE,CAAAyE,MAAA;IAAFzE,EAAE,CAAAqF,cAAA,cAU8lG,CAAC;IAVjmGrF,EAAE,CAAA2G,MAAA,EAU6mG,CAAC;IAVhnG3G,EAAE,CAAA+F,YAAA,CAUonG,CAAC;IAVvnG/F,EAAE,CAAAqF,cAAA,cAUypG,CAAC;IAV5pGrF,EAAE,CAAA2G,MAAA,EAUwqG,CAAC;IAV3qG3G,EAAE,CAAA+F,YAAA,CAU+qG,CAAC,CAAkB,CAAC;EAAA;EAAA,IAAAzB,EAAA;IAAA,MAAAiC,OAAA,GAAAhC,GAAA,CAAAiC,SAAA;IAVrsGxG,EAAE,CAAAiG,SAAA,CAUyiG,CAAC;IAV5iGjG,EAAE,CAAA6E,UAAA,QAAF7E,EAAE,CAAAgF,WAAA,OAAAuB,OAAA,CAAAG,GAAA,GAAF1G,EAAE,CAAAiF,aAUyiG,CAAC;IAV5iGjF,EAAE,CAAAiG,SAAA,EAU6mG,CAAC;IAVhnGjG,EAAE,CAAA4G,iBAAA,CAAAL,OAAA,CAAAM,IAU6mG,CAAC;IAVhnG7G,EAAE,CAAAiG,SAAA,EAUwqG,CAAC;IAV3qGjG,EAAE,CAAA4G,iBAAA,CAAAL,OAAA,CAAAO,IAUwqG,CAAC;EAAA;AAAA;AAAA,SAAAC,mDAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA0C,GAAA,GAV3qGhH,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAiH,uBAAA,EAUkuE,CAAC;IAVruEjH,EAAE,CAAAkH,UAAA,IAAAhC,kEAAA,0BAUg4E,CAAC;IAVn4ElF,EAAE,CAAAqF,cAAA,sBAU+vF,CAAC;IAVlwFrF,EAAE,CAAAsF,UAAA,qBAAA6B,oFAAAvB,MAAA;MAAF5F,EAAE,CAAAwF,aAAA,CAAAwB,GAAA;MAAA,MAAArC,MAAA,GAAF3E,EAAE,CAAA4E,aAAA;MAAA,OAAF5E,EAAE,CAAAyF,WAAA,CAU4uFd,MAAA,CAAAyC,SAAA,CAAAxB,MAAgB,CAAC;IAAA,CAAC,CAAC;IAVjwF5F,EAAE,CAAAkH,UAAA,IAAAd,2DAAA,mBAU66F,CAAC;IAVh7FpG,EAAE,CAAA+F,YAAA,CAUwtG,CAAC;IAV3tG/F,EAAE,CAAAqH,qBAAA;EAAA;EAAA,IAAA/C,EAAA;IAAA,MAAAK,MAAA,GAAF3E,EAAE,CAAA4E,aAAA;IAAF5E,EAAE,CAAAiG,SAAA,CAUqxE,CAAC;IAVxxEjG,EAAE,CAAA6E,UAAA,SAAAF,MAAA,CAAA2C,aAUqxE,CAAC;IAVxxEtH,EAAE,CAAAiG,SAAA,EAU6zF,CAAC;IAVh0FjG,EAAE,CAAA6E,UAAA,YAAAF,MAAA,CAAA4C,QAAA,EAU6zF,CAAC;EAAA;AAAA;AAxIr6F,MAAMC,6CAA6C,GAAG;EAClDC,SAAS,EAAE,EAAE;EACb1C,cAAc,EAAE,IAAI;EACpB2C,QAAQ,EAAE7D,EAAE,CAAC;IAAE4D,SAAS,EAAE,CAAC,CAAC;IAAEE,qBAAqB,EAAE,CAAC;EAAE,CAAC,CAAC;EAC1DC,SAAS,EAAE;AACf,CAAC;AACD;AACA;AACA;AACA,MAAMC,qCAAqC,GAAG7F,cAAc,CAACwF,6CAA6C,CAAC;AAC3G,SAASM,yCAAyCA,CAACC,OAAO,EAAE;EACxD,OAAO9F,iBAAiB,CAAC4F,qCAAqC,EAAEE,OAAO,EAAEP,6CAA6C,CAAC;AAC3H;AAEA,MAAMQ,wBAAwB,GAAG,SAAS;AAC1C;AACA,MAAMC,0BAA0B,SAAS1G,UAAU,CAAC;EAChD2G,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAAChC,KAAK,GAAGlG,MAAM,CAAC4B,UAAU,CAAC;IAC/B,IAAI,CAACuG,QAAQ,GAAGxF,WAAW,CAAC,IAAI,CAAC;IACjC,IAAI,CAACmF,OAAO,GAAG9H,MAAM,CAAC4H,qCAAqC,CAAC;IAC5D,IAAI,CAACQ,IAAI,GAAGpI,MAAM,CAACsC,qBAAqB,CAAC,CAAC8F,IAAI;IAC9C,IAAI,CAACC,IAAI,GAAGzF,eAAe,CAAC,CAAC;IAC7B,IAAI,CAACiC,KAAK,GAAGjE,QAAQ,CAACZ,MAAM,CAACuD,aAAa,CAAC,CAAC;IAC5C,IAAI,CAACkE,QAAQ,GAAG7G,QAAQ,CAACiD,IAAI,CAAC,IAAI,CAACiE,OAAO,CAACL,QAAQ,CAAC,CAAC;IACrD,IAAI,CAACD,SAAS,GAAGvH,MAAM,CAAC,IAAI,CAAC6H,OAAO,CAACN,SAAS,CAAC;IAC/C,IAAI,CAAC1C,cAAc,GAAG7E,MAAM,CAAC,IAAI,CAAC6H,OAAO,CAAChD,cAAc,CAAC;IACzD,IAAI,CAACiB,KAAK,GAAG/F,MAAM,CAACoD,gBAAgB,CAAC;IACrC,IAAI,CAAC6C,KAAK,GAAGrF,QAAQ,CAACZ,MAAM,CAACwD,wBAAwB,CAAC,CAAC;IACvD,IAAI,CAACoC,MAAM,GAAG3F,MAAM,CAAC,EAAE,CAAC;IACxB,IAAI,CAAC0H,SAAS,GAAG1H,MAAM,CAAC,IAAI,CAAC6H,OAAO,CAACH,SAAS,CAAC;IAC/C,IAAI,CAACL,QAAQ,GAAGpH,QAAQ,CAAC,MAAM,IAAI,CAACsH,SAAS,CAAC,CAAC,CAC1Cc,GAAG,CAAE7B,GAAG,KAAM;MACfA,GAAG;MACHG,IAAI,EAAE,IAAI,CAAC/B,KAAK,CAAC,CAAC,GAAG4B,GAAG,CAAC,IAAI,EAAE;MAC/BI,IAAI,EAAEpD,iBAAiB,CAACgD,GAAG,EAAE,IAAI,CAACgB,QAAQ,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC,CACEc,MAAM,CAAC,CAAC;MAAE3B,IAAI;MAAEC;IAAK,CAAC,KAAKrF,mBAAmB,CAACoF,IAAI,GAAGC,IAAI,EAAE,IAAI,CAACjB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACjF,IAAI,CAAC4C,IAAI,GAAGtI,QAAQ,CAAC,MAAM,IAAI,CAACuI,WAAW,CAAC,IAAI,CAAC3D,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC2C,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC;IACtG,IAAI,CAACe,CAAC,GAAGzG,mBAAmB,CAACY,eAAe,EAAE,oBAAoB,EAAE,IAAI,CAAC8F,WAAW,CAAC;IACrF,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACvB,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACwB,oBAAoB,GAAGhI,YAAY,CAAC,IAAI,CAACiE,cAAc,CAAC,CAACgE,IAAI,CAAChF,IAAI,CAAC,CAAC,CAAC,CAAC;EAC/E;EACA,IAAIiF,cAAcA,CAACC,KAAK,EAAE;IACtB,IAAI,CAACxB,SAAS,CAAC3B,GAAG,CAACmD,KAAK,CAAC;EAC7B;EACA,IAAIC,OAAOA,CAACpC,IAAI,EAAE;IACd,IAAI,CAAC/B,cAAc,CAACe,GAAG,CAACgB,IAAI,CAAC;EACjC;EACApB,cAAcA,CAAA,EAAG;IACb,IAAI,CAACyD,WAAW,EAAEC,GAAG,CAAC,CAAC,CAAC,EAAEC,aAAa,CAACC,KAAK,CAAC,CAAC;EACnD;EACAC,OAAOA,CAACC,KAAK,EAAE;IACX,MAAMC,cAAc,GAAG,IAAI,CAAC/B,QAAQ,CAAC,CAAC;IACtC,IAAI,CAAC3F,eAAe,CAACyH,KAAK,CAAC,IACvB,CAACC,cAAc,IACd,CAACD,KAAK,CAACE,SAAS,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAACH,KAAK,CAACE,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAE,EAAE;MAC3E;IACJ;IACA,MAAMC,QAAQ,GAAGJ,KAAK,CAACK,IAAI,IAAI,EAAE;IACjC,MAAMC,aAAa,GAAGF,QAAQ,CAACG,UAAU,CAACrI,SAAS,CAAC,GAC9CkI,QAAQ,GACRlI,SAAS,GAAGkI,QAAQ;IAC1B,IAAIjG,yBAAyB,CAACmG,aAAa,CAAC,KAAK,WAAW,EAAE;MAC1D;IACJ;IACA,MAAM/E,cAAc,GAAG1D,2BAA2B,CAACyI,aAAa,EAAEL,cAAc,CAAC;IACjF,IAAI1E,cAAc,EAAE;MAChB,IAAI,CAACA,cAAc,CAACe,GAAG,CAACf,cAAc,CAAC;IAC3C;EACJ;EACA0B,WAAWA,CAACyC,OAAO,EAAE;IACjB,IAAI,CAACZ,IAAI,CAACxC,GAAG,CAAC,KAAK,CAAC;IACpB,IAAI,CAACf,cAAc,CAACe,GAAG,CAACoD,OAAO,CAAC;IAChC,IAAI,CAACc,KAAK,EAAEX,aAAa,CAACC,KAAK,CAAC,CAAC;EACrC;EACAW,UAAUA,CAACC,aAAa,EAAE;IACtB,KAAK,CAACD,UAAU,CAACC,aAAa,CAAC;IAC/B,MAAMC,WAAW,GAAG,IAAI,CAAC1B,IAAI,CAAC,CAAC;IAC/B,IAAI,CAACI,cAAc,GAAGsB,WAAW,GAC3BjJ,gBAAgB,CAAC,IAAI,CAAC+H,KAAK,CAAC,CAAC,EAAEkB,WAAW,CAAC,GAC3C,IAAI,CAAClB,KAAK,CAAC,CAAC,CAAC,CAAC;IACpB,IAAI,CAACmB,GAAG,CAACC,aAAa,CAAC,CAAC;EAC5B;EACA,IAAIC,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAAClC,QAAQ,CAACtC,GAAG,CAACwE,QAAQ,CAAC;EAC/B;EACAC,OAAOA,CAAA,EAAG;IACN,MAAMC,aAAa,GAAG,IAAI,CAAC9C,QAAQ,CAAC,CAAC;IACrC,IAAI,CAAC,IAAI,CAACmB,cAAc,IAAI2B,aAAa,EAAE;MACvC,IAAI,CAAC3B,cAAc,GAAG,GAAGnH,SAAS,GAAGkC,qBAAqB,CAAC,IAAI,CAACmB,cAAc,CAAC,CAAC,EAAEyF,aAAa,CAAC,GAAG;IACvG;EACJ;EACAC,aAAaA,CAACC,WAAW,EAAE;IACvB,MAAMR,aAAa,GAAGQ,WAAW,CAACC,UAAU,CAAC3C,wBAAwB,EAAE,EAAE,CAAC;IAC1E,MAAMyB,cAAc,GAAG,IAAI,CAAC/B,QAAQ,CAAC,CAAC;IACtC,MAAMkD,kBAAkB,GAAGnB,cAAc,GACnC/H,SAAS,GAAGkC,qBAAqB,CAAC,IAAI,CAACmB,cAAc,CAAC,CAAC,EAAE0E,cAAc,CAAC,GACxE,EAAE;IACR,IAAI,CAACoB,QAAQ,CAACX,aAAa,KAAKU,kBAAkB,GAAG,EAAE,GAAGV,aAAa,CAAC;EAC5E;EACA9C,SAASA,CAAC;IAAE0D;EAAI,CAAC,EAAE;IACf,IAAIxH,eAAe,CAACwH,GAAG,CAAC,EAAE;MACtB,IAAI,CAACtC,MAAM,EAAEa,aAAa,CAACC,KAAK,CAAC;QAAEyB,aAAa,EAAE;MAAK,CAAC,CAAC;IAC7D;EACJ;EACArC,WAAWA,CAAC3D,cAAc,EAAE2C,QAAQ,EAAEE,SAAS,EAAE;IAC7C,IAAI,CAACF,QAAQ,EAAE;MACX,OAAO,IAAI;IACf;IACA,MAAM;MAAEsD,OAAO;MAAE,GAAGC;IAAY,CAAC,GAAG3J,4BAA4B,CAAC;MAC7DyD,cAAc;MACd2C,QAAQ;MACRE;IACJ,CAAC,CAAC;IACF,OAAO;MACH,GAAGqD,WAAW;MACdD,OAAO,EAAE,CACL,GAAGA,OAAO,EACV5J,yBAAyB,CAAC,GAAGM,SAAS,GAAGkC,qBAAqB,CAACmB,cAAc,EAAE2C,QAAQ,CAAC,GAAG,CAAC,EAC5FvG,+BAA+B,CAAC,CAAC;IAEzC,CAAC;EACL;EACA;IAAS,IAAI,CAAC+J,IAAI;MAAA,IAAAC,uCAAA;MAAA,gBAAAC,mCAAAC,CAAA;QAAA,QAAAF,uCAAA,KAAAA,uCAAA,GAA+EnL,EAAE,CAAAsL,qBAAA,CAAQrD,0BAA0B,IAAAoD,CAAA,IAA1BpD,0BAA0B;MAAA;IAAA,IAAqD;EAAE;EAC5L;IAAS,IAAI,CAACsD,IAAI,kBAD+EvL,EAAE,CAAAwL,iBAAA;MAAAC,IAAA,EACJxD,0BAA0B;MAAAyD,SAAA;MAAAC,SAAA,WAAAC,iCAAAtH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADxBtE,EAAE,CAAA6L,WAAA,CAU7B5K,gBAAgB,KAA2Bb,UAAU;UAV1BJ,EAAE,CAAA6L,WAAA,CAU8EjK,YAAY,KAA2BxB,UAAU;UAVjIJ,EAAE,CAAA6L,WAAA,CAU0NpJ,6BAA6B,KAA+BpC,WAAW;UAVnSL,EAAE,CAAA6L,WAAA,CAU+UzJ,SAAS,KAA2BhC,UAAU;QAAA;QAAA,IAAAkE,EAAA;UAAA,IAAAwH,EAAA;UAV/X9L,EAAE,CAAA+L,cAAA,CAAAD,EAAA,GAAF9L,EAAE,CAAAgM,WAAA,QAAAzH,GAAA,CAAAyF,KAAA,GAAA8B,EAAA,CAAAG,KAAA;UAAFjM,EAAE,CAAA+L,cAAA,CAAAD,EAAA,GAAF9L,EAAE,CAAAgM,WAAA,QAAAzH,GAAA,CAAAiE,MAAA,GAAAsD,EAAA,CAAAG,KAAA;UAAFjM,EAAE,CAAA+L,cAAA,CAAAD,EAAA,GAAF9L,EAAE,CAAAgM,WAAA,QAAAzH,GAAA,CAAA+F,QAAA,GAAAwB,EAAA,CAAAG,KAAA;UAAFjM,EAAE,CAAA+L,cAAA,CAAAD,EAAA,GAAF9L,EAAE,CAAAgM,WAAA,QAAAzH,GAAA,CAAA4E,WAAA,GAAA2C,EAAA;QAAA;MAAA;MAAAI,QAAA;MAAAC,YAAA,WAAAC,wCAAA9H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFtE,EAAE,CAAAqM,WAAA,cACJ9H,GAAA,CAAA8D,IAAA,CAAK,CAAC;QAAA;MAAA;MAAAiE,MAAA;QAAAhF,aAAA;QAAA0B,cAAA,GADJhJ,EAAE,CAAAuM,YAAA,CAAAC,IAAA;QAAAtD,OAAA,GAAFlJ,EAAE,CAAAuM,YAAA,CAAAC,IAAA;MAAA;MAAAC,OAAA;QAAA3D,oBAAA;MAAA;MAAA1E,UAAA;MAAAsI,QAAA,GAAF1M,EAAE,CAAA2M,kBAAA,CACwV,CACnbnL,YAAY,CAACyG,0BAA0B,CAAC,EACxCnG,wBAAwB,CAAC,EAAE,CAAC,EAC5BH,2BAA2B,CAAC;QAAEoJ,aAAa,EAAE;MAAK,CAAC,CAAC,EACpDvI,2BAA2B,CAAC;QAAEoK,OAAO,EAAE1M,MAAM,CAAC,KAAK;MAAE,CAAC,CAAC,EACvD6C,0BAA0B,CAAC;QACvB8J,UAAU,EAAE,OAAO;QACnBC,KAAK,EAAE;MACX,CAAC,CAAC,CACL,GAV4F9M,EAAE,CAAA+M,uBAAA,EAUub7J,EAAE,CAACC,QAAQ,EAAiBR,EAAE,CAACK,oBAAoB,EAAiBL,EAAE,CAACM,mBAAmB,IAVnhBjD,EAAE,CAAAgN,0BAAA,EAAFhN,EAAE,CAAAiN,mBAAA;MAAAC,kBAAA,EAAAhJ,GAAA;MAAAiJ,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA/C,QAAA,WAAAgD,oCAAAhJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAiJ,GAAA,GAAFvN,EAAE,CAAAoF,gBAAA;UAAFpF,EAAE,CAAAwN,eAAA,CAAAvJ,GAAA;UAAFjE,EAAE,CAAAqF,cAAA,sBAU4oB,CAAC;UAV/oBrF,EAAE,CAAAwE,SAAA,eAU44B,CAAC;UAV/4BxE,EAAE,CAAAkH,UAAA,IAAA7C,iDAAA,gCAAFrE,EAAE,CAAAyN,sBAUu6B,CAAC;UAV16BzN,EAAE,CAAA+F,YAAA,CAU0mC,CAAC;UAV7mC/F,EAAE,CAAAqF,cAAA,mBAU6nC,CAAC,cAAsmB,CAAC;UAVvuDrF,EAAE,CAAA0N,gBAAA,2BAAAC,mEAAA/H,MAAA;YAAF5F,EAAE,CAAAwF,aAAA,CAAA+H,GAAA;YAAFvN,EAAE,CAAA4N,kBAAA,CAAArJ,GAAA,CAAAsE,cAAA,EAAAjD,MAAA,MAAArB,GAAA,CAAAsE,cAAA,GAAAjD,MAAA;YAAA,OAAF5F,EAAE,CAAAyF,WAAA,CAAAG,MAAA;UAAA,CAUwiD,CAAC;UAV3iD5F,EAAE,CAAAsF,UAAA,iCAAAuI,yEAAAjI,MAAA;YAAF5F,EAAE,CAAAwF,aAAA,CAAA+H,GAAA;YAAA,OAAFvN,EAAE,CAAAyF,WAAA,CAU2kDlB,GAAA,CAAAgF,OAAA,CAAA3D,MAAc,CAAC;UAAA,CAAC,CAAC,kBAAAkI,0DAAA;YAV9lD9N,EAAE,CAAAwF,aAAA,CAAA+H,GAAA;YAAA,OAAFvN,EAAE,CAAAyF,WAAA,CAU+mDlB,GAAA,CAAAwJ,SAAA,CAAU,CAAC;UAAA,CAAC,CAAC,mBAAAC,2DAAA;YAV9nDhO,EAAE,CAAAwF,aAAA,CAAA+H,GAAA;YAAA,OAAFvN,EAAE,CAAAyF,WAAA,EAUipDlB,GAAA,CAAA0J,QAAA,CAAS,CAAC,IAAI1J,GAAA,CAAAgG,OAAA,CAAQ,CAAC;UAAA,CAAC,CAAC,2BAAAoD,mEAAA/H,MAAA;YAV5qD5F,EAAE,CAAAwF,aAAA,CAAA+H,GAAA;YAAA,OAAFvN,EAAE,CAAAyF,WAAA,CAUssDlB,GAAA,CAAAkG,aAAA,CAAA7E,MAAoB,CAAC;UAAA,CAAC,CAAC;UAV/tD5F,EAAE,CAAA+F,YAAA,CAUouD,CAAC;UAVvuD/F,EAAE,CAAAkO,YAAA,EAU0mE,CAAC;UAV7mElO,EAAE,CAAAqF,cAAA,cAUkoE,CAAC;UAVroErF,EAAE,CAAAkO,YAAA,KAU0pE,CAAC;UAV7pElO,EAAE,CAAA+F,YAAA,CAUwqE,CAAC,CAAiB,CAAC;UAV7rE/F,EAAE,CAAAkH,UAAA,IAAAH,kDAAA,yBAUkuE,CAAC;QAAA;QAAA,IAAAzC,EAAA;UAAA,MAAA6J,OAAA,GAVruEnO,EAAE,CAAAoO,WAAA;UAAFpO,EAAE,CAAA6E,UAAA,YAAAsJ,OAU4mB,CAAC,eAAA5J,GAAA,CAAA+D,IAAA,EAA4B,CAAC;UAV5oBtI,EAAE,CAAAiG,SAAA,CAU6yB,CAAC;UAVhzBjG,EAAE,CAAA6E,UAAA,aAAAN,GAAA,CAAA8J,QAAA,EAU6yB,CAAC,YAAA9J,GAAA,CAAA+D,IAAA,EAA6B,CAAC,mBAV90BtI,EAAE,CAAAsO,eAAA,KAAAnK,GAAA,CAU43B,CAAC;UAV/3BnE,EAAE,CAAAqM,WAAA,cAAA9H,GAAA,CAAAgK,IAAA;UAAFvO,EAAE,CAAAiG,SAAA,EAUi7C,CAAC;UAVp7CjG,EAAE,CAAA6E,UAAA,aAAAN,GAAA,CAAA8J,QAAA,EAUi7C,CAAC,YAAA9J,GAAA,CAAAkE,IAAA,EAA6B,CAAC,mBAVl9CzI,EAAE,CAAAsO,eAAA,KAAAnK,GAAA,CAUggD,CAAC;UAVngDnE,EAAE,CAAAwO,gBAAA,YAAAjK,GAAA,CAAAsE,cAUwiD,CAAC;UAV3iD7I,EAAE,CAAAqM,WAAA,cAAA9H,GAAA,CAAAgK,IAAA,gBAAAhK,GAAA,CAAA0J,QAAA;QAAA;MAAA;MAAAQ,YAAA,GAUouH1O,YAAY,EAA+BD,EAAE,CAAC4O,OAAO,EAAmH5O,EAAE,CAAC6O,IAAI,EAA4F3N,WAAW,EAA+BD,EAAE,CAAC6N,oBAAoB,EAAyP7N,EAAE,CAAC8N,0BAA0B,EAAiL9N,EAAE,CAAC+N,eAAe,EAAsF/N,EAAE,CAACgO,OAAO,EAA8M9N,gBAAgB,EAA6FW,YAAY,EAAqF2B,UAAU,EAAiFpB,EAAE,CAAC6M,oBAAoB,EAA4F7M,EAAE,CAACC,SAAS,EAAyJgB,WAAW,EAAgDY,EAAE,CAACiL,QAAQ,EAA4D3M,EAAE,CAAC4M,SAAS,EAA0F5M,EAAE,CAAC6M,qBAAqB,EAAuE7M,EAAE,CAAC8M,qBAAqB,EAA6H9M,EAAE,CAAC+M,4BAA4B,EAA6L/M,EAAE,CAACG,6BAA6B;MAAA6M,MAAA;MAAAC,eAAA;IAAA,EAAyG;EAAE;AAClyM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAZqGxP,EAAE,CAAAyP,iBAAA,CAYXxH,0BAA0B,EAAc,CAAC;IACzHwD,IAAI,EAAEnL,SAAS;IACfoP,IAAI,EAAE,CAAC;MAAEtL,UAAU,EAAE,IAAI;MAAEuL,QAAQ,EAAE,+BAA+B;MAAEC,OAAO,EAAE,CACnE7P,YAAY,EACZiB,WAAW,EACXC,gBAAgB,EAChBW,YAAY,EACZ2B,UAAU,EACVlB,WAAW,EACXe,WAAW,EACXV,YAAY,CACf;MAAE6M,eAAe,EAAEhP,uBAAuB,CAACsP,MAAM;MAAEC,SAAS,EAAE,CAC3DtO,YAAY,CAACyG,0BAA0B,CAAC,EACxCnG,wBAAwB,CAAC,EAAE,CAAC,EAC5BH,2BAA2B,CAAC;QAAEoJ,aAAa,EAAE;MAAK,CAAC,CAAC,EACpDvI,2BAA2B,CAAC;QAAEoK,OAAO,EAAE1M,MAAM,CAAC,KAAK;MAAE,CAAC,CAAC,EACvD6C,0BAA0B,CAAC;QACvB8J,UAAU,EAAE,OAAO;QACnBC,KAAK,EAAE;MACX,CAAC,CAAC,CACL;MAAEiD,cAAc,EAAE,CAAC5M,QAAQ,EAAEH,oBAAoB,EAAEC,mBAAmB,CAAC;MAAE+M,IAAI,EAAE;QAC5E,kBAAkB,EAAE;MACxB,CAAC;MAAE1F,QAAQ,EAAE,+rFAA+rF;MAAEgF,MAAM,EAAE,CAAC,gcAAgc;IAAE,CAAC;EACtqG,CAAC,CAAC,QAAkB;IAAEtF,KAAK,EAAE,CAAC;MACtByB,IAAI,EAAEjL,SAAS;MACfkP,IAAI,EAAE,CAACzO,gBAAgB,EAAE;QAAEgP,IAAI,EAAE7P;MAAW,CAAC;IACjD,CAAC,CAAC;IAAEoI,MAAM,EAAE,CAAC;MACTiD,IAAI,EAAEjL,SAAS;MACfkP,IAAI,EAAE,CAAC9N,YAAY,EAAE;QAAEqO,IAAI,EAAE7P;MAAW,CAAC;IAC7C,CAAC,CAAC;IAAE+I,WAAW,EAAE,CAAC;MACdsC,IAAI,EAAEhL,YAAY;MAClBiP,IAAI,EAAE,CAACtN,SAAS,EAAE;QAAE6N,IAAI,EAAE7P;MAAW,CAAC;IAC1C,CAAC,CAAC;IAAEkH,aAAa,EAAE,CAAC;MAChBmE,IAAI,EAAE/K;IACV,CAAC,CAAC;IAAEoI,oBAAoB,EAAE,CAAC;MACvB2C,IAAI,EAAE9K;IACV,CAAC,CAAC;IAAEqI,cAAc,EAAE,CAAC;MACjByC,IAAI,EAAE/K,KAAK;MACXgP,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAExG,OAAO,EAAE,CAAC;MACVuC,IAAI,EAAE/K,KAAK;MACXgP,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEpF,QAAQ,EAAE,CAAC;MACXmB,IAAI,EAAEjL,SAAS;MACfkP,IAAI,EAAE,CAAC9O,UAAU,CAAC,MAAM6B,6BAA6B,CAAC,EAAE;QAAEwN,IAAI,EAAE5P;MAAY,CAAC;IACjF,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASmH,6CAA6C,EAAEK,qCAAqC,EAAEI,0BAA0B,EAAEH,yCAAyC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}