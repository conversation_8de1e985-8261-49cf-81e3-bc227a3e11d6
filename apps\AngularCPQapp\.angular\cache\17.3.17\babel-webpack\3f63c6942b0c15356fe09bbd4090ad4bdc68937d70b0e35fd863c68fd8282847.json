{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive, Input } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { TuiAppearance } from '@taiga-ui/core/directives/appearance';\nclass TuiSurfaceStyles {\n  static {\n    this.ɵfac = function TuiSurfaceStyles_Factory(t) {\n      return new (t || TuiSurfaceStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiSurfaceStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-surface-styles\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiSurfaceStyles_Template(rf, ctx) {},\n      styles: [\"[data-surface]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;--tui-gap: .25rem;position:relative;box-sizing:border-box;background:none no-repeat;background-size:cover;overflow:hidden;isolation:isolate;-webkit-appearance:none;appearance:none;border:0;font-size:inherit;line-height:inherit;text-decoration:none;transition-property:background,border-radius,box-shadow,transform,-webkit-backdrop-filter,-webkit-mask!important;transition-property:backdrop-filter,background,border-radius,box-shadow,mask,transform!important;transition-property:backdrop-filter,background,border-radius,box-shadow,mask,transform,-webkit-backdrop-filter,-webkit-mask!important}[data-surface]:focus-visible{outline-color:var(--tui-border-focus)}@supports (not (-moz-appearance: none)) and (not (-webkit-hyphens: none)){[data-surface]:before{mix-blend-mode:multiply}}button[data-surface]{cursor:pointer}[data-surface]:before,[data-surface]:after,[tuiSurfaceLayer]:before,[tuiSurfaceLayer]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";z-index:-1;border-radius:inherit;pointer-events:none;background-size:cover;background-repeat:no-repeat;transition-property:opacity,transform,-webkit-backdrop-filter;transition-property:opacity,backdrop-filter,transform;transition-property:opacity,backdrop-filter,transform,-webkit-backdrop-filter}[tuiSurfaceLayer]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:0;left:0;inline-size:100%;block-size:100%;position:absolute!important;z-index:-1;object-fit:cover;border-radius:inherit;box-sizing:border-box;transition-property:box-shadow,filter,padding}input[tuiSurfaceLayer]+[tuiSurfaceLayer]{will-change:padding;background-clip:content-box;overflow:clip;overflow-clip-margin:content-box}input[tuiSurfaceLayer]:checked+[tuiSurfaceLayer]{padding:var(--tui-gap)}input[tuiSurfaceLayer]:focus-visible+[tuiSurfaceLayer]{padding:var(--tui-gap)}@media (hover: hover) and (pointer: fine){[data-surface]:hover input[tuiSurfaceLayer]+[tuiSurfaceLayer]{padding:var(--tui-gap)}}input[tuiSurfaceLayer]{color:var(--tui-background-accent-2);-webkit-appearance:none;appearance:none;margin:0;border-radius:inherit;outline:none;box-shadow:inset 0 0,inset 0 0 var(--tui-background-neutral-1)}input[tuiSurfaceLayer]:checked{box-shadow:inset 0 0 0 calc(var(--tui-gap) / 2),inset 0 0 var(--tui-background-neutral-1)}input[tuiSurfaceLayer]:focus-visible{box-shadow:inset 0 0,inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}input[tuiSurfaceLayer]:checked:focus-visible{filter:brightness(.7);box-shadow:inset 0 0 0 calc(var(--tui-gap) / 2),inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}@media (hover: hover) and (pointer: fine){[data-surface]:hover input[tuiSurfaceLayer]{box-shadow:inset 0 0,inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}[data-surface]:hover input[tuiSurfaceLayer]:checked{filter:brightness(.9);box-shadow:inset 0 0 0 calc(var(--tui-gap) / 2),inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}}[tuiSurface][data-surface=elevated],[tuiSurface][data-surface=floating]{box-shadow:var(--tui-shadow-small);background-color:var(--tui-background-elevation-2)}[tuiSurface][data-surface=elevated]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=floating]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{box-shadow:var(--tui-shadow-small);transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=elevated]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=floating]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{box-shadow:var(--tui-shadow-small-hover);transform:translate3d(0,-.25rem,0);background:var(--tui-background-elevation-2)}}[tuiSurface][data-surface=elevated]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=floating]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{box-shadow:var(--tui-shadow-small);transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=elevated]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=floating]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{box-shadow:var(--tui-shadow-small-hover);transform:translate3d(0,-.25rem,0);background:var(--tui-background-elevation-2)}}[tuiSurface][data-surface=flat],[tuiSurface][data-surface=neutral]{background-color:var(--tui-background-neutral-1)}[tuiSurface][data-surface=flat]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=neutral]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=flat]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=neutral]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{transform:scale(1.15)}}[tuiSurface][data-surface=flat]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=neutral]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=flat]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=neutral]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{transform:scale(1.15)}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSurfaceStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-surface-styles'\n      },\n      styles: [\"[data-surface]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;--tui-gap: .25rem;position:relative;box-sizing:border-box;background:none no-repeat;background-size:cover;overflow:hidden;isolation:isolate;-webkit-appearance:none;appearance:none;border:0;font-size:inherit;line-height:inherit;text-decoration:none;transition-property:background,border-radius,box-shadow,transform,-webkit-backdrop-filter,-webkit-mask!important;transition-property:backdrop-filter,background,border-radius,box-shadow,mask,transform!important;transition-property:backdrop-filter,background,border-radius,box-shadow,mask,transform,-webkit-backdrop-filter,-webkit-mask!important}[data-surface]:focus-visible{outline-color:var(--tui-border-focus)}@supports (not (-moz-appearance: none)) and (not (-webkit-hyphens: none)){[data-surface]:before{mix-blend-mode:multiply}}button[data-surface]{cursor:pointer}[data-surface]:before,[data-surface]:after,[tuiSurfaceLayer]:before,[tuiSurfaceLayer]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";z-index:-1;border-radius:inherit;pointer-events:none;background-size:cover;background-repeat:no-repeat;transition-property:opacity,transform,-webkit-backdrop-filter;transition-property:opacity,backdrop-filter,transform;transition-property:opacity,backdrop-filter,transform,-webkit-backdrop-filter}[tuiSurfaceLayer]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:0;left:0;inline-size:100%;block-size:100%;position:absolute!important;z-index:-1;object-fit:cover;border-radius:inherit;box-sizing:border-box;transition-property:box-shadow,filter,padding}input[tuiSurfaceLayer]+[tuiSurfaceLayer]{will-change:padding;background-clip:content-box;overflow:clip;overflow-clip-margin:content-box}input[tuiSurfaceLayer]:checked+[tuiSurfaceLayer]{padding:var(--tui-gap)}input[tuiSurfaceLayer]:focus-visible+[tuiSurfaceLayer]{padding:var(--tui-gap)}@media (hover: hover) and (pointer: fine){[data-surface]:hover input[tuiSurfaceLayer]+[tuiSurfaceLayer]{padding:var(--tui-gap)}}input[tuiSurfaceLayer]{color:var(--tui-background-accent-2);-webkit-appearance:none;appearance:none;margin:0;border-radius:inherit;outline:none;box-shadow:inset 0 0,inset 0 0 var(--tui-background-neutral-1)}input[tuiSurfaceLayer]:checked{box-shadow:inset 0 0 0 calc(var(--tui-gap) / 2),inset 0 0 var(--tui-background-neutral-1)}input[tuiSurfaceLayer]:focus-visible{box-shadow:inset 0 0,inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}input[tuiSurfaceLayer]:checked:focus-visible{filter:brightness(.7);box-shadow:inset 0 0 0 calc(var(--tui-gap) / 2),inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}@media (hover: hover) and (pointer: fine){[data-surface]:hover input[tuiSurfaceLayer]{box-shadow:inset 0 0,inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}[data-surface]:hover input[tuiSurfaceLayer]:checked{filter:brightness(.9);box-shadow:inset 0 0 0 calc(var(--tui-gap) / 2),inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}}[tuiSurface][data-surface=elevated],[tuiSurface][data-surface=floating]{box-shadow:var(--tui-shadow-small);background-color:var(--tui-background-elevation-2)}[tuiSurface][data-surface=elevated]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=floating]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{box-shadow:var(--tui-shadow-small);transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=elevated]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=floating]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{box-shadow:var(--tui-shadow-small-hover);transform:translate3d(0,-.25rem,0);background:var(--tui-background-elevation-2)}}[tuiSurface][data-surface=elevated]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=floating]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{box-shadow:var(--tui-shadow-small);transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=elevated]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=floating]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{box-shadow:var(--tui-shadow-small-hover);transform:translate3d(0,-.25rem,0);background:var(--tui-background-elevation-2)}}[tuiSurface][data-surface=flat],[tuiSurface][data-surface=neutral]{background-color:var(--tui-background-neutral-1)}[tuiSurface][data-surface=flat]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=neutral]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=flat]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=neutral]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{transform:scale(1.15)}}[tuiSurface][data-surface=flat]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=neutral]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=flat]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=neutral]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{transform:scale(1.15)}}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiSurface {\n  constructor() {\n    this.nothing = tuiWithStyles(TuiSurfaceStyles);\n    this.tuiSurface = '';\n  }\n  static {\n    this.ɵfac = function TuiSurface_Factory(t) {\n      return new (t || TuiSurface)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiSurface,\n      selectors: [[\"\", \"tuiSurface\", \"\"]],\n      hostAttrs: [\"tuiSurface\", \"\"],\n      hostVars: 1,\n      hostBindings: function TuiSurface_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-surface\", ctx.tuiSurface);\n        }\n      },\n      inputs: {\n        tuiSurface: \"tuiSurface\"\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([{\n        directive: i1.TuiAppearance,\n        inputs: [\"tuiAppearance\", \"tuiSurface\"]\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSurface, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiSurface]',\n      hostDirectives: [{\n        directive: TuiAppearance,\n        inputs: ['tuiAppearance: tuiSurface']\n      }],\n      host: {\n        tuiSurface: '',\n        '[attr.data-surface]': 'tuiSurface'\n      }\n    }]\n  }], null, {\n    tuiSurface: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiSurface };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Directive", "Input", "tuiWithStyles", "i1", "TuiAppearan<PERSON>", "TuiSurfaceStyles", "ɵfac", "TuiSurfaceStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiSurfaceStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "Tui<PERSON>urface", "constructor", "nothing", "tui<PERSON><PERSON><PERSON>", "TuiSurface_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiSurface_HostBindings", "ɵɵattribute", "inputs", "ɵɵHostDirectivesFeature", "directive", "selector", "hostDirectives"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-directives-surface.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive, Input } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { TuiAppearance } from '@taiga-ui/core/directives/appearance';\n\nclass TuiSurfaceStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSurfaceStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSurfaceStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-surface-styles\" }, ngImport: i0, template: '', isInline: true, styles: [\"[data-surface]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;--tui-gap: .25rem;position:relative;box-sizing:border-box;background:none no-repeat;background-size:cover;overflow:hidden;isolation:isolate;-webkit-appearance:none;appearance:none;border:0;font-size:inherit;line-height:inherit;text-decoration:none;transition-property:background,border-radius,box-shadow,transform,-webkit-backdrop-filter,-webkit-mask!important;transition-property:backdrop-filter,background,border-radius,box-shadow,mask,transform!important;transition-property:backdrop-filter,background,border-radius,box-shadow,mask,transform,-webkit-backdrop-filter,-webkit-mask!important}[data-surface]:focus-visible{outline-color:var(--tui-border-focus)}@supports (not (-moz-appearance: none)) and (not (-webkit-hyphens: none)){[data-surface]:before{mix-blend-mode:multiply}}button[data-surface]{cursor:pointer}[data-surface]:before,[data-surface]:after,[tuiSurfaceLayer]:before,[tuiSurfaceLayer]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";z-index:-1;border-radius:inherit;pointer-events:none;background-size:cover;background-repeat:no-repeat;transition-property:opacity,transform,-webkit-backdrop-filter;transition-property:opacity,backdrop-filter,transform;transition-property:opacity,backdrop-filter,transform,-webkit-backdrop-filter}[tuiSurfaceLayer]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:0;left:0;inline-size:100%;block-size:100%;position:absolute!important;z-index:-1;object-fit:cover;border-radius:inherit;box-sizing:border-box;transition-property:box-shadow,filter,padding}input[tuiSurfaceLayer]+[tuiSurfaceLayer]{will-change:padding;background-clip:content-box;overflow:clip;overflow-clip-margin:content-box}input[tuiSurfaceLayer]:checked+[tuiSurfaceLayer]{padding:var(--tui-gap)}input[tuiSurfaceLayer]:focus-visible+[tuiSurfaceLayer]{padding:var(--tui-gap)}@media (hover: hover) and (pointer: fine){[data-surface]:hover input[tuiSurfaceLayer]+[tuiSurfaceLayer]{padding:var(--tui-gap)}}input[tuiSurfaceLayer]{color:var(--tui-background-accent-2);-webkit-appearance:none;appearance:none;margin:0;border-radius:inherit;outline:none;box-shadow:inset 0 0,inset 0 0 var(--tui-background-neutral-1)}input[tuiSurfaceLayer]:checked{box-shadow:inset 0 0 0 calc(var(--tui-gap) / 2),inset 0 0 var(--tui-background-neutral-1)}input[tuiSurfaceLayer]:focus-visible{box-shadow:inset 0 0,inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}input[tuiSurfaceLayer]:checked:focus-visible{filter:brightness(.7);box-shadow:inset 0 0 0 calc(var(--tui-gap) / 2),inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}@media (hover: hover) and (pointer: fine){[data-surface]:hover input[tuiSurfaceLayer]{box-shadow:inset 0 0,inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}[data-surface]:hover input[tuiSurfaceLayer]:checked{filter:brightness(.9);box-shadow:inset 0 0 0 calc(var(--tui-gap) / 2),inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}}[tuiSurface][data-surface=elevated],[tuiSurface][data-surface=floating]{box-shadow:var(--tui-shadow-small);background-color:var(--tui-background-elevation-2)}[tuiSurface][data-surface=elevated]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=floating]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{box-shadow:var(--tui-shadow-small);transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=elevated]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=floating]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{box-shadow:var(--tui-shadow-small-hover);transform:translate3d(0,-.25rem,0);background:var(--tui-background-elevation-2)}}[tuiSurface][data-surface=elevated]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=floating]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{box-shadow:var(--tui-shadow-small);transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=elevated]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=floating]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{box-shadow:var(--tui-shadow-small-hover);transform:translate3d(0,-.25rem,0);background:var(--tui-background-elevation-2)}}[tuiSurface][data-surface=flat],[tuiSurface][data-surface=neutral]{background-color:var(--tui-background-neutral-1)}[tuiSurface][data-surface=flat]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=neutral]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=flat]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=neutral]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{transform:scale(1.15)}}[tuiSurface][data-surface=flat]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=neutral]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=flat]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=neutral]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{transform:scale(1.15)}}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSurfaceStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-surface-styles',\n                    }, styles: [\"[data-surface]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;--tui-gap: .25rem;position:relative;box-sizing:border-box;background:none no-repeat;background-size:cover;overflow:hidden;isolation:isolate;-webkit-appearance:none;appearance:none;border:0;font-size:inherit;line-height:inherit;text-decoration:none;transition-property:background,border-radius,box-shadow,transform,-webkit-backdrop-filter,-webkit-mask!important;transition-property:backdrop-filter,background,border-radius,box-shadow,mask,transform!important;transition-property:backdrop-filter,background,border-radius,box-shadow,mask,transform,-webkit-backdrop-filter,-webkit-mask!important}[data-surface]:focus-visible{outline-color:var(--tui-border-focus)}@supports (not (-moz-appearance: none)) and (not (-webkit-hyphens: none)){[data-surface]:before{mix-blend-mode:multiply}}button[data-surface]{cursor:pointer}[data-surface]:before,[data-surface]:after,[tuiSurfaceLayer]:before,[tuiSurfaceLayer]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";z-index:-1;border-radius:inherit;pointer-events:none;background-size:cover;background-repeat:no-repeat;transition-property:opacity,transform,-webkit-backdrop-filter;transition-property:opacity,backdrop-filter,transform;transition-property:opacity,backdrop-filter,transform,-webkit-backdrop-filter}[tuiSurfaceLayer]{transition-property:all;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:0;left:0;inline-size:100%;block-size:100%;position:absolute!important;z-index:-1;object-fit:cover;border-radius:inherit;box-sizing:border-box;transition-property:box-shadow,filter,padding}input[tuiSurfaceLayer]+[tuiSurfaceLayer]{will-change:padding;background-clip:content-box;overflow:clip;overflow-clip-margin:content-box}input[tuiSurfaceLayer]:checked+[tuiSurfaceLayer]{padding:var(--tui-gap)}input[tuiSurfaceLayer]:focus-visible+[tuiSurfaceLayer]{padding:var(--tui-gap)}@media (hover: hover) and (pointer: fine){[data-surface]:hover input[tuiSurfaceLayer]+[tuiSurfaceLayer]{padding:var(--tui-gap)}}input[tuiSurfaceLayer]{color:var(--tui-background-accent-2);-webkit-appearance:none;appearance:none;margin:0;border-radius:inherit;outline:none;box-shadow:inset 0 0,inset 0 0 var(--tui-background-neutral-1)}input[tuiSurfaceLayer]:checked{box-shadow:inset 0 0 0 calc(var(--tui-gap) / 2),inset 0 0 var(--tui-background-neutral-1)}input[tuiSurfaceLayer]:focus-visible{box-shadow:inset 0 0,inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}input[tuiSurfaceLayer]:checked:focus-visible{filter:brightness(.7);box-shadow:inset 0 0 0 calc(var(--tui-gap) / 2),inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}@media (hover: hover) and (pointer: fine){[data-surface]:hover input[tuiSurfaceLayer]{box-shadow:inset 0 0,inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}[data-surface]:hover input[tuiSurfaceLayer]:checked{filter:brightness(.9);box-shadow:inset 0 0 0 calc(var(--tui-gap) / 2),inset 0 0 0 calc(var(--tui-gap) / 2) var(--tui-background-neutral-1)}}[tuiSurface][data-surface=elevated],[tuiSurface][data-surface=floating]{box-shadow:var(--tui-shadow-small);background-color:var(--tui-background-elevation-2)}[tuiSurface][data-surface=elevated]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=floating]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{box-shadow:var(--tui-shadow-small);transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=elevated]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=floating]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{box-shadow:var(--tui-shadow-small-hover);transform:translate3d(0,-.25rem,0);background:var(--tui-background-elevation-2)}}[tuiSurface][data-surface=elevated]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=floating]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{box-shadow:var(--tui-shadow-small);transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=elevated]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=floating]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{box-shadow:var(--tui-shadow-small-hover);transform:translate3d(0,-.25rem,0);background:var(--tui-background-elevation-2)}}[tuiSurface][data-surface=flat],[tuiSurface][data-surface=neutral]{background-color:var(--tui-background-neutral-1)}[tuiSurface][data-surface=flat]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=neutral]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=flat]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=neutral]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{transform:scale(1.15)}}[tuiSurface][data-surface=flat]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active,[tuiSurface][data-surface=neutral]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active{transform:scale(.95)}@media (hover: hover) and (pointer: fine){[tuiSurface][data-surface=flat]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover,[tuiSurface][data-surface=neutral]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover{transform:scale(1.15)}}\\n\"] }]\n        }] });\nclass TuiSurface {\n    constructor() {\n        this.nothing = tuiWithStyles(TuiSurfaceStyles);\n        this.tuiSurface = '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSurface, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSurface, isStandalone: true, selector: \"[tuiSurface]\", inputs: { tuiSurface: \"tuiSurface\" }, host: { attributes: { \"tuiSurface\": \"\" }, properties: { \"attr.data-surface\": \"tuiSurface\" } }, hostDirectives: [{ directive: i1.TuiAppearance, inputs: [\"tuiAppearance\", \"tuiSurface\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSurface, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiSurface]',\n                    hostDirectives: [\n                        {\n                            directive: TuiAppearance,\n                            inputs: ['tuiAppearance: tuiSurface'],\n                        },\n                    ],\n                    host: {\n                        tuiSurface: '',\n                        '[attr.data-surface]': 'tuiSurface',\n                    },\n                }]\n        }], propDecorators: { tuiSurface: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiSurface };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACvG,SAASC,aAAa,QAAQ,mCAAmC;AACjE,OAAO,KAAKC,EAAE,MAAM,sCAAsC;AAC1D,SAASC,aAAa,QAAQ,sCAAsC;AAEpE,MAAMC,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACI,IAAI,kBAD+Eb,EAAE,CAAAc,iBAAA;MAAAC,IAAA,EACJN,gBAAgB;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADdnB,EAAE,CAAAoB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC+oM;EAAE;AACxvM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG9B,EAAE,CAAA+B,iBAAA,CAGXtB,gBAAgB,EAAc,CAAC;IAC/GM,IAAI,EAAEd,SAAS;IACf+B,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAE1B,iBAAiB,CAAC+B,IAAI;MAAEJ,eAAe,EAAE1B,uBAAuB,CAAC+B,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,44LAA44L;IAAE,CAAC;EACv6L,CAAC,CAAC;AAAA;AACV,MAAMU,UAAU,CAAC;EACbC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGjC,aAAa,CAACG,gBAAgB,CAAC;IAC9C,IAAI,CAAC+B,UAAU,GAAG,EAAE;EACxB;EACA;IAAS,IAAI,CAAC9B,IAAI,YAAA+B,mBAAA7B,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,UAAU;IAAA,CAAmD;EAAE;EAC1K;IAAS,IAAI,CAACK,IAAI,kBAf+E1C,EAAE,CAAA2C,iBAAA;MAAA5B,IAAA,EAeJsB,UAAU;MAAArB,SAAA;MAAAC,SAAA,iBAA0H,EAAE;MAAA2B,QAAA;MAAAC,YAAA,WAAAC,wBAAArB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAfpIzB,EAAE,CAAA+C,WAAA,iBAAArB,GAAA,CAAAc,UAAA;QAAA;MAAA;MAAAQ,MAAA;QAAAR,UAAA;MAAA;MAAAtB,UAAA;MAAAC,QAAA,GAAFnB,EAAE,CAAAiD,uBAAA;QAAAC,SAAA,EAeyN3C,EAAE,CAACC,aAAa;QAAAwC,MAAA;MAAA;IAAA,EAA6D;EAAE;AAC/Y;AACA;EAAA,QAAAlB,SAAA,oBAAAA,SAAA,KAjBqG9B,EAAE,CAAA+B,iBAAA,CAiBXM,UAAU,EAAc,CAAC;IACzGtB,IAAI,EAAEX,SAAS;IACf4B,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBiC,QAAQ,EAAE,cAAc;MACxBC,cAAc,EAAE,CACZ;QACIF,SAAS,EAAE1C,aAAa;QACxBwC,MAAM,EAAE,CAAC,2BAA2B;MACxC,CAAC,CACJ;MACDb,IAAI,EAAE;QACFK,UAAU,EAAE,EAAE;QACd,qBAAqB,EAAE;MAC3B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEA,UAAU,EAAE,CAAC;MAC3BzB,IAAI,EAAEV;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASgC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}