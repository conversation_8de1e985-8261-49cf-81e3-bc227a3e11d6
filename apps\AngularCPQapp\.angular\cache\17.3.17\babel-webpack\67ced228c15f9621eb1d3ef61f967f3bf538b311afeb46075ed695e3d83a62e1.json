{"ast": null, "code": "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, Input } from '@angular/core';\nimport { CHAR_NO_BREAK_SPACE } from '@taiga-ui/cdk/constants';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiWithStyles, tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_ANIMATIONS_SPEED } from '@taiga-ui/core/tokens';\nimport { tuiGetDuration } from '@taiga-ui/core/utils/miscellaneous';\nconst FADE = [{\n  opacity: 0.06\n}, {\n  opacity: 1\n}];\nclass TuiSkeletonStyles {\n  static {\n    this.ɵfac = function TuiSkeletonStyles_Factory(t) {\n      return new (t || TuiSkeletonStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiSkeletonStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-skeleton-styles\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiSkeletonStyles_Template(rf, ctx) {},\n      styles: [\"@keyframes tuiSkeleton{0%{opacity:.03}to{opacity:.06}}[tuiSkeleton]._skeleton{color:transparent;background:var(--tui-background-base)!important;box-shadow:none!important;filter:contrast(0) brightness(0);animation:tuiSkeleton ease-in-out calc(var(--tui-duration) * 4) infinite alternate;-webkit-user-select:none;user-select:none;pointer-events:none;opacity:.06}[tuiSkeleton]._skeleton[data-tui-skeleton]{background:transparent!important}[tuiSkeleton]._skeleton[data-tui-skeleton]:before{content:attr(data-tui-skeleton);background:var(--tui-background-base);font-size:smaller;-webkit-box-decoration-break:clone;box-decoration-break:clone;border-radius:.25rem}[tuiTheme=dark] [tuiSkeleton]._skeleton,[tuiTheme=dark][tuiSkeleton]._skeleton{filter:contrast(0) brightness(0) invert(1)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSkeletonStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-skeleton-styles'\n      },\n      styles: [\"@keyframes tuiSkeleton{0%{opacity:.03}to{opacity:.06}}[tuiSkeleton]._skeleton{color:transparent;background:var(--tui-background-base)!important;box-shadow:none!important;filter:contrast(0) brightness(0);animation:tuiSkeleton ease-in-out calc(var(--tui-duration) * 4) infinite alternate;-webkit-user-select:none;user-select:none;pointer-events:none;opacity:.06}[tuiSkeleton]._skeleton[data-tui-skeleton]{background:transparent!important}[tuiSkeleton]._skeleton[data-tui-skeleton]:before{content:attr(data-tui-skeleton);background:var(--tui-background-base);font-size:smaller;-webkit-box-decoration-break:clone;box-decoration-break:clone;border-radius:.25rem}[tuiTheme=dark] [tuiSkeleton]._skeleton,[tuiTheme=dark][tuiSkeleton]._skeleton{filter:contrast(0) brightness(0) invert(1)}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiSkeleton {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.duration = tuiGetDuration(inject(TUI_ANIMATIONS_SPEED)) * 2;\n    this.nothing = tuiWithStyles(TuiSkeletonStyles);\n    this.tuiSkeleton = false;\n  }\n  ngOnChanges({\n    tuiSkeleton\n  }) {\n    this.animation?.cancel();\n    if (!tuiSkeleton?.currentValue && !tuiSkeleton?.firstChange) {\n      this.animation = this.el.animate(FADE, this.duration);\n    }\n  }\n  getPlaceholder(value) {\n    switch (typeof value) {\n      case 'number':\n        return Array.from({\n          length: value\n        }).map(() => CHAR_NO_BREAK_SPACE.repeat(getLength())).join(' ');\n      case 'string':\n        return value;\n      default:\n        return null;\n    }\n  }\n  static {\n    this.ɵfac = function TuiSkeleton_Factory(t) {\n      return new (t || TuiSkeleton)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiSkeleton,\n      selectors: [[\"\", \"tuiSkeleton\", \"\"]],\n      hostAttrs: [\"tuiSkeleton\", \"\"],\n      hostVars: 3,\n      hostBindings: function TuiSkeleton_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-tui-skeleton\", ctx.getPlaceholder(ctx.tuiSkeleton));\n          i0.ɵɵclassProp(\"_skeleton\", ctx.tuiSkeleton);\n        }\n      },\n      inputs: {\n        tuiSkeleton: \"tuiSkeleton\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([tuiPure], TuiSkeleton.prototype, \"getPlaceholder\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSkeleton, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiSkeleton]',\n      host: {\n        tuiSkeleton: '',\n        '[class._skeleton]': 'tuiSkeleton',\n        '[attr.data-tui-skeleton]': 'getPlaceholder(tuiSkeleton)'\n      }\n    }]\n  }], null, {\n    tuiSkeleton: [{\n      type: Input\n    }],\n    getPlaceholder: []\n  });\n})();\nfunction getLength() {\n  return Math.floor(Math.random() * (15 - 5 + 1)) + 5;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiSkeleton };", "map": {"version": 3, "names": ["__decorate", "i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "Directive", "Input", "CHAR_NO_BREAK_SPACE", "tuiInjectElement", "tuiWithStyles", "tuiPure", "TUI_ANIMATIONS_SPEED", "tuiGetDuration", "FADE", "opacity", "TuiSkeletonStyles", "ɵfac", "TuiSkeletonStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiSkeletonStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiSkeleton", "constructor", "el", "duration", "nothing", "tuiSkeleton", "ngOnChanges", "animation", "cancel", "currentValue", "firstChange", "animate", "getPlaceholder", "value", "Array", "from", "length", "map", "repeat", "<PERSON><PERSON><PERSON><PERSON>", "join", "TuiSkeleton_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiSkeleton_HostBindings", "ɵɵattribute", "ɵɵclassProp", "inputs", "ɵɵNgOnChangesFeature", "prototype", "selector", "Math", "floor", "random"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-skeleton.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, Input } from '@angular/core';\nimport { CHAR_NO_BREAK_SPACE } from '@taiga-ui/cdk/constants';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiWithStyles, tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_ANIMATIONS_SPEED } from '@taiga-ui/core/tokens';\nimport { tuiGetDuration } from '@taiga-ui/core/utils/miscellaneous';\n\nconst FADE = [{ opacity: 0.06 }, { opacity: 1 }];\nclass TuiSkeletonStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSkeletonStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSkeletonStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-skeleton-styles\" }, ngImport: i0, template: '', isInline: true, styles: [\"@keyframes tuiSkeleton{0%{opacity:.03}to{opacity:.06}}[tuiSkeleton]._skeleton{color:transparent;background:var(--tui-background-base)!important;box-shadow:none!important;filter:contrast(0) brightness(0);animation:tuiSkeleton ease-in-out calc(var(--tui-duration) * 4) infinite alternate;-webkit-user-select:none;user-select:none;pointer-events:none;opacity:.06}[tuiSkeleton]._skeleton[data-tui-skeleton]{background:transparent!important}[tuiSkeleton]._skeleton[data-tui-skeleton]:before{content:attr(data-tui-skeleton);background:var(--tui-background-base);font-size:smaller;-webkit-box-decoration-break:clone;box-decoration-break:clone;border-radius:.25rem}[tuiTheme=dark] [tuiSkeleton]._skeleton,[tuiTheme=dark][tuiSkeleton]._skeleton{filter:contrast(0) brightness(0) invert(1)}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSkeletonStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-skeleton-styles',\n                    }, styles: [\"@keyframes tuiSkeleton{0%{opacity:.03}to{opacity:.06}}[tuiSkeleton]._skeleton{color:transparent;background:var(--tui-background-base)!important;box-shadow:none!important;filter:contrast(0) brightness(0);animation:tuiSkeleton ease-in-out calc(var(--tui-duration) * 4) infinite alternate;-webkit-user-select:none;user-select:none;pointer-events:none;opacity:.06}[tuiSkeleton]._skeleton[data-tui-skeleton]{background:transparent!important}[tuiSkeleton]._skeleton[data-tui-skeleton]:before{content:attr(data-tui-skeleton);background:var(--tui-background-base);font-size:smaller;-webkit-box-decoration-break:clone;box-decoration-break:clone;border-radius:.25rem}[tuiTheme=dark] [tuiSkeleton]._skeleton,[tuiTheme=dark][tuiSkeleton]._skeleton{filter:contrast(0) brightness(0) invert(1)}\\n\"] }]\n        }] });\nclass TuiSkeleton {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.duration = tuiGetDuration(inject(TUI_ANIMATIONS_SPEED)) * 2;\n        this.nothing = tuiWithStyles(TuiSkeletonStyles);\n        this.tuiSkeleton = false;\n    }\n    ngOnChanges({ tuiSkeleton }) {\n        this.animation?.cancel();\n        if (!tuiSkeleton?.currentValue && !tuiSkeleton?.firstChange) {\n            this.animation = this.el.animate(FADE, this.duration);\n        }\n    }\n    getPlaceholder(value) {\n        switch (typeof value) {\n            case 'number':\n                return Array.from({ length: value })\n                    .map(() => CHAR_NO_BREAK_SPACE.repeat(getLength()))\n                    .join(' ');\n            case 'string':\n                return value;\n            default:\n                return null;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSkeleton, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSkeleton, isStandalone: true, selector: \"[tuiSkeleton]\", inputs: { tuiSkeleton: \"tuiSkeleton\" }, host: { attributes: { \"tuiSkeleton\": \"\" }, properties: { \"class._skeleton\": \"tuiSkeleton\", \"attr.data-tui-skeleton\": \"getPlaceholder(tuiSkeleton)\" } }, usesOnChanges: true, ngImport: i0 }); }\n}\n__decorate([\n    tuiPure\n], TuiSkeleton.prototype, \"getPlaceholder\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSkeleton, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiSkeleton]',\n                    host: {\n                        tuiSkeleton: '',\n                        '[class._skeleton]': 'tuiSkeleton',\n                        '[attr.data-tui-skeleton]': 'getPlaceholder(tuiSkeleton)',\n                    },\n                }]\n        }], propDecorators: { tuiSkeleton: [{\n                type: Input\n            }], getPlaceholder: [] } });\nfunction getLength() {\n    return Math.floor(Math.random() * (15 - 5 + 1)) + 5;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiSkeleton };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAC/G,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,aAAa,EAAEC,OAAO,QAAQ,mCAAmC;AAC1E,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,cAAc,QAAQ,oCAAoC;AAEnE,MAAMC,IAAI,GAAG,CAAC;EAAEC,OAAO,EAAE;AAAK,CAAC,EAAE;EAAEA,OAAO,EAAE;AAAE,CAAC,CAAC;AAChD,MAAMC,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAACC,IAAI,YAAAC,0BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAACI,IAAI,kBAD+EnB,EAAE,CAAAoB,iBAAA;MAAAC,IAAA,EACJN,iBAAiB;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADfzB,EAAE,CAAA0B,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACohC;EAAE;AAC7nC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGpC,EAAE,CAAAqC,iBAAA,CAGXtB,iBAAiB,EAAc,CAAC;IAChHM,IAAI,EAAEpB,SAAS;IACfqC,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAEhC,iBAAiB,CAACqC,IAAI;MAAEJ,eAAe,EAAEhC,uBAAuB,CAACqC,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,+wBAA+wB;IAAE,CAAC;EAC1yB,CAAC,CAAC;AAAA;AACV,MAAMU,WAAW,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAGrC,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACsC,QAAQ,GAAGlC,cAAc,CAACR,MAAM,CAACO,oBAAoB,CAAC,CAAC,GAAG,CAAC;IAChE,IAAI,CAACoC,OAAO,GAAGtC,aAAa,CAACM,iBAAiB,CAAC;IAC/C,IAAI,CAACiC,WAAW,GAAG,KAAK;EAC5B;EACAC,WAAWA,CAAC;IAAED;EAAY,CAAC,EAAE;IACzB,IAAI,CAACE,SAAS,EAAEC,MAAM,CAAC,CAAC;IACxB,IAAI,CAACH,WAAW,EAAEI,YAAY,IAAI,CAACJ,WAAW,EAAEK,WAAW,EAAE;MACzD,IAAI,CAACH,SAAS,GAAG,IAAI,CAACL,EAAE,CAACS,OAAO,CAACzC,IAAI,EAAE,IAAI,CAACiC,QAAQ,CAAC;IACzD;EACJ;EACAS,cAAcA,CAACC,KAAK,EAAE;IAClB,QAAQ,OAAOA,KAAK;MAChB,KAAK,QAAQ;QACT,OAAOC,KAAK,CAACC,IAAI,CAAC;UAAEC,MAAM,EAAEH;QAAM,CAAC,CAAC,CAC/BI,GAAG,CAAC,MAAMrD,mBAAmB,CAACsD,MAAM,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC,CAClDC,IAAI,CAAC,GAAG,CAAC;MAClB,KAAK,QAAQ;QACT,OAAOP,KAAK;MAChB;QACI,OAAO,IAAI;IACnB;EACJ;EACA;IAAS,IAAI,CAACxC,IAAI,YAAAgD,oBAAA9C,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,WAAW;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACsB,IAAI,kBAnC+EjE,EAAE,CAAAkE,iBAAA;MAAA7C,IAAA,EAmCJsB,WAAW;MAAArB,SAAA;MAAAC,SAAA,kBAA8H,EAAE;MAAA4C,QAAA;MAAAC,YAAA,WAAAC,yBAAAtC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnCzI/B,EAAE,CAAAsE,WAAA,sBAmCJtC,GAAA,CAAAuB,cAAA,CAAAvB,GAAA,CAAAgB,WAA0B,CAAC;UAnCzBhD,EAAE,CAAAuE,WAAA,cAAAvC,GAAA,CAAAgB,WAmCM,CAAC;QAAA;MAAA;MAAAwB,MAAA;QAAAxB,WAAA;MAAA;MAAAxB,UAAA;MAAAC,QAAA,GAnCTzB,EAAE,CAAAyE,oBAAA;IAAA,EAmC4R;EAAE;AACrY;AACA1E,UAAU,CAAC,CACPW,OAAO,CACV,EAAEiC,WAAW,CAAC+B,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC;AACjD;EAAA,QAAAtC,SAAA,oBAAAA,SAAA,KAxCqGpC,EAAE,CAAAqC,iBAAA,CAwCXM,WAAW,EAAc,CAAC;IAC1GtB,IAAI,EAAEhB,SAAS;IACfiC,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBmD,QAAQ,EAAE,eAAe;MACzBlC,IAAI,EAAE;QACFO,WAAW,EAAE,EAAE;QACf,mBAAmB,EAAE,aAAa;QAClC,0BAA0B,EAAE;MAChC;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEA,WAAW,EAAE,CAAC;MAC5B3B,IAAI,EAAEf;IACV,CAAC,CAAC;IAAEiD,cAAc,EAAE;EAAG,CAAC;AAAA;AACpC,SAASO,SAASA,CAAA,EAAG;EACjB,OAAOc,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;AACvD;;AAEA;AACA;AACA;;AAEA,SAASnC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}