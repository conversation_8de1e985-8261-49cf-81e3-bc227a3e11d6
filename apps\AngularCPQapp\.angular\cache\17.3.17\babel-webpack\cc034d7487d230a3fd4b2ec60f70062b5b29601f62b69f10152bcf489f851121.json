{"ast": null, "code": "import { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { signal, inject, Component, ChangeDetectionStrategy, ViewChildren, Input } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { FormsModule } from '@angular/forms';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { TuiRepeatTimes } from '@taiga-ui/cdk/directives/repeat-times';\nimport { tuiWatch } from '@taiga-ui/cdk/observables';\nimport { delay, map } from 'rxjs';\nconst _c0 = [\"item\"];\nconst _c1 = a0 => ({\n  $implicit: a0\n});\nfunction TuiPager_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiPager_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3, 0);\n    i0.ɵɵtemplate(2, TuiPager_div_1_ng_container_2_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"_active\", i_r1 === ctx_r1.index)(\"_last\", i_r1 === ctx_r1.count - 1)(\"_post-active\", i_r1 === ctx_r1.index + 1)(\"_pre-active\", i_r1 === ctx_r1.index - 1)(\"_pre-last\", i_r1 === ctx_r1.count - 2)(\"_visible\", i_r1 >= ctx_r1.start && i_r1 <= ctx_r1.end);\n    i0.ɵɵattribute(\"data-index\", i_r1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.valueContent || null)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(15, _c1, i_r1));\n  }\n}\nclass TuiPager {\n  constructor() {\n    this.start = 0;\n    this.end = 0;\n    this.left = signal(0);\n    this.maxWidth = toSignal(inject(MutationObserverService, {\n      self: true\n    }).pipe(delay(0), map(() => this.visibleWidth), tuiWatch(), takeUntilDestroyed()));\n    this.max = 6;\n    this.count = this.max;\n    this.size = 'm';\n    this.index = 0;\n  }\n  ngOnChanges() {\n    this.move();\n  }\n  ngAfterViewInit() {\n    this.move();\n  }\n  get gap() {\n    return this.size === 'm' ? 9 : 7;\n  }\n  get visibleRange() {\n    const max = this.max > this.count ? this.count : this.max;\n    const start = Math.min(Math.max(this.index - Math.floor(max / 2), 0), this.count - max);\n    return [start, start + (max - 1)];\n  }\n  get visibleWidth() {\n    return (this.items?.map(item => item.nativeElement.offsetWidth ?? 0) ?? []).slice(this.start, this.end + 1).reduce((sum, item) => sum + item + this.gap, 0) - this.gap;\n  }\n  move() {\n    const [start, end] = this.visibleRange;\n    this.start = start;\n    this.end = end;\n    let left = this.start * this.gap;\n    for (let i = 0; i < this.start; i++) {\n      left += this.items?.get(i)?.nativeElement.offsetWidth ?? 0;\n    }\n    this.left.set(-1 * left);\n  }\n  static {\n    this.ɵfac = function TuiPager_Factory(t) {\n      return new (t || TuiPager)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiPager,\n      selectors: [[\"tui-pager\"]],\n      viewQuery: function TuiPager_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      hostVars: 5,\n      hostBindings: function TuiPager_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n          i0.ɵɵstyleProp(\"--t-gap\", ctx.gap, \"px\")(\"max-width\", ctx.maxWidth(), \"px\");\n        }\n      },\n      inputs: {\n        max: \"max\",\n        count: \"count\",\n        size: \"size\",\n        valueContent: \"valueContent\",\n        index: \"index\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MutationObserverService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          attributeOldValue: true,\n          characterData: true,\n          childList: true,\n          subtree: true\n        }\n      }]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 3,\n      consts: [[\"item\", \"\"], [1, \"t-items\"], [\"class\", \"t-item\", 3, \"_active\", \"_last\", \"_post-active\", \"_pre-active\", \"_pre-last\", \"_visible\", 4, \"tuiRepeatTimes\", \"tuiRepeatTimesOf\"], [1, \"t-item\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function TuiPager_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, TuiPager_div_1_Template, 3, 17, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx.left() + \"px)\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"tuiRepeatTimesOf\", ctx.count);\n        }\n      },\n      dependencies: [FormsModule, NgTemplateOutlet, TuiRepeatTimes],\n      styles: [\"[_nghost-%COMP%]{--t-size: .5rem;display:flex;align-items:center;overflow:hidden}[_nghost-%COMP%]   tui-root._mobile[_nghost-%COMP%], tui-root._mobile   [_nghost-%COMP%]{--t-size: .375rem}[data-size=m][_nghost-%COMP%]{min-block-size:1.25rem;--t-small-dot: scale(.75);--t-super-small-dot: scale(.5);--t-icon-size: 1rem}tui-root._mobile   [data-size=m][_nghost-%COMP%]{--t-icon-size: .875rem}[data-size=s][_nghost-%COMP%]{min-block-size:1rem;--t-small-dot: scale(.5);--t-super-small-dot: scale(.25);--t-icon-size: .875rem}tui-root._mobile   [data-size=s][_nghost-%COMP%]{--t-icon-size: .6875rem}.t-items[_ngcontent-%COMP%]{display:flex;gap:var(--t-gap);transition:transform var(--tui-duration-fast, .15s) var(--tui-curve-productive-standard, cubic-bezier(.2, 0, .4, .9))}.t-item[_ngcontent-%COMP%]{display:flex;align-self:center;align-items:center;justify-content:center;color:var(--tui-background-neutral-2)}.t-item._active[_ngcontent-%COMP%]{color:var(--tui-background-accent-1)}.t-item[_ngcontent-%COMP%]:empty:before{content:\\\"\\\";display:flex;min-inline-size:var(--t-size);min-block-size:var(--t-size);border-radius:100%;background:currentColor;transition:inherit}.t-item[_ngcontent-%COMP%]:empty:not(._visible):before, .t-item[_ngcontent-%COMP%]:empty:not(._visible) + .t-item[_ngcontent-%COMP%]:not(._active):not(._pre-active):empty:before, .t-item[_ngcontent-%COMP%]:empty:not(._last):not(._post-active):not(._active):nth-last-child(1 of._visible):before{transform:var(--t-super-small-dot)!important}.t-item[_ngcontent-%COMP%]:empty:not(._last)._post-active:nth-last-child(1 of._visible):before, .t-item[_ngcontent-%COMP%]:empty:not(._visible) + .t-item._pre-active[_ngcontent-%COMP%]:empty:before, .t-item[_ngcontent-%COMP%]:empty:not(._visible) + .t-item[_ngcontent-%COMP%]:empty + .t-item[_ngcontent-%COMP%]:not(._active):empty:before, .t-item[_ngcontent-%COMP%]:empty:not(._pre-last):nth-last-child(2 of._visible):not(._active):before{transform:var(--t-small-dot)}.t-item[_ngcontent-%COMP%]:empty + .t-item[_ngcontent-%COMP%]:not(:empty)._pre-last{transform:scale(.64)}.t-item[_ngcontent-%COMP%]:empty + .t-item[_ngcontent-%COMP%]:not(:empty)._pre-last:nth-last-child(2 of._visible){transform:scale(1)}.t-item[_ngcontent-%COMP%] >tui-icon{inline-size:var(--t-icon-size);block-size:var(--t-icon-size)}.t-item[_ngcontent-%COMP%], .t-item[_ngcontent-%COMP%] >tui-icon{transition:all var(--tui-duration-fast, .15s) var(--tui-curve-productive-standard, cubic-bezier(.2, 0, .4, .9))}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPager, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-pager',\n      imports: [FormsModule, NgTemplateOutlet, TuiRepeatTimes],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [MutationObserverService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          attributeOldValue: true,\n          characterData: true,\n          childList: true,\n          subtree: true\n        }\n      }],\n      host: {\n        '[attr.data-size]': 'size',\n        '[style.--t-gap.px]': 'gap',\n        '[style.max-width.px]': 'maxWidth()'\n      },\n      template: \"<div\\n    class=\\\"t-items\\\"\\n    [style.transform]=\\\"'translateX(' + left() + 'px)'\\\"\\n>\\n    <div\\n        *tuiRepeatTimes=\\\"let i of count\\\"\\n        #item\\n        class=\\\"t-item\\\"\\n        [attr.data-index]=\\\"i\\\"\\n        [class._active]=\\\"i === index\\\"\\n        [class._last]=\\\"i === count - 1\\\"\\n        [class._post-active]=\\\"i === index + 1\\\"\\n        [class._pre-active]=\\\"i === index - 1\\\"\\n        [class._pre-last]=\\\"i === count - 2\\\"\\n        [class._visible]=\\\"i >= start && i <= end\\\"\\n    >\\n        <ng-container *ngTemplateOutlet=\\\"valueContent || null; context: {$implicit: i}\\\" />\\n    </div>\\n</div>\\n\",\n      styles: [\":host{--t-size: .5rem;display:flex;align-items:center;overflow:hidden}:host :host-context(tui-root._mobile){--t-size: .375rem}:host[data-size=m]{min-block-size:1.25rem;--t-small-dot: scale(.75);--t-super-small-dot: scale(.5);--t-icon-size: 1rem}:host-context(tui-root._mobile) :host[data-size=m]{--t-icon-size: .875rem}:host[data-size=s]{min-block-size:1rem;--t-small-dot: scale(.5);--t-super-small-dot: scale(.25);--t-icon-size: .875rem}:host-context(tui-root._mobile) :host[data-size=s]{--t-icon-size: .6875rem}.t-items{display:flex;gap:var(--t-gap);transition:transform var(--tui-duration-fast, .15s) var(--tui-curve-productive-standard, cubic-bezier(.2, 0, .4, .9))}.t-item{display:flex;align-self:center;align-items:center;justify-content:center;color:var(--tui-background-neutral-2)}.t-item._active{color:var(--tui-background-accent-1)}.t-item:empty:before{content:\\\"\\\";display:flex;min-inline-size:var(--t-size);min-block-size:var(--t-size);border-radius:100%;background:currentColor;transition:inherit}.t-item:empty:not(._visible):before,.t-item:empty:not(._visible)+.t-item:not(._active):not(._pre-active):empty:before,.t-item:empty:not(._last):not(._post-active):not(._active):nth-last-child(1 of._visible):before{transform:var(--t-super-small-dot)!important}.t-item:empty:not(._last)._post-active:nth-last-child(1 of._visible):before,.t-item:empty:not(._visible)+.t-item._pre-active:empty:before,.t-item:empty:not(._visible)+.t-item:empty+.t-item:not(._active):empty:before,.t-item:empty:not(._pre-last):nth-last-child(2 of._visible):not(._active):before{transform:var(--t-small-dot)}.t-item:empty+.t-item:not(:empty)._pre-last{transform:scale(.64)}.t-item:empty+.t-item:not(:empty)._pre-last:nth-last-child(2 of._visible){transform:scale(1)}.t-item::ng-deep>tui-icon{inline-size:var(--t-icon-size);block-size:var(--t-icon-size)}.t-item,.t-item::ng-deep>tui-icon{transition:all var(--tui-duration-fast, .15s) var(--tui-curve-productive-standard, cubic-bezier(.2, 0, .4, .9))}\\n\"]\n    }]\n  }], null, {\n    items: [{\n      type: ViewChildren,\n      args: ['item']\n    }],\n    max: [{\n      type: Input\n    }],\n    count: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    valueContent: [{\n      type: Input\n    }],\n    index: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPager };", "map": {"version": 3, "names": ["NgTemplateOutlet", "i0", "signal", "inject", "Component", "ChangeDetectionStrategy", "ViewChildren", "Input", "toSignal", "takeUntilDestroyed", "FormsModule", "MutationObserverService", "WA_MUTATION_OBSERVER_INIT", "TuiRepeatTimes", "tuiWatch", "delay", "map", "_c0", "_c1", "a0", "$implicit", "TuiPager_div_1_ng_container_2_Template", "rf", "ctx", "ɵɵelementContainer", "TuiPager_div_1_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "i_r1", "ctx_r1", "ɵɵnextContext", "ɵɵclassProp", "index", "count", "start", "end", "ɵɵattribute", "ɵɵadvance", "ɵɵproperty", "valueContent", "ɵɵpureFunction1", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "left", "max<PERSON><PERSON><PERSON>", "self", "pipe", "visibleWidth", "max", "size", "ngOnChanges", "move", "ngAfterViewInit", "gap", "visibleRange", "Math", "min", "floor", "items", "item", "nativeElement", "offsetWidth", "slice", "reduce", "sum", "i", "get", "set", "ɵfac", "TuiPager_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "TuiPager_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostVars", "hostBindings", "TuiPager_HostBindings", "ɵɵstyleProp", "inputs", "standalone", "features", "ɵɵProvidersFeature", "provide", "useValue", "attributeOldValue", "characterData", "childList", "subtree", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiPager_Template", "dependencies", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-pager.mjs"], "sourcesContent": ["import { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { signal, inject, Component, ChangeDetectionStrategy, ViewChildren, Input } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { FormsModule } from '@angular/forms';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { TuiRepeatTimes } from '@taiga-ui/cdk/directives/repeat-times';\nimport { tuiWatch } from '@taiga-ui/cdk/observables';\nimport { delay, map } from 'rxjs';\n\nclass TuiPager {\n    constructor() {\n        this.start = 0;\n        this.end = 0;\n        this.left = signal(0);\n        this.maxWidth = toSignal(inject(MutationObserverService, { self: true }).pipe(delay(0), map(() => this.visibleWidth), tuiWatch(), takeUntilDestroyed()));\n        this.max = 6;\n        this.count = this.max;\n        this.size = 'm';\n        this.index = 0;\n    }\n    ngOnChanges() {\n        this.move();\n    }\n    ngAfterViewInit() {\n        this.move();\n    }\n    get gap() {\n        return this.size === 'm' ? 9 : 7;\n    }\n    get visibleRange() {\n        const max = this.max > this.count ? this.count : this.max;\n        const start = Math.min(Math.max(this.index - Math.floor(max / 2), 0), this.count - max);\n        return [start, start + (max - 1)];\n    }\n    get visibleWidth() {\n        return ((this.items?.map((item) => item.nativeElement.offsetWidth ?? 0) ?? [])\n            .slice(this.start, this.end + 1)\n            .reduce((sum, item) => sum + item + this.gap, 0) - this.gap);\n    }\n    move() {\n        const [start, end] = this.visibleRange;\n        this.start = start;\n        this.end = end;\n        let left = this.start * this.gap;\n        for (let i = 0; i < this.start; i++) {\n            left += this.items?.get(i)?.nativeElement.offsetWidth ?? 0;\n        }\n        this.left.set(-1 * left);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPager, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPager, isStandalone: true, selector: \"tui-pager\", inputs: { max: \"max\", count: \"count\", size: \"size\", valueContent: \"valueContent\", index: \"index\" }, host: { properties: { \"attr.data-size\": \"size\", \"style.--t-gap.px\": \"gap\", \"style.max-width.px\": \"maxWidth()\" } }, providers: [\n            MutationObserverService,\n            {\n                provide: WA_MUTATION_OBSERVER_INIT,\n                useValue: {\n                    attributeOldValue: true,\n                    characterData: true,\n                    childList: true,\n                    subtree: true,\n                },\n            },\n        ], viewQueries: [{ propertyName: \"items\", predicate: [\"item\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: \"<div\\n    class=\\\"t-items\\\"\\n    [style.transform]=\\\"'translateX(' + left() + 'px)'\\\"\\n>\\n    <div\\n        *tuiRepeatTimes=\\\"let i of count\\\"\\n        #item\\n        class=\\\"t-item\\\"\\n        [attr.data-index]=\\\"i\\\"\\n        [class._active]=\\\"i === index\\\"\\n        [class._last]=\\\"i === count - 1\\\"\\n        [class._post-active]=\\\"i === index + 1\\\"\\n        [class._pre-active]=\\\"i === index - 1\\\"\\n        [class._pre-last]=\\\"i === count - 2\\\"\\n        [class._visible]=\\\"i >= start && i <= end\\\"\\n    >\\n        <ng-container *ngTemplateOutlet=\\\"valueContent || null; context: {$implicit: i}\\\" />\\n    </div>\\n</div>\\n\", styles: [\":host{--t-size: .5rem;display:flex;align-items:center;overflow:hidden}:host :host-context(tui-root._mobile){--t-size: .375rem}:host[data-size=m]{min-block-size:1.25rem;--t-small-dot: scale(.75);--t-super-small-dot: scale(.5);--t-icon-size: 1rem}:host-context(tui-root._mobile) :host[data-size=m]{--t-icon-size: .875rem}:host[data-size=s]{min-block-size:1rem;--t-small-dot: scale(.5);--t-super-small-dot: scale(.25);--t-icon-size: .875rem}:host-context(tui-root._mobile) :host[data-size=s]{--t-icon-size: .6875rem}.t-items{display:flex;gap:var(--t-gap);transition:transform var(--tui-duration-fast, .15s) var(--tui-curve-productive-standard, cubic-bezier(.2, 0, .4, .9))}.t-item{display:flex;align-self:center;align-items:center;justify-content:center;color:var(--tui-background-neutral-2)}.t-item._active{color:var(--tui-background-accent-1)}.t-item:empty:before{content:\\\"\\\";display:flex;min-inline-size:var(--t-size);min-block-size:var(--t-size);border-radius:100%;background:currentColor;transition:inherit}.t-item:empty:not(._visible):before,.t-item:empty:not(._visible)+.t-item:not(._active):not(._pre-active):empty:before,.t-item:empty:not(._last):not(._post-active):not(._active):nth-last-child(1 of._visible):before{transform:var(--t-super-small-dot)!important}.t-item:empty:not(._last)._post-active:nth-last-child(1 of._visible):before,.t-item:empty:not(._visible)+.t-item._pre-active:empty:before,.t-item:empty:not(._visible)+.t-item:empty+.t-item:not(._active):empty:before,.t-item:empty:not(._pre-last):nth-last-child(2 of._visible):not(._active):before{transform:var(--t-small-dot)}.t-item:empty+.t-item:not(:empty)._pre-last{transform:scale(.64)}.t-item:empty+.t-item:not(:empty)._pre-last:nth-last-child(2 of._visible){transform:scale(1)}.t-item::ng-deep>tui-icon{inline-size:var(--t-icon-size);block-size:var(--t-icon-size)}.t-item,.t-item::ng-deep>tui-icon{transition:all var(--tui-duration-fast, .15s) var(--tui-curve-productive-standard, cubic-bezier(.2, 0, .4, .9))}\\n\"], dependencies: [{ kind: \"ngmodule\", type: FormsModule }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: TuiRepeatTimes, selector: \"[tuiRepeatTimes][tuiRepeatTimesOf]\", inputs: [\"tuiRepeatTimesOf\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPager, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-pager', imports: [FormsModule, NgTemplateOutlet, TuiRepeatTimes], changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        MutationObserverService,\n                        {\n                            provide: WA_MUTATION_OBSERVER_INIT,\n                            useValue: {\n                                attributeOldValue: true,\n                                characterData: true,\n                                childList: true,\n                                subtree: true,\n                            },\n                        },\n                    ], host: {\n                        '[attr.data-size]': 'size',\n                        '[style.--t-gap.px]': 'gap',\n                        '[style.max-width.px]': 'maxWidth()',\n                    }, template: \"<div\\n    class=\\\"t-items\\\"\\n    [style.transform]=\\\"'translateX(' + left() + 'px)'\\\"\\n>\\n    <div\\n        *tuiRepeatTimes=\\\"let i of count\\\"\\n        #item\\n        class=\\\"t-item\\\"\\n        [attr.data-index]=\\\"i\\\"\\n        [class._active]=\\\"i === index\\\"\\n        [class._last]=\\\"i === count - 1\\\"\\n        [class._post-active]=\\\"i === index + 1\\\"\\n        [class._pre-active]=\\\"i === index - 1\\\"\\n        [class._pre-last]=\\\"i === count - 2\\\"\\n        [class._visible]=\\\"i >= start && i <= end\\\"\\n    >\\n        <ng-container *ngTemplateOutlet=\\\"valueContent || null; context: {$implicit: i}\\\" />\\n    </div>\\n</div>\\n\", styles: [\":host{--t-size: .5rem;display:flex;align-items:center;overflow:hidden}:host :host-context(tui-root._mobile){--t-size: .375rem}:host[data-size=m]{min-block-size:1.25rem;--t-small-dot: scale(.75);--t-super-small-dot: scale(.5);--t-icon-size: 1rem}:host-context(tui-root._mobile) :host[data-size=m]{--t-icon-size: .875rem}:host[data-size=s]{min-block-size:1rem;--t-small-dot: scale(.5);--t-super-small-dot: scale(.25);--t-icon-size: .875rem}:host-context(tui-root._mobile) :host[data-size=s]{--t-icon-size: .6875rem}.t-items{display:flex;gap:var(--t-gap);transition:transform var(--tui-duration-fast, .15s) var(--tui-curve-productive-standard, cubic-bezier(.2, 0, .4, .9))}.t-item{display:flex;align-self:center;align-items:center;justify-content:center;color:var(--tui-background-neutral-2)}.t-item._active{color:var(--tui-background-accent-1)}.t-item:empty:before{content:\\\"\\\";display:flex;min-inline-size:var(--t-size);min-block-size:var(--t-size);border-radius:100%;background:currentColor;transition:inherit}.t-item:empty:not(._visible):before,.t-item:empty:not(._visible)+.t-item:not(._active):not(._pre-active):empty:before,.t-item:empty:not(._last):not(._post-active):not(._active):nth-last-child(1 of._visible):before{transform:var(--t-super-small-dot)!important}.t-item:empty:not(._last)._post-active:nth-last-child(1 of._visible):before,.t-item:empty:not(._visible)+.t-item._pre-active:empty:before,.t-item:empty:not(._visible)+.t-item:empty+.t-item:not(._active):empty:before,.t-item:empty:not(._pre-last):nth-last-child(2 of._visible):not(._active):before{transform:var(--t-small-dot)}.t-item:empty+.t-item:not(:empty)._pre-last{transform:scale(.64)}.t-item:empty+.t-item:not(:empty)._pre-last:nth-last-child(2 of._visible){transform:scale(1)}.t-item::ng-deep>tui-icon{inline-size:var(--t-icon-size);block-size:var(--t-icon-size)}.t-item,.t-item::ng-deep>tui-icon{transition:all var(--tui-duration-fast, .15s) var(--tui-curve-productive-standard, cubic-bezier(.2, 0, .4, .9))}\\n\"] }]\n        }], propDecorators: { items: [{\n                type: ViewChildren,\n                args: ['item']\n            }], max: [{\n                type: Input\n            }], count: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], valueContent: [{\n                type: Input\n            }], index: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPager };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,iBAAiB;AAClD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,YAAY,EAAEC,KAAK,QAAQ,eAAe;AACvG,SAASC,QAAQ,EAAEC,kBAAkB,QAAQ,4BAA4B;AACzE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,uBAAuB,EAAEC,yBAAyB,QAAQ,gCAAgC;AACnG,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,KAAK,EAAEC,GAAG,QAAQ,MAAM;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,SAAAE,uCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA0CmErB,EAAE,CAAAuB,kBAAA,EAY2nB,CAAC;EAAA;AAAA;AAAA,SAAAC,wBAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAZ9nBrB,EAAE,CAAAyB,cAAA,eAY6hB,CAAC;IAZhiBzB,EAAE,CAAA0B,UAAA,IAAAN,sCAAA,yBAY2nB,CAAC;IAZ9nBpB,EAAE,CAAA2B,YAAA,CAYuoB,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,IAAA,GAAAN,GAAA,CAAAH,SAAA;IAAA,MAAAU,MAAA,GAZ1oB7B,EAAE,CAAA8B,aAAA;IAAF9B,EAAE,CAAA+B,WAAA,YAAAH,IAAA,KAAAC,MAAA,CAAAG,KAYoS,CAAC,UAAAJ,IAAA,KAAAC,MAAA,CAAAI,KAAA,IAA0C,CAAC,iBAAAL,IAAA,KAAAC,MAAA,CAAAG,KAAA,IAAiD,CAAC,gBAAAJ,IAAA,KAAAC,MAAA,CAAAG,KAAA,IAAgD,CAAC,cAAAJ,IAAA,KAAAC,MAAA,CAAAI,KAAA,IAA8C,CAAC,aAAAL,IAAA,IAAAC,MAAA,CAAAK,KAAA,IAAAN,IAAA,IAAAC,MAAA,CAAAM,GAAoD,CAAC;IAZzhBnC,EAAE,CAAAoC,WAAA,eAAAR,IAAA;IAAF5B,EAAE,CAAAqC,SAAA,EAY+lB,CAAC;IAZlmBrC,EAAE,CAAAsC,UAAA,qBAAAT,MAAA,CAAAU,YAAA,QAY+lB,CAAC,4BAZlmBvC,EAAE,CAAAwC,eAAA,KAAAvB,GAAA,EAAAW,IAAA,CAYsnB,CAAC;EAAA;AAAA;AApD9tB,MAAMa,QAAQ,CAAC;EACXC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACR,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACQ,IAAI,GAAG1C,MAAM,CAAC,CAAC,CAAC;IACrB,IAAI,CAAC2C,QAAQ,GAAGrC,QAAQ,CAACL,MAAM,CAACQ,uBAAuB,EAAE;MAAEmC,IAAI,EAAE;IAAK,CAAC,CAAC,CAACC,IAAI,CAAChC,KAAK,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,MAAM,IAAI,CAACgC,YAAY,CAAC,EAAElC,QAAQ,CAAC,CAAC,EAAEL,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACxJ,IAAI,CAACwC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACf,KAAK,GAAG,IAAI,CAACe,GAAG;IACrB,IAAI,CAACC,IAAI,GAAG,GAAG;IACf,IAAI,CAACjB,KAAK,GAAG,CAAC;EAClB;EACAkB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,IAAI,CAAC,CAAC;EACf;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACD,IAAI,CAAC,CAAC;EACf;EACA,IAAIE,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACJ,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;EACpC;EACA,IAAIK,YAAYA,CAAA,EAAG;IACf,MAAMN,GAAG,GAAG,IAAI,CAACA,GAAG,GAAG,IAAI,CAACf,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,IAAI,CAACe,GAAG;IACzD,MAAMd,KAAK,GAAGqB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACP,GAAG,CAAC,IAAI,CAAChB,KAAK,GAAGuB,IAAI,CAACE,KAAK,CAACT,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAACf,KAAK,GAAGe,GAAG,CAAC;IACvF,OAAO,CAACd,KAAK,EAAEA,KAAK,IAAIc,GAAG,GAAG,CAAC,CAAC,CAAC;EACrC;EACA,IAAID,YAAYA,CAAA,EAAG;IACf,OAAQ,CAAC,IAAI,CAACW,KAAK,EAAE3C,GAAG,CAAE4C,IAAI,IAAKA,IAAI,CAACC,aAAa,CAACC,WAAW,IAAI,CAAC,CAAC,IAAI,EAAE,EACxEC,KAAK,CAAC,IAAI,CAAC5B,KAAK,EAAE,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC,CAC/B4B,MAAM,CAAC,CAACC,GAAG,EAAEL,IAAI,KAAKK,GAAG,GAAGL,IAAI,GAAG,IAAI,CAACN,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAACA,GAAG;EACnE;EACAF,IAAIA,CAAA,EAAG;IACH,MAAM,CAACjB,KAAK,EAAEC,GAAG,CAAC,GAAG,IAAI,CAACmB,YAAY;IACtC,IAAI,CAACpB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAIQ,IAAI,GAAG,IAAI,CAACT,KAAK,GAAG,IAAI,CAACmB,GAAG;IAChC,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC/B,KAAK,EAAE+B,CAAC,EAAE,EAAE;MACjCtB,IAAI,IAAI,IAAI,CAACe,KAAK,EAAEQ,GAAG,CAACD,CAAC,CAAC,EAAEL,aAAa,CAACC,WAAW,IAAI,CAAC;IAC9D;IACA,IAAI,CAAClB,IAAI,CAACwB,GAAG,CAAC,CAAC,CAAC,GAAGxB,IAAI,CAAC;EAC5B;EACA;IAAS,IAAI,CAACyB,IAAI,YAAAC,iBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyF7B,QAAQ;IAAA,CAAmD;EAAE;EACxK;IAAS,IAAI,CAAC8B,IAAI,kBAD+EvE,EAAE,CAAAwE,iBAAA;MAAAC,IAAA,EACJhC,QAAQ;MAAAiC,SAAA;MAAAC,SAAA,WAAAC,eAAAvD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADNrB,EAAE,CAAA6E,WAAA,CAAA7D,GAAA;QAAA;QAAA,IAAAK,EAAA;UAAA,IAAAyD,EAAA;UAAF9E,EAAE,CAAA+E,cAAA,CAAAD,EAAA,GAAF9E,EAAE,CAAAgF,WAAA,QAAA1D,GAAA,CAAAoC,KAAA,GAAAoB,EAAA;QAAA;MAAA;MAAAG,QAAA;MAAAC,YAAA,WAAAC,sBAAA9D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrB,EAAE,CAAAoC,WAAA,cAAAd,GAAA,CAAA2B,IAAA;UAAFjD,EAAE,CAAAoF,WAAA,YAAA9D,GAAA,CAAA+B,GAAA,MACG,CAAC,cAAR/B,GAAA,CAAAsB,QAAA,CAAS,CAAC,MAAH,CAAC;QAAA;MAAA;MAAAyC,MAAA;QAAArC,GAAA;QAAAf,KAAA;QAAAgB,IAAA;QAAAV,YAAA;QAAAP,KAAA;MAAA;MAAAsD,UAAA;MAAAC,QAAA,GADNvF,EAAE,CAAAwF,kBAAA,CACmR,CAC9W9E,uBAAuB,EACvB;QACI+E,OAAO,EAAE9E,yBAAyB;QAClC+E,QAAQ,EAAE;UACNC,iBAAiB,EAAE,IAAI;UACvBC,aAAa,EAAE,IAAI;UACnBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE;QACb;MACJ,CAAC,CACJ,GAZ4F9F,EAAE,CAAA+F,oBAAA,EAAF/F,EAAE,CAAAgG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kBAAAhF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrB,EAAE,CAAAyB,cAAA,YAY2H,CAAC;UAZ9HzB,EAAE,CAAA0B,UAAA,IAAAF,uBAAA,iBAY6hB,CAAC;UAZhiBxB,EAAE,CAAA2B,YAAA,CAY+oB,CAAC;QAAA;QAAA,IAAAN,EAAA;UAZlpBrB,EAAE,CAAAoF,WAAA,8BAAA9D,GAAA,CAAAqB,IAAA,UAYwH,CAAC;UAZ3H3C,EAAE,CAAAqC,SAAA,CAY+K,CAAC;UAZlLrC,EAAE,CAAAsC,UAAA,qBAAAhB,GAAA,CAAAW,KAY+K,CAAC;QAAA;MAAA;MAAAqE,YAAA,GAAg+E7F,WAAW,EAA+BV,gBAAgB,EAAoJa,cAAc;MAAA2F,MAAA;MAAAC,eAAA;IAAA,EAAwI;EAAE;AAC7lG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAdqGzG,EAAE,CAAA0G,iBAAA,CAcXjE,QAAQ,EAAc,CAAC;IACvGgC,IAAI,EAAEtE,SAAS;IACfwG,IAAI,EAAE,CAAC;MAAErB,UAAU,EAAE,IAAI;MAAEsB,QAAQ,EAAE,WAAW;MAAEC,OAAO,EAAE,CAACpG,WAAW,EAAEV,gBAAgB,EAAEa,cAAc,CAAC;MAAE4F,eAAe,EAAEpG,uBAAuB,CAAC0G,MAAM;MAAEC,SAAS,EAAE,CAC5JrG,uBAAuB,EACvB;QACI+E,OAAO,EAAE9E,yBAAyB;QAClC+E,QAAQ,EAAE;UACNC,iBAAiB,EAAE,IAAI;UACvBC,aAAa,EAAE,IAAI;UACnBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE;QACb;MACJ,CAAC,CACJ;MAAEkB,IAAI,EAAE;QACL,kBAAkB,EAAE,MAAM;QAC1B,oBAAoB,EAAE,KAAK;QAC3B,sBAAsB,EAAE;MAC5B,CAAC;MAAEZ,QAAQ,EAAE,gnBAAgnB;MAAEG,MAAM,EAAE,CAAC,s8DAAs8D;IAAE,CAAC;EAC7lF,CAAC,CAAC,QAAkB;IAAE7C,KAAK,EAAE,CAAC;MACtBe,IAAI,EAAEpE,YAAY;MAClBsG,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC,CAAC;IAAE3D,GAAG,EAAE,CAAC;MACNyB,IAAI,EAAEnE;IACV,CAAC,CAAC;IAAE2B,KAAK,EAAE,CAAC;MACRwC,IAAI,EAAEnE;IACV,CAAC,CAAC;IAAE2C,IAAI,EAAE,CAAC;MACPwB,IAAI,EAAEnE;IACV,CAAC,CAAC;IAAEiC,YAAY,EAAE,CAAC;MACfkC,IAAI,EAAEnE;IACV,CAAC,CAAC;IAAE0B,KAAK,EAAE,CAAC;MACRyC,IAAI,EAAEnE;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASmC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}