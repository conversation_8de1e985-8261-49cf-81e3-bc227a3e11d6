{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { inject, INJECTOR, Component, ChangeDetectionStrategy } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { TuiDialogService } from '@taiga-ui/core/components/dialog';\nimport { PolymorpheusComponent } from '@taiga-ui/polymorpheus';\nimport { from, of, switchMap } from 'rxjs';\nfunction tuiGenerateDialogableRoute(component, {\n  path = '',\n  outlet = '',\n  ...dialogOptions\n} = {}) {\n  return {\n    path,\n    outlet,\n    loadComponent: function () {\n      var _ref = _asyncToGenerator(function* () {\n        return Promise.resolve().then(function () {\n          return routableDialog_component;\n        });\n      });\n      return function loadComponent() {\n        return _ref.apply(this, arguments);\n      };\n    }(),\n    data: {\n      dialog: component,\n      backUrl: path.split('/').map(() => '..').join('/'),\n      isLazy: path === '',\n      dialogOptions\n    }\n  };\n}\nclass TuiRoutableDialog {\n  constructor() {\n    this.route = inject(ActivatedRoute);\n    this.router = inject(Router);\n    this.injector = inject(INJECTOR);\n    this.initialUrl = this.router.url;\n    this.dialog = inject(TuiDialogService);\n    const {\n      dialog\n    } = this.route.snapshot.data;\n    from(isClass(dialog) ? of(dialog) : dialog().then(m => m.default ?? m)).pipe(switchMap(dialog => this.dialog.open(new PolymorpheusComponent(dialog, this.injector), this.route.snapshot.data['dialogOptions'])), takeUntilDestroyed()).subscribe({\n      complete: () => this.onDialogClosing()\n    });\n  }\n  get lazyLoadedBackUrl() {\n    return (this.route.parent?.snapshot.url || []).map(() => '..').join('/');\n  }\n  onDialogClosing() {\n    if (this.initialUrl === this.router.url) {\n      this.navigateToParent();\n    }\n  }\n  navigateToParent() {\n    const backUrl = this.route.snapshot.data['isLazy'] ? this.lazyLoadedBackUrl : this.route.snapshot.data['backUrl'];\n    void this.router.navigate([backUrl], {\n      relativeTo: this.route\n    });\n  }\n  static {\n    this.ɵfac = function TuiRoutableDialog_Factory(t) {\n      return new (t || TuiRoutableDialog)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiRoutableDialog,\n      selectors: [[\"tui-routable-dialog\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiRoutableDialog_Template(rf, ctx) {},\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiRoutableDialog, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-routable-dialog',\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nfunction isClass(fn) {\n  return typeof fn === 'function' && Object.getOwnPropertyDescriptor(fn, 'prototype')?.writable === false;\n}\nvar routableDialog_component = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  default: TuiRoutableDialog\n});\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiGenerateDialogableRoute };", "map": {"version": 3, "names": ["i0", "inject", "INJECTOR", "Component", "ChangeDetectionStrategy", "takeUntilDestroyed", "ActivatedRoute", "Router", "TuiDialogService", "PolymorpheusComponent", "from", "of", "switchMap", "tuiGenerateDialogableRoute", "component", "path", "outlet", "dialogOptions", "loadComponent", "_ref", "_asyncToGenerator", "Promise", "resolve", "then", "routableDialog_component", "apply", "arguments", "data", "dialog", "backUrl", "split", "map", "join", "isLazy", "TuiRoutableDialog", "constructor", "route", "router", "injector", "initialUrl", "url", "snapshot", "isClass", "m", "default", "pipe", "open", "subscribe", "complete", "onDialogClosing", "lazyLoadedBackUrl", "parent", "navigateToParent", "navigate", "relativeTo", "ɵfac", "TuiRoutableDialog_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiRoutableDialog_Template", "rf", "ctx", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "fn", "Object", "getOwnPropertyDescriptor", "writable", "freeze", "__proto__"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-routable-dialog.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, INJECTOR, Component, ChangeDetectionStrategy } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { TuiDialogService } from '@taiga-ui/core/components/dialog';\nimport { PolymorpheusComponent } from '@taiga-ui/polymorpheus';\nimport { from, of, switchMap } from 'rxjs';\n\nfunction tuiGenerateDialogableRoute(component, { path = '', outlet = '', ...dialogOptions } = {}) {\n    return {\n        path,\n        outlet,\n        loadComponent: async () => Promise.resolve().then(function () { return routableDialog_component; }),\n        data: {\n            dialog: component,\n            backUrl: path\n                .split('/')\n                .map(() => '..')\n                .join('/'),\n            isLazy: path === '',\n            dialogOptions,\n        },\n    };\n}\n\nclass TuiRoutableDialog {\n    constructor() {\n        this.route = inject(ActivatedRoute);\n        this.router = inject(Router);\n        this.injector = inject(INJECTOR);\n        this.initialUrl = this.router.url;\n        this.dialog = inject(TuiDialogService);\n        const { dialog } = this.route.snapshot.data;\n        from(isClass(dialog) ? of(dialog) : dialog().then((m) => m.default ?? m))\n            .pipe(switchMap((dialog) => this.dialog.open(new PolymorpheusComponent(dialog, this.injector), this.route.snapshot.data['dialogOptions'])), takeUntilDestroyed())\n            .subscribe({ complete: () => this.onDialogClosing() });\n    }\n    get lazyLoadedBackUrl() {\n        return (this.route.parent?.snapshot.url || []).map(() => '..').join('/');\n    }\n    onDialogClosing() {\n        if (this.initialUrl === this.router.url) {\n            this.navigateToParent();\n        }\n    }\n    navigateToParent() {\n        const backUrl = this.route.snapshot.data['isLazy']\n            ? this.lazyLoadedBackUrl\n            : this.route.snapshot.data['backUrl'];\n        void this.router.navigate([backUrl], {\n            relativeTo: this.route,\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiRoutableDialog, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiRoutableDialog, isStandalone: true, selector: \"tui-routable-dialog\", ngImport: i0, template: '', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiRoutableDialog, decorators: [{\n            type: Component,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-routable-dialog',\n                    template: '',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }], ctorParameters: function () { return []; } });\nfunction isClass(fn) {\n    return (typeof fn === 'function' &&\n        Object.getOwnPropertyDescriptor(fn, 'prototype')?.writable === false);\n}\n\nvar routableDialog_component = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    default: TuiRoutableDialog\n});\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiGenerateDialogableRoute };\n"], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,uBAAuB,QAAQ,eAAe;AACpF,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,qBAAqB,QAAQ,wBAAwB;AAC9D,SAASC,IAAI,EAAEC,EAAE,EAAEC,SAAS,QAAQ,MAAM;AAE1C,SAASC,0BAA0BA,CAACC,SAAS,EAAE;EAAEC,IAAI,GAAG,EAAE;EAAEC,MAAM,GAAG,EAAE;EAAE,GAAGC;AAAc,CAAC,GAAG,CAAC,CAAC,EAAE;EAC9F,OAAO;IACHF,IAAI;IACJC,MAAM;IACNE,aAAa;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAE;QAAA,OAAYC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;UAAE,OAAOC,wBAAwB;QAAE,CAAC,CAAC;MAAA;MAAA,gBAAnGN,aAAaA,CAAA;QAAA,OAAAC,IAAA,CAAAM,KAAA,OAAAC,SAAA;MAAA;IAAA,GAAsF;IACnGC,IAAI,EAAE;MACFC,MAAM,EAAEd,SAAS;MACjBe,OAAO,EAAEd,IAAI,CACRe,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAC,MAAM,IAAI,CAAC,CACfC,IAAI,CAAC,GAAG,CAAC;MACdC,MAAM,EAAElB,IAAI,KAAK,EAAE;MACnBE;IACJ;EACJ,CAAC;AACL;AAEA,MAAMiB,iBAAiB,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAGnC,MAAM,CAACK,cAAc,CAAC;IACnC,IAAI,CAAC+B,MAAM,GAAGpC,MAAM,CAACM,MAAM,CAAC;IAC5B,IAAI,CAAC+B,QAAQ,GAAGrC,MAAM,CAACC,QAAQ,CAAC;IAChC,IAAI,CAACqC,UAAU,GAAG,IAAI,CAACF,MAAM,CAACG,GAAG;IACjC,IAAI,CAACZ,MAAM,GAAG3B,MAAM,CAACO,gBAAgB,CAAC;IACtC,MAAM;MAAEoB;IAAO,CAAC,GAAG,IAAI,CAACQ,KAAK,CAACK,QAAQ,CAACd,IAAI;IAC3CjB,IAAI,CAACgC,OAAO,CAACd,MAAM,CAAC,GAAGjB,EAAE,CAACiB,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,CAACL,IAAI,CAAEoB,CAAC,IAAKA,CAAC,CAACC,OAAO,IAAID,CAAC,CAAC,CAAC,CACpEE,IAAI,CAACjC,SAAS,CAAEgB,MAAM,IAAK,IAAI,CAACA,MAAM,CAACkB,IAAI,CAAC,IAAIrC,qBAAqB,CAACmB,MAAM,EAAE,IAAI,CAACU,QAAQ,CAAC,EAAE,IAAI,CAACF,KAAK,CAACK,QAAQ,CAACd,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EAAEtB,kBAAkB,CAAC,CAAC,CAAC,CAChK0C,SAAS,CAAC;MAAEC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACC,eAAe,CAAC;IAAE,CAAC,CAAC;EAC9D;EACA,IAAIC,iBAAiBA,CAAA,EAAG;IACpB,OAAO,CAAC,IAAI,CAACd,KAAK,CAACe,MAAM,EAAEV,QAAQ,CAACD,GAAG,IAAI,EAAE,EAAET,GAAG,CAAC,MAAM,IAAI,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAC5E;EACAiB,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACV,UAAU,KAAK,IAAI,CAACF,MAAM,CAACG,GAAG,EAAE;MACrC,IAAI,CAACY,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAA,gBAAgBA,CAAA,EAAG;IACf,MAAMvB,OAAO,GAAG,IAAI,CAACO,KAAK,CAACK,QAAQ,CAACd,IAAI,CAAC,QAAQ,CAAC,GAC5C,IAAI,CAACuB,iBAAiB,GACtB,IAAI,CAACd,KAAK,CAACK,QAAQ,CAACd,IAAI,CAAC,SAAS,CAAC;IACzC,KAAK,IAAI,CAACU,MAAM,CAACgB,QAAQ,CAAC,CAACxB,OAAO,CAAC,EAAE;MACjCyB,UAAU,EAAE,IAAI,CAAClB;IACrB,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACmB,IAAI,YAAAC,0BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFvB,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAACwB,IAAI,kBAD+E1D,EAAE,CAAA2D,iBAAA;MAAAC,IAAA,EACJ1B,iBAAiB;MAAA2B,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADf/D,EAAE,CAAAgE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACqK;EAAE;AAC9Q;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGzE,EAAE,CAAA0E,iBAAA,CAGXxC,iBAAiB,EAAc,CAAC;IAChH0B,IAAI,EAAEzD,SAAS;IACfwE,IAAI,EAAE,CAAC;MACCb,UAAU,EAAE,IAAI;MAChBc,QAAQ,EAAE,qBAAqB;MAC/BT,QAAQ,EAAE,EAAE;MACZK,eAAe,EAAEpE,uBAAuB,CAACyE;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AACtD,SAASnC,OAAOA,CAACoC,EAAE,EAAE;EACjB,OAAQ,OAAOA,EAAE,KAAK,UAAU,IAC5BC,MAAM,CAACC,wBAAwB,CAACF,EAAE,EAAE,WAAW,CAAC,EAAEG,QAAQ,KAAK,KAAK;AAC5E;AAEA,IAAIzD,wBAAwB,GAAG,aAAauD,MAAM,CAACG,MAAM,CAAC;EACtDC,SAAS,EAAE,IAAI;EACfvC,OAAO,EAAEV;AACb,CAAC,CAAC;;AAEF;AACA;AACA;;AAEA,SAASrB,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}