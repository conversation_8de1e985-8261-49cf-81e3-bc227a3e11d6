{"ast": null, "code": "import { TuiCalendarMonth } from '@taiga-ui/kit/components/calendar-month';\nimport { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, computed, effect, Directive, signal, Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { TUI_FIRST_DAY, TUI_LAST_DAY, TuiMonth } from '@taiga-ui/cdk/date-time';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldDirective, tuiTextfieldIconBinding, tuiInjectAuxiliary, TuiWithTextfield, TuiSelectLike, TuiTextfieldContent } from '@taiga-ui/core/components/textfield';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { TUI_IDENTITY_VALUE_TRANSFORMER, TuiControl, tuiAsControl, tuiValueTransformerFrom } from '@taiga-ui/cdk/classes';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { tuiDropdownOpen, tuiDropdownEnabled, TuiDropdownAuto } from '@taiga-ui/core/directives/dropdown';\nimport { TUI_MONTH_FORMATTER } from '@taiga-ui/kit/tokens';\nimport { tuiCreateTokenFromFactory, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_INPUT_DATE_OPTIONS_NEW, TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW } from '@taiga-ui/kit/components/input-date';\n\n/**\n * @deprecated remove in v5\n */\nconst _c0 = [\"tuiInputMonth\", \"\", \"type\", \"month\"];\nfunction TuiInputMonthComponent_ng_container_0_input_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 2);\n    i0.ɵɵlistener(\"click.stop.zoneless\", function TuiInputMonthComponent_ng_container_0_input_1_Template_input_click_stop_zoneless_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      return i0.ɵɵresetView(0);\n    })(\"input\", function TuiInputMonthComponent_ng_container_0_input_1_Template_input_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onInput($event.target.value));\n    })(\"pointerdown.stop.zoneless\", function TuiInputMonthComponent_ng_container_0_input_1_Template_input_pointerdown_stop_zoneless_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      return i0.ɵɵresetView(0);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"max\", (tmp_2_0 = ctx_r1.max()) == null ? null : tmp_2_0.toJSON())(\"min\", (tmp_3_0 = ctx_r1.min()) == null ? null : tmp_3_0.toJSON())(\"value\", (tmp_4_0 = ctx_r1.host.value()) == null ? null : tmp_4_0.toJSON());\n  }\n}\nfunction TuiInputMonthComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiInputMonthComponent_ng_container_0_input_1_Template, 1, 3, \"input\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst TUI_INPUT_MONTH_DEFAULT_OPTIONS = {\n  icon: () => '@tui.calendar',\n  valueTransformer: TUI_IDENTITY_VALUE_TRANSFORMER\n};\nconst TUI_INPUT_MONTH_OPTIONS = tuiCreateTokenFromFactory(() => ({\n  ...inject(TUI_INPUT_DATE_OPTIONS_NEW),\n  valueTransformer: TUI_IDENTITY_VALUE_TRANSFORMER\n}));\nconst tuiInputMonthOptionsProvider = options => tuiProvideOptions(TUI_INPUT_MONTH_OPTIONS, options, TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW);\nclass TuiInputMonthDirective extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.textfield = inject(TuiTextfieldDirective);\n    this.formatter = toSignal(inject(TUI_MONTH_FORMATTER));\n    this.open = tuiDropdownOpen();\n    this.icon = tuiTextfieldIconBinding(TUI_INPUT_MONTH_OPTIONS);\n    this.dropdownEnabled = tuiDropdownEnabled(computed(() => !this.native && this.interactive()));\n    this.valueEffect = effect(() => {\n      this.textfield.value.set(this.formatter()?.(this.value()) || '');\n    }, TUI_ALLOW_SIGNAL_WRITES);\n    this.calendarIn = effect(() => {\n      this.calendar()?.value.set(this.value());\n    }, TUI_ALLOW_SIGNAL_WRITES);\n    this.calendarOut = effect(onCleanup => {\n      const subscription = this.calendar()?.monthClick.subscribe(month => {\n        this.onChange(month);\n        this.open.set(false);\n      });\n      onCleanup(() => subscription?.unsubscribe());\n    });\n    this.calendar = tuiInjectAuxiliary(x => x instanceof TuiCalendarMonth);\n    this.native = tuiInjectElement().type === 'month' && inject(TUI_IS_MOBILE);\n  }\n  clear() {\n    this.onChange(null);\n    this.open.set(this.dropdownEnabled());\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiInputMonthDirective_BaseFactory;\n      return function TuiInputMonthDirective_Factory(t) {\n        return (ɵTuiInputMonthDirective_BaseFactory || (ɵTuiInputMonthDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiInputMonthDirective)))(t || TuiInputMonthDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiInputMonthDirective,\n      selectors: [[\"input\", \"tuiInputMonth\", \"\"]],\n      hostVars: 1,\n      hostBindings: function TuiInputMonthDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function TuiInputMonthDirective_input_HostBindingHandler($event) {\n            return ($event.inputType == null ? null : $event.inputType.includes(\"delete\")) && ctx.clear();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"disabled\", ctx.disabled());\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsControl(TuiInputMonthDirective), tuiValueTransformerFrom(TUI_INPUT_MONTH_OPTIONS)]), i0.ɵɵHostDirectivesFeature([i1.TuiWithTextfield, i1.TuiSelectLike, i2.TuiDropdownAuto]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputMonthDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiInputMonth]',\n      providers: [tuiAsControl(TuiInputMonthDirective), tuiValueTransformerFrom(TUI_INPUT_MONTH_OPTIONS)],\n      hostDirectives: [TuiWithTextfield, TuiSelectLike, TuiDropdownAuto],\n      host: {\n        '[disabled]': 'disabled()',\n        '(input)': '$event.inputType?.includes(\"delete\") && clear()'\n      }\n    }]\n  }], null, null);\n})();\nclass TuiInputMonthComponent {\n  constructor() {\n    this.host = inject(TuiInputMonthDirective);\n    this.min = signal(null);\n    this.max = signal(null);\n    this.calendarSync = effect(() => {\n      const calendar = this.host.calendar();\n      if (calendar) {\n        calendar.min.set(this.min() ?? TUI_FIRST_DAY); // TODO(v5): remove TUI_FIRST_DAY fallback\n        calendar.max.set(this.max() ?? TUI_LAST_DAY); // TODO(v5): remove TUI_LAST_DAY fallback\n      }\n    }, TUI_ALLOW_SIGNAL_WRITES);\n  }\n  // TODO(v5): use signal inputs\n  set minSetter(x) {\n    this.min.set(x);\n  }\n  // TODO(v5): use signal inputs\n  set maxSetter(x) {\n    this.max.set(x);\n  }\n  onInput(value) {\n    if (!value) {\n      return this.host.onChange(null);\n    }\n    const [year = 0, month = 0] = value.split('-').map(Number);\n    this.host.onChange(new TuiMonth(year, month - 1));\n  }\n  static {\n    this.ɵfac = function TuiInputMonthComponent_Factory(t) {\n      return new (t || TuiInputMonthComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiInputMonthComponent,\n      selectors: [[\"input\", \"tuiInputMonth\", \"\", \"type\", \"month\"]],\n      hostAttrs: [\"ngSkipHydration\", \"true\"],\n      hostVars: 1,\n      hostBindings: function TuiInputMonthComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"type\", \"text\");\n        }\n      },\n      inputs: {\n        minSetter: [i0.ɵɵInputFlags.None, \"min\", \"minSetter\"],\n        maxSetter: [i0.ɵɵInputFlags.None, \"max\", \"maxSetter\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"ngIf\"], [\"type\", \"month\", 3, \"max\", \"min\", \"value\", \"click.stop.zoneless\", \"input\", \"pointerdown.stop.zoneless\", 4, \"tuiTextfieldContent\"], [\"type\", \"month\", 3, \"click.stop.zoneless\", \"input\", \"pointerdown.stop.zoneless\", \"max\", \"min\", \"value\"]],\n      template: function TuiInputMonthComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiInputMonthComponent_ng_container_0_Template, 2, 0, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.host.native);\n        }\n      },\n      dependencies: [NgIf, TuiTextfieldContent],\n      styles: [\"tui-textfield input[tuiInputMonth]~.t-content input[type=month]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;opacity:0}tui-textfield input[tuiInputMonth]~.t-content input[type=month]::-webkit-calendar-picker-indicator{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputMonthComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiInputMonth][type=\"month\"]',\n      imports: [NgIf, TuiTextfieldContent],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        ngSkipHydration: 'true',\n        '[type]': '\"text\"'\n      },\n      template: \"<ng-container *ngIf=\\\"host.native\\\">\\n    <input\\n        *tuiTextfieldContent\\n        type=\\\"month\\\"\\n        [max]=\\\"max()?.toJSON()\\\"\\n        [min]=\\\"min()?.toJSON()\\\"\\n        [value]=\\\"host.value()?.toJSON()\\\"\\n        (click.stop.zoneless)=\\\"(0)\\\"\\n        (input)=\\\"onInput($any($event.target).value)\\\"\\n        (pointerdown.stop.zoneless)=\\\"(0)\\\"\\n    />\\n</ng-container>\\n\",\n      styles: [\"tui-textfield input[tuiInputMonth]~.t-content input[type=month]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;opacity:0}tui-textfield input[tuiInputMonth]~.t-content input[type=month]::-webkit-calendar-picker-indicator{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}\\n\"]\n    }]\n  }], null, {\n    minSetter: [{\n      type: Input,\n      args: ['min']\n    }],\n    maxSetter: [{\n      type: Input,\n      args: ['max']\n    }]\n  });\n})();\nconst TuiInputMonth = [TuiInputMonthComponent, TuiInputMonthDirective, TuiCalendarMonth];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_MONTH_DEFAULT_OPTIONS, TUI_INPUT_MONTH_OPTIONS, TuiInputMonth, TuiInputMonthComponent, TuiInputMonthDirective, tuiInputMonthOptionsProvider };", "map": {"version": 3, "names": ["TuiCalendarMonth", "NgIf", "i0", "inject", "computed", "effect", "Directive", "signal", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "TUI_ALLOW_SIGNAL_WRITES", "TUI_FIRST_DAY", "TUI_LAST_DAY", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i1", "TuiTextfieldDirective", "tuiTextfieldIconBinding", "tuiInjectAuxiliary", "TuiWithTextfield", "TuiSelectLike", "TuiTextfieldContent", "toSignal", "TUI_IDENTITY_VALUE_TRANSFORMER", "TuiControl", "tuiAsControl", "tuiValueTransformerFrom", "TUI_IS_MOBILE", "tuiInjectElement", "i2", "tuiDropdownOpen", "tuiDropdownEnabled", "TuiDropdownAuto", "TUI_MONTH_FORMATTER", "tuiCreateTokenFromFactory", "tuiProvideOptions", "TUI_INPUT_DATE_OPTIONS_NEW", "TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW", "_c0", "TuiInputMonthComponent_ng_container_0_input_1_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiInputMonthComponent_ng_container_0_input_1_Template_input_click_stop_zoneless_0_listener", "ɵɵrestoreView", "ɵɵresetView", "TuiInputMonthComponent_ng_container_0_input_1_Template_input_input_0_listener", "$event", "ctx_r1", "ɵɵnextContext", "onInput", "target", "value", "TuiInputMonthComponent_ng_container_0_input_1_Template_input_pointerdown_stop_zoneless_0_listener", "ɵɵelementEnd", "tmp_2_0", "tmp_3_0", "tmp_4_0", "ɵɵproperty", "max", "toJSON", "min", "host", "TuiInputMonthComponent_ng_container_0_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "TUI_INPUT_MONTH_DEFAULT_OPTIONS", "icon", "valueTransformer", "TUI_INPUT_MONTH_OPTIONS", "tuiInputMonthOptionsProvider", "options", "TuiInputMonthDirective", "constructor", "arguments", "textfield", "formatter", "open", "dropdownEnabled", "native", "interactive", "valueEffect", "set", "calendarIn", "calendar", "calendarOut", "onCleanup", "subscription", "monthClick", "subscribe", "month", "onChange", "unsubscribe", "x", "type", "clear", "ɵfac", "ɵTuiInputMonthDirective_BaseFactory", "TuiInputMonthDirective_Factory", "t", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "selectors", "hostVars", "hostBindings", "TuiInputMonthDirective_HostBindings", "TuiInputMonthDirective_input_HostBindingHandler", "inputType", "includes", "ɵɵhostProperty", "disabled", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "hostDirectives", "TuiInputMonthComponent", "calendarSync", "minSetter", "maxSetter", "year", "split", "map", "Number", "TuiInputMonthComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "hostAttrs", "TuiInputMonthComponent_HostBindings", "inputs", "ɵɵInputFlags", "None", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "consts", "template", "TuiInputMonthComponent_Template", "dependencies", "styles", "encapsulation", "changeDetection", "imports", "OnPush", "ngSkipHydration", "TuiInputMonth"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-input-month.mjs"], "sourcesContent": ["import { TuiCalendarMonth } from '@taiga-ui/kit/components/calendar-month';\nimport { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, computed, effect, Directive, signal, Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { TUI_FIRST_DAY, TUI_LAST_DAY, TuiMonth } from '@taiga-ui/cdk/date-time';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldDirective, tuiTextfieldIconBinding, tuiInjectAuxiliary, TuiWithTextfield, TuiSelectLike, TuiTextfieldContent } from '@taiga-ui/core/components/textfield';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { TUI_IDENTITY_VALUE_TRANSFORMER, TuiControl, tuiAsControl, tuiValueTransformerFrom } from '@taiga-ui/cdk/classes';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { tuiDropdownOpen, tuiDropdownEnabled, TuiDropdownAuto } from '@taiga-ui/core/directives/dropdown';\nimport { TUI_MONTH_FORMATTER } from '@taiga-ui/kit/tokens';\nimport { tuiCreateTokenFromFactory, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_INPUT_DATE_OPTIONS_NEW, TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW } from '@taiga-ui/kit/components/input-date';\n\n/**\n * @deprecated remove in v5\n */\nconst TUI_INPUT_MONTH_DEFAULT_OPTIONS = {\n    icon: () => '@tui.calendar',\n    valueTransformer: TUI_IDENTITY_VALUE_TRANSFORMER,\n};\nconst TUI_INPUT_MONTH_OPTIONS = tuiCreateTokenFromFactory(() => ({\n    ...inject(TUI_INPUT_DATE_OPTIONS_NEW),\n    valueTransformer: TUI_IDENTITY_VALUE_TRANSFORMER,\n}));\nconst tuiInputMonthOptionsProvider = (options) => tuiProvideOptions(TUI_INPUT_MONTH_OPTIONS, options, TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW);\n\nclass TuiInputMonthDirective extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.textfield = inject(TuiTextfieldDirective);\n        this.formatter = toSignal(inject(TUI_MONTH_FORMATTER));\n        this.open = tuiDropdownOpen();\n        this.icon = tuiTextfieldIconBinding(TUI_INPUT_MONTH_OPTIONS);\n        this.dropdownEnabled = tuiDropdownEnabled(computed(() => !this.native && this.interactive()));\n        this.valueEffect = effect(() => {\n            this.textfield.value.set(this.formatter()?.(this.value()) || '');\n        }, TUI_ALLOW_SIGNAL_WRITES);\n        this.calendarIn = effect(() => {\n            this.calendar()?.value.set(this.value());\n        }, TUI_ALLOW_SIGNAL_WRITES);\n        this.calendarOut = effect((onCleanup) => {\n            const subscription = this.calendar()?.monthClick.subscribe((month) => {\n                this.onChange(month);\n                this.open.set(false);\n            });\n            onCleanup(() => subscription?.unsubscribe());\n        });\n        this.calendar = tuiInjectAuxiliary((x) => x instanceof TuiCalendarMonth);\n        this.native = tuiInjectElement().type === 'month' && inject(TUI_IS_MOBILE);\n    }\n    clear() {\n        this.onChange(null);\n        this.open.set(this.dropdownEnabled());\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputMonthDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputMonthDirective, isStandalone: true, selector: \"input[tuiInputMonth]\", host: { listeners: { \"input\": \"$event.inputType?.includes(\\\"delete\\\") && clear()\" }, properties: { \"disabled\": \"disabled()\" } }, providers: [\n            tuiAsControl(TuiInputMonthDirective),\n            tuiValueTransformerFrom(TUI_INPUT_MONTH_OPTIONS),\n        ], usesInheritance: true, hostDirectives: [{ directive: i1.TuiWithTextfield }, { directive: i1.TuiSelectLike }, { directive: i2.TuiDropdownAuto }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputMonthDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiInputMonth]',\n                    providers: [\n                        tuiAsControl(TuiInputMonthDirective),\n                        tuiValueTransformerFrom(TUI_INPUT_MONTH_OPTIONS),\n                    ],\n                    hostDirectives: [TuiWithTextfield, TuiSelectLike, TuiDropdownAuto],\n                    host: {\n                        '[disabled]': 'disabled()',\n                        '(input)': '$event.inputType?.includes(\"delete\") && clear()',\n                    },\n                }]\n        }] });\n\nclass TuiInputMonthComponent {\n    constructor() {\n        this.host = inject(TuiInputMonthDirective);\n        this.min = signal(null);\n        this.max = signal(null);\n        this.calendarSync = effect(() => {\n            const calendar = this.host.calendar();\n            if (calendar) {\n                calendar.min.set(this.min() ?? TUI_FIRST_DAY); // TODO(v5): remove TUI_FIRST_DAY fallback\n                calendar.max.set(this.max() ?? TUI_LAST_DAY); // TODO(v5): remove TUI_LAST_DAY fallback\n            }\n        }, TUI_ALLOW_SIGNAL_WRITES);\n    }\n    // TODO(v5): use signal inputs\n    set minSetter(x) {\n        this.min.set(x);\n    }\n    // TODO(v5): use signal inputs\n    set maxSetter(x) {\n        this.max.set(x);\n    }\n    onInput(value) {\n        if (!value) {\n            return this.host.onChange(null);\n        }\n        const [year = 0, month = 0] = value.split('-').map(Number);\n        this.host.onChange(new TuiMonth(year, month - 1));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputMonthComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputMonthComponent, isStandalone: true, selector: \"input[tuiInputMonth][type=\\\"month\\\"]\", inputs: { minSetter: [\"min\", \"minSetter\"], maxSetter: [\"max\", \"maxSetter\"] }, host: { attributes: { \"ngSkipHydration\": \"true\" }, properties: { \"type\": \"\\\"text\\\"\" } }, ngImport: i0, template: \"<ng-container *ngIf=\\\"host.native\\\">\\n    <input\\n        *tuiTextfieldContent\\n        type=\\\"month\\\"\\n        [max]=\\\"max()?.toJSON()\\\"\\n        [min]=\\\"min()?.toJSON()\\\"\\n        [value]=\\\"host.value()?.toJSON()\\\"\\n        (click.stop.zoneless)=\\\"(0)\\\"\\n        (input)=\\\"onInput($any($event.target).value)\\\"\\n        (pointerdown.stop.zoneless)=\\\"(0)\\\"\\n    />\\n</ng-container>\\n\", styles: [\"tui-textfield input[tuiInputMonth]~.t-content input[type=month]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;opacity:0}tui-textfield input[tuiInputMonth]~.t-content input[type=month]::-webkit-calendar-picker-indicator{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: TuiTextfieldContent, selector: \"ng-template[tuiTextfieldContent]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputMonthComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'input[tuiInputMonth][type=\"month\"]', imports: [NgIf, TuiTextfieldContent], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        ngSkipHydration: 'true',\n                        '[type]': '\"text\"',\n                    }, template: \"<ng-container *ngIf=\\\"host.native\\\">\\n    <input\\n        *tuiTextfieldContent\\n        type=\\\"month\\\"\\n        [max]=\\\"max()?.toJSON()\\\"\\n        [min]=\\\"min()?.toJSON()\\\"\\n        [value]=\\\"host.value()?.toJSON()\\\"\\n        (click.stop.zoneless)=\\\"(0)\\\"\\n        (input)=\\\"onInput($any($event.target).value)\\\"\\n        (pointerdown.stop.zoneless)=\\\"(0)\\\"\\n    />\\n</ng-container>\\n\", styles: [\"tui-textfield input[tuiInputMonth]~.t-content input[type=month]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;opacity:0}tui-textfield input[tuiInputMonth]~.t-content input[type=month]::-webkit-calendar-picker-indicator{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}\\n\"] }]\n        }], propDecorators: { minSetter: [{\n                type: Input,\n                args: ['min']\n            }], maxSetter: [{\n                type: Input,\n                args: ['max']\n            }] } });\n\nconst TuiInputMonth = [\n    TuiInputMonthComponent,\n    TuiInputMonthDirective,\n    TuiCalendarMonth,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_MONTH_DEFAULT_OPTIONS, TUI_INPUT_MONTH_OPTIONS, TuiInputMonth, TuiInputMonthComponent, TuiInputMonthDirective, tuiInputMonthOptionsProvider };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,IAAI,QAAQ,iBAAiB;AACtC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,QAAQ,eAAe;AACzI,SAASC,uBAAuB,QAAQ,yBAAyB;AACjE,SAASC,aAAa,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,yBAAyB;AAC/E,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,qBAAqB,EAAEC,uBAAuB,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,mBAAmB,QAAQ,qCAAqC;AAC9K,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,8BAA8B,EAAEC,UAAU,EAAEC,YAAY,EAAEC,uBAAuB,QAAQ,uBAAuB;AACzH,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,OAAO,KAAKC,EAAE,MAAM,oCAAoC;AACxD,SAASC,eAAe,EAAEC,kBAAkB,EAAEC,eAAe,QAAQ,oCAAoC;AACzG,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,yBAAyB,EAAEC,iBAAiB,QAAQ,mCAAmC;AAChG,SAASC,0BAA0B,EAAEC,kCAAkC,QAAQ,qCAAqC;;AAEpH;AACA;AACA;AAFA,MAAAC,GAAA;AAAA,SAAAC,uDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAyCqGzC,EAAE,CAAA0C,gBAAA;IAAF1C,EAAE,CAAA2C,cAAA,cAoDqoB,CAAC;IApDxoB3C,EAAE,CAAA4C,UAAA,iCAAAC,4FAAA;MAAF7C,EAAE,CAAA8C,aAAA,CAAAL,GAAA;MAAA,OAAFzC,EAAE,CAAA+C,WAAA,CAoDqhB,CAAC;IAAA,CAAE,CAAC,mBAAAC,8EAAAC,MAAA;MApD3hBjD,EAAE,CAAA8C,aAAA,CAAAL,GAAA;MAAA,MAAAS,MAAA,GAAFlD,EAAE,CAAAmD,aAAA;MAAA,OAAFnD,EAAE,CAAA+C,WAAA,CAoD6iBG,MAAA,CAAAE,OAAA,CAAAH,MAAA,CAAAI,MAAA,CAAAC,KAAiC,CAAC;IAAA,CAAC,CAAC,uCAAAC,kGAAA;MApDnlBvD,EAAE,CAAA8C,aAAA,CAAAL,GAAA;MAAA,OAAFzC,EAAE,CAAA+C,WAAA,CAoD0nB,CAAC;IAAA,CAAE,CAAC;IApDhoB/C,EAAE,CAAAwD,YAAA,CAoDqoB,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,IAAAkB,OAAA;IAAA,IAAAC,OAAA;IAAA,IAAAC,OAAA;IAAA,MAAAT,MAAA,GApDxoBlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA4D,UAAA,SAAAH,OAAA,GAAAP,MAAA,CAAAW,GAAA,qBAAAJ,OAAA,CAAAK,MAAA,EAoDka,CAAC,SAAAJ,OAAA,GAAAR,MAAA,CAAAa,GAAA,qBAAAL,OAAA,CAAAI,MAAA,EAAkC,CAAC,WAAAH,OAAA,GAAAT,MAAA,CAAAc,IAAA,CAAAV,KAAA,qBAAAK,OAAA,CAAAG,MAAA,EAA2C,CAAC;EAAA;AAAA;AAAA,SAAAG,+CAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApDpfvC,EAAE,CAAAkE,uBAAA,EAoD6T,CAAC;IApDhUlE,EAAE,CAAAmE,UAAA,IAAA7B,sDAAA,kBAoDqoB,CAAC;IApDxoBtC,EAAE,CAAAoE,qBAAA;EAAA;AAAA;AAtCvG,MAAMC,+BAA+B,GAAG;EACpCC,IAAI,EAAEA,CAAA,KAAM,eAAe;EAC3BC,gBAAgB,EAAEjD;AACtB,CAAC;AACD,MAAMkD,uBAAuB,GAAGvC,yBAAyB,CAAC,OAAO;EAC7D,GAAGhC,MAAM,CAACkC,0BAA0B,CAAC;EACrCoC,gBAAgB,EAAEjD;AACtB,CAAC,CAAC,CAAC;AACH,MAAMmD,4BAA4B,GAAIC,OAAO,IAAKxC,iBAAiB,CAACsC,uBAAuB,EAAEE,OAAO,EAAEtC,kCAAkC,CAAC;AAEzI,MAAMuC,sBAAsB,SAASpD,UAAU,CAAC;EAC5CqD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,SAAS,GAAG7E,MAAM,CAACc,qBAAqB,CAAC;IAC9C,IAAI,CAACgE,SAAS,GAAG1D,QAAQ,CAACpB,MAAM,CAAC+B,mBAAmB,CAAC,CAAC;IACtD,IAAI,CAACgD,IAAI,GAAGnD,eAAe,CAAC,CAAC;IAC7B,IAAI,CAACyC,IAAI,GAAGtD,uBAAuB,CAACwD,uBAAuB,CAAC;IAC5D,IAAI,CAACS,eAAe,GAAGnD,kBAAkB,CAAC5B,QAAQ,CAAC,MAAM,CAAC,IAAI,CAACgF,MAAM,IAAI,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;IAC7F,IAAI,CAACC,WAAW,GAAGjF,MAAM,CAAC,MAAM;MAC5B,IAAI,CAAC2E,SAAS,CAACxB,KAAK,CAAC+B,GAAG,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC,GAAG,IAAI,CAACzB,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACpE,CAAC,EAAE5C,uBAAuB,CAAC;IAC3B,IAAI,CAAC4E,UAAU,GAAGnF,MAAM,CAAC,MAAM;MAC3B,IAAI,CAACoF,QAAQ,CAAC,CAAC,EAAEjC,KAAK,CAAC+B,GAAG,CAAC,IAAI,CAAC/B,KAAK,CAAC,CAAC,CAAC;IAC5C,CAAC,EAAE5C,uBAAuB,CAAC;IAC3B,IAAI,CAAC8E,WAAW,GAAGrF,MAAM,CAAEsF,SAAS,IAAK;MACrC,MAAMC,YAAY,GAAG,IAAI,CAACH,QAAQ,CAAC,CAAC,EAAEI,UAAU,CAACC,SAAS,CAAEC,KAAK,IAAK;QAClE,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC;QACpB,IAAI,CAACb,IAAI,CAACK,GAAG,CAAC,KAAK,CAAC;MACxB,CAAC,CAAC;MACFI,SAAS,CAAC,MAAMC,YAAY,EAAEK,WAAW,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC;IACF,IAAI,CAACR,QAAQ,GAAGtE,kBAAkB,CAAE+E,CAAC,IAAKA,CAAC,YAAYlG,gBAAgB,CAAC;IACxE,IAAI,CAACoF,MAAM,GAAGvD,gBAAgB,CAAC,CAAC,CAACsE,IAAI,KAAK,OAAO,IAAIhG,MAAM,CAACyB,aAAa,CAAC;EAC9E;EACAwE,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACJ,QAAQ,CAAC,IAAI,CAAC;IACnB,IAAI,CAACd,IAAI,CAACK,GAAG,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAC;EACzC;EACA;IAAS,IAAI,CAACkB,IAAI;MAAA,IAAAC,mCAAA;MAAA,gBAAAC,+BAAAC,CAAA;QAAA,QAAAF,mCAAA,KAAAA,mCAAA,GAA+EpG,EAAE,CAAAuG,qBAAA,CAAQ5B,sBAAsB,IAAA2B,CAAA,IAAtB3B,sBAAsB;MAAA;IAAA,IAAqD;EAAE;EACxL;IAAS,IAAI,CAAC6B,IAAI,kBAD+ExG,EAAE,CAAAyG,iBAAA;MAAAR,IAAA,EACJtB,sBAAsB;MAAA+B,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,oCAAAtE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADpBvC,EAAE,CAAA4C,UAAA,mBAAAkE,gDAAA7D,MAAA;YAAA,QAAAA,MAAA,CAAA8D,SAAA,kBAAA9D,MAAA,CAAA8D,SAAA,CAAAC,QAAA,CACuB,QAAQ,MAAKxE,GAAA,CAAA0D,KAAA,CAAM,CAAC;UAAA,CAA1B,CAAC;QAAA;QAAA,IAAA3D,EAAA;UADpBvC,EAAE,CAAAiH,cAAA,aACJzE,GAAA,CAAA0E,QAAA,CAAS,CAAY,CAAC;QAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GADpBpH,EAAE,CAAAqH,kBAAA,CACsN,CACjT7F,YAAY,CAACmD,sBAAsB,CAAC,EACpClD,uBAAuB,CAAC+C,uBAAuB,CAAC,CACnD,GAJ4FxE,EAAE,CAAAsH,uBAAA,EAIvCxG,EAAE,CAACI,gBAAgB,EAAiBJ,EAAE,CAACK,aAAa,EAAiBS,EAAE,CAACG,eAAe,IAJlD/B,EAAE,CAAAuH,0BAAA;IAAA,EAIoE;EAAE;AAC7K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KANqGxH,EAAE,CAAAyH,iBAAA,CAMX9C,sBAAsB,EAAc,CAAC;IACrHsB,IAAI,EAAE7F,SAAS;IACfsH,IAAI,EAAE,CAAC;MACCP,UAAU,EAAE,IAAI;MAChBQ,QAAQ,EAAE,sBAAsB;MAChCC,SAAS,EAAE,CACPpG,YAAY,CAACmD,sBAAsB,CAAC,EACpClD,uBAAuB,CAAC+C,uBAAuB,CAAC,CACnD;MACDqD,cAAc,EAAE,CAAC3G,gBAAgB,EAAEC,aAAa,EAAEY,eAAe,CAAC;MAClEiC,IAAI,EAAE;QACF,YAAY,EAAE,YAAY;QAC1B,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM8D,sBAAsB,CAAC;EACzBlD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACZ,IAAI,GAAG/D,MAAM,CAAC0E,sBAAsB,CAAC;IAC1C,IAAI,CAACZ,GAAG,GAAG1D,MAAM,CAAC,IAAI,CAAC;IACvB,IAAI,CAACwD,GAAG,GAAGxD,MAAM,CAAC,IAAI,CAAC;IACvB,IAAI,CAAC0H,YAAY,GAAG5H,MAAM,CAAC,MAAM;MAC7B,MAAMoF,QAAQ,GAAG,IAAI,CAACvB,IAAI,CAACuB,QAAQ,CAAC,CAAC;MACrC,IAAIA,QAAQ,EAAE;QACVA,QAAQ,CAACxB,GAAG,CAACsB,GAAG,CAAC,IAAI,CAACtB,GAAG,CAAC,CAAC,IAAIpD,aAAa,CAAC,CAAC,CAAC;QAC/C4E,QAAQ,CAAC1B,GAAG,CAACwB,GAAG,CAAC,IAAI,CAACxB,GAAG,CAAC,CAAC,IAAIjD,YAAY,CAAC,CAAC,CAAC;MAClD;IACJ,CAAC,EAAEF,uBAAuB,CAAC;EAC/B;EACA;EACA,IAAIsH,SAASA,CAAChC,CAAC,EAAE;IACb,IAAI,CAACjC,GAAG,CAACsB,GAAG,CAACW,CAAC,CAAC;EACnB;EACA;EACA,IAAIiC,SAASA,CAACjC,CAAC,EAAE;IACb,IAAI,CAACnC,GAAG,CAACwB,GAAG,CAACW,CAAC,CAAC;EACnB;EACA5C,OAAOA,CAACE,KAAK,EAAE;IACX,IAAI,CAACA,KAAK,EAAE;MACR,OAAO,IAAI,CAACU,IAAI,CAAC8B,QAAQ,CAAC,IAAI,CAAC;IACnC;IACA,MAAM,CAACoC,IAAI,GAAG,CAAC,EAAErC,KAAK,GAAG,CAAC,CAAC,GAAGvC,KAAK,CAAC6E,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IAC1D,IAAI,CAACrE,IAAI,CAAC8B,QAAQ,CAAC,IAAIjF,QAAQ,CAACqH,IAAI,EAAErC,KAAK,GAAG,CAAC,CAAC,CAAC;EACrD;EACA;IAAS,IAAI,CAACM,IAAI,YAAAmC,+BAAAhC,CAAA;MAAA,YAAAA,CAAA,IAAyFwB,sBAAsB;IAAA,CAAmD;EAAE;EACtL;IAAS,IAAI,CAACS,IAAI,kBApD+EvI,EAAE,CAAAwI,iBAAA;MAAAvC,IAAA,EAoDJ6B,sBAAsB;MAAApB,SAAA;MAAA+B,SAAA,sBAA+L,MAAM;MAAA9B,QAAA;MAAAC,YAAA,WAAA8B,oCAAAnG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApDzNvC,EAAE,CAAAiH,cAAA,SAoDJ,MAAqB,CAAC;QAAA;MAAA;MAAA0B,MAAA;QAAAX,SAAA,GApDpBhI,EAAE,CAAA4I,YAAA,CAAAC,IAAA;QAAAZ,SAAA,GAAFjI,EAAE,CAAA4I,YAAA,CAAAC,IAAA;MAAA;MAAA1B,UAAA;MAAAC,QAAA,GAAFpH,EAAE,CAAA8I,mBAAA;MAAAC,KAAA,EAAA1G,GAAA;MAAA2G,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAA7G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvC,EAAE,CAAAmE,UAAA,IAAAF,8CAAA,yBAoD6T,CAAC;QAAA;QAAA,IAAA1B,EAAA;UApDhUvC,EAAE,CAAA4D,UAAA,SAAApB,GAAA,CAAAwB,IAAA,CAAAkB,MAoD0T,CAAC;QAAA;MAAA;MAAAmE,YAAA,GAAwsBtJ,IAAI,EAA6FqB,mBAAmB;MAAAkI,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAkJ;EAAE;AACl3C;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KAtDqGxH,EAAE,CAAAyH,iBAAA,CAsDXK,sBAAsB,EAAc,CAAC;IACrH7B,IAAI,EAAE3F,SAAS;IACfoH,IAAI,EAAE,CAAC;MAAEP,UAAU,EAAE,IAAI;MAAEQ,QAAQ,EAAE,oCAAoC;MAAE8B,OAAO,EAAE,CAAC1J,IAAI,EAAEqB,mBAAmB,CAAC;MAAEmI,aAAa,EAAEhJ,iBAAiB,CAACsI,IAAI;MAAEW,eAAe,EAAEhJ,uBAAuB,CAACkJ,MAAM;MAAE1F,IAAI,EAAE;QACnM2F,eAAe,EAAE,MAAM;QACvB,QAAQ,EAAE;MACd,CAAC;MAAER,QAAQ,EAAE,iYAAiY;MAAEG,MAAM,EAAE,CAAC,iTAAiT;IAAE,CAAC;EACztB,CAAC,CAAC,QAAkB;IAAEtB,SAAS,EAAE,CAAC;MAC1B/B,IAAI,EAAExF,KAAK;MACXiH,IAAI,EAAE,CAAC,KAAK;IAChB,CAAC,CAAC;IAAEO,SAAS,EAAE,CAAC;MACZhC,IAAI,EAAExF,KAAK;MACXiH,IAAI,EAAE,CAAC,KAAK;IAChB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkC,aAAa,GAAG,CAClB9B,sBAAsB,EACtBnD,sBAAsB,EACtB7E,gBAAgB,CACnB;;AAED;AACA;AACA;;AAEA,SAASuE,+BAA+B,EAAEG,uBAAuB,EAAEoF,aAAa,EAAE9B,sBAAsB,EAAEnD,sBAAsB,EAAEF,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}