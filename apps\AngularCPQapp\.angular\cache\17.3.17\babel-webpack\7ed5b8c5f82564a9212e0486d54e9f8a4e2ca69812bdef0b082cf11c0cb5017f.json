{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { signal, inject, computed, Component, ChangeDetectionStrategy, Input, ContentChildren, Directive } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { NgControl } from '@angular/forms';\nimport { tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiOptionWithValue, tuiAsOptionContent } from '@taiga-ui/core/components/data-list';\nimport { TuiLink } from '@taiga-ui/core/components/link';\nimport { TuiTextfieldComponent, TUI_TEXTFIELD_OPTIONS } from '@taiga-ui/core/components/textfield';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { TUI_MULTI_SELECT_TEXTS } from '@taiga-ui/kit/tokens';\nimport { tuiInjectValue } from '@taiga-ui/kit/utils';\nimport { NgIf } from '@angular/common';\nimport { TuiCheckbox } from '@taiga-ui/kit/components/checkbox';\nconst _c0 = [\"tuiMultiSelectGroup\", \"\"];\nconst _c1 = [\"*\"];\nfunction TuiMultiSelectOption_input_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"checked\", ctx_r0.selected())(\"size\", ctx_r0.size() === \"l\" ? \"m\" : \"s\");\n  }\n}\nclass TuiMultiSelectGroupComponent {\n  constructor() {\n    this.values = signal([]);\n    this.handlers = inject(TUI_ITEMS_HANDLERS);\n    this.control = inject(TuiTextfieldComponent, {\n      optional: true\n    })?.control || inject(NgControl, {\n      optional: true\n    });\n    this.texts = toSignal(inject(TUI_MULTI_SELECT_TEXTS));\n    this.value = tuiInjectValue();\n    this.checked = computed(() => this.values().every(item => this.value().some(value => this.handlers.identityMatcher()(item, value))));\n    this.label = '';\n  }\n  set options(options) {\n    this.values.set(options.toArray().map(({\n      value\n    }) => value()).filter(tuiIsPresent));\n  }\n  toggle() {\n    const values = this.values();\n    const matcher = this.handlers.identityMatcher();\n    const others = this.value().filter(current => values.every(item => !matcher(current, item)));\n    this.control?.control?.setValue(this.checked() ? others : others.concat(values));\n  }\n  static {\n    this.ɵfac = function TuiMultiSelectGroupComponent_Factory(t) {\n      return new (t || TuiMultiSelectGroupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiMultiSelectGroupComponent,\n      selectors: [[\"tui-opt-group\", \"tuiMultiSelectGroup\", \"\"]],\n      contentQueries: function TuiMultiSelectGroupComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiOptionWithValue, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n        }\n      },\n      hostVars: 2,\n      hostBindings: function TuiMultiSelectGroupComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_label\", ctx.label);\n        }\n      },\n      inputs: {\n        label: \"label\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 6,\n      vars: 2,\n      consts: [[1, \"t-wrapper\"], [1, \"t-label\"], [\"tuiLink\", \"\", \"type\", \"button\", 1, \"t-button\", 3, \"click\"]],\n      template: function TuiMultiSelectGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"span\", 0)(1, \"span\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function TuiMultiSelectGroupComponent_Template_button_click_3_listener() {\n            return ctx.toggle();\n          });\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵprojection(5);\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.label);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", (tmp_1_0 = ctx.texts()) == null ? null : tmp_1_0[ctx.checked() ? \"none\" : \"all\"], \" \");\n        }\n      },\n      dependencies: [TuiLink],\n      styles: [\"._label[_nghost-%COMP%]:before{display:none}[_nghost-%COMP%]:not(:first-of-type)   .t-label[_ngcontent-%COMP%]:not(:empty){padding-top:1.25rem}[_nghost-%COMP%]:not(:first-of-type)   .t-button[_ngcontent-%COMP%]{margin-top:1.25rem}.t-wrapper[_ngcontent-%COMP%]{display:flex;align-items:flex-start}.t-wrapper[_ngcontent-%COMP%]:last-child{display:none}.t-label[_ngcontent-%COMP%]:not(:empty){flex:1;padding:.75rem 1rem .25rem .625rem}.t-button[_ngcontent-%COMP%]{margin:.75rem 1rem 0 auto;font-weight:400}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiMultiSelectGroupComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-opt-group[tuiMultiSelectGroup]',\n      imports: [TuiLink],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class._label]': 'label'\n      },\n      template: \"<span class=\\\"t-wrapper\\\">\\n    <span class=\\\"t-label\\\">{{ label }}</span>\\n    <button\\n        tuiLink\\n        type=\\\"button\\\"\\n        class=\\\"t-button\\\"\\n        (click)=\\\"toggle()\\\"\\n    >\\n        {{ texts()?.[checked() ? 'none' : 'all'] }}\\n    </button>\\n</span>\\n<ng-content />\\n\",\n      styles: [\":host._label:before{display:none}:host:not(:first-of-type) .t-label:not(:empty){padding-top:1.25rem}:host:not(:first-of-type) .t-button{margin-top:1.25rem}.t-wrapper{display:flex;align-items:flex-start}.t-wrapper:last-child{display:none}.t-label:not(:empty){flex:1;padding:.75rem 1rem .25rem .625rem}.t-button{margin:.75rem 1rem 0 auto;font-weight:400}\\n\"]\n    }]\n  }], null, {\n    label: [{\n      type: Input\n    }],\n    options: [{\n      type: ContentChildren,\n      args: [TuiOptionWithValue]\n    }]\n  });\n})();\nclass TuiMultiSelectOption {\n  constructor() {\n    this.option = inject(TuiOptionWithValue, {\n      optional: true\n    });\n    this.handlers = inject(TUI_ITEMS_HANDLERS);\n    this.value = tuiInjectValue();\n    this.size = inject(TUI_TEXTFIELD_OPTIONS).size;\n    this.selected = computed((controlValue = this.value(), optionValue = this.option?.value()) => tuiIsPresent(optionValue) && tuiIsPresent(controlValue) && controlValue.some(item => this.handlers?.identityMatcher()(item, optionValue) ?? item === optionValue));\n  }\n  static {\n    this.ɵfac = function TuiMultiSelectOption_Factory(t) {\n      return new (t || TuiMultiSelectOption)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiMultiSelectOption,\n      selectors: [[\"ng-component\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[\"tuiCheckbox\", \"\", \"type\", \"checkbox\", 3, \"checked\", \"size\", 4, \"ngIf\"], [\"tuiCheckbox\", \"\", \"type\", \"checkbox\", 3, \"checked\", \"size\"]],\n      template: function TuiMultiSelectOption_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiMultiSelectOption_input_0_Template, 1, 2, \"input\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.option);\n        }\n      },\n      dependencies: [NgIf, TuiCheckbox],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiMultiSelectOption, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      imports: [NgIf, TuiCheckbox],\n      template: `\n        <input\n            *ngIf=\"option\"\n            tuiCheckbox\n            type=\"checkbox\"\n            [checked]=\"selected()\"\n            [size]=\"size() === 'l' ? 'm' : 's'\"\n        />\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\nclass TuiMultiSelectGroupDirective {\n  static {\n    this.ɵfac = function TuiMultiSelectGroupDirective_Factory(t) {\n      return new (t || TuiMultiSelectGroupDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiMultiSelectGroupDirective,\n      selectors: [[\"\", \"tuiMultiSelectGroup\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsOptionContent(TuiMultiSelectOption)])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiMultiSelectGroupDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiMultiSelectGroup]',\n      providers: [tuiAsOptionContent(TuiMultiSelectOption)]\n    }]\n  }], null, null);\n})();\nconst TuiMultiSelect = [TuiMultiSelectGroupComponent, TuiMultiSelectGroupDirective];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiMultiSelect, TuiMultiSelectGroupComponent, TuiMultiSelectGroupDirective, TuiMultiSelectOption };", "map": {"version": 3, "names": ["i0", "signal", "inject", "computed", "Component", "ChangeDetectionStrategy", "Input", "ContentChildren", "Directive", "toSignal", "NgControl", "tuiIsPresent", "TuiOptionWithValue", "tuiAsOptionContent", "TuiLink", "TuiTextfieldComponent", "TUI_TEXTFIELD_OPTIONS", "TUI_ITEMS_HANDLERS", "TUI_MULTI_SELECT_TEXTS", "tuiInjectValue", "NgIf", "TuiCheckbox", "_c0", "_c1", "TuiMultiSelectOption_input_0_Template", "rf", "ctx", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "selected", "size", "TuiMultiSelectGroupComponent", "constructor", "values", "handlers", "control", "optional", "texts", "value", "checked", "every", "item", "some", "identityMatcher", "label", "options", "set", "toArray", "map", "filter", "toggle", "matcher", "others", "current", "setValue", "concat", "ɵfac", "TuiMultiSelectGroupComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "TuiMultiSelectGroupComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostVars", "hostBindings", "TuiMultiSelectGroupComponent_HostBindings", "ɵɵclassProp", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "TuiMultiSelectGroupComponent_Template", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TuiMultiSelectGroupComponent_Template_button_click_3_listener", "ɵɵprojection", "tmp_1_0", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "dependencies", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "host", "TuiMultiSelectOption", "option", "controlValue", "optionValue", "TuiMultiSelectOption_Factory", "TuiMultiSelectOption_Template", "ɵɵtemplate", "encapsulation", "TuiMultiSelectGroupDirective", "TuiMultiSelectGroupDirective_Factory", "ɵdir", "ɵɵdefineDirective", "ɵɵProvidersFeature", "providers", "TuiMultiSelect"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-multi-select.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { signal, inject, computed, Component, ChangeDetectionStrategy, Input, ContentChildren, Directive } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { NgControl } from '@angular/forms';\nimport { tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiOptionWithValue, tuiAsOptionContent } from '@taiga-ui/core/components/data-list';\nimport { TuiLink } from '@taiga-ui/core/components/link';\nimport { TuiTextfieldComponent, TUI_TEXTFIELD_OPTIONS } from '@taiga-ui/core/components/textfield';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { TUI_MULTI_SELECT_TEXTS } from '@taiga-ui/kit/tokens';\nimport { tuiInjectValue } from '@taiga-ui/kit/utils';\nimport { NgIf } from '@angular/common';\nimport { TuiCheckbox } from '@taiga-ui/kit/components/checkbox';\n\nclass TuiMultiSelectGroupComponent {\n    constructor() {\n        this.values = signal([]);\n        this.handlers = inject(TUI_ITEMS_HANDLERS);\n        this.control = inject(TuiTextfieldComponent, { optional: true })?.control ||\n            inject(NgControl, { optional: true });\n        this.texts = toSignal(inject(TUI_MULTI_SELECT_TEXTS));\n        this.value = tuiInjectValue();\n        this.checked = computed(() => this.values().every((item) => this.value().some((value) => this.handlers.identityMatcher()(item, value))));\n        this.label = '';\n    }\n    set options(options) {\n        this.values.set(options\n            .toArray()\n            .map(({ value }) => value())\n            .filter(tuiIsPresent));\n    }\n    toggle() {\n        const values = this.values();\n        const matcher = this.handlers.identityMatcher();\n        const others = this.value().filter((current) => values.every((item) => !matcher(current, item)));\n        this.control?.control?.setValue(this.checked() ? others : others.concat(values));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMultiSelectGroupComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiMultiSelectGroupComponent, isStandalone: true, selector: \"tui-opt-group[tuiMultiSelectGroup]\", inputs: { label: \"label\" }, host: { properties: { \"class._label\": \"label\" } }, queries: [{ propertyName: \"options\", predicate: TuiOptionWithValue }], ngImport: i0, template: \"<span class=\\\"t-wrapper\\\">\\n    <span class=\\\"t-label\\\">{{ label }}</span>\\n    <button\\n        tuiLink\\n        type=\\\"button\\\"\\n        class=\\\"t-button\\\"\\n        (click)=\\\"toggle()\\\"\\n    >\\n        {{ texts()?.[checked() ? 'none' : 'all'] }}\\n    </button>\\n</span>\\n<ng-content />\\n\", styles: [\":host._label:before{display:none}:host:not(:first-of-type) .t-label:not(:empty){padding-top:1.25rem}:host:not(:first-of-type) .t-button{margin-top:1.25rem}.t-wrapper{display:flex;align-items:flex-start}.t-wrapper:last-child{display:none}.t-label:not(:empty){flex:1;padding:.75rem 1rem .25rem .625rem}.t-button{margin:.75rem 1rem 0 auto;font-weight:400}\\n\"], dependencies: [{ kind: \"directive\", type: TuiLink, selector: \"a[tuiLink], button[tuiLink]\", inputs: [\"pseudo\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMultiSelectGroupComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-opt-group[tuiMultiSelectGroup]', imports: [TuiLink], changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[class._label]': 'label',\n                    }, template: \"<span class=\\\"t-wrapper\\\">\\n    <span class=\\\"t-label\\\">{{ label }}</span>\\n    <button\\n        tuiLink\\n        type=\\\"button\\\"\\n        class=\\\"t-button\\\"\\n        (click)=\\\"toggle()\\\"\\n    >\\n        {{ texts()?.[checked() ? 'none' : 'all'] }}\\n    </button>\\n</span>\\n<ng-content />\\n\", styles: [\":host._label:before{display:none}:host:not(:first-of-type) .t-label:not(:empty){padding-top:1.25rem}:host:not(:first-of-type) .t-button{margin-top:1.25rem}.t-wrapper{display:flex;align-items:flex-start}.t-wrapper:last-child{display:none}.t-label:not(:empty){flex:1;padding:.75rem 1rem .25rem .625rem}.t-button{margin:.75rem 1rem 0 auto;font-weight:400}\\n\"] }]\n        }], propDecorators: { label: [{\n                type: Input\n            }], options: [{\n                type: ContentChildren,\n                args: [TuiOptionWithValue]\n            }] } });\n\nclass TuiMultiSelectOption {\n    constructor() {\n        this.option = inject(TuiOptionWithValue, {\n            optional: true,\n        });\n        this.handlers = inject(TUI_ITEMS_HANDLERS);\n        this.value = tuiInjectValue();\n        this.size = inject(TUI_TEXTFIELD_OPTIONS).size;\n        this.selected = computed((controlValue = this.value(), optionValue = this.option?.value()) => tuiIsPresent(optionValue) &&\n            tuiIsPresent(controlValue) &&\n            controlValue.some((item) => this.handlers?.identityMatcher()(item, optionValue) ??\n                item === optionValue));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMultiSelectOption, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiMultiSelectOption, isStandalone: true, selector: \"ng-component\", ngImport: i0, template: `\n        <input\n            *ngIf=\"option\"\n            tuiCheckbox\n            type=\"checkbox\"\n            [checked]=\"selected()\"\n            [size]=\"size() === 'l' ? 'm' : 's'\"\n        />\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: TuiCheckbox, selector: \"input[type=\\\"checkbox\\\"][tuiCheckbox]\", inputs: [\"size\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMultiSelectOption, decorators: [{\n            type: Component,\n            args: [{\n                    standalone: true,\n                    imports: [NgIf, TuiCheckbox],\n                    template: `\n        <input\n            *ngIf=\"option\"\n            tuiCheckbox\n            type=\"checkbox\"\n            [checked]=\"selected()\"\n            [size]=\"size() === 'l' ? 'm' : 's'\"\n        />\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }] });\n\nclass TuiMultiSelectGroupDirective {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMultiSelectGroupDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiMultiSelectGroupDirective, isStandalone: true, selector: \"[tuiMultiSelectGroup]\", providers: [tuiAsOptionContent(TuiMultiSelectOption)], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMultiSelectGroupDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiMultiSelectGroup]',\n                    providers: [tuiAsOptionContent(TuiMultiSelectOption)],\n                }]\n        }] });\n\nconst TuiMultiSelect = [\n    TuiMultiSelectGroupComponent,\n    TuiMultiSelectGroupDirective,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiMultiSelect, TuiMultiSelectGroupComponent, TuiMultiSelectGroupDirective, TuiMultiSelectOption };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,eAAe,EAAEC,SAAS,QAAQ,eAAe;AAC/H,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,kBAAkB,EAAEC,kBAAkB,QAAQ,qCAAqC;AAC5F,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,qBAAqB,EAAEC,qBAAqB,QAAQ,qCAAqC;AAClG,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,WAAW,QAAQ,mCAAmC;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,sCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAyBqCzB,EAAE,CAAA2B,SAAA,cAoC9F,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GApC2F5B,EAAE,CAAA6B,aAAA;IAAF7B,EAAE,CAAA8B,UAAA,YAAAF,MAAA,CAAAG,QAAA,EAkCtE,CAAC,SAAAH,MAAA,CAAAI,IAAA,sBACY,CAAC;EAAA;AAAA;AA1D/C,MAAMC,4BAA4B,CAAC;EAC/BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAGlC,MAAM,CAAC,EAAE,CAAC;IACxB,IAAI,CAACmC,QAAQ,GAAGlC,MAAM,CAACe,kBAAkB,CAAC;IAC1C,IAAI,CAACoB,OAAO,GAAGnC,MAAM,CAACa,qBAAqB,EAAE;MAAEuB,QAAQ,EAAE;IAAK,CAAC,CAAC,EAAED,OAAO,IACrEnC,MAAM,CAACQ,SAAS,EAAE;MAAE4B,QAAQ,EAAE;IAAK,CAAC,CAAC;IACzC,IAAI,CAACC,KAAK,GAAG9B,QAAQ,CAACP,MAAM,CAACgB,sBAAsB,CAAC,CAAC;IACrD,IAAI,CAACsB,KAAK,GAAGrB,cAAc,CAAC,CAAC;IAC7B,IAAI,CAACsB,OAAO,GAAGtC,QAAQ,CAAC,MAAM,IAAI,CAACgC,MAAM,CAAC,CAAC,CAACO,KAAK,CAAEC,IAAI,IAAK,IAAI,CAACH,KAAK,CAAC,CAAC,CAACI,IAAI,CAAEJ,KAAK,IAAK,IAAI,CAACJ,QAAQ,CAACS,eAAe,CAAC,CAAC,CAACF,IAAI,EAAEH,KAAK,CAAC,CAAC,CAAC,CAAC;IACxI,IAAI,CAACM,KAAK,GAAG,EAAE;EACnB;EACA,IAAIC,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACZ,MAAM,CAACa,GAAG,CAACD,OAAO,CAClBE,OAAO,CAAC,CAAC,CACTC,GAAG,CAAC,CAAC;MAAEV;IAAM,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,CAC3BW,MAAM,CAACxC,YAAY,CAAC,CAAC;EAC9B;EACAyC,MAAMA,CAAA,EAAG;IACL,MAAMjB,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;IAC5B,MAAMkB,OAAO,GAAG,IAAI,CAACjB,QAAQ,CAACS,eAAe,CAAC,CAAC;IAC/C,MAAMS,MAAM,GAAG,IAAI,CAACd,KAAK,CAAC,CAAC,CAACW,MAAM,CAAEI,OAAO,IAAKpB,MAAM,CAACO,KAAK,CAAEC,IAAI,IAAK,CAACU,OAAO,CAACE,OAAO,EAAEZ,IAAI,CAAC,CAAC,CAAC;IAChG,IAAI,CAACN,OAAO,EAAEA,OAAO,EAAEmB,QAAQ,CAAC,IAAI,CAACf,OAAO,CAAC,CAAC,GAAGa,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACtB,MAAM,CAAC,CAAC;EACpF;EACA;IAAS,IAAI,CAACuB,IAAI,YAAAC,qCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyF3B,4BAA4B;IAAA,CAAmD;EAAE;EAC5L;IAAS,IAAI,CAAC4B,IAAI,kBAD+E7D,EAAE,CAAA8D,iBAAA;MAAAC,IAAA,EACJ9B,4BAA4B;MAAA+B,SAAA;MAAAC,cAAA,WAAAC,4CAAAzC,EAAA,EAAAC,GAAA,EAAAyC,QAAA;QAAA,IAAA1C,EAAA;UAD1BzB,EAAE,CAAAoE,cAAA,CAAAD,QAAA,EAC6NvD,kBAAkB;QAAA;QAAA,IAAAa,EAAA;UAAA,IAAA4C,EAAA;UADjPrE,EAAE,CAAAsE,cAAA,CAAAD,EAAA,GAAFrE,EAAE,CAAAuE,WAAA,QAAA7C,GAAA,CAAAqB,OAAA,GAAAsB,EAAA;QAAA;MAAA;MAAAG,QAAA;MAAAC,YAAA,WAAAC,0CAAAjD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzB,EAAE,CAAA2E,WAAA,WAAAjD,GAAA,CAAAoB,KACuB,CAAC;QAAA;MAAA;MAAA8B,MAAA;QAAA9B,KAAA;MAAA;MAAA+B,UAAA;MAAAC,QAAA,GAD1B9E,EAAE,CAAA+E,mBAAA;MAAAC,KAAA,EAAA1D,GAAA;MAAA2D,kBAAA,EAAA1D,GAAA;MAAA2D,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAA7D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzB,EAAE,CAAAuF,eAAA;UAAFvF,EAAE,CAAAwF,cAAA,aACsS,CAAC,aAA6B,CAAC;UADvUxF,EAAE,CAAAyF,MAAA,EAC+U,CAAC;UADlVzF,EAAE,CAAA0F,YAAA,CACsV,CAAC;UADzV1F,EAAE,CAAAwF,cAAA,eAC8c,CAAC;UADjdxF,EAAE,CAAA2F,UAAA,mBAAAC,8DAAA;YAAA,OAC8blE,GAAA,CAAA0B,MAAA,CAAO,CAAC;UAAA,CAAC,CAAC;UAD1cpD,EAAE,CAAAyF,MAAA,EACygB,CAAC;UAD5gBzF,EAAE,CAAA0F,YAAA,CACkhB,CAAC,CAAQ,CAAC;UAD9hB1F,EAAE,CAAA6F,YAAA,EAC2iB,CAAC;QAAA;QAAA,IAAApE,EAAA;UAAA,IAAAqE,OAAA;UAD9iB9F,EAAE,CAAA+F,SAAA,EAC+U,CAAC;UADlV/F,EAAE,CAAAgG,iBAAA,CAAAtE,GAAA,CAAAoB,KAC+U,CAAC;UADlV9C,EAAE,CAAA+F,SAAA,EACygB,CAAC;UAD5gB/F,EAAE,CAAAiG,kBAAA,OAAAH,OAAA,GAAApE,GAAA,CAAAa,KAAA,qBAAAuD,OAAA,CAAApE,GAAA,CAAAe,OAAA,yBACygB,CAAC;QAAA;MAAA;MAAAyD,YAAA,GAAicpF,OAAO;MAAAqF,MAAA;MAAAC,eAAA;IAAA,EAAuH;EAAE;AAClrC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGrG,EAAE,CAAAsG,iBAAA,CAGXrE,4BAA4B,EAAc,CAAC;IAC3H8B,IAAI,EAAE3D,SAAS;IACfmG,IAAI,EAAE,CAAC;MAAE1B,UAAU,EAAE,IAAI;MAAE2B,QAAQ,EAAE,oCAAoC;MAAEC,OAAO,EAAE,CAAC3F,OAAO,CAAC;MAAEsF,eAAe,EAAE/F,uBAAuB,CAACqG,MAAM;MAAEC,IAAI,EAAE;QAC1I,gBAAgB,EAAE;MACtB,CAAC;MAAEtB,QAAQ,EAAE,mSAAmS;MAAEc,MAAM,EAAE,CAAC,oWAAoW;IAAE,CAAC;EAC9qB,CAAC,CAAC,QAAkB;IAAErD,KAAK,EAAE,CAAC;MACtBiB,IAAI,EAAEzD;IACV,CAAC,CAAC;IAAEyC,OAAO,EAAE,CAAC;MACVgB,IAAI,EAAExD,eAAe;MACrBgG,IAAI,EAAE,CAAC3F,kBAAkB;IAC7B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgG,oBAAoB,CAAC;EACvB1E,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2E,MAAM,GAAG3G,MAAM,CAACU,kBAAkB,EAAE;MACrC0B,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,CAACF,QAAQ,GAAGlC,MAAM,CAACe,kBAAkB,CAAC;IAC1C,IAAI,CAACuB,KAAK,GAAGrB,cAAc,CAAC,CAAC;IAC7B,IAAI,CAACa,IAAI,GAAG9B,MAAM,CAACc,qBAAqB,CAAC,CAACgB,IAAI;IAC9C,IAAI,CAACD,QAAQ,GAAG5B,QAAQ,CAAC,CAAC2G,YAAY,GAAG,IAAI,CAACtE,KAAK,CAAC,CAAC,EAAEuE,WAAW,GAAG,IAAI,CAACF,MAAM,EAAErE,KAAK,CAAC,CAAC,KAAK7B,YAAY,CAACoG,WAAW,CAAC,IACnHpG,YAAY,CAACmG,YAAY,CAAC,IAC1BA,YAAY,CAAClE,IAAI,CAAED,IAAI,IAAK,IAAI,CAACP,QAAQ,EAAES,eAAe,CAAC,CAAC,CAACF,IAAI,EAAEoE,WAAW,CAAC,IAC3EpE,IAAI,KAAKoE,WAAW,CAAC,CAAC;EAClC;EACA;IAAS,IAAI,CAACrD,IAAI,YAAAsD,6BAAApD,CAAA;MAAA,YAAAA,CAAA,IAAyFgD,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAAC/C,IAAI,kBA7B+E7D,EAAE,CAAA8D,iBAAA;MAAAC,IAAA,EA6BJ6C,oBAAoB;MAAA5C,SAAA;MAAAa,UAAA;MAAAC,QAAA,GA7BlB9E,EAAE,CAAA+E,mBAAA;MAAAG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA4B,8BAAAxF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzB,EAAE,CAAAkH,UAAA,IAAA1F,qCAAA,kBAoC9F,CAAC;QAAA;QAAA,IAAAC,EAAA;UApC2FzB,EAAE,CAAA8B,UAAA,SAAAJ,GAAA,CAAAmF,MA+B/E,CAAC;QAAA;MAAA;MAAAX,YAAA,GAMwC9E,IAAI,EAA6FC,WAAW;MAAA8F,aAAA;MAAAf,eAAA;IAAA,EAA+H;EAAE;AAC9S;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvCqGrG,EAAE,CAAAsG,iBAAA,CAuCXM,oBAAoB,EAAc,CAAC;IACnH7C,IAAI,EAAE3D,SAAS;IACfmG,IAAI,EAAE,CAAC;MACC1B,UAAU,EAAE,IAAI;MAChB4B,OAAO,EAAE,CAACrF,IAAI,EAAEC,WAAW,CAAC;MAC5BgE,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MACee,eAAe,EAAE/F,uBAAuB,CAACqG;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMU,4BAA4B,CAAC;EAC/B;IAAS,IAAI,CAAC1D,IAAI,YAAA2D,qCAAAzD,CAAA;MAAA,YAAAA,CAAA,IAAyFwD,4BAA4B;IAAA,CAAmD;EAAE;EAC5L;IAAS,IAAI,CAACE,IAAI,kBA3D+EtH,EAAE,CAAAuH,iBAAA;MAAAxD,IAAA,EA2DJqD,4BAA4B;MAAApD,SAAA;MAAAa,UAAA;MAAAC,QAAA,GA3D1B9E,EAAE,CAAAwH,kBAAA,CA2D4F,CAAC3G,kBAAkB,CAAC+F,oBAAoB,CAAC,CAAC;IAAA,EAAiB;EAAE;AAChQ;AACA;EAAA,QAAAP,SAAA,oBAAAA,SAAA,KA7DqGrG,EAAE,CAAAsG,iBAAA,CA6DXc,4BAA4B,EAAc,CAAC;IAC3HrD,IAAI,EAAEvD,SAAS;IACf+F,IAAI,EAAE,CAAC;MACC1B,UAAU,EAAE,IAAI;MAChB2B,QAAQ,EAAE,uBAAuB;MACjCiB,SAAS,EAAE,CAAC5G,kBAAkB,CAAC+F,oBAAoB,CAAC;IACxD,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMc,cAAc,GAAG,CACnBzF,4BAA4B,EAC5BmF,4BAA4B,CAC/B;;AAED;AACA;AACA;;AAEA,SAASM,cAAc,EAAEzF,4BAA4B,EAAEmF,4BAA4B,EAAER,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}