{"ast": null, "code": "import { AsyncPipe, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, Injectable, TemplateRef, ChangeDetectorRef, forwardRef, Directive } from '@angular/core';\nimport { tuiIsString, tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiButtonOptionsProvider, TuiButton } from '@taiga-ui/core/components/button';\nimport { TuiFormatDatePipe } from '@taiga-ui/core/pipes/format-date';\nimport { TUI_CLOSE_WORD, TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { tuiIfMap } from '@taiga-ui/cdk/observables';\nimport { injectContext, PolymorpheusOutlet, PolymorpheusTemplate } from '@taiga-ui/polymorpheus';\nimport { Subject } from 'rxjs';\nimport { TuiPopoverService } from '@taiga-ui/cdk/services';\nimport { TUI_ALERTS } from '@taiga-ui/core/components/alert';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TuiLink } from '@taiga-ui/core/components/link';\nconst _c0 = [[[\"img\"]], [[\"tui-svg\"], [\"tui-icon\"]], \"*\", [[\"\", \"tuiButton\", \"\"]], [[\"\", \"tuiLink\", \"\"]]];\nconst _c1 = [\"img\", \"tui-svg,tui-icon\", \"*\", \"[tuiButton]\", \"[tuiLink]\"];\nfunction TuiPushComponent_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function TuiPushComponent_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.close.emit());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"border-radius\", 100, \"%\");\n    i0.ɵɵproperty(\"iconStart\", ctx_r1.icons.close);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 4, ctx_r1.closeWord$), \"\\n\");\n  }\n}\nfunction TuiPushComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 11);\n    i0.ɵɵpipe(1, \"tuiFormatDate\");\n    i0.ɵɵpipe(2, \"async\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"textContent\", ctx_r1.isString(ctx_r1.timestamp) ? ctx_r1.timestamp : i0.ɵɵpipeBind1(2, 3, i0.ɵɵpipeBind1(1, 1, ctx_r1.timestamp)));\n  }\n}\nfunction TuiPushAlert_tui_push_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.context.image, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction TuiPushAlert_tui_push_1_tui_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-icon\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r1.context.iconColor);\n    i0.ɵɵproperty(\"icon\", ctx_r1.context.icon);\n  }\n}\nfunction TuiPushAlert_tui_push_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 11);\n  }\n  if (rf & 2) {\n    const text_r3 = ctx.polymorpheusOutlet;\n    i0.ɵɵproperty(\"innerHTML\", text_r3, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction TuiPushAlert_tui_push_1_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function TuiPushAlert_tui_push_1_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.context.$implicit.next(ctx_r1.context.buttons[0] || \"\"));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.context.buttons[0], \" \");\n  }\n}\nfunction TuiPushAlert_tui_push_1_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function TuiPushAlert_tui_push_1_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.context.$implicit.next(ctx_r1.context.buttons[ctx_r1.context.buttons.length - 1] || \"\"));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.context.buttons[ctx_r1.context.buttons.length - 1], \" \");\n  }\n}\nfunction TuiPushAlert_tui_push_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tui-push\", 3);\n    i0.ɵɵlistener(\"close\", function TuiPushAlert_tui_push_1_Template_tui_push_close_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.context.$implicit.complete());\n    });\n    i0.ɵɵtemplate(1, TuiPushAlert_tui_push_1_img_1_Template, 1, 1, \"img\", 4)(2, TuiPushAlert_tui_push_1_tui_icon_2_Template, 1, 3, \"tui-icon\", 5)(3, TuiPushAlert_tui_push_1_div_3_Template, 1, 1, \"div\", 6)(4, TuiPushAlert_tui_push_1_button_4_Template, 2, 1, \"button\", 7)(5, TuiPushAlert_tui_push_1_button_5_Template, 2, 1, \"button\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"heading\", ctx_r1.context.heading)(\"timestamp\", ctx_r1.context.timestamp)(\"type\", ctx_r1.context.type);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.context.image);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.context.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r1.context.content)(\"polymorpheusOutletContext\", ctx_r1.context);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.context.buttons.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.context.buttons.length);\n  }\n}\nfunction TuiPushAlert_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiPushAlert_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiPushAlert_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r1.context.content);\n  }\n}\nclass TuiPushComponent {\n  constructor() {\n    this.isString = tuiIsString;\n    this.closeWord$ = inject(TUI_CLOSE_WORD);\n    this.icons = inject(TUI_COMMON_ICONS);\n    this.heading = '';\n    this.type = '';\n    this.lines = 2;\n    this.timestamp = '';\n    this.close = new EventEmitter();\n  }\n  static {\n    this.ɵfac = function TuiPushComponent_Factory(t) {\n      return new (t || TuiPushComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiPushComponent,\n      selectors: [[\"tui-push\"]],\n      hostVars: 2,\n      hostBindings: function TuiPushComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-lines\", ctx.lines);\n        }\n      },\n      inputs: {\n        heading: \"heading\",\n        type: \"type\",\n        lines: \"lines\",\n        timestamp: \"timestamp\"\n      },\n      outputs: {\n        close: \"close\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiButtonOptionsProvider({\n        size: 's',\n        appearance: 'secondary'\n      })]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 17,\n      vars: 4,\n      consts: [[1, \"t-image\"], [\"appearance\", \"\", \"size\", \"xs\", \"tuiIconButton\", \"\", \"type\", \"button\", \"class\", \"t-close\", 3, \"iconStart\", \"border-radius\", \"click\", 4, \"ngIf\"], [1, \"t-top\"], [1, \"t-icon\"], [\"class\", \"t-time\", 3, \"textContent\", 4, \"ngIf\"], [\"automation-id\", \"tui-push__heading\", 1, \"t-heading\"], [1, \"t-content\"], [1, \"t-actions\"], [1, \"t-button\"], [1, \"t-link\"], [\"appearance\", \"\", \"size\", \"xs\", \"tuiIconButton\", \"\", \"type\", \"button\", 1, \"t-close\", 3, \"click\", \"iconStart\"], [1, \"t-time\", 3, \"textContent\"]],\n      template: function TuiPushComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, TuiPushComponent_button_2_Template, 3, 6, \"button\", 1);\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"span\", 3);\n          i0.ɵɵprojection(5, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6);\n          i0.ɵɵtemplate(7, TuiPushComponent_span_7_Template, 3, 5, \"span\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"h3\", 5);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 6);\n          i0.ɵɵprojection(11, 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 7)(13, \"span\", 8);\n          i0.ɵɵprojection(14, 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\", 9);\n          i0.ɵɵprojection(16, 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.close.observed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.type, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.timestamp);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.heading, \"\\n\");\n        }\n      },\n      dependencies: [AsyncPipe, NgIf, TuiButton, TuiFormatDatePipe],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:block;inline-size:22.5rem;max-inline-size:100%;box-sizing:border-box;padding:1.25rem 1.25rem .25rem;overflow:hidden;font:var(--tui-font-text-m);border-radius:var(--tui-radius-xl);background:var(--tui-background-elevation-2);box-shadow:var(--tui-shadow-small)}.t-image[_ngcontent-%COMP%]{display:flex;flex-direction:column;max-block-size:10.625rem;overflow:hidden;margin:-1.25rem -1.25rem 1.25rem}.t-close[_ngcontent-%COMP%]{position:absolute;top:.75rem;right:.75rem;color:#0000008a;background:#f2f2f252;-webkit-backdrop-filter:blur(1rem);backdrop-filter:blur(1rem)}.t-top[_ngcontent-%COMP%]{display:flex;align-items:center;font:var(--tui-font-text-s);color:var(--tui-text-secondary)}.t-icon[_ngcontent-%COMP%]{block-size:1.25rem;margin-right:.5rem;transform:scale(.833);transform-origin:top left;color:var(--tui-text-action)}.t-time[_ngcontent-%COMP%]:before{content:\\\"\\\\2022\\\";display:inline-block;inline-size:1rem;text-align:center}.t-heading[_ngcontent-%COMP%]{line-height:1.25rem;font-weight:700;margin:.75rem 0 .25rem}.t-heading[_ngcontent-%COMP%], .t-content[_ngcontent-%COMP%]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:var(--t-lines);word-break:break-word;overflow:hidden}.t-actions[_ngcontent-%COMP%]{display:flex;align-items:center;font:var(--tui-font-text-s)}.t-button[_ngcontent-%COMP%]:not(:empty){margin:.75rem .75rem .75rem 0}.t-link[_ngcontent-%COMP%]{margin:.75rem 0}.t-link[_ngcontent-%COMP%]:empty{margin:.5rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPushComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-push',\n      imports: [AsyncPipe, NgIf, TuiButton, TuiFormatDatePipe],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiButtonOptionsProvider({\n        size: 's',\n        appearance: 'secondary'\n      })],\n      host: {\n        '[style.--t-lines]': 'lines'\n      },\n      template: \"<div class=\\\"t-image\\\">\\n    <ng-content select=\\\"img\\\" />\\n</div>\\n<button\\n    *ngIf=\\\"close.observed\\\"\\n    appearance=\\\"\\\"\\n    size=\\\"xs\\\"\\n    tuiIconButton\\n    type=\\\"button\\\"\\n    class=\\\"t-close\\\"\\n    [iconStart]=\\\"icons.close\\\"\\n    [style.border-radius.%]=\\\"100\\\"\\n    (click)=\\\"close.emit()\\\"\\n>\\n    {{ closeWord$ | async }}\\n</button>\\n<div class=\\\"t-top\\\">\\n    <span class=\\\"t-icon\\\">\\n        <ng-content select=\\\"tui-svg,tui-icon\\\" />\\n    </span>\\n    {{ type }}\\n    <span\\n        *ngIf=\\\"timestamp\\\"\\n        class=\\\"t-time\\\"\\n        [textContent]=\\\"isString(timestamp) ? timestamp : (timestamp | tuiFormatDate | async)\\\"\\n    ></span>\\n</div>\\n<h3\\n    automation-id=\\\"tui-push__heading\\\"\\n    class=\\\"t-heading\\\"\\n>\\n    {{ heading }}\\n</h3>\\n<div class=\\\"t-content\\\">\\n    <ng-content />\\n</div>\\n<div class=\\\"t-actions\\\">\\n    <span class=\\\"t-button\\\">\\n        <ng-content select=\\\"[tuiButton]\\\" />\\n    </span>\\n    <span class=\\\"t-link\\\">\\n        <ng-content select=\\\"[tuiLink]\\\" />\\n    </span>\\n</div>\\n\",\n      styles: [\":host{position:relative;display:block;inline-size:22.5rem;max-inline-size:100%;box-sizing:border-box;padding:1.25rem 1.25rem .25rem;overflow:hidden;font:var(--tui-font-text-m);border-radius:var(--tui-radius-xl);background:var(--tui-background-elevation-2);box-shadow:var(--tui-shadow-small)}.t-image{display:flex;flex-direction:column;max-block-size:10.625rem;overflow:hidden;margin:-1.25rem -1.25rem 1.25rem}.t-close{position:absolute;top:.75rem;right:.75rem;color:#0000008a;background:#f2f2f252;-webkit-backdrop-filter:blur(1rem);backdrop-filter:blur(1rem)}.t-top{display:flex;align-items:center;font:var(--tui-font-text-s);color:var(--tui-text-secondary)}.t-icon{block-size:1.25rem;margin-right:.5rem;transform:scale(.833);transform-origin:top left;color:var(--tui-text-action)}.t-time:before{content:\\\"\\\\2022\\\";display:inline-block;inline-size:1rem;text-align:center}.t-heading{line-height:1.25rem;font-weight:700;margin:.75rem 0 .25rem}.t-heading,.t-content{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:var(--t-lines);word-break:break-word;overflow:hidden}.t-actions{display:flex;align-items:center;font:var(--tui-font-text-s)}.t-button:not(:empty){margin:.75rem .75rem .75rem 0}.t-link{margin:.75rem 0}.t-link:empty{margin:.5rem}\\n\"]\n    }]\n  }], null, {\n    heading: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    lines: [{\n      type: Input\n    }],\n    timestamp: [{\n      type: Input\n    }],\n    close: [{\n      type: Output\n    }]\n  });\n})();\nconst TUI_PUSH_DEFAULT_OPTIONS = {\n  heading: '',\n  type: '',\n  timestamp: '',\n  image: '',\n  icon: '',\n  iconColor: '',\n  buttons: []\n};\n/**\n * Default parameters for Push component\n */\nconst TUI_PUSH_OPTIONS = tuiCreateToken(TUI_PUSH_DEFAULT_OPTIONS);\nfunction tuiPushOptionsProvider(options) {\n  return tuiProvideOptions(TUI_PUSH_OPTIONS, options, TUI_PUSH_DEFAULT_OPTIONS);\n}\nclass TuiPushAlert {\n  constructor() {\n    this.context = injectContext();\n  }\n  get isDirective() {\n    return this.context.content instanceof TuiPushDirective;\n  }\n  static {\n    this.ɵfac = function TuiPushAlert_Factory(t) {\n      return new (t || TuiPushAlert)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiPushAlert,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [\"role\", \"alert\"],\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([i1.TuiAnimated]), i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 2,\n      consts: [[\"directive\", \"\"], [1, \"t-wrapper\"], [3, \"heading\", \"timestamp\", \"type\", \"close\", 4, \"ngIf\", \"ngIfElse\"], [3, \"close\", \"heading\", \"timestamp\", \"type\"], [\"alt\", \"\", 3, \"src\", 4, \"ngIf\"], [3, \"icon\", \"color\", 4, \"ngIf\"], [3, \"innerHTML\", 4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [\"tuiButton\", \"\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"tuiLink\", \"\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"alt\", \"\", 3, \"src\"], [3, \"icon\"], [3, \"innerHTML\"], [\"tuiButton\", \"\", \"type\", \"button\", 3, \"click\"], [\"tuiLink\", \"\", \"type\", \"button\", 3, \"click\"], [4, \"polymorpheusOutlet\"]],\n      template: function TuiPushAlert_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, TuiPushAlert_tui_push_1_Template, 6, 9, \"tui-push\", 2)(2, TuiPushAlert_ng_template_2_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const directive_r6 = i0.ɵɵreference(3);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isDirective)(\"ngIfElse\", directive_r6);\n        }\n      },\n      dependencies: [NgIf, PolymorpheusOutlet, TuiButton, TuiIcon, TuiLink, TuiPushComponent],\n      styles: [\"[_nghost-%COMP%]{position:relative;bottom:1rem;display:grid;flex-shrink:0;max-inline-size:calc(100% - 2rem);margin:0 1rem 0 auto;border-radius:var(--tui-radius-xl);box-shadow:var(--tui-shadow-small);overflow:hidden;--tui-from: translateX(100%)}.tui-enter[_nghost-%COMP%], .tui-leave[_nghost-%COMP%]{animation-name:tuiFade,tuiSlide,tuiCollapse}.tui-leave[_nghost-%COMP%]:not(:first-child){transition-property:margin;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-top:-1rem!important}[_nghost-%COMP%]:first-child{margin-top:auto}[_nghost-%COMP%]:not(:last-child){transition-property:margin;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-bottom:1rem}.t-wrapper[_ngcontent-%COMP%]{grid-row:1 / span 2;overflow:hidden}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPushAlert, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      imports: [NgIf, PolymorpheusOutlet, TuiButton, TuiIcon, TuiLink, TuiPushComponent],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      hostDirectives: [TuiAnimated],\n      host: {\n        role: 'alert'\n      },\n      template: \"<div class=\\\"t-wrapper\\\">\\n    <tui-push\\n        *ngIf=\\\"!isDirective; else directive\\\"\\n        [heading]=\\\"context.heading\\\"\\n        [timestamp]=\\\"context.timestamp\\\"\\n        [type]=\\\"context.type\\\"\\n        (close)=\\\"context.$implicit.complete()\\\"\\n    >\\n        <img\\n            *ngIf=\\\"context.image\\\"\\n            alt=\\\"\\\"\\n            [src]=\\\"context.image\\\"\\n        />\\n        <tui-icon\\n            *ngIf=\\\"context.icon\\\"\\n            [icon]=\\\"context.icon\\\"\\n            [style.color]=\\\"context.iconColor\\\"\\n        />\\n        <div\\n            *polymorpheusOutlet=\\\"context.content as text; context: context\\\"\\n            [innerHTML]=\\\"text\\\"\\n        ></div>\\n        <button\\n            *ngIf=\\\"context.buttons.length > 1\\\"\\n            tuiButton\\n            type=\\\"button\\\"\\n            (click)=\\\"context.$implicit.next(context.buttons[0] || '')\\\"\\n        >\\n            {{ context.buttons[0] }}\\n        </button>\\n        <button\\n            *ngIf=\\\"context.buttons.length\\\"\\n            tuiLink\\n            type=\\\"button\\\"\\n            (click)=\\\"context.$implicit.next(context.buttons[context.buttons.length - 1] || '')\\\"\\n        >\\n            {{ context.buttons[context.buttons.length - 1] }}\\n        </button>\\n    </tui-push>\\n    <ng-template #directive>\\n        <ng-container *polymorpheusOutlet=\\\"context.content\\\" />\\n    </ng-template>\\n</div>\\n\",\n      styles: [\":host{position:relative;bottom:1rem;display:grid;flex-shrink:0;max-inline-size:calc(100% - 2rem);margin:0 1rem 0 auto;border-radius:var(--tui-radius-xl);box-shadow:var(--tui-shadow-small);overflow:hidden;--tui-from: translateX(100%)}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide,tuiCollapse}:host.tui-leave:not(:first-child){transition-property:margin;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-top:-1rem!important}:host:first-child{margin-top:auto}:host:not(:last-child){transition-property:margin;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-bottom:1rem}.t-wrapper{grid-row:1 / span 2;overflow:hidden}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiPushService extends TuiPopoverService {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiPushService_BaseFactory;\n      return function TuiPushService_Factory(t) {\n        return (ɵTuiPushService_BaseFactory || (ɵTuiPushService_BaseFactory = i0.ɵɵgetInheritedFactory(TuiPushService)))(t || TuiPushService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiPushService,\n      factory: () => (() => new TuiPushService(TUI_ALERTS, TuiPushAlert, inject(TUI_PUSH_OPTIONS)))(),\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPushService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => new TuiPushService(TUI_ALERTS, TuiPushAlert, inject(TUI_PUSH_OPTIONS))\n    }]\n  }], null, null);\n})();\nclass TuiPushDirective extends PolymorpheusTemplate {\n  constructor() {\n    super(inject(TemplateRef), inject(ChangeDetectorRef));\n    this.push = inject(forwardRef(() => TuiPushService));\n    this.show$ = new Subject();\n    this.show$.pipe(tuiIfMap(() => this.push.open(this)), takeUntilDestroyed()).subscribe();\n  }\n  set tuiPush(show) {\n    this.show$.next(show);\n  }\n  static {\n    this.ɵfac = function TuiPushDirective_Factory(t) {\n      return new (t || TuiPushDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiPushDirective,\n      selectors: [[\"ng-template\", \"tuiPush\", \"\"]],\n      inputs: {\n        tuiPush: \"tuiPush\"\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPushDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-template[tuiPush]'\n    }]\n  }], function () {\n    return [];\n  }, {\n    tuiPush: [{\n      type: Input\n    }]\n  });\n})();\nconst TuiPush = [TuiPushComponent, TuiPushAlert, TuiPushDirective];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_PUSH_DEFAULT_OPTIONS, TUI_PUSH_OPTIONS, TuiPush, TuiPushAlert, TuiPushComponent, TuiPushDirective, TuiPushService, tuiPushOptionsProvider };", "map": {"version": 3, "names": ["AsyncPipe", "NgIf", "i0", "inject", "EventEmitter", "Component", "ChangeDetectionStrategy", "Input", "Output", "Injectable", "TemplateRef", "ChangeDetectorRef", "forwardRef", "Directive", "tuiIsString", "tuiCreateToken", "tuiProvideOptions", "tuiButtonOptionsProvider", "TuiButton", "TuiFormatDatePipe", "TUI_CLOSE_WORD", "TUI_COMMON_ICONS", "takeUntilDestroyed", "tuiIfMap", "injectContext", "Polymorpheus<PERSON><PERSON>let", "PolymorpheusTemplate", "Subject", "TuiPopoverService", "TUI_ALERTS", "i1", "TuiAnimated", "TuiIcon", "TuiLink", "_c0", "_c1", "TuiPushComponent_button_2_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiPushComponent_button_2_Template_button_click_0_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "close", "emit", "ɵɵtext", "ɵɵpipe", "ɵɵelementEnd", "ɵɵstyleProp", "ɵɵproperty", "icons", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "closeWord$", "TuiPushComponent_span_7_Template", "ɵɵelement", "isString", "timestamp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_tui_push_1_img_1_Template", "context", "image", "ɵɵsanitizeUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_tui_push_1_tui_icon_2_Template", "iconColor", "icon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_tui_push_1_div_3_Template", "text_r3", "polymorpheusOutlet", "ɵɵsanitizeHtml", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_tui_push_1_button_4_Template", "_r4", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_tui_push_1_button_4_Template_button_click_0_listener", "$implicit", "next", "buttons", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_tui_push_1_button_5_Template", "_r5", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_tui_push_1_button_5_Template_button_click_0_listener", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_tui_push_1_Template", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_tui_push_1_Template_tui_push_close_0_listener", "complete", "ɵɵtemplate", "heading", "type", "content", "TuiPushAlert_ng_template_2_ng_container_0_Template", "ɵɵelementContainer", "TuiPushAlert_ng_template_2_Template", "TuiPushComponent", "constructor", "lines", "ɵfac", "TuiPushComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostVars", "hostBindings", "TuiPushComponent_HostBindings", "inputs", "outputs", "standalone", "features", "ɵɵProvidersFeature", "size", "appearance", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "TuiPushComponent_Template", "ɵɵprojectionDef", "ɵɵprojection", "observed", "dependencies", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "host", "TUI_PUSH_DEFAULT_OPTIONS", "TUI_PUSH_OPTIONS", "tuiPushOptionsProvider", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isDirective", "TuiPushDirective", "TuiPushAlert_Factory", "hostAttrs", "ɵɵHostDirectivesFeature", "TuiPushAlert_Template", "ɵɵtemplateRefExtractor", "directive_r6", "ɵɵreference", "hostDirectives", "role", "TuiPushService", "ɵTuiPushService_BaseFactory", "TuiPushService_Factory", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "useFactory", "push", "show$", "pipe", "open", "subscribe", "tui<PERSON><PERSON>", "show", "TuiPushDirective_Factory", "ɵdir", "ɵɵdefineDirective", "ɵɵInheritDefinitionFeature", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-push.mjs"], "sourcesContent": ["import { AsyncPipe, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, Injectable, TemplateRef, ChangeDetectorRef, forwardRef, Directive } from '@angular/core';\nimport { tuiIsString, tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiButtonOptionsProvider, TuiButton } from '@taiga-ui/core/components/button';\nimport { TuiFormatDatePipe } from '@taiga-ui/core/pipes/format-date';\nimport { TUI_CLOSE_WORD, TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { tuiIfMap } from '@taiga-ui/cdk/observables';\nimport { injectContext, PolymorpheusOutlet, PolymorpheusTemplate } from '@taiga-ui/polymorpheus';\nimport { Subject } from 'rxjs';\nimport { TuiPopoverService } from '@taiga-ui/cdk/services';\nimport { TUI_ALERTS } from '@taiga-ui/core/components/alert';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TuiLink } from '@taiga-ui/core/components/link';\n\nclass TuiPushComponent {\n    constructor() {\n        this.isString = tuiIsString;\n        this.closeWord$ = inject(TUI_CLOSE_WORD);\n        this.icons = inject(TUI_COMMON_ICONS);\n        this.heading = '';\n        this.type = '';\n        this.lines = 2;\n        this.timestamp = '';\n        this.close = new EventEmitter();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPushComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPushComponent, isStandalone: true, selector: \"tui-push\", inputs: { heading: \"heading\", type: \"type\", lines: \"lines\", timestamp: \"timestamp\" }, outputs: { close: \"close\" }, host: { properties: { \"style.--t-lines\": \"lines\" } }, providers: [tuiButtonOptionsProvider({ size: 's', appearance: 'secondary' })], ngImport: i0, template: \"<div class=\\\"t-image\\\">\\n    <ng-content select=\\\"img\\\" />\\n</div>\\n<button\\n    *ngIf=\\\"close.observed\\\"\\n    appearance=\\\"\\\"\\n    size=\\\"xs\\\"\\n    tuiIconButton\\n    type=\\\"button\\\"\\n    class=\\\"t-close\\\"\\n    [iconStart]=\\\"icons.close\\\"\\n    [style.border-radius.%]=\\\"100\\\"\\n    (click)=\\\"close.emit()\\\"\\n>\\n    {{ closeWord$ | async }}\\n</button>\\n<div class=\\\"t-top\\\">\\n    <span class=\\\"t-icon\\\">\\n        <ng-content select=\\\"tui-svg,tui-icon\\\" />\\n    </span>\\n    {{ type }}\\n    <span\\n        *ngIf=\\\"timestamp\\\"\\n        class=\\\"t-time\\\"\\n        [textContent]=\\\"isString(timestamp) ? timestamp : (timestamp | tuiFormatDate | async)\\\"\\n    ></span>\\n</div>\\n<h3\\n    automation-id=\\\"tui-push__heading\\\"\\n    class=\\\"t-heading\\\"\\n>\\n    {{ heading }}\\n</h3>\\n<div class=\\\"t-content\\\">\\n    <ng-content />\\n</div>\\n<div class=\\\"t-actions\\\">\\n    <span class=\\\"t-button\\\">\\n        <ng-content select=\\\"[tuiButton]\\\" />\\n    </span>\\n    <span class=\\\"t-link\\\">\\n        <ng-content select=\\\"[tuiLink]\\\" />\\n    </span>\\n</div>\\n\", styles: [\":host{position:relative;display:block;inline-size:22.5rem;max-inline-size:100%;box-sizing:border-box;padding:1.25rem 1.25rem .25rem;overflow:hidden;font:var(--tui-font-text-m);border-radius:var(--tui-radius-xl);background:var(--tui-background-elevation-2);box-shadow:var(--tui-shadow-small)}.t-image{display:flex;flex-direction:column;max-block-size:10.625rem;overflow:hidden;margin:-1.25rem -1.25rem 1.25rem}.t-close{position:absolute;top:.75rem;right:.75rem;color:#0000008a;background:#f2f2f252;-webkit-backdrop-filter:blur(1rem);backdrop-filter:blur(1rem)}.t-top{display:flex;align-items:center;font:var(--tui-font-text-s);color:var(--tui-text-secondary)}.t-icon{block-size:1.25rem;margin-right:.5rem;transform:scale(.833);transform-origin:top left;color:var(--tui-text-action)}.t-time:before{content:\\\"\\\\2022\\\";display:inline-block;inline-size:1rem;text-align:center}.t-heading{line-height:1.25rem;font-weight:700;margin:.75rem 0 .25rem}.t-heading,.t-content{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:var(--t-lines);word-break:break-word;overflow:hidden}.t-actions{display:flex;align-items:center;font:var(--tui-font-text-s)}.t-button:not(:empty){margin:.75rem .75rem .75rem 0}.t-link{margin:.75rem 0}.t-link:empty{margin:.5rem}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"pipe\", type: TuiFormatDatePipe, name: \"tuiFormatDate\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPushComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-push', imports: [AsyncPipe, NgIf, TuiButton, TuiFormatDatePipe], changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiButtonOptionsProvider({ size: 's', appearance: 'secondary' })], host: {\n                        '[style.--t-lines]': 'lines',\n                    }, template: \"<div class=\\\"t-image\\\">\\n    <ng-content select=\\\"img\\\" />\\n</div>\\n<button\\n    *ngIf=\\\"close.observed\\\"\\n    appearance=\\\"\\\"\\n    size=\\\"xs\\\"\\n    tuiIconButton\\n    type=\\\"button\\\"\\n    class=\\\"t-close\\\"\\n    [iconStart]=\\\"icons.close\\\"\\n    [style.border-radius.%]=\\\"100\\\"\\n    (click)=\\\"close.emit()\\\"\\n>\\n    {{ closeWord$ | async }}\\n</button>\\n<div class=\\\"t-top\\\">\\n    <span class=\\\"t-icon\\\">\\n        <ng-content select=\\\"tui-svg,tui-icon\\\" />\\n    </span>\\n    {{ type }}\\n    <span\\n        *ngIf=\\\"timestamp\\\"\\n        class=\\\"t-time\\\"\\n        [textContent]=\\\"isString(timestamp) ? timestamp : (timestamp | tuiFormatDate | async)\\\"\\n    ></span>\\n</div>\\n<h3\\n    automation-id=\\\"tui-push__heading\\\"\\n    class=\\\"t-heading\\\"\\n>\\n    {{ heading }}\\n</h3>\\n<div class=\\\"t-content\\\">\\n    <ng-content />\\n</div>\\n<div class=\\\"t-actions\\\">\\n    <span class=\\\"t-button\\\">\\n        <ng-content select=\\\"[tuiButton]\\\" />\\n    </span>\\n    <span class=\\\"t-link\\\">\\n        <ng-content select=\\\"[tuiLink]\\\" />\\n    </span>\\n</div>\\n\", styles: [\":host{position:relative;display:block;inline-size:22.5rem;max-inline-size:100%;box-sizing:border-box;padding:1.25rem 1.25rem .25rem;overflow:hidden;font:var(--tui-font-text-m);border-radius:var(--tui-radius-xl);background:var(--tui-background-elevation-2);box-shadow:var(--tui-shadow-small)}.t-image{display:flex;flex-direction:column;max-block-size:10.625rem;overflow:hidden;margin:-1.25rem -1.25rem 1.25rem}.t-close{position:absolute;top:.75rem;right:.75rem;color:#0000008a;background:#f2f2f252;-webkit-backdrop-filter:blur(1rem);backdrop-filter:blur(1rem)}.t-top{display:flex;align-items:center;font:var(--tui-font-text-s);color:var(--tui-text-secondary)}.t-icon{block-size:1.25rem;margin-right:.5rem;transform:scale(.833);transform-origin:top left;color:var(--tui-text-action)}.t-time:before{content:\\\"\\\\2022\\\";display:inline-block;inline-size:1rem;text-align:center}.t-heading{line-height:1.25rem;font-weight:700;margin:.75rem 0 .25rem}.t-heading,.t-content{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:var(--t-lines);word-break:break-word;overflow:hidden}.t-actions{display:flex;align-items:center;font:var(--tui-font-text-s)}.t-button:not(:empty){margin:.75rem .75rem .75rem 0}.t-link{margin:.75rem 0}.t-link:empty{margin:.5rem}\\n\"] }]\n        }], propDecorators: { heading: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }], lines: [{\n                type: Input\n            }], timestamp: [{\n                type: Input\n            }], close: [{\n                type: Output\n            }] } });\n\nconst TUI_PUSH_DEFAULT_OPTIONS = {\n    heading: '',\n    type: '',\n    timestamp: '',\n    image: '',\n    icon: '',\n    iconColor: '',\n    buttons: [],\n};\n/**\n * Default parameters for Push component\n */\nconst TUI_PUSH_OPTIONS = tuiCreateToken(TUI_PUSH_DEFAULT_OPTIONS);\nfunction tuiPushOptionsProvider(options) {\n    return tuiProvideOptions(TUI_PUSH_OPTIONS, options, TUI_PUSH_DEFAULT_OPTIONS);\n}\n\nclass TuiPushAlert {\n    constructor() {\n        this.context = injectContext();\n    }\n    get isDirective() {\n        return this.context.content instanceof TuiPushDirective;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPushAlert, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPushAlert, isStandalone: true, selector: \"ng-component\", host: { attributes: { \"role\": \"alert\" } }, hostDirectives: [{ directive: i1.TuiAnimated }], ngImport: i0, template: \"<div class=\\\"t-wrapper\\\">\\n    <tui-push\\n        *ngIf=\\\"!isDirective; else directive\\\"\\n        [heading]=\\\"context.heading\\\"\\n        [timestamp]=\\\"context.timestamp\\\"\\n        [type]=\\\"context.type\\\"\\n        (close)=\\\"context.$implicit.complete()\\\"\\n    >\\n        <img\\n            *ngIf=\\\"context.image\\\"\\n            alt=\\\"\\\"\\n            [src]=\\\"context.image\\\"\\n        />\\n        <tui-icon\\n            *ngIf=\\\"context.icon\\\"\\n            [icon]=\\\"context.icon\\\"\\n            [style.color]=\\\"context.iconColor\\\"\\n        />\\n        <div\\n            *polymorpheusOutlet=\\\"context.content as text; context: context\\\"\\n            [innerHTML]=\\\"text\\\"\\n        ></div>\\n        <button\\n            *ngIf=\\\"context.buttons.length > 1\\\"\\n            tuiButton\\n            type=\\\"button\\\"\\n            (click)=\\\"context.$implicit.next(context.buttons[0] || '')\\\"\\n        >\\n            {{ context.buttons[0] }}\\n        </button>\\n        <button\\n            *ngIf=\\\"context.buttons.length\\\"\\n            tuiLink\\n            type=\\\"button\\\"\\n            (click)=\\\"context.$implicit.next(context.buttons[context.buttons.length - 1] || '')\\\"\\n        >\\n            {{ context.buttons[context.buttons.length - 1] }}\\n        </button>\\n    </tui-push>\\n    <ng-template #directive>\\n        <ng-container *polymorpheusOutlet=\\\"context.content\\\" />\\n    </ng-template>\\n</div>\\n\", styles: [\":host{position:relative;bottom:1rem;display:grid;flex-shrink:0;max-inline-size:calc(100% - 2rem);margin:0 1rem 0 auto;border-radius:var(--tui-radius-xl);box-shadow:var(--tui-shadow-small);overflow:hidden;--tui-from: translateX(100%)}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide,tuiCollapse}:host.tui-leave:not(:first-child){transition-property:margin;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-top:-1rem!important}:host:first-child{margin-top:auto}:host:not(:last-child){transition-property:margin;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-bottom:1rem}.t-wrapper{grid-row:1 / span 2;overflow:hidden}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"component\", type: TuiIcon, selector: \"tui-icon\", inputs: [\"icon\", \"background\"] }, { kind: \"directive\", type: TuiLink, selector: \"a[tuiLink], button[tuiLink]\", inputs: [\"pseudo\"] }, { kind: \"component\", type: TuiPushComponent, selector: \"tui-push\", inputs: [\"heading\", \"type\", \"lines\", \"timestamp\"], outputs: [\"close\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPushAlert, decorators: [{\n            type: Component,\n            args: [{ standalone: true, imports: [NgIf, PolymorpheusOutlet, TuiButton, TuiIcon, TuiLink, TuiPushComponent], changeDetection: ChangeDetectionStrategy.OnPush, hostDirectives: [TuiAnimated], host: {\n                        role: 'alert',\n                    }, template: \"<div class=\\\"t-wrapper\\\">\\n    <tui-push\\n        *ngIf=\\\"!isDirective; else directive\\\"\\n        [heading]=\\\"context.heading\\\"\\n        [timestamp]=\\\"context.timestamp\\\"\\n        [type]=\\\"context.type\\\"\\n        (close)=\\\"context.$implicit.complete()\\\"\\n    >\\n        <img\\n            *ngIf=\\\"context.image\\\"\\n            alt=\\\"\\\"\\n            [src]=\\\"context.image\\\"\\n        />\\n        <tui-icon\\n            *ngIf=\\\"context.icon\\\"\\n            [icon]=\\\"context.icon\\\"\\n            [style.color]=\\\"context.iconColor\\\"\\n        />\\n        <div\\n            *polymorpheusOutlet=\\\"context.content as text; context: context\\\"\\n            [innerHTML]=\\\"text\\\"\\n        ></div>\\n        <button\\n            *ngIf=\\\"context.buttons.length > 1\\\"\\n            tuiButton\\n            type=\\\"button\\\"\\n            (click)=\\\"context.$implicit.next(context.buttons[0] || '')\\\"\\n        >\\n            {{ context.buttons[0] }}\\n        </button>\\n        <button\\n            *ngIf=\\\"context.buttons.length\\\"\\n            tuiLink\\n            type=\\\"button\\\"\\n            (click)=\\\"context.$implicit.next(context.buttons[context.buttons.length - 1] || '')\\\"\\n        >\\n            {{ context.buttons[context.buttons.length - 1] }}\\n        </button>\\n    </tui-push>\\n    <ng-template #directive>\\n        <ng-container *polymorpheusOutlet=\\\"context.content\\\" />\\n    </ng-template>\\n</div>\\n\", styles: [\":host{position:relative;bottom:1rem;display:grid;flex-shrink:0;max-inline-size:calc(100% - 2rem);margin:0 1rem 0 auto;border-radius:var(--tui-radius-xl);box-shadow:var(--tui-shadow-small);overflow:hidden;--tui-from: translateX(100%)}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide,tuiCollapse}:host.tui-leave:not(:first-child){transition-property:margin;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-top:-1rem!important}:host:first-child{margin-top:auto}:host:not(:last-child){transition-property:margin;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-bottom:1rem}.t-wrapper{grid-row:1 / span 2;overflow:hidden}\\n\"] }]\n        }] });\n\nclass TuiPushService extends TuiPopoverService {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPushService, deps: null, target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPushService, providedIn: 'root', useFactory: () => new TuiPushService(TUI_ALERTS, TuiPushAlert, inject(TUI_PUSH_OPTIONS)) }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPushService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                    useFactory: () => new TuiPushService(TUI_ALERTS, TuiPushAlert, inject(TUI_PUSH_OPTIONS)),\n                }]\n        }] });\n\nclass TuiPushDirective extends PolymorpheusTemplate {\n    constructor() {\n        super(inject(TemplateRef), inject(ChangeDetectorRef));\n        this.push = inject(forwardRef(() => TuiPushService));\n        this.show$ = new Subject();\n        this.show$\n            .pipe(tuiIfMap(() => this.push.open(this)), takeUntilDestroyed())\n            .subscribe();\n    }\n    set tuiPush(show) {\n        this.show$.next(show);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPushDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPushDirective, isStandalone: true, selector: \"ng-template[tuiPush]\", inputs: { tuiPush: \"tuiPush\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPushDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-template[tuiPush]',\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { tuiPush: [{\n                type: Input\n            }] } });\n\nconst TuiPush = [TuiPushComponent, TuiPushAlert, TuiPushDirective];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_PUSH_DEFAULT_OPTIONS, TUI_PUSH_OPTIONS, TuiPush, TuiPushAlert, TuiPushComponent, TuiPushDirective, TuiPushService, tuiPushOptionsProvider };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,IAAI,QAAQ,iBAAiB;AACjD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,SAAS,QAAQ,eAAe;AAC1K,SAASC,WAAW,EAAEC,cAAc,EAAEC,iBAAiB,QAAQ,mCAAmC;AAClG,SAASC,wBAAwB,EAAEC,SAAS,QAAQ,kCAAkC;AACtF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,uBAAuB;AACxE,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,aAAa,EAAEC,kBAAkB,EAAEC,oBAAoB,QAAQ,wBAAwB;AAChG,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,OAAO,KAAKC,EAAE,MAAM,mCAAmC;AACvD,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,OAAO,QAAQ,gCAAgC;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,mCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAa4CrC,EAAE,CAAAsC,gBAAA;IAAFtC,EAAE,CAAAuC,cAAA,gBAC6nB,CAAC;IADhoBvC,EAAE,CAAAwC,UAAA,mBAAAC,2DAAA;MAAFzC,EAAE,CAAA0C,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAF3C,EAAE,CAAA4C,aAAA;MAAA,OAAF5C,EAAE,CAAA6C,WAAA,CAC6mBF,MAAA,CAAAG,KAAA,CAAAC,IAAA,CAAW,CAAC;IAAA,CAAC,CAAC;IAD7nB/C,EAAE,CAAAgD,MAAA,EAC6pB,CAAC;IADhqBhD,EAAE,CAAAiD,MAAA;IAAFjD,EAAE,CAAAkD,YAAA,CACsqB,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAQ,MAAA,GADzqB3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAmD,WAAA,0BAC4lB,CAAC;IAD/lBnD,EAAE,CAAAoD,UAAA,cAAAT,MAAA,CAAAU,KAAA,CAAAP,KACujB,CAAC;IAD1jB9C,EAAE,CAAAsD,SAAA,CAC6pB,CAAC;IADhqBtD,EAAE,CAAAuD,kBAAA,MAAFvD,EAAE,CAAAwD,WAAA,OAAAb,MAAA,CAAAc,UAAA,OAC6pB,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADhqBnC,EAAE,CAAA2D,SAAA,cAC49B,CAAC;IAD/9B3D,EAAE,CAAAiD,MAAA;IAAFjD,EAAE,CAAAiD,MAAA;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAQ,MAAA,GAAF3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAoD,UAAA,gBAAAT,MAAA,CAAAiB,QAAA,CAAAjB,MAAA,CAAAkB,SAAA,IAAAlB,MAAA,CAAAkB,SAAA,GAAF7D,EAAE,CAAAwD,WAAA,OAAFxD,EAAE,CAAAwD,WAAA,OAAAb,MAAA,CAAAkB,SAAA,EAC88B,CAAC;EAAA;AAAA;AAAA,SAAAC,uCAAA3B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADj9BnC,EAAE,CAAA2D,SAAA,YA6C0iB,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAQ,MAAA,GA7C7iB3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAoD,UAAA,QAAAT,MAAA,CAAAoB,OAAA,CAAAC,KAAA,EAAFhE,EAAE,CAAAiE,aA6C8hB,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7CjiBnC,EAAE,CAAA2D,SAAA,kBA6CmsB,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAQ,MAAA,GA7CtsB3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAmD,WAAA,UAAAR,MAAA,CAAAoB,OAAA,CAAAI,SA6CurB,CAAC;IA7C1rBnE,EAAE,CAAAoD,UAAA,SAAAT,MAAA,CAAAoB,OAAA,CAAAK,IA6CsoB,CAAC;EAAA;AAAA;AAAA,SAAAC,uCAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7CzoBnC,EAAE,CAAA2D,SAAA,aA6Cm1B,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAmC,OAAA,GAAAlC,GAAA,CAAAmC,kBAAA;IA7Ct1BvE,EAAE,CAAAoD,UAAA,cAAAkB,OAAA,EAAFtE,EAAE,CAAAwE,cA6Ck0B,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuC,GAAA,GA7Cr0B1E,EAAE,CAAAsC,gBAAA;IAAFtC,EAAE,CAAAuC,cAAA,gBA6C+hC,CAAC;IA7CliCvC,EAAE,CAAAwC,UAAA,mBAAAmC,kEAAA;MAAF3E,EAAE,CAAA0C,aAAA,CAAAgC,GAAA;MAAA,MAAA/B,MAAA,GAAF3C,EAAE,CAAA4C,aAAA;MAAA,OAAF5C,EAAE,CAAA6C,WAAA,CA6Cm+BF,MAAA,CAAAoB,OAAA,CAAAa,SAAA,CAAAC,IAAA,CAAAlC,MAAA,CAAAoB,OAAA,CAAAe,OAAA,CAAuC,CAAC,KAAK,EAAE,CAAC;IAAA,CAAC,CAAC;IA7CvhC9E,EAAE,CAAAgD,MAAA,EA6C+kC,CAAC;IA7CllChD,EAAE,CAAAkD,YAAA,CA6CwlC,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAQ,MAAA,GA7C3lC3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAsD,SAAA,CA6C+kC,CAAC;IA7CllCtD,EAAE,CAAAuD,kBAAA,MAAAZ,MAAA,CAAAoB,OAAA,CAAAe,OAAA,QA6C+kC,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAA5C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA6C,GAAA,GA7CllChF,EAAE,CAAAsC,gBAAA;IAAFtC,EAAE,CAAAuC,cAAA,gBA6CuzC,CAAC;IA7C1zCvC,EAAE,CAAAwC,UAAA,mBAAAyC,kEAAA;MAAFjF,EAAE,CAAA0C,aAAA,CAAAsC,GAAA;MAAA,MAAArC,MAAA,GAAF3C,EAAE,CAAA4C,aAAA;MAAA,OAAF5C,EAAE,CAAA6C,WAAA,CA6CkuCF,MAAA,CAAAoB,OAAA,CAAAa,SAAA,CAAAC,IAAA,CAAAlC,MAAA,CAAAoB,OAAA,CAAAe,OAAA,CAAAnC,MAAA,CAAAoB,OAAA,CAAAe,OAAA,CAAAI,MAAA,GAAgE,CAAC,KAAK,EAAE,CAAC;IAAA,CAAC,CAAC;IA7C/yClF,EAAE,CAAAgD,MAAA,EA6Cg4C,CAAC;IA7Cn4ChD,EAAE,CAAAkD,YAAA,CA6Cy4C,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAQ,MAAA,GA7C54C3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAsD,SAAA,CA6Cg4C,CAAC;IA7Cn4CtD,EAAE,CAAAuD,kBAAA,MAAAZ,MAAA,CAAAoB,OAAA,CAAAe,OAAA,CAAAnC,MAAA,CAAAoB,OAAA,CAAAe,OAAA,CAAAI,MAAA,UA6Cg4C,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAhD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GA7Cn4CrC,EAAE,CAAAsC,gBAAA;IAAFtC,EAAE,CAAAuC,cAAA,iBA6Cgb,CAAC;IA7CnbvC,EAAE,CAAAwC,UAAA,mBAAA4C,2DAAA;MAAFpF,EAAE,CAAA0C,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAF3C,EAAE,CAAA4C,aAAA;MAAA,OAAF5C,EAAE,CAAA6C,WAAA,CA6C4YF,MAAA,CAAAoB,OAAA,CAAAa,SAAA,CAAAS,QAAA,CAA2B,CAAC;IAAA,CAAC,CAAC;IA7C5arF,EAAE,CAAAsF,UAAA,IAAAxB,sCAAA,gBA6C0iB,CAAC,IAAAI,2CAAA,qBAAwJ,CAAC,IAAAG,sCAAA,gBAAyI,CAAC,IAAAI,yCAAA,mBAAiN,CAAC,IAAAM,yCAAA,mBAAuR,CAAC;IA7C1zC/E,EAAE,CAAAkD,YAAA,CA6C05C,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAQ,MAAA,GA7C75C3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAoD,UAAA,YAAAT,MAAA,CAAAoB,OAAA,CAAAwB,OA6C2S,CAAC,cAAA5C,MAAA,CAAAoB,OAAA,CAAAF,SAA0C,CAAC,SAAAlB,MAAA,CAAAoB,OAAA,CAAAyB,IAAgC,CAAC;IA7C1XxF,EAAE,CAAAsD,SAAA,CA6Cie,CAAC;IA7CpetD,EAAE,CAAAoD,UAAA,SAAAT,MAAA,CAAAoB,OAAA,CAAAC,KA6Cie,CAAC;IA7CpehE,EAAE,CAAAsD,SAAA,CA6C+lB,CAAC;IA7ClmBtD,EAAE,CAAAoD,UAAA,SAAAT,MAAA,CAAAoB,OAAA,CAAAK,IA6C+lB,CAAC;IA7ClmBpE,EAAE,CAAAsD,SAAA,CA6CqwB,CAAC;IA7CxwBtD,EAAE,CAAAoD,UAAA,uBAAAT,MAAA,CAAAoB,OAAA,CAAA0B,OA6CqwB,CAAC,8BAAA9C,MAAA,CAAAoB,OAAwB,CAAC;IA7CjyB/D,EAAE,CAAAsD,SAAA,CA6Co5B,CAAC;IA7Cv5BtD,EAAE,CAAAoD,UAAA,SAAAT,MAAA,CAAAoB,OAAA,CAAAe,OAAA,CAAAI,MAAA,IA6Co5B,CAAC;IA7Cv5BlF,EAAE,CAAAsD,SAAA,CA6CqpC,CAAC;IA7CxpCtD,EAAE,CAAAoD,UAAA,SAAAT,MAAA,CAAAoB,OAAA,CAAAe,OAAA,CAAAI,MA6CqpC,CAAC;EAAA;AAAA;AAAA,SAAAQ,mDAAAvD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7CxpCnC,EAAE,CAAA2F,kBAAA,EA6C0/C,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAAzD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7C7/CnC,EAAE,CAAAsF,UAAA,IAAAI,kDAAA,0BA6C0/C,CAAC;EAAA;EAAA,IAAAvD,EAAA;IAAA,MAAAQ,MAAA,GA7C7/C3C,EAAE,CAAA4C,aAAA;IAAF5C,EAAE,CAAAoD,UAAA,uBAAAT,MAAA,CAAAoB,OAAA,CAAA0B,OA6Cq/C,CAAC;EAAA;AAAA;AAxD7lD,MAAMI,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,QAAQ,GAAGhD,WAAW;IAC3B,IAAI,CAAC6C,UAAU,GAAGxD,MAAM,CAACiB,cAAc,CAAC;IACxC,IAAI,CAACmC,KAAK,GAAGpD,MAAM,CAACkB,gBAAgB,CAAC;IACrC,IAAI,CAACoE,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACO,KAAK,GAAG,CAAC;IACd,IAAI,CAAClC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACf,KAAK,GAAG,IAAI5C,YAAY,CAAC,CAAC;EACnC;EACA;IAAS,IAAI,CAAC8F,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFL,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACM,IAAI,kBAD+EnG,EAAE,CAAAoG,iBAAA;MAAAZ,IAAA,EACJK,gBAAgB;MAAAQ,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,8BAAArE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADdnC,EAAE,CAAAmD,WAAA,cAAAf,GAAA,CAAA2D,KACW,CAAC;QAAA;MAAA;MAAAU,MAAA;QAAAlB,OAAA;QAAAC,IAAA;QAAAO,KAAA;QAAAlC,SAAA;MAAA;MAAA6C,OAAA;QAAA5D,KAAA;MAAA;MAAA6D,UAAA;MAAAC,QAAA,GADd5G,EAAE,CAAA6G,kBAAA,CAC4O,CAAC9F,wBAAwB,CAAC;QAAE+F,IAAI,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAY,CAAC,CAAC,CAAC,GADhT/G,EAAE,CAAAgH,mBAAA;MAAAC,kBAAA,EAAAhF,GAAA;MAAAiF,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAnF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnC,EAAE,CAAAuH,eAAA,CAAAvF,GAAA;UAAFhC,EAAE,CAAAuC,cAAA,YAC+V,CAAC;UADlWvC,EAAE,CAAAwH,YAAA,EACkY,CAAC;UADrYxH,EAAE,CAAAkD,YAAA,CAC0Y,CAAC;UAD7YlD,EAAE,CAAAsF,UAAA,IAAApD,kCAAA,mBAC6nB,CAAC;UADhoBlC,EAAE,CAAAuC,cAAA,YAC6rB,CAAC,aAA4B,CAAC;UAD7tBvC,EAAE,CAAAwH,YAAA,KAC8wB,CAAC;UADjxBxH,EAAE,CAAAkD,YAAA,CAC2xB,CAAC;UAD9xBlD,EAAE,CAAAgD,MAAA,EACizB,CAAC;UADpzBhD,EAAE,CAAAsF,UAAA,IAAA5B,gCAAA,iBACq9B,CAAC;UADx9B1D,EAAE,CAAAkD,YAAA,CACo+B,CAAC;UADv+BlD,EAAE,CAAAuC,cAAA,WAC8iC,CAAC;UADjjCvC,EAAE,CAAAgD,MAAA,EACmkC,CAAC;UADtkChD,EAAE,CAAAkD,YAAA,CACwkC,CAAC;UAD3kClD,EAAE,CAAAuC,cAAA,aACmmC,CAAC;UADtmCvC,EAAE,CAAAwH,YAAA,MACunC,CAAC;UAD1nCxH,EAAE,CAAAkD,YAAA,CAC+nC,CAAC;UADloClD,EAAE,CAAAuC,cAAA,aAC0pC,CAAC,cAA8B,CAAC;UAD5rCvC,EAAE,CAAAwH,YAAA,MACwuC,CAAC;UAD3uCxH,EAAE,CAAAkD,YAAA,CACqvC,CAAC;UADxvClD,EAAE,CAAAuC,cAAA,cACkxC,CAAC;UADrxCvC,EAAE,CAAAwH,YAAA,MAC+zC,CAAC;UADl0CxH,EAAE,CAAAkD,YAAA,CAC40C,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAf,EAAA;UADv1CnC,EAAE,CAAAsD,SAAA,EAC+a,CAAC;UADlbtD,EAAE,CAAAoD,UAAA,SAAAhB,GAAA,CAAAU,KAAA,CAAA2E,QAC+a,CAAC;UADlbzH,EAAE,CAAAsD,SAAA,EACizB,CAAC;UADpzBtD,EAAE,CAAAuD,kBAAA,MAAAnB,GAAA,CAAAoD,IAAA,KACizB,CAAC;UADpzBxF,EAAE,CAAAsD,SAAA,CACi1B,CAAC;UADp1BtD,EAAE,CAAAoD,UAAA,SAAAhB,GAAA,CAAAyB,SACi1B,CAAC;UADp1B7D,EAAE,CAAAsD,SAAA,EACmkC,CAAC;UADtkCtD,EAAE,CAAAuD,kBAAA,MAAAnB,GAAA,CAAAmD,OAAA,MACmkC,CAAC;QAAA;MAAA;MAAAmC,YAAA,GAAojD5H,SAAS,EAA8CC,IAAI,EAA6FiB,SAAS,EAA+HC,iBAAiB;MAAA0G,MAAA;MAAAC,eAAA;IAAA,EAAiF;EAAE;AACnmG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG7H,EAAE,CAAA8H,iBAAA,CAGXjC,gBAAgB,EAAc,CAAC;IAC/GL,IAAI,EAAErF,SAAS;IACf4H,IAAI,EAAE,CAAC;MAAEpB,UAAU,EAAE,IAAI;MAAEqB,QAAQ,EAAE,UAAU;MAAEC,OAAO,EAAE,CAACnI,SAAS,EAAEC,IAAI,EAAEiB,SAAS,EAAEC,iBAAiB,CAAC;MAAE2G,eAAe,EAAExH,uBAAuB,CAAC8H,MAAM;MAAEC,SAAS,EAAE,CAACpH,wBAAwB,CAAC;QAAE+F,IAAI,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAY,CAAC,CAAC,CAAC;MAAEqB,IAAI,EAAE;QACrO,mBAAmB,EAAE;MACzB,CAAC;MAAEf,QAAQ,EAAE,ghCAAghC;MAAEM,MAAM,EAAE,CAAC,6uCAA6uC;IAAE,CAAC;EACpyE,CAAC,CAAC,QAAkB;IAAEpC,OAAO,EAAE,CAAC;MACxBC,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAEmF,IAAI,EAAE,CAAC;MACPA,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAE0F,KAAK,EAAE,CAAC;MACRP,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAEwD,SAAS,EAAE,CAAC;MACZ2B,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAEyC,KAAK,EAAE,CAAC;MACR0C,IAAI,EAAElF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+H,wBAAwB,GAAG;EAC7B9C,OAAO,EAAE,EAAE;EACXC,IAAI,EAAE,EAAE;EACR3B,SAAS,EAAE,EAAE;EACbG,KAAK,EAAE,EAAE;EACTI,IAAI,EAAE,EAAE;EACRD,SAAS,EAAE,EAAE;EACbW,OAAO,EAAE;AACb,CAAC;AACD;AACA;AACA;AACA,MAAMwD,gBAAgB,GAAGzH,cAAc,CAACwH,wBAAwB,CAAC;AACjE,SAASE,sBAAsBA,CAACC,OAAO,EAAE;EACrC,OAAO1H,iBAAiB,CAACwH,gBAAgB,EAAEE,OAAO,EAAEH,wBAAwB,CAAC;AACjF;AAEA,MAAMI,YAAY,CAAC;EACf3C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC/B,OAAO,GAAGzC,aAAa,CAAC,CAAC;EAClC;EACA,IAAIoH,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC3E,OAAO,CAAC0B,OAAO,YAAYkD,gBAAgB;EAC3D;EACA;IAAS,IAAI,CAAC3C,IAAI,YAAA4C,qBAAA1C,CAAA;MAAA,YAAAA,CAAA,IAAyFuC,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACtC,IAAI,kBA7C+EnG,EAAE,CAAAoG,iBAAA;MAAAZ,IAAA,EA6CJiD,YAAY;MAAApC,SAAA;MAAAwC,SAAA,WAA8E,OAAO;MAAAlC,UAAA;MAAAC,QAAA,GA7C/F5G,EAAE,CAAA8I,uBAAA,EA6CiIlH,EAAE,CAACC,WAAW,IA7CjJ7B,EAAE,CAAAgH,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA0B,sBAAA5G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnC,EAAE,CAAAuC,cAAA,YA6CqM,CAAC;UA7CxMvC,EAAE,CAAAsF,UAAA,IAAAH,gCAAA,qBA6Cgb,CAAC,IAAAS,mCAAA,gCA7Cnb5F,EAAE,CAAAgJ,sBA6Cw7C,CAAC;UA7C37ChJ,EAAE,CAAAkD,YAAA,CA6CshD,CAAC;QAAA;QAAA,IAAAf,EAAA;UAAA,MAAA8G,YAAA,GA7CzhDjJ,EAAE,CAAAkJ,WAAA;UAAFlJ,EAAE,CAAAsD,SAAA,CA6CoP,CAAC;UA7CvPtD,EAAE,CAAAoD,UAAA,UAAAhB,GAAA,CAAAsG,WA6CoP,CAAC,aAAAO,YAAa,CAAC;QAAA;MAAA;MAAAvB,YAAA,GAA8hE3H,IAAI,EAA6FwB,kBAAkB,EAA8HP,SAAS,EAAoIc,OAAO,EAAqFC,OAAO,EAA4F8D,gBAAgB;MAAA8B,MAAA;MAAAC,eAAA;IAAA,EAAuJ;EAAE;AAC9mG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/CqG7H,EAAE,CAAA8H,iBAAA,CA+CXW,YAAY,EAAc,CAAC;IAC3GjD,IAAI,EAAErF,SAAS;IACf4H,IAAI,EAAE,CAAC;MAAEpB,UAAU,EAAE,IAAI;MAAEsB,OAAO,EAAE,CAAClI,IAAI,EAAEwB,kBAAkB,EAAEP,SAAS,EAAEc,OAAO,EAAEC,OAAO,EAAE8D,gBAAgB,CAAC;MAAE+B,eAAe,EAAExH,uBAAuB,CAAC8H,MAAM;MAAEiB,cAAc,EAAE,CAACtH,WAAW,CAAC;MAAEuG,IAAI,EAAE;QACzLgB,IAAI,EAAE;MACV,CAAC;MAAE/B,QAAQ,EAAE,82CAA82C;MAAEM,MAAM,EAAE,CAAC,+sBAA+sB;IAAE,CAAC;EACpmE,CAAC,CAAC;AAAA;AAEV,MAAM0B,cAAc,SAAS3H,iBAAiB,CAAC;EAC3C;IAAS,IAAI,CAACsE,IAAI;MAAA,IAAAsD,2BAAA;MAAA,gBAAAC,uBAAArD,CAAA;QAAA,QAAAoD,2BAAA,KAAAA,2BAAA,GAvD+EtJ,EAAE,CAAAwJ,qBAAA,CAuDQH,cAAc,IAAAnD,CAAA,IAAdmD,cAAc;MAAA;IAAA,IAAsD;EAAE;EACjL;IAAS,IAAI,CAACI,KAAK,kBAxD8EzJ,EAAE,CAAA0J,kBAAA;MAAAC,KAAA,EAwDYN,cAAc;MAAAO,OAAA,EAAAA,CAAA,MAAkC,MAAM,IAAIP,cAAc,CAAC1H,UAAU,EAAE8G,YAAY,EAAExI,MAAM,CAACqI,gBAAgB,CAAC,CAAC;MAAAuB,UAAA,EAAhG;IAAM,EAA6F;EAAE;AACpP;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KA1DqG7H,EAAE,CAAA8H,iBAAA,CA0DXuB,cAAc,EAAc,CAAC;IAC7G7D,IAAI,EAAEjF,UAAU;IAChBwH,IAAI,EAAE,CAAC;MACC8B,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAEA,CAAA,KAAM,IAAIT,cAAc,CAAC1H,UAAU,EAAE8G,YAAY,EAAExI,MAAM,CAACqI,gBAAgB,CAAC;IAC3F,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMK,gBAAgB,SAASnH,oBAAoB,CAAC;EAChDsE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC7F,MAAM,CAACO,WAAW,CAAC,EAAEP,MAAM,CAACQ,iBAAiB,CAAC,CAAC;IACrD,IAAI,CAACsJ,IAAI,GAAG9J,MAAM,CAACS,UAAU,CAAC,MAAM2I,cAAc,CAAC,CAAC;IACpD,IAAI,CAACW,KAAK,GAAG,IAAIvI,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACuI,KAAK,CACLC,IAAI,CAAC5I,QAAQ,CAAC,MAAM,IAAI,CAAC0I,IAAI,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE9I,kBAAkB,CAAC,CAAC,CAAC,CAChE+I,SAAS,CAAC,CAAC;EACpB;EACA,IAAIC,OAAOA,CAACC,IAAI,EAAE;IACd,IAAI,CAACL,KAAK,CAACnF,IAAI,CAACwF,IAAI,CAAC;EACzB;EACA;IAAS,IAAI,CAACrE,IAAI,YAAAsE,yBAAApE,CAAA;MAAA,YAAAA,CAAA,IAAyFyC,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAAC4B,IAAI,kBA/E+EvK,EAAE,CAAAwK,iBAAA;MAAAhF,IAAA,EA+EJmD,gBAAgB;MAAAtC,SAAA;MAAAI,MAAA;QAAA2D,OAAA;MAAA;MAAAzD,UAAA;MAAAC,QAAA,GA/Ed5G,EAAE,CAAAyK,0BAAA;IAAA,EA+E0I;EAAE;AACnP;AACA;EAAA,QAAA5C,SAAA,oBAAAA,SAAA,KAjFqG7H,EAAE,CAAA8H,iBAAA,CAiFXa,gBAAgB,EAAc,CAAC;IAC/GnD,IAAI,EAAE7E,SAAS;IACfoH,IAAI,EAAE,CAAC;MACCpB,UAAU,EAAE,IAAI;MAChBqB,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEoC,OAAO,EAAE,CAAC;MACpE5E,IAAI,EAAEnF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMqK,OAAO,GAAG,CAAC7E,gBAAgB,EAAE4C,YAAY,EAAEE,gBAAgB,CAAC;;AAElE;AACA;AACA;;AAEA,SAASN,wBAAwB,EAAEC,gBAAgB,EAAEoC,OAAO,EAAEjC,YAAY,EAAE5C,gBAAgB,EAAE8C,gBAAgB,EAAEU,cAAc,EAAEd,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}