{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, signal, Directive, Input } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nclass TuiIconBadgeStyles {\n  static {\n    this.ɵfac = function TuiIconBadgeStyles_Factory(t) {\n      return new (t || TuiIconBadgeStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiIconBadgeStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-icon-badge\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiIconBadgeStyles_Template(rf, ctx) {},\n      styles: [\"[tuiIconBadge]:before{content:\\\"\\\";position:absolute;right:.143em;bottom:.143em;display:var(--t-icon-badge, none);inline-size:.57em;block-size:.57em;transform:translate(50%,50%);-webkit-mask:var(--t-icon-badge) no-repeat center / contain;mask:var(--t-icon-badge) no-repeat center / contain;background:currentColor}[tuiIconBadge][style*=\\\"--t-icon-badge:\\\"]:after{-webkit-mask-image:var(--t-icon),radial-gradient(circle at bottom .1em right .1em,transparent calc(.4em - .5px),#000 .4em);mask-image:var(--t-icon),radial-gradient(circle at bottom .1em right .1em,transparent calc(.4em - .5px),#000 .4em);-webkit-mask-composite:source-in,xor;mask-composite:intersect}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiIconBadgeStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-icon-badge'\n      },\n      styles: [\"[tuiIconBadge]:before{content:\\\"\\\";position:absolute;right:.143em;bottom:.143em;display:var(--t-icon-badge, none);inline-size:.57em;block-size:.57em;transform:translate(50%,50%);-webkit-mask:var(--t-icon-badge) no-repeat center / contain;mask:var(--t-icon-badge) no-repeat center / contain;background:currentColor}[tuiIconBadge][style*=\\\"--t-icon-badge:\\\"]:after{-webkit-mask-image:var(--t-icon),radial-gradient(circle at bottom .1em right .1em,transparent calc(.4em - .5px),#000 .4em);mask-image:var(--t-icon),radial-gradient(circle at bottom .1em right .1em,transparent calc(.4em - .5px),#000 .4em);-webkit-mask-composite:source-in,xor;mask-composite:intersect}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiIconBadge {\n  constructor() {\n    this.icon = inject(TuiIcon);\n    this.nothing = tuiWithStyles(TuiIconBadgeStyles);\n    this.badgeSrc = signal(null);\n  }\n  set badge(icon) {\n    this.badgeSrc.set(this.icon.resolve(icon));\n  }\n  static {\n    this.ɵfac = function TuiIconBadge_Factory(t) {\n      return new (t || TuiIconBadge)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiIconBadge,\n      selectors: [[\"tui-icon\", \"badge\", \"\"]],\n      hostAttrs: [\"tuiIconBadge\", \"\"],\n      hostVars: 2,\n      hostBindings: function TuiIconBadge_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-icon-badge\", ctx.badgeSrc());\n        }\n      },\n      inputs: {\n        badge: \"badge\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiIconBadge, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'tui-icon[badge]',\n      host: {\n        tuiIconBadge: '',\n        '[style.--t-icon-badge]': 'badgeSrc()'\n      }\n    }]\n  }], null, {\n    badge: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiIconBadge };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "signal", "Directive", "Input", "tuiWithStyles", "TuiIcon", "TuiIconBadgeStyles", "ɵfac", "TuiIconBadgeStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiIconBadgeStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiIconBadge", "constructor", "icon", "nothing", "badgeSrc", "badge", "set", "resolve", "TuiIconBadge_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiIconBadge_HostBindings", "ɵɵstyleProp", "inputs", "selector", "tuiIconBadge"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-icon-badge.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, signal, Directive, Input } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\n\nclass TuiIconBadgeStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIconBadgeStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiIconBadgeStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-icon-badge\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiIconBadge]:before{content:\\\"\\\";position:absolute;right:.143em;bottom:.143em;display:var(--t-icon-badge, none);inline-size:.57em;block-size:.57em;transform:translate(50%,50%);-webkit-mask:var(--t-icon-badge) no-repeat center / contain;mask:var(--t-icon-badge) no-repeat center / contain;background:currentColor}[tuiIconBadge][style*=\\\"--t-icon-badge:\\\"]:after{-webkit-mask-image:var(--t-icon),radial-gradient(circle at bottom .1em right .1em,transparent calc(.4em - .5px),#000 .4em);mask-image:var(--t-icon),radial-gradient(circle at bottom .1em right .1em,transparent calc(.4em - .5px),#000 .4em);-webkit-mask-composite:source-in,xor;mask-composite:intersect}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIconBadgeStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-icon-badge',\n                    }, styles: [\"[tuiIconBadge]:before{content:\\\"\\\";position:absolute;right:.143em;bottom:.143em;display:var(--t-icon-badge, none);inline-size:.57em;block-size:.57em;transform:translate(50%,50%);-webkit-mask:var(--t-icon-badge) no-repeat center / contain;mask:var(--t-icon-badge) no-repeat center / contain;background:currentColor}[tuiIconBadge][style*=\\\"--t-icon-badge:\\\"]:after{-webkit-mask-image:var(--t-icon),radial-gradient(circle at bottom .1em right .1em,transparent calc(.4em - .5px),#000 .4em);mask-image:var(--t-icon),radial-gradient(circle at bottom .1em right .1em,transparent calc(.4em - .5px),#000 .4em);-webkit-mask-composite:source-in,xor;mask-composite:intersect}\\n\"] }]\n        }] });\nclass TuiIconBadge {\n    constructor() {\n        this.icon = inject(TuiIcon);\n        this.nothing = tuiWithStyles(TuiIconBadgeStyles);\n        this.badgeSrc = signal(null);\n    }\n    set badge(icon) {\n        this.badgeSrc.set(this.icon.resolve(icon));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIconBadge, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiIconBadge, isStandalone: true, selector: \"tui-icon[badge]\", inputs: { badge: \"badge\" }, host: { attributes: { \"tuiIconBadge\": \"\" }, properties: { \"style.--t-icon-badge\": \"badgeSrc()\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIconBadge, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-icon[badge]',\n                    host: {\n                        tuiIconBadge: '',\n                        '[style.--t-icon-badge]': 'badgeSrc()',\n                    },\n                }]\n        }], propDecorators: { badge: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiIconBadge };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACvH,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,OAAO,QAAQ,gCAAgC;AAExD,MAAMC,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACC,IAAI,YAAAC,2BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,kBAAkB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAACI,IAAI,kBAD+Ed,EAAE,CAAAe,iBAAA;MAAAC,IAAA,EACJN,kBAAkB;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADhBpB,EAAE,CAAAqB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC45B;EAAE;AACrgC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG/B,EAAE,CAAAgC,iBAAA,CAGXtB,kBAAkB,EAAc,CAAC;IACjHM,IAAI,EAAEf,SAAS;IACfgC,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAE3B,iBAAiB,CAACgC,IAAI;MAAEJ,eAAe,EAAE3B,uBAAuB,CAACgC,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,2pBAA2pB;IAAE,CAAC;EACtrB,CAAC,CAAC;AAAA;AACV,MAAMU,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,IAAI,GAAGpC,MAAM,CAACK,OAAO,CAAC;IAC3B,IAAI,CAACgC,OAAO,GAAGjC,aAAa,CAACE,kBAAkB,CAAC;IAChD,IAAI,CAACgC,QAAQ,GAAGrC,MAAM,CAAC,IAAI,CAAC;EAChC;EACA,IAAIsC,KAAKA,CAACH,IAAI,EAAE;IACZ,IAAI,CAACE,QAAQ,CAACE,GAAG,CAAC,IAAI,CAACJ,IAAI,CAACK,OAAO,CAACL,IAAI,CAAC,CAAC;EAC9C;EACA;IAAS,IAAI,CAAC7B,IAAI,YAAAmC,qBAAAjC,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACS,IAAI,kBAnB+E/C,EAAE,CAAAgD,iBAAA;MAAAhC,IAAA,EAmBJsB,YAAY;MAAArB,SAAA;MAAAC,SAAA,mBAAqH,EAAE;MAAA+B,QAAA;MAAAC,YAAA,WAAAC,0BAAAzB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnBjI1B,EAAE,CAAAoD,WAAA,mBAmBJzB,GAAA,CAAAe,QAAA,CAAS,CAAE,CAAC;QAAA;MAAA;MAAAW,MAAA;QAAAV,KAAA;MAAA;MAAAxB,UAAA;IAAA,EAAkM;EAAE;AACnT;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KArBqG/B,EAAE,CAAAgC,iBAAA,CAqBXM,YAAY,EAAc,CAAC;IAC3GtB,IAAI,EAAEV,SAAS;IACf2B,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBmC,QAAQ,EAAE,iBAAiB;MAC3BlB,IAAI,EAAE;QACFmB,YAAY,EAAE,EAAE;QAChB,wBAAwB,EAAE;MAC9B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEZ,KAAK,EAAE,CAAC;MACtB3B,IAAI,EAAET;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAAS+B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}