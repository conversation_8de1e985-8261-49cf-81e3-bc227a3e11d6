{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive } from '@angular/core';\nimport { Validators, NG_VALIDATORS } from '@angular/forms';\nimport { EMPTY_FUNCTION } from '@taiga-ui/cdk/constants';\nimport { tuiProvide } from '@taiga-ui/cdk/utils';\nclass TuiValidator {\n  constructor() {\n    this.onChange = EMPTY_FUNCTION;\n    this.validate = Validators.nullValidator;\n  }\n  registerOnValidatorChange(onChange) {\n    this.onChange = onChange;\n  }\n  ngOnChanges() {\n    this.onChange();\n  }\n  static {\n    this.ɵfac = function TuiValidator_Factory(t) {\n      return new (t || TuiValidator)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiValidator,\n      selectors: [[\"\", \"tuiValidator\", \"\"]],\n      inputs: {\n        validate: [i0.ɵɵInputFlags.None, \"tuiValidator\", \"validate\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(NG_VALIDATORS, TuiValidator, true)]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiValidator, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiValidator]',\n      inputs: ['validate: tuiValidator'],\n      providers: [tuiProvide(NG_VALIDATORS, TuiValidator, true)]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiValidator };", "map": {"version": 3, "names": ["i0", "Directive", "Validators", "NG_VALIDATORS", "EMPTY_FUNCTION", "tui<PERSON><PERSON><PERSON>", "TuiValida<PERSON>", "constructor", "onChange", "validate", "nullValidator", "registerOnValidatorChange", "ngOnChanges", "ɵfac", "TuiValidator_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "ɵɵInputFlags", "None", "standalone", "features", "ɵɵProvidersFeature", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-validator.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive } from '@angular/core';\nimport { Validators, NG_VALIDATORS } from '@angular/forms';\nimport { EMPTY_FUNCTION } from '@taiga-ui/cdk/constants';\nimport { tuiProvide } from '@taiga-ui/cdk/utils';\n\nclass TuiValidator {\n    constructor() {\n        this.onChange = EMPTY_FUNCTION;\n        this.validate = Validators.nullValidator;\n    }\n    registerOnValidatorChange(onChange) {\n        this.onChange = onChange;\n    }\n    ngOnChanges() {\n        this.onChange();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiValidator, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiValidator, isStandalone: true, selector: \"[tuiValidator]\", inputs: { validate: [\"tuiValidator\", \"validate\"] }, providers: [tuiProvide(NG_VALIDATORS, TuiValidator, true)], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiValidator, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiValidator]',\n                    inputs: ['validate: tuiValidator'],\n                    providers: [tuiProvide(NG_VALIDATORS, TuiValidator, true)],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiValidator };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,UAAU,EAAEC,aAAa,QAAQ,gBAAgB;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,UAAU,QAAQ,qBAAqB;AAEhD,MAAMC,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAGJ,cAAc;IAC9B,IAAI,CAACK,QAAQ,GAAGP,UAAU,CAACQ,aAAa;EAC5C;EACAC,yBAAyBA,CAACH,QAAQ,EAAE;IAChC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACJ,QAAQ,CAAC,CAAC;EACnB;EACA;IAAS,IAAI,CAACK,IAAI,YAAAC,qBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFT,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACU,IAAI,kBAD+EhB,EAAE,CAAAiB,iBAAA;MAAAC,IAAA,EACJZ,YAAY;MAAAa,SAAA;MAAAC,MAAA;QAAAX,QAAA,GADVT,EAAE,CAAAqB,YAAA,CAAAC,IAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFxB,EAAE,CAAAyB,kBAAA,CACyH,CAACpB,UAAU,CAACF,aAAa,EAAEG,YAAY,EAAE,IAAI,CAAC,CAAC,GAD1KN,EAAE,CAAA0B,oBAAA;IAAA,EAC8M;EAAE;AACvT;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG3B,EAAE,CAAA4B,iBAAA,CAGXtB,YAAY,EAAc,CAAC;IAC3GY,IAAI,EAAEjB,SAAS;IACf4B,IAAI,EAAE,CAAC;MACCN,UAAU,EAAE,IAAI;MAChBO,QAAQ,EAAE,gBAAgB;MAC1BV,MAAM,EAAE,CAAC,wBAAwB,CAAC;MAClCW,SAAS,EAAE,CAAC1B,UAAU,CAACF,aAAa,EAAEG,YAAY,EAAE,IAAI,CAAC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}