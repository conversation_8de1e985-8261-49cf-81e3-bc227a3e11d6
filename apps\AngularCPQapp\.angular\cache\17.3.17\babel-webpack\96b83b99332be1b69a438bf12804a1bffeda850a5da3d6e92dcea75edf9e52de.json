{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Directive, Input } from '@angular/core';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_NUMBER_FORMAT } from '@taiga-ui/core/tokens';\nimport { Observable, combineLatest, map, ReplaySubject } from 'rxjs';\nclass TuiNumberFormat extends Observable {\n  constructor() {\n    super(subscriber => combineLatest([this.parent, this.settings]).pipe(map(([parent, settings]) => ({\n      ...parent,\n      ...settings\n    }))).subscribe(subscriber));\n    this.settings = new ReplaySubject(1);\n    this.parent = inject(TUI_NUMBER_FORMAT, {\n      skipSelf: true\n    });\n  }\n  set tuiNumberFormat(format) {\n    this.settings.next(format);\n  }\n  static {\n    this.ɵfac = function TuiNumberFormat_Factory(t) {\n      return new (t || TuiNumberFormat)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiNumberFormat,\n      selectors: [[\"\", \"tuiNumberFormat\", \"\"]],\n      inputs: {\n        tuiNumberFormat: \"tuiNumberFormat\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TUI_NUMBER_FORMAT, TuiNumberFormat)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiNumberFormat, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiNumberFormat]',\n      providers: [tuiProvide(TUI_NUMBER_FORMAT, TuiNumberFormat)]\n    }]\n  }], function () {\n    return [];\n  }, {\n    tuiNumberFormat: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiNumberFormat };", "map": {"version": 3, "names": ["i0", "inject", "Directive", "Input", "tui<PERSON><PERSON><PERSON>", "TUI_NUMBER_FORMAT", "Observable", "combineLatest", "map", "ReplaySubject", "TuiNumberFormat", "constructor", "subscriber", "parent", "settings", "pipe", "subscribe", "skipSelf", "tuiNumberFormat", "format", "next", "ɵfac", "TuiNumberFormat_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-directives-number-format.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Directive, Input } from '@angular/core';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_NUMBER_FORMAT } from '@taiga-ui/core/tokens';\nimport { Observable, combineLatest, map, ReplaySubject } from 'rxjs';\n\nclass TuiNumberFormat extends Observable {\n    constructor() {\n        super((subscriber) => combineLatest([this.parent, this.settings])\n            .pipe(map(([parent, settings]) => ({ ...parent, ...settings })))\n            .subscribe(subscriber));\n        this.settings = new ReplaySubject(1);\n        this.parent = inject(TUI_NUMBER_FORMAT, { skipSelf: true });\n    }\n    set tuiNumberFormat(format) {\n        this.settings.next(format);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiNumberFormat, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiNumberFormat, isStandalone: true, selector: \"[tuiNumberFormat]\", inputs: { tuiNumberFormat: \"tuiNumberFormat\" }, providers: [tuiProvide(TUI_NUMBER_FORMAT, TuiNumberFormat)], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiNumberFormat, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiNumberFormat]',\n                    providers: [tuiProvide(TUI_NUMBER_FORMAT, TuiNumberFormat)],\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { tuiNumberFormat: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiNumberFormat };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACxD,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,UAAU,EAAEC,aAAa,EAAEC,GAAG,EAAEC,aAAa,QAAQ,MAAM;AAEpE,MAAMC,eAAe,SAASJ,UAAU,CAAC;EACrCK,WAAWA,CAAA,EAAG;IACV,KAAK,CAAEC,UAAU,IAAKL,aAAa,CAAC,CAAC,IAAI,CAACM,MAAM,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAC5DC,IAAI,CAACP,GAAG,CAAC,CAAC,CAACK,MAAM,EAAEC,QAAQ,CAAC,MAAM;MAAE,GAAGD,MAAM;MAAE,GAAGC;IAAS,CAAC,CAAC,CAAC,CAAC,CAC/DE,SAAS,CAACJ,UAAU,CAAC,CAAC;IAC3B,IAAI,CAACE,QAAQ,GAAG,IAAIL,aAAa,CAAC,CAAC,CAAC;IACpC,IAAI,CAACI,MAAM,GAAGZ,MAAM,CAACI,iBAAiB,EAAE;MAAEY,QAAQ,EAAE;IAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,eAAeA,CAACC,MAAM,EAAE;IACxB,IAAI,CAACL,QAAQ,CAACM,IAAI,CAACD,MAAM,CAAC;EAC9B;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFb,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACc,IAAI,kBAD+ExB,EAAE,CAAAyB,iBAAA;MAAAC,IAAA,EACJhB,eAAe;MAAAiB,SAAA;MAAAC,MAAA;QAAAV,eAAA;MAAA;MAAAW,UAAA;MAAAC,QAAA,GADb9B,EAAE,CAAA+B,kBAAA,CAC2H,CAAC3B,UAAU,CAACC,iBAAiB,EAAEK,eAAe,CAAC,CAAC,GAD7KV,EAAE,CAAAgC,0BAAA;IAAA,EACmN;EAAE;AAC5T;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGjC,EAAE,CAAAkC,iBAAA,CAGXxB,eAAe,EAAc,CAAC;IAC9GgB,IAAI,EAAExB,SAAS;IACfiC,IAAI,EAAE,CAAC;MACCN,UAAU,EAAE,IAAI;MAChBO,QAAQ,EAAE,mBAAmB;MAC7BC,SAAS,EAAE,CAACjC,UAAU,CAACC,iBAAiB,EAAEK,eAAe,CAAC;IAC9D,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEQ,eAAe,EAAE,CAAC;MAC5EQ,IAAI,EAAEvB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASO,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}