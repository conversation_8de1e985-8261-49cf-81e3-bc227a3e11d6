{"ast": null, "code": "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { Injectable, Directive, Optional, Self, SkipSelf, inject, Input, EventEmitter, Output, signal, INJECTOR, Component, ChangeDetectionStrategy, TemplateRef, NgZone, DestroyRef, ChangeDetectorRef } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { EMPTY_CLIENT_RECT } from '@taiga-ui/cdk/constants';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated, TuiAnimatedParent } from '@taiga-ui/cdk/directives/animated';\nimport { TuiHoveredService } from '@taiga-ui/cdk/directives/hovered';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement, tuiPointToClientRect } from '@taiga-ui/cdk/utils/dom';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { tuiCreateTokenFromFactory, tuiCreateToken, tuiProvide, tuiPure, tuiPx, tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiDriverDirective, TuiDriver, tuiAsDriver, TuiPositionAccessor, tuiFallbackAccessor, TuiRectAccessor, tuiAsRectAccessor, tuiAsVehicle, tuiPositionAccessorFor, tuiRectAccessorFor } from '@taiga-ui/core/classes';\nimport { TuiPositionService, TuiVisualViewportService } from '@taiga-ui/core/services';\nimport { TUI_VIEWPORT } from '@taiga-ui/core/tokens';\nimport { tuiOverrideOptions, tuiIsObscured } from '@taiga-ui/core/utils';\nimport { PolymorpheusComponent, injectContext, PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { BehaviorSubject, Subject, merge, switchMap, of, delay, takeUntil, repeat, filter, map, tap, takeWhile, distinctUntilChanged, fromEvent, debounce, timer, startWith, skip } from 'rxjs';\nimport { TuiActiveZone } from '@taiga-ui/cdk/directives/active-zone';\nimport { DOCUMENT, NgForOf } from '@angular/common';\nimport { tuiIfMap, tuiTypedFromEvent, tuiZonefreeScheduler, tuiZoneOptimized } from '@taiga-ui/cdk/observables';\nimport { tuiIsNativeFocused } from '@taiga-ui/cdk/utils/focus';\n\n/**\n * A component to display a hint\n */\nfunction TuiHintUnstyledComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c0 = [\"*\"];\nfunction TuiHintComponent_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 1);\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵproperty(\"innerHTML\", text_r1, i0.ɵɵsanitizeHtml);\n  }\n}\nconst _c1 = a0 => ({\n  $implicit: a0\n});\nfunction TuiHints_div_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiHints_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, TuiHints_div_0_ng_container_1_Template, 1, 0, \"ng-container\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hint_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"tuiActiveZoneParent\", hint_r1.activeZone || null);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", hint_r1.component)(\"polymorpheusOutletContext\", i0.ɵɵpureFunction1(3, _c1, hint_r1));\n  }\n}\nconst TUI_HINT_COMPONENT = tuiCreateTokenFromFactory(() => TuiHintComponent);\n\n/**\n * Service for displaying hints/tooltips\n */\nclass TuiHintService extends BehaviorSubject {\n  constructor() {\n    super([]);\n  }\n  add(directive) {\n    this.next(this.value.concat(directive));\n  }\n  remove(directive) {\n    if (this.value.includes(directive)) {\n      this.next(this.value.filter(hint => hint !== directive));\n    }\n  }\n  static {\n    this.ɵfac = function TuiHintService_Factory(t) {\n      return new (t || TuiHintService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiHintService,\n      factory: TuiHintService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nclass TuiHintDriver extends TuiDriverDirective {\n  constructor() {\n    super(...arguments);\n    this.type = 'hint';\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiHintDriver_BaseFactory;\n      return function TuiHintDriver_Factory(t) {\n        return (ɵTuiHintDriver_BaseFactory || (ɵTuiHintDriver_BaseFactory = i0.ɵɵgetInheritedFactory(TuiHintDriver)))(t || TuiHintDriver);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiHintDriver,\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintDriver, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], null, null);\n})();\nconst TUI_HINT_DIRECTIONS = ['bottom-left', 'bottom', 'bottom-right', 'top-left', 'top', 'top-right', 'left-top', 'left', 'left-bottom', 'right-top', 'right', 'right-bottom'];\n/** Default values for hint options */\nconst TUI_HINT_DEFAULT_OPTIONS = {\n  direction: 'bottom-left',\n  showDelay: 500,\n  hideDelay: 200,\n  appearance: '',\n  /** TODO @deprecated use {@link TUI_TOOLTIP_OPTIONS} instead **/\n  icon: '@tui.circle-help'\n};\n/**\n * Default parameters for hint directive\n */\nconst TUI_HINT_OPTIONS = tuiCreateToken(TUI_HINT_DEFAULT_OPTIONS);\nconst tuiHintOptionsProvider = override => ({\n  provide: TUI_HINT_OPTIONS,\n  deps: [[new Optional(), new Self(), TuiHintOptionsDirective], [new Optional(), new SkipSelf(), TUI_HINT_OPTIONS]],\n  useFactory: tuiOverrideOptions(override, TUI_HINT_DEFAULT_OPTIONS)\n});\n/**\n * @deprecated: drop in 5.0\n */\nclass TuiHintOptionsDirective {\n  constructor() {\n    this.options = inject(TUI_HINT_OPTIONS, {\n      skipSelf: true\n    });\n    this.direction = this.options.direction;\n    this.appearance = this.options.appearance;\n    this.showDelay = this.options.showDelay;\n    this.hideDelay = this.options.hideDelay;\n    this.icon = this.options.icon;\n    this.change$ = new Subject();\n  }\n  ngOnChanges() {\n    this.change$.next();\n  }\n  static {\n    this.ɵfac = function TuiHintOptionsDirective_Factory(t) {\n      return new (t || TuiHintOptionsDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiHintOptionsDirective,\n      selectors: [[\"\", \"tuiHintContent\", \"\"]],\n      inputs: {\n        content: [i0.ɵɵInputFlags.None, \"tuiHintContent\", \"content\"],\n        direction: [i0.ɵɵInputFlags.None, \"tuiHintDirection\", \"direction\"],\n        appearance: [i0.ɵɵInputFlags.None, \"tuiHintAppearance\", \"appearance\"],\n        showDelay: [i0.ɵɵInputFlags.None, \"tuiHintShowDelay\", \"showDelay\"],\n        hideDelay: [i0.ɵɵInputFlags.None, \"tuiHintHideDelay\", \"hideDelay\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TUI_HINT_OPTIONS, TuiHintOptionsDirective)]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintOptionsDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiHintContent]',\n      providers: [tuiProvide(TUI_HINT_OPTIONS, TuiHintOptionsDirective)]\n    }]\n  }], null, {\n    content: [{\n      type: Input,\n      args: ['tuiHintContent']\n    }],\n    direction: [{\n      type: Input,\n      args: ['tuiHintDirection']\n    }],\n    appearance: [{\n      type: Input,\n      args: ['tuiHintAppearance']\n    }],\n    showDelay: [{\n      type: Input,\n      args: ['tuiHintShowDelay']\n    }],\n    hideDelay: [{\n      type: Input,\n      args: ['tuiHintHideDelay']\n    }]\n  });\n})();\nclass TuiHintHover extends TuiDriver {\n  constructor() {\n    super(subscriber => this.stream$.subscribe(subscriber));\n    this.isMobile = inject(TUI_IS_MOBILE);\n    this.el = tuiInjectElement();\n    this.hovered$ = inject(TuiHoveredService);\n    this.options = inject(TUI_HINT_OPTIONS);\n    this.visible = false;\n    this.toggle$ = new Subject();\n    this.stream$ = merge(this.toggle$.pipe(switchMap(visible => this.isMobile ? of(visible) : of(visible).pipe(delay(visible ? 0 : this.tuiHintHideDelay))), takeUntil(this.hovered$), repeat()), this.hovered$.pipe(switchMap(visible => this.isMobile ? of(visible) : of(visible).pipe(delay(visible ? this.tuiHintShowDelay : this.tuiHintHideDelay))), takeUntil(this.toggle$), repeat())).pipe(filter(() => this.enabled), map(value => value && (this.el.hasAttribute('tuiHintPointer') || !tuiIsObscured(this.el))), tap(visible => {\n      this.visible = visible;\n    }));\n    this.parent = inject(TuiHintHover, {\n      optional: true,\n      skipSelf: true\n    });\n    this.tuiHintShowDelay = this.options.showDelay;\n    this.tuiHintHideDelay = this.options.hideDelay;\n    this.type = 'hint';\n    this.enabled = true;\n  }\n  toggle(visible = !this.visible) {\n    this.toggle$.next(visible);\n    this.parent?.toggle(visible);\n  }\n  close() {\n    this.toggle$.next(false);\n  }\n  static {\n    this.ɵfac = function TuiHintHover_Factory(t) {\n      return new (t || TuiHintHover)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiHintHover,\n      inputs: {\n        tuiHintShowDelay: \"tuiHintShowDelay\",\n        tuiHintHideDelay: \"tuiHintHideDelay\"\n      },\n      exportAs: [\"tuiHintHover\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsDriver(TuiHintHover), TuiHoveredService]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintHover, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      providers: [tuiAsDriver(TuiHintHover), TuiHoveredService],\n      exportAs: 'tuiHintHover'\n    }]\n  }], function () {\n    return [];\n  }, {\n    tuiHintShowDelay: [{\n      type: Input\n    }],\n    tuiHintHideDelay: [{\n      type: Input\n    }]\n  });\n})();\nconst GAP$1 = 8;\nconst ARROW_OFFSET = 24;\nconst TOP = 0;\nconst LEFT = 1;\nclass TuiHintPosition extends TuiPositionAccessor {\n  constructor() {\n    super(...arguments);\n    this.offset = inject(TUI_IS_MOBILE) ? 16 : 8;\n    this.viewport = inject(TUI_VIEWPORT);\n    this.accessor = tuiFallbackAccessor('hint')(inject(TuiRectAccessor), inject(TuiHintDirective));\n    this.points = TUI_HINT_DIRECTIONS.reduce((acc, direction) => ({\n      ...acc,\n      [direction]: [0, 0]\n    }), {});\n    this.direction = inject(TUI_HINT_OPTIONS).direction;\n    this.directionChange = new EventEmitter();\n    this.type = 'hint';\n  }\n  emitDirection(direction) {\n    this.directionChange.emit(direction);\n  }\n  getPosition(rect, el) {\n    const width = el?.clientWidth ?? rect.width;\n    const height = el?.clientHeight ?? rect.height;\n    const hostRect = this.accessor.getClientRect() ?? EMPTY_CLIENT_RECT;\n    const leftCenter = hostRect.left + hostRect.width / 2;\n    const topCenter = hostRect.top + hostRect.height / 2;\n    this.points['top-left'][TOP] = hostRect.top - height - this.offset;\n    this.points['top-left'][LEFT] = leftCenter - width + ARROW_OFFSET;\n    this.points.top[TOP] = this.points['top-left'][TOP];\n    this.points.top[LEFT] = leftCenter - width / 2;\n    this.points['top-right'][TOP] = this.points['top-left'][TOP];\n    this.points['top-right'][LEFT] = leftCenter - ARROW_OFFSET;\n    this.points['bottom-left'][TOP] = hostRect.bottom + this.offset;\n    this.points['bottom-left'][LEFT] = this.points['top-left'][LEFT];\n    this.points.bottom[TOP] = this.points['bottom-left'][TOP];\n    this.points.bottom[LEFT] = this.points.top[LEFT];\n    this.points['bottom-right'][TOP] = this.points['bottom-left'][TOP];\n    this.points['bottom-right'][LEFT] = this.points['top-right'][LEFT];\n    this.points['left-top'][TOP] = topCenter - height + ARROW_OFFSET;\n    this.points['left-top'][LEFT] = hostRect.left - width - this.offset;\n    this.points.left[TOP] = topCenter - height / 2;\n    this.points.left[LEFT] = this.points['left-top'][LEFT];\n    this.points['left-bottom'][TOP] = topCenter - ARROW_OFFSET;\n    this.points['left-bottom'][LEFT] = this.points['left-top'][LEFT];\n    this.points['right-top'][TOP] = this.points['left-top'][TOP];\n    this.points['right-top'][LEFT] = hostRect.right + this.offset;\n    this.points.right[TOP] = this.points.left[TOP];\n    this.points.right[LEFT] = this.points['right-top'][LEFT];\n    this.points['right-bottom'][TOP] = this.points['left-bottom'][TOP];\n    this.points['right-bottom'][LEFT] = this.points['right-top'][LEFT];\n    const priorityDirections = Array.isArray(this.direction) ? this.direction : [this.direction];\n    const sortedDirections = priorityDirections.concat(TUI_HINT_DIRECTIONS);\n    const direction = sortedDirections.find(direction => this.checkPosition(this.points[direction], width, height)) || this.fallback;\n    this.emitDirection(direction);\n    return this.points[direction];\n  }\n  get fallback() {\n    return this.points.top[TOP] > this.viewport.getClientRect().bottom - this.points.bottom[TOP] ? 'top' : 'bottom';\n  }\n  checkPosition([top, left], width, height) {\n    const viewport = this.viewport.getClientRect();\n    return top > viewport.top + GAP$1 && left > viewport.left + GAP$1 && top + height < viewport.bottom - GAP$1 && left + width < viewport.right - GAP$1;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiHintPosition_BaseFactory;\n      return function TuiHintPosition_Factory(t) {\n        return (ɵTuiHintPosition_BaseFactory || (ɵTuiHintPosition_BaseFactory = i0.ɵɵgetInheritedFactory(TuiHintPosition)))(t || TuiHintPosition);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiHintPosition,\n      inputs: {\n        direction: [i0.ɵɵInputFlags.None, \"tuiHintDirection\", \"direction\"]\n      },\n      outputs: {\n        directionChange: \"tuiHintDirectionChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n__decorate([tuiPure], TuiHintPosition.prototype, \"emitDirection\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintPosition, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], null, {\n    direction: [{\n      type: Input,\n      args: ['tuiHintDirection']\n    }],\n    directionChange: [{\n      type: Output,\n      args: ['tuiHintDirectionChange']\n    }],\n    emitDirection: []\n  });\n})();\nclass TuiHintDirective {\n  constructor() {\n    this.service = inject(TuiHintService);\n    this.appearance = inject(TUI_HINT_OPTIONS).appearance;\n    this.content = signal(null);\n    this.component = inject(PolymorpheusComponent);\n    this.el = tuiInjectElement();\n    this.activeZone = inject(TuiActiveZone, {\n      optional: true\n    });\n    this.type = 'hint';\n  }\n  set tuiHint(content) {\n    this.content.set(content);\n    if (!content) {\n      this.toggle(false);\n    }\n  }\n  ngOnDestroy() {\n    this.toggle(false);\n  }\n  getClientRect() {\n    return this.el.getBoundingClientRect();\n  }\n  toggle(show) {\n    if (show && this.content()) {\n      this.service.add(this);\n    } else {\n      this.service.remove(this);\n    }\n  }\n  static {\n    this.ɵfac = function TuiHintDirective_Factory(t) {\n      return new (t || TuiHintDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiHintDirective,\n      selectors: [[\"\", \"tuiHint\", \"\", 5, \"ng-container\", 5, \"ng-template\"]],\n      inputs: {\n        context: [i0.ɵɵInputFlags.None, \"tuiHintContext\", \"context\"],\n        appearance: [i0.ɵɵInputFlags.None, \"tuiHintAppearance\", \"appearance\"],\n        tuiHint: \"tuiHint\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsRectAccessor(TuiHintDirective), tuiAsVehicle(TuiHintDirective), {\n        provide: PolymorpheusComponent,\n        deps: [TUI_HINT_COMPONENT, INJECTOR],\n        useClass: PolymorpheusComponent\n      }]), i0.ɵɵHostDirectivesFeature([TuiHintDriver, {\n        directive: TuiHintHover,\n        inputs: [\"tuiHintHideDelay\", \"tuiHintHideDelay\", \"tuiHintShowDelay\", \"tuiHintShowDelay\"]\n      }, {\n        directive: TuiHintPosition,\n        inputs: [\"tuiHintDirection\", \"tuiHintDirection\"],\n        outputs: [\"tuiHintDirectionChange\", \"tuiHintDirectionChange\"]\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiHint]:not(ng-container):not(ng-template)',\n      providers: [tuiAsRectAccessor(TuiHintDirective), tuiAsVehicle(TuiHintDirective), {\n        provide: PolymorpheusComponent,\n        deps: [TUI_HINT_COMPONENT, INJECTOR],\n        useClass: PolymorpheusComponent\n      }],\n      hostDirectives: [TuiHintDriver, {\n        directive: TuiHintHover,\n        inputs: ['tuiHintHideDelay', 'tuiHintShowDelay']\n      }, {\n        directive: TuiHintPosition,\n        inputs: ['tuiHintDirection'],\n        outputs: ['tuiHintDirectionChange']\n      }]\n    }]\n  }], null, {\n    context: [{\n      type: Input,\n      args: ['tuiHintContext']\n    }],\n    appearance: [{\n      type: Input,\n      args: ['tuiHintAppearance']\n    }],\n    tuiHint: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiHintPointer extends TuiHintHover {\n  constructor() {\n    super(...arguments);\n    this.currentRect = EMPTY_CLIENT_RECT;\n  }\n  getClientRect() {\n    return this.currentRect;\n  }\n  onMove({\n    clientX,\n    clientY\n  }) {\n    this.currentRect = tuiPointToClientRect(clientX, clientY);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiHintPointer_BaseFactory;\n      return function TuiHintPointer_Factory(t) {\n        return (ɵTuiHintPointer_BaseFactory || (ɵTuiHintPointer_BaseFactory = i0.ɵɵgetInheritedFactory(TuiHintPointer)))(t || TuiHintPointer);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiHintPointer,\n      selectors: [[\"\", \"tuiHint\", \"\", \"tuiHintPointer\", \"\"]],\n      hostBindings: function TuiHintPointer_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mousemove.zoneless\", function TuiHintPointer_mousemove_zoneless_HostBindingHandler($event) {\n            return ctx.onMove($event);\n          });\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsRectAccessor(TuiHintPointer), tuiAsDriver(TuiHintPointer)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintPointer, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiHint][tuiHintPointer]',\n      providers: [tuiAsRectAccessor(TuiHintPointer), tuiAsDriver(TuiHintPointer)],\n      host: {\n        '(mousemove.zoneless)': 'onMove($event)'\n      }\n    }]\n  }], null, null);\n})();\nclass TuiHintUnstyledComponent {\n  constructor() {\n    this.context = injectContext();\n  }\n  static {\n    this.ɵfac = function TuiHintUnstyledComponent_Factory(t) {\n      return new (t || TuiHintUnstyledComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiHintUnstyledComponent,\n      selectors: [[\"ng-component\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"polymorpheusOutlet\"]],\n      template: function TuiHintUnstyledComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiHintUnstyledComponent_ng_container_0_Template, 1, 0, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.context.$implicit.content());\n        }\n      },\n      dependencies: [PolymorpheusOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintUnstyledComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      imports: [PolymorpheusOutlet],\n      template: '<ng-container *polymorpheusOutlet=\"context.$implicit.content()\" />',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\nclass TuiHintUnstyled {\n  constructor() {\n    const hint = inject(TuiHintDirective);\n    hint.component = new PolymorpheusComponent(TuiHintUnstyledComponent);\n    hint.content.set(inject(TemplateRef));\n  }\n  static {\n    this.ɵfac = function TuiHintUnstyled_Factory(t) {\n      return new (t || TuiHintUnstyled)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiHintUnstyled,\n      selectors: [[\"ng-template\", \"tuiHint\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintUnstyled, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-template[tuiHint]'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nconst TUI_HINT_PROVIDERS = [TuiPositionService, TuiHoveredService, tuiPositionAccessorFor('hint', TuiHintPosition), tuiRectAccessorFor('hint', TuiHintDirective)];\nconst GAP = 8;\n// TODO(v5): remove base component after angular update\nclass TuiHintBaseComponent {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.hover = inject(TuiHintHover);\n    this.vvs = inject(TuiVisualViewportService);\n    this.viewport = inject(TUI_VIEWPORT);\n    this.pointer = inject(TuiHintPointer, {\n      optional: true\n    });\n    this.accessor = inject(TuiRectAccessor);\n    this.hint = injectContext().$implicit;\n    this.isMobile = inject(TUI_IS_MOBILE);\n    this.content = this.hint.component.component === TuiHintUnstyledComponent ? signal('') : this.hint.content;\n    this.appearance = this.hint.appearance || this.hint.el.closest('[tuiTheme]')?.getAttribute('tuiTheme');\n    inject(TuiPositionService).pipe(takeWhile(() => this.hint.el.isConnected), map(point => this.vvs.correct(point)), takeUntilDestroyed()).subscribe({\n      next: ([top, left]) => this.update(top, left),\n      complete: () => this.hover.close()\n    });\n    inject(TuiHoveredService).pipe(takeUntilDestroyed()).subscribe(hover => this.hover.toggle(hover));\n  }\n  onClick(target) {\n    if (!target.closest(this.el.tagName) && !this.hint.el.contains(target) || tuiIsObscured(this.hint.el)) {\n      this.hover.toggle(false);\n    }\n  }\n  apply(top, left, beakTop, beakLeft) {\n    this.el.style.top = top;\n    this.el.style.left = left;\n    this.el.style.setProperty('--t-top', `${beakTop}%`);\n    this.el.style.setProperty('--t-left', `${beakLeft}%`);\n    this.el.style.setProperty('--t-rotate', !beakLeft || Math.ceil(beakLeft) === 100 ? '90deg' : '0deg');\n  }\n  update(top, left) {\n    const {\n      clientHeight,\n      clientWidth\n    } = this.el;\n    const rect = this.accessor.getClientRect();\n    if (rect === EMPTY_CLIENT_RECT || !clientHeight || !clientWidth) {\n      return;\n    }\n    const viewport = this.viewport.getClientRect();\n    const safeLeft = tuiClamp(Math.max(GAP, left), viewport.left + GAP, Math.max(GAP, viewport.width + viewport.left - clientWidth - GAP));\n    const [beakTop, beakLeft] = this.vvs.correct([rect.top + rect.height / 2 - top, rect.left + rect.width / 2 - safeLeft]);\n    this.apply(tuiPx(Math.round(top)), tuiPx(Math.round(safeLeft)), Math.round(tuiClamp(beakTop, 0, clientHeight) / clientHeight * 100), Math.round(tuiClamp(beakLeft, 0, clientWidth) / clientWidth * 100));\n  }\n  static {\n    this.ɵfac = function TuiHintBaseComponent_Factory(t) {\n      return new (t || TuiHintBaseComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiHintBaseComponent,\n      selectors: [[\"ng-component\"]],\n      hostVars: 6,\n      hostBindings: function TuiHintBaseComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TuiHintBaseComponent_click_HostBindingHandler($event) {\n            return ctx.onClick($event.target);\n          }, false, i0.ɵɵresolveDocument);\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-appearance\", ctx.appearance)(\"tuiTheme\", ctx.appearance === \"dark\" ? \"light\" : null);\n          i0.ɵɵclassProp(\"_untouchable\", ctx.pointer)(\"_mobile\", ctx.isMobile);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiHintBaseComponent_Template(rf, ctx) {},\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([tuiPure], TuiHintBaseComponent.prototype, \"apply\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintBaseComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class._untouchable]': 'pointer',\n        '[class._mobile]': 'isMobile',\n        '[attr.data-appearance]': 'appearance',\n        '[attr.tuiTheme]': 'appearance === \"dark\" ? \"light\" : null',\n        '(document:click)': 'onClick($event.target)'\n      }\n    }]\n  }], function () {\n    return [];\n  }, {\n    apply: []\n  });\n})();\nclass TuiHintComponent extends TuiHintBaseComponent {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiHintComponent_BaseFactory;\n      return function TuiHintComponent_Factory(t) {\n        return (ɵTuiHintComponent_BaseFactory || (ɵTuiHintComponent_BaseFactory = i0.ɵɵgetInheritedFactory(TuiHintComponent)))(t || TuiHintComponent);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiHintComponent,\n      selectors: [[\"tui-hint\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature(TUI_HINT_PROVIDERS), i0.ɵɵHostDirectivesFeature([i1.TuiAnimated]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 2,\n      consts: [[3, \"innerHTML\", 4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [3, \"innerHTML\"]],\n      template: function TuiHintComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵtemplate(1, TuiHintComponent_span_1_Template, 1, 1, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.content())(\"polymorpheusOutletContext\", ctx.hint.context);\n        }\n      },\n      dependencies: [PolymorpheusOutlet],\n      styles: [\"[_nghost-%COMP%]{position:absolute;max-inline-size:min(18rem,calc(100% - 1rem));padding:.75rem 1rem;background:var(--tui-background-accent-1);border-radius:var(--tui-radius-l);color:var(--tui-text-primary-on-accent-1);box-sizing:border-box;font:var(--tui-font-text-s);white-space:pre-line;overflow-wrap:break-word;transform-origin:var(--t-left) var(--t-top);--tui-background-elevation-2: var(--tui-background-elevation-3)}.tui-enter[_nghost-%COMP%], .tui-leave[_nghost-%COMP%]{animation-name:tuiFade}[_nghost-%COMP%]   tui-root._mobile.tui-enter[_nghost-%COMP%], tui-root._mobile   .tui-enter[_nghost-%COMP%]{animation:tuiFade var(--tui-duration) ease-in-out,tuiScale var(--tui-duration) cubic-bezier(.34,1.56,.64,1)}[_nghost-%COMP%]   tui-root._mobile.tui-leave[_nghost-%COMP%], tui-root._mobile   .tui-leave[_nghost-%COMP%]{animation:tuiFade var(--tui-duration) ease-in-out reverse,tuiScale var(--tui-duration) ease-in-out reverse}[_nghost-%COMP%]:before{content:\\\"\\\";position:absolute;top:var(--t-top);left:var(--t-left);inline-size:.75rem;block-size:.5rem;background:inherit;-webkit-mask-image:url('data:image/svg+xml,<svg viewBox=\\\"0 0 12 8\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M3.61336 1.69607L2.44882 2.96493C1.84795 3.61964 0.949361 3.99951 0.00053941 4C0.000359608 4 0.000179805 4 0 4C0.000179863 4 0.000359764 4 0.000539623 4C0.949362 4.00049 1.84795 4.38036 2.44882 5.03506L3.61336 6.30394C4.55981 7.33517 5.03303 7.85079 5.63254 7.96535C5.87433 8.01155 6.12436 8.01155 6.36616 7.96535C6.96567 7.85079 7.43889 7.33517 8.38534 6.30393L9.54988 5.03507C10.1511 4.37994 11.0505 4 12 4C11.0505 4 10.1511 3.62006 9.54988 2.96493L8.38534 1.69606C7.43889 0.664826 6.96567 0.149207 6.36616 0.0346517C6.12436 -0.0115506 5.87433 -0.0115506 5.63254 0.0346517C5.03303 0.149207 4.55981 0.664827 3.61336 1.69607Z\\\" /></svg>');mask-image:url('data:image/svg+xml,<svg viewBox=\\\"0 0 12 8\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M3.61336 1.69607L2.44882 2.96493C1.84795 3.61964 0.949361 3.99951 0.00053941 4C0.000359608 4 0.000179805 4 0 4C0.000179863 4 0.000359764 4 0.000539623 4C0.949362 4.00049 1.84795 4.38036 2.44882 5.03506L3.61336 6.30394C4.55981 7.33517 5.03303 7.85079 5.63254 7.96535C5.87433 8.01155 6.12436 8.01155 6.36616 7.96535C6.96567 7.85079 7.43889 7.33517 8.38534 6.30393L9.54988 5.03507C10.1511 4.37994 11.0505 4 12 4C11.0505 4 10.1511 3.62006 9.54988 2.96493L8.38534 1.69606C7.43889 0.664826 6.96567 0.149207 6.36616 0.0346517C6.12436 -0.0115506 5.87433 -0.0115506 5.63254 0.0346517C5.03303 0.149207 4.55981 0.664827 3.61336 1.69607Z\\\" /></svg>');transform:translate(-50%,-50%) rotate(var(--t-rotate))}._mobile[_nghost-%COMP%]{font:var(--tui-font-text-m)}._mobile[_nghost-%COMP%]:before{inline-size:1.5rem;block-size:1.125rem;-webkit-mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 18\\\"><path d=\\\"M7.22854 3.81615L4.89971 6.6711C3.69732 8.14514 1.8988 9 0 9C1.8988 9 3.69732 9.85486 4.89971 11.3289L7.22854 14.1839L7.22854 14.1839C9.12123 16.5041 10.0676 17.6643 11.2665 17.922C11.75 18.026 12.25 18.026 12.7335 17.922C13.9324 17.6643 14.8788 16.5041 16.7715 14.1839L19.1003 11.3289C20.3027 9.85486 22.1012 9 24 9C22.1012 9 20.3027 8.14514 19.1003 6.6711L16.7715 3.81614C14.8788 1.49586 13.9324 0.335716 12.7335 0.0779663C12.25 -0.0259888 11.75 -0.0259888 11.2665 0.0779663C10.0676 0.335716 9.12123 1.49586 7.22854 3.81614L7.22854 3.81615Z\\\" /></svg>');mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 18\\\"><path d=\\\"M7.22854 3.81615L4.89971 6.6711C3.69732 8.14514 1.8988 9 0 9C1.8988 9 3.69732 9.85486 4.89971 11.3289L7.22854 14.1839L7.22854 14.1839C9.12123 16.5041 10.0676 17.6643 11.2665 17.922C11.75 18.026 12.25 18.026 12.7335 17.922C13.9324 17.6643 14.8788 16.5041 16.7715 14.1839L19.1003 11.3289C20.3027 9.85486 22.1012 9 24 9C22.1012 9 20.3027 8.14514 19.1003 6.6711L16.7715 3.81614C14.8788 1.49586 13.9324 0.335716 12.7335 0.0779663C12.25 -0.0259888 11.75 -0.0259888 11.2665 0.0779663C10.0676 0.335716 9.12123 1.49586 7.22854 3.81614L7.22854 3.81615Z\\\" /></svg>')}[data-appearance=error][_nghost-%COMP%]{background:var(--tui-status-negative)}[data-appearance=dark][_nghost-%COMP%]{background:var(--tui-background-elevation-1);color:var(--tui-text-primary);filter:drop-shadow(0 0 .125rem rgba(0,0,0,.16)) drop-shadow(0 1.5rem 1rem rgba(0,0,0,.03)) drop-shadow(0 .75rem .75rem rgba(0,0,0,.04)) drop-shadow(0 .25rem .375rem rgba(0,0,0,.05))}[_nghost-%COMP%]:not([style*=top]){visibility:hidden}._untouchable[_nghost-%COMP%]{pointer-events:none}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-hint',\n      imports: [PolymorpheusOutlet],\n      template: `\n        <ng-content />\n        <span\n            *polymorpheusOutlet=\"content() as text; context: hint.context\"\n            [innerHTML]=\"text\"\n        ></span>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: TUI_HINT_PROVIDERS,\n      hostDirectives: [TuiAnimated],\n      styles: [\":host{position:absolute;max-inline-size:min(18rem,calc(100% - 1rem));padding:.75rem 1rem;background:var(--tui-background-accent-1);border-radius:var(--tui-radius-l);color:var(--tui-text-primary-on-accent-1);box-sizing:border-box;font:var(--tui-font-text-s);white-space:pre-line;overflow-wrap:break-word;transform-origin:var(--t-left) var(--t-top);--tui-background-elevation-2: var(--tui-background-elevation-3)}:host.tui-enter,:host.tui-leave{animation-name:tuiFade}:host :host-context(tui-root._mobile).tui-enter{animation:tuiFade var(--tui-duration) ease-in-out,tuiScale var(--tui-duration) cubic-bezier(.34,1.56,.64,1)}:host :host-context(tui-root._mobile).tui-leave{animation:tuiFade var(--tui-duration) ease-in-out reverse,tuiScale var(--tui-duration) ease-in-out reverse}:host:before{content:\\\"\\\";position:absolute;top:var(--t-top);left:var(--t-left);inline-size:.75rem;block-size:.5rem;background:inherit;-webkit-mask-image:url('data:image/svg+xml,<svg viewBox=\\\"0 0 12 8\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M3.61336 1.69607L2.44882 2.96493C1.84795 3.61964 0.949361 3.99951 0.00053941 4C0.000359608 4 0.000179805 4 0 4C0.000179863 4 0.000359764 4 0.000539623 4C0.949362 4.00049 1.84795 4.38036 2.44882 5.03506L3.61336 6.30394C4.55981 7.33517 5.03303 7.85079 5.63254 7.96535C5.87433 8.01155 6.12436 8.01155 6.36616 7.96535C6.96567 7.85079 7.43889 7.33517 8.38534 6.30393L9.54988 5.03507C10.1511 4.37994 11.0505 4 12 4C11.0505 4 10.1511 3.62006 9.54988 2.96493L8.38534 1.69606C7.43889 0.664826 6.96567 0.149207 6.36616 0.0346517C6.12436 -0.0115506 5.87433 -0.0115506 5.63254 0.0346517C5.03303 0.149207 4.55981 0.664827 3.61336 1.69607Z\\\" /></svg>');mask-image:url('data:image/svg+xml,<svg viewBox=\\\"0 0 12 8\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M3.61336 1.69607L2.44882 2.96493C1.84795 3.61964 0.949361 3.99951 0.00053941 4C0.000359608 4 0.000179805 4 0 4C0.000179863 4 0.000359764 4 0.000539623 4C0.949362 4.00049 1.84795 4.38036 2.44882 5.03506L3.61336 6.30394C4.55981 7.33517 5.03303 7.85079 5.63254 7.96535C5.87433 8.01155 6.12436 8.01155 6.36616 7.96535C6.96567 7.85079 7.43889 7.33517 8.38534 6.30393L9.54988 5.03507C10.1511 4.37994 11.0505 4 12 4C11.0505 4 10.1511 3.62006 9.54988 2.96493L8.38534 1.69606C7.43889 0.664826 6.96567 0.149207 6.36616 0.0346517C6.12436 -0.0115506 5.87433 -0.0115506 5.63254 0.0346517C5.03303 0.149207 4.55981 0.664827 3.61336 1.69607Z\\\" /></svg>');transform:translate(-50%,-50%) rotate(var(--t-rotate))}:host._mobile{font:var(--tui-font-text-m)}:host._mobile:before{inline-size:1.5rem;block-size:1.125rem;-webkit-mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 18\\\"><path d=\\\"M7.22854 3.81615L4.89971 6.6711C3.69732 8.14514 1.8988 9 0 9C1.8988 9 3.69732 9.85486 4.89971 11.3289L7.22854 14.1839L7.22854 14.1839C9.12123 16.5041 10.0676 17.6643 11.2665 17.922C11.75 18.026 12.25 18.026 12.7335 17.922C13.9324 17.6643 14.8788 16.5041 16.7715 14.1839L19.1003 11.3289C20.3027 9.85486 22.1012 9 24 9C22.1012 9 20.3027 8.14514 19.1003 6.6711L16.7715 3.81614C14.8788 1.49586 13.9324 0.335716 12.7335 0.0779663C12.25 -0.0259888 11.75 -0.0259888 11.2665 0.0779663C10.0676 0.335716 9.12123 1.49586 7.22854 3.81614L7.22854 3.81615Z\\\" /></svg>');mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 18\\\"><path d=\\\"M7.22854 3.81615L4.89971 6.6711C3.69732 8.14514 1.8988 9 0 9C1.8988 9 3.69732 9.85486 4.89971 11.3289L7.22854 14.1839L7.22854 14.1839C9.12123 16.5041 10.0676 17.6643 11.2665 17.922C11.75 18.026 12.25 18.026 12.7335 17.922C13.9324 17.6643 14.8788 16.5041 16.7715 14.1839L19.1003 11.3289C20.3027 9.85486 22.1012 9 24 9C22.1012 9 20.3027 8.14514 19.1003 6.6711L16.7715 3.81614C14.8788 1.49586 13.9324 0.335716 12.7335 0.0779663C12.25 -0.0259888 11.75 -0.0259888 11.2665 0.0779663C10.0676 0.335716 9.12123 1.49586 7.22854 3.81614L7.22854 3.81615Z\\\" /></svg>')}:host[data-appearance=error]{background:var(--tui-status-negative)}:host[data-appearance=dark]{background:var(--tui-background-elevation-1);color:var(--tui-text-primary);filter:drop-shadow(0 0 .125rem rgba(0,0,0,.16)) drop-shadow(0 1.5rem 1rem rgba(0,0,0,.03)) drop-shadow(0 .75rem .75rem rgba(0,0,0,.04)) drop-shadow(0 .25rem .375rem rgba(0,0,0,.05))}:host:not([style*=top]){visibility:hidden}:host._untouchable{pointer-events:none}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiHintDescribe extends TuiDriver {\n  constructor() {\n    super(subscriber => this.stream$.subscribe(subscriber));\n    this.doc = inject(DOCUMENT);\n    this.el = tuiInjectElement();\n    this.zone = inject(NgZone);\n    this.id$ = new BehaviorSubject('');\n    this.stream$ = this.id$.pipe(distinctUntilChanged(), tuiIfMap(() => fromEvent(this.doc, 'keydown', {\n      capture: true\n    }), tuiIsPresent), switchMap(() => this.focused ? of(false) : merge(tuiTypedFromEvent(this.doc, 'keyup'), tuiTypedFromEvent(this.element, 'blur')).pipe(map(() => this.focused))), debounce(visible => visible ? timer(1000, tuiZonefreeScheduler(this.zone)) : of(null)), startWith(false), distinctUntilChanged(), skip(1), tuiZoneOptimized());\n    this.type = 'hint';\n  }\n  set tuiHintDescribe(id) {\n    this.id$.next(id || '');\n  }\n  get element() {\n    return this.doc.getElementById(this.id$.value || '') || this.el;\n  }\n  get focused() {\n    return tuiIsNativeFocused(this.element);\n  }\n  static {\n    this.ɵfac = function TuiHintDescribe_Factory(t) {\n      return new (t || TuiHintDescribe)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiHintDescribe,\n      selectors: [[\"\", \"tuiHintDescribe\", \"\"]],\n      inputs: {\n        tuiHintDescribe: \"tuiHintDescribe\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsDriver(TuiHintDescribe)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n__decorate([tuiPure], TuiHintDescribe.prototype, \"element\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintDescribe, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiHintDescribe]',\n      providers: [tuiAsDriver(TuiHintDescribe)]\n    }]\n  }], function () {\n    return [];\n  }, {\n    tuiHintDescribe: [{\n      type: Input\n    }],\n    element: []\n  });\n})();\nclass TuiHintHost extends TuiRectAccessor {\n  constructor() {\n    super(...arguments);\n    this.type = 'hint';\n  }\n  getClientRect() {\n    return this.tuiHintHost?.getBoundingClientRect() || EMPTY_CLIENT_RECT;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiHintHost_BaseFactory;\n      return function TuiHintHost_Factory(t) {\n        return (ɵTuiHintHost_BaseFactory || (ɵTuiHintHost_BaseFactory = i0.ɵɵgetInheritedFactory(TuiHintHost)))(t || TuiHintHost);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiHintHost,\n      selectors: [[\"\", \"tuiHint\", \"\", \"tuiHintHost\", \"\"]],\n      inputs: {\n        tuiHintHost: \"tuiHintHost\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsRectAccessor(TuiHintHost)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintHost, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiHint][tuiHintHost]',\n      providers: [tuiAsRectAccessor(TuiHintHost)]\n    }]\n  }], null, {\n    tuiHintHost: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiHintManual extends TuiDriver {\n  constructor() {\n    super(subscriber => this.stream$.subscribe(subscriber));\n    this.hover = inject(TuiHintHover);\n    this.stream$ = new BehaviorSubject(false);\n    this.tuiHintManual = false;\n    this.type = 'hint';\n    this.hover.enabled = false;\n  }\n  ngOnChanges() {\n    this.stream$.next(!!this.tuiHintManual);\n    this.hover.enabled = this.tuiHintManual === null;\n  }\n  static {\n    this.ɵfac = function TuiHintManual_Factory(t) {\n      return new (t || TuiHintManual)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiHintManual,\n      selectors: [[\"\", \"tuiHint\", \"\", \"tuiHintManual\", \"\"]],\n      inputs: {\n        tuiHintManual: \"tuiHintManual\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsDriver(TuiHintManual)]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintManual, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiHint][tuiHintManual]',\n      providers: [tuiAsDriver(TuiHintManual)]\n    }]\n  }], function () {\n    return [];\n  }, {\n    tuiHintManual: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiHintOverflow {\n  constructor() {\n    this.hint = inject(TuiHintDirective);\n    this.tuiHintOverflow = '';\n  }\n  onMouseEnter({\n    scrollWidth,\n    clientWidth,\n    textContent\n  }) {\n    this.hint.tuiHint = scrollWidth > clientWidth && this.tuiHintOverflow !== null ? this.tuiHintOverflow || textContent : '';\n  }\n  static {\n    this.ɵfac = function TuiHintOverflow_Factory(t) {\n      return new (t || TuiHintOverflow)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiHintOverflow,\n      selectors: [[\"\", \"tuiHintOverflow\", \"\"]],\n      hostBindings: function TuiHintOverflow_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mouseenter\", function TuiHintOverflow_mouseenter_HostBindingHandler($event) {\n            return ctx.onMouseEnter($event.currentTarget);\n          });\n        }\n      },\n      inputs: {\n        tuiHintOverflow: \"tuiHintOverflow\"\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([{\n        directive: TuiHintDirective,\n        inputs: [\"tuiHintAppearance\", \"tuiHintAppearance\"]\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHintOverflow, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiHintOverflow]',\n      hostDirectives: [{\n        directive: TuiHintDirective,\n        inputs: ['tuiHintAppearance']\n      }],\n      host: {\n        '(mouseenter)': 'onMouseEnter($event.currentTarget)'\n      }\n    }]\n  }], null, {\n    tuiHintOverflow: [{\n      type: Input\n    }]\n  });\n})();\nconst TuiHint = [TuiHintComponent, TuiHintDirective, TuiHintOptionsDirective, TuiHintUnstyled, TuiHintDriver, TuiHintPosition, TuiHintHover, TuiHintOverflow, TuiHintDescribe, TuiHintHost, TuiHintManual, TuiHintPointer];\nclass TuiHints {\n  constructor() {\n    this.hints$ = inject(TuiHintService);\n    this.destroyRef = inject(DestroyRef);\n    this.cdr = inject(ChangeDetectorRef);\n    this.hints = [];\n  }\n  ngOnInit() {\n    // Due to this view being parallel to app content, `markForCheck` from `async` pipe\n    // can happen after view was checked, so calling `detectChanges` instead\n    this.hints$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(hints => {\n      this.hints = hints;\n      this.cdr.detectChanges();\n    });\n  }\n  static {\n    this.ɵfac = function TuiHints_Factory(t) {\n      return new (t || TuiHints)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiHints,\n      selectors: [[\"tui-hints\"]],\n      hostAttrs: [\"aria-live\", \"polite\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[\"role\", \"tooltip\", \"tuiAnimatedParent\", \"\", 3, \"tuiActiveZoneParent\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"tooltip\", \"tuiAnimatedParent\", \"\", 3, \"tuiActiveZoneParent\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"]],\n      template: function TuiHints_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiHints_div_0_Template, 2, 5, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngForOf\", ctx.hints);\n        }\n      },\n      dependencies: [NgForOf, PolymorpheusOutlet, TuiActiveZone, TuiAnimatedParent],\n      styles: [\"[_nghost-%COMP%]{position:fixed;top:0;left:0;inline-size:100%;block-size:100%;block-size:0}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHints, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-hints',\n      imports: [NgForOf, PolymorpheusOutlet, TuiActiveZone, TuiAnimatedParent],\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'aria-live': 'polite'\n      },\n      template: \"<div\\n    *ngFor=\\\"let hint of hints\\\"\\n    role=\\\"tooltip\\\"\\n    tuiAnimatedParent\\n    [tuiActiveZoneParent]=\\\"hint.activeZone || null\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"hint.component; context: {$implicit: hint}\\\" />\\n</div>\\n\",\n      styles: [\":host{position:fixed;top:0;left:0;inline-size:100%;block-size:100%;block-size:0}\\n\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_HINT_COMPONENT, TUI_HINT_DEFAULT_OPTIONS, TUI_HINT_DIRECTIONS, TUI_HINT_OPTIONS, TUI_HINT_PROVIDERS, TuiHint, TuiHintBaseComponent, TuiHintComponent, TuiHintDescribe, TuiHintDirective, TuiHintDriver, TuiHintHost, TuiHintHover, TuiHintManual, TuiHintOptionsDirective, TuiHintOverflow, TuiHintPointer, TuiHintPosition, TuiHintService, TuiHintUnstyled, TuiHintUnstyledComponent, TuiHints, tuiHintOptionsProvider };", "map": {"version": 3, "names": ["__decorate", "i0", "Injectable", "Directive", "Optional", "Self", "SkipSelf", "inject", "Input", "EventEmitter", "Output", "signal", "INJECTOR", "Component", "ChangeDetectionStrategy", "TemplateRef", "NgZone", "DestroyRef", "ChangeDetectorRef", "takeUntilDestroyed", "EMPTY_CLIENT_RECT", "i1", "TuiAnimated", "TuiAnimatedParent", "TuiHoveredService", "TUI_IS_MOBILE", "tuiInjectElement", "tuiPointToClientRect", "tui<PERSON><PERSON>", "tuiCreateTokenFromFactory", "tuiCreateToken", "tui<PERSON><PERSON><PERSON>", "tuiPure", "tuiPx", "tuiIsPresent", "TuiDriverDirective", "TuiDriver", "tuiAsDriver", "TuiPositionAccessor", "tui<PERSON>allbackAccessor", "TuiRectAccessor", "tuiAsRectAccessor", "tuiAsVehicle", "tuiPositionAccessorFor", "tuiRectAccessorFor", "TuiPositionService", "TuiVisualViewportService", "TUI_VIEWPORT", "tuiOverrideOptions", "tuiIsObscured", "PolymorpheusComponent", "injectContext", "Polymorpheus<PERSON><PERSON>let", "BehaviorSubject", "Subject", "merge", "switchMap", "of", "delay", "takeUntil", "repeat", "filter", "map", "tap", "<PERSON><PERSON><PERSON><PERSON>", "distinctUntilChanged", "fromEvent", "debounce", "timer", "startWith", "skip", "TuiActiveZone", "DOCUMENT", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tuiIfMap", "tuiTypedFromEvent", "tuiZonefreeScheduler", "tuiZoneOptimized", "tuiIsNativeFocused", "TuiHintUnstyledComponent_ng_container_0_Template", "rf", "ctx", "ɵɵelementContainer", "_c0", "TuiHintComponent_span_1_Template", "ɵɵelement", "text_r1", "polymorpheusOutlet", "ɵɵproperty", "ɵɵsanitizeHtml", "_c1", "a0", "$implicit", "TuiHints_div_0_ng_container_1_Template", "TuiHints_div_0_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "hint_r1", "activeZone", "ɵɵadvance", "component", "ɵɵpureFunction1", "TUI_HINT_COMPONENT", "TuiHintComponent", "TuiHintService", "constructor", "add", "directive", "next", "value", "concat", "remove", "includes", "hint", "ɵfac", "TuiHintService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "TuiHintDriver", "arguments", "ɵTuiHintDriver_BaseFactory", "TuiHintDriver_Factory", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "standalone", "features", "ɵɵInheritDefinitionFeature", "TUI_HINT_DIRECTIONS", "TUI_HINT_DEFAULT_OPTIONS", "direction", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "appearance", "icon", "TUI_HINT_OPTIONS", "tuiHintOptionsProvider", "override", "provide", "deps", "TuiHintOptionsDirective", "useFactory", "options", "skipSelf", "change$", "ngOnChanges", "TuiHintOptionsDirective_Factory", "selectors", "inputs", "content", "ɵɵInputFlags", "None", "ɵɵProvidersFeature", "ɵɵNgOnChangesFeature", "selector", "providers", "TuiHintHover", "subscriber", "stream$", "subscribe", "isMobile", "el", "hovered$", "visible", "toggle$", "pipe", "tuiHintHideDelay", "tuiHintShowDelay", "enabled", "hasAttribute", "parent", "optional", "toggle", "close", "TuiHintHover_Factory", "exportAs", "GAP$1", "ARROW_OFFSET", "TOP", "LEFT", "TuiHintPosition", "offset", "viewport", "accessor", "TuiHintDirective", "points", "reduce", "acc", "directionChange", "emitDirection", "emit", "getPosition", "rect", "width", "clientWidth", "height", "clientHeight", "hostRect", "getClientRect", "leftCenter", "left", "topCenter", "top", "bottom", "right", "priorityDirections", "Array", "isArray", "sortedDirections", "find", "checkPosition", "fallback", "ɵTuiHintPosition_BaseFactory", "TuiHintPosition_Factory", "outputs", "prototype", "service", "tuiHint", "set", "ngOnDestroy", "getBoundingClientRect", "show", "TuiHintDirective_Factory", "context", "useClass", "ɵɵHostDirectivesFeature", "hostDirectives", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentRect", "onMove", "clientX", "clientY", "ɵTuiHintPointer_BaseFactory", "TuiHintPointer_Factory", "hostBindings", "TuiHintPointer_HostBindings", "ɵɵlistener", "Tui<PERSON>int<PERSON><PERSON>er_mousemove_zoneless_HostBindingHandler", "$event", "host", "TuiHintUnstyledComponent", "TuiHintUnstyledComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiHintUnstyledComponent_Template", "dependencies", "encapsulation", "changeDetection", "imports", "OnPush", "TuiHintUnstyled", "TuiHintUnstyled_Factory", "TUI_HINT_PROVIDERS", "GAP", "TuiHintBaseComponent", "hover", "vvs", "pointer", "closest", "getAttribute", "isConnected", "point", "correct", "update", "complete", "onClick", "target", "tagName", "contains", "apply", "beakTop", "beakLeft", "style", "setProperty", "Math", "ceil", "safeLeft", "max", "round", "TuiHintBaseComponent_Factory", "hostVars", "TuiHintBaseComponent_HostBindings", "TuiHintBaseComponent_click_HostBindingHandler", "ɵɵresolveDocument", "ɵɵattribute", "ɵɵclassProp", "TuiHintBaseComponent_Template", "ɵTuiHintComponent_BaseFactory", "TuiHintComponent_Factory", "ngContentSelectors", "TuiHintComponent_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "TuiHintDescribe", "doc", "zone", "id$", "capture", "focused", "element", "tuiHintDescribe", "id", "getElementById", "TuiHintDescribe_Factory", "TuiHintHost", "tuiHintHost", "ɵTuiHintHost_BaseFactory", "TuiHintHost_Factory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TuiHintManual_Factory", "TuiHintOverflow", "tuiHintOverflow", "onMouseEnter", "scrollWidth", "textContent", "TuiHintOverflow_Factory", "TuiHintOverflow_HostBindings", "TuiHintOverflow_mouseenter_HostBindingHandler", "currentTarget", "TuiHint", "TuiHints", "hints$", "destroyRef", "cdr", "hints", "ngOnInit", "detectChanges", "TuiHints_Factory", "hostAttrs", "TuiHints_Template", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-directives-hint.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { Injectable, Directive, Optional, Self, SkipSelf, inject, Input, EventEmitter, Output, signal, INJECTOR, Component, ChangeDetectionStrategy, TemplateRef, NgZone, DestroyRef, ChangeDetectorRef } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { EMPTY_CLIENT_RECT } from '@taiga-ui/cdk/constants';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated, TuiAnimatedParent } from '@taiga-ui/cdk/directives/animated';\nimport { TuiHoveredService } from '@taiga-ui/cdk/directives/hovered';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement, tuiPointToClientRect } from '@taiga-ui/cdk/utils/dom';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { tuiCreateTokenFromFactory, tuiCreateToken, tuiProvide, tuiPure, tuiPx, tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiDriverDirective, TuiDriver, tuiAsDriver, TuiPositionAccessor, tuiFallbackAccessor, TuiRectAccessor, tuiAsRectAccessor, tuiAsVehicle, tuiPositionAccessorFor, tuiRectAccessorFor } from '@taiga-ui/core/classes';\nimport { TuiPositionService, TuiVisualViewportService } from '@taiga-ui/core/services';\nimport { TUI_VIEWPORT } from '@taiga-ui/core/tokens';\nimport { tuiOverrideOptions, tuiIsObscured } from '@taiga-ui/core/utils';\nimport { PolymorpheusComponent, injectContext, PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { BehaviorSubject, Subject, merge, switchMap, of, delay, takeUntil, repeat, filter, map, tap, takeWhile, distinctUntilChanged, fromEvent, debounce, timer, startWith, skip } from 'rxjs';\nimport { TuiActiveZone } from '@taiga-ui/cdk/directives/active-zone';\nimport { DOCUMENT, NgForOf } from '@angular/common';\nimport { tuiIfMap, tuiTypedFromEvent, tuiZonefreeScheduler, tuiZoneOptimized } from '@taiga-ui/cdk/observables';\nimport { tuiIsNativeFocused } from '@taiga-ui/cdk/utils/focus';\n\n/**\n * A component to display a hint\n */\nconst TUI_HINT_COMPONENT = tuiCreateTokenFromFactory(() => TuiHintComponent);\n\n/**\n * Service for displaying hints/tooltips\n */\nclass TuiHintService extends BehaviorSubject {\n    constructor() {\n        super([]);\n    }\n    add(directive) {\n        this.next(this.value.concat(directive));\n    }\n    remove(directive) {\n        if (this.value.includes(directive)) {\n            this.next(this.value.filter((hint) => hint !== directive));\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return []; } });\n\nclass TuiHintDriver extends TuiDriverDirective {\n    constructor() {\n        super(...arguments);\n        this.type = 'hint';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintDriver, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintDriver, isStandalone: true, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintDriver, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                }]\n        }] });\n\nconst TUI_HINT_DIRECTIONS = [\n    'bottom-left',\n    'bottom',\n    'bottom-right',\n    'top-left',\n    'top',\n    'top-right',\n    'left-top',\n    'left',\n    'left-bottom',\n    'right-top',\n    'right',\n    'right-bottom',\n];\n/** Default values for hint options */\nconst TUI_HINT_DEFAULT_OPTIONS = {\n    direction: 'bottom-left',\n    showDelay: 500,\n    hideDelay: 200,\n    appearance: '',\n    /** TODO @deprecated use {@link TUI_TOOLTIP_OPTIONS} instead **/\n    icon: '@tui.circle-help',\n};\n/**\n * Default parameters for hint directive\n */\nconst TUI_HINT_OPTIONS = tuiCreateToken(TUI_HINT_DEFAULT_OPTIONS);\nconst tuiHintOptionsProvider = (override) => ({\n    provide: TUI_HINT_OPTIONS,\n    deps: [\n        [new Optional(), new Self(), TuiHintOptionsDirective],\n        [new Optional(), new SkipSelf(), TUI_HINT_OPTIONS],\n    ],\n    useFactory: tuiOverrideOptions(override, TUI_HINT_DEFAULT_OPTIONS),\n});\n/**\n * @deprecated: drop in 5.0\n */\nclass TuiHintOptionsDirective {\n    constructor() {\n        this.options = inject(TUI_HINT_OPTIONS, { skipSelf: true });\n        this.direction = this.options.direction;\n        this.appearance = this.options.appearance;\n        this.showDelay = this.options.showDelay;\n        this.hideDelay = this.options.hideDelay;\n        this.icon = this.options.icon;\n        this.change$ = new Subject();\n    }\n    ngOnChanges() {\n        this.change$.next();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintOptionsDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintOptionsDirective, isStandalone: true, selector: \"[tuiHintContent]\", inputs: { content: [\"tuiHintContent\", \"content\"], direction: [\"tuiHintDirection\", \"direction\"], appearance: [\"tuiHintAppearance\", \"appearance\"], showDelay: [\"tuiHintShowDelay\", \"showDelay\"], hideDelay: [\"tuiHintHideDelay\", \"hideDelay\"] }, providers: [tuiProvide(TUI_HINT_OPTIONS, TuiHintOptionsDirective)], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintOptionsDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiHintContent]',\n                    providers: [tuiProvide(TUI_HINT_OPTIONS, TuiHintOptionsDirective)],\n                }]\n        }], propDecorators: { content: [{\n                type: Input,\n                args: ['tuiHintContent']\n            }], direction: [{\n                type: Input,\n                args: ['tuiHintDirection']\n            }], appearance: [{\n                type: Input,\n                args: ['tuiHintAppearance']\n            }], showDelay: [{\n                type: Input,\n                args: ['tuiHintShowDelay']\n            }], hideDelay: [{\n                type: Input,\n                args: ['tuiHintHideDelay']\n            }] } });\n\nclass TuiHintHover extends TuiDriver {\n    constructor() {\n        super((subscriber) => this.stream$.subscribe(subscriber));\n        this.isMobile = inject(TUI_IS_MOBILE);\n        this.el = tuiInjectElement();\n        this.hovered$ = inject(TuiHoveredService);\n        this.options = inject(TUI_HINT_OPTIONS);\n        this.visible = false;\n        this.toggle$ = new Subject();\n        this.stream$ = merge(this.toggle$.pipe(switchMap((visible) => this.isMobile\n            ? of(visible)\n            : of(visible).pipe(delay(visible ? 0 : this.tuiHintHideDelay))), takeUntil(this.hovered$), repeat()), this.hovered$.pipe(switchMap((visible) => this.isMobile\n            ? of(visible)\n            : of(visible).pipe(delay(visible ? this.tuiHintShowDelay : this.tuiHintHideDelay))), takeUntil(this.toggle$), repeat())).pipe(filter(() => this.enabled), map((value) => value &&\n            (this.el.hasAttribute('tuiHintPointer') || !tuiIsObscured(this.el))), tap((visible) => {\n            this.visible = visible;\n        }));\n        this.parent = inject(TuiHintHover, {\n            optional: true,\n            skipSelf: true,\n        });\n        this.tuiHintShowDelay = this.options.showDelay;\n        this.tuiHintHideDelay = this.options.hideDelay;\n        this.type = 'hint';\n        this.enabled = true;\n    }\n    toggle(visible = !this.visible) {\n        this.toggle$.next(visible);\n        this.parent?.toggle(visible);\n    }\n    close() {\n        this.toggle$.next(false);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintHover, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintHover, isStandalone: true, inputs: { tuiHintShowDelay: \"tuiHintShowDelay\", tuiHintHideDelay: \"tuiHintHideDelay\" }, providers: [tuiAsDriver(TuiHintHover), TuiHoveredService], exportAs: [\"tuiHintHover\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintHover, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    providers: [tuiAsDriver(TuiHintHover), TuiHoveredService],\n                    exportAs: 'tuiHintHover',\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { tuiHintShowDelay: [{\n                type: Input\n            }], tuiHintHideDelay: [{\n                type: Input\n            }] } });\n\nconst GAP$1 = 8;\nconst ARROW_OFFSET = 24;\nconst TOP = 0;\nconst LEFT = 1;\nclass TuiHintPosition extends TuiPositionAccessor {\n    constructor() {\n        super(...arguments);\n        this.offset = inject(TUI_IS_MOBILE) ? 16 : 8;\n        this.viewport = inject(TUI_VIEWPORT);\n        this.accessor = tuiFallbackAccessor('hint')(inject(TuiRectAccessor), inject(TuiHintDirective));\n        this.points = TUI_HINT_DIRECTIONS.reduce((acc, direction) => ({ ...acc, [direction]: [0, 0] }), {});\n        this.direction = inject(TUI_HINT_OPTIONS).direction;\n        this.directionChange = new EventEmitter();\n        this.type = 'hint';\n    }\n    emitDirection(direction) {\n        this.directionChange.emit(direction);\n    }\n    getPosition(rect, el) {\n        const width = el?.clientWidth ?? rect.width;\n        const height = el?.clientHeight ?? rect.height;\n        const hostRect = this.accessor.getClientRect() ?? EMPTY_CLIENT_RECT;\n        const leftCenter = hostRect.left + hostRect.width / 2;\n        const topCenter = hostRect.top + hostRect.height / 2;\n        this.points['top-left'][TOP] = hostRect.top - height - this.offset;\n        this.points['top-left'][LEFT] = leftCenter - width + ARROW_OFFSET;\n        this.points.top[TOP] = this.points['top-left'][TOP];\n        this.points.top[LEFT] = leftCenter - width / 2;\n        this.points['top-right'][TOP] = this.points['top-left'][TOP];\n        this.points['top-right'][LEFT] = leftCenter - ARROW_OFFSET;\n        this.points['bottom-left'][TOP] = hostRect.bottom + this.offset;\n        this.points['bottom-left'][LEFT] = this.points['top-left'][LEFT];\n        this.points.bottom[TOP] = this.points['bottom-left'][TOP];\n        this.points.bottom[LEFT] = this.points.top[LEFT];\n        this.points['bottom-right'][TOP] = this.points['bottom-left'][TOP];\n        this.points['bottom-right'][LEFT] = this.points['top-right'][LEFT];\n        this.points['left-top'][TOP] = topCenter - height + ARROW_OFFSET;\n        this.points['left-top'][LEFT] = hostRect.left - width - this.offset;\n        this.points.left[TOP] = topCenter - height / 2;\n        this.points.left[LEFT] = this.points['left-top'][LEFT];\n        this.points['left-bottom'][TOP] = topCenter - ARROW_OFFSET;\n        this.points['left-bottom'][LEFT] = this.points['left-top'][LEFT];\n        this.points['right-top'][TOP] = this.points['left-top'][TOP];\n        this.points['right-top'][LEFT] = hostRect.right + this.offset;\n        this.points.right[TOP] = this.points.left[TOP];\n        this.points.right[LEFT] = this.points['right-top'][LEFT];\n        this.points['right-bottom'][TOP] = this.points['left-bottom'][TOP];\n        this.points['right-bottom'][LEFT] = this.points['right-top'][LEFT];\n        const priorityDirections = Array.isArray(this.direction)\n            ? this.direction\n            : [this.direction];\n        const sortedDirections = priorityDirections.concat(TUI_HINT_DIRECTIONS);\n        const direction = sortedDirections.find((direction) => this.checkPosition(this.points[direction], width, height)) || this.fallback;\n        this.emitDirection(direction);\n        return this.points[direction];\n    }\n    get fallback() {\n        return this.points.top[TOP] >\n            this.viewport.getClientRect().bottom - this.points.bottom[TOP]\n            ? 'top'\n            : 'bottom';\n    }\n    checkPosition([top, left], width, height) {\n        const viewport = this.viewport.getClientRect();\n        return (top > viewport.top + GAP$1 &&\n            left > viewport.left + GAP$1 &&\n            top + height < viewport.bottom - GAP$1 &&\n            left + width < viewport.right - GAP$1);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintPosition, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintPosition, isStandalone: true, inputs: { direction: [\"tuiHintDirection\", \"direction\"] }, outputs: { directionChange: \"tuiHintDirectionChange\" }, usesInheritance: true, ngImport: i0 }); }\n}\n__decorate([\n    tuiPure\n], TuiHintPosition.prototype, \"emitDirection\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintPosition, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                }]\n        }], propDecorators: { direction: [{\n                type: Input,\n                args: ['tuiHintDirection']\n            }], directionChange: [{\n                type: Output,\n                args: ['tuiHintDirectionChange']\n            }], emitDirection: [] } });\n\nclass TuiHintDirective {\n    constructor() {\n        this.service = inject(TuiHintService);\n        this.appearance = inject(TUI_HINT_OPTIONS).appearance;\n        this.content = signal(null);\n        this.component = inject((PolymorpheusComponent));\n        this.el = tuiInjectElement();\n        this.activeZone = inject(TuiActiveZone, { optional: true });\n        this.type = 'hint';\n    }\n    set tuiHint(content) {\n        this.content.set(content);\n        if (!content) {\n            this.toggle(false);\n        }\n    }\n    ngOnDestroy() {\n        this.toggle(false);\n    }\n    getClientRect() {\n        return this.el.getBoundingClientRect();\n    }\n    toggle(show) {\n        if (show && this.content()) {\n            this.service.add(this);\n        }\n        else {\n            this.service.remove(this);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintDirective, isStandalone: true, selector: \"[tuiHint]:not(ng-container):not(ng-template)\", inputs: { context: [\"tuiHintContext\", \"context\"], appearance: [\"tuiHintAppearance\", \"appearance\"], tuiHint: \"tuiHint\" }, providers: [\n            tuiAsRectAccessor(TuiHintDirective),\n            tuiAsVehicle(TuiHintDirective),\n            {\n                provide: PolymorpheusComponent,\n                deps: [TUI_HINT_COMPONENT, INJECTOR],\n                useClass: PolymorpheusComponent,\n            },\n        ], hostDirectives: [{ directive: TuiHintDriver }, { directive: TuiHintHover, inputs: [\"tuiHintHideDelay\", \"tuiHintHideDelay\", \"tuiHintShowDelay\", \"tuiHintShowDelay\"] }, { directive: TuiHintPosition, inputs: [\"tuiHintDirection\", \"tuiHintDirection\"], outputs: [\"tuiHintDirectionChange\", \"tuiHintDirectionChange\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiHint]:not(ng-container):not(ng-template)',\n                    providers: [\n                        tuiAsRectAccessor(TuiHintDirective),\n                        tuiAsVehicle(TuiHintDirective),\n                        {\n                            provide: PolymorpheusComponent,\n                            deps: [TUI_HINT_COMPONENT, INJECTOR],\n                            useClass: PolymorpheusComponent,\n                        },\n                    ],\n                    hostDirectives: [\n                        TuiHintDriver,\n                        {\n                            directive: TuiHintHover,\n                            inputs: ['tuiHintHideDelay', 'tuiHintShowDelay'],\n                        },\n                        {\n                            directive: TuiHintPosition,\n                            inputs: ['tuiHintDirection'],\n                            outputs: ['tuiHintDirectionChange'],\n                        },\n                    ],\n                }]\n        }], propDecorators: { context: [{\n                type: Input,\n                args: ['tuiHintContext']\n            }], appearance: [{\n                type: Input,\n                args: ['tuiHintAppearance']\n            }], tuiHint: [{\n                type: Input\n            }] } });\n\nclass TuiHintPointer extends TuiHintHover {\n    constructor() {\n        super(...arguments);\n        this.currentRect = EMPTY_CLIENT_RECT;\n    }\n    getClientRect() {\n        return this.currentRect;\n    }\n    onMove({ clientX, clientY }) {\n        this.currentRect = tuiPointToClientRect(clientX, clientY);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintPointer, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintPointer, isStandalone: true, selector: \"[tuiHint][tuiHintPointer]\", host: { listeners: { \"mousemove.zoneless\": \"onMove($event)\" } }, providers: [tuiAsRectAccessor(TuiHintPointer), tuiAsDriver(TuiHintPointer)], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintPointer, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiHint][tuiHintPointer]',\n                    providers: [tuiAsRectAccessor(TuiHintPointer), tuiAsDriver(TuiHintPointer)],\n                    host: {\n                        '(mousemove.zoneless)': 'onMove($event)',\n                    },\n                }]\n        }] });\n\nclass TuiHintUnstyledComponent {\n    constructor() {\n        this.context = injectContext();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintUnstyledComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintUnstyledComponent, isStandalone: true, selector: \"ng-component\", ngImport: i0, template: '<ng-container *polymorpheusOutlet=\"context.$implicit.content()\" />', isInline: true, dependencies: [{ kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintUnstyledComponent, decorators: [{\n            type: Component,\n            args: [{\n                    standalone: true,\n                    imports: [PolymorpheusOutlet],\n                    template: '<ng-container *polymorpheusOutlet=\"context.$implicit.content()\" />',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }] });\nclass TuiHintUnstyled {\n    constructor() {\n        const hint = inject((TuiHintDirective));\n        hint.component = new PolymorpheusComponent(TuiHintUnstyledComponent);\n        hint.content.set(inject((TemplateRef)));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintUnstyled, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintUnstyled, isStandalone: true, selector: \"ng-template[tuiHint]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintUnstyled, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-template[tuiHint]',\n                }]\n        }], ctorParameters: function () { return []; } });\n\nconst TUI_HINT_PROVIDERS = [\n    TuiPositionService,\n    TuiHoveredService,\n    tuiPositionAccessorFor('hint', TuiHintPosition),\n    tuiRectAccessorFor('hint', TuiHintDirective),\n];\nconst GAP = 8;\n// TODO(v5): remove base component after angular update\nclass TuiHintBaseComponent {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.hover = inject(TuiHintHover);\n        this.vvs = inject(TuiVisualViewportService);\n        this.viewport = inject(TUI_VIEWPORT);\n        this.pointer = inject(TuiHintPointer, { optional: true });\n        this.accessor = inject(TuiRectAccessor);\n        this.hint = injectContext().$implicit;\n        this.isMobile = inject(TUI_IS_MOBILE);\n        this.content = this.hint.component.component === TuiHintUnstyledComponent\n            ? signal('')\n            : this.hint.content;\n        this.appearance = this.hint.appearance ||\n            this.hint.el.closest('[tuiTheme]')?.getAttribute('tuiTheme');\n        inject(TuiPositionService)\n            .pipe(takeWhile(() => this.hint.el.isConnected), map((point) => this.vvs.correct(point)), takeUntilDestroyed())\n            .subscribe({\n            next: ([top, left]) => this.update(top, left),\n            complete: () => this.hover.close(),\n        });\n        inject(TuiHoveredService)\n            .pipe(takeUntilDestroyed())\n            .subscribe((hover) => this.hover.toggle(hover));\n    }\n    onClick(target) {\n        if ((!target.closest(this.el.tagName) && !this.hint.el.contains(target)) ||\n            tuiIsObscured(this.hint.el)) {\n            this.hover.toggle(false);\n        }\n    }\n    apply(top, left, beakTop, beakLeft) {\n        this.el.style.top = top;\n        this.el.style.left = left;\n        this.el.style.setProperty('--t-top', `${beakTop}%`);\n        this.el.style.setProperty('--t-left', `${beakLeft}%`);\n        this.el.style.setProperty('--t-rotate', !beakLeft || Math.ceil(beakLeft) === 100 ? '90deg' : '0deg');\n    }\n    update(top, left) {\n        const { clientHeight, clientWidth } = this.el;\n        const rect = this.accessor.getClientRect();\n        if (rect === EMPTY_CLIENT_RECT || !clientHeight || !clientWidth) {\n            return;\n        }\n        const viewport = this.viewport.getClientRect();\n        const safeLeft = tuiClamp(Math.max(GAP, left), viewport.left + GAP, Math.max(GAP, viewport.width + viewport.left - clientWidth - GAP));\n        const [beakTop, beakLeft] = this.vvs.correct([\n            rect.top + rect.height / 2 - top,\n            rect.left + rect.width / 2 - safeLeft,\n        ]);\n        this.apply(tuiPx(Math.round(top)), tuiPx(Math.round(safeLeft)), Math.round((tuiClamp(beakTop, 0, clientHeight) / clientHeight) * 100), Math.round((tuiClamp(beakLeft, 0, clientWidth) / clientWidth) * 100));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintBaseComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintBaseComponent, isStandalone: true, selector: \"ng-component\", host: { listeners: { \"document:click\": \"onClick($event.target)\" }, properties: { \"class._untouchable\": \"pointer\", \"class._mobile\": \"isMobile\", \"attr.data-appearance\": \"appearance\", \"attr.tuiTheme\": \"appearance === \\\"dark\\\" ? \\\"light\\\" : null\" } }, ngImport: i0, template: '', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\n__decorate([\n    tuiPure\n], TuiHintBaseComponent.prototype, \"apply\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintBaseComponent, decorators: [{\n            type: Component,\n            args: [{\n                    standalone: true,\n                    template: '',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    host: {\n                        '[class._untouchable]': 'pointer',\n                        '[class._mobile]': 'isMobile',\n                        '[attr.data-appearance]': 'appearance',\n                        '[attr.tuiTheme]': 'appearance === \"dark\" ? \"light\" : null',\n                        '(document:click)': 'onClick($event.target)',\n                    },\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { apply: [] } });\nclass TuiHintComponent extends TuiHintBaseComponent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintComponent, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintComponent, isStandalone: true, selector: \"tui-hint\", providers: TUI_HINT_PROVIDERS, usesInheritance: true, hostDirectives: [{ directive: i1.TuiAnimated }], ngImport: i0, template: `\n        <ng-content />\n        <span\n            *polymorpheusOutlet=\"content() as text; context: hint.context\"\n            [innerHTML]=\"text\"\n        ></span>\n    `, isInline: true, styles: [\":host{position:absolute;max-inline-size:min(18rem,calc(100% - 1rem));padding:.75rem 1rem;background:var(--tui-background-accent-1);border-radius:var(--tui-radius-l);color:var(--tui-text-primary-on-accent-1);box-sizing:border-box;font:var(--tui-font-text-s);white-space:pre-line;overflow-wrap:break-word;transform-origin:var(--t-left) var(--t-top);--tui-background-elevation-2: var(--tui-background-elevation-3)}:host.tui-enter,:host.tui-leave{animation-name:tuiFade}:host :host-context(tui-root._mobile).tui-enter{animation:tuiFade var(--tui-duration) ease-in-out,tuiScale var(--tui-duration) cubic-bezier(.34,1.56,.64,1)}:host :host-context(tui-root._mobile).tui-leave{animation:tuiFade var(--tui-duration) ease-in-out reverse,tuiScale var(--tui-duration) ease-in-out reverse}:host:before{content:\\\"\\\";position:absolute;top:var(--t-top);left:var(--t-left);inline-size:.75rem;block-size:.5rem;background:inherit;-webkit-mask-image:url('data:image/svg+xml,<svg viewBox=\\\"0 0 12 8\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M3.61336 1.69607L2.44882 2.96493C1.84795 3.61964 0.949361 3.99951 0.00053941 4C0.000359608 4 0.000179805 4 0 4C0.000179863 4 0.000359764 4 0.000539623 4C0.949362 4.00049 1.84795 4.38036 2.44882 5.03506L3.61336 6.30394C4.55981 7.33517 5.03303 7.85079 5.63254 7.96535C5.87433 8.01155 6.12436 8.01155 6.36616 7.96535C6.96567 7.85079 7.43889 7.33517 8.38534 6.30393L9.54988 5.03507C10.1511 4.37994 11.0505 4 12 4C11.0505 4 10.1511 3.62006 9.54988 2.96493L8.38534 1.69606C7.43889 0.664826 6.96567 0.149207 6.36616 0.0346517C6.12436 -0.0115506 5.87433 -0.0115506 5.63254 0.0346517C5.03303 0.149207 4.55981 0.664827 3.61336 1.69607Z\\\" /></svg>');mask-image:url('data:image/svg+xml,<svg viewBox=\\\"0 0 12 8\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M3.61336 1.69607L2.44882 2.96493C1.84795 3.61964 0.949361 3.99951 0.00053941 4C0.000359608 4 0.000179805 4 0 4C0.000179863 4 0.000359764 4 0.000539623 4C0.949362 4.00049 1.84795 4.38036 2.44882 5.03506L3.61336 6.30394C4.55981 7.33517 5.03303 7.85079 5.63254 7.96535C5.87433 8.01155 6.12436 8.01155 6.36616 7.96535C6.96567 7.85079 7.43889 7.33517 8.38534 6.30393L9.54988 5.03507C10.1511 4.37994 11.0505 4 12 4C11.0505 4 10.1511 3.62006 9.54988 2.96493L8.38534 1.69606C7.43889 0.664826 6.96567 0.149207 6.36616 0.0346517C6.12436 -0.0115506 5.87433 -0.0115506 5.63254 0.0346517C5.03303 0.149207 4.55981 0.664827 3.61336 1.69607Z\\\" /></svg>');transform:translate(-50%,-50%) rotate(var(--t-rotate))}:host._mobile{font:var(--tui-font-text-m)}:host._mobile:before{inline-size:1.5rem;block-size:1.125rem;-webkit-mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 18\\\"><path d=\\\"M7.22854 3.81615L4.89971 6.6711C3.69732 8.14514 1.8988 9 0 9C1.8988 9 3.69732 9.85486 4.89971 11.3289L7.22854 14.1839L7.22854 14.1839C9.12123 16.5041 10.0676 17.6643 11.2665 17.922C11.75 18.026 12.25 18.026 12.7335 17.922C13.9324 17.6643 14.8788 16.5041 16.7715 14.1839L19.1003 11.3289C20.3027 9.85486 22.1012 9 24 9C22.1012 9 20.3027 8.14514 19.1003 6.6711L16.7715 3.81614C14.8788 1.49586 13.9324 0.335716 12.7335 0.0779663C12.25 -0.0259888 11.75 -0.0259888 11.2665 0.0779663C10.0676 0.335716 9.12123 1.49586 7.22854 3.81614L7.22854 3.81615Z\\\" /></svg>');mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 18\\\"><path d=\\\"M7.22854 3.81615L4.89971 6.6711C3.69732 8.14514 1.8988 9 0 9C1.8988 9 3.69732 9.85486 4.89971 11.3289L7.22854 14.1839L7.22854 14.1839C9.12123 16.5041 10.0676 17.6643 11.2665 17.922C11.75 18.026 12.25 18.026 12.7335 17.922C13.9324 17.6643 14.8788 16.5041 16.7715 14.1839L19.1003 11.3289C20.3027 9.85486 22.1012 9 24 9C22.1012 9 20.3027 8.14514 19.1003 6.6711L16.7715 3.81614C14.8788 1.49586 13.9324 0.335716 12.7335 0.0779663C12.25 -0.0259888 11.75 -0.0259888 11.2665 0.0779663C10.0676 0.335716 9.12123 1.49586 7.22854 3.81614L7.22854 3.81615Z\\\" /></svg>')}:host[data-appearance=error]{background:var(--tui-status-negative)}:host[data-appearance=dark]{background:var(--tui-background-elevation-1);color:var(--tui-text-primary);filter:drop-shadow(0 0 .125rem rgba(0,0,0,.16)) drop-shadow(0 1.5rem 1rem rgba(0,0,0,.03)) drop-shadow(0 .75rem .75rem rgba(0,0,0,.04)) drop-shadow(0 .25rem .375rem rgba(0,0,0,.05))}:host:not([style*=top]){visibility:hidden}:host._untouchable{pointer-events:none}\\n\"], dependencies: [{ kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-hint', imports: [PolymorpheusOutlet], template: `\n        <ng-content />\n        <span\n            *polymorpheusOutlet=\"content() as text; context: hint.context\"\n            [innerHTML]=\"text\"\n        ></span>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, providers: TUI_HINT_PROVIDERS, hostDirectives: [TuiAnimated], styles: [\":host{position:absolute;max-inline-size:min(18rem,calc(100% - 1rem));padding:.75rem 1rem;background:var(--tui-background-accent-1);border-radius:var(--tui-radius-l);color:var(--tui-text-primary-on-accent-1);box-sizing:border-box;font:var(--tui-font-text-s);white-space:pre-line;overflow-wrap:break-word;transform-origin:var(--t-left) var(--t-top);--tui-background-elevation-2: var(--tui-background-elevation-3)}:host.tui-enter,:host.tui-leave{animation-name:tuiFade}:host :host-context(tui-root._mobile).tui-enter{animation:tuiFade var(--tui-duration) ease-in-out,tuiScale var(--tui-duration) cubic-bezier(.34,1.56,.64,1)}:host :host-context(tui-root._mobile).tui-leave{animation:tuiFade var(--tui-duration) ease-in-out reverse,tuiScale var(--tui-duration) ease-in-out reverse}:host:before{content:\\\"\\\";position:absolute;top:var(--t-top);left:var(--t-left);inline-size:.75rem;block-size:.5rem;background:inherit;-webkit-mask-image:url('data:image/svg+xml,<svg viewBox=\\\"0 0 12 8\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M3.61336 1.69607L2.44882 2.96493C1.84795 3.61964 0.949361 3.99951 0.00053941 4C0.000359608 4 0.000179805 4 0 4C0.000179863 4 0.000359764 4 0.000539623 4C0.949362 4.00049 1.84795 4.38036 2.44882 5.03506L3.61336 6.30394C4.55981 7.33517 5.03303 7.85079 5.63254 7.96535C5.87433 8.01155 6.12436 8.01155 6.36616 7.96535C6.96567 7.85079 7.43889 7.33517 8.38534 6.30393L9.54988 5.03507C10.1511 4.37994 11.0505 4 12 4C11.0505 4 10.1511 3.62006 9.54988 2.96493L8.38534 1.69606C7.43889 0.664826 6.96567 0.149207 6.36616 0.0346517C6.12436 -0.0115506 5.87433 -0.0115506 5.63254 0.0346517C5.03303 0.149207 4.55981 0.664827 3.61336 1.69607Z\\\" /></svg>');mask-image:url('data:image/svg+xml,<svg viewBox=\\\"0 0 12 8\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M3.61336 1.69607L2.44882 2.96493C1.84795 3.61964 0.949361 3.99951 0.00053941 4C0.000359608 4 0.000179805 4 0 4C0.000179863 4 0.000359764 4 0.000539623 4C0.949362 4.00049 1.84795 4.38036 2.44882 5.03506L3.61336 6.30394C4.55981 7.33517 5.03303 7.85079 5.63254 7.96535C5.87433 8.01155 6.12436 8.01155 6.36616 7.96535C6.96567 7.85079 7.43889 7.33517 8.38534 6.30393L9.54988 5.03507C10.1511 4.37994 11.0505 4 12 4C11.0505 4 10.1511 3.62006 9.54988 2.96493L8.38534 1.69606C7.43889 0.664826 6.96567 0.149207 6.36616 0.0346517C6.12436 -0.0115506 5.87433 -0.0115506 5.63254 0.0346517C5.03303 0.149207 4.55981 0.664827 3.61336 1.69607Z\\\" /></svg>');transform:translate(-50%,-50%) rotate(var(--t-rotate))}:host._mobile{font:var(--tui-font-text-m)}:host._mobile:before{inline-size:1.5rem;block-size:1.125rem;-webkit-mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 18\\\"><path d=\\\"M7.22854 3.81615L4.89971 6.6711C3.69732 8.14514 1.8988 9 0 9C1.8988 9 3.69732 9.85486 4.89971 11.3289L7.22854 14.1839L7.22854 14.1839C9.12123 16.5041 10.0676 17.6643 11.2665 17.922C11.75 18.026 12.25 18.026 12.7335 17.922C13.9324 17.6643 14.8788 16.5041 16.7715 14.1839L19.1003 11.3289C20.3027 9.85486 22.1012 9 24 9C22.1012 9 20.3027 8.14514 19.1003 6.6711L16.7715 3.81614C14.8788 1.49586 13.9324 0.335716 12.7335 0.0779663C12.25 -0.0259888 11.75 -0.0259888 11.2665 0.0779663C10.0676 0.335716 9.12123 1.49586 7.22854 3.81614L7.22854 3.81615Z\\\" /></svg>');mask-image:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 18\\\"><path d=\\\"M7.22854 3.81615L4.89971 6.6711C3.69732 8.14514 1.8988 9 0 9C1.8988 9 3.69732 9.85486 4.89971 11.3289L7.22854 14.1839L7.22854 14.1839C9.12123 16.5041 10.0676 17.6643 11.2665 17.922C11.75 18.026 12.25 18.026 12.7335 17.922C13.9324 17.6643 14.8788 16.5041 16.7715 14.1839L19.1003 11.3289C20.3027 9.85486 22.1012 9 24 9C22.1012 9 20.3027 8.14514 19.1003 6.6711L16.7715 3.81614C14.8788 1.49586 13.9324 0.335716 12.7335 0.0779663C12.25 -0.0259888 11.75 -0.0259888 11.2665 0.0779663C10.0676 0.335716 9.12123 1.49586 7.22854 3.81614L7.22854 3.81615Z\\\" /></svg>')}:host[data-appearance=error]{background:var(--tui-status-negative)}:host[data-appearance=dark]{background:var(--tui-background-elevation-1);color:var(--tui-text-primary);filter:drop-shadow(0 0 .125rem rgba(0,0,0,.16)) drop-shadow(0 1.5rem 1rem rgba(0,0,0,.03)) drop-shadow(0 .75rem .75rem rgba(0,0,0,.04)) drop-shadow(0 .25rem .375rem rgba(0,0,0,.05))}:host:not([style*=top]){visibility:hidden}:host._untouchable{pointer-events:none}\\n\"] }]\n        }] });\n\nclass TuiHintDescribe extends TuiDriver {\n    constructor() {\n        super((subscriber) => this.stream$.subscribe(subscriber));\n        this.doc = inject(DOCUMENT);\n        this.el = tuiInjectElement();\n        this.zone = inject(NgZone);\n        this.id$ = new BehaviorSubject('');\n        this.stream$ = this.id$.pipe(distinctUntilChanged(), tuiIfMap(() => fromEvent(this.doc, 'keydown', { capture: true }), tuiIsPresent), switchMap(() => this.focused\n            ? of(false)\n            : merge(tuiTypedFromEvent(this.doc, 'keyup'), tuiTypedFromEvent(this.element, 'blur')).pipe(map(() => this.focused))), debounce((visible) => visible ? timer(1000, tuiZonefreeScheduler(this.zone)) : of(null)), startWith(false), distinctUntilChanged(), skip(1), tuiZoneOptimized());\n        this.type = 'hint';\n    }\n    set tuiHintDescribe(id) {\n        this.id$.next(id || '');\n    }\n    get element() {\n        return this.doc.getElementById(this.id$.value || '') || this.el;\n    }\n    get focused() {\n        return tuiIsNativeFocused(this.element);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintDescribe, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintDescribe, isStandalone: true, selector: \"[tuiHintDescribe]\", inputs: { tuiHintDescribe: \"tuiHintDescribe\" }, providers: [tuiAsDriver(TuiHintDescribe)], usesInheritance: true, ngImport: i0 }); }\n}\n__decorate([\n    tuiPure\n], TuiHintDescribe.prototype, \"element\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintDescribe, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiHintDescribe]',\n                    providers: [tuiAsDriver(TuiHintDescribe)],\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { tuiHintDescribe: [{\n                type: Input\n            }], element: [] } });\n\nclass TuiHintHost extends TuiRectAccessor {\n    constructor() {\n        super(...arguments);\n        this.type = 'hint';\n    }\n    getClientRect() {\n        return this.tuiHintHost?.getBoundingClientRect() || EMPTY_CLIENT_RECT;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintHost, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintHost, isStandalone: true, selector: \"[tuiHint][tuiHintHost]\", inputs: { tuiHintHost: \"tuiHintHost\" }, providers: [tuiAsRectAccessor(TuiHintHost)], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintHost, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiHint][tuiHintHost]',\n                    providers: [tuiAsRectAccessor(TuiHintHost)],\n                }]\n        }], propDecorators: { tuiHintHost: [{\n                type: Input\n            }] } });\n\nclass TuiHintManual extends TuiDriver {\n    constructor() {\n        super((subscriber) => this.stream$.subscribe(subscriber));\n        this.hover = inject(TuiHintHover);\n        this.stream$ = new BehaviorSubject(false);\n        this.tuiHintManual = false;\n        this.type = 'hint';\n        this.hover.enabled = false;\n    }\n    ngOnChanges() {\n        this.stream$.next(!!this.tuiHintManual);\n        this.hover.enabled = this.tuiHintManual === null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintManual, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintManual, isStandalone: true, selector: \"[tuiHint][tuiHintManual]\", inputs: { tuiHintManual: \"tuiHintManual\" }, providers: [tuiAsDriver(TuiHintManual)], usesInheritance: true, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintManual, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiHint][tuiHintManual]',\n                    providers: [tuiAsDriver(TuiHintManual)],\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { tuiHintManual: [{\n                type: Input\n            }] } });\n\nclass TuiHintOverflow {\n    constructor() {\n        this.hint = inject(TuiHintDirective);\n        this.tuiHintOverflow = '';\n    }\n    onMouseEnter({ scrollWidth, clientWidth, textContent }) {\n        this.hint.tuiHint =\n            scrollWidth > clientWidth && this.tuiHintOverflow !== null\n                ? this.tuiHintOverflow || textContent\n                : '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintOverflow, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHintOverflow, isStandalone: true, selector: \"[tuiHintOverflow]\", inputs: { tuiHintOverflow: \"tuiHintOverflow\" }, host: { listeners: { \"mouseenter\": \"onMouseEnter($event.currentTarget)\" } }, hostDirectives: [{ directive: TuiHintDirective, inputs: [\"tuiHintAppearance\", \"tuiHintAppearance\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHintOverflow, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiHintOverflow]',\n                    hostDirectives: [\n                        {\n                            directive: TuiHintDirective,\n                            inputs: ['tuiHintAppearance'],\n                        },\n                    ],\n                    host: {\n                        '(mouseenter)': 'onMouseEnter($event.currentTarget)',\n                    },\n                }]\n        }], propDecorators: { tuiHintOverflow: [{\n                type: Input\n            }] } });\n\nconst TuiHint = [\n    TuiHintComponent,\n    TuiHintDirective,\n    TuiHintOptionsDirective,\n    TuiHintUnstyled,\n    TuiHintDriver,\n    TuiHintPosition,\n    TuiHintHover,\n    TuiHintOverflow,\n    TuiHintDescribe,\n    TuiHintHost,\n    TuiHintManual,\n    TuiHintPointer,\n];\n\nclass TuiHints {\n    constructor() {\n        this.hints$ = inject(TuiHintService);\n        this.destroyRef = inject(DestroyRef);\n        this.cdr = inject(ChangeDetectorRef);\n        this.hints = [];\n    }\n    ngOnInit() {\n        // Due to this view being parallel to app content, `markForCheck` from `async` pipe\n        // can happen after view was checked, so calling `detectChanges` instead\n        this.hints$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((hints) => {\n            this.hints = hints;\n            this.cdr.detectChanges();\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHints, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHints, isStandalone: true, selector: \"tui-hints\", host: { attributes: { \"aria-live\": \"polite\" } }, ngImport: i0, template: \"<div\\n    *ngFor=\\\"let hint of hints\\\"\\n    role=\\\"tooltip\\\"\\n    tuiAnimatedParent\\n    [tuiActiveZoneParent]=\\\"hint.activeZone || null\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"hint.component; context: {$implicit: hint}\\\" />\\n</div>\\n\", styles: [\":host{position:fixed;top:0;left:0;inline-size:100%;block-size:100%;block-size:0}\\n\"], dependencies: [{ kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: TuiActiveZone, selector: \"[tuiActiveZone]:not(ng-container), [tuiActiveZoneChange]:not(ng-container), [tuiActiveZoneParent]:not(ng-container)\", inputs: [\"tuiActiveZoneParent\"], outputs: [\"tuiActiveZoneChange\"], exportAs: [\"tuiActiveZone\"] }, { kind: \"directive\", type: TuiAnimatedParent, selector: \"[tuiAnimatedParent]\" }], changeDetection: i0.ChangeDetectionStrategy.Default }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHints, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-hints', imports: [NgForOf, PolymorpheusOutlet, TuiActiveZone, TuiAnimatedParent], changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'aria-live': 'polite',\n                    }, template: \"<div\\n    *ngFor=\\\"let hint of hints\\\"\\n    role=\\\"tooltip\\\"\\n    tuiAnimatedParent\\n    [tuiActiveZoneParent]=\\\"hint.activeZone || null\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"hint.component; context: {$implicit: hint}\\\" />\\n</div>\\n\", styles: [\":host{position:fixed;top:0;left:0;inline-size:100%;block-size:100%;block-size:0}\\n\"] }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_HINT_COMPONENT, TUI_HINT_DEFAULT_OPTIONS, TUI_HINT_DIRECTIONS, TUI_HINT_OPTIONS, TUI_HINT_PROVIDERS, TuiHint, TuiHintBaseComponent, TuiHintComponent, TuiHintDescribe, TuiHintDirective, TuiHintDriver, TuiHintHost, TuiHintHover, TuiHintManual, TuiHintOptionsDirective, TuiHintOverflow, TuiHintPointer, TuiHintPosition, TuiHintService, TuiHintUnstyled, TuiHintUnstyledComponent, TuiHints, tuiHintOptionsProvider };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,YAAY,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,WAAW,EAAEC,MAAM,EAAEC,UAAU,EAAEC,iBAAiB,QAAQ,eAAe;AAC9N,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,OAAO,KAAKC,EAAE,MAAM,mCAAmC;AACvD,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,mCAAmC;AAClF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,gBAAgB,EAAEC,oBAAoB,QAAQ,yBAAyB;AAChF,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,yBAAyB,EAAEC,cAAc,EAAEC,UAAU,EAAEC,OAAO,EAAEC,KAAK,EAAEC,YAAY,QAAQ,mCAAmC;AACvI,SAASC,kBAAkB,EAAEC,SAAS,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,sBAAsB,EAAEC,kBAAkB,QAAQ,wBAAwB;AAC3N,SAASC,kBAAkB,EAAEC,wBAAwB,QAAQ,yBAAyB;AACtF,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,kBAAkB,EAAEC,aAAa,QAAQ,sBAAsB;AACxE,SAASC,qBAAqB,EAAEC,aAAa,EAAEC,kBAAkB,QAAQ,wBAAwB;AACjG,SAASC,eAAe,EAAEC,OAAO,EAAEC,KAAK,EAAEC,SAAS,EAAEC,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,IAAI,QAAQ,MAAM;AAC/L,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,QAAQ,EAAEC,OAAO,QAAQ,iBAAiB;AACnD,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,gBAAgB,QAAQ,2BAA2B;AAC/G,SAASC,kBAAkB,QAAQ,2BAA2B;;AAE9D;AACA;AACA;AAFA,SAAAC,iDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAoBqG/E,EAAE,CAAAiF,kBAAA,EA6V8J,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,iCAAAJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7VjK/E,EAAE,CAAAoF,SAAA,aAidxF,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,OAAA,GAAAL,GAAA,CAAAM,kBAAA;IAjdqFtF,EAAE,CAAAuF,UAAA,cAAAF,OAAA,EAAFrF,EAAE,CAAAwF,cAgd1E,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,SAAAE,uCAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhduE/E,EAAE,CAAAiF,kBAAA,EAsnBgW,CAAC;EAAA;AAAA;AAAA,SAAAY,wBAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtnBnW/E,EAAE,CAAA8F,cAAA,YAsnBuQ,CAAC;IAtnB1Q9F,EAAE,CAAA+F,UAAA,IAAAH,sCAAA,yBAsnBgW,CAAC;IAtnBnW5F,EAAE,CAAAgG,YAAA,CAsnBwW,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAkB,OAAA,GAAAjB,GAAA,CAAAW,SAAA;IAtnB3W3F,EAAE,CAAAuF,UAAA,wBAAAU,OAAA,CAAAC,UAAA,QAsnBoQ,CAAC;IAtnBvQlG,EAAE,CAAAmG,SAAA,CAsnBiU,CAAC;IAtnBpUnG,EAAE,CAAAuF,UAAA,uBAAAU,OAAA,CAAAG,SAsnBiU,CAAC,8BAtnBpUpG,EAAE,CAAAqG,eAAA,IAAAZ,GAAA,EAAAQ,OAAA,CAsnB2V,CAAC;EAAA;AAAA;AAvoBnc,MAAMK,kBAAkB,GAAG1E,yBAAyB,CAAC,MAAM2E,gBAAgB,CAAC;;AAE5E;AACA;AACA;AACA,MAAMC,cAAc,SAASpD,eAAe,CAAC;EACzCqD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,EAAE,CAAC;EACb;EACAC,GAAGA,CAACC,SAAS,EAAE;IACX,IAAI,CAACC,IAAI,CAAC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACH,SAAS,CAAC,CAAC;EAC3C;EACAI,MAAMA,CAACJ,SAAS,EAAE;IACd,IAAI,IAAI,CAACE,KAAK,CAACG,QAAQ,CAACL,SAAS,CAAC,EAAE;MAChC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACC,KAAK,CAACjD,MAAM,CAAEqD,IAAI,IAAKA,IAAI,KAAKN,SAAS,CAAC,CAAC;IAC9D;EACJ;EACA;IAAS,IAAI,CAACO,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFZ,cAAc;IAAA,CAAoD;EAAE;EAC/K;IAAS,IAAI,CAACa,KAAK,kBAD8ErH,EAAE,CAAAsH,kBAAA;MAAAC,KAAA,EACYf,cAAc;MAAAgB,OAAA,EAAdhB,cAAc,CAAAU,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAC1J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG1H,EAAE,CAAA2H,iBAAA,CAGXnB,cAAc,EAAc,CAAC;IAC7GoB,IAAI,EAAE3H,UAAU;IAChB4H,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAMK,aAAa,SAAS5F,kBAAkB,CAAC;EAC3CuE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGsB,SAAS,CAAC;IACnB,IAAI,CAACH,IAAI,GAAG,MAAM;EACtB;EACA;IAAS,IAAI,CAACV,IAAI;MAAA,IAAAc,0BAAA;MAAA,gBAAAC,sBAAAb,CAAA;QAAA,QAAAY,0BAAA,KAAAA,0BAAA,GAf+EhI,EAAE,CAAAkI,qBAAA,CAeQJ,aAAa,IAAAV,CAAA,IAAbU,aAAa;MAAA;IAAA,IAAqD;EAAE;EAC/K;IAAS,IAAI,CAACK,IAAI,kBAhB+EnI,EAAE,CAAAoI,iBAAA;MAAAR,IAAA,EAgBJE,aAAa;MAAAO,UAAA;MAAAC,QAAA,GAhBXtI,EAAE,CAAAuI,0BAAA;IAAA,EAgBqE;EAAE;AAC9K;AACA;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KAlBqG1H,EAAE,CAAA2H,iBAAA,CAkBXG,aAAa,EAAc,CAAC;IAC5GF,IAAI,EAAE1H,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCQ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMG,mBAAmB,GAAG,CACxB,aAAa,EACb,QAAQ,EACR,cAAc,EACd,UAAU,EACV,KAAK,EACL,WAAW,EACX,UAAU,EACV,MAAM,EACN,aAAa,EACb,WAAW,EACX,OAAO,EACP,cAAc,CACjB;AACD;AACA,MAAMC,wBAAwB,GAAG;EAC7BC,SAAS,EAAE,aAAa;EACxBC,SAAS,EAAE,GAAG;EACdC,SAAS,EAAE,GAAG;EACdC,UAAU,EAAE,EAAE;EACd;EACAC,IAAI,EAAE;AACV,CAAC;AACD;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGlH,cAAc,CAAC4G,wBAAwB,CAAC;AACjE,MAAMO,sBAAsB,GAAIC,QAAQ,KAAM;EAC1CC,OAAO,EAAEH,gBAAgB;EACzBI,IAAI,EAAE,CACF,CAAC,IAAIhJ,QAAQ,CAAC,CAAC,EAAE,IAAIC,IAAI,CAAC,CAAC,EAAEgJ,uBAAuB,CAAC,EACrD,CAAC,IAAIjJ,QAAQ,CAAC,CAAC,EAAE,IAAIE,QAAQ,CAAC,CAAC,EAAE0I,gBAAgB,CAAC,CACrD;EACDM,UAAU,EAAEtG,kBAAkB,CAACkG,QAAQ,EAAER,wBAAwB;AACrE,CAAC,CAAC;AACF;AACA;AACA;AACA,MAAMW,uBAAuB,CAAC;EAC1B3C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6C,OAAO,GAAGhJ,MAAM,CAACyI,gBAAgB,EAAE;MAAEQ,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC3D,IAAI,CAACb,SAAS,GAAG,IAAI,CAACY,OAAO,CAACZ,SAAS;IACvC,IAAI,CAACG,UAAU,GAAG,IAAI,CAACS,OAAO,CAACT,UAAU;IACzC,IAAI,CAACF,SAAS,GAAG,IAAI,CAACW,OAAO,CAACX,SAAS;IACvC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACU,OAAO,CAACV,SAAS;IACvC,IAAI,CAACE,IAAI,GAAG,IAAI,CAACQ,OAAO,CAACR,IAAI;IAC7B,IAAI,CAACU,OAAO,GAAG,IAAInG,OAAO,CAAC,CAAC;EAChC;EACAoG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,OAAO,CAAC5C,IAAI,CAAC,CAAC;EACvB;EACA;IAAS,IAAI,CAACM,IAAI,YAAAwC,gCAAAtC,CAAA;MAAA,YAAAA,CAAA,IAAyFgC,uBAAuB;IAAA,CAAmD;EAAE;EACvL;IAAS,IAAI,CAACjB,IAAI,kBA7E+EnI,EAAE,CAAAoI,iBAAA;MAAAR,IAAA,EA6EJwB,uBAAuB;MAAAO,SAAA;MAAAC,MAAA;QAAAC,OAAA,GA7ErB7J,EAAE,CAAA8J,YAAA,CAAAC,IAAA;QAAArB,SAAA,GAAF1I,EAAE,CAAA8J,YAAA,CAAAC,IAAA;QAAAlB,UAAA,GAAF7I,EAAE,CAAA8J,YAAA,CAAAC,IAAA;QAAApB,SAAA,GAAF3I,EAAE,CAAA8J,YAAA,CAAAC,IAAA;QAAAnB,SAAA,GAAF5I,EAAE,CAAA8J,YAAA,CAAAC,IAAA;MAAA;MAAA1B,UAAA;MAAAC,QAAA,GAAFtI,EAAE,CAAAgK,kBAAA,CA6EiU,CAAClI,UAAU,CAACiH,gBAAgB,EAAEK,uBAAuB,CAAC,CAAC,GA7E1XpJ,EAAE,CAAAiK,oBAAA;IAAA,EA6E8Z;EAAE;AACvgB;AACA;EAAA,QAAAvC,SAAA,oBAAAA,SAAA,KA/EqG1H,EAAE,CAAA2H,iBAAA,CA+EXyB,uBAAuB,EAAc,CAAC;IACtHxB,IAAI,EAAE1H,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCQ,UAAU,EAAE,IAAI;MAChB6B,QAAQ,EAAE,kBAAkB;MAC5BC,SAAS,EAAE,CAACrI,UAAU,CAACiH,gBAAgB,EAAEK,uBAAuB,CAAC;IACrE,CAAC;EACT,CAAC,CAAC,QAAkB;IAAES,OAAO,EAAE,CAAC;MACxBjC,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEa,SAAS,EAAE,CAAC;MACZd,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEgB,UAAU,EAAE,CAAC;MACbjB,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEc,SAAS,EAAE,CAAC;MACZf,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEe,SAAS,EAAE,CAAC;MACZhB,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMuC,YAAY,SAASjI,SAAS,CAAC;EACjCsE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAE4D,UAAU,IAAK,IAAI,CAACC,OAAO,CAACC,SAAS,CAACF,UAAU,CAAC,CAAC;IACzD,IAAI,CAACG,QAAQ,GAAGlK,MAAM,CAACkB,aAAa,CAAC;IACrC,IAAI,CAACiJ,EAAE,GAAGhJ,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACiJ,QAAQ,GAAGpK,MAAM,CAACiB,iBAAiB,CAAC;IACzC,IAAI,CAAC+H,OAAO,GAAGhJ,MAAM,CAACyI,gBAAgB,CAAC;IACvC,IAAI,CAAC4B,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,OAAO,GAAG,IAAIvH,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACiH,OAAO,GAAGhH,KAAK,CAAC,IAAI,CAACsH,OAAO,CAACC,IAAI,CAACtH,SAAS,CAAEoH,OAAO,IAAK,IAAI,CAACH,QAAQ,GACrEhH,EAAE,CAACmH,OAAO,CAAC,GACXnH,EAAE,CAACmH,OAAO,CAAC,CAACE,IAAI,CAACpH,KAAK,CAACkH,OAAO,GAAG,CAAC,GAAG,IAAI,CAACG,gBAAgB,CAAC,CAAC,CAAC,EAAEpH,SAAS,CAAC,IAAI,CAACgH,QAAQ,CAAC,EAAE/G,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC+G,QAAQ,CAACG,IAAI,CAACtH,SAAS,CAAEoH,OAAO,IAAK,IAAI,CAACH,QAAQ,GAC3JhH,EAAE,CAACmH,OAAO,CAAC,GACXnH,EAAE,CAACmH,OAAO,CAAC,CAACE,IAAI,CAACpH,KAAK,CAACkH,OAAO,GAAG,IAAI,CAACI,gBAAgB,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC,CAAC,EAAEpH,SAAS,CAAC,IAAI,CAACkH,OAAO,CAAC,EAAEjH,MAAM,CAAC,CAAC,CAAC,CAAC,CAACkH,IAAI,CAACjH,MAAM,CAAC,MAAM,IAAI,CAACoH,OAAO,CAAC,EAAEnH,GAAG,CAAEgD,KAAK,IAAKA,KAAK,KAC7K,IAAI,CAAC4D,EAAE,CAACQ,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAACjI,aAAa,CAAC,IAAI,CAACyH,EAAE,CAAC,CAAC,CAAC,EAAE3G,GAAG,CAAE6G,OAAO,IAAK;MACvF,IAAI,CAACA,OAAO,GAAGA,OAAO;IAC1B,CAAC,CAAC,CAAC;IACH,IAAI,CAACO,MAAM,GAAG5K,MAAM,CAAC8J,YAAY,EAAE;MAC/Be,QAAQ,EAAE,IAAI;MACd5B,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,CAACwB,gBAAgB,GAAG,IAAI,CAACzB,OAAO,CAACX,SAAS;IAC9C,IAAI,CAACmC,gBAAgB,GAAG,IAAI,CAACxB,OAAO,CAACV,SAAS;IAC9C,IAAI,CAAChB,IAAI,GAAG,MAAM;IAClB,IAAI,CAACoD,OAAO,GAAG,IAAI;EACvB;EACAI,MAAMA,CAACT,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO,EAAE;IAC5B,IAAI,CAACC,OAAO,CAAChE,IAAI,CAAC+D,OAAO,CAAC;IAC1B,IAAI,CAACO,MAAM,EAAEE,MAAM,CAACT,OAAO,CAAC;EAChC;EACAU,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACT,OAAO,CAAChE,IAAI,CAAC,KAAK,CAAC;EAC5B;EACA;IAAS,IAAI,CAACM,IAAI,YAAAoE,qBAAAlE,CAAA;MAAA,YAAAA,CAAA,IAAyFgD,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACjC,IAAI,kBAzI+EnI,EAAE,CAAAoI,iBAAA;MAAAR,IAAA,EAyIJwC,YAAY;MAAAR,MAAA;QAAAmB,gBAAA;QAAAD,gBAAA;MAAA;MAAAS,QAAA;MAAAlD,UAAA;MAAAC,QAAA,GAzIVtI,EAAE,CAAAgK,kBAAA,CAyIiI,CAAC5H,WAAW,CAACgI,YAAY,CAAC,EAAE7I,iBAAiB,CAAC,GAzIjLvB,EAAE,CAAAuI,0BAAA;IAAA,EAyImP;EAAE;AAC5V;AACA;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KA3IqG1H,EAAE,CAAA2H,iBAAA,CA2IXyC,YAAY,EAAc,CAAC;IAC3GxC,IAAI,EAAE1H,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCQ,UAAU,EAAE,IAAI;MAChB8B,SAAS,EAAE,CAAC/H,WAAW,CAACgI,YAAY,CAAC,EAAE7I,iBAAiB,CAAC;MACzDgK,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAER,gBAAgB,EAAE,CAAC;MAC7EnD,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEuK,gBAAgB,EAAE,CAAC;MACnBlD,IAAI,EAAErH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiL,KAAK,GAAG,CAAC;AACf,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,GAAG,GAAG,CAAC;AACb,MAAMC,IAAI,GAAG,CAAC;AACd,MAAMC,eAAe,SAASvJ,mBAAmB,CAAC;EAC9CoE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGsB,SAAS,CAAC;IACnB,IAAI,CAAC8D,MAAM,GAAGvL,MAAM,CAACkB,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC;IAC5C,IAAI,CAACsK,QAAQ,GAAGxL,MAAM,CAACwC,YAAY,CAAC;IACpC,IAAI,CAACiJ,QAAQ,GAAGzJ,mBAAmB,CAAC,MAAM,CAAC,CAAChC,MAAM,CAACiC,eAAe,CAAC,EAAEjC,MAAM,CAAC0L,gBAAgB,CAAC,CAAC;IAC9F,IAAI,CAACC,MAAM,GAAGzD,mBAAmB,CAAC0D,MAAM,CAAC,CAACC,GAAG,EAAEzD,SAAS,MAAM;MAAE,GAAGyD,GAAG;MAAE,CAACzD,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnG,IAAI,CAACA,SAAS,GAAGpI,MAAM,CAACyI,gBAAgB,CAAC,CAACL,SAAS;IACnD,IAAI,CAAC0D,eAAe,GAAG,IAAI5L,YAAY,CAAC,CAAC;IACzC,IAAI,CAACoH,IAAI,GAAG,MAAM;EACtB;EACAyE,aAAaA,CAAC3D,SAAS,EAAE;IACrB,IAAI,CAAC0D,eAAe,CAACE,IAAI,CAAC5D,SAAS,CAAC;EACxC;EACA6D,WAAWA,CAACC,IAAI,EAAE/B,EAAE,EAAE;IAClB,MAAMgC,KAAK,GAAGhC,EAAE,EAAEiC,WAAW,IAAIF,IAAI,CAACC,KAAK;IAC3C,MAAME,MAAM,GAAGlC,EAAE,EAAEmC,YAAY,IAAIJ,IAAI,CAACG,MAAM;IAC9C,MAAME,QAAQ,GAAG,IAAI,CAACd,QAAQ,CAACe,aAAa,CAAC,CAAC,IAAI3L,iBAAiB;IACnE,MAAM4L,UAAU,GAAGF,QAAQ,CAACG,IAAI,GAAGH,QAAQ,CAACJ,KAAK,GAAG,CAAC;IACrD,MAAMQ,SAAS,GAAGJ,QAAQ,CAACK,GAAG,GAAGL,QAAQ,CAACF,MAAM,GAAG,CAAC;IACpD,IAAI,CAACV,MAAM,CAAC,UAAU,CAAC,CAACP,GAAG,CAAC,GAAGmB,QAAQ,CAACK,GAAG,GAAGP,MAAM,GAAG,IAAI,CAACd,MAAM;IAClE,IAAI,CAACI,MAAM,CAAC,UAAU,CAAC,CAACN,IAAI,CAAC,GAAGoB,UAAU,GAAGN,KAAK,GAAGhB,YAAY;IACjE,IAAI,CAACQ,MAAM,CAACiB,GAAG,CAACxB,GAAG,CAAC,GAAG,IAAI,CAACO,MAAM,CAAC,UAAU,CAAC,CAACP,GAAG,CAAC;IACnD,IAAI,CAACO,MAAM,CAACiB,GAAG,CAACvB,IAAI,CAAC,GAAGoB,UAAU,GAAGN,KAAK,GAAG,CAAC;IAC9C,IAAI,CAACR,MAAM,CAAC,WAAW,CAAC,CAACP,GAAG,CAAC,GAAG,IAAI,CAACO,MAAM,CAAC,UAAU,CAAC,CAACP,GAAG,CAAC;IAC5D,IAAI,CAACO,MAAM,CAAC,WAAW,CAAC,CAACN,IAAI,CAAC,GAAGoB,UAAU,GAAGtB,YAAY;IAC1D,IAAI,CAACQ,MAAM,CAAC,aAAa,CAAC,CAACP,GAAG,CAAC,GAAGmB,QAAQ,CAACM,MAAM,GAAG,IAAI,CAACtB,MAAM;IAC/D,IAAI,CAACI,MAAM,CAAC,aAAa,CAAC,CAACN,IAAI,CAAC,GAAG,IAAI,CAACM,MAAM,CAAC,UAAU,CAAC,CAACN,IAAI,CAAC;IAChE,IAAI,CAACM,MAAM,CAACkB,MAAM,CAACzB,GAAG,CAAC,GAAG,IAAI,CAACO,MAAM,CAAC,aAAa,CAAC,CAACP,GAAG,CAAC;IACzD,IAAI,CAACO,MAAM,CAACkB,MAAM,CAACxB,IAAI,CAAC,GAAG,IAAI,CAACM,MAAM,CAACiB,GAAG,CAACvB,IAAI,CAAC;IAChD,IAAI,CAACM,MAAM,CAAC,cAAc,CAAC,CAACP,GAAG,CAAC,GAAG,IAAI,CAACO,MAAM,CAAC,aAAa,CAAC,CAACP,GAAG,CAAC;IAClE,IAAI,CAACO,MAAM,CAAC,cAAc,CAAC,CAACN,IAAI,CAAC,GAAG,IAAI,CAACM,MAAM,CAAC,WAAW,CAAC,CAACN,IAAI,CAAC;IAClE,IAAI,CAACM,MAAM,CAAC,UAAU,CAAC,CAACP,GAAG,CAAC,GAAGuB,SAAS,GAAGN,MAAM,GAAGlB,YAAY;IAChE,IAAI,CAACQ,MAAM,CAAC,UAAU,CAAC,CAACN,IAAI,CAAC,GAAGkB,QAAQ,CAACG,IAAI,GAAGP,KAAK,GAAG,IAAI,CAACZ,MAAM;IACnE,IAAI,CAACI,MAAM,CAACe,IAAI,CAACtB,GAAG,CAAC,GAAGuB,SAAS,GAAGN,MAAM,GAAG,CAAC;IAC9C,IAAI,CAACV,MAAM,CAACe,IAAI,CAACrB,IAAI,CAAC,GAAG,IAAI,CAACM,MAAM,CAAC,UAAU,CAAC,CAACN,IAAI,CAAC;IACtD,IAAI,CAACM,MAAM,CAAC,aAAa,CAAC,CAACP,GAAG,CAAC,GAAGuB,SAAS,GAAGxB,YAAY;IAC1D,IAAI,CAACQ,MAAM,CAAC,aAAa,CAAC,CAACN,IAAI,CAAC,GAAG,IAAI,CAACM,MAAM,CAAC,UAAU,CAAC,CAACN,IAAI,CAAC;IAChE,IAAI,CAACM,MAAM,CAAC,WAAW,CAAC,CAACP,GAAG,CAAC,GAAG,IAAI,CAACO,MAAM,CAAC,UAAU,CAAC,CAACP,GAAG,CAAC;IAC5D,IAAI,CAACO,MAAM,CAAC,WAAW,CAAC,CAACN,IAAI,CAAC,GAAGkB,QAAQ,CAACO,KAAK,GAAG,IAAI,CAACvB,MAAM;IAC7D,IAAI,CAACI,MAAM,CAACmB,KAAK,CAAC1B,GAAG,CAAC,GAAG,IAAI,CAACO,MAAM,CAACe,IAAI,CAACtB,GAAG,CAAC;IAC9C,IAAI,CAACO,MAAM,CAACmB,KAAK,CAACzB,IAAI,CAAC,GAAG,IAAI,CAACM,MAAM,CAAC,WAAW,CAAC,CAACN,IAAI,CAAC;IACxD,IAAI,CAACM,MAAM,CAAC,cAAc,CAAC,CAACP,GAAG,CAAC,GAAG,IAAI,CAACO,MAAM,CAAC,aAAa,CAAC,CAACP,GAAG,CAAC;IAClE,IAAI,CAACO,MAAM,CAAC,cAAc,CAAC,CAACN,IAAI,CAAC,GAAG,IAAI,CAACM,MAAM,CAAC,WAAW,CAAC,CAACN,IAAI,CAAC;IAClE,MAAM0B,kBAAkB,GAAGC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC7E,SAAS,CAAC,GAClD,IAAI,CAACA,SAAS,GACd,CAAC,IAAI,CAACA,SAAS,CAAC;IACtB,MAAM8E,gBAAgB,GAAGH,kBAAkB,CAACvG,MAAM,CAAC0B,mBAAmB,CAAC;IACvE,MAAME,SAAS,GAAG8E,gBAAgB,CAACC,IAAI,CAAE/E,SAAS,IAAK,IAAI,CAACgF,aAAa,CAAC,IAAI,CAACzB,MAAM,CAACvD,SAAS,CAAC,EAAE+D,KAAK,EAAEE,MAAM,CAAC,CAAC,IAAI,IAAI,CAACgB,QAAQ;IAClI,IAAI,CAACtB,aAAa,CAAC3D,SAAS,CAAC;IAC7B,OAAO,IAAI,CAACuD,MAAM,CAACvD,SAAS,CAAC;EACjC;EACA,IAAIiF,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC1B,MAAM,CAACiB,GAAG,CAACxB,GAAG,CAAC,GACvB,IAAI,CAACI,QAAQ,CAACgB,aAAa,CAAC,CAAC,CAACK,MAAM,GAAG,IAAI,CAAClB,MAAM,CAACkB,MAAM,CAACzB,GAAG,CAAC,GAC5D,KAAK,GACL,QAAQ;EAClB;EACAgC,aAAaA,CAAC,CAACR,GAAG,EAAEF,IAAI,CAAC,EAAEP,KAAK,EAAEE,MAAM,EAAE;IACtC,MAAMb,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACgB,aAAa,CAAC,CAAC;IAC9C,OAAQI,GAAG,GAAGpB,QAAQ,CAACoB,GAAG,GAAG1B,KAAK,IAC9BwB,IAAI,GAAGlB,QAAQ,CAACkB,IAAI,GAAGxB,KAAK,IAC5B0B,GAAG,GAAGP,MAAM,GAAGb,QAAQ,CAACqB,MAAM,GAAG3B,KAAK,IACtCwB,IAAI,GAAGP,KAAK,GAAGX,QAAQ,CAACsB,KAAK,GAAG5B,KAAK;EAC7C;EACA;IAAS,IAAI,CAACtE,IAAI;MAAA,IAAA0G,4BAAA;MAAA,gBAAAC,wBAAAzG,CAAA;QAAA,QAAAwG,4BAAA,KAAAA,4BAAA,GA7N+E5N,EAAE,CAAAkI,qBAAA,CA6NQ0D,eAAe,IAAAxE,CAAA,IAAfwE,eAAe;MAAA;IAAA,IAAqD;EAAE;EACjL;IAAS,IAAI,CAACzD,IAAI,kBA9N+EnI,EAAE,CAAAoI,iBAAA;MAAAR,IAAA,EA8NJgE,eAAe;MAAAhC,MAAA;QAAAlB,SAAA,GA9Nb1I,EAAE,CAAA8J,YAAA,CAAAC,IAAA;MAAA;MAAA+D,OAAA;QAAA1B,eAAA;MAAA;MAAA/D,UAAA;MAAAC,QAAA,GAAFtI,EAAE,CAAAuI,0BAAA;IAAA,EA8NyL;EAAE;AAClS;AACAxI,UAAU,CAAC,CACPgC,OAAO,CACV,EAAE6J,eAAe,CAACmC,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC;AACpD;EAAA,QAAArG,SAAA,oBAAAA,SAAA,KAnOqG1H,EAAE,CAAA2H,iBAAA,CAmOXiE,eAAe,EAAc,CAAC;IAC9GhE,IAAI,EAAE1H,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCQ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEK,SAAS,EAAE,CAAC;MAC1Bd,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEuE,eAAe,EAAE,CAAC;MAClBxE,IAAI,EAAEnH,MAAM;MACZoH,IAAI,EAAE,CAAC,wBAAwB;IACnC,CAAC,CAAC;IAAEwE,aAAa,EAAE;EAAG,CAAC;AAAA;AAEnC,MAAML,gBAAgB,CAAC;EACnBvF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuH,OAAO,GAAG1N,MAAM,CAACkG,cAAc,CAAC;IACrC,IAAI,CAACqC,UAAU,GAAGvI,MAAM,CAACyI,gBAAgB,CAAC,CAACF,UAAU;IACrD,IAAI,CAACgB,OAAO,GAAGnJ,MAAM,CAAC,IAAI,CAAC;IAC3B,IAAI,CAAC0F,SAAS,GAAG9F,MAAM,CAAE2C,qBAAsB,CAAC;IAChD,IAAI,CAACwH,EAAE,GAAGhJ,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACyE,UAAU,GAAG5F,MAAM,CAACgE,aAAa,EAAE;MAAE6G,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC3D,IAAI,CAACvD,IAAI,GAAG,MAAM;EACtB;EACA,IAAIqG,OAAOA,CAACpE,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,CAACqE,GAAG,CAACrE,OAAO,CAAC;IACzB,IAAI,CAACA,OAAO,EAAE;MACV,IAAI,CAACuB,MAAM,CAAC,KAAK,CAAC;IACtB;EACJ;EACA+C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC/C,MAAM,CAAC,KAAK,CAAC;EACtB;EACA0B,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACrC,EAAE,CAAC2D,qBAAqB,CAAC,CAAC;EAC1C;EACAhD,MAAMA,CAACiD,IAAI,EAAE;IACT,IAAIA,IAAI,IAAI,IAAI,CAACxE,OAAO,CAAC,CAAC,EAAE;MACxB,IAAI,CAACmE,OAAO,CAACtH,GAAG,CAAC,IAAI,CAAC;IAC1B,CAAC,MACI;MACD,IAAI,CAACsH,OAAO,CAACjH,MAAM,CAAC,IAAI,CAAC;IAC7B;EACJ;EACA;IAAS,IAAI,CAACG,IAAI,YAAAoH,yBAAAlH,CAAA;MAAA,YAAAA,CAAA,IAAyF4E,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAAC7D,IAAI,kBA/Q+EnI,EAAE,CAAAoI,iBAAA;MAAAR,IAAA,EA+QJoE,gBAAgB;MAAArC,SAAA;MAAAC,MAAA;QAAA2E,OAAA,GA/QdvO,EAAE,CAAA8J,YAAA,CAAAC,IAAA;QAAAlB,UAAA,GAAF7I,EAAE,CAAA8J,YAAA,CAAAC,IAAA;QAAAkE,OAAA;MAAA;MAAA5F,UAAA;MAAAC,QAAA,GAAFtI,EAAE,CAAAgK,kBAAA,CA+QgO,CAC3TxH,iBAAiB,CAACwJ,gBAAgB,CAAC,EACnCvJ,YAAY,CAACuJ,gBAAgB,CAAC,EAC9B;QACI9C,OAAO,EAAEjG,qBAAqB;QAC9BkG,IAAI,EAAE,CAAC7C,kBAAkB,EAAE3F,QAAQ,CAAC;QACpC6N,QAAQ,EAAEvL;MACd,CAAC,CACJ,GAvR4FjD,EAAE,CAAAyO,uBAAA,EAuR9D3G,aAAa;QAAAnB,SAAA,EAAiByD,YAAY;QAAAR,MAAA;MAAA;QAAAjD,SAAA,EAA2GiF,eAAe;QAAAhC,MAAA;QAAAkE,OAAA;MAAA;IAAA,EAAqI;EAAE;AACpV;AACA;EAAA,QAAApG,SAAA,oBAAAA,SAAA,KAzRqG1H,EAAE,CAAA2H,iBAAA,CAyRXqE,gBAAgB,EAAc,CAAC;IAC/GpE,IAAI,EAAE1H,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCQ,UAAU,EAAE,IAAI;MAChB6B,QAAQ,EAAE,8CAA8C;MACxDC,SAAS,EAAE,CACP3H,iBAAiB,CAACwJ,gBAAgB,CAAC,EACnCvJ,YAAY,CAACuJ,gBAAgB,CAAC,EAC9B;QACI9C,OAAO,EAAEjG,qBAAqB;QAC9BkG,IAAI,EAAE,CAAC7C,kBAAkB,EAAE3F,QAAQ,CAAC;QACpC6N,QAAQ,EAAEvL;MACd,CAAC,CACJ;MACDyL,cAAc,EAAE,CACZ5G,aAAa,EACb;QACInB,SAAS,EAAEyD,YAAY;QACvBR,MAAM,EAAE,CAAC,kBAAkB,EAAE,kBAAkB;MACnD,CAAC,EACD;QACIjD,SAAS,EAAEiF,eAAe;QAC1BhC,MAAM,EAAE,CAAC,kBAAkB,CAAC;QAC5BkE,OAAO,EAAE,CAAC,wBAAwB;MACtC,CAAC;IAET,CAAC;EACT,CAAC,CAAC,QAAkB;IAAES,OAAO,EAAE,CAAC;MACxB3G,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEgB,UAAU,EAAE,CAAC;MACbjB,IAAI,EAAErH,KAAK;MACXsH,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEoG,OAAO,EAAE,CAAC;MACVrG,IAAI,EAAErH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMoO,cAAc,SAASvE,YAAY,CAAC;EACtC3D,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGsB,SAAS,CAAC;IACnB,IAAI,CAAC6G,WAAW,GAAGzN,iBAAiB;EACxC;EACA2L,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC8B,WAAW;EAC3B;EACAC,MAAMA,CAAC;IAAEC,OAAO;IAAEC;EAAQ,CAAC,EAAE;IACzB,IAAI,CAACH,WAAW,GAAGlN,oBAAoB,CAACoN,OAAO,EAAEC,OAAO,CAAC;EAC7D;EACA;IAAS,IAAI,CAAC7H,IAAI;MAAA,IAAA8H,2BAAA;MAAA,gBAAAC,uBAAA7H,CAAA;QAAA,QAAA4H,2BAAA,KAAAA,2BAAA,GAzU+EhP,EAAE,CAAAkI,qBAAA,CAyUQyG,cAAc,IAAAvH,CAAA,IAAduH,cAAc;MAAA;IAAA,IAAqD;EAAE;EAChL;IAAS,IAAI,CAACxG,IAAI,kBA1U+EnI,EAAE,CAAAoI,iBAAA;MAAAR,IAAA,EA0UJ+G,cAAc;MAAAhF,SAAA;MAAAuF,YAAA,WAAAC,4BAAApK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1UZ/E,EAAE,CAAAoP,UAAA,gCAAAC,qDAAAC,MAAA;YAAA,OA0UJtK,GAAA,CAAA6J,MAAA,CAAAS,MAAa,CAAC;UAAA,CAAD,CAAC;QAAA;MAAA;MAAAjH,UAAA;MAAAC,QAAA,GA1UZtI,EAAE,CAAAgK,kBAAA,CA0UmJ,CAACxH,iBAAiB,CAACmM,cAAc,CAAC,EAAEvM,WAAW,CAACuM,cAAc,CAAC,CAAC,GA1UrN3O,EAAE,CAAAuI,0BAAA;IAAA,EA0U2P;EAAE;AACpW;AACA;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KA5UqG1H,EAAE,CAAA2H,iBAAA,CA4UXgH,cAAc,EAAc,CAAC;IAC7G/G,IAAI,EAAE1H,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCQ,UAAU,EAAE,IAAI;MAChB6B,QAAQ,EAAE,2BAA2B;MACrCC,SAAS,EAAE,CAAC3H,iBAAiB,CAACmM,cAAc,CAAC,EAAEvM,WAAW,CAACuM,cAAc,CAAC,CAAC;MAC3EY,IAAI,EAAE;QACF,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMC,wBAAwB,CAAC;EAC3B/I,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8H,OAAO,GAAGrL,aAAa,CAAC,CAAC;EAClC;EACA;IAAS,IAAI,CAACgE,IAAI,YAAAuI,iCAAArI,CAAA;MAAA,YAAAA,CAAA,IAAyFoI,wBAAwB;IAAA,CAAmD;EAAE;EACxL;IAAS,IAAI,CAACE,IAAI,kBA7V+E1P,EAAE,CAAA2P,iBAAA;MAAA/H,IAAA,EA6VJ4H,wBAAwB;MAAA7F,SAAA;MAAAtB,UAAA;MAAAC,QAAA,GA7VtBtI,EAAE,CAAA4P,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAlL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/E,EAAE,CAAA+F,UAAA,IAAAjB,gDAAA,yBA6V8J,CAAC;QAAA;QAAA,IAAAC,EAAA;UA7VjK/E,EAAE,CAAAuF,UAAA,uBAAAP,GAAA,CAAAuJ,OAAA,CAAA5I,SAAA,CAAAkE,OAAA,EA6V0J,CAAC;QAAA;MAAA;MAAAqG,YAAA,GAAiE/M,kBAAkB;MAAAgN,aAAA;MAAAC,eAAA;IAAA,EAAyJ;EAAE;AAChf;AACA;EAAA,QAAA1I,SAAA,oBAAAA,SAAA,KA/VqG1H,EAAE,CAAA2H,iBAAA,CA+VX6H,wBAAwB,EAAc,CAAC;IACvH5H,IAAI,EAAEhH,SAAS;IACfiH,IAAI,EAAE,CAAC;MACCQ,UAAU,EAAE,IAAI;MAChBgI,OAAO,EAAE,CAAClN,kBAAkB,CAAC;MAC7B6M,QAAQ,EAAE,oEAAoE;MAC9EI,eAAe,EAAEvP,uBAAuB,CAACyP;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMC,eAAe,CAAC;EAClB9J,WAAWA,CAAA,EAAG;IACV,MAAMQ,IAAI,GAAG3G,MAAM,CAAE0L,gBAAiB,CAAC;IACvC/E,IAAI,CAACb,SAAS,GAAG,IAAInD,qBAAqB,CAACuM,wBAAwB,CAAC;IACpEvI,IAAI,CAAC4C,OAAO,CAACqE,GAAG,CAAC5N,MAAM,CAAEQ,WAAY,CAAC,CAAC;EAC3C;EACA;IAAS,IAAI,CAACoG,IAAI,YAAAsJ,wBAAApJ,CAAA;MAAA,YAAAA,CAAA,IAAyFmJ,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACpI,IAAI,kBA/W+EnI,EAAE,CAAAoI,iBAAA;MAAAR,IAAA,EA+WJ2I,eAAe;MAAA5G,SAAA;MAAAtB,UAAA;IAAA,EAAuE;EAAE;AAC3L;AACA;EAAA,QAAAX,SAAA,oBAAAA,SAAA,KAjXqG1H,EAAE,CAAA2H,iBAAA,CAiXX4I,eAAe,EAAc,CAAC;IAC9G3I,IAAI,EAAE1H,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCQ,UAAU,EAAE,IAAI;MAChB6B,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAMuG,kBAAkB,GAAG,CACvB7N,kBAAkB,EAClBrB,iBAAiB,EACjBmB,sBAAsB,CAAC,MAAM,EAAEkJ,eAAe,CAAC,EAC/CjJ,kBAAkB,CAAC,MAAM,EAAEqJ,gBAAgB,CAAC,CAC/C;AACD,MAAM0E,GAAG,GAAG,CAAC;AACb;AACA,MAAMC,oBAAoB,CAAC;EACvBlK,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgE,EAAE,GAAGhJ,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACmP,KAAK,GAAGtQ,MAAM,CAAC8J,YAAY,CAAC;IACjC,IAAI,CAACyG,GAAG,GAAGvQ,MAAM,CAACuC,wBAAwB,CAAC;IAC3C,IAAI,CAACiJ,QAAQ,GAAGxL,MAAM,CAACwC,YAAY,CAAC;IACpC,IAAI,CAACgO,OAAO,GAAGxQ,MAAM,CAACqO,cAAc,EAAE;MAAExD,QAAQ,EAAE;IAAK,CAAC,CAAC;IACzD,IAAI,CAACY,QAAQ,GAAGzL,MAAM,CAACiC,eAAe,CAAC;IACvC,IAAI,CAAC0E,IAAI,GAAG/D,aAAa,CAAC,CAAC,CAACyC,SAAS;IACrC,IAAI,CAAC6E,QAAQ,GAAGlK,MAAM,CAACkB,aAAa,CAAC;IACrC,IAAI,CAACqI,OAAO,GAAG,IAAI,CAAC5C,IAAI,CAACb,SAAS,CAACA,SAAS,KAAKoJ,wBAAwB,GACnE9O,MAAM,CAAC,EAAE,CAAC,GACV,IAAI,CAACuG,IAAI,CAAC4C,OAAO;IACvB,IAAI,CAAChB,UAAU,GAAG,IAAI,CAAC5B,IAAI,CAAC4B,UAAU,IAClC,IAAI,CAAC5B,IAAI,CAACwD,EAAE,CAACsG,OAAO,CAAC,YAAY,CAAC,EAAEC,YAAY,CAAC,UAAU,CAAC;IAChE1Q,MAAM,CAACsC,kBAAkB,CAAC,CACrBiI,IAAI,CAAC9G,SAAS,CAAC,MAAM,IAAI,CAACkD,IAAI,CAACwD,EAAE,CAACwG,WAAW,CAAC,EAAEpN,GAAG,CAAEqN,KAAK,IAAK,IAAI,CAACL,GAAG,CAACM,OAAO,CAACD,KAAK,CAAC,CAAC,EAAEhQ,kBAAkB,CAAC,CAAC,CAAC,CAC9GqJ,SAAS,CAAC;MACX3D,IAAI,EAAEA,CAAC,CAACsG,GAAG,EAAEF,IAAI,CAAC,KAAK,IAAI,CAACoE,MAAM,CAAClE,GAAG,EAAEF,IAAI,CAAC;MAC7CqE,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACT,KAAK,CAACvF,KAAK,CAAC;IACrC,CAAC,CAAC;IACF/K,MAAM,CAACiB,iBAAiB,CAAC,CACpBsJ,IAAI,CAAC3J,kBAAkB,CAAC,CAAC,CAAC,CAC1BqJ,SAAS,CAAEqG,KAAK,IAAK,IAAI,CAACA,KAAK,CAACxF,MAAM,CAACwF,KAAK,CAAC,CAAC;EACvD;EACAU,OAAOA,CAACC,MAAM,EAAE;IACZ,IAAK,CAACA,MAAM,CAACR,OAAO,CAAC,IAAI,CAACtG,EAAE,CAAC+G,OAAO,CAAC,IAAI,CAAC,IAAI,CAACvK,IAAI,CAACwD,EAAE,CAACgH,QAAQ,CAACF,MAAM,CAAC,IACnEvO,aAAa,CAAC,IAAI,CAACiE,IAAI,CAACwD,EAAE,CAAC,EAAE;MAC7B,IAAI,CAACmG,KAAK,CAACxF,MAAM,CAAC,KAAK,CAAC;IAC5B;EACJ;EACAsG,KAAKA,CAACxE,GAAG,EAAEF,IAAI,EAAE2E,OAAO,EAAEC,QAAQ,EAAE;IAChC,IAAI,CAACnH,EAAE,CAACoH,KAAK,CAAC3E,GAAG,GAAGA,GAAG;IACvB,IAAI,CAACzC,EAAE,CAACoH,KAAK,CAAC7E,IAAI,GAAGA,IAAI;IACzB,IAAI,CAACvC,EAAE,CAACoH,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,GAAGH,OAAO,GAAG,CAAC;IACnD,IAAI,CAAClH,EAAE,CAACoH,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE,GAAGF,QAAQ,GAAG,CAAC;IACrD,IAAI,CAACnH,EAAE,CAACoH,KAAK,CAACC,WAAW,CAAC,YAAY,EAAE,CAACF,QAAQ,IAAIG,IAAI,CAACC,IAAI,CAACJ,QAAQ,CAAC,KAAK,GAAG,GAAG,OAAO,GAAG,MAAM,CAAC;EACxG;EACAR,MAAMA,CAAClE,GAAG,EAAEF,IAAI,EAAE;IACd,MAAM;MAAEJ,YAAY;MAAEF;IAAY,CAAC,GAAG,IAAI,CAACjC,EAAE;IAC7C,MAAM+B,IAAI,GAAG,IAAI,CAACT,QAAQ,CAACe,aAAa,CAAC,CAAC;IAC1C,IAAIN,IAAI,KAAKrL,iBAAiB,IAAI,CAACyL,YAAY,IAAI,CAACF,WAAW,EAAE;MAC7D;IACJ;IACA,MAAMZ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACgB,aAAa,CAAC,CAAC;IAC9C,MAAMmF,QAAQ,GAAGtQ,QAAQ,CAACoQ,IAAI,CAACG,GAAG,CAACxB,GAAG,EAAE1D,IAAI,CAAC,EAAElB,QAAQ,CAACkB,IAAI,GAAG0D,GAAG,EAAEqB,IAAI,CAACG,GAAG,CAACxB,GAAG,EAAE5E,QAAQ,CAACW,KAAK,GAAGX,QAAQ,CAACkB,IAAI,GAAGN,WAAW,GAAGgE,GAAG,CAAC,CAAC;IACtI,MAAM,CAACiB,OAAO,EAAEC,QAAQ,CAAC,GAAG,IAAI,CAACf,GAAG,CAACM,OAAO,CAAC,CACzC3E,IAAI,CAACU,GAAG,GAAGV,IAAI,CAACG,MAAM,GAAG,CAAC,GAAGO,GAAG,EAChCV,IAAI,CAACQ,IAAI,GAAGR,IAAI,CAACC,KAAK,GAAG,CAAC,GAAGwF,QAAQ,CACxC,CAAC;IACF,IAAI,CAACP,KAAK,CAAC1P,KAAK,CAAC+P,IAAI,CAACI,KAAK,CAACjF,GAAG,CAAC,CAAC,EAAElL,KAAK,CAAC+P,IAAI,CAACI,KAAK,CAACF,QAAQ,CAAC,CAAC,EAAEF,IAAI,CAACI,KAAK,CAAExQ,QAAQ,CAACgQ,OAAO,EAAE,CAAC,EAAE/E,YAAY,CAAC,GAAGA,YAAY,GAAI,GAAG,CAAC,EAAEmF,IAAI,CAACI,KAAK,CAAExQ,QAAQ,CAACiQ,QAAQ,EAAE,CAAC,EAAElF,WAAW,CAAC,GAAGA,WAAW,GAAI,GAAG,CAAC,CAAC;EAChN;EACA;IAAS,IAAI,CAACxF,IAAI,YAAAkL,6BAAAhL,CAAA;MAAA,YAAAA,CAAA,IAAyFuJ,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACjB,IAAI,kBAtb+E1P,EAAE,CAAA2P,iBAAA;MAAA/H,IAAA,EAsbJ+I,oBAAoB;MAAAhH,SAAA;MAAA0I,QAAA;MAAAnD,YAAA,WAAAoD,kCAAAvN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtblB/E,EAAE,CAAAoP,UAAA,mBAAAmD,8CAAAjD,MAAA;YAAA,OAsbJtK,GAAA,CAAAsM,OAAA,CAAAhC,MAAA,CAAAiC,MAAqB,CAAC;UAAA,UAtbpBvR,EAAE,CAAAwS,iBAsbe,CAAC;QAAA;QAAA,IAAAzN,EAAA;UAtblB/E,EAAE,CAAAyS,WAAA,oBAAAzN,GAAA,CAAA6D,UAAA,cAAA7D,GAAA,CAAA6D,UAAA,KAsbW,MAAM,GAAG,OAAO,GAAG,IAAI;UAtbpC7I,EAAE,CAAA0S,WAAA,iBAAA1N,GAAA,CAAA8L,OAsbe,CAAC,YAAA9L,GAAA,CAAAwF,QAAD,CAAC;QAAA;MAAA;MAAAnC,UAAA;MAAAC,QAAA,GAtblBtI,EAAE,CAAA4P,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAA2C,8BAAA5N,EAAA,EAAAC,GAAA;MAAAmL,aAAA;MAAAC,eAAA;IAAA,EAsbyZ;EAAE;AAClgB;AACArQ,UAAU,CAAC,CACPgC,OAAO,CACV,EAAE4O,oBAAoB,CAAC5C,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC;AACjD;EAAA,QAAArG,SAAA,oBAAAA,SAAA,KA3bqG1H,EAAE,CAAA2H,iBAAA,CA2bXgJ,oBAAoB,EAAc,CAAC;IACnH/I,IAAI,EAAEhH,SAAS;IACfiH,IAAI,EAAE,CAAC;MACCQ,UAAU,EAAE,IAAI;MAChB2H,QAAQ,EAAE,EAAE;MACZI,eAAe,EAAEvP,uBAAuB,CAACyP,MAAM;MAC/Cf,IAAI,EAAE;QACF,sBAAsB,EAAE,SAAS;QACjC,iBAAiB,EAAE,UAAU;QAC7B,wBAAwB,EAAE,YAAY;QACtC,iBAAiB,EAAE,wCAAwC;QAC3D,kBAAkB,EAAE;MACxB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEmC,KAAK,EAAE;EAAG,CAAC;AAAA;AACrF,MAAMnL,gBAAgB,SAASoK,oBAAoB,CAAC;EAChD;IAAS,IAAI,CAACzJ,IAAI;MAAA,IAAA0L,6BAAA;MAAA,gBAAAC,yBAAAzL,CAAA;QAAA,QAAAwL,6BAAA,KAAAA,6BAAA,GA3c+E5S,EAAE,CAAAkI,qBAAA,CA2cQ3B,gBAAgB,IAAAa,CAAA,IAAhBb,gBAAgB;MAAA;IAAA,IAAqD;EAAE;EAClL;IAAS,IAAI,CAACmJ,IAAI,kBA5c+E1P,EAAE,CAAA2P,iBAAA;MAAA/H,IAAA,EA4cJrB,gBAAgB;MAAAoD,SAAA;MAAAtB,UAAA;MAAAC,QAAA,GA5cdtI,EAAE,CAAAgK,kBAAA,CA4cmEyG,kBAAkB,GA5cvFzQ,EAAE,CAAAyO,uBAAA,EA4c4IrN,EAAE,CAACC,WAAW,IA5c5JrB,EAAE,CAAAuI,0BAAA,EAAFvI,EAAE,CAAA4P,mBAAA;MAAAkD,kBAAA,EAAA5N,GAAA;MAAA2K,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA+C,0BAAAhO,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/E,EAAE,CAAAgT,eAAA;UAAFhT,EAAE,CAAAiT,YAAA,EA6clF,CAAC;UA7c+EjT,EAAE,CAAA+F,UAAA,IAAAZ,gCAAA,iBAid/F,CAAC;QAAA;QAAA,IAAAJ,EAAA;UAjd4F/E,EAAE,CAAAmG,SAAA,CA+c7D,CAAC;UA/c0DnG,EAAE,CAAAuF,UAAA,uBAAAP,GAAA,CAAA6E,OAAA,EA+c7D,CAAC,8BAAA7E,GAAA,CAAAiC,IAAA,CAAAsH,OAA6B,CAAC;QAAA;MAAA;MAAA2B,YAAA,GAG8vI/M,kBAAkB;MAAA+P,MAAA;MAAA9C,eAAA;IAAA,EAAyJ;EAAE;AACp/I;AACA;EAAA,QAAA1I,SAAA,oBAAAA,SAAA,KApdqG1H,EAAE,CAAA2H,iBAAA,CAodXpB,gBAAgB,EAAc,CAAC;IAC/GqB,IAAI,EAAEhH,SAAS;IACfiH,IAAI,EAAE,CAAC;MAAEQ,UAAU,EAAE,IAAI;MAAE6B,QAAQ,EAAE,UAAU;MAAEmG,OAAO,EAAE,CAAClN,kBAAkB,CAAC;MAAE6M,QAAQ,EAAE;AACtG;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEI,eAAe,EAAEvP,uBAAuB,CAACyP,MAAM;MAAEnG,SAAS,EAAEsG,kBAAkB;MAAE/B,cAAc,EAAE,CAACrN,WAAW,CAAC;MAAE6R,MAAM,EAAE,CAAC,0vIAA0vI;IAAE,CAAC;EACp3I,CAAC,CAAC;AAAA;AAEV,MAAMC,eAAe,SAAShR,SAAS,CAAC;EACpCsE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAE4D,UAAU,IAAK,IAAI,CAACC,OAAO,CAACC,SAAS,CAACF,UAAU,CAAC,CAAC;IACzD,IAAI,CAAC+I,GAAG,GAAG9S,MAAM,CAACiE,QAAQ,CAAC;IAC3B,IAAI,CAACkG,EAAE,GAAGhJ,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAAC4R,IAAI,GAAG/S,MAAM,CAACS,MAAM,CAAC;IAC1B,IAAI,CAACuS,GAAG,GAAG,IAAIlQ,eAAe,CAAC,EAAE,CAAC;IAClC,IAAI,CAACkH,OAAO,GAAG,IAAI,CAACgJ,GAAG,CAACzI,IAAI,CAAC7G,oBAAoB,CAAC,CAAC,EAAES,QAAQ,CAAC,MAAMR,SAAS,CAAC,IAAI,CAACmP,GAAG,EAAE,SAAS,EAAE;MAAEG,OAAO,EAAE;IAAK,CAAC,CAAC,EAAEtR,YAAY,CAAC,EAAEsB,SAAS,CAAC,MAAM,IAAI,CAACiQ,OAAO,GAC5JhQ,EAAE,CAAC,KAAK,CAAC,GACTF,KAAK,CAACoB,iBAAiB,CAAC,IAAI,CAAC0O,GAAG,EAAE,OAAO,CAAC,EAAE1O,iBAAiB,CAAC,IAAI,CAAC+O,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC5I,IAAI,CAAChH,GAAG,CAAC,MAAM,IAAI,CAAC2P,OAAO,CAAC,CAAC,CAAC,EAAEtP,QAAQ,CAAEyG,OAAO,IAAKA,OAAO,GAAGxG,KAAK,CAAC,IAAI,EAAEQ,oBAAoB,CAAC,IAAI,CAAC0O,IAAI,CAAC,CAAC,GAAG7P,EAAE,CAAC,IAAI,CAAC,CAAC,EAAEY,SAAS,CAAC,KAAK,CAAC,EAAEJ,oBAAoB,CAAC,CAAC,EAAEK,IAAI,CAAC,CAAC,CAAC,EAAEO,gBAAgB,CAAC,CAAC,CAAC;IAC3R,IAAI,CAACgD,IAAI,GAAG,MAAM;EACtB;EACA,IAAI8L,eAAeA,CAACC,EAAE,EAAE;IACpB,IAAI,CAACL,GAAG,CAAC1M,IAAI,CAAC+M,EAAE,IAAI,EAAE,CAAC;EAC3B;EACA,IAAIF,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACL,GAAG,CAACQ,cAAc,CAAC,IAAI,CAACN,GAAG,CAACzM,KAAK,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC4D,EAAE;EACnE;EACA,IAAI+I,OAAOA,CAAA,EAAG;IACV,OAAO3O,kBAAkB,CAAC,IAAI,CAAC4O,OAAO,CAAC;EAC3C;EACA;IAAS,IAAI,CAACvM,IAAI,YAAA2M,wBAAAzM,CAAA;MAAA,YAAAA,CAAA,IAAyF+L,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAAChL,IAAI,kBArf+EnI,EAAE,CAAAoI,iBAAA;MAAAR,IAAA,EAqfJuL,eAAe;MAAAxJ,SAAA;MAAAC,MAAA;QAAA8J,eAAA;MAAA;MAAArL,UAAA;MAAAC,QAAA,GArfbtI,EAAE,CAAAgK,kBAAA,CAqf2H,CAAC5H,WAAW,CAAC+Q,eAAe,CAAC,CAAC,GArf3JnT,EAAE,CAAAuI,0BAAA;IAAA,EAqfiM;EAAE;AAC1S;AACAxI,UAAU,CAAC,CACPgC,OAAO,CACV,EAAEoR,eAAe,CAACpF,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC;AAC9C;EAAA,QAAArG,SAAA,oBAAAA,SAAA,KA1fqG1H,EAAE,CAAA2H,iBAAA,CA0fXwL,eAAe,EAAc,CAAC;IAC9GvL,IAAI,EAAE1H,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCQ,UAAU,EAAE,IAAI;MAChB6B,QAAQ,EAAE,mBAAmB;MAC7BC,SAAS,EAAE,CAAC/H,WAAW,CAAC+Q,eAAe,CAAC;IAC5C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEO,eAAe,EAAE,CAAC;MAC5E9L,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEkT,OAAO,EAAE;EAAG,CAAC;AAAA;AAE7B,MAAMK,WAAW,SAASvR,eAAe,CAAC;EACtCkE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGsB,SAAS,CAAC;IACnB,IAAI,CAACH,IAAI,GAAG,MAAM;EACtB;EACAkF,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACiH,WAAW,EAAE3F,qBAAqB,CAAC,CAAC,IAAIjN,iBAAiB;EACzE;EACA;IAAS,IAAI,CAAC+F,IAAI;MAAA,IAAA8M,wBAAA;MAAA,gBAAAC,oBAAA7M,CAAA;QAAA,QAAA4M,wBAAA,KAAAA,wBAAA,GA7gB+EhU,EAAE,CAAAkI,qBAAA,CA6gBQ4L,WAAW,IAAA1M,CAAA,IAAX0M,WAAW;MAAA;IAAA,IAAqD;EAAE;EAC7K;IAAS,IAAI,CAAC3L,IAAI,kBA9gB+EnI,EAAE,CAAAoI,iBAAA;MAAAR,IAAA,EA8gBJkM,WAAW;MAAAnK,SAAA;MAAAC,MAAA;QAAAmK,WAAA;MAAA;MAAA1L,UAAA;MAAAC,QAAA,GA9gBTtI,EAAE,CAAAgK,kBAAA,CA8gBoH,CAACxH,iBAAiB,CAACsR,WAAW,CAAC,CAAC,GA9gBtJ9T,EAAE,CAAAuI,0BAAA;IAAA,EA8gB4L;EAAE;AACrS;AACA;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KAhhBqG1H,EAAE,CAAA2H,iBAAA,CAghBXmM,WAAW,EAAc,CAAC;IAC1GlM,IAAI,EAAE1H,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCQ,UAAU,EAAE,IAAI;MAChB6B,QAAQ,EAAE,wBAAwB;MAClCC,SAAS,EAAE,CAAC3H,iBAAiB,CAACsR,WAAW,CAAC;IAC9C,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEC,WAAW,EAAE,CAAC;MAC5BnM,IAAI,EAAErH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2T,aAAa,SAAS/R,SAAS,CAAC;EAClCsE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAE4D,UAAU,IAAK,IAAI,CAACC,OAAO,CAACC,SAAS,CAACF,UAAU,CAAC,CAAC;IACzD,IAAI,CAACuG,KAAK,GAAGtQ,MAAM,CAAC8J,YAAY,CAAC;IACjC,IAAI,CAACE,OAAO,GAAG,IAAIlH,eAAe,CAAC,KAAK,CAAC;IACzC,IAAI,CAAC+Q,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACvM,IAAI,GAAG,MAAM;IAClB,IAAI,CAACgJ,KAAK,CAAC5F,OAAO,GAAG,KAAK;EAC9B;EACAvB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACa,OAAO,CAAC1D,IAAI,CAAC,CAAC,CAAC,IAAI,CAACuN,aAAa,CAAC;IACvC,IAAI,CAACvD,KAAK,CAAC5F,OAAO,GAAG,IAAI,CAACmJ,aAAa,KAAK,IAAI;EACpD;EACA;IAAS,IAAI,CAACjN,IAAI,YAAAkN,sBAAAhN,CAAA;MAAA,YAAAA,CAAA,IAAyF8M,aAAa;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAAC/L,IAAI,kBAziB+EnI,EAAE,CAAAoI,iBAAA;MAAAR,IAAA,EAyiBJsM,aAAa;MAAAvK,SAAA;MAAAC,MAAA;QAAAuK,aAAA;MAAA;MAAA9L,UAAA;MAAAC,QAAA,GAziBXtI,EAAE,CAAAgK,kBAAA,CAyiB4H,CAAC5H,WAAW,CAAC8R,aAAa,CAAC,CAAC,GAziB1JlU,EAAE,CAAAuI,0BAAA,EAAFvI,EAAE,CAAAiK,oBAAA;IAAA,EAyiBqN;EAAE;AAC9T;AACA;EAAA,QAAAvC,SAAA,oBAAAA,SAAA,KA3iBqG1H,EAAE,CAAA2H,iBAAA,CA2iBXuM,aAAa,EAAc,CAAC;IAC5GtM,IAAI,EAAE1H,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCQ,UAAU,EAAE,IAAI;MAChB6B,QAAQ,EAAE,0BAA0B;MACpCC,SAAS,EAAE,CAAC/H,WAAW,CAAC8R,aAAa,CAAC;IAC1C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEC,aAAa,EAAE,CAAC;MAC1EvM,IAAI,EAAErH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8T,eAAe,CAAC;EAClB5N,WAAWA,CAAA,EAAG;IACV,IAAI,CAACQ,IAAI,GAAG3G,MAAM,CAAC0L,gBAAgB,CAAC;IACpC,IAAI,CAACsI,eAAe,GAAG,EAAE;EAC7B;EACAC,YAAYA,CAAC;IAAEC,WAAW;IAAE9H,WAAW;IAAE+H;EAAY,CAAC,EAAE;IACpD,IAAI,CAACxN,IAAI,CAACgH,OAAO,GACbuG,WAAW,GAAG9H,WAAW,IAAI,IAAI,CAAC4H,eAAe,KAAK,IAAI,GACpD,IAAI,CAACA,eAAe,IAAIG,WAAW,GACnC,EAAE;EAChB;EACA;IAAS,IAAI,CAACvN,IAAI,YAAAwN,wBAAAtN,CAAA;MAAA,YAAAA,CAAA,IAAyFiN,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAAClM,IAAI,kBAlkB+EnI,EAAE,CAAAoI,iBAAA;MAAAR,IAAA,EAkkBJyM,eAAe;MAAA1K,SAAA;MAAAuF,YAAA,WAAAyF,6BAAA5P,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlkBb/E,EAAE,CAAAoP,UAAA,wBAAAwF,8CAAAtF,MAAA;YAAA,OAkkBJtK,GAAA,CAAAuP,YAAA,CAAAjF,MAAA,CAAAuF,aAAiC,CAAC;UAAA,CAApB,CAAC;QAAA;MAAA;MAAAjL,MAAA;QAAA0K,eAAA;MAAA;MAAAjM,UAAA;MAAAC,QAAA,GAlkBbtI,EAAE,CAAAyO,uBAAA;QAAA9H,SAAA,EAkkB2NqF,gBAAgB;QAAApC,MAAA;MAAA;IAAA,EAAwE;EAAE;AAC5Z;AACA;EAAA,QAAAlC,SAAA,oBAAAA,SAAA,KApkBqG1H,EAAE,CAAA2H,iBAAA,CAokBX0M,eAAe,EAAc,CAAC;IAC9GzM,IAAI,EAAE1H,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCQ,UAAU,EAAE,IAAI;MAChB6B,QAAQ,EAAE,mBAAmB;MAC7BwE,cAAc,EAAE,CACZ;QACI/H,SAAS,EAAEqF,gBAAgB;QAC3BpC,MAAM,EAAE,CAAC,mBAAmB;MAChC,CAAC,CACJ;MACD2F,IAAI,EAAE;QACF,cAAc,EAAE;MACpB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE+E,eAAe,EAAE,CAAC;MAChC1M,IAAI,EAAErH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMuU,OAAO,GAAG,CACZvO,gBAAgB,EAChByF,gBAAgB,EAChB5C,uBAAuB,EACvBmH,eAAe,EACfzI,aAAa,EACb8D,eAAe,EACfxB,YAAY,EACZiK,eAAe,EACflB,eAAe,EACfW,WAAW,EACXI,aAAa,EACbvF,cAAc,CACjB;AAED,MAAMoG,QAAQ,CAAC;EACXtO,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuO,MAAM,GAAG1U,MAAM,CAACkG,cAAc,CAAC;IACpC,IAAI,CAACyO,UAAU,GAAG3U,MAAM,CAACU,UAAU,CAAC;IACpC,IAAI,CAACkU,GAAG,GAAG5U,MAAM,CAACW,iBAAiB,CAAC;IACpC,IAAI,CAACkU,KAAK,GAAG,EAAE;EACnB;EACAC,QAAQA,CAAA,EAAG;IACP;IACA;IACA,IAAI,CAACJ,MAAM,CAACnK,IAAI,CAAC3J,kBAAkB,CAAC,IAAI,CAAC+T,UAAU,CAAC,CAAC,CAAC1K,SAAS,CAAE4K,KAAK,IAAK;MACvE,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACD,GAAG,CAACG,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACnO,IAAI,YAAAoO,iBAAAlO,CAAA;MAAA,YAAAA,CAAA,IAAyF2N,QAAQ;IAAA,CAAmD;EAAE;EACxK;IAAS,IAAI,CAACrF,IAAI,kBAtnB+E1P,EAAE,CAAA2P,iBAAA;MAAA/H,IAAA,EAsnBJmN,QAAQ;MAAApL,SAAA;MAAA4L,SAAA,gBAAgF,QAAQ;MAAAlN,UAAA;MAAAC,QAAA,GAtnB9FtI,EAAE,CAAA4P,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAwF,kBAAAzQ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/E,EAAE,CAAA+F,UAAA,IAAAF,uBAAA,gBAsnBuQ,CAAC;QAAA;QAAA,IAAAd,EAAA;UAtnB1Q/E,EAAE,CAAAuF,UAAA,YAAAP,GAAA,CAAAmQ,KAsnB8J,CAAC;QAAA;MAAA;MAAAjF,YAAA,GAAyV1L,OAAO,EAAmHrB,kBAAkB,EAA8HmB,aAAa,EAAgQhD,iBAAiB;MAAA4R,MAAA;IAAA,EAA4F;EAAE;AACruC;AACA;EAAA,QAAAxL,SAAA,oBAAAA,SAAA,KAxnBqG1H,EAAE,CAAA2H,iBAAA,CAwnBXoN,QAAQ,EAAc,CAAC;IACvGnN,IAAI,EAAEhH,SAAS;IACfiH,IAAI,EAAE,CAAC;MAAEQ,UAAU,EAAE,IAAI;MAAE6B,QAAQ,EAAE,WAAW;MAAEmG,OAAO,EAAE,CAAC7L,OAAO,EAAErB,kBAAkB,EAAEmB,aAAa,EAAEhD,iBAAiB,CAAC;MAAE8O,eAAe,EAAEvP,uBAAuB,CAAC4U,OAAO;MAAElG,IAAI,EAAE;QACxK,WAAW,EAAE;MACjB,CAAC;MAAES,QAAQ,EAAE,kPAAkP;MAAEkD,MAAM,EAAE,CAAC,oFAAoF;IAAE,CAAC;EAC7W,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS5M,kBAAkB,EAAEmC,wBAAwB,EAAED,mBAAmB,EAAEO,gBAAgB,EAAE0H,kBAAkB,EAAEqE,OAAO,EAAEnE,oBAAoB,EAAEpK,gBAAgB,EAAE4M,eAAe,EAAEnH,gBAAgB,EAAElE,aAAa,EAAEgM,WAAW,EAAE1J,YAAY,EAAE8J,aAAa,EAAE9K,uBAAuB,EAAEiL,eAAe,EAAE1F,cAAc,EAAE/C,eAAe,EAAEpF,cAAc,EAAE+J,eAAe,EAAEf,wBAAwB,EAAEuF,QAAQ,EAAE/L,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}