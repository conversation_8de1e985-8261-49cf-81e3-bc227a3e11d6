{"ast": null, "code": "import { TuiValidationError } from '@taiga-ui/cdk/classes';\nimport * as i0 from '@angular/core';\nimport { inject, signal, Directive, Input } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { NG_VALIDATORS } from '@angular/forms';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_DEFAULT_ERROR_MESSAGE } from '@taiga-ui/core/tokens';\nimport { TUI_VALIDATION_ERRORS } from '@taiga-ui/kit/tokens';\nimport { isObservable } from 'rxjs';\nfunction tuiCreateUnfinishedValidator(element, message) {\n  return ({\n    value\n  }) => value === null && element.value !== '' ? {\n    tuiUnfinished: new TuiValidationError(message)\n  } : null;\n}\nclass TuiUnfinishedValidator {\n  constructor() {\n    this.default = toSignal(inject(TUI_DEFAULT_ERROR_MESSAGE));\n    this.error = inject(TUI_VALIDATION_ERRORS)['tuiUnfinished'];\n    this.fallback = this.error ? signal(this.error) : this.default;\n    this.message = isObservable(this.error) ? toSignal(this.error) : this.fallback;\n    this.tuiUnfinishedValidator = '';\n    this.validate = tuiCreateUnfinishedValidator(tuiInjectElement(), () => this.tuiUnfinishedValidator || this.message());\n  }\n  static {\n    this.ɵfac = function TuiUnfinishedValidator_Factory(t) {\n      return new (t || TuiUnfinishedValidator)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiUnfinishedValidator,\n      selectors: [[\"input\", \"tuiUnfinishedValidator\", \"\"]],\n      inputs: {\n        tuiUnfinishedValidator: \"tuiUnfinishedValidator\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(NG_VALIDATORS, TuiUnfinishedValidator, true)])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiUnfinishedValidator, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiUnfinishedValidator]',\n      providers: [tuiProvide(NG_VALIDATORS, TuiUnfinishedValidator, true)]\n    }]\n  }], null, {\n    tuiUnfinishedValidator: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiUnfinishedValidator, tuiCreateUnfinishedValidator };", "map": {"version": 3, "names": ["TuiValidationError", "i0", "inject", "signal", "Directive", "Input", "toSignal", "NG_VALIDATORS", "tuiInjectElement", "tui<PERSON><PERSON><PERSON>", "TUI_DEFAULT_ERROR_MESSAGE", "TUI_VALIDATION_ERRORS", "isObservable", "tuiCreateUnfinishedValidator", "element", "message", "value", "tuiUnfinished", "TuiUnfinishedValidator", "constructor", "default", "error", "fallback", "tuiUnfinishedValidator", "validate", "ɵfac", "TuiUnfinishedValidator_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "standalone", "features", "ɵɵProvidersFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-unfinished-validator.mjs"], "sourcesContent": ["import { TuiValidationError } from '@taiga-ui/cdk/classes';\nimport * as i0 from '@angular/core';\nimport { inject, signal, Directive, Input } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { NG_VALIDATORS } from '@angular/forms';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_DEFAULT_ERROR_MESSAGE } from '@taiga-ui/core/tokens';\nimport { TUI_VALIDATION_ERRORS } from '@taiga-ui/kit/tokens';\nimport { isObservable } from 'rxjs';\n\nfunction tuiCreateUnfinishedValidator(element, message) {\n    return ({ value, }) => value === null && element.value !== ''\n        ? { tuiUnfinished: new TuiValidationError(message) }\n        : null;\n}\n\nclass TuiUnfinishedValidator {\n    constructor() {\n        this.default = toSignal(inject(TUI_DEFAULT_ERROR_MESSAGE));\n        this.error = inject(TUI_VALIDATION_ERRORS)['tuiUnfinished'];\n        this.fallback = this.error ? signal(this.error) : this.default;\n        this.message = isObservable(this.error)\n            ? toSignal(this.error)\n            : this.fallback;\n        this.tuiUnfinishedValidator = '';\n        this.validate = tuiCreateUnfinishedValidator(tuiInjectElement(), () => this.tuiUnfinishedValidator || this.message());\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiUnfinishedValidator, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiUnfinishedValidator, isStandalone: true, selector: \"input[tuiUnfinishedValidator]\", inputs: { tuiUnfinishedValidator: \"tuiUnfinishedValidator\" }, providers: [tuiProvide(NG_VALIDATORS, TuiUnfinishedValidator, true)], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiUnfinishedValidator, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiUnfinishedValidator]',\n                    providers: [tuiProvide(NG_VALIDATORS, TuiUnfinishedValidator, true)],\n                }]\n        }], propDecorators: { tuiUnfinishedValidator: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiUnfinishedValidator, tuiCreateUnfinishedValidator };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChE,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,yBAAyB,QAAQ,uBAAuB;AACjE,SAASC,qBAAqB,QAAQ,sBAAsB;AAC5D,SAASC,YAAY,QAAQ,MAAM;AAEnC,SAASC,4BAA4BA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACpD,OAAO,CAAC;IAAEC;EAAO,CAAC,KAAKA,KAAK,KAAK,IAAI,IAAIF,OAAO,CAACE,KAAK,KAAK,EAAE,GACvD;IAAEC,aAAa,EAAE,IAAIjB,kBAAkB,CAACe,OAAO;EAAE,CAAC,GAClD,IAAI;AACd;AAEA,MAAMG,sBAAsB,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGd,QAAQ,CAACJ,MAAM,CAACQ,yBAAyB,CAAC,CAAC;IAC1D,IAAI,CAACW,KAAK,GAAGnB,MAAM,CAACS,qBAAqB,CAAC,CAAC,eAAe,CAAC;IAC3D,IAAI,CAACW,QAAQ,GAAG,IAAI,CAACD,KAAK,GAAGlB,MAAM,CAAC,IAAI,CAACkB,KAAK,CAAC,GAAG,IAAI,CAACD,OAAO;IAC9D,IAAI,CAACL,OAAO,GAAGH,YAAY,CAAC,IAAI,CAACS,KAAK,CAAC,GACjCf,QAAQ,CAAC,IAAI,CAACe,KAAK,CAAC,GACpB,IAAI,CAACC,QAAQ;IACnB,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,QAAQ,GAAGX,4BAA4B,CAACL,gBAAgB,CAAC,CAAC,EAAE,MAAM,IAAI,CAACe,sBAAsB,IAAI,IAAI,CAACR,OAAO,CAAC,CAAC,CAAC;EACzH;EACA;IAAS,IAAI,CAACU,IAAI,YAAAC,+BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFT,sBAAsB;IAAA,CAAmD;EAAE;EACtL;IAAS,IAAI,CAACU,IAAI,kBAD+E3B,EAAE,CAAA4B,iBAAA;MAAAC,IAAA,EACJZ,sBAAsB;MAAAa,SAAA;MAAAC,MAAA;QAAAT,sBAAA;MAAA;MAAAU,UAAA;MAAAC,QAAA,GADpBjC,EAAE,CAAAkC,kBAAA,CAC4J,CAAC1B,UAAU,CAACF,aAAa,EAAEW,sBAAsB,EAAE,IAAI,CAAC,CAAC;IAAA,EAAiB;EAAE;AAC/U;AACA;EAAA,QAAAkB,SAAA,oBAAAA,SAAA,KAHqGnC,EAAE,CAAAoC,iBAAA,CAGXnB,sBAAsB,EAAc,CAAC;IACrHY,IAAI,EAAE1B,SAAS;IACfkC,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBM,QAAQ,EAAE,+BAA+B;MACzCC,SAAS,EAAE,CAAC/B,UAAU,CAACF,aAAa,EAAEW,sBAAsB,EAAE,IAAI,CAAC;IACvE,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEK,sBAAsB,EAAE,CAAC;MACvCO,IAAI,EAAEzB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASa,sBAAsB,EAAEL,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}