{"ast": null, "code": "import { tuiCreateToken } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nconst TUI_EMAIL_PIPE_OPTIONS = tuiCreateToken(['gmail.com', 'outlook.com', 'icloud.com', 'me.com', 'yahoo.com', 'mail.com', 'proton.me']);\nclass TuiEmailsPipe {\n  constructor() {\n    this.options = inject(TUI_EMAIL_PIPE_OPTIONS);\n  }\n  transform(query, suggestions = this.options) {\n    return query.includes('@') ? suggestions.map(item => query.slice(0, Math.max(0, query.indexOf('@') + 1)) + item).filter(item => item.startsWith(query)) : [];\n  }\n  static {\n    this.ɵfac = function TuiEmailsPipe_Factory(t) {\n      return new (t || TuiEmailsPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiEmails\",\n      type: TuiEmailsPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiEmailsPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiEmails'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_EMAIL_PIPE_OPTIONS, TuiEmailsPipe };", "map": {"version": 3, "names": ["tuiCreateToken", "i0", "inject", "<PERSON><PERSON>", "TUI_EMAIL_PIPE_OPTIONS", "TuiEmailsPipe", "constructor", "options", "transform", "query", "suggestions", "includes", "map", "item", "slice", "Math", "max", "indexOf", "filter", "startsWith", "ɵfac", "TuiEmailsPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-pipes-emails.mjs"], "sourcesContent": ["import { tuiCreateToken } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\n\nconst TUI_EMAIL_PIPE_OPTIONS = tuiCreateToken([\n    'gmail.com',\n    'outlook.com',\n    'icloud.com',\n    'me.com',\n    'yahoo.com',\n    'mail.com',\n    'proton.me',\n]);\n\nclass TuiEmailsPipe {\n    constructor() {\n        this.options = inject(TUI_EMAIL_PIPE_OPTIONS);\n    }\n    transform(query, suggestions = this.options) {\n        return query.includes('@')\n            ? suggestions\n                .map((item) => query.slice(0, Math.max(0, query.indexOf('@') + 1)) + item)\n                .filter((item) => item.startsWith(query))\n            : [];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Tui<PERSON>mailsPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiEmailsPipe, isStandalone: true, name: \"tuiEmails\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiEmailsPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiEmails',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_EMAIL_PIPE_OPTIONS, TuiEmailsPipe };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mCAAmC;AAClE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,IAAI,QAAQ,eAAe;AAE5C,MAAMC,sBAAsB,GAAGJ,cAAc,CAAC,CAC1C,WAAW,EACX,aAAa,EACb,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,UAAU,EACV,WAAW,CACd,CAAC;AAEF,MAAMK,aAAa,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGL,MAAM,CAACE,sBAAsB,CAAC;EACjD;EACAI,SAASA,CAACC,KAAK,EAAEC,WAAW,GAAG,IAAI,CAACH,OAAO,EAAE;IACzC,OAAOE,KAAK,CAACE,QAAQ,CAAC,GAAG,CAAC,GACpBD,WAAW,CACRE,GAAG,CAAEC,IAAI,IAAKJ,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,KAAK,CAACQ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGJ,IAAI,CAAC,CACzEK,MAAM,CAAEL,IAAI,IAAKA,IAAI,CAACM,UAAU,CAACV,KAAK,CAAC,CAAC,GAC3C,EAAE;EACZ;EACA;IAAS,IAAI,CAACW,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFjB,aAAa;IAAA,CAA8C;EAAE;EACxK;IAAS,IAAI,CAACkB,KAAK,kBAD8EtB,EAAE,CAAAuB,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMrB,aAAa;MAAAsB,IAAA;MAAAC,UAAA;IAAA,EAA0C;EAAE;AACtK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG5B,EAAE,CAAA6B,iBAAA,CAGXzB,aAAa,EAAc,CAAC;IAC5GqB,IAAI,EAAEvB,IAAI;IACV4B,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASrB,sBAAsB,EAAEC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}