{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { CpqConfiguratorComponent } from './cpq-configurator.component';\nimport { ProductTreeModule } from '../product-tree/product-tree.module';\nimport * as i0 from \"@angular/core\";\nexport class CpqConfiguratorModule {\n  static {\n    this.ɵfac = function CpqConfiguratorModule_Factory(t) {\n      return new (t || CpqConfiguratorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CpqConfiguratorModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, HttpClientModule, ProductTreeModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CpqConfiguratorModule, {\n    declarations: [CpqConfiguratorComponent],\n    imports: [CommonModule, FormsModule, HttpClientModule, ProductTreeModule],\n    exports: [CpqConfiguratorComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "HttpClientModule", "CpqConfiguratorComponent", "ProductTreeModule", "CpqConfiguratorModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet_CRM\\apps\\AngularCPQapp\\src\\app\\components\\cpq-configurator\\cpq-configurator.module.ts"], "sourcesContent": ["import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { CpqConfiguratorComponent } from './cpq-configurator.component';\nimport { ProductTreeModule } from '../product-tree/product-tree.module';\nimport { TuiTreeModule } from '@taiga-ui/kit';\nimport { TreeContentComponent } from './tree-content.component';\n\n@NgModule({\n  declarations: [CpqConfiguratorComponent],\n  imports: [\n    CommonModule,\n    FormsModule,\n    HttpClientModule,\n    ProductTreeModule\n  ],\n  exports: [CpqConfiguratorComponent],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA]\n})\nexport class CpqConfiguratorModule { }"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,iBAAiB,QAAQ,qCAAqC;;AAevE,OAAM,MAAOC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAR9BL,YAAY,EACZC,WAAW,EACXC,gBAAgB,EAChBE,iBAAiB;IAAA;EAAA;;;2EAKRC,qBAAqB;IAAAC,YAAA,GAVjBH,wBAAwB;IAAAI,OAAA,GAErCP,YAAY,EACZC,WAAW,EACXC,gBAAgB,EAChBE,iBAAiB;IAAAI,OAAA,GAETL,wBAAwB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}