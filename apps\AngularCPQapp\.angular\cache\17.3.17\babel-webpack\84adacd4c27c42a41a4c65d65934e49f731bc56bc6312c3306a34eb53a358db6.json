{"ast": null, "code": "import { Observable, startWith, fromEvent, concat, take, map, merge, endWith, takeWhile, repeat, tap, pipe, switchMap, EMPTY, takeUntil, NEVER, catchError, defaultIfEmpty, queueScheduler, asyncScheduler } from 'rxjs';\nimport { tuiIsPresent, tuiGetOriginalArrayFromQueryList } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { untracked, inject, ChangeDetectorRef, NgZone } from '@angular/core';\nfunction tuiCloseWatcher() {\n  return new Observable(subscriber => {\n    let watcher = getWatcher();\n    const setup = () => {\n      watcher = getWatcher();\n      watcher.onclose = () => setup();\n      watcher.oncancel = event => {\n        event.preventDefault();\n        subscriber.next();\n      };\n    };\n    setup();\n    return () => watcher.destroy();\n  });\n}\nfunction getWatcher() {\n  // @ts-ignore\n  return typeof CloseWatcher === 'undefined' ? {\n    destroy: () => {}\n  } : new CloseWatcher();\n}\n\n/**\n * Turns AbstractControl/Abstract-control-directive valueChanges into ReplaySubject(1)\n */\nfunction tuiControlValue(control) {\n  return new Observable(subscriber => control?.valueChanges?.pipe(startWith(control.value)).subscribe(subscriber));\n}\nfunction tuiTypedFromEvent(target, event, options = {}) {\n  /**\n   * @note:\n   * in RxJS 7 type signature `TuiTypedEventTarget<E>` !== `HasEventTargetAddRemove<E>`\n   */\n  return fromEvent(target, event, options);\n}\nclass TuiDragState {\n  constructor(stage, event) {\n    this.stage = stage;\n    this.event = event;\n  }\n}\nfunction tuiDragAndDropFrom(element) {\n  const {\n    ownerDocument\n  } = element;\n  return concat(tuiTypedFromEvent(element, 'mousedown').pipe(take(1), map(event => new TuiDragState('start', event))), merge(tuiTypedFromEvent(ownerDocument, 'mousemove').pipe(map(event => new TuiDragState('continues', event))), merge(tuiTypedFromEvent(ownerDocument, 'mouseup'), tuiTypedFromEvent(ownerDocument, 'dragend')).pipe(take(1), map(event => new TuiDragState('end', event)), endWith(null))).pipe(takeWhile(tuiIsPresent))).pipe(repeat());\n}\nfunction tuiPreventDefault() {\n  return tap(event => event.preventDefault());\n}\nfunction tuiStopPropagation() {\n  return tap(event => event.stopPropagation());\n}\nfunction tuiIfMap(project, predicate = Boolean) {\n  return pipe(switchMap(value => predicate(value) ? project(value) : EMPTY));\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nfunction tuiMustBePresent() {\n  return map(value => {\n    if (!tuiIsPresent(value)) {\n      throw new TuiValuePresentException();\n    }\n    return value;\n  });\n}\nclass TuiValuePresentException extends Error {\n  constructor() {\n    super(ngDevMode ? 'Value must present' : '');\n  }\n}\n\n/**\n * Converts changes observable of a QueryList to an Observable of arrays\n */\nfunction tuiQueryListChanges(queryList) {\n  return queryList.changes.pipe(startWith(null), map(() => tuiGetOriginalArrayFromQueryList(queryList)));\n}\n\n/**\n * Normalizes scroll event in case element is `html` (document.documentElement)\n */\nfunction tuiScrollFrom(element) {\n  return tuiTypedFromEvent(element === element.ownerDocument.documentElement ? element.ownerDocument : element, 'scroll');\n}\n\n// NOTE: takeUntilDestroyed and DestroyRef can cause error:\n// NG0911: View has already been destroyed\n// https://github.com/angular/angular/issues/54527\nfunction tuiTakeUntilDestroyed(destroyRef) {\n  return pipe(takeUntil(NEVER.pipe(takeUntilDestroyed(destroyRef), catchError(() => EMPTY), defaultIfEmpty(null))));\n}\nconst tuiUntrackedScheduler = {\n  now: queueScheduler.now.bind(queueScheduler),\n  schedule(work, delay, state) {\n    return queueScheduler.schedule(function (s) {\n      return untracked(() => work.call(this, s));\n    }, delay, state);\n  }\n};\nfunction tuiWatch(cdr = inject(ChangeDetectorRef)) {\n  return tap(() => cdr.markForCheck());\n}\nfunction tuiZonefull(zone = inject(NgZone)) {\n  return source => new Observable(subscriber => source.subscribe({\n    next: value => zone.run(() => subscriber.next(value)),\n    error: error => zone.run(() => subscriber.error(error)),\n    complete: () => zone.run(() => subscriber.complete())\n  }));\n}\nfunction tuiZonefree(zone = inject(NgZone)) {\n  return source => new Observable(subscriber => zone.runOutsideAngular(() => source.subscribe(subscriber)));\n}\nfunction tuiZoneOptimized(zone = inject(NgZone)) {\n  return pipe(tuiZonefree(zone), tuiZonefull(zone));\n}\nclass TuiZoneScheduler {\n  constructor(zoneConditionFn, scheduler = asyncScheduler) {\n    this.zoneConditionFn = zoneConditionFn;\n    this.scheduler = scheduler;\n  }\n  now() {\n    return this.scheduler.now();\n  }\n  schedule(...args) {\n    return this.zoneConditionFn(() => this.scheduler.schedule(...args));\n  }\n}\nfunction tuiZonefreeScheduler(zone = inject(NgZone), scheduler = asyncScheduler) {\n  return new TuiZoneScheduler(zone.runOutsideAngular.bind(zone), scheduler);\n}\nfunction tuiZonefullScheduler(zone = inject(NgZone), scheduler = asyncScheduler) {\n  return new TuiZoneScheduler(zone.run.bind(zone), scheduler);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiDragState, TuiValuePresentException, tuiCloseWatcher, tuiControlValue, tuiDragAndDropFrom, tuiIfMap, tuiMustBePresent, tuiPreventDefault, tuiQueryListChanges, tuiScrollFrom, tuiStopPropagation, tuiTakeUntilDestroyed, tuiTypedFromEvent, tuiUntrackedScheduler, tuiWatch, tuiZoneOptimized, tuiZonefree, tuiZonefreeScheduler, tuiZonefull, tuiZonefullScheduler };", "map": {"version": 3, "names": ["Observable", "startWith", "fromEvent", "concat", "take", "map", "merge", "endWith", "<PERSON><PERSON><PERSON><PERSON>", "repeat", "tap", "pipe", "switchMap", "EMPTY", "takeUntil", "NEVER", "catchError", "defaultIfEmpty", "queueScheduler", "asyncScheduler", "tuiIsPresent", "tuiGetOriginalArrayFromQueryList", "takeUntilDestroyed", "untracked", "inject", "ChangeDetectorRef", "NgZone", "tuiCloseWatcher", "subscriber", "watcher", "getW<PERSON>er", "setup", "onclose", "oncancel", "event", "preventDefault", "next", "destroy", "CloseWatcher", "tuiControlValue", "control", "valueChanges", "value", "subscribe", "tuiTypedFromEvent", "target", "options", "TuiDragState", "constructor", "stage", "tuiDragAndDropFrom", "element", "ownerDocument", "tuiPreventDefault", "tuiStopPropagation", "stopPropagation", "tuiIfMap", "project", "predicate", "Boolean", "tuiMustBePresent", "TuiValuePresentException", "Error", "ngDevMode", "tui<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "queryList", "changes", "tuiScrollFrom", "documentElement", "tuiTakeUntilDestroyed", "destroyRef", "tuiUntrackedScheduler", "now", "bind", "schedule", "work", "delay", "state", "s", "call", "tuiWatch", "cdr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tui<PERSON>onefull", "zone", "source", "run", "error", "complete", "tuiZonefree", "runOutsideAngular", "tuiZoneOptimized", "TuiZoneScheduler", "zoneConditionFn", "scheduler", "args", "tuiZonefreeScheduler", "tuiZonefullScheduler"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-observables.mjs"], "sourcesContent": ["import { Observable, startWith, fromEvent, concat, take, map, merge, endWith, takeWhile, repeat, tap, pipe, switchMap, EMPTY, takeUntil, NEVER, catchError, defaultIfEmpty, queueScheduler, asyncScheduler } from 'rxjs';\nimport { tuiIsPresent, tuiGetOriginalArrayFromQueryList } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { untracked, inject, ChangeDetectorRef, NgZone } from '@angular/core';\n\nfunction tuiCloseWatcher() {\n    return new Observable((subscriber) => {\n        let watcher = getWatcher();\n        const setup = () => {\n            watcher = getWatcher();\n            watcher.onclose = () => setup();\n            watcher.oncancel = (event) => {\n                event.preventDefault();\n                subscriber.next();\n            };\n        };\n        setup();\n        return () => watcher.destroy();\n    });\n}\nfunction getWatcher() {\n    // @ts-ignore\n    return typeof CloseWatcher === 'undefined' ? { destroy: () => { } } : new CloseWatcher();\n}\n\n/**\n * Turns AbstractControl/Abstract-control-directive valueChanges into ReplaySubject(1)\n */\nfunction tuiControlValue(control) {\n    return new Observable((subscriber) => control?.valueChanges?.pipe(startWith(control.value)).subscribe(subscriber));\n}\n\nfunction tuiTypedFromEvent(target, event, options = {}) {\n    /**\n     * @note:\n     * in RxJS 7 type signature `TuiTypedEventTarget<E>` !== `HasEventTargetAddRemove<E>`\n     */\n    return fromEvent(target, event, options);\n}\n\nclass TuiDragState {\n    constructor(stage, event) {\n        this.stage = stage;\n        this.event = event;\n    }\n}\nfunction tuiDragAndDropFrom(element) {\n    const { ownerDocument } = element;\n    return concat(tuiTypedFromEvent(element, 'mousedown').pipe(take(1), map((event) => new TuiDragState('start', event))), merge(tuiTypedFromEvent(ownerDocument, 'mousemove').pipe(map((event) => new TuiDragState('continues', event))), merge(tuiTypedFromEvent(ownerDocument, 'mouseup'), tuiTypedFromEvent(ownerDocument, 'dragend')).pipe(take(1), map((event) => new TuiDragState('end', event)), endWith(null))).pipe(takeWhile(tuiIsPresent))).pipe(repeat());\n}\n\nfunction tuiPreventDefault() {\n    return tap((event) => event.preventDefault());\n}\nfunction tuiStopPropagation() {\n    return tap((event) => event.stopPropagation());\n}\n\nfunction tuiIfMap(project, predicate = Boolean) {\n    return pipe(switchMap((value) => (predicate(value) ? project(value) : EMPTY)));\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nfunction tuiMustBePresent() {\n    return map((value) => {\n        if (!tuiIsPresent(value)) {\n            throw new TuiValuePresentException();\n        }\n        return value;\n    });\n}\nclass TuiValuePresentException extends Error {\n    constructor() {\n        super(ngDevMode ? 'Value must present' : '');\n    }\n}\n\n/**\n * Converts changes observable of a QueryList to an Observable of arrays\n */\nfunction tuiQueryListChanges(queryList) {\n    return queryList.changes.pipe(startWith(null), map(() => tuiGetOriginalArrayFromQueryList(queryList)));\n}\n\n/**\n * Normalizes scroll event in case element is `html` (document.documentElement)\n */\nfunction tuiScrollFrom(element) {\n    return tuiTypedFromEvent(element === element.ownerDocument.documentElement\n        ? element.ownerDocument\n        : element, 'scroll');\n}\n\n// NOTE: takeUntilDestroyed and DestroyRef can cause error:\n// NG0911: View has already been destroyed\n// https://github.com/angular/angular/issues/54527\nfunction tuiTakeUntilDestroyed(destroyRef) {\n    return pipe(takeUntil(NEVER.pipe(takeUntilDestroyed(destroyRef), catchError(() => EMPTY), defaultIfEmpty(null))));\n}\n\nconst tuiUntrackedScheduler = {\n    now: queueScheduler.now.bind(queueScheduler),\n    schedule(work, delay, state) {\n        return queueScheduler.schedule(function (s) {\n            return untracked(() => work.call(this, s));\n        }, delay, state);\n    },\n};\n\nfunction tuiWatch(cdr = inject(ChangeDetectorRef)) {\n    return tap(() => cdr.markForCheck());\n}\n\nfunction tuiZonefull(zone = inject(NgZone)) {\n    return (source) => new Observable((subscriber) => source.subscribe({\n        next: (value) => zone.run(() => subscriber.next(value)),\n        error: (error) => zone.run(() => subscriber.error(error)),\n        complete: () => zone.run(() => subscriber.complete()),\n    }));\n}\nfunction tuiZonefree(zone = inject(NgZone)) {\n    return (source) => new Observable((subscriber) => zone.runOutsideAngular(() => source.subscribe(subscriber)));\n}\nfunction tuiZoneOptimized(zone = inject(NgZone)) {\n    return pipe(tuiZonefree(zone), tuiZonefull(zone));\n}\nclass TuiZoneScheduler {\n    constructor(zoneConditionFn, scheduler = asyncScheduler) {\n        this.zoneConditionFn = zoneConditionFn;\n        this.scheduler = scheduler;\n    }\n    now() {\n        return this.scheduler.now();\n    }\n    schedule(...args) {\n        return this.zoneConditionFn(() => this.scheduler.schedule(...args));\n    }\n}\nfunction tuiZonefreeScheduler(zone = inject(NgZone), scheduler = asyncScheduler) {\n    return new TuiZoneScheduler(zone.runOutsideAngular.bind(zone), scheduler);\n}\nfunction tuiZonefullScheduler(zone = inject(NgZone), scheduler = asyncScheduler) {\n    return new TuiZoneScheduler(zone.run.bind(zone), scheduler);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiDragState, TuiValuePresentException, tuiCloseWatcher, tuiControlValue, tuiDragAndDropFrom, tuiIfMap, tuiMustBePresent, tuiPreventDefault, tuiQueryListChanges, tuiScrollFrom, tuiStopPropagation, tuiTakeUntilDestroyed, tuiTypedFromEvent, tuiUntrackedScheduler, tuiWatch, tuiZoneOptimized, tuiZonefree, tuiZonefreeScheduler, tuiZonefull, tuiZonefullScheduler };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,QAAQ,MAAM;AACxN,SAASC,YAAY,EAAEC,gCAAgC,QAAQ,mCAAmC;AAClG,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,SAAS,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,MAAM,QAAQ,eAAe;AAE5E,SAASC,eAAeA,CAAA,EAAG;EACvB,OAAO,IAAI3B,UAAU,CAAE4B,UAAU,IAAK;IAClC,IAAIC,OAAO,GAAGC,UAAU,CAAC,CAAC;IAC1B,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAChBF,OAAO,GAAGC,UAAU,CAAC,CAAC;MACtBD,OAAO,CAACG,OAAO,GAAG,MAAMD,KAAK,CAAC,CAAC;MAC/BF,OAAO,CAACI,QAAQ,GAAIC,KAAK,IAAK;QAC1BA,KAAK,CAACC,cAAc,CAAC,CAAC;QACtBP,UAAU,CAACQ,IAAI,CAAC,CAAC;MACrB,CAAC;IACL,CAAC;IACDL,KAAK,CAAC,CAAC;IACP,OAAO,MAAMF,OAAO,CAACQ,OAAO,CAAC,CAAC;EAClC,CAAC,CAAC;AACN;AACA,SAASP,UAAUA,CAAA,EAAG;EAClB;EACA,OAAO,OAAOQ,YAAY,KAAK,WAAW,GAAG;IAAED,OAAO,EAAEA,CAAA,KAAM,CAAE;EAAE,CAAC,GAAG,IAAIC,YAAY,CAAC,CAAC;AAC5F;;AAEA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,OAAO,EAAE;EAC9B,OAAO,IAAIxC,UAAU,CAAE4B,UAAU,IAAKY,OAAO,EAAEC,YAAY,EAAE9B,IAAI,CAACV,SAAS,CAACuC,OAAO,CAACE,KAAK,CAAC,CAAC,CAACC,SAAS,CAACf,UAAU,CAAC,CAAC;AACtH;AAEA,SAASgB,iBAAiBA,CAACC,MAAM,EAAEX,KAAK,EAAEY,OAAO,GAAG,CAAC,CAAC,EAAE;EACpD;AACJ;AACA;AACA;EACI,OAAO5C,SAAS,CAAC2C,MAAM,EAAEX,KAAK,EAAEY,OAAO,CAAC;AAC5C;AAEA,MAAMC,YAAY,CAAC;EACfC,WAAWA,CAACC,KAAK,EAAEf,KAAK,EAAE;IACtB,IAAI,CAACe,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACf,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA,SAASgB,kBAAkBA,CAACC,OAAO,EAAE;EACjC,MAAM;IAAEC;EAAc,CAAC,GAAGD,OAAO;EACjC,OAAOhD,MAAM,CAACyC,iBAAiB,CAACO,OAAO,EAAE,WAAW,CAAC,CAACxC,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAE6B,KAAK,IAAK,IAAIa,YAAY,CAAC,OAAO,EAAEb,KAAK,CAAC,CAAC,CAAC,EAAE5B,KAAK,CAACsC,iBAAiB,CAACQ,aAAa,EAAE,WAAW,CAAC,CAACzC,IAAI,CAACN,GAAG,CAAE6B,KAAK,IAAK,IAAIa,YAAY,CAAC,WAAW,EAAEb,KAAK,CAAC,CAAC,CAAC,EAAE5B,KAAK,CAACsC,iBAAiB,CAACQ,aAAa,EAAE,SAAS,CAAC,EAAER,iBAAiB,CAACQ,aAAa,EAAE,SAAS,CAAC,CAAC,CAACzC,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAE6B,KAAK,IAAK,IAAIa,YAAY,CAAC,KAAK,EAAEb,KAAK,CAAC,CAAC,EAAE3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAACI,IAAI,CAACH,SAAS,CAACY,YAAY,CAAC,CAAC,CAAC,CAACT,IAAI,CAACF,MAAM,CAAC,CAAC,CAAC;AACtc;AAEA,SAAS4C,iBAAiBA,CAAA,EAAG;EACzB,OAAO3C,GAAG,CAAEwB,KAAK,IAAKA,KAAK,CAACC,cAAc,CAAC,CAAC,CAAC;AACjD;AACA,SAASmB,kBAAkBA,CAAA,EAAG;EAC1B,OAAO5C,GAAG,CAAEwB,KAAK,IAAKA,KAAK,CAACqB,eAAe,CAAC,CAAC,CAAC;AAClD;AAEA,SAASC,QAAQA,CAACC,OAAO,EAAEC,SAAS,GAAGC,OAAO,EAAE;EAC5C,OAAOhD,IAAI,CAACC,SAAS,CAAE8B,KAAK,IAAMgB,SAAS,CAAChB,KAAK,CAAC,GAAGe,OAAO,CAACf,KAAK,CAAC,GAAG7B,KAAM,CAAC,CAAC;AAClF;;AAEA;AACA,SAAS+C,gBAAgBA,CAAA,EAAG;EACxB,OAAOvD,GAAG,CAAEqC,KAAK,IAAK;IAClB,IAAI,CAACtB,YAAY,CAACsB,KAAK,CAAC,EAAE;MACtB,MAAM,IAAImB,wBAAwB,CAAC,CAAC;IACxC;IACA,OAAOnB,KAAK;EAChB,CAAC,CAAC;AACN;AACA,MAAMmB,wBAAwB,SAASC,KAAK,CAAC;EACzCd,WAAWA,CAAA,EAAG;IACV,KAAK,CAACe,SAAS,GAAG,oBAAoB,GAAG,EAAE,CAAC;EAChD;AACJ;;AAEA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,SAAS,EAAE;EACpC,OAAOA,SAAS,CAACC,OAAO,CAACvD,IAAI,CAACV,SAAS,CAAC,IAAI,CAAC,EAAEI,GAAG,CAAC,MAAMgB,gCAAgC,CAAC4C,SAAS,CAAC,CAAC,CAAC;AAC1G;;AAEA;AACA;AACA;AACA,SAASE,aAAaA,CAAChB,OAAO,EAAE;EAC5B,OAAOP,iBAAiB,CAACO,OAAO,KAAKA,OAAO,CAACC,aAAa,CAACgB,eAAe,GACpEjB,OAAO,CAACC,aAAa,GACrBD,OAAO,EAAE,QAAQ,CAAC;AAC5B;;AAEA;AACA;AACA;AACA,SAASkB,qBAAqBA,CAACC,UAAU,EAAE;EACvC,OAAO3D,IAAI,CAACG,SAAS,CAACC,KAAK,CAACJ,IAAI,CAACW,kBAAkB,CAACgD,UAAU,CAAC,EAAEtD,UAAU,CAAC,MAAMH,KAAK,CAAC,EAAEI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACrH;AAEA,MAAMsD,qBAAqB,GAAG;EAC1BC,GAAG,EAAEtD,cAAc,CAACsD,GAAG,CAACC,IAAI,CAACvD,cAAc,CAAC;EAC5CwD,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;IACzB,OAAO3D,cAAc,CAACwD,QAAQ,CAAC,UAAUI,CAAC,EAAE;MACxC,OAAOvD,SAAS,CAAC,MAAMoD,IAAI,CAACI,IAAI,CAAC,IAAI,EAAED,CAAC,CAAC,CAAC;IAC9C,CAAC,EAAEF,KAAK,EAAEC,KAAK,CAAC;EACpB;AACJ,CAAC;AAED,SAASG,QAAQA,CAACC,GAAG,GAAGzD,MAAM,CAACC,iBAAiB,CAAC,EAAE;EAC/C,OAAOf,GAAG,CAAC,MAAMuE,GAAG,CAACC,YAAY,CAAC,CAAC,CAAC;AACxC;AAEA,SAASC,WAAWA,CAACC,IAAI,GAAG5D,MAAM,CAACE,MAAM,CAAC,EAAE;EACxC,OAAQ2D,MAAM,IAAK,IAAIrF,UAAU,CAAE4B,UAAU,IAAKyD,MAAM,CAAC1C,SAAS,CAAC;IAC/DP,IAAI,EAAGM,KAAK,IAAK0C,IAAI,CAACE,GAAG,CAAC,MAAM1D,UAAU,CAACQ,IAAI,CAACM,KAAK,CAAC,CAAC;IACvD6C,KAAK,EAAGA,KAAK,IAAKH,IAAI,CAACE,GAAG,CAAC,MAAM1D,UAAU,CAAC2D,KAAK,CAACA,KAAK,CAAC,CAAC;IACzDC,QAAQ,EAAEA,CAAA,KAAMJ,IAAI,CAACE,GAAG,CAAC,MAAM1D,UAAU,CAAC4D,QAAQ,CAAC,CAAC;EACxD,CAAC,CAAC,CAAC;AACP;AACA,SAASC,WAAWA,CAACL,IAAI,GAAG5D,MAAM,CAACE,MAAM,CAAC,EAAE;EACxC,OAAQ2D,MAAM,IAAK,IAAIrF,UAAU,CAAE4B,UAAU,IAAKwD,IAAI,CAACM,iBAAiB,CAAC,MAAML,MAAM,CAAC1C,SAAS,CAACf,UAAU,CAAC,CAAC,CAAC;AACjH;AACA,SAAS+D,gBAAgBA,CAACP,IAAI,GAAG5D,MAAM,CAACE,MAAM,CAAC,EAAE;EAC7C,OAAOf,IAAI,CAAC8E,WAAW,CAACL,IAAI,CAAC,EAAED,WAAW,CAACC,IAAI,CAAC,CAAC;AACrD;AACA,MAAMQ,gBAAgB,CAAC;EACnB5C,WAAWA,CAAC6C,eAAe,EAAEC,SAAS,GAAG3E,cAAc,EAAE;IACrD,IAAI,CAAC0E,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACAtB,GAAGA,CAAA,EAAG;IACF,OAAO,IAAI,CAACsB,SAAS,CAACtB,GAAG,CAAC,CAAC;EAC/B;EACAE,QAAQA,CAAC,GAAGqB,IAAI,EAAE;IACd,OAAO,IAAI,CAACF,eAAe,CAAC,MAAM,IAAI,CAACC,SAAS,CAACpB,QAAQ,CAAC,GAAGqB,IAAI,CAAC,CAAC;EACvE;AACJ;AACA,SAASC,oBAAoBA,CAACZ,IAAI,GAAG5D,MAAM,CAACE,MAAM,CAAC,EAAEoE,SAAS,GAAG3E,cAAc,EAAE;EAC7E,OAAO,IAAIyE,gBAAgB,CAACR,IAAI,CAACM,iBAAiB,CAACjB,IAAI,CAACW,IAAI,CAAC,EAAEU,SAAS,CAAC;AAC7E;AACA,SAASG,oBAAoBA,CAACb,IAAI,GAAG5D,MAAM,CAACE,MAAM,CAAC,EAAEoE,SAAS,GAAG3E,cAAc,EAAE;EAC7E,OAAO,IAAIyE,gBAAgB,CAACR,IAAI,CAACE,GAAG,CAACb,IAAI,CAACW,IAAI,CAAC,EAAEU,SAAS,CAAC;AAC/D;;AAEA;AACA;AACA;;AAEA,SAAS/C,YAAY,EAAEc,wBAAwB,EAAElC,eAAe,EAAEY,eAAe,EAAEW,kBAAkB,EAAEM,QAAQ,EAAEI,gBAAgB,EAAEP,iBAAiB,EAAEW,mBAAmB,EAAEG,aAAa,EAAEb,kBAAkB,EAAEe,qBAAqB,EAAEzB,iBAAiB,EAAE2B,qBAAqB,EAAES,QAAQ,EAAEW,gBAAgB,EAAEF,WAAW,EAAEO,oBAAoB,EAAEb,WAAW,EAAEc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}