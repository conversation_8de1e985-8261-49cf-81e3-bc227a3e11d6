{"ast": null, "code": "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { inject, Directive, Output, Component, ChangeDetectionStrategy } from '@angular/core';\nimport { shouldCall } from '@taiga-ui/event-plugins';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { merge, debounceTime, map, distinctUntilChanged } from 'rxjs';\nconst _c0 = [\"*\"];\nclass TuiElasticContainerDirective {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.tuiElasticContainer = merge(inject(ResizeObserverService, {\n      self: true\n    }), inject(MutationObserverService, {\n      self: true\n    })).pipe(debounceTime(0), map(() => this.el.clientHeight - 1), distinctUntilChanged());\n  }\n  static {\n    this.ɵfac = function TuiElasticContainerDirective_Factory(t) {\n      return new (t || TuiElasticContainerDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiElasticContainerDirective,\n      selectors: [[\"\", \"tuiElasticContainer\", \"\"]],\n      outputs: {\n        tuiElasticContainer: \"tuiElasticContainer\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([ResizeObserverService, MutationObserverService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          childList: true,\n          characterData: true,\n          subtree: true\n        }\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiElasticContainerDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiElasticContainer]',\n      providers: [ResizeObserverService, MutationObserverService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          childList: true,\n          characterData: true,\n          subtree: true\n        }\n      }]\n    }]\n  }], null, {\n    tuiElasticContainer: [{\n      type: Output\n    }]\n  });\n})();\nclass TuiElasticContainer {\n  constructor() {\n    this.height = NaN;\n    this.transitions = 0;\n  }\n  onAnimation(_name, count) {\n    this.transitions += count;\n  }\n  static {\n    this.ɵfac = function TuiElasticContainer_Factory(t) {\n      return new (t || TuiElasticContainer)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiElasticContainer,\n      selectors: [[\"tui-elastic-container\"]],\n      hostVars: 4,\n      hostBindings: function TuiElasticContainer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"height\", ctx.height, \"px\");\n          i0.ɵɵclassProp(\"_transitioning\", ctx.transitions);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"t-wrapper\", 3, \"transitioncancel.zoneless\", \"transitionend.zoneless\", \"transitionstart.zoneless\", \"tuiElasticContainer\"]],\n      template: function TuiElasticContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"transitioncancel.zoneless\", function TuiElasticContainer_Template_div_transitioncancel_zoneless_0_listener($event) {\n            return ctx.onAnimation($event.propertyName, -1);\n          })(\"transitionend.zoneless\", function TuiElasticContainer_Template_div_transitionend_zoneless_0_listener($event) {\n            return ctx.onAnimation($event.propertyName, -1);\n          })(\"transitionstart.zoneless\", function TuiElasticContainer_Template_div_transitionstart_zoneless_0_listener($event) {\n            return ctx.onAnimation($event.propertyName, 1);\n          })(\"tuiElasticContainer\", function TuiElasticContainer_Template_div_tuiElasticContainer_0_listener($event) {\n            return ctx.height = $event;\n          });\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [TuiElasticContainerDirective],\n      styles: [\"[_nghost-%COMP%]{transition-property:height;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:block;overflow:hidden}._transitioning[_nghost-%COMP%]{block-size:auto!important}.t-wrapper[_ngcontent-%COMP%]{padding-top:1px;margin-top:-1px}\"],\n      changeDetection: 0\n    });\n  }\n}\n__decorate([shouldCall(name => name === 'height')], TuiElasticContainer.prototype, \"onAnimation\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiElasticContainer, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-elastic-container',\n      imports: [TuiElasticContainerDirective],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[style.height.px]': 'height',\n        '[class._transitioning]': 'transitions'\n      },\n      template: \"<div\\n    class=\\\"t-wrapper\\\"\\n    (transitioncancel.zoneless)=\\\"onAnimation($any($event).propertyName, -1)\\\"\\n    (transitionend.zoneless)=\\\"onAnimation($any($event).propertyName, -1)\\\"\\n    (transitionstart.zoneless)=\\\"onAnimation($any($event).propertyName, 1)\\\"\\n    (tuiElasticContainer)=\\\"height = $event\\\"\\n>\\n    <ng-content />\\n</div>\\n\",\n      styles: [\":host{transition-property:height;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:block;overflow:hidden}:host._transitioning{block-size:auto!important}.t-wrapper{padding-top:1px;margin-top:-1px}\\n\"]\n    }]\n  }], null, {\n    onAnimation: []\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiElasticContainer, TuiElasticContainerDirective };", "map": {"version": 3, "names": ["__decorate", "i0", "inject", "Directive", "Output", "Component", "ChangeDetectionStrategy", "shouldCall", "MutationObserverService", "WA_MUTATION_OBSERVER_INIT", "ResizeObserverService", "tuiInjectElement", "merge", "debounceTime", "map", "distinctUntilChanged", "_c0", "TuiElasticContainerDirective", "constructor", "el", "tui<PERSON><PERSON><PERSON><PERSON><PERSON>", "self", "pipe", "clientHeight", "ɵfac", "TuiElasticContainerDirective_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "outputs", "standalone", "features", "ɵɵProvidersFeature", "provide", "useValue", "childList", "characterData", "subtree", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "TuiElasticContainer", "height", "NaN", "transitions", "onAnimation", "_name", "count", "TuiElasticContainer_Factory", "ɵcmp", "ɵɵdefineComponent", "hostVars", "hostBindings", "TuiElasticContainer_HostBindings", "rf", "ctx", "ɵɵstyleProp", "ɵɵclassProp", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "TuiElasticContainer_Template", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵlistener", "TuiElasticContainer_Template_div_transitioncancel_zoneless_0_listener", "$event", "propertyName", "TuiElasticContainer_Template_div_transitionend_zoneless_0_listener", "TuiElasticContainer_Template_div_transitionstart_zoneless_0_listener", "TuiElasticContainer_Template_div_tuiElasticContainer_0_listener", "ɵɵprojection", "ɵɵelementEnd", "dependencies", "styles", "changeDetection", "name", "prototype", "imports", "OnPush", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-elastic-container.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { inject, Directive, Output, Component, ChangeDetectionStrategy } from '@angular/core';\nimport { shouldCall } from '@taiga-ui/event-plugins';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { merge, debounceTime, map, distinctUntilChanged } from 'rxjs';\n\nclass TuiElasticContainerDirective {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.tuiElasticContainer = merge(inject(ResizeObserverService, { self: true }), inject(MutationObserverService, { self: true })).pipe(debounceTime(0), map(() => this.el.clientHeight - 1), distinctUntilChanged());\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiElasticContainerDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiElasticContainerDirective, isStandalone: true, selector: \"[tuiElasticContainer]\", outputs: { tuiElasticContainer: \"tuiElasticContainer\" }, providers: [\n            ResizeObserverService,\n            MutationObserverService,\n            {\n                provide: WA_MUTATION_OBSERVER_INIT,\n                useValue: {\n                    childList: true,\n                    characterData: true,\n                    subtree: true,\n                },\n            },\n        ], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiElasticContainerDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiElasticContainer]',\n                    providers: [\n                        ResizeObserverService,\n                        MutationObserverService,\n                        {\n                            provide: WA_MUTATION_OBSERVER_INIT,\n                            useValue: {\n                                childList: true,\n                                characterData: true,\n                                subtree: true,\n                            },\n                        },\n                    ],\n                }]\n        }], propDecorators: { tuiElasticContainer: [{\n                type: Output\n            }] } });\n\nclass TuiElasticContainer {\n    constructor() {\n        this.height = NaN;\n        this.transitions = 0;\n    }\n    onAnimation(_name, count) {\n        this.transitions += count;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiElasticContainer, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiElasticContainer, isStandalone: true, selector: \"tui-elastic-container\", host: { properties: { \"style.height.px\": \"height\", \"class._transitioning\": \"transitions\" } }, ngImport: i0, template: \"<div\\n    class=\\\"t-wrapper\\\"\\n    (transitioncancel.zoneless)=\\\"onAnimation($any($event).propertyName, -1)\\\"\\n    (transitionend.zoneless)=\\\"onAnimation($any($event).propertyName, -1)\\\"\\n    (transitionstart.zoneless)=\\\"onAnimation($any($event).propertyName, 1)\\\"\\n    (tuiElasticContainer)=\\\"height = $event\\\"\\n>\\n    <ng-content />\\n</div>\\n\", styles: [\":host{transition-property:height;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:block;overflow:hidden}:host._transitioning{block-size:auto!important}.t-wrapper{padding-top:1px;margin-top:-1px}\\n\"], dependencies: [{ kind: \"directive\", type: TuiElasticContainerDirective, selector: \"[tuiElasticContainer]\", outputs: [\"tuiElasticContainer\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\n__decorate([\n    shouldCall((name) => name === 'height')\n], TuiElasticContainer.prototype, \"onAnimation\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiElasticContainer, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-elastic-container', imports: [TuiElasticContainerDirective], changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[style.height.px]': 'height',\n                        '[class._transitioning]': 'transitions',\n                    }, template: \"<div\\n    class=\\\"t-wrapper\\\"\\n    (transitioncancel.zoneless)=\\\"onAnimation($any($event).propertyName, -1)\\\"\\n    (transitionend.zoneless)=\\\"onAnimation($any($event).propertyName, -1)\\\"\\n    (transitionstart.zoneless)=\\\"onAnimation($any($event).propertyName, 1)\\\"\\n    (tuiElasticContainer)=\\\"height = $event\\\"\\n>\\n    <ng-content />\\n</div>\\n\", styles: [\":host{transition-property:height;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:block;overflow:hidden}:host._transitioning{block-size:auto!important}.t-wrapper{padding-top:1px;margin-top:-1px}\\n\"] }]\n        }], propDecorators: { onAnimation: [] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiElasticContainer, TuiElasticContainerDirective };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,QAAQ,eAAe;AAC7F,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,uBAAuB,EAAEC,yBAAyB,QAAQ,gCAAgC;AACnG,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,KAAK,EAAEC,YAAY,EAAEC,GAAG,EAAEC,oBAAoB,QAAQ,MAAM;AAAC,MAAAC,GAAA;AAEtE,MAAMC,4BAA4B,CAAC;EAC/BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAGR,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACS,mBAAmB,GAAGR,KAAK,CAACV,MAAM,CAACQ,qBAAqB,EAAE;MAAEW,IAAI,EAAE;IAAK,CAAC,CAAC,EAAEnB,MAAM,CAACM,uBAAuB,EAAE;MAAEa,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAACT,YAAY,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,MAAM,IAAI,CAACK,EAAE,CAACI,YAAY,GAAG,CAAC,CAAC,EAAER,oBAAoB,CAAC,CAAC,CAAC;EACvN;EACA;IAAS,IAAI,CAACS,IAAI,YAAAC,qCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFT,4BAA4B;IAAA,CAAmD;EAAE;EAC5L;IAAS,IAAI,CAACU,IAAI,kBAD+E1B,EAAE,CAAA2B,iBAAA;MAAAC,IAAA,EACJZ,4BAA4B;MAAAa,SAAA;MAAAC,OAAA;QAAAX,mBAAA;MAAA;MAAAY,UAAA;MAAAC,QAAA,GAD1BhC,EAAE,CAAAiC,kBAAA,CACqJ,CAChPxB,qBAAqB,EACrBF,uBAAuB,EACvB;QACI2B,OAAO,EAAE1B,yBAAyB;QAClC2B,QAAQ,EAAE;UACNC,SAAS,EAAE,IAAI;UACfC,aAAa,EAAE,IAAI;UACnBC,OAAO,EAAE;QACb;MACJ,CAAC,CACJ;IAAA,EAAiB;EAAE;AAC5B;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAdqGvC,EAAE,CAAAwC,iBAAA,CAcXxB,4BAA4B,EAAc,CAAC;IAC3HY,IAAI,EAAE1B,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCV,UAAU,EAAE,IAAI;MAChBW,QAAQ,EAAE,uBAAuB;MACjCC,SAAS,EAAE,CACPlC,qBAAqB,EACrBF,uBAAuB,EACvB;QACI2B,OAAO,EAAE1B,yBAAyB;QAClC2B,QAAQ,EAAE;UACNC,SAAS,EAAE,IAAI;UACfC,aAAa,EAAE,IAAI;UACnBC,OAAO,EAAE;QACb;MACJ,CAAC;IAET,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEnB,mBAAmB,EAAE,CAAC;MACpCS,IAAI,EAAEzB;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyC,mBAAmB,CAAC;EACtB3B,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4B,MAAM,GAAGC,GAAG;IACjB,IAAI,CAACC,WAAW,GAAG,CAAC;EACxB;EACAC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACtB,IAAI,CAACH,WAAW,IAAIG,KAAK;EAC7B;EACA;IAAS,IAAI,CAAC3B,IAAI,YAAA4B,4BAAA1B,CAAA;MAAA,YAAAA,CAAA,IAAyFmB,mBAAmB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAACQ,IAAI,kBA7C+EpD,EAAE,CAAAqD,iBAAA;MAAAzB,IAAA,EA6CJgB,mBAAmB;MAAAf,SAAA;MAAAyB,QAAA;MAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7CjBzD,EAAE,CAAA2D,WAAA,WAAAD,GAAA,CAAAb,MAAA,MA6Cc,CAAC;UA7CjB7C,EAAE,CAAA4D,WAAA,mBAAAF,GAAA,CAAAX,WA6Cc,CAAC;QAAA;MAAA;MAAAhB,UAAA;MAAAC,QAAA,GA7CjBhC,EAAE,CAAA6D,mBAAA;MAAAC,kBAAA,EAAA/C,GAAA;MAAAgD,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzD,EAAE,CAAAoE,eAAA;UAAFpE,EAAE,CAAAqE,cAAA,YA6Cwf,CAAC;UA7C3frE,EAAE,CAAAsE,UAAA,uCAAAC,sEAAAC,MAAA;YAAA,OA6CgQd,GAAA,CAAAV,WAAA,CAAAwB,MAAA,CAAAC,YAAA,GAAwC,CAAC,CAAC;UAAA,CAAC,CAAC,oCAAAC,mEAAAF,MAAA;YAAA,OAAiCd,GAAA,CAAAV,WAAA,CAAAwB,MAAA,CAAAC,YAAA,GAAwC,CAAC,CAAC;UAAA,CAAC,CAAC,sCAAAE,qEAAAH,MAAA;YAAA,OAAmCd,GAAA,CAAAV,WAAA,CAAAwB,MAAA,CAAAC,YAAA,EAAuC,CAAC,CAAC;UAAA,CAAC,CAAC,iCAAAG,gEAAAJ,MAAA;YAAA,OAAAd,GAAA,CAAAb,MAAA,GAAA2B,MAAA;UAAA,CAA8C,CAAC;UA7CxfxE,EAAE,CAAA6E,YAAA,EA6C4gB,CAAC;UA7C/gB7E,EAAE,CAAA8E,YAAA,CA6CohB,CAAC;QAAA;MAAA;MAAAC,YAAA,GAA4S/D,4BAA4B;MAAAgE,MAAA;MAAAC,eAAA;IAAA,EAA+H;EAAE;AACrkC;AACAlF,UAAU,CAAC,CACPO,UAAU,CAAE4E,IAAI,IAAKA,IAAI,KAAK,QAAQ,CAAC,CAC1C,EAAEtC,mBAAmB,CAACuC,SAAS,EAAE,aAAa,EAAE,IAAI,CAAC;AACtD;EAAA,QAAA5C,SAAA,oBAAAA,SAAA,KAlDqGvC,EAAE,CAAAwC,iBAAA,CAkDXI,mBAAmB,EAAc,CAAC;IAClHhB,IAAI,EAAExB,SAAS;IACfqC,IAAI,EAAE,CAAC;MAAEV,UAAU,EAAE,IAAI;MAAEW,QAAQ,EAAE,uBAAuB;MAAE0C,OAAO,EAAE,CAACpE,4BAA4B,CAAC;MAAEiE,eAAe,EAAE5E,uBAAuB,CAACgF,MAAM;MAAEC,IAAI,EAAE;QAClJ,mBAAmB,EAAE,QAAQ;QAC7B,wBAAwB,EAAE;MAC9B,CAAC;MAAEpB,QAAQ,EAAE,0VAA0V;MAAEc,MAAM,EAAE,CAAC,iPAAiP;IAAE,CAAC;EAClnB,CAAC,CAAC,QAAkB;IAAEhC,WAAW,EAAE;EAAG,CAAC;AAAA;;AAE/C;AACA;AACA;;AAEA,SAASJ,mBAAmB,EAAE5B,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}