{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, signal, isSignal, computed, effect, untracked, Directive, Input } from '@angular/core';\nimport { TuiControl, tuiAsControl } from '@taiga-ui/cdk/classes';\nimport { TUI_STRICT_MATCHER, TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiAsOptionContent } from '@taiga-ui/core/components/data-list';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldComponent, TuiTextfieldDirective, tuiInjectAuxiliary, tuiAsTextfieldAccessor, TuiWithTextfield } from '@taiga-ui/core/components/textfield';\nimport { tuiDropdownOpen, tuiDropdownEnabled, TuiDropdownDirective } from '@taiga-ui/core/directives/dropdown';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { TuiSelectOption } from '@taiga-ui/kit/components/select';\nclass TuiComboBox extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.el = tuiInjectElement();\n    this.host = inject(TuiTextfieldComponent);\n    this.textfield = inject(TuiTextfieldDirective);\n    this.open = tuiDropdownOpen();\n    this.dropdownEnabled = tuiDropdownEnabled(this.interactive);\n    this.dropdown = inject(TuiDropdownDirective);\n    this.itemsHandlers = inject(TUI_ITEMS_HANDLERS);\n    this.matcher = signal(TUI_STRICT_MATCHER);\n    this.strict = signal(true);\n    this.datalist = tuiInjectAuxiliary(x => x !== this && 'options' in x && isSignal(x.options));\n    this.options = computed(() => this.datalist()?.options?.() // TODO(v5): remove optional call `?.()`\n    .filter(x => !this.itemsHandlers.disabledItemHandler()(x)) ?? []);\n    this.nonStrictValueEffect = effect(() => {\n      if (!this.options().length && !this.strict()) {\n        this.onChange(this.textfield.value() || null);\n      }\n    }, TUI_ALLOW_SIGNAL_WRITES);\n    this.matchingEffect = effect(() => {\n      const options = this.options();\n      const matcher = this.matcher();\n      if (!options.length || !matcher) {\n        return;\n      }\n      const textfieldValue = this.textfield.value();\n      const selectedOption = options.find(x => matcher(x, textfieldValue, this.itemsHandlers.stringify())) ?? null;\n      const stringified = this.stringify(selectedOption);\n      const fallback = this.strict() || !textfieldValue ? null : textfieldValue;\n      this.onChange(selectedOption ?? fallback);\n      if (stringified && stringified !== textfieldValue) {\n        this.textfield.value.set(stringified);\n      }\n    }, TUI_ALLOW_SIGNAL_WRITES);\n    this.blurEffect = effect(() => {\n      const incomplete = untracked(() => this.strict() && !this.value());\n      if (!this.host.focused() && incomplete) {\n        this.textfield.value.set('');\n      }\n    }, TUI_ALLOW_SIGNAL_WRITES);\n  }\n  // TODO(v5): use signal input\n  set strictSetter(x) {\n    this.strict.set(x);\n  }\n  // TODO(v5): use signal input\n  set matcherSetter(x) {\n    this.matcher.set(x);\n  }\n  setValue(value) {\n    const stringified = this.stringify(value);\n    if (stringified !== this.textfield.value()) {\n      this.textfield.value.set(stringified);\n      setTimeout((end = stringified.length) => this.el.setSelectionRange(end, end));\n    }\n    this.onChange(value);\n    if (!value) {\n      this.toggleDropdown(true);\n    }\n  }\n  writeValue(value) {\n    super.writeValue(value);\n    this.textfield.value.set(this.stringify(value));\n  }\n  toggleDropdown(open = !this.open()) {\n    if (this.dropdownEnabled() && this.dropdown._content()) {\n      this.open.set(open);\n    }\n  }\n  onInput() {\n    setTimeout(() => this.toggleDropdown(true));\n  }\n  keydownEnter(event) {\n    if (!this.open()) {\n      return;\n    }\n    event.preventDefault();\n    const options = this.options();\n    if (options.length === 1 && options[0]) {\n      this.setValue(options[0]);\n      this.toggleDropdown(false);\n    }\n  }\n  stringify(value) {\n    return value ? this.itemsHandlers.stringify()(value) : '';\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiComboBox_BaseFactory;\n      return function TuiComboBox_Factory(t) {\n        return (ɵTuiComboBox_BaseFactory || (ɵTuiComboBox_BaseFactory = i0.ɵɵgetInheritedFactory(TuiComboBox)))(t || TuiComboBox);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiComboBox,\n      selectors: [[\"input\", \"tuiComboBox\", \"\"]],\n      hostVars: 1,\n      hostBindings: function TuiComboBox_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TuiComboBox_click_HostBindingHandler() {\n            return ctx.toggleDropdown();\n          })(\"input\", function TuiComboBox_input_HostBindingHandler() {\n            return ctx.onInput();\n          })(\"keydown.enter\", function TuiComboBox_keydown_enter_HostBindingHandler($event) {\n            return ctx.keydownEnter($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"disabled\", ctx.disabled());\n        }\n      },\n      inputs: {\n        strictSetter: [i0.ɵɵInputFlags.None, \"strict\", \"strictSetter\"],\n        matcherSetter: [i0.ɵɵInputFlags.None, \"matcher\", \"matcherSetter\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsOptionContent(TuiSelectOption), tuiAsTextfieldAccessor(TuiComboBox), tuiAsControl(TuiComboBox)]), i0.ɵɵHostDirectivesFeature([i1.TuiWithTextfield]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiComboBox, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiComboBox]',\n      providers: [tuiAsOptionContent(TuiSelectOption), tuiAsTextfieldAccessor(TuiComboBox), tuiAsControl(TuiComboBox)],\n      hostDirectives: [TuiWithTextfield],\n      host: {\n        '[disabled]': 'disabled()',\n        '(click)': 'toggleDropdown()',\n        '(input)': 'onInput()',\n        '(keydown.enter)': 'keydownEnter($event)'\n      }\n    }]\n  }], null, {\n    strictSetter: [{\n      type: Input,\n      args: ['strict']\n    }],\n    matcherSetter: [{\n      type: Input,\n      args: ['matcher']\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiComboBox };", "map": {"version": 3, "names": ["i0", "inject", "signal", "isSignal", "computed", "effect", "untracked", "Directive", "Input", "TuiControl", "tuiAsControl", "TUI_STRICT_MATCHER", "TUI_ALLOW_SIGNAL_WRITES", "tuiInjectElement", "tuiAsOptionContent", "i1", "TuiTextfieldComponent", "TuiTextfieldDirective", "tuiInjectAuxiliary", "tuiAsTextfieldAccessor", "TuiWithTextfield", "tuiDropdownOpen", "tuiDropdownEnabled", "TuiDropdownDirective", "TUI_ITEMS_HANDLERS", "TuiSelectOption", "TuiComboBox", "constructor", "arguments", "el", "host", "textfield", "open", "dropdownEnabled", "interactive", "dropdown", "itemsHandlers", "matcher", "strict", "datalist", "x", "options", "filter", "disabledItemHandler", "nonStrictValueEffect", "length", "onChange", "value", "matchingEffect", "textfieldValue", "selectedOption", "find", "stringify", "stringified", "fallback", "set", "blurEffect", "incomplete", "focused", "strictSetter", "matcherSetter", "setValue", "setTimeout", "end", "setSelectionRange", "toggleDropdown", "writeValue", "_content", "onInput", "keydownEnter", "event", "preventDefault", "ɵfac", "ɵTuiComboBox_BaseFactory", "TuiComboBox_Factory", "t", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostVars", "hostBindings", "TuiComboBox_HostBindings", "rf", "ctx", "ɵɵlistener", "TuiComboBox_click_HostBindingHandler", "TuiComboBox_input_HostBindingHandler", "TuiComboBox_keydown_enter_HostBindingHandler", "$event", "ɵɵhostProperty", "disabled", "inputs", "ɵɵInputFlags", "None", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "hostDirectives"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-combo-box.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, signal, isSignal, computed, effect, untracked, Directive, Input } from '@angular/core';\nimport { TuiControl, tuiAsControl } from '@taiga-ui/cdk/classes';\nimport { TUI_STRICT_MATCHER, TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiAsOptionContent } from '@taiga-ui/core/components/data-list';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldComponent, TuiTextfieldDirective, tuiInjectAuxiliary, tuiAsTextfieldAccessor, TuiWithTextfield } from '@taiga-ui/core/components/textfield';\nimport { tuiDropdownOpen, tuiDropdownEnabled, TuiDropdownDirective } from '@taiga-ui/core/directives/dropdown';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { TuiSelectOption } from '@taiga-ui/kit/components/select';\n\nclass TuiComboBox extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.el = tuiInjectElement();\n        this.host = inject(TuiTextfieldComponent);\n        this.textfield = inject(TuiTextfieldDirective);\n        this.open = tuiDropdownOpen();\n        this.dropdownEnabled = tuiDropdownEnabled(this.interactive);\n        this.dropdown = inject(TuiDropdownDirective);\n        this.itemsHandlers = inject(TUI_ITEMS_HANDLERS);\n        this.matcher = signal(TUI_STRICT_MATCHER);\n        this.strict = signal(true);\n        this.datalist = tuiInjectAuxiliary((x) => x !== this && 'options' in x && isSignal(x.options));\n        this.options = computed(() => this.datalist()\n            ?.options?.() // TODO(v5): remove optional call `?.()`\n            .filter((x) => !this.itemsHandlers.disabledItemHandler()(x)) ?? []);\n        this.nonStrictValueEffect = effect(() => {\n            if (!this.options().length && !this.strict()) {\n                this.onChange(this.textfield.value() || null);\n            }\n        }, TUI_ALLOW_SIGNAL_WRITES);\n        this.matchingEffect = effect(() => {\n            const options = this.options();\n            const matcher = this.matcher();\n            if (!options.length || !matcher) {\n                return;\n            }\n            const textfieldValue = this.textfield.value();\n            const selectedOption = options.find((x) => matcher(x, textfieldValue, this.itemsHandlers.stringify())) ?? null;\n            const stringified = this.stringify(selectedOption);\n            const fallback = this.strict() || !textfieldValue ? null : textfieldValue;\n            this.onChange(selectedOption ?? fallback);\n            if (stringified && stringified !== textfieldValue) {\n                this.textfield.value.set(stringified);\n            }\n        }, TUI_ALLOW_SIGNAL_WRITES);\n        this.blurEffect = effect(() => {\n            const incomplete = untracked(() => this.strict() && !this.value());\n            if (!this.host.focused() && incomplete) {\n                this.textfield.value.set('');\n            }\n        }, TUI_ALLOW_SIGNAL_WRITES);\n    }\n    // TODO(v5): use signal input\n    set strictSetter(x) {\n        this.strict.set(x);\n    }\n    // TODO(v5): use signal input\n    set matcherSetter(x) {\n        this.matcher.set(x);\n    }\n    setValue(value) {\n        const stringified = this.stringify(value);\n        if (stringified !== this.textfield.value()) {\n            this.textfield.value.set(stringified);\n            setTimeout((end = stringified.length) => this.el.setSelectionRange(end, end));\n        }\n        this.onChange(value);\n        if (!value) {\n            this.toggleDropdown(true);\n        }\n    }\n    writeValue(value) {\n        super.writeValue(value);\n        this.textfield.value.set(this.stringify(value));\n    }\n    toggleDropdown(open = !this.open()) {\n        if (this.dropdownEnabled() && this.dropdown._content()) {\n            this.open.set(open);\n        }\n    }\n    onInput() {\n        setTimeout(() => this.toggleDropdown(true));\n    }\n    keydownEnter(event) {\n        if (!this.open()) {\n            return;\n        }\n        event.preventDefault();\n        const options = this.options();\n        if (options.length === 1 && options[0]) {\n            this.setValue(options[0]);\n            this.toggleDropdown(false);\n        }\n    }\n    stringify(value) {\n        return value ? this.itemsHandlers.stringify()(value) : '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiComboBox, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiComboBox, isStandalone: true, selector: \"input[tuiComboBox]\", inputs: { strictSetter: [\"strict\", \"strictSetter\"], matcherSetter: [\"matcher\", \"matcherSetter\"] }, host: { listeners: { \"click\": \"toggleDropdown()\", \"input\": \"onInput()\", \"keydown.enter\": \"keydownEnter($event)\" }, properties: { \"disabled\": \"disabled()\" } }, providers: [\n            tuiAsOptionContent(TuiSelectOption),\n            tuiAsTextfieldAccessor(TuiComboBox),\n            tuiAsControl(TuiComboBox),\n        ], usesInheritance: true, hostDirectives: [{ directive: i1.TuiWithTextfield }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiComboBox, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiComboBox]',\n                    providers: [\n                        tuiAsOptionContent(TuiSelectOption),\n                        tuiAsTextfieldAccessor(TuiComboBox),\n                        tuiAsControl(TuiComboBox),\n                    ],\n                    hostDirectives: [TuiWithTextfield],\n                    host: {\n                        '[disabled]': 'disabled()',\n                        '(click)': 'toggleDropdown()',\n                        '(input)': 'onInput()',\n                        '(keydown.enter)': 'keydownEnter($event)',\n                    },\n                }]\n        }], propDecorators: { strictSetter: [{\n                type: Input,\n                args: ['strict']\n            }], matcherSetter: [{\n                type: Input,\n                args: ['matcher']\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiComboBox };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACvG,SAASC,UAAU,EAAEC,YAAY,QAAQ,uBAAuB;AAChE,SAASC,kBAAkB,EAAEC,uBAAuB,QAAQ,yBAAyB;AACrF,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,qBAAqB,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,sBAAsB,EAAEC,gBAAgB,QAAQ,qCAAqC;AAChK,SAASC,eAAe,EAAEC,kBAAkB,EAAEC,oBAAoB,QAAQ,oCAAoC;AAC9G,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,eAAe,QAAQ,iCAAiC;AAEjE,MAAMC,WAAW,SAASjB,UAAU,CAAC;EACjCkB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,EAAE,GAAGhB,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACiB,IAAI,GAAG7B,MAAM,CAACe,qBAAqB,CAAC;IACzC,IAAI,CAACe,SAAS,GAAG9B,MAAM,CAACgB,qBAAqB,CAAC;IAC9C,IAAI,CAACe,IAAI,GAAGX,eAAe,CAAC,CAAC;IAC7B,IAAI,CAACY,eAAe,GAAGX,kBAAkB,CAAC,IAAI,CAACY,WAAW,CAAC;IAC3D,IAAI,CAACC,QAAQ,GAAGlC,MAAM,CAACsB,oBAAoB,CAAC;IAC5C,IAAI,CAACa,aAAa,GAAGnC,MAAM,CAACuB,kBAAkB,CAAC;IAC/C,IAAI,CAACa,OAAO,GAAGnC,MAAM,CAACS,kBAAkB,CAAC;IACzC,IAAI,CAAC2B,MAAM,GAAGpC,MAAM,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACqC,QAAQ,GAAGrB,kBAAkB,CAAEsB,CAAC,IAAKA,CAAC,KAAK,IAAI,IAAI,SAAS,IAAIA,CAAC,IAAIrC,QAAQ,CAACqC,CAAC,CAACC,OAAO,CAAC,CAAC;IAC9F,IAAI,CAACA,OAAO,GAAGrC,QAAQ,CAAC,MAAM,IAAI,CAACmC,QAAQ,CAAC,CAAC,EACvCE,OAAO,GAAG,CAAC,CAAC;IAAA,CACbC,MAAM,CAAEF,CAAC,IAAK,CAAC,IAAI,CAACJ,aAAa,CAACO,mBAAmB,CAAC,CAAC,CAACH,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACvE,IAAI,CAACI,oBAAoB,GAAGvC,MAAM,CAAC,MAAM;MACrC,IAAI,CAAC,IAAI,CAACoC,OAAO,CAAC,CAAC,CAACI,MAAM,IAAI,CAAC,IAAI,CAACP,MAAM,CAAC,CAAC,EAAE;QAC1C,IAAI,CAACQ,QAAQ,CAAC,IAAI,CAACf,SAAS,CAACgB,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC;MACjD;IACJ,CAAC,EAAEnC,uBAAuB,CAAC;IAC3B,IAAI,CAACoC,cAAc,GAAG3C,MAAM,CAAC,MAAM;MAC/B,MAAMoC,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;MAC9B,MAAMJ,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;MAC9B,IAAI,CAACI,OAAO,CAACI,MAAM,IAAI,CAACR,OAAO,EAAE;QAC7B;MACJ;MACA,MAAMY,cAAc,GAAG,IAAI,CAAClB,SAAS,CAACgB,KAAK,CAAC,CAAC;MAC7C,MAAMG,cAAc,GAAGT,OAAO,CAACU,IAAI,CAAEX,CAAC,IAAKH,OAAO,CAACG,CAAC,EAAES,cAAc,EAAE,IAAI,CAACb,aAAa,CAACgB,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;MAC9G,MAAMC,WAAW,GAAG,IAAI,CAACD,SAAS,CAACF,cAAc,CAAC;MAClD,MAAMI,QAAQ,GAAG,IAAI,CAAChB,MAAM,CAAC,CAAC,IAAI,CAACW,cAAc,GAAG,IAAI,GAAGA,cAAc;MACzE,IAAI,CAACH,QAAQ,CAACI,cAAc,IAAII,QAAQ,CAAC;MACzC,IAAID,WAAW,IAAIA,WAAW,KAAKJ,cAAc,EAAE;QAC/C,IAAI,CAAClB,SAAS,CAACgB,KAAK,CAACQ,GAAG,CAACF,WAAW,CAAC;MACzC;IACJ,CAAC,EAAEzC,uBAAuB,CAAC;IAC3B,IAAI,CAAC4C,UAAU,GAAGnD,MAAM,CAAC,MAAM;MAC3B,MAAMoD,UAAU,GAAGnD,SAAS,CAAC,MAAM,IAAI,CAACgC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAACS,KAAK,CAAC,CAAC,CAAC;MAClE,IAAI,CAAC,IAAI,CAACjB,IAAI,CAAC4B,OAAO,CAAC,CAAC,IAAID,UAAU,EAAE;QACpC,IAAI,CAAC1B,SAAS,CAACgB,KAAK,CAACQ,GAAG,CAAC,EAAE,CAAC;MAChC;IACJ,CAAC,EAAE3C,uBAAuB,CAAC;EAC/B;EACA;EACA,IAAI+C,YAAYA,CAACnB,CAAC,EAAE;IAChB,IAAI,CAACF,MAAM,CAACiB,GAAG,CAACf,CAAC,CAAC;EACtB;EACA;EACA,IAAIoB,aAAaA,CAACpB,CAAC,EAAE;IACjB,IAAI,CAACH,OAAO,CAACkB,GAAG,CAACf,CAAC,CAAC;EACvB;EACAqB,QAAQA,CAACd,KAAK,EAAE;IACZ,MAAMM,WAAW,GAAG,IAAI,CAACD,SAAS,CAACL,KAAK,CAAC;IACzC,IAAIM,WAAW,KAAK,IAAI,CAACtB,SAAS,CAACgB,KAAK,CAAC,CAAC,EAAE;MACxC,IAAI,CAAChB,SAAS,CAACgB,KAAK,CAACQ,GAAG,CAACF,WAAW,CAAC;MACrCS,UAAU,CAAC,CAACC,GAAG,GAAGV,WAAW,CAACR,MAAM,KAAK,IAAI,CAAChB,EAAE,CAACmC,iBAAiB,CAACD,GAAG,EAAEA,GAAG,CAAC,CAAC;IACjF;IACA,IAAI,CAACjB,QAAQ,CAACC,KAAK,CAAC;IACpB,IAAI,CAACA,KAAK,EAAE;MACR,IAAI,CAACkB,cAAc,CAAC,IAAI,CAAC;IAC7B;EACJ;EACAC,UAAUA,CAACnB,KAAK,EAAE;IACd,KAAK,CAACmB,UAAU,CAACnB,KAAK,CAAC;IACvB,IAAI,CAAChB,SAAS,CAACgB,KAAK,CAACQ,GAAG,CAAC,IAAI,CAACH,SAAS,CAACL,KAAK,CAAC,CAAC;EACnD;EACAkB,cAAcA,CAACjC,IAAI,GAAG,CAAC,IAAI,CAACA,IAAI,CAAC,CAAC,EAAE;IAChC,IAAI,IAAI,CAACC,eAAe,CAAC,CAAC,IAAI,IAAI,CAACE,QAAQ,CAACgC,QAAQ,CAAC,CAAC,EAAE;MACpD,IAAI,CAACnC,IAAI,CAACuB,GAAG,CAACvB,IAAI,CAAC;IACvB;EACJ;EACAoC,OAAOA,CAAA,EAAG;IACNN,UAAU,CAAC,MAAM,IAAI,CAACG,cAAc,CAAC,IAAI,CAAC,CAAC;EAC/C;EACAI,YAAYA,CAACC,KAAK,EAAE;IAChB,IAAI,CAAC,IAAI,CAACtC,IAAI,CAAC,CAAC,EAAE;MACd;IACJ;IACAsC,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,MAAM9B,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;IAC9B,IAAIA,OAAO,CAACI,MAAM,KAAK,CAAC,IAAIJ,OAAO,CAAC,CAAC,CAAC,EAAE;MACpC,IAAI,CAACoB,QAAQ,CAACpB,OAAO,CAAC,CAAC,CAAC,CAAC;MACzB,IAAI,CAACwB,cAAc,CAAC,KAAK,CAAC;IAC9B;EACJ;EACAb,SAASA,CAACL,KAAK,EAAE;IACb,OAAOA,KAAK,GAAG,IAAI,CAACX,aAAa,CAACgB,SAAS,CAAC,CAAC,CAACL,KAAK,CAAC,GAAG,EAAE;EAC7D;EACA;IAAS,IAAI,CAACyB,IAAI;MAAA,IAAAC,wBAAA;MAAA,gBAAAC,oBAAAC,CAAA;QAAA,QAAAF,wBAAA,KAAAA,wBAAA,GAA+EzE,EAAE,CAAA4E,qBAAA,CAAQlD,WAAW,IAAAiD,CAAA,IAAXjD,WAAW;MAAA;IAAA,IAAqD;EAAE;EAC7K;IAAS,IAAI,CAACmD,IAAI,kBAD+E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EACJrD,WAAW;MAAAsD,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADTpF,EAAE,CAAAsF,UAAA,mBAAAC,qCAAA;YAAA,OACJF,GAAA,CAAApB,cAAA,CAAe,CAAC;UAAA,CAAN,CAAC,mBAAAuB,qCAAA;YAAA,OAAXH,GAAA,CAAAjB,OAAA,CAAQ,CAAC;UAAA,CAAC,CAAC,2BAAAqB,6CAAAC,MAAA;YAAA,OAAXL,GAAA,CAAAhB,YAAA,CAAAqB,MAAmB,CAAC;UAAA,CAAV,CAAC;QAAA;QAAA,IAAAN,EAAA;UADTpF,EAAE,CAAA2F,cAAA,aACJN,GAAA,CAAAO,QAAA,CAAS,CAAC,CAAC;QAAA;MAAA;MAAAC,MAAA;QAAAlC,YAAA,GADT3D,EAAE,CAAA8F,YAAA,CAAAC,IAAA;QAAAnC,aAAA,GAAF5D,EAAE,CAAA8F,YAAA,CAAAC,IAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFjG,EAAE,CAAAkG,kBAAA,CAC0U,CACrapF,kBAAkB,CAACW,eAAe,CAAC,EACnCN,sBAAsB,CAACO,WAAW,CAAC,EACnChB,YAAY,CAACgB,WAAW,CAAC,CAC5B,GAL4F1B,EAAE,CAAAmG,uBAAA,EAKvCpF,EAAE,CAACK,gBAAgB,IALkBpB,EAAE,CAAAoG,0BAAA;IAAA,EAKA;EAAE;AACzG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAPqGrG,EAAE,CAAAsG,iBAAA,CAOX5E,WAAW,EAAc,CAAC;IAC1GqD,IAAI,EAAExE,SAAS;IACfgG,IAAI,EAAE,CAAC;MACCP,UAAU,EAAE,IAAI;MAChBQ,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CACP3F,kBAAkB,CAACW,eAAe,CAAC,EACnCN,sBAAsB,CAACO,WAAW,CAAC,EACnChB,YAAY,CAACgB,WAAW,CAAC,CAC5B;MACDgF,cAAc,EAAE,CAACtF,gBAAgB,CAAC;MAClCU,IAAI,EAAE;QACF,YAAY,EAAE,YAAY;QAC1B,SAAS,EAAE,kBAAkB;QAC7B,SAAS,EAAE,WAAW;QACtB,iBAAiB,EAAE;MACvB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE6B,YAAY,EAAE,CAAC;MAC7BoB,IAAI,EAAEvE,KAAK;MACX+F,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAE3C,aAAa,EAAE,CAAC;MAChBmB,IAAI,EAAEvE,KAAK;MACX+F,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAAS7E,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}