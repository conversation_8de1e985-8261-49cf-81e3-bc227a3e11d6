{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction AppComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵelement(1, \"app-cpq-configurator\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26);\n    i0.ɵɵtext(3, \"\\uD83D\\uDEA7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"Gestion des Devis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Cette fonctionnalit\\u00E9 sera bient\\u00F4t disponible.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AppComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26);\n    i0.ɵɵtext(3, \"\\uD83D\\uDEA7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"Catalogue Produits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Cette fonctionnalit\\u00E9 sera bient\\u00F4t disponible.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class AppComponent {\n  constructor() {\n    this.title = 'Application CPQ - Configure Price Quote';\n    // Variables pour la navigation future\n    this.activeTab = 'cpq-configurator';\n  }\n  // Méthodes pour la navigation (pour futurs composants)\n  setActiveTab(tab) {\n    this.activeTab = tab;\n  }\n  isTabActive(tab) {\n    return this.activeTab === tab;\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 45,\n      vars: 10,\n      consts: [[1, \"app-container\"], [1, \"app-header\"], [1, \"header-content\"], [1, \"app-title\"], [1, \"app-icon\"], [1, \"header-info\"], [1, \"version-badge\"], [1, \"environment-badge\"], [1, \"app-navigation\"], [1, \"nav-container\"], [1, \"nav-tabs\"], [1, \"nav-item\"], [1, \"nav-link\", 3, \"click\"], [1, \"nav-icon\"], [\"disabled\", \"\", 1, \"nav-link\", \"disabled\", 3, \"click\"], [1, \"coming-soon\"], [1, \"app-main\"], [1, \"main-container\"], [\"class\", \"tab-content active\", 4, \"ngIf\"], [1, \"app-footer\"], [1, \"footer-content\"], [1, \"footer-left\"], [1, \"copyright\"], [1, \"footer-right\"], [1, \"tab-content\", \"active\"], [1, \"coming-soon-content\"], [1, \"coming-soon-icon\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"h1\", 3)(4, \"span\", 4);\n          i0.ɵɵtext(5, \"\\uD83C\\uDFAF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"span\", 6);\n          i0.ɵɵtext(9, \"v1.0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"span\", 7);\n          i0.ɵɵtext(11, \"Dynamics 365\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"nav\", 8)(13, \"div\", 9)(14, \"ul\", 10)(15, \"li\", 11)(16, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function AppComponent_Template_button_click_16_listener() {\n            return ctx.setActiveTab(\"cpq-configurator\");\n          });\n          i0.ɵɵelementStart(17, \"span\", 13);\n          i0.ɵɵtext(18, \"\\u2699\\uFE0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(19, \" Configurateur CPQ \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"li\", 11)(21, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function AppComponent_Template_button_click_21_listener() {\n            return ctx.setActiveTab(\"quote-manager\");\n          });\n          i0.ɵɵelementStart(22, \"span\", 13);\n          i0.ɵɵtext(23, \"\\uD83D\\uDCCB\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24, \" Gestion des Devis \");\n          i0.ɵɵelementStart(25, \"span\", 15);\n          i0.ɵɵtext(26, \"Bient\\u00F4t\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"li\", 11)(28, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function AppComponent_Template_button_click_28_listener() {\n            return ctx.setActiveTab(\"product-catalog\");\n          });\n          i0.ɵɵelementStart(29, \"span\", 13);\n          i0.ɵɵtext(30, \"\\uD83D\\uDCE6\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(31, \" Catalogue Produits \");\n          i0.ɵɵelementStart(32, \"span\", 15);\n          i0.ɵɵtext(33, \"Bient\\u00F4t\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(34, \"main\", 16)(35, \"div\", 17);\n          i0.ɵɵtemplate(36, AppComponent_div_36_Template, 2, 0, \"div\", 18)(37, AppComponent_div_37_Template, 8, 0, \"div\", 18)(38, AppComponent_div_38_Template, 8, 0, \"div\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"footer\", 19)(40, \"div\", 20)(41, \"div\", 21)(42, \"span\", 22);\n          i0.ɵɵtext(43, \"\\u00A9 2024 Application CPQ - Dynamics 365\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(44, \"div\", 23);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", ctx.title, \" \");\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"active\", ctx.isTabActive(\"cpq-configurator\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"active\", ctx.isTabActive(\"quote-manager\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"active\", ctx.isTabActive(\"product-catalog\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"cpq-configurator\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"quote-manager\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"product-catalog\");\n        }\n      },\n      dependencies: [i1.NgIf],\n      styles: [\"\\n\\n[_ngcontent-%COMP%]:root {\\n  --primary-color: #0078d4;\\n  --primary-hover: #106ebe;\\n  --secondary-color: #6c757d;\\n  --success-color: #28a745;\\n  --warning-color: #ffc107;\\n  --danger-color: #dc3545;\\n  --light-color: #f8f9fa;\\n  --dark-color: #343a40;\\n  --darker-color: #212529;\\n  --border-color: #dee2e6;\\n  --shadow-sm: 0 1px 3px rgba(0,0,0,0.05);\\n  --shadow-md: 0 2px 4px rgba(0,0,0,0.1);\\n  --shadow-lg: 0 4px 8px rgba(0,0,0,0.15);\\n  --border-radius: 8px;\\n  --border-radius-sm: 4px;\\n  --border-radius-lg: 12px;\\n  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n  --transition: all 0.2s ease;\\n  --max-width: 1200px;\\n}\\n\\n\\n\\n.app-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: var(--light-color);\\n  font-family: var(--font-family);\\n  padding-top: 0; \\n\\n}\\n\\n\\n\\n.app-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--dark-color) 0%, var(--darker-color) 100%);\\n  color: #34495e;\\n  padding: 1rem 0;\\n  box-shadow: var(--shadow-md);\\n  position: static; \\n\\n  width: 100%;\\n  z-index: 1000;\\n  animation: _ngcontent-%COMP%_slideInFromTop 0.5s ease-out;\\n}\\n\\n\\n\\n.app-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: var(--light-color);\\n  font-family: var(--font-family);\\n  padding-top: 0; \\n\\n}\\n\\n.header-content[_ngcontent-%COMP%], .nav-container[_ngcontent-%COMP%], .main-container[_ngcontent-%COMP%], .footer-content[_ngcontent-%COMP%] {\\n  max-width: var(--max-width);\\n  margin: 0 auto;\\n  padding: 0 2rem;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.app-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.app-icon[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n}\\n\\n.header-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: center;\\n}\\n\\n.version-badge[_ngcontent-%COMP%], .environment-badge[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n  padding: 0.25rem 0.5rem;\\n  border-radius: var(--border-radius-lg);\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.app-navigation[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-bottom: 1px solid var(--border-color);\\n  box-shadow: var(--shadow-sm);\\n  animation: _ngcontent-%COMP%_slideInFromTop 0.5s ease-out 0.1s both;\\n}\\n\\n.nav-tabs[_ngcontent-%COMP%] {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n  display: flex;\\n  gap: 0;\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 1rem 1.5rem;\\n  background: none;\\n  border: none;\\n  color: var(--secondary-color);\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  border-bottom: 3px solid transparent;\\n  position: relative;\\n  overflow: hidden;\\n  z-index: 1;\\n}\\n\\n.nav-link[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(0, 120, 212, 0.1), transparent);\\n  transition: left 0.5s;\\n}\\n\\n.nav-link[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 50%;\\n  width: 0;\\n  height: 3px;\\n  background-color: var(--primary-color);\\n  transition: all 0.3s ease;\\n  transform: translateX(-50%);\\n}\\n\\n.nav-link[_ngcontent-%COMP%]:hover:not(.disabled)::before {\\n  left: 100%;\\n}\\n\\n.nav-link[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  color: var(--primary-color);\\n  background-color: var(--light-color);\\n}\\n\\n.nav-link[_ngcontent-%COMP%]:hover:not(.disabled):not(.active)::after {\\n  width: 80%;\\n}\\n\\n.nav-link.active[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  border-bottom-color: var(--primary-color);\\n  background-color: var(--light-color);\\n}\\n\\n.nav-link.active[_ngcontent-%COMP%]::after {\\n  width: 100%;\\n}\\n\\n.nav-link.disabled[_ngcontent-%COMP%] {\\n  color: #adb5bd;\\n  cursor: not-allowed;\\n  opacity: 0.6;\\n}\\n\\n.nav-link[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--primary-color);\\n  outline-offset: -2px;\\n}\\n\\n.nav-link[_ngcontent-%COMP%]:focus-visible {\\n  outline: 2px solid var(--primary-color);\\n  outline-offset: 2px;\\n  border-radius: var(--border-radius-sm);\\n}\\n\\n.nav-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n\\n.coming-soon[_ngcontent-%COMP%] {\\n  background-color: var(--warning-color);\\n  color: #212529;\\n  font-size: 0.6rem;\\n  padding: 0.1rem 0.3rem;\\n  border-radius: var(--border-radius-sm);\\n  margin-left: 0.5rem;\\n  font-weight: 600;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n\\n\\n.app-main[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 2rem 0;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  display: none;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n.tab-content.active[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n\\n\\n.coming-soon-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 4rem 2rem;\\n  background-color: white;\\n  border-radius: var(--border-radius);\\n  box-shadow: var(--shadow-md);\\n}\\n\\n.coming-soon-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.coming-soon-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.coming-soon-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--secondary-color);\\n  font-size: 1.1rem;\\n}\\n\\n\\n\\n.app-footer[_ngcontent-%COMP%] {\\n  background-color: var(--dark-color);\\n  color: #adb5bd;\\n  padding: 1rem 0;\\n  margin-top: auto;\\n}\\n\\n.footer-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  font-size: 0.85rem;\\n}\\n\\n.footer-left[_ngcontent-%COMP%]   .copyright[_ngcontent-%COMP%] {\\n  color: #adb5bd;\\n}\\n\\n.footer-right[_ngcontent-%COMP%]   .build-info[_ngcontent-%COMP%] {\\n  color: var(--secondary-color);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from { opacity: 0; transform: translateY(10px); }\\n  to { opacity: 1; transform: translateY(0); }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInFromTop {\\n  from { transform: translateY(-20px); opacity: 0; }\\n  to { transform: translateY(0); opacity: 1; }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInFromLeft {\\n  from { transform: translateX(-20px); opacity: 0; }\\n  to { transform: translateX(0); opacity: 1; }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% { transform: scale(1); }\\n  50% { transform: scale(1.05); }\\n  100% { transform: scale(1); }\\n}\\n\\n\\n\\n.notification-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 80px;\\n  right: 20px;\\n  z-index: 1050;\\n  max-width: 300px;\\n}\\n\\n.notification[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: var(--border-radius);\\n  box-shadow: var(--shadow-lg);\\n  margin-bottom: 10px;\\n  padding: 15px;\\n  border-left: 4px solid var(--primary-color);\\n  animation: _ngcontent-%COMP%_slideInFromLeft 0.3s ease-out;\\n}\\n\\n.notification.success[_ngcontent-%COMP%] { border-left-color: var(--success-color); }\\n.notification.warning[_ngcontent-%COMP%] { border-left-color: var(--warning-color); }\\n.notification.error[_ngcontent-%COMP%] { border-left-color: var(--danger-color); }\\n\\n\\n\\n.tooltip-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.tooltip-text[_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  width: 200px;\\n  background-color: #333;\\n  color: #fff;\\n  text-align: center;\\n  border-radius: var(--border-radius-sm);\\n  padding: 8px;\\n  position: absolute;\\n  z-index: 1001;\\n  bottom: 125%;\\n  left: 50%;\\n  margin-left: -100px;\\n  opacity: 0;\\n  transition: opacity 0.3s;\\n  font-size: 0.85rem;\\n}\\n\\n.tooltip-container[_ngcontent-%COMP%]:hover   .tooltip-text[_ngcontent-%COMP%] {\\n  visibility: visible;\\n  opacity: 1;\\n}\\n\\n\\n\\n@media (prefers-color-scheme: dark) {\\n  .dark-mode[_ngcontent-%COMP%] {\\n    --light-color: #2d3748;\\n    --dark-color: #1a202c;\\n    --border-color: #4a5568;\\n  }\\n  \\n  .dark-mode[_ngcontent-%COMP%]   .app-container[_ngcontent-%COMP%] {\\n    background-color: var(--dark-color);\\n    color: #e2e8f0;\\n  }\\n  \\n  .dark-mode[_ngcontent-%COMP%]   .app-navigation[_ngcontent-%COMP%] {\\n    background-color: var(--light-color);\\n    border-bottom-color: var(--border-color);\\n  }\\n  \\n  .dark-mode[_ngcontent-%COMP%]   .coming-soon-content[_ngcontent-%COMP%] {\\n    background-color: var(--light-color);\\n    color: #e2e8f0;\\n  }\\n}\\n\\n\\n\\n@media (prefers-reduced-motion: reduce) {\\n  *[_ngcontent-%COMP%] {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n  }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .header-content[_ngcontent-%COMP%], .nav-container[_ngcontent-%COMP%], .main-container[_ngcontent-%COMP%], .footer-content[_ngcontent-%COMP%] {\\n    padding: 0 1rem;\\n  }\\n  \\n  .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n    text-align: center;\\n  }\\n  \\n  .app-title[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  \\n  .nav-tabs[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .nav-link[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n    border-bottom: none;\\n    border-left: 3px solid transparent;\\n  }\\n  \\n  .nav-link.active[_ngcontent-%COMP%] {\\n    border-left-color: var(--primary-color);\\n    border-bottom-color: transparent;\\n  }\\n  \\n  .footer-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n    text-align: center;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .app-main[_ngcontent-%COMP%] {\\n    padding: 1rem 0;\\n  }\\n  \\n  .coming-soon-content[_ngcontent-%COMP%] {\\n    padding: 2rem 1rem;\\n  }\\n  \\n  .coming-soon-icon[_ngcontent-%COMP%] {\\n    font-size: 3rem;\\n  }\\n  \\n  .nav-link[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  \\n  .coming-soon[_ngcontent-%COMP%] {\\n    font-size: 0.5rem;\\n    padding: 0.05rem 0.2rem;\\n  }\\n}\\n\\n@media (max-width: 320px) {\\n  .header-content[_ngcontent-%COMP%], .nav-container[_ngcontent-%COMP%], .main-container[_ngcontent-%COMP%], .footer-content[_ngcontent-%COMP%] {\\n    padding: 0 0.5rem;\\n  }\\n  \\n  .app-title[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  \\n  .footer-content[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n}\\n\\n@media (min-width: 1400px) {\\n  .header-content[_ngcontent-%COMP%], .nav-container[_ngcontent-%COMP%], .main-container[_ngcontent-%COMP%], .footer-content[_ngcontent-%COMP%] {\\n    max-width: 1400px;\\n  }\\n}\\n\\n@media (min-resolution: 192dpi) {\\n  .app-icon[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n}\\n\\n@media print {\\n  .app-header[_ngcontent-%COMP%], .app-navigation[_ngcontent-%COMP%], .app-footer[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  \\n  .app-main[_ngcontent-%COMP%] {\\n    padding: 0;\\n  }\\n  \\n  .main-container[_ngcontent-%COMP%] {\\n    max-width: none;\\n    padding: 0;\\n  }\\n}\\n\\n\\n\\n.sr-only[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border: 0;\\n}\\n\\n.text-center[_ngcontent-%COMP%] { text-align: center; }\\n.text-left[_ngcontent-%COMP%] { text-align: left; }\\n.text-right[_ngcontent-%COMP%] { text-align: right; }\\n\\n.d-none[_ngcontent-%COMP%] { display: none !important; }\\n.d-block[_ngcontent-%COMP%] { display: block !important; }\\n.d-flex[_ngcontent-%COMP%] { display: flex !important; }\\n\\n.justify-content-center[_ngcontent-%COMP%] { justify-content: center !important; }\\n.align-items-center[_ngcontent-%COMP%] { align-items: center !important; }\\n\\n.mb-0[_ngcontent-%COMP%] { margin-bottom: 0 !important; }\\n.mb-1[_ngcontent-%COMP%] { margin-bottom: 0.5rem !important; }\\n.mb-2[_ngcontent-%COMP%] { margin-bottom: 1rem !important; }\\n.mb-3[_ngcontent-%COMP%] { margin-bottom: 1.5rem !important; }\\n\\n.mt-0[_ngcontent-%COMP%] { margin-top: 0 !important; }\\n.mt-1[_ngcontent-%COMP%] { margin-top: 0.5rem !important; }\\n.mt-2[_ngcontent-%COMP%] { margin-top: 1rem !important; }\\n.mt-3[_ngcontent-%COMP%] { margin-top: 1.5rem !important; }\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "AppComponent", "constructor", "title", "activeTab", "setActiveTab", "tab", "isTabActive", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵlistener", "AppComponent_Template_button_click_16_listener", "AppComponent_Template_button_click_21_listener", "AppComponent_Template_button_click_28_listener", "ɵɵtemplate", "AppComponent_div_36_Template", "AppComponent_div_37_Template", "AppComponent_div_38_Template", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵclassProp", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet_CRM\\apps\\AngularCPQapp\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet_CRM\\apps\\AngularCPQapp\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css'],\n  standalone: false\n})\nexport class AppComponent {\n  title = 'Application CPQ - Configure Price Quote';\n  \n  // Variables pour la navigation future\n  activeTab: string = 'cpq-configurator';\n  \n  // Méthodes pour la navigation (pour futurs composants)\n  setActiveTab(tab: string) {\n    this.activeTab = tab;\n  }\n  \n  isTabActive(tab: string): boolean {\n    return this.activeTab === tab;\n  }\n}", "<div class=\"app-container\">\n  <!-- Header principal de l'application avec background foncé -->\n  <header class=\"app-header\">\n    <div class=\"header-content\">\n      <h1 class=\"app-title\">\n        <span class=\"app-icon\">🎯</span>\n        {{ title }}\n      </h1>\n      <div class=\"header-info\">\n        <span class=\"version-badge\">v1.0</span>\n        <span class=\"environment-badge\">Dynamics 365</span>\n      </div>\n    </div>\n  </header>\n\n  <!-- Navigation pour futurs composants -->\n  <nav class=\"app-navigation\">\n    <div class=\"nav-container\">\n      <ul class=\"nav-tabs\">\n        <li class=\"nav-item\">\n          <button \n            class=\"nav-link\"\n            [class.active]=\"isTabActive('cpq-configurator')\"\n            (click)=\"setActiveTab('cpq-configurator')\">\n            <span class=\"nav-icon\">⚙️</span>\n            Configurateur CPQ\n          </button>\n        </li>\n        <!-- Futurs onglets -->\n        <li class=\"nav-item\">\n          <button \n            class=\"nav-link disabled\"\n            [class.active]=\"isTabActive('quote-manager')\"\n            (click)=\"setActiveTab('quote-manager')\"\n            disabled>\n            <span class=\"nav-icon\">📋</span>\n            Gestion des Devis\n            <span class=\"coming-soon\">Bientôt</span>\n          </button>\n        </li>\n        <li class=\"nav-item\">\n          <button \n            class=\"nav-link disabled\"\n            [class.active]=\"isTabActive('product-catalog')\"\n            (click)=\"setActiveTab('product-catalog')\"\n            disabled>\n            <span class=\"nav-icon\">📦</span>\n            Catalogue Produits\n            <span class=\"coming-soon\">Bientôt</span>\n          </button>\n        </li>\n      </ul>\n    </div>\n  </nav>\n\n  <!-- Zone de contenu principal -->\n  <main class=\"app-main\">\n    <div class=\"main-container\">\n      \n      <!-- Composant CPQ Configurator -->\n      <div *ngIf=\"activeTab === 'cpq-configurator'\" class=\"tab-content active\">\n        <app-cpq-configurator></app-cpq-configurator>\n      </div>\n\n      <!-- Futurs composants -->\n      <div *ngIf=\"activeTab === 'quote-manager'\" class=\"tab-content active\">\n        <div class=\"coming-soon-content\">\n          <div class=\"coming-soon-icon\">🚧</div>\n          <h2>Gestion des Devis</h2>\n          <p>Cette fonctionnalité sera bientôt disponible.</p>\n        </div>\n      </div>\n\n      <div *ngIf=\"activeTab === 'product-catalog'\" class=\"tab-content active\">\n        <div class=\"coming-soon-content\">\n          <div class=\"coming-soon-icon\">🚧</div>\n          <h2>Catalogue Produits</h2>\n          <p>Cette fonctionnalité sera bientôt disponible.</p>\n        </div>\n      </div>\n\n    </div>\n  </main>\n\n  <!-- Footer -->\n  <footer class=\"app-footer\">\n    <div class=\"footer-content\">\n      <div class=\"footer-left\">\n        <span class=\"copyright\">© 2024 Application CPQ - Dynamics 365</span>\n      </div>\n       <div class=\"footer-right\">\n         <!-- <span class=\"build-info\">Build: {{ new Date().getFullYear() }}.{{ (new Date().getMonth() + 1).toString().padStart(2, '0') }}</span>  -->\n      </div> \n    </div>\n  </footer>\n</div>\n\n"], "mappings": ";;;;IC4DMA,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAE,SAAA,2BAA6C;IAC/CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKFH,EAFJ,CAAAC,cAAA,cAAsE,cACnC,cACD;IAAAD,EAAA,CAAAI,MAAA,mBAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACtCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,wBAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,8DAA6C;IAEpDJ,EAFoD,CAAAG,YAAA,EAAI,EAChD,EACF;;;;;IAIFH,EAFJ,CAAAC,cAAA,cAAwE,cACrC,cACD;IAAAD,EAAA,CAAAI,MAAA,mBAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACtCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,yBAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,8DAA6C;IAEpDJ,EAFoD,CAAAG,YAAA,EAAI,EAChD,EACF;;;ADvEZ,OAAM,MAAOE,YAAY;EANzBC,YAAA;IAOE,KAAAC,KAAK,GAAG,yCAAyC;IAEjD;IACA,KAAAC,SAAS,GAAW,kBAAkB;;EAEtC;EACAC,YAAYA,CAACC,GAAW;IACtB,IAAI,CAACF,SAAS,GAAGE,GAAG;EACtB;EAEAC,WAAWA,CAACD,GAAW;IACrB,OAAO,IAAI,CAACF,SAAS,KAAKE,GAAG;EAC/B;;;uBAbWL,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAO,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCHjBlB,EALR,CAAAC,cAAA,aAA2B,gBAEE,aACG,YACJ,cACG;UAAAD,EAAA,CAAAI,MAAA,mBAAE;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAChCH,EAAA,CAAAI,MAAA,GACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAEHH,EADF,CAAAC,cAAA,aAAyB,cACK;UAAAD,EAAA,CAAAI,MAAA,WAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UACvCH,EAAA,CAAAC,cAAA,eAAgC;UAAAD,EAAA,CAAAI,MAAA,oBAAY;UAGlDJ,EAHkD,CAAAG,YAAA,EAAO,EAC/C,EACF,EACC;UAODH,EAJR,CAAAC,cAAA,cAA4B,cACC,cACJ,cACE,kBAI0B;UAA3CD,EAAA,CAAAoB,UAAA,mBAAAC,+CAAA;YAAA,OAASF,GAAA,CAAAV,YAAA,CAAa,kBAAkB,CAAC;UAAA,EAAC;UAC1CT,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAI,MAAA,oBAAE;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAChCH,EAAA,CAAAI,MAAA,2BACF;UACFJ,EADE,CAAAG,YAAA,EAAS,EACN;UAGHH,EADF,CAAAC,cAAA,cAAqB,kBAKR;UADTD,EAAA,CAAAoB,UAAA,mBAAAE,+CAAA;YAAA,OAASH,GAAA,CAAAV,YAAA,CAAa,eAAe,CAAC;UAAA,EAAC;UAEvCT,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAI,MAAA,oBAAE;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAChCH,EAAA,CAAAI,MAAA,2BACA;UAAAJ,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAI,MAAA,oBAAO;UAErCJ,EAFqC,CAAAG,YAAA,EAAO,EACjC,EACN;UAEHH,EADF,CAAAC,cAAA,cAAqB,kBAKR;UADTD,EAAA,CAAAoB,UAAA,mBAAAG,+CAAA;YAAA,OAASJ,GAAA,CAAAV,YAAA,CAAa,iBAAiB,CAAC;UAAA,EAAC;UAEzCT,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAI,MAAA,oBAAE;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAChCH,EAAA,CAAAI,MAAA,4BACA;UAAAJ,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAI,MAAA,oBAAO;UAK3CJ,EAL2C,CAAAG,YAAA,EAAO,EACjC,EACN,EACF,EACD,EACF;UAIJH,EADF,CAAAC,cAAA,gBAAuB,eACO;UAgB1BD,EAbA,CAAAwB,UAAA,KAAAC,4BAAA,kBAAyE,KAAAC,4BAAA,kBAKH,KAAAC,4BAAA,kBAQE;UAS5E3B,EADE,CAAAG,YAAA,EAAM,EACD;UAMDH,EAHN,CAAAC,cAAA,kBAA2B,eACG,eACD,gBACC;UAAAD,EAAA,CAAAI,MAAA,kDAAqC;UAC/DJ,EAD+D,CAAAG,YAAA,EAAO,EAChE;UACLH,EAAA,CAAAE,SAAA,eAEK;UAGZF,EAFI,CAAAG,YAAA,EAAM,EACC,EACL;;;UAzFEH,EAAA,CAAA4B,SAAA,GACF;UADE5B,EAAA,CAAA6B,kBAAA,MAAAV,GAAA,CAAAZ,KAAA,MACF;UAeMP,EAAA,CAAA4B,SAAA,IAAgD;UAAhD5B,EAAA,CAAA8B,WAAA,WAAAX,GAAA,CAAAR,WAAA,qBAAgD;UAUhDX,EAAA,CAAA4B,SAAA,GAA6C;UAA7C5B,EAAA,CAAA8B,WAAA,WAAAX,GAAA,CAAAR,WAAA,kBAA6C;UAW7CX,EAAA,CAAA4B,SAAA,GAA+C;UAA/C5B,EAAA,CAAA8B,WAAA,WAAAX,GAAA,CAAAR,WAAA,oBAA+C;UAiB/CX,EAAA,CAAA4B,SAAA,GAAsC;UAAtC5B,EAAA,CAAA+B,UAAA,SAAAZ,GAAA,CAAAX,SAAA,wBAAsC;UAKtCR,EAAA,CAAA4B,SAAA,EAAmC;UAAnC5B,EAAA,CAAA+B,UAAA,SAAAZ,GAAA,CAAAX,SAAA,qBAAmC;UAQnCR,EAAA,CAAA4B,SAAA,EAAqC;UAArC5B,EAAA,CAAA+B,UAAA,SAAAZ,GAAA,CAAAX,SAAA,uBAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}