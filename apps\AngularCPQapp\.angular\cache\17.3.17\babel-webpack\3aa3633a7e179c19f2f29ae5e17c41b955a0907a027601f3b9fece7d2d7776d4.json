{"ast": null, "code": "import { CHAR_NO_BREAK_SPACE, CHAR_EN_DASH } from '@taiga-ui/cdk/constants';\nimport { tuiInRange, tuiNormalizeToIntNumber } from '@taiga-ui/cdk/utils/math';\nfunction tuiDateClamp(date, min, max) {\n  if (max && max < date) {\n    return max;\n  }\n  if (min && min > date) {\n    return min;\n  }\n  return date;\n}\nconst DAYS_IN_WEEK = 7;\nconst DAYS_IN_NORMAL_YEAR = 365;\nconst DAYS_IN_LEAP_YEAR = 366;\nconst MONTHS_IN_YEAR = 12;\nconst MIN_DAY = 1;\nconst MIN_MONTH = 0;\nconst MAX_MONTH = 11;\nconst MIN_YEAR = 0;\nconst MAX_YEAR = 9999;\nconst MAX_DISPLAYED_YEAR = 2099;\nconst RANGE_SEPARATOR_CHAR = `${CHAR_NO_BREAK_SPACE}${CHAR_EN_DASH}${CHAR_NO_BREAK_SPACE}`;\nconst MILLISECONDS_IN_SECOND = 1000;\nconst SECONDS_IN_MINUTE = 60;\nconst MINUTES_IN_HOUR = 60;\nconst HOURS_IN_DAY = 24;\nconst MILLISECONDS_IN_MINUTE = MILLISECONDS_IN_SECOND * SECONDS_IN_MINUTE;\nconst MILLISECONDS_IN_HOUR = MILLISECONDS_IN_MINUTE * MINUTES_IN_HOUR;\nconst MILLISECONDS_IN_DAY = MILLISECONDS_IN_HOUR * HOURS_IN_DAY;\n\n/**\n * @internal 'dd.mm.yyyy'.length\n * Used in:\n * - {@link TuiInputDateComponent}\n * - {@link TuiInputDateRangeComponent}\n * - {@link TuiInputDateTimeComponent}\n */\nconst DATE_FILLER_LENGTH = 10;\n/**\n * @internal\n * Used in {@link TuiInputDateRangeComponent}\n */\nconst DATE_RANGE_FILLER_LENGTH = 2 * DATE_FILLER_LENGTH + RANGE_SEPARATOR_CHAR.length;\nconst TuiDayOfWeek = {\n  Sunday: 0,\n  Monday: 1,\n  Tuesday: 2,\n  Wednesday: 3,\n  Thursday: 4,\n  Friday: 5,\n  Saturday: 6\n};\nconst TuiMonthNumber = {\n  January: 0,\n  February: 1,\n  March: 2,\n  April: 3,\n  May: 4,\n  June: 5,\n  July: 6,\n  August: 7,\n  September: 8,\n  October: 9,\n  November: 10,\n  December: 11\n};\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Immutable year object\n * @nosideeffects\n */\nclass TuiYear {\n  constructor(year) {\n    this.year = year;\n    ngDevMode && console.assert(TuiYear.isValidYear(year));\n  }\n  /**\n   * Checks year for validity\n   */\n  static isValidYear(year) {\n    return Number.isInteger(year) && tuiInRange(year, MIN_YEAR, MAX_YEAR + 1);\n  }\n  /**\n   * Check if passed year is a leap year\n   */\n  static isLeapYear(year) {\n    ngDevMode && console.assert(TuiYear.isValidYear(year));\n    return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;\n  }\n  /**\n   * Returns amount of leap years from year 0 to the passed one\n   */\n  static getAbsoluteLeapYears(year) {\n    ngDevMode && console.assert(TuiYear.isValidYear(year));\n    return Math.ceil(year / 400) + (Math.ceil(year / 4) - Math.ceil(year / 100));\n  }\n  static lengthBetween(from, to) {\n    return to.year - from.year;\n  }\n  /**\n   * Normalizes year by clamping it between min and max years\n   */\n  static normalizeYearPart(year) {\n    return tuiNormalizeToIntNumber(year, MIN_YEAR, MAX_YEAR);\n  }\n  get formattedYear() {\n    return String(this.year).padStart(4, '0');\n  }\n  get isLeapYear() {\n    return TuiYear.isLeapYear(this.year);\n  }\n  /**\n   * Returns amount of leap years from year 0 to current\n   */\n  get absoluteLeapYears() {\n    return TuiYear.getAbsoluteLeapYears(this.year);\n  }\n  /**\n   * Passed year is after current\n   */\n  yearBefore({\n    year\n  }) {\n    return this.year < year;\n  }\n  /**\n   * Passed year is the same or after current\n   */\n  yearSameOrBefore({\n    year\n  }) {\n    return this.year <= year;\n  }\n  /**\n   * Passed year is the same as current\n   */\n  yearSame({\n    year\n  }) {\n    return this.year === year;\n  }\n  /**\n   * Passed year is either the same of before the current\n   */\n  yearSameOrAfter({\n    year\n  }) {\n    return this.year >= year;\n  }\n  /**\n   * Passed year is before current\n   */\n  yearAfter({\n    year\n  }) {\n    return this.year > year;\n  }\n  /**\n   * Immutably offsets year\n   */\n  append({\n    year = 0\n  }) {\n    ngDevMode && console.assert(Number.isInteger(year));\n    const resultYear = this.year + year;\n    ngDevMode && console.assert(TuiYear.isValidYear(resultYear));\n    return new TuiYear(resultYear);\n  }\n  toString() {\n    return this.formattedYear;\n  }\n  valueOf() {\n    return this.year;\n  }\n  /**\n   * Returns the primitive value of the given Date object.\n   * Depending on the argument, the method can return either a string or a number.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/@@toPrimitive\n   */\n  [Symbol.toPrimitive](hint) {\n    return Date.prototype[Symbol.toPrimitive].call(this, hint);\n  }\n  toJSON() {\n    return this.formattedYear;\n  }\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Immutable object consisting of year and month\n */\nclass TuiMonth extends TuiYear {\n  /**\n   * @param year\n   * @param month (starting with 0)\n   */\n  constructor(year, month) {\n    super(year);\n    this.month = month;\n    ngDevMode && console.assert(TuiMonth.isValidMonth(year, month));\n  }\n  /**\n   * Tests month and year for validity\n   */\n  static isValidMonth(year, month) {\n    return TuiYear.isValidYear(year) && TuiMonth.isValidMonthPart(month);\n  }\n  /**\n   * Returns number of days in a month\n   */\n  static getMonthDaysCount(month, isLeapYear) {\n    ngDevMode && console.assert(TuiMonth.isValidMonthPart(month));\n    switch (month) {\n      case TuiMonthNumber.April:\n      case TuiMonthNumber.June:\n      case TuiMonthNumber.November:\n      case TuiMonthNumber.September:\n        return 30;\n      case TuiMonthNumber.February:\n        return isLeapYear ? 29 : 28;\n      default:\n        return 31;\n    }\n  }\n  /**\n   * Returns current month and year based on local time zone\n   * @nosideeffects\n   */\n  static currentLocal() {\n    const nativeDate = new Date();\n    return new TuiMonth(nativeDate.getFullYear(), nativeDate.getMonth());\n  }\n  /**\n   * Returns current month and year based on UTC\n   */\n  static currentUtc() {\n    const nativeDate = new Date();\n    return new TuiMonth(nativeDate.getUTCFullYear(), nativeDate.getUTCMonth());\n  }\n  static lengthBetween(from, to) {\n    const absoluteFrom = from.month + from.year * 12;\n    const absoluteTo = to.month + to.year * 12;\n    return absoluteTo - absoluteFrom;\n  }\n  /**\n   * Normalizes number by clamping it between min and max month\n   */\n  static normalizeMonthPart(month) {\n    return tuiNormalizeToIntNumber(month, MIN_MONTH, MAX_MONTH);\n  }\n  /**\n   * Tests month for validity\n   */\n  static isValidMonthPart(month) {\n    return Number.isInteger(month) && tuiInRange(month, MIN_MONTH, MAX_MONTH + 1);\n  }\n  get formattedMonthPart() {\n    return String(this.month + 1).padStart(2, '0');\n  }\n  /**\n   * Returns days in a month\n   */\n  get daysCount() {\n    return TuiMonth.getMonthDaysCount(this.month, this.isLeapYear);\n  }\n  /**\n   * Passed month and year are after current\n   */\n  monthBefore(another) {\n    return this.yearBefore(another) || this.yearSame(another) && this.month < another.month;\n  }\n  /**\n   * Passed month and year are after or the same as current\n   */\n  monthSameOrBefore(another) {\n    return this.yearBefore(another) || this.yearSame(another) && this.month <= another.month;\n  }\n  /**\n   * Passed month and year are the same as current\n   */\n  monthSame(another) {\n    return this.yearSame(another) && this.month === another.month;\n  }\n  /**\n   * Passed month and year are either before or equal to current\n   */\n  monthSameOrAfter(another) {\n    return this.yearAfter(another) || this.yearSame(another) && this.month >= another.month;\n  }\n  /**\n   * Passed month and year are before current\n   */\n  monthAfter(another) {\n    return this.yearAfter(another) || this.yearSame(another) && this.month > another.month;\n  }\n  /**\n   * Immutably alters current month and year by passed offset\n   *\n   * @param offset\n   * @return new month and year object as a result of offsetting current\n   */\n  append({\n    year = 0,\n    month = 0\n  }) {\n    const totalMonths = (this.year + year) * MONTHS_IN_YEAR + this.month + month;\n    return new TuiMonth(Math.floor(totalMonths / MONTHS_IN_YEAR), totalMonths % MONTHS_IN_YEAR);\n  }\n  toString() {\n    return `${this.formattedMonthPart}.${this.formattedYear}`;\n  }\n  valueOf() {\n    return this.toLocalNativeDate().valueOf();\n  }\n  toJSON() {\n    return `${super.toJSON()}-${this.formattedMonthPart}`;\n  }\n  /**\n   * Returns native {@link Date} based on local time zone\n   */\n  toLocalNativeDate() {\n    return new Date(this.year, this.month);\n  }\n  /**\n   * Returns native {@link Date} based on UTC\n   */\n  toUtcNativeDate() {\n    return new Date(Date.UTC(this.year, this.month));\n  }\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Immutable date object, consisting of day, month and year\n */\nclass TuiDay extends TuiMonth {\n  /**\n   * @param year\n   * @param month (starting with 0)\n   * @param day\n   */\n  constructor(year, month, day) {\n    super(year, month);\n    this.day = day;\n    ngDevMode && console.assert(TuiDay.isValidDay(year, month, day));\n  }\n  /**\n   * Creates {@link TuiDay} from native {@link Date} based on local time zone\n   */\n  static fromLocalNativeDate(date) {\n    return new TuiDay(date.getFullYear(), date.getMonth(), date.getDate());\n  }\n  /**\n   * Creates {@link TuiDay} from native {@link Date} using UTC\n   */\n  static fromUtcNativeDate(date) {\n    return new TuiDay(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate());\n  }\n  /**\n   * Check validity of year, month and day\n   *\n   * @param year\n   * @param month\n   * @param day\n   * @return boolean validity\n   */\n  static isValidDay(year, month, day) {\n    return TuiMonth.isValidMonth(year, month) && Number.isInteger(day) && tuiInRange(day, MIN_DAY, TuiMonth.getMonthDaysCount(month, TuiYear.isLeapYear(year)) + 1);\n  }\n  /**\n   * Current day based on local time zone\n   */\n  static currentLocal() {\n    const nativeDate = new Date();\n    const year = nativeDate.getFullYear();\n    const month = nativeDate.getMonth();\n    const day = nativeDate.getDate();\n    return new TuiDay(year, month, day);\n  }\n  /**\n   * Returns current day based on UTC\n   */\n  static currentUtc() {\n    const nativeDate = new Date();\n    const year = nativeDate.getUTCFullYear();\n    const month = nativeDate.getUTCMonth();\n    const day = nativeDate.getUTCDate();\n    return new TuiDay(year, month, day);\n  }\n  /**\n   * Calculates {@link TuiDay} normalizing year, month and day. {@link NaN} is turned into minimal value.\n   *\n   * @param year any year value, including invalid\n   * @param month any month value, including invalid (months start with 0)\n   * @param day any day value, including invalid\n   * @return normalized date\n   */\n  static normalizeOf(year, month, day) {\n    const normalizedYear = TuiYear.normalizeYearPart(year);\n    const normalizedMonth = TuiMonth.normalizeMonthPart(month);\n    const normalizedDay = TuiDay.normalizeDayPart(day, normalizedMonth, normalizedYear);\n    return new TuiDay(normalizedYear, normalizedMonth, normalizedDay);\n  }\n  static lengthBetween(from, to) {\n    return Math.round((to.toLocalNativeDate().getTime() - from.toLocalNativeDate().getTime()) / (1000 * 60 * 60 * 24));\n  }\n  static parseRawDateString(date, dateMode = 'DMY') {\n    ngDevMode && console.assert(date.length === DATE_FILLER_LENGTH, '[parseRawDateString]: wrong date string length');\n    switch (dateMode) {\n      case 'MDY':\n        return {\n          day: parseInt(date.slice(3, 5), 10),\n          month: parseInt(date.slice(0, 2), 10) - 1,\n          year: parseInt(date.slice(6, 10), 10)\n        };\n      case 'YMD':\n        return {\n          day: parseInt(date.slice(8, 10), 10),\n          month: parseInt(date.slice(5, 7), 10) - 1,\n          year: parseInt(date.slice(0, 4), 10)\n        };\n      case 'DMY':\n      default:\n        return {\n          day: parseInt(date.slice(0, 2), 10),\n          month: parseInt(date.slice(3, 5), 10) - 1,\n          year: parseInt(date.slice(6, 10), 10)\n        };\n    }\n  }\n  // TODO: Move month and year related code corresponding classes\n  /**\n   * Parsing a string with date with normalization\n   *\n   * @param rawDate date string\n   * @param dateMode date format of the date string (DMY | MDY | YMD)\n   * @return normalized date\n   */\n  static normalizeParse(rawDate, dateMode = 'DMY') {\n    const {\n      day,\n      month,\n      year\n    } = this.parseRawDateString(rawDate, dateMode);\n    return TuiDay.normalizeOf(year, month, day);\n  }\n  /**\n   * Parsing a date stringified in a toJSON format\n   * @param yearMonthDayString date string in format of YYYY-MM-DD\n   * @return date\n   * @throws exceptions if any part of the date is invalid\n   */\n  static jsonParse(yearMonthDayString) {\n    const {\n      day,\n      month,\n      year\n    } = this.parseRawDateString(yearMonthDayString, 'YMD');\n    if (!TuiMonth.isValidMonth(year, month) || !Number.isInteger(day) || !tuiInRange(day, MIN_DAY, TuiMonth.getMonthDaysCount(month, TuiYear.isLeapYear(year)) + 1)) {\n      throw new TuiInvalidDayException(year, month, day);\n    }\n    return new TuiDay(year, month, day);\n  }\n  static normalizeDayPart(day, month, year) {\n    ngDevMode && console.assert(TuiMonth.isValidMonth(year, month));\n    const monthDaysCount = TuiMonth.getMonthDaysCount(month, TuiYear.isLeapYear(year));\n    return tuiNormalizeToIntNumber(day, 1, monthDaysCount);\n  }\n  get formattedDayPart() {\n    return String(this.day).padStart(2, '0');\n  }\n  get isWeekend() {\n    const dayOfWeek = this.dayOfWeek(false);\n    return dayOfWeek === TuiDayOfWeek.Saturday || dayOfWeek === TuiDayOfWeek.Sunday;\n  }\n  /**\n   * Returns day of week\n   *\n   * @param startFromMonday whether week starts from Monday and not from Sunday\n   * @return day of week (from 0 to 6)\n   */\n  dayOfWeek(startFromMonday = true) {\n    const dayOfWeek = startFromMonday ? this.toLocalNativeDate().getDay() - 1 : this.toLocalNativeDate().getDay();\n    return dayOfWeek < 0 ? 6 : dayOfWeek;\n  }\n  /**\n   * Passed date is after current\n   */\n  dayBefore(another) {\n    return this.monthBefore(another) || this.monthSame(another) && this.day < another.day;\n  }\n  /**\n   * Passed date is after or equals to current\n   */\n  daySameOrBefore(another) {\n    return this.monthBefore(another) || this.monthSame(another) && this.day <= another.day;\n  }\n  /**\n   * Passed date is the same as current\n   */\n  daySame(another) {\n    return this.monthSame(another) && this.day === another.day;\n  }\n  /**\n   * Passed date is either before or the same as current\n   */\n  daySameOrAfter(another) {\n    return this.monthAfter(another) || this.monthSame(another) && this.day >= another.day;\n  }\n  /**\n   * Passed date is before current\n   */\n  dayAfter(another) {\n    return this.monthAfter(another) || this.monthSame(another) && this.day > another.day;\n  }\n  /**\n   * Clamping date between two limits\n   *\n   * @param min\n   * @param max\n   * @return clamped date\n   */\n  dayLimit(min, max) {\n    if (min !== null && this.dayBefore(min)) {\n      return min;\n    }\n    if (max !== null && this.dayAfter(max)) {\n      return max;\n    }\n    return this;\n  }\n  /**\n   * Immutably alters current day by passed offset\n   *\n   * If resulting month has more days than original one, date is rounded to the maximum day\n   * in the resulting month. Offset of days will be calculated based on the resulted year and month\n   * to not interfere with parent classes methods\n   *\n   * @param offset\n   * @return new date object as a result of offsetting current\n   */\n  append({\n    year = 0,\n    month = 0,\n    day = 0\n  }) {\n    const totalMonths = (this.year + year) * MONTHS_IN_YEAR + this.month + month;\n    let years = Math.floor(totalMonths / MONTHS_IN_YEAR);\n    let months = totalMonths % MONTHS_IN_YEAR;\n    let days = Math.min(this.day, TuiMonth.getMonthDaysCount(months, TuiYear.isLeapYear(years))) + day;\n    while (days > TuiMonth.getMonthDaysCount(months, TuiYear.isLeapYear(years))) {\n      days -= TuiMonth.getMonthDaysCount(months, TuiYear.isLeapYear(years));\n      if (months === TuiMonthNumber.December) {\n        years++;\n        months = TuiMonthNumber.January;\n      } else {\n        months++;\n      }\n    }\n    while (days < MIN_DAY) {\n      if (months === TuiMonthNumber.January) {\n        years--;\n        months = TuiMonthNumber.December;\n      } else {\n        months--;\n      }\n      days += TuiMonth.getMonthDaysCount(months, TuiYear.isLeapYear(years));\n    }\n    return new TuiDay(years, months, days);\n  }\n  /**\n   * Returns formatted whole date\n   */\n  getFormattedDay(dateFormat, separator) {\n    ngDevMode && console.assert(separator.length === 1, 'Separator should consist of only 1 symbol');\n    const dd = this.formattedDayPart;\n    const mm = this.formattedMonthPart;\n    const yyyy = this.formattedYear;\n    switch (dateFormat) {\n      case 'MDY':\n        return `${mm}${separator}${dd}${separator}${yyyy}`;\n      case 'YMD':\n        return `${yyyy}${separator}${mm}${separator}${dd}`;\n      case 'DMY':\n      default:\n        return `${dd}${separator}${mm}${separator}${yyyy}`;\n    }\n  }\n  toString(dateFormat = 'DMY', separator = '.') {\n    return this.getFormattedDay(dateFormat, separator);\n  }\n  toJSON() {\n    return `${super.toJSON()}-${this.formattedDayPart}`;\n  }\n  /**\n   * Returns native {@link Date} based on local time zone\n   */\n  toLocalNativeDate() {\n    return new Date(this.year, this.month, this.day);\n  }\n  /**\n   * Returns native {@link Date} based on UTC\n   */\n  toUtcNativeDate() {\n    return new Date(Date.UTC(this.year, this.month, this.day));\n  }\n}\nclass TuiInvalidDayException extends Error {\n  constructor(year, month, day) {\n    super(ngDevMode ? `Invalid day: ${year}-${month}-${day}` : '');\n  }\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * An immutable range of two {@link TuiMonth} objects\n */\nclass TuiMonthRange {\n  constructor(from, to) {\n    this.from = from;\n    this.to = to;\n    ngDevMode && console.assert(from.monthSameOrBefore(to));\n  }\n  static sort(month1, month2) {\n    return month1.monthSameOrBefore(month2) ? new TuiMonthRange(month1, month2) : new TuiMonthRange(month2, month1);\n  }\n  get isSingleMonth() {\n    return this.from.monthSame(this.to);\n  }\n  monthSame(another) {\n    return this.from.monthSame(another.from) && this.to.monthSame(another.to);\n  }\n  toString() {\n    return `${this.from}${RANGE_SEPARATOR_CHAR}${this.to}`;\n  }\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * An immutable range of two {@link TuiDay} objects\n */\nclass TuiDayRange extends TuiMonthRange {\n  constructor(from, to) {\n    super(from, to);\n    this.from = from;\n    this.to = to;\n    ngDevMode && console.assert(from.daySameOrBefore(to));\n  }\n  /**\n   * Creates range from two days after sorting them\n   *\n   * @param day1\n   * @param day2\n   * @return new range with sorted days\n   */\n  static sort(day1, day2) {\n    return day1.daySameOrBefore(day2) ? new TuiDayRange(day1, day2) : new TuiDayRange(day2, day1);\n  }\n  /**\n   * Parse and correct a day range in string format\n   *\n   * @param rangeString a string of dates in a format dd.mm.yyyy - dd.mm.yyyy\n   * @param dateMode {@link TuiDateMode}\n   * @return normalized day range object\n   */\n  static normalizeParse(rangeString, dateMode = 'DMY') {\n    const leftDay = TuiDay.normalizeParse(rangeString.slice(0, DATE_FILLER_LENGTH), dateMode);\n    if (rangeString.length < DATE_RANGE_FILLER_LENGTH) {\n      return new TuiDayRange(leftDay, leftDay);\n    }\n    return TuiDayRange.sort(leftDay, TuiDay.normalizeParse(rangeString.slice(DATE_FILLER_LENGTH + RANGE_SEPARATOR_CHAR.length), dateMode));\n  }\n  get isSingleDay() {\n    return this.from.daySame(this.to);\n  }\n  /**\n   * Tests ranges for identity\n   *\n   * @param another second range to test against current\n   * @return `true` if days are identical\n   */\n  daySame(another) {\n    return this.from.daySame(another.from) && this.to.daySame(another.to);\n  }\n  /**\n   * Locks range between two days included, or limits from one side if the other is null\n   *\n   * @param min\n   * @param max\n   * @return range — clamped range\n   */\n  dayLimit(min, max) {\n    return new TuiDayRange(this.from.dayLimit(min, max), this.to.dayLimit(min, max));\n  }\n  /**\n   * Human readable format.\n   */\n  getFormattedDayRange(dateFormat, dateSeparator) {\n    const from = this.from.getFormattedDay(dateFormat, dateSeparator);\n    const to = this.to.getFormattedDay(dateFormat, dateSeparator);\n    return `${from}${RANGE_SEPARATOR_CHAR}${to}`;\n  }\n  toString(dateFormat = 'DMY', dateSeparator = '.') {\n    return this.getFormattedDayRange(dateFormat, dateSeparator);\n  }\n  toArray() {\n    const {\n      from,\n      to\n    } = this;\n    const arr = [];\n    for (const day = from.toUtcNativeDate(); day <= to.toUtcNativeDate(); day.setDate(day.getDate() + 1)) {\n      arr.push(TuiDay.fromLocalNativeDate(day));\n    }\n    return arr;\n  }\n}\nconst TUI_FIRST_DAY = new TuiDay(MIN_YEAR, MIN_MONTH, MIN_DAY);\nconst TUI_LAST_DAY = new TuiDay(MAX_YEAR, MAX_MONTH, 31);\nconst TUI_LAST_DISPLAYED_DAY = new TuiDay(MAX_DISPLAYED_YEAR, MAX_MONTH, 31);\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Immutable time object with hours, minutes, seconds and ms\n */\nclass TuiTime {\n  constructor(hours, minutes, seconds = 0, ms = 0) {\n    this.hours = hours;\n    this.minutes = minutes;\n    this.seconds = seconds;\n    this.ms = ms;\n    ngDevMode && console.assert(\n    // Currently `TuiTime` could have hours more than 23\n    // in order to not break current behaviour of `isValidTime` the logic is duplicated\n    Number.isInteger(hours) && tuiInRange(hours, 0, Infinity) && Number.isInteger(minutes) && tuiInRange(minutes, 0, MINUTES_IN_HOUR) && Number.isInteger(seconds) && tuiInRange(seconds, 0, SECONDS_IN_MINUTE) && Number.isInteger(ms) && tuiInRange(ms, 0, 1000), 'Time must be real, but got:', hours, minutes, seconds, ms);\n  }\n  /**\n   * Checks if time is valid\n   */\n  static isValidTime(hours, minutes, seconds = 0, ms = 0) {\n    return Number.isInteger(hours) && tuiInRange(hours, 0, HOURS_IN_DAY) && Number.isInteger(minutes) && tuiInRange(minutes, 0, MINUTES_IN_HOUR) && Number.isInteger(seconds) && tuiInRange(seconds, 0, SECONDS_IN_MINUTE) && Number.isInteger(ms) && tuiInRange(ms, 0, 1000);\n  }\n  /**\n   * Current UTC time.\n   */\n  static current() {\n    return TuiTime.fromAbsoluteMilliseconds(Date.now() % MILLISECONDS_IN_DAY);\n  }\n  /**\n   * Current time in local timezone\n   */\n  static currentLocal() {\n    const date = new Date();\n    return TuiTime.fromAbsoluteMilliseconds((Date.now() - date.getTimezoneOffset() * MILLISECONDS_IN_MINUTE) % MILLISECONDS_IN_DAY);\n  }\n  /**\n   * Calculates TuiTime from milliseconds\n   */\n  static fromAbsoluteMilliseconds(milliseconds) {\n    ngDevMode && console.assert(Number.isInteger(milliseconds));\n    ngDevMode && console.assert(tuiInRange(milliseconds, 0, MILLISECONDS_IN_DAY), `Milliseconds must be below ${MILLISECONDS_IN_DAY} (milliseconds in a day).`);\n    const hours = Math.floor(milliseconds / MILLISECONDS_IN_HOUR);\n    const minutes = Math.floor(milliseconds % MILLISECONDS_IN_HOUR / MILLISECONDS_IN_MINUTE);\n    const seconds = Math.floor(milliseconds % MILLISECONDS_IN_HOUR % MILLISECONDS_IN_MINUTE / 1000) || 0;\n    const ms = Math.floor(milliseconds % MILLISECONDS_IN_HOUR % MILLISECONDS_IN_MINUTE % 1000) || 0;\n    return new TuiTime(hours, minutes, seconds, ms);\n  }\n  /**\n   * Parses string into TuiTime object\n   */\n  static fromString(time) {\n    const hours = this.parseHours(time);\n    const minutes = Number(time.slice(3, 5)) || 0;\n    const seconds = Number(time.slice(6, 8)) || 0;\n    const ms = Number(time.slice(9, 12)) || 0;\n    return new TuiTime(hours, minutes, seconds, ms);\n  }\n  /**\n   * Converts Date object into TuiTime\n   * @param date\n   */\n  static fromLocalNativeDate(date) {\n    return new TuiTime(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n  }\n  static parseMeridiemPeriod(time) {\n    return /[AP]M/.exec(time.toUpperCase().replaceAll(/\\W/g, ''))?.[0] || null;\n  }\n  static parseHours(time) {\n    const hours = Number(time.slice(0, 2));\n    const meridiem = this.parseMeridiemPeriod(time);\n    if (!meridiem) {\n      return hours;\n    }\n    if (hours === 12) {\n      return meridiem === 'AM' ? 0 : 12;\n    }\n    return meridiem === 'PM' ? hours + 12 : hours;\n  }\n  /**\n   * Shifts time by hours and minutes\n   */\n  shift({\n    hours = 0,\n    minutes = 0,\n    seconds = 0,\n    ms = 0\n  }) {\n    const totalMs = this.toAbsoluteMilliseconds() + hours * MILLISECONDS_IN_HOUR + minutes * MILLISECONDS_IN_MINUTE + seconds * MILLISECONDS_IN_SECOND + ms;\n    const totalSeconds = Math.floor(totalMs / MILLISECONDS_IN_SECOND);\n    const totalMinutes = Math.floor(totalSeconds / SECONDS_IN_MINUTE);\n    const totalHours = Math.floor(totalMinutes / MINUTES_IN_HOUR);\n    return new TuiTime(this.normalizeToRange(totalHours, HOURS_IN_DAY), this.normalizeToRange(totalMinutes, MINUTES_IN_HOUR), this.normalizeToRange(totalSeconds, SECONDS_IN_MINUTE), this.normalizeToRange(totalMs, MILLISECONDS_IN_SECOND));\n  }\n  /**\n   * Converts TuiTime to string\n   */\n  toString(mode) {\n    const needAddMs = mode?.startsWith('HH:MM:SS.MSS') || !mode && this.ms > 0;\n    const needAddSeconds = needAddMs || mode?.startsWith('HH:MM:SS') || !mode && this.seconds > 0;\n    const {\n      hours = this.hours,\n      meridiem = ''\n    } = mode?.includes('AA') ? this.toTwelveHour(this.hours) : {};\n    const hhMm = `${this.formatTime(hours)}:${this.formatTime(this.minutes)}`;\n    const ss = needAddSeconds ? `:${this.formatTime(this.seconds)}` : '';\n    const mss = needAddMs ? `.${this.formatTime(this.ms, 3)}` : '';\n    const aa = meridiem && `${CHAR_NO_BREAK_SPACE}${meridiem}`;\n    return `${hhMm}${ss}${mss}${aa}`;\n  }\n  valueOf() {\n    return this.toAbsoluteMilliseconds();\n  }\n  /**\n   * Returns the primitive value of the given Date object.\n   * Depending on the argument, the method can return either a string or a number.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/@@toPrimitive\n   */\n  [Symbol.toPrimitive](hint) {\n    return Date.prototype[Symbol.toPrimitive].call(this, hint);\n  }\n  /**\n   * Converts TuiTime to milliseconds\n   */\n  toAbsoluteMilliseconds() {\n    return this.hours * MILLISECONDS_IN_HOUR + this.minutes * MILLISECONDS_IN_MINUTE + this.seconds * 1000 + this.ms;\n  }\n  formatTime(time, digits = 2) {\n    return String(time).padStart(digits, '0');\n  }\n  toTwelveHour(hours) {\n    const meridiem = hours >= 12 ? 'PM' : 'AM';\n    if (hours === 0 || hours === 12) {\n      return {\n        meridiem,\n        hours: 12\n      };\n    }\n    return {\n      meridiem,\n      hours: hours % 12\n    };\n  }\n  normalizeToRange(value, modulus) {\n    return (value % modulus + modulus) % modulus;\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DATE_FILLER_LENGTH, DATE_RANGE_FILLER_LENGTH, DAYS_IN_LEAP_YEAR, DAYS_IN_NORMAL_YEAR, DAYS_IN_WEEK, HOURS_IN_DAY, MAX_DISPLAYED_YEAR, MAX_MONTH, MAX_YEAR, MILLISECONDS_IN_DAY, MILLISECONDS_IN_HOUR, MILLISECONDS_IN_MINUTE, MILLISECONDS_IN_SECOND, MINUTES_IN_HOUR, MIN_DAY, MIN_MONTH, MIN_YEAR, MONTHS_IN_YEAR, RANGE_SEPARATOR_CHAR, SECONDS_IN_MINUTE, TUI_FIRST_DAY, TUI_LAST_DAY, TUI_LAST_DISPLAYED_DAY, TuiDay, TuiDayOfWeek, TuiDayRange, TuiInvalidDayException, TuiMonth, TuiMonthNumber, TuiMonthRange, TuiTime, TuiYear, tuiDateClamp };", "map": {"version": 3, "names": ["CHAR_NO_BREAK_SPACE", "CHAR_EN_DASH", "tuiInRange", "tuiNormalizeToIntNumber", "tuiDateClamp", "date", "min", "max", "DAYS_IN_WEEK", "DAYS_IN_NORMAL_YEAR", "DAYS_IN_LEAP_YEAR", "MONTHS_IN_YEAR", "MIN_DAY", "MIN_MONTH", "MAX_MONTH", "MIN_YEAR", "MAX_YEAR", "MAX_DISPLAYED_YEAR", "RANGE_SEPARATOR_CHAR", "MILLISECONDS_IN_SECOND", "SECONDS_IN_MINUTE", "MINUTES_IN_HOUR", "HOURS_IN_DAY", "MILLISECONDS_IN_MINUTE", "MILLISECONDS_IN_HOUR", "MILLISECONDS_IN_DAY", "DATE_FILLER_LENGTH", "DATE_RANGE_FILLER_LENGTH", "length", "TuiDayOfWeek", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "TuiMonthNumber", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "year", "ngDevMode", "console", "assert", "isValidYear", "Number", "isInteger", "isLeapYear", "getAbsoluteLeapYears", "Math", "ceil", "lengthBetween", "from", "to", "normalizeYearPart", "formattedYear", "String", "padStart", "absoluteLeapYears", "yearBefore", "yearSameOrBefore", "yearSame", "yearSameOrAfter", "yearAfter", "append", "resultYear", "toString", "valueOf", "Symbol", "toPrimitive", "hint", "Date", "prototype", "call", "toJSON", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "month", "isValidMonth", "isValidMonthPart", "getMonthDaysCount", "currentLocal", "nativeDate", "getFullYear", "getMonth", "currentUtc", "getUTCFullYear", "getUTCMonth", "absoluteFrom", "absoluteTo", "normalizeMonthPart", "formattedMonthPart", "daysCount", "monthBefore", "another", "monthSameOrBefore", "monthSame", "monthSameOrAfter", "monthAfter", "totalMonths", "floor", "toLocalNativeDate", "toUtcNativeDate", "UTC", "TuiDay", "day", "isValidDay", "fromLocalNativeDate", "getDate", "fromUtcNativeDate", "getUTCDate", "normalizeOf", "normalizedYear", "normalizedMonth", "normalizedDay", "normalizeDayPart", "round", "getTime", "parseRawDateString", "dateMode", "parseInt", "slice", "normalizeParse", "rawDate", "jsonParse", "yearMonthDayString", "TuiInvalidDayException", "monthDaysCount", "formattedDayPart", "isWeekend", "dayOfWeek", "startFromMonday", "getDay", "dayBefore", "daySameOrBefore", "daySame", "daySameOrAfter", "dayAfter", "dayLimit", "years", "months", "days", "getFormattedDay", "dateFormat", "separator", "dd", "mm", "yyyy", "Error", "TuiMonthRange", "sort", "month1", "month2", "isSingleMonth", "TuiDayRange", "day1", "day2", "rangeString", "leftDay", "isSingleDay", "getFormattedDayRange", "dateSeparator", "toArray", "arr", "setDate", "push", "TUI_FIRST_DAY", "TUI_LAST_DAY", "TUI_LAST_DISPLAYED_DAY", "TuiTime", "hours", "minutes", "seconds", "ms", "Infinity", "isValidTime", "current", "fromAbsoluteMilliseconds", "now", "getTimezoneOffset", "milliseconds", "fromString", "time", "parseHours", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "parseMeridiemPeriod", "exec", "toUpperCase", "replaceAll", "meridiem", "shift", "totalMs", "toAbsoluteMilliseconds", "totalSeconds", "totalMinutes", "totalHours", "normalizeToRange", "mode", "needAddMs", "startsWith", "needAddSeconds", "includes", "toTwelveHour", "hhMm", "formatTime", "ss", "mss", "aa", "digits", "value", "modulus"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-date-time.mjs"], "sourcesContent": ["import { CHAR_NO_BREAK_SPACE, CHAR_EN_DASH } from '@taiga-ui/cdk/constants';\nimport { tuiInRange, tuiNormalizeToIntNumber } from '@taiga-ui/cdk/utils/math';\n\nfunction tuiDateClamp(date, min, max) {\n    if (max && max < date) {\n        return max;\n    }\n    if (min && min > date) {\n        return min;\n    }\n    return date;\n}\n\nconst DAYS_IN_WEEK = 7;\nconst DAYS_IN_NORMAL_YEAR = 365;\nconst DAYS_IN_LEAP_YEAR = 366;\nconst MONTHS_IN_YEAR = 12;\nconst MIN_DAY = 1;\nconst MIN_MONTH = 0;\nconst MAX_MONTH = 11;\nconst MIN_YEAR = 0;\nconst MAX_YEAR = 9999;\nconst MAX_DISPLAYED_YEAR = 2099;\nconst RANGE_SEPARATOR_CHAR = `${CHAR_NO_BREAK_SPACE}${CHAR_EN_DASH}${CHAR_NO_BREAK_SPACE}`;\nconst MILLISECONDS_IN_SECOND = 1000;\nconst SECONDS_IN_MINUTE = 60;\nconst MINUTES_IN_HOUR = 60;\nconst HOURS_IN_DAY = 24;\nconst MILLISECONDS_IN_MINUTE = MILLISECONDS_IN_SECOND * SECONDS_IN_MINUTE;\nconst MILLISECONDS_IN_HOUR = MILLISECONDS_IN_MINUTE * MINUTES_IN_HOUR;\nconst MILLISECONDS_IN_DAY = MILLISECONDS_IN_HOUR * HOURS_IN_DAY;\n\n/**\n * @internal 'dd.mm.yyyy'.length\n * Used in:\n * - {@link TuiInputDateComponent}\n * - {@link TuiInputDateRangeComponent}\n * - {@link TuiInputDateTimeComponent}\n */\nconst DATE_FILLER_LENGTH = 10;\n/**\n * @internal\n * Used in {@link TuiInputDateRangeComponent}\n */\nconst DATE_RANGE_FILLER_LENGTH = 2 * DATE_FILLER_LENGTH + RANGE_SEPARATOR_CHAR.length;\n\nconst TuiDayOfWeek = {\n    Sunday: 0,\n    Monday: 1,\n    Tuesday: 2,\n    Wednesday: 3,\n    Thursday: 4,\n    Friday: 5,\n    Saturday: 6,\n};\n\nconst TuiMonthNumber = {\n    January: 0,\n    February: 1,\n    March: 2,\n    April: 3,\n    May: 4,\n    June: 5,\n    July: 6,\n    August: 7,\n    September: 8,\n    October: 9,\n    November: 10,\n    December: 11,\n};\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Immutable year object\n * @nosideeffects\n */\nclass TuiYear {\n    constructor(year) {\n        this.year = year;\n        ngDevMode && console.assert(TuiYear.isValidYear(year));\n    }\n    /**\n     * Checks year for validity\n     */\n    static isValidYear(year) {\n        return Number.isInteger(year) && tuiInRange(year, MIN_YEAR, MAX_YEAR + 1);\n    }\n    /**\n     * Check if passed year is a leap year\n     */\n    static isLeapYear(year) {\n        ngDevMode && console.assert(TuiYear.isValidYear(year));\n        return year % 400 === 0 || (year % 4 === 0 && year % 100 !== 0);\n    }\n    /**\n     * Returns amount of leap years from year 0 to the passed one\n     */\n    static getAbsoluteLeapYears(year) {\n        ngDevMode && console.assert(TuiYear.isValidYear(year));\n        return Math.ceil(year / 400) + (Math.ceil(year / 4) - Math.ceil(year / 100));\n    }\n    static lengthBetween(from, to) {\n        return to.year - from.year;\n    }\n    /**\n     * Normalizes year by clamping it between min and max years\n     */\n    static normalizeYearPart(year) {\n        return tuiNormalizeToIntNumber(year, MIN_YEAR, MAX_YEAR);\n    }\n    get formattedYear() {\n        return String(this.year).padStart(4, '0');\n    }\n    get isLeapYear() {\n        return TuiYear.isLeapYear(this.year);\n    }\n    /**\n     * Returns amount of leap years from year 0 to current\n     */\n    get absoluteLeapYears() {\n        return TuiYear.getAbsoluteLeapYears(this.year);\n    }\n    /**\n     * Passed year is after current\n     */\n    yearBefore({ year }) {\n        return this.year < year;\n    }\n    /**\n     * Passed year is the same or after current\n     */\n    yearSameOrBefore({ year }) {\n        return this.year <= year;\n    }\n    /**\n     * Passed year is the same as current\n     */\n    yearSame({ year }) {\n        return this.year === year;\n    }\n    /**\n     * Passed year is either the same of before the current\n     */\n    yearSameOrAfter({ year }) {\n        return this.year >= year;\n    }\n    /**\n     * Passed year is before current\n     */\n    yearAfter({ year }) {\n        return this.year > year;\n    }\n    /**\n     * Immutably offsets year\n     */\n    append({ year = 0 }) {\n        ngDevMode && console.assert(Number.isInteger(year));\n        const resultYear = this.year + year;\n        ngDevMode && console.assert(TuiYear.isValidYear(resultYear));\n        return new TuiYear(resultYear);\n    }\n    toString() {\n        return this.formattedYear;\n    }\n    valueOf() {\n        return this.year;\n    }\n    /**\n     * Returns the primitive value of the given Date object.\n     * Depending on the argument, the method can return either a string or a number.\n     * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/@@toPrimitive\n     */\n    [Symbol.toPrimitive](hint) {\n        return Date.prototype[Symbol.toPrimitive].call(this, hint);\n    }\n    toJSON() {\n        return this.formattedYear;\n    }\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Immutable object consisting of year and month\n */\nclass TuiMonth extends TuiYear {\n    /**\n     * @param year\n     * @param month (starting with 0)\n     */\n    constructor(year, month) {\n        super(year);\n        this.month = month;\n        ngDevMode && console.assert(TuiMonth.isValidMonth(year, month));\n    }\n    /**\n     * Tests month and year for validity\n     */\n    static isValidMonth(year, month) {\n        return TuiYear.isValidYear(year) && TuiMonth.isValidMonthPart(month);\n    }\n    /**\n     * Returns number of days in a month\n     */\n    static getMonthDaysCount(month, isLeapYear) {\n        ngDevMode && console.assert(TuiMonth.isValidMonthPart(month));\n        switch (month) {\n            case TuiMonthNumber.April:\n            case TuiMonthNumber.June:\n            case TuiMonthNumber.November:\n            case TuiMonthNumber.September:\n                return 30;\n            case TuiMonthNumber.February:\n                return isLeapYear ? 29 : 28;\n            default:\n                return 31;\n        }\n    }\n    /**\n     * Returns current month and year based on local time zone\n     * @nosideeffects\n     */\n    static currentLocal() {\n        const nativeDate = new Date();\n        return new TuiMonth(nativeDate.getFullYear(), nativeDate.getMonth());\n    }\n    /**\n     * Returns current month and year based on UTC\n     */\n    static currentUtc() {\n        const nativeDate = new Date();\n        return new TuiMonth(nativeDate.getUTCFullYear(), nativeDate.getUTCMonth());\n    }\n    static lengthBetween(from, to) {\n        const absoluteFrom = from.month + from.year * 12;\n        const absoluteTo = to.month + to.year * 12;\n        return absoluteTo - absoluteFrom;\n    }\n    /**\n     * Normalizes number by clamping it between min and max month\n     */\n    static normalizeMonthPart(month) {\n        return tuiNormalizeToIntNumber(month, MIN_MONTH, MAX_MONTH);\n    }\n    /**\n     * Tests month for validity\n     */\n    static isValidMonthPart(month) {\n        return Number.isInteger(month) && tuiInRange(month, MIN_MONTH, MAX_MONTH + 1);\n    }\n    get formattedMonthPart() {\n        return String(this.month + 1).padStart(2, '0');\n    }\n    /**\n     * Returns days in a month\n     */\n    get daysCount() {\n        return TuiMonth.getMonthDaysCount(this.month, this.isLeapYear);\n    }\n    /**\n     * Passed month and year are after current\n     */\n    monthBefore(another) {\n        return (this.yearBefore(another) ||\n            (this.yearSame(another) && this.month < another.month));\n    }\n    /**\n     * Passed month and year are after or the same as current\n     */\n    monthSameOrBefore(another) {\n        return (this.yearBefore(another) ||\n            (this.yearSame(another) && this.month <= another.month));\n    }\n    /**\n     * Passed month and year are the same as current\n     */\n    monthSame(another) {\n        return this.yearSame(another) && this.month === another.month;\n    }\n    /**\n     * Passed month and year are either before or equal to current\n     */\n    monthSameOrAfter(another) {\n        return (this.yearAfter(another) ||\n            (this.yearSame(another) && this.month >= another.month));\n    }\n    /**\n     * Passed month and year are before current\n     */\n    monthAfter(another) {\n        return (this.yearAfter(another) ||\n            (this.yearSame(another) && this.month > another.month));\n    }\n    /**\n     * Immutably alters current month and year by passed offset\n     *\n     * @param offset\n     * @return new month and year object as a result of offsetting current\n     */\n    append({ year = 0, month = 0 }) {\n        const totalMonths = (this.year + year) * MONTHS_IN_YEAR + this.month + month;\n        return new TuiMonth(Math.floor(totalMonths / MONTHS_IN_YEAR), totalMonths % MONTHS_IN_YEAR);\n    }\n    toString() {\n        return `${this.formattedMonthPart}.${this.formattedYear}`;\n    }\n    valueOf() {\n        return this.toLocalNativeDate().valueOf();\n    }\n    toJSON() {\n        return `${super.toJSON()}-${this.formattedMonthPart}`;\n    }\n    /**\n     * Returns native {@link Date} based on local time zone\n     */\n    toLocalNativeDate() {\n        return new Date(this.year, this.month);\n    }\n    /**\n     * Returns native {@link Date} based on UTC\n     */\n    toUtcNativeDate() {\n        return new Date(Date.UTC(this.year, this.month));\n    }\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Immutable date object, consisting of day, month and year\n */\nclass TuiDay extends TuiMonth {\n    /**\n     * @param year\n     * @param month (starting with 0)\n     * @param day\n     */\n    constructor(year, month, day) {\n        super(year, month);\n        this.day = day;\n        ngDevMode && console.assert(TuiDay.isValidDay(year, month, day));\n    }\n    /**\n     * Creates {@link TuiDay} from native {@link Date} based on local time zone\n     */\n    static fromLocalNativeDate(date) {\n        return new TuiDay(date.getFullYear(), date.getMonth(), date.getDate());\n    }\n    /**\n     * Creates {@link TuiDay} from native {@link Date} using UTC\n     */\n    static fromUtcNativeDate(date) {\n        return new TuiDay(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate());\n    }\n    /**\n     * Check validity of year, month and day\n     *\n     * @param year\n     * @param month\n     * @param day\n     * @return boolean validity\n     */\n    static isValidDay(year, month, day) {\n        return (TuiMonth.isValidMonth(year, month) &&\n            Number.isInteger(day) &&\n            tuiInRange(day, MIN_DAY, TuiMonth.getMonthDaysCount(month, TuiYear.isLeapYear(year)) + 1));\n    }\n    /**\n     * Current day based on local time zone\n     */\n    static currentLocal() {\n        const nativeDate = new Date();\n        const year = nativeDate.getFullYear();\n        const month = nativeDate.getMonth();\n        const day = nativeDate.getDate();\n        return new TuiDay(year, month, day);\n    }\n    /**\n     * Returns current day based on UTC\n     */\n    static currentUtc() {\n        const nativeDate = new Date();\n        const year = nativeDate.getUTCFullYear();\n        const month = nativeDate.getUTCMonth();\n        const day = nativeDate.getUTCDate();\n        return new TuiDay(year, month, day);\n    }\n    /**\n     * Calculates {@link TuiDay} normalizing year, month and day. {@link NaN} is turned into minimal value.\n     *\n     * @param year any year value, including invalid\n     * @param month any month value, including invalid (months start with 0)\n     * @param day any day value, including invalid\n     * @return normalized date\n     */\n    static normalizeOf(year, month, day) {\n        const normalizedYear = TuiYear.normalizeYearPart(year);\n        const normalizedMonth = TuiMonth.normalizeMonthPart(month);\n        const normalizedDay = TuiDay.normalizeDayPart(day, normalizedMonth, normalizedYear);\n        return new TuiDay(normalizedYear, normalizedMonth, normalizedDay);\n    }\n    static lengthBetween(from, to) {\n        return Math.round((to.toLocalNativeDate().getTime() - from.toLocalNativeDate().getTime()) /\n            (1000 * 60 * 60 * 24));\n    }\n    static parseRawDateString(date, dateMode = 'DMY') {\n        ngDevMode &&\n            console.assert(date.length === DATE_FILLER_LENGTH, '[parseRawDateString]: wrong date string length');\n        switch (dateMode) {\n            case 'MDY':\n                return {\n                    day: parseInt(date.slice(3, 5), 10),\n                    month: parseInt(date.slice(0, 2), 10) - 1,\n                    year: parseInt(date.slice(6, 10), 10),\n                };\n            case 'YMD':\n                return {\n                    day: parseInt(date.slice(8, 10), 10),\n                    month: parseInt(date.slice(5, 7), 10) - 1,\n                    year: parseInt(date.slice(0, 4), 10),\n                };\n            case 'DMY':\n            default:\n                return {\n                    day: parseInt(date.slice(0, 2), 10),\n                    month: parseInt(date.slice(3, 5), 10) - 1,\n                    year: parseInt(date.slice(6, 10), 10),\n                };\n        }\n    }\n    // TODO: Move month and year related code corresponding classes\n    /**\n     * Parsing a string with date with normalization\n     *\n     * @param rawDate date string\n     * @param dateMode date format of the date string (DMY | MDY | YMD)\n     * @return normalized date\n     */\n    static normalizeParse(rawDate, dateMode = 'DMY') {\n        const { day, month, year } = this.parseRawDateString(rawDate, dateMode);\n        return TuiDay.normalizeOf(year, month, day);\n    }\n    /**\n     * Parsing a date stringified in a toJSON format\n     * @param yearMonthDayString date string in format of YYYY-MM-DD\n     * @return date\n     * @throws exceptions if any part of the date is invalid\n     */\n    static jsonParse(yearMonthDayString) {\n        const { day, month, year } = this.parseRawDateString(yearMonthDayString, 'YMD');\n        if (!TuiMonth.isValidMonth(year, month) ||\n            !Number.isInteger(day) ||\n            !tuiInRange(day, MIN_DAY, TuiMonth.getMonthDaysCount(month, TuiYear.isLeapYear(year)) + 1)) {\n            throw new TuiInvalidDayException(year, month, day);\n        }\n        return new TuiDay(year, month, day);\n    }\n    static normalizeDayPart(day, month, year) {\n        ngDevMode && console.assert(TuiMonth.isValidMonth(year, month));\n        const monthDaysCount = TuiMonth.getMonthDaysCount(month, TuiYear.isLeapYear(year));\n        return tuiNormalizeToIntNumber(day, 1, monthDaysCount);\n    }\n    get formattedDayPart() {\n        return String(this.day).padStart(2, '0');\n    }\n    get isWeekend() {\n        const dayOfWeek = this.dayOfWeek(false);\n        return dayOfWeek === TuiDayOfWeek.Saturday || dayOfWeek === TuiDayOfWeek.Sunday;\n    }\n    /**\n     * Returns day of week\n     *\n     * @param startFromMonday whether week starts from Monday and not from Sunday\n     * @return day of week (from 0 to 6)\n     */\n    dayOfWeek(startFromMonday = true) {\n        const dayOfWeek = startFromMonday\n            ? this.toLocalNativeDate().getDay() - 1\n            : this.toLocalNativeDate().getDay();\n        return dayOfWeek < 0 ? 6 : dayOfWeek;\n    }\n    /**\n     * Passed date is after current\n     */\n    dayBefore(another) {\n        return (this.monthBefore(another) ||\n            (this.monthSame(another) && this.day < another.day));\n    }\n    /**\n     * Passed date is after or equals to current\n     */\n    daySameOrBefore(another) {\n        return (this.monthBefore(another) ||\n            (this.monthSame(another) && this.day <= another.day));\n    }\n    /**\n     * Passed date is the same as current\n     */\n    daySame(another) {\n        return this.monthSame(another) && this.day === another.day;\n    }\n    /**\n     * Passed date is either before or the same as current\n     */\n    daySameOrAfter(another) {\n        return (this.monthAfter(another) ||\n            (this.monthSame(another) && this.day >= another.day));\n    }\n    /**\n     * Passed date is before current\n     */\n    dayAfter(another) {\n        return (this.monthAfter(another) ||\n            (this.monthSame(another) && this.day > another.day));\n    }\n    /**\n     * Clamping date between two limits\n     *\n     * @param min\n     * @param max\n     * @return clamped date\n     */\n    dayLimit(min, max) {\n        if (min !== null && this.dayBefore(min)) {\n            return min;\n        }\n        if (max !== null && this.dayAfter(max)) {\n            return max;\n        }\n        return this;\n    }\n    /**\n     * Immutably alters current day by passed offset\n     *\n     * If resulting month has more days than original one, date is rounded to the maximum day\n     * in the resulting month. Offset of days will be calculated based on the resulted year and month\n     * to not interfere with parent classes methods\n     *\n     * @param offset\n     * @return new date object as a result of offsetting current\n     */\n    append({ year = 0, month = 0, day = 0 }) {\n        const totalMonths = (this.year + year) * MONTHS_IN_YEAR + this.month + month;\n        let years = Math.floor(totalMonths / MONTHS_IN_YEAR);\n        let months = totalMonths % MONTHS_IN_YEAR;\n        let days = Math.min(this.day, TuiMonth.getMonthDaysCount(months, TuiYear.isLeapYear(years))) + day;\n        while (days > TuiMonth.getMonthDaysCount(months, TuiYear.isLeapYear(years))) {\n            days -= TuiMonth.getMonthDaysCount(months, TuiYear.isLeapYear(years));\n            if (months === TuiMonthNumber.December) {\n                years++;\n                months = TuiMonthNumber.January;\n            }\n            else {\n                months++;\n            }\n        }\n        while (days < MIN_DAY) {\n            if (months === TuiMonthNumber.January) {\n                years--;\n                months = TuiMonthNumber.December;\n            }\n            else {\n                months--;\n            }\n            days += TuiMonth.getMonthDaysCount(months, TuiYear.isLeapYear(years));\n        }\n        return new TuiDay(years, months, days);\n    }\n    /**\n     * Returns formatted whole date\n     */\n    getFormattedDay(dateFormat, separator) {\n        ngDevMode &&\n            console.assert(separator.length === 1, 'Separator should consist of only 1 symbol');\n        const dd = this.formattedDayPart;\n        const mm = this.formattedMonthPart;\n        const yyyy = this.formattedYear;\n        switch (dateFormat) {\n            case 'MDY':\n                return `${mm}${separator}${dd}${separator}${yyyy}`;\n            case 'YMD':\n                return `${yyyy}${separator}${mm}${separator}${dd}`;\n            case 'DMY':\n            default:\n                return `${dd}${separator}${mm}${separator}${yyyy}`;\n        }\n    }\n    toString(dateFormat = 'DMY', separator = '.') {\n        return this.getFormattedDay(dateFormat, separator);\n    }\n    toJSON() {\n        return `${super.toJSON()}-${this.formattedDayPart}`;\n    }\n    /**\n     * Returns native {@link Date} based on local time zone\n     */\n    toLocalNativeDate() {\n        return new Date(this.year, this.month, this.day);\n    }\n    /**\n     * Returns native {@link Date} based on UTC\n     */\n    toUtcNativeDate() {\n        return new Date(Date.UTC(this.year, this.month, this.day));\n    }\n}\nclass TuiInvalidDayException extends Error {\n    constructor(year, month, day) {\n        super(ngDevMode ? `Invalid day: ${year}-${month}-${day}` : '');\n    }\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * An immutable range of two {@link TuiMonth} objects\n */\nclass TuiMonthRange {\n    constructor(from, to) {\n        this.from = from;\n        this.to = to;\n        ngDevMode && console.assert(from.monthSameOrBefore(to));\n    }\n    static sort(month1, month2) {\n        return month1.monthSameOrBefore(month2)\n            ? new TuiMonthRange(month1, month2)\n            : new TuiMonthRange(month2, month1);\n    }\n    get isSingleMonth() {\n        return this.from.monthSame(this.to);\n    }\n    monthSame(another) {\n        return this.from.monthSame(another.from) && this.to.monthSame(another.to);\n    }\n    toString() {\n        return `${this.from}${RANGE_SEPARATOR_CHAR}${this.to}`;\n    }\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * An immutable range of two {@link TuiDay} objects\n */\nclass TuiDayRange extends TuiMonthRange {\n    constructor(from, to) {\n        super(from, to);\n        this.from = from;\n        this.to = to;\n        ngDevMode && console.assert(from.daySameOrBefore(to));\n    }\n    /**\n     * Creates range from two days after sorting them\n     *\n     * @param day1\n     * @param day2\n     * @return new range with sorted days\n     */\n    static sort(day1, day2) {\n        return day1.daySameOrBefore(day2)\n            ? new TuiDayRange(day1, day2)\n            : new TuiDayRange(day2, day1);\n    }\n    /**\n     * Parse and correct a day range in string format\n     *\n     * @param rangeString a string of dates in a format dd.mm.yyyy - dd.mm.yyyy\n     * @param dateMode {@link TuiDateMode}\n     * @return normalized day range object\n     */\n    static normalizeParse(rangeString, dateMode = 'DMY') {\n        const leftDay = TuiDay.normalizeParse(rangeString.slice(0, DATE_FILLER_LENGTH), dateMode);\n        if (rangeString.length < DATE_RANGE_FILLER_LENGTH) {\n            return new TuiDayRange(leftDay, leftDay);\n        }\n        return TuiDayRange.sort(leftDay, TuiDay.normalizeParse(rangeString.slice(DATE_FILLER_LENGTH + RANGE_SEPARATOR_CHAR.length), dateMode));\n    }\n    get isSingleDay() {\n        return this.from.daySame(this.to);\n    }\n    /**\n     * Tests ranges for identity\n     *\n     * @param another second range to test against current\n     * @return `true` if days are identical\n     */\n    daySame(another) {\n        return this.from.daySame(another.from) && this.to.daySame(another.to);\n    }\n    /**\n     * Locks range between two days included, or limits from one side if the other is null\n     *\n     * @param min\n     * @param max\n     * @return range — clamped range\n     */\n    dayLimit(min, max) {\n        return new TuiDayRange(this.from.dayLimit(min, max), this.to.dayLimit(min, max));\n    }\n    /**\n     * Human readable format.\n     */\n    getFormattedDayRange(dateFormat, dateSeparator) {\n        const from = this.from.getFormattedDay(dateFormat, dateSeparator);\n        const to = this.to.getFormattedDay(dateFormat, dateSeparator);\n        return `${from}${RANGE_SEPARATOR_CHAR}${to}`;\n    }\n    toString(dateFormat = 'DMY', dateSeparator = '.') {\n        return this.getFormattedDayRange(dateFormat, dateSeparator);\n    }\n    toArray() {\n        const { from, to } = this;\n        const arr = [];\n        for (const day = from.toUtcNativeDate(); day <= to.toUtcNativeDate(); day.setDate(day.getDate() + 1)) {\n            arr.push(TuiDay.fromLocalNativeDate(day));\n        }\n        return arr;\n    }\n}\n\nconst TUI_FIRST_DAY = new TuiDay(MIN_YEAR, MIN_MONTH, MIN_DAY);\nconst TUI_LAST_DAY = new TuiDay(MAX_YEAR, MAX_MONTH, 31);\nconst TUI_LAST_DISPLAYED_DAY = new TuiDay(MAX_DISPLAYED_YEAR, MAX_MONTH, 31);\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Immutable time object with hours, minutes, seconds and ms\n */\nclass TuiTime {\n    constructor(hours, minutes, seconds = 0, ms = 0) {\n        this.hours = hours;\n        this.minutes = minutes;\n        this.seconds = seconds;\n        this.ms = ms;\n        ngDevMode &&\n            console.assert(\n            // Currently `TuiTime` could have hours more than 23\n            // in order to not break current behaviour of `isValidTime` the logic is duplicated\n            Number.isInteger(hours) &&\n                tuiInRange(hours, 0, Infinity) &&\n                Number.isInteger(minutes) &&\n                tuiInRange(minutes, 0, MINUTES_IN_HOUR) &&\n                Number.isInteger(seconds) &&\n                tuiInRange(seconds, 0, SECONDS_IN_MINUTE) &&\n                Number.isInteger(ms) &&\n                tuiInRange(ms, 0, 1000), 'Time must be real, but got:', hours, minutes, seconds, ms);\n    }\n    /**\n     * Checks if time is valid\n     */\n    static isValidTime(hours, minutes, seconds = 0, ms = 0) {\n        return (Number.isInteger(hours) &&\n            tuiInRange(hours, 0, HOURS_IN_DAY) &&\n            Number.isInteger(minutes) &&\n            tuiInRange(minutes, 0, MINUTES_IN_HOUR) &&\n            Number.isInteger(seconds) &&\n            tuiInRange(seconds, 0, SECONDS_IN_MINUTE) &&\n            Number.isInteger(ms) &&\n            tuiInRange(ms, 0, 1000));\n    }\n    /**\n     * Current UTC time.\n     */\n    static current() {\n        return TuiTime.fromAbsoluteMilliseconds(Date.now() % MILLISECONDS_IN_DAY);\n    }\n    /**\n     * Current time in local timezone\n     */\n    static currentLocal() {\n        const date = new Date();\n        return TuiTime.fromAbsoluteMilliseconds((Date.now() - date.getTimezoneOffset() * MILLISECONDS_IN_MINUTE) %\n            MILLISECONDS_IN_DAY);\n    }\n    /**\n     * Calculates TuiTime from milliseconds\n     */\n    static fromAbsoluteMilliseconds(milliseconds) {\n        ngDevMode && console.assert(Number.isInteger(milliseconds));\n        ngDevMode &&\n            console.assert(tuiInRange(milliseconds, 0, MILLISECONDS_IN_DAY), `Milliseconds must be below ${MILLISECONDS_IN_DAY} (milliseconds in a day).`);\n        const hours = Math.floor(milliseconds / MILLISECONDS_IN_HOUR);\n        const minutes = Math.floor((milliseconds % MILLISECONDS_IN_HOUR) / MILLISECONDS_IN_MINUTE);\n        const seconds = Math.floor(((milliseconds % MILLISECONDS_IN_HOUR) % MILLISECONDS_IN_MINUTE) / 1000) || 0;\n        const ms = Math.floor(((milliseconds % MILLISECONDS_IN_HOUR) % MILLISECONDS_IN_MINUTE) % 1000) || 0;\n        return new TuiTime(hours, minutes, seconds, ms);\n    }\n    /**\n     * Parses string into TuiTime object\n     */\n    static fromString(time) {\n        const hours = this.parseHours(time);\n        const minutes = Number(time.slice(3, 5)) || 0;\n        const seconds = Number(time.slice(6, 8)) || 0;\n        const ms = Number(time.slice(9, 12)) || 0;\n        return new TuiTime(hours, minutes, seconds, ms);\n    }\n    /**\n     * Converts Date object into TuiTime\n     * @param date\n     */\n    static fromLocalNativeDate(date) {\n        return new TuiTime(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n    }\n    static parseMeridiemPeriod(time) {\n        return (/[AP]M/.exec(time.toUpperCase().replaceAll(/\\W/g, ''))?.[0] || null);\n    }\n    static parseHours(time) {\n        const hours = Number(time.slice(0, 2));\n        const meridiem = this.parseMeridiemPeriod(time);\n        if (!meridiem) {\n            return hours;\n        }\n        if (hours === 12) {\n            return meridiem === 'AM' ? 0 : 12;\n        }\n        return meridiem === 'PM' ? hours + 12 : hours;\n    }\n    /**\n     * Shifts time by hours and minutes\n     */\n    shift({ hours = 0, minutes = 0, seconds = 0, ms = 0 }) {\n        const totalMs = this.toAbsoluteMilliseconds() +\n            hours * MILLISECONDS_IN_HOUR +\n            minutes * MILLISECONDS_IN_MINUTE +\n            seconds * MILLISECONDS_IN_SECOND +\n            ms;\n        const totalSeconds = Math.floor(totalMs / MILLISECONDS_IN_SECOND);\n        const totalMinutes = Math.floor(totalSeconds / SECONDS_IN_MINUTE);\n        const totalHours = Math.floor(totalMinutes / MINUTES_IN_HOUR);\n        return new TuiTime(this.normalizeToRange(totalHours, HOURS_IN_DAY), this.normalizeToRange(totalMinutes, MINUTES_IN_HOUR), this.normalizeToRange(totalSeconds, SECONDS_IN_MINUTE), this.normalizeToRange(totalMs, MILLISECONDS_IN_SECOND));\n    }\n    /**\n     * Converts TuiTime to string\n     */\n    toString(mode) {\n        const needAddMs = mode?.startsWith('HH:MM:SS.MSS') || (!mode && this.ms > 0);\n        const needAddSeconds = needAddMs || mode?.startsWith('HH:MM:SS') || (!mode && this.seconds > 0);\n        const { hours = this.hours, meridiem = '' } = mode?.includes('AA')\n            ? this.toTwelveHour(this.hours)\n            : {};\n        const hhMm = `${this.formatTime(hours)}:${this.formatTime(this.minutes)}`;\n        const ss = needAddSeconds ? `:${this.formatTime(this.seconds)}` : '';\n        const mss = needAddMs ? `.${this.formatTime(this.ms, 3)}` : '';\n        const aa = meridiem && `${CHAR_NO_BREAK_SPACE}${meridiem}`;\n        return `${hhMm}${ss}${mss}${aa}`;\n    }\n    valueOf() {\n        return this.toAbsoluteMilliseconds();\n    }\n    /**\n     * Returns the primitive value of the given Date object.\n     * Depending on the argument, the method can return either a string or a number.\n     * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/@@toPrimitive\n     */\n    [Symbol.toPrimitive](hint) {\n        return Date.prototype[Symbol.toPrimitive].call(this, hint);\n    }\n    /**\n     * Converts TuiTime to milliseconds\n     */\n    toAbsoluteMilliseconds() {\n        return (this.hours * MILLISECONDS_IN_HOUR +\n            this.minutes * MILLISECONDS_IN_MINUTE +\n            this.seconds * 1000 +\n            this.ms);\n    }\n    formatTime(time, digits = 2) {\n        return String(time).padStart(digits, '0');\n    }\n    toTwelveHour(hours) {\n        const meridiem = hours >= 12 ? 'PM' : 'AM';\n        if (hours === 0 || hours === 12) {\n            return { meridiem, hours: 12 };\n        }\n        return { meridiem, hours: hours % 12 };\n    }\n    normalizeToRange(value, modulus) {\n        return ((value % modulus) + modulus) % modulus;\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DATE_FILLER_LENGTH, DATE_RANGE_FILLER_LENGTH, DAYS_IN_LEAP_YEAR, DAYS_IN_NORMAL_YEAR, DAYS_IN_WEEK, HOURS_IN_DAY, MAX_DISPLAYED_YEAR, MAX_MONTH, MAX_YEAR, MILLISECONDS_IN_DAY, MILLISECONDS_IN_HOUR, MILLISECONDS_IN_MINUTE, MILLISECONDS_IN_SECOND, MINUTES_IN_HOUR, MIN_DAY, MIN_MONTH, MIN_YEAR, MONTHS_IN_YEAR, RANGE_SEPARATOR_CHAR, SECONDS_IN_MINUTE, TUI_FIRST_DAY, TUI_LAST_DAY, TUI_LAST_DISPLAYED_DAY, TuiDay, TuiDayOfWeek, TuiDayRange, TuiInvalidDayException, TuiMonth, TuiMonthNumber, TuiMonthRange, TuiTime, TuiYear, tuiDateClamp };\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,YAAY,QAAQ,yBAAyB;AAC3E,SAASC,UAAU,EAAEC,uBAAuB,QAAQ,0BAA0B;AAE9E,SAASC,YAAYA,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAClC,IAAIA,GAAG,IAAIA,GAAG,GAAGF,IAAI,EAAE;IACnB,OAAOE,GAAG;EACd;EACA,IAAID,GAAG,IAAIA,GAAG,GAAGD,IAAI,EAAE;IACnB,OAAOC,GAAG;EACd;EACA,OAAOD,IAAI;AACf;AAEA,MAAMG,YAAY,GAAG,CAAC;AACtB,MAAMC,mBAAmB,GAAG,GAAG;AAC/B,MAAMC,iBAAiB,GAAG,GAAG;AAC7B,MAAMC,cAAc,GAAG,EAAE;AACzB,MAAMC,OAAO,GAAG,CAAC;AACjB,MAAMC,SAAS,GAAG,CAAC;AACnB,MAAMC,SAAS,GAAG,EAAE;AACpB,MAAMC,QAAQ,GAAG,CAAC;AAClB,MAAMC,QAAQ,GAAG,IAAI;AACrB,MAAMC,kBAAkB,GAAG,IAAI;AAC/B,MAAMC,oBAAoB,GAAG,GAAGlB,mBAAmB,GAAGC,YAAY,GAAGD,mBAAmB,EAAE;AAC1F,MAAMmB,sBAAsB,GAAG,IAAI;AACnC,MAAMC,iBAAiB,GAAG,EAAE;AAC5B,MAAMC,eAAe,GAAG,EAAE;AAC1B,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,sBAAsB,GAAGJ,sBAAsB,GAAGC,iBAAiB;AACzE,MAAMI,oBAAoB,GAAGD,sBAAsB,GAAGF,eAAe;AACrE,MAAMI,mBAAmB,GAAGD,oBAAoB,GAAGF,YAAY;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,kBAAkB,GAAG,EAAE;AAC7B;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAG,CAAC,GAAGD,kBAAkB,GAAGR,oBAAoB,CAACU,MAAM;AAErF,MAAMC,YAAY,GAAG;EACjBC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,CAAC;EACVC,SAAS,EAAE,CAAC;EACZC,QAAQ,EAAE,CAAC;EACXC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE;AACd,CAAC;AAED,MAAMC,cAAc,GAAG;EACnBC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,CAAC;EACXC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,CAAC;EACTC,SAAS,EAAE,CAAC;EACZC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,EAAE;EACZC,QAAQ,EAAE;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACVC,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChBC,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACL,OAAO,CAACM,WAAW,CAACJ,IAAI,CAAC,CAAC;EAC1D;EACA;AACJ;AACA;EACI,OAAOI,WAAWA,CAACJ,IAAI,EAAE;IACrB,OAAOK,MAAM,CAACC,SAAS,CAACN,IAAI,CAAC,IAAIlD,UAAU,CAACkD,IAAI,EAAErC,QAAQ,EAAEC,QAAQ,GAAG,CAAC,CAAC;EAC7E;EACA;AACJ;AACA;EACI,OAAO2C,UAAUA,CAACP,IAAI,EAAE;IACpBC,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACL,OAAO,CAACM,WAAW,CAACJ,IAAI,CAAC,CAAC;IACtD,OAAOA,IAAI,GAAG,GAAG,KAAK,CAAC,IAAKA,IAAI,GAAG,CAAC,KAAK,CAAC,IAAIA,IAAI,GAAG,GAAG,KAAK,CAAE;EACnE;EACA;AACJ;AACA;EACI,OAAOQ,oBAAoBA,CAACR,IAAI,EAAE;IAC9BC,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACL,OAAO,CAACM,WAAW,CAACJ,IAAI,CAAC,CAAC;IACtD,OAAOS,IAAI,CAACC,IAAI,CAACV,IAAI,GAAG,GAAG,CAAC,IAAIS,IAAI,CAACC,IAAI,CAACV,IAAI,GAAG,CAAC,CAAC,GAAGS,IAAI,CAACC,IAAI,CAACV,IAAI,GAAG,GAAG,CAAC,CAAC;EAChF;EACA,OAAOW,aAAaA,CAACC,IAAI,EAAEC,EAAE,EAAE;IAC3B,OAAOA,EAAE,CAACb,IAAI,GAAGY,IAAI,CAACZ,IAAI;EAC9B;EACA;AACJ;AACA;EACI,OAAOc,iBAAiBA,CAACd,IAAI,EAAE;IAC3B,OAAOjD,uBAAuB,CAACiD,IAAI,EAAErC,QAAQ,EAAEC,QAAQ,CAAC;EAC5D;EACA,IAAImD,aAAaA,CAAA,EAAG;IAChB,OAAOC,MAAM,CAAC,IAAI,CAAChB,IAAI,CAAC,CAACiB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC7C;EACA,IAAIV,UAAUA,CAAA,EAAG;IACb,OAAOT,OAAO,CAACS,UAAU,CAAC,IAAI,CAACP,IAAI,CAAC;EACxC;EACA;AACJ;AACA;EACI,IAAIkB,iBAAiBA,CAAA,EAAG;IACpB,OAAOpB,OAAO,CAACU,oBAAoB,CAAC,IAAI,CAACR,IAAI,CAAC;EAClD;EACA;AACJ;AACA;EACImB,UAAUA,CAAC;IAAEnB;EAAK,CAAC,EAAE;IACjB,OAAO,IAAI,CAACA,IAAI,GAAGA,IAAI;EAC3B;EACA;AACJ;AACA;EACIoB,gBAAgBA,CAAC;IAAEpB;EAAK,CAAC,EAAE;IACvB,OAAO,IAAI,CAACA,IAAI,IAAIA,IAAI;EAC5B;EACA;AACJ;AACA;EACIqB,QAAQA,CAAC;IAAErB;EAAK,CAAC,EAAE;IACf,OAAO,IAAI,CAACA,IAAI,KAAKA,IAAI;EAC7B;EACA;AACJ;AACA;EACIsB,eAAeA,CAAC;IAAEtB;EAAK,CAAC,EAAE;IACtB,OAAO,IAAI,CAACA,IAAI,IAAIA,IAAI;EAC5B;EACA;AACJ;AACA;EACIuB,SAASA,CAAC;IAAEvB;EAAK,CAAC,EAAE;IAChB,OAAO,IAAI,CAACA,IAAI,GAAGA,IAAI;EAC3B;EACA;AACJ;AACA;EACIwB,MAAMA,CAAC;IAAExB,IAAI,GAAG;EAAE,CAAC,EAAE;IACjBC,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACE,MAAM,CAACC,SAAS,CAACN,IAAI,CAAC,CAAC;IACnD,MAAMyB,UAAU,GAAG,IAAI,CAACzB,IAAI,GAAGA,IAAI;IACnCC,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACL,OAAO,CAACM,WAAW,CAACqB,UAAU,CAAC,CAAC;IAC5D,OAAO,IAAI3B,OAAO,CAAC2B,UAAU,CAAC;EAClC;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACX,aAAa;EAC7B;EACAY,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC3B,IAAI;EACpB;EACA;AACJ;AACA;AACA;AACA;EACI,CAAC4B,MAAM,CAACC,WAAW,EAAEC,IAAI,EAAE;IACvB,OAAOC,IAAI,CAACC,SAAS,CAACJ,MAAM,CAACC,WAAW,CAAC,CAACI,IAAI,CAAC,IAAI,EAAEH,IAAI,CAAC;EAC9D;EACAI,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACnB,aAAa;EAC7B;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMoB,QAAQ,SAASrC,OAAO,CAAC;EAC3B;AACJ;AACA;AACA;EACIC,WAAWA,CAACC,IAAI,EAAEoC,KAAK,EAAE;IACrB,KAAK,CAACpC,IAAI,CAAC;IACX,IAAI,CAACoC,KAAK,GAAGA,KAAK;IAClBnC,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACgC,QAAQ,CAACE,YAAY,CAACrC,IAAI,EAAEoC,KAAK,CAAC,CAAC;EACnE;EACA;AACJ;AACA;EACI,OAAOC,YAAYA,CAACrC,IAAI,EAAEoC,KAAK,EAAE;IAC7B,OAAOtC,OAAO,CAACM,WAAW,CAACJ,IAAI,CAAC,IAAImC,QAAQ,CAACG,gBAAgB,CAACF,KAAK,CAAC;EACxE;EACA;AACJ;AACA;EACI,OAAOG,iBAAiBA,CAACH,KAAK,EAAE7B,UAAU,EAAE;IACxCN,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACgC,QAAQ,CAACG,gBAAgB,CAACF,KAAK,CAAC,CAAC;IAC7D,QAAQA,KAAK;MACT,KAAKnD,cAAc,CAACI,KAAK;MACzB,KAAKJ,cAAc,CAACM,IAAI;MACxB,KAAKN,cAAc,CAACW,QAAQ;MAC5B,KAAKX,cAAc,CAACS,SAAS;QACzB,OAAO,EAAE;MACb,KAAKT,cAAc,CAACE,QAAQ;QACxB,OAAOoB,UAAU,GAAG,EAAE,GAAG,EAAE;MAC/B;QACI,OAAO,EAAE;IACjB;EACJ;EACA;AACJ;AACA;AACA;EACI,OAAOiC,YAAYA,CAAA,EAAG;IAClB,MAAMC,UAAU,GAAG,IAAIV,IAAI,CAAC,CAAC;IAC7B,OAAO,IAAII,QAAQ,CAACM,UAAU,CAACC,WAAW,CAAC,CAAC,EAAED,UAAU,CAACE,QAAQ,CAAC,CAAC,CAAC;EACxE;EACA;AACJ;AACA;EACI,OAAOC,UAAUA,CAAA,EAAG;IAChB,MAAMH,UAAU,GAAG,IAAIV,IAAI,CAAC,CAAC;IAC7B,OAAO,IAAII,QAAQ,CAACM,UAAU,CAACI,cAAc,CAAC,CAAC,EAAEJ,UAAU,CAACK,WAAW,CAAC,CAAC,CAAC;EAC9E;EACA,OAAOnC,aAAaA,CAACC,IAAI,EAAEC,EAAE,EAAE;IAC3B,MAAMkC,YAAY,GAAGnC,IAAI,CAACwB,KAAK,GAAGxB,IAAI,CAACZ,IAAI,GAAG,EAAE;IAChD,MAAMgD,UAAU,GAAGnC,EAAE,CAACuB,KAAK,GAAGvB,EAAE,CAACb,IAAI,GAAG,EAAE;IAC1C,OAAOgD,UAAU,GAAGD,YAAY;EACpC;EACA;AACJ;AACA;EACI,OAAOE,kBAAkBA,CAACb,KAAK,EAAE;IAC7B,OAAOrF,uBAAuB,CAACqF,KAAK,EAAE3E,SAAS,EAAEC,SAAS,CAAC;EAC/D;EACA;AACJ;AACA;EACI,OAAO4E,gBAAgBA,CAACF,KAAK,EAAE;IAC3B,OAAO/B,MAAM,CAACC,SAAS,CAAC8B,KAAK,CAAC,IAAItF,UAAU,CAACsF,KAAK,EAAE3E,SAAS,EAAEC,SAAS,GAAG,CAAC,CAAC;EACjF;EACA,IAAIwF,kBAAkBA,CAAA,EAAG;IACrB,OAAOlC,MAAM,CAAC,IAAI,CAACoB,KAAK,GAAG,CAAC,CAAC,CAACnB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAClD;EACA;AACJ;AACA;EACI,IAAIkC,SAASA,CAAA,EAAG;IACZ,OAAOhB,QAAQ,CAACI,iBAAiB,CAAC,IAAI,CAACH,KAAK,EAAE,IAAI,CAAC7B,UAAU,CAAC;EAClE;EACA;AACJ;AACA;EACI6C,WAAWA,CAACC,OAAO,EAAE;IACjB,OAAQ,IAAI,CAAClC,UAAU,CAACkC,OAAO,CAAC,IAC3B,IAAI,CAAChC,QAAQ,CAACgC,OAAO,CAAC,IAAI,IAAI,CAACjB,KAAK,GAAGiB,OAAO,CAACjB,KAAM;EAC9D;EACA;AACJ;AACA;EACIkB,iBAAiBA,CAACD,OAAO,EAAE;IACvB,OAAQ,IAAI,CAAClC,UAAU,CAACkC,OAAO,CAAC,IAC3B,IAAI,CAAChC,QAAQ,CAACgC,OAAO,CAAC,IAAI,IAAI,CAACjB,KAAK,IAAIiB,OAAO,CAACjB,KAAM;EAC/D;EACA;AACJ;AACA;EACImB,SAASA,CAACF,OAAO,EAAE;IACf,OAAO,IAAI,CAAChC,QAAQ,CAACgC,OAAO,CAAC,IAAI,IAAI,CAACjB,KAAK,KAAKiB,OAAO,CAACjB,KAAK;EACjE;EACA;AACJ;AACA;EACIoB,gBAAgBA,CAACH,OAAO,EAAE;IACtB,OAAQ,IAAI,CAAC9B,SAAS,CAAC8B,OAAO,CAAC,IAC1B,IAAI,CAAChC,QAAQ,CAACgC,OAAO,CAAC,IAAI,IAAI,CAACjB,KAAK,IAAIiB,OAAO,CAACjB,KAAM;EAC/D;EACA;AACJ;AACA;EACIqB,UAAUA,CAACJ,OAAO,EAAE;IAChB,OAAQ,IAAI,CAAC9B,SAAS,CAAC8B,OAAO,CAAC,IAC1B,IAAI,CAAChC,QAAQ,CAACgC,OAAO,CAAC,IAAI,IAAI,CAACjB,KAAK,GAAGiB,OAAO,CAACjB,KAAM;EAC9D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIZ,MAAMA,CAAC;IAAExB,IAAI,GAAG,CAAC;IAAEoC,KAAK,GAAG;EAAE,CAAC,EAAE;IAC5B,MAAMsB,WAAW,GAAG,CAAC,IAAI,CAAC1D,IAAI,GAAGA,IAAI,IAAIzC,cAAc,GAAG,IAAI,CAAC6E,KAAK,GAAGA,KAAK;IAC5E,OAAO,IAAID,QAAQ,CAAC1B,IAAI,CAACkD,KAAK,CAACD,WAAW,GAAGnG,cAAc,CAAC,EAAEmG,WAAW,GAAGnG,cAAc,CAAC;EAC/F;EACAmE,QAAQA,CAAA,EAAG;IACP,OAAO,GAAG,IAAI,CAACwB,kBAAkB,IAAI,IAAI,CAACnC,aAAa,EAAE;EAC7D;EACAY,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACiC,iBAAiB,CAAC,CAAC,CAACjC,OAAO,CAAC,CAAC;EAC7C;EACAO,MAAMA,CAAA,EAAG;IACL,OAAO,GAAG,KAAK,CAACA,MAAM,CAAC,CAAC,IAAI,IAAI,CAACgB,kBAAkB,EAAE;EACzD;EACA;AACJ;AACA;EACIU,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI7B,IAAI,CAAC,IAAI,CAAC/B,IAAI,EAAE,IAAI,CAACoC,KAAK,CAAC;EAC1C;EACA;AACJ;AACA;EACIyB,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI9B,IAAI,CAACA,IAAI,CAAC+B,GAAG,CAAC,IAAI,CAAC9D,IAAI,EAAE,IAAI,CAACoC,KAAK,CAAC,CAAC;EACpD;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM2B,MAAM,SAAS5B,QAAQ,CAAC;EAC1B;AACJ;AACA;AACA;AACA;EACIpC,WAAWA,CAACC,IAAI,EAAEoC,KAAK,EAAE4B,GAAG,EAAE;IAC1B,KAAK,CAAChE,IAAI,EAAEoC,KAAK,CAAC;IAClB,IAAI,CAAC4B,GAAG,GAAGA,GAAG;IACd/D,SAAS,IAAIC,OAAO,CAACC,MAAM,CAAC4D,MAAM,CAACE,UAAU,CAACjE,IAAI,EAAEoC,KAAK,EAAE4B,GAAG,CAAC,CAAC;EACpE;EACA;AACJ;AACA;EACI,OAAOE,mBAAmBA,CAACjH,IAAI,EAAE;IAC7B,OAAO,IAAI8G,MAAM,CAAC9G,IAAI,CAACyF,WAAW,CAAC,CAAC,EAAEzF,IAAI,CAAC0F,QAAQ,CAAC,CAAC,EAAE1F,IAAI,CAACkH,OAAO,CAAC,CAAC,CAAC;EAC1E;EACA;AACJ;AACA;EACI,OAAOC,iBAAiBA,CAACnH,IAAI,EAAE;IAC3B,OAAO,IAAI8G,MAAM,CAAC9G,IAAI,CAAC4F,cAAc,CAAC,CAAC,EAAE5F,IAAI,CAAC6F,WAAW,CAAC,CAAC,EAAE7F,IAAI,CAACoH,UAAU,CAAC,CAAC,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOJ,UAAUA,CAACjE,IAAI,EAAEoC,KAAK,EAAE4B,GAAG,EAAE;IAChC,OAAQ7B,QAAQ,CAACE,YAAY,CAACrC,IAAI,EAAEoC,KAAK,CAAC,IACtC/B,MAAM,CAACC,SAAS,CAAC0D,GAAG,CAAC,IACrBlH,UAAU,CAACkH,GAAG,EAAExG,OAAO,EAAE2E,QAAQ,CAACI,iBAAiB,CAACH,KAAK,EAAEtC,OAAO,CAACS,UAAU,CAACP,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EACjG;EACA;AACJ;AACA;EACI,OAAOwC,YAAYA,CAAA,EAAG;IAClB,MAAMC,UAAU,GAAG,IAAIV,IAAI,CAAC,CAAC;IAC7B,MAAM/B,IAAI,GAAGyC,UAAU,CAACC,WAAW,CAAC,CAAC;IACrC,MAAMN,KAAK,GAAGK,UAAU,CAACE,QAAQ,CAAC,CAAC;IACnC,MAAMqB,GAAG,GAAGvB,UAAU,CAAC0B,OAAO,CAAC,CAAC;IAChC,OAAO,IAAIJ,MAAM,CAAC/D,IAAI,EAAEoC,KAAK,EAAE4B,GAAG,CAAC;EACvC;EACA;AACJ;AACA;EACI,OAAOpB,UAAUA,CAAA,EAAG;IAChB,MAAMH,UAAU,GAAG,IAAIV,IAAI,CAAC,CAAC;IAC7B,MAAM/B,IAAI,GAAGyC,UAAU,CAACI,cAAc,CAAC,CAAC;IACxC,MAAMT,KAAK,GAAGK,UAAU,CAACK,WAAW,CAAC,CAAC;IACtC,MAAMkB,GAAG,GAAGvB,UAAU,CAAC4B,UAAU,CAAC,CAAC;IACnC,OAAO,IAAIN,MAAM,CAAC/D,IAAI,EAAEoC,KAAK,EAAE4B,GAAG,CAAC;EACvC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOM,WAAWA,CAACtE,IAAI,EAAEoC,KAAK,EAAE4B,GAAG,EAAE;IACjC,MAAMO,cAAc,GAAGzE,OAAO,CAACgB,iBAAiB,CAACd,IAAI,CAAC;IACtD,MAAMwE,eAAe,GAAGrC,QAAQ,CAACc,kBAAkB,CAACb,KAAK,CAAC;IAC1D,MAAMqC,aAAa,GAAGV,MAAM,CAACW,gBAAgB,CAACV,GAAG,EAAEQ,eAAe,EAAED,cAAc,CAAC;IACnF,OAAO,IAAIR,MAAM,CAACQ,cAAc,EAAEC,eAAe,EAAEC,aAAa,CAAC;EACrE;EACA,OAAO9D,aAAaA,CAACC,IAAI,EAAEC,EAAE,EAAE;IAC3B,OAAOJ,IAAI,CAACkE,KAAK,CAAC,CAAC9D,EAAE,CAAC+C,iBAAiB,CAAC,CAAC,CAACgB,OAAO,CAAC,CAAC,GAAGhE,IAAI,CAACgD,iBAAiB,CAAC,CAAC,CAACgB,OAAO,CAAC,CAAC,KACnF,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC9B;EACA,OAAOC,kBAAkBA,CAAC5H,IAAI,EAAE6H,QAAQ,GAAG,KAAK,EAAE;IAC9C7E,SAAS,IACLC,OAAO,CAACC,MAAM,CAAClD,IAAI,CAACuB,MAAM,KAAKF,kBAAkB,EAAE,gDAAgD,CAAC;IACxG,QAAQwG,QAAQ;MACZ,KAAK,KAAK;QACN,OAAO;UACHd,GAAG,EAAEe,QAAQ,CAAC9H,IAAI,CAAC+H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;UACnC5C,KAAK,EAAE2C,QAAQ,CAAC9H,IAAI,CAAC+H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;UACzChF,IAAI,EAAE+E,QAAQ,CAAC9H,IAAI,CAAC+H,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;QACxC,CAAC;MACL,KAAK,KAAK;QACN,OAAO;UACHhB,GAAG,EAAEe,QAAQ,CAAC9H,IAAI,CAAC+H,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;UACpC5C,KAAK,EAAE2C,QAAQ,CAAC9H,IAAI,CAAC+H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;UACzChF,IAAI,EAAE+E,QAAQ,CAAC9H,IAAI,CAAC+H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QACvC,CAAC;MACL,KAAK,KAAK;MACV;QACI,OAAO;UACHhB,GAAG,EAAEe,QAAQ,CAAC9H,IAAI,CAAC+H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;UACnC5C,KAAK,EAAE2C,QAAQ,CAAC9H,IAAI,CAAC+H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;UACzChF,IAAI,EAAE+E,QAAQ,CAAC9H,IAAI,CAAC+H,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;QACxC,CAAC;IACT;EACJ;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,cAAcA,CAACC,OAAO,EAAEJ,QAAQ,GAAG,KAAK,EAAE;IAC7C,MAAM;MAAEd,GAAG;MAAE5B,KAAK;MAAEpC;IAAK,CAAC,GAAG,IAAI,CAAC6E,kBAAkB,CAACK,OAAO,EAAEJ,QAAQ,CAAC;IACvE,OAAOf,MAAM,CAACO,WAAW,CAACtE,IAAI,EAAEoC,KAAK,EAAE4B,GAAG,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOmB,SAASA,CAACC,kBAAkB,EAAE;IACjC,MAAM;MAAEpB,GAAG;MAAE5B,KAAK;MAAEpC;IAAK,CAAC,GAAG,IAAI,CAAC6E,kBAAkB,CAACO,kBAAkB,EAAE,KAAK,CAAC;IAC/E,IAAI,CAACjD,QAAQ,CAACE,YAAY,CAACrC,IAAI,EAAEoC,KAAK,CAAC,IACnC,CAAC/B,MAAM,CAACC,SAAS,CAAC0D,GAAG,CAAC,IACtB,CAAClH,UAAU,CAACkH,GAAG,EAAExG,OAAO,EAAE2E,QAAQ,CAACI,iBAAiB,CAACH,KAAK,EAAEtC,OAAO,CAACS,UAAU,CAACP,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;MAC5F,MAAM,IAAIqF,sBAAsB,CAACrF,IAAI,EAAEoC,KAAK,EAAE4B,GAAG,CAAC;IACtD;IACA,OAAO,IAAID,MAAM,CAAC/D,IAAI,EAAEoC,KAAK,EAAE4B,GAAG,CAAC;EACvC;EACA,OAAOU,gBAAgBA,CAACV,GAAG,EAAE5B,KAAK,EAAEpC,IAAI,EAAE;IACtCC,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACgC,QAAQ,CAACE,YAAY,CAACrC,IAAI,EAAEoC,KAAK,CAAC,CAAC;IAC/D,MAAMkD,cAAc,GAAGnD,QAAQ,CAACI,iBAAiB,CAACH,KAAK,EAAEtC,OAAO,CAACS,UAAU,CAACP,IAAI,CAAC,CAAC;IAClF,OAAOjD,uBAAuB,CAACiH,GAAG,EAAE,CAAC,EAAEsB,cAAc,CAAC;EAC1D;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAOvE,MAAM,CAAC,IAAI,CAACgD,GAAG,CAAC,CAAC/C,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC5C;EACA,IAAIuE,SAASA,CAAA,EAAG;IACZ,MAAMC,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,KAAK,CAAC;IACvC,OAAOA,SAAS,KAAKhH,YAAY,CAACO,QAAQ,IAAIyG,SAAS,KAAKhH,YAAY,CAACC,MAAM;EACnF;EACA;AACJ;AACA;AACA;AACA;AACA;EACI+G,SAASA,CAACC,eAAe,GAAG,IAAI,EAAE;IAC9B,MAAMD,SAAS,GAAGC,eAAe,GAC3B,IAAI,CAAC9B,iBAAiB,CAAC,CAAC,CAAC+B,MAAM,CAAC,CAAC,GAAG,CAAC,GACrC,IAAI,CAAC/B,iBAAiB,CAAC,CAAC,CAAC+B,MAAM,CAAC,CAAC;IACvC,OAAOF,SAAS,GAAG,CAAC,GAAG,CAAC,GAAGA,SAAS;EACxC;EACA;AACJ;AACA;EACIG,SAASA,CAACvC,OAAO,EAAE;IACf,OAAQ,IAAI,CAACD,WAAW,CAACC,OAAO,CAAC,IAC5B,IAAI,CAACE,SAAS,CAACF,OAAO,CAAC,IAAI,IAAI,CAACW,GAAG,GAAGX,OAAO,CAACW,GAAI;EAC3D;EACA;AACJ;AACA;EACI6B,eAAeA,CAACxC,OAAO,EAAE;IACrB,OAAQ,IAAI,CAACD,WAAW,CAACC,OAAO,CAAC,IAC5B,IAAI,CAACE,SAAS,CAACF,OAAO,CAAC,IAAI,IAAI,CAACW,GAAG,IAAIX,OAAO,CAACW,GAAI;EAC5D;EACA;AACJ;AACA;EACI8B,OAAOA,CAACzC,OAAO,EAAE;IACb,OAAO,IAAI,CAACE,SAAS,CAACF,OAAO,CAAC,IAAI,IAAI,CAACW,GAAG,KAAKX,OAAO,CAACW,GAAG;EAC9D;EACA;AACJ;AACA;EACI+B,cAAcA,CAAC1C,OAAO,EAAE;IACpB,OAAQ,IAAI,CAACI,UAAU,CAACJ,OAAO,CAAC,IAC3B,IAAI,CAACE,SAAS,CAACF,OAAO,CAAC,IAAI,IAAI,CAACW,GAAG,IAAIX,OAAO,CAACW,GAAI;EAC5D;EACA;AACJ;AACA;EACIgC,QAAQA,CAAC3C,OAAO,EAAE;IACd,OAAQ,IAAI,CAACI,UAAU,CAACJ,OAAO,CAAC,IAC3B,IAAI,CAACE,SAAS,CAACF,OAAO,CAAC,IAAI,IAAI,CAACW,GAAG,GAAGX,OAAO,CAACW,GAAI;EAC3D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIiC,QAAQA,CAAC/I,GAAG,EAAEC,GAAG,EAAE;IACf,IAAID,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC0I,SAAS,CAAC1I,GAAG,CAAC,EAAE;MACrC,OAAOA,GAAG;IACd;IACA,IAAIC,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC6I,QAAQ,CAAC7I,GAAG,CAAC,EAAE;MACpC,OAAOA,GAAG;IACd;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIqE,MAAMA,CAAC;IAAExB,IAAI,GAAG,CAAC;IAAEoC,KAAK,GAAG,CAAC;IAAE4B,GAAG,GAAG;EAAE,CAAC,EAAE;IACrC,MAAMN,WAAW,GAAG,CAAC,IAAI,CAAC1D,IAAI,GAAGA,IAAI,IAAIzC,cAAc,GAAG,IAAI,CAAC6E,KAAK,GAAGA,KAAK;IAC5E,IAAI8D,KAAK,GAAGzF,IAAI,CAACkD,KAAK,CAACD,WAAW,GAAGnG,cAAc,CAAC;IACpD,IAAI4I,MAAM,GAAGzC,WAAW,GAAGnG,cAAc;IACzC,IAAI6I,IAAI,GAAG3F,IAAI,CAACvD,GAAG,CAAC,IAAI,CAAC8G,GAAG,EAAE7B,QAAQ,CAACI,iBAAiB,CAAC4D,MAAM,EAAErG,OAAO,CAACS,UAAU,CAAC2F,KAAK,CAAC,CAAC,CAAC,GAAGlC,GAAG;IAClG,OAAOoC,IAAI,GAAGjE,QAAQ,CAACI,iBAAiB,CAAC4D,MAAM,EAAErG,OAAO,CAACS,UAAU,CAAC2F,KAAK,CAAC,CAAC,EAAE;MACzEE,IAAI,IAAIjE,QAAQ,CAACI,iBAAiB,CAAC4D,MAAM,EAAErG,OAAO,CAACS,UAAU,CAAC2F,KAAK,CAAC,CAAC;MACrE,IAAIC,MAAM,KAAKlH,cAAc,CAACY,QAAQ,EAAE;QACpCqG,KAAK,EAAE;QACPC,MAAM,GAAGlH,cAAc,CAACC,OAAO;MACnC,CAAC,MACI;QACDiH,MAAM,EAAE;MACZ;IACJ;IACA,OAAOC,IAAI,GAAG5I,OAAO,EAAE;MACnB,IAAI2I,MAAM,KAAKlH,cAAc,CAACC,OAAO,EAAE;QACnCgH,KAAK,EAAE;QACPC,MAAM,GAAGlH,cAAc,CAACY,QAAQ;MACpC,CAAC,MACI;QACDsG,MAAM,EAAE;MACZ;MACAC,IAAI,IAAIjE,QAAQ,CAACI,iBAAiB,CAAC4D,MAAM,EAAErG,OAAO,CAACS,UAAU,CAAC2F,KAAK,CAAC,CAAC;IACzE;IACA,OAAO,IAAInC,MAAM,CAACmC,KAAK,EAAEC,MAAM,EAAEC,IAAI,CAAC;EAC1C;EACA;AACJ;AACA;EACIC,eAAeA,CAACC,UAAU,EAAEC,SAAS,EAAE;IACnCtG,SAAS,IACLC,OAAO,CAACC,MAAM,CAACoG,SAAS,CAAC/H,MAAM,KAAK,CAAC,EAAE,2CAA2C,CAAC;IACvF,MAAMgI,EAAE,GAAG,IAAI,CAACjB,gBAAgB;IAChC,MAAMkB,EAAE,GAAG,IAAI,CAACvD,kBAAkB;IAClC,MAAMwD,IAAI,GAAG,IAAI,CAAC3F,aAAa;IAC/B,QAAQuF,UAAU;MACd,KAAK,KAAK;QACN,OAAO,GAAGG,EAAE,GAAGF,SAAS,GAAGC,EAAE,GAAGD,SAAS,GAAGG,IAAI,EAAE;MACtD,KAAK,KAAK;QACN,OAAO,GAAGA,IAAI,GAAGH,SAAS,GAAGE,EAAE,GAAGF,SAAS,GAAGC,EAAE,EAAE;MACtD,KAAK,KAAK;MACV;QACI,OAAO,GAAGA,EAAE,GAAGD,SAAS,GAAGE,EAAE,GAAGF,SAAS,GAAGG,IAAI,EAAE;IAC1D;EACJ;EACAhF,QAAQA,CAAC4E,UAAU,GAAG,KAAK,EAAEC,SAAS,GAAG,GAAG,EAAE;IAC1C,OAAO,IAAI,CAACF,eAAe,CAACC,UAAU,EAAEC,SAAS,CAAC;EACtD;EACArE,MAAMA,CAAA,EAAG;IACL,OAAO,GAAG,KAAK,CAACA,MAAM,CAAC,CAAC,IAAI,IAAI,CAACqD,gBAAgB,EAAE;EACvD;EACA;AACJ;AACA;EACI3B,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI7B,IAAI,CAAC,IAAI,CAAC/B,IAAI,EAAE,IAAI,CAACoC,KAAK,EAAE,IAAI,CAAC4B,GAAG,CAAC;EACpD;EACA;AACJ;AACA;EACIH,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI9B,IAAI,CAACA,IAAI,CAAC+B,GAAG,CAAC,IAAI,CAAC9D,IAAI,EAAE,IAAI,CAACoC,KAAK,EAAE,IAAI,CAAC4B,GAAG,CAAC,CAAC;EAC9D;AACJ;AACA,MAAMqB,sBAAsB,SAASsB,KAAK,CAAC;EACvC5G,WAAWA,CAACC,IAAI,EAAEoC,KAAK,EAAE4B,GAAG,EAAE;IAC1B,KAAK,CAAC/D,SAAS,GAAG,gBAAgBD,IAAI,IAAIoC,KAAK,IAAI4B,GAAG,EAAE,GAAG,EAAE,CAAC;EAClE;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM4C,aAAa,CAAC;EAChB7G,WAAWA,CAACa,IAAI,EAAEC,EAAE,EAAE;IAClB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZZ,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACS,IAAI,CAAC0C,iBAAiB,CAACzC,EAAE,CAAC,CAAC;EAC3D;EACA,OAAOgG,IAAIA,CAACC,MAAM,EAAEC,MAAM,EAAE;IACxB,OAAOD,MAAM,CAACxD,iBAAiB,CAACyD,MAAM,CAAC,GACjC,IAAIH,aAAa,CAACE,MAAM,EAAEC,MAAM,CAAC,GACjC,IAAIH,aAAa,CAACG,MAAM,EAAED,MAAM,CAAC;EAC3C;EACA,IAAIE,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACpG,IAAI,CAAC2C,SAAS,CAAC,IAAI,CAAC1C,EAAE,CAAC;EACvC;EACA0C,SAASA,CAACF,OAAO,EAAE;IACf,OAAO,IAAI,CAACzC,IAAI,CAAC2C,SAAS,CAACF,OAAO,CAACzC,IAAI,CAAC,IAAI,IAAI,CAACC,EAAE,CAAC0C,SAAS,CAACF,OAAO,CAACxC,EAAE,CAAC;EAC7E;EACAa,QAAQA,CAAA,EAAG;IACP,OAAO,GAAG,IAAI,CAACd,IAAI,GAAG9C,oBAAoB,GAAG,IAAI,CAAC+C,EAAE,EAAE;EAC1D;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMoG,WAAW,SAASL,aAAa,CAAC;EACpC7G,WAAWA,CAACa,IAAI,EAAEC,EAAE,EAAE;IAClB,KAAK,CAACD,IAAI,EAAEC,EAAE,CAAC;IACf,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZZ,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACS,IAAI,CAACiF,eAAe,CAAChF,EAAE,CAAC,CAAC;EACzD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOgG,IAAIA,CAACK,IAAI,EAAEC,IAAI,EAAE;IACpB,OAAOD,IAAI,CAACrB,eAAe,CAACsB,IAAI,CAAC,GAC3B,IAAIF,WAAW,CAACC,IAAI,EAAEC,IAAI,CAAC,GAC3B,IAAIF,WAAW,CAACE,IAAI,EAAED,IAAI,CAAC;EACrC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOjC,cAAcA,CAACmC,WAAW,EAAEtC,QAAQ,GAAG,KAAK,EAAE;IACjD,MAAMuC,OAAO,GAAGtD,MAAM,CAACkB,cAAc,CAACmC,WAAW,CAACpC,KAAK,CAAC,CAAC,EAAE1G,kBAAkB,CAAC,EAAEwG,QAAQ,CAAC;IACzF,IAAIsC,WAAW,CAAC5I,MAAM,GAAGD,wBAAwB,EAAE;MAC/C,OAAO,IAAI0I,WAAW,CAACI,OAAO,EAAEA,OAAO,CAAC;IAC5C;IACA,OAAOJ,WAAW,CAACJ,IAAI,CAACQ,OAAO,EAAEtD,MAAM,CAACkB,cAAc,CAACmC,WAAW,CAACpC,KAAK,CAAC1G,kBAAkB,GAAGR,oBAAoB,CAACU,MAAM,CAAC,EAAEsG,QAAQ,CAAC,CAAC;EAC1I;EACA,IAAIwC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC1G,IAAI,CAACkF,OAAO,CAAC,IAAI,CAACjF,EAAE,CAAC;EACrC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiF,OAAOA,CAACzC,OAAO,EAAE;IACb,OAAO,IAAI,CAACzC,IAAI,CAACkF,OAAO,CAACzC,OAAO,CAACzC,IAAI,CAAC,IAAI,IAAI,CAACC,EAAE,CAACiF,OAAO,CAACzC,OAAO,CAACxC,EAAE,CAAC;EACzE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIoF,QAAQA,CAAC/I,GAAG,EAAEC,GAAG,EAAE;IACf,OAAO,IAAI8J,WAAW,CAAC,IAAI,CAACrG,IAAI,CAACqF,QAAQ,CAAC/I,GAAG,EAAEC,GAAG,CAAC,EAAE,IAAI,CAAC0D,EAAE,CAACoF,QAAQ,CAAC/I,GAAG,EAAEC,GAAG,CAAC,CAAC;EACpF;EACA;AACJ;AACA;EACIoK,oBAAoBA,CAACjB,UAAU,EAAEkB,aAAa,EAAE;IAC5C,MAAM5G,IAAI,GAAG,IAAI,CAACA,IAAI,CAACyF,eAAe,CAACC,UAAU,EAAEkB,aAAa,CAAC;IACjE,MAAM3G,EAAE,GAAG,IAAI,CAACA,EAAE,CAACwF,eAAe,CAACC,UAAU,EAAEkB,aAAa,CAAC;IAC7D,OAAO,GAAG5G,IAAI,GAAG9C,oBAAoB,GAAG+C,EAAE,EAAE;EAChD;EACAa,QAAQA,CAAC4E,UAAU,GAAG,KAAK,EAAEkB,aAAa,GAAG,GAAG,EAAE;IAC9C,OAAO,IAAI,CAACD,oBAAoB,CAACjB,UAAU,EAAEkB,aAAa,CAAC;EAC/D;EACAC,OAAOA,CAAA,EAAG;IACN,MAAM;MAAE7G,IAAI;MAAEC;IAAG,CAAC,GAAG,IAAI;IACzB,MAAM6G,GAAG,GAAG,EAAE;IACd,KAAK,MAAM1D,GAAG,GAAGpD,IAAI,CAACiD,eAAe,CAAC,CAAC,EAAEG,GAAG,IAAInD,EAAE,CAACgD,eAAe,CAAC,CAAC,EAAEG,GAAG,CAAC2D,OAAO,CAAC3D,GAAG,CAACG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;MAClGuD,GAAG,CAACE,IAAI,CAAC7D,MAAM,CAACG,mBAAmB,CAACF,GAAG,CAAC,CAAC;IAC7C;IACA,OAAO0D,GAAG;EACd;AACJ;AAEA,MAAMG,aAAa,GAAG,IAAI9D,MAAM,CAACpG,QAAQ,EAAEF,SAAS,EAAED,OAAO,CAAC;AAC9D,MAAMsK,YAAY,GAAG,IAAI/D,MAAM,CAACnG,QAAQ,EAAEF,SAAS,EAAE,EAAE,CAAC;AACxD,MAAMqK,sBAAsB,GAAG,IAAIhE,MAAM,CAAClG,kBAAkB,EAAEH,SAAS,EAAE,EAAE,CAAC;;AAE5E;AACA;AACA;AACA;AACA,MAAMsK,OAAO,CAAC;EACVjI,WAAWA,CAACkI,KAAK,EAAEC,OAAO,EAAEC,OAAO,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAE;IAC7C,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZnI,SAAS,IACLC,OAAO,CAACC,MAAM;IACd;IACA;IACAE,MAAM,CAACC,SAAS,CAAC2H,KAAK,CAAC,IACnBnL,UAAU,CAACmL,KAAK,EAAE,CAAC,EAAEI,QAAQ,CAAC,IAC9BhI,MAAM,CAACC,SAAS,CAAC4H,OAAO,CAAC,IACzBpL,UAAU,CAACoL,OAAO,EAAE,CAAC,EAAEjK,eAAe,CAAC,IACvCoC,MAAM,CAACC,SAAS,CAAC6H,OAAO,CAAC,IACzBrL,UAAU,CAACqL,OAAO,EAAE,CAAC,EAAEnK,iBAAiB,CAAC,IACzCqC,MAAM,CAACC,SAAS,CAAC8H,EAAE,CAAC,IACpBtL,UAAU,CAACsL,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,6BAA6B,EAAEH,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,EAAE,CAAC;EAChG;EACA;AACJ;AACA;EACI,OAAOE,WAAWA,CAACL,KAAK,EAAEC,OAAO,EAAEC,OAAO,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAE;IACpD,OAAQ/H,MAAM,CAACC,SAAS,CAAC2H,KAAK,CAAC,IAC3BnL,UAAU,CAACmL,KAAK,EAAE,CAAC,EAAE/J,YAAY,CAAC,IAClCmC,MAAM,CAACC,SAAS,CAAC4H,OAAO,CAAC,IACzBpL,UAAU,CAACoL,OAAO,EAAE,CAAC,EAAEjK,eAAe,CAAC,IACvCoC,MAAM,CAACC,SAAS,CAAC6H,OAAO,CAAC,IACzBrL,UAAU,CAACqL,OAAO,EAAE,CAAC,EAAEnK,iBAAiB,CAAC,IACzCqC,MAAM,CAACC,SAAS,CAAC8H,EAAE,CAAC,IACpBtL,UAAU,CAACsL,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC;EAC/B;EACA;AACJ;AACA;EACI,OAAOG,OAAOA,CAAA,EAAG;IACb,OAAOP,OAAO,CAACQ,wBAAwB,CAACzG,IAAI,CAAC0G,GAAG,CAAC,CAAC,GAAGpK,mBAAmB,CAAC;EAC7E;EACA;AACJ;AACA;EACI,OAAOmE,YAAYA,CAAA,EAAG;IAClB,MAAMvF,IAAI,GAAG,IAAI8E,IAAI,CAAC,CAAC;IACvB,OAAOiG,OAAO,CAACQ,wBAAwB,CAAC,CAACzG,IAAI,CAAC0G,GAAG,CAAC,CAAC,GAAGxL,IAAI,CAACyL,iBAAiB,CAAC,CAAC,GAAGvK,sBAAsB,IACnGE,mBAAmB,CAAC;EAC5B;EACA;AACJ;AACA;EACI,OAAOmK,wBAAwBA,CAACG,YAAY,EAAE;IAC1C1I,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACE,MAAM,CAACC,SAAS,CAACqI,YAAY,CAAC,CAAC;IAC3D1I,SAAS,IACLC,OAAO,CAACC,MAAM,CAACrD,UAAU,CAAC6L,YAAY,EAAE,CAAC,EAAEtK,mBAAmB,CAAC,EAAE,8BAA8BA,mBAAmB,2BAA2B,CAAC;IAClJ,MAAM4J,KAAK,GAAGxH,IAAI,CAACkD,KAAK,CAACgF,YAAY,GAAGvK,oBAAoB,CAAC;IAC7D,MAAM8J,OAAO,GAAGzH,IAAI,CAACkD,KAAK,CAAEgF,YAAY,GAAGvK,oBAAoB,GAAID,sBAAsB,CAAC;IAC1F,MAAMgK,OAAO,GAAG1H,IAAI,CAACkD,KAAK,CAAGgF,YAAY,GAAGvK,oBAAoB,GAAID,sBAAsB,GAAI,IAAI,CAAC,IAAI,CAAC;IACxG,MAAMiK,EAAE,GAAG3H,IAAI,CAACkD,KAAK,CAAGgF,YAAY,GAAGvK,oBAAoB,GAAID,sBAAsB,GAAI,IAAI,CAAC,IAAI,CAAC;IACnG,OAAO,IAAI6J,OAAO,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,EAAE,CAAC;EACnD;EACA;AACJ;AACA;EACI,OAAOQ,UAAUA,CAACC,IAAI,EAAE;IACpB,MAAMZ,KAAK,GAAG,IAAI,CAACa,UAAU,CAACD,IAAI,CAAC;IACnC,MAAMX,OAAO,GAAG7H,MAAM,CAACwI,IAAI,CAAC7D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7C,MAAMmD,OAAO,GAAG9H,MAAM,CAACwI,IAAI,CAAC7D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7C,MAAMoD,EAAE,GAAG/H,MAAM,CAACwI,IAAI,CAAC7D,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC;IACzC,OAAO,IAAIgD,OAAO,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,EAAE,CAAC;EACnD;EACA;AACJ;AACA;AACA;EACI,OAAOlE,mBAAmBA,CAACjH,IAAI,EAAE;IAC7B,OAAO,IAAI+K,OAAO,CAAC/K,IAAI,CAAC8L,QAAQ,CAAC,CAAC,EAAE9L,IAAI,CAAC+L,UAAU,CAAC,CAAC,EAAE/L,IAAI,CAACgM,UAAU,CAAC,CAAC,EAAEhM,IAAI,CAACiM,eAAe,CAAC,CAAC,CAAC;EACrG;EACA,OAAOC,mBAAmBA,CAACN,IAAI,EAAE;IAC7B,OAAQ,OAAO,CAACO,IAAI,CAACP,IAAI,CAACQ,WAAW,CAAC,CAAC,CAACC,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI;EAC/E;EACA,OAAOR,UAAUA,CAACD,IAAI,EAAE;IACpB,MAAMZ,KAAK,GAAG5H,MAAM,CAACwI,IAAI,CAAC7D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,MAAMuE,QAAQ,GAAG,IAAI,CAACJ,mBAAmB,CAACN,IAAI,CAAC;IAC/C,IAAI,CAACU,QAAQ,EAAE;MACX,OAAOtB,KAAK;IAChB;IACA,IAAIA,KAAK,KAAK,EAAE,EAAE;MACd,OAAOsB,QAAQ,KAAK,IAAI,GAAG,CAAC,GAAG,EAAE;IACrC;IACA,OAAOA,QAAQ,KAAK,IAAI,GAAGtB,KAAK,GAAG,EAAE,GAAGA,KAAK;EACjD;EACA;AACJ;AACA;EACIuB,KAAKA,CAAC;IAAEvB,KAAK,GAAG,CAAC;IAAEC,OAAO,GAAG,CAAC;IAAEC,OAAO,GAAG,CAAC;IAAEC,EAAE,GAAG;EAAE,CAAC,EAAE;IACnD,MAAMqB,OAAO,GAAG,IAAI,CAACC,sBAAsB,CAAC,CAAC,GACzCzB,KAAK,GAAG7J,oBAAoB,GAC5B8J,OAAO,GAAG/J,sBAAsB,GAChCgK,OAAO,GAAGpK,sBAAsB,GAChCqK,EAAE;IACN,MAAMuB,YAAY,GAAGlJ,IAAI,CAACkD,KAAK,CAAC8F,OAAO,GAAG1L,sBAAsB,CAAC;IACjE,MAAM6L,YAAY,GAAGnJ,IAAI,CAACkD,KAAK,CAACgG,YAAY,GAAG3L,iBAAiB,CAAC;IACjE,MAAM6L,UAAU,GAAGpJ,IAAI,CAACkD,KAAK,CAACiG,YAAY,GAAG3L,eAAe,CAAC;IAC7D,OAAO,IAAI+J,OAAO,CAAC,IAAI,CAAC8B,gBAAgB,CAACD,UAAU,EAAE3L,YAAY,CAAC,EAAE,IAAI,CAAC4L,gBAAgB,CAACF,YAAY,EAAE3L,eAAe,CAAC,EAAE,IAAI,CAAC6L,gBAAgB,CAACH,YAAY,EAAE3L,iBAAiB,CAAC,EAAE,IAAI,CAAC8L,gBAAgB,CAACL,OAAO,EAAE1L,sBAAsB,CAAC,CAAC;EAC7O;EACA;AACJ;AACA;EACI2D,QAAQA,CAACqI,IAAI,EAAE;IACX,MAAMC,SAAS,GAAGD,IAAI,EAAEE,UAAU,CAAC,cAAc,CAAC,IAAK,CAACF,IAAI,IAAI,IAAI,CAAC3B,EAAE,GAAG,CAAE;IAC5E,MAAM8B,cAAc,GAAGF,SAAS,IAAID,IAAI,EAAEE,UAAU,CAAC,UAAU,CAAC,IAAK,CAACF,IAAI,IAAI,IAAI,CAAC5B,OAAO,GAAG,CAAE;IAC/F,MAAM;MAAEF,KAAK,GAAG,IAAI,CAACA,KAAK;MAAEsB,QAAQ,GAAG;IAAG,CAAC,GAAGQ,IAAI,EAAEI,QAAQ,CAAC,IAAI,CAAC,GAC5D,IAAI,CAACC,YAAY,CAAC,IAAI,CAACnC,KAAK,CAAC,GAC7B,CAAC,CAAC;IACR,MAAMoC,IAAI,GAAG,GAAG,IAAI,CAACC,UAAU,CAACrC,KAAK,CAAC,IAAI,IAAI,CAACqC,UAAU,CAAC,IAAI,CAACpC,OAAO,CAAC,EAAE;IACzE,MAAMqC,EAAE,GAAGL,cAAc,GAAG,IAAI,IAAI,CAACI,UAAU,CAAC,IAAI,CAACnC,OAAO,CAAC,EAAE,GAAG,EAAE;IACpE,MAAMqC,GAAG,GAAGR,SAAS,GAAG,IAAI,IAAI,CAACM,UAAU,CAAC,IAAI,CAAClC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE;IAC9D,MAAMqC,EAAE,GAAGlB,QAAQ,IAAI,GAAG3M,mBAAmB,GAAG2M,QAAQ,EAAE;IAC1D,OAAO,GAAGc,IAAI,GAAGE,EAAE,GAAGC,GAAG,GAAGC,EAAE,EAAE;EACpC;EACA9I,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC+H,sBAAsB,CAAC,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;EACI,CAAC9H,MAAM,CAACC,WAAW,EAAEC,IAAI,EAAE;IACvB,OAAOC,IAAI,CAACC,SAAS,CAACJ,MAAM,CAACC,WAAW,CAAC,CAACI,IAAI,CAAC,IAAI,EAAEH,IAAI,CAAC;EAC9D;EACA;AACJ;AACA;EACI4H,sBAAsBA,CAAA,EAAG;IACrB,OAAQ,IAAI,CAACzB,KAAK,GAAG7J,oBAAoB,GACrC,IAAI,CAAC8J,OAAO,GAAG/J,sBAAsB,GACrC,IAAI,CAACgK,OAAO,GAAG,IAAI,GACnB,IAAI,CAACC,EAAE;EACf;EACAkC,UAAUA,CAACzB,IAAI,EAAE6B,MAAM,GAAG,CAAC,EAAE;IACzB,OAAO1J,MAAM,CAAC6H,IAAI,CAAC,CAAC5H,QAAQ,CAACyJ,MAAM,EAAE,GAAG,CAAC;EAC7C;EACAN,YAAYA,CAACnC,KAAK,EAAE;IAChB,MAAMsB,QAAQ,GAAGtB,KAAK,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;IAC1C,IAAIA,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;MAC7B,OAAO;QAAEsB,QAAQ;QAAEtB,KAAK,EAAE;MAAG,CAAC;IAClC;IACA,OAAO;MAAEsB,QAAQ;MAAEtB,KAAK,EAAEA,KAAK,GAAG;IAAG,CAAC;EAC1C;EACA6B,gBAAgBA,CAACa,KAAK,EAAEC,OAAO,EAAE;IAC7B,OAAO,CAAED,KAAK,GAAGC,OAAO,GAAIA,OAAO,IAAIA,OAAO;EAClD;AACJ;;AAEA;AACA;AACA;;AAEA,SAAStM,kBAAkB,EAAEC,wBAAwB,EAAEjB,iBAAiB,EAAED,mBAAmB,EAAED,YAAY,EAAEc,YAAY,EAAEL,kBAAkB,EAAEH,SAAS,EAAEE,QAAQ,EAAES,mBAAmB,EAAED,oBAAoB,EAAED,sBAAsB,EAAEJ,sBAAsB,EAAEE,eAAe,EAAET,OAAO,EAAEC,SAAS,EAAEE,QAAQ,EAAEJ,cAAc,EAAEO,oBAAoB,EAAEE,iBAAiB,EAAE6J,aAAa,EAAEC,YAAY,EAAEC,sBAAsB,EAAEhE,MAAM,EAAEtF,YAAY,EAAEwI,WAAW,EAAE5B,sBAAsB,EAAElD,QAAQ,EAAElD,cAAc,EAAE2H,aAAa,EAAEoB,OAAO,EAAElI,OAAO,EAAE9C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}