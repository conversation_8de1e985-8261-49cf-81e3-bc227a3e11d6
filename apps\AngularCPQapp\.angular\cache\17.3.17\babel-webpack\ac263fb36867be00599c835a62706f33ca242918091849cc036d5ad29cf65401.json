{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, inject, TemplateRef, Directive, Input, Component, ChangeDetectionStrategy } from '@angular/core';\nimport { TuiPortalService, TuiPortals, tuiAsPortal } from '@taiga-ui/cdk/classes';\nclass TuiPopupService extends TuiPortalService {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiPopupService_BaseFactory;\n      return function TuiPopupService_Factory(t) {\n        return (ɵTuiPopupService_BaseFactory || (ɵTuiPopupService_BaseFactory = i0.ɵɵgetInheritedFactory(TuiPopupService)))(t || TuiPopupService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiPopupService,\n      factory: TuiPopupService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPopupService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass TuiPopup {\n  constructor() {\n    this.template = inject(TemplateRef);\n    this.service = inject(TuiPopupService);\n  }\n  set tuiPopup(show) {\n    this.viewRef?.destroy();\n    if (show) {\n      this.viewRef = this.service.addTemplate(this.template);\n    }\n  }\n  ngOnDestroy() {\n    this.viewRef?.destroy();\n  }\n  static {\n    this.ɵfac = function TuiPopup_Factory(t) {\n      return new (t || TuiPopup)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiPopup,\n      selectors: [[\"ng-template\", \"tuiPopup\", \"\"]],\n      inputs: {\n        tuiPopup: \"tuiPopup\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPopup, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-template[tuiPopup]'\n    }]\n  }], null, {\n    tuiPopup: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiPopups extends TuiPortals {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiPopups_BaseFactory;\n      return function TuiPopups_Factory(t) {\n        return (ɵTuiPopups_BaseFactory || (ɵTuiPopups_BaseFactory = i0.ɵɵgetInheritedFactory(TuiPopups)))(t || TuiPopups);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiPopups,\n      selectors: [[\"tui-popups\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsPortal(TuiPopupService)]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"viewContainer\", \"\"]],\n      template: function TuiPopups_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, null, 0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPopups, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-popups',\n      template: '<ng-container #viewContainer />',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAsPortal(TuiPopupService)]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPopup, TuiPopupService, TuiPopups };", "map": {"version": 3, "names": ["i0", "Injectable", "inject", "TemplateRef", "Directive", "Input", "Component", "ChangeDetectionStrategy", "TuiPortalService", "TuiPortals", "tui<PERSON><PERSON><PERSON><PERSON>", "TuiPopupService", "ɵfac", "ɵTuiPopupService_BaseFactory", "TuiPopupService_Factory", "t", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "template", "service", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "show", "viewRef", "destroy", "addTemplate", "ngOnDestroy", "TuiPopup_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "standalone", "selector", "TuiPopups", "ɵTuiPopups_BaseFactory", "TuiPopups_Factory", "ɵcmp", "ɵɵdefineComponent", "features", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TuiPopups_Template", "rf", "ctx", "ɵɵelementContainer", "encapsulation", "changeDetection", "OnPush", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-directives-popup.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, inject, TemplateRef, Directive, Input, Component, ChangeDetectionStrategy } from '@angular/core';\nimport { TuiPortalService, TuiPortals, tuiAsPortal } from '@taiga-ui/cdk/classes';\n\nclass TuiPopupService extends TuiPortalService {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPopupService, deps: null, target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPopupService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPopupService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\nclass TuiPopup {\n    constructor() {\n        this.template = inject(TemplateRef);\n        this.service = inject(TuiPopupService);\n    }\n    set tuiPopup(show) {\n        this.viewRef?.destroy();\n        if (show) {\n            this.viewRef = this.service.addTemplate(this.template);\n        }\n    }\n    ngOnDestroy() {\n        this.viewRef?.destroy();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPopup, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPopup, isStandalone: true, selector: \"ng-template[tuiPopup]\", inputs: { tuiPopup: \"tuiPopup\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPopup, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-template[tuiPopup]',\n                }]\n        }], propDecorators: { tuiPopup: [{\n                type: Input\n            }] } });\n\nclass TuiPopups extends TuiPortals {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPopups, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPopups, isStandalone: true, selector: \"tui-popups\", providers: [tuiAsPortal(TuiPopupService)], usesInheritance: true, ngImport: i0, template: '<ng-container #viewContainer />', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPopups, decorators: [{\n            type: Component,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-popups',\n                    template: '<ng-container #viewContainer />',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    providers: [tuiAsPortal(TuiPopupService)],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPopup, TuiPopupService, TuiPopups };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,uBAAuB,QAAQ,eAAe;AACrH,SAASC,gBAAgB,EAAEC,UAAU,EAAEC,WAAW,QAAQ,uBAAuB;AAEjF,MAAMC,eAAe,SAASH,gBAAgB,CAAC;EAC3C;IAAS,IAAI,CAACI,IAAI;MAAA,IAAAC,4BAAA;MAAA,gBAAAC,wBAAAC,CAAA;QAAA,QAAAF,4BAAA,KAAAA,4BAAA,GAA+Eb,EAAE,CAAAgB,qBAAA,CAAQL,eAAe,IAAAI,CAAA,IAAfJ,eAAe;MAAA;IAAA,IAAsD;EAAE;EAClL;IAAS,IAAI,CAACM,KAAK,kBAD8EjB,EAAE,CAAAkB,kBAAA;MAAAC,KAAA,EACYR,eAAe;MAAAS,OAAA,EAAfT,eAAe,CAAAC,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGtB,EAAE,CAAAuB,iBAAA,CAGXZ,eAAe,EAAc,CAAC;IAC9Ga,IAAI,EAAEvB,UAAU;IAChBwB,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMK,QAAQ,CAAC;EACXC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG1B,MAAM,CAACC,WAAW,CAAC;IACnC,IAAI,CAAC0B,OAAO,GAAG3B,MAAM,CAACS,eAAe,CAAC;EAC1C;EACA,IAAImB,QAAQA,CAACC,IAAI,EAAE;IACf,IAAI,CAACC,OAAO,EAAEC,OAAO,CAAC,CAAC;IACvB,IAAIF,IAAI,EAAE;MACN,IAAI,CAACC,OAAO,GAAG,IAAI,CAACH,OAAO,CAACK,WAAW,CAAC,IAAI,CAACN,QAAQ,CAAC;IAC1D;EACJ;EACAO,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,OAAO,EAAEC,OAAO,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAACrB,IAAI,YAAAwB,iBAAArB,CAAA;MAAA,YAAAA,CAAA,IAAyFW,QAAQ;IAAA,CAAmD;EAAE;EACxK;IAAS,IAAI,CAACW,IAAI,kBAzB+ErC,EAAE,CAAAsC,iBAAA;MAAAd,IAAA,EAyBJE,QAAQ;MAAAa,SAAA;MAAAC,MAAA;QAAAV,QAAA;MAAA;MAAAW,UAAA;IAAA,EAA0G;EAAE;AACvN;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KA3BqGtB,EAAE,CAAAuB,iBAAA,CA2BXG,QAAQ,EAAc,CAAC;IACvGF,IAAI,EAAEpB,SAAS;IACfqB,IAAI,EAAE,CAAC;MACCgB,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEZ,QAAQ,EAAE,CAAC;MACzBN,IAAI,EAAEnB;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsC,SAAS,SAASlC,UAAU,CAAC;EAC/B;IAAS,IAAI,CAACG,IAAI;MAAA,IAAAgC,sBAAA;MAAA,gBAAAC,kBAAA9B,CAAA;QAAA,QAAA6B,sBAAA,KAAAA,sBAAA,GAtC+E5C,EAAE,CAAAgB,qBAAA,CAsCQ2B,SAAS,IAAA5B,CAAA,IAAT4B,SAAS;MAAA;IAAA,IAAqD;EAAE;EAC3K;IAAS,IAAI,CAACG,IAAI,kBAvC+E9C,EAAE,CAAA+C,iBAAA;MAAAvB,IAAA,EAuCJmB,SAAS;MAAAJ,SAAA;MAAAE,UAAA;MAAAO,QAAA,GAvCPhD,EAAE,CAAAiD,kBAAA,CAuC8D,CAACvC,WAAW,CAACC,eAAe,CAAC,CAAC,GAvC9FX,EAAE,CAAAkD,0BAAA,EAAFlD,EAAE,CAAAmD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA1B,QAAA,WAAA2B,mBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFxD,EAAE,CAAA0D,kBAAA,WAuC4K,CAAC;QAAA;MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAwE;EAAE;AAC9V;AACA;EAAA,QAAAtC,SAAA,oBAAAA,SAAA,KAzCqGtB,EAAE,CAAAuB,iBAAA,CAyCXoB,SAAS,EAAc,CAAC;IACxGnB,IAAI,EAAElB,SAAS;IACfmB,IAAI,EAAE,CAAC;MACCgB,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,YAAY;MACtBd,QAAQ,EAAE,iCAAiC;MAC3CgC,eAAe,EAAErD,uBAAuB,CAACsD,MAAM;MAC/CC,SAAS,EAAE,CAACpD,WAAW,CAACC,eAAe,CAAC;IAC5C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASe,QAAQ,EAAEf,eAAe,EAAEgC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}