{"ast": null, "code": "import { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { inject, NgZone, PLATFORM_ID } from '@angular/core';\nimport { WA_WINDOW, WA_USER_AGENT, WA_NAVIGATOR } from '@ng-web-apis/common';\nimport { tuiTypedFromEvent, tuiZonefreeScheduler } from '@taiga-ui/cdk/observables';\nimport { tuiCreateTokenFromFactory, tuiGetDocumentOrShadowRoot, tuiIsNativeMouseFocusable, tuiGetActualTarget } from '@taiga-ui/cdk/utils';\nimport { BehaviorSubject, switchMap, timer, map, startWith, share, merge, filter, takeUntil, repeat, withLatestFrom, of, take, distinctUntilChanged, fromEvent, shareReplay } from 'rxjs';\nimport { ɵAnimationEngine } from '@angular/animations/browser';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { isIos } from '@ng-web-apis/platform';\nimport { TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { tuiCreateToken } from '@taiga-ui/cdk/utils/miscellaneous';\n\n/**\n * Element currently being removed by AnimationEngine\n */\nconst TUI_REMOVED_ELEMENT = tuiCreateTokenFromFactory(() => {\n  const stub = {\n    onRemovalComplete: () => {}\n  };\n  const element$ = new BehaviorSubject(null);\n  const engine = inject(ɵAnimationEngine, {\n    optional: true\n  }) || stub;\n  const {\n    onRemovalComplete = stub.onRemovalComplete\n  } = engine;\n  engine.onRemovalComplete = (element, context) => {\n    element$.next(element);\n    onRemovalComplete.call(engine, element, context);\n  };\n  return element$.pipe(switchMap(element => timer(0).pipe(map(() => null), startWith(element))), share());\n});\n\n// Checks if focusout event should be considered leaving active zone\nfunction isValidFocusout(target, removedElement = null) {\n  return (\n    // Not due to switching tabs/going to DevTools\n    tuiGetDocumentOrShadowRoot(target).activeElement !== target &&\n    // Not due to button/input becoming disabled or under disabled fieldset\n    !target.matches(':disabled') &&\n    // Not due to element being removed from DOM\n    !removedElement?.contains(target) &&\n    // Not due to scrollable element became non-scrollable\n    tuiIsNativeMouseFocusable(target)\n  );\n}\nfunction shadowRootActiveElement(root) {\n  return merge(tuiTypedFromEvent(root, 'focusin').pipe(map(({\n    target\n  }) => target)), tuiTypedFromEvent(root, 'focusout').pipe(filter(({\n    target,\n    relatedTarget\n  }) => !!relatedTarget && isValidFocusout(target)), map(({\n    relatedTarget\n  }) => relatedTarget)));\n}\n/**\n * Active element on the document for ActiveZone\n */\nconst TUI_ACTIVE_ELEMENT = tuiCreateTokenFromFactory(() => {\n  const removedElement$ = inject(TUI_REMOVED_ELEMENT);\n  const win = inject(WA_WINDOW);\n  const doc = inject(DOCUMENT);\n  const zone = inject(NgZone);\n  const focusout$ = tuiTypedFromEvent(win, 'focusout', {\n    capture: true\n  });\n  const focusin$ = tuiTypedFromEvent(win, 'focusin', {\n    capture: true\n  });\n  const blur$ = tuiTypedFromEvent(win, 'blur');\n  const mousedown$ = tuiTypedFromEvent(win, 'mousedown');\n  const mouseup$ = tuiTypedFromEvent(win, 'mouseup');\n  return merge(focusout$.pipe(takeUntil(mousedown$), repeat({\n    delay: () => mouseup$\n  }), withLatestFrom(removedElement$), filter(([event, removedElement]) => isValidFocusout(tuiGetActualTarget(event), removedElement)), map(([{\n    relatedTarget\n  }]) => relatedTarget)), blur$.pipe(map(() => doc.activeElement), filter(element => !!element?.matches('iframe'))), focusin$.pipe(switchMap(event => {\n    const target = tuiGetActualTarget(event);\n    const root = tuiGetDocumentOrShadowRoot(target) || doc;\n    return root === doc ? of(target) : shadowRootActiveElement(root).pipe(startWith(target));\n  })), mousedown$.pipe(switchMap(event => {\n    const actualTargetInCurrentTime = tuiGetActualTarget(event);\n    return !doc.activeElement || doc.activeElement === doc.body ? of(actualTargetInCurrentTime) : focusout$.pipe(take(1), map(\n    /**\n     * Do not use `map(() => tuiGetActualTarget(event))`\n     * because we have different result in runtime\n     */\n    () => actualTargetInCurrentTime), takeUntil(timer(0, tuiZonefreeScheduler(zone))));\n  }))).pipe(distinctUntilChanged(), share());\n});\nconst TUI_BASE_HREF = tuiCreateTokenFromFactory(() => inject(DOCUMENT).querySelector('base')?.href ?? '');\n\n// https://stackoverflow.com/a/11381730/2706426 http://detectmobilebrowsers.com/\nconst firstRegex = /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series([46])0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/;\nconst secondRegex = /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br([ev])w|bumb|bw-([nu])|c55\\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do([cp])o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly([-_])|g1 u|g560|gene|gf-5|g-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd-([mpt])|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c([- _agpst])|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac([ \\-/])|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja([tv])a|jbro|jemu|jigs|kddi|keji|kgt([ /])|klon|kpt |kwc-|kyo([ck])|le(no|xi)|lg( g|\\/([klu])|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t([- ov])|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30([02])|n50([025])|n7(0([01])|10)|ne(([cm])-|on|tf|wf|wg|wt)|nok([6i])|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan([adt])|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\\/|se(c([-01])|47|mc|nd|ri)|sgh-|shar|sie([-m])|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel([im])|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c([- ])|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/;\nconst TUI_IS_MOBILE = tuiCreateTokenFromFactory(() => firstRegex.test(inject(WA_USER_AGENT).toLowerCase()) || secondRegex.test(inject(WA_USER_AGENT).slice(0, 4).toLowerCase()));\nconst TUI_IS_IOS = tuiCreateTokenFromFactory(() => isIos(inject(WA_NAVIGATOR)));\nconst TUI_IS_ANDROID = tuiCreateTokenFromFactory(() => inject(TUI_IS_MOBILE) && !inject(TUI_IS_IOS));\nconst TUI_IS_WEBKIT = tuiCreateTokenFromFactory(() => !!inject(WA_WINDOW)?.webkitConvertPointFromNodeToPage);\nconst TUI_PLATFORM = tuiCreateTokenFromFactory(() => {\n  if (inject(TUI_IS_IOS)) {\n    return 'ios';\n  }\n  return inject(TUI_IS_ANDROID) ? 'android' : 'web';\n});\nconst TUI_IS_TOUCH = tuiCreateTokenFromFactory(() => {\n  const media = inject(WA_WINDOW).matchMedia('(pointer: coarse)');\n  return toSignal(fromEvent(media, 'change').pipe(map(() => media.matches)), {\n    initialValue: media.matches\n  });\n});\n/**\n * Detect if app is running under Cypress\n * {@link https://docs.cypress.io/faq/questions/using-cypress-faq#Is-there-any-way-to-detect-if-my-app-is-running-under-Cypress Cypress docs}\n */\nconst TUI_IS_CYPRESS = tuiCreateTokenFromFactory(() => !!inject(WA_WINDOW).Cypress);\n/**\n * Manually provide `true` when running tests in Playwright\n */\nconst TUI_IS_PLAYWRIGHT = tuiCreateTokenFromFactory(TUI_FALSE_HANDLER);\n/**\n * Detect if app is running under any of test frameworks\n */\nconst TUI_IS_E2E = tuiCreateTokenFromFactory(() => inject(TUI_IS_CYPRESS) || inject(TUI_IS_PLAYWRIGHT));\nconst TUI_FALLBACK_VALUE = tuiCreateToken(null);\nfunction tuiFallbackValueProvider(useValue) {\n  return {\n    provide: TUI_FALLBACK_VALUE,\n    useValue\n  };\n}\n\n/**\n * SSR safe default empty Range\n */\nconst TUI_RANGE = tuiCreateTokenFromFactory(() => isPlatformBrowser(inject(PLATFORM_ID)) ? new Range() : {});\nconst TUI_WINDOW_SIZE = tuiCreateTokenFromFactory(() => {\n  const w = inject(WA_WINDOW);\n  return tuiTypedFromEvent(w, 'resize').pipe(startWith(null), map(() => {\n    const width = Math.max(w.document.documentElement.clientWidth || 0, w.innerWidth || 0, w.visualViewport?.width || 0);\n    const height = Math.max(w.document.documentElement.clientHeight || 0, w.innerHeight || 0, w.visualViewport?.height || 0);\n    const rect = {\n      width,\n      height,\n      top: 0,\n      left: 0,\n      right: width,\n      bottom: height,\n      x: 0,\n      y: 0\n    };\n    return {\n      ...rect,\n      toJSON: () => JSON.stringify(rect)\n    };\n  }), shareReplay({\n    bufferSize: 1,\n    refCount: true\n  }));\n});\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_ACTIVE_ELEMENT, TUI_BASE_HREF, TUI_FALLBACK_VALUE, TUI_IS_ANDROID, TUI_IS_CYPRESS, TUI_IS_E2E, TUI_IS_IOS, TUI_IS_MOBILE, TUI_IS_PLAYWRIGHT, TUI_IS_TOUCH, TUI_IS_WEBKIT, TUI_PLATFORM, TUI_RANGE, TUI_REMOVED_ELEMENT, TUI_WINDOW_SIZE, tuiFallbackValueProvider };", "map": {"version": 3, "names": ["DOCUMENT", "isPlatformBrowser", "inject", "NgZone", "PLATFORM_ID", "WA_WINDOW", "WA_USER_AGENT", "WA_NAVIGATOR", "tuiTypedFromEvent", "tuiZonefreeScheduler", "tuiCreateTokenFromFactory", "tuiGetDocumentOrShadowRoot", "tuiIsNativeMouseFocusable", "tuiGetActualTarget", "BehaviorSubject", "switchMap", "timer", "map", "startWith", "share", "merge", "filter", "takeUntil", "repeat", "withLatestFrom", "of", "take", "distinctUntilChanged", "fromEvent", "shareReplay", "ɵAnimationEngine", "toSignal", "isIos", "TUI_FALSE_HANDLER", "tuiCreateToken", "TUI_REMOVED_ELEMENT", "stub", "onRemovalComplete", "element$", "engine", "optional", "element", "context", "next", "call", "pipe", "isValidFocusout", "target", "removedElement", "activeElement", "matches", "contains", "shadowRootActiveElement", "root", "relatedTarget", "TUI_ACTIVE_ELEMENT", "removedElement$", "win", "doc", "zone", "focusout$", "capture", "focusin$", "blur$", "mousedown$", "mouseup$", "delay", "event", "actualTargetInCurrentTime", "body", "TUI_BASE_HREF", "querySelector", "href", "firstRegex", "secondRegex", "TUI_IS_MOBILE", "test", "toLowerCase", "slice", "TUI_IS_IOS", "TUI_IS_ANDROID", "TUI_IS_WEBKIT", "webkitConvertPointFromNodeToPage", "TUI_PLATFORM", "TUI_IS_TOUCH", "media", "matchMedia", "initialValue", "TUI_IS_CYPRESS", "Cypress", "TUI_IS_PLAYWRIGHT", "TUI_IS_E2E", "TUI_FALLBACK_VALUE", "tuiFallback<PERSON><PERSON>ue<PERSON>", "useValue", "provide", "TUI_RANGE", "Range", "TUI_WINDOW_SIZE", "w", "width", "Math", "max", "document", "documentElement", "clientWidth", "innerWidth", "visualViewport", "height", "clientHeight", "innerHeight", "rect", "top", "left", "right", "bottom", "x", "y", "toJSON", "JSON", "stringify", "bufferSize", "refCount"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-tokens.mjs"], "sourcesContent": ["import { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { inject, NgZone, PLATFORM_ID } from '@angular/core';\nimport { WA_WINDOW, WA_USER_AGENT, WA_NAVIGATOR } from '@ng-web-apis/common';\nimport { tuiTypedFromEvent, tuiZonefreeScheduler } from '@taiga-ui/cdk/observables';\nimport { tuiCreateTokenFromFactory, tuiGetDocumentOrShadowRoot, tuiIsNativeMouseFocusable, tuiGetActualTarget } from '@taiga-ui/cdk/utils';\nimport { BehaviorSubject, switchMap, timer, map, startWith, share, merge, filter, takeUntil, repeat, withLatestFrom, of, take, distinctUntilChanged, fromEvent, shareReplay } from 'rxjs';\nimport { ɵAnimationEngine } from '@angular/animations/browser';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { isIos } from '@ng-web-apis/platform';\nimport { TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { tuiCreateToken } from '@taiga-ui/cdk/utils/miscellaneous';\n\n/**\n * Element currently being removed by AnimationEngine\n */\nconst TUI_REMOVED_ELEMENT = tuiCreateTokenFromFactory(() => {\n    const stub = { onRemovalComplete: () => { } };\n    const element$ = new BehaviorSubject(null);\n    const engine = inject(ɵAnimationEngine, { optional: true }) || stub;\n    const { onRemovalComplete = stub.onRemovalComplete } = engine;\n    engine.onRemovalComplete = (element, context) => {\n        element$.next(element);\n        onRemovalComplete.call(engine, element, context);\n    };\n    return element$.pipe(switchMap((element) => timer(0).pipe(map(() => null), startWith(element))), share());\n});\n\n// Checks if focusout event should be considered leaving active zone\nfunction isValidFocusout(target, removedElement = null) {\n    return (\n    // Not due to switching tabs/going to DevTools\n    tuiGetDocumentOrShadowRoot(target).activeElement !== target &&\n        // Not due to button/input becoming disabled or under disabled fieldset\n        !target.matches(':disabled') &&\n        // Not due to element being removed from DOM\n        !removedElement?.contains(target) &&\n        // Not due to scrollable element became non-scrollable\n        tuiIsNativeMouseFocusable(target));\n}\nfunction shadowRootActiveElement(root) {\n    return merge(tuiTypedFromEvent(root, 'focusin').pipe(map(({ target }) => target)), tuiTypedFromEvent(root, 'focusout').pipe(filter(({ target, relatedTarget }) => !!relatedTarget && isValidFocusout(target)), map(({ relatedTarget }) => relatedTarget)));\n}\n/**\n * Active element on the document for ActiveZone\n */\nconst TUI_ACTIVE_ELEMENT = tuiCreateTokenFromFactory(() => {\n    const removedElement$ = inject(TUI_REMOVED_ELEMENT);\n    const win = inject(WA_WINDOW);\n    const doc = inject(DOCUMENT);\n    const zone = inject(NgZone);\n    const focusout$ = tuiTypedFromEvent(win, 'focusout', { capture: true });\n    const focusin$ = tuiTypedFromEvent(win, 'focusin', { capture: true });\n    const blur$ = tuiTypedFromEvent(win, 'blur');\n    const mousedown$ = tuiTypedFromEvent(win, 'mousedown');\n    const mouseup$ = tuiTypedFromEvent(win, 'mouseup');\n    return merge(focusout$.pipe(takeUntil(mousedown$), repeat({ delay: () => mouseup$ }), withLatestFrom(removedElement$), filter(([event, removedElement]) => isValidFocusout(tuiGetActualTarget(event), removedElement)), map(([{ relatedTarget }]) => relatedTarget)), blur$.pipe(map(() => doc.activeElement), filter((element) => !!element?.matches('iframe'))), focusin$.pipe(switchMap((event) => {\n        const target = tuiGetActualTarget(event);\n        const root = tuiGetDocumentOrShadowRoot(target) || doc;\n        return root === doc\n            ? of(target)\n            : shadowRootActiveElement(root).pipe(startWith(target));\n    })), mousedown$.pipe(switchMap((event) => {\n        const actualTargetInCurrentTime = tuiGetActualTarget(event);\n        return !doc.activeElement || doc.activeElement === doc.body\n            ? of(actualTargetInCurrentTime)\n            : focusout$.pipe(take(1), map(\n            /**\n             * Do not use `map(() => tuiGetActualTarget(event))`\n             * because we have different result in runtime\n             */\n            () => actualTargetInCurrentTime), takeUntil(timer(0, tuiZonefreeScheduler(zone))));\n    }))).pipe(distinctUntilChanged(), share());\n});\n\nconst TUI_BASE_HREF = tuiCreateTokenFromFactory(() => inject(DOCUMENT).querySelector('base')?.href ?? '');\n\n// https://stackoverflow.com/a/11381730/2706426 http://detectmobilebrowsers.com/\nconst firstRegex = /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series([46])0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/;\nconst secondRegex = /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br([ev])w|bumb|bw-([nu])|c55\\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do([cp])o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly([-_])|g1 u|g560|gene|gf-5|g-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd-([mpt])|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c([- _agpst])|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac([ \\-/])|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja([tv])a|jbro|jemu|jigs|kddi|keji|kgt([ /])|klon|kpt |kwc-|kyo([ck])|le(no|xi)|lg( g|\\/([klu])|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t([- ov])|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30([02])|n50([025])|n7(0([01])|10)|ne(([cm])-|on|tf|wf|wg|wt)|nok([6i])|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan([adt])|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\\/|se(c([-01])|47|mc|nd|ri)|sgh-|shar|sie([-m])|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel([im])|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c([- ])|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/;\nconst TUI_IS_MOBILE = tuiCreateTokenFromFactory(() => firstRegex.test(inject(WA_USER_AGENT).toLowerCase()) ||\n    secondRegex.test(inject(WA_USER_AGENT).slice(0, 4).toLowerCase()));\nconst TUI_IS_IOS = tuiCreateTokenFromFactory(() => isIos(inject(WA_NAVIGATOR)));\nconst TUI_IS_ANDROID = tuiCreateTokenFromFactory(() => inject(TUI_IS_MOBILE) && !inject(TUI_IS_IOS));\nconst TUI_IS_WEBKIT = tuiCreateTokenFromFactory(() => !!inject(WA_WINDOW)?.webkitConvertPointFromNodeToPage);\nconst TUI_PLATFORM = tuiCreateTokenFromFactory(() => {\n    if (inject(TUI_IS_IOS)) {\n        return 'ios';\n    }\n    return inject(TUI_IS_ANDROID) ? 'android' : 'web';\n});\nconst TUI_IS_TOUCH = tuiCreateTokenFromFactory(() => {\n    const media = inject(WA_WINDOW).matchMedia('(pointer: coarse)');\n    return toSignal(fromEvent(media, 'change').pipe(map(() => media.matches)), {\n        initialValue: media.matches,\n    });\n});\n/**\n * Detect if app is running under Cypress\n * {@link https://docs.cypress.io/faq/questions/using-cypress-faq#Is-there-any-way-to-detect-if-my-app-is-running-under-Cypress Cypress docs}\n */\nconst TUI_IS_CYPRESS = tuiCreateTokenFromFactory(() => !!inject(WA_WINDOW).Cypress);\n/**\n * Manually provide `true` when running tests in Playwright\n */\nconst TUI_IS_PLAYWRIGHT = tuiCreateTokenFromFactory(TUI_FALSE_HANDLER);\n/**\n * Detect if app is running under any of test frameworks\n */\nconst TUI_IS_E2E = tuiCreateTokenFromFactory(() => inject(TUI_IS_CYPRESS) || inject(TUI_IS_PLAYWRIGHT));\n\nconst TUI_FALLBACK_VALUE = tuiCreateToken(null);\nfunction tuiFallbackValueProvider(useValue) {\n    return {\n        provide: TUI_FALLBACK_VALUE,\n        useValue,\n    };\n}\n\n/**\n * SSR safe default empty Range\n */\nconst TUI_RANGE = tuiCreateTokenFromFactory(() => isPlatformBrowser(inject(PLATFORM_ID)) ? new Range() : {});\n\nconst TUI_WINDOW_SIZE = tuiCreateTokenFromFactory(() => {\n    const w = inject(WA_WINDOW);\n    return tuiTypedFromEvent(w, 'resize').pipe(startWith(null), map(() => {\n        const width = Math.max(w.document.documentElement.clientWidth || 0, w.innerWidth || 0, w.visualViewport?.width || 0);\n        const height = Math.max(w.document.documentElement.clientHeight || 0, w.innerHeight || 0, w.visualViewport?.height || 0);\n        const rect = {\n            width,\n            height,\n            top: 0,\n            left: 0,\n            right: width,\n            bottom: height,\n            x: 0,\n            y: 0,\n        };\n        return {\n            ...rect,\n            toJSON: () => JSON.stringify(rect),\n        };\n    }), shareReplay({ bufferSize: 1, refCount: true }));\n});\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_ACTIVE_ELEMENT, TUI_BASE_HREF, TUI_FALLBACK_VALUE, TUI_IS_ANDROID, TUI_IS_CYPRESS, TUI_IS_E2E, TUI_IS_IOS, TUI_IS_MOBILE, TUI_IS_PLAYWRIGHT, TUI_IS_TOUCH, TUI_IS_WEBKIT, TUI_PLATFORM, TUI_RANGE, TUI_REMOVED_ELEMENT, TUI_WINDOW_SIZE, tuiFallbackValueProvider };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,iBAAiB,QAAQ,iBAAiB;AAC7D,SAASC,MAAM,EAAEC,MAAM,EAAEC,WAAW,QAAQ,eAAe;AAC3D,SAASC,SAAS,EAAEC,aAAa,EAAEC,YAAY,QAAQ,qBAAqB;AAC5E,SAASC,iBAAiB,EAAEC,oBAAoB,QAAQ,2BAA2B;AACnF,SAASC,yBAAyB,EAAEC,0BAA0B,EAAEC,yBAAyB,EAAEC,kBAAkB,QAAQ,qBAAqB;AAC1I,SAASC,eAAe,EAAEC,SAAS,EAAEC,KAAK,EAAEC,GAAG,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,cAAc,EAAEC,EAAE,EAAEC,IAAI,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,WAAW,QAAQ,MAAM;AACzL,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,cAAc,QAAQ,mCAAmC;;AAElE;AACA;AACA;AACA,MAAMC,mBAAmB,GAAGzB,yBAAyB,CAAC,MAAM;EACxD,MAAM0B,IAAI,GAAG;IAAEC,iBAAiB,EAAEA,CAAA,KAAM,CAAE;EAAE,CAAC;EAC7C,MAAMC,QAAQ,GAAG,IAAIxB,eAAe,CAAC,IAAI,CAAC;EAC1C,MAAMyB,MAAM,GAAGrC,MAAM,CAAC4B,gBAAgB,EAAE;IAAEU,QAAQ,EAAE;EAAK,CAAC,CAAC,IAAIJ,IAAI;EACnE,MAAM;IAAEC,iBAAiB,GAAGD,IAAI,CAACC;EAAkB,CAAC,GAAGE,MAAM;EAC7DA,MAAM,CAACF,iBAAiB,GAAG,CAACI,OAAO,EAAEC,OAAO,KAAK;IAC7CJ,QAAQ,CAACK,IAAI,CAACF,OAAO,CAAC;IACtBJ,iBAAiB,CAACO,IAAI,CAACL,MAAM,EAAEE,OAAO,EAAEC,OAAO,CAAC;EACpD,CAAC;EACD,OAAOJ,QAAQ,CAACO,IAAI,CAAC9B,SAAS,CAAE0B,OAAO,IAAKzB,KAAK,CAAC,CAAC,CAAC,CAAC6B,IAAI,CAAC5B,GAAG,CAAC,MAAM,IAAI,CAAC,EAAEC,SAAS,CAACuB,OAAO,CAAC,CAAC,CAAC,EAAEtB,KAAK,CAAC,CAAC,CAAC;AAC7G,CAAC,CAAC;;AAEF;AACA,SAAS2B,eAAeA,CAACC,MAAM,EAAEC,cAAc,GAAG,IAAI,EAAE;EACpD;IACA;IACArC,0BAA0B,CAACoC,MAAM,CAAC,CAACE,aAAa,KAAKF,MAAM;IACvD;IACA,CAACA,MAAM,CAACG,OAAO,CAAC,WAAW,CAAC;IAC5B;IACA,CAACF,cAAc,EAAEG,QAAQ,CAACJ,MAAM,CAAC;IACjC;IACAnC,yBAAyB,CAACmC,MAAM;EAAC;AACzC;AACA,SAASK,uBAAuBA,CAACC,IAAI,EAAE;EACnC,OAAOjC,KAAK,CAACZ,iBAAiB,CAAC6C,IAAI,EAAE,SAAS,CAAC,CAACR,IAAI,CAAC5B,GAAG,CAAC,CAAC;IAAE8B;EAAO,CAAC,KAAKA,MAAM,CAAC,CAAC,EAAEvC,iBAAiB,CAAC6C,IAAI,EAAE,UAAU,CAAC,CAACR,IAAI,CAACxB,MAAM,CAAC,CAAC;IAAE0B,MAAM;IAAEO;EAAc,CAAC,KAAK,CAAC,CAACA,aAAa,IAAIR,eAAe,CAACC,MAAM,CAAC,CAAC,EAAE9B,GAAG,CAAC,CAAC;IAAEqC;EAAc,CAAC,KAAKA,aAAa,CAAC,CAAC,CAAC;AAC9P;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG7C,yBAAyB,CAAC,MAAM;EACvD,MAAM8C,eAAe,GAAGtD,MAAM,CAACiC,mBAAmB,CAAC;EACnD,MAAMsB,GAAG,GAAGvD,MAAM,CAACG,SAAS,CAAC;EAC7B,MAAMqD,GAAG,GAAGxD,MAAM,CAACF,QAAQ,CAAC;EAC5B,MAAM2D,IAAI,GAAGzD,MAAM,CAACC,MAAM,CAAC;EAC3B,MAAMyD,SAAS,GAAGpD,iBAAiB,CAACiD,GAAG,EAAE,UAAU,EAAE;IAAEI,OAAO,EAAE;EAAK,CAAC,CAAC;EACvE,MAAMC,QAAQ,GAAGtD,iBAAiB,CAACiD,GAAG,EAAE,SAAS,EAAE;IAAEI,OAAO,EAAE;EAAK,CAAC,CAAC;EACrE,MAAME,KAAK,GAAGvD,iBAAiB,CAACiD,GAAG,EAAE,MAAM,CAAC;EAC5C,MAAMO,UAAU,GAAGxD,iBAAiB,CAACiD,GAAG,EAAE,WAAW,CAAC;EACtD,MAAMQ,QAAQ,GAAGzD,iBAAiB,CAACiD,GAAG,EAAE,SAAS,CAAC;EAClD,OAAOrC,KAAK,CAACwC,SAAS,CAACf,IAAI,CAACvB,SAAS,CAAC0C,UAAU,CAAC,EAAEzC,MAAM,CAAC;IAAE2C,KAAK,EAAEA,CAAA,KAAMD;EAAS,CAAC,CAAC,EAAEzC,cAAc,CAACgC,eAAe,CAAC,EAAEnC,MAAM,CAAC,CAAC,CAAC8C,KAAK,EAAEnB,cAAc,CAAC,KAAKF,eAAe,CAACjC,kBAAkB,CAACsD,KAAK,CAAC,EAAEnB,cAAc,CAAC,CAAC,EAAE/B,GAAG,CAAC,CAAC,CAAC;IAAEqC;EAAc,CAAC,CAAC,KAAKA,aAAa,CAAC,CAAC,EAAES,KAAK,CAAClB,IAAI,CAAC5B,GAAG,CAAC,MAAMyC,GAAG,CAACT,aAAa,CAAC,EAAE5B,MAAM,CAAEoB,OAAO,IAAK,CAAC,CAACA,OAAO,EAAES,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEY,QAAQ,CAACjB,IAAI,CAAC9B,SAAS,CAAEoD,KAAK,IAAK;IAClY,MAAMpB,MAAM,GAAGlC,kBAAkB,CAACsD,KAAK,CAAC;IACxC,MAAMd,IAAI,GAAG1C,0BAA0B,CAACoC,MAAM,CAAC,IAAIW,GAAG;IACtD,OAAOL,IAAI,KAAKK,GAAG,GACbjC,EAAE,CAACsB,MAAM,CAAC,GACVK,uBAAuB,CAACC,IAAI,CAAC,CAACR,IAAI,CAAC3B,SAAS,CAAC6B,MAAM,CAAC,CAAC;EAC/D,CAAC,CAAC,CAAC,EAAEiB,UAAU,CAACnB,IAAI,CAAC9B,SAAS,CAAEoD,KAAK,IAAK;IACtC,MAAMC,yBAAyB,GAAGvD,kBAAkB,CAACsD,KAAK,CAAC;IAC3D,OAAO,CAACT,GAAG,CAACT,aAAa,IAAIS,GAAG,CAACT,aAAa,KAAKS,GAAG,CAACW,IAAI,GACrD5C,EAAE,CAAC2C,yBAAyB,CAAC,GAC7BR,SAAS,CAACf,IAAI,CAACnB,IAAI,CAAC,CAAC,CAAC,EAAET,GAAG;IAC7B;AACZ;AACA;AACA;IACY,MAAMmD,yBAAyB,CAAC,EAAE9C,SAAS,CAACN,KAAK,CAAC,CAAC,EAAEP,oBAAoB,CAACkD,IAAI,CAAC,CAAC,CAAC,CAAC;EAC1F,CAAC,CAAC,CAAC,CAAC,CAACd,IAAI,CAAClB,oBAAoB,CAAC,CAAC,EAAER,KAAK,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC;AAEF,MAAMmD,aAAa,GAAG5D,yBAAyB,CAAC,MAAMR,MAAM,CAACF,QAAQ,CAAC,CAACuE,aAAa,CAAC,MAAM,CAAC,EAAEC,IAAI,IAAI,EAAE,CAAC;;AAEzG;AACA,MAAMC,UAAU,GAAG,0TAA0T;AAC7U,MAAMC,WAAW,GAAG,iiDAAiiD;AACrjD,MAAMC,aAAa,GAAGjE,yBAAyB,CAAC,MAAM+D,UAAU,CAACG,IAAI,CAAC1E,MAAM,CAACI,aAAa,CAAC,CAACuE,WAAW,CAAC,CAAC,CAAC,IACtGH,WAAW,CAACE,IAAI,CAAC1E,MAAM,CAACI,aAAa,CAAC,CAACwE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACD,WAAW,CAAC,CAAC,CAAC,CAAC;AACtE,MAAME,UAAU,GAAGrE,yBAAyB,CAAC,MAAMsB,KAAK,CAAC9B,MAAM,CAACK,YAAY,CAAC,CAAC,CAAC;AAC/E,MAAMyE,cAAc,GAAGtE,yBAAyB,CAAC,MAAMR,MAAM,CAACyE,aAAa,CAAC,IAAI,CAACzE,MAAM,CAAC6E,UAAU,CAAC,CAAC;AACpG,MAAME,aAAa,GAAGvE,yBAAyB,CAAC,MAAM,CAAC,CAACR,MAAM,CAACG,SAAS,CAAC,EAAE6E,gCAAgC,CAAC;AAC5G,MAAMC,YAAY,GAAGzE,yBAAyB,CAAC,MAAM;EACjD,IAAIR,MAAM,CAAC6E,UAAU,CAAC,EAAE;IACpB,OAAO,KAAK;EAChB;EACA,OAAO7E,MAAM,CAAC8E,cAAc,CAAC,GAAG,SAAS,GAAG,KAAK;AACrD,CAAC,CAAC;AACF,MAAMI,YAAY,GAAG1E,yBAAyB,CAAC,MAAM;EACjD,MAAM2E,KAAK,GAAGnF,MAAM,CAACG,SAAS,CAAC,CAACiF,UAAU,CAAC,mBAAmB,CAAC;EAC/D,OAAOvD,QAAQ,CAACH,SAAS,CAACyD,KAAK,EAAE,QAAQ,CAAC,CAACxC,IAAI,CAAC5B,GAAG,CAAC,MAAMoE,KAAK,CAACnC,OAAO,CAAC,CAAC,EAAE;IACvEqC,YAAY,EAAEF,KAAK,CAACnC;EACxB,CAAC,CAAC;AACN,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMsC,cAAc,GAAG9E,yBAAyB,CAAC,MAAM,CAAC,CAACR,MAAM,CAACG,SAAS,CAAC,CAACoF,OAAO,CAAC;AACnF;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGhF,yBAAyB,CAACuB,iBAAiB,CAAC;AACtE;AACA;AACA;AACA,MAAM0D,UAAU,GAAGjF,yBAAyB,CAAC,MAAMR,MAAM,CAACsF,cAAc,CAAC,IAAItF,MAAM,CAACwF,iBAAiB,CAAC,CAAC;AAEvG,MAAME,kBAAkB,GAAG1D,cAAc,CAAC,IAAI,CAAC;AAC/C,SAAS2D,wBAAwBA,CAACC,QAAQ,EAAE;EACxC,OAAO;IACHC,OAAO,EAAEH,kBAAkB;IAC3BE;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA,MAAME,SAAS,GAAGtF,yBAAyB,CAAC,MAAMT,iBAAiB,CAACC,MAAM,CAACE,WAAW,CAAC,CAAC,GAAG,IAAI6F,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAE5G,MAAMC,eAAe,GAAGxF,yBAAyB,CAAC,MAAM;EACpD,MAAMyF,CAAC,GAAGjG,MAAM,CAACG,SAAS,CAAC;EAC3B,OAAOG,iBAAiB,CAAC2F,CAAC,EAAE,QAAQ,CAAC,CAACtD,IAAI,CAAC3B,SAAS,CAAC,IAAI,CAAC,EAAED,GAAG,CAAC,MAAM;IAClE,MAAMmF,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACH,CAAC,CAACI,QAAQ,CAACC,eAAe,CAACC,WAAW,IAAI,CAAC,EAAEN,CAAC,CAACO,UAAU,IAAI,CAAC,EAAEP,CAAC,CAACQ,cAAc,EAAEP,KAAK,IAAI,CAAC,CAAC;IACpH,MAAMQ,MAAM,GAAGP,IAAI,CAACC,GAAG,CAACH,CAAC,CAACI,QAAQ,CAACC,eAAe,CAACK,YAAY,IAAI,CAAC,EAAEV,CAAC,CAACW,WAAW,IAAI,CAAC,EAAEX,CAAC,CAACQ,cAAc,EAAEC,MAAM,IAAI,CAAC,CAAC;IACxH,MAAMG,IAAI,GAAG;MACTX,KAAK;MACLQ,MAAM;MACNI,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAEd,KAAK;MACZe,MAAM,EAAEP,MAAM;MACdQ,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACP,CAAC;IACD,OAAO;MACH,GAAGN,IAAI;MACPO,MAAM,EAAEA,CAAA,KAAMC,IAAI,CAACC,SAAS,CAACT,IAAI;IACrC,CAAC;EACL,CAAC,CAAC,EAAElF,WAAW,CAAC;IAAE4F,UAAU,EAAE,CAAC;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC;;AAEF;AACA;AACA;;AAEA,SAASnE,kBAAkB,EAAEe,aAAa,EAAEsB,kBAAkB,EAAEZ,cAAc,EAAEQ,cAAc,EAAEG,UAAU,EAAEZ,UAAU,EAAEJ,aAAa,EAAEe,iBAAiB,EAAEN,YAAY,EAAEH,aAAa,EAAEE,YAAY,EAAEa,SAAS,EAAE7D,mBAAmB,EAAE+D,eAAe,EAAEL,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}