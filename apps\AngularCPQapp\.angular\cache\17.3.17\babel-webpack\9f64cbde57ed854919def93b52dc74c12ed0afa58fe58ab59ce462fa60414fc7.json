{"ast": null, "code": "import { AsyncPipe } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Injectable, Directive } from '@angular/core';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { TUI_CLOSE_WORD, TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { injectContext, PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { TuiPopoverDirective } from '@taiga-ui/cdk/directives/popover';\nimport { TuiPopoverService, tuiAsPopover } from '@taiga-ui/cdk/services';\nimport { TUI_DIALOGS } from '@taiga-ui/core/components/dialog';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nfunction TuiPdfViewerComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r1, \" \");\n  }\n}\nfunction TuiPdfViewerComponent_iframe_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"iframe\", 7);\n  }\n  if (rf & 2) {\n    const content_r2 = ctx.polymorpheusOutlet;\n    i0.ɵɵproperty(\"src\", content_r2, i0.ɵɵsanitizeResourceUrl);\n  }\n}\nclass TuiPdfViewerComponent {\n  constructor() {\n    this.closeWord$ = inject(TUI_CLOSE_WORD);\n    this.icons = inject(TUI_COMMON_ICONS);\n    this.context = injectContext();\n  }\n  onKeyDownEsc() {\n    this.context.$implicit.complete();\n  }\n  static {\n    this.ɵfac = function TuiPdfViewerComponent_Factory(t) {\n      return new (t || TuiPdfViewerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiPdfViewerComponent,\n      selectors: [[\"tui-pdf-viewer\"]],\n      hostBindings: function TuiPdfViewerComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.esc\", function TuiPdfViewerComponent_keydown_esc_HostBindingHandler() {\n            return ctx.onKeyDownEsc();\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([i1.TuiAnimated]), i0.ɵɵStandaloneFeature],\n      decls: 10,\n      vars: 11,\n      consts: [[1, \"t-header\"], [\"automation-id\", \"tui-pdf-viewer__label\", 1, \"t-title\"], [1, \"t-actions\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [\"appearance\", \"\", \"size\", \"s\", \"tuiIconButton\", \"\", \"type\", \"button\", 1, \"t-close\", 3, \"click\", \"iconStart\"], [1, \"t-content\"], [\"title\", \"pdf\", \"class\", \"t-iframe\", 3, \"src\", 4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [\"title\", \"pdf\", 1, \"t-iframe\", 3, \"src\"]],\n      template: function TuiPdfViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\", 0)(1, \"h2\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 2);\n          i0.ɵɵtemplate(4, TuiPdfViewerComponent_ng_container_4_Template, 2, 1, \"ng-container\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function TuiPdfViewerComponent_Template_button_click_5_listener() {\n            return ctx.context.$implicit.complete();\n          });\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"async\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"section\", 5);\n          i0.ɵɵtemplate(9, TuiPdfViewerComponent_iframe_9_Template, 1, 1, \"iframe\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.context.label, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.context.actions)(\"polymorpheusOutletContext\", ctx.context);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"border-radius\", 100, \"%\");\n          i0.ɵɵproperty(\"iconStart\", ctx.icons.close);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 9, ctx.closeWord$), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.context.content)(\"polymorpheusOutletContext\", ctx.context);\n        }\n      },\n      dependencies: [AsyncPipe, PolymorpheusOutlet, TuiButton],\n      styles: [\"[_nghost-%COMP%]{display:block;inline-size:100%;block-size:100%;box-sizing:border-box;color:#fff;background:#333639}.tui-enter[_nghost-%COMP%], .tui-leave[_nghost-%COMP%]{animation-name:tuiFade,tuiSlide}.t-header[_ngcontent-%COMP%]{display:flex;align-items:center;block-size:4rem;padding:0 1rem 0 1.5625rem;box-shadow:inset 0 -1px #535659}.t-title[_ngcontent-%COMP%]{margin:0;font:var(--tui-font-text-m);white-space:nowrap;text-overflow:ellipsis;padding-right:.3125rem;overflow:hidden}.t-actions[_ngcontent-%COMP%]{display:flex;margin-left:auto}.t-close[_ngcontent-%COMP%]{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-left:.75rem;color:#fff;background:#ffffff52}.t-close[_ngcontent-%COMP%]:hover{background:#fff6}.t-content[_ngcontent-%COMP%]{block-size:calc(100% - 4rem);overflow:hidden}.t-iframe[_ngcontent-%COMP%]{inline-size:100%;block-size:100%}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPdfViewerComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-pdf-viewer',\n      imports: [AsyncPipe, PolymorpheusOutlet, TuiButton],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      hostDirectives: [TuiAnimated],\n      host: {\n        '(document:keydown.esc)': 'onKeyDownEsc()'\n      },\n      template: \"<header class=\\\"t-header\\\">\\n    <h2\\n        automation-id=\\\"tui-pdf-viewer__label\\\"\\n        class=\\\"t-title\\\"\\n    >\\n        {{ context.label }}\\n    </h2>\\n    <div class=\\\"t-actions\\\">\\n        <ng-container *polymorpheusOutlet=\\\"context.actions as text; context: context\\\">\\n            {{ text }}\\n        </ng-container>\\n    </div>\\n    <button\\n        appearance=\\\"\\\"\\n        size=\\\"s\\\"\\n        tuiIconButton\\n        type=\\\"button\\\"\\n        class=\\\"t-close\\\"\\n        [iconStart]=\\\"icons.close\\\"\\n        [style.border-radius.%]=\\\"100\\\"\\n        (click)=\\\"context.$implicit.complete()\\\"\\n    >\\n        {{ closeWord$ | async }}\\n    </button>\\n</header>\\n<section class=\\\"t-content\\\">\\n    <iframe\\n        *polymorpheusOutlet=\\\"context.content as content; context: context\\\"\\n        title=\\\"pdf\\\"\\n        class=\\\"t-iframe\\\"\\n        [src]=\\\"content\\\"\\n    ></iframe>\\n</section>\\n\",\n      styles: [\":host{display:block;inline-size:100%;block-size:100%;box-sizing:border-box;color:#fff;background:#333639}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide}.t-header{display:flex;align-items:center;block-size:4rem;padding:0 1rem 0 1.5625rem;box-shadow:inset 0 -1px #535659}.t-title{margin:0;font:var(--tui-font-text-m);white-space:nowrap;text-overflow:ellipsis;padding-right:.3125rem;overflow:hidden}.t-actions{display:flex;margin-left:auto}.t-close{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-left:.75rem;color:#fff;background:#ffffff52}.t-close:hover{background:#fff6}.t-content{block-size:calc(100% - 4rem);overflow:hidden}.t-iframe{inline-size:100%;block-size:100%}\\n\"]\n    }]\n  }], null, null);\n})();\nconst TUI_PDF_VIEWER_DEFAULT_OPTIONS = {\n  label: '',\n  actions: '',\n  data: undefined\n};\n/**\n * Default parameters for PdfViewer component\n */\nconst TUI_PDF_VIEWER_OPTIONS = tuiCreateToken(TUI_PDF_VIEWER_DEFAULT_OPTIONS);\nfunction tuiPdfViewerOptionsProvider(options) {\n  return tuiProvideOptions(TUI_PDF_VIEWER_OPTIONS, options, TUI_PDF_VIEWER_DEFAULT_OPTIONS);\n}\nclass TuiPdfViewerService extends TuiPopoverService {\n  open(content, options = {}) {\n    return super.open(content, options);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiPdfViewerService_BaseFactory;\n      return function TuiPdfViewerService_Factory(t) {\n        return (ɵTuiPdfViewerService_BaseFactory || (ɵTuiPdfViewerService_BaseFactory = i0.ɵɵgetInheritedFactory(TuiPdfViewerService)))(t || TuiPdfViewerService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiPdfViewerService,\n      factory: () => (() => new TuiPdfViewerService(TUI_DIALOGS, TuiPdfViewerComponent, inject(TUI_PDF_VIEWER_OPTIONS)))(),\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPdfViewerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => new TuiPdfViewerService(TUI_DIALOGS, TuiPdfViewerComponent, inject(TUI_PDF_VIEWER_OPTIONS))\n    }]\n  }], null, null);\n})();\nclass TuiPdfViewerDirective extends TuiPopoverDirective {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiPdfViewerDirective_BaseFactory;\n      return function TuiPdfViewerDirective_Factory(t) {\n        return (ɵTuiPdfViewerDirective_BaseFactory || (ɵTuiPdfViewerDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiPdfViewerDirective)))(t || TuiPdfViewerDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiPdfViewerDirective,\n      selectors: [[\"ng-template\", \"tuiPdfViewer\", \"\"]],\n      inputs: {\n        options: [i0.ɵɵInputFlags.None, \"tuiPdfViewerOptions\", \"options\"],\n        open: [i0.ɵɵInputFlags.None, \"tuiPdfViewer\", \"open\"]\n      },\n      outputs: {\n        openChange: \"tuiPdfViewerChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsPopover(TuiPdfViewerService)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPdfViewerDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-template[tuiPdfViewer]',\n      inputs: ['options: tuiPdfViewerOptions', 'open: tuiPdfViewer'],\n      outputs: ['openChange: tuiPdfViewerChange'],\n      providers: [tuiAsPopover(TuiPdfViewerService)]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_PDF_VIEWER_DEFAULT_OPTIONS, TUI_PDF_VIEWER_OPTIONS, TuiPdfViewerComponent, TuiPdfViewerDirective, TuiPdfViewerService, tuiPdfViewerOptionsProvider };", "map": {"version": 3, "names": ["AsyncPipe", "i0", "inject", "Component", "ChangeDetectionStrategy", "Injectable", "Directive", "i1", "TuiAnimated", "TuiButton", "TUI_CLOSE_WORD", "TUI_COMMON_ICONS", "injectContext", "Polymorpheus<PERSON><PERSON>let", "TuiPopoverDirective", "TuiPopoverService", "tuiAsPopover", "TUI_DIALOGS", "tuiCreateToken", "tuiProvideOptions", "TuiPdfViewerComponent_ng_container_4_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "text_r1", "polymorpheusOutlet", "ɵɵadvance", "ɵɵtextInterpolate1", "TuiPdfViewerComponent_iframe_9_Template", "ɵɵelement", "content_r2", "ɵɵproperty", "ɵɵsanitizeResourceUrl", "TuiPdfViewerComponent", "constructor", "closeWord$", "icons", "context", "onKeyDownEsc", "$implicit", "complete", "ɵfac", "TuiPdfViewerComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostBindings", "TuiPdfViewerComponent_HostBindings", "ɵɵlistener", "TuiPdfViewerComponent_keydown_esc_HostBindingHandler", "ɵɵresolveDocument", "standalone", "features", "ɵɵHostDirectivesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiPdfViewerComponent_Template", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtemplate", "TuiPdfViewerComponent_Template_button_click_5_listener", "ɵɵpipe", "label", "actions", "ɵɵstyleProp", "close", "ɵɵpipeBind1", "content", "dependencies", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "hostDirectives", "host", "TUI_PDF_VIEWER_DEFAULT_OPTIONS", "data", "undefined", "TUI_PDF_VIEWER_OPTIONS", "tuiPdfViewerOptionsProvider", "options", "TuiPdfViewerService", "open", "ɵTuiPdfViewerService_BaseFactory", "TuiPdfViewerService_Factory", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "useFactory", "TuiPdfViewerDirective", "ɵTuiPdfViewerDirective_BaseFactory", "TuiPdfViewerDirective_Factory", "ɵdir", "ɵɵdefineDirective", "inputs", "ɵɵInputFlags", "None", "outputs", "openChange", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-pdf-viewer.mjs"], "sourcesContent": ["import { Async<PERSON>ipe } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Injectable, Directive } from '@angular/core';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { TUI_CLOSE_WORD, TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { injectContext, PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { TuiPopoverDirective } from '@taiga-ui/cdk/directives/popover';\nimport { TuiPopoverService, tuiAsPopover } from '@taiga-ui/cdk/services';\nimport { TUI_DIALOGS } from '@taiga-ui/core/components/dialog';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\n\nclass TuiPdfViewerComponent {\n    constructor() {\n        this.closeWord$ = inject(TUI_CLOSE_WORD);\n        this.icons = inject(TUI_COMMON_ICONS);\n        this.context = injectContext();\n    }\n    onKeyDownEsc() {\n        this.context.$implicit.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPdfViewerComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPdfViewerComponent, isStandalone: true, selector: \"tui-pdf-viewer\", host: { listeners: { \"document:keydown.esc\": \"onKeyDownEsc()\" } }, hostDirectives: [{ directive: i1.TuiAnimated }], ngImport: i0, template: \"<header class=\\\"t-header\\\">\\n    <h2\\n        automation-id=\\\"tui-pdf-viewer__label\\\"\\n        class=\\\"t-title\\\"\\n    >\\n        {{ context.label }}\\n    </h2>\\n    <div class=\\\"t-actions\\\">\\n        <ng-container *polymorpheusOutlet=\\\"context.actions as text; context: context\\\">\\n            {{ text }}\\n        </ng-container>\\n    </div>\\n    <button\\n        appearance=\\\"\\\"\\n        size=\\\"s\\\"\\n        tuiIconButton\\n        type=\\\"button\\\"\\n        class=\\\"t-close\\\"\\n        [iconStart]=\\\"icons.close\\\"\\n        [style.border-radius.%]=\\\"100\\\"\\n        (click)=\\\"context.$implicit.complete()\\\"\\n    >\\n        {{ closeWord$ | async }}\\n    </button>\\n</header>\\n<section class=\\\"t-content\\\">\\n    <iframe\\n        *polymorpheusOutlet=\\\"context.content as content; context: context\\\"\\n        title=\\\"pdf\\\"\\n        class=\\\"t-iframe\\\"\\n        [src]=\\\"content\\\"\\n    ></iframe>\\n</section>\\n\", styles: [\":host{display:block;inline-size:100%;block-size:100%;box-sizing:border-box;color:#fff;background:#333639}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide}.t-header{display:flex;align-items:center;block-size:4rem;padding:0 1rem 0 1.5625rem;box-shadow:inset 0 -1px #535659}.t-title{margin:0;font:var(--tui-font-text-m);white-space:nowrap;text-overflow:ellipsis;padding-right:.3125rem;overflow:hidden}.t-actions{display:flex;margin-left:auto}.t-close{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-left:.75rem;color:#fff;background:#ffffff52}.t-close:hover{background:#fff6}.t-content{block-size:calc(100% - 4rem);overflow:hidden}.t-iframe{inline-size:100%;block-size:100%}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPdfViewerComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-pdf-viewer', imports: [AsyncPipe, PolymorpheusOutlet, TuiButton], changeDetection: ChangeDetectionStrategy.OnPush, hostDirectives: [TuiAnimated], host: {\n                        '(document:keydown.esc)': 'onKeyDownEsc()',\n                    }, template: \"<header class=\\\"t-header\\\">\\n    <h2\\n        automation-id=\\\"tui-pdf-viewer__label\\\"\\n        class=\\\"t-title\\\"\\n    >\\n        {{ context.label }}\\n    </h2>\\n    <div class=\\\"t-actions\\\">\\n        <ng-container *polymorpheusOutlet=\\\"context.actions as text; context: context\\\">\\n            {{ text }}\\n        </ng-container>\\n    </div>\\n    <button\\n        appearance=\\\"\\\"\\n        size=\\\"s\\\"\\n        tuiIconButton\\n        type=\\\"button\\\"\\n        class=\\\"t-close\\\"\\n        [iconStart]=\\\"icons.close\\\"\\n        [style.border-radius.%]=\\\"100\\\"\\n        (click)=\\\"context.$implicit.complete()\\\"\\n    >\\n        {{ closeWord$ | async }}\\n    </button>\\n</header>\\n<section class=\\\"t-content\\\">\\n    <iframe\\n        *polymorpheusOutlet=\\\"context.content as content; context: context\\\"\\n        title=\\\"pdf\\\"\\n        class=\\\"t-iframe\\\"\\n        [src]=\\\"content\\\"\\n    ></iframe>\\n</section>\\n\", styles: [\":host{display:block;inline-size:100%;block-size:100%;box-sizing:border-box;color:#fff;background:#333639}:host.tui-enter,:host.tui-leave{animation-name:tuiFade,tuiSlide}.t-header{display:flex;align-items:center;block-size:4rem;padding:0 1rem 0 1.5625rem;box-shadow:inset 0 -1px #535659}.t-title{margin:0;font:var(--tui-font-text-m);white-space:nowrap;text-overflow:ellipsis;padding-right:.3125rem;overflow:hidden}.t-actions{display:flex;margin-left:auto}.t-close{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-left:.75rem;color:#fff;background:#ffffff52}.t-close:hover{background:#fff6}.t-content{block-size:calc(100% - 4rem);overflow:hidden}.t-iframe{inline-size:100%;block-size:100%}\\n\"] }]\n        }] });\n\nconst TUI_PDF_VIEWER_DEFAULT_OPTIONS = {\n    label: '',\n    actions: '',\n    data: undefined,\n};\n/**\n * Default parameters for PdfViewer component\n */\nconst TUI_PDF_VIEWER_OPTIONS = tuiCreateToken(TUI_PDF_VIEWER_DEFAULT_OPTIONS);\nfunction tuiPdfViewerOptionsProvider(options) {\n    return tuiProvideOptions(TUI_PDF_VIEWER_OPTIONS, options, TUI_PDF_VIEWER_DEFAULT_OPTIONS);\n}\n\nclass TuiPdfViewerService extends TuiPopoverService {\n    open(content, options = {}) {\n        return super.open(content, options);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPdfViewerService, deps: null, target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPdfViewerService, providedIn: 'root', useFactory: () => new TuiPdfViewerService(TUI_DIALOGS, TuiPdfViewerComponent, inject(TUI_PDF_VIEWER_OPTIONS)) }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPdfViewerService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                    useFactory: () => new TuiPdfViewerService(TUI_DIALOGS, TuiPdfViewerComponent, inject(TUI_PDF_VIEWER_OPTIONS)),\n                }]\n        }] });\n\nclass TuiPdfViewerDirective extends TuiPopoverDirective {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPdfViewerDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPdfViewerDirective, isStandalone: true, selector: \"ng-template[tuiPdfViewer]\", inputs: { options: [\"tuiPdfViewerOptions\", \"options\"], open: [\"tuiPdfViewer\", \"open\"] }, outputs: { openChange: \"tuiPdfViewerChange\" }, providers: [tuiAsPopover(TuiPdfViewerService)], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPdfViewerDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-template[tuiPdfViewer]',\n                    inputs: ['options: tuiPdfViewerOptions', 'open: tuiPdfViewer'],\n                    outputs: ['openChange: tuiPdfViewerChange'],\n                    providers: [tuiAsPopover(TuiPdfViewerService)],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_PDF_VIEWER_DEFAULT_OPTIONS, TUI_PDF_VIEWER_OPTIONS, TuiPdfViewerComponent, TuiPdfViewerDirective, TuiPdfViewerService, tuiPdfViewerOptionsProvider };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,UAAU,EAAEC,SAAS,QAAQ,eAAe;AACjG,OAAO,KAAKC,EAAE,MAAM,mCAAmC;AACvD,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,uBAAuB;AACxE,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,wBAAwB;AAC1E,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,wBAAwB;AACxE,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,mCAAmC;AAAC,SAAAC,8CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAWepB,EAAE,CAAAsB,uBAAA,EACue,CAAC;IAD1etB,EAAE,CAAAuB,MAAA,EACygB,CAAC;IAD5gBvB,EAAE,CAAAwB,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAAF1B,EAAE,CAAA2B,SAAA,CACygB,CAAC;IAD5gB3B,EAAE,CAAA4B,kBAAA,MAAAH,OAAA,KACygB,CAAC;EAAA;AAAA;AAAA,SAAAI,wCAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD5gBpB,EAAE,CAAA8B,SAAA,eACokC,CAAC;EAAA;EAAA,IAAAV,EAAA;IAAA,MAAAW,UAAA,GAAAV,GAAA,CAAAK,kBAAA;IADvkC1B,EAAE,CAAAgC,UAAA,QAAAD,UAAA,EAAF/B,EAAE,CAAAiC,qBACojC,CAAC;EAAA;AAAA;AAV5pC,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAGnC,MAAM,CAACQ,cAAc,CAAC;IACxC,IAAI,CAAC4B,KAAK,GAAGpC,MAAM,CAACS,gBAAgB,CAAC;IACrC,IAAI,CAAC4B,OAAO,GAAG3B,aAAa,CAAC,CAAC;EAClC;EACA4B,YAAYA,CAAA,EAAG;IACX,IAAI,CAACD,OAAO,CAACE,SAAS,CAACC,QAAQ,CAAC,CAAC;EACrC;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,8BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFV,qBAAqB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAACW,IAAI,kBAD+E7C,EAAE,CAAA8C,iBAAA;MAAAC,IAAA,EACJb,qBAAqB;MAAAc,SAAA;MAAAC,YAAA,WAAAC,mCAAA9B,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADnBpB,EAAE,CAAAmD,UAAA,yBAAAC,qDAAA;YAAA,OACJ/B,GAAA,CAAAkB,YAAA,CAAa,CAAC;UAAA,UADZvC,EAAE,CAAAqD,iBACgB,CAAC;QAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GADnBvD,EAAE,CAAAwD,uBAAA,EACoKlD,EAAE,CAACC,WAAW,IADpLP,EAAE,CAAAyD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAA1C,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFpB,EAAE,CAAA+D,cAAA,eAC0O,CAAC,WAA2F,CAAC;UADzU/D,EAAE,CAAAuB,MAAA,EACyW,CAAC;UAD5WvB,EAAE,CAAAgE,YAAA,CAC8W,CAAC;UADjXhE,EAAE,CAAA+D,cAAA,YAC6Y,CAAC;UADhZ/D,EAAE,CAAAiE,UAAA,IAAA9C,6CAAA,yBACue,CAAC;UAD1enB,EAAE,CAAAgE,YAAA,CACoiB,CAAC;UADviBhE,EAAE,CAAA+D,cAAA,eACgzB,CAAC;UADnzB/D,EAAE,CAAAmD,UAAA,mBAAAe,uDAAA;YAAA,OAC4wB7C,GAAA,CAAAiB,OAAA,CAAAE,SAAA,CAAAC,QAAA,CAA2B,CAAC;UAAA,CAAC,CAAC;UAD5yBzC,EAAE,CAAAuB,MAAA,EACw1B,CAAC;UAD31BvB,EAAE,CAAAmE,MAAA;UAAFnE,EAAE,CAAAgE,YAAA,CACi2B,CAAC,CAAU,CAAC;UAD/2BhE,EAAE,CAAA+D,cAAA,gBAC24B,CAAC;UAD94B/D,EAAE,CAAAiE,UAAA,IAAApC,uCAAA,mBAC2jC,CAAC;UAD9jC7B,EAAE,CAAAgE,YAAA,CACglC,CAAC;QAAA;QAAA,IAAA5C,EAAA;UADnlCpB,EAAE,CAAA2B,SAAA,EACyW,CAAC;UAD5W3B,EAAE,CAAA4B,kBAAA,MAAAP,GAAA,CAAAiB,OAAA,CAAA8B,KAAA,KACyW,CAAC;UAD5WpE,EAAE,CAAA2B,SAAA,EAC2c,CAAC;UAD9c3B,EAAE,CAAAgC,UAAA,uBAAAX,GAAA,CAAAiB,OAAA,CAAA+B,OAC2c,CAAC,8BAAAhD,GAAA,CAAAiB,OAAwB,CAAC;UADvetC,EAAE,CAAA2B,SAAA,CACuvB,CAAC;UAD1vB3B,EAAE,CAAAsE,WAAA,0BACuvB,CAAC;UAD1vBtE,EAAE,CAAAgC,UAAA,cAAAX,GAAA,CAAAgB,KAAA,CAAAkC,KAC8sB,CAAC;UADjtBvE,EAAE,CAAA2B,SAAA,CACw1B,CAAC;UAD31B3B,EAAE,CAAA4B,kBAAA,MAAF5B,EAAE,CAAAwE,WAAA,OAAAnD,GAAA,CAAAe,UAAA,MACw1B,CAAC;UAD31BpC,EAAE,CAAA2B,SAAA,EACw8B,CAAC;UAD38B3B,EAAE,CAAAgC,UAAA,uBAAAX,GAAA,CAAAiB,OAAA,CAAAmC,OACw8B,CAAC,8BAAApD,GAAA,CAAAiB,OAA2B,CAAC;QAAA;MAAA;MAAAoC,YAAA,GAA85B3E,SAAS,EAA8Ca,kBAAkB,EAA8HJ,SAAS;MAAAmE,MAAA;MAAAC,eAAA;IAAA,EAA+J;EAAE;AAC31E;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG7E,EAAE,CAAA8E,iBAAA,CAGX5C,qBAAqB,EAAc,CAAC;IACpHa,IAAI,EAAE7C,SAAS;IACf6E,IAAI,EAAE,CAAC;MAAEzB,UAAU,EAAE,IAAI;MAAE0B,QAAQ,EAAE,gBAAgB;MAAEC,OAAO,EAAE,CAAClF,SAAS,EAAEa,kBAAkB,EAAEJ,SAAS,CAAC;MAAEoE,eAAe,EAAEzE,uBAAuB,CAAC+E,MAAM;MAAEC,cAAc,EAAE,CAAC5E,WAAW,CAAC;MAAE6E,IAAI,EAAE;QACtL,wBAAwB,EAAE;MAC9B,CAAC;MAAEvB,QAAQ,EAAE,q4BAAq4B;MAAEc,MAAM,EAAE,CAAC,4vBAA4vB;IAAE,CAAC;EACxqD,CAAC,CAAC;AAAA;AAEV,MAAMU,8BAA8B,GAAG;EACnCjB,KAAK,EAAE,EAAE;EACTC,OAAO,EAAE,EAAE;EACXiB,IAAI,EAAEC;AACV,CAAC;AACD;AACA;AACA;AACA,MAAMC,sBAAsB,GAAGvE,cAAc,CAACoE,8BAA8B,CAAC;AAC7E,SAASI,2BAA2BA,CAACC,OAAO,EAAE;EAC1C,OAAOxE,iBAAiB,CAACsE,sBAAsB,EAAEE,OAAO,EAAEL,8BAA8B,CAAC;AAC7F;AAEA,MAAMM,mBAAmB,SAAS7E,iBAAiB,CAAC;EAChD8E,IAAIA,CAACnB,OAAO,EAAEiB,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,OAAO,KAAK,CAACE,IAAI,CAACnB,OAAO,EAAEiB,OAAO,CAAC;EACvC;EACA;IAAS,IAAI,CAAChD,IAAI;MAAA,IAAAmD,gCAAA;MAAA,gBAAAC,4BAAAlD,CAAA;QAAA,QAAAiD,gCAAA,KAAAA,gCAAA,GA3B+E7F,EAAE,CAAA+F,qBAAA,CA2BQJ,mBAAmB,IAAA/C,CAAA,IAAnB+C,mBAAmB;MAAA;IAAA,IAAsD;EAAE;EACtL;IAAS,IAAI,CAACK,KAAK,kBA5B8EhG,EAAE,CAAAiG,kBAAA;MAAAC,KAAA,EA4BYP,mBAAmB;MAAAQ,OAAA,EAAAA,CAAA,MAAkC,MAAM,IAAIR,mBAAmB,CAAC3E,WAAW,EAAEkB,qBAAqB,EAAEjC,MAAM,CAACuF,sBAAsB,CAAC,CAAC;MAAAY,UAAA,EAArH;IAAM,EAAkH;EAAE;AAC9Q;AACA;EAAA,QAAAvB,SAAA,oBAAAA,SAAA,KA9BqG7E,EAAE,CAAA8E,iBAAA,CA8BXa,mBAAmB,EAAc,CAAC;IAClH5C,IAAI,EAAE3C,UAAU;IAChB2E,IAAI,EAAE,CAAC;MACCqB,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAEA,CAAA,KAAM,IAAIV,mBAAmB,CAAC3E,WAAW,EAAEkB,qBAAqB,EAAEjC,MAAM,CAACuF,sBAAsB,CAAC;IAChH,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMc,qBAAqB,SAASzF,mBAAmB,CAAC;EACpD;IAAS,IAAI,CAAC6B,IAAI;MAAA,IAAA6D,kCAAA;MAAA,gBAAAC,8BAAA5D,CAAA;QAAA,QAAA2D,kCAAA,KAAAA,kCAAA,GAvC+EvG,EAAE,CAAA+F,qBAAA,CAuCQO,qBAAqB,IAAA1D,CAAA,IAArB0D,qBAAqB;MAAA;IAAA,IAAqD;EAAE;EACvL;IAAS,IAAI,CAACG,IAAI,kBAxC+EzG,EAAE,CAAA0G,iBAAA;MAAA3D,IAAA,EAwCJuD,qBAAqB;MAAAtD,SAAA;MAAA2D,MAAA;QAAAjB,OAAA,GAxCnB1F,EAAE,CAAA4G,YAAA,CAAAC,IAAA;QAAAjB,IAAA,GAAF5F,EAAE,CAAA4G,YAAA,CAAAC,IAAA;MAAA;MAAAC,OAAA;QAAAC,UAAA;MAAA;MAAAzD,UAAA;MAAAC,QAAA,GAAFvD,EAAE,CAAAgH,kBAAA,CAwCiO,CAACjG,YAAY,CAAC4E,mBAAmB,CAAC,CAAC,GAxCtQ3F,EAAE,CAAAiH,0BAAA;IAAA,EAwC4S;EAAE;AACrZ;AACA;EAAA,QAAApC,SAAA,oBAAAA,SAAA,KA1CqG7E,EAAE,CAAA8E,iBAAA,CA0CXwB,qBAAqB,EAAc,CAAC;IACpHvD,IAAI,EAAE1C,SAAS;IACf0E,IAAI,EAAE,CAAC;MACCzB,UAAU,EAAE,IAAI;MAChB0B,QAAQ,EAAE,2BAA2B;MACrC2B,MAAM,EAAE,CAAC,8BAA8B,EAAE,oBAAoB,CAAC;MAC9DG,OAAO,EAAE,CAAC,gCAAgC,CAAC;MAC3CI,SAAS,EAAE,CAACnG,YAAY,CAAC4E,mBAAmB,CAAC;IACjD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASN,8BAA8B,EAAEG,sBAAsB,EAAEtD,qBAAqB,EAAEoE,qBAAqB,EAAEX,mBAAmB,EAAEF,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}