{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Directive } from '@angular/core';\nimport { tuiIfMap } from '@taiga-ui/cdk/observables';\nimport { TuiPopoverService } from '@taiga-ui/cdk/services';\nimport { PolymorpheusTemplate } from '@taiga-ui/polymorpheus';\nimport { Subject, distinctUntilChanged, ignoreElements, endWith, share } from 'rxjs';\nclass TuiPopoverDirective extends PolymorpheusTemplate {\n  constructor() {\n    super(...arguments);\n    this.service = inject(TuiPopoverService);\n    this.open$ = new Subject();\n    this.options = {};\n    this.open = false;\n    this.openChange = this.open$.pipe(distinctUntilChanged(), tuiIfMap(() => this.service.open(this, this.options).pipe(ignoreElements(), endWith(false))), share());\n  }\n  ngOnChanges() {\n    this.open$.next(this.open);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiPopoverDirective_BaseFactory;\n      return function TuiPopoverDirective_Factory(t) {\n        return (ɵTuiPopoverDirective_BaseFactory || (ɵTuiPopoverDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiPopoverDirective)))(t || TuiPopoverDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiPopoverDirective,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPopoverDirective, [{\n    type: Directive\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPopoverDirective };", "map": {"version": 3, "names": ["i0", "inject", "Directive", "tuiIfMap", "TuiPopoverService", "PolymorpheusTemplate", "Subject", "distinctUntilChanged", "ignoreElements", "endWith", "share", "TuiPopoverDirective", "constructor", "arguments", "service", "open$", "options", "open", "openChange", "pipe", "ngOnChanges", "next", "ɵfac", "ɵTuiPopoverDirective_BaseFactory", "TuiPopoverDirective_Factory", "t", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "type", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-popover.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Directive } from '@angular/core';\nimport { tuiIfMap } from '@taiga-ui/cdk/observables';\nimport { TuiPopoverService } from '@taiga-ui/cdk/services';\nimport { PolymorpheusTemplate } from '@taiga-ui/polymorpheus';\nimport { Subject, distinctUntilChanged, ignoreElements, endWith, share } from 'rxjs';\n\nclass TuiPopoverDirective extends PolymorpheusTemplate {\n    constructor() {\n        super(...arguments);\n        this.service = inject((TuiPopoverService));\n        this.open$ = new Subject();\n        this.options = {};\n        this.open = false;\n        this.openChange = this.open$.pipe(distinctUntilChanged(), tuiIfMap(() => this.service.open(this, this.options).pipe(ignoreElements(), endWith(false))), share());\n    }\n    ngOnChanges() {\n        this.open$.next(this.open);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPopoverDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPopoverDirective, usesInheritance: true, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPopoverDirective, decorators: [{\n            type: Directive\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPopoverDirective };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACjD,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,OAAO,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AAEpF,MAAMC,mBAAmB,SAASN,oBAAoB,CAAC;EACnDO,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,OAAO,GAAGb,MAAM,CAAEG,iBAAkB,CAAC;IAC1C,IAAI,CAACW,KAAK,GAAG,IAAIT,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACU,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACH,KAAK,CAACI,IAAI,CAACZ,oBAAoB,CAAC,CAAC,EAAEJ,QAAQ,CAAC,MAAM,IAAI,CAACW,OAAO,CAACG,IAAI,CAAC,IAAI,EAAE,IAAI,CAACD,OAAO,CAAC,CAACG,IAAI,CAACX,cAAc,CAAC,CAAC,EAAEC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC;EACpK;EACAU,WAAWA,CAAA,EAAG;IACV,IAAI,CAACL,KAAK,CAACM,IAAI,CAAC,IAAI,CAACJ,IAAI,CAAC;EAC9B;EACA;IAAS,IAAI,CAACK,IAAI;MAAA,IAAAC,gCAAA;MAAA,gBAAAC,4BAAAC,CAAA;QAAA,QAAAF,gCAAA,KAAAA,gCAAA,GAA+EvB,EAAE,CAAA0B,qBAAA,CAAQf,mBAAmB,IAAAc,CAAA,IAAnBd,mBAAmB;MAAA;IAAA,IAAqD;EAAE;EACrL;IAAS,IAAI,CAACgB,IAAI,kBAD+E3B,EAAE,CAAA4B,iBAAA;MAAAC,IAAA,EACJlB,mBAAmB;MAAAmB,QAAA,GADjB9B,EAAE,CAAA+B,0BAAA,EAAF/B,EAAE,CAAAgC,oBAAA;IAAA,EAC4E;EAAE;AACrL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGjC,EAAE,CAAAkC,iBAAA,CAGXvB,mBAAmB,EAAc,CAAC;IAClHkB,IAAI,EAAE3B;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASS,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}