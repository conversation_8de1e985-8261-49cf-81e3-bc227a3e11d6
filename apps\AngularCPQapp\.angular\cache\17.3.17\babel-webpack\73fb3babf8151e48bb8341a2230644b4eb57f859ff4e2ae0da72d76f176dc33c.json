{"ast": null, "code": "import { isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, Component, ChangeDetectionStrategy, Input } from '@angular/core';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { TuiRectAccessor, tuiAsRectAccessor } from '@taiga-ui/core/classes';\nclass TuiPulse extends TuiRectAccessor {\n  constructor() {\n    super(...arguments);\n    this.isBrowser = isPlatformBrowser(inject(PLATFORM_ID));\n    this.el = tuiInjectElement();\n    this.playing = true;\n    this.type = 'hint';\n  }\n  getClientRect() {\n    const rect = this.el.getBoundingClientRect();\n    return this.isBrowser ? new DOMRect(rect.x - 4, rect.y - 4, rect.width + 8, rect.height + 8) : rect;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiPulse_BaseFactory;\n      return function TuiPulse_Factory(t) {\n        return (ɵTuiPulse_BaseFactory || (ɵTuiPulse_BaseFactory = i0.ɵɵgetInheritedFactory(TuiPulse)))(t || TuiPulse);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiPulse,\n      selectors: [[\"tui-pulse\"]],\n      hostVars: 2,\n      hostBindings: function TuiPulse_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_playing\", ctx.playing);\n        }\n      },\n      inputs: {\n        playing: \"playing\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsRectAccessor(TuiPulse)]), i0.ɵɵHostDirectivesFeature([i1.TuiAnimated]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiPulse_Template(rf, ctx) {},\n      styles: [\"@keyframes _ngcontent-%COMP%_tuiPulse{to{opacity:0;transform:scale(2.5)}}[_nghost-%COMP%]{position:relative;color:var(--tui-background-accent-1)}.tui-enter[_nghost-%COMP%], .tui-leave[_nghost-%COMP%]{animation-name:tuiScale}[_nghost-%COMP%]:before, [_nghost-%COMP%]:after{transition-property:box-shadow;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;inline-size:.5rem;block-size:.5rem;border-radius:100%;margin:-.25rem;background:currentColor}[_nghost-%COMP%]:before{opacity:1;transform:scale(0)}._playing[_nghost-%COMP%]:before{animation:_ngcontent-%COMP%_tuiPulse 1s 1s ease-in-out infinite}._playing[_nghost-%COMP%]:after{box-shadow:0 0 .5rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPulse, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-pulse',\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAsRectAccessor(TuiPulse)],\n      hostDirectives: [TuiAnimated],\n      host: {\n        '[class._playing]': 'playing'\n      },\n      styles: [\"@keyframes tuiPulse{to{opacity:0;transform:scale(2.5)}}:host{position:relative;color:var(--tui-background-accent-1)}:host.tui-enter,:host.tui-leave{animation-name:tuiScale}:host:before,:host:after{transition-property:box-shadow;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;inline-size:.5rem;block-size:.5rem;border-radius:100%;margin:-.25rem;background:currentColor}:host:before{opacity:1;transform:scale(0)}:host._playing:before{animation:tuiPulse 1s 1s ease-in-out infinite}:host._playing:after{box-shadow:0 0 .5rem}\\n\"]\n    }]\n  }], null, {\n    playing: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPulse };", "map": {"version": 3, "names": ["isPlatformBrowser", "i0", "inject", "PLATFORM_ID", "Component", "ChangeDetectionStrategy", "Input", "i1", "TuiAnimated", "tuiInjectElement", "TuiRectAccessor", "tuiAsRectAccessor", "TuiPulse", "constructor", "arguments", "<PERSON><PERSON><PERSON><PERSON>", "el", "playing", "type", "getClientRect", "rect", "getBoundingClientRect", "DOMRect", "x", "y", "width", "height", "ɵfac", "ɵTuiPulse_BaseFactory", "TuiPulse_Factory", "t", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostVars", "hostBindings", "TuiPulse_HostBindings", "rf", "ctx", "ɵɵclassProp", "inputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiPulse_Template", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "providers", "hostDirectives", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-pulse.mjs"], "sourcesContent": ["import { isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, Component, ChangeDetectionStrategy, Input } from '@angular/core';\nimport * as i1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { TuiRectAccessor, tuiAsRectAccessor } from '@taiga-ui/core/classes';\n\nclass TuiPulse extends TuiRectAccessor {\n    constructor() {\n        super(...arguments);\n        this.isBrowser = isPlatformBrowser(inject(PLATFORM_ID));\n        this.el = tuiInjectElement();\n        this.playing = true;\n        this.type = 'hint';\n    }\n    getClientRect() {\n        const rect = this.el.getBoundingClientRect();\n        return this.isBrowser\n            ? new DOMRect(rect.x - 4, rect.y - 4, rect.width + 8, rect.height + 8)\n            : rect;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPulse, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPulse, isStandalone: true, selector: \"tui-pulse\", inputs: { playing: \"playing\" }, host: { properties: { \"class._playing\": \"playing\" } }, providers: [tuiAsRectAccessor(TuiPulse)], usesInheritance: true, hostDirectives: [{ directive: i1.TuiAnimated }], ngImport: i0, template: '', isInline: true, styles: [\"@keyframes tuiPulse{to{opacity:0;transform:scale(2.5)}}:host{position:relative;color:var(--tui-background-accent-1)}:host.tui-enter,:host.tui-leave{animation-name:tuiScale}:host:before,:host:after{transition-property:box-shadow;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;inline-size:.5rem;block-size:.5rem;border-radius:100%;margin:-.25rem;background:currentColor}:host:before{opacity:1;transform:scale(0)}:host._playing:before{animation:tuiPulse 1s 1s ease-in-out infinite}:host._playing:after{box-shadow:0 0 .5rem}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPulse, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-pulse', template: '', changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiAsRectAccessor(TuiPulse)], hostDirectives: [TuiAnimated], host: {\n                        '[class._playing]': 'playing',\n                    }, styles: [\"@keyframes tuiPulse{to{opacity:0;transform:scale(2.5)}}:host{position:relative;color:var(--tui-background-accent-1)}:host.tui-enter,:host.tui-leave{animation-name:tuiScale}:host:before,:host:after{transition-property:box-shadow;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;inline-size:.5rem;block-size:.5rem;border-radius:100%;margin:-.25rem;background:currentColor}:host:before{opacity:1;transform:scale(0)}:host._playing:before{animation:tuiPulse 1s 1s ease-in-out infinite}:host._playing:after{box-shadow:0 0 .5rem}\\n\"] }]\n        }], propDecorators: { playing: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPulse };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,iBAAiB;AACnD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,QAAQ,eAAe;AAC9F,OAAO,KAAKC,EAAE,MAAM,mCAAmC;AACvD,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,eAAe,EAAEC,iBAAiB,QAAQ,wBAAwB;AAE3E,MAAMC,QAAQ,SAASF,eAAe,CAAC;EACnCG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,SAAS,GAAGf,iBAAiB,CAACE,MAAM,CAACC,WAAW,CAAC,CAAC;IACvD,IAAI,CAACa,EAAE,GAAGP,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACQ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,IAAI,GAAG,MAAM;EACtB;EACAC,aAAaA,CAAA,EAAG;IACZ,MAAMC,IAAI,GAAG,IAAI,CAACJ,EAAE,CAACK,qBAAqB,CAAC,CAAC;IAC5C,OAAO,IAAI,CAACN,SAAS,GACf,IAAIO,OAAO,CAACF,IAAI,CAACG,CAAC,GAAG,CAAC,EAAEH,IAAI,CAACI,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAACK,KAAK,GAAG,CAAC,EAAEL,IAAI,CAACM,MAAM,GAAG,CAAC,CAAC,GACpEN,IAAI;EACd;EACA;IAAS,IAAI,CAACO,IAAI;MAAA,IAAAC,qBAAA;MAAA,gBAAAC,iBAAAC,CAAA;QAAA,QAAAF,qBAAA,KAAAA,qBAAA,GAA+E3B,EAAE,CAAA8B,qBAAA,CAAQnB,QAAQ,IAAAkB,CAAA,IAARlB,QAAQ;MAAA;IAAA,IAAqD;EAAE;EAC1K;IAAS,IAAI,CAACoB,IAAI,kBAD+E/B,EAAE,CAAAgC,iBAAA;MAAAf,IAAA,EACJN,QAAQ;MAAAsB,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADNrC,EAAE,CAAAuC,WAAA,aAAAD,GAAA,CAAAtB,OACG,CAAC;QAAA;MAAA;MAAAwB,MAAA;QAAAxB,OAAA;MAAA;MAAAyB,UAAA;MAAAC,QAAA,GADN1C,EAAE,CAAA2C,kBAAA,CACmJ,CAACjC,iBAAiB,CAACC,QAAQ,CAAC,CAAC,GADlLX,EAAE,CAAA4C,uBAAA,EACuOtC,EAAE,CAACC,WAAW,IADvPP,EAAE,CAAA6C,0BAAA,EAAF7C,EAAE,CAAA8C,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,kBAAAb,EAAA,EAAAC,GAAA;MAAAa,MAAA;MAAAC,eAAA;IAAA,EACu7B;EAAE;AAChiC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGrD,EAAE,CAAAsD,iBAAA,CAGX3C,QAAQ,EAAc,CAAC;IACvGM,IAAI,EAAEd,SAAS;IACfoD,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEe,QAAQ,EAAE,WAAW;MAAEP,QAAQ,EAAE,EAAE;MAAEG,eAAe,EAAEhD,uBAAuB,CAACqD,MAAM;MAAEC,SAAS,EAAE,CAAChD,iBAAiB,CAACC,QAAQ,CAAC,CAAC;MAAEgD,cAAc,EAAE,CAACpD,WAAW,CAAC;MAAEqD,IAAI,EAAE;QACpL,kBAAkB,EAAE;MACxB,CAAC;MAAET,MAAM,EAAE,CAAC,glBAAglB;IAAE,CAAC;EAC3mB,CAAC,CAAC,QAAkB;IAAEnC,OAAO,EAAE,CAAC;MACxBC,IAAI,EAAEZ;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASM,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}