{"ast": null, "code": "import { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ViewContainerRef, ElementRef, TemplateRef, Component, ChangeDetectionStrategy, ViewChild, Input, signal, Directive } from '@angular/core';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiScrollControls } from '@taiga-ui/core/components/scrollbar';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldComponent, TuiWithTextfield, TUI_TEXTFIELD_OPTIONS } from '@taiga-ui/core/components/textfield';\nimport { TUI_SCROLL_REF } from '@taiga-ui/core/tokens';\nimport { PolymorpheusOutlet, injectContext, PolymorpheusComponent } from '@taiga-ui/polymorpheus';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { Validators, NG_VALIDATORS } from '@angular/forms';\nconst _c0 = [\"text\"];\nconst _c1 = [\"tuiTextarea\", \"\"];\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nfunction TuiTextarea_ng_template_0_tui_scroll_controls_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-scroll-controls\", 6);\n  }\n}\nfunction TuiTextarea_ng_template_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(text_r1);\n  }\n}\nfunction TuiTextarea_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiTextarea_ng_template_0_tui_scroll_controls_0_Template, 1, 0, \"tui-scroll-controls\", 2);\n    i0.ɵɵelementStart(1, \"span\", 3, 1);\n    i0.ɵɵtemplate(3, TuiTextarea_ng_template_0_ng_container_3_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"span\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isMobile);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"max-height\", 1.25 * ctx_r1.max, \"em\")(\"min-height\", 1.25 * ctx_r1.min, \"em\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r1.content)(\"polymorpheusOutletContext\", i0.ɵɵpureFunction1(7, _c2, ctx_r1.el.value));\n  }\n}\nconst [TUI_TEXTAREA_OPTIONS, tuiTextareaOptionsProvider] = tuiCreateOptions({\n  min: 1,\n  max: 3,\n  content: ({\n    $implicit\n  }) => $implicit\n});\nclass TuiTextarea {\n  constructor() {\n    this.options = inject(TUI_TEXTAREA_OPTIONS);\n    this.vcr = inject(ViewContainerRef);\n    this.el = tuiInjectElement();\n    this.textfield = inject(TuiTextfieldComponent);\n    this.isMobile = inject(TUI_IS_MOBILE);\n    this.min = this.options.min;\n    this.max = this.options.max;\n    this.content = this.options.content;\n  }\n  ngAfterViewInit() {\n    if (this.template) {\n      this.vcr.createEmbeddedView(this.template);\n    }\n  }\n  onScroll() {\n    this.text?.nativeElement.scrollTo({\n      top: this.el.scrollTop\n    });\n  }\n  static {\n    this.ɵfac = function TuiTextarea_Factory(t) {\n      return new (t || TuiTextarea)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTextarea,\n      selectors: [[\"textarea\", \"tuiTextarea\", \"\"]],\n      viewQuery: function TuiTextarea_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 5);\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.text = _t.first);\n        }\n      },\n      hostVars: 2,\n      hostBindings: function TuiTextarea_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"scroll.zoneless\", function TuiTextarea_scroll_zoneless_HostBindingHandler() {\n            return ctx.onScroll();\n          })(\"scroll.once\", function TuiTextarea_scroll_once_HostBindingHandler() {\n            return ctx.onScroll();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_mobile\", ctx.isMobile);\n        }\n      },\n      inputs: {\n        min: \"min\",\n        max: \"max\",\n        content: \"content\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TUI_SCROLL_REF, ElementRef)]), i0.ɵɵHostDirectivesFeature([i1.TuiWithTextfield]), i0.ɵɵStandaloneFeature],\n      attrs: _c1,\n      decls: 2,\n      vars: 0,\n      consts: [[\"template\", \"\"], [\"text\", \"\"], [\"class\", \"t-scroll\", 4, \"ngIf\"], [\"aria-hidden\", \"true\", 1, \"t-ghost\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [1, \"t-hack\"], [1, \"t-scroll\"]],\n      template: function TuiTextarea_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiTextarea_ng_template_0_Template, 5, 9, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [NgIf, PolymorpheusOutlet, TuiScrollControls],\n      styles: [\"tui-textfield._with-label[data-size=\\\"m\\\"][_nghost-%COMP%], tui-textfield._with-label[data-size=\\\"m\\\"]   [_nghost-%COMP%]{border-top-width:calc(.5rem + var(--t-height) / 3);padding-block-start:0}tui-textfield._with-label[data-size=\\\"l\\\"][_nghost-%COMP%], tui-textfield._with-label[data-size=\\\"l\\\"]   [_nghost-%COMP%]{border-top-width:calc(.625rem + var(--t-height) / 3);padding-block-start:0}tui-textfield[data-size=\\\"s\\\"][_nghost-%COMP%], tui-textfield[data-size=\\\"s\\\"]   [_nghost-%COMP%]{padding-block-start:.5rem;padding-block-end:.25rem}tui-textfield[data-size=\\\"m\\\"][_nghost-%COMP%], tui-textfield[data-size=\\\"m\\\"]   [_nghost-%COMP%]{padding-block-start:.875rem;padding-block-end:.25rem}tui-textfield[data-size=\\\"l\\\"][_nghost-%COMP%], tui-textfield[data-size=\\\"l\\\"]   [_nghost-%COMP%]{padding-block-start:1.125rem;padding-block-end:.375rem}[_nghost-%COMP%]{word-break:break-word;border:0 solid transparent}[data-appearance=table][_nghost-%COMP%]{border-top:1px solid transparent;border-bottom:none!important}[_nghost-%COMP%]:not(._mobile){scrollbar-width:none;-ms-overflow-style:none}[_nghost-%COMP%]:not(._mobile)::-webkit-scrollbar, [_nghost-%COMP%]:not(._mobile)::-webkit-scrollbar-thumb{display:none}.t-scroll[_ngcontent-%COMP%]{position:absolute;top:0;left:0;bottom:0;right:0;top:.25rem;bottom:.25rem;min-block-size:0}  tui-textfield._with-label[data-size=m] .t-scroll{top:calc(.5rem + var(--t-height) / 3)}  tui-textfield._with-label[data-size=l] .t-scroll{top:calc(.625rem + var(--t-height) / 3)}.t-ghost[_ngcontent-%COMP%]{z-index:1;order:1;inline-size:-webkit-fill-available;inline-size:-moz-available;inline-size:stretch;white-space:pre-wrap;word-break:break-word;overflow-wrap:break-word;padding-inline-start:var(--t-start);padding-inline-end:calc(var(--t-end) + var(--t-side));pointer-events:none;box-sizing:content-box;overflow:hidden;color:transparent}.t-ghost[_ngcontent-%COMP%]:after{content:\\\" \\\"}  tui-textfield._with-label[data-size=m] textarea~.t-ghost{margin-block-start:-1.375rem;padding-block-end:.3125rem}  tui-textfield._with-label[data-size=l] textarea~.t-ghost{margin-block-start:-1.75rem;padding-block-end:.5rem}  tui-textfield[data-size=s] textarea~.t-ghost{margin-block-start:-1.5rem;padding-block-end:.5rem}  tui-textfield[data-size=m] textarea~.t-ghost{margin-block-start:-2rem;padding-block-end:.875rem}  tui-textfield[data-size=l] textarea~.t-ghost{margin-block-start:-2.5rem;padding-block-end:1rem}.t-hack[_ngcontent-%COMP%]{transition-property:box-shadow;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:0;left:0;inline-size:100%;block-size:100%;pointer-events:none;border-radius:inherit;box-shadow:inset 0 0 0 1px transparent}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTextarea, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'textarea[tuiTextarea]',\n      imports: [NgIf, PolymorpheusOutlet, TuiScrollControls],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiProvide(TUI_SCROLL_REF, ElementRef)],\n      hostDirectives: [TuiWithTextfield],\n      host: {\n        '[class._mobile]': 'isMobile',\n        '(scroll.zoneless)': 'onScroll()',\n        // To trigger CD for #text\n        '(scroll.once)': 'onScroll()'\n      },\n      template: \"<ng-template #template>\\n    <tui-scroll-controls\\n        *ngIf=\\\"!isMobile\\\"\\n        class=\\\"t-scroll\\\"\\n    />\\n    <span\\n        #text\\n        aria-hidden=\\\"true\\\"\\n        class=\\\"t-ghost\\\"\\n        [style.max-height.em]=\\\"1.25 * max\\\"\\n        [style.min-height.em]=\\\"1.25 * min\\\"\\n    >\\n        <ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: el.value}\\\">{{ text }}</ng-container>\\n    </span>\\n    <span class=\\\"t-hack\\\"></span>\\n</ng-template>\\n\",\n      styles: [\":host-context(tui-textfield._with-label[data-size=\\\"m\\\"]){border-top-width:calc(.5rem + var(--t-height) / 3);padding-block-start:0}:host-context(tui-textfield._with-label[data-size=\\\"l\\\"]){border-top-width:calc(.625rem + var(--t-height) / 3);padding-block-start:0}:host-context(tui-textfield[data-size=\\\"s\\\"]){padding-block-start:.5rem;padding-block-end:.25rem}:host-context(tui-textfield[data-size=\\\"m\\\"]){padding-block-start:.875rem;padding-block-end:.25rem}:host-context(tui-textfield[data-size=\\\"l\\\"]){padding-block-start:1.125rem;padding-block-end:.375rem}:host{word-break:break-word;border:0 solid transparent}:host[data-appearance=table]{border-top:1px solid transparent;border-bottom:none!important}:host:not(._mobile){scrollbar-width:none;-ms-overflow-style:none}:host:not(._mobile)::-webkit-scrollbar,:host:not(._mobile)::-webkit-scrollbar-thumb{display:none}.t-scroll{position:absolute;top:0;left:0;bottom:0;right:0;top:.25rem;bottom:.25rem;min-block-size:0}::ng-deep tui-textfield._with-label[data-size=m] .t-scroll{top:calc(.5rem + var(--t-height) / 3)}::ng-deep tui-textfield._with-label[data-size=l] .t-scroll{top:calc(.625rem + var(--t-height) / 3)}.t-ghost{z-index:1;order:1;inline-size:-webkit-fill-available;inline-size:-moz-available;inline-size:stretch;white-space:pre-wrap;word-break:break-word;overflow-wrap:break-word;padding-inline-start:var(--t-start);padding-inline-end:calc(var(--t-end) + var(--t-side));pointer-events:none;box-sizing:content-box;overflow:hidden;color:transparent}.t-ghost:after{content:\\\" \\\"}::ng-deep tui-textfield._with-label[data-size=m] textarea~.t-ghost{margin-block-start:-1.375rem;padding-block-end:.3125rem}::ng-deep tui-textfield._with-label[data-size=l] textarea~.t-ghost{margin-block-start:-1.75rem;padding-block-end:.5rem}::ng-deep tui-textfield[data-size=s] textarea~.t-ghost{margin-block-start:-1.5rem;padding-block-end:.5rem}::ng-deep tui-textfield[data-size=m] textarea~.t-ghost{margin-block-start:-2rem;padding-block-end:.875rem}::ng-deep tui-textfield[data-size=l] textarea~.t-ghost{margin-block-start:-2.5rem;padding-block-end:1rem}.t-hack{transition-property:box-shadow;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:0;left:0;inline-size:100%;block-size:100%;pointer-events:none;border-radius:inherit;box-shadow:inset 0 0 0 1px transparent}\\n\"]\n    }]\n  }], null, {\n    template: [{\n      type: ViewChild,\n      args: [TemplateRef]\n    }],\n    text: [{\n      type: ViewChild,\n      args: ['text']\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    content: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiTextareaLimitComponent {\n  constructor() {\n    this.limit = inject(TuiTextareaLimit).limit;\n    this.context = injectContext();\n    this.background = 'linear-gradient(transparent 0.125rem, var(--tui-status-negative-pale) 0.125rem, var(--tui-status-negative-pale) calc(100% - 0.125rem), transparent calc(100% - 0.125rem))';\n  }\n  static {\n    this.ɵfac = function TuiTextareaLimitComponent_Factory(t) {\n      return new (t || TuiTextareaLimitComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTextareaLimitComponent,\n      selectors: [[\"ng-component\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 4,\n      consts: [[3, \"textContent\"]],\n      template: function TuiTextareaLimitComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"span\", 0)(1, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"textContent\", ctx.context.$implicit.slice(0, ctx.limit()));\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"background\", ctx.background);\n          i0.ɵɵproperty(\"textContent\", ctx.context.$implicit.slice(ctx.limit()));\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTextareaLimitComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: `\n        <span [textContent]=\"context.$implicit.slice(0, limit())\"></span>\n        <span\n            [style.background]=\"background\"\n            [textContent]=\"context.$implicit.slice(limit())\"\n        ></span>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\nclass TuiTextareaCounterComponent {\n  constructor() {\n    this.limit = signal(0);\n    this.length = signal(0);\n  }\n  static {\n    this.ɵfac = function TuiTextareaCounterComponent_Factory(t) {\n      return new (t || TuiTextareaCounterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTextareaCounterComponent,\n      selectors: [[\"ng-component\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 2,\n      template: function TuiTextareaCounterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtext(0);\n        }\n        if (rf & 2) {\n          i0.ɵɵtextInterpolate2(\"\", ctx.length(), \" / \", ctx.limit(), \"\");\n        }\n      },\n      styles: [\"[_nghost-%COMP%]{z-index:1;inline-size:100%;order:2;text-align:end;pointer-events:none;padding-bottom:.75rem;font:var(--tui-font-text-ui-xs);color:var(--tui-text-secondary)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTextareaCounterComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '{{ length() }} / {{ limit() }}',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\":host{z-index:1;inline-size:100%;order:2;text-align:end;pointer-events:none;padding-bottom:.75rem;font:var(--tui-font-text-ui-xs);color:var(--tui-text-secondary)}\\n\"]\n    }]\n  }], null, null);\n})();\nconst COMPONENT = new PolymorpheusComponent(TuiTextareaLimitComponent);\nclass TuiTextareaLimit {\n  constructor() {\n    this.textfield = inject(TuiTextfieldComponent);\n    this.ref = inject(ViewContainerRef).createComponent(TuiTextareaCounterComponent);\n    this.size = inject(TUI_TEXTFIELD_OPTIONS).size;\n    this.limit = signal(0);\n  }\n  // TODO: Use signal inputs in v5\n  set limitSetter(limit) {\n    this.limit.set(limit);\n  }\n  ngDoCheck() {\n    this.ref.instance.length.set(this.textfield.value().length);\n    this.ref.instance.limit.set(this.limit());\n  }\n  validate(control) {\n    return Validators.maxLength(this.limit())(control);\n  }\n  static {\n    this.ɵfac = function TuiTextareaLimit_Factory(t) {\n      return new (t || TuiTextareaLimit)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTextareaLimit,\n      selectors: [[\"\", \"tuiTextarea\", \"\", \"limit\", \"\"]],\n      hostVars: 2,\n      hostBindings: function TuiTextareaLimit_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"border-block-end-width\", ctx.size() === \"l\" ? 1.875 : 1.75, \"rem\");\n        }\n      },\n      inputs: {\n        limitSetter: [i0.ɵɵInputFlags.None, \"limit\", \"limitSetter\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(NG_VALIDATORS, TuiTextareaLimit, true), tuiTextareaOptionsProvider({\n        content: COMPONENT\n      })])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTextareaLimit, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiTextarea][limit]',\n      providers: [tuiProvide(NG_VALIDATORS, TuiTextareaLimit, true), tuiTextareaOptionsProvider({\n        content: COMPONENT\n      })],\n      host: {\n        '[style.border-block-end-width.rem]': 'size() === \"l\" ? 1.875 : 1.75'\n      }\n    }]\n  }], null, {\n    limitSetter: [{\n      type: Input,\n      args: ['limit']\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_TEXTAREA_OPTIONS, TuiTextarea, TuiTextareaCounterComponent, TuiTextareaLimit, TuiTextareaLimitComponent, tuiTextareaOptionsProvider };", "map": {"version": 3, "names": ["NgIf", "i0", "inject", "ViewContainerRef", "ElementRef", "TemplateRef", "Component", "ChangeDetectionStrategy", "ViewChild", "Input", "signal", "Directive", "TUI_IS_MOBILE", "tuiInjectElement", "tui<PERSON><PERSON><PERSON>", "TuiScrollControls", "i1", "TuiTextfieldComponent", "TuiWithTextfield", "TUI_TEXTFIELD_OPTIONS", "TUI_SCROLL_REF", "Polymorpheus<PERSON><PERSON>let", "injectContext", "PolymorpheusComponent", "tuiCreateOptions", "Validators", "NG_VALIDATORS", "_c0", "_c1", "_c2", "a0", "$implicit", "TuiTextarea_ng_template_0_tui_scroll_controls_0_Template", "rf", "ctx", "ɵɵelement", "TuiTextarea_ng_template_0_ng_container_3_Template", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "text_r1", "polymorpheusOutlet", "ɵɵadvance", "ɵɵtextInterpolate", "TuiTextarea_ng_template_0_Template", "ɵɵtemplate", "ɵɵelementStart", "ɵɵelementEnd", "ctx_r1", "ɵɵnextContext", "ɵɵproperty", "isMobile", "ɵɵstyleProp", "max", "min", "content", "ɵɵpureFunction1", "el", "value", "TUI_TEXTAREA_OPTIONS", "tuiTextareaOptionsProvider", "TuiTextarea", "constructor", "options", "vcr", "textfield", "ngAfterViewInit", "template", "createEmbeddedView", "onScroll", "text", "nativeElement", "scrollTo", "top", "scrollTop", "ɵfac", "TuiTextarea_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "TuiTextarea_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostVars", "hostBindings", "TuiTextarea_HostBindings", "ɵɵlistener", "TuiTextarea_scroll_zoneless_HostBindingHandler", "TuiTextarea_scroll_once_HostBindingHandler", "ɵɵclassProp", "inputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "consts", "TuiTextarea_Template", "ɵɵtemplateRefExtractor", "dependencies", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "hostDirectives", "host", "TuiTextareaLimitComponent", "limit", "TuiTextareaLimit", "context", "background", "TuiTextareaLimitComponent_Factory", "TuiTextareaLimitComponent_Template", "slice", "encapsulation", "TuiTextareaCounterComponent", "length", "TuiTextareaCounterComponent_Factory", "TuiTextareaCounterComponent_Template", "ɵɵtextInterpolate2", "COMPONENT", "ref", "createComponent", "size", "limitSetter", "set", "ngDoCheck", "instance", "validate", "control", "max<PERSON><PERSON><PERSON>", "TuiTextareaLimit_Factory", "ɵdir", "ɵɵdefineDirective", "TuiTextareaLimit_HostBindings", "ɵɵInputFlags", "None"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-textarea.mjs"], "sourcesContent": ["import { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ViewContainerRef, ElementRef, TemplateRef, Component, ChangeDetectionStrategy, ViewChild, Input, signal, Directive } from '@angular/core';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiScrollControls } from '@taiga-ui/core/components/scrollbar';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldComponent, TuiWithTextfield, TUI_TEXTFIELD_OPTIONS } from '@taiga-ui/core/components/textfield';\nimport { TUI_SCROLL_REF } from '@taiga-ui/core/tokens';\nimport { PolymorpheusOutlet, injectContext, PolymorpheusComponent } from '@taiga-ui/polymorpheus';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { Validators, NG_VALIDATORS } from '@angular/forms';\n\nconst [TUI_TEXTAREA_OPTIONS, tuiTextareaOptionsProvider] = tuiCreateOptions({\n    min: 1,\n    max: 3,\n    content: ({ $implicit }) => $implicit,\n});\n\nclass TuiTextarea {\n    constructor() {\n        this.options = inject(TUI_TEXTAREA_OPTIONS);\n        this.vcr = inject(ViewContainerRef);\n        this.el = tuiInjectElement();\n        this.textfield = inject((TuiTextfieldComponent));\n        this.isMobile = inject(TUI_IS_MOBILE);\n        this.min = this.options.min;\n        this.max = this.options.max;\n        this.content = this.options.content;\n    }\n    ngAfterViewInit() {\n        if (this.template) {\n            this.vcr.createEmbeddedView(this.template);\n        }\n    }\n    onScroll() {\n        this.text?.nativeElement.scrollTo({ top: this.el.scrollTop });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextarea, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTextarea, isStandalone: true, selector: \"textarea[tuiTextarea]\", inputs: { min: \"min\", max: \"max\", content: \"content\" }, host: { listeners: { \"scroll.zoneless\": \"onScroll()\", \"scroll.once\": \"onScroll()\" }, properties: { \"class._mobile\": \"isMobile\" } }, providers: [tuiProvide(TUI_SCROLL_REF, ElementRef)], viewQueries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true }, { propertyName: \"text\", first: true, predicate: [\"text\"], descendants: true }], hostDirectives: [{ directive: i1.TuiWithTextfield }], ngImport: i0, template: \"<ng-template #template>\\n    <tui-scroll-controls\\n        *ngIf=\\\"!isMobile\\\"\\n        class=\\\"t-scroll\\\"\\n    />\\n    <span\\n        #text\\n        aria-hidden=\\\"true\\\"\\n        class=\\\"t-ghost\\\"\\n        [style.max-height.em]=\\\"1.25 * max\\\"\\n        [style.min-height.em]=\\\"1.25 * min\\\"\\n    >\\n        <ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: el.value}\\\">{{ text }}</ng-container>\\n    </span>\\n    <span class=\\\"t-hack\\\"></span>\\n</ng-template>\\n\", styles: [\":host-context(tui-textfield._with-label[data-size=\\\"m\\\"]){border-top-width:calc(.5rem + var(--t-height) / 3);padding-block-start:0}:host-context(tui-textfield._with-label[data-size=\\\"l\\\"]){border-top-width:calc(.625rem + var(--t-height) / 3);padding-block-start:0}:host-context(tui-textfield[data-size=\\\"s\\\"]){padding-block-start:.5rem;padding-block-end:.25rem}:host-context(tui-textfield[data-size=\\\"m\\\"]){padding-block-start:.875rem;padding-block-end:.25rem}:host-context(tui-textfield[data-size=\\\"l\\\"]){padding-block-start:1.125rem;padding-block-end:.375rem}:host{word-break:break-word;border:0 solid transparent}:host[data-appearance=table]{border-top:1px solid transparent;border-bottom:none!important}:host:not(._mobile){scrollbar-width:none;-ms-overflow-style:none}:host:not(._mobile)::-webkit-scrollbar,:host:not(._mobile)::-webkit-scrollbar-thumb{display:none}.t-scroll{position:absolute;top:0;left:0;bottom:0;right:0;top:.25rem;bottom:.25rem;min-block-size:0}::ng-deep tui-textfield._with-label[data-size=m] .t-scroll{top:calc(.5rem + var(--t-height) / 3)}::ng-deep tui-textfield._with-label[data-size=l] .t-scroll{top:calc(.625rem + var(--t-height) / 3)}.t-ghost{z-index:1;order:1;inline-size:-webkit-fill-available;inline-size:-moz-available;inline-size:stretch;white-space:pre-wrap;word-break:break-word;overflow-wrap:break-word;padding-inline-start:var(--t-start);padding-inline-end:calc(var(--t-end) + var(--t-side));pointer-events:none;box-sizing:content-box;overflow:hidden;color:transparent}.t-ghost:after{content:\\\" \\\"}::ng-deep tui-textfield._with-label[data-size=m] textarea~.t-ghost{margin-block-start:-1.375rem;padding-block-end:.3125rem}::ng-deep tui-textfield._with-label[data-size=l] textarea~.t-ghost{margin-block-start:-1.75rem;padding-block-end:.5rem}::ng-deep tui-textfield[data-size=s] textarea~.t-ghost{margin-block-start:-1.5rem;padding-block-end:.5rem}::ng-deep tui-textfield[data-size=m] textarea~.t-ghost{margin-block-start:-2rem;padding-block-end:.875rem}::ng-deep tui-textfield[data-size=l] textarea~.t-ghost{margin-block-start:-2.5rem;padding-block-end:1rem}.t-hack{transition-property:box-shadow;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:0;left:0;inline-size:100%;block-size:100%;pointer-events:none;border-radius:inherit;box-shadow:inset 0 0 0 1px transparent}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"component\", type: TuiScrollControls, selector: \"tui-scroll-controls\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextarea, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'textarea[tuiTextarea]', imports: [NgIf, PolymorpheusOutlet, TuiScrollControls], changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiProvide(TUI_SCROLL_REF, ElementRef)], hostDirectives: [TuiWithTextfield], host: {\n                        '[class._mobile]': 'isMobile',\n                        '(scroll.zoneless)': 'onScroll()',\n                        // To trigger CD for #text\n                        '(scroll.once)': 'onScroll()',\n                    }, template: \"<ng-template #template>\\n    <tui-scroll-controls\\n        *ngIf=\\\"!isMobile\\\"\\n        class=\\\"t-scroll\\\"\\n    />\\n    <span\\n        #text\\n        aria-hidden=\\\"true\\\"\\n        class=\\\"t-ghost\\\"\\n        [style.max-height.em]=\\\"1.25 * max\\\"\\n        [style.min-height.em]=\\\"1.25 * min\\\"\\n    >\\n        <ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: el.value}\\\">{{ text }}</ng-container>\\n    </span>\\n    <span class=\\\"t-hack\\\"></span>\\n</ng-template>\\n\", styles: [\":host-context(tui-textfield._with-label[data-size=\\\"m\\\"]){border-top-width:calc(.5rem + var(--t-height) / 3);padding-block-start:0}:host-context(tui-textfield._with-label[data-size=\\\"l\\\"]){border-top-width:calc(.625rem + var(--t-height) / 3);padding-block-start:0}:host-context(tui-textfield[data-size=\\\"s\\\"]){padding-block-start:.5rem;padding-block-end:.25rem}:host-context(tui-textfield[data-size=\\\"m\\\"]){padding-block-start:.875rem;padding-block-end:.25rem}:host-context(tui-textfield[data-size=\\\"l\\\"]){padding-block-start:1.125rem;padding-block-end:.375rem}:host{word-break:break-word;border:0 solid transparent}:host[data-appearance=table]{border-top:1px solid transparent;border-bottom:none!important}:host:not(._mobile){scrollbar-width:none;-ms-overflow-style:none}:host:not(._mobile)::-webkit-scrollbar,:host:not(._mobile)::-webkit-scrollbar-thumb{display:none}.t-scroll{position:absolute;top:0;left:0;bottom:0;right:0;top:.25rem;bottom:.25rem;min-block-size:0}::ng-deep tui-textfield._with-label[data-size=m] .t-scroll{top:calc(.5rem + var(--t-height) / 3)}::ng-deep tui-textfield._with-label[data-size=l] .t-scroll{top:calc(.625rem + var(--t-height) / 3)}.t-ghost{z-index:1;order:1;inline-size:-webkit-fill-available;inline-size:-moz-available;inline-size:stretch;white-space:pre-wrap;word-break:break-word;overflow-wrap:break-word;padding-inline-start:var(--t-start);padding-inline-end:calc(var(--t-end) + var(--t-side));pointer-events:none;box-sizing:content-box;overflow:hidden;color:transparent}.t-ghost:after{content:\\\" \\\"}::ng-deep tui-textfield._with-label[data-size=m] textarea~.t-ghost{margin-block-start:-1.375rem;padding-block-end:.3125rem}::ng-deep tui-textfield._with-label[data-size=l] textarea~.t-ghost{margin-block-start:-1.75rem;padding-block-end:.5rem}::ng-deep tui-textfield[data-size=s] textarea~.t-ghost{margin-block-start:-1.5rem;padding-block-end:.5rem}::ng-deep tui-textfield[data-size=m] textarea~.t-ghost{margin-block-start:-2rem;padding-block-end:.875rem}::ng-deep tui-textfield[data-size=l] textarea~.t-ghost{margin-block-start:-2.5rem;padding-block-end:1rem}.t-hack{transition-property:box-shadow;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:0;left:0;inline-size:100%;block-size:100%;pointer-events:none;border-radius:inherit;box-shadow:inset 0 0 0 1px transparent}\\n\"] }]\n        }], propDecorators: { template: [{\n                type: ViewChild,\n                args: [TemplateRef]\n            }], text: [{\n                type: ViewChild,\n                args: ['text']\n            }], min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], content: [{\n                type: Input\n            }] } });\n\nclass TuiTextareaLimitComponent {\n    constructor() {\n        this.limit = inject(TuiTextareaLimit).limit;\n        this.context = injectContext();\n        this.background = 'linear-gradient(transparent 0.125rem, var(--tui-status-negative-pale) 0.125rem, var(--tui-status-negative-pale) calc(100% - 0.125rem), transparent calc(100% - 0.125rem))';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextareaLimitComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTextareaLimitComponent, isStandalone: true, selector: \"ng-component\", ngImport: i0, template: `\n        <span [textContent]=\"context.$implicit.slice(0, limit())\"></span>\n        <span\n            [style.background]=\"background\"\n            [textContent]=\"context.$implicit.slice(limit())\"\n        ></span>\n    `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextareaLimitComponent, decorators: [{\n            type: Component,\n            args: [{\n                    standalone: true,\n                    template: `\n        <span [textContent]=\"context.$implicit.slice(0, limit())\"></span>\n        <span\n            [style.background]=\"background\"\n            [textContent]=\"context.$implicit.slice(limit())\"\n        ></span>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }] });\nclass TuiTextareaCounterComponent {\n    constructor() {\n        this.limit = signal(0);\n        this.length = signal(0);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextareaCounterComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTextareaCounterComponent, isStandalone: true, selector: \"ng-component\", ngImport: i0, template: '{{ length() }} / {{ limit() }}', isInline: true, styles: [\":host{z-index:1;inline-size:100%;order:2;text-align:end;pointer-events:none;padding-bottom:.75rem;font:var(--tui-font-text-ui-xs);color:var(--tui-text-secondary)}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextareaCounterComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '{{ length() }} / {{ limit() }}', changeDetection: ChangeDetectionStrategy.OnPush, styles: [\":host{z-index:1;inline-size:100%;order:2;text-align:end;pointer-events:none;padding-bottom:.75rem;font:var(--tui-font-text-ui-xs);color:var(--tui-text-secondary)}\\n\"] }]\n        }] });\nconst COMPONENT = new PolymorpheusComponent(TuiTextareaLimitComponent);\nclass TuiTextareaLimit {\n    constructor() {\n        this.textfield = inject(TuiTextfieldComponent);\n        this.ref = inject(ViewContainerRef).createComponent(TuiTextareaCounterComponent);\n        this.size = inject(TUI_TEXTFIELD_OPTIONS).size;\n        this.limit = signal(0);\n    }\n    // TODO: Use signal inputs in v5\n    set limitSetter(limit) {\n        this.limit.set(limit);\n    }\n    ngDoCheck() {\n        this.ref.instance.length.set(this.textfield.value().length);\n        this.ref.instance.limit.set(this.limit());\n    }\n    validate(control) {\n        return Validators.maxLength(this.limit())(control);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextareaLimit, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTextareaLimit, isStandalone: true, selector: \"[tuiTextarea][limit]\", inputs: { limitSetter: [\"limit\", \"limitSetter\"] }, host: { properties: { \"style.border-block-end-width.rem\": \"size() === \\\"l\\\" ? 1.875 : 1.75\" } }, providers: [\n            tuiProvide(NG_VALIDATORS, TuiTextareaLimit, true),\n            tuiTextareaOptionsProvider({ content: COMPONENT }),\n        ], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTextareaLimit, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiTextarea][limit]',\n                    providers: [\n                        tuiProvide(NG_VALIDATORS, TuiTextareaLimit, true),\n                        tuiTextareaOptionsProvider({ content: COMPONENT }),\n                    ],\n                    host: {\n                        '[style.border-block-end-width.rem]': 'size() === \"l\" ? 1.875 : 1.75',\n                    },\n                }]\n        }], propDecorators: { limitSetter: [{\n                type: Input,\n                args: ['limit']\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_TEXTAREA_OPTIONS, TuiTextarea, TuiTextareaCounterComponent, TuiTextareaLimit, TuiTextareaLimitComponent, tuiTextareaOptionsProvider };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,iBAAiB;AACtC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AAC1J,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,qBAAqB,EAAEC,gBAAgB,EAAEC,qBAAqB,QAAQ,qCAAqC;AACpH,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,kBAAkB,EAAEC,aAAa,EAAEC,qBAAqB,QAAQ,wBAAwB;AACjG,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,UAAU,EAAEC,aAAa,QAAQ,gBAAgB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,SAAAE,yDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA2B0ChC,EAAE,CAAAkC,SAAA,4BACqqB,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADxqBhC,EAAE,CAAAoC,uBAAA,EAC27B,CAAC;IAD97BpC,EAAE,CAAAqC,MAAA,EACq8B,CAAC;IADx8BrC,EAAE,CAAAsC,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,OAAA,GAAAN,GAAA,CAAAO,kBAAA;IAAFxC,EAAE,CAAAyC,SAAA,CACq8B,CAAC;IADx8BzC,EAAE,CAAA0C,iBAAA,CAAAH,OACq8B,CAAC;EAAA;AAAA;AAAA,SAAAI,mCAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADx8BhC,EAAE,CAAA4C,UAAA,IAAAb,wDAAA,gCACqqB,CAAC;IADxqB/B,EAAE,CAAA6C,cAAA,gBAC21B,CAAC;IAD91B7C,EAAE,CAAA4C,UAAA,IAAAT,iDAAA,yBAC27B,CAAC;IAD97BnC,EAAE,CAAA8C,YAAA,CACi+B,CAAC;IADp+B9C,EAAE,CAAAkC,SAAA,aACqgC,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAe,MAAA,GADxgC/C,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAiD,UAAA,UAAAF,MAAA,CAAAG,QAC+nB,CAAC;IADloBlD,EAAE,CAAAyC,SAAA,CACsyB,CAAC;IADzyBzC,EAAE,CAAAmD,WAAA,sBAAAJ,MAAA,CAAAK,GAAA,MACsyB,CAAC,sBAAAL,MAAA,CAAAM,GAAA,MAA6C,CAAC;IADv1BrD,EAAE,CAAAyC,SAAA,EACi5B,CAAC;IADp5BzC,EAAE,CAAAiD,UAAA,uBAAAF,MAAA,CAAAO,OACi5B,CAAC,8BADp5BtD,EAAE,CAAAuD,eAAA,IAAA3B,GAAA,EAAAmB,MAAA,CAAAS,EAAA,CAAAC,KAAA,CACw7B,CAAC;EAAA;AAAA;AA1BhiC,MAAM,CAACC,oBAAoB,EAAEC,0BAA0B,CAAC,GAAGpC,gBAAgB,CAAC;EACxE8B,GAAG,EAAE,CAAC;EACND,GAAG,EAAE,CAAC;EACNE,OAAO,EAAEA,CAAC;IAAExB;EAAU,CAAC,KAAKA;AAChC,CAAC,CAAC;AAEF,MAAM8B,WAAW,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG7D,MAAM,CAACyD,oBAAoB,CAAC;IAC3C,IAAI,CAACK,GAAG,GAAG9D,MAAM,CAACC,gBAAgB,CAAC;IACnC,IAAI,CAACsD,EAAE,GAAG5C,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACoD,SAAS,GAAG/D,MAAM,CAAEe,qBAAsB,CAAC;IAChD,IAAI,CAACkC,QAAQ,GAAGjD,MAAM,CAACU,aAAa,CAAC;IACrC,IAAI,CAAC0C,GAAG,GAAG,IAAI,CAACS,OAAO,CAACT,GAAG;IAC3B,IAAI,CAACD,GAAG,GAAG,IAAI,CAACU,OAAO,CAACV,GAAG;IAC3B,IAAI,CAACE,OAAO,GAAG,IAAI,CAACQ,OAAO,CAACR,OAAO;EACvC;EACAW,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,QAAQ,EAAE;MACf,IAAI,CAACH,GAAG,CAACI,kBAAkB,CAAC,IAAI,CAACD,QAAQ,CAAC;IAC9C;EACJ;EACAE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,IAAI,EAAEC,aAAa,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,IAAI,CAAChB,EAAE,CAACiB;IAAU,CAAC,CAAC;EACjE;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFhB,WAAW;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACiB,IAAI,kBAD+E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EACJnB,WAAW;MAAAoB,SAAA;MAAAC,SAAA,WAAAC,kBAAAlD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADThC,EAAE,CAAAmF,WAAA,CACmX/E,WAAW;UADhYJ,EAAE,CAAAmF,WAAA,CAAAzD,GAAA;QAAA;QAAA,IAAAM,EAAA;UAAA,IAAAoD,EAAA;UAAFpF,EAAE,CAAAqF,cAAA,CAAAD,EAAA,GAAFpF,EAAE,CAAAsF,WAAA,QAAArD,GAAA,CAAAiC,QAAA,GAAAkB,EAAA,CAAAG,KAAA;UAAFvF,EAAE,CAAAqF,cAAA,CAAAD,EAAA,GAAFpF,EAAE,CAAAsF,WAAA,QAAArD,GAAA,CAAAoC,IAAA,GAAAe,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,yBAAA1D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhC,EAAE,CAAA2F,UAAA,6BAAAC,+CAAA;YAAA,OACJ3D,GAAA,CAAAmC,QAAA,CAAS,CAAC;UAAA,EAAC,yBAAAyB,2CAAA;YAAA,OAAX5D,GAAA,CAAAmC,QAAA,CAAS,CAAC;UAAA,EAAC;QAAA;QAAA,IAAApC,EAAA;UADThC,EAAE,CAAA8F,WAAA,YAAA7D,GAAA,CAAAiB,QACM,CAAC;QAAA;MAAA;MAAA6C,MAAA;QAAA1C,GAAA;QAAAD,GAAA;QAAAE,OAAA;MAAA;MAAA0C,UAAA;MAAAC,QAAA,GADTjG,EAAE,CAAAkG,kBAAA,CACuQ,CAACrF,UAAU,CAACM,cAAc,EAAEhB,UAAU,CAAC,CAAC,GADjTH,EAAE,CAAAmG,uBAAA,EACmgBpF,EAAE,CAACE,gBAAgB,IADxhBjB,EAAE,CAAAoG,mBAAA;MAAAC,KAAA,EAAA1E,GAAA;MAAA2E,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAtC,QAAA,WAAAuC,qBAAAzE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhC,EAAE,CAAA4C,UAAA,IAAAD,kCAAA,gCAAF3C,EAAE,CAAA0G,sBAC0kB,CAAC;QAAA;MAAA;MAAAC,YAAA,GAAu0F5G,IAAI,EAA6FqB,kBAAkB,EAA8HN,iBAAiB;MAAA8F,MAAA;MAAAC,eAAA;IAAA,EAA2F;EAAE;AACx1H;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG9G,EAAE,CAAA+G,iBAAA,CAGXnD,WAAW,EAAc,CAAC;IAC1GmB,IAAI,EAAE1E,SAAS;IACf2G,IAAI,EAAE,CAAC;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,QAAQ,EAAE,uBAAuB;MAAEC,OAAO,EAAE,CAACnH,IAAI,EAAEqB,kBAAkB,EAAEN,iBAAiB,CAAC;MAAE+F,eAAe,EAAEvG,uBAAuB,CAAC6G,MAAM;MAAEC,SAAS,EAAE,CAACvG,UAAU,CAACM,cAAc,EAAEhB,UAAU,CAAC,CAAC;MAAEkH,cAAc,EAAE,CAACpG,gBAAgB,CAAC;MAAEqG,IAAI,EAAE;QAC1P,iBAAiB,EAAE,UAAU;QAC7B,mBAAmB,EAAE,YAAY;QACjC;QACA,eAAe,EAAE;MACrB,CAAC;MAAEpD,QAAQ,EAAE,seAAse;MAAE0C,MAAM,EAAE,CAAC,i0EAAi0E;IAAE,CAAC;EAC90F,CAAC,CAAC,QAAkB;IAAE1C,QAAQ,EAAE,CAAC;MACzBa,IAAI,EAAExE,SAAS;MACfyG,IAAI,EAAE,CAAC5G,WAAW;IACtB,CAAC,CAAC;IAAEiE,IAAI,EAAE,CAAC;MACPU,IAAI,EAAExE,SAAS;MACfyG,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC,CAAC;IAAE3D,GAAG,EAAE,CAAC;MACN0B,IAAI,EAAEvE;IACV,CAAC,CAAC;IAAE4C,GAAG,EAAE,CAAC;MACN2B,IAAI,EAAEvE;IACV,CAAC,CAAC;IAAE8C,OAAO,EAAE,CAAC;MACVyB,IAAI,EAAEvE;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+G,yBAAyB,CAAC;EAC5B1D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2D,KAAK,GAAGvH,MAAM,CAACwH,gBAAgB,CAAC,CAACD,KAAK;IAC3C,IAAI,CAACE,OAAO,GAAGrG,aAAa,CAAC,CAAC;IAC9B,IAAI,CAACsG,UAAU,GAAG,2KAA2K;EACjM;EACA;IAAS,IAAI,CAACjD,IAAI,YAAAkD,kCAAAhD,CAAA;MAAA,YAAAA,CAAA,IAAyF2C,yBAAyB;IAAA,CAAmD;EAAE;EACzL;IAAS,IAAI,CAAC1C,IAAI,kBAhC+E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EAgCJwC,yBAAyB;MAAAvC,SAAA;MAAAgB,UAAA;MAAAC,QAAA,GAhCvBjG,EAAE,CAAAoG,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAtC,QAAA,WAAA2D,mCAAA7F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhC,EAAE,CAAAkC,SAAA,aAiC/B,CAAC,aAI1D,CAAC;QAAA;QAAA,IAAAF,EAAA;UArCqFhC,EAAE,CAAAiD,UAAA,gBAAAhB,GAAA,CAAAyF,OAAA,CAAA5F,SAAA,CAAAgG,KAAA,IAAA7F,GAAA,CAAAuF,KAAA,GAiCvC,CAAC;UAjCoCxH,EAAE,CAAAyC,SAAA,CAmC7D,CAAC;UAnC0DzC,EAAE,CAAAmD,WAAA,eAAAlB,GAAA,CAAA0F,UAmC7D,CAAC;UAnC0D3H,EAAE,CAAAiD,UAAA,gBAAAhB,GAAA,CAAAyF,OAAA,CAAA5F,SAAA,CAAAgG,KAAA,CAAA7F,GAAA,CAAAuF,KAAA,GAoC5C,CAAC;QAAA;MAAA;MAAAO,aAAA;MAAAlB,eAAA;IAAA,EAEgB;EAAE;AAC9E;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxCqG9G,EAAE,CAAA+G,iBAAA,CAwCXQ,yBAAyB,EAAc,CAAC;IACxHxC,IAAI,EAAE1E,SAAS;IACf2G,IAAI,EAAE,CAAC;MACChB,UAAU,EAAE,IAAI;MAChB9B,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA,KAAK;MACe2C,eAAe,EAAEvG,uBAAuB,CAAC6G;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMa,2BAA2B,CAAC;EAC9BnE,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2D,KAAK,GAAG/G,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI,CAACwH,MAAM,GAAGxH,MAAM,CAAC,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAACiE,IAAI,YAAAwD,oCAAAtD,CAAA;MAAA,YAAAA,CAAA,IAAyFoD,2BAA2B;IAAA,CAAmD;EAAE;EAC3L;IAAS,IAAI,CAACnD,IAAI,kBA5D+E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EA4DJiD,2BAA2B;MAAAhD,SAAA;MAAAgB,UAAA;MAAAC,QAAA,GA5DzBjG,EAAE,CAAAoG,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAArC,QAAA,WAAAiE,qCAAAnG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhC,EAAE,CAAAqC,MAAA,EA4D6H,CAAC;QAAA;QAAA,IAAAL,EAAA;UA5DhIhC,EAAE,CAAAoI,kBAAA,KAAAnG,GAAA,CAAAgG,MAAA,WAAAhG,GAAA,CAAAuF,KAAA,MA4D6H,CAAC;QAAA;MAAA;MAAAZ,MAAA;MAAAC,eAAA;IAAA,EAA0P;EAAE;AACje;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9DqG9G,EAAE,CAAA+G,iBAAA,CA8DXiB,2BAA2B,EAAc,CAAC;IAC1HjD,IAAI,EAAE1E,SAAS;IACf2G,IAAI,EAAE,CAAC;MAAEhB,UAAU,EAAE,IAAI;MAAE9B,QAAQ,EAAE,gCAAgC;MAAE2C,eAAe,EAAEvG,uBAAuB,CAAC6G,MAAM;MAAEP,MAAM,EAAE,CAAC,sKAAsK;IAAE,CAAC;EAC9S,CAAC,CAAC;AAAA;AACV,MAAMyB,SAAS,GAAG,IAAI/G,qBAAqB,CAACiG,yBAAyB,CAAC;AACtE,MAAME,gBAAgB,CAAC;EACnB5D,WAAWA,CAAA,EAAG;IACV,IAAI,CAACG,SAAS,GAAG/D,MAAM,CAACe,qBAAqB,CAAC;IAC9C,IAAI,CAACsH,GAAG,GAAGrI,MAAM,CAACC,gBAAgB,CAAC,CAACqI,eAAe,CAACP,2BAA2B,CAAC;IAChF,IAAI,CAACQ,IAAI,GAAGvI,MAAM,CAACiB,qBAAqB,CAAC,CAACsH,IAAI;IAC9C,IAAI,CAAChB,KAAK,GAAG/G,MAAM,CAAC,CAAC,CAAC;EAC1B;EACA;EACA,IAAIgI,WAAWA,CAACjB,KAAK,EAAE;IACnB,IAAI,CAACA,KAAK,CAACkB,GAAG,CAAClB,KAAK,CAAC;EACzB;EACAmB,SAASA,CAAA,EAAG;IACR,IAAI,CAACL,GAAG,CAACM,QAAQ,CAACX,MAAM,CAACS,GAAG,CAAC,IAAI,CAAC1E,SAAS,CAACP,KAAK,CAAC,CAAC,CAACwE,MAAM,CAAC;IAC3D,IAAI,CAACK,GAAG,CAACM,QAAQ,CAACpB,KAAK,CAACkB,GAAG,CAAC,IAAI,CAAClB,KAAK,CAAC,CAAC,CAAC;EAC7C;EACAqB,QAAQA,CAACC,OAAO,EAAE;IACd,OAAOtH,UAAU,CAACuH,SAAS,CAAC,IAAI,CAACvB,KAAK,CAAC,CAAC,CAAC,CAACsB,OAAO,CAAC;EACtD;EACA;IAAS,IAAI,CAACpE,IAAI,YAAAsE,yBAAApE,CAAA;MAAA,YAAAA,CAAA,IAAyF6C,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACwB,IAAI,kBAtF+EjJ,EAAE,CAAAkJ,iBAAA;MAAAnE,IAAA,EAsFJ0C,gBAAgB;MAAAzC,SAAA;MAAAQ,QAAA;MAAAC,YAAA,WAAA0D,8BAAAnH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtFdhC,EAAE,CAAAmD,WAAA,2BAsFJlB,GAAA,CAAAuG,IAAA,CAAK,CAAC,KAAK,GAAG,GAAG,KAAK,GAAG,IAAI,OAAd,CAAC;QAAA;MAAA;MAAAzC,MAAA;QAAA0C,WAAA,GAtFdzI,EAAE,CAAAoJ,YAAA,CAAAC,IAAA;MAAA;MAAArD,UAAA;MAAAC,QAAA,GAAFjG,EAAE,CAAAkG,kBAAA,CAsFmO,CAC9TrF,UAAU,CAACY,aAAa,EAAEgG,gBAAgB,EAAE,IAAI,CAAC,EACjD9D,0BAA0B,CAAC;QAAEL,OAAO,EAAE+E;MAAU,CAAC,CAAC,CACrD;IAAA,EAAiB;EAAE;AAC5B;AACA;EAAA,QAAAvB,SAAA,oBAAAA,SAAA,KA3FqG9G,EAAE,CAAA+G,iBAAA,CA2FXU,gBAAgB,EAAc,CAAC;IAC/G1C,IAAI,EAAErE,SAAS;IACfsG,IAAI,EAAE,CAAC;MACChB,UAAU,EAAE,IAAI;MAChBiB,QAAQ,EAAE,sBAAsB;MAChCG,SAAS,EAAE,CACPvG,UAAU,CAACY,aAAa,EAAEgG,gBAAgB,EAAE,IAAI,CAAC,EACjD9D,0BAA0B,CAAC;QAAEL,OAAO,EAAE+E;MAAU,CAAC,CAAC,CACrD;MACDf,IAAI,EAAE;QACF,oCAAoC,EAAE;MAC1C;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEmB,WAAW,EAAE,CAAC;MAC5B1D,IAAI,EAAEvE,KAAK;MACXwG,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAAStD,oBAAoB,EAAEE,WAAW,EAAEoE,2BAA2B,EAAEP,gBAAgB,EAAEF,yBAAyB,EAAE5D,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}