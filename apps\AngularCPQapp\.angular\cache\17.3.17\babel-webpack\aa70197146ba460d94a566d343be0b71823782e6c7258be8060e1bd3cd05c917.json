{"ast": null, "code": "import { Ng<PERSON><PERSON>, CurrencyPipe } from '@angular/common';\nimport { TuiTreeItemContent } from '@taiga-ui/kit';\nimport * as i0 from \"@angular/core\";\nexport class TreeContentComponent extends TuiTreeItemContent {\n  get icon() {\n    return this.isExpandable ? 'bi-folder-fill text-warning' : 'bi-file-earmark text-primary';\n  }\n  onCheckboxChange(event) {\n    const checked = event.target.checked;\n    // Émettre un événement personnalisé pour gérer la sélection\n    this.context.$implicit.selected = checked;\n  }\n  static {\n    this.ɵfac = /*@__PURE__*/(() => {\n      let ɵTreeContentComponent_BaseFactory;\n      return function TreeContentComponent_Factory(t) {\n        return (ɵTreeContentComponent_BaseFactory || (ɵTreeContentComponent_BaseFactory = i0.ɵɵgetInheritedFactory(TreeContentComponent)))(t || TreeContentComponent);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TreeContentComponent,\n      selectors: [[\"app-tree-content\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 9,\n      vars: 7,\n      consts: [[1, \"tui-tree-content\"], [1, \"bi\", \"me-2\", 3, \"ngClass\"], [1, \"tui-tree-content-label\"], [1, \"tui-tree-content-price\", \"ms-auto\"], [1, \"tui-tree-content-checkbox\", \"ms-2\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"checked\"]],\n      template: function TreeContentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"i\", 1);\n          i0.ɵɵelementStart(2, \"span\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"span\", 3);\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"currency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"input\", 5);\n          i0.ɵɵlistener(\"change\", function TreeContentComponent_Template_input_change_8_listener($event) {\n            return ctx.onCheckboxChange($event);\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", ctx.icon);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.context.$implicit.text);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 4, ctx.context.$implicit.price, \"EUR\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"checked\", ctx.context.$implicit.selected);\n        }\n      },\n      dependencies: [NgClass, CurrencyPipe],\n      styles: [\".tui-tree-content[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      padding: 8px 12px;\\n      border-radius: 4px;\\n      transition: background-color 0.2s;\\n    }\\n    \\n    .tui-tree-content[_ngcontent-%COMP%]:hover {\\n      background-color: #f8f9fa;\\n    }\\n    \\n    .tui-tree-content-label[_ngcontent-%COMP%] {\\n      flex: 1;\\n    }\\n    \\n    .tui-tree-content-price[_ngcontent-%COMP%] {\\n      font-weight: bold;\\n      color: #28a745;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9jcHEtY29uZmlndXJhdG9yL3RyZWUtY29udGVudC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJO01BQ0UsYUFBYTtNQUNiLG1CQUFtQjtNQUNuQixpQkFBaUI7TUFDakIsa0JBQWtCO01BQ2xCLGlDQUFpQztJQUNuQzs7SUFFQTtNQUNFLHlCQUF5QjtJQUMzQjs7SUFFQTtNQUNFLE9BQU87SUFDVDs7SUFFQTtNQUNFLGlCQUFpQjtNQUNqQixjQUFjO0lBQ2hCIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLnR1aS10cmVlLWNvbnRlbnQge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBwYWRkaW5nOiA4cHggMTJweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4ycztcbiAgICB9XG4gICAgXG4gICAgLnR1aS10cmVlLWNvbnRlbnQ6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcbiAgICB9XG4gICAgXG4gICAgLnR1aS10cmVlLWNvbnRlbnQtbGFiZWwge1xuICAgICAgZmxleDogMTtcbiAgICB9XG4gICAgXG4gICAgLnR1aS10cmVlLWNvbnRlbnQtcHJpY2Uge1xuICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gICAgICBjb2xvcjogIzI4YTc0NTtcbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["Ng<PERSON><PERSON>", "C<PERSON><PERSON>cyPipe", "TuiTreeItemContent", "TreeContentComponent", "icon", "isExpandable", "onCheckboxChange", "event", "checked", "target", "context", "$implicit", "selected", "t", "selectors", "standalone", "features", "i0", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TreeContentComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TreeContentComponent_Template_input_change_8_listener", "$event", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate", "text", "ɵɵpipeBind2", "price", "styles", "changeDetection"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet_CRM\\apps\\AngularCPQapp\\src\\app\\components\\cpq-configurator\\tree-content.component.ts"], "sourcesContent": ["import { NgClass, CurrencyPipe } from '@angular/common';\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\nimport { TuiTreeItemContent } from '@taiga-ui/kit';\n\n@Component({\n  selector: 'app-tree-content',\n  standalone: true,\n  imports: [NgClass, CurrencyPipe],\n  template: `\n    <div class=\"tui-tree-content\">\n      <i \n        class=\"bi me-2\"\n        [ngClass]=\"icon\"\n      ></i>\n      <span class=\"tui-tree-content-label\">{{ context.$implicit.text }}</span>\n      <span class=\"tui-tree-content-price ms-auto\">{{ context.$implicit.price | currency:'EUR' }}</span>\n      <div class=\"tui-tree-content-checkbox ms-2\">\n        <input \n          type=\"checkbox\" \n          class=\"form-check-input\"\n          [checked]=\"context.$implicit.selected\"\n          (change)=\"onCheckboxChange($event)\"\n        >\n      </div>\n    </div>\n  `,\n  styles: [`\n    .tui-tree-content {\n      display: flex;\n      align-items: center;\n      padding: 8px 12px;\n      border-radius: 4px;\n      transition: background-color 0.2s;\n    }\n    \n    .tui-tree-content:hover {\n      background-color: #f8f9fa;\n    }\n    \n    .tui-tree-content-label {\n      flex: 1;\n    }\n    \n    .tui-tree-content-price {\n      font-weight: bold;\n      color: #28a745;\n    }\n  `],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TreeContentComponent extends TuiTreeItemContent {\n  get icon(): string {\n    return this.isExpandable ? 'bi-folder-fill text-warning' : 'bi-file-earmark text-primary';\n  }\n\n  onCheckboxChange(event: Event): void {\n    const checked = (event.target as HTMLInputElement).checked;\n    // Émettre un événement personnalisé pour gérer la sélection\n    this.context.$implicit.selected = checked;\n  }\n}"], "mappings": "AAAA,SAASA,OAAO,EAAEC,YAAY,QAAQ,iBAAiB;AAEvD,SAASC,kBAAkB,QAAQ,eAAe;;AAgDlD,OAAM,MAAOC,oBAAqB,SAAQD,kBAAkB;EAC1D,IAAIE,IAAIA,CAAA;IACN,OAAO,IAAI,CAACC,YAAY,GAAG,6BAA6B,GAAG,8BAA8B;EAC3F;EAEAC,gBAAgBA,CAACC,KAAY;IAC3B,MAAMC,OAAO,GAAID,KAAK,CAACE,MAA2B,CAACD,OAAO;IAC1D;IACA,IAAI,CAACE,OAAO,CAACC,SAAS,CAACC,QAAQ,GAAGJ,OAAO;EAC3C;;;;;mHATWL,oBAAoB,IAAAU,CAAA,IAApBV,oBAAoB;MAAA;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAW,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,0BAAA,EAAAD,EAAA,CAAAE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzC7BR,EAAA,CAAAU,cAAA,aAA8B;UAC5BV,EAAA,CAAAW,SAAA,WAGK;UACLX,EAAA,CAAAU,cAAA,cAAqC;UAAAV,EAAA,CAAAY,MAAA,GAA4B;UAAAZ,EAAA,CAAAa,YAAA,EAAO;UACxEb,EAAA,CAAAU,cAAA,cAA6C;UAAAV,EAAA,CAAAY,MAAA,GAA8C;;UAAAZ,EAAA,CAAAa,YAAA,EAAO;UAEhGb,EADF,CAAAU,cAAA,aAA4C,eAMzC;UADCV,EAAA,CAAAc,UAAA,oBAAAC,sDAAAC,MAAA;YAAA,OAAUP,GAAA,CAAApB,gBAAA,CAAA2B,MAAA,CAAwB;UAAA,EAAC;UAGzChB,EAPI,CAAAa,YAAA,EAKC,EACG,EACF;;;UAZFb,EAAA,CAAAiB,SAAA,EAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,YAAAT,GAAA,CAAAtB,IAAA,CAAgB;UAEmBa,EAAA,CAAAiB,SAAA,GAA4B;UAA5BjB,EAAA,CAAAmB,iBAAA,CAAAV,GAAA,CAAAhB,OAAA,CAAAC,SAAA,CAAA0B,IAAA,CAA4B;UACpBpB,EAAA,CAAAiB,SAAA,GAA8C;UAA9CjB,EAAA,CAAAmB,iBAAA,CAAAnB,EAAA,CAAAqB,WAAA,OAAAZ,GAAA,CAAAhB,OAAA,CAAAC,SAAA,CAAA4B,KAAA,SAA8C;UAKvFtB,EAAA,CAAAiB,SAAA,GAAsC;UAAtCjB,EAAA,CAAAkB,UAAA,YAAAT,GAAA,CAAAhB,OAAA,CAAAC,SAAA,CAAAC,QAAA,CAAsC;;;qBAbpCZ,OAAO,EAAEC,YAAY;MAAAuC,MAAA;MAAAC,eAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}