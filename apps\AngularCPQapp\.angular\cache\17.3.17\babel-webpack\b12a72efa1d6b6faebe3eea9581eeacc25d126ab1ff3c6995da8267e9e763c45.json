{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Pi<PERSON> } from '@angular/core';\nimport { TUI_ASSETS_PATH } from '@taiga-ui/core/tokens';\n\n// TODO: Move to kit in v5\nclass TuiFlagPipe {\n  constructor() {\n    this.staticPath = inject(TUI_ASSETS_PATH);\n  }\n  transform(countryIsoCode) {\n    if (!countryIsoCode) {\n      return null;\n    }\n    return `${this.staticPath}/flags/${countryIsoCode.toLowerCase()}.svg`;\n  }\n  static {\n    this.ɵfac = function TuiFlagPipe_Factory(t) {\n      return new (t || TuiFlagPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiFlag\",\n      type: TuiFlagPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFlagPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiFlag'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFlagPipe };", "map": {"version": 3, "names": ["i0", "inject", "<PERSON><PERSON>", "TUI_ASSETS_PATH", "TuiFlagPipe", "constructor", "staticPath", "transform", "countryIsoCode", "toLowerCase", "ɵfac", "TuiFlagPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-pipes-flag.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Pi<PERSON> } from '@angular/core';\nimport { TUI_ASSETS_PATH } from '@taiga-ui/core/tokens';\n\n// TODO: Move to kit in v5\nclass TuiFlagPipe {\n    constructor() {\n        this.staticPath = inject(TUI_ASSETS_PATH);\n    }\n    transform(countryIsoCode) {\n        if (!countryIsoCode) {\n            return null;\n        }\n        return `${this.staticPath}/flags/${countryIsoCode.toLowerCase()}.svg`;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFlagPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFlagPipe, isStandalone: true, name: \"tuiFlag\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFlagPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiFlag',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFlagPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,IAAI,QAAQ,eAAe;AAC5C,SAASC,eAAe,QAAQ,uBAAuB;;AAEvD;AACA,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAGL,MAAM,CAACE,eAAe,CAAC;EAC7C;EACAI,SAASA,CAACC,cAAc,EAAE;IACtB,IAAI,CAACA,cAAc,EAAE;MACjB,OAAO,IAAI;IACf;IACA,OAAO,GAAG,IAAI,CAACF,UAAU,UAAUE,cAAc,CAACC,WAAW,CAAC,CAAC,MAAM;EACzE;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFR,WAAW;IAAA,CAA8C;EAAE;EACtK;IAAS,IAAI,CAACS,KAAK,kBAD8Eb,EAAE,CAAAc,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMZ,WAAW;MAAAa,IAAA;MAAAC,UAAA;IAAA,EAAwC;EAAE;AAClK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGnB,EAAE,CAAAoB,iBAAA,CAGXhB,WAAW,EAAc,CAAC;IAC1GY,IAAI,EAAEd,IAAI;IACVmB,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}