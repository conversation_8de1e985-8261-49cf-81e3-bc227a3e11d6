<div class="container-fluid">
  <!-- Indicateur de chargement -->
  <div *ngIf="isLoading" class="loading-overlay">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Chargement...</span>
    </div>
  </div>
  
  <!-- En-tête fixe sans animation -->
  <div class="row">
    <div class="col-12">
      <div class="cpq-header">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h2 class="mb-1">
                <i class="bi bi-gear-fill me-2"></i>
                Configurateur CPQ
              </h2>
              <p class="text-muted mb-0">Configurez votre devis personnalisé</p>
            </div>
            <div class="text-end">
              <span class="badge bg-primary fs-6">Étape {{ currentStep }}</span>
              <div class="text-muted small mt-1">Étape {{ currentStep }}/{{ maxSteps }}</div>
            </div>
          </div>
          
          <!-- Barre de progression -->
          <div class="progress mt-3">
            <div 
              class="progress-bar" 
              role="progressbar" 
              [style.width.%]="getProgressPercentage()">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- ÉTAPE 1: Sélection des familles -->
  <div class="row" *ngIf="currentStep >= 1">
    <div class="col-12">
      <div class="cpq-card">
        <div class="card-header">
          <h4 class="mb-1">
            <i class="bi bi-collection-fill me-2"></i>
            Sélectionnez les familles de produits
          </h4>
          <p class="mb-0">Choisissez une ou plusieurs familles dans la catégorie <strong class="text-white">{{ selectedCategory.name }}</strong></p>
        </div>
        <div class="card-body">
          
          <div class="row g-4 px-4 py-4">
            <div 
              *ngFor="let family of carFamilies" 
              class="col-lg-4 col-md-6">
              
              <div 
                class="card family-card"
                [class.selected]="isFamilySelected(family.id)"
                [class.disabled]="currentStep > 1"
                (click)="currentStep === 1 && toggleFamilySelection(family.id)">
                
                <div class="card-body">
                  <div class="d-flex align-items-start">
                    <div class="family-icon me-3">
                      <i [class]="family.icon"></i>
                    </div>
                    <div class="flex-grow-1">
                      <h6 class="card-title">{{ family.name }}</h6>
                      <p class="card-text">{{ family.description }}</p>
                      
                      <div class="d-flex justify-content-between align-items-center mt-3">
                        <span class="badge bg-secondary">
                          Famille de produits
                        </span>
                        <div class="form-check">
                          <input 
                            class="form-check-input" 
                            type="checkbox" 
                            [checked]="isFamilySelected(family.id)"
                            [disabled]="currentStep > 1"
                            (change)="currentStep === 1 && toggleFamilySelection(family.id)">
                          <label class="form-check-label">
                            Sélectionner
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Résumé des sélections -->
          <div *ngIf="selectedFamilyIds.length > 0" class="mt-4">
            <div class="alert alert-success">
              <i class="bi bi-check-circle me-2"></i>
              <strong>{{ selectedFamilyIds.length }}</strong> famille(s) sélectionnée(s) :
              <span *ngFor="let familyId of selectedFamilyIds; let last = last">
                <strong>{{ getFamilyName(familyId) }}</strong><span *ngIf="!last">, </span>
              </span>
            </div>
          </div>
        </div>
        
        <div class="card-footer" *ngIf="selectedFamilyIds.length > 0 && currentStep === 1">
          <div class="d-flex justify-content-end">
            <button 
              class="btn btn-primary btn-lg"
              (click)="nextStep()">
              Voir les produits ({{ selectedFamilyIds.length }})
              <i class="bi bi-arrow-right ms-2"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Séparateur -->
  <div class="step-separator text-center my-4" *ngIf="currentStep > 1">
    <i class="bi bi-arrow-down-circle-fill fs-1 text-primary"></i>
  </div>

    <!-- ÉTAPE 2: Sélection des produits -->
  <div class="card mb-4" *ngIf="currentStep >= 2">
    <div class="card-header">
      <h3><i class="bi bi-box-seam"></i> Sélection des produits</h3>
      <p class="mb-0">Choisissez les produits et leurs quantités</p>
    </div>
    
    <div class="card-body">
      <!-- Message d'erreur -->
      <div class="alert alert-danger" *ngIf="errorMessage">
        <i class="bi bi-exclamation-triangle"></i> {{ errorMessage }}
      </div>

      <!-- Message de succès -->
      <div class="alert alert-success" *ngIf="successMessage">
        <i class="bi bi-check-circle"></i> {{ successMessage }}
      </div>

      <!-- Visible à l'étape 2 et au-delà -->
      <div *ngIf="currentStep >= 2">
        
        <!-- CARD PRODUITS DISPONIBLES -->
        <div class="card product-selection-card mb-4">
          <div class="card-header bg-info text-white">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="mb-0">
                  <i class="bi bi-search me-2"></i>
                  Produits disponibles
                </h5>
                <small>Sélectionnez les produits que vous souhaitez ajouter</small>
              </div>
              <div class="d-flex align-items-center gap-2">
                <span class="badge bg-light text-dark">
                  {{ pagination.totalItems }} produit(s)
                </span>
                <button 
                  class="btn btn-sm btn-outline-light"
                  (click)="clearAllFilters()"
                  *ngIf="productFilters.searchTerm || productFilters.familyFilter !== 'all' || productFilters.typeFilter !== 'all'">
                  <i class="bi bi-x-circle"></i> Effacer filtres
                </button>
              </div>
            </div>
          </div>

          <div class="card-body">
            <!-- Barre de recherche et filtres -->
            <div class="search-filters-section mb-4">
              <div class="row g-3">
                <!-- Barre de recherche -->
                <div class="col-md-4">
                  <div class="search-input-group position-relative">
                    <span class="position-absolute top-50 start-0 translate-middle-y ms-3">
                      <i class="bi bi-search"></i>
                    </span>
                    <input 
                      type="text" 
                      class="form-control search-input ps-5" 
                      placeholder="Rechercher un produit..."
                      [(ngModel)]="productFilters.searchTerm"
                      (input)="onFilterChange()">
                    <button 
                      class="btn btn-outline-secondary clear-search-btn position-absolute top-50 end-0 translate-middle-y me-2"
                      *ngIf="productFilters.searchTerm"
                      (click)="productFilters.searchTerm = ''; onFilterChange()">
                      <i class="bi bi-x"></i>
                    </button>
                  </div>
                </div>

                <!-- Filtre par famille -->
                <div class="col-md-3">
                  <select 
                    class="form-select filter-select"
                    [(ngModel)]="productFilters.familyFilter"
                    (change)="onFilterChange()">
                    <option value="all">Toutes les familles</option>
                    <option 
                      *ngFor="let family of getAvailableFamiliesForFilter()" 
                      [value]="family.id">
                      {{ family.name }}
                    </option>
                  </select>
                </div>

                <!-- Filtre par type -->
                <div class="col-md-3">
                  <select 
                    class="form-select filter-select"
                    [(ngModel)]="productFilters.typeFilter"
                    (change)="onFilterChange()">
                    <option value="all">Tous les types</option>
                    <option value="main">Produits principaux</option>
                    <option value="option">Options</option>
                  </select>
                </div>

                <!-- Pagination par page -->
                <div class="col-md-2">
                  <select 
                    class="form-select pagination-select"
                    [(ngModel)]="pagination.itemsPerPage"
                    (change)="onFilterChange()">
                    <option [value]="25">25 par page</option>
                    <option [value]="50">50 par page</option>
                    <option [value]="75">75 par page</option>
                    <option [value]="0">Tout afficher</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Liste des produits disponibles -->
            <div class="products-grid">
              <div class="row g-3">
                <div class="col-lg-6 col-xl-4 mb-3" *ngFor="let product of filteredProducts">
                  <div class="card product-card h-100" 
                       [class.temp-selected]="isTempProductSelected(product)"
                       [class.confirmed]="isProductConfirmed(product)">
                    <div class="card-body d-flex flex-column">
                      <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="product-icon">
                          <i class="bi bi-box-seam fs-2"></i>
                        </div>
                        <div class="product-status-indicators">
                          <span class="badge bg-success me-1" *ngIf="isProductConfirmed(product)">
                            <i class="bi bi-check-circle"></i> Confirmé
                          </span>
                          <span class="badge bg-warning" *ngIf="isTempProductSelected(product) && !isProductConfirmed(product)">
                            <i class="bi bi-clock"></i> Sélectionné
                          </span>
                        </div>
                      </div>
                      
                      <h6 class="card-title fw-bold">{{ product.name }}</h6>
                      <p class="card-text text-muted small mb-1">
                        <i class="bi bi-hash"></i> {{ product.productid }}
                      </p>
                      <p class="card-text flex-grow-1 product-description">{{ product.description }}</p>
                      
                      <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                          <span class="badge product-type-badge" 
                                [ngClass]="{
                                  'bg-primary': !product.parentproductid,
                                  'bg-info': product.parentproductid
                                }">
                            {{ product.parentproductid ? 'Option' : 'Principal' }}
                          </span>
                          <strong class="text-success fs-6 product-price">{{ formatPrice((product.price || 0) + getPropertiesPriceForProduct(product.productid)) }}</strong>
                        </div>
                        
                        <div class="d-grid gap-2">
                          <button 
                            class="btn btn-sm product-action-btn"
                            [ngClass]="{
                              'btn-outline-primary': !isTempProductSelected(product) && !isProductConfirmed(product),
                              'btn-warning': isTempProductSelected(product) && !isProductConfirmed(product),
                              'btn-success': isProductConfirmed(product)
                            }"
                            (click)="toggleTempProductSelection(product)"
                            [disabled]="isProductConfirmed(product) || currentStep > 2">
                            <i class="bi" 
                               [ngClass]="{
                                 'bi-plus-lg': !isTempProductSelected(product) && !isProductConfirmed(product),
                                 'bi-dash-lg': isTempProductSelected(product) && !isProductConfirmed(product),
                                 'bi-check-lg': isProductConfirmed(product)
                               }"></i>
                            <span *ngIf="!isTempProductSelected(product) && !isProductConfirmed(product)">Sélectionner</span>
                            <span *ngIf="isTempProductSelected(product) && !isProductConfirmed(product)">Désélectionner</span>
                            <span *ngIf="isProductConfirmed(product)">Confirmé</span>
                          </button>
                          
                          <!-- Bouton pour voir les produits associés -->
                          <button 
                            *ngIf="hasDirectChildren(product.productid)"
                            class="btn btn-sm btn-outline-info mt-2 w-100"
                            (click)="showAssociatedProducts(product)">
                            <i class="bi bi-diagram-3 me-1"></i> Voir les produits associés
                          </button>
                          
                          <!-- Nous supprimons l'accordéon car nous utiliserons un modal à la place -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Message si aucun produit -->
              <div *ngIf="filteredProducts.length === 0" class="empty-state">
                <div class="empty-state-icon">
                  <i class="bi bi-inbox"></i>
                </div>
                <div class="empty-state-title">Aucun produit trouvé</div>
                <div class="empty-state-text">
                  Essayez de modifier vos critères de recherche ou vos filtres
                </div>
              </div>
            </div>

            <!-- Pagination -->
            <div class="pagination-section mt-4" *ngIf="getTotalPages() > 1">
              <nav aria-label="Navigation des produits">
                <ul class="pagination justify-content-center">
                  <li class="page-item" [class.disabled]="pagination.currentPage === 1">
                    <button class="page-link" (click)="onPageChange(pagination.currentPage - 1)">
                      <i class="bi bi-chevron-left"></i>
                    </button>
                  </li>
                  
                  <li class="page-item" 
                      *ngFor="let page of getPageNumbers()"
                      [class.active]="page === pagination.currentPage"
                      [class.disabled]="page === -1">
                    <button class="page-link" 
                            *ngIf="page !== -1"
                            (click)="onPageChange(page)">
                      {{ page }}
                    </button>
                    <span class="page-link" *ngIf="page === -1">
                                            ...
                    </span>
                  </li>
                  
                  <li class="page-item" [class.disabled]="pagination.currentPage === getTotalPages()">
                    <button class="page-link" (click)="onPageChange(pagination.currentPage + 1)">
                      <i class="bi bi-chevron-right"></i>
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>

        <!-- La carte "Sélection temporaire" a été supprimée -->

        <!-- CARD PANIER -->
        <div class="card confirmed-products-card mb-4">
          <div class="card-header bg-success text-white">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="mb-0">
                  <i class="bi bi-cart-check me-2"></i>
                  Panier
                </h5>
                <small>Gérez vos produits sélectionnés, leurs propriétés et quantités</small>
              </div>
              <div class="d-flex align-items-center gap-2">
                <span class="badge bg-light text-dark">
                  {{ confirmedSelectedProducts.length }} produit(s)
                </span>
                <button 
                  class="btn btn-sm btn-outline-light"
                  (click)="toggleAllProductsExpansion()"
                  *ngIf="confirmedSelectedProducts.length > 0">
                  <i class="bi" [ngClass]="{
                    'bi-arrows-expand': expandedProducts.size === 0,
                    'bi-arrows-collapse': expandedProducts.size > 0
                  }"></i>
                  {{ expandedProducts.size === 0 ? 'Tout étendre' : 'Tout réduire' }}
                </button>
              </div>
            </div>
          </div>
          
          <div class="card-body">
            <div *ngIf="confirmedSelectedProducts.length === 0" class="empty-state">
              <div class="empty-state-icon">
                <i class="bi bi-cart-x"></i>
              </div>
              <div class="empty-state-title">Votre panier est vide</div>
              <div class="empty-state-text">Sélectionnez des produits pour les ajouter à votre panier</div>
            </div>

            <div *ngIf="confirmedSelectedProducts.length > 0">
              <!-- Utilisation du composant récursif pour chaque produit dans le panier -->
              <div *ngFor="let selection of confirmedSelectedProducts; let i = index" class="mb-3">
                <app-product-tree
                  [product]="selection.product"
                  [level]="0"
                  [isSelected]="true"
                  [isMainProduct]="true"
                  [isIncluded]="false"
                  [isExpanded]="isProductExpanded(selection.product.productid)"
                  [quantity]="selection.quantity"
                  [directChildren]="[]"
                  [hasChildren]="hasDirectChildren(selection.product.productid)"
                  [hasSelectedProperties]="hasProductProperties(selection.product.productid)"
                  [selectedPropertiesCount]="getSelectedPropertiesCountForProduct(selection.product.productid)"
                  [freeProperties]="getProductFreeProperties(selection.product.productid)"
                  [paidProperties]="getProductPaidProperties(selection.product.productid)"
                  [requiredProperties]="getProductRequiredProperties(selection.product.productid)"
                  [isLocked]="currentStep > 2"
                  (productToggle)="removeConfirmedProduct($event)"
                  (productInclusionToggle)="toggleProductInclusion($event)"
                  (quantityUpdate)="updateProductQuantity($event.productId, $event.event)"
                  (quantityIncrease)="increaseQuantity($event)"
                  (quantityDecrease)="decreaseQuantity($event)"
                  (expansionToggle)="toggleProductExpansion($event)"
                  (editProperties)="openPropertiesModal($event)">
                </app-product-tree>
                
                <hr *ngIf="i < confirmedSelectedProducts.length - 1">
              </div>
            </div>
          </div>
          
          <div class="card-footer" *ngIf="confirmedSelectedProducts.length > 0">
            <div class="d-flex justify-content-between align-items-center">
              <div class="confirmed-products-summary">
                <span class="text-muted">
                  {{ confirmedSelectedProducts.length }} produit(s) • 
                  {{ getTotalConfirmedQuantity() }} article(s) • 
                  <strong class="text-success">{{ formatPrice(totalPrice) }}</strong>
                </span>
              </div>
              <div class="d-flex gap-2">
                <button 
                  class="btn btn-outline-danger btn-sm" 
                  (click)="clearAllConfirmedProducts()"
                  [disabled]="currentStep > 2">
                  <i class="bi bi-trash"></i> Tout supprimer
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions de navigation -->
        <div class="step-navigation-actions">
          <div class="d-flex justify-content-between align-items-center">
            <button 
              class="btn btn-outline-secondary" 
              (click)="previousStep()"
              *ngIf="currentStep === 2">
              <i class="bi bi-arrow-left me-2"></i> Retour aux familles
            </button>
            <div *ngIf="currentStep > 2"></div> <!-- Espace vide pour maintenir l'alignement -->
            
            <div class="step-info d-flex align-items-center gap-3">
              <div class="selection-summary">
                <span class="badge bg-info me-2">{{ confirmedSelectedProducts.length }} produits</span>
                <span class="text-muted">Total: <strong>{{ formatPrice(totalPrice) }}</strong></span>
              </div>
              
              <button 
                class="btn btn-primary btn-lg"
                [disabled]="confirmedSelectedProducts.length === 0"
                (click)="nextStep()"
                *ngIf="currentStep === 2">
                Suivant
                <i class="bi bi-arrow-right ms-2"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>



  <!-- Alerte pour les propriétés requises non sélectionnées (affichée à l'étape 2) -->
  <div *ngIf="currentStep === 2 && !areAllRequiredPropertiesSelected() && getMissingRequiredProperties().length > 0" class="alert alert-warning mb-4">
    <div class="d-flex align-items-center">
      <i class="bi bi-exclamation-triangle-fill fs-4 me-2"></i>
      <div>
        <h5 class="mb-1">Propriétés requises manquantes</h5>
        <p class="mb-0">Certaines propriétés requises n'ont pas été sélectionnées. Utilisez le bouton "Propriétés" sur chaque produit pour les configurer.</p>
      </div>
    </div>
  </div>

  <!-- Séparateur -->
  <div class="step-separator text-center my-4" *ngIf="currentStep > 2">
    <i class="bi bi-arrow-down-circle-fill fs-1 text-primary"></i>
  </div>

    <!-- ÉTAPE 3: Récapitulatif -->
  <div class="card mb-4" *ngIf="currentStep >= 3">
    <div class="card-header">
      <h3><i class="bi bi-clipboard-check"></i> Récapitulatif</h3>
      <p class="mb-0">Vérifiez votre configuration avant de générer le devis</p>
    </div>
    
    <div class="card-body">
      <!-- Résumé des familles -->
      <div class="mb-4">
        <h5>Familles sélectionnées</h5>
        <div class="d-flex flex-wrap gap-2">
          <span 
            *ngFor="let familyId of selectedFamilyIds" 
            class="badge bg-primary fs-6">
            {{ getFamilyName(familyId) }}
          </span>
        </div>
      </div>

      <!-- Récapitulatif des prix -->
      <div class="mb-4">
        <div id="price-summary-card" class="card bg-light">
          <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-calculator me-2"></i>Récapitulatif des prix</h5>
            <button class="btn btn-sm btn-outline-secondary" (click)="togglePricePanel()">
              <i class="bi" [ngClass]="isPricePanelExpanded ? 'bi-chevron-up' : 'bi-chevron-down'"></i>
            </button>
          </div>
          <div class="card-body" *ngIf="isPricePanelExpanded">
            <div class="row">
              <div class="col-md-8">
                <div class="price-breakdown-details">
                  <div class="d-flex justify-content-between py-2 border-bottom">
                    <span><i class="bi bi-folder me-2"></i>Produits de base:</span>
                    <span class="fw-bold">{{ formatPrice(getMainProductsTotal()) }}</span>
                  </div>
                  <div class="d-flex justify-content-between py-2 border-bottom">
                    <span><i class="bi bi-gear me-2"></i>personnalisations:</span>
                    <span class="fw-bold">{{ formatPrice(propertiesPrice) }}</span>
                  </div>
                  <div class="d-flex justify-content-between py-2 border-bottom" *ngIf="getOptionsTotal() > 0">
                    <span><i class="bi bi-wrench me-2"></i>Options:</span>
                    <span class="fw-bold">{{ formatPrice(getOptionsTotal()) }}</span>
                  </div>
                  <div class="d-flex justify-content-between py-2">
                    <span><i class="bi bi-calculator me-2"></i>Quantité totale:</span>
                    <span class="fw-bold">{{ getTotalConfirmedQuantity() }} article(s)</span>
                  </div>
                </div>
                
                <!-- TreeTable pour les produits et leurs options -->
                <div class="mt-4">
                  <h6 class="mb-3">Détail des produits et options</h6>
                  <table class="table table-sm table-bordered tree-table">
                    <thead class="table-light">
                      <tr>
                        <th>Produit</th>
                        <th>Quantité</th>
                        <th>Prix unitaire</th>
                        <th>Options</th>
                        <th>Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      <!-- Produits organisés hiérarchiquement -->
                      <ng-container *ngFor="let item of getHierarchicalProductsForTable()">
                        <tr [class]="'level-' + item.level">
                          <td>
                            <div class="d-flex align-items-center">
                              <span [style.paddingLeft.px]="item.level * 20">
                                <!-- Flèche d'expansion si le produit a des enfants -->
                                <i *ngIf="item.hasChildren" 
                                   class="bi expand-icon me-1" 
                                   [ngClass]="item.expanded ? 'bi-chevron-down' : 'bi-chevron-right'"
                                   (click)="toggleTableRowExpansion(item.selection.product.productid)"></i>
                                <!-- Espace pour aligner les produits sans enfants -->
                                <span *ngIf="!item.hasChildren" class="expand-placeholder me-1">&nbsp;&nbsp;</span>
                                
                                <i class="bi" [ngClass]="{
                                  'bi-folder-fill': item.selection.selectionSource === 'MAIN_LIST',
                                  'bi-gear-fill': item.selection.selectionSource === 'TREE_INCLUSION'
                                }"></i>
                                {{ item.selection.product.name }}
                              </span>
                            </div>
                          </td>
                          <td class="text-center">{{ item.selection.quantity }}</td>
                          <td class="text-end">{{ formatPrice(item.selection.product.price || 0) }}</td>
                          <td class="text-end">
                            {{ formatPrice(getPropertiesPriceForProduct(item.selection.product.productid)) }}
                          </td>
                          <td class="text-end fw-bold">
                            {{ formatPrice((item.selection.product.price || 0) * item.selection.quantity + 
                               getPropertiesPriceForProduct(item.selection.product.productid) * item.selection.quantity + 
                               (item.childrenPrice || 0)) }}
                          </td>
                        </tr>
                      </ng-container>
                      <!-- Ligne de total -->
                      <tr class="table-success">
                        <td colspan="4" class="text-end fw-bold">Total général:</td>
                        <td class="text-end fw-bold">{{ formatPrice(totalPrice) }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <div class="col-md-4">
                <div class="final-total-box">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <strong class="fs-4">Total général:</strong>
                    <strong class="text-success fs-3">{{ formatPrice(totalPrice) }}</strong>
                  </div>
                  <div class="text-center">
                    <small class="text-muted">TVA incluse • {{ confirmedSelectedProducts.length }} produit(s)</small>
                  </div>
                  <div class="text-center mt-2">
                    <small class="badge bg-info">Configuration validée</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions finales -->
      <div class="d-flex justify-content-between">
        <button class="btn btn-outline-secondary" (click)="previousStep()" *ngIf="currentStep === 3">
          <i class="bi bi-arrow-left"></i> Retour à la sélection
        </button>
        <div *ngIf="currentStep > 3"></div> <!-- Espace vide pour maintenir l'alignement -->
        <div class="d-flex gap-2">
          <button class="btn btn-outline-danger" (click)="showResetConfirmation()">
            <i class="bi bi-arrow-counterclockwise"></i> Réinitialiser
          </button>
          <button class="btn btn-outline-info download-btn" (click)="exportPriceSummaryToPdf()">
            <i class="bi bi-file-pdf"></i> Télécharger en PDF
          </button>
          <button class="btn btn-outline-primary">
            <i class="bi bi-save"></i> Sauvegarder le brouillon
          </button>
          <button class="btn btn-success btn-lg">
            <i class="bi bi-file-earmark-text"></i> Générer le devis final
          </button>
        </div>
      </div>
    </div>
  </div>

    <!-- Modal de confirmation pour les conflits parent-enfant -->
    <div class="modal fade" id="parentChildConflictModal" tabindex="-1" aria-labelledby="conflictModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-warning text-dark">
            <h5 class="modal-title" id="conflictModalLabel">
              <i class="bi bi-exclamation-triangle me-2"></i>
              Conflit de hiérarchie détecté
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fermer"></button>
          </div>
          <div class="modal-body">
            <p>
              Vous tentez de sélectionner <strong>{{ conflictProduct?.name }}</strong> qui fait partie d'une hiérarchie de produits.
            </p>
            <div class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i>
              En confirmant, tous les produits enfants de cette hiérarchie seront désélectionnés et masqués.
            </div>
            <div *ngIf="selectedChildrenProducts.length > 0" class="mt-3">
              <h6 class="text-danger"><i class="bi bi-exclamation-circle me-2"></i>Produits qui seront désélectionnés :</h6>
              <ul class="list-group mt-2">
                <li *ngFor="let selection of selectedChildrenProducts" class="list-group-item list-group-item-danger">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <strong>{{ selection.product.name }}</strong>
                      <small class="d-block text-muted">{{ selection.product.productid }}</small>
                    </div>
                    <span class="badge bg-secondary">{{ formatPrice(selection.product.price || 0) }}</span>
                  </div>
                </li>
              </ul>
            </div>
            <p class="mt-3 mb-0">Voulez-vous continuer ?</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annuler</button>
            <button type="button" class="btn btn-warning" (click)="confirmParentChildConflict()" data-bs-dismiss="modal">
              <i class="bi bi-check-circle me-2"></i>
              Confirmer et continuer
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Modal pour éditer les propriétés d'un produit -->
    <div class="modal fade" id="propertiesModal" tabindex="-1" aria-labelledby="propertiesModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
          <div class="modal-header bg-primary text-white">
            <h5 class="modal-title" id="propertiesModalLabel">
              <i class="bi bi-pencil-square me-2"></i>
              Propriétés du produit: {{ selectedProductForProperties?.name }}
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fermer"></button>
          </div>
          <div class="modal-body">
            <div *ngIf="selectedProductForProperties">
              <!-- Onglets pour les catégories de propriétés -->
              <ul class="nav nav-tabs mb-3" id="propertiesTabs" role="tablist">
                <li class="nav-item" role="presentation">
                  <button class="nav-link active" id="free-tab" data-bs-toggle="tab" data-bs-target="#free-properties" type="button" role="tab" aria-controls="free-properties" aria-selected="true">
                    <i class="bi bi-gift me-1 text-success"></i> Propriétés gratuites
                  </button>
                </li>
                <li class="nav-item" role="presentation">
                  <button class="nav-link" id="paid-tab" data-bs-toggle="tab" data-bs-target="#paid-properties" type="button" role="tab" aria-controls="paid-properties" aria-selected="false">
                    <i class="bi bi-currency-euro me-1 text-warning"></i> Propriétés payantes
                  </button>
                </li>
                <li class="nav-item" role="presentation">
                  <button class="nav-link" id="required-tab" data-bs-toggle="tab" data-bs-target="#required-properties" type="button" role="tab" aria-controls="required-properties" aria-selected="false">
                    <i class="bi bi-asterisk me-1 text-danger"></i> Propriétés requises
                  </button>
                </li>
              </ul>
              
              <!-- Contenu des onglets -->
              <div class="tab-content" id="propertiesTabsContent">
                <!-- Propriétés gratuites -->
                <div class="tab-pane fade show active" id="free-properties" role="tabpanel" aria-labelledby="free-tab">
                  <div class="row g-3">
                    <div *ngFor="let propertySelection of getGroupedPropertiesForProduct(selectedProductForProperties.productid).free" 
                         class="col-md-6">
                      <div class="card property-option-card h-100"
                           [class.selected]="propertySelection.selected"
                           [class.required-property]="propertySelection.property?.isrequired"
                           (click)="togglePropertySelection(selectedProductForProperties.productid, propertySelection.propertyid)">
                        <div class="card-body">
                          <div class="d-flex align-items-start">
                            <div class="property-option-icon me-3">
                              <i class="bi" 
                                 [ngClass]="{
                                   'bi-palette': propertySelection.property?.propertytype === 1,
                                   'bi-circle': propertySelection.property?.propertytype === 2,
                                   'bi-texture': propertySelection.property?.propertytype === 3,
                                   'bi-star': propertySelection.property?.propertytype === 4
                                 }"></i>
                            </div>
                            <div class="flex-grow-1">
                              <h6 class="property-option-title">
                                {{ propertySelection.property?.name }}
                                <span *ngIf="propertySelection.property?.isrequired" class="required-indicator">*</span>
                              </h6>
                              <p class="property-option-description small text-muted mb-2">
                                {{ propertySelection.property?.description }}
                              </p>
                              <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-success">Inclus</span>
                                <div class="form-check">
                                  <input 
                                    class="form-check-input" 
                                    type="radio" 
                                    [name]="'modal-property-' + propertySelection.property?.propertytype + '-' + selectedProductForProperties.productid"
                                    [checked]="propertySelection.selected"
                                    (change)="togglePropertySelection(selectedProductForProperties.productid, propertySelection.propertyid)">
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Message si aucune propriété gratuite -->
                    <div *ngIf="getGroupedPropertiesForProduct(selectedProductForProperties.productid).free.length === 0" class="col-12">
                      <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Aucune propriété gratuite disponible pour ce produit.
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Propriétés payantes -->
                <div class="tab-pane fade" id="paid-properties" role="tabpanel" aria-labelledby="paid-tab">
                  <div class="row g-3">
                    <div *ngFor="let propertySelection of getGroupedPropertiesForProduct(selectedProductForProperties.productid).paid" 
                         class="col-md-6">
                      <div class="card property-option-card h-100"
                           [class.selected]="propertySelection.selected"
                           [class.required-property]="propertySelection.property?.isrequired"
                           (click)="togglePropertySelection(selectedProductForProperties.productid, propertySelection.propertyid)">
                        <div class="card-body">
                          <div class="d-flex align-items-start">
                            <div class="property-option-icon me-3">
                              <i class="bi" 
                                 [ngClass]="{
                                   'bi-palette': propertySelection.property?.propertytype === 1,
                                   'bi-circle': propertySelection.property?.propertytype === 2,
                                   'bi-texture': propertySelection.property?.propertytype === 3,
                                   'bi-star': propertySelection.property?.propertytype === 4
                                 }"></i>
                            </div>
                            <div class="flex-grow-1">
                              <h6 class="property-option-title">
                                {{ propertySelection.property?.name }}
                                <span *ngIf="propertySelection.property?.isrequired" class="required-indicator">*</span>
                              </h6>
                              <p class="property-option-description small text-muted mb-2">
                                {{ propertySelection.property?.description }}
                              </p>
                              <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-warning text-dark">
                                  {{ formatPrice(propertySelection.property?.price || 0) }}
                                </span>
                                <div class="form-check">
                                  <input 
                                    class="form-check-input" 
                                    [type]="propertySelection.property?.isexclusive ? 'radio' : 'checkbox'"
                                    [name]="propertySelection.property?.isexclusive ? 'modal-property-' + propertySelection.property?.propertytype + '-' + selectedProductForProperties.productid : ''"
                                    [checked]="propertySelection.selected"
                                    (change)="togglePropertySelection(selectedProductForProperties.productid, propertySelection.propertyid)">
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Message si aucune propriété payante -->
                    <div *ngIf="getGroupedPropertiesForProduct(selectedProductForProperties.productid).paid.length === 0" class="col-12">
                      <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Aucune propriété payante disponible pour ce produit.
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Propriétés requises -->
                <div class="tab-pane fade" id="required-properties" role="tabpanel" aria-labelledby="required-tab">
                  <div class="row g-3">
                    <div *ngFor="let propertySelection of getRequiredPropertiesForProduct(selectedProductForProperties.productid)" 
                         class="col-md-6">
                      <div class="card property-option-card h-100 required-property"
                           [class.selected]="propertySelection.selected">
                        <div class="card-body">
                          <div class="d-flex align-items-start">
                            <div class="property-option-icon me-3 bg-danger text-white">
                              <i class="bi bi-asterisk"></i>
                            </div>
                            <div class="flex-grow-1">
                              <h6 class="property-option-title">
                                {{ propertySelection.property?.name }}
                                <span class="required-indicator">*</span>
                              </h6>
                              <p class="property-option-description small text-muted mb-2">
                                {{ propertySelection.property?.description }}
                              </p>
                              <div class="d-flex justify-content-between align-items-center">
                                <span class="badge" [ngClass]="{
                                  'bg-success': propertySelection.property?.price === 0,
                                  'bg-warning text-dark': propertySelection.property?.price! > 0
                                }">
                                  {{ propertySelection.property?.price === 0 ? 'Inclus' : formatPrice(propertySelection.property?.price || 0) }}
                                </span>
                                <div class="form-check">
                                  <input 
                                    class="form-check-input" 
                                    type="checkbox"
                                    [checked]="true"
                                    disabled>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Message si aucune propriété requise -->
                    <div *ngIf="getRequiredPropertiesForProduct(selectedProductForProperties.productid).length === 0" class="col-12">
                      <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Aucune propriété requise pour ce produit.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Résumé des propriétés sélectionnées -->
              <div class="mt-4 pt-3 border-top">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <h6 class="mb-0">Propriétés sélectionnées</h6>
                    <p class="text-muted small mb-0">
                      {{ getSelectedPropertiesCountForProduct(selectedProductForProperties.productid) }} propriété(s) sélectionnée(s)
                    </p>
                  </div>
                  <div class="text-end">
                    <span class="badge bg-success fs-6">{{ formatPrice(getPropertiesPriceForProduct(selectedProductForProperties.productid)) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Fermer</button>
            <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
              <i class="bi bi-check-circle me-2"></i>
              Confirmer les propriétés
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Modal de confirmation pour la réinitialisation -->
    <div class="modal fade" id="resetConfirmationModal" tabindex="-1" aria-labelledby="resetConfirmationModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title" id="resetConfirmationModalLabel">
              <i class="bi bi-exclamation-triangle me-2"></i>
              Confirmation de réinitialisation
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fermer"></button>
          </div>
          <div class="modal-body">
            <p>
              Êtes-vous sûr de vouloir réinitialiser complètement la configuration ?
            </p>
            <div class="alert alert-warning">
              <i class="bi bi-info-circle me-2"></i>
              Cette action supprimera toutes vos sélections et vous ramènera à l'étape 1. Cette action est irréversible.
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annuler</button>
            <button type="button" class="btn btn-danger" (click)="resetConfiguration()" data-bs-dismiss="modal">
              <i class="bi bi-arrow-counterclockwise me-2"></i>
              Réinitialiser
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Modal pour afficher les produits associés avec Taiga UI -->
    <div class="modal fade" id="associatedProductsModal" tabindex="-1" aria-labelledby="associatedProductsModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
          <div class="modal-header bg-info text-white">
            <h5 class="modal-title" id="associatedProductsModalLabel">
              <i class="bi bi-diagram-3 me-2"></i>
              Produits associés à {{ currentRootProduct?.name }}
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fermer"></button>
          </div>
          <div class="modal-body">
            <div *ngIf="currentRootProduct" class="associated-products-tree">
              <!-- Arbre Taiga UI -->
              <tui-tree
                *ngFor="let node of treeData"
                [value]="node"
                [childrenHandler]="treeHandler"
                [tuiTreeController]="true"
              ></tui-tree>
              
              <!-- Instructions pour l'utilisateur -->
              <div class="alert alert-info mt-3">
                <i class="bi bi-info-circle me-2"></i>
                <strong>Comment utiliser :</strong> Cochez les cases à côté des produits que vous souhaitez sélectionner. 
                Cliquez sur les flèches pour étendre ou réduire les catégories.
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Fermer</button>
            <button type="button" class="btn btn-primary" (click)="confirmAssociatedProducts()" data-bs-dismiss="modal">
              <i class="bi bi-check-circle me-2"></i>
              Confirmer la sélection
            </button>
          </div>
        </div>
      </div>
    </div>





