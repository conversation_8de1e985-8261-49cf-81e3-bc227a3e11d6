{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { signal, Optional, SkipSelf, inject, Directive, Input, effect } from '@angular/core';\nimport { tuiCreateToken, tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_DEFAULT_IDENTITY_MATCHER, TUI_FALSE_HANDLER, TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { NG_VALIDATORS } from '@angular/forms';\nimport { TuiValidator } from '@taiga-ui/cdk/directives/validator';\nconst TUI_DEFAULT_ITEMS_HANDLERS = {\n  stringify: signal(String),\n  identityMatcher: signal(TUI_DEFAULT_IDENTITY_MATCHER),\n  disabledItemHandler: signal(TUI_FALSE_HANDLER)\n};\n/**\n * Default items handlers for components\n */\nconst TUI_ITEMS_HANDLERS = tuiCreateToken(TUI_DEFAULT_ITEMS_HANDLERS);\nfunction tuiItemsHandlersProvider(options) {\n  return {\n    provide: TUI_ITEMS_HANDLERS,\n    deps: [[new Optional(), new SkipSelf(), TUI_ITEMS_HANDLERS]],\n    useFactory: parent => ({\n      stringify: signal(parent?.stringify() ?? TUI_DEFAULT_ITEMS_HANDLERS.stringify()),\n      identityMatcher: signal(parent?.identityMatcher() ?? TUI_DEFAULT_ITEMS_HANDLERS.identityMatcher()),\n      disabledItemHandler: signal(parent?.disabledItemHandler() ?? TUI_DEFAULT_ITEMS_HANDLERS.disabledItemHandler()),\n      ...options\n    })\n  };\n}\nclass TuiItemsHandlersDirective {\n  constructor() {\n    this.defaultHandlers = inject(TUI_ITEMS_HANDLERS, {\n      skipSelf: true\n    });\n    this.stringify = signal(this.defaultHandlers.stringify());\n    this.identityMatcher = signal(this.defaultHandlers.identityMatcher());\n    this.disabledItemHandler = signal(this.defaultHandlers.disabledItemHandler());\n  }\n  // TODO(v5): use signal inputs\n  set stringifySetter(x) {\n    this.stringify.set(x);\n  }\n  // TODO(v5): use signal inputs\n  set identityMatcherSetter(x) {\n    this.identityMatcher.set(x);\n  }\n  // TODO(v5): use signal inputs\n  set disabledItemHandlerSetter(x) {\n    this.disabledItemHandler.set(x);\n  }\n  static {\n    this.ɵfac = function TuiItemsHandlersDirective_Factory(t) {\n      return new (t || TuiItemsHandlersDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiItemsHandlersDirective,\n      inputs: {\n        stringifySetter: [i0.ɵɵInputFlags.None, \"stringify\", \"stringifySetter\"],\n        identityMatcherSetter: [i0.ɵɵInputFlags.None, \"identityMatcher\", \"identityMatcherSetter\"],\n        disabledItemHandlerSetter: [i0.ɵɵInputFlags.None, \"disabledItemHandler\", \"disabledItemHandlerSetter\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TUI_ITEMS_HANDLERS, TuiItemsHandlersDirective)])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiItemsHandlersDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      providers: [tuiProvide(TUI_ITEMS_HANDLERS, TuiItemsHandlersDirective)]\n    }]\n  }], null, {\n    stringifySetter: [{\n      type: Input,\n      args: ['stringify']\n    }],\n    identityMatcherSetter: [{\n      type: Input,\n      args: ['identityMatcher']\n    }],\n    disabledItemHandlerSetter: [{\n      type: Input,\n      args: ['disabledItemHandler']\n    }]\n  });\n})();\nclass TuiWithItemsHandlers {\n  static {\n    this.ɵfac = function TuiWithItemsHandlers_Factory(t) {\n      return new (t || TuiWithItemsHandlers)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiWithItemsHandlers,\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([{\n        directive: TuiItemsHandlersDirective,\n        inputs: [\"stringify\", \"stringify\", \"identityMatcher\", \"identityMatcher\", \"disabledItemHandler\", \"disabledItemHandler\"]\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiWithItemsHandlers, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      hostDirectives: [{\n        directive: TuiItemsHandlersDirective,\n        inputs: ['stringify', 'identityMatcher', 'disabledItemHandler']\n      }]\n    }]\n  }], null, null);\n})();\nclass TuiItemsHandlersValidator extends TuiValidator {\n  constructor() {\n    super(...arguments);\n    this.handlers = inject(TuiItemsHandlersDirective);\n    this.update = effect(() => {\n      this.handlers.disabledItemHandler();\n      this.onChange();\n    }, TUI_ALLOW_SIGNAL_WRITES);\n    this.validate = ({\n      value\n    }) => Array.isArray(value) && value.some(item => this.handlers.disabledItemHandler()(item)) || value && this.handlers.disabledItemHandler()(value) ? {\n      tuiDisabledItem: value\n    } : null;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiItemsHandlersValidator_BaseFactory;\n      return function TuiItemsHandlersValidator_Factory(t) {\n        return (ɵTuiItemsHandlersValidator_BaseFactory || (ɵTuiItemsHandlersValidator_BaseFactory = i0.ɵɵgetInheritedFactory(TuiItemsHandlersValidator)))(t || TuiItemsHandlersValidator);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiItemsHandlersValidator,\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(NG_VALIDATORS, TuiItemsHandlersValidator, true)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiItemsHandlersValidator, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      providers: [tuiProvide(NG_VALIDATORS, TuiItemsHandlersValidator, true)]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DEFAULT_ITEMS_HANDLERS, TUI_ITEMS_HANDLERS, TuiItemsHandlersDirective, TuiItemsHandlersValidator, TuiWithItemsHandlers, tuiItemsHandlersProvider };", "map": {"version": 3, "names": ["i0", "signal", "Optional", "SkipSelf", "inject", "Directive", "Input", "effect", "tuiCreateToken", "tui<PERSON><PERSON><PERSON>", "TUI_DEFAULT_IDENTITY_MATCHER", "TUI_FALSE_HANDLER", "TUI_ALLOW_SIGNAL_WRITES", "NG_VALIDATORS", "TuiValida<PERSON>", "TUI_DEFAULT_ITEMS_HANDLERS", "stringify", "String", "identityMatcher", "disabledItemHandler", "TUI_ITEMS_HANDLERS", "tuiItemsHandlersProvider", "options", "provide", "deps", "useFactory", "parent", "TuiItemsHandlersDirective", "constructor", "defaultHandlers", "skipSelf", "stringifySetter", "x", "set", "identityMatcherSetter", "disabledItemHandlerSetter", "ɵfac", "TuiItemsHandlersDirective_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "inputs", "ɵɵInputFlags", "None", "standalone", "features", "ɵɵProvidersFeature", "ngDevMode", "ɵsetClassMetadata", "args", "providers", "TuiWithItemsHandlers", "TuiWithItemsHandlers_Factory", "ɵɵHostDirectivesFeature", "directive", "hostDirectives", "TuiItemsHandlersValidator", "arguments", "handlers", "update", "onChange", "validate", "value", "Array", "isArray", "some", "item", "tuiDisabledItem", "ɵTuiItemsHandlersValidator_BaseFactory", "TuiItemsHandlersValidator_Factory", "ɵɵgetInheritedFactory", "ɵɵInheritDefinitionFeature"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-directives-items-handlers.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { signal, Optional, SkipSelf, inject, Directive, Input, effect } from '@angular/core';\nimport { tuiCreateToken, tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_DEFAULT_IDENTITY_MATCHER, TUI_FALSE_HANDLER, TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { NG_VALIDATORS } from '@angular/forms';\nimport { TuiValidator } from '@taiga-ui/cdk/directives/validator';\n\nconst TUI_DEFAULT_ITEMS_HANDLERS = {\n    stringify: signal(String),\n    identityMatcher: signal(TUI_DEFAULT_IDENTITY_MATCHER),\n    disabledItemHandler: signal(TUI_FALSE_HANDLER),\n};\n/**\n * Default items handlers for components\n */\nconst TUI_ITEMS_HANDLERS = tuiCreateToken(TUI_DEFAULT_ITEMS_HANDLERS);\nfunction tuiItemsHandlersProvider(options) {\n    return {\n        provide: TUI_ITEMS_HANDLERS,\n        deps: [[new Optional(), new SkipSelf(), TUI_ITEMS_HANDLERS]],\n        useFactory: (parent) => ({\n            stringify: signal(parent?.stringify() ?? TUI_DEFAULT_ITEMS_HANDLERS.stringify()),\n            identityMatcher: signal(parent?.identityMatcher() ?? TUI_DEFAULT_ITEMS_HANDLERS.identityMatcher()),\n            disabledItemHandler: signal(parent?.disabledItemHandler() ??\n                TUI_DEFAULT_ITEMS_HANDLERS.disabledItemHandler()),\n            ...options,\n        }),\n    };\n}\n\nclass TuiItemsHandlersDirective {\n    constructor() {\n        this.defaultHandlers = inject(TUI_ITEMS_HANDLERS, {\n            skipSelf: true,\n        });\n        this.stringify = signal(this.defaultHandlers.stringify());\n        this.identityMatcher = signal(this.defaultHandlers.identityMatcher());\n        this.disabledItemHandler = signal(this.defaultHandlers.disabledItemHandler());\n    }\n    // TODO(v5): use signal inputs\n    set stringifySetter(x) {\n        this.stringify.set(x);\n    }\n    // TODO(v5): use signal inputs\n    set identityMatcherSetter(x) {\n        this.identityMatcher.set(x);\n    }\n    // TODO(v5): use signal inputs\n    set disabledItemHandlerSetter(x) {\n        this.disabledItemHandler.set(x);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiItemsHandlersDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiItemsHandlersDirective, isStandalone: true, inputs: { stringifySetter: [\"stringify\", \"stringifySetter\"], identityMatcherSetter: [\"identityMatcher\", \"identityMatcherSetter\"], disabledItemHandlerSetter: [\"disabledItemHandler\", \"disabledItemHandlerSetter\"] }, providers: [tuiProvide(TUI_ITEMS_HANDLERS, TuiItemsHandlersDirective)], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiItemsHandlersDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    providers: [tuiProvide(TUI_ITEMS_HANDLERS, TuiItemsHandlersDirective)],\n                }]\n        }], propDecorators: { stringifySetter: [{\n                type: Input,\n                args: ['stringify']\n            }], identityMatcherSetter: [{\n                type: Input,\n                args: ['identityMatcher']\n            }], disabledItemHandlerSetter: [{\n                type: Input,\n                args: ['disabledItemHandler']\n            }] } });\nclass TuiWithItemsHandlers {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiWithItemsHandlers, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiWithItemsHandlers, isStandalone: true, hostDirectives: [{ directive: TuiItemsHandlersDirective, inputs: [\"stringify\", \"stringify\", \"identityMatcher\", \"identityMatcher\", \"disabledItemHandler\", \"disabledItemHandler\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiWithItemsHandlers, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    hostDirectives: [\n                        {\n                            directive: TuiItemsHandlersDirective,\n                            inputs: ['stringify', 'identityMatcher', 'disabledItemHandler'],\n                        },\n                    ],\n                }]\n        }] });\n\nclass TuiItemsHandlersValidator extends TuiValidator {\n    constructor() {\n        super(...arguments);\n        this.handlers = inject(TuiItemsHandlersDirective);\n        this.update = effect(() => {\n            this.handlers.disabledItemHandler();\n            this.onChange();\n        }, TUI_ALLOW_SIGNAL_WRITES);\n        this.validate = ({ value }) => (Array.isArray(value) &&\n            value.some((item) => this.handlers.disabledItemHandler()(item))) ||\n            (value && this.handlers.disabledItemHandler()(value))\n            ? { tuiDisabledItem: value }\n            : null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiItemsHandlersValidator, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiItemsHandlersValidator, isStandalone: true, providers: [tuiProvide(NG_VALIDATORS, TuiItemsHandlersValidator, true)], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiItemsHandlersValidator, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    providers: [tuiProvide(NG_VALIDATORS, TuiItemsHandlersValidator, true)],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DEFAULT_ITEMS_HANDLERS, TUI_ITEMS_HANDLERS, TuiItemsHandlersDirective, TuiItemsHandlersValidator, TuiWithItemsHandlers, tuiItemsHandlersProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAC5F,SAASC,cAAc,EAAEC,UAAU,QAAQ,mCAAmC;AAC9E,SAASC,4BAA4B,EAAEC,iBAAiB,EAAEC,uBAAuB,QAAQ,yBAAyB;AAClH,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,YAAY,QAAQ,oCAAoC;AAEjE,MAAMC,0BAA0B,GAAG;EAC/BC,SAAS,EAAEf,MAAM,CAACgB,MAAM,CAAC;EACzBC,eAAe,EAAEjB,MAAM,CAACS,4BAA4B,CAAC;EACrDS,mBAAmB,EAAElB,MAAM,CAACU,iBAAiB;AACjD,CAAC;AACD;AACA;AACA;AACA,MAAMS,kBAAkB,GAAGZ,cAAc,CAACO,0BAA0B,CAAC;AACrE,SAASM,wBAAwBA,CAACC,OAAO,EAAE;EACvC,OAAO;IACHC,OAAO,EAAEH,kBAAkB;IAC3BI,IAAI,EAAE,CAAC,CAAC,IAAItB,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAEiB,kBAAkB,CAAC,CAAC;IAC5DK,UAAU,EAAGC,MAAM,KAAM;MACrBV,SAAS,EAAEf,MAAM,CAACyB,MAAM,EAAEV,SAAS,CAAC,CAAC,IAAID,0BAA0B,CAACC,SAAS,CAAC,CAAC,CAAC;MAChFE,eAAe,EAAEjB,MAAM,CAACyB,MAAM,EAAER,eAAe,CAAC,CAAC,IAAIH,0BAA0B,CAACG,eAAe,CAAC,CAAC,CAAC;MAClGC,mBAAmB,EAAElB,MAAM,CAACyB,MAAM,EAAEP,mBAAmB,CAAC,CAAC,IACrDJ,0BAA0B,CAACI,mBAAmB,CAAC,CAAC,CAAC;MACrD,GAAGG;IACP,CAAC;EACL,CAAC;AACL;AAEA,MAAMK,yBAAyB,CAAC;EAC5BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,eAAe,GAAGzB,MAAM,CAACgB,kBAAkB,EAAE;MAC9CU,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,CAACd,SAAS,GAAGf,MAAM,CAAC,IAAI,CAAC4B,eAAe,CAACb,SAAS,CAAC,CAAC,CAAC;IACzD,IAAI,CAACE,eAAe,GAAGjB,MAAM,CAAC,IAAI,CAAC4B,eAAe,CAACX,eAAe,CAAC,CAAC,CAAC;IACrE,IAAI,CAACC,mBAAmB,GAAGlB,MAAM,CAAC,IAAI,CAAC4B,eAAe,CAACV,mBAAmB,CAAC,CAAC,CAAC;EACjF;EACA;EACA,IAAIY,eAAeA,CAACC,CAAC,EAAE;IACnB,IAAI,CAAChB,SAAS,CAACiB,GAAG,CAACD,CAAC,CAAC;EACzB;EACA;EACA,IAAIE,qBAAqBA,CAACF,CAAC,EAAE;IACzB,IAAI,CAACd,eAAe,CAACe,GAAG,CAACD,CAAC,CAAC;EAC/B;EACA;EACA,IAAIG,yBAAyBA,CAACH,CAAC,EAAE;IAC7B,IAAI,CAACb,mBAAmB,CAACc,GAAG,CAACD,CAAC,CAAC;EACnC;EACA;IAAS,IAAI,CAACI,IAAI,YAAAC,kCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFX,yBAAyB;IAAA,CAAmD;EAAE;EACzL;IAAS,IAAI,CAACY,IAAI,kBAD+EvC,EAAE,CAAAwC,iBAAA;MAAAC,IAAA,EACJd,yBAAyB;MAAAe,MAAA;QAAAX,eAAA,GADvB/B,EAAE,CAAA2C,YAAA,CAAAC,IAAA;QAAAV,qBAAA,GAAFlC,EAAE,CAAA2C,YAAA,CAAAC,IAAA;QAAAT,yBAAA,GAAFnC,EAAE,CAAA2C,YAAA,CAAAC,IAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAF9C,EAAE,CAAA+C,kBAAA,CAC2Q,CAACtC,UAAU,CAACW,kBAAkB,EAAEO,yBAAyB,CAAC,CAAC;IAAA,EAAiB;EAAE;AAChc;AACA;EAAA,QAAAqB,SAAA,oBAAAA,SAAA,KAHqGhD,EAAE,CAAAiD,iBAAA,CAGXtB,yBAAyB,EAAc,CAAC;IACxHc,IAAI,EAAEpC,SAAS;IACf6C,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBM,SAAS,EAAE,CAAC1C,UAAU,CAACW,kBAAkB,EAAEO,yBAAyB,CAAC;IACzE,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEI,eAAe,EAAE,CAAC;MAChCU,IAAI,EAAEnC,KAAK;MACX4C,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEhB,qBAAqB,EAAE,CAAC;MACxBO,IAAI,EAAEnC,KAAK;MACX4C,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEf,yBAAyB,EAAE,CAAC;MAC5BM,IAAI,EAAEnC,KAAK;MACX4C,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC;EAAE,CAAC;AAAA;AAChB,MAAME,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAAChB,IAAI,YAAAiB,6BAAAf,CAAA;MAAA,YAAAA,CAAA,IAAyFc,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACb,IAAI,kBArB+EvC,EAAE,CAAAwC,iBAAA;MAAAC,IAAA,EAqBJW,oBAAoB;MAAAP,UAAA;MAAAC,QAAA,GArBlB9C,EAAE,CAAAsD,uBAAA;QAAAC,SAAA,EAqBoE5B,yBAAyB;QAAAe,MAAA;MAAA;IAAA,EAA4I;EAAE;AAClV;AACA;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAvBqGhD,EAAE,CAAAiD,iBAAA,CAuBXG,oBAAoB,EAAc,CAAC;IACnHX,IAAI,EAAEpC,SAAS;IACf6C,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBW,cAAc,EAAE,CACZ;QACID,SAAS,EAAE5B,yBAAyB;QACpCe,MAAM,EAAE,CAAC,WAAW,EAAE,iBAAiB,EAAE,qBAAqB;MAClE,CAAC;IAET,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMe,yBAAyB,SAAS3C,YAAY,CAAC;EACjDc,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAG8B,SAAS,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAGvD,MAAM,CAACuB,yBAAyB,CAAC;IACjD,IAAI,CAACiC,MAAM,GAAGrD,MAAM,CAAC,MAAM;MACvB,IAAI,CAACoD,QAAQ,CAACxC,mBAAmB,CAAC,CAAC;MACnC,IAAI,CAAC0C,QAAQ,CAAC,CAAC;IACnB,CAAC,EAAEjD,uBAAuB,CAAC;IAC3B,IAAI,CAACkD,QAAQ,GAAG,CAAC;MAAEC;IAAM,CAAC,KAAMC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAChDA,KAAK,CAACG,IAAI,CAAEC,IAAI,IAAK,IAAI,CAACR,QAAQ,CAACxC,mBAAmB,CAAC,CAAC,CAACgD,IAAI,CAAC,CAAC,IAC9DJ,KAAK,IAAI,IAAI,CAACJ,QAAQ,CAACxC,mBAAmB,CAAC,CAAC,CAAC4C,KAAK,CAAE,GACnD;MAAEK,eAAe,EAAEL;IAAM,CAAC,GAC1B,IAAI;EACd;EACA;IAAS,IAAI,CAAC3B,IAAI;MAAA,IAAAiC,sCAAA;MAAA,gBAAAC,kCAAAhC,CAAA;QAAA,QAAA+B,sCAAA,KAAAA,sCAAA,GAlD+ErE,EAAE,CAAAuE,qBAAA,CAkDQd,yBAAyB,IAAAnB,CAAA,IAAzBmB,yBAAyB;MAAA;IAAA,IAAqD;EAAE;EAC3L;IAAS,IAAI,CAAClB,IAAI,kBAnD+EvC,EAAE,CAAAwC,iBAAA;MAAAC,IAAA,EAmDJgB,yBAAyB;MAAAZ,UAAA;MAAAC,QAAA,GAnDvB9C,EAAE,CAAA+C,kBAAA,CAmDsD,CAACtC,UAAU,CAACI,aAAa,EAAE4C,yBAAyB,EAAE,IAAI,CAAC,CAAC,GAnDpHzD,EAAE,CAAAwE,0BAAA;IAAA,EAmD0J;EAAE;AACnQ;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KArDqGhD,EAAE,CAAAiD,iBAAA,CAqDXQ,yBAAyB,EAAc,CAAC;IACxHhB,IAAI,EAAEpC,SAAS;IACf6C,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBM,SAAS,EAAE,CAAC1C,UAAU,CAACI,aAAa,EAAE4C,yBAAyB,EAAE,IAAI,CAAC;IAC1E,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS1C,0BAA0B,EAAEK,kBAAkB,EAAEO,yBAAyB,EAAE8B,yBAAyB,EAAEL,oBAAoB,EAAE/B,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}