{"ast": null, "code": "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { TUI_DEFAULT_MATCHER } from '@taiga-ui/cdk/constants';\nimport { tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_DATA_LIST_HOST } from '@taiga-ui/core/components/data-list';\nimport { TuiTextfieldComponent } from '@taiga-ui/core/components/textfield';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { tuiIsFlat } from '@taiga-ui/kit/utils';\n\n// TODO: Consider replacing TuiTextfieldComponent with proper token once we refactor textfields\nclass TuiFilterByInputPipe {\n  constructor() {\n    // TODO: Remove optional after legacy controls are dropped\n    this.textfield = inject(TuiTextfieldComponent, {\n      optional: true\n    });\n    this.host = inject(TUI_DATA_LIST_HOST);\n    this.itemsHandlers = inject(TUI_ITEMS_HANDLERS);\n  }\n  transform(items, matcher = TUI_DEFAULT_MATCHER) {\n    return this.filter(items, matcher, (this.textfield ? this.itemsHandlers.stringify() :\n    // TODO(v5): delete backward compatibility\n    this.host.stringify) || String, this.textfield?.input?.nativeElement.value || this.host.nativeFocusableElement?.value || '');\n  }\n  filter(items, matcher, stringify, query) {\n    if (!items) {\n      return null;\n    }\n    return tuiIsFlat(items) ? this.filterFlat(items, matcher, stringify, query) : this.filter2d(items, matcher, stringify, query);\n  }\n  filterFlat(items, matcher, stringify, query) {\n    const match = this.getMatch(items, stringify, query);\n    return match != null ? items : items.filter(item => matcher(item, query, stringify));\n  }\n  filter2d(items, matcher, stringify, query) {\n    const match = items.find(item => this.getMatch(item, stringify, query) != null);\n    return match != null ? items : items.map(inner => this.filterFlat(inner, matcher, stringify, query));\n  }\n  getMatch(items, stringify, query) {\n    // TODO: Refactor when tui-textfield[multi] is ready\n    if (this.host.tagValidator) {\n      return undefined;\n    }\n    return items.find(item => stringify(item).toLocaleLowerCase() === query.toLocaleLowerCase());\n  }\n  static {\n    this.ɵfac = function TuiFilterByInputPipe_Factory(t) {\n      return new (t || TuiFilterByInputPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiFilterByInput\",\n      type: TuiFilterByInputPipe,\n      pure: false,\n      standalone: true\n    });\n  }\n}\n__decorate([tuiPure], TuiFilterByInputPipe.prototype, \"filter\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFilterByInputPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiFilterByInput',\n      pure: false\n    }]\n  }], null, {\n    filter: []\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFilterByInputPipe };", "map": {"version": 3, "names": ["__decorate", "i0", "inject", "<PERSON><PERSON>", "TUI_DEFAULT_MATCHER", "tuiPure", "TUI_DATA_LIST_HOST", "TuiTextfieldComponent", "TUI_ITEMS_HANDLERS", "tui<PERSON>sFlat", "TuiFilterByInputPipe", "constructor", "textfield", "optional", "host", "itemsHandlers", "transform", "items", "matcher", "filter", "stringify", "String", "input", "nativeElement", "value", "nativeFocusableElement", "query", "filterFlat", "filter2d", "match", "getMatch", "item", "find", "map", "inner", "tagValidator", "undefined", "toLocaleLowerCase", "ɵfac", "TuiFilterByInputPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "prototype", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-pipes-filter-by-input.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { TUI_DEFAULT_MATCHER } from '@taiga-ui/cdk/constants';\nimport { tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_DATA_LIST_HOST } from '@taiga-ui/core/components/data-list';\nimport { TuiTextfieldComponent } from '@taiga-ui/core/components/textfield';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { tuiIsFlat } from '@taiga-ui/kit/utils';\n\n// TODO: Consider replacing TuiTextfieldComponent with proper token once we refactor textfields\nclass TuiFilterByInputPipe {\n    constructor() {\n        // TODO: Remove optional after legacy controls are dropped\n        this.textfield = inject(TuiTextfieldComponent, { optional: true });\n        this.host = inject(TUI_DATA_LIST_HOST);\n        this.itemsHandlers = inject(TUI_ITEMS_HANDLERS);\n    }\n    transform(items, matcher = TUI_DEFAULT_MATCHER) {\n        return this.filter(items, matcher, (this.textfield\n            ? this.itemsHandlers.stringify()\n            : // TODO(v5): delete backward compatibility\n                this.host.stringify) || String, this.textfield?.input?.nativeElement.value ||\n            this.host.nativeFocusableElement?.value ||\n            '');\n    }\n    filter(items, matcher, stringify, query) {\n        if (!items) {\n            return null;\n        }\n        return tuiIsFlat(items)\n            ? this.filterFlat(items, matcher, stringify, query)\n            : this.filter2d(items, matcher, stringify, query);\n    }\n    filterFlat(items, matcher, stringify, query) {\n        const match = this.getMatch(items, stringify, query);\n        return match != null\n            ? items\n            : items.filter((item) => matcher(item, query, stringify));\n    }\n    filter2d(items, matcher, stringify, query) {\n        const match = items.find((item) => this.getMatch(item, stringify, query) != null);\n        return match != null\n            ? items\n            : items.map((inner) => this.filterFlat(inner, matcher, stringify, query));\n    }\n    getMatch(items, stringify, query) {\n        // TODO: Refactor when tui-textfield[multi] is ready\n        if (this.host.tagValidator) {\n            return undefined;\n        }\n        return items.find((item) => stringify(item).toLocaleLowerCase() === query.toLocaleLowerCase());\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFilterByInputPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFilterByInputPipe, isStandalone: true, name: \"tuiFilterByInput\", pure: false }); }\n}\n__decorate([\n    tuiPure\n], TuiFilterByInputPipe.prototype, \"filter\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFilterByInputPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiFilterByInput',\n                    pure: false,\n                }]\n        }], propDecorators: { filter: [] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFilterByInputPipe };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,IAAI,QAAQ,eAAe;AAC5C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,qBAAqB,QAAQ,qCAAqC;AAC3E,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,SAAS,QAAQ,qBAAqB;;AAE/C;AACA,MAAMC,oBAAoB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,SAAS,GAAGV,MAAM,CAACK,qBAAqB,EAAE;MAAEM,QAAQ,EAAE;IAAK,CAAC,CAAC;IAClE,IAAI,CAACC,IAAI,GAAGZ,MAAM,CAACI,kBAAkB,CAAC;IACtC,IAAI,CAACS,aAAa,GAAGb,MAAM,CAACM,kBAAkB,CAAC;EACnD;EACAQ,SAASA,CAACC,KAAK,EAAEC,OAAO,GAAGd,mBAAmB,EAAE;IAC5C,OAAO,IAAI,CAACe,MAAM,CAACF,KAAK,EAAEC,OAAO,EAAE,CAAC,IAAI,CAACN,SAAS,GAC5C,IAAI,CAACG,aAAa,CAACK,SAAS,CAAC,CAAC;IAC9B;IACE,IAAI,CAACN,IAAI,CAACM,SAAS,KAAKC,MAAM,EAAE,IAAI,CAACT,SAAS,EAAEU,KAAK,EAAEC,aAAa,CAACC,KAAK,IAC9E,IAAI,CAACV,IAAI,CAACW,sBAAsB,EAAED,KAAK,IACvC,EAAE,CAAC;EACX;EACAL,MAAMA,CAACF,KAAK,EAAEC,OAAO,EAAEE,SAAS,EAAEM,KAAK,EAAE;IACrC,IAAI,CAACT,KAAK,EAAE;MACR,OAAO,IAAI;IACf;IACA,OAAOR,SAAS,CAACQ,KAAK,CAAC,GACjB,IAAI,CAACU,UAAU,CAACV,KAAK,EAAEC,OAAO,EAAEE,SAAS,EAAEM,KAAK,CAAC,GACjD,IAAI,CAACE,QAAQ,CAACX,KAAK,EAAEC,OAAO,EAAEE,SAAS,EAAEM,KAAK,CAAC;EACzD;EACAC,UAAUA,CAACV,KAAK,EAAEC,OAAO,EAAEE,SAAS,EAAEM,KAAK,EAAE;IACzC,MAAMG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAACb,KAAK,EAAEG,SAAS,EAAEM,KAAK,CAAC;IACpD,OAAOG,KAAK,IAAI,IAAI,GACdZ,KAAK,GACLA,KAAK,CAACE,MAAM,CAAEY,IAAI,IAAKb,OAAO,CAACa,IAAI,EAAEL,KAAK,EAAEN,SAAS,CAAC,CAAC;EACjE;EACAQ,QAAQA,CAACX,KAAK,EAAEC,OAAO,EAAEE,SAAS,EAAEM,KAAK,EAAE;IACvC,MAAMG,KAAK,GAAGZ,KAAK,CAACe,IAAI,CAAED,IAAI,IAAK,IAAI,CAACD,QAAQ,CAACC,IAAI,EAAEX,SAAS,EAAEM,KAAK,CAAC,IAAI,IAAI,CAAC;IACjF,OAAOG,KAAK,IAAI,IAAI,GACdZ,KAAK,GACLA,KAAK,CAACgB,GAAG,CAAEC,KAAK,IAAK,IAAI,CAACP,UAAU,CAACO,KAAK,EAAEhB,OAAO,EAAEE,SAAS,EAAEM,KAAK,CAAC,CAAC;EACjF;EACAI,QAAQA,CAACb,KAAK,EAAEG,SAAS,EAAEM,KAAK,EAAE;IAC9B;IACA,IAAI,IAAI,CAACZ,IAAI,CAACqB,YAAY,EAAE;MACxB,OAAOC,SAAS;IACpB;IACA,OAAOnB,KAAK,CAACe,IAAI,CAAED,IAAI,IAAKX,SAAS,CAACW,IAAI,CAAC,CAACM,iBAAiB,CAAC,CAAC,KAAKX,KAAK,CAACW,iBAAiB,CAAC,CAAC,CAAC;EAClG;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyF9B,oBAAoB;IAAA,CAA8C;EAAE;EAC/K;IAAS,IAAI,CAAC+B,KAAK,kBAD8ExC,EAAE,CAAAyC,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMlC,oBAAoB;MAAAmC,IAAA;MAAAC,UAAA;IAAA,EAA8D;EAAE;AACjM;AACA9C,UAAU,CAAC,CACPK,OAAO,CACV,EAAEK,oBAAoB,CAACqC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;AAClD;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KANqG/C,EAAE,CAAAgD,iBAAA,CAMXvC,oBAAoB,EAAc,CAAC;IACnHkC,IAAI,EAAEzC,IAAI;IACV+C,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE,kBAAkB;MACxBE,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE1B,MAAM,EAAE;EAAG,CAAC;AAAA;;AAE1C;AACA;AACA;;AAEA,SAAST,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}