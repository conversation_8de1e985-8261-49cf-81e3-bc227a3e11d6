{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule } from '@angular/common/http';\nimport { FormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\n// App components and modules\nimport { AppComponent } from './app.component';\nimport { CpqConfiguratorModule } from './components/cpq-configurator/cpq-configurator.module';\nlet AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent],\n  imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, FormsModule, CommonModule, CpqConfiguratorModule],\n  providers: [],\n  bootstrap: [AppComponent],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA]\n})], AppModule);\nexport { AppModule };", "map": {"version": 3, "names": ["NgModule", "CUSTOM_ELEMENTS_SCHEMA", "BrowserModule", "BrowserAnimationsModule", "HttpClientModule", "FormsModule", "CommonModule", "AppComponent", "CpqConfiguratorModule", "AppModule", "__decorate", "declarations", "imports", "providers", "bootstrap", "schemas"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet_CRM\\apps\\AngularCPQapp\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule } from '@angular/common/http';\nimport { FormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\n\n// App components and modules\nimport { AppComponent } from './app.component';\nimport { CpqConfiguratorModule } from './components/cpq-configurator/cpq-configurator.module';\n\n@NgModule({\n  declarations: [\n    AppComponent\n  ],\n  imports: [\n    BrowserModule,\n    BrowserAnimationsModule,\n    HttpClientModule,\n    FormsModule,\n    CommonModule,\n    CpqConfiguratorModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA]\n})\nexport class AppModule { }"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,sBAAsB,QAAQ,eAAe;AAChE,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,uDAAuD;AAkBtF,IAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,GAAAC,UAAA,EAhBrBV,QAAQ,CAAC;EACRW,YAAY,EAAE,CACZJ,YAAY,CACb;EACDK,OAAO,EAAE,CACPV,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBC,WAAW,EACXC,YAAY,EACZE,qBAAqB,CACtB;EACDK,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,CAACP,YAAY,CAAC;EACzBQ,OAAO,EAAE,CAACd,sBAAsB;CACjC,CAAC,C,EACWQ,SAAS,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}