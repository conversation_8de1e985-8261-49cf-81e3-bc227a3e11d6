{"ast": null, "code": "import { TuiTime, HOURS_IN_DAY } from '@taiga-ui/cdk/date-time';\nimport { inject } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { NgControl } from '@angular/forms';\nimport { tuiControlValue } from '@taiga-ui/cdk/observables';\nimport { TuiTextfieldComponent } from '@taiga-ui/core/components/textfield';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { tuiDirectiveBinding } from '@taiga-ui/cdk/utils';\nimport { CHAR_PLUS } from '@taiga-ui/cdk/constants';\nimport { getCountryCallingCode } from 'libphonenumber-js/core';\nfunction tuiCreateTimePeriods(minHour = 0, maxHour = HOURS_IN_DAY, minutes = [0, 30]) {\n  const timeArray = [];\n  for (let i = minHour; i < maxHour; i++) {\n    minutes.forEach(minute => {\n      const time = new TuiTime(i, minute);\n      timeArray.push(time);\n    });\n  }\n  return timeArray;\n}\nfunction tuiInjectValue() {\n  const control = inject(TuiTextfieldComponent, {\n    optional: true\n  })?.control || inject(NgControl, {\n    optional: true\n  });\n  return toSignal(tuiControlValue(control), {\n    requireSync: true\n  });\n}\nfunction tuiIsFlat(items) {\n  return !Array.isArray(items[0]);\n}\nfunction tuiMaskito(options) {\n  return tuiDirectiveBinding(MaskitoDirective, 'options', options);\n}\nfunction tuiGetCallingCode(iso, metadata) {\n  return metadata ? CHAR_PLUS + getCountryCallingCode(iso, metadata) : '';\n}\nfunction tuiToggleDay(days, day) {\n  return (days?.find(item => item.daySame(day)) ? days.filter(item => !item.daySame(day)) : days?.concat(day)) || [];\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiCreateTimePeriods, tuiGetCallingCode, tuiInjectValue, tuiIsFlat, tuiMaskito, tuiToggleDay };", "map": {"version": 3, "names": ["TuiTime", "HOURS_IN_DAY", "inject", "toSignal", "NgControl", "tuiControlValue", "TuiTextfieldComponent", "MaskitoDirective", "tuiDirectiveBinding", "CHAR_PLUS", "getCountryCallingCode", "tuiCreateTimePeriods", "minHour", "maxHour", "minutes", "timeArray", "i", "for<PERSON>ach", "minute", "time", "push", "tuiInjectValue", "control", "optional", "requireSync", "tui<PERSON>sFlat", "items", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "tuiGetCallingCode", "iso", "metadata", "tuiToggleDay", "days", "day", "find", "item", "daySame", "filter", "concat"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-utils.mjs"], "sourcesContent": ["import { TuiTime, HOURS_IN_DAY } from '@taiga-ui/cdk/date-time';\nimport { inject } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { NgControl } from '@angular/forms';\nimport { tuiControlValue } from '@taiga-ui/cdk/observables';\nimport { TuiTextfieldComponent } from '@taiga-ui/core/components/textfield';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { tuiDirectiveBinding } from '@taiga-ui/cdk/utils';\nimport { CHAR_PLUS } from '@taiga-ui/cdk/constants';\nimport { getCountryCallingCode } from 'libphonenumber-js/core';\n\nfunction tuiCreateTimePeriods(minHour = 0, maxHour = HOURS_IN_DAY, minutes = [0, 30]) {\n    const timeArray = [];\n    for (let i = minHour; i < maxHour; i++) {\n        minutes.forEach((minute) => {\n            const time = new TuiTime(i, minute);\n            timeArray.push(time);\n        });\n    }\n    return timeArray;\n}\n\nfunction tuiInjectValue() {\n    const control = inject(TuiTextfieldComponent, { optional: true })?.control ||\n        inject(NgControl, { optional: true });\n    return toSignal(tuiControlValue(control), { requireSync: true });\n}\n\nfunction tuiIsFlat(items) {\n    return !Array.isArray(items[0]);\n}\n\nfunction tuiMaskito(options) {\n    return tuiDirectiveBinding(MaskitoDirective, 'options', options);\n}\n\nfunction tuiGetCallingCode(iso, metadata) {\n    return metadata ? CHAR_PLUS + getCountryCallingCode(iso, metadata) : '';\n}\n\nfunction tuiToggleDay(days, day) {\n    return ((days?.find((item) => item.daySame(day))\n        ? days.filter((item) => !item.daySame(day))\n        : days?.concat(day)) || []);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiCreateTimePeriods, tuiGetCallingCode, tuiInjectValue, tuiIsFlat, tuiMaskito, tuiToggleDay };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,YAAY,QAAQ,yBAAyB;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,qBAAqB,QAAQ,qCAAqC;AAC3E,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,qBAAqB,QAAQ,wBAAwB;AAE9D,SAASC,oBAAoBA,CAACC,OAAO,GAAG,CAAC,EAAEC,OAAO,GAAGZ,YAAY,EAAEa,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;EAClF,MAAMC,SAAS,GAAG,EAAE;EACpB,KAAK,IAAIC,CAAC,GAAGJ,OAAO,EAAEI,CAAC,GAAGH,OAAO,EAAEG,CAAC,EAAE,EAAE;IACpCF,OAAO,CAACG,OAAO,CAAEC,MAAM,IAAK;MACxB,MAAMC,IAAI,GAAG,IAAInB,OAAO,CAACgB,CAAC,EAAEE,MAAM,CAAC;MACnCH,SAAS,CAACK,IAAI,CAACD,IAAI,CAAC;IACxB,CAAC,CAAC;EACN;EACA,OAAOJ,SAAS;AACpB;AAEA,SAASM,cAAcA,CAAA,EAAG;EACtB,MAAMC,OAAO,GAAGpB,MAAM,CAACI,qBAAqB,EAAE;IAAEiB,QAAQ,EAAE;EAAK,CAAC,CAAC,EAAED,OAAO,IACtEpB,MAAM,CAACE,SAAS,EAAE;IAAEmB,QAAQ,EAAE;EAAK,CAAC,CAAC;EACzC,OAAOpB,QAAQ,CAACE,eAAe,CAACiB,OAAO,CAAC,EAAE;IAAEE,WAAW,EAAE;EAAK,CAAC,CAAC;AACpE;AAEA,SAASC,SAASA,CAACC,KAAK,EAAE;EACtB,OAAO,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;AACnC;AAEA,SAASG,UAAUA,CAACC,OAAO,EAAE;EACzB,OAAOtB,mBAAmB,CAACD,gBAAgB,EAAE,SAAS,EAAEuB,OAAO,CAAC;AACpE;AAEA,SAASC,iBAAiBA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EACtC,OAAOA,QAAQ,GAAGxB,SAAS,GAAGC,qBAAqB,CAACsB,GAAG,EAAEC,QAAQ,CAAC,GAAG,EAAE;AAC3E;AAEA,SAASC,YAAYA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC7B,OAAQ,CAACD,IAAI,EAAEE,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,OAAO,CAACH,GAAG,CAAC,CAAC,GAC1CD,IAAI,CAACK,MAAM,CAAEF,IAAI,IAAK,CAACA,IAAI,CAACC,OAAO,CAACH,GAAG,CAAC,CAAC,GACzCD,IAAI,EAAEM,MAAM,CAACL,GAAG,CAAC,KAAK,EAAE;AAClC;;AAEA;AACA;AACA;;AAEA,SAASzB,oBAAoB,EAAEoB,iBAAiB,EAAEV,cAAc,EAAEI,SAAS,EAAEI,UAAU,EAAEK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}