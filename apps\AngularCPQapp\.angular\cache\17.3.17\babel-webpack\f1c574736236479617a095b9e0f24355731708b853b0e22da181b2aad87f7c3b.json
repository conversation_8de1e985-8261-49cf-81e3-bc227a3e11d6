{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Directive, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { Subject, merge, fromEvent } from 'rxjs';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nconst TUI_FLUID_TYPOGRAPHY_DEFAULT_OPTIONS = {\n  min: 0.625,\n  max: 1.5\n};\nconst TUI_FLUID_TYPOGRAPHY_OPTIONS = tuiCreateToken(TUI_FLUID_TYPOGRAPHY_DEFAULT_OPTIONS);\nfunction tuiFluidTypographyOptionsProvider(options) {\n  return tuiProvideOptions(TUI_FLUID_TYPOGRAPHY_OPTIONS, options, TUI_FLUID_TYPOGRAPHY_DEFAULT_OPTIONS);\n}\nconst STEP = 1 / 16;\nclass TuiFluidTypography {\n  constructor() {\n    // TODO: refactor to signal inputs after Angular update\n    this.changes$ = new Subject();\n    this.el = tuiInjectElement();\n    this.options = inject(TUI_FLUID_TYPOGRAPHY_OPTIONS);\n    this.sub = merge(this.changes$, inject(ResizeObserverService, {\n      self: true\n    }), inject(MutationObserverService, {\n      self: true\n    }), fromEvent(this.el, 'input')).pipe(tuiZonefree(), takeUntilDestroyed()).subscribe(() => {\n      const min = Number(this.tuiFluidTypography[0] || this.options.min);\n      const max = Number(this.tuiFluidTypography[1] || this.options.max);\n      for (let i = max; i >= min; i -= STEP) {\n        this.el.style.fontSize = `${i}rem`;\n        if (this.el.scrollWidth <= this.el.clientWidth) {\n          break;\n        }\n      }\n    });\n    this.tuiFluidTypography = '';\n  }\n  ngOnChanges() {\n    this.changes$.next();\n  }\n  static {\n    this.ɵfac = function TuiFluidTypography_Factory(t) {\n      return new (t || TuiFluidTypography)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiFluidTypography,\n      selectors: [[\"\", \"tuiFluidTypography\", \"\"]],\n      hostAttrs: [\"tuiFluidTypography\", \"\"],\n      inputs: {\n        tuiFluidTypography: \"tuiFluidTypography\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([ResizeObserverService, MutationObserverService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          characterData: true,\n          subtree: true\n        }\n      }]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFluidTypography, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiFluidTypography]',\n      providers: [ResizeObserverService, MutationObserverService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          characterData: true,\n          subtree: true\n        }\n      }],\n      host: {\n        tuiFluidTypography: ''\n      }\n    }]\n  }], null, {\n    tuiFluidTypography: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_FLUID_TYPOGRAPHY_DEFAULT_OPTIONS, TUI_FLUID_TYPOGRAPHY_OPTIONS, TuiFluidTypography, tuiFluidTypographyOptionsProvider };", "map": {"version": 3, "names": ["i0", "inject", "Directive", "Input", "takeUntilDestroyed", "MutationObserverService", "WA_MUTATION_OBSERVER_INIT", "ResizeObserverService", "tuiZonefree", "tuiInjectElement", "Subject", "merge", "fromEvent", "tuiCreateToken", "tuiProvideOptions", "TUI_FLUID_TYPOGRAPHY_DEFAULT_OPTIONS", "min", "max", "TUI_FLUID_TYPOGRAPHY_OPTIONS", "tuiFluidTypographyOptionsProvider", "options", "STEP", "TuiFluidTypography", "constructor", "changes$", "el", "sub", "self", "pipe", "subscribe", "Number", "tuiFluidTypography", "i", "style", "fontSize", "scrollWidth", "clientWidth", "ngOnChanges", "next", "ɵfac", "TuiFluidTypography_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "inputs", "standalone", "features", "ɵɵProvidersFeature", "provide", "useValue", "characterData", "subtree", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-fluid-typography.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Directive, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { Subject, merge, fromEvent } from 'rxjs';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\n\nconst TUI_FLUID_TYPOGRAPHY_DEFAULT_OPTIONS = {\n    min: 0.625,\n    max: 1.5,\n};\nconst TUI_FLUID_TYPOGRAPHY_OPTIONS = tuiCreateToken(TUI_FLUID_TYPOGRAPHY_DEFAULT_OPTIONS);\nfunction tuiFluidTypographyOptionsProvider(options) {\n    return tuiProvideOptions(TUI_FLUID_TYPOGRAPHY_OPTIONS, options, TUI_FLUID_TYPOGRAPHY_DEFAULT_OPTIONS);\n}\n\nconst STEP = 1 / 16;\nclass TuiFluidTypography {\n    constructor() {\n        // TODO: refactor to signal inputs after Angular update\n        this.changes$ = new Subject();\n        this.el = tuiInjectElement();\n        this.options = inject(TUI_FLUID_TYPOGRAPHY_OPTIONS);\n        this.sub = merge(this.changes$, inject(ResizeObserverService, { self: true }), inject(MutationObserverService, { self: true }), fromEvent(this.el, 'input'))\n            .pipe(tuiZonefree(), takeUntilDestroyed())\n            .subscribe(() => {\n            const min = Number(this.tuiFluidTypography[0] || this.options.min);\n            const max = Number(this.tuiFluidTypography[1] || this.options.max);\n            for (let i = max; i >= min; i -= STEP) {\n                this.el.style.fontSize = `${i}rem`;\n                if (this.el.scrollWidth <= this.el.clientWidth) {\n                    break;\n                }\n            }\n        });\n        this.tuiFluidTypography = '';\n    }\n    ngOnChanges() {\n        this.changes$.next();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFluidTypography, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiFluidTypography, isStandalone: true, selector: \"[tuiFluidTypography]\", inputs: { tuiFluidTypography: \"tuiFluidTypography\" }, host: { attributes: { \"tuiFluidTypography\": \"\" } }, providers: [\n            ResizeObserverService,\n            MutationObserverService,\n            {\n                provide: WA_MUTATION_OBSERVER_INIT,\n                useValue: { characterData: true, subtree: true },\n            },\n        ], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFluidTypography, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiFluidTypography]',\n                    providers: [\n                        ResizeObserverService,\n                        MutationObserverService,\n                        {\n                            provide: WA_MUTATION_OBSERVER_INIT,\n                            useValue: { characterData: true, subtree: true },\n                        },\n                    ],\n                    host: { tuiFluidTypography: '' },\n                }]\n        }], propDecorators: { tuiFluidTypography: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_FLUID_TYPOGRAPHY_DEFAULT_OPTIONS, TUI_FLUID_TYPOGRAPHY_OPTIONS, TuiFluidTypography, tuiFluidTypographyOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACxD,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,uBAAuB,EAAEC,yBAAyB,QAAQ,gCAAgC;AACnG,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,OAAO,EAAEC,KAAK,EAAEC,SAAS,QAAQ,MAAM;AAChD,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,mCAAmC;AAErF,MAAMC,oCAAoC,GAAG;EACzCC,GAAG,EAAE,KAAK;EACVC,GAAG,EAAE;AACT,CAAC;AACD,MAAMC,4BAA4B,GAAGL,cAAc,CAACE,oCAAoC,CAAC;AACzF,SAASI,iCAAiCA,CAACC,OAAO,EAAE;EAChD,OAAON,iBAAiB,CAACI,4BAA4B,EAAEE,OAAO,EAAEL,oCAAoC,CAAC;AACzG;AAEA,MAAMM,IAAI,GAAG,CAAC,GAAG,EAAE;AACnB,MAAMC,kBAAkB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,QAAQ,GAAG,IAAId,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACe,EAAE,GAAGhB,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACW,OAAO,GAAGnB,MAAM,CAACiB,4BAA4B,CAAC;IACnD,IAAI,CAACQ,GAAG,GAAGf,KAAK,CAAC,IAAI,CAACa,QAAQ,EAAEvB,MAAM,CAACM,qBAAqB,EAAE;MAAEoB,IAAI,EAAE;IAAK,CAAC,CAAC,EAAE1B,MAAM,CAACI,uBAAuB,EAAE;MAAEsB,IAAI,EAAE;IAAK,CAAC,CAAC,EAAEf,SAAS,CAAC,IAAI,CAACa,EAAE,EAAE,OAAO,CAAC,CAAC,CACvJG,IAAI,CAACpB,WAAW,CAAC,CAAC,EAAEJ,kBAAkB,CAAC,CAAC,CAAC,CACzCyB,SAAS,CAAC,MAAM;MACjB,MAAMb,GAAG,GAAGc,MAAM,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAAC,IAAI,IAAI,CAACX,OAAO,CAACJ,GAAG,CAAC;MAClE,MAAMC,GAAG,GAAGa,MAAM,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAAC,IAAI,IAAI,CAACX,OAAO,CAACH,GAAG,CAAC;MAClE,KAAK,IAAIe,CAAC,GAAGf,GAAG,EAAEe,CAAC,IAAIhB,GAAG,EAAEgB,CAAC,IAAIX,IAAI,EAAE;QACnC,IAAI,CAACI,EAAE,CAACQ,KAAK,CAACC,QAAQ,GAAG,GAAGF,CAAC,KAAK;QAClC,IAAI,IAAI,CAACP,EAAE,CAACU,WAAW,IAAI,IAAI,CAACV,EAAE,CAACW,WAAW,EAAE;UAC5C;QACJ;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACL,kBAAkB,GAAG,EAAE;EAChC;EACAM,WAAWA,CAAA,EAAG;IACV,IAAI,CAACb,QAAQ,CAACc,IAAI,CAAC,CAAC;EACxB;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,2BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFnB,kBAAkB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAACoB,IAAI,kBAD+E1C,EAAE,CAAA2C,iBAAA;MAAAC,IAAA,EACJtB,kBAAkB;MAAAuB,SAAA;MAAAC,SAAA,yBAA0J,EAAE;MAAAC,MAAA;QAAAhB,kBAAA;MAAA;MAAAiB,UAAA;MAAAC,QAAA,GAD5KjD,EAAE,CAAAkD,kBAAA,CAC2L,CACtR3C,qBAAqB,EACrBF,uBAAuB,EACvB;QACI8C,OAAO,EAAE7C,yBAAyB;QAClC8C,QAAQ,EAAE;UAAEC,aAAa,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAK;MACnD,CAAC,CACJ,GAR4FtD,EAAE,CAAAuD,oBAAA;IAAA,EAQxD;EAAE;AACjD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAVqGxD,EAAE,CAAAyD,iBAAA,CAUXnC,kBAAkB,EAAc,CAAC;IACjHsB,IAAI,EAAE1C,SAAS;IACfwD,IAAI,EAAE,CAAC;MACCV,UAAU,EAAE,IAAI;MAChBW,QAAQ,EAAE,sBAAsB;MAChCC,SAAS,EAAE,CACPrD,qBAAqB,EACrBF,uBAAuB,EACvB;QACI8C,OAAO,EAAE7C,yBAAyB;QAClC8C,QAAQ,EAAE;UAAEC,aAAa,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAK;MACnD,CAAC,CACJ;MACDO,IAAI,EAAE;QAAE9B,kBAAkB,EAAE;MAAG;IACnC,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEA,kBAAkB,EAAE,CAAC;MACnCa,IAAI,EAAEzC;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASY,oCAAoC,EAAEG,4BAA4B,EAAEI,kBAAkB,EAAEH,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}