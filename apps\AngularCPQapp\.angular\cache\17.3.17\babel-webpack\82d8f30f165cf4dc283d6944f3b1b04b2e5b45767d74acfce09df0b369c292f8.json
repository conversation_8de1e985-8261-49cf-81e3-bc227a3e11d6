{"ast": null, "code": "import { TuiCalendar } from '@taiga-ui/core/components/calendar';\nimport { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, computed, effect, signal, Directive, Input, Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\nimport { TUI_FIRST_DAY, TUI_LAST_DAY, DATE_FILLER_LENGTH, TuiDay } from '@taiga-ui/cdk/date-time';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldDirective, tuiInjectAuxiliary, TuiTextfieldComponent, tuiTextfieldIconBinding, TuiWithTextfield, TuiTextfieldContent } from '@taiga-ui/core/components/textfield';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport * as i4 from '@maskito/angular';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { maskitoDateOptionsGenerator } from '@maskito/kit';\nimport { TUI_IDENTITY_VALUE_TRANSFORMER, TuiControl, tuiAsControl, tuiValueTransformerFrom } from '@taiga-ui/cdk/classes';\nimport { TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiDirectiveBinding, changeDateSeparator } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiAsOptionContent } from '@taiga-ui/core/components/data-list';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { tuiDropdownOpen, tuiDropdownEnabled, TuiDropdownAuto } from '@taiga-ui/core/directives/dropdown';\nimport * as i3 from '@taiga-ui/core/directives/items-handlers';\nimport { TuiItemsHandlersDirective, TUI_ITEMS_HANDLERS, TuiItemsHandlersValidator } from '@taiga-ui/core/directives/items-handlers';\nimport { TUI_DATE_FORMAT, TUI_DEFAULT_DATE_FORMAT } from '@taiga-ui/core/tokens';\nimport { TuiCalendarRange } from '@taiga-ui/kit/components/calendar-range';\nimport { TuiSelectOption } from '@taiga-ui/kit/components/select';\nimport { TUI_DATE_TEXTS } from '@taiga-ui/kit/tokens';\nimport { tuiMaskito } from '@taiga-ui/kit/utils';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nconst _c0 = [\"tuiInputDate\", \"\", \"type\", \"date\"];\nfunction TuiInputDateComponent_ng_container_0_input_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 2);\n    i0.ɵɵlistener(\"click.stop.zoneless\", function TuiInputDateComponent_ng_container_0_input_1_Template_input_click_stop_zoneless_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      return i0.ɵɵresetView(0);\n    })(\"input\", function TuiInputDateComponent_ng_container_0_input_1_Template_input_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onInput($event.target.value));\n    })(\"pointerdown.stop.zoneless\", function TuiInputDateComponent_ng_container_0_input_1_Template_input_pointerdown_stop_zoneless_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      return i0.ɵɵresetView(0);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"max\", ctx_r1.host.max().toJSON())(\"min\", ctx_r1.host.min().toJSON())(\"value\", (tmp_4_0 = ctx_r1.host.value()) == null ? null : tmp_4_0.toJSON());\n    i0.ɵɵattribute(\"list\", ctx_r1.list);\n  }\n}\nfunction TuiInputDateComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiInputDateComponent_ng_container_0_input_1_Template, 1, 4, \"input\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW = {\n  icon: () => '@tui.calendar',\n  min: TUI_FIRST_DAY,\n  max: TUI_LAST_DAY,\n  valueTransformer: TUI_IDENTITY_VALUE_TRANSFORMER\n};\nconst [TUI_INPUT_DATE_OPTIONS_NEW, tuiInputDateOptionsProviderNew] = tuiCreateOptions(TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW);\nconst TUI_DATE_ADAPTER = {\n  DMY: 'dd/mm/yyyy',\n  MDY: 'mm/dd/yyyy',\n  YMD: 'yyyy/mm/dd'\n};\nclass TuiInputDateBase extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.el = tuiInjectElement();\n    this.options = inject(TUI_INPUT_DATE_OPTIONS_NEW);\n    this.handlers = inject(TuiItemsHandlersDirective);\n    this.textfield = inject(TuiTextfieldDirective);\n    this.texts = toSignal(inject(TUI_DATE_TEXTS));\n    this.calendar = tuiInjectAuxiliary(x => x instanceof TuiCalendar || x instanceof TuiCalendarRange);\n    this.filler = tuiDirectiveBinding(TuiTextfieldComponent, 'fillerSetter', computed(() => {\n      const {\n        mode,\n        separator\n      } = this.format();\n      const texts = this.texts() || '';\n      return texts && changeDateSeparator(texts[mode], separator);\n    }), {});\n    this.mobile = inject(TUI_IS_MOBILE);\n    this.open = tuiDropdownOpen();\n    this.icon = tuiTextfieldIconBinding(TUI_INPUT_DATE_OPTIONS_NEW);\n    this.dropdownEnabled = tuiDropdownEnabled(computed(() => !this.native && this.interactive()));\n    this.format = toSignal(inject(TUI_DATE_FORMAT), {\n      initialValue: TUI_DEFAULT_DATE_FORMAT\n    });\n    this.mask = tuiMaskito(computed(() => maskitoDateOptionsGenerator({\n      separator: this.format().separator,\n      mode: TUI_DATE_ADAPTER[this.format().mode],\n      min: this.min().toLocalNativeDate(),\n      max: this.max().toLocalNativeDate()\n    })));\n    this.valueEffect = effect(() => {\n      const value = this.value()?.toString(this.format().mode, this.format().separator) ?? (this.filler().length === this.el.value.length ? '' : this.el.value);\n      this.textfield.value.set(value);\n    }, TUI_ALLOW_SIGNAL_WRITES);\n    this.calendarIn = effect(() => {\n      if (this.calendar()) {\n        this.processCalendar(this.calendar());\n      }\n    }, TUI_ALLOW_SIGNAL_WRITES);\n    this.calendarOut = effect(onCleanup => {\n      const subscription = this.calendar()?.valueChange.subscribe(value => {\n        this.onChange(value);\n        this.open.set(false);\n        this.el.blur();\n      });\n      onCleanup(() => subscription?.unsubscribe());\n    });\n    this.native = this.el.type === 'date' && this.mobile;\n    this.min = signal(this.options.min);\n    this.max = signal(this.options.max);\n  }\n  set minSetter(min) {\n    this.min.set(min || this.options.min);\n  }\n  set maxSetter(max) {\n    this.max.set(max || this.options.max);\n  }\n  processCalendar(calendar) {\n    calendar.value = this.value();\n    calendar.disabledItemHandler = this.handlers.disabledItemHandler();\n    calendar.min = this.min();\n    calendar.max = this.max();\n  }\n  onClick() {\n    if (!this.mobile) {\n      this.open.update(open => !open);\n    }\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiInputDateBase_BaseFactory;\n      return function TuiInputDateBase_Factory(t) {\n        return (ɵTuiInputDateBase_BaseFactory || (ɵTuiInputDateBase_BaseFactory = i0.ɵɵgetInheritedFactory(TuiInputDateBase)))(t || TuiInputDateBase);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiInputDateBase,\n      hostVars: 2,\n      hostBindings: function TuiInputDateBase_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function TuiInputDateBase_input_HostBindingHandler($event) {\n            return ctx.onValueChange($event.target.value);\n          })(\"click.capture.stop\", function TuiInputDateBase_click_capture_stop_HostBindingHandler() {\n            return ctx.onClick();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"disabled\", ctx.disabled());\n          i0.ɵɵattribute(\"inputmode\", ctx.mobile && ctx.open() ? \"none\" : \"numeric\");\n        }\n      },\n      inputs: {\n        minSetter: [i0.ɵɵInputFlags.None, \"min\", \"minSetter\"],\n        maxSetter: [i0.ɵɵInputFlags.None, \"max\", \"maxSetter\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputDateBase, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      host: {\n        '[attr.inputmode]': 'mobile && open() ? \"none\" : \"numeric\"',\n        '[disabled]': 'disabled()',\n        '(input)': 'onValueChange($event.target.value)',\n        '(click.capture.stop)': 'onClick()'\n      }\n    }]\n  }], null, {\n    minSetter: [{\n      type: Input,\n      args: ['min']\n    }],\n    maxSetter: [{\n      type: Input,\n      args: ['max']\n    }]\n  });\n})();\nclass TuiInputDateDirective extends TuiInputDateBase {\n  constructor() {\n    super(...arguments);\n    this.identity = inject(TUI_ITEMS_HANDLERS).identityMatcher.set((a, b) => a.daySame(b));\n  }\n  onValueChange(value) {\n    this.control?.control?.updateValueAndValidity({\n      emitEvent: false\n    });\n    this.onChange(value.length === DATE_FILLER_LENGTH ? TuiDay.normalizeParse(value, this.format().mode) : null);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiInputDateDirective_BaseFactory;\n      return function TuiInputDateDirective_Factory(t) {\n        return (ɵTuiInputDateDirective_BaseFactory || (ɵTuiInputDateDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiInputDateDirective)))(t || TuiInputDateDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiInputDateDirective,\n      selectors: [[\"input\", \"tuiInputDate\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsOptionContent(TuiSelectOption), tuiAsControl(TuiInputDateDirective), tuiValueTransformerFrom(TUI_INPUT_DATE_OPTIONS_NEW)]), i0.ɵɵHostDirectivesFeature([i1.TuiWithTextfield, i2.TuiDropdownAuto, i3.TuiItemsHandlersValidator, i4.MaskitoDirective]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputDateDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiInputDate]',\n      providers: [tuiAsOptionContent(TuiSelectOption), tuiAsControl(TuiInputDateDirective), tuiValueTransformerFrom(TUI_INPUT_DATE_OPTIONS_NEW)],\n      hostDirectives: [TuiWithTextfield, TuiDropdownAuto, TuiItemsHandlersValidator, MaskitoDirective]\n    }]\n  }], null, null);\n})();\nclass TuiInputDateComponent {\n  constructor() {\n    this.host = inject(TuiInputDateDirective);\n    this.list = null;\n  }\n  onInput(value) {\n    if (!value) {\n      return this.host.onChange(null);\n    }\n    const [year = 0, month = 0, day = 0] = value.split('-').map(Number);\n    this.host.onChange(new TuiDay(year, month - 1, day));\n  }\n  static {\n    this.ɵfac = function TuiInputDateComponent_Factory(t) {\n      return new (t || TuiInputDateComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiInputDateComponent,\n      selectors: [[\"input\", \"tuiInputDate\", \"\", \"type\", \"date\"]],\n      hostAttrs: [\"ngSkipHydration\", \"true\"],\n      hostVars: 2,\n      hostBindings: function TuiInputDateComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"type\", \"text\");\n          i0.ɵɵattribute(\"list\", null);\n        }\n      },\n      inputs: {\n        list: \"list\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"ngIf\"], [\"type\", \"date\", 3, \"max\", \"min\", \"value\", \"click.stop.zoneless\", \"input\", \"pointerdown.stop.zoneless\", 4, \"tuiTextfieldContent\"], [\"type\", \"date\", 3, \"click.stop.zoneless\", \"input\", \"pointerdown.stop.zoneless\", \"max\", \"min\", \"value\"]],\n      template: function TuiInputDateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiInputDateComponent_ng_container_0_Template, 2, 0, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.host.native);\n        }\n      },\n      dependencies: [NgIf, TuiTextfieldContent],\n      styles: [\"tui-textfield input[tuiInputDate]~.t-content input[type=date]{position:absolute;top:0;left:auto;right:0;bottom:0;inline-size:2.5rem;padding:0;opacity:0}tui-textfield input[tuiInputDate]~.t-content input[type=date]::-webkit-calendar-picker-indicator{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputDateComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiInputDate][type=\"date\"]',\n      imports: [NgIf, TuiTextfieldContent],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        ngSkipHydration: 'true',\n        '[type]': '\"text\"',\n        '[attr.list]': 'null'\n      },\n      template: \"<ng-container *ngIf=\\\"host.native\\\">\\n    <input\\n        *tuiTextfieldContent\\n        type=\\\"date\\\"\\n        [attr.list]=\\\"list\\\"\\n        [max]=\\\"host.max().toJSON()\\\"\\n        [min]=\\\"host.min().toJSON()\\\"\\n        [value]=\\\"host.value()?.toJSON()\\\"\\n        (click.stop.zoneless)=\\\"(0)\\\"\\n        (input)=\\\"onInput($any($event.target).value)\\\"\\n        (pointerdown.stop.zoneless)=\\\"(0)\\\"\\n    />\\n</ng-container>\\n\",\n      styles: [\"tui-textfield input[tuiInputDate]~.t-content input[type=date]{position:absolute;top:0;left:auto;right:0;bottom:0;inline-size:2.5rem;padding:0;opacity:0}tui-textfield input[tuiInputDate]~.t-content input[type=date]::-webkit-calendar-picker-indicator{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}\\n\"]\n    }]\n  }], null, {\n    list: [{\n      type: Input\n    }]\n  });\n})();\nconst TuiInputDate = [TuiInputDateDirective, TuiInputDateComponent, TuiCalendar];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DATE_ADAPTER, TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW, TUI_INPUT_DATE_OPTIONS_NEW, TuiInputDate, TuiInputDateBase, TuiInputDateComponent, TuiInputDateDirective, tuiInputDateOptionsProviderNew };", "map": {"version": 3, "names": ["TuiCalendar", "NgIf", "i0", "inject", "computed", "effect", "signal", "Directive", "Input", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "TUI_FIRST_DAY", "TUI_LAST_DAY", "DATE_FILLER_LENGTH", "TuiDay", "i1", "TuiTextfieldDirective", "tuiInjectAuxiliary", "TuiTextfieldComponent", "tuiTextfieldIconBinding", "TuiWithTextfield", "TuiTextfieldContent", "toSignal", "i4", "MaskitoDirective", "maskitoDateOptionsGenerator", "TUI_IDENTITY_VALUE_TRANSFORMER", "TuiControl", "tuiAsControl", "tuiValueTransformerFrom", "TUI_ALLOW_SIGNAL_WRITES", "TUI_IS_MOBILE", "tuiInjectElement", "tuiDirectiveBinding", "changeDateSeparator", "tuiAsOptionContent", "i2", "tuiDropdownOpen", "tuiDropdownEnabled", "TuiDropdownAuto", "i3", "TuiItemsHandlersDirective", "TUI_ITEMS_HANDLERS", "TuiItemsHandlersValidator", "TUI_DATE_FORMAT", "TUI_DEFAULT_DATE_FORMAT", "TuiCalendarRange", "TuiSelectOption", "TUI_DATE_TEXTS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tuiCreateOptions", "_c0", "TuiInputDateComponent_ng_container_0_input_1_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiInputDateComponent_ng_container_0_input_1_Template_input_click_stop_zoneless_0_listener", "ɵɵrestoreView", "ɵɵresetView", "TuiInputDateComponent_ng_container_0_input_1_Template_input_input_0_listener", "$event", "ctx_r1", "ɵɵnextContext", "onInput", "target", "value", "TuiInputDateComponent_ng_container_0_input_1_Template_input_pointerdown_stop_zoneless_0_listener", "ɵɵelementEnd", "tmp_4_0", "ɵɵproperty", "host", "max", "toJSON", "min", "ɵɵattribute", "list", "TuiInputDateComponent_ng_container_0_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW", "icon", "valueTransformer", "TUI_INPUT_DATE_OPTIONS_NEW", "tuiInputDateOptionsProviderNew", "TUI_DATE_ADAPTER", "DMY", "MDY", "YMD", "TuiInputDateBase", "constructor", "arguments", "el", "options", "handlers", "textfield", "texts", "calendar", "x", "filler", "mode", "separator", "format", "mobile", "open", "dropdownEnabled", "native", "interactive", "initialValue", "mask", "toLocalNativeDate", "valueEffect", "toString", "length", "set", "calendarIn", "processCalendar", "calendarOut", "onCleanup", "subscription", "valueChange", "subscribe", "onChange", "blur", "unsubscribe", "type", "minSetter", "maxSetter", "disabledItemHandler", "onClick", "update", "ɵfac", "ɵTuiInputDateBase_BaseFactory", "TuiInputDateBase_Factory", "t", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiInputDateBase_HostBindings", "TuiInputDateBase_input_HostBindingHandler", "onValueChange", "TuiInputDateBase_click_capture_stop_HostBindingHandler", "ɵɵhostProperty", "disabled", "inputs", "ɵɵInputFlags", "None", "standalone", "features", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "TuiInputDateDirective", "identity", "identityMatcher", "a", "b", "daySame", "control", "updateValueAndValidity", "emitEvent", "normalizeParse", "ɵTuiInputDateDirective_BaseFactory", "TuiInputDateDirective_Factory", "selectors", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "selector", "providers", "hostDirectives", "TuiInputDateComponent", "year", "month", "day", "split", "map", "Number", "TuiInputDateComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "hostAttrs", "TuiInputDateComponent_HostBindings", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "consts", "template", "TuiInputDateComponent_Template", "dependencies", "styles", "encapsulation", "changeDetection", "imports", "OnPush", "ngSkipHydration", "TuiInputDate"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-input-date.mjs"], "sourcesContent": ["import { TuiCalendar } from '@taiga-ui/core/components/calendar';\nimport { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, computed, effect, signal, Directive, Input, Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\nimport { TUI_FIRST_DAY, TUI_LAST_DAY, DATE_FILLER_LENGTH, TuiDay } from '@taiga-ui/cdk/date-time';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldDirective, tuiInjectAuxiliary, TuiTextfieldComponent, tuiTextfieldIconBinding, TuiWithTextfield, TuiTextfieldContent } from '@taiga-ui/core/components/textfield';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport * as i4 from '@maskito/angular';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { maskitoDateOptionsGenerator } from '@maskito/kit';\nimport { TUI_IDENTITY_VALUE_TRANSFORMER, TuiControl, tuiAsControl, tuiValueTransformerFrom } from '@taiga-ui/cdk/classes';\nimport { TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiDirectiveBinding, changeDateSeparator } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiAsOptionContent } from '@taiga-ui/core/components/data-list';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { tuiDropdownOpen, tuiDropdownEnabled, TuiDropdownAuto } from '@taiga-ui/core/directives/dropdown';\nimport * as i3 from '@taiga-ui/core/directives/items-handlers';\nimport { TuiItemsHandlersDirective, TUI_ITEMS_HANDLERS, TuiItemsHandlersValidator } from '@taiga-ui/core/directives/items-handlers';\nimport { TUI_DATE_FORMAT, TUI_DEFAULT_DATE_FORMAT } from '@taiga-ui/core/tokens';\nimport { TuiCalendarRange } from '@taiga-ui/kit/components/calendar-range';\nimport { TuiSelectOption } from '@taiga-ui/kit/components/select';\nimport { TUI_DATE_TEXTS } from '@taiga-ui/kit/tokens';\nimport { tuiMaskito } from '@taiga-ui/kit/utils';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\n\nconst TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW = {\n    icon: () => '@tui.calendar',\n    min: TUI_FIRST_DAY,\n    max: TUI_LAST_DAY,\n    valueTransformer: TUI_IDENTITY_VALUE_TRANSFORMER,\n};\nconst [TUI_INPUT_DATE_OPTIONS_NEW, tuiInputDateOptionsProviderNew] = tuiCreateOptions(TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW);\n\nconst TUI_DATE_ADAPTER = {\n    DMY: 'dd/mm/yyyy',\n    MDY: 'mm/dd/yyyy',\n    YMD: 'yyyy/mm/dd',\n};\nclass TuiInputDateBase extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.el = tuiInjectElement();\n        this.options = inject(TUI_INPUT_DATE_OPTIONS_NEW);\n        this.handlers = inject(TuiItemsHandlersDirective);\n        this.textfield = inject(TuiTextfieldDirective);\n        this.texts = toSignal(inject(TUI_DATE_TEXTS));\n        this.calendar = tuiInjectAuxiliary((x) => x instanceof TuiCalendar || x instanceof TuiCalendarRange);\n        this.filler = tuiDirectiveBinding(TuiTextfieldComponent, 'fillerSetter', computed(() => {\n            const { mode, separator } = this.format();\n            const texts = this.texts() || '';\n            return texts && changeDateSeparator(texts[mode], separator);\n        }), {});\n        this.mobile = inject(TUI_IS_MOBILE);\n        this.open = tuiDropdownOpen();\n        this.icon = tuiTextfieldIconBinding(TUI_INPUT_DATE_OPTIONS_NEW);\n        this.dropdownEnabled = tuiDropdownEnabled(computed(() => !this.native && this.interactive()));\n        this.format = toSignal(inject(TUI_DATE_FORMAT), {\n            initialValue: TUI_DEFAULT_DATE_FORMAT,\n        });\n        this.mask = tuiMaskito(computed(() => maskitoDateOptionsGenerator({\n            separator: this.format().separator,\n            mode: TUI_DATE_ADAPTER[this.format().mode],\n            min: this.min().toLocalNativeDate(),\n            max: this.max().toLocalNativeDate(),\n        })));\n        this.valueEffect = effect(() => {\n            const value = this.value()?.toString(this.format().mode, this.format().separator) ??\n                (this.filler().length === this.el.value.length ? '' : this.el.value);\n            this.textfield.value.set(value);\n        }, TUI_ALLOW_SIGNAL_WRITES);\n        this.calendarIn = effect(() => {\n            if (this.calendar()) {\n                this.processCalendar(this.calendar());\n            }\n        }, TUI_ALLOW_SIGNAL_WRITES);\n        this.calendarOut = effect((onCleanup) => {\n            const subscription = this.calendar()?.valueChange.subscribe((value) => {\n                this.onChange(value);\n                this.open.set(false);\n                this.el.blur();\n            });\n            onCleanup(() => subscription?.unsubscribe());\n        });\n        this.native = this.el.type === 'date' && this.mobile;\n        this.min = signal(this.options.min);\n        this.max = signal(this.options.max);\n    }\n    set minSetter(min) {\n        this.min.set(min || this.options.min);\n    }\n    set maxSetter(max) {\n        this.max.set(max || this.options.max);\n    }\n    processCalendar(calendar) {\n        calendar.value = this.value();\n        calendar.disabledItemHandler = this.handlers.disabledItemHandler();\n        calendar.min = this.min();\n        calendar.max = this.max();\n    }\n    onClick() {\n        if (!this.mobile) {\n            this.open.update((open) => !open);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputDateBase, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputDateBase, isStandalone: true, inputs: { minSetter: [\"min\", \"minSetter\"], maxSetter: [\"max\", \"maxSetter\"] }, host: { listeners: { \"input\": \"onValueChange($event.target.value)\", \"click.capture.stop\": \"onClick()\" }, properties: { \"attr.inputmode\": \"mobile && open() ? \\\"none\\\" : \\\"numeric\\\"\", \"disabled\": \"disabled()\" } }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputDateBase, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    host: {\n                        '[attr.inputmode]': 'mobile && open() ? \"none\" : \"numeric\"',\n                        '[disabled]': 'disabled()',\n                        '(input)': 'onValueChange($event.target.value)',\n                        '(click.capture.stop)': 'onClick()',\n                    },\n                }]\n        }], propDecorators: { minSetter: [{\n                type: Input,\n                args: ['min']\n            }], maxSetter: [{\n                type: Input,\n                args: ['max']\n            }] } });\nclass TuiInputDateDirective extends TuiInputDateBase {\n    constructor() {\n        super(...arguments);\n        this.identity = inject(TUI_ITEMS_HANDLERS).identityMatcher.set((a, b) => a.daySame(b));\n    }\n    onValueChange(value) {\n        this.control?.control?.updateValueAndValidity({ emitEvent: false });\n        this.onChange(value.length === DATE_FILLER_LENGTH\n            ? TuiDay.normalizeParse(value, this.format().mode)\n            : null);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputDateDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputDateDirective, isStandalone: true, selector: \"input[tuiInputDate]\", providers: [\n            tuiAsOptionContent(TuiSelectOption),\n            tuiAsControl(TuiInputDateDirective),\n            tuiValueTransformerFrom(TUI_INPUT_DATE_OPTIONS_NEW),\n        ], usesInheritance: true, hostDirectives: [{ directive: i1.TuiWithTextfield }, { directive: i2.TuiDropdownAuto }, { directive: i3.TuiItemsHandlersValidator }, { directive: i4.MaskitoDirective }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputDateDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiInputDate]',\n                    providers: [\n                        tuiAsOptionContent(TuiSelectOption),\n                        tuiAsControl(TuiInputDateDirective),\n                        tuiValueTransformerFrom(TUI_INPUT_DATE_OPTIONS_NEW),\n                    ],\n                    hostDirectives: [\n                        TuiWithTextfield,\n                        TuiDropdownAuto,\n                        TuiItemsHandlersValidator,\n                        MaskitoDirective,\n                    ],\n                }]\n        }] });\n\nclass TuiInputDateComponent {\n    constructor() {\n        this.host = inject(TuiInputDateDirective);\n        this.list = null;\n    }\n    onInput(value) {\n        if (!value) {\n            return this.host.onChange(null);\n        }\n        const [year = 0, month = 0, day = 0] = value.split('-').map(Number);\n        this.host.onChange(new TuiDay(year, month - 1, day));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputDateComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputDateComponent, isStandalone: true, selector: \"input[tuiInputDate][type=\\\"date\\\"]\", inputs: { list: \"list\" }, host: { attributes: { \"ngSkipHydration\": \"true\" }, properties: { \"type\": \"\\\"text\\\"\", \"attr.list\": \"null\" } }, ngImport: i0, template: \"<ng-container *ngIf=\\\"host.native\\\">\\n    <input\\n        *tuiTextfieldContent\\n        type=\\\"date\\\"\\n        [attr.list]=\\\"list\\\"\\n        [max]=\\\"host.max().toJSON()\\\"\\n        [min]=\\\"host.min().toJSON()\\\"\\n        [value]=\\\"host.value()?.toJSON()\\\"\\n        (click.stop.zoneless)=\\\"(0)\\\"\\n        (input)=\\\"onInput($any($event.target).value)\\\"\\n        (pointerdown.stop.zoneless)=\\\"(0)\\\"\\n    />\\n</ng-container>\\n\", styles: [\"tui-textfield input[tuiInputDate]~.t-content input[type=date]{position:absolute;top:0;left:auto;right:0;bottom:0;inline-size:2.5rem;padding:0;opacity:0}tui-textfield input[tuiInputDate]~.t-content input[type=date]::-webkit-calendar-picker-indicator{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: TuiTextfieldContent, selector: \"ng-template[tuiTextfieldContent]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputDateComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'input[tuiInputDate][type=\"date\"]', imports: [NgIf, TuiTextfieldContent], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        ngSkipHydration: 'true',\n                        '[type]': '\"text\"',\n                        '[attr.list]': 'null',\n                    }, template: \"<ng-container *ngIf=\\\"host.native\\\">\\n    <input\\n        *tuiTextfieldContent\\n        type=\\\"date\\\"\\n        [attr.list]=\\\"list\\\"\\n        [max]=\\\"host.max().toJSON()\\\"\\n        [min]=\\\"host.min().toJSON()\\\"\\n        [value]=\\\"host.value()?.toJSON()\\\"\\n        (click.stop.zoneless)=\\\"(0)\\\"\\n        (input)=\\\"onInput($any($event.target).value)\\\"\\n        (pointerdown.stop.zoneless)=\\\"(0)\\\"\\n    />\\n</ng-container>\\n\", styles: [\"tui-textfield input[tuiInputDate]~.t-content input[type=date]{position:absolute;top:0;left:auto;right:0;bottom:0;inline-size:2.5rem;padding:0;opacity:0}tui-textfield input[tuiInputDate]~.t-content input[type=date]::-webkit-calendar-picker-indicator{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}\\n\"] }]\n        }], propDecorators: { list: [{\n                type: Input\n            }] } });\n\nconst TuiInputDate = [\n    TuiInputDateDirective,\n    TuiInputDateComponent,\n    TuiCalendar,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DATE_ADAPTER, TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW, TUI_INPUT_DATE_OPTIONS_NEW, TuiInputDate, TuiInputDateBase, TuiInputDateComponent, TuiInputDateDirective, tuiInputDateOptionsProviderNew };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oCAAoC;AAChE,SAASC,IAAI,QAAQ,iBAAiB;AACtC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,QAAQ,eAAe;AACzI,SAASC,aAAa,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,MAAM,QAAQ,yBAAyB;AACjG,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,qBAAqB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,uBAAuB,EAAEC,gBAAgB,EAAEC,mBAAmB,QAAQ,qCAAqC;AACtL,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,OAAO,KAAKC,EAAE,MAAM,kBAAkB;AACtC,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,2BAA2B,QAAQ,cAAc;AAC1D,SAASC,8BAA8B,EAAEC,UAAU,EAAEC,YAAY,EAAEC,uBAAuB,QAAQ,uBAAuB;AACzH,SAASC,uBAAuB,QAAQ,yBAAyB;AACjE,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,mBAAmB,EAAEC,mBAAmB,QAAQ,mCAAmC;AAC5F,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,OAAO,KAAKC,EAAE,MAAM,oCAAoC;AACxD,SAASC,eAAe,EAAEC,kBAAkB,EAAEC,eAAe,QAAQ,oCAAoC;AACzG,OAAO,KAAKC,EAAE,MAAM,0CAA0C;AAC9D,SAASC,yBAAyB,EAAEC,kBAAkB,EAAEC,yBAAyB,QAAQ,0CAA0C;AACnI,SAASC,eAAe,EAAEC,uBAAuB,QAAQ,uBAAuB;AAChF,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,gBAAgB,QAAQ,wBAAwB;AAAC,MAAAC,GAAA;AAAA,SAAAC,sDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAiF2CtD,EAAE,CAAAuD,gBAAA;IAAFvD,EAAE,CAAAwD,cAAA,cAuEwoB,CAAC;IAvE3oBxD,EAAE,CAAAyD,UAAA,iCAAAC,2FAAA;MAAF1D,EAAE,CAAA2D,aAAA,CAAAL,GAAA;MAAA,OAAFtD,EAAE,CAAA4D,WAAA,CAuEwhB,CAAC;IAAA,CAAE,CAAC,mBAAAC,6EAAAC,MAAA;MAvE9hB9D,EAAE,CAAA2D,aAAA,CAAAL,GAAA;MAAA,MAAAS,MAAA,GAAF/D,EAAE,CAAAgE,aAAA;MAAA,OAAFhE,EAAE,CAAA4D,WAAA,CAuEgjBG,MAAA,CAAAE,OAAA,CAAAH,MAAA,CAAAI,MAAA,CAAAC,KAAiC,CAAC;IAAA,CAAC,CAAC,uCAAAC,iGAAA;MAvEtlBpE,EAAE,CAAA2D,aAAA,CAAAL,GAAA;MAAA,OAAFtD,EAAE,CAAA4D,WAAA,CAuE6nB,CAAC;IAAA,CAAE,CAAC;IAvEnoB5D,EAAE,CAAAqE,YAAA,CAuEwoB,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,IAAAkB,OAAA;IAAA,MAAAP,MAAA,GAvE3oB/D,EAAE,CAAAgE,aAAA;IAAFhE,EAAE,CAAAuE,UAAA,QAAAR,MAAA,CAAAS,IAAA,CAAAC,GAAA,GAAAC,MAAA,EAuEia,CAAC,QAAAX,MAAA,CAAAS,IAAA,CAAAG,GAAA,GAAAD,MAAA,EAAsC,CAAC,WAAAJ,OAAA,GAAAP,MAAA,CAAAS,IAAA,CAAAL,KAAA,qBAAAG,OAAA,CAAAI,MAAA,EAA2C,CAAC;IAvEvf1E,EAAE,CAAA4E,WAAA,SAAAb,MAAA,CAAAc,IAAA;EAAA;AAAA;AAAA,SAAAC,8CAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFpD,EAAE,CAAA+E,uBAAA,EAuE2R,CAAC;IAvE9R/E,EAAE,CAAAgF,UAAA,IAAA7B,qDAAA,kBAuEwoB,CAAC;IAvE3oBnD,EAAE,CAAAiF,qBAAA;EAAA;AAAA;AA/EvG,MAAMC,kCAAkC,GAAG;EACvCC,IAAI,EAAEA,CAAA,KAAM,eAAe;EAC3BR,GAAG,EAAEjE,aAAa;EAClB+D,GAAG,EAAE9D,YAAY;EACjByE,gBAAgB,EAAE3D;AACtB,CAAC;AACD,MAAM,CAAC4D,0BAA0B,EAAEC,8BAA8B,CAAC,GAAGrC,gBAAgB,CAACiC,kCAAkC,CAAC;AAEzH,MAAMK,gBAAgB,GAAG;EACrBC,GAAG,EAAE,YAAY;EACjBC,GAAG,EAAE,YAAY;EACjBC,GAAG,EAAE;AACT,CAAC;AACD,MAAMC,gBAAgB,SAASjE,UAAU,CAAC;EACtCkE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,EAAE,GAAG/D,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACgE,OAAO,GAAG9F,MAAM,CAACoF,0BAA0B,CAAC;IACjD,IAAI,CAACW,QAAQ,GAAG/F,MAAM,CAACuC,yBAAyB,CAAC;IACjD,IAAI,CAACyD,SAAS,GAAGhG,MAAM,CAACc,qBAAqB,CAAC;IAC9C,IAAI,CAACmF,KAAK,GAAG7E,QAAQ,CAACpB,MAAM,CAAC8C,cAAc,CAAC,CAAC;IAC7C,IAAI,CAACoD,QAAQ,GAAGnF,kBAAkB,CAAEoF,CAAC,IAAKA,CAAC,YAAYtG,WAAW,IAAIsG,CAAC,YAAYvD,gBAAgB,CAAC;IACpG,IAAI,CAACwD,MAAM,GAAGrE,mBAAmB,CAACf,qBAAqB,EAAE,cAAc,EAAEf,QAAQ,CAAC,MAAM;MACpF,MAAM;QAAEoG,IAAI;QAAEC;MAAU,CAAC,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;MACzC,MAAMN,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,IAAI,EAAE;MAChC,OAAOA,KAAK,IAAIjE,mBAAmB,CAACiE,KAAK,CAACI,IAAI,CAAC,EAAEC,SAAS,CAAC;IAC/D,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACP,IAAI,CAACE,MAAM,GAAGxG,MAAM,CAAC6B,aAAa,CAAC;IACnC,IAAI,CAAC4E,IAAI,GAAGtE,eAAe,CAAC,CAAC;IAC7B,IAAI,CAAC+C,IAAI,GAAGjE,uBAAuB,CAACmE,0BAA0B,CAAC;IAC/D,IAAI,CAACsB,eAAe,GAAGtE,kBAAkB,CAACnC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC0G,MAAM,IAAI,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;IAC7F,IAAI,CAACL,MAAM,GAAGnF,QAAQ,CAACpB,MAAM,CAAC0C,eAAe,CAAC,EAAE;MAC5CmE,YAAY,EAAElE;IAClB,CAAC,CAAC;IACF,IAAI,CAACmE,IAAI,GAAG/D,UAAU,CAAC9C,QAAQ,CAAC,MAAMsB,2BAA2B,CAAC;MAC9D+E,SAAS,EAAE,IAAI,CAACC,MAAM,CAAC,CAAC,CAACD,SAAS;MAClCD,IAAI,EAAEf,gBAAgB,CAAC,IAAI,CAACiB,MAAM,CAAC,CAAC,CAACF,IAAI,CAAC;MAC1C3B,GAAG,EAAE,IAAI,CAACA,GAAG,CAAC,CAAC,CAACqC,iBAAiB,CAAC,CAAC;MACnCvC,GAAG,EAAE,IAAI,CAACA,GAAG,CAAC,CAAC,CAACuC,iBAAiB,CAAC;IACtC,CAAC,CAAC,CAAC,CAAC;IACJ,IAAI,CAACC,WAAW,GAAG9G,MAAM,CAAC,MAAM;MAC5B,MAAMgE,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,EAAE+C,QAAQ,CAAC,IAAI,CAACV,MAAM,CAAC,CAAC,CAACF,IAAI,EAAE,IAAI,CAACE,MAAM,CAAC,CAAC,CAACD,SAAS,CAAC,KAC5E,IAAI,CAACF,MAAM,CAAC,CAAC,CAACc,MAAM,KAAK,IAAI,CAACrB,EAAE,CAAC3B,KAAK,CAACgD,MAAM,GAAG,EAAE,GAAG,IAAI,CAACrB,EAAE,CAAC3B,KAAK,CAAC;MACxE,IAAI,CAAC8B,SAAS,CAAC9B,KAAK,CAACiD,GAAG,CAACjD,KAAK,CAAC;IACnC,CAAC,EAAEtC,uBAAuB,CAAC;IAC3B,IAAI,CAACwF,UAAU,GAAGlH,MAAM,CAAC,MAAM;MAC3B,IAAI,IAAI,CAACgG,QAAQ,CAAC,CAAC,EAAE;QACjB,IAAI,CAACmB,eAAe,CAAC,IAAI,CAACnB,QAAQ,CAAC,CAAC,CAAC;MACzC;IACJ,CAAC,EAAEtE,uBAAuB,CAAC;IAC3B,IAAI,CAAC0F,WAAW,GAAGpH,MAAM,CAAEqH,SAAS,IAAK;MACrC,MAAMC,YAAY,GAAG,IAAI,CAACtB,QAAQ,CAAC,CAAC,EAAEuB,WAAW,CAACC,SAAS,CAAExD,KAAK,IAAK;QACnE,IAAI,CAACyD,QAAQ,CAACzD,KAAK,CAAC;QACpB,IAAI,CAACuC,IAAI,CAACU,GAAG,CAAC,KAAK,CAAC;QACpB,IAAI,CAACtB,EAAE,CAAC+B,IAAI,CAAC,CAAC;MAClB,CAAC,CAAC;MACFL,SAAS,CAAC,MAAMC,YAAY,EAAEK,WAAW,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC;IACF,IAAI,CAAClB,MAAM,GAAG,IAAI,CAACd,EAAE,CAACiC,IAAI,KAAK,MAAM,IAAI,IAAI,CAACtB,MAAM;IACpD,IAAI,CAAC9B,GAAG,GAAGvE,MAAM,CAAC,IAAI,CAAC2F,OAAO,CAACpB,GAAG,CAAC;IACnC,IAAI,CAACF,GAAG,GAAGrE,MAAM,CAAC,IAAI,CAAC2F,OAAO,CAACtB,GAAG,CAAC;EACvC;EACA,IAAIuD,SAASA,CAACrD,GAAG,EAAE;IACf,IAAI,CAACA,GAAG,CAACyC,GAAG,CAACzC,GAAG,IAAI,IAAI,CAACoB,OAAO,CAACpB,GAAG,CAAC;EACzC;EACA,IAAIsD,SAASA,CAACxD,GAAG,EAAE;IACf,IAAI,CAACA,GAAG,CAAC2C,GAAG,CAAC3C,GAAG,IAAI,IAAI,CAACsB,OAAO,CAACtB,GAAG,CAAC;EACzC;EACA6C,eAAeA,CAACnB,QAAQ,EAAE;IACtBA,QAAQ,CAAChC,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;IAC7BgC,QAAQ,CAAC+B,mBAAmB,GAAG,IAAI,CAAClC,QAAQ,CAACkC,mBAAmB,CAAC,CAAC;IAClE/B,QAAQ,CAACxB,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACzBwB,QAAQ,CAAC1B,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;EAC7B;EACA0D,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAAC1B,MAAM,EAAE;MACd,IAAI,CAACC,IAAI,CAAC0B,MAAM,CAAE1B,IAAI,IAAK,CAACA,IAAI,CAAC;IACrC;EACJ;EACA;IAAS,IAAI,CAAC2B,IAAI;MAAA,IAAAC,6BAAA;MAAA,gBAAAC,yBAAAC,CAAA;QAAA,QAAAF,6BAAA,KAAAA,6BAAA,GAA+EtI,EAAE,CAAAyI,qBAAA,CAAQ9C,gBAAgB,IAAA6C,CAAA,IAAhB7C,gBAAgB;MAAA;IAAA,IAAqD;EAAE;EAClL;IAAS,IAAI,CAAC+C,IAAI,kBAD+E1I,EAAE,CAAA2I,iBAAA;MAAAZ,IAAA,EACJpC,gBAAgB;MAAAiD,QAAA;MAAAC,YAAA,WAAAC,8BAAA1F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADdpD,EAAE,CAAAyD,UAAA,mBAAAsF,0CAAAjF,MAAA;YAAA,OACJT,GAAA,CAAA2F,aAAA,CAAAlF,MAAA,CAAAI,MAAA,CAAAC,KAAiC,CAAC;UAAA,CAAnB,CAAC,gCAAA8E,uDAAA;YAAA,OAAhB5F,GAAA,CAAA8E,OAAA,CAAQ,CAAC;UAAA,CAAM,CAAC;QAAA;QAAA,IAAA/E,EAAA;UADdpD,EAAE,CAAAkJ,cAAA,aACJ7F,GAAA,CAAA8F,QAAA,CAAS,CAAM,CAAC;UADdnJ,EAAE,CAAA4E,WAAA,cAAAvB,GAAA,CAAAoD,MAAA,IACMpD,GAAA,CAAAqD,IAAA,CAAK,CAAC,GAAG,MAAM,GAAG,SAAS;QAAA;MAAA;MAAA0C,MAAA;QAAApB,SAAA,GADnChI,EAAE,CAAAqJ,YAAA,CAAAC,IAAA;QAAArB,SAAA,GAAFjI,EAAE,CAAAqJ,YAAA,CAAAC,IAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFxJ,EAAE,CAAAyJ,0BAAA;IAAA,EAC0W;EAAE;AACnd;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG1J,EAAE,CAAA2J,iBAAA,CAGXhE,gBAAgB,EAAc,CAAC;IAC/GoC,IAAI,EAAE1H,SAAS;IACfuJ,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChB/E,IAAI,EAAE;QACF,kBAAkB,EAAE,uCAAuC;QAC3D,YAAY,EAAE,YAAY;QAC1B,SAAS,EAAE,oCAAoC;QAC/C,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEwD,SAAS,EAAE,CAAC;MAC1BD,IAAI,EAAEzH,KAAK;MACXsJ,IAAI,EAAE,CAAC,KAAK;IAChB,CAAC,CAAC;IAAE3B,SAAS,EAAE,CAAC;MACZF,IAAI,EAAEzH,KAAK;MACXsJ,IAAI,EAAE,CAAC,KAAK;IAChB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMC,qBAAqB,SAASlE,gBAAgB,CAAC;EACjDC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACiE,QAAQ,GAAG7J,MAAM,CAACwC,kBAAkB,CAAC,CAACsH,eAAe,CAAC3C,GAAG,CAAC,CAAC4C,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,OAAO,CAACD,CAAC,CAAC,CAAC;EAC1F;EACAjB,aAAaA,CAAC7E,KAAK,EAAE;IACjB,IAAI,CAACgG,OAAO,EAAEA,OAAO,EAAEC,sBAAsB,CAAC;MAAEC,SAAS,EAAE;IAAM,CAAC,CAAC;IACnE,IAAI,CAACzC,QAAQ,CAACzD,KAAK,CAACgD,MAAM,KAAKvG,kBAAkB,GAC3CC,MAAM,CAACyJ,cAAc,CAACnG,KAAK,EAAE,IAAI,CAACqC,MAAM,CAAC,CAAC,CAACF,IAAI,CAAC,GAChD,IAAI,CAAC;EACf;EACA;IAAS,IAAI,CAAC+B,IAAI;MAAA,IAAAkC,kCAAA;MAAA,gBAAAC,8BAAAhC,CAAA;QAAA,QAAA+B,kCAAA,KAAAA,kCAAA,GAhC+EvK,EAAE,CAAAyI,qBAAA,CAgCQoB,qBAAqB,IAAArB,CAAA,IAArBqB,qBAAqB;MAAA;IAAA,IAAqD;EAAE;EACvL;IAAS,IAAI,CAACnB,IAAI,kBAjC+E1I,EAAE,CAAA2I,iBAAA;MAAAZ,IAAA,EAiCJ8B,qBAAqB;MAAAY,SAAA;MAAAlB,UAAA;MAAAC,QAAA,GAjCnBxJ,EAAE,CAAA0K,kBAAA,CAiCmF,CAC9KxI,kBAAkB,CAACY,eAAe,CAAC,EACnCnB,YAAY,CAACkI,qBAAqB,CAAC,EACnCjI,uBAAuB,CAACyD,0BAA0B,CAAC,CACtD,GArC4FrF,EAAE,CAAA2K,uBAAA,EAqCvC7J,EAAE,CAACK,gBAAgB,EAAiBgB,EAAE,CAACG,eAAe,EAAiBC,EAAE,CAACG,yBAAyB,EAAiBpB,EAAE,CAACC,gBAAgB,IArClGvB,EAAE,CAAAyJ,0BAAA;IAAA,EAqCoH;EAAE;AAC7N;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvCqG1J,EAAE,CAAA2J,iBAAA,CAuCXE,qBAAqB,EAAc,CAAC;IACpH9B,IAAI,EAAE1H,SAAS;IACfuJ,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBqB,QAAQ,EAAE,qBAAqB;MAC/BC,SAAS,EAAE,CACP3I,kBAAkB,CAACY,eAAe,CAAC,EACnCnB,YAAY,CAACkI,qBAAqB,CAAC,EACnCjI,uBAAuB,CAACyD,0BAA0B,CAAC,CACtD;MACDyF,cAAc,EAAE,CACZ3J,gBAAgB,EAChBmB,eAAe,EACfI,yBAAyB,EACzBnB,gBAAgB;IAExB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMwJ,qBAAqB,CAAC;EACxBnF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpB,IAAI,GAAGvE,MAAM,CAAC4J,qBAAqB,CAAC;IACzC,IAAI,CAAChF,IAAI,GAAG,IAAI;EACpB;EACAZ,OAAOA,CAACE,KAAK,EAAE;IACX,IAAI,CAACA,KAAK,EAAE;MACR,OAAO,IAAI,CAACK,IAAI,CAACoD,QAAQ,CAAC,IAAI,CAAC;IACnC;IACA,MAAM,CAACoD,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,CAAC,GAAG/G,KAAK,CAACgH,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IACnE,IAAI,CAAC7G,IAAI,CAACoD,QAAQ,CAAC,IAAI/G,MAAM,CAACmK,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAEC,GAAG,CAAC,CAAC;EACxD;EACA;IAAS,IAAI,CAAC7C,IAAI,YAAAiD,8BAAA9C,CAAA;MAAA,YAAAA,CAAA,IAAyFuC,qBAAqB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAACQ,IAAI,kBAvE+EvL,EAAE,CAAAwL,iBAAA;MAAAzD,IAAA,EAuEJgD,qBAAqB;MAAAN,SAAA;MAAAgB,SAAA,sBAAyI,MAAM;MAAA7C,QAAA;MAAAC,YAAA,WAAA6C,mCAAAtI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvElKpD,EAAE,CAAAkJ,cAAA,SAuEJ,MAAoB,CAAC;UAvEnBlJ,EAAE,CAAA4E,WAAA,SAuEJ,IAAI;QAAA;MAAA;MAAAwE,MAAA;QAAAvE,IAAA;MAAA;MAAA0E,UAAA;MAAAC,QAAA,GAvEFxJ,EAAE,CAAA2L,mBAAA;MAAAC,KAAA,EAAA1I,GAAA;MAAA2I,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAA7I,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFpD,EAAE,CAAAgF,UAAA,IAAAF,6CAAA,yBAuE2R,CAAC;QAAA;QAAA,IAAA1B,EAAA;UAvE9RpD,EAAE,CAAAuE,UAAA,SAAAlB,GAAA,CAAAmB,IAAA,CAAAoC,MAuEwR,CAAC;QAAA;MAAA;MAAAsF,YAAA,GAAyvBnM,IAAI,EAA6FqB,mBAAmB;MAAA+K,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAkJ;EAAE;AACj4C;AACA;EAAA,QAAA3C,SAAA,oBAAAA,SAAA,KAzEqG1J,EAAE,CAAA2J,iBAAA,CAyEXoB,qBAAqB,EAAc,CAAC;IACpHhD,IAAI,EAAExH,SAAS;IACfqJ,IAAI,EAAE,CAAC;MAAEL,UAAU,EAAE,IAAI;MAAEqB,QAAQ,EAAE,kCAAkC;MAAE0B,OAAO,EAAE,CAACvM,IAAI,EAAEqB,mBAAmB,CAAC;MAAEgL,aAAa,EAAE5L,iBAAiB,CAAC8I,IAAI;MAAE+C,eAAe,EAAE5L,uBAAuB,CAAC8L,MAAM;MAAE/H,IAAI,EAAE;QACjMgI,eAAe,EAAE,MAAM;QACvB,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE;MACnB,CAAC;MAAER,QAAQ,EAAE,saAAsa;MAAEG,MAAM,EAAE,CAAC,6TAA6T;IAAE,CAAC;EAC1wB,CAAC,CAAC,QAAkB;IAAEtH,IAAI,EAAE,CAAC;MACrBkD,IAAI,EAAEzH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmM,YAAY,GAAG,CACjB5C,qBAAqB,EACrBkB,qBAAqB,EACrBjL,WAAW,CACd;;AAED;AACA;AACA;;AAEA,SAASyF,gBAAgB,EAAEL,kCAAkC,EAAEG,0BAA0B,EAAEoH,YAAY,EAAE9G,gBAAgB,EAAEoF,qBAAqB,EAAElB,qBAAqB,EAAEvE,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}