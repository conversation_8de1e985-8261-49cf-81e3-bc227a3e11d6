{"ast": null, "code": "import { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, signal, computed, ElementRef, Component, ChangeDetectionStrategy, ViewChild, Input, Directive } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TUI_IS_MOBILE, tuiFallbackValueProvider } from '@taiga-ui/cdk/tokens';\nimport { tuiDirectiveBinding, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport * as i2$1 from '@taiga-ui/core/components/textfield';\nimport { TUI_TEXTFIELD_OPTIONS, TuiTextfieldComponent, TuiTextfieldMultiComponent, tuiAsTextfieldAccessor, TuiTextfieldBase } from '@taiga-ui/core/components/textfield';\nimport { TuiAppearance } from '@taiga-ui/core/directives/appearance';\nimport { TuiHintDirective, TuiHintOverflow } from '@taiga-ui/core/directives/hint';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport * as i1 from '@taiga-ui/kit/components/chip';\nimport { TuiChip } from '@taiga-ui/kit/components/chip';\nimport { TuiFade } from '@taiga-ui/kit/directives/fade';\nimport { tuiInjectValue } from '@taiga-ui/kit/utils';\nimport { injectContext } from '@taiga-ui/polymorpheus';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { TuiControl, tuiAsControl } from '@taiga-ui/cdk/classes';\nimport { TuiActiveZone } from '@taiga-ui/cdk/directives/active-zone';\nimport * as i1$1 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport { tuiInjectElement, tuiGetClipboardDataText } from '@taiga-ui/cdk/utils/dom';\nimport { tuiDropdownOpen, TuiDropdownDirective } from '@taiga-ui/core/directives/dropdown';\nimport { filter } from 'rxjs';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nfunction TuiInputChipComponent_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 3);\n    i0.ɵɵlistener(\"click.stop\", function TuiInputChipComponent_button_3_Template_button_click_stop_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.delete());\n    })(\"pointerdown.prevent.stop.zoneless\", function TuiInputChipComponent_button_3_Template_button_pointerdown_prevent_stop_zoneless_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      return i0.ɵɵresetView(0);\n    });\n    i0.ɵɵtext(1, \" Remove\\n\");\n    i0.ɵɵelementEnd();\n  }\n}\nclass TuiInputChipComponent {\n  constructor() {\n    this.options = inject(TUI_TEXTFIELD_OPTIONS);\n    this.context = injectContext();\n    this.value = tuiInjectValue();\n    this.mobile = inject(TUI_IS_MOBILE);\n    this.internal = signal(this.context.$implicit.item);\n    this.editing = signal(false);\n    this.hint = inject(TuiHintDirective, {\n      self: true,\n      optional: true\n    });\n    this.handlers = inject(TUI_ITEMS_HANDLERS);\n    this.textfield = inject(TuiTextfieldComponent);\n    this.disabled = tuiDirectiveBinding(TuiAppearance, 'tuiAppearanceState', computed(() => this.handlers.disabledItemHandler()(this.context.$implicit.item) ? 'disabled' : null));\n    this.size = tuiDirectiveBinding(TuiChip, 'size', computed(() => this.options.size() === 'l' ? 's' : 'xs'));\n    this.editable = true;\n  }\n  get index() {\n    return this.context.$implicit.index;\n  }\n  delete() {\n    this.textfield.cva?.onChange(this.value().filter((_, i) => i !== this.index));\n    if (!this.mobile) {\n      this.textfield.input?.nativeElement.focus({\n        preventScroll: true\n      });\n    }\n  }\n  save() {\n    if (!this.internal()) {\n      this.delete();\n    } else if (this.handlers.disabledItemHandler()(this.internal())) {\n      return;\n    }\n    const value = this.value().map((item, index) => index === this.index ? this.internal() : item);\n    this.textfield.cva?.onChange(value);\n    this.editing.set(false);\n    this.textfield.input?.nativeElement.focus({\n      preventScroll: true\n    });\n  }\n  cancel() {\n    this.editing.set(false);\n    this.internal.set(this.context.$implicit.item);\n  }\n  edit() {\n    if (!this.editable || !this.textfield.cva?.interactive() || !tuiIsString(this.internal())) {\n      return;\n    }\n    this.editing.set(true);\n    setTimeout(() => this.input?.nativeElement.focus());\n  }\n  static {\n    this.ɵfac = function TuiInputChipComponent_Factory(t) {\n      return new (t || TuiInputChipComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiInputChipComponent,\n      selectors: [[\"tui-input-chip\"]],\n      viewQuery: function TuiInputChipComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TuiChip, 5, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n        }\n      },\n      hostAttrs: [\"tuiChip\", \"\", 1, \"tui-interactive\"],\n      hostVars: 3,\n      hostBindings: function TuiInputChipComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TuiInputChipComponent_click_HostBindingHandler($event) {\n            return ctx.editing() && $event.stopPropagation();\n          })(\"keydown.backspace.prevent\", function TuiInputChipComponent_keydown_backspace_prevent_HostBindingHandler() {\n            return ctx.delete();\n          })(\"keydown.enter.prevent\", function TuiInputChipComponent_keydown_enter_prevent_HostBindingHandler() {\n            return ctx.edit();\n          })(\"dblclick\", function TuiInputChipComponent_dblclick_HostBindingHandler() {\n            return ctx.edit();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"tabIndex\", ctx.disabled() ? null : -1);\n          i0.ɵɵclassProp(\"_edit\", ctx.editing());\n        }\n      },\n      inputs: {\n        editable: \"editable\"\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([i1.TuiChip]), i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 5,\n      consts: [[\"appearance\", \"\", \"enterkeyhint\", \"enter\", \"tuiChip\", \"\", 1, \"t-input\", 3, \"blur\", \"keydown.enter\", \"keydown.esc\", \"keydown.stop\", \"ngModelChange\", \"disabled\", \"ngModel\"], [\"tuiFade\", \"\", \"tuiFadeOffset\", \"0.5rem\", 1, \"t-text\", 3, \"mousedown.prevent.zoneless\", \"tuiHintOverflow\"], [\"iconStart\", \"@tui.x\", \"tabIndex\", \"-1\", \"tuiIconButton\", \"\", \"type\", \"button\", 3, \"click.stop\", \"pointerdown.prevent.stop.zoneless\", 4, \"ngIf\"], [\"iconStart\", \"@tui.x\", \"tabIndex\", \"-1\", \"tuiIconButton\", \"\", \"type\", \"button\", 3, \"click.stop\", \"pointerdown.prevent.stop.zoneless\"]],\n      template: function TuiInputChipComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"input\", 0);\n          i0.ɵɵlistener(\"blur\", function TuiInputChipComponent_Template_input_blur_0_listener() {\n            return ctx.cancel();\n          })(\"keydown.enter\", function TuiInputChipComponent_Template_input_keydown_enter_0_listener() {\n            return ctx.save();\n          })(\"keydown.esc\", function TuiInputChipComponent_Template_input_keydown_esc_0_listener() {\n            return ctx.cancel();\n          })(\"keydown.stop\", function TuiInputChipComponent_Template_input_keydown_stop_0_listener() {\n            return 0;\n          })(\"ngModelChange\", function TuiInputChipComponent_Template_input_ngModelChange_0_listener($event) {\n            return ctx.internal.set($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵlistener(\"mousedown.prevent.zoneless\", function TuiInputChipComponent_Template_div_mousedown_prevent_zoneless_1_listener() {\n            return 0;\n          });\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, TuiInputChipComponent_button_3_Template, 2, 0, \"button\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"disabled\", !ctx.editing())(\"ngModel\", ctx.internal());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"tuiHintOverflow\", (ctx.hint == null ? null : ctx.hint.content()) ? null : ctx.handlers.stringify()(ctx.internal()));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.handlers.stringify()(ctx.internal()), \"\\n\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.textfield.cva == null ? null : ctx.textfield.cva.interactive()) && !ctx.editing() && !ctx.disabled());\n        }\n      },\n      dependencies: [FormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, NgIf, ReactiveFormsModule, TuiButton, TuiChip, TuiFade, TuiHintOverflow],\n      styles: [\"[_nghost-%COMP%]{cursor:pointer;margin:.125rem 0;margin-inline-end:.25rem}[data-state=disabled][_nghost-%COMP%]{pointer-events:none}[_nghost-%COMP%]   .t-input[_ngcontent-%COMP%]{padding:0;text-indent:.375rem;transition:none;color:var(--tui-text-primary);cursor:text;outline:none}[_nghost-%COMP%]   .t-input[_ngcontent-%COMP%]:disabled{visibility:hidden}._edit[_nghost-%COMP%]{background:transparent}._edit[_nghost-%COMP%]   .t-text[_ngcontent-%COMP%]{pointer-events:none;visibility:hidden}._edit[_nghost-%COMP%]:before{color:transparent;transition:none}tui-textfield[data-size=\\\"s\\\"][_nghost-%COMP%], tui-textfield[data-size=\\\"s\\\"]   [_nghost-%COMP%]{left:-.375rem;margin:.0625rem 0;margin-inline-end:.125rem}@supports (inset-inline-start: 0){tui-textfield[data-size=\\\"s\\\"][_nghost-%COMP%], tui-textfield[data-size=\\\"s\\\"]   [_nghost-%COMP%]{left:unset;inset-inline-start:-.375rem}}tui-textfield[data-size=\\\"m\\\"][_nghost-%COMP%], tui-textfield[data-size=\\\"m\\\"]   [_nghost-%COMP%]{left:-.125rem}@supports (inset-inline-start: 0){tui-textfield[data-size=\\\"m\\\"][_nghost-%COMP%], tui-textfield[data-size=\\\"m\\\"]   [_nghost-%COMP%]{left:unset;inset-inline-start:-.125rem}}tui-textfield[data-size=\\\"l\\\"][_nghost-%COMP%], tui-textfield[data-size=\\\"l\\\"]   [_nghost-%COMP%]{left:-.25rem}@supports (inset-inline-start: 0){tui-textfield[data-size=\\\"l\\\"][_nghost-%COMP%], tui-textfield[data-size=\\\"l\\\"]   [_nghost-%COMP%]{left:unset;inset-inline-start:-.25rem}}tui-textfield[data-size=\\\"l\\\"][_nghost-%COMP%]   .t-input[_ngcontent-%COMP%], tui-textfield[data-size=\\\"l\\\"]   [_nghost-%COMP%]   .t-input[_ngcontent-%COMP%]{text-indent:.625rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputChipComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-input-chip',\n      imports: [FormsModule, NgIf, ReactiveFormsModule, TuiButton, TuiChip, TuiFade, TuiHintOverflow],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      hostDirectives: [TuiChip],\n      host: {\n        tuiChip: '',\n        class: 'tui-interactive',\n        '[class._edit]': 'editing()',\n        '[attr.tabIndex]': 'disabled() ? null : -1',\n        '(click)': 'editing() && $event.stopPropagation()',\n        '(keydown.backspace.prevent)': 'delete()',\n        '(keydown.enter.prevent)': 'edit()',\n        '(dblclick)': 'edit()'\n      },\n      template: \"<input\\n    appearance=\\\"\\\"\\n    enterkeyhint=\\\"enter\\\"\\n    tuiChip\\n    class=\\\"t-input\\\"\\n    [disabled]=\\\"!editing()\\\"\\n    [ngModel]=\\\"internal()\\\"\\n    (blur)=\\\"cancel()\\\"\\n    (keydown.enter)=\\\"save()\\\"\\n    (keydown.esc)=\\\"cancel()\\\"\\n    (keydown.stop)=\\\"(0)\\\"\\n    (ngModelChange)=\\\"internal.set($event)\\\"\\n/>\\n<div\\n    tuiFade\\n    tuiFadeOffset=\\\"0.5rem\\\"\\n    class=\\\"t-text\\\"\\n    [tuiHintOverflow]=\\\"hint?.content() ? null : handlers.stringify()(internal())\\\"\\n    (mousedown.prevent.zoneless)=\\\"(0)\\\"\\n>\\n    {{ handlers.stringify()(internal()) }}\\n</div>\\n<button\\n    *ngIf=\\\"textfield.cva?.interactive() && !editing() && !disabled()\\\"\\n    iconStart=\\\"@tui.x\\\"\\n    tabIndex=\\\"-1\\\"\\n    tuiIconButton\\n    type=\\\"button\\\"\\n    (click.stop)=\\\"delete()\\\"\\n    (pointerdown.prevent.stop.zoneless)=\\\"(0)\\\"\\n>\\n    Remove\\n</button>\\n\",\n      styles: [\":host{cursor:pointer;margin:.125rem 0;margin-inline-end:.25rem}:host[data-state=disabled]{pointer-events:none}:host .t-input{padding:0;text-indent:.375rem;transition:none;color:var(--tui-text-primary);cursor:text;outline:none}:host .t-input:disabled{visibility:hidden}:host._edit{background:transparent}:host._edit .t-text{pointer-events:none;visibility:hidden}:host._edit:before{color:transparent;transition:none}:host-context(tui-textfield[data-size=\\\"s\\\"]){left:-.375rem;margin:.0625rem 0;margin-inline-end:.125rem}@supports (inset-inline-start: 0){:host-context(tui-textfield[data-size=\\\"s\\\"]){left:unset;inset-inline-start:-.375rem}}:host-context(tui-textfield[data-size=\\\"m\\\"]){left:-.125rem}@supports (inset-inline-start: 0){:host-context(tui-textfield[data-size=\\\"m\\\"]){left:unset;inset-inline-start:-.125rem}}:host-context(tui-textfield[data-size=\\\"l\\\"]){left:-.25rem}@supports (inset-inline-start: 0){:host-context(tui-textfield[data-size=\\\"l\\\"]){left:unset;inset-inline-start:-.25rem}}:host-context(tui-textfield[data-size=\\\"l\\\"]) .t-input{text-indent:.625rem}\\n\"]\n    }]\n  }], null, {\n    input: [{\n      type: ViewChild,\n      args: [TuiChip, {\n        read: ElementRef\n      }]\n    }],\n    editable: [{\n      type: Input\n    }]\n  });\n})();\nconst TUI_INPUT_CHIP_DEFAULT_OPTIONS = {\n  separator: ',',\n  unique: true\n};\nconst [TUI_INPUT_CHIP_OPTIONS, tuiInputChipOptionsProvider] = tuiCreateOptions(TUI_INPUT_CHIP_DEFAULT_OPTIONS);\nclass TuiInputChipDirective extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.handlers = inject(TUI_ITEMS_HANDLERS);\n    this.options = inject(TUI_INPUT_CHIP_OPTIONS);\n    this.mobile = inject(TUI_IS_MOBILE);\n    this.textfield = inject(TuiTextfieldMultiComponent);\n    this.open = tuiDropdownOpen();\n    this.dropdown = inject(TuiDropdownDirective);\n    this.sub = inject(TuiActiveZone).tuiActiveZoneChange.pipe(filter(active => !active), takeUntilDestroyed()).subscribe(() => {\n      this.onEnter();\n      this.textfield.value.set('');\n    });\n    this.separator = this.options.separator;\n    this.unique = this.options.unique;\n    this.el = tuiInjectElement();\n  }\n  setValue(value) {\n    this.textfield.value.set('');\n    this.onChange(this.unique ? Array.from(new Set(value.reverse())).reverse() : value);\n  }\n  onEnter() {\n    const value = this.textfield.value().trim();\n    const items = this.separator ? value.split(this.separator) : [value];\n    const valid = items.filter(item => item && !this.handlers.disabledItemHandler()(item));\n    if (!value || !valid.length) {\n      return;\n    }\n    this.setValue([...this.value(), ...valid]);\n    this.scrollTo();\n  }\n  onInput() {\n    this.open.set(!!this.dropdown.content);\n    if (this.separator && this.textfield.value().match(this.separator)) {\n      this.onEnter();\n    } else {\n      this.scrollTo();\n    }\n  }\n  onPaste(event) {\n    const value = 'dataTransfer' in event ? event.dataTransfer?.getData('text/plain') || '' : tuiGetClipboardDataText(event);\n    this.textfield.value.set(value);\n    this.onEnter();\n  }\n  onBackspace(key) {\n    // (keydown.backspace) doesn't emit event on empty input in ios safari\n    if (key === 'Backspace' && !this.textfield.value() && this.interactive() && (this.mobile || !this.textfield.item)) {\n      this.onChange(this.value().slice(0, -1));\n    }\n  }\n  scrollTo() {\n    let sign = 1;\n    try {\n      sign = this.textfield.el.matches(':dir(rtl)') ? -1 : 1;\n    } catch {}\n    // Allow change detection to run and add new tag to DOM\n    setTimeout(() => {\n      this.textfield.el.scrollTo({\n        left: sign * Number.MAX_SAFE_INTEGER,\n        top: Number.MAX_SAFE_INTEGER\n      });\n    });\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiInputChipDirective_BaseFactory;\n      return function TuiInputChipDirective_Factory(t) {\n        return (ɵTuiInputChipDirective_BaseFactory || (ɵTuiInputChipDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiInputChipDirective)))(t || TuiInputChipDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiInputChipDirective,\n      selectors: [[\"input\", \"tuiInputChip\", \"\"]],\n      hostAttrs: [\"enterkeyhint\", \"enter\"],\n      hostVars: 1,\n      hostBindings: function TuiInputChipDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.enter.prevent\", function TuiInputChipDirective_keydown_enter_prevent_HostBindingHandler() {\n            return ctx.onEnter();\n          })(\"keydown.zoneless\", function TuiInputChipDirective_keydown_zoneless_HostBindingHandler($event) {\n            return ctx.onBackspace($event.key);\n          })(\"input\", function TuiInputChipDirective_input_HostBindingHandler() {\n            return ctx.onInput();\n          })(\"paste.prevent\", function TuiInputChipDirective_paste_prevent_HostBindingHandler($event) {\n            return ctx.onPaste($event);\n          })(\"drop.prevent\", function TuiInputChipDirective_drop_prevent_HostBindingHandler($event) {\n            return ctx.onPaste($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"disabled\", ctx.disabled());\n        }\n      },\n      inputs: {\n        separator: \"separator\",\n        unique: \"unique\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsControl(TuiInputChipDirective), tuiFallbackValueProvider([]), tuiAsTextfieldAccessor(TuiInputChipDirective)]), i0.ɵɵHostDirectivesFeature([i1$1.TuiNativeValidator, {\n        directive: i2$1.TuiTextfieldBase,\n        inputs: [\"invalid\", \"invalid\", \"focused\", \"focused\", \"readOnly\", \"readOnly\", \"state\", \"state\"]\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputChipDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiInputChip]',\n      providers: [tuiAsControl(TuiInputChipDirective), tuiFallbackValueProvider([]), tuiAsTextfieldAccessor(TuiInputChipDirective)],\n      hostDirectives: [TuiNativeValidator, {\n        directive: TuiTextfieldBase,\n        inputs: ['invalid', 'focused', 'readOnly', 'state']\n      }],\n      host: {\n        enterkeyhint: 'enter',\n        '[disabled]': 'disabled()',\n        '(keydown.enter.prevent)': 'onEnter()',\n        '(keydown.zoneless)': 'onBackspace($event.key)',\n        '(input)': 'onInput()',\n        '(paste.prevent)': 'onPaste($event)',\n        '(drop.prevent)': 'onPaste($event)'\n      }\n    }]\n  }], null, {\n    separator: [{\n      type: Input\n    }],\n    unique: [{\n      type: Input\n    }]\n  });\n})();\nconst TuiInputChip = [TuiInputChipDirective, TuiInputChipComponent];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_CHIP_DEFAULT_OPTIONS, TUI_INPUT_CHIP_OPTIONS, TuiInputChip, TuiInputChipComponent, TuiInputChipDirective, tuiInputChipOptionsProvider };", "map": {"version": 3, "names": ["NgIf", "i0", "inject", "signal", "computed", "ElementRef", "Component", "ChangeDetectionStrategy", "ViewChild", "Input", "Directive", "i2", "FormsModule", "ReactiveFormsModule", "TUI_IS_MOBILE", "tuiFallback<PERSON><PERSON>ue<PERSON>", "tuiDirectiveBinding", "tuiIsString", "TuiButton", "i2$1", "TUI_TEXTFIELD_OPTIONS", "TuiTextfieldComponent", "TuiTextfieldMultiComponent", "tuiAsTextfieldAccessor", "TuiTextfieldBase", "TuiAppearan<PERSON>", "TuiHintDirective", "TuiHintOverflow", "TUI_ITEMS_HANDLERS", "i1", "TuiChip", "TuiFade", "tuiInjectValue", "injectContext", "takeUntilDestroyed", "TuiControl", "tuiAsControl", "TuiActiveZone", "i1$1", "TuiNativeValidator", "tuiInjectElement", "tuiGetClipboardDataText", "tuiDropdownOpen", "TuiDropdownDirective", "filter", "tuiCreateOptions", "TuiInputChipComponent_button_3_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiInputChipComponent_button_3_Template_button_click_stop_0_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "delete", "TuiInputChipComponent_button_3_Template_button_pointerdown_prevent_stop_zoneless_0_listener", "ɵɵtext", "ɵɵelementEnd", "TuiInputChipComponent", "constructor", "options", "context", "value", "mobile", "internal", "$implicit", "item", "editing", "hint", "self", "optional", "handlers", "textfield", "disabled", "disabledItemHandler", "size", "editable", "index", "cva", "onChange", "_", "i", "input", "nativeElement", "focus", "preventScroll", "save", "map", "set", "cancel", "edit", "interactive", "setTimeout", "ɵfac", "TuiInputChipComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "TuiInputChipComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "TuiInputChipComponent_HostBindings", "TuiInputChipComponent_click_HostBindingHandler", "$event", "stopPropagation", "TuiInputChipComponent_keydown_backspace_prevent_HostBindingHandler", "TuiInputChipComponent_keydown_enter_prevent_HostBindingHandler", "TuiInputChipComponent_dblclick_HostBindingHandler", "ɵɵattribute", "ɵɵclassProp", "inputs", "standalone", "features", "ɵɵHostDirectivesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiInputChipComponent_Template", "TuiInputChipComponent_Template_input_blur_0_listener", "TuiInputChipComponent_Template_input_keydown_enter_0_listener", "TuiInputChipComponent_Template_input_keydown_esc_0_listener", "TuiInputChipComponent_Template_input_keydown_stop_0_listener", "TuiInputChipComponent_Template_input_ngModelChange_0_listener", "TuiInputChipComponent_Template_div_mousedown_prevent_zoneless_1_listener", "ɵɵtemplate", "ɵɵproperty", "ɵɵadvance", "content", "stringify", "ɵɵtextInterpolate1", "dependencies", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "hostDirectives", "host", "tuiChip", "class", "read", "TUI_INPUT_CHIP_DEFAULT_OPTIONS", "separator", "unique", "TUI_INPUT_CHIP_OPTIONS", "tuiInputChipOptionsProvider", "TuiInputChipDirective", "arguments", "open", "dropdown", "sub", "tuiActiveZoneChange", "pipe", "active", "subscribe", "onEnter", "el", "setValue", "Array", "from", "Set", "reverse", "trim", "items", "split", "valid", "length", "scrollTo", "onInput", "match", "onPaste", "event", "dataTransfer", "getData", "onBackspace", "key", "slice", "sign", "matches", "left", "Number", "MAX_SAFE_INTEGER", "top", "ɵTuiInputChipDirective_BaseFactory", "TuiInputChipDirective_Factory", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "TuiInputChipDirective_HostBindings", "TuiInputChipDirective_keydown_enter_prevent_HostBindingHandler", "TuiInputChipDirective_keydown_zoneless_HostBindingHandler", "TuiInputChipDirective_input_HostBindingHandler", "TuiInputChipDirective_paste_prevent_HostBindingHandler", "TuiInputChipDirective_drop_prevent_HostBindingHandler", "ɵɵhostProperty", "ɵɵProvidersFeature", "directive", "ɵɵInheritDefinitionFeature", "providers", "enterkeyhint", "TuiInputChip"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-input-chip.mjs"], "sourcesContent": ["import { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, signal, computed, ElementRef, Component, ChangeDetectionStrategy, ViewChild, Input, Directive } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TUI_IS_MOBILE, tuiFallbackValueProvider } from '@taiga-ui/cdk/tokens';\nimport { tuiDirectiveBinding, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport * as i2$1 from '@taiga-ui/core/components/textfield';\nimport { TUI_TEXTFIELD_OPTIONS, TuiTextfieldComponent, TuiTextfieldMultiComponent, tuiAsTextfieldAccessor, TuiTextfieldBase } from '@taiga-ui/core/components/textfield';\nimport { TuiAppearance } from '@taiga-ui/core/directives/appearance';\nimport { TuiHintDirective, TuiHintOverflow } from '@taiga-ui/core/directives/hint';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport * as i1 from '@taiga-ui/kit/components/chip';\nimport { TuiChip } from '@taiga-ui/kit/components/chip';\nimport { TuiFade } from '@taiga-ui/kit/directives/fade';\nimport { tuiInjectValue } from '@taiga-ui/kit/utils';\nimport { injectContext } from '@taiga-ui/polymorpheus';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { TuiControl, tuiAsControl } from '@taiga-ui/cdk/classes';\nimport { TuiActiveZone } from '@taiga-ui/cdk/directives/active-zone';\nimport * as i1$1 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport { tuiInjectElement, tuiGetClipboardDataText } from '@taiga-ui/cdk/utils/dom';\nimport { tuiDropdownOpen, TuiDropdownDirective } from '@taiga-ui/core/directives/dropdown';\nimport { filter } from 'rxjs';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\n\nclass TuiInputChipComponent {\n    constructor() {\n        this.options = inject(TUI_TEXTFIELD_OPTIONS);\n        this.context = injectContext();\n        this.value = tuiInjectValue();\n        this.mobile = inject(TUI_IS_MOBILE);\n        this.internal = signal(this.context.$implicit.item);\n        this.editing = signal(false);\n        this.hint = inject(TuiHintDirective, { self: true, optional: true });\n        this.handlers = inject(TUI_ITEMS_HANDLERS);\n        this.textfield = inject(TuiTextfieldComponent);\n        this.disabled = tuiDirectiveBinding(TuiAppearance, 'tuiAppearanceState', computed(() => this.handlers.disabledItemHandler()(this.context.$implicit.item)\n            ? 'disabled'\n            : null));\n        this.size = tuiDirectiveBinding(TuiChip, 'size', computed(() => (this.options.size() === 'l' ? 's' : 'xs')));\n        this.editable = true;\n    }\n    get index() {\n        return this.context.$implicit.index;\n    }\n    delete() {\n        this.textfield.cva?.onChange(this.value().filter((_, i) => i !== this.index));\n        if (!this.mobile) {\n            this.textfield.input?.nativeElement.focus({ preventScroll: true });\n        }\n    }\n    save() {\n        if (!this.internal()) {\n            this.delete();\n        }\n        else if (this.handlers.disabledItemHandler()(this.internal())) {\n            return;\n        }\n        const value = this.value().map((item, index) => index === this.index ? this.internal() : item);\n        this.textfield.cva?.onChange(value);\n        this.editing.set(false);\n        this.textfield.input?.nativeElement.focus({ preventScroll: true });\n    }\n    cancel() {\n        this.editing.set(false);\n        this.internal.set(this.context.$implicit.item);\n    }\n    edit() {\n        if (!this.editable ||\n            !this.textfield.cva?.interactive() ||\n            !tuiIsString(this.internal())) {\n            return;\n        }\n        this.editing.set(true);\n        setTimeout(() => this.input?.nativeElement.focus());\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputChipComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputChipComponent, isStandalone: true, selector: \"tui-input-chip\", inputs: { editable: \"editable\" }, host: { attributes: { \"tuiChip\": \"\" }, listeners: { \"click\": \"editing() && $event.stopPropagation()\", \"keydown.backspace.prevent\": \"delete()\", \"keydown.enter.prevent\": \"edit()\", \"dblclick\": \"edit()\" }, properties: { \"class._edit\": \"editing()\", \"attr.tabIndex\": \"disabled() ? null : -1\" }, classAttribute: \"tui-interactive\" }, viewQueries: [{ propertyName: \"input\", first: true, predicate: TuiChip, descendants: true, read: ElementRef }], hostDirectives: [{ directive: i1.TuiChip }], ngImport: i0, template: \"<input\\n    appearance=\\\"\\\"\\n    enterkeyhint=\\\"enter\\\"\\n    tuiChip\\n    class=\\\"t-input\\\"\\n    [disabled]=\\\"!editing()\\\"\\n    [ngModel]=\\\"internal()\\\"\\n    (blur)=\\\"cancel()\\\"\\n    (keydown.enter)=\\\"save()\\\"\\n    (keydown.esc)=\\\"cancel()\\\"\\n    (keydown.stop)=\\\"(0)\\\"\\n    (ngModelChange)=\\\"internal.set($event)\\\"\\n/>\\n<div\\n    tuiFade\\n    tuiFadeOffset=\\\"0.5rem\\\"\\n    class=\\\"t-text\\\"\\n    [tuiHintOverflow]=\\\"hint?.content() ? null : handlers.stringify()(internal())\\\"\\n    (mousedown.prevent.zoneless)=\\\"(0)\\\"\\n>\\n    {{ handlers.stringify()(internal()) }}\\n</div>\\n<button\\n    *ngIf=\\\"textfield.cva?.interactive() && !editing() && !disabled()\\\"\\n    iconStart=\\\"@tui.x\\\"\\n    tabIndex=\\\"-1\\\"\\n    tuiIconButton\\n    type=\\\"button\\\"\\n    (click.stop)=\\\"delete()\\\"\\n    (pointerdown.prevent.stop.zoneless)=\\\"(0)\\\"\\n>\\n    Remove\\n</button>\\n\", styles: [\":host{cursor:pointer;margin:.125rem 0;margin-inline-end:.25rem}:host[data-state=disabled]{pointer-events:none}:host .t-input{padding:0;text-indent:.375rem;transition:none;color:var(--tui-text-primary);cursor:text;outline:none}:host .t-input:disabled{visibility:hidden}:host._edit{background:transparent}:host._edit .t-text{pointer-events:none;visibility:hidden}:host._edit:before{color:transparent;transition:none}:host-context(tui-textfield[data-size=\\\"s\\\"]){left:-.375rem;margin:.0625rem 0;margin-inline-end:.125rem}@supports (inset-inline-start: 0){:host-context(tui-textfield[data-size=\\\"s\\\"]){left:unset;inset-inline-start:-.375rem}}:host-context(tui-textfield[data-size=\\\"m\\\"]){left:-.125rem}@supports (inset-inline-start: 0){:host-context(tui-textfield[data-size=\\\"m\\\"]){left:unset;inset-inline-start:-.125rem}}:host-context(tui-textfield[data-size=\\\"l\\\"]){left:-.25rem}@supports (inset-inline-start: 0){:host-context(tui-textfield[data-size=\\\"l\\\"]){left:unset;inset-inline-start:-.25rem}}:host-context(tui-textfield[data-size=\\\"l\\\"]) .t-input{text-indent:.625rem}\\n\"], dependencies: [{ kind: \"ngmodule\", type: FormsModule }, { kind: \"directive\", type: i2.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { kind: \"directive\", type: i2.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i2.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"ngmodule\", type: ReactiveFormsModule }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"directive\", type: TuiChip, selector: \"tui-chip,[tuiChip]\", inputs: [\"size\"] }, { kind: \"directive\", type: TuiFade, selector: \"[tuiFade]\", inputs: [\"tuiFadeHeight\", \"tuiFadeSize\", \"tuiFadeOffset\", \"tuiFade\"] }, { kind: \"directive\", type: TuiHintOverflow, selector: \"[tuiHintOverflow]\", inputs: [\"tuiHintOverflow\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputChipComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-input-chip', imports: [\n                        FormsModule,\n                        NgIf,\n                        ReactiveFormsModule,\n                        TuiButton,\n                        TuiChip,\n                        TuiFade,\n                        TuiHintOverflow,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, hostDirectives: [TuiChip], host: {\n                        tuiChip: '',\n                        class: 'tui-interactive',\n                        '[class._edit]': 'editing()',\n                        '[attr.tabIndex]': 'disabled() ? null : -1',\n                        '(click)': 'editing() && $event.stopPropagation()',\n                        '(keydown.backspace.prevent)': 'delete()',\n                        '(keydown.enter.prevent)': 'edit()',\n                        '(dblclick)': 'edit()',\n                    }, template: \"<input\\n    appearance=\\\"\\\"\\n    enterkeyhint=\\\"enter\\\"\\n    tuiChip\\n    class=\\\"t-input\\\"\\n    [disabled]=\\\"!editing()\\\"\\n    [ngModel]=\\\"internal()\\\"\\n    (blur)=\\\"cancel()\\\"\\n    (keydown.enter)=\\\"save()\\\"\\n    (keydown.esc)=\\\"cancel()\\\"\\n    (keydown.stop)=\\\"(0)\\\"\\n    (ngModelChange)=\\\"internal.set($event)\\\"\\n/>\\n<div\\n    tuiFade\\n    tuiFadeOffset=\\\"0.5rem\\\"\\n    class=\\\"t-text\\\"\\n    [tuiHintOverflow]=\\\"hint?.content() ? null : handlers.stringify()(internal())\\\"\\n    (mousedown.prevent.zoneless)=\\\"(0)\\\"\\n>\\n    {{ handlers.stringify()(internal()) }}\\n</div>\\n<button\\n    *ngIf=\\\"textfield.cva?.interactive() && !editing() && !disabled()\\\"\\n    iconStart=\\\"@tui.x\\\"\\n    tabIndex=\\\"-1\\\"\\n    tuiIconButton\\n    type=\\\"button\\\"\\n    (click.stop)=\\\"delete()\\\"\\n    (pointerdown.prevent.stop.zoneless)=\\\"(0)\\\"\\n>\\n    Remove\\n</button>\\n\", styles: [\":host{cursor:pointer;margin:.125rem 0;margin-inline-end:.25rem}:host[data-state=disabled]{pointer-events:none}:host .t-input{padding:0;text-indent:.375rem;transition:none;color:var(--tui-text-primary);cursor:text;outline:none}:host .t-input:disabled{visibility:hidden}:host._edit{background:transparent}:host._edit .t-text{pointer-events:none;visibility:hidden}:host._edit:before{color:transparent;transition:none}:host-context(tui-textfield[data-size=\\\"s\\\"]){left:-.375rem;margin:.0625rem 0;margin-inline-end:.125rem}@supports (inset-inline-start: 0){:host-context(tui-textfield[data-size=\\\"s\\\"]){left:unset;inset-inline-start:-.375rem}}:host-context(tui-textfield[data-size=\\\"m\\\"]){left:-.125rem}@supports (inset-inline-start: 0){:host-context(tui-textfield[data-size=\\\"m\\\"]){left:unset;inset-inline-start:-.125rem}}:host-context(tui-textfield[data-size=\\\"l\\\"]){left:-.25rem}@supports (inset-inline-start: 0){:host-context(tui-textfield[data-size=\\\"l\\\"]){left:unset;inset-inline-start:-.25rem}}:host-context(tui-textfield[data-size=\\\"l\\\"]) .t-input{text-indent:.625rem}\\n\"] }]\n        }], propDecorators: { input: [{\n                type: ViewChild,\n                args: [TuiChip, { read: ElementRef }]\n            }], editable: [{\n                type: Input\n            }] } });\n\nconst TUI_INPUT_CHIP_DEFAULT_OPTIONS = {\n    separator: ',',\n    unique: true,\n};\nconst [TUI_INPUT_CHIP_OPTIONS, tuiInputChipOptionsProvider] = tuiCreateOptions(TUI_INPUT_CHIP_DEFAULT_OPTIONS);\n\nclass TuiInputChipDirective extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.handlers = inject(TUI_ITEMS_HANDLERS);\n        this.options = inject(TUI_INPUT_CHIP_OPTIONS);\n        this.mobile = inject(TUI_IS_MOBILE);\n        this.textfield = inject(TuiTextfieldMultiComponent);\n        this.open = tuiDropdownOpen();\n        this.dropdown = inject(TuiDropdownDirective);\n        this.sub = inject(TuiActiveZone)\n            .tuiActiveZoneChange.pipe(filter((active) => !active), takeUntilDestroyed())\n            .subscribe(() => {\n            this.onEnter();\n            this.textfield.value.set('');\n        });\n        this.separator = this.options.separator;\n        this.unique = this.options.unique;\n        this.el = tuiInjectElement();\n    }\n    setValue(value) {\n        this.textfield.value.set('');\n        this.onChange(this.unique ? Array.from(new Set(value.reverse())).reverse() : value);\n    }\n    onEnter() {\n        const value = this.textfield.value().trim();\n        const items = this.separator ? value.split(this.separator) : [value];\n        const valid = items.filter((item) => item && !this.handlers.disabledItemHandler()(item));\n        if (!value || !valid.length) {\n            return;\n        }\n        this.setValue([...this.value(), ...valid]);\n        this.scrollTo();\n    }\n    onInput() {\n        this.open.set(!!this.dropdown.content);\n        if (this.separator && this.textfield.value().match(this.separator)) {\n            this.onEnter();\n        }\n        else {\n            this.scrollTo();\n        }\n    }\n    onPaste(event) {\n        const value = 'dataTransfer' in event\n            ? event.dataTransfer?.getData('text/plain') || ''\n            : tuiGetClipboardDataText(event);\n        this.textfield.value.set(value);\n        this.onEnter();\n    }\n    onBackspace(key) {\n        // (keydown.backspace) doesn't emit event on empty input in ios safari\n        if (key === 'Backspace' &&\n            !this.textfield.value() &&\n            this.interactive() &&\n            (this.mobile || !this.textfield.item)) {\n            this.onChange(this.value().slice(0, -1));\n        }\n    }\n    scrollTo() {\n        let sign = 1;\n        try {\n            sign = this.textfield.el.matches(':dir(rtl)') ? -1 : 1;\n        }\n        catch { }\n        // Allow change detection to run and add new tag to DOM\n        setTimeout(() => {\n            this.textfield.el.scrollTo({\n                left: sign * Number.MAX_SAFE_INTEGER,\n                top: Number.MAX_SAFE_INTEGER,\n            });\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputChipDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputChipDirective, isStandalone: true, selector: \"input[tuiInputChip]\", inputs: { separator: \"separator\", unique: \"unique\" }, host: { attributes: { \"enterkeyhint\": \"enter\" }, listeners: { \"keydown.enter.prevent\": \"onEnter()\", \"keydown.zoneless\": \"onBackspace($event.key)\", \"input\": \"onInput()\", \"paste.prevent\": \"onPaste($event)\", \"drop.prevent\": \"onPaste($event)\" }, properties: { \"disabled\": \"disabled()\" } }, providers: [\n            tuiAsControl(TuiInputChipDirective),\n            tuiFallbackValueProvider([]),\n            tuiAsTextfieldAccessor(TuiInputChipDirective),\n        ], usesInheritance: true, hostDirectives: [{ directive: i1$1.TuiNativeValidator }, { directive: i2$1.TuiTextfieldBase, inputs: [\"invalid\", \"invalid\", \"focused\", \"focused\", \"readOnly\", \"readOnly\", \"state\", \"state\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputChipDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiInputChip]',\n                    providers: [\n                        tuiAsControl(TuiInputChipDirective),\n                        tuiFallbackValueProvider([]),\n                        tuiAsTextfieldAccessor(TuiInputChipDirective),\n                    ],\n                    hostDirectives: [\n                        TuiNativeValidator,\n                        {\n                            directive: TuiTextfieldBase,\n                            inputs: ['invalid', 'focused', 'readOnly', 'state'],\n                        },\n                    ],\n                    host: {\n                        enterkeyhint: 'enter',\n                        '[disabled]': 'disabled()',\n                        '(keydown.enter.prevent)': 'onEnter()',\n                        '(keydown.zoneless)': 'onBackspace($event.key)',\n                        '(input)': 'onInput()',\n                        '(paste.prevent)': 'onPaste($event)',\n                        '(drop.prevent)': 'onPaste($event)',\n                    },\n                }]\n        }], propDecorators: { separator: [{\n                type: Input\n            }], unique: [{\n                type: Input\n            }] } });\n\nconst TuiInputChip = [TuiInputChipDirective, TuiInputChipComponent];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_CHIP_DEFAULT_OPTIONS, TUI_INPUT_CHIP_OPTIONS, TuiInputChip, TuiInputChipComponent, TuiInputChipDirective, tuiInputChipOptionsProvider };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,iBAAiB;AACtC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,QAAQ,eAAe;AACrI,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,aAAa,EAAEC,wBAAwB,QAAQ,sBAAsB;AAC9E,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,mCAAmC;AACpF,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,OAAO,KAAKC,IAAI,MAAM,qCAAqC;AAC3D,SAASC,qBAAqB,EAAEC,qBAAqB,EAAEC,0BAA0B,EAAEC,sBAAsB,EAAEC,gBAAgB,QAAQ,qCAAqC;AACxK,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,gBAAgB,EAAEC,eAAe,QAAQ,gCAAgC;AAClF,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,OAAO,KAAKC,EAAE,MAAM,+BAA+B;AACnD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,UAAU,EAAEC,YAAY,QAAQ,uBAAuB;AAChE,SAASC,aAAa,QAAQ,sCAAsC;AACpE,OAAO,KAAKC,IAAI,MAAM,2CAA2C;AACjE,SAASC,kBAAkB,QAAQ,2CAA2C;AAC9E,SAASC,gBAAgB,EAAEC,uBAAuB,QAAQ,yBAAyB;AACnF,SAASC,eAAe,EAAEC,oBAAoB,QAAQ,oCAAoC;AAC1F,SAASC,MAAM,QAAQ,MAAM;AAC7B,SAASC,gBAAgB,QAAQ,wBAAwB;AAAC,SAAAC,wCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAqD2ChD,EAAE,CAAAiD,gBAAA;IAAFjD,EAAE,CAAAkD,cAAA,eACw5C,CAAC;IAD35ClD,EAAE,CAAAmD,UAAA,wBAAAC,qEAAA;MAAFpD,EAAE,CAAAqD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFtD,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAAwD,WAAA,CAC21CF,MAAA,CAAAG,MAAA,CAAO,CAAC;IAAA,CAAC,CAAC,+CAAAC,4FAAA;MADv2C1D,EAAE,CAAAqD,aAAA,CAAAL,GAAA;MAAA,OAAFhD,EAAE,CAAAwD,WAAA,CACk5C,CAAC;IAAA,CAAE,CAAC;IADx5CxD,EAAE,CAAA2D,MAAA,eACs6C,CAAC;IADz6C3D,EAAE,CAAA4D,YAAA,CAC+6C,CAAC;EAAA;AAAA;AApDvhD,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG9D,MAAM,CAACkB,qBAAqB,CAAC;IAC5C,IAAI,CAAC6C,OAAO,GAAGhC,aAAa,CAAC,CAAC;IAC9B,IAAI,CAACiC,KAAK,GAAGlC,cAAc,CAAC,CAAC;IAC7B,IAAI,CAACmC,MAAM,GAAGjE,MAAM,CAACY,aAAa,CAAC;IACnC,IAAI,CAACsD,QAAQ,GAAGjE,MAAM,CAAC,IAAI,CAAC8D,OAAO,CAACI,SAAS,CAACC,IAAI,CAAC;IACnD,IAAI,CAACC,OAAO,GAAGpE,MAAM,CAAC,KAAK,CAAC;IAC5B,IAAI,CAACqE,IAAI,GAAGtE,MAAM,CAACwB,gBAAgB,EAAE;MAAE+C,IAAI,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;IACpE,IAAI,CAACC,QAAQ,GAAGzE,MAAM,CAAC0B,kBAAkB,CAAC;IAC1C,IAAI,CAACgD,SAAS,GAAG1E,MAAM,CAACmB,qBAAqB,CAAC;IAC9C,IAAI,CAACwD,QAAQ,GAAG7D,mBAAmB,CAACS,aAAa,EAAE,oBAAoB,EAAErB,QAAQ,CAAC,MAAM,IAAI,CAACuE,QAAQ,CAACG,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAACb,OAAO,CAACI,SAAS,CAACC,IAAI,CAAC,GAClJ,UAAU,GACV,IAAI,CAAC,CAAC;IACZ,IAAI,CAACS,IAAI,GAAG/D,mBAAmB,CAACc,OAAO,EAAE,MAAM,EAAE1B,QAAQ,CAAC,MAAO,IAAI,CAAC4D,OAAO,CAACe,IAAI,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,IAAK,CAAC,CAAC;IAC5G,IAAI,CAACC,QAAQ,GAAG,IAAI;EACxB;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAChB,OAAO,CAACI,SAAS,CAACY,KAAK;EACvC;EACAvB,MAAMA,CAAA,EAAG;IACL,IAAI,CAACkB,SAAS,CAACM,GAAG,EAAEC,QAAQ,CAAC,IAAI,CAACjB,KAAK,CAAC,CAAC,CAACtB,MAAM,CAAC,CAACwC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAK,IAAI,CAACJ,KAAK,CAAC,CAAC;IAC7E,IAAI,CAAC,IAAI,CAACd,MAAM,EAAE;MACd,IAAI,CAACS,SAAS,CAACU,KAAK,EAAEC,aAAa,CAACC,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;IACtE;EACJ;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACtB,QAAQ,CAAC,CAAC,EAAE;MAClB,IAAI,CAACV,MAAM,CAAC,CAAC;IACjB,CAAC,MACI,IAAI,IAAI,CAACiB,QAAQ,CAACG,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAACV,QAAQ,CAAC,CAAC,CAAC,EAAE;MAC3D;IACJ;IACA,MAAMF,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,CAACyB,GAAG,CAAC,CAACrB,IAAI,EAAEW,KAAK,KAAKA,KAAK,KAAK,IAAI,CAACA,KAAK,GAAG,IAAI,CAACb,QAAQ,CAAC,CAAC,GAAGE,IAAI,CAAC;IAC9F,IAAI,CAACM,SAAS,CAACM,GAAG,EAAEC,QAAQ,CAACjB,KAAK,CAAC;IACnC,IAAI,CAACK,OAAO,CAACqB,GAAG,CAAC,KAAK,CAAC;IACvB,IAAI,CAAChB,SAAS,CAACU,KAAK,EAAEC,aAAa,CAACC,KAAK,CAAC;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC;EACtE;EACAI,MAAMA,CAAA,EAAG;IACL,IAAI,CAACtB,OAAO,CAACqB,GAAG,CAAC,KAAK,CAAC;IACvB,IAAI,CAACxB,QAAQ,CAACwB,GAAG,CAAC,IAAI,CAAC3B,OAAO,CAACI,SAAS,CAACC,IAAI,CAAC;EAClD;EACAwB,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACd,QAAQ,IACd,CAAC,IAAI,CAACJ,SAAS,CAACM,GAAG,EAAEa,WAAW,CAAC,CAAC,IAClC,CAAC9E,WAAW,CAAC,IAAI,CAACmD,QAAQ,CAAC,CAAC,CAAC,EAAE;MAC/B;IACJ;IACA,IAAI,CAACG,OAAO,CAACqB,GAAG,CAAC,IAAI,CAAC;IACtBI,UAAU,CAAC,MAAM,IAAI,CAACV,KAAK,EAAEC,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC;EACvD;EACA;IAAS,IAAI,CAACS,IAAI,YAAAC,8BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFrC,qBAAqB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAACsC,IAAI,kBAD+EnG,EAAE,CAAAoG,iBAAA;MAAAC,IAAA,EACJxC,qBAAqB;MAAAyC,SAAA;MAAAC,SAAA,WAAAC,4BAAA1D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADnB9C,EAAE,CAAAyG,WAAA,CAC0e5E,OAAO,KAA2BzB,UAAU;QAAA;QAAA,IAAA0C,EAAA;UAAA,IAAA4D,EAAA;UADxhB1G,EAAE,CAAA2G,cAAA,CAAAD,EAAA,GAAF1G,EAAE,CAAA4G,WAAA,QAAA7D,GAAA,CAAAsC,KAAA,GAAAqB,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA,cACsI,EAAE;MAAAC,QAAA;MAAAC,YAAA,WAAAC,mCAAAnE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD1I9C,EAAE,CAAAmD,UAAA,mBAAA+D,+CAAAC,MAAA;YAAA,OACJpE,GAAA,CAAAuB,OAAA,CAAQ,CAAC,IAAI6C,MAAA,CAAAC,eAAA,CAAuB,CAAC;UAAA,CAAjB,CAAC,uCAAAC,mEAAA;YAAA,OAArBtE,GAAA,CAAAU,MAAA,CAAO,CAAC;UAAA,CAAY,CAAC,mCAAA6D,+DAAA;YAAA,OAArBvE,GAAA,CAAA8C,IAAA,CAAK,CAAC;UAAA,CAAc,CAAC,sBAAA0B,kDAAA;YAAA,OAArBxE,GAAA,CAAA8C,IAAA,CAAK,CAAC;UAAA,CAAc,CAAC;QAAA;QAAA,IAAA/C,EAAA;UADnB9C,EAAE,CAAAwH,WAAA,aACJzE,GAAA,CAAA6B,QAAA,CAAS,CAAC,GAAG,IAAI,IAAI,CAAC;UADpB5E,EAAE,CAAAyH,WAAA,UACJ1E,GAAA,CAAAuB,OAAA,CAAQ,CAAY,CAAC;QAAA;MAAA;MAAAoD,MAAA;QAAA3C,QAAA;MAAA;MAAA4C,UAAA;MAAAC,QAAA,GADnB5H,EAAE,CAAA6H,uBAAA,EACyjBjG,EAAE,CAACC,OAAO,IADrkB7B,EAAE,CAAA8H,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAArF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAkD,cAAA,cAC+5B,CAAC;UADl6BlD,EAAE,CAAAmD,UAAA,kBAAAiF,qDAAA;YAAA,OACwwBrF,GAAA,CAAA6C,MAAA,CAAO,CAAC;UAAA,CAAC,CAAC,2BAAAyC,8DAAA;YAAA,OAAwBtF,GAAA,CAAA0C,IAAA,CAAK,CAAC;UAAA,CAAC,CAAC,yBAAA6C,4DAAA;YAAA,OAAsBvF,GAAA,CAAA6C,MAAA,CAAO,CAAC;UAAA,CAAC,CAAC,0BAAA2C,6DAAA;YAAA,OAAwB,CAAC;UAAA,CAAE,CAAC,2BAAAC,8DAAArB,MAAA;YAAA,OAAwBpE,GAAA,CAAAoB,QAAA,CAAAwB,GAAA,CAAAwB,MAAmB,CAAC;UAAA,CAAC,CAAC;UAD95BnH,EAAE,CAAA4D,YAAA,CAC+5B,CAAC;UADl6B5D,EAAE,CAAAkD,cAAA,YACwmC,CAAC;UAD3mClD,EAAE,CAAAmD,UAAA,wCAAAsF,yEAAA;YAAA,OACkmC,CAAC;UAAA,CAAE,CAAC;UADxmCzI,EAAE,CAAA2D,MAAA,EACspC,CAAC;UADzpC3D,EAAE,CAAA4D,YAAA,CAC4pC,CAAC;UAD/pC5D,EAAE,CAAA0I,UAAA,IAAA7F,uCAAA,mBACw5C,CAAC;QAAA;QAAA,IAAAC,EAAA;UAD35C9C,EAAE,CAAA2I,UAAA,cAAA5F,GAAA,CAAAuB,OAAA,EAC0tB,CAAC,YAAAvB,GAAA,CAAAoB,QAAA,EAA6B,CAAC;UAD3vBnE,EAAE,CAAA4I,SAAA,CAC2jC,CAAC;UAD9jC5I,EAAE,CAAA2I,UAAA,qBAAA5F,GAAA,CAAAwB,IAAA,kBAAAxB,GAAA,CAAAwB,IAAA,CAAAsE,OAAA,aAAA9F,GAAA,CAAA2B,QAAA,CAAAoE,SAAA,GAAA/F,GAAA,CAAAoB,QAAA,GAC2jC,CAAC;UAD9jCnE,EAAE,CAAA4I,SAAA,CACspC,CAAC;UADzpC5I,EAAE,CAAA+I,kBAAA,MAAAhG,GAAA,CAAA2B,QAAA,CAAAoE,SAAA,GAAA/F,GAAA,CAAAoB,QAAA,SACspC,CAAC;UADzpCnE,EAAE,CAAA4I,SAAA,CAC4uC,CAAC;UAD/uC5I,EAAE,CAAA2I,UAAA,UAAA5F,GAAA,CAAA4B,SAAA,CAAAM,GAAA,kBAAAlC,GAAA,CAAA4B,SAAA,CAAAM,GAAA,CAAAa,WAAA,QAAA/C,GAAA,CAAAuB,OAAA,OAAAvB,GAAA,CAAA6B,QAAA,EAC4uC,CAAC;QAAA;MAAA;MAAAoE,YAAA,GAAgzCrI,WAAW,EAA+BD,EAAE,CAACuI,oBAAoB,EAAyPvI,EAAE,CAACwI,eAAe,EAAsFxI,EAAE,CAACyI,OAAO,EAA8MpJ,IAAI,EAA4Fa,mBAAmB,EAA+BK,SAAS,EAAoIY,OAAO,EAAiFC,OAAO,EAA4HJ,eAAe;MAAA0H,MAAA;MAAAC,eAAA;IAAA,EAAsH;EAAE;AAC/3H;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGtJ,EAAE,CAAAuJ,iBAAA,CAGX1F,qBAAqB,EAAc,CAAC;IACpHwC,IAAI,EAAEhG,SAAS;IACfmJ,IAAI,EAAE,CAAC;MAAE7B,UAAU,EAAE,IAAI;MAAE8B,QAAQ,EAAE,gBAAgB;MAAEC,OAAO,EAAE,CACpD/I,WAAW,EACXZ,IAAI,EACJa,mBAAmB,EACnBK,SAAS,EACTY,OAAO,EACPC,OAAO,EACPJ,eAAe,CAClB;MAAE2H,eAAe,EAAE/I,uBAAuB,CAACqJ,MAAM;MAAEC,cAAc,EAAE,CAAC/H,OAAO,CAAC;MAAEgI,IAAI,EAAE;QACjFC,OAAO,EAAE,EAAE;QACXC,KAAK,EAAE,iBAAiB;QACxB,eAAe,EAAE,WAAW;QAC5B,iBAAiB,EAAE,wBAAwB;QAC3C,SAAS,EAAE,uCAAuC;QAClD,6BAA6B,EAAE,UAAU;QACzC,yBAAyB,EAAE,QAAQ;QACnC,YAAY,EAAE;MAClB,CAAC;MAAE7B,QAAQ,EAAE,m1BAAm1B;MAAEkB,MAAM,EAAE,CAAC,mjCAAmjC;IAAE,CAAC;EAC76D,CAAC,CAAC,QAAkB;IAAE/D,KAAK,EAAE,CAAC;MACtBgB,IAAI,EAAE9F,SAAS;MACfiJ,IAAI,EAAE,CAAC3H,OAAO,EAAE;QAAEmI,IAAI,EAAE5J;MAAW,CAAC;IACxC,CAAC,CAAC;IAAE2E,QAAQ,EAAE,CAAC;MACXsB,IAAI,EAAE7F;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyJ,8BAA8B,GAAG;EACnCC,SAAS,EAAE,GAAG;EACdC,MAAM,EAAE;AACZ,CAAC;AACD,MAAM,CAACC,sBAAsB,EAAEC,2BAA2B,CAAC,GAAGzH,gBAAgB,CAACqH,8BAA8B,CAAC;AAE9G,MAAMK,qBAAqB,SAASpI,UAAU,CAAC;EAC3C4B,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGyG,SAAS,CAAC;IACnB,IAAI,CAAC7F,QAAQ,GAAGzE,MAAM,CAAC0B,kBAAkB,CAAC;IAC1C,IAAI,CAACoC,OAAO,GAAG9D,MAAM,CAACmK,sBAAsB,CAAC;IAC7C,IAAI,CAAClG,MAAM,GAAGjE,MAAM,CAACY,aAAa,CAAC;IACnC,IAAI,CAAC8D,SAAS,GAAG1E,MAAM,CAACoB,0BAA0B,CAAC;IACnD,IAAI,CAACmJ,IAAI,GAAG/H,eAAe,CAAC,CAAC;IAC7B,IAAI,CAACgI,QAAQ,GAAGxK,MAAM,CAACyC,oBAAoB,CAAC;IAC5C,IAAI,CAACgI,GAAG,GAAGzK,MAAM,CAACmC,aAAa,CAAC,CAC3BuI,mBAAmB,CAACC,IAAI,CAACjI,MAAM,CAAEkI,MAAM,IAAK,CAACA,MAAM,CAAC,EAAE5I,kBAAkB,CAAC,CAAC,CAAC,CAC3E6I,SAAS,CAAC,MAAM;MACjB,IAAI,CAACC,OAAO,CAAC,CAAC;MACd,IAAI,CAACpG,SAAS,CAACV,KAAK,CAAC0B,GAAG,CAAC,EAAE,CAAC;IAChC,CAAC,CAAC;IACF,IAAI,CAACuE,SAAS,GAAG,IAAI,CAACnG,OAAO,CAACmG,SAAS;IACvC,IAAI,CAACC,MAAM,GAAG,IAAI,CAACpG,OAAO,CAACoG,MAAM;IACjC,IAAI,CAACa,EAAE,GAAGzI,gBAAgB,CAAC,CAAC;EAChC;EACA0I,QAAQA,CAAChH,KAAK,EAAE;IACZ,IAAI,CAACU,SAAS,CAACV,KAAK,CAAC0B,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAI,CAACT,QAAQ,CAAC,IAAI,CAACiF,MAAM,GAAGe,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACnH,KAAK,CAACoH,OAAO,CAAC,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC,CAAC,GAAGpH,KAAK,CAAC;EACvF;EACA8G,OAAOA,CAAA,EAAG;IACN,MAAM9G,KAAK,GAAG,IAAI,CAACU,SAAS,CAACV,KAAK,CAAC,CAAC,CAACqH,IAAI,CAAC,CAAC;IAC3C,MAAMC,KAAK,GAAG,IAAI,CAACrB,SAAS,GAAGjG,KAAK,CAACuH,KAAK,CAAC,IAAI,CAACtB,SAAS,CAAC,GAAG,CAACjG,KAAK,CAAC;IACpE,MAAMwH,KAAK,GAAGF,KAAK,CAAC5I,MAAM,CAAE0B,IAAI,IAAKA,IAAI,IAAI,CAAC,IAAI,CAACK,QAAQ,CAACG,mBAAmB,CAAC,CAAC,CAACR,IAAI,CAAC,CAAC;IACxF,IAAI,CAACJ,KAAK,IAAI,CAACwH,KAAK,CAACC,MAAM,EAAE;MACzB;IACJ;IACA,IAAI,CAACT,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAChH,KAAK,CAAC,CAAC,EAAE,GAAGwH,KAAK,CAAC,CAAC;IAC1C,IAAI,CAACE,QAAQ,CAAC,CAAC;EACnB;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACpB,IAAI,CAAC7E,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC8E,QAAQ,CAAC5B,OAAO,CAAC;IACtC,IAAI,IAAI,CAACqB,SAAS,IAAI,IAAI,CAACvF,SAAS,CAACV,KAAK,CAAC,CAAC,CAAC4H,KAAK,CAAC,IAAI,CAAC3B,SAAS,CAAC,EAAE;MAChE,IAAI,CAACa,OAAO,CAAC,CAAC;IAClB,CAAC,MACI;MACD,IAAI,CAACY,QAAQ,CAAC,CAAC;IACnB;EACJ;EACAG,OAAOA,CAACC,KAAK,EAAE;IACX,MAAM9H,KAAK,GAAG,cAAc,IAAI8H,KAAK,GAC/BA,KAAK,CAACC,YAAY,EAAEC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,GAC/CzJ,uBAAuB,CAACuJ,KAAK,CAAC;IACpC,IAAI,CAACpH,SAAS,CAACV,KAAK,CAAC0B,GAAG,CAAC1B,KAAK,CAAC;IAC/B,IAAI,CAAC8G,OAAO,CAAC,CAAC;EAClB;EACAmB,WAAWA,CAACC,GAAG,EAAE;IACb;IACA,IAAIA,GAAG,KAAK,WAAW,IACnB,CAAC,IAAI,CAACxH,SAAS,CAACV,KAAK,CAAC,CAAC,IACvB,IAAI,CAAC6B,WAAW,CAAC,CAAC,KACjB,IAAI,CAAC5B,MAAM,IAAI,CAAC,IAAI,CAACS,SAAS,CAACN,IAAI,CAAC,EAAE;MACvC,IAAI,CAACa,QAAQ,CAAC,IAAI,CAACjB,KAAK,CAAC,CAAC,CAACmI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5C;EACJ;EACAT,QAAQA,CAAA,EAAG;IACP,IAAIU,IAAI,GAAG,CAAC;IACZ,IAAI;MACAA,IAAI,GAAG,IAAI,CAAC1H,SAAS,CAACqG,EAAE,CAACsB,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC1D,CAAC,CACD,MAAM,CAAE;IACR;IACAvG,UAAU,CAAC,MAAM;MACb,IAAI,CAACpB,SAAS,CAACqG,EAAE,CAACW,QAAQ,CAAC;QACvBY,IAAI,EAAEF,IAAI,GAAGG,MAAM,CAACC,gBAAgB;QACpCC,GAAG,EAAEF,MAAM,CAACC;MAChB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACzG,IAAI;MAAA,IAAA2G,kCAAA;MAAA,gBAAAC,8BAAA1G,CAAA;QAAA,QAAAyG,kCAAA,KAAAA,kCAAA,GA5G+E3M,EAAE,CAAA6M,qBAAA,CA4GQvC,qBAAqB,IAAApE,CAAA,IAArBoE,qBAAqB;MAAA;IAAA,IAAqD;EAAE;EACvL;IAAS,IAAI,CAACwC,IAAI,kBA7G+E9M,EAAE,CAAA+M,iBAAA;MAAA1G,IAAA,EA6GJiE,qBAAqB;MAAAhE,SAAA;MAAAQ,SAAA,mBAAmJ,OAAO;MAAAC,QAAA;MAAAC,YAAA,WAAAgG,mCAAAlK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7G7K9C,EAAE,CAAAmD,UAAA,mCAAA8J,+DAAA;YAAA,OA6GJlK,GAAA,CAAAgI,OAAA,CAAQ,CAAC;UAAA,CAAW,CAAC,8BAAAmC,0DAAA/F,MAAA;YAAA,OAArBpE,GAAA,CAAAmJ,WAAA,CAAA/E,MAAA,CAAAgF,GAAsB,CAAC;UAAA,CAAH,CAAC,mBAAAgB,+CAAA;YAAA,OAArBpK,GAAA,CAAA6I,OAAA,CAAQ,CAAC;UAAA,CAAW,CAAC,2BAAAwB,uDAAAjG,MAAA;YAAA,OAArBpE,GAAA,CAAA+I,OAAA,CAAA3E,MAAc,CAAC;UAAA,CAAK,CAAC,0BAAAkG,sDAAAlG,MAAA;YAAA,OAArBpE,GAAA,CAAA+I,OAAA,CAAA3E,MAAc,CAAC;UAAA,CAAK,CAAC;QAAA;QAAA,IAAArE,EAAA;UA7GnB9C,EAAE,CAAAsN,cAAA,aA6GJvK,GAAA,CAAA6B,QAAA,CAAS,CAAW,CAAC;QAAA;MAAA;MAAA8C,MAAA;QAAAwC,SAAA;QAAAC,MAAA;MAAA;MAAAxC,UAAA;MAAAC,QAAA,GA7GnB5H,EAAE,CAAAuN,kBAAA,CA6Gua,CAClgBpL,YAAY,CAACmI,qBAAqB,CAAC,EACnCxJ,wBAAwB,CAAC,EAAE,CAAC,EAC5BQ,sBAAsB,CAACgJ,qBAAqB,CAAC,CAChD,GAjH4FtK,EAAE,CAAA6H,uBAAA,EAiHvCxF,IAAI,CAACC,kBAAkB;QAAAkL,SAAA,EAAiBtM,IAAI,CAACK,gBAAgB;QAAAmG,MAAA;MAAA,KAjHxB1H,EAAE,CAAAyN,0BAAA;IAAA,EAiH0I;EAAE;AACnP;AACA;EAAA,QAAAnE,SAAA,oBAAAA,SAAA,KAnHqGtJ,EAAE,CAAAuJ,iBAAA,CAmHXe,qBAAqB,EAAc,CAAC;IACpHjE,IAAI,EAAE5F,SAAS;IACf+I,IAAI,EAAE,CAAC;MACC7B,UAAU,EAAE,IAAI;MAChB8B,QAAQ,EAAE,qBAAqB;MAC/BiE,SAAS,EAAE,CACPvL,YAAY,CAACmI,qBAAqB,CAAC,EACnCxJ,wBAAwB,CAAC,EAAE,CAAC,EAC5BQ,sBAAsB,CAACgJ,qBAAqB,CAAC,CAChD;MACDV,cAAc,EAAE,CACZtH,kBAAkB,EAClB;QACIkL,SAAS,EAAEjM,gBAAgB;QAC3BmG,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO;MACtD,CAAC,CACJ;MACDmC,IAAI,EAAE;QACF8D,YAAY,EAAE,OAAO;QACrB,YAAY,EAAE,YAAY;QAC1B,yBAAyB,EAAE,WAAW;QACtC,oBAAoB,EAAE,yBAAyB;QAC/C,SAAS,EAAE,WAAW;QACtB,iBAAiB,EAAE,iBAAiB;QACpC,gBAAgB,EAAE;MACtB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEzD,SAAS,EAAE,CAAC;MAC1B7D,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAE2J,MAAM,EAAE,CAAC;MACT9D,IAAI,EAAE7F;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMoN,YAAY,GAAG,CAACtD,qBAAqB,EAAEzG,qBAAqB,CAAC;;AAEnE;AACA;AACA;;AAEA,SAASoG,8BAA8B,EAAEG,sBAAsB,EAAEwD,YAAY,EAAE/J,qBAAqB,EAAEyG,qBAAqB,EAAED,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}