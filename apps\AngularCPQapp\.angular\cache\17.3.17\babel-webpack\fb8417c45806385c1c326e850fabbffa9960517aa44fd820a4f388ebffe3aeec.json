{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { SkipSelf, Optional, inject, DestroyRef, Directive } from '@angular/core';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { Observable, merge, distinctUntilChanged } from 'rxjs';\nclass TuiAccessor {}\nclass TuiPositionAccessor extends TuiAccessor {}\nclass TuiRectAccessor extends TuiAccessor {}\nfunction tuiProvideAccessor(provide, type, fallback) {\n  return {\n    provide,\n    deps: [[new SkipSelf(), new Optional(), provide], fallback],\n    useFactory: tuiFallbackAccessor(type)\n  };\n}\nfunction tuiFallbackAccessor(type) {\n  return (accessors, fallback) => accessors?.find?.(accessor => accessor !== fallback && accessor.type === type) || fallback;\n}\nfunction tuiPositionAccessorFor(type, fallback) {\n  return tuiProvideAccessor(TuiPositionAccessor, type, fallback);\n}\nfunction tuiRectAccessorFor(type, fallback) {\n  return tuiProvideAccessor(TuiRectAccessor, type, fallback);\n}\nfunction tuiAsPositionAccessor(accessor) {\n  return tuiProvide(TuiPositionAccessor, accessor, true);\n}\nfunction tuiAsRectAccessor(accessor) {\n  return tuiProvide(TuiRectAccessor, accessor, true);\n}\nclass TuiVehicle {}\nfunction tuiAsVehicle(vehicle) {\n  return tuiProvide(TuiVehicle, vehicle, true);\n}\nclass TuiDriver extends Observable {}\nfunction tuiAsDriver(driver) {\n  return tuiProvide(TuiDriver, driver, true);\n}\nclass TuiDriverDirective {\n  constructor() {\n    this.destroyRef = inject(DestroyRef);\n    this.drivers = inject(TuiDriver, {\n      self: true,\n      optional: true\n    }) || [];\n    this.vehicles = inject(TuiVehicle, {\n      self: true,\n      optional: true\n    });\n  }\n  ngAfterViewInit() {\n    const vehicle = this.vehicles?.find(({\n      type\n    }) => type === this.type);\n    merge(...this.drivers.filter(({\n      type\n    }) => type === this.type)).pipe(distinctUntilChanged(), takeUntilDestroyed(this.destroyRef)).subscribe(value => {\n      vehicle?.toggle(value);\n    });\n  }\n  static {\n    this.ɵfac = function TuiDriverDirective_Factory(t) {\n      return new (t || TuiDriverDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDriverDirective\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDriverDirective, [{\n    type: Directive\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiAccessor, TuiDriver, TuiDriverDirective, TuiPositionAccessor, TuiRectAccessor, TuiVehicle, tuiAsDriver, tuiAsPositionAccessor, tuiAsRectAccessor, tuiAsVehicle, tuiFallbackAccessor, tuiPositionAccessorFor, tuiProvideAccessor, tuiRectAccessorFor };", "map": {"version": 3, "names": ["i0", "SkipSelf", "Optional", "inject", "DestroyRef", "Directive", "tui<PERSON><PERSON><PERSON>", "takeUntilDestroyed", "Observable", "merge", "distinctUntilChanged", "TuiAccessor", "TuiPositionAccessor", "TuiRectAccessor", "tuiProvideAccessor", "provide", "type", "fallback", "deps", "useFactory", "tui<PERSON>allbackAccessor", "accessors", "find", "accessor", "tuiPositionAccessorFor", "tuiRectAccessorFor", "tuiAsPositionAccessor", "tuiAsRectAccessor", "TuiVehicle", "tuiAsVehicle", "vehicle", "TuiDriver", "tuiAsDriver", "driver", "TuiDriverDirective", "constructor", "destroyRef", "drivers", "self", "optional", "vehicles", "ngAfterViewInit", "filter", "pipe", "subscribe", "value", "toggle", "ɵfac", "TuiDriverDirective_Factory", "t", "ɵdir", "ɵɵdefineDirective", "ngDevMode", "ɵsetClassMetadata"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-classes.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { SkipSelf, Optional, inject, DestroyRef, Directive } from '@angular/core';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { Observable, merge, distinctUntilChanged } from 'rxjs';\n\nclass TuiAccessor {\n}\nclass TuiPositionAccessor extends TuiAccessor {\n}\nclass TuiRectAccessor extends TuiAccessor {\n}\nfunction tuiProvideAccessor(provide, type, fallback) {\n    return {\n        provide,\n        deps: [[new SkipSelf(), new Optional(), provide], fallback],\n        useFactory: tuiFallbackAccessor(type),\n    };\n}\nfunction tuiFallbackAccessor(type) {\n    return (accessors, fallback) => accessors?.find?.((accessor) => accessor !== fallback && accessor.type === type) || fallback;\n}\nfunction tuiPositionAccessorFor(type, fallback) {\n    return tuiProvideAccessor(TuiPositionAccessor, type, fallback);\n}\nfunction tuiRectAccessorFor(type, fallback) {\n    return tuiProvideAccessor(TuiRectAccessor, type, fallback);\n}\nfunction tuiAsPositionAccessor(accessor) {\n    return tuiProvide(TuiPositionAccessor, accessor, true);\n}\nfunction tuiAsRectAccessor(accessor) {\n    return tuiProvide(TuiRectAccessor, accessor, true);\n}\n\nclass TuiVehicle {\n}\nfunction tuiAsVehicle(vehicle) {\n    return tuiProvide(TuiVehicle, vehicle, true);\n}\n\nclass TuiDriver extends Observable {\n}\nfunction tuiAsDriver(driver) {\n    return tuiProvide(TuiDriver, driver, true);\n}\nclass TuiDriverDirective {\n    constructor() {\n        this.destroyRef = inject(DestroyRef);\n        this.drivers = inject(TuiDriver, { self: true, optional: true }) || [];\n        this.vehicles = inject(TuiVehicle, {\n            self: true,\n            optional: true,\n        });\n    }\n    ngAfterViewInit() {\n        const vehicle = this.vehicles?.find(({ type }) => type === this.type);\n        merge(...this.drivers.filter(({ type }) => type === this.type))\n            .pipe(distinctUntilChanged(), takeUntilDestroyed(this.destroyRef))\n            .subscribe((value) => {\n            vehicle?.toggle(value);\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDriverDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDriverDirective, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDriverDirective, decorators: [{\n            type: Directive\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiAccessor, TuiDriver, TuiDriverDirective, TuiPositionAccessor, TuiRectAccessor, TuiVehicle, tuiAsDriver, tuiAsPositionAccessor, tuiAsRectAccessor, tuiAsVehicle, tuiFallbackAccessor, tuiPositionAccessorFor, tuiProvideAccessor, tuiRectAccessorFor };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,QAAQ,eAAe;AACjF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,UAAU,EAAEC,KAAK,EAAEC,oBAAoB,QAAQ,MAAM;AAE9D,MAAMC,WAAW,CAAC;AAElB,MAAMC,mBAAmB,SAASD,WAAW,CAAC;AAE9C,MAAME,eAAe,SAASF,WAAW,CAAC;AAE1C,SAASG,kBAAkBA,CAACC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAE;EACjD,OAAO;IACHF,OAAO;IACPG,IAAI,EAAE,CAAC,CAAC,IAAIjB,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAEa,OAAO,CAAC,EAAEE,QAAQ,CAAC;IAC3DE,UAAU,EAAEC,mBAAmB,CAACJ,IAAI;EACxC,CAAC;AACL;AACA,SAASI,mBAAmBA,CAACJ,IAAI,EAAE;EAC/B,OAAO,CAACK,SAAS,EAAEJ,QAAQ,KAAKI,SAAS,EAAEC,IAAI,GAAIC,QAAQ,IAAKA,QAAQ,KAAKN,QAAQ,IAAIM,QAAQ,CAACP,IAAI,KAAKA,IAAI,CAAC,IAAIC,QAAQ;AAChI;AACA,SAASO,sBAAsBA,CAACR,IAAI,EAAEC,QAAQ,EAAE;EAC5C,OAAOH,kBAAkB,CAACF,mBAAmB,EAAEI,IAAI,EAAEC,QAAQ,CAAC;AAClE;AACA,SAASQ,kBAAkBA,CAACT,IAAI,EAAEC,QAAQ,EAAE;EACxC,OAAOH,kBAAkB,CAACD,eAAe,EAAEG,IAAI,EAAEC,QAAQ,CAAC;AAC9D;AACA,SAASS,qBAAqBA,CAACH,QAAQ,EAAE;EACrC,OAAOjB,UAAU,CAACM,mBAAmB,EAAEW,QAAQ,EAAE,IAAI,CAAC;AAC1D;AACA,SAASI,iBAAiBA,CAACJ,QAAQ,EAAE;EACjC,OAAOjB,UAAU,CAACO,eAAe,EAAEU,QAAQ,EAAE,IAAI,CAAC;AACtD;AAEA,MAAMK,UAAU,CAAC;AAEjB,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC3B,OAAOxB,UAAU,CAACsB,UAAU,EAAEE,OAAO,EAAE,IAAI,CAAC;AAChD;AAEA,MAAMC,SAAS,SAASvB,UAAU,CAAC;AAEnC,SAASwB,WAAWA,CAACC,MAAM,EAAE;EACzB,OAAO3B,UAAU,CAACyB,SAAS,EAAEE,MAAM,EAAE,IAAI,CAAC;AAC9C;AACA,MAAMC,kBAAkB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAGjC,MAAM,CAACC,UAAU,CAAC;IACpC,IAAI,CAACiC,OAAO,GAAGlC,MAAM,CAAC4B,SAAS,EAAE;MAAEO,IAAI,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,IAAI,EAAE;IACtE,IAAI,CAACC,QAAQ,GAAGrC,MAAM,CAACyB,UAAU,EAAE;MAC/BU,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACd,CAAC,CAAC;EACN;EACAE,eAAeA,CAAA,EAAG;IACd,MAAMX,OAAO,GAAG,IAAI,CAACU,QAAQ,EAAElB,IAAI,CAAC,CAAC;MAAEN;IAAK,CAAC,KAAKA,IAAI,KAAK,IAAI,CAACA,IAAI,CAAC;IACrEP,KAAK,CAAC,GAAG,IAAI,CAAC4B,OAAO,CAACK,MAAM,CAAC,CAAC;MAAE1B;IAAK,CAAC,KAAKA,IAAI,KAAK,IAAI,CAACA,IAAI,CAAC,CAAC,CAC1D2B,IAAI,CAACjC,oBAAoB,CAAC,CAAC,EAAEH,kBAAkB,CAAC,IAAI,CAAC6B,UAAU,CAAC,CAAC,CACjEQ,SAAS,CAAEC,KAAK,IAAK;MACtBf,OAAO,EAAEgB,MAAM,CAACD,KAAK,CAAC;IAC1B,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,2BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFf,kBAAkB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAACgB,IAAI,kBAD+ElD,EAAE,CAAAmD,iBAAA;MAAAnC,IAAA,EACJkB;IAAkB,EAAiB;EAAE;AACxI;AACA;EAAA,QAAAkB,SAAA,oBAAAA,SAAA,KAHqGpD,EAAE,CAAAqD,iBAAA,CAGXnB,kBAAkB,EAAc,CAAC;IACjHlB,IAAI,EAAEX;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASM,WAAW,EAAEoB,SAAS,EAAEG,kBAAkB,EAAEtB,mBAAmB,EAAEC,eAAe,EAAEe,UAAU,EAAEI,WAAW,EAAEN,qBAAqB,EAAEC,iBAAiB,EAAEE,YAAY,EAAET,mBAAmB,EAAEI,sBAAsB,EAAEV,kBAAkB,EAAEW,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}