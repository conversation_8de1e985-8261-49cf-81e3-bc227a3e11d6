{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Renderer2, Directive, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { svgNodeFilter } from '@taiga-ui/cdk/constants';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiPx } from '@taiga-ui/cdk/utils/miscellaneous';\nconst [TUI_HIGHLIGHT_OPTIONS, tuiHighlightOptionsProvider] = tuiCreateOptions({\n  highlightColor: 'var(--tui-service-selection-background)'\n});\nclass TuiHighlight {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.renderer = inject(Renderer2);\n    this.doc = inject(DOCUMENT);\n    this.highlight = this.setUpHighlight();\n    this.treeWalker = this.doc.createTreeWalker(this.el, NodeFilter.SHOW_TEXT, svgNodeFilter);\n    this.tuiHighlight = '';\n    this.tuiHighlightColor = inject(TUI_HIGHLIGHT_OPTIONS).highlightColor;\n    inject(ResizeObserverService, {\n      self: true\n    }).pipe(takeUntilDestroyed()).subscribe(() => this.updateStyles());\n  }\n  ngOnChanges() {\n    this.updateStyles();\n  }\n  get match() {\n    return this.indexOf(this.el.textContent) !== -1;\n  }\n  updateStyles() {\n    this.highlight.style.display = 'none';\n    if (!this.match) {\n      return;\n    }\n    this.treeWalker.currentNode = this.el;\n    do {\n      const index = this.indexOf(this.treeWalker.currentNode.nodeValue);\n      if (index === -1) {\n        continue;\n      }\n      const range = this.doc.createRange();\n      range.setStart(this.treeWalker.currentNode, index);\n      range.setEnd(this.treeWalker.currentNode, index + this.tuiHighlight.length);\n      const hostRect = this.el.getBoundingClientRect();\n      const {\n        left,\n        top,\n        width,\n        height\n      } = range.getBoundingClientRect();\n      const {\n        style\n      } = this.highlight;\n      style.background = this.tuiHighlightColor;\n      style.left = tuiPx(left - hostRect.left);\n      style.top = tuiPx(top - hostRect.top);\n      style.width = tuiPx(width);\n      style.height = tuiPx(height);\n      style.display = 'block';\n      return;\n    } while (this.treeWalker.nextNode());\n  }\n  indexOf(source) {\n    return !source || !this.tuiHighlight ? -1 : source.toLowerCase().indexOf(this.tuiHighlight.toLowerCase());\n  }\n  setUpHighlight() {\n    const highlight = this.renderer.createElement('div');\n    const {\n      style\n    } = highlight;\n    style.background = this.tuiHighlightColor;\n    style.zIndex = '-1';\n    style.position = 'absolute';\n    this.renderer.appendChild(this.el, highlight);\n    return highlight;\n  }\n  static {\n    this.ɵfac = function TuiHighlight_Factory(t) {\n      return new (t || TuiHighlight)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiHighlight,\n      selectors: [[\"\", \"tuiHighlight\", \"\"]],\n      hostVars: 4,\n      hostBindings: function TuiHighlight_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"position\", \"relative\")(\"z-index\", 0);\n        }\n      },\n      inputs: {\n        tuiHighlight: \"tuiHighlight\",\n        tuiHighlightColor: \"tuiHighlightColor\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([ResizeObserverService]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHighlight, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiHighlight]',\n      providers: [ResizeObserverService],\n      host: {\n        '[style.position]': '\"relative\"',\n        '[style.zIndex]': '0'\n      }\n    }]\n  }], function () {\n    return [];\n  }, {\n    tuiHighlight: [{\n      type: Input\n    }],\n    tuiHighlightColor: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_HIGHLIGHT_OPTIONS, TuiHighlight, tuiHighlightOptionsProvider };", "map": {"version": 3, "names": ["DOCUMENT", "i0", "inject", "Renderer2", "Directive", "Input", "takeUntilDestroyed", "ResizeObserverService", "svgNodeFilter", "tuiCreateOptions", "tuiInjectElement", "tuiPx", "TUI_HIGHLIGHT_OPTIONS", "tuiHighlightOptionsProvider", "highlightColor", "TuiHighlight", "constructor", "el", "renderer", "doc", "highlight", "setUpHighlight", "<PERSON><PERSON><PERSON><PERSON>", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_TEXT", "tui<PERSON><PERSON><PERSON>", "tuiHighlightColor", "self", "pipe", "subscribe", "updateStyles", "ngOnChanges", "match", "indexOf", "textContent", "style", "display", "currentNode", "index", "nodeValue", "range", "createRange", "setStart", "setEnd", "length", "hostRect", "getBoundingClientRect", "left", "top", "width", "height", "background", "nextNode", "source", "toLowerCase", "createElement", "zIndex", "position", "append<PERSON><PERSON><PERSON>", "ɵfac", "TuiHighlight_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostVars", "hostBindings", "TuiHighlight_HostBindings", "rf", "ctx", "ɵɵstyleProp", "inputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-highlight.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Renderer2, Directive, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { svgNodeFilter } from '@taiga-ui/cdk/constants';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiPx } from '@taiga-ui/cdk/utils/miscellaneous';\n\nconst [TUI_HIGHLIGHT_OPTIONS, tuiHighlightOptionsProvider] = tuiCreateOptions({\n    highlightColor: 'var(--tui-service-selection-background)',\n});\nclass TuiHighlight {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.renderer = inject(Renderer2);\n        this.doc = inject(DOCUMENT);\n        this.highlight = this.setUpHighlight();\n        this.treeWalker = this.doc.createTreeWalker(this.el, NodeFilter.SHOW_TEXT, svgNodeFilter);\n        this.tuiHighlight = '';\n        this.tuiHighlightColor = inject(TUI_HIGHLIGHT_OPTIONS).highlightColor;\n        inject(ResizeObserverService, { self: true })\n            .pipe(takeUntilDestroyed())\n            .subscribe(() => this.updateStyles());\n    }\n    ngOnChanges() {\n        this.updateStyles();\n    }\n    get match() {\n        return this.indexOf(this.el.textContent) !== -1;\n    }\n    updateStyles() {\n        this.highlight.style.display = 'none';\n        if (!this.match) {\n            return;\n        }\n        this.treeWalker.currentNode = this.el;\n        do {\n            const index = this.indexOf(this.treeWalker.currentNode.nodeValue);\n            if (index === -1) {\n                continue;\n            }\n            const range = this.doc.createRange();\n            range.setStart(this.treeWalker.currentNode, index);\n            range.setEnd(this.treeWalker.currentNode, index + this.tuiHighlight.length);\n            const hostRect = this.el.getBoundingClientRect();\n            const { left, top, width, height } = range.getBoundingClientRect();\n            const { style } = this.highlight;\n            style.background = this.tuiHighlightColor;\n            style.left = tuiPx(left - hostRect.left);\n            style.top = tuiPx(top - hostRect.top);\n            style.width = tuiPx(width);\n            style.height = tuiPx(height);\n            style.display = 'block';\n            return;\n        } while (this.treeWalker.nextNode());\n    }\n    indexOf(source) {\n        return !source || !this.tuiHighlight\n            ? -1\n            : source.toLowerCase().indexOf(this.tuiHighlight.toLowerCase());\n    }\n    setUpHighlight() {\n        const highlight = this.renderer.createElement('div');\n        const { style } = highlight;\n        style.background = this.tuiHighlightColor;\n        style.zIndex = '-1';\n        style.position = 'absolute';\n        this.renderer.appendChild(this.el, highlight);\n        return highlight;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHighlight, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHighlight, isStandalone: true, selector: \"[tuiHighlight]\", inputs: { tuiHighlight: \"tuiHighlight\", tuiHighlightColor: \"tuiHighlightColor\" }, host: { properties: { \"style.position\": \"\\\"relative\\\"\", \"style.zIndex\": \"0\" } }, providers: [ResizeObserverService], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHighlight, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiHighlight]',\n                    providers: [ResizeObserverService],\n                    host: {\n                        '[style.position]': '\"relative\"',\n                        '[style.zIndex]': '0',\n                    },\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { tuiHighlight: [{\n                type: Input\n            }], tuiHighlightColor: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_HIGHLIGHT_OPTIONS, TuiHighlight, tuiHighlightOptionsProvider };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACnE,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,KAAK,QAAQ,mCAAmC;AAEzD,MAAM,CAACC,qBAAqB,EAAEC,2BAA2B,CAAC,GAAGJ,gBAAgB,CAAC;EAC1EK,cAAc,EAAE;AACpB,CAAC,CAAC;AACF,MAAMC,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAGP,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACQ,QAAQ,GAAGhB,MAAM,CAACC,SAAS,CAAC;IACjC,IAAI,CAACgB,GAAG,GAAGjB,MAAM,CAACF,QAAQ,CAAC;IAC3B,IAAI,CAACoB,SAAS,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,IAAI,CAACC,UAAU,GAAG,IAAI,CAACH,GAAG,CAACI,gBAAgB,CAAC,IAAI,CAACN,EAAE,EAAEO,UAAU,CAACC,SAAS,EAAEjB,aAAa,CAAC;IACzF,IAAI,CAACkB,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,iBAAiB,GAAGzB,MAAM,CAACU,qBAAqB,CAAC,CAACE,cAAc;IACrEZ,MAAM,CAACK,qBAAqB,EAAE;MAAEqB,IAAI,EAAE;IAAK,CAAC,CAAC,CACxCC,IAAI,CAACvB,kBAAkB,CAAC,CAAC,CAAC,CAC1BwB,SAAS,CAAC,MAAM,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC;EAC7C;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,YAAY,CAAC,CAAC;EACvB;EACA,IAAIE,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,OAAO,CAAC,IAAI,CAACjB,EAAE,CAACkB,WAAW,CAAC,KAAK,CAAC,CAAC;EACnD;EACAJ,YAAYA,CAAA,EAAG;IACX,IAAI,CAACX,SAAS,CAACgB,KAAK,CAACC,OAAO,GAAG,MAAM;IACrC,IAAI,CAAC,IAAI,CAACJ,KAAK,EAAE;MACb;IACJ;IACA,IAAI,CAACX,UAAU,CAACgB,WAAW,GAAG,IAAI,CAACrB,EAAE;IACrC,GAAG;MACC,MAAMsB,KAAK,GAAG,IAAI,CAACL,OAAO,CAAC,IAAI,CAACZ,UAAU,CAACgB,WAAW,CAACE,SAAS,CAAC;MACjE,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;QACd;MACJ;MACA,MAAME,KAAK,GAAG,IAAI,CAACtB,GAAG,CAACuB,WAAW,CAAC,CAAC;MACpCD,KAAK,CAACE,QAAQ,CAAC,IAAI,CAACrB,UAAU,CAACgB,WAAW,EAAEC,KAAK,CAAC;MAClDE,KAAK,CAACG,MAAM,CAAC,IAAI,CAACtB,UAAU,CAACgB,WAAW,EAAEC,KAAK,GAAG,IAAI,CAACb,YAAY,CAACmB,MAAM,CAAC;MAC3E,MAAMC,QAAQ,GAAG,IAAI,CAAC7B,EAAE,CAAC8B,qBAAqB,CAAC,CAAC;MAChD,MAAM;QAAEC,IAAI;QAAEC,GAAG;QAAEC,KAAK;QAAEC;MAAO,CAAC,GAAGV,KAAK,CAACM,qBAAqB,CAAC,CAAC;MAClE,MAAM;QAAEX;MAAM,CAAC,GAAG,IAAI,CAAChB,SAAS;MAChCgB,KAAK,CAACgB,UAAU,GAAG,IAAI,CAACzB,iBAAiB;MACzCS,KAAK,CAACY,IAAI,GAAGrC,KAAK,CAACqC,IAAI,GAAGF,QAAQ,CAACE,IAAI,CAAC;MACxCZ,KAAK,CAACa,GAAG,GAAGtC,KAAK,CAACsC,GAAG,GAAGH,QAAQ,CAACG,GAAG,CAAC;MACrCb,KAAK,CAACc,KAAK,GAAGvC,KAAK,CAACuC,KAAK,CAAC;MAC1Bd,KAAK,CAACe,MAAM,GAAGxC,KAAK,CAACwC,MAAM,CAAC;MAC5Bf,KAAK,CAACC,OAAO,GAAG,OAAO;MACvB;IACJ,CAAC,QAAQ,IAAI,CAACf,UAAU,CAAC+B,QAAQ,CAAC,CAAC;EACvC;EACAnB,OAAOA,CAACoB,MAAM,EAAE;IACZ,OAAO,CAACA,MAAM,IAAI,CAAC,IAAI,CAAC5B,YAAY,GAC9B,CAAC,CAAC,GACF4B,MAAM,CAACC,WAAW,CAAC,CAAC,CAACrB,OAAO,CAAC,IAAI,CAACR,YAAY,CAAC6B,WAAW,CAAC,CAAC,CAAC;EACvE;EACAlC,cAAcA,CAAA,EAAG;IACb,MAAMD,SAAS,GAAG,IAAI,CAACF,QAAQ,CAACsC,aAAa,CAAC,KAAK,CAAC;IACpD,MAAM;MAAEpB;IAAM,CAAC,GAAGhB,SAAS;IAC3BgB,KAAK,CAACgB,UAAU,GAAG,IAAI,CAACzB,iBAAiB;IACzCS,KAAK,CAACqB,MAAM,GAAG,IAAI;IACnBrB,KAAK,CAACsB,QAAQ,GAAG,UAAU;IAC3B,IAAI,CAACxC,QAAQ,CAACyC,WAAW,CAAC,IAAI,CAAC1C,EAAE,EAAEG,SAAS,CAAC;IAC7C,OAAOA,SAAS;EACpB;EACA;IAAS,IAAI,CAACwC,IAAI,YAAAC,qBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyF/C,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACgD,IAAI,kBAD+E9D,EAAE,CAAA+D,iBAAA;MAAAC,IAAA,EACJlD,YAAY;MAAAmD,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADVrE,EAAE,CAAAuE,WAAA,aACJ,UAAW,CAAC,YAAZ,CAAW,CAAC;QAAA;MAAA;MAAAC,MAAA;QAAA/C,YAAA;QAAAC,iBAAA;MAAA;MAAA+C,UAAA;MAAAC,QAAA,GADV1E,EAAE,CAAA2E,kBAAA,CACwO,CAACrE,qBAAqB,CAAC,GADjQN,EAAE,CAAA4E,oBAAA;IAAA,EACqS;EAAE;AAC9Y;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG7E,EAAE,CAAA8E,iBAAA,CAGXhE,YAAY,EAAc,CAAC;IAC3GkD,IAAI,EAAE7D,SAAS;IACf4E,IAAI,EAAE,CAAC;MACCN,UAAU,EAAE,IAAI;MAChBO,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,CAAC3E,qBAAqB,CAAC;MAClC4E,IAAI,EAAE;QACF,kBAAkB,EAAE,YAAY;QAChC,gBAAgB,EAAE;MACtB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEzD,YAAY,EAAE,CAAC;MACzEuC,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEsB,iBAAiB,EAAE,CAAC;MACpBsC,IAAI,EAAE5D;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASO,qBAAqB,EAAEG,YAAY,EAAEF,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}