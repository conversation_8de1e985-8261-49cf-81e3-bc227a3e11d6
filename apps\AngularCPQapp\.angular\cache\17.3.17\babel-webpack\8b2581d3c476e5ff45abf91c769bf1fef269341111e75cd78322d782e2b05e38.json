{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { ElementRef, Directive } from '@angular/core';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils';\nclass TuiElement {\n  constructor() {\n    this.nativeElement = tuiInjectElement();\n    /**\n     * @note:\n     * Typically, when your constructor is invoked with new,\n     * an object is created, its constructor is assigned to\n     * the invoked constructor and the object is then assigned\n     * to this before executing any operations specified\n     * in your constructor method.\n     *\n     * ERROR TypeError: Class constructor ElementRef cannot be invoked without 'new'\n     * https://github.com/taiga-family/taiga-ui/issues/3072\n     *\n     * This way we can instantiate object creation\n     * without additional prototype chain for possible fix bug.\n     */\n    return new ElementRef(this.nativeElement);\n  }\n  static {\n    this.ɵfac = function TuiElement_Factory(t) {\n      return new (t || TuiElement)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiElement,\n      selectors: [[\"\", \"tuiElement\", \"\"]],\n      exportAs: [\"elementRef\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiElement, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiElement]',\n      exportAs: 'elementRef'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiElement };", "map": {"version": 3, "names": ["i0", "ElementRef", "Directive", "tuiInjectElement", "TuiElement", "constructor", "nativeElement", "ɵfac", "TuiElement_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "exportAs", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-element.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { ElementRef, Directive } from '@angular/core';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils';\n\nclass TuiElement {\n    constructor() {\n        this.nativeElement = tuiInjectElement();\n        /**\n         * @note:\n         * Typically, when your constructor is invoked with new,\n         * an object is created, its constructor is assigned to\n         * the invoked constructor and the object is then assigned\n         * to this before executing any operations specified\n         * in your constructor method.\n         *\n         * ERROR TypeError: Class constructor ElementRef cannot be invoked without 'new'\n         * https://github.com/taiga-family/taiga-ui/issues/3072\n         *\n         * This way we can instantiate object creation\n         * without additional prototype chain for possible fix bug.\n         */\n        return new ElementRef(this.nativeElement);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiElement, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiElement, isStandalone: true, selector: \"[tuiElement]\", exportAs: [\"elementRef\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiElement, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiElement]',\n                    exportAs: 'elementRef',\n                }]\n        }], ctorParameters: function () { return []; } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiElement };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,SAAS,QAAQ,eAAe;AACrD,SAASC,gBAAgB,QAAQ,qBAAqB;AAEtD,MAAMC,UAAU,CAAC;EACbC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,aAAa,GAAGH,gBAAgB,CAAC,CAAC;IACvC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,OAAO,IAAIF,UAAU,CAAC,IAAI,CAACK,aAAa,CAAC;EAC7C;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,mBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFL,UAAU;IAAA,CAAmD;EAAE;EAC1K;IAAS,IAAI,CAACM,IAAI,kBAD+EV,EAAE,CAAAW,iBAAA;MAAAC,IAAA,EACJR,UAAU;MAAAS,SAAA;MAAAC,QAAA;MAAAC,UAAA;IAAA,EAAyF;EAAE;AACxM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGhB,EAAE,CAAAiB,iBAAA,CAGXb,UAAU,EAAc,CAAC;IACzGQ,IAAI,EAAEV,SAAS;IACfgB,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,cAAc;MACxBL,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;;AAEtD;AACA;AACA;;AAEA,SAASV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}