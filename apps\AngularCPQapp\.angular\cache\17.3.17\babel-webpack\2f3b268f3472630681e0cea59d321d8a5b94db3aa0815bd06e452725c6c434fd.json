{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, forwardRef, Directive, ContentChild } from '@angular/core';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_DATA_LIST_HOST } from '@taiga-ui/core/components/data-list';\nclass TuiLabelStyles {\n  static {\n    this.ɵfac = function TuiLabelStyles_Factory(t) {\n      return new (t || TuiLabelStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiLabelStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-label\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiLabelStyles_Template(rf, ctx) {},\n      styles: [\"[tuiLabel]{display:flex;gap:.25rem;flex-direction:column;font:var(--tui-font-text-s);color:var(--tui-text-primary)}[tuiLabel]:not([data-orientation=vertical]){flex-direction:row;inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;font:var(--tui-font-text-m)}[tuiLabel]:has(tui-textfield),[tuiLabel]:has(tui-primitive-textfield),[tuiLabel]:has(tui-textarea){flex-direction:column!important;inline-size:auto!important;font:var(--tui-font-text-s)!important}[tuiLabel] input[type=checkbox],[tuiLabel] input[type=radio]{margin-inline-end:.5rem}[tuiLabel] input[type=checkbox][data-size=s],[tuiLabel] input[type=radio][data-size=s]{margin-inline-end:.25rem;margin-top:.125rem}[tuiLabel] small{font:var(--tui-font-text-s)}[tuiLabel] [tuiTitle]{margin-top:.125rem}[tuiLabel] [tuiSubtitle]{color:var(--tui-text-secondary)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiLabelStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-label'\n      },\n      styles: [\"[tuiLabel]{display:flex;gap:.25rem;flex-direction:column;font:var(--tui-font-text-s);color:var(--tui-text-primary)}[tuiLabel]:not([data-orientation=vertical]){flex-direction:row;inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;font:var(--tui-font-text-m)}[tuiLabel]:has(tui-textfield),[tuiLabel]:has(tui-primitive-textfield),[tuiLabel]:has(tui-textarea){flex-direction:column!important;inline-size:auto!important;font:var(--tui-font-text-s)!important}[tuiLabel] input[type=checkbox],[tuiLabel] input[type=radio]{margin-inline-end:.5rem}[tuiLabel] input[type=checkbox][data-size=s],[tuiLabel] input[type=radio][data-size=s]{margin-inline-end:.25rem;margin-top:.125rem}[tuiLabel] small{font:var(--tui-font-text-s)}[tuiLabel] [tuiTitle]{margin-top:.125rem}[tuiLabel] [tuiSubtitle]{color:var(--tui-text-secondary)}\\n\"]\n    }]\n  }], null, null);\n})();\n// TODO: Replace TUI_DATA_LIST_HOST with proper token once we refactor textfields\nclass TuiLabel {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.nothing = tuiWithStyles(TuiLabelStyles);\n    this.parent = inject(forwardRef(() => TUI_DATA_LIST_HOST), {\n      optional: true\n    });\n  }\n  static {\n    this.ɵfac = function TuiLabel_Factory(t) {\n      return new (t || TuiLabel)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiLabel,\n      selectors: [[\"label\", \"tuiLabel\", \"\"]],\n      contentQueries: function TuiLabel_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TUI_DATA_LIST_HOST, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.textfield = _t.first);\n        }\n      },\n      hostVars: 2,\n      hostBindings: function TuiLabel_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"for\", ctx.el.htmlFor || (ctx.parent == null ? null : ctx.parent.id))(\"data-orientation\", ctx.textfield ? \"vertical\" : \"horizontal\");\n        }\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiLabel, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'label[tuiLabel]',\n      host: {\n        '[attr.for]': 'el.htmlFor || parent?.id',\n        '[attr.data-orientation]': 'textfield ? \"vertical\" : \"horizontal\"'\n      }\n    }]\n  }], null, {\n    textfield: [{\n      type: ContentChild,\n      args: [forwardRef(() => TUI_DATA_LIST_HOST)]\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiLabel };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "forwardRef", "Directive", "ContentChild", "tuiInjectElement", "tuiWithStyles", "TUI_DATA_LIST_HOST", "TuiLabelStyles", "ɵfac", "TuiLabelStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiLabelStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "Tui<PERSON><PERSON>l", "constructor", "el", "nothing", "parent", "optional", "TuiLabel_Factory", "ɵdir", "ɵɵdefineDirective", "contentQueries", "TuiLabel_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "textfield", "first", "hostVars", "hostBindings", "TuiLabel_HostBindings", "ɵɵattribute", "htmlFor", "id", "selector"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-components-label.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, forwardRef, Directive, ContentChild } from '@angular/core';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_DATA_LIST_HOST } from '@taiga-ui/core/components/data-list';\n\nclass TuiLabelStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLabelStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiLabelStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-label\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiLabel]{display:flex;gap:.25rem;flex-direction:column;font:var(--tui-font-text-s);color:var(--tui-text-primary)}[tuiLabel]:not([data-orientation=vertical]){flex-direction:row;inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;font:var(--tui-font-text-m)}[tuiLabel]:has(tui-textfield),[tuiLabel]:has(tui-primitive-textfield),[tuiLabel]:has(tui-textarea){flex-direction:column!important;inline-size:auto!important;font:var(--tui-font-text-s)!important}[tuiLabel] input[type=checkbox],[tuiLabel] input[type=radio]{margin-inline-end:.5rem}[tuiLabel] input[type=checkbox][data-size=s],[tuiLabel] input[type=radio][data-size=s]{margin-inline-end:.25rem;margin-top:.125rem}[tuiLabel] small{font:var(--tui-font-text-s)}[tuiLabel] [tuiTitle]{margin-top:.125rem}[tuiLabel] [tuiSubtitle]{color:var(--tui-text-secondary)}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLabelStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-label',\n                    }, styles: [\"[tuiLabel]{display:flex;gap:.25rem;flex-direction:column;font:var(--tui-font-text-s);color:var(--tui-text-primary)}[tuiLabel]:not([data-orientation=vertical]){flex-direction:row;inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;font:var(--tui-font-text-m)}[tuiLabel]:has(tui-textfield),[tuiLabel]:has(tui-primitive-textfield),[tuiLabel]:has(tui-textarea){flex-direction:column!important;inline-size:auto!important;font:var(--tui-font-text-s)!important}[tuiLabel] input[type=checkbox],[tuiLabel] input[type=radio]{margin-inline-end:.5rem}[tuiLabel] input[type=checkbox][data-size=s],[tuiLabel] input[type=radio][data-size=s]{margin-inline-end:.25rem;margin-top:.125rem}[tuiLabel] small{font:var(--tui-font-text-s)}[tuiLabel] [tuiTitle]{margin-top:.125rem}[tuiLabel] [tuiSubtitle]{color:var(--tui-text-secondary)}\\n\"] }]\n        }] });\n// TODO: Replace TUI_DATA_LIST_HOST with proper token once we refactor textfields\nclass TuiLabel {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.nothing = tuiWithStyles(TuiLabelStyles);\n        this.parent = inject(forwardRef(() => TUI_DATA_LIST_HOST), { optional: true });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiLabel, isStandalone: true, selector: \"label[tuiLabel]\", host: { properties: { \"attr.for\": \"el.htmlFor || parent?.id\", \"attr.data-orientation\": \"textfield ? \\\"vertical\\\" : \\\"horizontal\\\"\" } }, queries: [{ propertyName: \"textfield\", first: true, predicate: i0.forwardRef(function () { return TUI_DATA_LIST_HOST; }), descendants: true }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'label[tuiLabel]',\n                    host: {\n                        '[attr.for]': 'el.htmlFor || parent?.id',\n                        '[attr.data-orientation]': 'textfield ? \"vertical\" : \"horizontal\"',\n                    },\n                }]\n        }], propDecorators: { textfield: [{\n                type: ContentChild,\n                args: [forwardRef(() => TUI_DATA_LIST_HOST)]\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiLabel };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,YAAY,QAAQ,eAAe;AAClI,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,kBAAkB,QAAQ,qCAAqC;AAExE,MAAMC,cAAc,CAAC;EACjB;IAAS,IAAI,CAACC,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACI,IAAI,kBAD+Ef,EAAE,CAAAgB,iBAAA;MAAAC,IAAA,EACJN,cAAc;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADZrB,EAAE,CAAAsB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC0kC;EAAE;AACnrC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGhC,EAAE,CAAAiC,iBAAA,CAGXtB,cAAc,EAAc,CAAC;IAC7GM,IAAI,EAAEhB,SAAS;IACfiC,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAE5B,iBAAiB,CAACiC,IAAI;MAAEJ,eAAe,EAAE5B,uBAAuB,CAACiC,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,k1BAAk1B;IAAE,CAAC;EAC72B,CAAC,CAAC;AAAA;AACV;AACA,MAAMU,QAAQ,CAAC;EACXC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAGjC,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACkC,OAAO,GAAGjC,aAAa,CAACE,cAAc,CAAC;IAC5C,IAAI,CAACgC,MAAM,GAAGvC,MAAM,CAACC,UAAU,CAAC,MAAMK,kBAAkB,CAAC,EAAE;MAAEkC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAClF;EACA;IAAS,IAAI,CAAChC,IAAI,YAAAiC,iBAAA/B,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,QAAQ;IAAA,CAAmD;EAAE;EACxK;IAAS,IAAI,CAACO,IAAI,kBAjB+E9C,EAAE,CAAA+C,iBAAA;MAAA9B,IAAA,EAiBJsB,QAAQ;MAAArB,SAAA;MAAA8B,cAAA,WAAAC,wBAAAtB,EAAA,EAAAC,GAAA,EAAAsB,QAAA;QAAA,IAAAvB,EAAA;UAjBN3B,EAAE,CAAAmD,cAAA,CAAAD,QAAA,EAiBiSxC,kBAAkB;QAAA;QAAA,IAAAiB,EAAA;UAAA,IAAAyB,EAAA;UAjBrTpD,EAAE,CAAAqD,cAAA,CAAAD,EAAA,GAAFpD,EAAE,CAAAsD,WAAA,QAAA1B,GAAA,CAAA2B,SAAA,GAAAH,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,sBAAAhC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3B,EAAE,CAAA4D,WAAA,QAAAhC,GAAA,CAAAa,EAAA,CAAAoB,OAAA,KAAAjC,GAAA,CAAAe,MAAA,kBAAAf,GAAA,CAAAe,MAAA,CAAAmB,EAAA,uBAAAlC,GAAA,CAAA2B,SAAA,GAiBQ,UAAU,GAAG,YAAY;QAAA;MAAA;MAAAnC,UAAA;IAAA,EAA6T;EAAE;AACvc;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAnBqGhC,EAAE,CAAAiC,iBAAA,CAmBXM,QAAQ,EAAc,CAAC;IACvGtB,IAAI,EAAEX,SAAS;IACf4B,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChB2C,QAAQ,EAAE,iBAAiB;MAC3B1B,IAAI,EAAE;QACF,YAAY,EAAE,0BAA0B;QACxC,yBAAyB,EAAE;MAC/B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEkB,SAAS,EAAE,CAAC;MAC1BtC,IAAI,EAAEV,YAAY;MAClB2B,IAAI,EAAE,CAAC7B,UAAU,CAAC,MAAMK,kBAAkB,CAAC;IAC/C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAAS6B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}