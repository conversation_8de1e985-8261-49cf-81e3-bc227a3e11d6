{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, computed, effect, signal, Directive, Input, DestroyRef, NgZone, Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport * as i2 from '@maskito/angular';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { maskitoInitialCalibrationPlugin } from '@maskito/core';\nimport { maskitoParseNumber, maskitoNumberOptionsGenerator, maskitoCaretGuard } from '@maskito/kit';\nimport { TuiControl, tuiAsControl, tuiValueTransformerFrom } from '@taiga-ui/cdk/classes';\nimport { TUI_ALLOW_SIGNAL_WRITES, CHAR_MINUS, CHAR_HYPHEN } from '@taiga-ui/cdk/constants';\nimport { TUI_IS_IOS } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiIsSafeToRound, tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldDirective, TuiWithTextfield, TUI_TEXTFIELD_OPTIONS, TuiTextfieldContent } from '@taiga-ui/core/components/textfield';\nimport { TUI_NUMBER_FORMAT, TUI_DEFAULT_NUMBER_FORMAT } from '@taiga-ui/core/tokens';\nimport { tuiFormatNumber } from '@taiga-ui/core/utils/format';\nimport { tuiMaskito } from '@taiga-ui/kit/utils';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { NgIf } from '@angular/common';\nimport { tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { timer } from 'rxjs';\nconst _c0 = [\"tuiInputNumber\", \"\", \"step\", \"\"];\nfunction TuiInputNumberStep_ng_container_0_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 2)(1, \"button\", 3);\n    i0.ɵɵlistener(\"click.prevent\", function TuiInputNumberStep_ng_container_0_section_1_Template_button_click_prevent_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onStep(ctx_r1.step()));\n    })(\"mousedown.prevent\", function TuiInputNumberStep_ng_container_0_section_1_Template_button_mousedown_prevent_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.el.focus());\n    });\n    i0.ɵɵtext(2, \" + \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 3);\n    i0.ɵɵlistener(\"click.prevent\", function TuiInputNumberStep_ng_container_0_section_1_Template_button_click_prevent_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onStep(-ctx_r1.step()));\n    })(\"mousedown.prevent\", function TuiInputNumberStep_ng_container_0_section_1_Template_button_mousedown_prevent_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.el.focus());\n    });\n    i0.ɵɵtext(4, \" - \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"appearance\", ctx_r1.appearance())(\"disabled\", !ctx_r1.input.interactive() || ctx_r1.value() >= ctx_r1.input.max())(\"iconStart\", ctx_r1.options.icons.increase);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"appearance\", ctx_r1.appearance())(\"disabled\", !ctx_r1.input.interactive() || ctx_r1.value() <= ctx_r1.input.min())(\"iconStart\", ctx_r1.options.icons.decrease);\n  }\n}\nfunction TuiInputNumberStep_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiInputNumberStep_ng_container_0_section_1_Template, 5, 6, \"section\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.step());\n  }\n}\nconst TUI_INPUT_NUMBER_DEFAULT_OPTIONS = {\n  min: Number.MIN_SAFE_INTEGER,\n  max: Number.MAX_SAFE_INTEGER,\n  prefix: '',\n  postfix: '',\n  step: 0,\n  icons: {\n    increase: '@tui.plus',\n    decrease: '@tui.minus'\n  },\n  valueTransformer: null\n};\nconst [TUI_INPUT_NUMBER_OPTIONS, tuiInputNumberOptionsProvider] = tuiCreateOptions(TUI_INPUT_NUMBER_DEFAULT_OPTIONS);\nconst DEFAULT_MAX_LENGTH = 18;\nclass TuiInputNumberDirective extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.textfield = inject(TuiTextfieldDirective);\n    this.isIOS = inject(TUI_IS_IOS);\n    this.numberFormat = toSignal(inject(TUI_NUMBER_FORMAT), {\n      initialValue: TUI_DEFAULT_NUMBER_FORMAT\n    });\n    this.formatted = computed(() => maskitoParseNumber(this.textfield.value(), this.numberFormat()));\n    this.precision = computed(() => Number.isNaN(this.numberFormat().precision) ? 2 : this.numberFormat().precision);\n    this.unfinished = computed((value = this.formatted()) => value < 0 ? value > this.max() : value < this.min());\n    this.onChangeEffect = effect(() => {\n      const value = this.formatted();\n      if (Number.isNaN(value)) {\n        this.onChange(null);\n        return;\n      }\n      if (this.unfinished() || value < this.min() || value > this.max() || this.value() === value) {\n        return;\n      }\n      this.onChange(value);\n    }, TUI_ALLOW_SIGNAL_WRITES);\n    this.options = inject(TUI_INPUT_NUMBER_OPTIONS);\n    this.element = tuiInjectElement();\n    this.inputMode = computed(() => {\n      if (this.isIOS) {\n        return this.min() < 0 ? 'text' // iPhone does not have minus sign if inputMode equals to 'numeric' / 'decimal'\n        : 'decimal';\n      }\n      /**\n       * Samsung Keyboard does not minus sign for `inputmode=decimal`\n       * @see https://github.com/taiga-family/taiga-ui/issues/11061#issuecomment-2939103792\n       */\n      return 'numeric';\n    });\n    this.defaultMaxLength = computed(() => {\n      const {\n        decimalSeparator,\n        thousandSeparator\n      } = this.numberFormat();\n      const decimalPart = !!this.precision() && this.textfield.value().includes(decimalSeparator);\n      const precision = decimalPart ? Math.min(this.precision() + 1, 20) : 0;\n      const takeThousand = thousandSeparator.repeat(5).length;\n      return DEFAULT_MAX_LENGTH + precision + takeThousand;\n    });\n    this.mask = tuiMaskito(computed(({\n      decimalMode,\n      ...numberFormat\n    } = this.numberFormat(), maximumFractionDigits = this.precision()) => this.computeMask({\n      ...numberFormat,\n      maximumFractionDigits,\n      min: this.min(),\n      max: this.max(),\n      prefix: this.prefix(),\n      postfix: this.postfix(),\n      minimumFractionDigits: decimalMode === 'always' ? maximumFractionDigits : 0\n    })));\n    this.min = signal(this.options.min);\n    this.max = signal(this.options.max);\n    this.prefix = signal(this.options.prefix);\n    this.postfix = signal(this.options.postfix);\n  }\n  set minSetter(x) {\n    this.updateMinMaxLimits(x, this.max());\n  }\n  set maxSetter(x) {\n    this.updateMinMaxLimits(this.min(), x);\n  }\n  // TODO(v5): replace with signal input\n  set prefixSetter(x) {\n    this.prefix.set(x);\n  }\n  // TODO(v5): replace with signal input\n  set postfixSetter(x) {\n    this.postfix.set(x);\n  }\n  writeValue(value) {\n    super.writeValue(value);\n    this.setValue(this.value());\n  }\n  setValue(value) {\n    this.textfield.value.set(this.formatNumber(value));\n  }\n  onBlur() {\n    this.onTouched();\n    if (!this.unfinished()) {\n      this.setValue(this.value());\n    }\n  }\n  onFocus() {\n    if (Number.isNaN(this.formatted()) && !this.readOnly()) {\n      this.textfield.value.set(this.prefix() + this.postfix());\n    }\n  }\n  formatNumber(value) {\n    if (value === null || Number.isNaN(value)) {\n      return '';\n    }\n    return (this.prefix() !== CHAR_MINUS ? this.prefix() : '') + tuiFormatNumber(value, {\n      ...this.numberFormat(),\n      /**\n       * Number can satisfy interval [Number.MIN_SAFE_INTEGER; Number.MAX_SAFE_INTEGER]\n       * but its rounding can violate it.\n       * Before BigInt support there is no perfect solution – only trade off.\n       * No rounding is better than lose precision and incorrect mutation of already valid value.\n       */\n      precision: tuiIsSafeToRound(value, this.precision()) ? this.precision() : Infinity\n    }).replace(CHAR_HYPHEN, CHAR_MINUS) + this.postfix();\n  }\n  updateMinMaxLimits(nullableMin, nullableMax) {\n    const min = this.transformer.fromControlValue(nullableMin) ?? this.options.min;\n    const max = this.transformer.fromControlValue(nullableMax) ?? this.options.max;\n    this.min.set(Math.min(min, max));\n    this.max.set(Math.max(min, max));\n  }\n  computeMask(params) {\n    const {\n      prefix = '',\n      postfix = ''\n    } = params;\n    const {\n      plugins,\n      ...options\n    } = maskitoNumberOptionsGenerator(params);\n    const initialCalibrationPlugin = maskitoInitialCalibrationPlugin(maskitoNumberOptionsGenerator({\n      ...params,\n      min: Number.MIN_SAFE_INTEGER,\n      max: Number.MAX_SAFE_INTEGER\n    }));\n    return {\n      ...options,\n      plugins: [...plugins, initialCalibrationPlugin, maskitoCaretGuard(value => [prefix.length, value.length - postfix.length])]\n    };\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiInputNumberDirective_BaseFactory;\n      return function TuiInputNumberDirective_Factory(t) {\n        return (ɵTuiInputNumberDirective_BaseFactory || (ɵTuiInputNumberDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiInputNumberDirective)))(t || TuiInputNumberDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiInputNumberDirective,\n      selectors: [[\"input\", \"tuiInputNumber\", \"\"]],\n      hostVars: 3,\n      hostBindings: function TuiInputNumberDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"blur\", function TuiInputNumberDirective_blur_HostBindingHandler() {\n            return ctx.onBlur();\n          })(\"focus\", function TuiInputNumberDirective_focus_HostBindingHandler() {\n            return ctx.onFocus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"disabled\", ctx.disabled());\n          i0.ɵɵattribute(\"inputMode\", ctx.inputMode())(\"maxLength\", ctx.element.maxLength > 0 ? ctx.element.maxLength : ctx.defaultMaxLength());\n        }\n      },\n      inputs: {\n        minSetter: [i0.ɵɵInputFlags.None, \"min\", \"minSetter\"],\n        maxSetter: [i0.ɵɵInputFlags.None, \"max\", \"maxSetter\"],\n        prefixSetter: [i0.ɵɵInputFlags.None, \"prefix\", \"prefixSetter\"],\n        postfixSetter: [i0.ɵɵInputFlags.None, \"postfix\", \"postfixSetter\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsControl(TuiInputNumberDirective), tuiValueTransformerFrom(TUI_INPUT_NUMBER_OPTIONS)]), i0.ɵɵHostDirectivesFeature([i1.TuiWithTextfield, i2.MaskitoDirective]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputNumberDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiInputNumber]',\n      providers: [tuiAsControl(TuiInputNumberDirective), tuiValueTransformerFrom(TUI_INPUT_NUMBER_OPTIONS)],\n      hostDirectives: [TuiWithTextfield, MaskitoDirective],\n      host: {\n        '[disabled]': 'disabled()',\n        '[attr.inputMode]': 'inputMode()',\n        '[attr.maxLength]': 'element.maxLength > 0 ? element.maxLength : defaultMaxLength()',\n        '(blur)': 'onBlur()',\n        '(focus)': 'onFocus()'\n      }\n    }]\n  }], null, {\n    minSetter: [{\n      type: Input,\n      args: ['min']\n    }],\n    maxSetter: [{\n      type: Input,\n      args: ['max']\n    }],\n    prefixSetter: [{\n      type: Input,\n      args: ['prefix']\n    }],\n    postfixSetter: [{\n      type: Input,\n      args: ['postfix']\n    }]\n  });\n})();\nclass TuiInputNumberStep {\n  constructor() {\n    this.destroyRef = inject(DestroyRef);\n    this.zone = inject(NgZone);\n    this.el = tuiInjectElement();\n    this.appearance = inject(TUI_TEXTFIELD_OPTIONS).appearance;\n    this.options = inject(TUI_INPUT_NUMBER_OPTIONS);\n    this.input = inject(TuiInputNumberDirective, {\n      self: true\n    });\n    this.step = signal(this.options.step);\n    this.value = computed(() => this.input.value() ?? NaN);\n  }\n  // TODO(v5): replace with signal input\n  set stepSetter(x) {\n    this.step.set(x);\n  }\n  onStep(step) {\n    const current = Number.isNaN(this.value()) ? 0 : this.value();\n    const value = tuiClamp(current + step, this.input.min(), this.input.max());\n    if (Number.isNaN(this.value())) {\n      timer(0).pipe(tuiZonefree(this.zone), takeUntilDestroyed(this.destroyRef)).subscribe(() => {\n        const caretIndex = this.el.value.length - this.input.postfix().length;\n        this.el.setSelectionRange(caretIndex, caretIndex);\n      });\n    }\n    this.input.setValue(value);\n  }\n  static {\n    this.ɵfac = function TuiInputNumberStep_Factory(t) {\n      return new (t || TuiInputNumberStep)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiInputNumberStep,\n      selectors: [[\"input\", \"tuiInputNumber\", \"\", \"step\", \"\"]],\n      hostAttrs: [\"ngSkipHydration\", \"true\"],\n      hostVars: 2,\n      hostBindings: function TuiInputNumberStep_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.arrowDown.prevent\", function TuiInputNumberStep_keydown_arrowDown_prevent_HostBindingHandler() {\n            return ctx.onStep(-ctx.step());\n          })(\"keydown.arrowUp.prevent\", function TuiInputNumberStep_keydown_arrowUp_prevent_HostBindingHandler() {\n            return ctx.onStep(ctx.step());\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_with-buttons\", ctx.step());\n        }\n      },\n      inputs: {\n        stepSetter: [i0.ɵɵInputFlags.None, \"step\", \"stepSetter\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 1,\n      vars: 0,\n      consts: [[4, \"tuiTextfieldContent\"], [\"class\", \"t-input-number-buttons\", 4, \"ngIf\"], [1, \"t-input-number-buttons\"], [\"size\", \"s\", \"tabindex\", \"-1\", \"tuiIconButton\", \"\", \"type\", \"button\", 1, \"t-button\", 3, \"click.prevent\", \"mousedown.prevent\", \"appearance\", \"disabled\", \"iconStart\"]],\n      template: function TuiInputNumberStep_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiInputNumberStep_ng_container_0_Template, 2, 1, \"ng-container\", 0);\n        }\n      },\n      dependencies: [NgIf, TuiButton, TuiTextfieldContent],\n      styles: [\".t-input-number-buttons.t-input-number-buttons{position:absolute;right:0;display:flex;block-size:var(--t-height);flex-direction:column;gap:.125rem;border-radius:inherit}tui-textfield[data-size=s] .t-input-number-buttons.t-input-number-buttons{flex-direction:row-reverse}.t-input-number-buttons.t-input-number-buttons>*{flex:1 1 0;border-radius:0}.t-input-number-buttons.t-input-number-buttons>*:first-child{border-top-right-radius:inherit}.t-input-number-buttons.t-input-number-buttons>*:last-child{border-bottom-right-radius:inherit}tui-textfield[data-size=l] .t-input-number-buttons.t-input-number-buttons>*{inline-size:var(--tui-height-m)}tui-textfield[data-size=s] .t-input-number-buttons.t-input-number-buttons>*:first-child{border-top-right-radius:inherit;border-bottom-right-radius:inherit}tui-textfield[data-size=s] .t-input-number-buttons.t-input-number-buttons>*:last-child{border-radius:0}[tuiInputNumber]._with-buttons{border-top-right-radius:0;border-bottom-right-radius:0}tui-textfield[data-size=l]{--t-input-number-offset-end: calc(var(--tui-height-m) + .125rem)}tui-textfield[data-size=m]{--t-input-number-offset-end: calc(var(--tui-height-s) + .125rem)}tui-textfield[data-size=s]{--t-input-number-offset-end: calc(2 * var(--tui-height-s) + .25rem)}[tuiInputNumber]._with-buttons,[tuiInputNumber]._with-buttons~.t-template{inline-size:calc(100% - var(--t-input-number-offset-end))}[tuiInputNumber]._with-buttons~.t-content{margin-inline-end:var(--t-input-number-offset-end)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputNumberStep, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiInputNumber][step]',\n      imports: [NgIf, TuiButton, TuiTextfieldContent],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        ngSkipHydration: 'true',\n        '(keydown.arrowDown.prevent)': 'onStep(-step())',\n        '(keydown.arrowUp.prevent)': 'onStep(step())',\n        '[class._with-buttons]': 'step()'\n      },\n      template: \"<ng-container *tuiTextfieldContent>\\n    <section\\n        *ngIf=\\\"step()\\\"\\n        class=\\\"t-input-number-buttons\\\"\\n    >\\n        <button\\n            size=\\\"s\\\"\\n            tabindex=\\\"-1\\\"\\n            tuiIconButton\\n            type=\\\"button\\\"\\n            class=\\\"t-button\\\"\\n            [appearance]=\\\"appearance()\\\"\\n            [disabled]=\\\"!input.interactive() || value() >= input.max()\\\"\\n            [iconStart]=\\\"options.icons.increase\\\"\\n            (click.prevent)=\\\"onStep(step())\\\"\\n            (mousedown.prevent)=\\\"el.focus()\\\"\\n        >\\n            +\\n        </button>\\n\\n        <button\\n            size=\\\"s\\\"\\n            tabindex=\\\"-1\\\"\\n            tuiIconButton\\n            type=\\\"button\\\"\\n            class=\\\"t-button\\\"\\n            [appearance]=\\\"appearance()\\\"\\n            [disabled]=\\\"!input.interactive() || value() <= input.min()\\\"\\n            [iconStart]=\\\"options.icons.decrease\\\"\\n            (click.prevent)=\\\"onStep(-step())\\\"\\n            (mousedown.prevent)=\\\"el.focus()\\\"\\n        >\\n            -\\n        </button>\\n    </section>\\n</ng-container>\\n\",\n      styles: [\".t-input-number-buttons.t-input-number-buttons{position:absolute;right:0;display:flex;block-size:var(--t-height);flex-direction:column;gap:.125rem;border-radius:inherit}tui-textfield[data-size=s] .t-input-number-buttons.t-input-number-buttons{flex-direction:row-reverse}.t-input-number-buttons.t-input-number-buttons>*{flex:1 1 0;border-radius:0}.t-input-number-buttons.t-input-number-buttons>*:first-child{border-top-right-radius:inherit}.t-input-number-buttons.t-input-number-buttons>*:last-child{border-bottom-right-radius:inherit}tui-textfield[data-size=l] .t-input-number-buttons.t-input-number-buttons>*{inline-size:var(--tui-height-m)}tui-textfield[data-size=s] .t-input-number-buttons.t-input-number-buttons>*:first-child{border-top-right-radius:inherit;border-bottom-right-radius:inherit}tui-textfield[data-size=s] .t-input-number-buttons.t-input-number-buttons>*:last-child{border-radius:0}[tuiInputNumber]._with-buttons{border-top-right-radius:0;border-bottom-right-radius:0}tui-textfield[data-size=l]{--t-input-number-offset-end: calc(var(--tui-height-m) + .125rem)}tui-textfield[data-size=m]{--t-input-number-offset-end: calc(var(--tui-height-s) + .125rem)}tui-textfield[data-size=s]{--t-input-number-offset-end: calc(2 * var(--tui-height-s) + .25rem)}[tuiInputNumber]._with-buttons,[tuiInputNumber]._with-buttons~.t-template{inline-size:calc(100% - var(--t-input-number-offset-end))}[tuiInputNumber]._with-buttons~.t-content{margin-inline-end:var(--t-input-number-offset-end)}\\n\"]\n    }]\n  }], null, {\n    stepSetter: [{\n      type: Input,\n      args: ['step']\n    }]\n  });\n})();\nconst TuiInputNumber = [TuiInputNumberDirective, TuiInputNumberStep];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_NUMBER_DEFAULT_OPTIONS, TUI_INPUT_NUMBER_OPTIONS, TuiInputNumber, TuiInputNumberDirective, TuiInputNumberStep, tuiInputNumberOptionsProvider };", "map": {"version": 3, "names": ["i0", "inject", "computed", "effect", "signal", "Directive", "Input", "DestroyRef", "NgZone", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "toSignal", "takeUntilDestroyed", "i2", "MaskitoDirective", "maskitoInitialCalibrationPlugin", "maskitoParseNumber", "maskitoNumberOptionsGenerator", "maskitoCaretGuard", "TuiControl", "tuiAsControl", "tuiValueTransformerFrom", "TUI_ALLOW_SIGNAL_WRITES", "CHAR_MINUS", "CHAR_HYPHEN", "TUI_IS_IOS", "tuiInjectElement", "tuiIsSafeToRound", "tui<PERSON><PERSON>", "i1", "TuiTextfieldDirective", "TuiWithTextfield", "TUI_TEXTFIELD_OPTIONS", "TuiTextfieldContent", "TUI_NUMBER_FORMAT", "TUI_DEFAULT_NUMBER_FORMAT", "tuiFormatNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tuiCreateOptions", "NgIf", "tuiZonefree", "TuiButton", "timer", "_c0", "TuiInputNumberStep_ng_container_0_section_1_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiInputNumberStep_ng_container_0_section_1_Template_button_click_prevent_1_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onStep", "step", "TuiInputNumberStep_ng_container_0_section_1_Template_button_mousedown_prevent_1_listener", "el", "focus", "ɵɵtext", "ɵɵelementEnd", "TuiInputNumberStep_ng_container_0_section_1_Template_button_click_prevent_3_listener", "TuiInputNumberStep_ng_container_0_section_1_Template_button_mousedown_prevent_3_listener", "ɵɵadvance", "ɵɵproperty", "appearance", "input", "interactive", "value", "max", "options", "icons", "increase", "min", "decrease", "TuiInputNumberStep_ng_container_0_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "TUI_INPUT_NUMBER_DEFAULT_OPTIONS", "Number", "MIN_SAFE_INTEGER", "MAX_SAFE_INTEGER", "prefix", "postfix", "valueTransformer", "TUI_INPUT_NUMBER_OPTIONS", "tuiInputNumberOptionsProvider", "DEFAULT_MAX_LENGTH", "TuiInputNumberDirective", "constructor", "arguments", "textfield", "isIOS", "numberFormat", "initialValue", "formatted", "precision", "isNaN", "unfinished", "onChangeEffect", "onChange", "element", "inputMode", "defaultMaxLength", "decimalSeparator", "thousandSeparator", "decimalPart", "includes", "Math", "takeThousand", "repeat", "length", "mask", "decimalMode", "maximumFractionDigits", "computeMask", "minimumFractionDigits", "minSetter", "x", "updateMinMaxLimits", "maxSetter", "prefixSetter", "set", "postfixSetter", "writeValue", "setValue", "formatNumber", "onBlur", "onTouched", "onFocus", "readOnly", "Infinity", "replace", "nullableMin", "nullableMax", "transformer", "fromControlValue", "params", "plugins", "initialCalibrationPlugin", "ɵfac", "ɵTuiInputNumberDirective_BaseFactory", "TuiInputNumberDirective_Factory", "t", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostVars", "hostBindings", "TuiInputNumberDirective_HostBindings", "TuiInputNumberDirective_blur_HostBindingHandler", "TuiInputNumberDirective_focus_HostBindingHandler", "ɵɵhostProperty", "disabled", "ɵɵattribute", "max<PERSON><PERSON><PERSON>", "inputs", "ɵɵInputFlags", "None", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "hostDirectives", "host", "TuiInputNumberStep", "destroyRef", "zone", "self", "NaN", "stepSetter", "current", "pipe", "subscribe", "caretIndex", "setSelectionRange", "TuiInputNumberStep_Factory", "ɵcmp", "ɵɵdefineComponent", "hostAttrs", "TuiInputNumberStep_HostBindings", "TuiInputNumberStep_keydown_arrowDown_prevent_HostBindingHandler", "TuiInputNumberStep_keydown_arrowUp_prevent_HostBindingHandler", "ɵɵclassProp", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "consts", "template", "TuiInputNumberStep_Template", "dependencies", "styles", "encapsulation", "changeDetection", "imports", "OnPush", "ngSkipHydration", "TuiInputNumber"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-input-number.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, computed, effect, signal, Directive, Input, DestroyRef, NgZone, Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport * as i2 from '@maskito/angular';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { maskitoInitialCalibrationPlugin } from '@maskito/core';\nimport { maskitoParseNumber, maskitoNumberOptionsGenerator, maskitoCaretGuard } from '@maskito/kit';\nimport { TuiControl, tuiAsControl, tuiValueTransformerFrom } from '@taiga-ui/cdk/classes';\nimport { TUI_ALLOW_SIGNAL_WRITES, CHAR_MINUS, CHAR_HYPHEN } from '@taiga-ui/cdk/constants';\nimport { TUI_IS_IOS } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiIsSafeToRound, tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldDirective, TuiWithTextfield, TUI_TEXTFIELD_OPTIONS, TuiTextfieldContent } from '@taiga-ui/core/components/textfield';\nimport { TUI_NUMBER_FORMAT, TUI_DEFAULT_NUMBER_FORMAT } from '@taiga-ui/core/tokens';\nimport { tuiFormatNumber } from '@taiga-ui/core/utils/format';\nimport { tuiMaskito } from '@taiga-ui/kit/utils';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { NgIf } from '@angular/common';\nimport { tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { timer } from 'rxjs';\n\nconst TUI_INPUT_NUMBER_DEFAULT_OPTIONS = {\n    min: Number.MIN_SAFE_INTEGER,\n    max: Number.MAX_SAFE_INTEGER,\n    prefix: '',\n    postfix: '',\n    step: 0,\n    icons: {\n        increase: '@tui.plus',\n        decrease: '@tui.minus',\n    },\n    valueTransformer: null,\n};\nconst [TUI_INPUT_NUMBER_OPTIONS, tuiInputNumberOptionsProvider] = tuiCreateOptions(TUI_INPUT_NUMBER_DEFAULT_OPTIONS);\n\nconst DEFAULT_MAX_LENGTH = 18;\nclass TuiInputNumberDirective extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.textfield = inject(TuiTextfieldDirective);\n        this.isIOS = inject(TUI_IS_IOS);\n        this.numberFormat = toSignal(inject(TUI_NUMBER_FORMAT), {\n            initialValue: TUI_DEFAULT_NUMBER_FORMAT,\n        });\n        this.formatted = computed(() => maskitoParseNumber(this.textfield.value(), this.numberFormat()));\n        this.precision = computed(() => Number.isNaN(this.numberFormat().precision) ? 2 : this.numberFormat().precision);\n        this.unfinished = computed((value = this.formatted()) => value < 0 ? value > this.max() : value < this.min());\n        this.onChangeEffect = effect(() => {\n            const value = this.formatted();\n            if (Number.isNaN(value)) {\n                this.onChange(null);\n                return;\n            }\n            if (this.unfinished() ||\n                value < this.min() ||\n                value > this.max() ||\n                this.value() === value) {\n                return;\n            }\n            this.onChange(value);\n        }, TUI_ALLOW_SIGNAL_WRITES);\n        this.options = inject(TUI_INPUT_NUMBER_OPTIONS);\n        this.element = tuiInjectElement();\n        this.inputMode = computed(() => {\n            if (this.isIOS) {\n                return this.min() < 0\n                    ? 'text' // iPhone does not have minus sign if inputMode equals to 'numeric' / 'decimal'\n                    : 'decimal';\n            }\n            /**\n             * Samsung Keyboard does not minus sign for `inputmode=decimal`\n             * @see https://github.com/taiga-family/taiga-ui/issues/11061#issuecomment-2939103792\n             */\n            return 'numeric';\n        });\n        this.defaultMaxLength = computed(() => {\n            const { decimalSeparator, thousandSeparator } = this.numberFormat();\n            const decimalPart = !!this.precision() && this.textfield.value().includes(decimalSeparator);\n            const precision = decimalPart ? Math.min(this.precision() + 1, 20) : 0;\n            const takeThousand = thousandSeparator.repeat(5).length;\n            return DEFAULT_MAX_LENGTH + precision + takeThousand;\n        });\n        this.mask = tuiMaskito(computed(({ decimalMode, ...numberFormat } = this.numberFormat(), maximumFractionDigits = this.precision()) => this.computeMask({\n            ...numberFormat,\n            maximumFractionDigits,\n            min: this.min(),\n            max: this.max(),\n            prefix: this.prefix(),\n            postfix: this.postfix(),\n            minimumFractionDigits: decimalMode === 'always' ? maximumFractionDigits : 0,\n        })));\n        this.min = signal(this.options.min);\n        this.max = signal(this.options.max);\n        this.prefix = signal(this.options.prefix);\n        this.postfix = signal(this.options.postfix);\n    }\n    set minSetter(x) {\n        this.updateMinMaxLimits(x, this.max());\n    }\n    set maxSetter(x) {\n        this.updateMinMaxLimits(this.min(), x);\n    }\n    // TODO(v5): replace with signal input\n    set prefixSetter(x) {\n        this.prefix.set(x);\n    }\n    // TODO(v5): replace with signal input\n    set postfixSetter(x) {\n        this.postfix.set(x);\n    }\n    writeValue(value) {\n        super.writeValue(value);\n        this.setValue(this.value());\n    }\n    setValue(value) {\n        this.textfield.value.set(this.formatNumber(value));\n    }\n    onBlur() {\n        this.onTouched();\n        if (!this.unfinished()) {\n            this.setValue(this.value());\n        }\n    }\n    onFocus() {\n        if (Number.isNaN(this.formatted()) && !this.readOnly()) {\n            this.textfield.value.set(this.prefix() + this.postfix());\n        }\n    }\n    formatNumber(value) {\n        if (value === null || Number.isNaN(value)) {\n            return '';\n        }\n        return ((this.prefix() !== CHAR_MINUS ? this.prefix() : '') +\n            tuiFormatNumber(value, {\n                ...this.numberFormat(),\n                /**\n                 * Number can satisfy interval [Number.MIN_SAFE_INTEGER; Number.MAX_SAFE_INTEGER]\n                 * but its rounding can violate it.\n                 * Before BigInt support there is no perfect solution – only trade off.\n                 * No rounding is better than lose precision and incorrect mutation of already valid value.\n                 */\n                precision: tuiIsSafeToRound(value, this.precision())\n                    ? this.precision()\n                    : Infinity,\n            }).replace(CHAR_HYPHEN, CHAR_MINUS) +\n            this.postfix());\n    }\n    updateMinMaxLimits(nullableMin, nullableMax) {\n        const min = this.transformer.fromControlValue(nullableMin) ?? this.options.min;\n        const max = this.transformer.fromControlValue(nullableMax) ?? this.options.max;\n        this.min.set(Math.min(min, max));\n        this.max.set(Math.max(min, max));\n    }\n    computeMask(params) {\n        const { prefix = '', postfix = '' } = params;\n        const { plugins, ...options } = maskitoNumberOptionsGenerator(params);\n        const initialCalibrationPlugin = maskitoInitialCalibrationPlugin(maskitoNumberOptionsGenerator({\n            ...params,\n            min: Number.MIN_SAFE_INTEGER,\n            max: Number.MAX_SAFE_INTEGER,\n        }));\n        return {\n            ...options,\n            plugins: [\n                ...plugins,\n                initialCalibrationPlugin,\n                maskitoCaretGuard((value) => [\n                    prefix.length,\n                    value.length - postfix.length,\n                ]),\n            ],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputNumberDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputNumberDirective, isStandalone: true, selector: \"input[tuiInputNumber]\", inputs: { minSetter: [\"min\", \"minSetter\"], maxSetter: [\"max\", \"maxSetter\"], prefixSetter: [\"prefix\", \"prefixSetter\"], postfixSetter: [\"postfix\", \"postfixSetter\"] }, host: { listeners: { \"blur\": \"onBlur()\", \"focus\": \"onFocus()\" }, properties: { \"disabled\": \"disabled()\", \"attr.inputMode\": \"inputMode()\", \"attr.maxLength\": \"element.maxLength > 0 ? element.maxLength : defaultMaxLength()\" } }, providers: [\n            tuiAsControl(TuiInputNumberDirective),\n            tuiValueTransformerFrom(TUI_INPUT_NUMBER_OPTIONS),\n        ], usesInheritance: true, hostDirectives: [{ directive: i1.TuiWithTextfield }, { directive: i2.MaskitoDirective }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputNumberDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiInputNumber]',\n                    providers: [\n                        tuiAsControl(TuiInputNumberDirective),\n                        tuiValueTransformerFrom(TUI_INPUT_NUMBER_OPTIONS),\n                    ],\n                    hostDirectives: [TuiWithTextfield, MaskitoDirective],\n                    host: {\n                        '[disabled]': 'disabled()',\n                        '[attr.inputMode]': 'inputMode()',\n                        '[attr.maxLength]': 'element.maxLength > 0 ? element.maxLength : defaultMaxLength()',\n                        '(blur)': 'onBlur()',\n                        '(focus)': 'onFocus()',\n                    },\n                }]\n        }], propDecorators: { minSetter: [{\n                type: Input,\n                args: ['min']\n            }], maxSetter: [{\n                type: Input,\n                args: ['max']\n            }], prefixSetter: [{\n                type: Input,\n                args: ['prefix']\n            }], postfixSetter: [{\n                type: Input,\n                args: ['postfix']\n            }] } });\n\nclass TuiInputNumberStep {\n    constructor() {\n        this.destroyRef = inject(DestroyRef);\n        this.zone = inject(NgZone);\n        this.el = tuiInjectElement();\n        this.appearance = inject(TUI_TEXTFIELD_OPTIONS).appearance;\n        this.options = inject(TUI_INPUT_NUMBER_OPTIONS);\n        this.input = inject(TuiInputNumberDirective, { self: true });\n        this.step = signal(this.options.step);\n        this.value = computed(() => this.input.value() ?? NaN);\n    }\n    // TODO(v5): replace with signal input\n    set stepSetter(x) {\n        this.step.set(x);\n    }\n    onStep(step) {\n        const current = Number.isNaN(this.value()) ? 0 : this.value();\n        const value = tuiClamp(current + step, this.input.min(), this.input.max());\n        if (Number.isNaN(this.value())) {\n            timer(0)\n                .pipe(tuiZonefree(this.zone), takeUntilDestroyed(this.destroyRef))\n                .subscribe(() => {\n                const caretIndex = this.el.value.length - this.input.postfix().length;\n                this.el.setSelectionRange(caretIndex, caretIndex);\n            });\n        }\n        this.input.setValue(value);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputNumberStep, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputNumberStep, isStandalone: true, selector: \"input[tuiInputNumber][step]\", inputs: { stepSetter: [\"step\", \"stepSetter\"] }, host: { attributes: { \"ngSkipHydration\": \"true\" }, listeners: { \"keydown.arrowDown.prevent\": \"onStep(-step())\", \"keydown.arrowUp.prevent\": \"onStep(step())\" }, properties: { \"class._with-buttons\": \"step()\" } }, ngImport: i0, template: \"<ng-container *tuiTextfieldContent>\\n    <section\\n        *ngIf=\\\"step()\\\"\\n        class=\\\"t-input-number-buttons\\\"\\n    >\\n        <button\\n            size=\\\"s\\\"\\n            tabindex=\\\"-1\\\"\\n            tuiIconButton\\n            type=\\\"button\\\"\\n            class=\\\"t-button\\\"\\n            [appearance]=\\\"appearance()\\\"\\n            [disabled]=\\\"!input.interactive() || value() >= input.max()\\\"\\n            [iconStart]=\\\"options.icons.increase\\\"\\n            (click.prevent)=\\\"onStep(step())\\\"\\n            (mousedown.prevent)=\\\"el.focus()\\\"\\n        >\\n            +\\n        </button>\\n\\n        <button\\n            size=\\\"s\\\"\\n            tabindex=\\\"-1\\\"\\n            tuiIconButton\\n            type=\\\"button\\\"\\n            class=\\\"t-button\\\"\\n            [appearance]=\\\"appearance()\\\"\\n            [disabled]=\\\"!input.interactive() || value() <= input.min()\\\"\\n            [iconStart]=\\\"options.icons.decrease\\\"\\n            (click.prevent)=\\\"onStep(-step())\\\"\\n            (mousedown.prevent)=\\\"el.focus()\\\"\\n        >\\n            -\\n        </button>\\n    </section>\\n</ng-container>\\n\", styles: [\".t-input-number-buttons.t-input-number-buttons{position:absolute;right:0;display:flex;block-size:var(--t-height);flex-direction:column;gap:.125rem;border-radius:inherit}tui-textfield[data-size=s] .t-input-number-buttons.t-input-number-buttons{flex-direction:row-reverse}.t-input-number-buttons.t-input-number-buttons>*{flex:1 1 0;border-radius:0}.t-input-number-buttons.t-input-number-buttons>*:first-child{border-top-right-radius:inherit}.t-input-number-buttons.t-input-number-buttons>*:last-child{border-bottom-right-radius:inherit}tui-textfield[data-size=l] .t-input-number-buttons.t-input-number-buttons>*{inline-size:var(--tui-height-m)}tui-textfield[data-size=s] .t-input-number-buttons.t-input-number-buttons>*:first-child{border-top-right-radius:inherit;border-bottom-right-radius:inherit}tui-textfield[data-size=s] .t-input-number-buttons.t-input-number-buttons>*:last-child{border-radius:0}[tuiInputNumber]._with-buttons{border-top-right-radius:0;border-bottom-right-radius:0}tui-textfield[data-size=l]{--t-input-number-offset-end: calc(var(--tui-height-m) + .125rem)}tui-textfield[data-size=m]{--t-input-number-offset-end: calc(var(--tui-height-s) + .125rem)}tui-textfield[data-size=s]{--t-input-number-offset-end: calc(2 * var(--tui-height-s) + .25rem)}[tuiInputNumber]._with-buttons,[tuiInputNumber]._with-buttons~.t-template{inline-size:calc(100% - var(--t-input-number-offset-end))}[tuiInputNumber]._with-buttons~.t-content{margin-inline-end:var(--t-input-number-offset-end)}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"directive\", type: TuiTextfieldContent, selector: \"ng-template[tuiTextfieldContent]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputNumberStep, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'input[tuiInputNumber][step]', imports: [NgIf, TuiButton, TuiTextfieldContent], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        ngSkipHydration: 'true',\n                        '(keydown.arrowDown.prevent)': 'onStep(-step())',\n                        '(keydown.arrowUp.prevent)': 'onStep(step())',\n                        '[class._with-buttons]': 'step()',\n                    }, template: \"<ng-container *tuiTextfieldContent>\\n    <section\\n        *ngIf=\\\"step()\\\"\\n        class=\\\"t-input-number-buttons\\\"\\n    >\\n        <button\\n            size=\\\"s\\\"\\n            tabindex=\\\"-1\\\"\\n            tuiIconButton\\n            type=\\\"button\\\"\\n            class=\\\"t-button\\\"\\n            [appearance]=\\\"appearance()\\\"\\n            [disabled]=\\\"!input.interactive() || value() >= input.max()\\\"\\n            [iconStart]=\\\"options.icons.increase\\\"\\n            (click.prevent)=\\\"onStep(step())\\\"\\n            (mousedown.prevent)=\\\"el.focus()\\\"\\n        >\\n            +\\n        </button>\\n\\n        <button\\n            size=\\\"s\\\"\\n            tabindex=\\\"-1\\\"\\n            tuiIconButton\\n            type=\\\"button\\\"\\n            class=\\\"t-button\\\"\\n            [appearance]=\\\"appearance()\\\"\\n            [disabled]=\\\"!input.interactive() || value() <= input.min()\\\"\\n            [iconStart]=\\\"options.icons.decrease\\\"\\n            (click.prevent)=\\\"onStep(-step())\\\"\\n            (mousedown.prevent)=\\\"el.focus()\\\"\\n        >\\n            -\\n        </button>\\n    </section>\\n</ng-container>\\n\", styles: [\".t-input-number-buttons.t-input-number-buttons{position:absolute;right:0;display:flex;block-size:var(--t-height);flex-direction:column;gap:.125rem;border-radius:inherit}tui-textfield[data-size=s] .t-input-number-buttons.t-input-number-buttons{flex-direction:row-reverse}.t-input-number-buttons.t-input-number-buttons>*{flex:1 1 0;border-radius:0}.t-input-number-buttons.t-input-number-buttons>*:first-child{border-top-right-radius:inherit}.t-input-number-buttons.t-input-number-buttons>*:last-child{border-bottom-right-radius:inherit}tui-textfield[data-size=l] .t-input-number-buttons.t-input-number-buttons>*{inline-size:var(--tui-height-m)}tui-textfield[data-size=s] .t-input-number-buttons.t-input-number-buttons>*:first-child{border-top-right-radius:inherit;border-bottom-right-radius:inherit}tui-textfield[data-size=s] .t-input-number-buttons.t-input-number-buttons>*:last-child{border-radius:0}[tuiInputNumber]._with-buttons{border-top-right-radius:0;border-bottom-right-radius:0}tui-textfield[data-size=l]{--t-input-number-offset-end: calc(var(--tui-height-m) + .125rem)}tui-textfield[data-size=m]{--t-input-number-offset-end: calc(var(--tui-height-s) + .125rem)}tui-textfield[data-size=s]{--t-input-number-offset-end: calc(2 * var(--tui-height-s) + .25rem)}[tuiInputNumber]._with-buttons,[tuiInputNumber]._with-buttons~.t-template{inline-size:calc(100% - var(--t-input-number-offset-end))}[tuiInputNumber]._with-buttons~.t-content{margin-inline-end:var(--t-input-number-offset-end)}\\n\"] }]\n        }], propDecorators: { stepSetter: [{\n                type: Input,\n                args: ['step']\n            }] } });\n\nconst TuiInputNumber = [TuiInputNumberDirective, TuiInputNumberStep];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_NUMBER_DEFAULT_OPTIONS, TUI_INPUT_NUMBER_OPTIONS, TuiInputNumber, TuiInputNumberDirective, TuiInputNumberStep, tuiInputNumberOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,QAAQ,eAAe;AAC7J,SAASC,QAAQ,EAAEC,kBAAkB,QAAQ,4BAA4B;AACzE,OAAO,KAAKC,EAAE,MAAM,kBAAkB;AACtC,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,+BAA+B,QAAQ,eAAe;AAC/D,SAASC,kBAAkB,EAAEC,6BAA6B,EAAEC,iBAAiB,QAAQ,cAAc;AACnG,SAASC,UAAU,EAAEC,YAAY,EAAEC,uBAAuB,QAAQ,uBAAuB;AACzF,SAASC,uBAAuB,EAAEC,UAAU,EAAEC,WAAW,QAAQ,yBAAyB;AAC1F,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,gBAAgB,EAAEC,QAAQ,QAAQ,0BAA0B;AACrE,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,qBAAqB,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,mBAAmB,QAAQ,qCAAqC;AACzI,SAASC,iBAAiB,EAAEC,yBAAyB,QAAQ,uBAAuB;AACpF,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,KAAK,QAAQ,MAAM;AAAC,MAAAC,GAAA;AAAA,SAAAC,qDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GA0JwEhD,EAAE,CAAAiD,gBAAA;IAAFjD,EAAE,CAAAkD,cAAA,gBAmEme,CAAC,eAAkb,CAAC;IAnEz5BlD,EAAE,CAAAmD,UAAA,2BAAAC,qFAAA;MAAFpD,EAAE,CAAAqD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFtD,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAAwD,WAAA,CAmE40BF,MAAA,CAAAG,MAAA,CAAOH,MAAA,CAAAI,IAAA,CAAK,CAAC,CAAC;IAAA,CAAC,CAAC,+BAAAC,yFAAA;MAnE91B3D,EAAE,CAAAqD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFtD,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAAwD,WAAA,CAmEg4BF,MAAA,CAAAM,EAAA,CAAAC,KAAA,CAAS,CAAC;IAAA,CAAC,CAAC;IAnE94B7D,EAAE,CAAA8D,MAAA,SAmE+6B,CAAC;IAnEl7B9D,EAAE,CAAA+D,YAAA,CAmEw7B,CAAC;IAnE37B/D,EAAE,CAAAkD,cAAA,eAmE82C,CAAC;IAnEj3ClD,EAAE,CAAAmD,UAAA,2BAAAa,qFAAA;MAAFhE,EAAE,CAAAqD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFtD,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAAwD,WAAA,CAmEmyCF,MAAA,CAAAG,MAAA,EAAQH,MAAA,CAAAI,IAAA,CAAK,CAAC,CAAC;IAAA,CAAC,CAAC,+BAAAO,yFAAA;MAnEtzCjE,EAAE,CAAAqD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFtD,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAAwD,WAAA,CAmEw1CF,MAAA,CAAAM,EAAA,CAAAC,KAAA,CAAS,CAAC;IAAA,CAAC,CAAC;IAnEt2C7D,EAAE,CAAA8D,MAAA,SAmEu4C,CAAC;IAnE14C9D,EAAE,CAAA+D,YAAA,CAmEg5C,CAAC,CAAe,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAQ,MAAA,GAnEn6CtD,EAAE,CAAAuD,aAAA;IAAFvD,EAAE,CAAAkE,SAAA,CAmE4qB,CAAC;IAnE/qBlE,EAAE,CAAAmE,UAAA,eAAAb,MAAA,CAAAc,UAAA,EAmE4qB,CAAC,cAAAd,MAAA,CAAAe,KAAA,CAAAC,WAAA,MAAAhB,MAAA,CAAAiB,KAAA,MAAAjB,MAAA,CAAAe,KAAA,CAAAG,GAAA,EAA0E,CAAC,cAAAlB,MAAA,CAAAmB,OAAA,CAAAC,KAAA,CAAAC,QAAmD,CAAC;IAnE9yB3E,EAAE,CAAAkE,SAAA,EAmEmoC,CAAC;IAnEtoClE,EAAE,CAAAmE,UAAA,eAAAb,MAAA,CAAAc,UAAA,EAmEmoC,CAAC,cAAAd,MAAA,CAAAe,KAAA,CAAAC,WAAA,MAAAhB,MAAA,CAAAiB,KAAA,MAAAjB,MAAA,CAAAe,KAAA,CAAAO,GAAA,EAA0E,CAAC,cAAAtB,MAAA,CAAAmB,OAAA,CAAAC,KAAA,CAAAG,QAAmD,CAAC;EAAA;AAAA;AAAA,SAAAC,2CAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnErwC9C,EAAE,CAAA+E,uBAAA,EAmE0Y,CAAC;IAnE7Y/E,EAAE,CAAAgF,UAAA,IAAAnC,oDAAA,oBAmEme,CAAC;IAnEte7C,EAAE,CAAAiF,qBAAA;EAAA;EAAA,IAAAnC,EAAA;IAAA,MAAAQ,MAAA,GAAFtD,EAAE,CAAAuD,aAAA;IAAFvD,EAAE,CAAAkE,SAAA,CAmEgb,CAAC;IAnEnblE,EAAE,CAAAmE,UAAA,SAAAb,MAAA,CAAAI,IAAA,EAmEgb,CAAC;EAAA;AAAA;AA3NxhB,MAAMwB,gCAAgC,GAAG;EACrCN,GAAG,EAAEO,MAAM,CAACC,gBAAgB;EAC5BZ,GAAG,EAAEW,MAAM,CAACE,gBAAgB;EAC5BC,MAAM,EAAE,EAAE;EACVC,OAAO,EAAE,EAAE;EACX7B,IAAI,EAAE,CAAC;EACPgB,KAAK,EAAE;IACHC,QAAQ,EAAE,WAAW;IACrBE,QAAQ,EAAE;EACd,CAAC;EACDW,gBAAgB,EAAE;AACtB,CAAC;AACD,MAAM,CAACC,wBAAwB,EAAEC,6BAA6B,CAAC,GAAGnD,gBAAgB,CAAC2C,gCAAgC,CAAC;AAEpH,MAAMS,kBAAkB,GAAG,EAAE;AAC7B,MAAMC,uBAAuB,SAASxE,UAAU,CAAC;EAC7CyE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,SAAS,GAAG9F,MAAM,CAAC8B,qBAAqB,CAAC;IAC9C,IAAI,CAACiE,KAAK,GAAG/F,MAAM,CAACyB,UAAU,CAAC;IAC/B,IAAI,CAACuE,YAAY,GAAGrF,QAAQ,CAACX,MAAM,CAACkC,iBAAiB,CAAC,EAAE;MACpD+D,YAAY,EAAE9D;IAClB,CAAC,CAAC;IACF,IAAI,CAAC+D,SAAS,GAAGjG,QAAQ,CAAC,MAAMe,kBAAkB,CAAC,IAAI,CAAC8E,SAAS,CAACxB,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC0B,YAAY,CAAC,CAAC,CAAC,CAAC;IAChG,IAAI,CAACG,SAAS,GAAGlG,QAAQ,CAAC,MAAMiF,MAAM,CAACkB,KAAK,CAAC,IAAI,CAACJ,YAAY,CAAC,CAAC,CAACG,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAACH,YAAY,CAAC,CAAC,CAACG,SAAS,CAAC;IAChH,IAAI,CAACE,UAAU,GAAGpG,QAAQ,CAAC,CAACqE,KAAK,GAAG,IAAI,CAAC4B,SAAS,CAAC,CAAC,KAAK5B,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGD,KAAK,GAAG,IAAI,CAACK,GAAG,CAAC,CAAC,CAAC;IAC7G,IAAI,CAAC2B,cAAc,GAAGpG,MAAM,CAAC,MAAM;MAC/B,MAAMoE,KAAK,GAAG,IAAI,CAAC4B,SAAS,CAAC,CAAC;MAC9B,IAAIhB,MAAM,CAACkB,KAAK,CAAC9B,KAAK,CAAC,EAAE;QACrB,IAAI,CAACiC,QAAQ,CAAC,IAAI,CAAC;QACnB;MACJ;MACA,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,IACjB/B,KAAK,GAAG,IAAI,CAACK,GAAG,CAAC,CAAC,IAClBL,KAAK,GAAG,IAAI,CAACC,GAAG,CAAC,CAAC,IAClB,IAAI,CAACD,KAAK,CAAC,CAAC,KAAKA,KAAK,EAAE;QACxB;MACJ;MACA,IAAI,CAACiC,QAAQ,CAACjC,KAAK,CAAC;IACxB,CAAC,EAAEhD,uBAAuB,CAAC;IAC3B,IAAI,CAACkD,OAAO,GAAGxE,MAAM,CAACwF,wBAAwB,CAAC;IAC/C,IAAI,CAACgB,OAAO,GAAG9E,gBAAgB,CAAC,CAAC;IACjC,IAAI,CAAC+E,SAAS,GAAGxG,QAAQ,CAAC,MAAM;MAC5B,IAAI,IAAI,CAAC8F,KAAK,EAAE;QACZ,OAAO,IAAI,CAACpB,GAAG,CAAC,CAAC,GAAG,CAAC,GACf,MAAM,CAAC;QAAA,EACP,SAAS;MACnB;MACA;AACZ;AACA;AACA;MACY,OAAO,SAAS;IACpB,CAAC,CAAC;IACF,IAAI,CAAC+B,gBAAgB,GAAGzG,QAAQ,CAAC,MAAM;MACnC,MAAM;QAAE0G,gBAAgB;QAAEC;MAAkB,CAAC,GAAG,IAAI,CAACZ,YAAY,CAAC,CAAC;MACnE,MAAMa,WAAW,GAAG,CAAC,CAAC,IAAI,CAACV,SAAS,CAAC,CAAC,IAAI,IAAI,CAACL,SAAS,CAACxB,KAAK,CAAC,CAAC,CAACwC,QAAQ,CAACH,gBAAgB,CAAC;MAC3F,MAAMR,SAAS,GAAGU,WAAW,GAAGE,IAAI,CAACpC,GAAG,CAAC,IAAI,CAACwB,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;MACtE,MAAMa,YAAY,GAAGJ,iBAAiB,CAACK,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM;MACvD,OAAOxB,kBAAkB,GAAGS,SAAS,GAAGa,YAAY;IACxD,CAAC,CAAC;IACF,IAAI,CAACG,IAAI,GAAG9E,UAAU,CAACpC,QAAQ,CAAC,CAAC;MAAEmH,WAAW;MAAE,GAAGpB;IAAa,CAAC,GAAG,IAAI,CAACA,YAAY,CAAC,CAAC,EAAEqB,qBAAqB,GAAG,IAAI,CAAClB,SAAS,CAAC,CAAC,KAAK,IAAI,CAACmB,WAAW,CAAC;MACnJ,GAAGtB,YAAY;MACfqB,qBAAqB;MACrB1C,GAAG,EAAE,IAAI,CAACA,GAAG,CAAC,CAAC;MACfJ,GAAG,EAAE,IAAI,CAACA,GAAG,CAAC,CAAC;MACfc,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;MACrBC,OAAO,EAAE,IAAI,CAACA,OAAO,CAAC,CAAC;MACvBiC,qBAAqB,EAAEH,WAAW,KAAK,QAAQ,GAAGC,qBAAqB,GAAG;IAC9E,CAAC,CAAC,CAAC,CAAC;IACJ,IAAI,CAAC1C,GAAG,GAAGxE,MAAM,CAAC,IAAI,CAACqE,OAAO,CAACG,GAAG,CAAC;IACnC,IAAI,CAACJ,GAAG,GAAGpE,MAAM,CAAC,IAAI,CAACqE,OAAO,CAACD,GAAG,CAAC;IACnC,IAAI,CAACc,MAAM,GAAGlF,MAAM,CAAC,IAAI,CAACqE,OAAO,CAACa,MAAM,CAAC;IACzC,IAAI,CAACC,OAAO,GAAGnF,MAAM,CAAC,IAAI,CAACqE,OAAO,CAACc,OAAO,CAAC;EAC/C;EACA,IAAIkC,SAASA,CAACC,CAAC,EAAE;IACb,IAAI,CAACC,kBAAkB,CAACD,CAAC,EAAE,IAAI,CAAClD,GAAG,CAAC,CAAC,CAAC;EAC1C;EACA,IAAIoD,SAASA,CAACF,CAAC,EAAE;IACb,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAAC/C,GAAG,CAAC,CAAC,EAAE8C,CAAC,CAAC;EAC1C;EACA;EACA,IAAIG,YAAYA,CAACH,CAAC,EAAE;IAChB,IAAI,CAACpC,MAAM,CAACwC,GAAG,CAACJ,CAAC,CAAC;EACtB;EACA;EACA,IAAIK,aAAaA,CAACL,CAAC,EAAE;IACjB,IAAI,CAACnC,OAAO,CAACuC,GAAG,CAACJ,CAAC,CAAC;EACvB;EACAM,UAAUA,CAACzD,KAAK,EAAE;IACd,KAAK,CAACyD,UAAU,CAACzD,KAAK,CAAC;IACvB,IAAI,CAAC0D,QAAQ,CAAC,IAAI,CAAC1D,KAAK,CAAC,CAAC,CAAC;EAC/B;EACA0D,QAAQA,CAAC1D,KAAK,EAAE;IACZ,IAAI,CAACwB,SAAS,CAACxB,KAAK,CAACuD,GAAG,CAAC,IAAI,CAACI,YAAY,CAAC3D,KAAK,CAAC,CAAC;EACtD;EACA4D,MAAMA,CAAA,EAAG;IACL,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB,IAAI,CAAC,IAAI,CAAC9B,UAAU,CAAC,CAAC,EAAE;MACpB,IAAI,CAAC2B,QAAQ,CAAC,IAAI,CAAC1D,KAAK,CAAC,CAAC,CAAC;IAC/B;EACJ;EACA8D,OAAOA,CAAA,EAAG;IACN,IAAIlD,MAAM,CAACkB,KAAK,CAAC,IAAI,CAACF,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACmC,QAAQ,CAAC,CAAC,EAAE;MACpD,IAAI,CAACvC,SAAS,CAACxB,KAAK,CAACuD,GAAG,CAAC,IAAI,CAACxC,MAAM,CAAC,CAAC,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;IAC5D;EACJ;EACA2C,YAAYA,CAAC3D,KAAK,EAAE;IAChB,IAAIA,KAAK,KAAK,IAAI,IAAIY,MAAM,CAACkB,KAAK,CAAC9B,KAAK,CAAC,EAAE;MACvC,OAAO,EAAE;IACb;IACA,OAAQ,CAAC,IAAI,CAACe,MAAM,CAAC,CAAC,KAAK9D,UAAU,GAAG,IAAI,CAAC8D,MAAM,CAAC,CAAC,GAAG,EAAE,IACtDjD,eAAe,CAACkC,KAAK,EAAE;MACnB,GAAG,IAAI,CAAC0B,YAAY,CAAC,CAAC;MACtB;AAChB;AACA;AACA;AACA;AACA;MACgBG,SAAS,EAAExE,gBAAgB,CAAC2C,KAAK,EAAE,IAAI,CAAC6B,SAAS,CAAC,CAAC,CAAC,GAC9C,IAAI,CAACA,SAAS,CAAC,CAAC,GAChBmC;IACV,CAAC,CAAC,CAACC,OAAO,CAAC/G,WAAW,EAAED,UAAU,CAAC,GACnC,IAAI,CAAC+D,OAAO,CAAC,CAAC;EACtB;EACAoC,kBAAkBA,CAACc,WAAW,EAAEC,WAAW,EAAE;IACzC,MAAM9D,GAAG,GAAG,IAAI,CAAC+D,WAAW,CAACC,gBAAgB,CAACH,WAAW,CAAC,IAAI,IAAI,CAAChE,OAAO,CAACG,GAAG;IAC9E,MAAMJ,GAAG,GAAG,IAAI,CAACmE,WAAW,CAACC,gBAAgB,CAACF,WAAW,CAAC,IAAI,IAAI,CAACjE,OAAO,CAACD,GAAG;IAC9E,IAAI,CAACI,GAAG,CAACkD,GAAG,CAACd,IAAI,CAACpC,GAAG,CAACA,GAAG,EAAEJ,GAAG,CAAC,CAAC;IAChC,IAAI,CAACA,GAAG,CAACsD,GAAG,CAACd,IAAI,CAACxC,GAAG,CAACI,GAAG,EAAEJ,GAAG,CAAC,CAAC;EACpC;EACA+C,WAAWA,CAACsB,MAAM,EAAE;IAChB,MAAM;MAAEvD,MAAM,GAAG,EAAE;MAAEC,OAAO,GAAG;IAAG,CAAC,GAAGsD,MAAM;IAC5C,MAAM;MAAEC,OAAO;MAAE,GAAGrE;IAAQ,CAAC,GAAGvD,6BAA6B,CAAC2H,MAAM,CAAC;IACrE,MAAME,wBAAwB,GAAG/H,+BAA+B,CAACE,6BAA6B,CAAC;MAC3F,GAAG2H,MAAM;MACTjE,GAAG,EAAEO,MAAM,CAACC,gBAAgB;MAC5BZ,GAAG,EAAEW,MAAM,CAACE;IAChB,CAAC,CAAC,CAAC;IACH,OAAO;MACH,GAAGZ,OAAO;MACVqE,OAAO,EAAE,CACL,GAAGA,OAAO,EACVC,wBAAwB,EACxB5H,iBAAiB,CAAEoD,KAAK,IAAK,CACzBe,MAAM,CAAC6B,MAAM,EACb5C,KAAK,CAAC4C,MAAM,GAAG5B,OAAO,CAAC4B,MAAM,CAChC,CAAC;IAEV,CAAC;EACL;EACA;IAAS,IAAI,CAAC6B,IAAI;MAAA,IAAAC,oCAAA;MAAA,gBAAAC,gCAAAC,CAAA;QAAA,QAAAF,oCAAA,KAAAA,oCAAA,GAA+EjJ,EAAE,CAAAoJ,qBAAA,CAAQxD,uBAAuB,IAAAuD,CAAA,IAAvBvD,uBAAuB;MAAA;IAAA,IAAqD;EAAE;EACzL;IAAS,IAAI,CAACyD,IAAI,kBAD+ErJ,EAAE,CAAAsJ,iBAAA;MAAAC,IAAA,EACJ3D,uBAAuB;MAAA4D,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,qCAAA7G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADrB9C,EAAE,CAAAmD,UAAA,kBAAAyG,gDAAA;YAAA,OACJ7G,GAAA,CAAAoF,MAAA,CAAO,CAAC;UAAA,CAAc,CAAC,mBAAA0B,iDAAA;YAAA,OAAvB9G,GAAA,CAAAsF,OAAA,CAAQ,CAAC;UAAA,CAAa,CAAC;QAAA;QAAA,IAAAvF,EAAA;UADrB9C,EAAE,CAAA8J,cAAA,aACJ/G,GAAA,CAAAgH,QAAA,CAAS,CAAa,CAAC;UADrB/J,EAAE,CAAAgK,WAAA,cACJjH,GAAA,CAAA2D,SAAA,CAAU,CAAC,eAAA3D,GAAA,CAAA0D,OAAA,CAAAwD,SAAA,GAAS,CAAC,GAAAlH,GAAA,CAAA0D,OAAA,CAAAwD,SAAA,GAAuBlH,GAAA,CAAA4D,gBAAA,CAAiB,CAAC;QAAA;MAAA;MAAAuD,MAAA;QAAAzC,SAAA,GAD5DzH,EAAE,CAAAmK,YAAA,CAAAC,IAAA;QAAAxC,SAAA,GAAF5H,EAAE,CAAAmK,YAAA,CAAAC,IAAA;QAAAvC,YAAA,GAAF7H,EAAE,CAAAmK,YAAA,CAAAC,IAAA;QAAArC,aAAA,GAAF/H,EAAE,CAAAmK,YAAA,CAAAC,IAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFtK,EAAE,CAAAuK,kBAAA,CAC8d,CACzjBlJ,YAAY,CAACuE,uBAAuB,CAAC,EACrCtE,uBAAuB,CAACmE,wBAAwB,CAAC,CACpD,GAJ4FzF,EAAE,CAAAwK,uBAAA,EAIvC1I,EAAE,CAACE,gBAAgB,EAAiBlB,EAAE,CAACC,gBAAgB,IAJlBf,EAAE,CAAAyK,0BAAA;IAAA,EAIoC;EAAE;AAC7I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KANqG1K,EAAE,CAAA2K,iBAAA,CAMX/E,uBAAuB,EAAc,CAAC;IACtH2D,IAAI,EAAElJ,SAAS;IACfuK,IAAI,EAAE,CAAC;MACCP,UAAU,EAAE,IAAI;MAChBQ,QAAQ,EAAE,uBAAuB;MACjCC,SAAS,EAAE,CACPzJ,YAAY,CAACuE,uBAAuB,CAAC,EACrCtE,uBAAuB,CAACmE,wBAAwB,CAAC,CACpD;MACDsF,cAAc,EAAE,CAAC/I,gBAAgB,EAAEjB,gBAAgB,CAAC;MACpDiK,IAAI,EAAE;QACF,YAAY,EAAE,YAAY;QAC1B,kBAAkB,EAAE,aAAa;QACjC,kBAAkB,EAAE,gEAAgE;QACpF,QAAQ,EAAE,UAAU;QACpB,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEvD,SAAS,EAAE,CAAC;MAC1B8B,IAAI,EAAEjJ,KAAK;MACXsK,IAAI,EAAE,CAAC,KAAK;IAChB,CAAC,CAAC;IAAEhD,SAAS,EAAE,CAAC;MACZ2B,IAAI,EAAEjJ,KAAK;MACXsK,IAAI,EAAE,CAAC,KAAK;IAChB,CAAC,CAAC;IAAE/C,YAAY,EAAE,CAAC;MACf0B,IAAI,EAAEjJ,KAAK;MACXsK,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAE7C,aAAa,EAAE,CAAC;MAChBwB,IAAI,EAAEjJ,KAAK;MACXsK,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMK,kBAAkB,CAAC;EACrBpF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqF,UAAU,GAAGjL,MAAM,CAACM,UAAU,CAAC;IACpC,IAAI,CAAC4K,IAAI,GAAGlL,MAAM,CAACO,MAAM,CAAC;IAC1B,IAAI,CAACoD,EAAE,GAAGjC,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACyC,UAAU,GAAGnE,MAAM,CAACgC,qBAAqB,CAAC,CAACmC,UAAU;IAC1D,IAAI,CAACK,OAAO,GAAGxE,MAAM,CAACwF,wBAAwB,CAAC;IAC/C,IAAI,CAACpB,KAAK,GAAGpE,MAAM,CAAC2F,uBAAuB,EAAE;MAAEwF,IAAI,EAAE;IAAK,CAAC,CAAC;IAC5D,IAAI,CAAC1H,IAAI,GAAGtD,MAAM,CAAC,IAAI,CAACqE,OAAO,CAACf,IAAI,CAAC;IACrC,IAAI,CAACa,KAAK,GAAGrE,QAAQ,CAAC,MAAM,IAAI,CAACmE,KAAK,CAACE,KAAK,CAAC,CAAC,IAAI8G,GAAG,CAAC;EAC1D;EACA;EACA,IAAIC,UAAUA,CAAC5D,CAAC,EAAE;IACd,IAAI,CAAChE,IAAI,CAACoE,GAAG,CAACJ,CAAC,CAAC;EACpB;EACAjE,MAAMA,CAACC,IAAI,EAAE;IACT,MAAM6H,OAAO,GAAGpG,MAAM,CAACkB,KAAK,CAAC,IAAI,CAAC9B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;IAC7D,MAAMA,KAAK,GAAG1C,QAAQ,CAAC0J,OAAO,GAAG7H,IAAI,EAAE,IAAI,CAACW,KAAK,CAACO,GAAG,CAAC,CAAC,EAAE,IAAI,CAACP,KAAK,CAACG,GAAG,CAAC,CAAC,CAAC;IAC1E,IAAIW,MAAM,CAACkB,KAAK,CAAC,IAAI,CAAC9B,KAAK,CAAC,CAAC,CAAC,EAAE;MAC5B5B,KAAK,CAAC,CAAC,CAAC,CACH6I,IAAI,CAAC/I,WAAW,CAAC,IAAI,CAAC0I,IAAI,CAAC,EAAEtK,kBAAkB,CAAC,IAAI,CAACqK,UAAU,CAAC,CAAC,CACjEO,SAAS,CAAC,MAAM;QACjB,MAAMC,UAAU,GAAG,IAAI,CAAC9H,EAAE,CAACW,KAAK,CAAC4C,MAAM,GAAG,IAAI,CAAC9C,KAAK,CAACkB,OAAO,CAAC,CAAC,CAAC4B,MAAM;QACrE,IAAI,CAACvD,EAAE,CAAC+H,iBAAiB,CAACD,UAAU,EAAEA,UAAU,CAAC;MACrD,CAAC,CAAC;IACN;IACA,IAAI,CAACrH,KAAK,CAAC4D,QAAQ,CAAC1D,KAAK,CAAC;EAC9B;EACA;IAAS,IAAI,CAACyE,IAAI,YAAA4C,2BAAAzC,CAAA;MAAA,YAAAA,CAAA,IAAyF8B,kBAAkB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAACY,IAAI,kBAnE+E7L,EAAE,CAAA8L,iBAAA;MAAAvC,IAAA,EAmEJ0B,kBAAkB;MAAAzB,SAAA;MAAAuC,SAAA,sBAAwJ,MAAM;MAAAtC,QAAA;MAAAC,YAAA,WAAAsC,gCAAAlJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnE9K9C,EAAE,CAAAmD,UAAA,uCAAA8I,gEAAA;YAAA,OAmEJlJ,GAAA,CAAAU,MAAA,EAAQV,GAAA,CAAAW,IAAA,CAAK,CAAC,CAAC;UAAA,CAAE,CAAC,qCAAAwI,8DAAA;YAAA,OAAlBnJ,GAAA,CAAAU,MAAA,CAAOV,GAAA,CAAAW,IAAA,CAAK,CAAC,CAAC;UAAA,CAAG,CAAC;QAAA;QAAA,IAAAZ,EAAA;UAnEhB9C,EAAE,CAAAmM,WAAA,kBAmEJpJ,GAAA,CAAAW,IAAA,CAAK,CAAY,CAAC;QAAA;MAAA;MAAAwG,MAAA;QAAAoB,UAAA,GAnEhBtL,EAAE,CAAAmK,YAAA,CAAAC,IAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFtK,EAAE,CAAAoM,mBAAA;MAAAC,KAAA,EAAAzJ,GAAA;MAAA0J,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAA5J,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAgF,UAAA,IAAAF,0CAAA,yBAmE0Y,CAAC;QAAA;MAAA;MAAA6H,YAAA,GAAwjFnK,IAAI,EAA6FE,SAAS,EAAoIR,mBAAmB;MAAA0K,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAkJ;EAAE;AAC/7G;AACA;EAAA,QAAApC,SAAA,oBAAAA,SAAA,KArEqG1K,EAAE,CAAA2K,iBAAA,CAqEXM,kBAAkB,EAAc,CAAC;IACjH1B,IAAI,EAAE9I,SAAS;IACfmK,IAAI,EAAE,CAAC;MAAEP,UAAU,EAAE,IAAI;MAAEQ,QAAQ,EAAE,6BAA6B;MAAEkC,OAAO,EAAE,CAACvK,IAAI,EAAEE,SAAS,EAAER,mBAAmB,CAAC;MAAE2K,aAAa,EAAEnM,iBAAiB,CAAC0J,IAAI;MAAE0C,eAAe,EAAEnM,uBAAuB,CAACqM,MAAM;MAAEhC,IAAI,EAAE;QACvMiC,eAAe,EAAE,MAAM;QACvB,6BAA6B,EAAE,iBAAiB;QAChD,2BAA2B,EAAE,gBAAgB;QAC7C,uBAAuB,EAAE;MAC7B,CAAC;MAAER,QAAQ,EAAE,8kCAA8kC;MAAEG,MAAM,EAAE,CAAC,s9CAAs9C;IAAE,CAAC;EAC3kF,CAAC,CAAC,QAAkB;IAAEtB,UAAU,EAAE,CAAC;MAC3B/B,IAAI,EAAEjJ,KAAK;MACXsK,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsC,cAAc,GAAG,CAACtH,uBAAuB,EAAEqF,kBAAkB,CAAC;;AAEpE;AACA;AACA;;AAEA,SAAS/F,gCAAgC,EAAEO,wBAAwB,EAAEyH,cAAc,EAAEtH,uBAAuB,EAAEqF,kBAAkB,EAAEvF,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}