{"ast": null, "code": "import { tuiRound } from '@taiga-ui/cdk/utils/math';\nimport { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { inject, signal, computed, Component, ChangeDetectionStrategy, Input, INJECTOR, Directive, forwardRef, ContentChild } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { NgControl, NgModel } from '@angular/forms';\nimport { TuiControl } from '@taiga-ui/cdk/classes';\nimport { tuiWatch, tuiControlValue, tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tuiFallbackValueProvider } from '@taiga-ui/cdk/tokens';\nimport { tuiCreateToken, tuiProvideOptions, tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { take, timer, switchMap, merge, tap, map, combineLatest, filter } from 'rxjs';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiAsAuxiliary } from '@taiga-ui/core/tokens';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { DOCUMENT, AsyncPipe, NgIf } from '@angular/common';\nimport { TUI_TRUE_HANDLER, TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\n\n/**\n * Used as a limit for eliminating JS issues with floating point math\n */\nconst _c0 = [\"type\", \"range\", \"tuiSlider\", \"\"];\nconst _c1 = [\"tuiSliderThumbLabel\", \"\"];\nconst _c2 = [\"*\", [[\"input\", \"type\", \"range\"]]];\nconst _c3 = [\"*\", \"input[type=range]\"];\nfunction TuiSliderThumbLabel_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst TUI_FLOATING_PRECISION = 7;\nfunction tuiFindKeyStepsBoundariesByFn(keySteps, fn) {\n  const keyStepUpperIndex = keySteps.findIndex((keyStep, i) => i && fn(keyStep));\n  const lowerStep = keySteps[keyStepUpperIndex - 1] || keySteps[0];\n  const upperStep = keySteps[keyStepUpperIndex] || keySteps[keySteps.length - 1] || [0, 0];\n  return [lowerStep, upperStep];\n}\nfunction tuiPercentageToKeyStepValue(valuePercentage, keySteps) {\n  const [[lowerStepPercent, lowerStepValue], [upperStepPercent, upperStepValue]] = tuiFindKeyStepsBoundariesByFn(keySteps, ([keyStepPercentage, _]) => valuePercentage <= keyStepPercentage);\n  const ratio = (valuePercentage - lowerStepPercent) / (upperStepPercent - lowerStepPercent);\n  const controlValue = (upperStepValue - lowerStepValue) * ratio + lowerStepValue;\n  return tuiRound(controlValue, TUI_FLOATING_PRECISION);\n}\nfunction tuiKeyStepValueToPercentage(value, keySteps) {\n  const [[lowerStepPercent, lowerStepValue], [upperStepPercent, upperStepValue]] = tuiFindKeyStepsBoundariesByFn(keySteps, ([_, keyStepValue]) => value <= keyStepValue);\n  const ratio = (value - lowerStepValue) / (upperStepValue - lowerStepValue) || 0;\n  return (upperStepPercent - lowerStepPercent) * ratio + lowerStepPercent;\n}\nfunction tuiCreateKeyStepsTransformer(keySteps, slider) {\n  return new class {\n    fromControlValue(controlValue) {\n      const newValuePercentage = tuiKeyStepValueToPercentage(controlValue, keySteps);\n      return newValuePercentage * (slider.max - slider.min) / 100 + slider.min;\n    }\n    toControlValue(nativeValue) {\n      const valueRatio = (nativeValue - slider.min) / (slider.max - slider.min) || 0;\n      return tuiPercentageToKeyStepValue(valueRatio * 100, keySteps);\n    }\n  }();\n}\nconst TUI_SLIDER_DEFAULT_OPTIONS = {\n  size: 'm',\n  trackColor: 'var(--tui-background-neutral-2)'\n};\n/**\n * Default parameters for Slider component\n */\nconst TUI_SLIDER_OPTIONS = tuiCreateToken(TUI_SLIDER_DEFAULT_OPTIONS);\nfunction tuiSliderOptionsProvider(options) {\n  return tuiProvideOptions(TUI_SLIDER_OPTIONS, options, TUI_SLIDER_DEFAULT_OPTIONS);\n}\nclass TuiSliderComponent {\n  constructor() {\n    this.control = inject(NgControl, {\n      self: true,\n      optional: true\n    });\n    this.options = inject(TUI_SLIDER_OPTIONS);\n    this.segments = signal([1]);\n    this.ticksGradient = computed((segments = this.segments()) => this.getTicksGradient(segments));\n    this.size = this.options.size;\n    this.el = tuiInjectElement();\n    this.keySteps = inject(TuiSliderKeyStepsBase, {\n      self: true,\n      optional: true\n    });\n    if (this.control instanceof NgModel) {\n      /**\n       * The ValueAccessor.writeValue method is called twice on any value accessor during component initialization,\n       * when a control is bound using [(ngModel)], first time with a phantom null value.\n       * With `changeDetection: ChangeDetectionStrategy.OnPush` the second call of writeValue with real value don't re-render the view.\n       * ___\n       * See this {@link https://github.com/angular/angular/issues/14988 issue}\n       */\n      this.control.valueChanges?.pipe(tuiWatch(), take(1)).subscribe();\n    }\n  }\n  // TODO(v5): use signal inputs\n  set segmentsSetter(segments) {\n    this.segments.set(segments);\n  }\n  get valueRatio() {\n    return (this.value - this.min) / (this.max - this.min) || 0;\n  }\n  get min() {\n    return Number(this.el.min);\n  }\n  set min(x) {\n    this.el.min = String(x);\n  }\n  get max() {\n    return Number(this.el.max || 100);\n  }\n  set max(x) {\n    this.el.max = String(x);\n  }\n  get step() {\n    if (!this.el.step) {\n      return 1;\n    }\n    return this.el.step === 'any' ? 0 : Number(this.el.step);\n  }\n  set step(x) {\n    this.el.step = String(x);\n  }\n  get value() {\n    /**\n     * If developer uses `[(ngModel)]` and programmatically change value,\n     * the `el.nativeElement.value` is equal to the previous value at this moment\n     * (it will be updated only in next microtask).\n     * @see https://github.com/angular/angular/issues/13568\n     */\n    if (this.control instanceof NgModel) {\n      const transformer = this.keySteps?.transformer();\n      const value = transformer ? transformer.fromControlValue(this.control.value) : this.control.viewModel;\n      return this.step ? tuiRound(Math.round(value / this.step) * this.step, TUI_FLOATING_PRECISION) : value;\n    }\n    return Number(this.el.value) || 0;\n  }\n  set value(newValue) {\n    this.el.value = `${newValue}`;\n  }\n  getTicksGradient(segments) {\n    if (segments.length <= 1) {\n      return 'linear-gradient(to right, transparent 0 100%)';\n    }\n    const percentages = segments.filter(segment => segment > 0 && segment < 1).map(segment => segment * 100);\n    return percentages.reduce((acc, segment, index) => `${acc}\n                var(--tui-text-tertiary) ${segment}% calc(${segment}% + var(--t-tick-thickness)),\n                transparent ${segment}% ${percentages[index + 1] ?? 100}%${percentages[index + 1] ? ',' : ')'}\n                `, `linear-gradient(to right, transparent 0 ${percentages[0]}%,`);\n  }\n  static {\n    this.ɵfac = function TuiSliderComponent_Factory(t) {\n      return new (t || TuiSliderComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiSliderComponent,\n      selectors: [[\"input\", \"type\", \"range\", \"tuiSlider\", \"\"]],\n      hostVars: 7,\n      hostBindings: function TuiSliderComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function TuiSliderComponent_input_HostBindingHandler() {\n            return 0;\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n          i0.ɵɵstyleProp(\"--tui-slider-track-color\", ctx.options.trackColor)(\"--tui-ticks-gradient\", ctx.ticksGradient())(\"--tui-slider-fill-ratio\", ctx.valueRatio);\n        }\n      },\n      inputs: {\n        size: \"size\",\n        segmentsSetter: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"segments\", \"segmentsSetter\", x => Array.isArray(x) ? x : new Array(x).fill(null).map((_, i) => i / x)]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsAuxiliary(TuiSliderComponent)]), i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 0,\n      vars: 0,\n      template: function TuiSliderComponent_Template(rf, ctx) {},\n      styles: [\"[_nghost-%COMP%]{--t-tick-thickness: .25rem;position:relative;display:block;inline-size:100%;color:var(--tui-background-accent-1);cursor:pointer;-webkit-appearance:none;appearance:none;block-size:.125rem;padding:.4375rem 0;background-color:transparent;background-clip:content-box;outline:none;border-radius:var(--tui-radius-m)}[_nghost-%COMP%]:active{cursor:ew-resize}[_nghost-%COMP%]:disabled{opacity:var(--tui-disabled-opacity);cursor:auto}[data-size=s][_nghost-%COMP%]:not(:disabled):before{transition-property:transform,opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:.25rem;left:calc(var(--tui-slider-fill-ratio) * 100% - var(--tui-slider-fill-ratio) * .5rem);inline-size:.5rem;block-size:.5rem;border-radius:50%;transform:var(--tui-slider-thumb-transform, scale(1));content:\\\"\\\";cursor:ew-resize;background:currentColor;opacity:0}[data-size=s][_nghost-%COMP%]:active:before{opacity:.2;transform:var(--tui-slider-thumb-transform, scale(1)) scale(2.33)}[data-size=m][_nghost-%COMP%]:not(:disabled):before{transition-property:transform,opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:.125rem;left:calc(var(--tui-slider-fill-ratio) * 100% - var(--tui-slider-fill-ratio) * .75rem);inline-size:.75rem;block-size:.75rem;border-radius:50%;transform:var(--tui-slider-thumb-transform, scale(1));content:\\\"\\\";cursor:ew-resize;background:currentColor;opacity:0}[data-size=m][_nghost-%COMP%]:active:before{opacity:.2;transform:var(--tui-slider-thumb-transform, scale(1)) scale(2.33)}[_nghost-%COMP%]::-webkit-slider-container{border-radius:inherit}[data-size=m][_nghost-%COMP%]::-webkit-slider-runnable-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient),linear-gradient(to right,currentColor calc(100% * var(--tui-slider-fill-ratio)),transparent calc(100% * var(--tui-slider-fill-ratio)));background-position-x:calc((.75rem - var(--t-tick-thickness)) / 2),0;background-size:calc(100% - .75rem),auto}[data-size=s][_nghost-%COMP%]::-webkit-slider-runnable-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient),linear-gradient(to right,currentColor calc(100% * var(--tui-slider-fill-ratio)),transparent calc(100% * var(--tui-slider-fill-ratio)));background-position-x:calc((.5rem - var(--t-tick-thickness)) / 2),0;background-size:calc(100% - .5rem),auto}[data-size=m][_nghost-%COMP%]::-moz-range-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient);background-position-x:calc((.75rem - var(--t-tick-thickness)) / 2);background-size:calc(100% - .75rem)}[data-size=s][_nghost-%COMP%]::-moz-range-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient);background-position-x:calc((.5rem - var(--t-tick-thickness)) / 2);background-size:calc(100% - .5rem)}[data-size=m][_nghost-%COMP%]::-webkit-slider-thumb{-webkit-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.75rem;inline-size:.75rem;box-sizing:content-box;background-clip:content-box;border:.125rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1));margin-top:-.4375rem}:not(:disabled)[data-size=m][_nghost-%COMP%]::-webkit-slider-thumb{cursor:ew-resize}:not(:disabled)[data-size=m][_nghost-%COMP%]::-webkit-slider-thumb:hover, :active:not(:disabled)[data-size=m][_nghost-%COMP%]::-webkit-slider-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.333)}:focus-visible[data-size=m][_nghost-%COMP%]::-webkit-slider-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}[data-size=s][_nghost-%COMP%]::-webkit-slider-thumb{-webkit-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.5rem;inline-size:.5rem;box-sizing:content-box;background-clip:content-box;border:.25rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1));margin-top:-.4375rem}:not(:disabled)[data-size=s][_nghost-%COMP%]::-webkit-slider-thumb{cursor:ew-resize}:not(:disabled)[data-size=s][_nghost-%COMP%]::-webkit-slider-thumb:hover, :active:not(:disabled)[data-size=s][_nghost-%COMP%]::-webkit-slider-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.5)}:focus-visible[data-size=s][_nghost-%COMP%]::-webkit-slider-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}[data-size=m][_nghost-%COMP%]::-moz-range-thumb{-moz-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.75rem;inline-size:.75rem;box-sizing:content-box;background-clip:content-box;border:.125rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1))}:not(:disabled)[data-size=m][_nghost-%COMP%]::-moz-range-thumb{cursor:ew-resize}:not(:disabled)[data-size=m][_nghost-%COMP%]::-moz-range-thumb:hover, :active:not(:disabled)[data-size=m][_nghost-%COMP%]::-moz-range-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.333)}:focus-visible[data-size=m][_nghost-%COMP%]::-moz-range-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}[data-size=s][_nghost-%COMP%]::-moz-range-thumb{-moz-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.5rem;inline-size:.5rem;box-sizing:content-box;background-clip:content-box;border:.25rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1))}:not(:disabled)[data-size=s][_nghost-%COMP%]::-moz-range-thumb{cursor:ew-resize}:not(:disabled)[data-size=s][_nghost-%COMP%]::-moz-range-thumb:hover, :active:not(:disabled)[data-size=s][_nghost-%COMP%]::-moz-range-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.5)}:focus-visible[data-size=s][_nghost-%COMP%]::-moz-range-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}[_nghost-%COMP%]::-moz-range-progress{border-radius:inherit}[_nghost-%COMP%]::-moz-range-progress{block-size:.125rem;background:currentColor;border-top-right-radius:0;border-bottom-right-radius:0}tui-textfield   [type=\\\"range\\\"][_nghost-%COMP%]{--tui-radius: var(--tui-radius-m);position:absolute;top:100%;left:calc(var(--tui-radius) / 2);right:0;inline-size:calc(100% - calc(var(--tui-radius) / 2));box-sizing:border-box;block-size:1rem;margin:-.5625rem 0 0;padding:0;border-top-left-radius:0;border-bottom-left-radius:calc(var(--tui-radius) * 10) calc(var(--tui-radius) * 2);pointer-events:auto}tui-textfield[data-size=\\\"l\\\"]   [type=\\\"range\\\"][_nghost-%COMP%]{--tui-radius: var(--tui-radius-l)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSliderComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'input[type=range][tuiSlider]',\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAsAuxiliary(TuiSliderComponent)],\n      host: {\n        /**\n         * For change detection.\n         * Webkit does not have built-in method for customization of filling progress (as Firefox).\n         * We draw filling of progress by `background: linear-gradient(...)` of the track.\n         * This function triggers change detection (for {@link valueRatio} getter) when we drag thumb of the input.\n         */\n        '(input)': '0',\n        '[style.--tui-slider-track-color]': 'options.trackColor',\n        '[style.--tui-ticks-gradient]': 'ticksGradient()',\n        '[style.--tui-slider-fill-ratio]': 'valueRatio',\n        '[attr.data-size]': 'size'\n      },\n      styles: [\":host{--t-tick-thickness: .25rem;position:relative;display:block;inline-size:100%;color:var(--tui-background-accent-1);cursor:pointer;-webkit-appearance:none;appearance:none;block-size:.125rem;padding:.4375rem 0;background-color:transparent;background-clip:content-box;outline:none;border-radius:var(--tui-radius-m)}:host:active{cursor:ew-resize}:host:disabled{opacity:var(--tui-disabled-opacity);cursor:auto}:host[data-size=s]:not(:disabled):before{transition-property:transform,opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:.25rem;left:calc(var(--tui-slider-fill-ratio) * 100% - var(--tui-slider-fill-ratio) * .5rem);inline-size:.5rem;block-size:.5rem;border-radius:50%;transform:var(--tui-slider-thumb-transform, scale(1));content:\\\"\\\";cursor:ew-resize;background:currentColor;opacity:0}:host[data-size=s]:active:before{opacity:.2;transform:var(--tui-slider-thumb-transform, scale(1)) scale(2.33)}:host[data-size=m]:not(:disabled):before{transition-property:transform,opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:.125rem;left:calc(var(--tui-slider-fill-ratio) * 100% - var(--tui-slider-fill-ratio) * .75rem);inline-size:.75rem;block-size:.75rem;border-radius:50%;transform:var(--tui-slider-thumb-transform, scale(1));content:\\\"\\\";cursor:ew-resize;background:currentColor;opacity:0}:host[data-size=m]:active:before{opacity:.2;transform:var(--tui-slider-thumb-transform, scale(1)) scale(2.33)}:host::-webkit-slider-container{border-radius:inherit}:host[data-size=m]::-webkit-slider-runnable-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient),linear-gradient(to right,currentColor calc(100% * var(--tui-slider-fill-ratio)),transparent calc(100% * var(--tui-slider-fill-ratio)));background-position-x:calc((.75rem - var(--t-tick-thickness)) / 2),0;background-size:calc(100% - .75rem),auto}:host[data-size=s]::-webkit-slider-runnable-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient),linear-gradient(to right,currentColor calc(100% * var(--tui-slider-fill-ratio)),transparent calc(100% * var(--tui-slider-fill-ratio)));background-position-x:calc((.5rem - var(--t-tick-thickness)) / 2),0;background-size:calc(100% - .5rem),auto}:host[data-size=m]::-moz-range-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient);background-position-x:calc((.75rem - var(--t-tick-thickness)) / 2);background-size:calc(100% - .75rem)}:host[data-size=s]::-moz-range-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient);background-position-x:calc((.5rem - var(--t-tick-thickness)) / 2);background-size:calc(100% - .5rem)}:host[data-size=m]::-webkit-slider-thumb{-webkit-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.75rem;inline-size:.75rem;box-sizing:content-box;background-clip:content-box;border:.125rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1));margin-top:-.4375rem}:not(:disabled):host[data-size=m]::-webkit-slider-thumb{cursor:ew-resize}:not(:disabled):host[data-size=m]::-webkit-slider-thumb:hover,:active:not(:disabled):host[data-size=m]::-webkit-slider-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.333)}:focus-visible:host[data-size=m]::-webkit-slider-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}:host[data-size=s]::-webkit-slider-thumb{-webkit-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.5rem;inline-size:.5rem;box-sizing:content-box;background-clip:content-box;border:.25rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1));margin-top:-.4375rem}:not(:disabled):host[data-size=s]::-webkit-slider-thumb{cursor:ew-resize}:not(:disabled):host[data-size=s]::-webkit-slider-thumb:hover,:active:not(:disabled):host[data-size=s]::-webkit-slider-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.5)}:focus-visible:host[data-size=s]::-webkit-slider-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}:host[data-size=m]::-moz-range-thumb{-moz-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.75rem;inline-size:.75rem;box-sizing:content-box;background-clip:content-box;border:.125rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1))}:not(:disabled):host[data-size=m]::-moz-range-thumb{cursor:ew-resize}:not(:disabled):host[data-size=m]::-moz-range-thumb:hover,:active:not(:disabled):host[data-size=m]::-moz-range-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.333)}:focus-visible:host[data-size=m]::-moz-range-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}:host[data-size=s]::-moz-range-thumb{-moz-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.5rem;inline-size:.5rem;box-sizing:content-box;background-clip:content-box;border:.25rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1))}:not(:disabled):host[data-size=s]::-moz-range-thumb{cursor:ew-resize}:not(:disabled):host[data-size=s]::-moz-range-thumb:hover,:active:not(:disabled):host[data-size=s]::-moz-range-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.5)}:focus-visible:host[data-size=s]::-moz-range-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}:host::-moz-range-progress{border-radius:inherit}:host::-moz-range-progress{block-size:.125rem;background:currentColor;border-top-right-radius:0;border-bottom-right-radius:0}:host-context(tui-textfield) :host([type=\\\"range\\\"]){--tui-radius: var(--tui-radius-m);position:absolute;top:100%;left:calc(var(--tui-radius) / 2);right:0;inline-size:calc(100% - calc(var(--tui-radius) / 2));box-sizing:border-box;block-size:1rem;margin:-.5625rem 0 0;padding:0;border-top-left-radius:0;border-bottom-left-radius:calc(var(--tui-radius) * 10) calc(var(--tui-radius) * 2);pointer-events:auto}:host-context(tui-textfield[data-size=\\\"l\\\"]) :host([type=\\\"range\\\"]){--tui-radius: var(--tui-radius-l)}\\n\"]\n    }]\n  }], function () {\n    return [];\n  }, {\n    size: [{\n      type: Input\n    }],\n    segmentsSetter: [{\n      type: Input,\n      args: [{\n        alias: 'segments',\n        transform: x => Array.isArray(x) ? x : new Array(x).fill(null).map((_, i) => i / x)\n      }]\n    }]\n  });\n})();\nclass TuiSliderKeyStepsBase {\n  constructor() {\n    this.injector = inject(INJECTOR);\n    this.control = inject(NgControl, {\n      self: true,\n      optional: true\n    });\n    this.step = 1;\n    this.transformer = signal(null);\n    this.value = toSignal(timer(0) // https://github.com/angular/angular/issues/54418\n    .pipe(switchMap(() => tuiControlValue(this.control))));\n  }\n  get slider() {\n    return this.injector.get(TuiSliderComponent);\n  }\n  set keySteps(steps) {\n    this.transformer.set(steps && tuiCreateKeyStepsTransformer(steps, this.slider));\n    this.min = steps?.[0][1];\n    this.max = steps?.[steps.length - 1]?.[1];\n  }\n  /**\n   * TODO(v5): standardize logic between `TuiSlider` & `TuiInputSlider` (for non-linear slider `step` means percentage)\n   * Add these host-bindings to `TuiSliderKeyStepsBase`:\n   * ```\n   * host: {\n   *     '[attr.min]': '0',\n   *     '[attr.step]': '1',\n   *     '[attr.max]': 'totalSteps',\n   * },\n   * ```\n   */\n  get totalSteps() {\n    /**\n     * Not-integer amount of steps is invalid usage of native sliders\n     * ```html\n     * <input type=\"range\" [max]=\"100\" [step]=\"3.33\" />\n     * ```\n     * (impossible to select 100; 99.9 is max allowed value)\n     */\n    return this.step ? Math.round(100 / this.step) : Infinity;\n  }\n  takeStep(coefficient) {\n    const newValue = this.slider.value + coefficient;\n    return this.transformer()?.toControlValue(this.slider.value + coefficient) ?? newValue;\n  }\n  static {\n    this.ɵfac = function TuiSliderKeyStepsBase_Factory(t) {\n      return new (t || TuiSliderKeyStepsBase)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiSliderKeyStepsBase,\n      selectors: [[\"input\", \"tuiSlider\", \"\", \"keySteps\", \"\"]],\n      hostVars: 3,\n      hostBindings: function TuiSliderKeyStepsBase_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-valuemin\", ctx.min)(\"aria-valuemax\", ctx.max)(\"aria-valuenow\", ctx.value());\n        }\n      },\n      inputs: {\n        step: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"step\", \"step\", x => x === 'any' ? null : x],\n        keySteps: \"keySteps\"\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n__decorate([tuiPure], TuiSliderKeyStepsBase.prototype, \"slider\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSliderKeyStepsBase, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiSlider][keySteps]',\n      host: {\n        '[attr.aria-valuemin]': 'min',\n        '[attr.aria-valuemax]': 'max',\n        '[attr.aria-valuenow]': 'value()'\n      }\n    }]\n  }], null, {\n    step: [{\n      type: Input,\n      args: [{\n        transform: x => x === 'any' ? null : x\n      }]\n    }],\n    slider: [],\n    keySteps: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiSliderKeySteps extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.slider = inject(forwardRef(() => TuiSliderComponent));\n  }\n  set keySteps(steps) {\n    this.transformer = tuiCreateKeyStepsTransformer(steps, this.slider);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiSliderKeySteps_BaseFactory;\n      return function TuiSliderKeySteps_Factory(t) {\n        return (ɵTuiSliderKeySteps_BaseFactory || (ɵTuiSliderKeySteps_BaseFactory = i0.ɵɵgetInheritedFactory(TuiSliderKeySteps)))(t || TuiSliderKeySteps);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiSliderKeySteps,\n      selectors: [[\"input\", \"tuiSlider\", \"\", \"keySteps\", \"\", \"ngModel\", \"\"], [\"input\", \"tuiSlider\", \"\", \"keySteps\", \"\", \"formControl\", \"\"], [\"input\", \"tuiSlider\", \"\", \"keySteps\", \"\", \"formControlName\", \"\"]],\n      hostVars: 2,\n      hostBindings: function TuiSliderKeySteps_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"blur\", function TuiSliderKeySteps_blur_HostBindingHandler() {\n            return ctx.onTouched();\n          })(\"input\", function TuiSliderKeySteps_input_HostBindingHandler($event) {\n            return ctx.onChange($event.target.value);\n          })(\"change\", function TuiSliderKeySteps_change_HostBindingHandler($event) {\n            return ctx.onChange($event.target.value);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"value\", ctx.value())(\"disabled\", ctx.disabled());\n        }\n      },\n      inputs: {\n        keySteps: \"keySteps\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiFallbackValueProvider(0)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSliderKeySteps, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiSlider][keySteps][ngModel],input[tuiSlider][keySteps][formControl],input[tuiSlider][keySteps][formControlName]',\n      providers: [tuiFallbackValueProvider(0)],\n      host: {\n        '[value]': 'value()',\n        '[disabled]': 'disabled()',\n        '(blur)': 'onTouched()',\n        '(input)': 'onChange($event.target.value)',\n        '(change)': 'onChange($event.target.value)'\n      }\n    }]\n  }], null, {\n    keySteps: [{\n      type: Input\n    }]\n  });\n})();\nconst SLIDER_INTERACTION_KEYS = new Set(['ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'End', 'Home', 'PageDown', 'PageUp']);\n/**\n * Native <input type='range' readonly> doesn't work.\n * This directive imitates this native behaviour.\n */\nclass TuiSliderReadonly {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.doc = inject(DOCUMENT);\n    this.readonly = true;\n    const touchStart$ = tuiTypedFromEvent(this.el, 'touchstart', {\n      passive: false\n    });\n    const touchMove$ = tuiTypedFromEvent(this.doc, 'touchmove', {\n      passive: false\n    });\n    const touchEnd$ = tuiTypedFromEvent(this.doc, 'touchend', {\n      passive: true\n    });\n    const shouldPreventMove$ = merge(touchStart$.pipe(tap(e => this.preventEvent(e)), map(TUI_TRUE_HANDLER)), touchEnd$.pipe(map(TUI_FALSE_HANDLER)));\n    /**\n     * @bad TODO think about another solution.\n     * Keep in mind that preventing touch event (on slider) inside `@HostListener('touchstart')` doesn't work for mobile chrome.\n     */\n    combineLatest([touchMove$, shouldPreventMove$]).pipe(filter(([_, shouldPreventMove]) => shouldPreventMove), takeUntilDestroyed()).subscribe(([moveEvent]) => this.preventEvent(moveEvent));\n  }\n  preventEvent(event) {\n    if (event.cancelable && this.readonly) {\n      event.preventDefault();\n    }\n  }\n  preventKeyboardInteraction(event) {\n    if (SLIDER_INTERACTION_KEYS.has(event.key)) {\n      this.preventEvent(event);\n    }\n  }\n  static {\n    this.ɵfac = function TuiSliderReadonly_Factory(t) {\n      return new (t || TuiSliderReadonly)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiSliderReadonly,\n      selectors: [[\"input\", \"tuiSlider\", \"\", \"readonly\", \"\"]],\n      hostBindings: function TuiSliderReadonly_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function TuiSliderReadonly_keydown_HostBindingHandler($event) {\n            return ctx.preventKeyboardInteraction($event);\n          })(\"mousedown\", function TuiSliderReadonly_mousedown_HostBindingHandler($event) {\n            return ctx.preventEvent($event);\n          });\n        }\n      },\n      inputs: {\n        readonly: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"readonly\", \"readonly\", coerceBooleanProperty]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSliderReadonly, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiSlider][readonly]',\n      host: {\n        '(keydown)': 'preventKeyboardInteraction($event)',\n        '(mousedown)': 'preventEvent($event)'\n      }\n    }]\n  }], function () {\n    return [];\n  }, {\n    readonly: [{\n      type: Input,\n      args: [{\n        transform: coerceBooleanProperty\n      }]\n    }]\n  });\n})();\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nclass TuiSliderThumbLabel {\n  ngAfterContentInit() {\n    ngDevMode && console.assert(Boolean(this.control?.valueChanges), '\\n[tuiSliderThumbLabel] expected <input tuiSlider type=\"range\" /> to use Angular Forms.\\n' + 'Use [(ngModel)] or [formControl] or formControlName for correct work.');\n  }\n  get size() {\n    return this.slider?.size || 'm';\n  }\n  get ratio() {\n    return this.slider?.valueRatio || 0;\n  }\n  get ghostLeft() {\n    return this.ratio * (this.slider?.el.offsetWidth || 0);\n  }\n  static {\n    this.ɵfac = function TuiSliderThumbLabel_Factory(t) {\n      return new (t || TuiSliderThumbLabel)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiSliderThumbLabel,\n      selectors: [[\"\", \"tuiSliderThumbLabel\", \"\"]],\n      contentQueries: function TuiSliderThumbLabel_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiSliderComponent, 5);\n          i0.ɵɵcontentQuery(dirIndex, NgControl, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slider = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.control = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c1,\n      ngContentSelectors: _c3,\n      decls: 5,\n      vars: 8,\n      consts: [[4, \"ngIf\"], [1, \"t-ghost\"]],\n      template: function TuiSliderThumbLabel_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵtemplate(0, TuiSliderThumbLabel_ng_container_0_Template, 1, 0, \"ng-container\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵprojection(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(4, 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 6, ctx.control == null ? null : ctx.control.valueChanges));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"--tui-slider-thumb-ratio\", ctx.ratio)(\"left\", ctx.ghostLeft, \"px\");\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n        }\n      },\n      dependencies: [AsyncPipe, NgIf],\n      styles: [\"[_nghost-%COMP%]{position:relative}.t-ghost[_ngcontent-%COMP%]{position:absolute;top:0;bottom:0;margin:auto;border-radius:50%;pointer-events:none}.t-ghost[data-size=s][_ngcontent-%COMP%]{inline-size:.5rem;block-size:.5rem;transform:translate(calc(var(--tui-slider-thumb-ratio) * -.5rem))}.t-ghost[data-size=m][_ngcontent-%COMP%]{inline-size:.75rem;block-size:.75rem;transform:translate(calc(var(--tui-slider-thumb-ratio) * -.75rem))}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSliderThumbLabel, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: '[tuiSliderThumbLabel]',\n      imports: [AsyncPipe, NgIf],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-container *ngIf=\\\"control?.valueChanges | async\\\" />\\n\\n<div\\n    class=\\\"t-ghost\\\"\\n    [attr.data-size]=\\\"size\\\"\\n    [style.--tui-slider-thumb-ratio]=\\\"ratio\\\"\\n    [style.left.px]=\\\"ghostLeft\\\"\\n>\\n    <ng-content />\\n</div>\\n\\n<ng-content select=\\\"input[type=range]\\\" />\\n\",\n      styles: [\":host{position:relative}.t-ghost{position:absolute;top:0;bottom:0;margin:auto;border-radius:50%;pointer-events:none}.t-ghost[data-size=s]{inline-size:.5rem;block-size:.5rem;transform:translate(calc(var(--tui-slider-thumb-ratio) * -.5rem))}.t-ghost[data-size=m]{inline-size:.75rem;block-size:.75rem;transform:translate(calc(var(--tui-slider-thumb-ratio) * -.75rem))}\\n\"]\n    }]\n  }], null, {\n    slider: [{\n      type: ContentChild,\n      args: [TuiSliderComponent]\n    }],\n    control: [{\n      type: ContentChild,\n      args: [NgControl]\n    }]\n  });\n})();\nconst TuiSlider = [TuiSliderComponent, TuiSliderThumbLabel, TuiSliderKeyStepsBase, TuiSliderKeySteps, TuiSliderReadonly];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_FLOATING_PRECISION, TUI_SLIDER_DEFAULT_OPTIONS, TUI_SLIDER_OPTIONS, TuiSlider, TuiSliderComponent, TuiSliderKeySteps, TuiSliderKeyStepsBase, TuiSliderReadonly, TuiSliderThumbLabel, tuiCreateKeyStepsTransformer, tuiKeyStepValueToPercentage, tuiPercentageToKeyStepValue, tuiSliderOptionsProvider };", "map": {"version": 3, "names": ["tuiRound", "__decorate", "i0", "inject", "signal", "computed", "Component", "ChangeDetectionStrategy", "Input", "INJECTOR", "Directive", "forwardRef", "ContentChild", "toSignal", "takeUntilDestroyed", "NgControl", "NgModel", "TuiControl", "tuiWatch", "tuiControlValue", "tuiTypedFromEvent", "tuiFallback<PERSON><PERSON>ue<PERSON>", "tuiCreateToken", "tuiProvideOptions", "tuiPure", "take", "timer", "switchMap", "merge", "tap", "map", "combineLatest", "filter", "tuiInjectElement", "tuiAsAuxiliary", "coerceBooleanProperty", "DOCUMENT", "AsyncPipe", "NgIf", "TUI_TRUE_HANDLER", "TUI_FALSE_HANDLER", "_c0", "_c1", "_c2", "_c3", "TuiSliderThumbLabel_ng_container_0_Template", "rf", "ctx", "ɵɵelementContainer", "TUI_FLOATING_PRECISION", "tuiFindKeyStepsBoundariesByFn", "keySteps", "fn", "keyStepUpperIndex", "findIndex", "keyStep", "i", "lowerStep", "upperStep", "length", "tuiPercentageToKeyStepValue", "valuePercentage", "lowerStepPercent", "lowerStepValue", "upperStepPercent", "upperStepValue", "keyStepPercentage", "_", "ratio", "controlValue", "tuiKeyStepValueToPercentage", "value", "keyStepValue", "tuiCreateKeyStepsTransformer", "slider", "fromControlValue", "newValuePercentage", "max", "min", "toControlValue", "nativeValue", "valueRatio", "TUI_SLIDER_DEFAULT_OPTIONS", "size", "trackColor", "TUI_SLIDER_OPTIONS", "tuiSliderOptionsProvider", "options", "TuiSliderComponent", "constructor", "control", "self", "optional", "segments", "ticksGradient", "getTicksGradient", "el", "TuiSliderKeyStepsBase", "valueChanges", "pipe", "subscribe", "segmentsSetter", "set", "Number", "x", "String", "step", "transformer", "viewModel", "Math", "round", "newValue", "percentages", "segment", "reduce", "acc", "index", "ɵfac", "TuiSliderComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "TuiSliderComponent_HostBindings", "ɵɵlistener", "TuiSliderComponent_input_HostBindingHandler", "ɵɵattribute", "ɵɵstyleProp", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "Array", "isArray", "fill", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInputTransformsFeature", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "template", "TuiSliderComponent_Template", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "providers", "host", "alias", "transform", "injector", "get", "steps", "totalSteps", "Infinity", "takeStep", "coefficient", "TuiSliderKeyStepsBase_Factory", "ɵdir", "ɵɵdefineDirective", "TuiSliderKeyStepsBase_HostBindings", "prototype", "TuiSliderKeySteps", "arguments", "ɵTuiSliderKeySteps_BaseFactory", "TuiSliderKeySteps_Factory", "ɵɵgetInheritedFactory", "TuiSliderKeySteps_HostBindings", "TuiSliderKeySteps_blur_HostBindingHandler", "onTouched", "TuiSliderKeySteps_input_HostBindingHandler", "$event", "onChange", "target", "TuiSliderKeySteps_change_HostBindingHandler", "ɵɵhostProperty", "disabled", "ɵɵInheritDefinitionFeature", "SLIDER_INTERACTION_KEYS", "Set", "Tui<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doc", "readonly", "touchStart$", "passive", "touchMove$", "touchEnd$", "shouldPreventMove$", "e", "preventEvent", "shouldPreventMove", "moveEvent", "event", "cancelable", "preventDefault", "preventKeyboardInteraction", "has", "key", "TuiSliderReadonly_Factory", "TuiSliderReadonly_HostBindings", "TuiSliderReadonly_keydown_HostBindingHandler", "TuiSliderReadonly_mousedown_HostBindingHandler", "TuiSliderThumbLabel", "ngAfterContentInit", "console", "assert", "Boolean", "ghostLeft", "offsetWidth", "TuiSliderThumbLabel_Factory", "contentQueries", "TuiSliderThumbLabel_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "ngContentSelectors", "consts", "TuiSliderThumbLabel_Template", "ɵɵprojectionDef", "ɵɵtemplate", "ɵɵpipe", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpipeBind1", "ɵɵadvance", "dependencies", "imports", "TuiSlider"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-slider.mjs"], "sourcesContent": ["import { tuiRound } from '@taiga-ui/cdk/utils/math';\nimport { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { inject, signal, computed, Component, ChangeDetectionStrategy, Input, INJECTOR, Directive, forwardRef, ContentChild } from '@angular/core';\nimport { toSignal, takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { NgControl, NgModel } from '@angular/forms';\nimport { TuiControl } from '@taiga-ui/cdk/classes';\nimport { tuiWatch, tuiControlValue, tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tuiFallbackValueProvider } from '@taiga-ui/cdk/tokens';\nimport { tuiCreateToken, tuiProvideOptions, tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { take, timer, switchMap, merge, tap, map, combineLatest, filter } from 'rxjs';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiAsAuxiliary } from '@taiga-ui/core/tokens';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { DOCUMENT, AsyncPipe, NgIf } from '@angular/common';\nimport { TUI_TRUE_HANDLER, TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\n\n/**\n * Used as a limit for eliminating JS issues with floating point math\n */\nconst TUI_FLOATING_PRECISION = 7;\nfunction tuiFindKeyStepsBoundariesByFn(keySteps, fn) {\n    const keyStepUpperIndex = keySteps.findIndex((keyStep, i) => i && fn(keyStep));\n    const lowerStep = keySteps[keyStepUpperIndex - 1] || keySteps[0];\n    const upperStep = keySteps[keyStepUpperIndex] ||\n        keySteps[keySteps.length - 1] || [0, 0];\n    return [lowerStep, upperStep];\n}\nfunction tuiPercentageToKeyStepValue(valuePercentage, keySteps) {\n    const [[lowerStepPercent, lowerStepValue], [upperStepPercent, upperStepValue]] = tuiFindKeyStepsBoundariesByFn(keySteps, ([keyStepPercentage, _]) => valuePercentage <= keyStepPercentage);\n    const ratio = (valuePercentage - lowerStepPercent) / (upperStepPercent - lowerStepPercent);\n    const controlValue = (upperStepValue - lowerStepValue) * ratio + lowerStepValue;\n    return tuiRound(controlValue, TUI_FLOATING_PRECISION);\n}\nfunction tuiKeyStepValueToPercentage(value, keySteps) {\n    const [[lowerStepPercent, lowerStepValue], [upperStepPercent, upperStepValue]] = tuiFindKeyStepsBoundariesByFn(keySteps, ([_, keyStepValue]) => value <= keyStepValue);\n    const ratio = (value - lowerStepValue) / (upperStepValue - lowerStepValue) || 0;\n    return (upperStepPercent - lowerStepPercent) * ratio + lowerStepPercent;\n}\nfunction tuiCreateKeyStepsTransformer(keySteps, slider) {\n    return new (class {\n        fromControlValue(controlValue) {\n            const newValuePercentage = tuiKeyStepValueToPercentage(controlValue, keySteps);\n            return (newValuePercentage * (slider.max - slider.min)) / 100 + slider.min;\n        }\n        toControlValue(nativeValue) {\n            const valueRatio = (nativeValue - slider.min) / (slider.max - slider.min) || 0;\n            return tuiPercentageToKeyStepValue(valueRatio * 100, keySteps);\n        }\n    })();\n}\n\nconst TUI_SLIDER_DEFAULT_OPTIONS = {\n    size: 'm',\n    trackColor: 'var(--tui-background-neutral-2)',\n};\n/**\n * Default parameters for Slider component\n */\nconst TUI_SLIDER_OPTIONS = tuiCreateToken(TUI_SLIDER_DEFAULT_OPTIONS);\nfunction tuiSliderOptionsProvider(options) {\n    return tuiProvideOptions(TUI_SLIDER_OPTIONS, options, TUI_SLIDER_DEFAULT_OPTIONS);\n}\n\nclass TuiSliderComponent {\n    constructor() {\n        this.control = inject(NgControl, { self: true, optional: true });\n        this.options = inject(TUI_SLIDER_OPTIONS);\n        this.segments = signal([1]);\n        this.ticksGradient = computed((segments = this.segments()) => this.getTicksGradient(segments));\n        this.size = this.options.size;\n        this.el = tuiInjectElement();\n        this.keySteps = inject(TuiSliderKeyStepsBase, {\n            self: true,\n            optional: true,\n        });\n        if (this.control instanceof NgModel) {\n            /**\n             * The ValueAccessor.writeValue method is called twice on any value accessor during component initialization,\n             * when a control is bound using [(ngModel)], first time with a phantom null value.\n             * With `changeDetection: ChangeDetectionStrategy.OnPush` the second call of writeValue with real value don't re-render the view.\n             * ___\n             * See this {@link https://github.com/angular/angular/issues/14988 issue}\n             */\n            this.control.valueChanges?.pipe(tuiWatch(), take(1)).subscribe();\n        }\n    }\n    // TODO(v5): use signal inputs\n    set segmentsSetter(segments) {\n        this.segments.set(segments);\n    }\n    get valueRatio() {\n        return (this.value - this.min) / (this.max - this.min) || 0;\n    }\n    get min() {\n        return Number(this.el.min);\n    }\n    set min(x) {\n        this.el.min = String(x);\n    }\n    get max() {\n        return Number(this.el.max || 100);\n    }\n    set max(x) {\n        this.el.max = String(x);\n    }\n    get step() {\n        if (!this.el.step) {\n            return 1;\n        }\n        return this.el.step === 'any' ? 0 : Number(this.el.step);\n    }\n    set step(x) {\n        this.el.step = String(x);\n    }\n    get value() {\n        /**\n         * If developer uses `[(ngModel)]` and programmatically change value,\n         * the `el.nativeElement.value` is equal to the previous value at this moment\n         * (it will be updated only in next microtask).\n         * @see https://github.com/angular/angular/issues/13568\n         */\n        if (this.control instanceof NgModel) {\n            const transformer = this.keySteps?.transformer();\n            const value = transformer\n                ? transformer.fromControlValue(this.control.value)\n                : this.control.viewModel;\n            return this.step\n                ? tuiRound(Math.round(value / this.step) * this.step, TUI_FLOATING_PRECISION)\n                : value;\n        }\n        return Number(this.el.value) || 0;\n    }\n    set value(newValue) {\n        this.el.value = `${newValue}`;\n    }\n    getTicksGradient(segments) {\n        if (segments.length <= 1) {\n            return 'linear-gradient(to right, transparent 0 100%)';\n        }\n        const percentages = segments\n            .filter((segment) => segment > 0 && segment < 1)\n            .map((segment) => segment * 100);\n        return percentages.reduce((acc, segment, index) => `${acc}\n                var(--tui-text-tertiary) ${segment}% calc(${segment}% + var(--t-tick-thickness)),\n                transparent ${segment}% ${percentages[index + 1] ?? 100}%${percentages[index + 1] ? ',' : ')'}\n                `, `linear-gradient(to right, transparent 0 ${percentages[0]}%,`);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSliderComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"16.2.12\", type: TuiSliderComponent, isStandalone: true, selector: \"input[type=range][tuiSlider]\", inputs: { size: \"size\", segmentsSetter: [\"segments\", \"segmentsSetter\", (x) => Array.isArray(x) ? x : new Array(x).fill(null).map((_, i) => i / x)] }, host: { listeners: { \"input\": \"0\" }, properties: { \"style.--tui-slider-track-color\": \"options.trackColor\", \"style.--tui-ticks-gradient\": \"ticksGradient()\", \"style.--tui-slider-fill-ratio\": \"valueRatio\", \"attr.data-size\": \"size\" } }, providers: [tuiAsAuxiliary(TuiSliderComponent)], ngImport: i0, template: '', isInline: true, styles: [\":host{--t-tick-thickness: .25rem;position:relative;display:block;inline-size:100%;color:var(--tui-background-accent-1);cursor:pointer;-webkit-appearance:none;appearance:none;block-size:.125rem;padding:.4375rem 0;background-color:transparent;background-clip:content-box;outline:none;border-radius:var(--tui-radius-m)}:host:active{cursor:ew-resize}:host:disabled{opacity:var(--tui-disabled-opacity);cursor:auto}:host[data-size=s]:not(:disabled):before{transition-property:transform,opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:.25rem;left:calc(var(--tui-slider-fill-ratio) * 100% - var(--tui-slider-fill-ratio) * .5rem);inline-size:.5rem;block-size:.5rem;border-radius:50%;transform:var(--tui-slider-thumb-transform, scale(1));content:\\\"\\\";cursor:ew-resize;background:currentColor;opacity:0}:host[data-size=s]:active:before{opacity:.2;transform:var(--tui-slider-thumb-transform, scale(1)) scale(2.33)}:host[data-size=m]:not(:disabled):before{transition-property:transform,opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:.125rem;left:calc(var(--tui-slider-fill-ratio) * 100% - var(--tui-slider-fill-ratio) * .75rem);inline-size:.75rem;block-size:.75rem;border-radius:50%;transform:var(--tui-slider-thumb-transform, scale(1));content:\\\"\\\";cursor:ew-resize;background:currentColor;opacity:0}:host[data-size=m]:active:before{opacity:.2;transform:var(--tui-slider-thumb-transform, scale(1)) scale(2.33)}:host::-webkit-slider-container{border-radius:inherit}:host[data-size=m]::-webkit-slider-runnable-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient),linear-gradient(to right,currentColor calc(100% * var(--tui-slider-fill-ratio)),transparent calc(100% * var(--tui-slider-fill-ratio)));background-position-x:calc((.75rem - var(--t-tick-thickness)) / 2),0;background-size:calc(100% - .75rem),auto}:host[data-size=s]::-webkit-slider-runnable-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient),linear-gradient(to right,currentColor calc(100% * var(--tui-slider-fill-ratio)),transparent calc(100% * var(--tui-slider-fill-ratio)));background-position-x:calc((.5rem - var(--t-tick-thickness)) / 2),0;background-size:calc(100% - .5rem),auto}:host[data-size=m]::-moz-range-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient);background-position-x:calc((.75rem - var(--t-tick-thickness)) / 2);background-size:calc(100% - .75rem)}:host[data-size=s]::-moz-range-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient);background-position-x:calc((.5rem - var(--t-tick-thickness)) / 2);background-size:calc(100% - .5rem)}:host[data-size=m]::-webkit-slider-thumb{-webkit-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.75rem;inline-size:.75rem;box-sizing:content-box;background-clip:content-box;border:.125rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1));margin-top:-.4375rem}:not(:disabled):host[data-size=m]::-webkit-slider-thumb{cursor:ew-resize}:not(:disabled):host[data-size=m]::-webkit-slider-thumb:hover,:active:not(:disabled):host[data-size=m]::-webkit-slider-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.333)}:focus-visible:host[data-size=m]::-webkit-slider-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}:host[data-size=s]::-webkit-slider-thumb{-webkit-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.5rem;inline-size:.5rem;box-sizing:content-box;background-clip:content-box;border:.25rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1));margin-top:-.4375rem}:not(:disabled):host[data-size=s]::-webkit-slider-thumb{cursor:ew-resize}:not(:disabled):host[data-size=s]::-webkit-slider-thumb:hover,:active:not(:disabled):host[data-size=s]::-webkit-slider-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.5)}:focus-visible:host[data-size=s]::-webkit-slider-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}:host[data-size=m]::-moz-range-thumb{-moz-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.75rem;inline-size:.75rem;box-sizing:content-box;background-clip:content-box;border:.125rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1))}:not(:disabled):host[data-size=m]::-moz-range-thumb{cursor:ew-resize}:not(:disabled):host[data-size=m]::-moz-range-thumb:hover,:active:not(:disabled):host[data-size=m]::-moz-range-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.333)}:focus-visible:host[data-size=m]::-moz-range-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}:host[data-size=s]::-moz-range-thumb{-moz-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.5rem;inline-size:.5rem;box-sizing:content-box;background-clip:content-box;border:.25rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1))}:not(:disabled):host[data-size=s]::-moz-range-thumb{cursor:ew-resize}:not(:disabled):host[data-size=s]::-moz-range-thumb:hover,:active:not(:disabled):host[data-size=s]::-moz-range-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.5)}:focus-visible:host[data-size=s]::-moz-range-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}:host::-moz-range-progress{border-radius:inherit}:host::-moz-range-progress{block-size:.125rem;background:currentColor;border-top-right-radius:0;border-bottom-right-radius:0}:host-context(tui-textfield) :host([type=\\\"range\\\"]){--tui-radius: var(--tui-radius-m);position:absolute;top:100%;left:calc(var(--tui-radius) / 2);right:0;inline-size:calc(100% - calc(var(--tui-radius) / 2));box-sizing:border-box;block-size:1rem;margin:-.5625rem 0 0;padding:0;border-top-left-radius:0;border-bottom-left-radius:calc(var(--tui-radius) * 10) calc(var(--tui-radius) * 2);pointer-events:auto}:host-context(tui-textfield[data-size=\\\"l\\\"]) :host([type=\\\"range\\\"]){--tui-radius: var(--tui-radius-l)}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSliderComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'input[type=range][tuiSlider]', template: '', changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiAsAuxiliary(TuiSliderComponent)], host: {\n                        /**\n                         * For change detection.\n                         * Webkit does not have built-in method for customization of filling progress (as Firefox).\n                         * We draw filling of progress by `background: linear-gradient(...)` of the track.\n                         * This function triggers change detection (for {@link valueRatio} getter) when we drag thumb of the input.\n                         */\n                        '(input)': '0',\n                        '[style.--tui-slider-track-color]': 'options.trackColor',\n                        '[style.--tui-ticks-gradient]': 'ticksGradient()',\n                        '[style.--tui-slider-fill-ratio]': 'valueRatio',\n                        '[attr.data-size]': 'size',\n                    }, styles: [\":host{--t-tick-thickness: .25rem;position:relative;display:block;inline-size:100%;color:var(--tui-background-accent-1);cursor:pointer;-webkit-appearance:none;appearance:none;block-size:.125rem;padding:.4375rem 0;background-color:transparent;background-clip:content-box;outline:none;border-radius:var(--tui-radius-m)}:host:active{cursor:ew-resize}:host:disabled{opacity:var(--tui-disabled-opacity);cursor:auto}:host[data-size=s]:not(:disabled):before{transition-property:transform,opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:.25rem;left:calc(var(--tui-slider-fill-ratio) * 100% - var(--tui-slider-fill-ratio) * .5rem);inline-size:.5rem;block-size:.5rem;border-radius:50%;transform:var(--tui-slider-thumb-transform, scale(1));content:\\\"\\\";cursor:ew-resize;background:currentColor;opacity:0}:host[data-size=s]:active:before{opacity:.2;transform:var(--tui-slider-thumb-transform, scale(1)) scale(2.33)}:host[data-size=m]:not(:disabled):before{transition-property:transform,opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:.125rem;left:calc(var(--tui-slider-fill-ratio) * 100% - var(--tui-slider-fill-ratio) * .75rem);inline-size:.75rem;block-size:.75rem;border-radius:50%;transform:var(--tui-slider-thumb-transform, scale(1));content:\\\"\\\";cursor:ew-resize;background:currentColor;opacity:0}:host[data-size=m]:active:before{opacity:.2;transform:var(--tui-slider-thumb-transform, scale(1)) scale(2.33)}:host::-webkit-slider-container{border-radius:inherit}:host[data-size=m]::-webkit-slider-runnable-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient),linear-gradient(to right,currentColor calc(100% * var(--tui-slider-fill-ratio)),transparent calc(100% * var(--tui-slider-fill-ratio)));background-position-x:calc((.75rem - var(--t-tick-thickness)) / 2),0;background-size:calc(100% - .75rem),auto}:host[data-size=s]::-webkit-slider-runnable-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient),linear-gradient(to right,currentColor calc(100% * var(--tui-slider-fill-ratio)),transparent calc(100% * var(--tui-slider-fill-ratio)));background-position-x:calc((.5rem - var(--t-tick-thickness)) / 2),0;background-size:calc(100% - .5rem),auto}:host[data-size=m]::-moz-range-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient);background-position-x:calc((.75rem - var(--t-tick-thickness)) / 2);background-size:calc(100% - .75rem)}:host[data-size=s]::-moz-range-track{block-size:.125rem;border-radius:inherit;background-repeat:no-repeat;background-color:var(--tui-slider-track-color);background-image:var(--tui-ticks-gradient);background-position-x:calc((.5rem - var(--t-tick-thickness)) / 2);background-size:calc(100% - .5rem)}:host[data-size=m]::-webkit-slider-thumb{-webkit-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.75rem;inline-size:.75rem;box-sizing:content-box;background-clip:content-box;border:.125rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1));margin-top:-.4375rem}:not(:disabled):host[data-size=m]::-webkit-slider-thumb{cursor:ew-resize}:not(:disabled):host[data-size=m]::-webkit-slider-thumb:hover,:active:not(:disabled):host[data-size=m]::-webkit-slider-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.333)}:focus-visible:host[data-size=m]::-webkit-slider-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}:host[data-size=s]::-webkit-slider-thumb{-webkit-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.5rem;inline-size:.5rem;box-sizing:content-box;background-clip:content-box;border:.25rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1));margin-top:-.4375rem}:not(:disabled):host[data-size=s]::-webkit-slider-thumb{cursor:ew-resize}:not(:disabled):host[data-size=s]::-webkit-slider-thumb:hover,:active:not(:disabled):host[data-size=s]::-webkit-slider-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.5)}:focus-visible:host[data-size=s]::-webkit-slider-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}:host[data-size=m]::-moz-range-thumb{-moz-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.75rem;inline-size:.75rem;box-sizing:content-box;background-clip:content-box;border:.125rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1))}:not(:disabled):host[data-size=m]::-moz-range-thumb{cursor:ew-resize}:not(:disabled):host[data-size=m]::-moz-range-thumb:hover,:active:not(:disabled):host[data-size=m]::-moz-range-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.333)}:focus-visible:host[data-size=m]::-moz-range-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}:host[data-size=s]::-moz-range-thumb{-moz-transition-property:transform;transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;isolation:isolate;-webkit-appearance:none;appearance:none;background-color:currentColor;box-shadow:none;border-radius:50%;block-size:.5rem;inline-size:.5rem;box-sizing:content-box;background-clip:content-box;border:.25rem solid transparent;border-inline-start:0;border-inline-end:0;transform:var(--tui-slider-thumb-transform, scale(1))}:not(:disabled):host[data-size=s]::-moz-range-thumb{cursor:ew-resize}:not(:disabled):host[data-size=s]::-moz-range-thumb:hover,:active:not(:disabled):host[data-size=s]::-moz-range-thumb{transform:var(--tui-slider-thumb-transform, scale(1)) scale(1.5)}:focus-visible:host[data-size=s]::-moz-range-thumb{box-shadow:0 0 0 2px inset var(--tui-border-focus)}:host::-moz-range-progress{border-radius:inherit}:host::-moz-range-progress{block-size:.125rem;background:currentColor;border-top-right-radius:0;border-bottom-right-radius:0}:host-context(tui-textfield) :host([type=\\\"range\\\"]){--tui-radius: var(--tui-radius-m);position:absolute;top:100%;left:calc(var(--tui-radius) / 2);right:0;inline-size:calc(100% - calc(var(--tui-radius) / 2));box-sizing:border-box;block-size:1rem;margin:-.5625rem 0 0;padding:0;border-top-left-radius:0;border-bottom-left-radius:calc(var(--tui-radius) * 10) calc(var(--tui-radius) * 2);pointer-events:auto}:host-context(tui-textfield[data-size=\\\"l\\\"]) :host([type=\\\"range\\\"]){--tui-radius: var(--tui-radius-l)}\\n\"] }]\n        }], ctorParameters: function () { return []; }, propDecorators: { size: [{\n                type: Input\n            }], segmentsSetter: [{\n                type: Input,\n                args: [{\n                        alias: 'segments',\n                        transform: (x) => Array.isArray(x) ? x : new Array(x).fill(null).map((_, i) => i / x),\n                    }]\n            }] } });\n\nclass TuiSliderKeyStepsBase {\n    constructor() {\n        this.injector = inject(INJECTOR);\n        this.control = inject(NgControl, { self: true, optional: true });\n        this.step = 1;\n        this.transformer = signal(null);\n        this.value = toSignal(timer(0) // https://github.com/angular/angular/issues/54418\n            .pipe(switchMap(() => tuiControlValue(this.control))));\n    }\n    get slider() {\n        return this.injector.get(TuiSliderComponent);\n    }\n    set keySteps(steps) {\n        this.transformer.set(steps && tuiCreateKeyStepsTransformer(steps, this.slider));\n        this.min = steps?.[0][1];\n        this.max = steps?.[steps.length - 1]?.[1];\n    }\n    /**\n     * TODO(v5): standardize logic between `TuiSlider` & `TuiInputSlider` (for non-linear slider `step` means percentage)\n     * Add these host-bindings to `TuiSliderKeyStepsBase`:\n     * ```\n     * host: {\n     *     '[attr.min]': '0',\n     *     '[attr.step]': '1',\n     *     '[attr.max]': 'totalSteps',\n     * },\n     * ```\n     */\n    get totalSteps() {\n        /**\n         * Not-integer amount of steps is invalid usage of native sliders\n         * ```html\n         * <input type=\"range\" [max]=\"100\" [step]=\"3.33\" />\n         * ```\n         * (impossible to select 100; 99.9 is max allowed value)\n         */\n        return this.step ? Math.round(100 / this.step) : Infinity;\n    }\n    takeStep(coefficient) {\n        const newValue = this.slider.value + coefficient;\n        return (this.transformer()?.toControlValue(this.slider.value + coefficient) ??\n            newValue);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSliderKeyStepsBase, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"16.2.12\", type: TuiSliderKeyStepsBase, isStandalone: true, selector: \"input[tuiSlider][keySteps]\", inputs: { step: [\"step\", \"step\", (x) => (x === 'any' ? null : x)], keySteps: \"keySteps\" }, host: { properties: { \"attr.aria-valuemin\": \"min\", \"attr.aria-valuemax\": \"max\", \"attr.aria-valuenow\": \"value()\" } }, ngImport: i0 }); }\n}\n__decorate([\n    tuiPure\n], TuiSliderKeyStepsBase.prototype, \"slider\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSliderKeyStepsBase, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiSlider][keySteps]',\n                    host: {\n                        '[attr.aria-valuemin]': 'min',\n                        '[attr.aria-valuemax]': 'max',\n                        '[attr.aria-valuenow]': 'value()',\n                    },\n                }]\n        }], propDecorators: { step: [{\n                type: Input,\n                args: [{ transform: (x) => (x === 'any' ? null : x) }]\n            }], slider: [], keySteps: [{\n                type: Input\n            }] } });\nclass TuiSliderKeySteps extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.slider = inject(forwardRef(() => TuiSliderComponent));\n    }\n    set keySteps(steps) {\n        this.transformer = tuiCreateKeyStepsTransformer(steps, this.slider);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSliderKeySteps, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSliderKeySteps, isStandalone: true, selector: \"input[tuiSlider][keySteps][ngModel],input[tuiSlider][keySteps][formControl],input[tuiSlider][keySteps][formControlName]\", inputs: { keySteps: \"keySteps\" }, host: { listeners: { \"blur\": \"onTouched()\", \"input\": \"onChange($event.target.value)\", \"change\": \"onChange($event.target.value)\" }, properties: { \"value\": \"value()\", \"disabled\": \"disabled()\" } }, providers: [tuiFallbackValueProvider(0)], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSliderKeySteps, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiSlider][keySteps][ngModel],input[tuiSlider][keySteps][formControl],input[tuiSlider][keySteps][formControlName]',\n                    providers: [tuiFallbackValueProvider(0)],\n                    host: {\n                        '[value]': 'value()',\n                        '[disabled]': 'disabled()',\n                        '(blur)': 'onTouched()',\n                        '(input)': 'onChange($event.target.value)',\n                        '(change)': 'onChange($event.target.value)',\n                    },\n                }]\n        }], propDecorators: { keySteps: [{\n                type: Input\n            }] } });\n\nconst SLIDER_INTERACTION_KEYS = new Set([\n    'ArrowDown',\n    'ArrowLeft',\n    'ArrowRight',\n    'ArrowUp',\n    'End',\n    'Home',\n    'PageDown',\n    'PageUp',\n]);\n/**\n * Native <input type='range' readonly> doesn't work.\n * This directive imitates this native behaviour.\n */\nclass TuiSliderReadonly {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.doc = inject(DOCUMENT);\n        this.readonly = true;\n        const touchStart$ = tuiTypedFromEvent(this.el, 'touchstart', {\n            passive: false,\n        });\n        const touchMove$ = tuiTypedFromEvent(this.doc, 'touchmove', {\n            passive: false,\n        });\n        const touchEnd$ = tuiTypedFromEvent(this.doc, 'touchend', {\n            passive: true,\n        });\n        const shouldPreventMove$ = merge(touchStart$.pipe(tap((e) => this.preventEvent(e)), map(TUI_TRUE_HANDLER)), touchEnd$.pipe(map(TUI_FALSE_HANDLER)));\n        /**\n         * @bad TODO think about another solution.\n         * Keep in mind that preventing touch event (on slider) inside `@HostListener('touchstart')` doesn't work for mobile chrome.\n         */\n        combineLatest([touchMove$, shouldPreventMove$])\n            .pipe(filter(([_, shouldPreventMove]) => shouldPreventMove), takeUntilDestroyed())\n            .subscribe(([moveEvent]) => this.preventEvent(moveEvent));\n    }\n    preventEvent(event) {\n        if (event.cancelable && this.readonly) {\n            event.preventDefault();\n        }\n    }\n    preventKeyboardInteraction(event) {\n        if (SLIDER_INTERACTION_KEYS.has(event.key)) {\n            this.preventEvent(event);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSliderReadonly, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"16.2.12\", type: TuiSliderReadonly, isStandalone: true, selector: \"input[tuiSlider][readonly]\", inputs: { readonly: [\"readonly\", \"readonly\", coerceBooleanProperty] }, host: { listeners: { \"keydown\": \"preventKeyboardInteraction($event)\", \"mousedown\": \"preventEvent($event)\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSliderReadonly, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiSlider][readonly]',\n                    host: {\n                        '(keydown)': 'preventKeyboardInteraction($event)',\n                        '(mousedown)': 'preventEvent($event)',\n                    },\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { readonly: [{\n                type: Input,\n                args: [{ transform: coerceBooleanProperty }]\n            }] } });\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nclass TuiSliderThumbLabel {\n    ngAfterContentInit() {\n        ngDevMode &&\n            console.assert(Boolean(this.control?.valueChanges), '\\n[tuiSliderThumbLabel] expected <input tuiSlider type=\"range\" /> to use Angular Forms.\\n' +\n                'Use [(ngModel)] or [formControl] or formControlName for correct work.');\n    }\n    get size() {\n        return this.slider?.size || 'm';\n    }\n    get ratio() {\n        return this.slider?.valueRatio || 0;\n    }\n    get ghostLeft() {\n        return this.ratio * (this.slider?.el.offsetWidth || 0);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSliderThumbLabel, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSliderThumbLabel, isStandalone: true, selector: \"[tuiSliderThumbLabel]\", queries: [{ propertyName: \"slider\", first: true, predicate: TuiSliderComponent, descendants: true }, { propertyName: \"control\", first: true, predicate: NgControl, descendants: true }], ngImport: i0, template: \"<ng-container *ngIf=\\\"control?.valueChanges | async\\\" />\\n\\n<div\\n    class=\\\"t-ghost\\\"\\n    [attr.data-size]=\\\"size\\\"\\n    [style.--tui-slider-thumb-ratio]=\\\"ratio\\\"\\n    [style.left.px]=\\\"ghostLeft\\\"\\n>\\n    <ng-content />\\n</div>\\n\\n<ng-content select=\\\"input[type=range]\\\" />\\n\", styles: [\":host{position:relative}.t-ghost{position:absolute;top:0;bottom:0;margin:auto;border-radius:50%;pointer-events:none}.t-ghost[data-size=s]{inline-size:.5rem;block-size:.5rem;transform:translate(calc(var(--tui-slider-thumb-ratio) * -.5rem))}.t-ghost[data-size=m]{inline-size:.75rem;block-size:.75rem;transform:translate(calc(var(--tui-slider-thumb-ratio) * -.75rem))}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSliderThumbLabel, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: '[tuiSliderThumbLabel]', imports: [AsyncPipe, NgIf], changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-container *ngIf=\\\"control?.valueChanges | async\\\" />\\n\\n<div\\n    class=\\\"t-ghost\\\"\\n    [attr.data-size]=\\\"size\\\"\\n    [style.--tui-slider-thumb-ratio]=\\\"ratio\\\"\\n    [style.left.px]=\\\"ghostLeft\\\"\\n>\\n    <ng-content />\\n</div>\\n\\n<ng-content select=\\\"input[type=range]\\\" />\\n\", styles: [\":host{position:relative}.t-ghost{position:absolute;top:0;bottom:0;margin:auto;border-radius:50%;pointer-events:none}.t-ghost[data-size=s]{inline-size:.5rem;block-size:.5rem;transform:translate(calc(var(--tui-slider-thumb-ratio) * -.5rem))}.t-ghost[data-size=m]{inline-size:.75rem;block-size:.75rem;transform:translate(calc(var(--tui-slider-thumb-ratio) * -.75rem))}\\n\"] }]\n        }], propDecorators: { slider: [{\n                type: ContentChild,\n                args: [TuiSliderComponent]\n            }], control: [{\n                type: ContentChild,\n                args: [NgControl]\n            }] } });\n\nconst TuiSlider = [\n    TuiSliderComponent,\n    TuiSliderThumbLabel,\n    TuiSliderKeyStepsBase,\n    TuiSliderKeySteps,\n    TuiSliderReadonly,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_FLOATING_PRECISION, TUI_SLIDER_DEFAULT_OPTIONS, TUI_SLIDER_OPTIONS, TuiSlider, TuiSliderComponent, TuiSliderKeySteps, TuiSliderKeyStepsBase, TuiSliderReadonly, TuiSliderThumbLabel, tuiCreateKeyStepsTransformer, tuiKeyStepValueToPercentage, tuiPercentageToKeyStepValue, tuiSliderOptionsProvider };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,YAAY,QAAQ,eAAe;AAClJ,SAASC,QAAQ,EAAEC,kBAAkB,QAAQ,4BAA4B;AACzE,SAASC,SAAS,EAAEC,OAAO,QAAQ,gBAAgB;AACnD,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,QAAQ,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,2BAA2B;AACxF,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,OAAO,QAAQ,mCAAmC;AAC9F,SAASC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,aAAa,EAAEC,MAAM,QAAQ,MAAM;AACrF,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,QAAQ,iBAAiB;AAC3D,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,yBAAyB;;AAE7E;AACA;AACA;AAFA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,4CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAmIqG5C,EAAE,CAAA8C,kBAAA,EA8MiV,CAAC;EAAA;AAAA;AA9Uzb,MAAMC,sBAAsB,GAAG,CAAC;AAChC,SAASC,6BAA6BA,CAACC,QAAQ,EAAEC,EAAE,EAAE;EACjD,MAAMC,iBAAiB,GAAGF,QAAQ,CAACG,SAAS,CAAC,CAACC,OAAO,EAAEC,CAAC,KAAKA,CAAC,IAAIJ,EAAE,CAACG,OAAO,CAAC,CAAC;EAC9E,MAAME,SAAS,GAAGN,QAAQ,CAACE,iBAAiB,GAAG,CAAC,CAAC,IAAIF,QAAQ,CAAC,CAAC,CAAC;EAChE,MAAMO,SAAS,GAAGP,QAAQ,CAACE,iBAAiB,CAAC,IACzCF,QAAQ,CAACA,QAAQ,CAACQ,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3C,OAAO,CAACF,SAAS,EAAEC,SAAS,CAAC;AACjC;AACA,SAASE,2BAA2BA,CAACC,eAAe,EAAEV,QAAQ,EAAE;EAC5D,MAAM,CAAC,CAACW,gBAAgB,EAAEC,cAAc,CAAC,EAAE,CAACC,gBAAgB,EAAEC,cAAc,CAAC,CAAC,GAAGf,6BAA6B,CAACC,QAAQ,EAAE,CAAC,CAACe,iBAAiB,EAAEC,CAAC,CAAC,KAAKN,eAAe,IAAIK,iBAAiB,CAAC;EAC1L,MAAME,KAAK,GAAG,CAACP,eAAe,GAAGC,gBAAgB,KAAKE,gBAAgB,GAAGF,gBAAgB,CAAC;EAC1F,MAAMO,YAAY,GAAG,CAACJ,cAAc,GAAGF,cAAc,IAAIK,KAAK,GAAGL,cAAc;EAC/E,OAAO/D,QAAQ,CAACqE,YAAY,EAAEpB,sBAAsB,CAAC;AACzD;AACA,SAASqB,2BAA2BA,CAACC,KAAK,EAAEpB,QAAQ,EAAE;EAClD,MAAM,CAAC,CAACW,gBAAgB,EAAEC,cAAc,CAAC,EAAE,CAACC,gBAAgB,EAAEC,cAAc,CAAC,CAAC,GAAGf,6BAA6B,CAACC,QAAQ,EAAE,CAAC,CAACgB,CAAC,EAAEK,YAAY,CAAC,KAAKD,KAAK,IAAIC,YAAY,CAAC;EACtK,MAAMJ,KAAK,GAAG,CAACG,KAAK,GAAGR,cAAc,KAAKE,cAAc,GAAGF,cAAc,CAAC,IAAI,CAAC;EAC/E,OAAO,CAACC,gBAAgB,GAAGF,gBAAgB,IAAIM,KAAK,GAAGN,gBAAgB;AAC3E;AACA,SAASW,4BAA4BA,CAACtB,QAAQ,EAAEuB,MAAM,EAAE;EACpD,OAAO,IAAK,MAAM;IACdC,gBAAgBA,CAACN,YAAY,EAAE;MAC3B,MAAMO,kBAAkB,GAAGN,2BAA2B,CAACD,YAAY,EAAElB,QAAQ,CAAC;MAC9E,OAAQyB,kBAAkB,IAAIF,MAAM,CAACG,GAAG,GAAGH,MAAM,CAACI,GAAG,CAAC,GAAI,GAAG,GAAGJ,MAAM,CAACI,GAAG;IAC9E;IACAC,cAAcA,CAACC,WAAW,EAAE;MACxB,MAAMC,UAAU,GAAG,CAACD,WAAW,GAAGN,MAAM,CAACI,GAAG,KAAKJ,MAAM,CAACG,GAAG,GAAGH,MAAM,CAACI,GAAG,CAAC,IAAI,CAAC;MAC9E,OAAOlB,2BAA2B,CAACqB,UAAU,GAAG,GAAG,EAAE9B,QAAQ,CAAC;IAClE;EACJ,CAAC,CAAE,CAAC;AACR;AAEA,MAAM+B,0BAA0B,GAAG;EAC/BC,IAAI,EAAE,GAAG;EACTC,UAAU,EAAE;AAChB,CAAC;AACD;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG/D,cAAc,CAAC4D,0BAA0B,CAAC;AACrE,SAASI,wBAAwBA,CAACC,OAAO,EAAE;EACvC,OAAOhE,iBAAiB,CAAC8D,kBAAkB,EAAEE,OAAO,EAAEL,0BAA0B,CAAC;AACrF;AAEA,MAAMM,kBAAkB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGvF,MAAM,CAACY,SAAS,EAAE;MAAE4E,IAAI,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChE,IAAI,CAACL,OAAO,GAAGpF,MAAM,CAACkF,kBAAkB,CAAC;IACzC,IAAI,CAACQ,QAAQ,GAAGzF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,IAAI,CAAC0F,aAAa,GAAGzF,QAAQ,CAAC,CAACwF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC,KAAK,IAAI,CAACE,gBAAgB,CAACF,QAAQ,CAAC,CAAC;IAC9F,IAAI,CAACV,IAAI,GAAG,IAAI,CAACI,OAAO,CAACJ,IAAI;IAC7B,IAAI,CAACa,EAAE,GAAG/D,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACkB,QAAQ,GAAGhD,MAAM,CAAC8F,qBAAqB,EAAE;MAC1CN,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,IAAI,CAACF,OAAO,YAAY1E,OAAO,EAAE;MACjC;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAAC0E,OAAO,CAACQ,YAAY,EAAEC,IAAI,CAACjF,QAAQ,CAAC,CAAC,EAAEO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC2E,SAAS,CAAC,CAAC;IACpE;EACJ;EACA;EACA,IAAIC,cAAcA,CAACR,QAAQ,EAAE;IACzB,IAAI,CAACA,QAAQ,CAACS,GAAG,CAACT,QAAQ,CAAC;EAC/B;EACA,IAAIZ,UAAUA,CAAA,EAAG;IACb,OAAO,CAAC,IAAI,CAACV,KAAK,GAAG,IAAI,CAACO,GAAG,KAAK,IAAI,CAACD,GAAG,GAAG,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC;EAC/D;EACA,IAAIA,GAAGA,CAAA,EAAG;IACN,OAAOyB,MAAM,CAAC,IAAI,CAACP,EAAE,CAAClB,GAAG,CAAC;EAC9B;EACA,IAAIA,GAAGA,CAAC0B,CAAC,EAAE;IACP,IAAI,CAACR,EAAE,CAAClB,GAAG,GAAG2B,MAAM,CAACD,CAAC,CAAC;EAC3B;EACA,IAAI3B,GAAGA,CAAA,EAAG;IACN,OAAO0B,MAAM,CAAC,IAAI,CAACP,EAAE,CAACnB,GAAG,IAAI,GAAG,CAAC;EACrC;EACA,IAAIA,GAAGA,CAAC2B,CAAC,EAAE;IACP,IAAI,CAACR,EAAE,CAACnB,GAAG,GAAG4B,MAAM,CAACD,CAAC,CAAC;EAC3B;EACA,IAAIE,IAAIA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACV,EAAE,CAACU,IAAI,EAAE;MACf,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACV,EAAE,CAACU,IAAI,KAAK,KAAK,GAAG,CAAC,GAAGH,MAAM,CAAC,IAAI,CAACP,EAAE,CAACU,IAAI,CAAC;EAC5D;EACA,IAAIA,IAAIA,CAACF,CAAC,EAAE;IACR,IAAI,CAACR,EAAE,CAACU,IAAI,GAAGD,MAAM,CAACD,CAAC,CAAC;EAC5B;EACA,IAAIjC,KAAKA,CAAA,EAAG;IACR;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAACmB,OAAO,YAAY1E,OAAO,EAAE;MACjC,MAAM2F,WAAW,GAAG,IAAI,CAACxD,QAAQ,EAAEwD,WAAW,CAAC,CAAC;MAChD,MAAMpC,KAAK,GAAGoC,WAAW,GACnBA,WAAW,CAAChC,gBAAgB,CAAC,IAAI,CAACe,OAAO,CAACnB,KAAK,CAAC,GAChD,IAAI,CAACmB,OAAO,CAACkB,SAAS;MAC5B,OAAO,IAAI,CAACF,IAAI,GACV1G,QAAQ,CAAC6G,IAAI,CAACC,KAAK,CAACvC,KAAK,GAAG,IAAI,CAACmC,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI,EAAEzD,sBAAsB,CAAC,GAC3EsB,KAAK;IACf;IACA,OAAOgC,MAAM,CAAC,IAAI,CAACP,EAAE,CAACzB,KAAK,CAAC,IAAI,CAAC;EACrC;EACA,IAAIA,KAAKA,CAACwC,QAAQ,EAAE;IAChB,IAAI,CAACf,EAAE,CAACzB,KAAK,GAAG,GAAGwC,QAAQ,EAAE;EACjC;EACAhB,gBAAgBA,CAACF,QAAQ,EAAE;IACvB,IAAIA,QAAQ,CAAClC,MAAM,IAAI,CAAC,EAAE;MACtB,OAAO,+CAA+C;IAC1D;IACA,MAAMqD,WAAW,GAAGnB,QAAQ,CACvB7D,MAAM,CAAEiF,OAAO,IAAKA,OAAO,GAAG,CAAC,IAAIA,OAAO,GAAG,CAAC,CAAC,CAC/CnF,GAAG,CAAEmF,OAAO,IAAKA,OAAO,GAAG,GAAG,CAAC;IACpC,OAAOD,WAAW,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEF,OAAO,EAAEG,KAAK,KAAK,GAAGD,GAAG;AACjE,2CAA2CF,OAAO,UAAUA,OAAO;AACnE,8BAA8BA,OAAO,KAAKD,WAAW,CAACI,KAAK,GAAG,CAAC,CAAC,IAAI,GAAG,IAAIJ,WAAW,CAACI,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;AAC7G,iBAAiB,EAAE,2CAA2CJ,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;EAC7E;EACA;IAAS,IAAI,CAACK,IAAI,YAAAC,2BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyF/B,kBAAkB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAACgC,IAAI,kBAD+EtH,EAAE,CAAAuH,iBAAA;MAAAC,IAAA,EACJlC,kBAAkB;MAAAmC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,gCAAAhF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADhB5C,EAAE,CAAA6H,UAAA,mBAAAC,4CAAA;YAAA,OACJ,CAAC;UAAA,CAAgB,CAAC;QAAA;QAAA,IAAAlF,EAAA;UADhB5C,EAAE,CAAA+H,WAAA,cAAAlF,GAAA,CAAAoC,IAAA;UAAFjF,EAAE,CAAAgI,WAAA,6BAAAnF,GAAA,CAAAwC,OAAA,CAAAH,UACa,CAAC,yBAAlBrC,GAAA,CAAA+C,aAAA,CAAc,CAAG,CAAC,4BAAA/C,GAAA,CAAAkC,UAAD,CAAC;QAAA;MAAA;MAAAkD,MAAA;QAAAhD,IAAA;QAAAkB,cAAA,GADhBnG,EAAE,CAAAkI,YAAA,CAAAC,0BAAA,gCACsJ7B,CAAC,IAAK8B,KAAK,CAACC,OAAO,CAAC/B,CAAC,CAAC,GAAGA,CAAC,GAAG,IAAI8B,KAAK,CAAC9B,CAAC,CAAC,CAACgC,IAAI,CAAC,IAAI,CAAC,CAAC1G,GAAG,CAAC,CAACqC,CAAC,EAAEX,CAAC,KAAKA,CAAC,GAAGgD,CAAC,CAAC;MAAA;MAAAiC,UAAA;MAAAC,QAAA,GADjOxI,EAAE,CAAAyI,kBAAA,CACwd,CAACzG,cAAc,CAACsD,kBAAkB,CAAC,CAAC,GAD9ftF,EAAE,CAAA0I,wBAAA,EAAF1I,EAAE,CAAA2I,mBAAA;MAAAC,KAAA,EAAArG,GAAA;MAAAsG,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,4BAAApG,EAAA,EAAAC,GAAA;MAAAoG,MAAA;MAAAC,eAAA;IAAA,EACuzP;EAAE;AACh6P;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGnJ,EAAE,CAAAoJ,iBAAA,CAGX9D,kBAAkB,EAAc,CAAC;IACjHkC,IAAI,EAAEpH,SAAS;IACfiJ,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEe,QAAQ,EAAE,8BAA8B;MAAEP,QAAQ,EAAE,EAAE;MAAEG,eAAe,EAAE7I,uBAAuB,CAACkJ,MAAM;MAAEC,SAAS,EAAE,CAACxH,cAAc,CAACsD,kBAAkB,CAAC,CAAC;MAAEmE,IAAI,EAAE;QAC/K;AACxB;AACA;AACA;AACA;AACA;QACwB,SAAS,EAAE,GAAG;QACd,kCAAkC,EAAE,oBAAoB;QACxD,8BAA8B,EAAE,iBAAiB;QACjD,iCAAiC,EAAE,YAAY;QAC/C,kBAAkB,EAAE;MACxB,CAAC;MAAER,MAAM,EAAE,CAAC,4sOAA4sO;IAAE,CAAC;EACvuO,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEhE,IAAI,EAAE,CAAC;MACjEuC,IAAI,EAAElH;IACV,CAAC,CAAC;IAAE6F,cAAc,EAAE,CAAC;MACjBqB,IAAI,EAAElH,KAAK;MACX+I,IAAI,EAAE,CAAC;QACCK,KAAK,EAAE,UAAU;QACjBC,SAAS,EAAGrD,CAAC,IAAK8B,KAAK,CAACC,OAAO,CAAC/B,CAAC,CAAC,GAAGA,CAAC,GAAG,IAAI8B,KAAK,CAAC9B,CAAC,CAAC,CAACgC,IAAI,CAAC,IAAI,CAAC,CAAC1G,GAAG,CAAC,CAACqC,CAAC,EAAEX,CAAC,KAAKA,CAAC,GAAGgD,CAAC;MACxF,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMP,qBAAqB,CAAC;EACxBR,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqE,QAAQ,GAAG3J,MAAM,CAACM,QAAQ,CAAC;IAChC,IAAI,CAACiF,OAAO,GAAGvF,MAAM,CAACY,SAAS,EAAE;MAAE4E,IAAI,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChE,IAAI,CAACc,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,WAAW,GAAGvG,MAAM,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACmE,KAAK,GAAG1D,QAAQ,CAACa,KAAK,CAAC,CAAC,CAAC,CAAC;IAAA,CAC1ByE,IAAI,CAACxE,SAAS,CAAC,MAAMR,eAAe,CAAC,IAAI,CAACuE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9D;EACA,IAAIhB,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACoF,QAAQ,CAACC,GAAG,CAACvE,kBAAkB,CAAC;EAChD;EACA,IAAIrC,QAAQA,CAAC6G,KAAK,EAAE;IAChB,IAAI,CAACrD,WAAW,CAACL,GAAG,CAAC0D,KAAK,IAAIvF,4BAA4B,CAACuF,KAAK,EAAE,IAAI,CAACtF,MAAM,CAAC,CAAC;IAC/E,IAAI,CAACI,GAAG,GAAGkF,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,IAAI,CAACnF,GAAG,GAAGmF,KAAK,GAAGA,KAAK,CAACrG,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIsG,UAAUA,CAAA,EAAG;IACb;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,OAAO,IAAI,CAACvD,IAAI,GAAGG,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,IAAI,CAACJ,IAAI,CAAC,GAAGwD,QAAQ;EAC7D;EACAC,QAAQA,CAACC,WAAW,EAAE;IAClB,MAAMrD,QAAQ,GAAG,IAAI,CAACrC,MAAM,CAACH,KAAK,GAAG6F,WAAW;IAChD,OAAQ,IAAI,CAACzD,WAAW,CAAC,CAAC,EAAE5B,cAAc,CAAC,IAAI,CAACL,MAAM,CAACH,KAAK,GAAG6F,WAAW,CAAC,IACvErD,QAAQ;EAChB;EACA;IAAS,IAAI,CAACM,IAAI,YAAAgD,8BAAA9C,CAAA;MAAA,YAAAA,CAAA,IAAyFtB,qBAAqB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAACqE,IAAI,kBAxE+EpK,EAAE,CAAAqK,iBAAA;MAAA7C,IAAA,EAwEJzB,qBAAqB;MAAA0B,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAA2C,mCAAA1H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxEnB5C,EAAE,CAAA+H,WAAA,kBAAAlF,GAAA,CAAA+B,GAAA,mBAAA/B,GAAA,CAAA8B,GAAA,mBAwEJ9B,GAAA,CAAAwB,KAAA,CAAM,CAAC;QAAA;MAAA;MAAA4D,MAAA;QAAAzB,IAAA,GAxELxG,EAAE,CAAAkI,YAAA,CAAAC,0BAAA,kBAwEiH7B,CAAC,IAAMA,CAAC,KAAK,KAAK,GAAG,IAAI,GAAGA,CAAE;QAAArD,QAAA;MAAA;MAAAsF,UAAA;MAAAC,QAAA,GAxEjJxI,EAAE,CAAA0I,wBAAA;IAAA,EAwE8S;EAAE;AACvZ;AACA3I,UAAU,CAAC,CACPuB,OAAO,CACV,EAAEyE,qBAAqB,CAACwE,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;AACnD;EAAA,QAAApB,SAAA,oBAAAA,SAAA,KA7EqGnJ,EAAE,CAAAoJ,iBAAA,CA6EXrD,qBAAqB,EAAc,CAAC;IACpHyB,IAAI,EAAEhH,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBe,QAAQ,EAAE,4BAA4B;MACtCG,IAAI,EAAE;QACF,sBAAsB,EAAE,KAAK;QAC7B,sBAAsB,EAAE,KAAK;QAC7B,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEjD,IAAI,EAAE,CAAC;MACrBgB,IAAI,EAAElH,KAAK;MACX+I,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAGrD,CAAC,IAAMA,CAAC,KAAK,KAAK,GAAG,IAAI,GAAGA;MAAG,CAAC;IACzD,CAAC,CAAC;IAAE9B,MAAM,EAAE,EAAE;IAAEvB,QAAQ,EAAE,CAAC;MACvBuE,IAAI,EAAElH;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMkK,iBAAiB,SAASzJ,UAAU,CAAC;EACvCwE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGkF,SAAS,CAAC;IACnB,IAAI,CAACjG,MAAM,GAAGvE,MAAM,CAACQ,UAAU,CAAC,MAAM6E,kBAAkB,CAAC,CAAC;EAC9D;EACA,IAAIrC,QAAQA,CAAC6G,KAAK,EAAE;IAChB,IAAI,CAACrD,WAAW,GAAGlC,4BAA4B,CAACuF,KAAK,EAAE,IAAI,CAACtF,MAAM,CAAC;EACvE;EACA;IAAS,IAAI,CAAC2C,IAAI;MAAA,IAAAuD,8BAAA;MAAA,gBAAAC,0BAAAtD,CAAA;QAAA,QAAAqD,8BAAA,KAAAA,8BAAA,GAtG+E1K,EAAE,CAAA4K,qBAAA,CAsGQJ,iBAAiB,IAAAnD,CAAA,IAAjBmD,iBAAiB;MAAA;IAAA,IAAqD;EAAE;EACnL;IAAS,IAAI,CAACJ,IAAI,kBAvG+EpK,EAAE,CAAAqK,iBAAA;MAAA7C,IAAA,EAuGJgD,iBAAiB;MAAA/C,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAkD,+BAAAjI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvGf5C,EAAE,CAAA6H,UAAA,kBAAAiD,0CAAA;YAAA,OAuGJjI,GAAA,CAAAkI,SAAA,CAAU,CAAC;UAAA,CAAK,CAAC,mBAAAC,2CAAAC,MAAA;YAAA,OAAjBpI,GAAA,CAAAqI,QAAA,CAAAD,MAAA,CAAAE,MAAA,CAAA9G,KAA4B,CAAC;UAAA,CAAb,CAAC,oBAAA+G,4CAAAH,MAAA;YAAA,OAAjBpI,GAAA,CAAAqI,QAAA,CAAAD,MAAA,CAAAE,MAAA,CAAA9G,KAA4B,CAAC;UAAA,CAAb,CAAC;QAAA;QAAA,IAAAzB,EAAA;UAvGf5C,EAAE,CAAAqL,cAAA,UAuGJxI,GAAA,CAAAwB,KAAA,CAAM,CAAU,CAAC,aAAjBxB,GAAA,CAAAyI,QAAA,CAAS,CAAO,CAAC;QAAA;MAAA;MAAArD,MAAA;QAAAhF,QAAA;MAAA;MAAAsF,UAAA;MAAAC,QAAA,GAvGfxI,EAAE,CAAAyI,kBAAA,CAuGwZ,CAACtH,wBAAwB,CAAC,CAAC,CAAC,CAAC,GAvGvbnB,EAAE,CAAAuL,0BAAA;IAAA,EAuG6d;EAAE;AACtkB;AACA;EAAA,QAAApC,SAAA,oBAAAA,SAAA,KAzGqGnJ,EAAE,CAAAoJ,iBAAA,CAyGXoB,iBAAiB,EAAc,CAAC;IAChHhD,IAAI,EAAEhH,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBe,QAAQ,EAAE,yHAAyH;MACnIE,SAAS,EAAE,CAACrI,wBAAwB,CAAC,CAAC,CAAC,CAAC;MACxCsI,IAAI,EAAE;QACF,SAAS,EAAE,SAAS;QACpB,YAAY,EAAE,YAAY;QAC1B,QAAQ,EAAE,aAAa;QACvB,SAAS,EAAE,+BAA+B;QAC1C,UAAU,EAAE;MAChB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAExG,QAAQ,EAAE,CAAC;MACzBuE,IAAI,EAAElH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkL,uBAAuB,GAAG,IAAIC,GAAG,CAAC,CACpC,WAAW,EACX,WAAW,EACX,YAAY,EACZ,SAAS,EACT,KAAK,EACL,MAAM,EACN,UAAU,EACV,QAAQ,CACX,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,CAAC;EACpBnG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACO,EAAE,GAAG/D,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAAC4J,GAAG,GAAG1L,MAAM,CAACiC,QAAQ,CAAC;IAC3B,IAAI,CAAC0J,QAAQ,GAAG,IAAI;IACpB,MAAMC,WAAW,GAAG3K,iBAAiB,CAAC,IAAI,CAAC4E,EAAE,EAAE,YAAY,EAAE;MACzDgG,OAAO,EAAE;IACb,CAAC,CAAC;IACF,MAAMC,UAAU,GAAG7K,iBAAiB,CAAC,IAAI,CAACyK,GAAG,EAAE,WAAW,EAAE;MACxDG,OAAO,EAAE;IACb,CAAC,CAAC;IACF,MAAME,SAAS,GAAG9K,iBAAiB,CAAC,IAAI,CAACyK,GAAG,EAAE,UAAU,EAAE;MACtDG,OAAO,EAAE;IACb,CAAC,CAAC;IACF,MAAMG,kBAAkB,GAAGvK,KAAK,CAACmK,WAAW,CAAC5F,IAAI,CAACtE,GAAG,CAAEuK,CAAC,IAAK,IAAI,CAACC,YAAY,CAACD,CAAC,CAAC,CAAC,EAAEtK,GAAG,CAACS,gBAAgB,CAAC,CAAC,EAAE2J,SAAS,CAAC/F,IAAI,CAACrE,GAAG,CAACU,iBAAiB,CAAC,CAAC,CAAC;IACnJ;AACR;AACA;AACA;IACQT,aAAa,CAAC,CAACkK,UAAU,EAAEE,kBAAkB,CAAC,CAAC,CAC1ChG,IAAI,CAACnE,MAAM,CAAC,CAAC,CAACmC,CAAC,EAAEmI,iBAAiB,CAAC,KAAKA,iBAAiB,CAAC,EAAExL,kBAAkB,CAAC,CAAC,CAAC,CACjFsF,SAAS,CAAC,CAAC,CAACmG,SAAS,CAAC,KAAK,IAAI,CAACF,YAAY,CAACE,SAAS,CAAC,CAAC;EACjE;EACAF,YAAYA,CAACG,KAAK,EAAE;IAChB,IAAIA,KAAK,CAACC,UAAU,IAAI,IAAI,CAACX,QAAQ,EAAE;MACnCU,KAAK,CAACE,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAC,0BAA0BA,CAACH,KAAK,EAAE;IAC9B,IAAId,uBAAuB,CAACkB,GAAG,CAACJ,KAAK,CAACK,GAAG,CAAC,EAAE;MACxC,IAAI,CAACR,YAAY,CAACG,KAAK,CAAC;IAC5B;EACJ;EACA;IAAS,IAAI,CAACnF,IAAI,YAAAyF,0BAAAvF,CAAA;MAAA,YAAAA,CAAA,IAAyFqE,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAACtB,IAAI,kBA3K+EpK,EAAE,CAAAqK,iBAAA;MAAA7C,IAAA,EA2KJkE,iBAAiB;MAAAjE,SAAA;MAAAE,YAAA,WAAAkF,+BAAAjK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3Kf5C,EAAE,CAAA6H,UAAA,qBAAAiF,6CAAA7B,MAAA;YAAA,OA2KJpI,GAAA,CAAA4J,0BAAA,CAAAxB,MAAiC,CAAC;UAAA,CAAlB,CAAC,uBAAA8B,+CAAA9B,MAAA;YAAA,OAAjBpI,GAAA,CAAAsJ,YAAA,CAAAlB,MAAmB,CAAC;UAAA,CAAJ,CAAC;QAAA;MAAA;MAAAhD,MAAA;QAAA2D,QAAA,GA3Kf5L,EAAE,CAAAkI,YAAA,CAAAC,0BAAA,0BA2KwHlG,qBAAqB;MAAA;MAAAsG,UAAA;MAAAC,QAAA,GA3K/IxI,EAAE,CAAA0I,wBAAA;IAAA,EA2KgR;EAAE;AACzX;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA7KqGnJ,EAAE,CAAAoJ,iBAAA,CA6KXsC,iBAAiB,EAAc,CAAC;IAChHlE,IAAI,EAAEhH,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBe,QAAQ,EAAE,4BAA4B;MACtCG,IAAI,EAAE;QACF,WAAW,EAAE,oCAAoC;QACjD,aAAa,EAAE;MACnB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEmC,QAAQ,EAAE,CAAC;MACrEpE,IAAI,EAAElH,KAAK;MACX+I,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE1H;MAAsB,CAAC;IAC/C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA,MAAM+K,mBAAmB,CAAC;EACtBC,kBAAkBA,CAAA,EAAG;IACjB9D,SAAS,IACL+D,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,IAAI,CAAC5H,OAAO,EAAEQ,YAAY,CAAC,EAAE,2FAA2F,GAC3I,uEAAuE,CAAC;EACpF;EACA,IAAIf,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACT,MAAM,EAAES,IAAI,IAAI,GAAG;EACnC;EACA,IAAIf,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACM,MAAM,EAAEO,UAAU,IAAI,CAAC;EACvC;EACA,IAAIsI,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACnJ,KAAK,IAAI,IAAI,CAACM,MAAM,EAAEsB,EAAE,CAACwH,WAAW,IAAI,CAAC,CAAC;EAC1D;EACA;IAAS,IAAI,CAACnG,IAAI,YAAAoG,4BAAAlG,CAAA;MAAA,YAAAA,CAAA,IAAyF2F,mBAAmB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAAC1F,IAAI,kBA9M+EtH,EAAE,CAAAuH,iBAAA;MAAAC,IAAA,EA8MJwF,mBAAmB;MAAAvF,SAAA;MAAA+F,cAAA,WAAAC,mCAAA7K,EAAA,EAAAC,GAAA,EAAA6K,QAAA;QAAA,IAAA9K,EAAA;UA9MjB5C,EAAE,CAAA2N,cAAA,CAAAD,QAAA,EA8MoIpI,kBAAkB;UA9MxJtF,EAAE,CAAA2N,cAAA,CAAAD,QAAA,EA8MgO7M,SAAS;QAAA;QAAA,IAAA+B,EAAA;UAAA,IAAAgL,EAAA;UA9M3O5N,EAAE,CAAA6N,cAAA,CAAAD,EAAA,GAAF5N,EAAE,CAAA8N,WAAA,QAAAjL,GAAA,CAAA2B,MAAA,GAAAoJ,EAAA,CAAAG,KAAA;UAAF/N,EAAE,CAAA6N,cAAA,CAAAD,EAAA,GAAF5N,EAAE,CAAA8N,WAAA,QAAAjL,GAAA,CAAA2C,OAAA,GAAAoI,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAxF,UAAA;MAAAC,QAAA,GAAFxI,EAAE,CAAA2I,mBAAA;MAAAC,KAAA,EAAApG,GAAA;MAAAwL,kBAAA,EAAAtL,GAAA;MAAAmG,KAAA;MAAAC,IAAA;MAAAmF,MAAA;MAAAlF,QAAA,WAAAmF,6BAAAtL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5C,EAAE,CAAAmO,eAAA,CAAA1L,GAAA;UAAFzC,EAAE,CAAAoO,UAAA,IAAAzL,2CAAA,yBA8MiV,CAAC;UA9MpV3C,EAAE,CAAAqO,MAAA;UAAFrO,EAAE,CAAAsO,cAAA,YA8Mqe,CAAC;UA9MxetO,EAAE,CAAAuO,YAAA,EA8Myf,CAAC;UA9M5fvO,EAAE,CAAAwO,YAAA,CA8MigB,CAAC;UA9MpgBxO,EAAE,CAAAuO,YAAA,KA8MgjB,CAAC;QAAA;QAAA,IAAA3L,EAAA;UA9MnjB5C,EAAE,CAAAyO,UAAA,SAAFzO,EAAE,CAAA0O,WAAA,OAAA7L,GAAA,CAAA2C,OAAA,kBAAA3C,GAAA,CAAA2C,OAAA,CAAAQ,YAAA,CA8M4U,CAAC;UA9M/UhG,EAAE,CAAA2O,SAAA,EA8M+b,CAAC;UA9Mlc3O,EAAE,CAAAgI,WAAA,6BAAAnF,GAAA,CAAAqB,KA8M+b,CAAC,SAAArB,GAAA,CAAAwK,SAAA,MAAkC,CAAC;UA9MrerN,EAAE,CAAA+H,WAAA,cAAAlF,GAAA,CAAAoC,IAAA;QAAA;MAAA;MAAA2J,YAAA,GA8Mw9BzM,SAAS,EAA8CC,IAAI;MAAA6G,MAAA;MAAAC,eAAA;IAAA,EAAwH;EAAE;AACpvC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhNqGnJ,EAAE,CAAAoJ,iBAAA,CAgNX4D,mBAAmB,EAAc,CAAC;IAClHxF,IAAI,EAAEpH,SAAS;IACfiJ,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEe,QAAQ,EAAE,uBAAuB;MAAEuF,OAAO,EAAE,CAAC1M,SAAS,EAAEC,IAAI,CAAC;MAAE8G,eAAe,EAAE7I,uBAAuB,CAACkJ,MAAM;MAAER,QAAQ,EAAE,2RAA2R;MAAEE,MAAM,EAAE,CAAC,iXAAiX;IAAE,CAAC;EACnzB,CAAC,CAAC,QAAkB;IAAEzE,MAAM,EAAE,CAAC;MACvBgD,IAAI,EAAE9G,YAAY;MAClB2I,IAAI,EAAE,CAAC/D,kBAAkB;IAC7B,CAAC,CAAC;IAAEE,OAAO,EAAE,CAAC;MACVgC,IAAI,EAAE9G,YAAY;MAClB2I,IAAI,EAAE,CAACxI,SAAS;IACpB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiO,SAAS,GAAG,CACdxJ,kBAAkB,EAClB0H,mBAAmB,EACnBjH,qBAAqB,EACrByE,iBAAiB,EACjBkB,iBAAiB,CACpB;;AAED;AACA;AACA;;AAEA,SAAS3I,sBAAsB,EAAEiC,0BAA0B,EAAEG,kBAAkB,EAAE2J,SAAS,EAAExJ,kBAAkB,EAAEkF,iBAAiB,EAAEzE,qBAAqB,EAAE2F,iBAAiB,EAAEsB,mBAAmB,EAAEzI,4BAA4B,EAAEH,2BAA2B,EAAEV,2BAA2B,EAAE0B,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}