{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive, Input } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nclass TuiFloatingContainerStyles {\n  static {\n    this.ɵfac = function TuiFloatingContainerStyles_Factory(t) {\n      return new (t || TuiFloatingContainerStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiFloatingContainerStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-floating-container\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiFloatingContainerStyles_Template(rf, ctx) {},\n      styles: [\"[tuiFloatingContainer]{transition-property:bottom;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:sticky;bottom:calc(100 * var(--tui-viewport-vh) - var(--tui-viewport-height) - var(--tui-viewport-y));z-index:1;margin-top:1rem;padding-bottom:calc(1rem + env(safe-area-inset-bottom));text-align:center;font:var(--tui-font-text-ui-s);color:var(--tui-text-secondary)}[tuiFloatingContainer],[tuiFloatingContainer]>tui-elastic-container>.t-wrapper{display:flex;flex-direction:column;gap:.5rem}[tuiFloatingContainer]>*,[tuiFloatingContainer]>tui-elastic-container>.t-wrapper>*{inline-size:100%}[tuiFloatingContainer]:has(>*:only-child:not(tui-elastic-container)),[tuiFloatingContainer]:has(*.ng-animating:first-child~.ng-animating:last-child){padding-bottom:calc(1.5rem + env(safe-area-inset-bottom))}[tuiFloatingContainer]:before{top:0;left:0;bottom:0;right:0;transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;left:-1rem;right:-1rem;z-index:-1;background:var(--t-background, var(--tui-background-elevation-1));-webkit-mask-image:linear-gradient(180deg,transparent,black 2.5rem);mask-image:linear-gradient(180deg,transparent,black 2.5rem)}tui-sheet-dialog [tuiFloatingContainer]{margin-bottom:calc(-1.5rem - env(safe-area-inset-bottom))}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFloatingContainerStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-floating-container'\n      },\n      styles: [\"[tuiFloatingContainer]{transition-property:bottom;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:sticky;bottom:calc(100 * var(--tui-viewport-vh) - var(--tui-viewport-height) - var(--tui-viewport-y));z-index:1;margin-top:1rem;padding-bottom:calc(1rem + env(safe-area-inset-bottom));text-align:center;font:var(--tui-font-text-ui-s);color:var(--tui-text-secondary)}[tuiFloatingContainer],[tuiFloatingContainer]>tui-elastic-container>.t-wrapper{display:flex;flex-direction:column;gap:.5rem}[tuiFloatingContainer]>*,[tuiFloatingContainer]>tui-elastic-container>.t-wrapper>*{inline-size:100%}[tuiFloatingContainer]:has(>*:only-child:not(tui-elastic-container)),[tuiFloatingContainer]:has(*.ng-animating:first-child~.ng-animating:last-child){padding-bottom:calc(1.5rem + env(safe-area-inset-bottom))}[tuiFloatingContainer]:before{top:0;left:0;bottom:0;right:0;transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;left:-1rem;right:-1rem;z-index:-1;background:var(--t-background, var(--tui-background-elevation-1));-webkit-mask-image:linear-gradient(180deg,transparent,black 2.5rem);mask-image:linear-gradient(180deg,transparent,black 2.5rem)}tui-sheet-dialog [tuiFloatingContainer]{margin-bottom:calc(-1.5rem - env(safe-area-inset-bottom))}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiFloatingContainer {\n  constructor() {\n    this.nothing = tuiWithStyles(TuiFloatingContainerStyles);\n    this.background = '';\n  }\n  static {\n    this.ɵfac = function TuiFloatingContainer_Factory(t) {\n      return new (t || TuiFloatingContainer)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiFloatingContainer,\n      selectors: [[\"\", \"tuiFloatingContainer\", \"\"]],\n      hostAttrs: [\"tuiFloatingContainer\", \"\"],\n      hostVars: 2,\n      hostBindings: function TuiFloatingContainer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-background\", ctx.background);\n        }\n      },\n      inputs: {\n        background: [i0.ɵɵInputFlags.None, \"tuiFloatingContainer\", \"background\"]\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFloatingContainer, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiFloatingContainer]',\n      host: {\n        tuiFloatingContainer: '',\n        '[style.--t-background]': 'background'\n      }\n    }]\n  }], null, {\n    background: [{\n      type: Input,\n      args: ['tuiFloatingContainer']\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFloatingContainer };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Directive", "Input", "tuiWithStyles", "TuiFloatingContainerStyles", "ɵfac", "TuiFloatingContainerStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiFloatingContainerStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiFloatingContainer", "constructor", "nothing", "background", "TuiFloatingContainer_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiFloatingContainer_HostBindings", "ɵɵstyleProp", "inputs", "ɵɵInputFlags", "selector", "tuiFloatingContainer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-floating-container.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive, Input } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\n\nclass TuiFloatingContainerStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFloatingContainerStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiFloatingContainerStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-floating-container\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiFloatingContainer]{transition-property:bottom;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:sticky;bottom:calc(100 * var(--tui-viewport-vh) - var(--tui-viewport-height) - var(--tui-viewport-y));z-index:1;margin-top:1rem;padding-bottom:calc(1rem + env(safe-area-inset-bottom));text-align:center;font:var(--tui-font-text-ui-s);color:var(--tui-text-secondary)}[tuiFloatingContainer],[tuiFloatingContainer]>tui-elastic-container>.t-wrapper{display:flex;flex-direction:column;gap:.5rem}[tuiFloatingContainer]>*,[tuiFloatingContainer]>tui-elastic-container>.t-wrapper>*{inline-size:100%}[tuiFloatingContainer]:has(>*:only-child:not(tui-elastic-container)),[tuiFloatingContainer]:has(*.ng-animating:first-child~.ng-animating:last-child){padding-bottom:calc(1.5rem + env(safe-area-inset-bottom))}[tuiFloatingContainer]:before{top:0;left:0;bottom:0;right:0;transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;left:-1rem;right:-1rem;z-index:-1;background:var(--t-background, var(--tui-background-elevation-1));-webkit-mask-image:linear-gradient(180deg,transparent,black 2.5rem);mask-image:linear-gradient(180deg,transparent,black 2.5rem)}tui-sheet-dialog [tuiFloatingContainer]{margin-bottom:calc(-1.5rem - env(safe-area-inset-bottom))}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFloatingContainerStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-floating-container',\n                    }, styles: [\"[tuiFloatingContainer]{transition-property:bottom;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:sticky;bottom:calc(100 * var(--tui-viewport-vh) - var(--tui-viewport-height) - var(--tui-viewport-y));z-index:1;margin-top:1rem;padding-bottom:calc(1rem + env(safe-area-inset-bottom));text-align:center;font:var(--tui-font-text-ui-s);color:var(--tui-text-secondary)}[tuiFloatingContainer],[tuiFloatingContainer]>tui-elastic-container>.t-wrapper{display:flex;flex-direction:column;gap:.5rem}[tuiFloatingContainer]>*,[tuiFloatingContainer]>tui-elastic-container>.t-wrapper>*{inline-size:100%}[tuiFloatingContainer]:has(>*:only-child:not(tui-elastic-container)),[tuiFloatingContainer]:has(*.ng-animating:first-child~.ng-animating:last-child){padding-bottom:calc(1.5rem + env(safe-area-inset-bottom))}[tuiFloatingContainer]:before{top:0;left:0;bottom:0;right:0;transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;left:-1rem;right:-1rem;z-index:-1;background:var(--t-background, var(--tui-background-elevation-1));-webkit-mask-image:linear-gradient(180deg,transparent,black 2.5rem);mask-image:linear-gradient(180deg,transparent,black 2.5rem)}tui-sheet-dialog [tuiFloatingContainer]{margin-bottom:calc(-1.5rem - env(safe-area-inset-bottom))}\\n\"] }]\n        }] });\nclass TuiFloatingContainer {\n    constructor() {\n        this.nothing = tuiWithStyles(TuiFloatingContainerStyles);\n        this.background = '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFloatingContainer, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiFloatingContainer, isStandalone: true, selector: \"[tuiFloatingContainer]\", inputs: { background: [\"tuiFloatingContainer\", \"background\"] }, host: { attributes: { \"tuiFloatingContainer\": \"\" }, properties: { \"style.--t-background\": \"background\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFloatingContainer, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiFloatingContainer]',\n                    host: {\n                        tuiFloatingContainer: '',\n                        '[style.--t-background]': 'background',\n                    },\n                }]\n        }], propDecorators: { background: [{\n                type: Input,\n                args: ['tuiFloatingContainer']\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFloatingContainer };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACvG,SAASC,aAAa,QAAQ,mCAAmC;AAEjE,MAAMC,0BAA0B,CAAC;EAC7B;IAAS,IAAI,CAACC,IAAI,YAAAC,mCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,0BAA0B;IAAA,CAAmD;EAAE;EAC1L;IAAS,IAAI,CAACI,IAAI,kBAD+EX,EAAE,CAAAY,iBAAA;MAAAC,IAAA,EACJN,0BAA0B;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADxBjB,EAAE,CAAAkB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACgnD;EAAE;AACztD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG5B,EAAE,CAAA6B,iBAAA,CAGXtB,0BAA0B,EAAc,CAAC;IACzHM,IAAI,EAAEZ,SAAS;IACf6B,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAExB,iBAAiB,CAAC6B,IAAI;MAAEJ,eAAe,EAAExB,uBAAuB,CAAC6B,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,+1CAA+1C;IAAE,CAAC;EAC13C,CAAC,CAAC;AAAA;AACV,MAAMU,oBAAoB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG/B,aAAa,CAACC,0BAA0B,CAAC;IACxD,IAAI,CAAC+B,UAAU,GAAG,EAAE;EACxB;EACA;IAAS,IAAI,CAAC9B,IAAI,YAAA+B,6BAAA7B,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACK,IAAI,kBAf+ExC,EAAE,CAAAyC,iBAAA;MAAA5B,IAAA,EAeJsB,oBAAoB;MAAArB,SAAA;MAAAC,SAAA,2BAAwK,EAAE;MAAA2B,QAAA;MAAAC,YAAA,WAAAC,kCAAArB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAf5LvB,EAAE,CAAA6C,WAAA,mBAAArB,GAAA,CAAAc,UAee,CAAC;QAAA;MAAA;MAAAQ,MAAA;QAAAR,UAAA,GAflBtC,EAAE,CAAA+C,YAAA,CAAAhB,IAAA;MAAA;MAAAf,UAAA;IAAA,EAeqQ;EAAE;AAC9W;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAjBqG5B,EAAE,CAAA6B,iBAAA,CAiBXM,oBAAoB,EAAc,CAAC;IACnHtB,IAAI,EAAET,SAAS;IACf0B,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBgC,QAAQ,EAAE,wBAAwB;MAClCf,IAAI,EAAE;QACFgB,oBAAoB,EAAE,EAAE;QACxB,wBAAwB,EAAE;MAC9B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEX,UAAU,EAAE,CAAC;MAC3BzB,IAAI,EAAER,KAAK;MACXyB,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASK,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}