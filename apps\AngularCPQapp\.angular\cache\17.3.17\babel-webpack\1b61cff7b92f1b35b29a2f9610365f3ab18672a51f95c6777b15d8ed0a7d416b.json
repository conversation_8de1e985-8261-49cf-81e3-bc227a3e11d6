{"ast": null, "code": "import { Async<PERSON><PERSON><PERSON>, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, EventEmitter, ElementRef, Component, ChangeDetectionStrategy, ViewChildren, Input, Output } from '@angular/core';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { TuiLet } from '@taiga-ui/cdk/directives/let';\nimport { TuiRepeatTimes } from '@taiga-ui/cdk/directives/repeat-times';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiIsNativeFocusedIn } from '@taiga-ui/cdk/utils/focus';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { TUI_SPIN_ICONS } from '@taiga-ui/core/tokens';\nimport { TUI_PAGINATION_TEXTS } from '@taiga-ui/kit/tokens';\nimport { PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nconst _c0 = [\"element\"];\nconst _c1 = a0 => ({\n  $implicit: a0\n});\nfunction TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_button_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r6 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r6, \" \");\n  }\n}\nfunction TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10, 2);\n    i0.ɵɵlistener(\"click\", function TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const index_r4 = i0.ɵɵnextContext().tuiLet;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onElementClick(index_r4));\n    })(\"keydown.arrowLeft.prevent\", function TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_button_1_Template_button_keydown_arrowLeft_prevent_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const element_r5 = i0.ɵɵreference(1);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.onElementKeyDownArrowLeft(element_r5));\n    })(\"keydown.arrowRight.prevent\", function TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_button_1_Template_button_keydown_arrowRight_prevent_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const element_r5 = i0.ɵɵreference(1);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.onElementKeyDownArrowRight(element_r5));\n    });\n    i0.ɵɵtemplate(2, TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_button_1_ng_container_2_Template, 2, 1, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const index_r4 = i0.ɵɵnextContext().tuiLet;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"appearance\", ctx_r1.getElementMode(index_r4))(\"disabled\", ctx_r1.disabled)(\"size\", ctx_r1.buttonSize)(\"tabIndex\", ctx_r1.elementIsFocusable(index_r4) ? 0 : -1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r1.content || index_r4 + 1)(\"polymorpheusOutletContext\", i0.ɵɵpureFunction1(6, _c1, index_r4));\n  }\n}\nfunction TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_button_1_Template, 3, 8, \"button\", 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const index_r4 = ctx.tuiLet;\n    i0.ɵɵnextContext(4);\n    const dotsTemplate_r7 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", index_r4 !== null)(\"ngIfElse\", dotsTemplate_r7);\n  }\n}\nfunction TuiPagination_ng_container_1_ng_container_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_Template, 2, 2, \"ng-container\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const elementIndex_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tuiLet\", ctx_r1.getItemIndexByElementIndex(elementIndex_r8));\n  }\n}\nfunction TuiPagination_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function TuiPagination_ng_container_1_ng_container_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onArrowClick(\"left\"));\n    })(\"mousedown.zoneless.prevent\", function TuiPagination_ng_container_1_ng_container_1_Template_button_mousedown_zoneless_prevent_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      return i0.ɵɵresetView(0);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TuiPagination_ng_container_1_ng_container_1_ng_container_3_Template, 2, 1, \"ng-container\", 7);\n    i0.ɵɵelementStart(4, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function TuiPagination_ng_container_1_ng_container_1_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onArrowClick(\"right\"));\n    })(\"mousedown.zoneless.prevent\", function TuiPagination_ng_container_1_ng_container_1_Template_button_mousedown_zoneless_prevent_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      return i0.ɵɵresetView(0);\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const texts_r9 = ctx.ngIf;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.arrowIsDisabledLeft)(\"iconStart\", ctx_r1.icons.decrement)(\"size\", ctx_r1.buttonSize);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", texts_r9[0], \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tuiRepeatTimesOf\", ctx_r1.elementsLength);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.arrowIsDisabledRight)(\"iconStart\", ctx_r1.icons.increment)(\"size\", ctx_r1.buttonSize);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", texts_r9[1], \" \");\n  }\n}\nfunction TuiPagination_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiPagination_ng_container_1_ng_container_1_Template, 6, 9, \"ng-container\", 5);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(2, 1, ctx_r1.texts$));\n  }\n}\nfunction TuiPagination_ng_template_2_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13, 2);\n    i0.ɵɵlistener(\"click\", function TuiPagination_ng_template_2_button_0_Template_button_click_0_listener() {\n      const indexItem_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onElementClick(indexItem_r11));\n    })(\"keydown.arrowLeft.prevent\", function TuiPagination_ng_template_2_button_0_Template_button_keydown_arrowLeft_prevent_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const element_r12 = i0.ɵɵreference(1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onElementKeyDownArrowLeft(element_r12));\n    })(\"keydown.arrowRight.prevent\", function TuiPagination_ng_template_2_button_0_Template_button_keydown_arrowRight_prevent_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const element_r12 = i0.ɵɵreference(1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onElementKeyDownArrowRight(element_r12));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const indexItem_r11 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"appearance\", ctx_r1.getElementMode(indexItem_r11))(\"disabled\", ctx_r1.disabled)(\"tabIndex\", ctx_r1.elementIsFocusable(indexItem_r11) ? 0 : -1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", indexItem_r11 + 1, \" \");\n  }\n}\nfunction TuiPagination_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiPagination_ng_template_2_button_0_Template, 3, 4, \"button\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"tuiRepeatTimesOf\", ctx_r1.length);\n  }\n}\nfunction TuiPagination_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"t-dots_small\", ctx_r1.size === \"m\");\n  }\n}\nconst DOTS_LENGTH = 1;\nconst ACTIVE_ITEM_LENGTH = 1;\nclass TuiPagination {\n  constructor() {\n    this.els = EMPTY_QUERY;\n    this.el = tuiInjectElement();\n    this.texts$ = inject(TUI_PAGINATION_TEXTS);\n    this.icons = inject(TUI_SPIN_ICONS);\n    this.length = 1;\n    this.focusable = true;\n    this.size = 'l';\n    this.disabled = false;\n    /**\n     * Amount of visible pages around active page\n     */\n    this.activePadding = 1;\n    /**\n     * Amount of visible pages at the edges\n     */\n    this.sidePadding = 1;\n    /**\n     * Active page index\n     */\n    this.index = 0;\n    this.indexChange = new EventEmitter();\n  }\n  get nativeFocusableElement() {\n    if (this.disabled) {\n      return null;\n    }\n    let activeElementIndex = 0;\n    const {\n      elementsLength\n    } = this;\n    for (let i = 0; i < elementsLength; i++) {\n      const itemIndex = this.getItemIndexByElementIndex(i);\n      if (itemIndex) {\n        activeElementIndex++;\n      }\n      if (itemIndex === this.index) {\n        break;\n      }\n    }\n    return this.els.find((_, index) => index === activeElementIndex)?.nativeElement ?? null;\n  }\n  get focused() {\n    return tuiIsNativeFocusedIn(this.el);\n  }\n  get arrowIsDisabledLeft() {\n    return this.index === 0;\n  }\n  get arrowIsDisabledRight() {\n    return this.reverseIndex === 0;\n  }\n  /**\n   * Number of items in a container.\n   */\n  get elementsLength() {\n    return this.itemsFit ? this.length : this.maxElementsLength;\n  }\n  get buttonSize() {\n    return this.size === 'm' ? 'xs' : 's';\n  }\n  elementIsFocusable(index) {\n    return this.index === index && !this.focused;\n  }\n  /**\n   * Get index by element index\n   * @param elementIndex\n   * @returns index or null (for '…')\n   */\n  getItemIndexByElementIndex(elementIndex) {\n    if (this.size === 's') {\n      return elementIndex;\n    }\n    if (elementIndex < this.sidePadding) {\n      return elementIndex;\n    }\n    if (elementIndex === this.sidePadding && this.hasCollapsedItems(this.index)) {\n      return null;\n    }\n    const reverseElementIndex = this.lastElementIndex - elementIndex;\n    if (reverseElementIndex === this.sidePadding && this.hasCollapsedItems(this.reverseIndex)) {\n      return null;\n    }\n    if (reverseElementIndex < this.sidePadding) {\n      return this.lastIndex - reverseElementIndex;\n    }\n    const computedIndex = this.index - this.maxHalfLength + elementIndex;\n    return tuiClamp(computedIndex, elementIndex, this.lastIndex - reverseElementIndex);\n  }\n  getElementMode(index) {\n    const fallback = this.size === 's' ? 'secondary' : 'flat';\n    return this.index === index ? 'primary' : fallback;\n  }\n  onElementClick(index) {\n    this.updateIndex(index);\n  }\n  onElementKeyDownArrowLeft(element) {\n    if (element === this.els.first.nativeElement) {\n      return;\n    }\n    const previous = this.els.find((_, index, array) => array[index + 1]?.nativeElement === element);\n    previous?.nativeElement.focus();\n  }\n  onElementKeyDownArrowRight(element) {\n    if (element === this.els.last.nativeElement) {\n      return;\n    }\n    const next = this.els.find((_, index, array) => array[index - 1]?.nativeElement === element);\n    next?.nativeElement.focus();\n  }\n  onArrowClick(direction) {\n    this.tryChangeTo(direction);\n    this.focusActive();\n  }\n  /**\n   * Active index from the end\n   */\n  get reverseIndex() {\n    return this.lastIndex - this.index;\n  }\n  /**\n   * Max number of elements in half (not counting the middle one).\n   */\n  get maxHalfLength() {\n    return this.sidePadding + DOTS_LENGTH + this.activePadding;\n  }\n  /**\n   * Is there '...' anywhere\n   */\n  get itemsFit() {\n    return this.length <= this.maxElementsLength;\n  }\n  /**\n   * Max number of elements\n   */\n  get maxElementsLength() {\n    return this.maxHalfLength * 2 + ACTIVE_ITEM_LENGTH;\n  }\n  get lastIndex() {\n    return this.length - 1;\n  }\n  get lastElementIndex() {\n    return this.elementsLength - 1;\n  }\n  /**\n   * Are there collapsed items at that index\n   * @param index\n   * @returns there are collapsed items\n   */\n  hasCollapsedItems(index) {\n    return !this.itemsFit && index > this.maxHalfLength;\n  }\n  tryChangeTo(direction) {\n    this.updateIndex(tuiClamp(this.index + (direction === 'right' ? 1 : -1), 0, this.lastIndex));\n  }\n  focusActive() {\n    const {\n      nativeFocusableElement\n    } = this;\n    if (nativeFocusableElement) {\n      nativeFocusableElement.focus();\n    }\n  }\n  updateIndex(index) {\n    if (this.index === index) {\n      return;\n    }\n    this.index = index;\n    this.indexChange.emit(index);\n  }\n  static {\n    this.ɵfac = function TuiPagination_Factory(t) {\n      return new (t || TuiPagination)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiPagination,\n      selectors: [[\"tui-pagination\"]],\n      viewQuery: function TuiPagination_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.els = _t);\n        }\n      },\n      inputs: {\n        length: \"length\",\n        focusable: \"focusable\",\n        size: \"size\",\n        disabled: \"disabled\",\n        activePadding: \"activePadding\",\n        sidePadding: \"sidePadding\",\n        content: \"content\",\n        index: \"index\"\n      },\n      outputs: {\n        indexChange: \"indexChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 2,\n      consts: [[\"smallButtons\", \"\"], [\"dotsTemplate\", \"\"], [\"element\", \"\"], [1, \"t-content\"], [4, \"ngIf\", \"ngIfElse\"], [4, \"ngIf\"], [\"appearance\", \"flat\", \"tabIndex\", \"-1\", \"tuiIconButton\", \"\", \"type\", \"button\", 1, \"t-button\", 3, \"click\", \"mousedown.zoneless.prevent\", \"disabled\", \"iconStart\", \"size\"], [4, \"tuiRepeatTimes\", \"tuiRepeatTimesOf\"], [4, \"tuiLet\"], [\"automation-id\", \"tui-pagination__element\", \"tuiButton\", \"\", \"type\", \"button\", \"class\", \"t-button\", 3, \"appearance\", \"disabled\", \"size\", \"tabIndex\", \"click\", \"keydown.arrowLeft.prevent\", \"keydown.arrowRight.prevent\", 4, \"ngIf\", \"ngIfElse\"], [\"automation-id\", \"tui-pagination__element\", \"tuiButton\", \"\", \"type\", \"button\", 1, \"t-button\", 3, \"click\", \"keydown.arrowLeft.prevent\", \"keydown.arrowRight.prevent\", \"appearance\", \"disabled\", \"size\", \"tabIndex\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [\"tuiButton\", \"\", \"type\", \"button\", \"class\", \"t-button t-button_small\", 3, \"appearance\", \"disabled\", \"tabIndex\", \"click\", \"keydown.arrowLeft.prevent\", \"keydown.arrowRight.prevent\", 4, \"tuiRepeatTimes\", \"tuiRepeatTimesOf\"], [\"tuiButton\", \"\", \"type\", \"button\", 1, \"t-button\", \"t-button_small\", 3, \"click\", \"keydown.arrowLeft.prevent\", \"keydown.arrowRight.prevent\", \"appearance\", \"disabled\", \"tabIndex\"], [\"automation-id\", \"tui-pagination__element\", 1, \"t-dots\"]],\n      template: function TuiPagination_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 3);\n          i0.ɵɵtemplate(1, TuiPagination_ng_container_1_Template, 3, 3, \"ng-container\", 4)(2, TuiPagination_ng_template_2_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, TuiPagination_ng_template_4_Template, 1, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const smallButtons_r13 = i0.ɵɵreference(3);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.size !== \"s\")(\"ngIfElse\", smallButtons_r13);\n        }\n      },\n      dependencies: [AsyncPipe, NgIf, PolymorpheusOutlet, TuiButton, TuiLet, TuiRepeatTimes],\n      styles: [\"[_nghost-%COMP%]{display:block;font:var(--tui-font-text-s);color:var(--tui-text-primary);text-align:center}.t-content[_ngcontent-%COMP%]{display:flex;justify-content:center}.t-button[_ngcontent-%COMP%]{inline-size:var(--tui-height-s);margin:0 .125rem;flex-shrink:0}.t-button[_ngcontent-%COMP%]:first-child{margin-inline-start:0}.t-button[_ngcontent-%COMP%]:last-child{margin-inline-end:0}.t-button[data-size=xs][_ngcontent-%COMP%]{inline-size:var(--tui-height-xs)}.t-button.t-button.t-button_small[_ngcontent-%COMP%]{inline-size:.5rem;block-size:.5rem;font-size:0;padding:0;margin:0}.t-button.t-button.t-button_small[_ngcontent-%COMP%]:not(:first-child){margin-left:.5rem}.t-dots[_ngcontent-%COMP%]{inline-size:var(--tui-height-s);block-size:var(--tui-height-s);line-height:var(--tui-height-s);margin:0 .125rem;flex-shrink:0;color:var(--tui-text-action);text-align:center;cursor:default}.t-dots_small[_ngcontent-%COMP%]{inline-size:var(--tui-height-xs);block-size:var(--tui-height-xs);line-height:var(--tui-height-xs)}.t-dots[_ngcontent-%COMP%]:before{content:\\\"\\\\2026\\\"}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPagination, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-pagination',\n      imports: [AsyncPipe, NgIf, PolymorpheusOutlet, TuiButton, TuiLet, TuiRepeatTimes],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"t-content\\\">\\n    <ng-container *ngIf=\\\"size !== 's'; else smallButtons\\\">\\n        <ng-container *ngIf=\\\"texts$ | async as texts\\\">\\n            <button\\n                appearance=\\\"flat\\\"\\n                tabIndex=\\\"-1\\\"\\n                tuiIconButton\\n                type=\\\"button\\\"\\n                class=\\\"t-button\\\"\\n                [disabled]=\\\"arrowIsDisabledLeft\\\"\\n                [iconStart]=\\\"icons.decrement\\\"\\n                [size]=\\\"buttonSize\\\"\\n                (click)=\\\"onArrowClick('left')\\\"\\n                (mousedown.zoneless.prevent)=\\\"(0)\\\"\\n            >\\n                {{ texts[0] }}\\n            </button>\\n            <ng-container *tuiRepeatTimes=\\\"let elementIndex of elementsLength\\\">\\n                <ng-container *tuiLet=\\\"getItemIndexByElementIndex(elementIndex) as index\\\">\\n                    <button\\n                        *ngIf=\\\"index !== null; else dotsTemplate\\\"\\n                        #element\\n                        automation-id=\\\"tui-pagination__element\\\"\\n                        tuiButton\\n                        type=\\\"button\\\"\\n                        class=\\\"t-button\\\"\\n                        [appearance]=\\\"getElementMode(index)\\\"\\n                        [disabled]=\\\"disabled\\\"\\n                        [size]=\\\"buttonSize\\\"\\n                        [tabIndex]=\\\"elementIsFocusable(index) ? 0 : -1\\\"\\n                        (click)=\\\"onElementClick(index)\\\"\\n                        (keydown.arrowLeft.prevent)=\\\"onElementKeyDownArrowLeft(element)\\\"\\n                        (keydown.arrowRight.prevent)=\\\"onElementKeyDownArrowRight(element)\\\"\\n                    >\\n                        <ng-container *polymorpheusOutlet=\\\"content || index + 1 as text; context: {$implicit: index}\\\">\\n                            {{ text }}\\n                        </ng-container>\\n                    </button>\\n                </ng-container>\\n            </ng-container>\\n            <button\\n                appearance=\\\"flat\\\"\\n                tabIndex=\\\"-1\\\"\\n                tuiIconButton\\n                type=\\\"button\\\"\\n                class=\\\"t-button\\\"\\n                [disabled]=\\\"arrowIsDisabledRight\\\"\\n                [iconStart]=\\\"icons.increment\\\"\\n                [size]=\\\"buttonSize\\\"\\n                (click)=\\\"onArrowClick('right')\\\"\\n                (mousedown.zoneless.prevent)=\\\"(0)\\\"\\n            >\\n                {{ texts[1] }}\\n            </button>\\n        </ng-container>\\n    </ng-container>\\n    <ng-template #smallButtons>\\n        <button\\n            *tuiRepeatTimes=\\\"let indexItem of length\\\"\\n            #element\\n            tuiButton\\n            type=\\\"button\\\"\\n            class=\\\"t-button t-button_small\\\"\\n            [appearance]=\\\"getElementMode(indexItem)\\\"\\n            [disabled]=\\\"disabled\\\"\\n            [tabIndex]=\\\"elementIsFocusable(indexItem) ? 0 : -1\\\"\\n            (click)=\\\"onElementClick(indexItem)\\\"\\n            (keydown.arrowLeft.prevent)=\\\"onElementKeyDownArrowLeft(element)\\\"\\n            (keydown.arrowRight.prevent)=\\\"onElementKeyDownArrowRight(element)\\\"\\n        >\\n            {{ indexItem + 1 }}\\n        </button>\\n    </ng-template>\\n    <ng-template #dotsTemplate>\\n        <div\\n            automation-id=\\\"tui-pagination__element\\\"\\n            class=\\\"t-dots\\\"\\n            [class.t-dots_small]=\\\"size === 'm'\\\"\\n        ></div>\\n    </ng-template>\\n</div>\\n\",\n      styles: [\":host{display:block;font:var(--tui-font-text-s);color:var(--tui-text-primary);text-align:center}.t-content{display:flex;justify-content:center}.t-button{inline-size:var(--tui-height-s);margin:0 .125rem;flex-shrink:0}.t-button:first-child{margin-inline-start:0}.t-button:last-child{margin-inline-end:0}.t-button[data-size=xs]{inline-size:var(--tui-height-xs)}.t-button.t-button.t-button_small{inline-size:.5rem;block-size:.5rem;font-size:0;padding:0;margin:0}.t-button.t-button.t-button_small:not(:first-child){margin-left:.5rem}.t-dots{inline-size:var(--tui-height-s);block-size:var(--tui-height-s);line-height:var(--tui-height-s);margin:0 .125rem;flex-shrink:0;color:var(--tui-text-action);text-align:center;cursor:default}.t-dots_small{inline-size:var(--tui-height-xs);block-size:var(--tui-height-xs);line-height:var(--tui-height-xs)}.t-dots:before{content:\\\"\\\\2026\\\"}\\n\"]\n    }]\n  }], null, {\n    els: [{\n      type: ViewChildren,\n      args: ['element', {\n        read: ElementRef\n      }]\n    }],\n    length: [{\n      type: Input\n    }],\n    focusable: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    activePadding: [{\n      type: Input\n    }],\n    sidePadding: [{\n      type: Input\n    }],\n    content: [{\n      type: Input\n    }],\n    index: [{\n      type: Input\n    }],\n    indexChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPagination };", "map": {"version": 3, "names": ["AsyncPipe", "NgIf", "i0", "inject", "EventEmitter", "ElementRef", "Component", "ChangeDetectionStrategy", "ViewChildren", "Input", "Output", "EMPTY_QUERY", "TuiLet", "TuiRepeatTimes", "tuiInjectElement", "tuiIsNativeFocusedIn", "tui<PERSON><PERSON>", "TuiButton", "TUI_SPIN_ICONS", "TUI_PAGINATION_TEXTS", "Polymorpheus<PERSON><PERSON>let", "_c0", "_c1", "a0", "$implicit", "TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_button_1_ng_container_2_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "text_r6", "polymorpheusOutlet", "ɵɵadvance", "ɵɵtextInterpolate1", "TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_button_1_Template", "_r3", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_button_1_Template_button_click_0_listener", "ɵɵrestoreView", "index_r4", "ɵɵnextContext", "tuiLet", "ctx_r1", "ɵɵresetView", "onElementClick", "TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_button_1_Template_button_keydown_arrowLeft_prevent_0_listener", "element_r5", "ɵɵreference", "onElementKeyDownArrowLeft", "TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_button_1_Template_button_keydown_arrowRight_prevent_0_listener", "onElementKeyDownArrowRight", "ɵɵtemplate", "ɵɵelementEnd", "ɵɵproperty", "getElementMode", "disabled", "buttonSize", "elementIsFocusable", "content", "ɵɵpureFunction1", "TuiPagination_ng_container_1_ng_container_1_ng_container_3_ng_container_1_Template", "dotsTemplate_r7", "TuiPagination_ng_container_1_ng_container_1_ng_container_3_Template", "elementIndex_r8", "getItemIndexByElementIndex", "TuiPagination_ng_container_1_ng_container_1_Template", "_r1", "TuiPagination_ng_container_1_ng_container_1_Template_button_click_1_listener", "onArrowClick", "TuiPagination_ng_container_1_ng_container_1_Template_button_mousedown_zoneless_prevent_1_listener", "TuiPagination_ng_container_1_ng_container_1_Template_button_click_4_listener", "TuiPagination_ng_container_1_ng_container_1_Template_button_mousedown_zoneless_prevent_4_listener", "texts_r9", "ngIf", "arrowIsDisabledLeft", "icons", "decrement", "elements<PERSON>ength", "arrowIsDisabledRight", "increment", "TuiPagination_ng_container_1_Template", "ɵɵpipe", "ɵɵpipeBind1", "texts$", "TuiPagination_ng_template_2_button_0_Template", "_r10", "TuiPagination_ng_template_2_button_0_Template_button_click_0_listener", "indexItem_r11", "TuiPagination_ng_template_2_button_0_Template_button_keydown_arrowLeft_prevent_0_listener", "element_r12", "TuiPagination_ng_template_2_button_0_Template_button_keydown_arrowRight_prevent_0_listener", "TuiPagination_ng_template_2_Template", "length", "TuiPagination_ng_template_4_Template", "ɵɵelement", "ɵɵclassProp", "size", "DOTS_LENGTH", "ACTIVE_ITEM_LENGTH", "TuiPagination", "constructor", "els", "el", "focusable", "activePadding", "sidePadding", "index", "indexChange", "nativeFocusableElement", "activeElementIndex", "i", "itemIndex", "find", "_", "nativeElement", "focused", "reverseIndex", "itemsFit", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elementIndex", "hasCollapsedItems", "reverseElementIndex", "lastElementIndex", "lastIndex", "computedIndex", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fallback", "updateIndex", "element", "first", "previous", "array", "focus", "last", "next", "direction", "tryChangeTo", "focusActive", "emit", "ɵfac", "TuiPagination_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "TuiPagination_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiPagination_Template", "ɵɵtemplateRefExtractor", "smallButtons_r13", "dependencies", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "read"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-pagination.mjs"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON><PERSON>, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, EventEmitter, ElementRef, Component, ChangeDetectionStrategy, ViewChildren, Input, Output } from '@angular/core';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { TuiLet } from '@taiga-ui/cdk/directives/let';\nimport { TuiRepeatTimes } from '@taiga-ui/cdk/directives/repeat-times';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiIsNativeFocusedIn } from '@taiga-ui/cdk/utils/focus';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { TUI_SPIN_ICONS } from '@taiga-ui/core/tokens';\nimport { TUI_PAGINATION_TEXTS } from '@taiga-ui/kit/tokens';\nimport { PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\n\nconst DOTS_LENGTH = 1;\nconst ACTIVE_ITEM_LENGTH = 1;\nclass TuiPagination {\n    constructor() {\n        this.els = EMPTY_QUERY;\n        this.el = tuiInjectElement();\n        this.texts$ = inject(TUI_PAGINATION_TEXTS);\n        this.icons = inject(TUI_SPIN_ICONS);\n        this.length = 1;\n        this.focusable = true;\n        this.size = 'l';\n        this.disabled = false;\n        /**\n         * Amount of visible pages around active page\n         */\n        this.activePadding = 1;\n        /**\n         * Amount of visible pages at the edges\n         */\n        this.sidePadding = 1;\n        /**\n         * Active page index\n         */\n        this.index = 0;\n        this.indexChange = new EventEmitter();\n    }\n    get nativeFocusableElement() {\n        if (this.disabled) {\n            return null;\n        }\n        let activeElementIndex = 0;\n        const { elementsLength } = this;\n        for (let i = 0; i < elementsLength; i++) {\n            const itemIndex = this.getItemIndexByElementIndex(i);\n            if (itemIndex) {\n                activeElementIndex++;\n            }\n            if (itemIndex === this.index) {\n                break;\n            }\n        }\n        return (this.els.find((_, index) => index === activeElementIndex)?.nativeElement ??\n            null);\n    }\n    get focused() {\n        return tuiIsNativeFocusedIn(this.el);\n    }\n    get arrowIsDisabledLeft() {\n        return this.index === 0;\n    }\n    get arrowIsDisabledRight() {\n        return this.reverseIndex === 0;\n    }\n    /**\n     * Number of items in a container.\n     */\n    get elementsLength() {\n        return this.itemsFit ? this.length : this.maxElementsLength;\n    }\n    get buttonSize() {\n        return this.size === 'm' ? 'xs' : 's';\n    }\n    elementIsFocusable(index) {\n        return this.index === index && !this.focused;\n    }\n    /**\n     * Get index by element index\n     * @param elementIndex\n     * @returns index or null (for '…')\n     */\n    getItemIndexByElementIndex(elementIndex) {\n        if (this.size === 's') {\n            return elementIndex;\n        }\n        if (elementIndex < this.sidePadding) {\n            return elementIndex;\n        }\n        if (elementIndex === this.sidePadding && this.hasCollapsedItems(this.index)) {\n            return null;\n        }\n        const reverseElementIndex = this.lastElementIndex - elementIndex;\n        if (reverseElementIndex === this.sidePadding &&\n            this.hasCollapsedItems(this.reverseIndex)) {\n            return null;\n        }\n        if (reverseElementIndex < this.sidePadding) {\n            return this.lastIndex - reverseElementIndex;\n        }\n        const computedIndex = this.index - this.maxHalfLength + elementIndex;\n        return tuiClamp(computedIndex, elementIndex, this.lastIndex - reverseElementIndex);\n    }\n    getElementMode(index) {\n        const fallback = this.size === 's' ? 'secondary' : 'flat';\n        return this.index === index ? 'primary' : fallback;\n    }\n    onElementClick(index) {\n        this.updateIndex(index);\n    }\n    onElementKeyDownArrowLeft(element) {\n        if (element === this.els.first.nativeElement) {\n            return;\n        }\n        const previous = this.els.find((_, index, array) => array[index + 1]?.nativeElement === element);\n        previous?.nativeElement.focus();\n    }\n    onElementKeyDownArrowRight(element) {\n        if (element === this.els.last.nativeElement) {\n            return;\n        }\n        const next = this.els.find((_, index, array) => array[index - 1]?.nativeElement === element);\n        next?.nativeElement.focus();\n    }\n    onArrowClick(direction) {\n        this.tryChangeTo(direction);\n        this.focusActive();\n    }\n    /**\n     * Active index from the end\n     */\n    get reverseIndex() {\n        return this.lastIndex - this.index;\n    }\n    /**\n     * Max number of elements in half (not counting the middle one).\n     */\n    get maxHalfLength() {\n        return this.sidePadding + DOTS_LENGTH + this.activePadding;\n    }\n    /**\n     * Is there '...' anywhere\n     */\n    get itemsFit() {\n        return this.length <= this.maxElementsLength;\n    }\n    /**\n     * Max number of elements\n     */\n    get maxElementsLength() {\n        return this.maxHalfLength * 2 + ACTIVE_ITEM_LENGTH;\n    }\n    get lastIndex() {\n        return this.length - 1;\n    }\n    get lastElementIndex() {\n        return this.elementsLength - 1;\n    }\n    /**\n     * Are there collapsed items at that index\n     * @param index\n     * @returns there are collapsed items\n     */\n    hasCollapsedItems(index) {\n        return !this.itemsFit && index > this.maxHalfLength;\n    }\n    tryChangeTo(direction) {\n        this.updateIndex(tuiClamp(this.index + (direction === 'right' ? 1 : -1), 0, this.lastIndex));\n    }\n    focusActive() {\n        const { nativeFocusableElement } = this;\n        if (nativeFocusableElement) {\n            nativeFocusableElement.focus();\n        }\n    }\n    updateIndex(index) {\n        if (this.index === index) {\n            return;\n        }\n        this.index = index;\n        this.indexChange.emit(index);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPagination, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPagination, isStandalone: true, selector: \"tui-pagination\", inputs: { length: \"length\", focusable: \"focusable\", size: \"size\", disabled: \"disabled\", activePadding: \"activePadding\", sidePadding: \"sidePadding\", content: \"content\", index: \"index\" }, outputs: { indexChange: \"indexChange\" }, viewQueries: [{ propertyName: \"els\", predicate: [\"element\"], descendants: true, read: ElementRef }], ngImport: i0, template: \"<div class=\\\"t-content\\\">\\n    <ng-container *ngIf=\\\"size !== 's'; else smallButtons\\\">\\n        <ng-container *ngIf=\\\"texts$ | async as texts\\\">\\n            <button\\n                appearance=\\\"flat\\\"\\n                tabIndex=\\\"-1\\\"\\n                tuiIconButton\\n                type=\\\"button\\\"\\n                class=\\\"t-button\\\"\\n                [disabled]=\\\"arrowIsDisabledLeft\\\"\\n                [iconStart]=\\\"icons.decrement\\\"\\n                [size]=\\\"buttonSize\\\"\\n                (click)=\\\"onArrowClick('left')\\\"\\n                (mousedown.zoneless.prevent)=\\\"(0)\\\"\\n            >\\n                {{ texts[0] }}\\n            </button>\\n            <ng-container *tuiRepeatTimes=\\\"let elementIndex of elementsLength\\\">\\n                <ng-container *tuiLet=\\\"getItemIndexByElementIndex(elementIndex) as index\\\">\\n                    <button\\n                        *ngIf=\\\"index !== null; else dotsTemplate\\\"\\n                        #element\\n                        automation-id=\\\"tui-pagination__element\\\"\\n                        tuiButton\\n                        type=\\\"button\\\"\\n                        class=\\\"t-button\\\"\\n                        [appearance]=\\\"getElementMode(index)\\\"\\n                        [disabled]=\\\"disabled\\\"\\n                        [size]=\\\"buttonSize\\\"\\n                        [tabIndex]=\\\"elementIsFocusable(index) ? 0 : -1\\\"\\n                        (click)=\\\"onElementClick(index)\\\"\\n                        (keydown.arrowLeft.prevent)=\\\"onElementKeyDownArrowLeft(element)\\\"\\n                        (keydown.arrowRight.prevent)=\\\"onElementKeyDownArrowRight(element)\\\"\\n                    >\\n                        <ng-container *polymorpheusOutlet=\\\"content || index + 1 as text; context: {$implicit: index}\\\">\\n                            {{ text }}\\n                        </ng-container>\\n                    </button>\\n                </ng-container>\\n            </ng-container>\\n            <button\\n                appearance=\\\"flat\\\"\\n                tabIndex=\\\"-1\\\"\\n                tuiIconButton\\n                type=\\\"button\\\"\\n                class=\\\"t-button\\\"\\n                [disabled]=\\\"arrowIsDisabledRight\\\"\\n                [iconStart]=\\\"icons.increment\\\"\\n                [size]=\\\"buttonSize\\\"\\n                (click)=\\\"onArrowClick('right')\\\"\\n                (mousedown.zoneless.prevent)=\\\"(0)\\\"\\n            >\\n                {{ texts[1] }}\\n            </button>\\n        </ng-container>\\n    </ng-container>\\n    <ng-template #smallButtons>\\n        <button\\n            *tuiRepeatTimes=\\\"let indexItem of length\\\"\\n            #element\\n            tuiButton\\n            type=\\\"button\\\"\\n            class=\\\"t-button t-button_small\\\"\\n            [appearance]=\\\"getElementMode(indexItem)\\\"\\n            [disabled]=\\\"disabled\\\"\\n            [tabIndex]=\\\"elementIsFocusable(indexItem) ? 0 : -1\\\"\\n            (click)=\\\"onElementClick(indexItem)\\\"\\n            (keydown.arrowLeft.prevent)=\\\"onElementKeyDownArrowLeft(element)\\\"\\n            (keydown.arrowRight.prevent)=\\\"onElementKeyDownArrowRight(element)\\\"\\n        >\\n            {{ indexItem + 1 }}\\n        </button>\\n    </ng-template>\\n    <ng-template #dotsTemplate>\\n        <div\\n            automation-id=\\\"tui-pagination__element\\\"\\n            class=\\\"t-dots\\\"\\n            [class.t-dots_small]=\\\"size === 'm'\\\"\\n        ></div>\\n    </ng-template>\\n</div>\\n\", styles: [\":host{display:block;font:var(--tui-font-text-s);color:var(--tui-text-primary);text-align:center}.t-content{display:flex;justify-content:center}.t-button{inline-size:var(--tui-height-s);margin:0 .125rem;flex-shrink:0}.t-button:first-child{margin-inline-start:0}.t-button:last-child{margin-inline-end:0}.t-button[data-size=xs]{inline-size:var(--tui-height-xs)}.t-button.t-button.t-button_small{inline-size:.5rem;block-size:.5rem;font-size:0;padding:0;margin:0}.t-button.t-button.t-button_small:not(:first-child){margin-left:.5rem}.t-dots{inline-size:var(--tui-height-s);block-size:var(--tui-height-s);line-height:var(--tui-height-s);margin:0 .125rem;flex-shrink:0;color:var(--tui-text-action);text-align:center;cursor:default}.t-dots_small{inline-size:var(--tui-height-xs);block-size:var(--tui-height-xs);line-height:var(--tui-height-xs)}.t-dots:before{content:\\\"\\\\2026\\\"}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"directive\", type: TuiLet, selector: \"[tuiLet]\", inputs: [\"tuiLet\"] }, { kind: \"directive\", type: TuiRepeatTimes, selector: \"[tuiRepeatTimes][tuiRepeatTimesOf]\", inputs: [\"tuiRepeatTimesOf\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPagination, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-pagination', imports: [AsyncPipe, NgIf, PolymorpheusOutlet, TuiButton, TuiLet, TuiRepeatTimes], changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div class=\\\"t-content\\\">\\n    <ng-container *ngIf=\\\"size !== 's'; else smallButtons\\\">\\n        <ng-container *ngIf=\\\"texts$ | async as texts\\\">\\n            <button\\n                appearance=\\\"flat\\\"\\n                tabIndex=\\\"-1\\\"\\n                tuiIconButton\\n                type=\\\"button\\\"\\n                class=\\\"t-button\\\"\\n                [disabled]=\\\"arrowIsDisabledLeft\\\"\\n                [iconStart]=\\\"icons.decrement\\\"\\n                [size]=\\\"buttonSize\\\"\\n                (click)=\\\"onArrowClick('left')\\\"\\n                (mousedown.zoneless.prevent)=\\\"(0)\\\"\\n            >\\n                {{ texts[0] }}\\n            </button>\\n            <ng-container *tuiRepeatTimes=\\\"let elementIndex of elementsLength\\\">\\n                <ng-container *tuiLet=\\\"getItemIndexByElementIndex(elementIndex) as index\\\">\\n                    <button\\n                        *ngIf=\\\"index !== null; else dotsTemplate\\\"\\n                        #element\\n                        automation-id=\\\"tui-pagination__element\\\"\\n                        tuiButton\\n                        type=\\\"button\\\"\\n                        class=\\\"t-button\\\"\\n                        [appearance]=\\\"getElementMode(index)\\\"\\n                        [disabled]=\\\"disabled\\\"\\n                        [size]=\\\"buttonSize\\\"\\n                        [tabIndex]=\\\"elementIsFocusable(index) ? 0 : -1\\\"\\n                        (click)=\\\"onElementClick(index)\\\"\\n                        (keydown.arrowLeft.prevent)=\\\"onElementKeyDownArrowLeft(element)\\\"\\n                        (keydown.arrowRight.prevent)=\\\"onElementKeyDownArrowRight(element)\\\"\\n                    >\\n                        <ng-container *polymorpheusOutlet=\\\"content || index + 1 as text; context: {$implicit: index}\\\">\\n                            {{ text }}\\n                        </ng-container>\\n                    </button>\\n                </ng-container>\\n            </ng-container>\\n            <button\\n                appearance=\\\"flat\\\"\\n                tabIndex=\\\"-1\\\"\\n                tuiIconButton\\n                type=\\\"button\\\"\\n                class=\\\"t-button\\\"\\n                [disabled]=\\\"arrowIsDisabledRight\\\"\\n                [iconStart]=\\\"icons.increment\\\"\\n                [size]=\\\"buttonSize\\\"\\n                (click)=\\\"onArrowClick('right')\\\"\\n                (mousedown.zoneless.prevent)=\\\"(0)\\\"\\n            >\\n                {{ texts[1] }}\\n            </button>\\n        </ng-container>\\n    </ng-container>\\n    <ng-template #smallButtons>\\n        <button\\n            *tuiRepeatTimes=\\\"let indexItem of length\\\"\\n            #element\\n            tuiButton\\n            type=\\\"button\\\"\\n            class=\\\"t-button t-button_small\\\"\\n            [appearance]=\\\"getElementMode(indexItem)\\\"\\n            [disabled]=\\\"disabled\\\"\\n            [tabIndex]=\\\"elementIsFocusable(indexItem) ? 0 : -1\\\"\\n            (click)=\\\"onElementClick(indexItem)\\\"\\n            (keydown.arrowLeft.prevent)=\\\"onElementKeyDownArrowLeft(element)\\\"\\n            (keydown.arrowRight.prevent)=\\\"onElementKeyDownArrowRight(element)\\\"\\n        >\\n            {{ indexItem + 1 }}\\n        </button>\\n    </ng-template>\\n    <ng-template #dotsTemplate>\\n        <div\\n            automation-id=\\\"tui-pagination__element\\\"\\n            class=\\\"t-dots\\\"\\n            [class.t-dots_small]=\\\"size === 'm'\\\"\\n        ></div>\\n    </ng-template>\\n</div>\\n\", styles: [\":host{display:block;font:var(--tui-font-text-s);color:var(--tui-text-primary);text-align:center}.t-content{display:flex;justify-content:center}.t-button{inline-size:var(--tui-height-s);margin:0 .125rem;flex-shrink:0}.t-button:first-child{margin-inline-start:0}.t-button:last-child{margin-inline-end:0}.t-button[data-size=xs]{inline-size:var(--tui-height-xs)}.t-button.t-button.t-button_small{inline-size:.5rem;block-size:.5rem;font-size:0;padding:0;margin:0}.t-button.t-button.t-button_small:not(:first-child){margin-left:.5rem}.t-dots{inline-size:var(--tui-height-s);block-size:var(--tui-height-s);line-height:var(--tui-height-s);margin:0 .125rem;flex-shrink:0;color:var(--tui-text-action);text-align:center;cursor:default}.t-dots_small{inline-size:var(--tui-height-xs);block-size:var(--tui-height-xs);line-height:var(--tui-height-xs)}.t-dots:before{content:\\\"\\\\2026\\\"}\\n\"] }]\n        }], propDecorators: { els: [{\n                type: ViewChildren,\n                args: ['element', { read: ElementRef }]\n            }], length: [{\n                type: Input\n            }], focusable: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], activePadding: [{\n                type: Input\n            }], sidePadding: [{\n                type: Input\n            }], content: [{\n                type: Input\n            }], index: [{\n                type: Input\n            }], indexChange: [{\n                type: Output\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPagination };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,IAAI,QAAQ,iBAAiB;AACjD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AACjI,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,MAAM,QAAQ,8BAA8B;AACrD,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,SAASC,kBAAkB,QAAQ,wBAAwB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,SAAAE,2GAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA4KyCxB,EAAE,CAAA0B,uBAAA,EACsoE,CAAC;IADzoE1B,EAAE,CAAA2B,MAAA,EACwsE,CAAC;IAD3sE3B,EAAE,CAAA4B,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAAF9B,EAAE,CAAA+B,SAAA,CACwsE,CAAC;IAD3sE/B,EAAE,CAAAgC,kBAAA,MAAAH,OAAA,KACwsE,CAAC;EAAA;AAAA;AAAA,SAAAI,4FAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAU,GAAA,GAD3sElC,EAAE,CAAAmC,gBAAA;IAAFnC,EAAE,CAAAoC,cAAA,mBAC4gE,CAAC;IAD/gEpC,EAAE,CAAAqC,UAAA,mBAAAC,oHAAA;MAAFtC,EAAE,CAAAuC,aAAA,CAAAL,GAAA;MAAA,MAAAM,QAAA,GAAFxC,EAAE,CAAAyC,aAAA,GAAAC,MAAA;MAAA,MAAAC,MAAA,GAAF3C,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA4C,WAAA,CACqyDD,MAAA,CAAAE,cAAA,CAAAL,QAAoB,CAAC;IAAA,CAAC,CAAC,uCAAAM,wIAAA;MAD9zD9C,EAAE,CAAAuC,aAAA,CAAAL,GAAA;MAAA,MAAAa,UAAA,GAAF/C,EAAE,CAAAgD,WAAA;MAAA,MAAAL,MAAA,GAAF3C,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA4C,WAAA,CACo3DD,MAAA,CAAAM,yBAAA,CAAAF,UAAiC,CAAC;IAAA,CAAC,CAAC,wCAAAG,yIAAA;MAD15DlD,EAAE,CAAAuC,aAAA,CAAAL,GAAA;MAAA,MAAAa,UAAA,GAAF/C,EAAE,CAAAgD,WAAA;MAAA,MAAAL,MAAA,GAAF3C,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA4C,WAAA,CACi9DD,MAAA,CAAAQ,0BAAA,CAAAJ,UAAkC,CAAC;IAAA,CAAC,CAAC;IADx/D/C,EAAE,CAAAoD,UAAA,IAAA7B,0GAAA,0BACsoE,CAAC;IADzoEvB,EAAE,CAAAqD,YAAA,CACsvE,CAAC;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAgB,QAAA,GADzvExC,EAAE,CAAAyC,aAAA,GAAAC,MAAA;IAAA,MAAAC,MAAA,GAAF3C,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAAsD,UAAA,eAAAX,MAAA,CAAAY,cAAA,CAAAf,QAAA,CACqlD,CAAC,aAAAG,MAAA,CAAAa,QAAgD,CAAC,SAAAb,MAAA,CAAAc,UAA8C,CAAC,aAAAd,MAAA,CAAAe,kBAAA,CAAAlB,QAAA,UAA0E,CAAC;IADnwDxC,EAAE,CAAA+B,SAAA,EAC+lE,CAAC;IADlmE/B,EAAE,CAAAsD,UAAA,uBAAAX,MAAA,CAAAgB,OAAA,IAAAnB,QAAA,IAC+lE,CAAC,8BADlmExC,EAAE,CAAA4D,eAAA,IAAAxC,GAAA,EAAAoB,QAAA,CACmoE,CAAC;EAAA;AAAA;AAAA,SAAAqB,mFAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADtoExB,EAAE,CAAA0B,uBAAA,EACstC,CAAC;IADztC1B,EAAE,CAAAoD,UAAA,IAAAnB,2FAAA,mBAC4gE,CAAC;IAD/gEjC,EAAE,CAAA4B,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAgB,QAAA,GAAAf,GAAA,CAAAiB,MAAA;IAAF1C,EAAE,CAAAyC,aAAA;IAAA,MAAAqB,eAAA,GAAF9D,EAAE,CAAAgD,WAAA;IAAFhD,EAAE,CAAA+B,SAAA,CACqyC,CAAC;IADxyC/B,EAAE,CAAAsD,UAAA,SAAAd,QAAA,SACqyC,CAAC,aAAAsB,eAAgB,CAAC;EAAA;AAAA;AAAA,SAAAC,oEAAAvC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADzzCxB,EAAE,CAAA0B,uBAAA,EACwnC,CAAC;IAD3nC1B,EAAE,CAAAoD,UAAA,IAAAS,kFAAA,yBACstC,CAAC;IADztC7D,EAAE,CAAA4B,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAwC,eAAA,GAAAvC,GAAA,CAAAH,SAAA;IAAA,MAAAqB,MAAA,GAAF3C,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAA+B,SAAA,CAC2sC,CAAC;IAD9sC/B,EAAE,CAAAsD,UAAA,WAAAX,MAAA,CAAAsB,0BAAA,CAAAD,eAAA,CAC2sC,CAAC;EAAA;AAAA;AAAA,SAAAE,qDAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA2C,GAAA,GAD9sCnE,EAAE,CAAAmC,gBAAA;IAAFnC,EAAE,CAAA0B,uBAAA,EAC4iB,CAAC;IAD/iB1B,EAAE,CAAAoC,cAAA,eAC8+B,CAAC;IADj/BpC,EAAE,CAAAqC,UAAA,mBAAA+B,6EAAA;MAAFpE,EAAE,CAAAuC,aAAA,CAAA4B,GAAA;MAAA,MAAAxB,MAAA,GAAF3C,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA4C,WAAA,CACo5BD,MAAA,CAAA0B,YAAA,CAAa,MAAM,CAAC;IAAA,CAAC,CAAC,wCAAAC,kGAAA;MAD56BtE,EAAE,CAAAuC,aAAA,CAAA4B,GAAA;MAAA,OAAFnE,EAAE,CAAA4C,WAAA,CAC49B,CAAC;IAAA,CAAE,CAAC;IADl+B5C,EAAE,CAAA2B,MAAA,EAC4hC,CAAC;IAD/hC3B,EAAE,CAAAqD,YAAA,CACqiC,CAAC;IADxiCrD,EAAE,CAAAoD,UAAA,IAAAW,mEAAA,yBACwnC,CAAC;IAD3nC/D,EAAE,CAAAoC,cAAA,eACwvF,CAAC;IAD3vFpC,EAAE,CAAAqC,UAAA,mBAAAkC,6EAAA;MAAFvE,EAAE,CAAAuC,aAAA,CAAA4B,GAAA;MAAA,MAAAxB,MAAA,GAAF3C,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA4C,WAAA,CAC6pFD,MAAA,CAAA0B,YAAA,CAAa,OAAO,CAAC;IAAA,CAAC,CAAC,wCAAAG,kGAAA;MADtrFxE,EAAE,CAAAuC,aAAA,CAAA4B,GAAA;MAAA,OAAFnE,EAAE,CAAA4C,WAAA,CACsuF,CAAC;IAAA,CAAE,CAAC;IAD5uF5C,EAAE,CAAA2B,MAAA,EACsyF,CAAC;IADzyF3B,EAAE,CAAAqD,YAAA,CAC+yF,CAAC;IADlzFrD,EAAE,CAAA4B,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAiD,QAAA,GAAAhD,GAAA,CAAAiD,IAAA;IAAA,MAAA/B,MAAA,GAAF3C,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAA+B,SAAA,CAC+xB,CAAC;IADlyB/B,EAAE,CAAAsD,UAAA,aAAAX,MAAA,CAAAgC,mBAC+xB,CAAC,cAAAhC,MAAA,CAAAiC,KAAA,CAAAC,SAAgD,CAAC,SAAAlC,MAAA,CAAAc,UAAsC,CAAC;IAD13BzD,EAAE,CAAA+B,SAAA,CAC4hC,CAAC;IAD/hC/B,EAAE,CAAAgC,kBAAA,MAAAyC,QAAA,QAC4hC,CAAC;IAD/hCzE,EAAE,CAAA+B,SAAA,CACqnC,CAAC;IADxnC/B,EAAE,CAAAsD,UAAA,qBAAAX,MAAA,CAAAmC,cACqnC,CAAC;IADxnC9E,EAAE,CAAA+B,SAAA,CACwiF,CAAC;IAD3iF/B,EAAE,CAAAsD,UAAA,aAAAX,MAAA,CAAAoC,oBACwiF,CAAC,cAAApC,MAAA,CAAAiC,KAAA,CAAAI,SAAgD,CAAC,SAAArC,MAAA,CAAAc,UAAsC,CAAC;IADnoFzD,EAAE,CAAA+B,SAAA,CACsyF,CAAC;IADzyF/B,EAAE,CAAAgC,kBAAA,MAAAyC,QAAA,QACsyF,CAAC;EAAA;AAAA;AAAA,SAAAQ,sCAAAzD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADzyFxB,EAAE,CAAA0B,uBAAA,EACkf,CAAC;IADrf1B,EAAE,CAAAoD,UAAA,IAAAc,oDAAA,yBAC4iB,CAAC;IAD/iBlE,EAAE,CAAAkF,MAAA;IAAFlF,EAAE,CAAA4B,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAmB,MAAA,GAAF3C,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAA+B,SAAA,CACiiB,CAAC;IADpiB/B,EAAE,CAAAsD,UAAA,SAAFtD,EAAE,CAAAmF,WAAA,OAAAxC,MAAA,CAAAyC,MAAA,CACiiB,CAAC;EAAA;AAAA;AAAA,SAAAC,8CAAA7D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA8D,IAAA,GADpiBtF,EAAE,CAAAmC,gBAAA;IAAFnC,EAAE,CAAAoC,cAAA,mBACi8G,CAAC;IADp8GpC,EAAE,CAAAqC,UAAA,mBAAAkD,sEAAA;MAAA,MAAAC,aAAA,GAAFxF,EAAE,CAAAuC,aAAA,CAAA+C,IAAA,EAAAhE,SAAA;MAAA,MAAAqB,MAAA,GAAF3C,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA4C,WAAA,CAC0vGD,MAAA,CAAAE,cAAA,CAAA2C,aAAwB,CAAC;IAAA,CAAC,CAAC,uCAAAC,0FAAA;MADvxGzF,EAAE,CAAAuC,aAAA,CAAA+C,IAAA;MAAA,MAAAI,WAAA,GAAF1F,EAAE,CAAAgD,WAAA;MAAA,MAAAL,MAAA,GAAF3C,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA4C,WAAA,CACi0GD,MAAA,CAAAM,yBAAA,CAAAyC,WAAiC,CAAC;IAAA,CAAC,CAAC,wCAAAC,2FAAA;MADv2G3F,EAAE,CAAAuC,aAAA,CAAA+C,IAAA;MAAA,MAAAI,WAAA,GAAF1F,EAAE,CAAAgD,WAAA;MAAA,MAAAL,MAAA,GAAF3C,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA4C,WAAA,CACk5GD,MAAA,CAAAQ,0BAAA,CAAAuC,WAAkC,CAAC;IAAA,CAAC,CAAC;IADz7G1F,EAAE,CAAA2B,MAAA,EAC4+G,CAAC;IAD/+G3B,EAAE,CAAAqD,YAAA,CACq/G,CAAC;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAgE,aAAA,GAAA/D,GAAA,CAAAH,SAAA;IAAA,MAAAqB,MAAA,GADx/G3C,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAAsD,UAAA,eAAAX,MAAA,CAAAY,cAAA,CAAAiC,aAAA,CACynG,CAAC,aAAA7C,MAAA,CAAAa,QAAoC,CAAC,aAAAb,MAAA,CAAAe,kBAAA,CAAA8B,aAAA,UAAkE,CAAC;IADpuGxF,EAAE,CAAA+B,SAAA,EAC4+G,CAAC;IAD/+G/B,EAAE,CAAAgC,kBAAA,MAAAwD,aAAA,SAC4+G,CAAC;EAAA;AAAA;AAAA,SAAAI,qCAAApE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD/+GxB,EAAE,CAAAoD,UAAA,IAAAiC,6CAAA,oBACi8G,CAAC;EAAA;EAAA,IAAA7D,EAAA;IAAA,MAAAmB,MAAA,GADp8G3C,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAAsD,UAAA,qBAAAX,MAAA,CAAAkD,MACs8F,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAAtE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADz8FxB,EAAE,CAAA+F,SAAA,aACitH,CAAC;EAAA;EAAA,IAAAvE,EAAA;IAAA,MAAAmB,MAAA,GADptH3C,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAAgG,WAAA,iBAAArD,MAAA,CAAAsD,IAAA,QACgsH,CAAC;EAAA;AAAA;AA3KxyH,MAAMC,WAAW,GAAG,CAAC;AACrB,MAAMC,kBAAkB,GAAG,CAAC;AAC5B,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,GAAG,GAAG7F,WAAW;IACtB,IAAI,CAAC8F,EAAE,GAAG3F,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACwE,MAAM,GAAGnF,MAAM,CAACgB,oBAAoB,CAAC;IAC1C,IAAI,CAAC2D,KAAK,GAAG3E,MAAM,CAACe,cAAc,CAAC;IACnC,IAAI,CAAC6E,MAAM,GAAG,CAAC;IACf,IAAI,CAACW,SAAS,GAAG,IAAI;IACrB,IAAI,CAACP,IAAI,GAAG,GAAG;IACf,IAAI,CAACzC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACiD,aAAa,GAAG,CAAC;IACtB;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB;AACR;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,WAAW,GAAG,IAAI1G,YAAY,CAAC,CAAC;EACzC;EACA,IAAI2G,sBAAsBA,CAAA,EAAG;IACzB,IAAI,IAAI,CAACrD,QAAQ,EAAE;MACf,OAAO,IAAI;IACf;IACA,IAAIsD,kBAAkB,GAAG,CAAC;IAC1B,MAAM;MAAEhC;IAAe,CAAC,GAAG,IAAI;IAC/B,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjC,cAAc,EAAEiC,CAAC,EAAE,EAAE;MACrC,MAAMC,SAAS,GAAG,IAAI,CAAC/C,0BAA0B,CAAC8C,CAAC,CAAC;MACpD,IAAIC,SAAS,EAAE;QACXF,kBAAkB,EAAE;MACxB;MACA,IAAIE,SAAS,KAAK,IAAI,CAACL,KAAK,EAAE;QAC1B;MACJ;IACJ;IACA,OAAQ,IAAI,CAACL,GAAG,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEP,KAAK,KAAKA,KAAK,KAAKG,kBAAkB,CAAC,EAAEK,aAAa,IAC5E,IAAI;EACZ;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAOvG,oBAAoB,CAAC,IAAI,CAAC0F,EAAE,CAAC;EACxC;EACA,IAAI5B,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACgC,KAAK,KAAK,CAAC;EAC3B;EACA,IAAI5B,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACsC,YAAY,KAAK,CAAC;EAClC;EACA;AACJ;AACA;EACI,IAAIvC,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACwC,QAAQ,GAAG,IAAI,CAACzB,MAAM,GAAG,IAAI,CAAC0B,iBAAiB;EAC/D;EACA,IAAI9D,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACwC,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG,GAAG;EACzC;EACAvC,kBAAkBA,CAACiD,KAAK,EAAE;IACtB,OAAO,IAAI,CAACA,KAAK,KAAKA,KAAK,IAAI,CAAC,IAAI,CAACS,OAAO;EAChD;EACA;AACJ;AACA;AACA;AACA;EACInD,0BAA0BA,CAACuD,YAAY,EAAE;IACrC,IAAI,IAAI,CAACvB,IAAI,KAAK,GAAG,EAAE;MACnB,OAAOuB,YAAY;IACvB;IACA,IAAIA,YAAY,GAAG,IAAI,CAACd,WAAW,EAAE;MACjC,OAAOc,YAAY;IACvB;IACA,IAAIA,YAAY,KAAK,IAAI,CAACd,WAAW,IAAI,IAAI,CAACe,iBAAiB,CAAC,IAAI,CAACd,KAAK,CAAC,EAAE;MACzE,OAAO,IAAI;IACf;IACA,MAAMe,mBAAmB,GAAG,IAAI,CAACC,gBAAgB,GAAGH,YAAY;IAChE,IAAIE,mBAAmB,KAAK,IAAI,CAAChB,WAAW,IACxC,IAAI,CAACe,iBAAiB,CAAC,IAAI,CAACJ,YAAY,CAAC,EAAE;MAC3C,OAAO,IAAI;IACf;IACA,IAAIK,mBAAmB,GAAG,IAAI,CAAChB,WAAW,EAAE;MACxC,OAAO,IAAI,CAACkB,SAAS,GAAGF,mBAAmB;IAC/C;IACA,MAAMG,aAAa,GAAG,IAAI,CAAClB,KAAK,GAAG,IAAI,CAACmB,aAAa,GAAGN,YAAY;IACpE,OAAO1G,QAAQ,CAAC+G,aAAa,EAAEL,YAAY,EAAE,IAAI,CAACI,SAAS,GAAGF,mBAAmB,CAAC;EACtF;EACAnE,cAAcA,CAACoD,KAAK,EAAE;IAClB,MAAMoB,QAAQ,GAAG,IAAI,CAAC9B,IAAI,KAAK,GAAG,GAAG,WAAW,GAAG,MAAM;IACzD,OAAO,IAAI,CAACU,KAAK,KAAKA,KAAK,GAAG,SAAS,GAAGoB,QAAQ;EACtD;EACAlF,cAAcA,CAAC8D,KAAK,EAAE;IAClB,IAAI,CAACqB,WAAW,CAACrB,KAAK,CAAC;EAC3B;EACA1D,yBAAyBA,CAACgF,OAAO,EAAE;IAC/B,IAAIA,OAAO,KAAK,IAAI,CAAC3B,GAAG,CAAC4B,KAAK,CAACf,aAAa,EAAE;MAC1C;IACJ;IACA,MAAMgB,QAAQ,GAAG,IAAI,CAAC7B,GAAG,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEP,KAAK,EAAEyB,KAAK,KAAKA,KAAK,CAACzB,KAAK,GAAG,CAAC,CAAC,EAAEQ,aAAa,KAAKc,OAAO,CAAC;IAChGE,QAAQ,EAAEhB,aAAa,CAACkB,KAAK,CAAC,CAAC;EACnC;EACAlF,0BAA0BA,CAAC8E,OAAO,EAAE;IAChC,IAAIA,OAAO,KAAK,IAAI,CAAC3B,GAAG,CAACgC,IAAI,CAACnB,aAAa,EAAE;MACzC;IACJ;IACA,MAAMoB,IAAI,GAAG,IAAI,CAACjC,GAAG,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEP,KAAK,EAAEyB,KAAK,KAAKA,KAAK,CAACzB,KAAK,GAAG,CAAC,CAAC,EAAEQ,aAAa,KAAKc,OAAO,CAAC;IAC5FM,IAAI,EAAEpB,aAAa,CAACkB,KAAK,CAAC,CAAC;EAC/B;EACAhE,YAAYA,CAACmE,SAAS,EAAE;IACpB,IAAI,CAACC,WAAW,CAACD,SAAS,CAAC;IAC3B,IAAI,CAACE,WAAW,CAAC,CAAC;EACtB;EACA;AACJ;AACA;EACI,IAAIrB,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACO,SAAS,GAAG,IAAI,CAACjB,KAAK;EACtC;EACA;AACJ;AACA;EACI,IAAImB,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACpB,WAAW,GAAGR,WAAW,GAAG,IAAI,CAACO,aAAa;EAC9D;EACA;AACJ;AACA;EACI,IAAIa,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACzB,MAAM,IAAI,IAAI,CAAC0B,iBAAiB;EAChD;EACA;AACJ;AACA;EACI,IAAIA,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACO,aAAa,GAAG,CAAC,GAAG3B,kBAAkB;EACtD;EACA,IAAIyB,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC/B,MAAM,GAAG,CAAC;EAC1B;EACA,IAAI8B,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC7C,cAAc,GAAG,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;EACI2C,iBAAiBA,CAACd,KAAK,EAAE;IACrB,OAAO,CAAC,IAAI,CAACW,QAAQ,IAAIX,KAAK,GAAG,IAAI,CAACmB,aAAa;EACvD;EACAW,WAAWA,CAACD,SAAS,EAAE;IACnB,IAAI,CAACR,WAAW,CAAClH,QAAQ,CAAC,IAAI,CAAC6F,KAAK,IAAI6B,SAAS,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACZ,SAAS,CAAC,CAAC;EAChG;EACAc,WAAWA,CAAA,EAAG;IACV,MAAM;MAAE7B;IAAuB,CAAC,GAAG,IAAI;IACvC,IAAIA,sBAAsB,EAAE;MACxBA,sBAAsB,CAACwB,KAAK,CAAC,CAAC;IAClC;EACJ;EACAL,WAAWA,CAACrB,KAAK,EAAE;IACf,IAAI,IAAI,CAACA,KAAK,KAAKA,KAAK,EAAE;MACtB;IACJ;IACA,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,WAAW,CAAC+B,IAAI,CAAChC,KAAK,CAAC;EAChC;EACA;IAAS,IAAI,CAACiC,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyF1C,aAAa;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAAC2C,IAAI,kBAD+E/I,EAAE,CAAAgJ,iBAAA;MAAAC,IAAA,EACJ7C,aAAa;MAAA8C,SAAA;MAAAC,SAAA,WAAAC,oBAAA5H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADXxB,EAAE,CAAAqJ,WAAA,CAAAlI,GAAA,KACoXhB,UAAU;QAAA;QAAA,IAAAqB,EAAA;UAAA,IAAA8H,EAAA;UADhYtJ,EAAE,CAAAuJ,cAAA,CAAAD,EAAA,GAAFtJ,EAAE,CAAAwJ,WAAA,QAAA/H,GAAA,CAAA6E,GAAA,GAAAgD,EAAA;QAAA;MAAA;MAAAG,MAAA;QAAA5D,MAAA;QAAAW,SAAA;QAAAP,IAAA;QAAAzC,QAAA;QAAAiD,aAAA;QAAAC,WAAA;QAAA/C,OAAA;QAAAgD,KAAA;MAAA;MAAA+C,OAAA;QAAA9C,WAAA;MAAA;MAAA+C,UAAA;MAAAC,QAAA,GAAF5J,EAAE,CAAA6J,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAA1I,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFxB,EAAE,CAAAoC,cAAA,YACob,CAAC;UADvbpC,EAAE,CAAAoD,UAAA,IAAA6B,qCAAA,yBACkf,CAAC,IAAAW,oCAAA,gCADrf5F,EAAE,CAAAmK,sBAC83F,CAAC,IAAArE,oCAAA,gCADj4F9F,EAAE,CAAAmK,sBAC0iH,CAAC;UAD7iHnK,EAAE,CAAAqD,YAAA,CAC6uH,CAAC;QAAA;QAAA,IAAA7B,EAAA;UAAA,MAAA4I,gBAAA,GADhvHpK,EAAE,CAAAgD,WAAA;UAAFhD,EAAE,CAAA+B,SAAA,CAC8d,CAAC;UADje/B,EAAE,CAAAsD,UAAA,SAAA7B,GAAA,CAAAwE,IAAA,QAC8d,CAAC,aAAAmE,gBAAgB,CAAC;QAAA;MAAA;MAAAC,YAAA,GAA8pIvK,SAAS,EAA8CC,IAAI,EAA6FmB,kBAAkB,EAA8HH,SAAS,EAAoIL,MAAM,EAAyEC,cAAc;MAAA2J,MAAA;MAAAC,eAAA;IAAA,EAAwI;EAAE;AACj5K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGxK,EAAE,CAAAyK,iBAAA,CAGXrE,aAAa,EAAc,CAAC;IAC5G6C,IAAI,EAAE7I,SAAS;IACfsK,IAAI,EAAE,CAAC;MAAEf,UAAU,EAAE,IAAI;MAAEgB,QAAQ,EAAE,gBAAgB;MAAEC,OAAO,EAAE,CAAC9K,SAAS,EAAEC,IAAI,EAAEmB,kBAAkB,EAAEH,SAAS,EAAEL,MAAM,EAAEC,cAAc,CAAC;MAAE4J,eAAe,EAAElK,uBAAuB,CAACwK,MAAM;MAAEZ,QAAQ,EAAE,s1GAAs1G;MAAEK,MAAM,EAAE,CAAC,02BAA02B;IAAE,CAAC;EACv5I,CAAC,CAAC,QAAkB;IAAEhE,GAAG,EAAE,CAAC;MACpB2C,IAAI,EAAE3I,YAAY;MAClBoK,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEI,IAAI,EAAE3K;MAAW,CAAC;IAC1C,CAAC,CAAC;IAAE0F,MAAM,EAAE,CAAC;MACToD,IAAI,EAAE1I;IACV,CAAC,CAAC;IAAEiG,SAAS,EAAE,CAAC;MACZyC,IAAI,EAAE1I;IACV,CAAC,CAAC;IAAE0F,IAAI,EAAE,CAAC;MACPgD,IAAI,EAAE1I;IACV,CAAC,CAAC;IAAEiD,QAAQ,EAAE,CAAC;MACXyF,IAAI,EAAE1I;IACV,CAAC,CAAC;IAAEkG,aAAa,EAAE,CAAC;MAChBwC,IAAI,EAAE1I;IACV,CAAC,CAAC;IAAEmG,WAAW,EAAE,CAAC;MACduC,IAAI,EAAE1I;IACV,CAAC,CAAC;IAAEoD,OAAO,EAAE,CAAC;MACVsF,IAAI,EAAE1I;IACV,CAAC,CAAC;IAAEoG,KAAK,EAAE,CAAC;MACRsC,IAAI,EAAE1I;IACV,CAAC,CAAC;IAAEqG,WAAW,EAAE,CAAC;MACdqC,IAAI,EAAEzI;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAAS4F,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}