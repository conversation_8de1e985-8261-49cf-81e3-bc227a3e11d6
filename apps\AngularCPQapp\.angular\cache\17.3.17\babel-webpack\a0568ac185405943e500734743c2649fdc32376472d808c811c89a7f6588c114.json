{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, signal, computed, Directive } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { tuiDirectiveBinding, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TuiTextfieldComponent } from '@taiga-ui/core/components/textfield';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { TUI_APPEARANCE_OPTIONS, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport * as i2 from '@taiga-ui/core/directives/hint';\nimport { TuiHintDirective } from '@taiga-ui/core/directives/hint';\nimport { TUI_PASSWORD_TEXTS } from '@taiga-ui/kit/tokens';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nconst [TUI_PASSWORD_OPTIONS, tuiPasswordOptionsProvider] = tuiCreateOptions({\n  icons: {\n    hide: '@tui.eye-off',\n    show: '@tui.eye'\n  }\n});\nclass TuiPassword {\n  constructor() {\n    this.options = inject(TUI_PASSWORD_OPTIONS);\n    this.texts = toSignal(inject(TUI_PASSWORD_TEXTS), {\n      initialValue: ['', '']\n    });\n    this.textfield = inject(TuiTextfieldComponent);\n    this.hidden = signal(true);\n    this.icon = tuiDirectiveBinding(TuiIcon, 'icon', computed((size = this.textfield.options.size()) => {\n      const icon = this.hidden() ? this.options.icons.show : this.options.icons.hide;\n      return tuiIsString(icon) ? icon : icon(size);\n    }));\n    this.hint = tuiDirectiveBinding(TuiHintDirective, 'tuiHint', computed(() => this.hidden() ? this.texts()[0] : this.texts()[1]));\n  }\n  toggle() {\n    this.hidden.set(!this.hidden());\n    this.textfield.input?.nativeElement.setAttribute('type', this.hidden() ? 'password' : 'text');\n  }\n  static {\n    this.ɵfac = function TuiPassword_Factory(t) {\n      return new (t || TuiPassword)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiPassword,\n      selectors: [[\"tui-icon\", \"tuiPassword\", \"\"]],\n      hostAttrs: [2, \"cursor\", \"pointer\"],\n      hostVars: 2,\n      hostBindings: function TuiPassword_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TuiPassword_click_HostBindingHandler() {\n            return ctx.toggle();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"border\", ctx.textfield.options.size() === \"s\" ? \"0.25rem solid transparent\" : null);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: TUI_APPEARANCE_OPTIONS,\n        useValue: {\n          appearance: 'icon'\n        }\n      }]), i0.ɵɵHostDirectivesFeature([i1.TuiWithAppearance, {\n        directive: i2.TuiHintDirective,\n        inputs: [\"tuiHintAppearance\", \"tuiHintAppearance\", \"tuiHintContext\", \"tuiHintContext\"]\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPassword, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'tui-icon[tuiPassword]',\n      providers: [{\n        provide: TUI_APPEARANCE_OPTIONS,\n        useValue: {\n          appearance: 'icon'\n        }\n      }],\n      hostDirectives: [TuiWithAppearance, {\n        directive: TuiHintDirective,\n        inputs: ['tuiHintAppearance', 'tuiHintContext']\n      }],\n      host: {\n        style: 'cursor: pointer',\n        '(click)': 'toggle()',\n        '[style.border]': 'textfield.options.size() === \"s\" ? \"0.25rem solid transparent\" : null'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_PASSWORD_OPTIONS, TuiPassword, tuiPasswordOptionsProvider };", "map": {"version": 3, "names": ["i0", "inject", "signal", "computed", "Directive", "toSignal", "tuiDirectiveBinding", "tuiIsString", "TuiIcon", "TuiTextfieldComponent", "i1", "TUI_APPEARANCE_OPTIONS", "TuiWithAppearance", "i2", "TuiHintDirective", "TUI_PASSWORD_TEXTS", "tuiCreateOptions", "TUI_PASSWORD_OPTIONS", "tuiPasswordOptionsProvider", "icons", "hide", "show", "TuiPassword", "constructor", "options", "texts", "initialValue", "textfield", "hidden", "icon", "size", "hint", "toggle", "set", "input", "nativeElement", "setAttribute", "ɵfac", "TuiPassword_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "TuiPassword_HostBindings", "rf", "ctx", "ɵɵlistener", "TuiPassword_click_HostBindingHandler", "ɵɵstyleProp", "standalone", "features", "ɵɵProvidersFeature", "provide", "useValue", "appearance", "ɵɵHostDirectivesFeature", "directive", "inputs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "hostDirectives", "host", "style"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-password.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, signal, computed, Directive } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { tuiDirectiveBinding, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TuiTextfieldComponent } from '@taiga-ui/core/components/textfield';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { TUI_APPEARANCE_OPTIONS, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport * as i2 from '@taiga-ui/core/directives/hint';\nimport { TuiHintDirective } from '@taiga-ui/core/directives/hint';\nimport { TUI_PASSWORD_TEXTS } from '@taiga-ui/kit/tokens';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\n\nconst [TUI_PASSWORD_OPTIONS, tuiPasswordOptionsProvider] = tuiCreateOptions({\n    icons: {\n        hide: '@tui.eye-off',\n        show: '@tui.eye',\n    },\n});\n\nclass TuiPassword {\n    constructor() {\n        this.options = inject(TUI_PASSWORD_OPTIONS);\n        this.texts = toSignal(inject(TUI_PASSWORD_TEXTS), {\n            initialValue: ['', ''],\n        });\n        this.textfield = inject(TuiTextfieldComponent);\n        this.hidden = signal(true);\n        this.icon = tuiDirectiveBinding(TuiIcon, 'icon', computed((size = this.textfield.options.size()) => {\n            const icon = this.hidden()\n                ? this.options.icons.show\n                : this.options.icons.hide;\n            return tuiIsString(icon) ? icon : icon(size);\n        }));\n        this.hint = tuiDirectiveBinding(TuiHintDirective, 'tuiHint', computed(() => (this.hidden() ? this.texts()[0] : this.texts()[1])));\n    }\n    toggle() {\n        this.hidden.set(!this.hidden());\n        this.textfield.input?.nativeElement.setAttribute('type', this.hidden() ? 'password' : 'text');\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPassword, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPassword, isStandalone: true, selector: \"tui-icon[tuiPassword]\", host: { listeners: { \"click\": \"toggle()\" }, properties: { \"style.border\": \"textfield.options.size() === \\\"s\\\" ? \\\"0.25rem solid transparent\\\" : null\" }, styleAttribute: \"cursor: pointer\" }, providers: [\n            {\n                provide: TUI_APPEARANCE_OPTIONS,\n                useValue: { appearance: 'icon' },\n            },\n        ], hostDirectives: [{ directive: i1.TuiWithAppearance }, { directive: i2.TuiHintDirective, inputs: [\"tuiHintAppearance\", \"tuiHintAppearance\", \"tuiHintContext\", \"tuiHintContext\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPassword, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-icon[tuiPassword]',\n                    providers: [\n                        {\n                            provide: TUI_APPEARANCE_OPTIONS,\n                            useValue: { appearance: 'icon' },\n                        },\n                    ],\n                    hostDirectives: [\n                        TuiWithAppearance,\n                        {\n                            directive: TuiHintDirective,\n                            inputs: ['tuiHintAppearance', 'tuiHintContext'],\n                        },\n                    ],\n                    host: {\n                        style: 'cursor: pointer',\n                        '(click)': 'toggle()',\n                        '[style.border]': 'textfield.options.size() === \"s\" ? \"0.25rem solid transparent\" : null',\n                    },\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_PASSWORD_OPTIONS, TuiPassword, tuiPasswordOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,eAAe;AACnE,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,mCAAmC;AACpF,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,qBAAqB,QAAQ,qCAAqC;AAC3E,OAAO,KAAKC,EAAE,MAAM,sCAAsC;AAC1D,SAASC,sBAAsB,EAAEC,iBAAiB,QAAQ,sCAAsC;AAChG,OAAO,KAAKC,EAAE,MAAM,gCAAgC;AACpD,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,gBAAgB,QAAQ,wBAAwB;AAEzD,MAAM,CAACC,oBAAoB,EAAEC,0BAA0B,CAAC,GAAGF,gBAAgB,CAAC;EACxEG,KAAK,EAAE;IACHC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE;EACV;AACJ,CAAC,CAAC;AAEF,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGvB,MAAM,CAACgB,oBAAoB,CAAC;IAC3C,IAAI,CAACQ,KAAK,GAAGpB,QAAQ,CAACJ,MAAM,CAACc,kBAAkB,CAAC,EAAE;MAC9CW,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE;IACzB,CAAC,CAAC;IACF,IAAI,CAACC,SAAS,GAAG1B,MAAM,CAACQ,qBAAqB,CAAC;IAC9C,IAAI,CAACmB,MAAM,GAAG1B,MAAM,CAAC,IAAI,CAAC;IAC1B,IAAI,CAAC2B,IAAI,GAAGvB,mBAAmB,CAACE,OAAO,EAAE,MAAM,EAAEL,QAAQ,CAAC,CAAC2B,IAAI,GAAG,IAAI,CAACH,SAAS,CAACH,OAAO,CAACM,IAAI,CAAC,CAAC,KAAK;MAChG,MAAMD,IAAI,GAAG,IAAI,CAACD,MAAM,CAAC,CAAC,GACpB,IAAI,CAACJ,OAAO,CAACL,KAAK,CAACE,IAAI,GACvB,IAAI,CAACG,OAAO,CAACL,KAAK,CAACC,IAAI;MAC7B,OAAOb,WAAW,CAACsB,IAAI,CAAC,GAAGA,IAAI,GAAGA,IAAI,CAACC,IAAI,CAAC;IAChD,CAAC,CAAC,CAAC;IACH,IAAI,CAACC,IAAI,GAAGzB,mBAAmB,CAACQ,gBAAgB,EAAE,SAAS,EAAEX,QAAQ,CAAC,MAAO,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAG,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;EACrI;EACAO,MAAMA,CAAA,EAAG;IACL,IAAI,CAACJ,MAAM,CAACK,GAAG,CAAC,CAAC,IAAI,CAACL,MAAM,CAAC,CAAC,CAAC;IAC/B,IAAI,CAACD,SAAS,CAACO,KAAK,EAAEC,aAAa,CAACC,YAAY,CAAC,MAAM,EAAE,IAAI,CAACR,MAAM,CAAC,CAAC,GAAG,UAAU,GAAG,MAAM,CAAC;EACjG;EACA;IAAS,IAAI,CAACS,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFjB,WAAW;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACkB,IAAI,kBAD+ExC,EAAE,CAAAyC,iBAAA;MAAAC,IAAA,EACJpB,WAAW;MAAAqB,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADThD,EAAE,CAAAkD,UAAA,mBAAAC,qCAAA;YAAA,OACJF,GAAA,CAAAjB,MAAA,CAAO,CAAC;UAAA,CAAE,CAAC;QAAA;QAAA,IAAAgB,EAAA;UADThD,EAAE,CAAAoD,WAAA,WACJH,GAAA,CAAAtB,SAAA,CAAAH,OAAA,CAAAM,IAAA,CAAuB,CAAC,KAAK,GAAG,GAAG,2BAA2B,GAAG,IAAvD,CAAC;QAAA;MAAA;MAAAuB,UAAA;MAAAC,QAAA,GADTtD,EAAE,CAAAuD,kBAAA,CACyQ,CACpW;QACIC,OAAO,EAAE7C,sBAAsB;QAC/B8C,QAAQ,EAAE;UAAEC,UAAU,EAAE;QAAO;MACnC,CAAC,CACJ,GAN4F1D,EAAE,CAAA2D,uBAAA,EAM9DjD,EAAE,CAACE,iBAAiB;QAAAgD,SAAA,EAAiB/C,EAAE,CAACC,gBAAgB;QAAA+C,MAAA;MAAA;IAAA,EAA4G;EAAE;AAC/M;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KARqG9D,EAAE,CAAA+D,iBAAA,CAQXzC,WAAW,EAAc,CAAC;IAC1GoB,IAAI,EAAEtC,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCX,UAAU,EAAE,IAAI;MAChBY,QAAQ,EAAE,uBAAuB;MACjCC,SAAS,EAAE,CACP;QACIV,OAAO,EAAE7C,sBAAsB;QAC/B8C,QAAQ,EAAE;UAAEC,UAAU,EAAE;QAAO;MACnC,CAAC,CACJ;MACDS,cAAc,EAAE,CACZvD,iBAAiB,EACjB;QACIgD,SAAS,EAAE9C,gBAAgB;QAC3B+C,MAAM,EAAE,CAAC,mBAAmB,EAAE,gBAAgB;MAClD,CAAC,CACJ;MACDO,IAAI,EAAE;QACFC,KAAK,EAAE,iBAAiB;QACxB,SAAS,EAAE,UAAU;QACrB,gBAAgB,EAAE;MACtB;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASpD,oBAAoB,EAAEK,WAAW,EAAEJ,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}