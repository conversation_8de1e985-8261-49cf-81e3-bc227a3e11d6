{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, ViewContainerRef, TemplateRef, Directive, Input } from '@angular/core';\n\n/**\n * @internal\n */\nclass TuiLetContext {\n  constructor(internalDirectiveInstance) {\n    this.internalDirectiveInstance = internalDirectiveInstance;\n  }\n  get $implicit() {\n    return this.internalDirectiveInstance.tuiLet;\n  }\n  get tuiLet() {\n    return this.internalDirectiveInstance.tuiLet;\n  }\n}\n\n/**\n * Works like *ngIf but does not have a condition — use it to declare\n * the result of pipes calculation (i.e. async pipe)\n */\nclass TuiLet {\n  constructor() {\n    inject(ViewContainerRef).createEmbeddedView(inject(TemplateRef), new TuiLetContext(this));\n  }\n  /**\n   * Asserts the correct type of the context for the template that `TuiLet` will render.\n   *\n   * The presence of this method is a signal to the Ivy template type-check compiler that the\n   * `TuiLet` structural directive renders its template with a specific context type.\n   */\n  static ngTemplateContextGuard(_dir, _ctx) {\n    return true;\n  }\n  static {\n    this.ɵfac = function TuiLet_Factory(t) {\n      return new (t || TuiLet)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiLet,\n      selectors: [[\"\", \"tuiLet\", \"\"]],\n      inputs: {\n        tuiLet: \"tuiLet\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiLet, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiLet]'\n    }]\n  }], function () {\n    return [];\n  }, {\n    tuiLet: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiLet, TuiLetContext };", "map": {"version": 3, "names": ["i0", "inject", "ViewContainerRef", "TemplateRef", "Directive", "Input", "TuiLetContext", "constructor", "internalDirectiveInstance", "$implicit", "tuiLet", "TuiLet", "createEmbeddedView", "ngTemplateContextGuard", "_dir", "_ctx", "ɵfac", "TuiLet_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-let.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, ViewContainerRef, TemplateRef, Directive, Input } from '@angular/core';\n\n/**\n * @internal\n */\nclass TuiLetContext {\n    constructor(internalDirectiveInstance) {\n        this.internalDirectiveInstance = internalDirectiveInstance;\n    }\n    get $implicit() {\n        return this.internalDirectiveInstance.tuiLet;\n    }\n    get tuiLet() {\n        return this.internalDirectiveInstance.tuiLet;\n    }\n}\n\n/**\n * Works like *ngIf but does not have a condition — use it to declare\n * the result of pipes calculation (i.e. async pipe)\n */\nclass TuiLet {\n    constructor() {\n        inject(ViewContainerRef).createEmbeddedView(inject((TemplateRef)), new TuiLetContext(this));\n    }\n    /**\n     * Asserts the correct type of the context for the template that `TuiLet` will render.\n     *\n     * The presence of this method is a signal to the Ivy template type-check compiler that the\n     * `TuiLet` structural directive renders its template with a specific context type.\n     */\n    static ngTemplateContextGuard(_dir, _ctx) {\n        return true;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLet, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiLet, isStandalone: true, selector: \"[tuiLet]\", inputs: { tuiLet: \"tuiLet\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLet, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiLet]',\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { tuiLet: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiLet, TuiLetContext };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;;AAEvF;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAACC,yBAAyB,EAAE;IACnC,IAAI,CAACA,yBAAyB,GAAGA,yBAAyB;EAC9D;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACD,yBAAyB,CAACE,MAAM;EAChD;EACA,IAAIA,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,yBAAyB,CAACE,MAAM;EAChD;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMC,MAAM,CAAC;EACTJ,WAAWA,CAAA,EAAG;IACVN,MAAM,CAACC,gBAAgB,CAAC,CAACU,kBAAkB,CAACX,MAAM,CAAEE,WAAY,CAAC,EAAE,IAAIG,aAAa,CAAC,IAAI,CAAC,CAAC;EAC/F;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOO,sBAAsBA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACtC,OAAO,IAAI;EACf;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,eAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFP,MAAM;IAAA,CAAmD;EAAE;EACtK;IAAS,IAAI,CAACQ,IAAI,kBAD+EnB,EAAE,CAAAoB,iBAAA;MAAAC,IAAA,EACJV,MAAM;MAAAW,SAAA;MAAAC,MAAA;QAAAb,MAAA;MAAA;MAAAc,UAAA;IAAA,EAAyF;EAAE;AACpM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGzB,EAAE,CAAA0B,iBAAA,CAGXf,MAAM,EAAc,CAAC;IACrGU,IAAI,EAAEjB,SAAS;IACfuB,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAElB,MAAM,EAAE,CAAC;MACnEW,IAAI,EAAEhB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASM,MAAM,EAAEL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}