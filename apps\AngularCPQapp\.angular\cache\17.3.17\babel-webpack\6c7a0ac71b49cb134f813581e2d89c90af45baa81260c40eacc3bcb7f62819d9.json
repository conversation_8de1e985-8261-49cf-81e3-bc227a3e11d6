{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, DestroyRef, ChangeDetectorRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { NgControl } from '@angular/forms';\nimport * as i2 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport { tuiWatch } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiCreateToken, tuiProvideOptions, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { TuiAppearance } from '@taiga-ui/core/directives/appearance';\nimport { tuiInjectIconResolver } from '@taiga-ui/core/tokens';\nconst _c0 = [\"type\", \"checkbox\", \"tuiSwitch\", \"\"];\nconst TUI_SWITCH_DEFAULT_OPTIONS = {\n  showIcons: true,\n  size: 'm',\n  icon: '@tui.check',\n  appearance: el => el.checked ? 'primary' : 'secondary'\n};\nconst TUI_SWITCH_OPTIONS = tuiCreateToken(TUI_SWITCH_DEFAULT_OPTIONS);\nfunction tuiSwitchOptionsProvider(options) {\n  return tuiProvideOptions(TUI_SWITCH_OPTIONS, options, TUI_SWITCH_DEFAULT_OPTIONS);\n}\nclass TuiSwitch {\n  constructor() {\n    this.appearance = inject(TuiAppearance);\n    this.resolver = tuiInjectIconResolver();\n    this.destroyRef = inject(DestroyRef);\n    this.cdr = inject(ChangeDetectorRef);\n    this.options = inject(TUI_SWITCH_OPTIONS);\n    this.el = tuiInjectElement();\n    this.control = inject(NgControl, {\n      self: true,\n      optional: true\n    });\n    this.size = this.options.size;\n    this.showIcons = this.options.showIcons;\n  }\n  ngOnInit() {\n    this.control?.valueChanges?.pipe(tuiWatch(this.cdr), takeUntilDestroyed(this.destroyRef)).subscribe();\n  }\n  ngDoCheck() {\n    this.appearance.tuiAppearance = this.options.appearance(this.el);\n  }\n  get icon() {\n    const {\n      options,\n      resolver,\n      size\n    } = this;\n    const icon = tuiIsString(options.icon) ? options.icon : options.icon(size);\n    return this.showIcons && icon ? `url(${resolver(icon)})` : null;\n  }\n  static {\n    this.ɵfac = function TuiSwitch_Factory(t) {\n      return new (t || TuiSwitch)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiSwitch,\n      selectors: [[\"input\", \"type\", \"checkbox\", \"tuiSwitch\", \"\"]],\n      hostAttrs: [\"role\", \"switch\"],\n      hostVars: 6,\n      hostBindings: function TuiSwitch_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"disabled\", !ctx.control || ctx.control.disabled);\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n          i0.ɵɵstyleProp(\"--t-checked-icon\", ctx.icon);\n          i0.ɵɵclassProp(\"_readonly\", !ctx.control);\n        }\n      },\n      inputs: {\n        size: \"size\",\n        showIcons: \"showIcons\"\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([{\n        directive: i1.TuiAppearance,\n        inputs: [\"tuiAppearanceState\", \"tuiAppearanceState\", \"tuiAppearanceFocus\", \"tuiAppearanceFocus\", \"tuiAppearanceMode\", \"tuiAppearanceMode\"]\n      }, i2.TuiNativeValidator]), i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 0,\n      vars: 0,\n      template: function TuiSwitch_Template(rf, ctx) {},\n      styles: [\"[tuiSwitch]{transition-property:background,box-shadow;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;inline-size:3rem;block-size:1.5rem;border-radius:2rem;overflow:hidden;cursor:pointer;margin:0;flex-shrink:0;color:#fff!important}[tuiSwitch][data-size=s]{block-size:1rem;inline-size:2rem}[tuiSwitch][data-size=s]:before{inline-size:1rem;transform:translate(-1rem);-webkit-mask-size:.75rem;mask-size:.75rem}[tuiSwitch][data-size=s]:after{inline-size:1rem;box-shadow:-2.625rem 0 0 .5rem var(--tui-background-base);outline-width:.167rem;transform:scale(.375)}[tuiSwitch][data-size=s]:checked:after{transform:scale(.375) translate(2.625rem)}[tuiSwitch]:checked:before{transform:none}[tuiSwitch]:checked:after{transform:scale(.33333) translate(4.5rem)}[tuiSwitch]:disabled._readonly{opacity:1}[tuiSwitch]:before,[tuiSwitch]:after{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;block-size:100%;inline-size:1.5rem}[tuiSwitch]:before{display:var(--t-checked-icon, none);background:currentColor;-webkit-mask:var(--t-checked-icon) no-repeat center;mask:var(--t-checked-icon) no-repeat center;-webkit-mask-size:1rem;mask-size:1rem;transform:translate(-1.5rem)}[tuiSwitch]:after{right:0;border-radius:100%;transform:scale(.33333);box-shadow:-4.5rem 0 0 .75rem var(--tui-background-base);outline:.375rem solid var(--tui-background-neutral-2-pressed);outline-offset:var(--t-checked-icon, 20rem)}[tuiSwitch]:invalid:not([data-mode]),[tuiSwitch][data-mode~=invalid]{color:#fff}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSwitch, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'input[type=\"checkbox\"][tuiSwitch]',\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      hostDirectives: [{\n        directive: TuiAppearance,\n        inputs: ['tuiAppearanceState', 'tuiAppearanceFocus', 'tuiAppearanceMode']\n      }, TuiNativeValidator],\n      host: {\n        role: 'switch',\n        '[disabled]': '!control || control.disabled',\n        '[attr.data-size]': 'size',\n        '[class._readonly]': '!control',\n        '[style.--t-checked-icon]': 'icon'\n      },\n      styles: [\"[tuiSwitch]{transition-property:background,box-shadow;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;inline-size:3rem;block-size:1.5rem;border-radius:2rem;overflow:hidden;cursor:pointer;margin:0;flex-shrink:0;color:#fff!important}[tuiSwitch][data-size=s]{block-size:1rem;inline-size:2rem}[tuiSwitch][data-size=s]:before{inline-size:1rem;transform:translate(-1rem);-webkit-mask-size:.75rem;mask-size:.75rem}[tuiSwitch][data-size=s]:after{inline-size:1rem;box-shadow:-2.625rem 0 0 .5rem var(--tui-background-base);outline-width:.167rem;transform:scale(.375)}[tuiSwitch][data-size=s]:checked:after{transform:scale(.375) translate(2.625rem)}[tuiSwitch]:checked:before{transform:none}[tuiSwitch]:checked:after{transform:scale(.33333) translate(4.5rem)}[tuiSwitch]:disabled._readonly{opacity:1}[tuiSwitch]:before,[tuiSwitch]:after{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;block-size:100%;inline-size:1.5rem}[tuiSwitch]:before{display:var(--t-checked-icon, none);background:currentColor;-webkit-mask:var(--t-checked-icon) no-repeat center;mask:var(--t-checked-icon) no-repeat center;-webkit-mask-size:1rem;mask-size:1rem;transform:translate(-1.5rem)}[tuiSwitch]:after{right:0;border-radius:100%;transform:scale(.33333);box-shadow:-4.5rem 0 0 .75rem var(--tui-background-base);outline:.375rem solid var(--tui-background-neutral-2-pressed);outline-offset:var(--t-checked-icon, 20rem)}[tuiSwitch]:invalid:not([data-mode]),[tuiSwitch][data-mode~=invalid]{color:#fff}\\n\"]\n    }]\n  }], null, {\n    size: [{\n      type: Input\n    }],\n    showIcons: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_SWITCH_DEFAULT_OPTIONS, TUI_SWITCH_OPTIONS, TuiSwitch, tuiSwitchOptionsProvider };", "map": {"version": 3, "names": ["i0", "inject", "DestroyRef", "ChangeDetectorRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "takeUntilDestroyed", "NgControl", "i2", "TuiNativeValidator", "tuiWatch", "tuiInjectElement", "tuiCreateToken", "tuiProvideOptions", "tuiIsString", "i1", "TuiAppearan<PERSON>", "tuiInjectIconResolver", "_c0", "TUI_SWITCH_DEFAULT_OPTIONS", "showIcons", "size", "icon", "appearance", "el", "checked", "TUI_SWITCH_OPTIONS", "tuiSwitchOptionsProvider", "options", "TuiSwitch", "constructor", "resolver", "destroyRef", "cdr", "control", "self", "optional", "ngOnInit", "valueChanges", "pipe", "subscribe", "ngDoCheck", "tui<PERSON><PERSON><PERSON><PERSON>", "ɵfac", "TuiSwitch_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "TuiSwitch_HostBindings", "rf", "ctx", "ɵɵhostProperty", "disabled", "ɵɵattribute", "ɵɵstyleProp", "ɵɵclassProp", "inputs", "standalone", "features", "ɵɵHostDirectivesFeature", "directive", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "template", "TuiSwitch_Template", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "hostDirectives", "host", "role"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-switch.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, DestroyRef, ChangeDetectorRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { NgControl } from '@angular/forms';\nimport * as i2 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport { tuiWatch } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiCreateToken, tuiProvideOptions, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { TuiAppearance } from '@taiga-ui/core/directives/appearance';\nimport { tuiInjectIconResolver } from '@taiga-ui/core/tokens';\n\nconst TUI_SWITCH_DEFAULT_OPTIONS = {\n    showIcons: true,\n    size: 'm',\n    icon: '@tui.check',\n    appearance: (el) => (el.checked ? 'primary' : 'secondary'),\n};\nconst TUI_SWITCH_OPTIONS = tuiCreateToken(TUI_SWITCH_DEFAULT_OPTIONS);\nfunction tuiSwitchOptionsProvider(options) {\n    return tuiProvideOptions(TUI_SWITCH_OPTIONS, options, TUI_SWITCH_DEFAULT_OPTIONS);\n}\n\nclass TuiSwitch {\n    constructor() {\n        this.appearance = inject(TuiAppearance);\n        this.resolver = tuiInjectIconResolver();\n        this.destroyRef = inject(DestroyRef);\n        this.cdr = inject(ChangeDetectorRef);\n        this.options = inject(TUI_SWITCH_OPTIONS);\n        this.el = tuiInjectElement();\n        this.control = inject(NgControl, { self: true, optional: true });\n        this.size = this.options.size;\n        this.showIcons = this.options.showIcons;\n    }\n    ngOnInit() {\n        this.control?.valueChanges\n            ?.pipe(tuiWatch(this.cdr), takeUntilDestroyed(this.destroyRef))\n            .subscribe();\n    }\n    ngDoCheck() {\n        this.appearance.tuiAppearance = this.options.appearance(this.el);\n    }\n    get icon() {\n        const { options, resolver, size } = this;\n        const icon = tuiIsString(options.icon) ? options.icon : options.icon(size);\n        return this.showIcons && icon ? `url(${resolver(icon)})` : null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSwitch, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSwitch, isStandalone: true, selector: \"input[type=\\\"checkbox\\\"][tuiSwitch]\", inputs: { size: \"size\", showIcons: \"showIcons\" }, host: { attributes: { \"role\": \"switch\" }, properties: { \"disabled\": \"!control || control.disabled\", \"attr.data-size\": \"size\", \"class._readonly\": \"!control\", \"style.--t-checked-icon\": \"icon\" } }, hostDirectives: [{ directive: i1.TuiAppearance, inputs: [\"tuiAppearanceState\", \"tuiAppearanceState\", \"tuiAppearanceFocus\", \"tuiAppearanceFocus\", \"tuiAppearanceMode\", \"tuiAppearanceMode\"] }, { directive: i2.TuiNativeValidator }], ngImport: i0, template: '', isInline: true, styles: [\"[tuiSwitch]{transition-property:background,box-shadow;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;inline-size:3rem;block-size:1.5rem;border-radius:2rem;overflow:hidden;cursor:pointer;margin:0;flex-shrink:0;color:#fff!important}[tuiSwitch][data-size=s]{block-size:1rem;inline-size:2rem}[tuiSwitch][data-size=s]:before{inline-size:1rem;transform:translate(-1rem);-webkit-mask-size:.75rem;mask-size:.75rem}[tuiSwitch][data-size=s]:after{inline-size:1rem;box-shadow:-2.625rem 0 0 .5rem var(--tui-background-base);outline-width:.167rem;transform:scale(.375)}[tuiSwitch][data-size=s]:checked:after{transform:scale(.375) translate(2.625rem)}[tuiSwitch]:checked:before{transform:none}[tuiSwitch]:checked:after{transform:scale(.33333) translate(4.5rem)}[tuiSwitch]:disabled._readonly{opacity:1}[tuiSwitch]:before,[tuiSwitch]:after{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;block-size:100%;inline-size:1.5rem}[tuiSwitch]:before{display:var(--t-checked-icon, none);background:currentColor;-webkit-mask:var(--t-checked-icon) no-repeat center;mask:var(--t-checked-icon) no-repeat center;-webkit-mask-size:1rem;mask-size:1rem;transform:translate(-1.5rem)}[tuiSwitch]:after{right:0;border-radius:100%;transform:scale(.33333);box-shadow:-4.5rem 0 0 .75rem var(--tui-background-base);outline:.375rem solid var(--tui-background-neutral-2-pressed);outline-offset:var(--t-checked-icon, 20rem)}[tuiSwitch]:invalid:not([data-mode]),[tuiSwitch][data-mode~=invalid]{color:#fff}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSwitch, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'input[type=\"checkbox\"][tuiSwitch]', template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, hostDirectives: [\n                        {\n                            directive: TuiAppearance,\n                            inputs: ['tuiAppearanceState', 'tuiAppearanceFocus', 'tuiAppearanceMode'],\n                        },\n                        TuiNativeValidator,\n                    ], host: {\n                        role: 'switch',\n                        '[disabled]': '!control || control.disabled',\n                        '[attr.data-size]': 'size',\n                        '[class._readonly]': '!control',\n                        '[style.--t-checked-icon]': 'icon',\n                    }, styles: [\"[tuiSwitch]{transition-property:background,box-shadow;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;inline-size:3rem;block-size:1.5rem;border-radius:2rem;overflow:hidden;cursor:pointer;margin:0;flex-shrink:0;color:#fff!important}[tuiSwitch][data-size=s]{block-size:1rem;inline-size:2rem}[tuiSwitch][data-size=s]:before{inline-size:1rem;transform:translate(-1rem);-webkit-mask-size:.75rem;mask-size:.75rem}[tuiSwitch][data-size=s]:after{inline-size:1rem;box-shadow:-2.625rem 0 0 .5rem var(--tui-background-base);outline-width:.167rem;transform:scale(.375)}[tuiSwitch][data-size=s]:checked:after{transform:scale(.375) translate(2.625rem)}[tuiSwitch]:checked:before{transform:none}[tuiSwitch]:checked:after{transform:scale(.33333) translate(4.5rem)}[tuiSwitch]:disabled._readonly{opacity:1}[tuiSwitch]:before,[tuiSwitch]:after{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;block-size:100%;inline-size:1.5rem}[tuiSwitch]:before{display:var(--t-checked-icon, none);background:currentColor;-webkit-mask:var(--t-checked-icon) no-repeat center;mask:var(--t-checked-icon) no-repeat center;-webkit-mask-size:1rem;mask-size:1rem;transform:translate(-1.5rem)}[tuiSwitch]:after{right:0;border-radius:100%;transform:scale(.33333);box-shadow:-4.5rem 0 0 .75rem var(--tui-background-base);outline:.375rem solid var(--tui-background-neutral-2-pressed);outline-offset:var(--t-checked-icon, 20rem)}[tuiSwitch]:invalid:not([data-mode]),[tuiSwitch][data-mode~=invalid]{color:#fff}\\n\"] }]\n        }], propDecorators: { size: [{\n                type: Input\n            }], showIcons: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_SWITCH_DEFAULT_OPTIONS, TUI_SWITCH_OPTIONS, TuiSwitch, tuiSwitchOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,QAAQ,eAAe;AACnI,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,KAAKC,EAAE,MAAM,2CAA2C;AAC/D,SAASC,kBAAkB,QAAQ,2CAA2C;AAC9E,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,WAAW,QAAQ,mCAAmC;AAClG,OAAO,KAAKC,EAAE,MAAM,sCAAsC;AAC1D,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,qBAAqB,QAAQ,uBAAuB;AAAC,MAAAC,GAAA;AAE9D,MAAMC,0BAA0B,GAAG;EAC/BC,SAAS,EAAE,IAAI;EACfC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,YAAY;EAClBC,UAAU,EAAGC,EAAE,IAAMA,EAAE,CAACC,OAAO,GAAG,SAAS,GAAG;AAClD,CAAC;AACD,MAAMC,kBAAkB,GAAGd,cAAc,CAACO,0BAA0B,CAAC;AACrE,SAASQ,wBAAwBA,CAACC,OAAO,EAAE;EACvC,OAAOf,iBAAiB,CAACa,kBAAkB,EAAEE,OAAO,EAAET,0BAA0B,CAAC;AACrF;AAEA,MAAMU,SAAS,CAAC;EACZC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACP,UAAU,GAAGxB,MAAM,CAACiB,aAAa,CAAC;IACvC,IAAI,CAACe,QAAQ,GAAGd,qBAAqB,CAAC,CAAC;IACvC,IAAI,CAACe,UAAU,GAAGjC,MAAM,CAACC,UAAU,CAAC;IACpC,IAAI,CAACiC,GAAG,GAAGlC,MAAM,CAACE,iBAAiB,CAAC;IACpC,IAAI,CAAC2B,OAAO,GAAG7B,MAAM,CAAC2B,kBAAkB,CAAC;IACzC,IAAI,CAACF,EAAE,GAAGb,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACuB,OAAO,GAAGnC,MAAM,CAACQ,SAAS,EAAE;MAAE4B,IAAI,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChE,IAAI,CAACf,IAAI,GAAG,IAAI,CAACO,OAAO,CAACP,IAAI;IAC7B,IAAI,CAACD,SAAS,GAAG,IAAI,CAACQ,OAAO,CAACR,SAAS;EAC3C;EACAiB,QAAQA,CAAA,EAAG;IACP,IAAI,CAACH,OAAO,EAAEI,YAAY,EACpBC,IAAI,CAAC7B,QAAQ,CAAC,IAAI,CAACuB,GAAG,CAAC,EAAE3B,kBAAkB,CAAC,IAAI,CAAC0B,UAAU,CAAC,CAAC,CAC9DQ,SAAS,CAAC,CAAC;EACpB;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAAClB,UAAU,CAACmB,aAAa,GAAG,IAAI,CAACd,OAAO,CAACL,UAAU,CAAC,IAAI,CAACC,EAAE,CAAC;EACpE;EACA,IAAIF,IAAIA,CAAA,EAAG;IACP,MAAM;MAAEM,OAAO;MAAEG,QAAQ;MAAEV;IAAK,CAAC,GAAG,IAAI;IACxC,MAAMC,IAAI,GAAGR,WAAW,CAACc,OAAO,CAACN,IAAI,CAAC,GAAGM,OAAO,CAACN,IAAI,GAAGM,OAAO,CAACN,IAAI,CAACD,IAAI,CAAC;IAC1E,OAAO,IAAI,CAACD,SAAS,IAAIE,IAAI,GAAG,OAAOS,QAAQ,CAACT,IAAI,CAAC,GAAG,GAAG,IAAI;EACnE;EACA;IAAS,IAAI,CAACqB,IAAI,YAAAC,kBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFhB,SAAS;IAAA,CAAmD;EAAE;EACzK;IAAS,IAAI,CAACiB,IAAI,kBAD+EhD,EAAE,CAAAiD,iBAAA;MAAAC,IAAA,EACJnB,SAAS;MAAAoB,SAAA;MAAAC,SAAA,WAAuJ,QAAQ;MAAAC,QAAA;MAAAC,YAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADtKxD,EAAE,CAAA0D,cAAA,cAAAD,GAAA,CAAArB,OAAA,IAAAqB,GAAA,CAAArB,OAAA,CAAAuB,QACI,CAAC;UADP3D,EAAE,CAAA4D,WAAA,cAAAH,GAAA,CAAAlC,IAAA;UAAFvB,EAAE,CAAA6D,WAAA,qBAAAJ,GAAA,CAAAjC,IACI,CAAC;UADPxB,EAAE,CAAA8D,WAAA,eAAAL,GAAA,CAAArB,OACI,CAAC;QAAA;MAAA;MAAA2B,MAAA;QAAAxC,IAAA;QAAAD,SAAA;MAAA;MAAA0C,UAAA;MAAAC,QAAA,GADPjE,EAAE,CAAAkE,uBAAA;QAAAC,SAAA,EAC+VlD,EAAE,CAACC,aAAa;QAAA6C,MAAA;MAAA,GAA6JrD,EAAE,CAACC,kBAAkB,IADniBX,EAAE,CAAAoE,mBAAA;MAAAC,KAAA,EAAAjD,GAAA;MAAAkD,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,mBAAAjB,EAAA,EAAAC,GAAA;MAAAiB,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC6vE;EAAE;AACt2E;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG7E,EAAE,CAAA8E,iBAAA,CAGX/C,SAAS,EAAc,CAAC;IACxGmB,IAAI,EAAE9C,SAAS;IACf2E,IAAI,EAAE,CAAC;MAAEf,UAAU,EAAE,IAAI;MAAEgB,QAAQ,EAAE,mCAAmC;MAAER,QAAQ,EAAE,EAAE;MAAEG,aAAa,EAAEtE,iBAAiB,CAAC4E,IAAI;MAAEL,eAAe,EAAEtE,uBAAuB,CAAC4E,MAAM;MAAEC,cAAc,EAAE,CACpL;QACIhB,SAAS,EAAEjD,aAAa;QACxB6C,MAAM,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,mBAAmB;MAC5E,CAAC,EACDpD,kBAAkB,CACrB;MAAEyE,IAAI,EAAE;QACLC,IAAI,EAAE,QAAQ;QACd,YAAY,EAAE,8BAA8B;QAC5C,kBAAkB,EAAE,MAAM;QAC1B,mBAAmB,EAAE,UAAU;QAC/B,0BAA0B,EAAE;MAChC,CAAC;MAAEX,MAAM,EAAE,CAAC,gkDAAgkD;IAAE,CAAC;EAC3lD,CAAC,CAAC,QAAkB;IAAEnD,IAAI,EAAE,CAAC;MACrB2B,IAAI,EAAE3C;IACV,CAAC,CAAC;IAAEe,SAAS,EAAE,CAAC;MACZ4B,IAAI,EAAE3C;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASc,0BAA0B,EAAEO,kBAAkB,EAAEG,SAAS,EAAEF,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}