{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { signal, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Pipe } from '@angular/core';\nimport { tuiInjectIconResolver, TUI_ICON_START, TUI_ICON_END } from '@taiga-ui/core/tokens';\nclass TuiIcon {\n  constructor() {\n    this.resolver = tuiInjectIconResolver();\n    this.backgroundSrc = signal(null);\n    this.iconSrc = signal(this.resolve(inject(TUI_ICON_START, {\n      self: true,\n      optional: true\n    }) || inject(TUI_ICON_END, {\n      self: true,\n      optional: true\n    })));\n  }\n  set icon(icon) {\n    this.iconSrc.set(this.resolve(icon));\n  }\n  set background(background) {\n    this.backgroundSrc.set(this.resolve(background));\n  }\n  resolve(value) {\n    return value ? `url(${this.resolver(value)})` : null;\n  }\n  static {\n    this.ɵfac = function TuiIcon_Factory(t) {\n      return new (t || TuiIcon)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiIcon,\n      selectors: [[\"tui-icon\"]],\n      hostVars: 4,\n      hostBindings: function TuiIcon_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-icon\", ctx.iconSrc() || \"url()\")(\"--t-icon-bg\", ctx.backgroundSrc());\n        }\n      },\n      inputs: {\n        icon: \"icon\",\n        background: \"background\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiIcon_Template(rf, ctx) {},\n      styles: [\"tui-icon{position:relative;display:inline-block;inline-size:1em;block-size:1em;font-size:1.5rem;flex-shrink:0;border:0 solid transparent;vertical-align:middle;box-sizing:border-box;-webkit-mask:var(--t-icon-bg) no-repeat center / contain;mask:var(--t-icon-bg) no-repeat center / contain}@media (hover: hover) and (pointer: fine){tui-icon[data-appearance=icon]:hover{color:var(--tui-text-secondary)}}tui-icon:after,tui-icon[tuiIcons]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";display:block;-webkit-mask:var(--t-icon) no-repeat center / contain;mask:var(--t-icon) no-repeat center / contain;background:currentColor}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiIcon, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-icon',\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[style.--t-icon]': 'iconSrc() || \"url()\"',\n        '[style.--t-icon-bg]': 'backgroundSrc()'\n      },\n      styles: [\"tui-icon{position:relative;display:inline-block;inline-size:1em;block-size:1em;font-size:1.5rem;flex-shrink:0;border:0 solid transparent;vertical-align:middle;box-sizing:border-box;-webkit-mask:var(--t-icon-bg) no-repeat center / contain;mask:var(--t-icon-bg) no-repeat center / contain}@media (hover: hover) and (pointer: fine){tui-icon[data-appearance=icon]:hover{color:var(--tui-text-secondary)}}tui-icon:after,tui-icon[tuiIcons]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";display:block;-webkit-mask:var(--t-icon) no-repeat center / contain;mask:var(--t-icon) no-repeat center / contain;background:currentColor}\\n\"]\n    }]\n  }], null, {\n    icon: [{\n      type: Input\n    }],\n    background: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiIconPipe {\n  constructor() {\n    this.transform = tuiInjectIconResolver();\n  }\n  static {\n    this.ɵfac = function TuiIconPipe_Factory(t) {\n      return new (t || TuiIconPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiIcon\",\n      type: TuiIconPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiIconPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiIcon'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiIcon, TuiIconPipe };", "map": {"version": 3, "names": ["i0", "signal", "inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "<PERSON><PERSON>", "tuiInjectIconResolver", "TUI_ICON_START", "TUI_ICON_END", "TuiIcon", "constructor", "resolver", "backgroundSrc", "iconSrc", "resolve", "self", "optional", "icon", "set", "background", "value", "ɵfac", "TuiIcon_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "TuiIcon_HostBindings", "rf", "ctx", "ɵɵstyleProp", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiIcon_Template", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "host", "TuiIconPipe", "transform", "TuiIconPipe_Factory", "ɵpipe", "ɵɵdefinePipe", "name", "pure"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-components-icon.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { signal, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Pipe } from '@angular/core';\nimport { tuiInjectIconResolver, TUI_ICON_START, TUI_ICON_END } from '@taiga-ui/core/tokens';\n\nclass TuiIcon {\n    constructor() {\n        this.resolver = tuiInjectIconResolver();\n        this.backgroundSrc = signal(null);\n        this.iconSrc = signal(this.resolve(inject(TUI_ICON_START, { self: true, optional: true }) ||\n            inject(TUI_ICON_END, { self: true, optional: true })));\n    }\n    set icon(icon) {\n        this.iconSrc.set(this.resolve(icon));\n    }\n    set background(background) {\n        this.backgroundSrc.set(this.resolve(background));\n    }\n    resolve(value) {\n        return value ? `url(${this.resolver(value)})` : null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIcon, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiIcon, isStandalone: true, selector: \"tui-icon\", inputs: { icon: \"icon\", background: \"background\" }, host: { properties: { \"style.--t-icon\": \"iconSrc() || \\\"url()\\\"\", \"style.--t-icon-bg\": \"backgroundSrc()\" } }, ngImport: i0, template: '', isInline: true, styles: [\"tui-icon{position:relative;display:inline-block;inline-size:1em;block-size:1em;font-size:1.5rem;flex-shrink:0;border:0 solid transparent;vertical-align:middle;box-sizing:border-box;-webkit-mask:var(--t-icon-bg) no-repeat center / contain;mask:var(--t-icon-bg) no-repeat center / contain}@media (hover: hover) and (pointer: fine){tui-icon[data-appearance=icon]:hover{color:var(--tui-text-secondary)}}tui-icon:after,tui-icon[tuiIcons]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";display:block;-webkit-mask:var(--t-icon) no-repeat center / contain;mask:var(--t-icon) no-repeat center / contain;background:currentColor}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIcon, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-icon', template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[style.--t-icon]': 'iconSrc() || \"url()\"',\n                        '[style.--t-icon-bg]': 'backgroundSrc()',\n                    }, styles: [\"tui-icon{position:relative;display:inline-block;inline-size:1em;block-size:1em;font-size:1.5rem;flex-shrink:0;border:0 solid transparent;vertical-align:middle;box-sizing:border-box;-webkit-mask:var(--t-icon-bg) no-repeat center / contain;mask:var(--t-icon-bg) no-repeat center / contain}@media (hover: hover) and (pointer: fine){tui-icon[data-appearance=icon]:hover{color:var(--tui-text-secondary)}}tui-icon:after,tui-icon[tuiIcons]:after{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";display:block;-webkit-mask:var(--t-icon) no-repeat center / contain;mask:var(--t-icon) no-repeat center / contain;background:currentColor}\\n\"] }]\n        }], propDecorators: { icon: [{\n                type: Input\n            }], background: [{\n                type: Input\n            }] } });\n\nclass TuiIconPipe {\n    constructor() {\n        this.transform = tuiInjectIconResolver();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIconPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIconPipe, isStandalone: true, name: \"tuiIcon\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIconPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiIcon',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiIcon, TuiIconPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,IAAI,QAAQ,eAAe;AAClH,SAASC,qBAAqB,EAAEC,cAAc,EAAEC,YAAY,QAAQ,uBAAuB;AAE3F,MAAMC,OAAO,CAAC;EACVC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAGL,qBAAqB,CAAC,CAAC;IACvC,IAAI,CAACM,aAAa,GAAGb,MAAM,CAAC,IAAI,CAAC;IACjC,IAAI,CAACc,OAAO,GAAGd,MAAM,CAAC,IAAI,CAACe,OAAO,CAACd,MAAM,CAACO,cAAc,EAAE;MAAEQ,IAAI,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,IACrFhB,MAAM,CAACQ,YAAY,EAAE;MAAEO,IAAI,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAAC;EAC9D;EACA,IAAIC,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACJ,OAAO,CAACK,GAAG,CAAC,IAAI,CAACJ,OAAO,CAACG,IAAI,CAAC,CAAC;EACxC;EACA,IAAIE,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,CAACP,aAAa,CAACM,GAAG,CAAC,IAAI,CAACJ,OAAO,CAACK,UAAU,CAAC,CAAC;EACpD;EACAL,OAAOA,CAACM,KAAK,EAAE;IACX,OAAOA,KAAK,GAAG,OAAO,IAAI,CAACT,QAAQ,CAACS,KAAK,CAAC,GAAG,GAAG,IAAI;EACxD;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,gBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFd,OAAO;IAAA,CAAmD;EAAE;EACvK;IAAS,IAAI,CAACe,IAAI,kBAD+E1B,EAAE,CAAA2B,iBAAA;MAAAC,IAAA,EACJjB,OAAO;MAAAkB,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADLjC,EAAE,CAAAmC,WAAA,aACJD,GAAA,CAAAnB,OAAA,CAAQ,CAAC,IAAI,OAAP,CAAC,gBAAPmB,GAAA,CAAApB,aAAA,CAAc,CAAR,CAAC;QAAA;MAAA;MAAAsB,MAAA;QAAAjB,IAAA;QAAAE,UAAA;MAAA;MAAAgB,UAAA;MAAAC,QAAA,GADLtC,EAAE,CAAAuC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,iBAAAV,EAAA,EAAAC,GAAA;MAAAU,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC0/B;EAAE;AACnmC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG/C,EAAE,CAAAgD,iBAAA,CAGXrC,OAAO,EAAc,CAAC;IACtGiB,IAAI,EAAEzB,SAAS;IACf8C,IAAI,EAAE,CAAC;MAAEZ,UAAU,EAAE,IAAI;MAAEa,QAAQ,EAAE,UAAU;MAAER,QAAQ,EAAE,EAAE;MAAEG,aAAa,EAAEzC,iBAAiB,CAAC+C,IAAI;MAAEL,eAAe,EAAEzC,uBAAuB,CAAC+C,MAAM;MAAEC,IAAI,EAAE;QACjJ,kBAAkB,EAAE,sBAAsB;QAC1C,qBAAqB,EAAE;MAC3B,CAAC;MAAET,MAAM,EAAE,CAAC,kpBAAkpB;IAAE,CAAC;EAC7qB,CAAC,CAAC,QAAkB;IAAEzB,IAAI,EAAE,CAAC;MACrBS,IAAI,EAAEtB;IACV,CAAC,CAAC;IAAEe,UAAU,EAAE,CAAC;MACbO,IAAI,EAAEtB;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgD,WAAW,CAAC;EACd1C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2C,SAAS,GAAG/C,qBAAqB,CAAC,CAAC;EAC5C;EACA;IAAS,IAAI,CAACe,IAAI,YAAAiC,oBAAA/B,CAAA;MAAA,YAAAA,CAAA,IAAyF6B,WAAW;IAAA,CAA8C;EAAE;EACtK;IAAS,IAAI,CAACG,KAAK,kBApB8EzD,EAAE,CAAA0D,YAAA;MAAAC,IAAA;MAAA/B,IAAA,EAoBM0B,WAAW;MAAAM,IAAA;MAAAvB,UAAA;IAAA,EAAwC;EAAE;AAClK;AACA;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KAtBqG/C,EAAE,CAAAgD,iBAAA,CAsBXM,WAAW,EAAc,CAAC;IAC1G1B,IAAI,EAAErB,IAAI;IACV0C,IAAI,EAAE,CAAC;MACCZ,UAAU,EAAE,IAAI;MAChBsB,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAShD,OAAO,EAAE2C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}