{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule, AsyncPipe, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, ChangeDetectorRef } from '@angular/core';\nimport { TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { TuiDay, TuiMonth, TuiDayRange, TUI_FIRST_DAY, TUI_LAST_DAY, MIN_YEAR, MAX_YEAR, TuiYear, TuiMonthRange, TUI_LAST_DISPLAYED_DAY } from '@taiga-ui/cdk/date-time';\nimport { TuiMapperPipe } from '@taiga-ui/cdk/pipes/mapper';\nimport { tuiCreateToken, tuiProvideOptions, tuiNullableSame, tuiPure, tuiIsNumber } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiScrollIntoView, TuiScrollbar } from '@taiga-ui/core/components/scrollbar';\nimport { TUI_SHORT_WEEK_DAYS, TUI_DAY_TYPE_HANDLER, tuiAsAuxiliary } from '@taiga-ui/core/tokens';\nimport { Subject } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { TuiHovered } from '@taiga-ui/cdk/directives/hovered';\nimport { TuiLet } from '@taiga-ui/cdk/directives/let';\nimport { TuiRepeatTimes } from '@taiga-ui/cdk/directives/repeat-times';\nimport { TuiCalendarSheetPipe, TuiOrderWeekDaysPipe, TuiMonthPipe } from '@taiga-ui/core/pipes';\nimport { TuiLink } from '@taiga-ui/core/components/link';\nimport { TuiSpinButton } from '@taiga-ui/core/components/spin-button';\nconst _c0 = (a0, a1, a2, a3, a4) => [a0, a1, a2, a3, a4];\nfunction TuiCalendarSheet_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 3);\n  }\n  if (rf & 2) {\n    const day_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"textContent\", day_r1);\n  }\n}\nfunction TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_div_1_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 11);\n  }\n  if (rf & 2) {\n    const markers_r5 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵstyleProp(\"background\", (markers_r5 == null ? null : markers_r5[1]) || \"\");\n  }\n}\nfunction TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"div\", 11);\n    i0.ɵɵtemplate(2, TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_div_1_div_3_div_2_Template, 1, 2, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const markers_r5 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", markers_r5 == null ? null : markers_r5[0]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", markers_r5.length > 1);\n  }\n}\nfunction TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵpipe(1, \"tuiMapper\");\n    i0.ɵɵlistener(\"click\", function TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const item_r3 = i0.ɵɵnextContext().tuiLet;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.onItemClick(item_r3));\n    })(\"tuiHoveredChange\", function TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_div_1_Template_div_tuiHoveredChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const item_r3 = i0.ɵɵnextContext().tuiLet;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.onItemHovered($event && item_r3));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_div_1_div_3_Template, 3, 3, \"div\", 9);\n    i0.ɵɵpipe(4, \"tuiMapper\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().tuiLet;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"t-cell_disabled\", ctx_r3.disabledItemHandler(item_r3))(\"t-cell_today\", ctx_r3.itemIsToday(item_r3))(\"t-cell_unavailable\", ctx_r3.itemIsUnavailable(item_r3));\n    i0.ɵɵattribute(\"data-range\", ctx_r3.getItemRange(item_r3))(\"data-type\", i0.ɵɵpipeBind2(1, 10, item_r3, ctx_r3.dayTypeHandler));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r3.day, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBindV(4, 13, i0.ɵɵpureFunction5(19, _c0, item_r3, ctx_r3.toMarkers, ctx_r3.itemIsToday(item_r3), ctx_r3.getItemRange(item_r3), ctx_r3.markerHandler)));\n  }\n}\nfunction TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_div_1_Template, 5, 25, \"div\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.tuiLet;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3 && (!ctx_r3.itemIsUnavailable(item_r3) || ctx_r3.showAdjacent));\n  }\n}\nfunction TuiCalendarSheet_div_4_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_Template, 2, 1, \"ng-container\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const colIndex_r6 = ctx.$implicit;\n    const rowIndex_r7 = i0.ɵɵnextContext().$implicit;\n    const sheet_r8 = i0.ɵɵnextContext().tuiLet;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tuiLet\", sheet_r8[rowIndex_r7] == null ? null : sheet_r8[rowIndex_r7][colIndex_r6]);\n  }\n}\nfunction TuiCalendarSheet_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtemplate(1, TuiCalendarSheet_div_4_div_1_ng_container_1_Template, 2, 1, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rowIndex_r7 = ctx.$implicit;\n    const sheet_r8 = i0.ɵɵnextContext().tuiLet;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tuiRepeatTimesOf\", (sheet_r8[rowIndex_r7] == null ? null : sheet_r8[rowIndex_r7].length) || 0);\n  }\n}\nfunction TuiCalendarSheet_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, TuiCalendarSheet_div_4_div_1_Template, 2, 1, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const sheet_r8 = ctx.tuiLet;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tuiRepeatTimesOf\", sheet_r8.length);\n  }\n}\nfunction TuiCalendarSpin_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.value.formattedYear, \" \");\n  }\n}\nfunction TuiCalendarSpin_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 3);\n    i0.ɵɵlistener(\"click\", function TuiCalendarSpin_ng_template_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onYearClick());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.value.formattedYear, \" \");\n  }\n}\nfunction TuiCalendarYear_div_0_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵlistener(\"click\", function TuiCalendarYear_div_0_ng_container_1_div_1_Template_div_click_0_listener() {\n      const item_r2 = i0.ɵɵrestoreView(_r1).tuiLet;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.yearClick.emit(item_r2));\n    })(\"tuiHoveredChange\", function TuiCalendarYear_div_0_ng_container_1_div_1_Template_div_tuiHoveredChange_0_listener($event) {\n      const item_r2 = i0.ɵɵrestoreView(_r1).tuiLet;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onItemHovered($event, item_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.tuiLet;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"t-cell_disabled\", ctx_r2.isDisabled(item_r2))(\"t-cell_today\", ctx_r2.itemIsToday(item_r2));\n    i0.ɵɵproperty(\"tuiScrollIntoView\", ctx_r2.scrollItemIntoView(item_r2));\n    i0.ɵɵattribute(\"data-range\", ctx_r2.getItemRange(item_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r2, \" \");\n  }\n}\nfunction TuiCalendarYear_div_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiCalendarYear_div_0_ng_container_1_div_1_Template, 2, 7, \"div\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const colIndex_r4 = ctx.$implicit;\n    const rowIndex_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tuiLet\", ctx_r2.getItem(rowIndex_r5, colIndex_r4));\n  }\n}\nfunction TuiCalendarYear_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, TuiCalendarYear_div_0_ng_container_1_Template, 2, 1, \"ng-container\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tuiRepeatTimesOf\", 4);\n  }\n}\nfunction TuiCalendar_tui_scrollbar_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tui-scrollbar\", 2)(1, \"tui-calendar-year\", 3);\n    i0.ɵɵlistener(\"yearClick\", function TuiCalendar_tui_scrollbar_0_Template_tui_calendar_year_yearClick_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPickerYearClick($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"initialItem\", ctx_r1.month.year)(\"max\", ctx_r1.computedMax.year)(\"min\", ctx_r1.computedMin.year)(\"rangeMode\", ctx_r1.options.rangeMode)(\"value\", ctx_r1.value);\n  }\n}\nfunction TuiCalendar_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tui-calendar-spin\", 4);\n    i0.ɵɵlistener(\"valueChange\", function TuiCalendar_ng_template_1_Template_tui_calendar_spin_valueChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPaginationValueChange($event));\n    })(\"yearClick\", function TuiCalendar_ng_template_1_Template_tui_calendar_spin_yearClick_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPaginationYearClick());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"tui-calendar-sheet\", 5);\n    i0.ɵɵpipe(2, \"tuiMapper\");\n    i0.ɵɵlistener(\"dayClick\", function TuiCalendar_ng_template_1_Template_tui_calendar_sheet_dayClick_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDayClick($event));\n    })(\"hoveredItemChange\", function TuiCalendar_ng_template_1_Template_tui_calendar_sheet_hoveredItemChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onHoveredItemChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"max\", ctx_r1.computedMaxViewedMonth)(\"min\", ctx_r1.computedMinViewedMonth)(\"value\", ctx_r1.month);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabledItemHandler\", i0.ɵɵpipeBind4(2, 9, ctx_r1.disabledItemHandler, ctx_r1.disabledItemHandlerMapper, ctx_r1.computedMin, ctx_r1.computedMax))(\"hoveredItem\", ctx_r1.hoveredItem)(\"markerHandler\", ctx_r1.markerHandler)(\"month\", ctx_r1.month)(\"showAdjacent\", ctx_r1.showAdjacent)(\"value\", ctx_r1.value);\n  }\n}\nconst TUI_CALENDAR_SHEET_DEFAULT_OPTIONS = {\n  rangeMode: false\n};\nconst TUI_CALENDAR_SHEET_OPTIONS = tuiCreateToken(TUI_CALENDAR_SHEET_DEFAULT_OPTIONS);\nfunction tuiCalendarSheetOptionsProvider(options) {\n  return tuiProvideOptions(TUI_CALENDAR_SHEET_OPTIONS, options, TUI_CALENDAR_SHEET_DEFAULT_OPTIONS);\n}\nclass TuiCalendarSheet {\n  constructor() {\n    this.options = inject(TUI_CALENDAR_SHEET_OPTIONS);\n    this.today = TuiDay.currentLocal();\n    this.unorderedWeekDays$ = inject(TUI_SHORT_WEEK_DAYS);\n    this.dayTypeHandler = inject(TUI_DAY_TYPE_HANDLER);\n    this.month = TuiMonth.currentLocal();\n    this.disabledItemHandler = TUI_FALSE_HANDLER;\n    this.markerHandler = null;\n    this.value = null;\n    this.hoveredItem = null;\n    this.showAdjacent = true;\n    /**\n     * @deprecated use static DI options instead\n     * ```\n     * tuiCalendarSheetOptionsProvider({rangeMode: true})\n     * ```\n     * TODO(v5): delete it\n     */\n    this.single = true;\n    this.hoveredItemChange = new EventEmitter();\n    this.dayClick = new EventEmitter();\n    this.toMarkers = (day, today, range, markerHandler) => {\n      if (today || ['active', 'end', 'start'].includes(range || '')) {\n        return null;\n      }\n      const markers = markerHandler?.(day);\n      return markers?.length ? markers : null;\n    };\n  }\n  /**\n   * @deprecated TODO(v5): delete it. It is used nowhere except unit tests\n   */\n  itemIsInterval(day) {\n    const {\n      value,\n      hoveredItem\n    } = this;\n    if (!(value instanceof TuiDayRange)) {\n      return false;\n    }\n    if (!value.isSingleDay) {\n      return value.from.daySameOrBefore(day) && value.to.dayAfter(day);\n    }\n    if (hoveredItem === null) {\n      return false;\n    }\n    const range = TuiDayRange.sort(value.from, hoveredItem);\n    return range.from.daySameOrBefore(day) && range.to.dayAfter(day);\n  }\n  onItemHovered(item) {\n    this.updateHoveredItem(item || null);\n  }\n  getItemRange(item) {\n    const {\n      value,\n      hoveredItem\n    } = this;\n    if (!value) {\n      return null;\n    }\n    if (value instanceof TuiDay && !this.computedRangeMode) {\n      return value.daySame(item) ? 'active' : null;\n    }\n    if (value instanceof TuiDayRange && value.isSingleDay) {\n      return value.from.daySame(item) ? 'active' : null;\n    }\n    if (!(value instanceof TuiDay) && !(value instanceof TuiDayRange)) {\n      return value.find(day => day.daySame(item)) ? 'active' : null;\n    }\n    const range = this.getRange(value, hoveredItem);\n    if (range.isSingleDay && range.from.daySame(item)) {\n      return 'active';\n    }\n    if (range.from.daySame(item)) {\n      return 'start';\n    }\n    if (range.to.daySame(item)) {\n      return 'end';\n    }\n    return range.from.dayBefore(item) && range.to.dayAfter(item) ? 'middle' : null;\n  }\n  get computedRangeMode() {\n    return !this.single || this.options.rangeMode;\n  }\n  get isRangePicking() {\n    return this.computedRangeMode ? this.value instanceof TuiDay :\n    /**\n     * Only for backward compatibility!\n     * TODO(v5): replace with `this.options.rangeMode && this.value instanceof TuiDay`\n     */\n    this.value instanceof TuiDayRange && this.value.isSingleDay;\n  }\n  itemIsToday(item) {\n    return this.today.daySame(item);\n  }\n  itemIsUnavailable(item) {\n    return !this.month.monthSame(item);\n  }\n  onItemClick(item) {\n    this.dayClick.emit(item);\n  }\n  getRange(value, hoveredItem) {\n    if (value instanceof TuiDay) {\n      return TuiDayRange.sort(value, hoveredItem ?? value);\n    }\n    return value.isSingleDay ? TuiDayRange.sort(value.from, hoveredItem ?? value.to) : value;\n  }\n  updateHoveredItem(day) {\n    if (tuiNullableSame(this.hoveredItem, day, (a, b) => a.daySame(b))) {\n      return;\n    }\n    this.hoveredItem = day;\n    this.hoveredItemChange.emit(day);\n  }\n  static {\n    this.ɵfac = function TuiCalendarSheet_Factory(t) {\n      return new (t || TuiCalendarSheet)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiCalendarSheet,\n      selectors: [[\"tui-calendar-sheet\"]],\n      hostVars: 2,\n      hostBindings: function TuiCalendarSheet_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_picking\", ctx.isRangePicking);\n        }\n      },\n      inputs: {\n        month: \"month\",\n        disabledItemHandler: \"disabledItemHandler\",\n        markerHandler: \"markerHandler\",\n        value: \"value\",\n        hoveredItem: \"hoveredItem\",\n        showAdjacent: \"showAdjacent\",\n        single: \"single\"\n      },\n      outputs: {\n        hoveredItemChange: \"hoveredItemChange\",\n        dayClick: \"dayClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 9,\n      consts: [[1, \"t-row\", \"t-row_weekday\"], [\"class\", \"t-cell\", 3, \"textContent\", 4, \"ngFor\", \"ngForOf\"], [4, \"tuiLet\"], [1, \"t-cell\", 3, \"textContent\"], [\"automation-id\", \"tui-calendar-sheet__row\", \"class\", \"t-row\", 4, \"tuiRepeatTimes\", \"tuiRepeatTimesOf\"], [\"automation-id\", \"tui-calendar-sheet__row\", 1, \"t-row\"], [4, \"tuiRepeatTimes\", \"tuiRepeatTimesOf\"], [\"automation-id\", \"tui-calendar-sheet__cell\", \"class\", \"t-cell\", 3, \"t-cell_disabled\", \"t-cell_today\", \"t-cell_unavailable\", \"click\", \"tuiHoveredChange\", 4, \"ngIf\"], [\"automation-id\", \"tui-calendar-sheet__cell\", 1, \"t-cell\", 3, \"click\", \"tuiHoveredChange\"], [\"class\", \"t-dots\", 4, \"ngIf\"], [1, \"t-dots\"], [1, \"t-dot\"], [\"class\", \"t-dot\", 3, \"background\", 4, \"ngIf\"]],\n      template: function TuiCalendarSheet_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, TuiCalendarSheet_div_1_Template, 1, 1, \"div\", 1);\n          i0.ɵɵpipe(2, \"tuiOrderWeekDays\");\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, TuiCalendarSheet_div_4_Template, 2, 1, \"div\", 2);\n          i0.ɵɵpipe(5, \"tuiCalendarSheet\");\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(3, 4, i0.ɵɵpipeBind1(2, 2, ctx.unorderedWeekDays$)));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"tuiLet\", i0.ɵɵpipeBind2(5, 6, ctx.month, true));\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, i1.AsyncPipe, TuiCalendarSheetPipe, TuiHovered, TuiLet, TuiMapperPipe, TuiOrderWeekDaysPipe, TuiRepeatTimes],\n      styles: [\".t-row[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;font:var(--tui-font-text-m)}.t-row[_ngcontent-%COMP%]:last-child{justify-content:flex-start}.t-cell[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;justify-content:center;line-height:2rem;isolation:isolate;cursor:pointer;overflow:hidden;border:.125rem solid transparent;box-sizing:border-box;-webkit-mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem));mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem))}.t-cell[_ngcontent-%COMP%]:first-child{border-inline-start-color:transparent!important}.t-cell[_ngcontent-%COMP%]:last-child{border-inline-end-color:transparent!important}.t-cell[_ngcontent-%COMP%]:before, .t-cell[_ngcontent-%COMP%]:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";z-index:-1;border-radius:var(--tui-radius-m)}.t-cell[_ngcontent-%COMP%]:after{-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat}.t-cell[data-range][_ngcontent-%COMP%]:before{background:var(--tui-background-neutral-1)}._picking[_nghost-%COMP%]   .t-cell[data-range][_ngcontent-%COMP%]:before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle][_ngcontent-%COMP%]{border-color:var(--tui-background-neutral-1)}._picking[_nghost-%COMP%]   .t-cell[data-range=middle][_ngcontent-%COMP%]{border-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle][_ngcontent-%COMP%]:not(:first-child):before{border-top-left-radius:0;border-bottom-left-radius:0}.t-cell[data-range=middle][_ngcontent-%COMP%]:not(:last-child):before{border-top-right-radius:0;border-bottom-right-radius:0}.t-cell[data-range=start][_ngcontent-%COMP%]{border-inline-end-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}._picking[_nghost-%COMP%]   .t-cell[data-range=start][_ngcontent-%COMP%]{border-inline-end-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start][_ngcontent-%COMP%]:not(:last-child):before{right:-1rem}.t-cell[data-range=start][_ngcontent-%COMP%]:after{background:var(--tui-background-accent-1)}.t-cell[data-range=end][_ngcontent-%COMP%]{border-inline-start-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}._picking[_nghost-%COMP%]   .t-cell[data-range=end][_ngcontent-%COMP%]{border-inline-start-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=end][_ngcontent-%COMP%]:not(:first-child):before{left:-1rem}.t-cell[data-range=end][_ngcontent-%COMP%]:after{background:var(--tui-background-accent-1);transform:scaleX(-1)}.t-cell[data-range=active][_ngcontent-%COMP%]{color:var(--tui-text-primary-on-accent-1)}.t-cell[data-range=active][_ngcontent-%COMP%]:after{background:var(--tui-background-accent-1);-webkit-mask:none;mask:none}.t-cell_disabled[_ngcontent-%COMP%]{opacity:var(--tui-disabled-opacity);pointer-events:none}.t-cell_today[_ngcontent-%COMP%]{text-decoration:underline;text-underline-offset:.25rem}@media (hover: hover) and (pointer: fine){.t-cell[_ngcontent-%COMP%]:hover:not([data-range=start]):not([data-range=end]):before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start][_ngcontent-%COMP%]:hover:after, .t-cell[data-range=end][_ngcontent-%COMP%]:hover:after, .t-cell[data-range=active][_ngcontent-%COMP%]:hover:after{background:var(--tui-background-accent-1-hover)}}.t-cell[_ngcontent-%COMP%]{inline-size:calc(100% / 7)}[data-type=weekday][_ngcontent-%COMP%]{color:var(--tui-text-primary)}[data-type=weekend][_ngcontent-%COMP%]{color:var(--tui-text-negative)}.t-row[_ngcontent-%COMP%]{justify-content:flex-start}.t-row[_ngcontent-%COMP%]:first-child{justify-content:flex-end}.t-row_weekday[_ngcontent-%COMP%]{font:var(--tui-font-text-s);color:var(--tui-text-secondary);pointer-events:none}.t-cell_unavailable[_ngcontent-%COMP%]{opacity:var(--tui-disabled-opacity)}.t-dots[_ngcontent-%COMP%]{position:absolute;bottom:0;display:flex;justify-content:center;margin-top:-.5rem;padding-bottom:.25rem}.t-dot[_ngcontent-%COMP%]{display:inline-block;inline-size:.25rem;block-size:.25rem;border-radius:100%;margin:0 .0625rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n__decorate([tuiPure], TuiCalendarSheet.prototype, \"getRange\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCalendarSheet, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-calendar-sheet',\n      imports: [CommonModule, TuiCalendarSheetPipe, TuiHovered, TuiLet, TuiMapperPipe, TuiOrderWeekDaysPipe, TuiRepeatTimes],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class._picking]': 'isRangePicking'\n      },\n      template: \"<div class=\\\"t-row t-row_weekday\\\">\\n    <div\\n        *ngFor=\\\"let day of unorderedWeekDays$ | tuiOrderWeekDays | async\\\"\\n        class=\\\"t-cell\\\"\\n        [textContent]=\\\"day\\\"\\n    ></div>\\n</div>\\n<div *tuiLet=\\\"month | tuiCalendarSheet: true as sheet\\\">\\n    <div\\n        *tuiRepeatTimes=\\\"let rowIndex of sheet.length\\\"\\n        automation-id=\\\"tui-calendar-sheet__row\\\"\\n        class=\\\"t-row\\\"\\n    >\\n        <ng-container *tuiRepeatTimes=\\\"let colIndex of sheet[rowIndex]?.length || 0\\\">\\n            <ng-container *tuiLet=\\\"sheet[rowIndex]?.[colIndex] as item\\\">\\n                <div\\n                    *ngIf=\\\"item && (!itemIsUnavailable(item) || showAdjacent)\\\"\\n                    automation-id=\\\"tui-calendar-sheet__cell\\\"\\n                    class=\\\"t-cell\\\"\\n                    [attr.data-range]=\\\"getItemRange(item)\\\"\\n                    [attr.data-type]=\\\"item | tuiMapper: dayTypeHandler\\\"\\n                    [class.t-cell_disabled]=\\\"disabledItemHandler(item)\\\"\\n                    [class.t-cell_today]=\\\"itemIsToday(item)\\\"\\n                    [class.t-cell_unavailable]=\\\"itemIsUnavailable(item)\\\"\\n                    (click)=\\\"onItemClick(item)\\\"\\n                    (tuiHoveredChange)=\\\"onItemHovered($event && item)\\\"\\n                >\\n                    {{ item.day }}\\n                    <div\\n                        *ngIf=\\\"\\n                            item\\n                                | tuiMapper\\n                                    : toMarkers\\n                                    : itemIsToday(item)\\n                                    : getItemRange(item)\\n                                    : markerHandler as markers\\n                        \\\"\\n                        class=\\\"t-dots\\\"\\n                    >\\n                        <div\\n                            class=\\\"t-dot\\\"\\n                            [style.background]=\\\"markers?.[0]\\\"\\n                        ></div>\\n                        <div\\n                            *ngIf=\\\"markers.length > 1\\\"\\n                            class=\\\"t-dot\\\"\\n                            [style.background]=\\\"markers?.[1] || ''\\\"\\n                        ></div>\\n                    </div>\\n                </div>\\n            </ng-container>\\n        </ng-container>\\n    </div>\\n</div>\\n\",\n      styles: [\".t-row{display:flex;justify-content:flex-start;font:var(--tui-font-text-m)}.t-row:last-child{justify-content:flex-start}.t-cell{position:relative;display:flex;align-items:center;justify-content:center;line-height:2rem;isolation:isolate;cursor:pointer;overflow:hidden;border:.125rem solid transparent;box-sizing:border-box;-webkit-mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem));mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem))}.t-cell:first-child{border-inline-start-color:transparent!important}.t-cell:last-child{border-inline-end-color:transparent!important}.t-cell:before,.t-cell:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";z-index:-1;border-radius:var(--tui-radius-m)}.t-cell:after{-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat}.t-cell[data-range]:before{background:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range]:before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]:not(:first-child):before{border-top-left-radius:0;border-bottom-left-radius:0}.t-cell[data-range=middle]:not(:last-child):before{border-top-right-radius:0;border-bottom-right-radius:0}.t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:not(:last-child):before{right:-1rem}.t-cell[data-range=start]:after{background:var(--tui-background-accent-1)}.t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=end]:not(:first-child):before{left:-1rem}.t-cell[data-range=end]:after{background:var(--tui-background-accent-1);transform:scaleX(-1)}.t-cell[data-range=active]{color:var(--tui-text-primary-on-accent-1)}.t-cell[data-range=active]:after{background:var(--tui-background-accent-1);-webkit-mask:none;mask:none}.t-cell_disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}.t-cell_today{text-decoration:underline;text-underline-offset:.25rem}@media (hover: hover) and (pointer: fine){.t-cell:hover:not([data-range=start]):not([data-range=end]):before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:hover:after,.t-cell[data-range=end]:hover:after,.t-cell[data-range=active]:hover:after{background:var(--tui-background-accent-1-hover)}}.t-cell{inline-size:calc(100% / 7)}[data-type=weekday]{color:var(--tui-text-primary)}[data-type=weekend]{color:var(--tui-text-negative)}.t-row{justify-content:flex-start}.t-row:first-child{justify-content:flex-end}.t-row_weekday{font:var(--tui-font-text-s);color:var(--tui-text-secondary);pointer-events:none}.t-cell_unavailable{opacity:var(--tui-disabled-opacity)}.t-dots{position:absolute;bottom:0;display:flex;justify-content:center;margin-top:-.5rem;padding-bottom:.25rem}.t-dot{display:inline-block;inline-size:.25rem;block-size:.25rem;border-radius:100%;margin:0 .0625rem}\\n\"]\n    }]\n  }], null, {\n    month: [{\n      type: Input\n    }],\n    disabledItemHandler: [{\n      type: Input\n    }],\n    markerHandler: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    hoveredItem: [{\n      type: Input\n    }],\n    showAdjacent: [{\n      type: Input\n    }],\n    single: [{\n      type: Input\n    }],\n    hoveredItemChange: [{\n      type: Output\n    }],\n    dayClick: [{\n      type: Output\n    }],\n    getRange: []\n  });\n})();\nclass TuiCalendarSpin {\n  constructor() {\n    this.value = TuiMonth.currentLocal();\n    this.min = TUI_FIRST_DAY;\n    this.max = TUI_LAST_DAY;\n    this.valueChange = new EventEmitter();\n    this.yearClick = new EventEmitter();\n  }\n  onYearClick() {\n    this.yearClick.next(this.value);\n  }\n  append(date) {\n    const value = this.value.append(date);\n    if (this.min.monthSameOrAfter(value)) {\n      this.updateValue(this.min);\n    } else {\n      this.updateValue(this.max.monthSameOrBefore(value) ? this.max : value);\n    }\n  }\n  updateValue(value) {\n    if (this.value.monthSame(value)) {\n      return;\n    }\n    this.value = value;\n    this.valueChange.emit(value);\n  }\n  static {\n    this.ɵfac = function TuiCalendarSpin_Factory(t) {\n      return new (t || TuiCalendarSpin)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiCalendarSpin,\n      selectors: [[\"tui-calendar-spin\"]],\n      inputs: {\n        value: \"value\",\n        min: \"min\",\n        max: \"max\"\n      },\n      outputs: {\n        valueChange: \"valueChange\",\n        yearClick: \"yearClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 10,\n      consts: [[\"button\", \"\"], [3, \"leftClick\", \"rightClick\", \"focusable\", \"leftDisabled\", \"rightDisabled\"], [4, \"ngIf\", \"ngIfElse\"], [\"id\", \"year-btn\", \"automation-id\", \"tui-primitive-year-month-pagination__year-button\", \"tabIndex\", \"-1\", \"tuiLink\", \"\", \"type\", \"button\", 3, \"click\"]],\n      template: function TuiCalendarSpin_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"tui-spin-button\", 1);\n          i0.ɵɵlistener(\"leftClick\", function TuiCalendarSpin_Template_tui_spin_button_leftClick_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.append({\n              month: -1\n            }));\n          })(\"rightClick\", function TuiCalendarSpin_Template_tui_spin_button_rightClick_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.append({\n              month: 1\n            }));\n          });\n          i0.ɵɵtext(1);\n          i0.ɵɵpipe(2, \"tuiMonth\");\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵtemplate(4, TuiCalendarSpin_ng_container_4_Template, 2, 1, \"ng-container\", 2)(5, TuiCalendarSpin_ng_template_5_Template, 2, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const button_r4 = i0.ɵɵreference(6);\n          i0.ɵɵproperty(\"focusable\", false)(\"leftDisabled\", ctx.value.monthSameOrBefore(ctx.min))(\"rightDisabled\", ctx.value.monthSameOrAfter(ctx.max));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 8, i0.ɵɵpipeBind1(2, 6, ctx.value)), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.min.year === ctx.max.year)(\"ngIfElse\", button_r4);\n        }\n      },\n      dependencies: [AsyncPipe, NgIf, TuiLink, TuiMonthPipe, TuiSpinButton],\n      styles: [\"[_nghost-%COMP%]{display:block}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCalendarSpin, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-calendar-spin',\n      imports: [AsyncPipe, NgIf, TuiLink, TuiMonthPipe, TuiSpinButton],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<tui-spin-button\\n    [focusable]=\\\"false\\\"\\n    [leftDisabled]=\\\"value.monthSameOrBefore(min)\\\"\\n    [rightDisabled]=\\\"value.monthSameOrAfter(max)\\\"\\n    (leftClick)=\\\"append({month: -1})\\\"\\n    (rightClick)=\\\"append({month: 1})\\\"\\n>\\n    {{ value | tuiMonth | async }}\\n    <ng-container *ngIf=\\\"min.year === max.year; else button\\\">\\n        {{ value.formattedYear }}\\n    </ng-container>\\n    <ng-template #button>\\n        <button\\n            id=\\\"year-btn\\\"\\n            automation-id=\\\"tui-primitive-year-month-pagination__year-button\\\"\\n            tabIndex=\\\"-1\\\"\\n            tuiLink\\n            type=\\\"button\\\"\\n            (click)=\\\"onYearClick()\\\"\\n        >\\n            {{ value.formattedYear }}\\n        </button>\\n    </ng-template>\\n</tui-spin-button>\\n\",\n      styles: [\":host{display:block}\\n\"]\n    }]\n  }], null, {\n    value: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    yearClick: [{\n      type: Output\n    }]\n  });\n})();\nconst LIMIT = 100;\nconst ITEMS_IN_ROW = 4;\nclass TuiCalendarYear {\n  constructor() {\n    this.hoveredItem = null;\n    this.currentYear = TuiMonth.currentLocal().year;\n    this.value = null;\n    this.initialItem = this.currentYear;\n    this.min = MIN_YEAR;\n    this.max = MAX_YEAR;\n    this.rangeMode = false;\n    this.disabledItemHandler = TUI_FALSE_HANDLER;\n    this.yearClick = new EventEmitter();\n  }\n  isDisabled(item) {\n    return this.max && this.max < item || this.min && this.min > item || this.disabledItemHandler(item);\n  }\n  getItemRange(item) {\n    const {\n      value,\n      hoveredItem\n    } = this;\n    if (value instanceof TuiYear && value.year === item) {\n      return 'active';\n    }\n    if (tuiIsNumber(value)) {\n      return value === item ? 'active' : null;\n    }\n    if (!(value instanceof TuiMonthRange) && !(value instanceof TuiYear)) {\n      return value?.find(day => day.year === item) ? 'active' : null;\n    }\n    const hovered = this.isRangePicking ? hoveredItem : null;\n    const from = 'from' in value ? value.from?.year : value.year;\n    const to = 'from' in value ? value.to.year : value.year;\n    const min = Math.min(from, hovered ?? to);\n    const max = Math.max(from, hovered ?? to);\n    if (min === max && from === to && from === item) {\n      return 'active';\n    }\n    if (min === item) {\n      return 'start';\n    }\n    if (max === item) {\n      return 'end';\n    }\n    return min < item && item < max ? 'middle' : null;\n  }\n  onItemHovered(hovered, item) {\n    this.updateHoveredItem(hovered, item);\n  }\n  get isRangePicking() {\n    return this.rangeMode && (this.value instanceof TuiDay || this.value instanceof TuiMonth);\n  }\n  get rows() {\n    return Math.ceil((this.calculatedMax - this.calculatedMin) / ITEMS_IN_ROW);\n  }\n  scrollItemIntoView(item) {\n    return this.initialItem === item;\n  }\n  getItem(rowIndex, colIndex) {\n    return rowIndex * ITEMS_IN_ROW + colIndex + this.calculatedMin;\n  }\n  itemIsToday(item) {\n    return this.currentYear === item;\n  }\n  get calculatedMin() {\n    const initial = this.initialItem - LIMIT;\n    const min = this.min ?? MIN_YEAR;\n    return min > initial ? min : initial;\n  }\n  get calculatedMax() {\n    const initial = this.initialItem + LIMIT;\n    const max = this.max ?? MAX_YEAR;\n    return max < initial ? max + 1 : initial;\n  }\n  updateHoveredItem(hovered, item) {\n    this.hoveredItem = hovered ? item : null;\n  }\n  static {\n    this.ɵfac = function TuiCalendarYear_Factory(t) {\n      return new (t || TuiCalendarYear)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiCalendarYear,\n      selectors: [[\"tui-calendar-year\"]],\n      hostVars: 2,\n      hostBindings: function TuiCalendarYear_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_picking\", ctx.isRangePicking);\n        }\n      },\n      inputs: {\n        value: \"value\",\n        initialItem: \"initialItem\",\n        min: \"min\",\n        max: \"max\",\n        rangeMode: \"rangeMode\",\n        disabledItemHandler: \"disabledItemHandler\"\n      },\n      outputs: {\n        yearClick: \"yearClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[\"automation-id\", \"tui-calendar-year__row\", \"class\", \"t-row\", 4, \"tuiRepeatTimes\", \"tuiRepeatTimesOf\"], [\"automation-id\", \"tui-calendar-year__row\", 1, \"t-row\"], [4, \"tuiRepeatTimes\", \"tuiRepeatTimesOf\"], [\"automation-id\", \"tui-calendar-year__cell\", \"class\", \"t-cell\", 3, \"t-cell_disabled\", \"t-cell_today\", \"tuiScrollIntoView\", \"click\", \"tuiHoveredChange\", 4, \"tuiLet\"], [\"automation-id\", \"tui-calendar-year__cell\", 1, \"t-cell\", 3, \"click\", \"tuiHoveredChange\", \"tuiScrollIntoView\"]],\n      template: function TuiCalendarYear_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiCalendarYear_div_0_Template, 2, 1, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"tuiRepeatTimesOf\", ctx.rows);\n        }\n      },\n      dependencies: [TuiHovered, TuiLet, TuiRepeatTimes, TuiScrollIntoView],\n      styles: [\".t-row[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;font:var(--tui-font-text-m)}.t-row[_ngcontent-%COMP%]:first-child{justify-content:flex-end}.t-row[_ngcontent-%COMP%]:last-child{justify-content:flex-start}.t-cell[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;justify-content:center;line-height:2rem;isolation:isolate;cursor:pointer;overflow:hidden;border:.125rem solid transparent;box-sizing:border-box;-webkit-mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem));mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem))}.t-cell[_ngcontent-%COMP%]:first-child{border-inline-start-color:transparent!important}.t-cell[_ngcontent-%COMP%]:last-child{border-inline-end-color:transparent!important}.t-cell[_ngcontent-%COMP%]:before, .t-cell[_ngcontent-%COMP%]:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";z-index:-1;border-radius:var(--tui-radius-m)}.t-cell[_ngcontent-%COMP%]:after{-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat}.t-cell[data-range][_ngcontent-%COMP%]:before{background:var(--tui-background-neutral-1)}._picking[_nghost-%COMP%]   .t-cell[data-range][_ngcontent-%COMP%]:before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle][_ngcontent-%COMP%]{border-color:var(--tui-background-neutral-1)}._picking[_nghost-%COMP%]   .t-cell[data-range=middle][_ngcontent-%COMP%]{border-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle][_ngcontent-%COMP%]:not(:first-child):before{border-top-left-radius:0;border-bottom-left-radius:0}.t-cell[data-range=middle][_ngcontent-%COMP%]:not(:last-child):before{border-top-right-radius:0;border-bottom-right-radius:0}.t-cell[data-range=start][_ngcontent-%COMP%]{border-inline-end-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}._picking[_nghost-%COMP%]   .t-cell[data-range=start][_ngcontent-%COMP%]{border-inline-end-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start][_ngcontent-%COMP%]:not(:last-child):before{right:-1rem}.t-cell[data-range=start][_ngcontent-%COMP%]:after{background:var(--tui-background-accent-1)}.t-cell[data-range=end][_ngcontent-%COMP%]{border-inline-start-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}._picking[_nghost-%COMP%]   .t-cell[data-range=end][_ngcontent-%COMP%]{border-inline-start-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=end][_ngcontent-%COMP%]:not(:first-child):before{left:-1rem}.t-cell[data-range=end][_ngcontent-%COMP%]:after{background:var(--tui-background-accent-1);transform:scaleX(-1)}.t-cell[data-range=active][_ngcontent-%COMP%]{color:var(--tui-text-primary-on-accent-1)}.t-cell[data-range=active][_ngcontent-%COMP%]:after{background:var(--tui-background-accent-1);-webkit-mask:none;mask:none}.t-cell_disabled[_ngcontent-%COMP%]{opacity:var(--tui-disabled-opacity);pointer-events:none}.t-cell_today[_ngcontent-%COMP%]{text-decoration:underline;text-underline-offset:.25rem}@media (hover: hover) and (pointer: fine){.t-cell[_ngcontent-%COMP%]:hover:not([data-range=start]):not([data-range=end]):before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start][_ngcontent-%COMP%]:hover:after, .t-cell[data-range=end][_ngcontent-%COMP%]:hover:after, .t-cell[data-range=active][_ngcontent-%COMP%]:hover:after{background:var(--tui-background-accent-1-hover)}}[_nghost-%COMP%]{display:block;padding-inline-end:1rem}.t-cell[_ngcontent-%COMP%]{flex:1;border-block-start-width:.5rem;border-block-end-width:.5rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCalendarYear, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-calendar-year',\n      imports: [TuiHovered, TuiLet, TuiRepeatTimes, TuiScrollIntoView],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class._picking]': 'isRangePicking'\n      },\n      template: \"<div\\n    *tuiRepeatTimes=\\\"let rowIndex of rows\\\"\\n    automation-id=\\\"tui-calendar-year__row\\\"\\n    class=\\\"t-row\\\"\\n>\\n    <ng-container *tuiRepeatTimes=\\\"let colIndex of 4\\\">\\n        <div\\n            *tuiLet=\\\"getItem(rowIndex, colIndex) as item\\\"\\n            automation-id=\\\"tui-calendar-year__cell\\\"\\n            class=\\\"t-cell\\\"\\n            [attr.data-range]=\\\"getItemRange(item)\\\"\\n            [class.t-cell_disabled]=\\\"isDisabled(item)\\\"\\n            [class.t-cell_today]=\\\"itemIsToday(item)\\\"\\n            [tuiScrollIntoView]=\\\"scrollItemIntoView(item)\\\"\\n            (click)=\\\"yearClick.emit(item)\\\"\\n            (tuiHoveredChange)=\\\"onItemHovered($event, item)\\\"\\n        >\\n            {{ item }}\\n        </div>\\n    </ng-container>\\n</div>\\n\",\n      styles: [\".t-row{display:flex;justify-content:flex-start;font:var(--tui-font-text-m)}.t-row:first-child{justify-content:flex-end}.t-row:last-child{justify-content:flex-start}.t-cell{position:relative;display:flex;align-items:center;justify-content:center;line-height:2rem;isolation:isolate;cursor:pointer;overflow:hidden;border:.125rem solid transparent;box-sizing:border-box;-webkit-mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem));mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem))}.t-cell:first-child{border-inline-start-color:transparent!important}.t-cell:last-child{border-inline-end-color:transparent!important}.t-cell:before,.t-cell:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";z-index:-1;border-radius:var(--tui-radius-m)}.t-cell:after{-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat}.t-cell[data-range]:before{background:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range]:before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]:not(:first-child):before{border-top-left-radius:0;border-bottom-left-radius:0}.t-cell[data-range=middle]:not(:last-child):before{border-top-right-radius:0;border-bottom-right-radius:0}.t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:not(:last-child):before{right:-1rem}.t-cell[data-range=start]:after{background:var(--tui-background-accent-1)}.t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=end]:not(:first-child):before{left:-1rem}.t-cell[data-range=end]:after{background:var(--tui-background-accent-1);transform:scaleX(-1)}.t-cell[data-range=active]{color:var(--tui-text-primary-on-accent-1)}.t-cell[data-range=active]:after{background:var(--tui-background-accent-1);-webkit-mask:none;mask:none}.t-cell_disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}.t-cell_today{text-decoration:underline;text-underline-offset:.25rem}@media (hover: hover) and (pointer: fine){.t-cell:hover:not([data-range=start]):not([data-range=end]):before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:hover:after,.t-cell[data-range=end]:hover:after,.t-cell[data-range=active]:hover:after{background:var(--tui-background-accent-1-hover)}}:host{display:block;padding-inline-end:1rem}.t-cell{flex:1;border-block-start-width:.5rem;border-block-end-width:.5rem}\\n\"]\n    }]\n  }], null, {\n    value: [{\n      type: Input\n    }],\n    initialItem: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    rangeMode: [{\n      type: Input\n    }],\n    disabledItemHandler: [{\n      type: Input\n    }],\n    yearClick: [{\n      type: Output\n    }]\n  });\n})();\nclass TuiCalendar {\n  constructor() {\n    this.cdr = inject(ChangeDetectorRef);\n    this.day = null;\n    this.view = 'month';\n    this.options = inject(TUI_CALENDAR_SHEET_OPTIONS);\n    this.month = TuiMonth.currentLocal();\n    this.disabledItemHandler = TUI_FALSE_HANDLER;\n    this.min = TUI_FIRST_DAY;\n    this.max = TUI_LAST_DAY;\n    this.minViewedMonth = TUI_FIRST_DAY;\n    this.maxViewedMonth = TUI_LAST_DAY;\n    this.hoveredItem = null;\n    this.showAdjacent = true;\n    this.markerHandler = null;\n    this.dayClick = new EventEmitter();\n    this.monthChange = new EventEmitter();\n    this.hoveredItemChange = new EventEmitter();\n    /** @deprecated for private use only until Calendars are refactored */\n    this.valueChange = new Subject();\n    this.disabledItemHandlerMapper = (disabledItemHandler, min, max) => item => item.dayBefore(min) || item.dayAfter(max) || disabledItemHandler(item);\n  }\n  set value(value) {\n    this.cdr.markForCheck();\n    this.day = value;\n    if (this.showAdjacent && value instanceof TuiDay && value.daySameOrBefore(TUI_LAST_DISPLAYED_DAY)) {\n      this.month = value;\n    }\n  }\n  set initialView(view) {\n    this.view = view;\n  }\n  get value() {\n    return this.day;\n  }\n  onPaginationValueChange(month) {\n    this.updateViewedMonth(month);\n  }\n  onDayClick(day) {\n    this.dayClick.emit(day);\n    this.valueChange.next(day);\n  }\n  onHoveredItemChange(day) {\n    this.updateHoveredDay(day);\n  }\n  get computedMin() {\n    return this.min ?? TUI_FIRST_DAY;\n  }\n  get computedMax() {\n    return this.max ?? TUI_LAST_DAY;\n  }\n  get computedMinViewedMonth() {\n    const min = this.computedMin;\n    const minViewed = this.minViewedMonth ?? TUI_FIRST_DAY;\n    return minViewed.monthSameOrAfter(min) ? minViewed : min;\n  }\n  get computedMaxViewedMonth() {\n    const max = this.computedMax;\n    const maxViewed = this.maxViewedMonth ?? TUI_LAST_DAY;\n    return maxViewed.monthSameOrBefore(max) ? maxViewed : max;\n  }\n  get isInYearView() {\n    return this.view === 'year';\n  }\n  onPaginationYearClick() {\n    this.view = 'year';\n  }\n  onPickerYearClick(year) {\n    this.view = 'month';\n    this.updateViewedMonth(new TuiMonth(year, this.month.month));\n  }\n  updateViewedMonth(month) {\n    if (this.month.monthSame(month)) {\n      return;\n    }\n    this.month = month;\n    this.monthChange.emit(month);\n  }\n  updateHoveredDay(day) {\n    if (tuiNullableSame(this.hoveredItem, day, (a, b) => a.daySame(b))) {\n      return;\n    }\n    this.hoveredItem = day;\n    this.hoveredItemChange.emit(day);\n  }\n  static {\n    this.ɵfac = function TuiCalendar_Factory(t) {\n      return new (t || TuiCalendar)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiCalendar,\n      selectors: [[\"tui-calendar\"]],\n      hostBindings: function TuiCalendar_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"pointerdown.prevent.zoneless\", function TuiCalendar_pointerdown_prevent_zoneless_HostBindingHandler() {\n            return 0;\n          });\n        }\n      },\n      inputs: {\n        month: \"month\",\n        disabledItemHandler: \"disabledItemHandler\",\n        min: \"min\",\n        max: \"max\",\n        minViewedMonth: \"minViewedMonth\",\n        maxViewedMonth: \"maxViewedMonth\",\n        hoveredItem: \"hoveredItem\",\n        showAdjacent: \"showAdjacent\",\n        markerHandler: \"markerHandler\",\n        value: \"value\",\n        initialView: \"initialView\"\n      },\n      outputs: {\n        dayClick: \"dayClick\",\n        monthChange: \"monthChange\",\n        hoveredItemChange: \"hoveredItemChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsAuxiliary(TuiCalendar)]), i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[\"calendar\", \"\"], [\"automation-id\", \"tui-calendar__scrollbar\", \"class\", \"t-scrollbar\", 4, \"ngIf\", \"ngIfElse\"], [\"automation-id\", \"tui-calendar__scrollbar\", 1, \"t-scrollbar\"], [\"automation-id\", \"tui-calendar__year\", 3, \"yearClick\", \"initialItem\", \"max\", \"min\", \"rangeMode\", \"value\"], [\"automation-id\", \"tui-calendar__pagination\", 1, \"t-pagination\", 3, \"valueChange\", \"yearClick\", \"max\", \"min\", \"value\"], [\"automation-id\", \"tui-calendar__calendar\", 3, \"dayClick\", \"hoveredItemChange\", \"disabledItemHandler\", \"hoveredItem\", \"markerHandler\", \"month\", \"showAdjacent\", \"value\"]],\n      template: function TuiCalendar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiCalendar_tui_scrollbar_0_Template, 2, 5, \"tui-scrollbar\", 1)(1, TuiCalendar_ng_template_1_Template, 3, 14, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const calendar_r4 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isInYearView)(\"ngIfElse\", calendar_r4);\n        }\n      },\n      dependencies: [NgIf, TuiCalendarSheet, TuiCalendarSpin, TuiCalendarYear, TuiMapperPipe, TuiScrollbar],\n      styles: [\"[_nghost-%COMP%]{display:block;min-block-size:20.25rem;inline-size:18rem;padding:1rem 1.125rem;box-sizing:border-box;flex-shrink:0}tui-dropdown-mobile[_nghost-%COMP%], tui-dropdown-mobile   [_nghost-%COMP%]{inline-size:100%}.t-scrollbar[_ngcontent-%COMP%]{block-size:18.25rem;inline-size:calc(100% + 1rem)}.t-pagination[_ngcontent-%COMP%]{margin-bottom:1rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCalendar, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-calendar',\n      imports: [NgIf, TuiCalendarSheet, TuiCalendarSpin, TuiCalendarYear, TuiMapperPipe, TuiScrollbar],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAsAuxiliary(TuiCalendar)],\n      host: {\n        '(pointerdown.prevent.zoneless)': '0'\n      },\n      template: \"<tui-scrollbar\\n    *ngIf=\\\"isInYearView; else calendar\\\"\\n    automation-id=\\\"tui-calendar__scrollbar\\\"\\n    class=\\\"t-scrollbar\\\"\\n>\\n    <tui-calendar-year\\n        automation-id=\\\"tui-calendar__year\\\"\\n        [initialItem]=\\\"month.year\\\"\\n        [max]=\\\"computedMax.year\\\"\\n        [min]=\\\"computedMin.year\\\"\\n        [rangeMode]=\\\"options.rangeMode\\\"\\n        [value]=\\\"value\\\"\\n        (yearClick)=\\\"onPickerYearClick($event)\\\"\\n    />\\n</tui-scrollbar>\\n<ng-template #calendar>\\n    <tui-calendar-spin\\n        automation-id=\\\"tui-calendar__pagination\\\"\\n        class=\\\"t-pagination\\\"\\n        [max]=\\\"computedMaxViewedMonth\\\"\\n        [min]=\\\"computedMinViewedMonth\\\"\\n        [value]=\\\"month\\\"\\n        (valueChange)=\\\"onPaginationValueChange($event)\\\"\\n        (yearClick)=\\\"onPaginationYearClick()\\\"\\n    />\\n    <tui-calendar-sheet\\n        automation-id=\\\"tui-calendar__calendar\\\"\\n        [disabledItemHandler]=\\\"disabledItemHandler | tuiMapper: disabledItemHandlerMapper : computedMin : computedMax\\\"\\n        [hoveredItem]=\\\"hoveredItem\\\"\\n        [markerHandler]=\\\"markerHandler\\\"\\n        [month]=\\\"month\\\"\\n        [showAdjacent]=\\\"showAdjacent\\\"\\n        [value]=\\\"value\\\"\\n        (dayClick)=\\\"onDayClick($event)\\\"\\n        (hoveredItemChange)=\\\"onHoveredItemChange($event)\\\"\\n    />\\n</ng-template>\\n\",\n      styles: [\":host{display:block;min-block-size:20.25rem;inline-size:18rem;padding:1rem 1.125rem;box-sizing:border-box;flex-shrink:0}:host-context(tui-dropdown-mobile){inline-size:100%}.t-scrollbar{block-size:18.25rem;inline-size:calc(100% + 1rem)}.t-pagination{margin-bottom:1rem}\\n\"]\n    }]\n  }], null, {\n    month: [{\n      type: Input\n    }],\n    disabledItemHandler: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    minViewedMonth: [{\n      type: Input\n    }],\n    maxViewedMonth: [{\n      type: Input\n    }],\n    hoveredItem: [{\n      type: Input\n    }],\n    showAdjacent: [{\n      type: Input\n    }],\n    markerHandler: [{\n      type: Input\n    }],\n    dayClick: [{\n      type: Output\n    }],\n    monthChange: [{\n      type: Output\n    }],\n    hoveredItemChange: [{\n      type: Output\n    }],\n    value: [{\n      type: Input\n    }],\n    initialView: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_CALENDAR_SHEET_DEFAULT_OPTIONS, TUI_CALENDAR_SHEET_OPTIONS, TuiCalendar, TuiCalendarSheet, TuiCalendarSpin, TuiCalendarYear, tuiCalendarSheetOptionsProvider };", "map": {"version": 3, "names": ["i1", "CommonModule", "AsyncPipe", "NgIf", "i0", "inject", "EventEmitter", "Component", "ChangeDetectionStrategy", "Input", "Output", "ChangeDetectorRef", "TUI_FALSE_HANDLER", "TuiDay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TuiDayRange", "TUI_FIRST_DAY", "TUI_LAST_DAY", "MIN_YEAR", "MAX_YEAR", "<PERSON><PERSON><PERSON><PERSON>", "TuiMonthRange", "TUI_LAST_DISPLAYED_DAY", "TuiMapperPipe", "tuiCreateToken", "tuiProvideOptions", "tuiNullable<PERSON>ame", "tuiPure", "tuiIsNumber", "TuiScrollIntoView", "TuiScrollbar", "TUI_SHORT_WEEK_DAYS", "TUI_DAY_TYPE_HANDLER", "tuiAsAuxiliary", "Subject", "__decorate", "TuiHovered", "TuiLet", "TuiRepeatTimes", "TuiCalendarSheetPipe", "TuiOrderWeekDaysPipe", "TuiMonthPipe", "TuiLink", "TuiSpinButton", "_c0", "a0", "a1", "a2", "a3", "a4", "TuiCalendarSheet_div_1_Template", "rf", "ctx", "ɵɵelement", "day_r1", "$implicit", "ɵɵproperty", "TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_div_1_div_3_div_2_Template", "markers_r5", "ɵɵnextContext", "ngIf", "ɵɵstyleProp", "TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_div_1_div_3_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "ɵɵadvance", "length", "TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_div_1_Template", "_r2", "ɵɵgetCurrentView", "ɵɵpipe", "ɵɵlistener", "TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_div_1_Template_div_click_0_listener", "ɵɵrestoreView", "item_r3", "tuiLet", "ctx_r3", "ɵɵresetView", "onItemClick", "TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_div_1_Template_div_tuiHoveredChange_0_listener", "$event", "onItemHovered", "ɵɵtext", "ɵɵclassProp", "disabledItemHandler", "itemIsToday", "itemIsUnavailable", "ɵɵattribute", "getItemRange", "ɵɵpipeBind2", "dayType<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "day", "ɵɵpipeBindV", "ɵɵpureFunction5", "toM<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TuiCalendarSheet_div_4_div_1_ng_container_1_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "showAd<PERSON><PERSON>", "TuiCalendarSheet_div_4_div_1_ng_container_1_Template", "colIndex_r6", "rowIndex_r7", "sheet_r8", "TuiCalendarSheet_div_4_div_1_Template", "TuiCalendarSheet_div_4_Template", "TuiCalendarSpin_ng_container_4_Template", "ctx_r1", "value", "formattedYear", "TuiCalendarSpin_ng_template_5_Template", "_r3", "TuiCalendarSpin_ng_template_5_Template_button_click_0_listener", "onYearClick", "TuiCalendarYear_div_0_ng_container_1_div_1_Template", "_r1", "TuiCalendarYear_div_0_ng_container_1_div_1_Template_div_click_0_listener", "item_r2", "ctx_r2", "yearClick", "emit", "TuiCalendarYear_div_0_ng_container_1_div_1_Template_div_tuiHoveredChange_0_listener", "isDisabled", "scrollItemIntoView", "TuiCalendarYear_div_0_ng_container_1_Template", "colIndex_r4", "rowIndex_r5", "getItem", "TuiCalendarYear_div_0_Template", "TuiCalendar_tui_scrollbar_0_Template", "TuiCalendar_tui_scrollbar_0_Template_tui_calendar_year_yearClick_1_listener", "onPickerYearClick", "month", "year", "computedMax", "computedMin", "options", "rangeMode", "TuiCalendar_ng_template_1_Template", "TuiCalendar_ng_template_1_Template_tui_calendar_spin_valueChange_0_listener", "onPaginationValueChange", "TuiCalendar_ng_template_1_Template_tui_calendar_spin_yearClick_0_listener", "onPaginationYearClick", "TuiCalendar_ng_template_1_Template_tui_calendar_sheet_dayClick_1_listener", "onDayClick", "TuiCalendar_ng_template_1_Template_tui_calendar_sheet_hoveredItemChange_1_listener", "onHoveredItemChange", "computedMaxViewedMonth", "computed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵpipeBind4", "disabledItemHandlerMapper", "hoveredItem", "TUI_CALENDAR_SHEET_DEFAULT_OPTIONS", "TUI_CALENDAR_SHEET_OPTIONS", "tuiCalendarSheetOptionsProvider", "TuiCalendarSheet", "constructor", "today", "currentLocal", "unorderedWeekDays$", "single", "hoveredItemChange", "dayClick", "range", "includes", "markers", "itemIsInterval", "isSingleDay", "from", "daySameOrBefore", "to", "dayAfter", "sort", "item", "updateHoveredItem", "computedRangeMode", "daySame", "find", "getRange", "dayBefore", "isRangePicking", "monthSame", "a", "b", "ɵfac", "TuiCalendarSheet_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "TuiCalendarSheet_HostBindings", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiCalendarSheet_Template", "ɵɵpipeBind1", "dependencies", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles", "changeDetection", "prototype", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "host", "TuiCalendarSpin", "min", "max", "valueChange", "next", "append", "date", "monthSameOrAfter", "updateValue", "monthSameOrBefore", "TuiCalendarSpin_Factory", "TuiCalendarSpin_Template", "TuiCalendar<PERSON>pin_Template_tui_spin_button_leftClick_0_listener", "TuiCalendar<PERSON>pin_Template_tui_spin_button_rightClick_0_listener", "ɵɵtemplateRefExtractor", "button_r4", "ɵɵreference", "LIMIT", "ITEMS_IN_ROW", "TuiCalendarYear", "currentYear", "initialItem", "hovered", "Math", "rows", "ceil", "calculatedMax", "calculatedMin", "rowIndex", "colIndex", "initial", "TuiCalendarYear_Factory", "TuiCalendarYear_HostBindings", "TuiCalendarYear_Template", "TuiCalendar", "cdr", "view", "minViewed<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monthChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initialView", "updateViewedMonth", "updateHoveredDay", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "isInYearView", "TuiCalendar_Factory", "TuiCalendar_HostBindings", "TuiCalendar_pointerdown_prevent_zoneless_HostBindingHandler", "ɵɵProvidersFeature", "TuiCalendar_Template", "calendar_r4", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-components-calendar.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule, AsyncPipe, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, ChangeDetectorRef } from '@angular/core';\nimport { TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { TuiDay, TuiMonth, TuiDayRange, TUI_FIRST_DAY, TUI_LAST_DAY, MIN_YEAR, MAX_YEAR, TuiYear, TuiMonthRange, TUI_LAST_DISPLAYED_DAY } from '@taiga-ui/cdk/date-time';\nimport { TuiMapperPipe } from '@taiga-ui/cdk/pipes/mapper';\nimport { tuiCreateToken, tuiProvideOptions, tuiNullableSame, tuiPure, tuiIsNumber } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiScrollIntoView, TuiScrollbar } from '@taiga-ui/core/components/scrollbar';\nimport { TUI_SHORT_WEEK_DAYS, TUI_DAY_TYPE_HANDLER, tuiAsAuxiliary } from '@taiga-ui/core/tokens';\nimport { Subject } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { TuiHovered } from '@taiga-ui/cdk/directives/hovered';\nimport { TuiLet } from '@taiga-ui/cdk/directives/let';\nimport { TuiRepeatTimes } from '@taiga-ui/cdk/directives/repeat-times';\nimport { TuiCalendarSheetPipe, TuiOrderWeekDaysPipe, TuiMonthPipe } from '@taiga-ui/core/pipes';\nimport { TuiLink } from '@taiga-ui/core/components/link';\nimport { TuiSpinButton } from '@taiga-ui/core/components/spin-button';\n\nconst TUI_CALENDAR_SHEET_DEFAULT_OPTIONS = {\n    rangeMode: false,\n};\nconst TUI_CALENDAR_SHEET_OPTIONS = tuiCreateToken(TUI_CALENDAR_SHEET_DEFAULT_OPTIONS);\nfunction tuiCalendarSheetOptionsProvider(options) {\n    return tuiProvideOptions(TUI_CALENDAR_SHEET_OPTIONS, options, TUI_CALENDAR_SHEET_DEFAULT_OPTIONS);\n}\n\nclass TuiCalendarSheet {\n    constructor() {\n        this.options = inject(TUI_CALENDAR_SHEET_OPTIONS);\n        this.today = TuiDay.currentLocal();\n        this.unorderedWeekDays$ = inject(TUI_SHORT_WEEK_DAYS);\n        this.dayTypeHandler = inject(TUI_DAY_TYPE_HANDLER);\n        this.month = TuiMonth.currentLocal();\n        this.disabledItemHandler = TUI_FALSE_HANDLER;\n        this.markerHandler = null;\n        this.value = null;\n        this.hoveredItem = null;\n        this.showAdjacent = true;\n        /**\n         * @deprecated use static DI options instead\n         * ```\n         * tuiCalendarSheetOptionsProvider({rangeMode: true})\n         * ```\n         * TODO(v5): delete it\n         */\n        this.single = true;\n        this.hoveredItemChange = new EventEmitter();\n        this.dayClick = new EventEmitter();\n        this.toMarkers = (day, today, range, markerHandler) => {\n            if (today || ['active', 'end', 'start'].includes(range || '')) {\n                return null;\n            }\n            const markers = markerHandler?.(day);\n            return markers?.length ? markers : null;\n        };\n    }\n    /**\n     * @deprecated TODO(v5): delete it. It is used nowhere except unit tests\n     */\n    itemIsInterval(day) {\n        const { value, hoveredItem } = this;\n        if (!(value instanceof TuiDayRange)) {\n            return false;\n        }\n        if (!value.isSingleDay) {\n            return value.from.daySameOrBefore(day) && value.to.dayAfter(day);\n        }\n        if (hoveredItem === null) {\n            return false;\n        }\n        const range = TuiDayRange.sort(value.from, hoveredItem);\n        return range.from.daySameOrBefore(day) && range.to.dayAfter(day);\n    }\n    onItemHovered(item) {\n        this.updateHoveredItem(item || null);\n    }\n    getItemRange(item) {\n        const { value, hoveredItem } = this;\n        if (!value) {\n            return null;\n        }\n        if (value instanceof TuiDay && !this.computedRangeMode) {\n            return value.daySame(item) ? 'active' : null;\n        }\n        if (value instanceof TuiDayRange && value.isSingleDay) {\n            return value.from.daySame(item) ? 'active' : null;\n        }\n        if (!(value instanceof TuiDay) && !(value instanceof TuiDayRange)) {\n            return value.find((day) => day.daySame(item)) ? 'active' : null;\n        }\n        const range = this.getRange(value, hoveredItem);\n        if (range.isSingleDay && range.from.daySame(item)) {\n            return 'active';\n        }\n        if (range.from.daySame(item)) {\n            return 'start';\n        }\n        if (range.to.daySame(item)) {\n            return 'end';\n        }\n        return range.from.dayBefore(item) && range.to.dayAfter(item) ? 'middle' : null;\n    }\n    get computedRangeMode() {\n        return !this.single || this.options.rangeMode;\n    }\n    get isRangePicking() {\n        return this.computedRangeMode\n            ? this.value instanceof TuiDay\n            : /**\n               * Only for backward compatibility!\n               * TODO(v5): replace with `this.options.rangeMode && this.value instanceof TuiDay`\n               */\n                this.value instanceof TuiDayRange && this.value.isSingleDay;\n    }\n    itemIsToday(item) {\n        return this.today.daySame(item);\n    }\n    itemIsUnavailable(item) {\n        return !this.month.monthSame(item);\n    }\n    onItemClick(item) {\n        this.dayClick.emit(item);\n    }\n    getRange(value, hoveredItem) {\n        if (value instanceof TuiDay) {\n            return TuiDayRange.sort(value, hoveredItem ?? value);\n        }\n        return value.isSingleDay\n            ? TuiDayRange.sort(value.from, hoveredItem ?? value.to)\n            : value;\n    }\n    updateHoveredItem(day) {\n        if (tuiNullableSame(this.hoveredItem, day, (a, b) => a.daySame(b))) {\n            return;\n        }\n        this.hoveredItem = day;\n        this.hoveredItemChange.emit(day);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendarSheet, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCalendarSheet, isStandalone: true, selector: \"tui-calendar-sheet\", inputs: { month: \"month\", disabledItemHandler: \"disabledItemHandler\", markerHandler: \"markerHandler\", value: \"value\", hoveredItem: \"hoveredItem\", showAdjacent: \"showAdjacent\", single: \"single\" }, outputs: { hoveredItemChange: \"hoveredItemChange\", dayClick: \"dayClick\" }, host: { properties: { \"class._picking\": \"isRangePicking\" } }, ngImport: i0, template: \"<div class=\\\"t-row t-row_weekday\\\">\\n    <div\\n        *ngFor=\\\"let day of unorderedWeekDays$ | tuiOrderWeekDays | async\\\"\\n        class=\\\"t-cell\\\"\\n        [textContent]=\\\"day\\\"\\n    ></div>\\n</div>\\n<div *tuiLet=\\\"month | tuiCalendarSheet: true as sheet\\\">\\n    <div\\n        *tuiRepeatTimes=\\\"let rowIndex of sheet.length\\\"\\n        automation-id=\\\"tui-calendar-sheet__row\\\"\\n        class=\\\"t-row\\\"\\n    >\\n        <ng-container *tuiRepeatTimes=\\\"let colIndex of sheet[rowIndex]?.length || 0\\\">\\n            <ng-container *tuiLet=\\\"sheet[rowIndex]?.[colIndex] as item\\\">\\n                <div\\n                    *ngIf=\\\"item && (!itemIsUnavailable(item) || showAdjacent)\\\"\\n                    automation-id=\\\"tui-calendar-sheet__cell\\\"\\n                    class=\\\"t-cell\\\"\\n                    [attr.data-range]=\\\"getItemRange(item)\\\"\\n                    [attr.data-type]=\\\"item | tuiMapper: dayTypeHandler\\\"\\n                    [class.t-cell_disabled]=\\\"disabledItemHandler(item)\\\"\\n                    [class.t-cell_today]=\\\"itemIsToday(item)\\\"\\n                    [class.t-cell_unavailable]=\\\"itemIsUnavailable(item)\\\"\\n                    (click)=\\\"onItemClick(item)\\\"\\n                    (tuiHoveredChange)=\\\"onItemHovered($event && item)\\\"\\n                >\\n                    {{ item.day }}\\n                    <div\\n                        *ngIf=\\\"\\n                            item\\n                                | tuiMapper\\n                                    : toMarkers\\n                                    : itemIsToday(item)\\n                                    : getItemRange(item)\\n                                    : markerHandler as markers\\n                        \\\"\\n                        class=\\\"t-dots\\\"\\n                    >\\n                        <div\\n                            class=\\\"t-dot\\\"\\n                            [style.background]=\\\"markers?.[0]\\\"\\n                        ></div>\\n                        <div\\n                            *ngIf=\\\"markers.length > 1\\\"\\n                            class=\\\"t-dot\\\"\\n                            [style.background]=\\\"markers?.[1] || ''\\\"\\n                        ></div>\\n                    </div>\\n                </div>\\n            </ng-container>\\n        </ng-container>\\n    </div>\\n</div>\\n\", styles: [\".t-row{display:flex;justify-content:flex-start;font:var(--tui-font-text-m)}.t-row:last-child{justify-content:flex-start}.t-cell{position:relative;display:flex;align-items:center;justify-content:center;line-height:2rem;isolation:isolate;cursor:pointer;overflow:hidden;border:.125rem solid transparent;box-sizing:border-box;-webkit-mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem));mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem))}.t-cell:first-child{border-inline-start-color:transparent!important}.t-cell:last-child{border-inline-end-color:transparent!important}.t-cell:before,.t-cell:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";z-index:-1;border-radius:var(--tui-radius-m)}.t-cell:after{-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat}.t-cell[data-range]:before{background:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range]:before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]:not(:first-child):before{border-top-left-radius:0;border-bottom-left-radius:0}.t-cell[data-range=middle]:not(:last-child):before{border-top-right-radius:0;border-bottom-right-radius:0}.t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:not(:last-child):before{right:-1rem}.t-cell[data-range=start]:after{background:var(--tui-background-accent-1)}.t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=end]:not(:first-child):before{left:-1rem}.t-cell[data-range=end]:after{background:var(--tui-background-accent-1);transform:scaleX(-1)}.t-cell[data-range=active]{color:var(--tui-text-primary-on-accent-1)}.t-cell[data-range=active]:after{background:var(--tui-background-accent-1);-webkit-mask:none;mask:none}.t-cell_disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}.t-cell_today{text-decoration:underline;text-underline-offset:.25rem}@media (hover: hover) and (pointer: fine){.t-cell:hover:not([data-range=start]):not([data-range=end]):before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:hover:after,.t-cell[data-range=end]:hover:after,.t-cell[data-range=active]:hover:after{background:var(--tui-background-accent-1-hover)}}.t-cell{inline-size:calc(100% / 7)}[data-type=weekday]{color:var(--tui-text-primary)}[data-type=weekend]{color:var(--tui-text-negative)}.t-row{justify-content:flex-start}.t-row:first-child{justify-content:flex-end}.t-row_weekday{font:var(--tui-font-text-s);color:var(--tui-text-secondary);pointer-events:none}.t-cell_unavailable{opacity:var(--tui-disabled-opacity)}.t-dots{position:absolute;bottom:0;display:flex;justify-content:center;margin-top:-.5rem;padding-bottom:.25rem}.t-dot{display:inline-block;inline-size:.25rem;block-size:.25rem;border-radius:100%;margin:0 .0625rem}\\n\"], dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"pipe\", type: i1.AsyncPipe, name: \"async\" }, { kind: \"pipe\", type: TuiCalendarSheetPipe, name: \"tuiCalendarSheet\" }, { kind: \"directive\", type: TuiHovered, selector: \"[tuiHoveredChange]\", outputs: [\"tuiHoveredChange\"] }, { kind: \"directive\", type: TuiLet, selector: \"[tuiLet]\", inputs: [\"tuiLet\"] }, { kind: \"pipe\", type: TuiMapperPipe, name: \"tuiMapper\" }, { kind: \"pipe\", type: TuiOrderWeekDaysPipe, name: \"tuiOrderWeekDays\" }, { kind: \"directive\", type: TuiRepeatTimes, selector: \"[tuiRepeatTimes][tuiRepeatTimesOf]\", inputs: [\"tuiRepeatTimesOf\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\n__decorate([\n    tuiPure\n], TuiCalendarSheet.prototype, \"getRange\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendarSheet, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-calendar-sheet', imports: [\n                        CommonModule,\n                        TuiCalendarSheetPipe,\n                        TuiHovered,\n                        TuiLet,\n                        TuiMapperPipe,\n                        TuiOrderWeekDaysPipe,\n                        TuiRepeatTimes,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[class._picking]': 'isRangePicking',\n                    }, template: \"<div class=\\\"t-row t-row_weekday\\\">\\n    <div\\n        *ngFor=\\\"let day of unorderedWeekDays$ | tuiOrderWeekDays | async\\\"\\n        class=\\\"t-cell\\\"\\n        [textContent]=\\\"day\\\"\\n    ></div>\\n</div>\\n<div *tuiLet=\\\"month | tuiCalendarSheet: true as sheet\\\">\\n    <div\\n        *tuiRepeatTimes=\\\"let rowIndex of sheet.length\\\"\\n        automation-id=\\\"tui-calendar-sheet__row\\\"\\n        class=\\\"t-row\\\"\\n    >\\n        <ng-container *tuiRepeatTimes=\\\"let colIndex of sheet[rowIndex]?.length || 0\\\">\\n            <ng-container *tuiLet=\\\"sheet[rowIndex]?.[colIndex] as item\\\">\\n                <div\\n                    *ngIf=\\\"item && (!itemIsUnavailable(item) || showAdjacent)\\\"\\n                    automation-id=\\\"tui-calendar-sheet__cell\\\"\\n                    class=\\\"t-cell\\\"\\n                    [attr.data-range]=\\\"getItemRange(item)\\\"\\n                    [attr.data-type]=\\\"item | tuiMapper: dayTypeHandler\\\"\\n                    [class.t-cell_disabled]=\\\"disabledItemHandler(item)\\\"\\n                    [class.t-cell_today]=\\\"itemIsToday(item)\\\"\\n                    [class.t-cell_unavailable]=\\\"itemIsUnavailable(item)\\\"\\n                    (click)=\\\"onItemClick(item)\\\"\\n                    (tuiHoveredChange)=\\\"onItemHovered($event && item)\\\"\\n                >\\n                    {{ item.day }}\\n                    <div\\n                        *ngIf=\\\"\\n                            item\\n                                | tuiMapper\\n                                    : toMarkers\\n                                    : itemIsToday(item)\\n                                    : getItemRange(item)\\n                                    : markerHandler as markers\\n                        \\\"\\n                        class=\\\"t-dots\\\"\\n                    >\\n                        <div\\n                            class=\\\"t-dot\\\"\\n                            [style.background]=\\\"markers?.[0]\\\"\\n                        ></div>\\n                        <div\\n                            *ngIf=\\\"markers.length > 1\\\"\\n                            class=\\\"t-dot\\\"\\n                            [style.background]=\\\"markers?.[1] || ''\\\"\\n                        ></div>\\n                    </div>\\n                </div>\\n            </ng-container>\\n        </ng-container>\\n    </div>\\n</div>\\n\", styles: [\".t-row{display:flex;justify-content:flex-start;font:var(--tui-font-text-m)}.t-row:last-child{justify-content:flex-start}.t-cell{position:relative;display:flex;align-items:center;justify-content:center;line-height:2rem;isolation:isolate;cursor:pointer;overflow:hidden;border:.125rem solid transparent;box-sizing:border-box;-webkit-mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem));mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem))}.t-cell:first-child{border-inline-start-color:transparent!important}.t-cell:last-child{border-inline-end-color:transparent!important}.t-cell:before,.t-cell:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";z-index:-1;border-radius:var(--tui-radius-m)}.t-cell:after{-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat}.t-cell[data-range]:before{background:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range]:before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]:not(:first-child):before{border-top-left-radius:0;border-bottom-left-radius:0}.t-cell[data-range=middle]:not(:last-child):before{border-top-right-radius:0;border-bottom-right-radius:0}.t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:not(:last-child):before{right:-1rem}.t-cell[data-range=start]:after{background:var(--tui-background-accent-1)}.t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=end]:not(:first-child):before{left:-1rem}.t-cell[data-range=end]:after{background:var(--tui-background-accent-1);transform:scaleX(-1)}.t-cell[data-range=active]{color:var(--tui-text-primary-on-accent-1)}.t-cell[data-range=active]:after{background:var(--tui-background-accent-1);-webkit-mask:none;mask:none}.t-cell_disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}.t-cell_today{text-decoration:underline;text-underline-offset:.25rem}@media (hover: hover) and (pointer: fine){.t-cell:hover:not([data-range=start]):not([data-range=end]):before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:hover:after,.t-cell[data-range=end]:hover:after,.t-cell[data-range=active]:hover:after{background:var(--tui-background-accent-1-hover)}}.t-cell{inline-size:calc(100% / 7)}[data-type=weekday]{color:var(--tui-text-primary)}[data-type=weekend]{color:var(--tui-text-negative)}.t-row{justify-content:flex-start}.t-row:first-child{justify-content:flex-end}.t-row_weekday{font:var(--tui-font-text-s);color:var(--tui-text-secondary);pointer-events:none}.t-cell_unavailable{opacity:var(--tui-disabled-opacity)}.t-dots{position:absolute;bottom:0;display:flex;justify-content:center;margin-top:-.5rem;padding-bottom:.25rem}.t-dot{display:inline-block;inline-size:.25rem;block-size:.25rem;border-radius:100%;margin:0 .0625rem}\\n\"] }]\n        }], propDecorators: { month: [{\n                type: Input\n            }], disabledItemHandler: [{\n                type: Input\n            }], markerHandler: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], hoveredItem: [{\n                type: Input\n            }], showAdjacent: [{\n                type: Input\n            }], single: [{\n                type: Input\n            }], hoveredItemChange: [{\n                type: Output\n            }], dayClick: [{\n                type: Output\n            }], getRange: [] } });\n\nclass TuiCalendarSpin {\n    constructor() {\n        this.value = TuiMonth.currentLocal();\n        this.min = TUI_FIRST_DAY;\n        this.max = TUI_LAST_DAY;\n        this.valueChange = new EventEmitter();\n        this.yearClick = new EventEmitter();\n    }\n    onYearClick() {\n        this.yearClick.next(this.value);\n    }\n    append(date) {\n        const value = this.value.append(date);\n        if (this.min.monthSameOrAfter(value)) {\n            this.updateValue(this.min);\n        }\n        else {\n            this.updateValue(this.max.monthSameOrBefore(value) ? this.max : value);\n        }\n    }\n    updateValue(value) {\n        if (this.value.monthSame(value)) {\n            return;\n        }\n        this.value = value;\n        this.valueChange.emit(value);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendarSpin, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCalendarSpin, isStandalone: true, selector: \"tui-calendar-spin\", inputs: { value: \"value\", min: \"min\", max: \"max\" }, outputs: { valueChange: \"valueChange\", yearClick: \"yearClick\" }, ngImport: i0, template: \"<tui-spin-button\\n    [focusable]=\\\"false\\\"\\n    [leftDisabled]=\\\"value.monthSameOrBefore(min)\\\"\\n    [rightDisabled]=\\\"value.monthSameOrAfter(max)\\\"\\n    (leftClick)=\\\"append({month: -1})\\\"\\n    (rightClick)=\\\"append({month: 1})\\\"\\n>\\n    {{ value | tuiMonth | async }}\\n    <ng-container *ngIf=\\\"min.year === max.year; else button\\\">\\n        {{ value.formattedYear }}\\n    </ng-container>\\n    <ng-template #button>\\n        <button\\n            id=\\\"year-btn\\\"\\n            automation-id=\\\"tui-primitive-year-month-pagination__year-button\\\"\\n            tabIndex=\\\"-1\\\"\\n            tuiLink\\n            type=\\\"button\\\"\\n            (click)=\\\"onYearClick()\\\"\\n        >\\n            {{ value.formattedYear }}\\n        </button>\\n    </ng-template>\\n</tui-spin-button>\\n\", styles: [\":host{display:block}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: TuiLink, selector: \"a[tuiLink], button[tuiLink]\", inputs: [\"pseudo\"] }, { kind: \"pipe\", type: TuiMonthPipe, name: \"tuiMonth\" }, { kind: \"component\", type: TuiSpinButton, selector: \"tui-spin-button\", inputs: [\"focusable\", \"disabled\", \"leftDisabled\", \"rightDisabled\"], outputs: [\"leftClick\", \"rightClick\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendarSpin, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-calendar-spin', imports: [AsyncPipe, NgIf, TuiLink, TuiMonthPipe, TuiSpinButton], changeDetection: ChangeDetectionStrategy.OnPush, template: \"<tui-spin-button\\n    [focusable]=\\\"false\\\"\\n    [leftDisabled]=\\\"value.monthSameOrBefore(min)\\\"\\n    [rightDisabled]=\\\"value.monthSameOrAfter(max)\\\"\\n    (leftClick)=\\\"append({month: -1})\\\"\\n    (rightClick)=\\\"append({month: 1})\\\"\\n>\\n    {{ value | tuiMonth | async }}\\n    <ng-container *ngIf=\\\"min.year === max.year; else button\\\">\\n        {{ value.formattedYear }}\\n    </ng-container>\\n    <ng-template #button>\\n        <button\\n            id=\\\"year-btn\\\"\\n            automation-id=\\\"tui-primitive-year-month-pagination__year-button\\\"\\n            tabIndex=\\\"-1\\\"\\n            tuiLink\\n            type=\\\"button\\\"\\n            (click)=\\\"onYearClick()\\\"\\n        >\\n            {{ value.formattedYear }}\\n        </button>\\n    </ng-template>\\n</tui-spin-button>\\n\", styles: [\":host{display:block}\\n\"] }]\n        }], propDecorators: { value: [{\n                type: Input\n            }], min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], valueChange: [{\n                type: Output\n            }], yearClick: [{\n                type: Output\n            }] } });\n\nconst LIMIT = 100;\nconst ITEMS_IN_ROW = 4;\nclass TuiCalendarYear {\n    constructor() {\n        this.hoveredItem = null;\n        this.currentYear = TuiMonth.currentLocal().year;\n        this.value = null;\n        this.initialItem = this.currentYear;\n        this.min = MIN_YEAR;\n        this.max = MAX_YEAR;\n        this.rangeMode = false;\n        this.disabledItemHandler = TUI_FALSE_HANDLER;\n        this.yearClick = new EventEmitter();\n    }\n    isDisabled(item) {\n        return ((this.max && this.max < item) ||\n            (this.min && this.min > item) ||\n            this.disabledItemHandler(item));\n    }\n    getItemRange(item) {\n        const { value, hoveredItem } = this;\n        if (value instanceof TuiYear && value.year === item) {\n            return 'active';\n        }\n        if (tuiIsNumber(value)) {\n            return value === item ? 'active' : null;\n        }\n        if (!(value instanceof TuiMonthRange) && !(value instanceof TuiYear)) {\n            return value?.find((day) => day.year === item) ? 'active' : null;\n        }\n        const hovered = this.isRangePicking ? hoveredItem : null;\n        const from = 'from' in value ? value.from?.year : value.year;\n        const to = 'from' in value ? value.to.year : value.year;\n        const min = Math.min(from, hovered ?? to);\n        const max = Math.max(from, hovered ?? to);\n        if (min === max && from === to && from === item) {\n            return 'active';\n        }\n        if (min === item) {\n            return 'start';\n        }\n        if (max === item) {\n            return 'end';\n        }\n        return min < item && item < max ? 'middle' : null;\n    }\n    onItemHovered(hovered, item) {\n        this.updateHoveredItem(hovered, item);\n    }\n    get isRangePicking() {\n        return (this.rangeMode &&\n            (this.value instanceof TuiDay || this.value instanceof TuiMonth));\n    }\n    get rows() {\n        return Math.ceil((this.calculatedMax - this.calculatedMin) / ITEMS_IN_ROW);\n    }\n    scrollItemIntoView(item) {\n        return this.initialItem === item;\n    }\n    getItem(rowIndex, colIndex) {\n        return rowIndex * ITEMS_IN_ROW + colIndex + this.calculatedMin;\n    }\n    itemIsToday(item) {\n        return this.currentYear === item;\n    }\n    get calculatedMin() {\n        const initial = this.initialItem - LIMIT;\n        const min = this.min ?? MIN_YEAR;\n        return min > initial ? min : initial;\n    }\n    get calculatedMax() {\n        const initial = this.initialItem + LIMIT;\n        const max = this.max ?? MAX_YEAR;\n        return max < initial ? max + 1 : initial;\n    }\n    updateHoveredItem(hovered, item) {\n        this.hoveredItem = hovered ? item : null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendarYear, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCalendarYear, isStandalone: true, selector: \"tui-calendar-year\", inputs: { value: \"value\", initialItem: \"initialItem\", min: \"min\", max: \"max\", rangeMode: \"rangeMode\", disabledItemHandler: \"disabledItemHandler\" }, outputs: { yearClick: \"yearClick\" }, host: { properties: { \"class._picking\": \"isRangePicking\" } }, ngImport: i0, template: \"<div\\n    *tuiRepeatTimes=\\\"let rowIndex of rows\\\"\\n    automation-id=\\\"tui-calendar-year__row\\\"\\n    class=\\\"t-row\\\"\\n>\\n    <ng-container *tuiRepeatTimes=\\\"let colIndex of 4\\\">\\n        <div\\n            *tuiLet=\\\"getItem(rowIndex, colIndex) as item\\\"\\n            automation-id=\\\"tui-calendar-year__cell\\\"\\n            class=\\\"t-cell\\\"\\n            [attr.data-range]=\\\"getItemRange(item)\\\"\\n            [class.t-cell_disabled]=\\\"isDisabled(item)\\\"\\n            [class.t-cell_today]=\\\"itemIsToday(item)\\\"\\n            [tuiScrollIntoView]=\\\"scrollItemIntoView(item)\\\"\\n            (click)=\\\"yearClick.emit(item)\\\"\\n            (tuiHoveredChange)=\\\"onItemHovered($event, item)\\\"\\n        >\\n            {{ item }}\\n        </div>\\n    </ng-container>\\n</div>\\n\", styles: [\".t-row{display:flex;justify-content:flex-start;font:var(--tui-font-text-m)}.t-row:first-child{justify-content:flex-end}.t-row:last-child{justify-content:flex-start}.t-cell{position:relative;display:flex;align-items:center;justify-content:center;line-height:2rem;isolation:isolate;cursor:pointer;overflow:hidden;border:.125rem solid transparent;box-sizing:border-box;-webkit-mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem));mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem))}.t-cell:first-child{border-inline-start-color:transparent!important}.t-cell:last-child{border-inline-end-color:transparent!important}.t-cell:before,.t-cell:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";z-index:-1;border-radius:var(--tui-radius-m)}.t-cell:after{-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat}.t-cell[data-range]:before{background:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range]:before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]:not(:first-child):before{border-top-left-radius:0;border-bottom-left-radius:0}.t-cell[data-range=middle]:not(:last-child):before{border-top-right-radius:0;border-bottom-right-radius:0}.t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:not(:last-child):before{right:-1rem}.t-cell[data-range=start]:after{background:var(--tui-background-accent-1)}.t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=end]:not(:first-child):before{left:-1rem}.t-cell[data-range=end]:after{background:var(--tui-background-accent-1);transform:scaleX(-1)}.t-cell[data-range=active]{color:var(--tui-text-primary-on-accent-1)}.t-cell[data-range=active]:after{background:var(--tui-background-accent-1);-webkit-mask:none;mask:none}.t-cell_disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}.t-cell_today{text-decoration:underline;text-underline-offset:.25rem}@media (hover: hover) and (pointer: fine){.t-cell:hover:not([data-range=start]):not([data-range=end]):before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:hover:after,.t-cell[data-range=end]:hover:after,.t-cell[data-range=active]:hover:after{background:var(--tui-background-accent-1-hover)}}:host{display:block;padding-inline-end:1rem}.t-cell{flex:1;border-block-start-width:.5rem;border-block-end-width:.5rem}\\n\"], dependencies: [{ kind: \"directive\", type: TuiHovered, selector: \"[tuiHoveredChange]\", outputs: [\"tuiHoveredChange\"] }, { kind: \"directive\", type: TuiLet, selector: \"[tuiLet]\", inputs: [\"tuiLet\"] }, { kind: \"directive\", type: TuiRepeatTimes, selector: \"[tuiRepeatTimes][tuiRepeatTimesOf]\", inputs: [\"tuiRepeatTimesOf\"] }, { kind: \"directive\", type: TuiScrollIntoView, selector: \"[tuiScrollIntoView]\", inputs: [\"tuiScrollIntoView\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendarYear, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-calendar-year', imports: [TuiHovered, TuiLet, TuiRepeatTimes, TuiScrollIntoView], changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[class._picking]': 'isRangePicking',\n                    }, template: \"<div\\n    *tuiRepeatTimes=\\\"let rowIndex of rows\\\"\\n    automation-id=\\\"tui-calendar-year__row\\\"\\n    class=\\\"t-row\\\"\\n>\\n    <ng-container *tuiRepeatTimes=\\\"let colIndex of 4\\\">\\n        <div\\n            *tuiLet=\\\"getItem(rowIndex, colIndex) as item\\\"\\n            automation-id=\\\"tui-calendar-year__cell\\\"\\n            class=\\\"t-cell\\\"\\n            [attr.data-range]=\\\"getItemRange(item)\\\"\\n            [class.t-cell_disabled]=\\\"isDisabled(item)\\\"\\n            [class.t-cell_today]=\\\"itemIsToday(item)\\\"\\n            [tuiScrollIntoView]=\\\"scrollItemIntoView(item)\\\"\\n            (click)=\\\"yearClick.emit(item)\\\"\\n            (tuiHoveredChange)=\\\"onItemHovered($event, item)\\\"\\n        >\\n            {{ item }}\\n        </div>\\n    </ng-container>\\n</div>\\n\", styles: [\".t-row{display:flex;justify-content:flex-start;font:var(--tui-font-text-m)}.t-row:first-child{justify-content:flex-end}.t-row:last-child{justify-content:flex-start}.t-cell{position:relative;display:flex;align-items:center;justify-content:center;line-height:2rem;isolation:isolate;cursor:pointer;overflow:hidden;border:.125rem solid transparent;box-sizing:border-box;-webkit-mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem));mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem))}.t-cell:first-child{border-inline-start-color:transparent!important}.t-cell:last-child{border-inline-end-color:transparent!important}.t-cell:before,.t-cell:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";z-index:-1;border-radius:var(--tui-radius-m)}.t-cell:after{-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat}.t-cell[data-range]:before{background:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range]:before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]:not(:first-child):before{border-top-left-radius:0;border-bottom-left-radius:0}.t-cell[data-range=middle]:not(:last-child):before{border-top-right-radius:0;border-bottom-right-radius:0}.t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:not(:last-child):before{right:-1rem}.t-cell[data-range=start]:after{background:var(--tui-background-accent-1)}.t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=end]:not(:first-child):before{left:-1rem}.t-cell[data-range=end]:after{background:var(--tui-background-accent-1);transform:scaleX(-1)}.t-cell[data-range=active]{color:var(--tui-text-primary-on-accent-1)}.t-cell[data-range=active]:after{background:var(--tui-background-accent-1);-webkit-mask:none;mask:none}.t-cell_disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}.t-cell_today{text-decoration:underline;text-underline-offset:.25rem}@media (hover: hover) and (pointer: fine){.t-cell:hover:not([data-range=start]):not([data-range=end]):before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:hover:after,.t-cell[data-range=end]:hover:after,.t-cell[data-range=active]:hover:after{background:var(--tui-background-accent-1-hover)}}:host{display:block;padding-inline-end:1rem}.t-cell{flex:1;border-block-start-width:.5rem;border-block-end-width:.5rem}\\n\"] }]\n        }], propDecorators: { value: [{\n                type: Input\n            }], initialItem: [{\n                type: Input\n            }], min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], rangeMode: [{\n                type: Input\n            }], disabledItemHandler: [{\n                type: Input\n            }], yearClick: [{\n                type: Output\n            }] } });\n\nclass TuiCalendar {\n    constructor() {\n        this.cdr = inject(ChangeDetectorRef);\n        this.day = null;\n        this.view = 'month';\n        this.options = inject(TUI_CALENDAR_SHEET_OPTIONS);\n        this.month = TuiMonth.currentLocal();\n        this.disabledItemHandler = TUI_FALSE_HANDLER;\n        this.min = TUI_FIRST_DAY;\n        this.max = TUI_LAST_DAY;\n        this.minViewedMonth = TUI_FIRST_DAY;\n        this.maxViewedMonth = TUI_LAST_DAY;\n        this.hoveredItem = null;\n        this.showAdjacent = true;\n        this.markerHandler = null;\n        this.dayClick = new EventEmitter();\n        this.monthChange = new EventEmitter();\n        this.hoveredItemChange = new EventEmitter();\n        /** @deprecated for private use only until Calendars are refactored */\n        this.valueChange = new Subject();\n        this.disabledItemHandlerMapper = (disabledItemHandler, min, max) => (item) => item.dayBefore(min) || item.dayAfter(max) || disabledItemHandler(item);\n    }\n    set value(value) {\n        this.cdr.markForCheck();\n        this.day = value;\n        if (this.showAdjacent &&\n            value instanceof TuiDay &&\n            value.daySameOrBefore(TUI_LAST_DISPLAYED_DAY)) {\n            this.month = value;\n        }\n    }\n    set initialView(view) {\n        this.view = view;\n    }\n    get value() {\n        return this.day;\n    }\n    onPaginationValueChange(month) {\n        this.updateViewedMonth(month);\n    }\n    onDayClick(day) {\n        this.dayClick.emit(day);\n        this.valueChange.next(day);\n    }\n    onHoveredItemChange(day) {\n        this.updateHoveredDay(day);\n    }\n    get computedMin() {\n        return this.min ?? TUI_FIRST_DAY;\n    }\n    get computedMax() {\n        return this.max ?? TUI_LAST_DAY;\n    }\n    get computedMinViewedMonth() {\n        const min = this.computedMin;\n        const minViewed = this.minViewedMonth ?? TUI_FIRST_DAY;\n        return minViewed.monthSameOrAfter(min) ? minViewed : min;\n    }\n    get computedMaxViewedMonth() {\n        const max = this.computedMax;\n        const maxViewed = this.maxViewedMonth ?? TUI_LAST_DAY;\n        return maxViewed.monthSameOrBefore(max) ? maxViewed : max;\n    }\n    get isInYearView() {\n        return this.view === 'year';\n    }\n    onPaginationYearClick() {\n        this.view = 'year';\n    }\n    onPickerYearClick(year) {\n        this.view = 'month';\n        this.updateViewedMonth(new TuiMonth(year, this.month.month));\n    }\n    updateViewedMonth(month) {\n        if (this.month.monthSame(month)) {\n            return;\n        }\n        this.month = month;\n        this.monthChange.emit(month);\n    }\n    updateHoveredDay(day) {\n        if (tuiNullableSame(this.hoveredItem, day, (a, b) => a.daySame(b))) {\n            return;\n        }\n        this.hoveredItem = day;\n        this.hoveredItemChange.emit(day);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendar, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCalendar, isStandalone: true, selector: \"tui-calendar\", inputs: { month: \"month\", disabledItemHandler: \"disabledItemHandler\", min: \"min\", max: \"max\", minViewedMonth: \"minViewedMonth\", maxViewedMonth: \"maxViewedMonth\", hoveredItem: \"hoveredItem\", showAdjacent: \"showAdjacent\", markerHandler: \"markerHandler\", value: \"value\", initialView: \"initialView\" }, outputs: { dayClick: \"dayClick\", monthChange: \"monthChange\", hoveredItemChange: \"hoveredItemChange\" }, host: { listeners: { \"pointerdown.prevent.zoneless\": \"0\" } }, providers: [tuiAsAuxiliary(TuiCalendar)], ngImport: i0, template: \"<tui-scrollbar\\n    *ngIf=\\\"isInYearView; else calendar\\\"\\n    automation-id=\\\"tui-calendar__scrollbar\\\"\\n    class=\\\"t-scrollbar\\\"\\n>\\n    <tui-calendar-year\\n        automation-id=\\\"tui-calendar__year\\\"\\n        [initialItem]=\\\"month.year\\\"\\n        [max]=\\\"computedMax.year\\\"\\n        [min]=\\\"computedMin.year\\\"\\n        [rangeMode]=\\\"options.rangeMode\\\"\\n        [value]=\\\"value\\\"\\n        (yearClick)=\\\"onPickerYearClick($event)\\\"\\n    />\\n</tui-scrollbar>\\n<ng-template #calendar>\\n    <tui-calendar-spin\\n        automation-id=\\\"tui-calendar__pagination\\\"\\n        class=\\\"t-pagination\\\"\\n        [max]=\\\"computedMaxViewedMonth\\\"\\n        [min]=\\\"computedMinViewedMonth\\\"\\n        [value]=\\\"month\\\"\\n        (valueChange)=\\\"onPaginationValueChange($event)\\\"\\n        (yearClick)=\\\"onPaginationYearClick()\\\"\\n    />\\n    <tui-calendar-sheet\\n        automation-id=\\\"tui-calendar__calendar\\\"\\n        [disabledItemHandler]=\\\"disabledItemHandler | tuiMapper: disabledItemHandlerMapper : computedMin : computedMax\\\"\\n        [hoveredItem]=\\\"hoveredItem\\\"\\n        [markerHandler]=\\\"markerHandler\\\"\\n        [month]=\\\"month\\\"\\n        [showAdjacent]=\\\"showAdjacent\\\"\\n        [value]=\\\"value\\\"\\n        (dayClick)=\\\"onDayClick($event)\\\"\\n        (hoveredItemChange)=\\\"onHoveredItemChange($event)\\\"\\n    />\\n</ng-template>\\n\", styles: [\":host{display:block;min-block-size:20.25rem;inline-size:18rem;padding:1rem 1.125rem;box-sizing:border-box;flex-shrink:0}:host-context(tui-dropdown-mobile){inline-size:100%}.t-scrollbar{block-size:18.25rem;inline-size:calc(100% + 1rem)}.t-pagination{margin-bottom:1rem}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: TuiCalendarSheet, selector: \"tui-calendar-sheet\", inputs: [\"month\", \"disabledItemHandler\", \"markerHandler\", \"value\", \"hoveredItem\", \"showAdjacent\", \"single\"], outputs: [\"hoveredItemChange\", \"dayClick\"] }, { kind: \"component\", type: TuiCalendarSpin, selector: \"tui-calendar-spin\", inputs: [\"value\", \"min\", \"max\"], outputs: [\"valueChange\", \"yearClick\"] }, { kind: \"component\", type: TuiCalendarYear, selector: \"tui-calendar-year\", inputs: [\"value\", \"initialItem\", \"min\", \"max\", \"rangeMode\", \"disabledItemHandler\"], outputs: [\"yearClick\"] }, { kind: \"pipe\", type: TuiMapperPipe, name: \"tuiMapper\" }, { kind: \"component\", type: TuiScrollbar, selector: \"tui-scrollbar\", inputs: [\"hidden\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendar, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-calendar', imports: [\n                        NgIf,\n                        TuiCalendarSheet,\n                        TuiCalendarSpin,\n                        TuiCalendarYear,\n                        TuiMapperPipe,\n                        TuiScrollbar,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiAsAuxiliary(TuiCalendar)], host: {\n                        '(pointerdown.prevent.zoneless)': '0',\n                    }, template: \"<tui-scrollbar\\n    *ngIf=\\\"isInYearView; else calendar\\\"\\n    automation-id=\\\"tui-calendar__scrollbar\\\"\\n    class=\\\"t-scrollbar\\\"\\n>\\n    <tui-calendar-year\\n        automation-id=\\\"tui-calendar__year\\\"\\n        [initialItem]=\\\"month.year\\\"\\n        [max]=\\\"computedMax.year\\\"\\n        [min]=\\\"computedMin.year\\\"\\n        [rangeMode]=\\\"options.rangeMode\\\"\\n        [value]=\\\"value\\\"\\n        (yearClick)=\\\"onPickerYearClick($event)\\\"\\n    />\\n</tui-scrollbar>\\n<ng-template #calendar>\\n    <tui-calendar-spin\\n        automation-id=\\\"tui-calendar__pagination\\\"\\n        class=\\\"t-pagination\\\"\\n        [max]=\\\"computedMaxViewedMonth\\\"\\n        [min]=\\\"computedMinViewedMonth\\\"\\n        [value]=\\\"month\\\"\\n        (valueChange)=\\\"onPaginationValueChange($event)\\\"\\n        (yearClick)=\\\"onPaginationYearClick()\\\"\\n    />\\n    <tui-calendar-sheet\\n        automation-id=\\\"tui-calendar__calendar\\\"\\n        [disabledItemHandler]=\\\"disabledItemHandler | tuiMapper: disabledItemHandlerMapper : computedMin : computedMax\\\"\\n        [hoveredItem]=\\\"hoveredItem\\\"\\n        [markerHandler]=\\\"markerHandler\\\"\\n        [month]=\\\"month\\\"\\n        [showAdjacent]=\\\"showAdjacent\\\"\\n        [value]=\\\"value\\\"\\n        (dayClick)=\\\"onDayClick($event)\\\"\\n        (hoveredItemChange)=\\\"onHoveredItemChange($event)\\\"\\n    />\\n</ng-template>\\n\", styles: [\":host{display:block;min-block-size:20.25rem;inline-size:18rem;padding:1rem 1.125rem;box-sizing:border-box;flex-shrink:0}:host-context(tui-dropdown-mobile){inline-size:100%}.t-scrollbar{block-size:18.25rem;inline-size:calc(100% + 1rem)}.t-pagination{margin-bottom:1rem}\\n\"] }]\n        }], propDecorators: { month: [{\n                type: Input\n            }], disabledItemHandler: [{\n                type: Input\n            }], min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], minViewedMonth: [{\n                type: Input\n            }], maxViewedMonth: [{\n                type: Input\n            }], hoveredItem: [{\n                type: Input\n            }], showAdjacent: [{\n                type: Input\n            }], markerHandler: [{\n                type: Input\n            }], dayClick: [{\n                type: Output\n            }], monthChange: [{\n                type: Output\n            }], hoveredItemChange: [{\n                type: Output\n            }], value: [{\n                type: Input\n            }], initialView: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_CALENDAR_SHEET_DEFAULT_OPTIONS, TUI_CALENDAR_SHEET_OPTIONS, TuiCalendar, TuiCalendarSheet, TuiCalendarSpin, TuiCalendarYear, tuiCalendarSheetOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,EAAEC,SAAS,EAAEC,IAAI,QAAQ,iBAAiB;AAC/D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,iBAAiB,QAAQ,eAAe;AAC1H,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,aAAa,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,aAAa,EAAEC,sBAAsB,QAAQ,yBAAyB;AACxK,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,OAAO,EAAEC,WAAW,QAAQ,mCAAmC;AAC5H,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,qCAAqC;AACrF,SAASC,mBAAmB,EAAEC,oBAAoB,EAAEC,cAAc,QAAQ,uBAAuB;AACjG,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,MAAM,QAAQ,8BAA8B;AACrD,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,oBAAoB,EAAEC,oBAAoB,EAAEC,YAAY,QAAQ,sBAAsB;AAC/F,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,aAAa,QAAQ,uCAAuC;AAAC,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,MAAAJ,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;AAAA,SAAAC,gCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA0H+B/C,EAAE,CAAAiD,SAAA,YACumB,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAAAF,GAAA,CAAAG,SAAA;IAD1mBnD,EAAE,CAAAoD,UAAA,gBAAAF,MAC0lB,CAAC;EAAA;AAAA;AAAA,SAAAG,sFAAAN,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD7lB/C,EAAE,CAAAiD,SAAA,aAC8iF,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAO,UAAA,GADjjFtD,EAAE,CAAAuD,aAAA,GAAAC,IAAA;IAAFxD,EAAE,CAAAyD,WAAA,gBAAAH,UAAA,kBAAAA,UAAA,UAC6gF,CAAC;EAAA;AAAA;AAAA,SAAAI,gFAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADhhF/C,EAAE,CAAA2D,cAAA,aACopE,CAAC;IADvpE3D,EAAE,CAAAiD,SAAA,aACi0E,CAAC;IADp0EjD,EAAE,CAAA4D,UAAA,IAAAP,qFAAA,iBACwiF,CAAC;IAD3iFrD,EAAE,CAAA6D,YAAA,CAC0kF,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAO,UAAA,GAAAN,GAAA,CAAAQ,IAAA;IAD7kFxD,EAAE,CAAA8D,SAAA,CACgyE,CAAC;IADnyE9D,EAAE,CAAAyD,WAAA,eAAAH,UAAA,kBAAAA,UAAA,GACgyE,CAAC;IADnyEtD,EAAE,CAAA8D,SAAA,CACu5E,CAAC;IAD15E9D,EAAE,CAAAoD,UAAA,SAAAE,UAAA,CAAAS,MAAA,IACu5E,CAAC;EAAA;AAAA;AAAA,SAAAC,0EAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkB,GAAA,GAD15EjE,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA2D,cAAA,YACoqD,CAAC;IADvqD3D,EAAE,CAAAmE,MAAA;IAAFnE,EAAE,CAAAoE,UAAA,mBAAAC,+FAAA;MAAFrE,EAAE,CAAAsE,aAAA,CAAAL,GAAA;MAAA,MAAAM,OAAA,GAAFvE,EAAE,CAAAuD,aAAA,GAAAiB,MAAA;MAAA,MAAAC,MAAA,GAAFzE,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAA0E,WAAA,CACqjDD,MAAA,CAAAE,WAAA,CAAAJ,OAAgB,CAAC;IAAA,CAAC,CAAC,8BAAAK,0GAAAC,MAAA;MAD1kD7E,EAAE,CAAAsE,aAAA,CAAAL,GAAA;MAAA,MAAAM,OAAA,GAAFvE,EAAE,CAAAuD,aAAA,GAAAiB,MAAA;MAAA,MAAAC,MAAA,GAAFzE,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAA0E,WAAA,CACmnDD,MAAA,CAAAK,aAAA,CAAAD,MAAA,IAAAN,OAA4B,CAAC;IAAA,CAAC,CAAC;IADppDvE,EAAE,CAAA+E,MAAA,EAC8tD,CAAC;IADjuD/E,EAAE,CAAA4D,UAAA,IAAAF,+EAAA,gBACopE,CAAC;IADvpE1D,EAAE,CAAAmE,MAAA;IAAFnE,EAAE,CAAA6D,YAAA,CACkmF,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAwB,OAAA,GADrmFvE,EAAE,CAAAuD,aAAA,GAAAiB,MAAA;IAAA,MAAAC,MAAA,GAAFzE,EAAE,CAAAuD,aAAA;IAAFvD,EAAE,CAAAgF,WAAA,oBAAAP,MAAA,CAAAQ,mBAAA,CAAAV,OAAA,CACw4C,CAAC,iBAAAE,MAAA,CAAAS,WAAA,CAAAX,OAAA,CAA+D,CAAC,uBAAAE,MAAA,CAAAU,iBAAA,CAAAZ,OAAA,CAA2E,CAAC;IADvhDvE,EAAE,CAAAoF,WAAA,eAAAX,MAAA,CAAAY,YAAA,CAAAd,OAAA,gBAAFvE,EAAE,CAAAsF,WAAA,QAAAf,OAAA,EAAAE,MAAA,CAAAc,cAAA;IAAFvF,EAAE,CAAA8D,SAAA,EAC8tD,CAAC;IADjuD9D,EAAE,CAAAwF,kBAAA,MAAAjB,OAAA,CAAAkB,GAAA,KAC8tD,CAAC;IADjuDzF,EAAE,CAAA8D,SAAA,CACqkE,CAAC;IADxkE9D,EAAE,CAAAoD,UAAA,SAAFpD,EAAE,CAAA0F,WAAA,QAAF1F,EAAE,CAAA2F,eAAA,KAAAnD,GAAA,EAAA+B,OAAA,EAAAE,MAAA,CAAAmB,SAAA,EAAAnB,MAAA,CAAAS,WAAA,CAAAX,OAAA,GAAAE,MAAA,CAAAY,YAAA,CAAAd,OAAA,GAAAE,MAAA,CAAAoB,aAAA,EACqkE,CAAC;EAAA;AAAA;AAAA,SAAAC,oEAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADxkE/C,EAAE,CAAA+F,uBAAA,EACs+B,CAAC;IADz+B/F,EAAE,CAAA4D,UAAA,IAAAI,yEAAA,iBACoqD,CAAC;IADvqDhE,EAAE,CAAAgG,qBAAA;EAAA;EAAA,IAAAjD,EAAA;IAAA,MAAAwB,OAAA,GAAAvB,GAAA,CAAAwB,MAAA;IAAA,MAAAC,MAAA,GAAFzE,EAAE,CAAAuD,aAAA;IAAFvD,EAAE,CAAA8D,SAAA,CAC4kC,CAAC;IAD/kC9D,EAAE,CAAAoD,UAAA,SAAAmB,OAAA,MAAAE,MAAA,CAAAU,iBAAA,CAAAZ,OAAA,KAAAE,MAAA,CAAAwB,YAAA,CAC4kC,CAAC;EAAA;AAAA;AAAA,SAAAC,qDAAAnD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD/kC/C,EAAE,CAAA+F,uBAAA,EAC05B,CAAC;IAD75B/F,EAAE,CAAA4D,UAAA,IAAAkC,mEAAA,yBACs+B,CAAC;IADz+B9F,EAAE,CAAAgG,qBAAA;EAAA;EAAA,IAAAjD,EAAA;IAAA,MAAAoD,WAAA,GAAAnD,GAAA,CAAAG,SAAA;IAAA,MAAAiD,WAAA,GAAFpG,EAAE,CAAAuD,aAAA,GAAAJ,SAAA;IAAA,MAAAkD,QAAA,GAAFrG,EAAE,CAAAuD,aAAA,GAAAiB,MAAA;IAAFxE,EAAE,CAAA8D,SAAA,CAC49B,CAAC;IAD/9B9D,EAAE,CAAAoD,UAAA,WAAAiD,QAAA,CAAAD,WAAA,mBAAAC,QAAA,CAAAD,WAAA,EAAAD,WAAA,CAC49B,CAAC;EAAA;AAAA;AAAA,SAAAG,sCAAAvD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD/9B/C,EAAE,CAAA2D,cAAA,YACi0B,CAAC;IADp0B3D,EAAE,CAAA4D,UAAA,IAAAsC,oDAAA,yBAC05B,CAAC;IAD75BlG,EAAE,CAAA6D,YAAA,CACoqF,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAqD,WAAA,GAAApD,GAAA,CAAAG,SAAA;IAAA,MAAAkD,QAAA,GADvqFrG,EAAE,CAAAuD,aAAA,GAAAiB,MAAA;IAAFxE,EAAE,CAAA8D,SAAA,CACu5B,CAAC;IAD15B9D,EAAE,CAAAoD,UAAA,sBAAAiD,QAAA,CAAAD,WAAA,mBAAAC,QAAA,CAAAD,WAAA,EAAArC,MAAA,MACu5B,CAAC;EAAA;AAAA;AAAA,SAAAwC,gCAAAxD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD15B/C,EAAE,CAAA2D,cAAA,SAC0qB,CAAC;IAD7qB3D,EAAE,CAAA4D,UAAA,IAAA0C,qCAAA,gBACi0B,CAAC;IADp0BtG,EAAE,CAAA6D,YAAA,CAC4qF,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAsD,QAAA,GAAArD,GAAA,CAAAwB,MAAA;IAD/qFxE,EAAE,CAAA8D,SAAA,CAC4uB,CAAC;IAD/uB9D,EAAE,CAAAoD,UAAA,qBAAAiD,QAAA,CAAAtC,MAC4uB,CAAC;EAAA;AAAA;AAAA,SAAAyC,wCAAAzD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD/uB/C,EAAE,CAAA+F,uBAAA,EAmE4hB,CAAC;IAnE/hB/F,EAAE,CAAA+E,MAAA,EAmEqkB,CAAC;IAnExkB/E,EAAE,CAAAgG,qBAAA;EAAA;EAAA,IAAAjD,EAAA;IAAA,MAAA0D,MAAA,GAAFzG,EAAE,CAAAuD,aAAA;IAAFvD,EAAE,CAAA8D,SAAA,CAmEqkB,CAAC;IAnExkB9D,EAAE,CAAAwF,kBAAA,MAAAiB,MAAA,CAAAC,KAAA,CAAAC,aAAA,KAmEqkB,CAAC;EAAA;AAAA;AAAA,SAAAC,uCAAA7D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA8D,GAAA,GAnExkB7G,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA2D,cAAA,eAmE82B,CAAC;IAnEj3B3D,EAAE,CAAAoE,UAAA,mBAAA0C,+DAAA;MAAF9G,EAAE,CAAAsE,aAAA,CAAAuC,GAAA;MAAA,MAAAJ,MAAA,GAAFzG,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAA0E,WAAA,CAmEq1B+B,MAAA,CAAAM,WAAA,CAAY,CAAC;IAAA,CAAC,CAAC;IAnEt2B/G,EAAE,CAAA+E,MAAA,EAmE+5B,CAAC;IAnEl6B/E,EAAE,CAAA6D,YAAA,CAmEw6B,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAA0D,MAAA,GAnE36BzG,EAAE,CAAAuD,aAAA;IAAFvD,EAAE,CAAA8D,SAAA,CAmE+5B,CAAC;IAnEl6B9D,EAAE,CAAAwF,kBAAA,MAAAiB,MAAA,CAAAC,KAAA,CAAAC,aAAA,KAmE+5B,CAAC;EAAA;AAAA;AAAA,SAAAK,oDAAAjE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkE,GAAA,GAnEl6BjH,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA2D,cAAA,YAmKggC,CAAC;IAnKngC3D,EAAE,CAAAoE,UAAA,mBAAA8C,yEAAA;MAAA,MAAAC,OAAA,GAAFnH,EAAE,CAAAsE,aAAA,CAAA2C,GAAA,EAAAzC,MAAA;MAAA,MAAA4C,MAAA,GAAFpH,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAA0E,WAAA,CAmKg6B0C,MAAA,CAAAC,SAAA,CAAAC,IAAA,CAAAH,OAAmB,CAAC;IAAA,CAAC,CAAC,8BAAAI,oFAAA1C,MAAA;MAAA,MAAAsC,OAAA,GAnKx7BnH,EAAE,CAAAsE,aAAA,CAAA2C,GAAA,EAAAzC,MAAA;MAAA,MAAA4C,MAAA,GAAFpH,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAA0E,WAAA,CAmKy9B0C,MAAA,CAAAtC,aAAA,CAAAD,MAAA,EAAAsC,OAA0B,CAAC;IAAA,CAAC,CAAC;IAnKx/BnH,EAAE,CAAA+E,MAAA,EAmKkiC,CAAC;IAnKriC/E,EAAE,CAAA6D,YAAA,CAmKwiC,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAoE,OAAA,GAAAnE,GAAA,CAAAwB,MAAA;IAAA,MAAA4C,MAAA,GAnK3iCpH,EAAE,CAAAuD,aAAA;IAAFvD,EAAE,CAAAgF,WAAA,oBAAAoC,MAAA,CAAAI,UAAA,CAAAL,OAAA,CAmKixB,CAAC,iBAAAC,MAAA,CAAAlC,WAAA,CAAAiC,OAAA,CAAuD,CAAC;IAnK50BnH,EAAE,CAAAoD,UAAA,sBAAAgE,MAAA,CAAAK,kBAAA,CAAAN,OAAA,CAmKu4B,CAAC;IAnK14BnH,EAAE,CAAAoF,WAAA,eAAAgC,MAAA,CAAA/B,YAAA,CAAA8B,OAAA;IAAFnH,EAAE,CAAA8D,SAAA,CAmKkiC,CAAC;IAnKriC9D,EAAE,CAAAwF,kBAAA,MAAA2B,OAAA,KAmKkiC,CAAC;EAAA;AAAA;AAAA,SAAAO,8CAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnKriC/C,EAAE,CAAA+F,uBAAA,EAmKigB,CAAC;IAnKpgB/F,EAAE,CAAA4D,UAAA,IAAAoD,mDAAA,gBAmKggC,CAAC;IAnKngChH,EAAE,CAAAgG,qBAAA;EAAA;EAAA,IAAAjD,EAAA;IAAA,MAAA4E,WAAA,GAAA3E,GAAA,CAAAG,SAAA;IAAA,MAAAyE,WAAA,GAAF5H,EAAE,CAAAuD,aAAA,GAAAJ,SAAA;IAAA,MAAAiE,MAAA,GAAFpH,EAAE,CAAAuD,aAAA;IAAFvD,EAAE,CAAA8D,SAAA,CAmKmkB,CAAC;IAnKtkB9D,EAAE,CAAAoD,UAAA,WAAAgE,MAAA,CAAAS,OAAA,CAAAD,WAAA,EAAAD,WAAA,CAmKmkB,CAAC;EAAA;AAAA;AAAA,SAAAG,+BAAA/E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnKtkB/C,EAAE,CAAA2D,cAAA,YAmKuc,CAAC;IAnK1c3D,EAAE,CAAA4D,UAAA,IAAA8D,6CAAA,yBAmKigB,CAAC;IAnKpgB1H,EAAE,CAAA6D,YAAA,CAmKqkC,CAAC;EAAA;EAAA,IAAAd,EAAA;IAnKxkC/C,EAAE,CAAA8D,SAAA,CAmK8f,CAAC;IAnKjgB9D,EAAE,CAAAoD,UAAA,sBAmK8f,CAAC;EAAA;AAAA;AAAA,SAAA2E,qCAAAhF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkE,GAAA,GAnKjgBjH,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA2D,cAAA,sBAkR8sB,CAAC,0BAAoT,CAAC;IAlRtgC3D,EAAE,CAAAoE,UAAA,uBAAA4D,4EAAAnD,MAAA;MAAF7E,EAAE,CAAAsE,aAAA,CAAA2C,GAAA;MAAA,MAAAR,MAAA,GAAFzG,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAA0E,WAAA,CAkRi+B+B,MAAA,CAAAwB,iBAAA,CAAApD,MAAwB,CAAC;IAAA,CAAC,CAAC;IAlR9/B7E,EAAE,CAAA6D,YAAA,CAkRmgC,CAAC,CAAiB,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAA0D,MAAA,GAlRxhCzG,EAAE,CAAAuD,aAAA;IAAFvD,EAAE,CAAA8D,SAAA,CAkR0zB,CAAC;IAlR7zB9D,EAAE,CAAAoD,UAAA,gBAAAqD,MAAA,CAAAyB,KAAA,CAAAC,IAkR0zB,CAAC,QAAA1B,MAAA,CAAA2B,WAAA,CAAAD,IAAmC,CAAC,QAAA1B,MAAA,CAAA4B,WAAA,CAAAF,IAAmC,CAAC,cAAA1B,MAAA,CAAA6B,OAAA,CAAAC,SAA0C,CAAC,UAAA9B,MAAA,CAAAC,KAA0B,CAAC;EAAA;AAAA;AAAA,SAAA8B,mCAAAzF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA8D,GAAA,GAlR38B7G,EAAE,CAAAkE,gBAAA;IAAFlE,EAAE,CAAA2D,cAAA,0BAkR63C,CAAC;IAlRh4C3D,EAAE,CAAAoE,UAAA,yBAAAqE,4EAAA5D,MAAA;MAAF7E,EAAE,CAAAsE,aAAA,CAAAuC,GAAA;MAAA,MAAAJ,MAAA,GAAFzG,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAA0E,WAAA,CAkRoyC+B,MAAA,CAAAiC,uBAAA,CAAA7D,MAA8B,CAAC;IAAA,CAAC,CAAC,uBAAA8D,0EAAA;MAlRv0C3I,EAAE,CAAAsE,aAAA,CAAAuC,GAAA;MAAA,MAAAJ,MAAA,GAAFzG,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAA0E,WAAA,CAkR61C+B,MAAA,CAAAmC,qBAAA,CAAsB,CAAC;IAAA,CAAC,CAAC;IAlRx3C5I,EAAE,CAAA6D,YAAA,CAkR63C,CAAC;IAlRh4C7D,EAAE,CAAA2D,cAAA,2BAkRm2D,CAAC;IAlRt2D3D,EAAE,CAAAmE,MAAA;IAAFnE,EAAE,CAAAoE,UAAA,sBAAAyE,0EAAAhE,MAAA;MAAF7E,EAAE,CAAAsE,aAAA,CAAAuC,GAAA;MAAA,MAAAJ,MAAA,GAAFzG,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAA0E,WAAA,CAkR2wD+B,MAAA,CAAAqC,UAAA,CAAAjE,MAAiB,CAAC;IAAA,CAAC,CAAC,+BAAAkE,mFAAAlE,MAAA;MAlRjyD7E,EAAE,CAAAsE,aAAA,CAAAuC,GAAA;MAAA,MAAAJ,MAAA,GAAFzG,EAAE,CAAAuD,aAAA;MAAA,OAAFvD,EAAE,CAAA0E,WAAA,CAkR+zD+B,MAAA,CAAAuC,mBAAA,CAAAnE,MAA0B,CAAC;IAAA,CAAC,CAAC;IAlR91D7E,EAAE,CAAA6D,YAAA,CAkRm2D,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAA0D,MAAA,GAlRt2DzG,EAAE,CAAAuD,aAAA;IAAFvD,EAAE,CAAAoD,UAAA,QAAAqD,MAAA,CAAAwC,sBAkRosC,CAAC,QAAAxC,MAAA,CAAAyC,sBAAyC,CAAC,UAAAzC,MAAA,CAAAyB,KAA0B,CAAC;IAlR5wClI,EAAE,CAAA8D,SAAA,CAkRkkD,CAAC;IAlRrkD9D,EAAE,CAAAoD,UAAA,wBAAFpD,EAAE,CAAAmJ,WAAA,OAAA1C,MAAA,CAAAxB,mBAAA,EAAAwB,MAAA,CAAA2C,yBAAA,EAAA3C,MAAA,CAAA4B,WAAA,EAAA5B,MAAA,CAAA2B,WAAA,CAkRkkD,CAAC,gBAAA3B,MAAA,CAAA4C,WAAsC,CAAC,kBAAA5C,MAAA,CAAAZ,aAA0C,CAAC,UAAAY,MAAA,CAAAyB,KAA0B,CAAC,iBAAAzB,MAAA,CAAAR,YAAwC,CAAC,UAAAQ,MAAA,CAAAC,KAA0B,CAAC;EAAA;AAAA;AA1Y31D,MAAM4C,kCAAkC,GAAG;EACvCf,SAAS,EAAE;AACf,CAAC;AACD,MAAMgB,0BAA0B,GAAGnI,cAAc,CAACkI,kCAAkC,CAAC;AACrF,SAASE,+BAA+BA,CAAClB,OAAO,EAAE;EAC9C,OAAOjH,iBAAiB,CAACkI,0BAA0B,EAAEjB,OAAO,EAAEgB,kCAAkC,CAAC;AACrG;AAEA,MAAMG,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpB,OAAO,GAAGrI,MAAM,CAACsJ,0BAA0B,CAAC;IACjD,IAAI,CAACI,KAAK,GAAGlJ,MAAM,CAACmJ,YAAY,CAAC,CAAC;IAClC,IAAI,CAACC,kBAAkB,GAAG5J,MAAM,CAAC0B,mBAAmB,CAAC;IACrD,IAAI,CAAC4D,cAAc,GAAGtF,MAAM,CAAC2B,oBAAoB,CAAC;IAClD,IAAI,CAACsG,KAAK,GAAGxH,QAAQ,CAACkJ,YAAY,CAAC,CAAC;IACpC,IAAI,CAAC3E,mBAAmB,GAAGzE,iBAAiB;IAC5C,IAAI,CAACqF,aAAa,GAAG,IAAI;IACzB,IAAI,CAACa,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC2C,WAAW,GAAG,IAAI;IACvB,IAAI,CAACpD,YAAY,GAAG,IAAI;IACxB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC6D,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,iBAAiB,GAAG,IAAI7J,YAAY,CAAC,CAAC;IAC3C,IAAI,CAAC8J,QAAQ,GAAG,IAAI9J,YAAY,CAAC,CAAC;IAClC,IAAI,CAAC0F,SAAS,GAAG,CAACH,GAAG,EAAEkE,KAAK,EAAEM,KAAK,EAAEpE,aAAa,KAAK;MACnD,IAAI8D,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,CAACO,QAAQ,CAACD,KAAK,IAAI,EAAE,CAAC,EAAE;QAC3D,OAAO,IAAI;MACf;MACA,MAAME,OAAO,GAAGtE,aAAa,GAAGJ,GAAG,CAAC;MACpC,OAAO0E,OAAO,EAAEpG,MAAM,GAAGoG,OAAO,GAAG,IAAI;IAC3C,CAAC;EACL;EACA;AACJ;AACA;EACIC,cAAcA,CAAC3E,GAAG,EAAE;IAChB,MAAM;MAAEiB,KAAK;MAAE2C;IAAY,CAAC,GAAG,IAAI;IACnC,IAAI,EAAE3C,KAAK,YAAY/F,WAAW,CAAC,EAAE;MACjC,OAAO,KAAK;IAChB;IACA,IAAI,CAAC+F,KAAK,CAAC2D,WAAW,EAAE;MACpB,OAAO3D,KAAK,CAAC4D,IAAI,CAACC,eAAe,CAAC9E,GAAG,CAAC,IAAIiB,KAAK,CAAC8D,EAAE,CAACC,QAAQ,CAAChF,GAAG,CAAC;IACpE;IACA,IAAI4D,WAAW,KAAK,IAAI,EAAE;MACtB,OAAO,KAAK;IAChB;IACA,MAAMY,KAAK,GAAGtJ,WAAW,CAAC+J,IAAI,CAAChE,KAAK,CAAC4D,IAAI,EAAEjB,WAAW,CAAC;IACvD,OAAOY,KAAK,CAACK,IAAI,CAACC,eAAe,CAAC9E,GAAG,CAAC,IAAIwE,KAAK,CAACO,EAAE,CAACC,QAAQ,CAAChF,GAAG,CAAC;EACpE;EACAX,aAAaA,CAAC6F,IAAI,EAAE;IAChB,IAAI,CAACC,iBAAiB,CAACD,IAAI,IAAI,IAAI,CAAC;EACxC;EACAtF,YAAYA,CAACsF,IAAI,EAAE;IACf,MAAM;MAAEjE,KAAK;MAAE2C;IAAY,CAAC,GAAG,IAAI;IACnC,IAAI,CAAC3C,KAAK,EAAE;MACR,OAAO,IAAI;IACf;IACA,IAAIA,KAAK,YAAYjG,MAAM,IAAI,CAAC,IAAI,CAACoK,iBAAiB,EAAE;MACpD,OAAOnE,KAAK,CAACoE,OAAO,CAACH,IAAI,CAAC,GAAG,QAAQ,GAAG,IAAI;IAChD;IACA,IAAIjE,KAAK,YAAY/F,WAAW,IAAI+F,KAAK,CAAC2D,WAAW,EAAE;MACnD,OAAO3D,KAAK,CAAC4D,IAAI,CAACQ,OAAO,CAACH,IAAI,CAAC,GAAG,QAAQ,GAAG,IAAI;IACrD;IACA,IAAI,EAAEjE,KAAK,YAAYjG,MAAM,CAAC,IAAI,EAAEiG,KAAK,YAAY/F,WAAW,CAAC,EAAE;MAC/D,OAAO+F,KAAK,CAACqE,IAAI,CAAEtF,GAAG,IAAKA,GAAG,CAACqF,OAAO,CAACH,IAAI,CAAC,CAAC,GAAG,QAAQ,GAAG,IAAI;IACnE;IACA,MAAMV,KAAK,GAAG,IAAI,CAACe,QAAQ,CAACtE,KAAK,EAAE2C,WAAW,CAAC;IAC/C,IAAIY,KAAK,CAACI,WAAW,IAAIJ,KAAK,CAACK,IAAI,CAACQ,OAAO,CAACH,IAAI,CAAC,EAAE;MAC/C,OAAO,QAAQ;IACnB;IACA,IAAIV,KAAK,CAACK,IAAI,CAACQ,OAAO,CAACH,IAAI,CAAC,EAAE;MAC1B,OAAO,OAAO;IAClB;IACA,IAAIV,KAAK,CAACO,EAAE,CAACM,OAAO,CAACH,IAAI,CAAC,EAAE;MACxB,OAAO,KAAK;IAChB;IACA,OAAOV,KAAK,CAACK,IAAI,CAACW,SAAS,CAACN,IAAI,CAAC,IAAIV,KAAK,CAACO,EAAE,CAACC,QAAQ,CAACE,IAAI,CAAC,GAAG,QAAQ,GAAG,IAAI;EAClF;EACA,IAAIE,iBAAiBA,CAAA,EAAG;IACpB,OAAO,CAAC,IAAI,CAACf,MAAM,IAAI,IAAI,CAACxB,OAAO,CAACC,SAAS;EACjD;EACA,IAAI2C,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACL,iBAAiB,GACvB,IAAI,CAACnE,KAAK,YAAYjG,MAAM;IAC5B;AACd;AACA;AACA;IACgB,IAAI,CAACiG,KAAK,YAAY/F,WAAW,IAAI,IAAI,CAAC+F,KAAK,CAAC2D,WAAW;EACvE;EACAnF,WAAWA,CAACyF,IAAI,EAAE;IACd,OAAO,IAAI,CAAChB,KAAK,CAACmB,OAAO,CAACH,IAAI,CAAC;EACnC;EACAxF,iBAAiBA,CAACwF,IAAI,EAAE;IACpB,OAAO,CAAC,IAAI,CAACzC,KAAK,CAACiD,SAAS,CAACR,IAAI,CAAC;EACtC;EACAhG,WAAWA,CAACgG,IAAI,EAAE;IACd,IAAI,CAACX,QAAQ,CAAC1C,IAAI,CAACqD,IAAI,CAAC;EAC5B;EACAK,QAAQA,CAACtE,KAAK,EAAE2C,WAAW,EAAE;IACzB,IAAI3C,KAAK,YAAYjG,MAAM,EAAE;MACzB,OAAOE,WAAW,CAAC+J,IAAI,CAAChE,KAAK,EAAE2C,WAAW,IAAI3C,KAAK,CAAC;IACxD;IACA,OAAOA,KAAK,CAAC2D,WAAW,GAClB1J,WAAW,CAAC+J,IAAI,CAAChE,KAAK,CAAC4D,IAAI,EAAEjB,WAAW,IAAI3C,KAAK,CAAC8D,EAAE,CAAC,GACrD9D,KAAK;EACf;EACAkE,iBAAiBA,CAACnF,GAAG,EAAE;IACnB,IAAInE,eAAe,CAAC,IAAI,CAAC+H,WAAW,EAAE5D,GAAG,EAAE,CAAC2F,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACN,OAAO,CAACO,CAAC,CAAC,CAAC,EAAE;MAChE;IACJ;IACA,IAAI,CAAChC,WAAW,GAAG5D,GAAG;IACtB,IAAI,CAACsE,iBAAiB,CAACzC,IAAI,CAAC7B,GAAG,CAAC;EACpC;EACA;IAAS,IAAI,CAAC6F,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyF/B,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACgC,IAAI,kBAD+EzL,EAAE,CAAA0L,iBAAA;MAAAC,IAAA,EACJlC,gBAAgB;MAAAmC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,8BAAAhJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADd/C,EAAE,CAAAgF,WAAA,aAAAhC,GAAA,CAAAkI,cACW,CAAC;QAAA;MAAA;MAAAc,MAAA;QAAA9D,KAAA;QAAAjD,mBAAA;QAAAY,aAAA;QAAAa,KAAA;QAAA2C,WAAA;QAAApD,YAAA;QAAA6D,MAAA;MAAA;MAAAmC,OAAA;QAAAlC,iBAAA;QAAAC,QAAA;MAAA;MAAAkC,UAAA;MAAAC,QAAA,GADdnM,EAAE,CAAAoM,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAA1J,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/C,EAAE,CAAA2D,cAAA,YAC0c,CAAC;UAD7c3D,EAAE,CAAA4D,UAAA,IAAAd,+BAAA,gBACimB,CAAC;UADpmB9C,EAAE,CAAAmE,MAAA;UAAFnE,EAAE,CAAAmE,MAAA;UAAFnE,EAAE,CAAA6D,YAAA,CAC+mB,CAAC;UADlnB7D,EAAE,CAAA4D,UAAA,IAAA2C,+BAAA,gBAC0qB,CAAC;UAD7qBvG,EAAE,CAAAmE,MAAA;QAAA;QAAA,IAAApB,EAAA;UAAF/C,EAAE,CAAA8D,SAAA,CAC+hB,CAAC;UADliB9D,EAAE,CAAAoD,UAAA,YAAFpD,EAAE,CAAA0M,WAAA,OAAF1M,EAAE,CAAA0M,WAAA,OAAA1J,GAAA,CAAA6G,kBAAA,EAC+hB,CAAC;UADliB7J,EAAE,CAAA8D,SAAA,EAC+pB,CAAC;UADlqB9D,EAAE,CAAAoD,UAAA,WAAFpD,EAAE,CAAAsF,WAAA,OAAAtC,GAAA,CAAAkF,KAAA,OAC+pB,CAAC;QAAA;MAAA;MAAAyE,YAAA,GAA2oM9M,YAAY,EAA+BD,EAAE,CAACgN,OAAO,EAAmHhN,EAAE,CAACG,IAAI,EAAwFH,EAAE,CAACE,SAAS,EAAyCqC,oBAAoB,EAAyDH,UAAU,EAA8FC,MAAM,EAAoEd,aAAa,EAA6CiB,oBAAoB,EAAyDF,cAAc;MAAA2K,MAAA;MAAAC,eAAA;IAAA,EAAwI;EAAE;AAC5uP;AACA/K,UAAU,CAAC,CACPR,OAAO,CACV,EAAEkI,gBAAgB,CAACsD,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC;AAChD;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KANqGhN,EAAE,CAAAiN,iBAAA,CAMXxD,gBAAgB,EAAc,CAAC;IAC/GkC,IAAI,EAAExL,SAAS;IACf+M,IAAI,EAAE,CAAC;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,QAAQ,EAAE,oBAAoB;MAAEC,OAAO,EAAE,CACxDvN,YAAY,EACZsC,oBAAoB,EACpBH,UAAU,EACVC,MAAM,EACNd,aAAa,EACbiB,oBAAoB,EACpBF,cAAc,CACjB;MAAE4K,eAAe,EAAE1M,uBAAuB,CAACiN,MAAM;MAAEC,IAAI,EAAE;QACtD,kBAAkB,EAAE;MACxB,CAAC;MAAEd,QAAQ,EAAE,ywEAAywE;MAAEK,MAAM,EAAE,CAAC,okIAAokI;IAAE,CAAC;EACp3M,CAAC,CAAC,QAAkB;IAAE3E,KAAK,EAAE,CAAC;MACtByD,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAE4E,mBAAmB,EAAE,CAAC;MACtB0G,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEwF,aAAa,EAAE,CAAC;MAChB8F,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEqG,KAAK,EAAE,CAAC;MACRiF,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEgJ,WAAW,EAAE,CAAC;MACdsC,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAE4F,YAAY,EAAE,CAAC;MACf0F,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEyJ,MAAM,EAAE,CAAC;MACT6B,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAE0J,iBAAiB,EAAE,CAAC;MACpB4B,IAAI,EAAErL;IACV,CAAC,CAAC;IAAE0J,QAAQ,EAAE,CAAC;MACX2B,IAAI,EAAErL;IACV,CAAC,CAAC;IAAE0K,QAAQ,EAAE;EAAG,CAAC;AAAA;AAE9B,MAAMuC,eAAe,CAAC;EAClB7D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChD,KAAK,GAAGhG,QAAQ,CAACkJ,YAAY,CAAC,CAAC;IACpC,IAAI,CAAC4D,GAAG,GAAG5M,aAAa;IACxB,IAAI,CAAC6M,GAAG,GAAG5M,YAAY;IACvB,IAAI,CAAC6M,WAAW,GAAG,IAAIxN,YAAY,CAAC,CAAC;IACrC,IAAI,CAACmH,SAAS,GAAG,IAAInH,YAAY,CAAC,CAAC;EACvC;EACA6G,WAAWA,CAAA,EAAG;IACV,IAAI,CAACM,SAAS,CAACsG,IAAI,CAAC,IAAI,CAACjH,KAAK,CAAC;EACnC;EACAkH,MAAMA,CAACC,IAAI,EAAE;IACT,MAAMnH,KAAK,GAAG,IAAI,CAACA,KAAK,CAACkH,MAAM,CAACC,IAAI,CAAC;IACrC,IAAI,IAAI,CAACL,GAAG,CAACM,gBAAgB,CAACpH,KAAK,CAAC,EAAE;MAClC,IAAI,CAACqH,WAAW,CAAC,IAAI,CAACP,GAAG,CAAC;IAC9B,CAAC,MACI;MACD,IAAI,CAACO,WAAW,CAAC,IAAI,CAACN,GAAG,CAACO,iBAAiB,CAACtH,KAAK,CAAC,GAAG,IAAI,CAAC+G,GAAG,GAAG/G,KAAK,CAAC;IAC1E;EACJ;EACAqH,WAAWA,CAACrH,KAAK,EAAE;IACf,IAAI,IAAI,CAACA,KAAK,CAACyE,SAAS,CAACzE,KAAK,CAAC,EAAE;MAC7B;IACJ;IACA,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACgH,WAAW,CAACpG,IAAI,CAACZ,KAAK,CAAC;EAChC;EACA;IAAS,IAAI,CAAC4E,IAAI,YAAA2C,wBAAAzC,CAAA;MAAA,YAAAA,CAAA,IAAyF+B,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAAC9B,IAAI,kBAnE+EzL,EAAE,CAAA0L,iBAAA;MAAAC,IAAA,EAmEJ4B,eAAe;MAAA3B,SAAA;MAAAI,MAAA;QAAAtF,KAAA;QAAA8G,GAAA;QAAAC,GAAA;MAAA;MAAAxB,OAAA;QAAAyB,WAAA;QAAArG,SAAA;MAAA;MAAA6E,UAAA;MAAAC,QAAA,GAnEbnM,EAAE,CAAAoM,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA0B,yBAAAnL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAkE,GAAA,GAAFjH,EAAE,CAAAkE,gBAAA;UAAFlE,EAAE,CAAA2D,cAAA,wBAmEub,CAAC;UAnE1b3D,EAAE,CAAAoE,UAAA,uBAAA+J,8DAAA;YAAFnO,EAAE,CAAAsE,aAAA,CAAA2C,GAAA;YAAA,OAAFjH,EAAE,CAAA0E,WAAA,CAmEuX1B,GAAA,CAAA4K,MAAA,CAAO;cAAA1F,KAAA,GAAS;YAAC,CAAC,CAAC;UAAA,CAAC,CAAC,wBAAAkG,+DAAA;YAnE9YpO,EAAE,CAAAsE,aAAA,CAAA2C,GAAA;YAAA,OAAFjH,EAAE,CAAA0E,WAAA,CAmEia1B,GAAA,CAAA4K,MAAA,CAAO;cAAA1F,KAAA,EAAQ;YAAC,CAAC,CAAC;UAAA,CAAC,CAAC;UAnEvblI,EAAE,CAAA+E,MAAA,EAmEie,CAAC;UAnEpe/E,EAAE,CAAAmE,MAAA;UAAFnE,EAAE,CAAAmE,MAAA;UAAFnE,EAAE,CAAA4D,UAAA,IAAA4C,uCAAA,yBAmE4hB,CAAC,IAAAI,sCAAA,gCAnE/hB5G,EAAE,CAAAqO,sBAmE+mB,CAAC;UAnElnBrO,EAAE,CAAA6D,YAAA,CAmEg9B,CAAC;QAAA;QAAA,IAAAd,EAAA;UAAA,MAAAuL,SAAA,GAnEn9BtO,EAAE,CAAAuO,WAAA;UAAFvO,EAAE,CAAAoD,UAAA,mBAmEwP,CAAC,iBAAAJ,GAAA,CAAA0D,KAAA,CAAAsH,iBAAA,CAAAhL,GAAA,CAAAwK,GAAA,CAAoD,CAAC,kBAAAxK,GAAA,CAAA0D,KAAA,CAAAoH,gBAAA,CAAA9K,GAAA,CAAAyK,GAAA,CAAoD,CAAC;UAnErWzN,EAAE,CAAA8D,SAAA,CAmEie,CAAC;UAnEpe9D,EAAE,CAAAwF,kBAAA,MAAFxF,EAAE,CAAA0M,WAAA,OAAF1M,EAAE,CAAA0M,WAAA,OAAA1J,GAAA,CAAA0D,KAAA,OAmEie,CAAC;UAnEpe1G,EAAE,CAAA8D,SAAA,EAmE8gB,CAAC;UAnEjhB9D,EAAE,CAAAoD,UAAA,SAAAJ,GAAA,CAAAwK,GAAA,CAAArF,IAAA,KAAAnF,GAAA,CAAAyK,GAAA,CAAAtF,IAmE8gB,CAAC,aAAAmG,SAAU,CAAC;QAAA;MAAA;MAAA3B,YAAA,GAAqgB7M,SAAS,EAA8CC,IAAI,EAA6FuC,OAAO,EAAuFD,YAAY,EAAiDE,aAAa;MAAAsK,MAAA;MAAAC,eAAA;IAAA,EAAiM;EAAE;AACzoD;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KArEqGhN,EAAE,CAAAiN,iBAAA,CAqEXM,eAAe,EAAc,CAAC;IAC9G5B,IAAI,EAAExL,SAAS;IACf+M,IAAI,EAAE,CAAC;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,QAAQ,EAAE,mBAAmB;MAAEC,OAAO,EAAE,CAACtN,SAAS,EAAEC,IAAI,EAAEuC,OAAO,EAAED,YAAY,EAAEE,aAAa,CAAC;MAAEuK,eAAe,EAAE1M,uBAAuB,CAACiN,MAAM;MAAEb,QAAQ,EAAE,uwBAAuwB;MAAEK,MAAM,EAAE,CAAC,wBAAwB;IAAE,CAAC;EACx+B,CAAC,CAAC,QAAkB;IAAEnG,KAAK,EAAE,CAAC;MACtBiF,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEmN,GAAG,EAAE,CAAC;MACN7B,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEoN,GAAG,EAAE,CAAC;MACN9B,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEqN,WAAW,EAAE,CAAC;MACd/B,IAAI,EAAErL;IACV,CAAC,CAAC;IAAE+G,SAAS,EAAE,CAAC;MACZsE,IAAI,EAAErL;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkO,KAAK,GAAG,GAAG;AACjB,MAAMC,YAAY,GAAG,CAAC;AACtB,MAAMC,eAAe,CAAC;EAClBhF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACL,WAAW,GAAG,IAAI;IACvB,IAAI,CAACsF,WAAW,GAAGjO,QAAQ,CAACkJ,YAAY,CAAC,CAAC,CAACzB,IAAI;IAC/C,IAAI,CAACzB,KAAK,GAAG,IAAI;IACjB,IAAI,CAACkI,WAAW,GAAG,IAAI,CAACD,WAAW;IACnC,IAAI,CAACnB,GAAG,GAAG1M,QAAQ;IACnB,IAAI,CAAC2M,GAAG,GAAG1M,QAAQ;IACnB,IAAI,CAACwH,SAAS,GAAG,KAAK;IACtB,IAAI,CAACtD,mBAAmB,GAAGzE,iBAAiB;IAC5C,IAAI,CAAC6G,SAAS,GAAG,IAAInH,YAAY,CAAC,CAAC;EACvC;EACAsH,UAAUA,CAACmD,IAAI,EAAE;IACb,OAAS,IAAI,CAAC8C,GAAG,IAAI,IAAI,CAACA,GAAG,GAAG9C,IAAI,IAC/B,IAAI,CAAC6C,GAAG,IAAI,IAAI,CAACA,GAAG,GAAG7C,IAAK,IAC7B,IAAI,CAAC1F,mBAAmB,CAAC0F,IAAI,CAAC;EACtC;EACAtF,YAAYA,CAACsF,IAAI,EAAE;IACf,MAAM;MAAEjE,KAAK;MAAE2C;IAAY,CAAC,GAAG,IAAI;IACnC,IAAI3C,KAAK,YAAY1F,OAAO,IAAI0F,KAAK,CAACyB,IAAI,KAAKwC,IAAI,EAAE;MACjD,OAAO,QAAQ;IACnB;IACA,IAAInJ,WAAW,CAACkF,KAAK,CAAC,EAAE;MACpB,OAAOA,KAAK,KAAKiE,IAAI,GAAG,QAAQ,GAAG,IAAI;IAC3C;IACA,IAAI,EAAEjE,KAAK,YAAYzF,aAAa,CAAC,IAAI,EAAEyF,KAAK,YAAY1F,OAAO,CAAC,EAAE;MAClE,OAAO0F,KAAK,EAAEqE,IAAI,CAAEtF,GAAG,IAAKA,GAAG,CAAC0C,IAAI,KAAKwC,IAAI,CAAC,GAAG,QAAQ,GAAG,IAAI;IACpE;IACA,MAAMkE,OAAO,GAAG,IAAI,CAAC3D,cAAc,GAAG7B,WAAW,GAAG,IAAI;IACxD,MAAMiB,IAAI,GAAG,MAAM,IAAI5D,KAAK,GAAGA,KAAK,CAAC4D,IAAI,EAAEnC,IAAI,GAAGzB,KAAK,CAACyB,IAAI;IAC5D,MAAMqC,EAAE,GAAG,MAAM,IAAI9D,KAAK,GAAGA,KAAK,CAAC8D,EAAE,CAACrC,IAAI,GAAGzB,KAAK,CAACyB,IAAI;IACvD,MAAMqF,GAAG,GAAGsB,IAAI,CAACtB,GAAG,CAAClD,IAAI,EAAEuE,OAAO,IAAIrE,EAAE,CAAC;IACzC,MAAMiD,GAAG,GAAGqB,IAAI,CAACrB,GAAG,CAACnD,IAAI,EAAEuE,OAAO,IAAIrE,EAAE,CAAC;IACzC,IAAIgD,GAAG,KAAKC,GAAG,IAAInD,IAAI,KAAKE,EAAE,IAAIF,IAAI,KAAKK,IAAI,EAAE;MAC7C,OAAO,QAAQ;IACnB;IACA,IAAI6C,GAAG,KAAK7C,IAAI,EAAE;MACd,OAAO,OAAO;IAClB;IACA,IAAI8C,GAAG,KAAK9C,IAAI,EAAE;MACd,OAAO,KAAK;IAChB;IACA,OAAO6C,GAAG,GAAG7C,IAAI,IAAIA,IAAI,GAAG8C,GAAG,GAAG,QAAQ,GAAG,IAAI;EACrD;EACA3I,aAAaA,CAAC+J,OAAO,EAAElE,IAAI,EAAE;IACzB,IAAI,CAACC,iBAAiB,CAACiE,OAAO,EAAElE,IAAI,CAAC;EACzC;EACA,IAAIO,cAAcA,CAAA,EAAG;IACjB,OAAQ,IAAI,CAAC3C,SAAS,KACjB,IAAI,CAAC7B,KAAK,YAAYjG,MAAM,IAAI,IAAI,CAACiG,KAAK,YAAYhG,QAAQ,CAAC;EACxE;EACA,IAAIqO,IAAIA,CAAA,EAAG;IACP,OAAOD,IAAI,CAACE,IAAI,CAAC,CAAC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,aAAa,IAAIT,YAAY,CAAC;EAC9E;EACAhH,kBAAkBA,CAACkD,IAAI,EAAE;IACrB,OAAO,IAAI,CAACiE,WAAW,KAAKjE,IAAI;EACpC;EACA9C,OAAOA,CAACsH,QAAQ,EAAEC,QAAQ,EAAE;IACxB,OAAOD,QAAQ,GAAGV,YAAY,GAAGW,QAAQ,GAAG,IAAI,CAACF,aAAa;EAClE;EACAhK,WAAWA,CAACyF,IAAI,EAAE;IACd,OAAO,IAAI,CAACgE,WAAW,KAAKhE,IAAI;EACpC;EACA,IAAIuE,aAAaA,CAAA,EAAG;IAChB,MAAMG,OAAO,GAAG,IAAI,CAACT,WAAW,GAAGJ,KAAK;IACxC,MAAMhB,GAAG,GAAG,IAAI,CAACA,GAAG,IAAI1M,QAAQ;IAChC,OAAO0M,GAAG,GAAG6B,OAAO,GAAG7B,GAAG,GAAG6B,OAAO;EACxC;EACA,IAAIJ,aAAaA,CAAA,EAAG;IAChB,MAAMI,OAAO,GAAG,IAAI,CAACT,WAAW,GAAGJ,KAAK;IACxC,MAAMf,GAAG,GAAG,IAAI,CAACA,GAAG,IAAI1M,QAAQ;IAChC,OAAO0M,GAAG,GAAG4B,OAAO,GAAG5B,GAAG,GAAG,CAAC,GAAG4B,OAAO;EAC5C;EACAzE,iBAAiBA,CAACiE,OAAO,EAAElE,IAAI,EAAE;IAC7B,IAAI,CAACtB,WAAW,GAAGwF,OAAO,GAAGlE,IAAI,GAAG,IAAI;EAC5C;EACA;IAAS,IAAI,CAACW,IAAI,YAAAgE,wBAAA9D,CAAA;MAAA,YAAAA,CAAA,IAAyFkD,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACjD,IAAI,kBAnK+EzL,EAAE,CAAA0L,iBAAA;MAAAC,IAAA,EAmKJ+C,eAAe;MAAA9C,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAyD,6BAAAxM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnKb/C,EAAE,CAAAgF,WAAA,aAAAhC,GAAA,CAAAkI,cAmKU,CAAC;QAAA;MAAA;MAAAc,MAAA;QAAAtF,KAAA;QAAAkI,WAAA;QAAApB,GAAA;QAAAC,GAAA;QAAAlF,SAAA;QAAAtD,mBAAA;MAAA;MAAAgH,OAAA;QAAA5E,SAAA;MAAA;MAAA6E,UAAA;MAAAC,QAAA,GAnKbnM,EAAE,CAAAoM,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAgD,yBAAAzM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/C,EAAE,CAAA4D,UAAA,IAAAkE,8BAAA,gBAmKuc,CAAC;QAAA;QAAA,IAAA/E,EAAA;UAnK1c/C,EAAE,CAAAoD,UAAA,qBAAAJ,GAAA,CAAA+L,IAmK+X,CAAC;QAAA;MAAA;MAAApC,YAAA,GAAs6I3K,UAAU,EAA8FC,MAAM,EAAyEC,cAAc,EAA6GT,iBAAiB;MAAAoL,MAAA;MAAAC,eAAA;IAAA,EAA0H;EAAE;AAC50K;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KArKqGhN,EAAE,CAAAiN,iBAAA,CAqKXyB,eAAe,EAAc,CAAC;IAC9G/C,IAAI,EAAExL,SAAS;IACf+M,IAAI,EAAE,CAAC;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,QAAQ,EAAE,mBAAmB;MAAEC,OAAO,EAAE,CAACpL,UAAU,EAAEC,MAAM,EAAEC,cAAc,EAAET,iBAAiB,CAAC;MAAEqL,eAAe,EAAE1M,uBAAuB,CAACiN,MAAM;MAAEC,IAAI,EAAE;QACvK,kBAAkB,EAAE;MACxB,CAAC;MAAEd,QAAQ,EAAE,0vBAA0vB;MAAEK,MAAM,EAAE,CAAC,qqHAAqqH;IAAE,CAAC;EACt8I,CAAC,CAAC,QAAkB;IAAEnG,KAAK,EAAE,CAAC;MACtBiF,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEuO,WAAW,EAAE,CAAC;MACdjD,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEmN,GAAG,EAAE,CAAC;MACN7B,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEoN,GAAG,EAAE,CAAC;MACN9B,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEkI,SAAS,EAAE,CAAC;MACZoD,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAE4E,mBAAmB,EAAE,CAAC;MACtB0G,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEgH,SAAS,EAAE,CAAC;MACZsE,IAAI,EAAErL;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmP,WAAW,CAAC;EACd/F,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgG,GAAG,GAAGzP,MAAM,CAACM,iBAAiB,CAAC;IACpC,IAAI,CAACkF,GAAG,GAAG,IAAI;IACf,IAAI,CAACkK,IAAI,GAAG,OAAO;IACnB,IAAI,CAACrH,OAAO,GAAGrI,MAAM,CAACsJ,0BAA0B,CAAC;IACjD,IAAI,CAACrB,KAAK,GAAGxH,QAAQ,CAACkJ,YAAY,CAAC,CAAC;IACpC,IAAI,CAAC3E,mBAAmB,GAAGzE,iBAAiB;IAC5C,IAAI,CAACgN,GAAG,GAAG5M,aAAa;IACxB,IAAI,CAAC6M,GAAG,GAAG5M,YAAY;IACvB,IAAI,CAAC+O,cAAc,GAAGhP,aAAa;IACnC,IAAI,CAACiP,cAAc,GAAGhP,YAAY;IAClC,IAAI,CAACwI,WAAW,GAAG,IAAI;IACvB,IAAI,CAACpD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACJ,aAAa,GAAG,IAAI;IACzB,IAAI,CAACmE,QAAQ,GAAG,IAAI9J,YAAY,CAAC,CAAC;IAClC,IAAI,CAAC4P,WAAW,GAAG,IAAI5P,YAAY,CAAC,CAAC;IACrC,IAAI,CAAC6J,iBAAiB,GAAG,IAAI7J,YAAY,CAAC,CAAC;IAC3C;IACA,IAAI,CAACwN,WAAW,GAAG,IAAI5L,OAAO,CAAC,CAAC;IAChC,IAAI,CAACsH,yBAAyB,GAAG,CAACnE,mBAAmB,EAAEuI,GAAG,EAAEC,GAAG,KAAM9C,IAAI,IAAKA,IAAI,CAACM,SAAS,CAACuC,GAAG,CAAC,IAAI7C,IAAI,CAACF,QAAQ,CAACgD,GAAG,CAAC,IAAIxI,mBAAmB,CAAC0F,IAAI,CAAC;EACxJ;EACA,IAAIjE,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACgJ,GAAG,CAACK,YAAY,CAAC,CAAC;IACvB,IAAI,CAACtK,GAAG,GAAGiB,KAAK;IAChB,IAAI,IAAI,CAACT,YAAY,IACjBS,KAAK,YAAYjG,MAAM,IACvBiG,KAAK,CAAC6D,eAAe,CAACrJ,sBAAsB,CAAC,EAAE;MAC/C,IAAI,CAACgH,KAAK,GAAGxB,KAAK;IACtB;EACJ;EACA,IAAIsJ,WAAWA,CAACL,IAAI,EAAE;IAClB,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA,IAAIjJ,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjB,GAAG;EACnB;EACAiD,uBAAuBA,CAACR,KAAK,EAAE;IAC3B,IAAI,CAAC+H,iBAAiB,CAAC/H,KAAK,CAAC;EACjC;EACAY,UAAUA,CAACrD,GAAG,EAAE;IACZ,IAAI,CAACuE,QAAQ,CAAC1C,IAAI,CAAC7B,GAAG,CAAC;IACvB,IAAI,CAACiI,WAAW,CAACC,IAAI,CAAClI,GAAG,CAAC;EAC9B;EACAuD,mBAAmBA,CAACvD,GAAG,EAAE;IACrB,IAAI,CAACyK,gBAAgB,CAACzK,GAAG,CAAC;EAC9B;EACA,IAAI4C,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACmF,GAAG,IAAI5M,aAAa;EACpC;EACA,IAAIwH,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACqF,GAAG,IAAI5M,YAAY;EACnC;EACA,IAAIqI,sBAAsBA,CAAA,EAAG;IACzB,MAAMsE,GAAG,GAAG,IAAI,CAACnF,WAAW;IAC5B,MAAM8H,SAAS,GAAG,IAAI,CAACP,cAAc,IAAIhP,aAAa;IACtD,OAAOuP,SAAS,CAACrC,gBAAgB,CAACN,GAAG,CAAC,GAAG2C,SAAS,GAAG3C,GAAG;EAC5D;EACA,IAAIvE,sBAAsBA,CAAA,EAAG;IACzB,MAAMwE,GAAG,GAAG,IAAI,CAACrF,WAAW;IAC5B,MAAMgI,SAAS,GAAG,IAAI,CAACP,cAAc,IAAIhP,YAAY;IACrD,OAAOuP,SAAS,CAACpC,iBAAiB,CAACP,GAAG,CAAC,GAAG2C,SAAS,GAAG3C,GAAG;EAC7D;EACA,IAAI4C,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACV,IAAI,KAAK,MAAM;EAC/B;EACA/G,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC+G,IAAI,GAAG,MAAM;EACtB;EACA1H,iBAAiBA,CAACE,IAAI,EAAE;IACpB,IAAI,CAACwH,IAAI,GAAG,OAAO;IACnB,IAAI,CAACM,iBAAiB,CAAC,IAAIvP,QAAQ,CAACyH,IAAI,EAAE,IAAI,CAACD,KAAK,CAACA,KAAK,CAAC,CAAC;EAChE;EACA+H,iBAAiBA,CAAC/H,KAAK,EAAE;IACrB,IAAI,IAAI,CAACA,KAAK,CAACiD,SAAS,CAACjD,KAAK,CAAC,EAAE;MAC7B;IACJ;IACA,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC4H,WAAW,CAACxI,IAAI,CAACY,KAAK,CAAC;EAChC;EACAgI,gBAAgBA,CAACzK,GAAG,EAAE;IAClB,IAAInE,eAAe,CAAC,IAAI,CAAC+H,WAAW,EAAE5D,GAAG,EAAE,CAAC2F,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACN,OAAO,CAACO,CAAC,CAAC,CAAC,EAAE;MAChE;IACJ;IACA,IAAI,CAAChC,WAAW,GAAG5D,GAAG;IACtB,IAAI,CAACsE,iBAAiB,CAACzC,IAAI,CAAC7B,GAAG,CAAC;EACpC;EACA;IAAS,IAAI,CAAC6F,IAAI,YAAAgF,oBAAA9E,CAAA;MAAA,YAAAA,CAAA,IAAyFiE,WAAW;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAAChE,IAAI,kBAlR+EzL,EAAE,CAAA0L,iBAAA;MAAAC,IAAA,EAkRJ8D,WAAW;MAAA7D,SAAA;MAAAE,YAAA,WAAAyE,yBAAAxN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlRT/C,EAAE,CAAAoE,UAAA,0CAAAoM,4DAAA;YAAA,OAkRJ,CAAC;UAAA,CAAS,CAAC;QAAA;MAAA;MAAAxE,MAAA;QAAA9D,KAAA;QAAAjD,mBAAA;QAAAuI,GAAA;QAAAC,GAAA;QAAAmC,cAAA;QAAAC,cAAA;QAAAxG,WAAA;QAAApD,YAAA;QAAAJ,aAAA;QAAAa,KAAA;QAAAsJ,WAAA;MAAA;MAAA/D,OAAA;QAAAjC,QAAA;QAAA8F,WAAA;QAAA/F,iBAAA;MAAA;MAAAmC,UAAA;MAAAC,QAAA,GAlRTnM,EAAE,CAAAyQ,kBAAA,CAkRihB,CAAC5O,cAAc,CAAC4N,WAAW,CAAC,CAAC,GAlRhjBzP,EAAE,CAAAoM,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAkE,qBAAA3N,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/C,EAAE,CAAA4D,UAAA,IAAAmE,oCAAA,0BAkR8sB,CAAC,IAAAS,kCAAA,iCAlRjtBxI,EAAE,CAAAqO,sBAkR8iC,CAAC;QAAA;QAAA,IAAAtL,EAAA;UAAA,MAAA4N,WAAA,GAlRjjC3Q,EAAE,CAAAuO,WAAA;UAAFvO,EAAE,CAAAoD,UAAA,SAAAJ,GAAA,CAAAqN,YAkRknB,CAAC,aAAAM,WAAY,CAAC;QAAA;MAAA;MAAAhE,YAAA,GAA+jD5M,IAAI,EAA6F0J,gBAAgB,EAAwN8D,eAAe,EAAsImB,eAAe,EAAqKvN,aAAa,EAAkDO,YAAY;MAAAmL,MAAA;MAAAC,eAAA;IAAA,EAAyG;EAAE;AAC9mG;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KApRqGhN,EAAE,CAAAiN,iBAAA,CAoRXwC,WAAW,EAAc,CAAC;IAC1G9D,IAAI,EAAExL,SAAS;IACf+M,IAAI,EAAE,CAAC;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,QAAQ,EAAE,cAAc;MAAEC,OAAO,EAAE,CAClDrN,IAAI,EACJ0J,gBAAgB,EAChB8D,eAAe,EACfmB,eAAe,EACfvN,aAAa,EACbO,YAAY,CACf;MAAEoL,eAAe,EAAE1M,uBAAuB,CAACiN,MAAM;MAAEuD,SAAS,EAAE,CAAC/O,cAAc,CAAC4N,WAAW,CAAC,CAAC;MAAEnC,IAAI,EAAE;QAChG,gCAAgC,EAAE;MACtC,CAAC;MAAEd,QAAQ,EAAE,+yCAA+yC;MAAEK,MAAM,EAAE,CAAC,gRAAgR;IAAE,CAAC;EACtmD,CAAC,CAAC,QAAkB;IAAE3E,KAAK,EAAE,CAAC;MACtByD,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAE4E,mBAAmB,EAAE,CAAC;MACtB0G,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEmN,GAAG,EAAE,CAAC;MACN7B,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEoN,GAAG,EAAE,CAAC;MACN9B,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEuP,cAAc,EAAE,CAAC;MACjBjE,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEwP,cAAc,EAAE,CAAC;MACjBlE,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEgJ,WAAW,EAAE,CAAC;MACdsC,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAE4F,YAAY,EAAE,CAAC;MACf0F,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAEwF,aAAa,EAAE,CAAC;MAChB8F,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAE2J,QAAQ,EAAE,CAAC;MACX2B,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEwP,WAAW,EAAE,CAAC;MACdnE,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEyJ,iBAAiB,EAAE,CAAC;MACpB4B,IAAI,EAAErL;IACV,CAAC,CAAC;IAAEoG,KAAK,EAAE,CAAC;MACRiF,IAAI,EAAEtL;IACV,CAAC,CAAC;IAAE2P,WAAW,EAAE,CAAC;MACdrE,IAAI,EAAEtL;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASiJ,kCAAkC,EAAEC,0BAA0B,EAAEkG,WAAW,EAAEhG,gBAAgB,EAAE8D,eAAe,EAAEmB,eAAe,EAAElF,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}