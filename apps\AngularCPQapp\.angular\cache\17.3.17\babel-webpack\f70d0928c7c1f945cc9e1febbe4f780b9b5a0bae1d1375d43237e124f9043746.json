{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, ElementRef, Directive, ContentChildren, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiQueryListChanges, tuiControlValue, tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { tuiInjectElement, tuiIsHTMLElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiPx } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiBadgeNotificationOptionsProvider } from '@taiga-ui/kit/components/badge-notification';\nimport { NgControl, RadioControlValueAccessor } from '@angular/forms';\nimport { RouterLinkActive } from '@angular/router';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { switchMap, map } from 'rxjs';\nconst _c0 = [\"*\"];\nclass TuiSegmentedDirective {\n  constructor() {\n    this.controls = EMPTY_QUERY;\n    this.radios = EMPTY_QUERY;\n    this.links = EMPTY_QUERY;\n    this.elements = EMPTY_QUERY;\n    this.component = inject(TuiSegmented);\n    this.el = tuiInjectElement();\n  }\n  ngAfterContentInit() {\n    tuiQueryListChanges(this.controls).pipe(switchMap(() => tuiControlValue(this.controls.first)), map(value => this.radios.toArray().findIndex(c => c.value === value))).subscribe(index => {\n      this.component.update(index);\n    });\n  }\n  ngAfterContentChecked() {\n    if (this.links.length) {\n      this.update(this.elements.get(this.linkIndex)?.nativeElement || null);\n    }\n  }\n  update(target) {\n    this.component.update(this.getIndex(target));\n  }\n  get linkIndex() {\n    return this.links.toArray().findIndex(link => link.isActive);\n  }\n  getIndex(element) {\n    return Array.from(this.el.children).findIndex(tab => tab.contains(element));\n  }\n  static {\n    this.ɵfac = function TuiSegmentedDirective_Factory(t) {\n      return new (t || TuiSegmentedDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiSegmentedDirective,\n      contentQueries: function TuiSegmentedDirective_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NgControl, 5);\n          i0.ɵɵcontentQuery(dirIndex, RadioControlValueAccessor, 5);\n          i0.ɵɵcontentQuery(dirIndex, RouterLinkActive, 4);\n          i0.ɵɵcontentQuery(dirIndex, RouterLinkActive, 4, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.controls = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.radios = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.links = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elements = _t);\n        }\n      },\n      hostBindings: function TuiSegmentedDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TuiSegmentedDirective_click_HostBindingHandler($event) {\n            return ctx.update($event.target);\n          });\n        }\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSegmentedDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      host: {\n        '(click)': 'update($event.target)'\n      }\n    }]\n  }], null, {\n    controls: [{\n      type: ContentChildren,\n      args: [NgControl, {\n        descendants: true\n      }]\n    }],\n    radios: [{\n      type: ContentChildren,\n      args: [RadioControlValueAccessor, {\n        descendants: true\n      }]\n    }],\n    links: [{\n      type: ContentChildren,\n      args: [RouterLinkActive]\n    }],\n    elements: [{\n      type: ContentChildren,\n      args: [RouterLinkActive, {\n        read: ElementRef\n      }]\n    }]\n  });\n})();\nconst [TUI_SEGMENTED_OPTIONS, tuiSegmentedOptionsProvider] = tuiCreateOptions({\n  size: 's'\n});\nclass TuiSegmented {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.sub = inject(ResizeObserverService, {\n      self: true\n    }).pipe(tuiZonefree(), takeUntilDestroyed()).subscribe(() => this.refresh());\n    this.size = inject(TUI_SEGMENTED_OPTIONS).size;\n    this.activeItemIndex = 0;\n    this.activeItemIndexChange = new EventEmitter();\n  }\n  ngOnChanges() {\n    this.refresh();\n  }\n  update(activeItemIndex) {\n    if (activeItemIndex === this.activeItemIndex || activeItemIndex < 0) {\n      return;\n    }\n    this.activeItemIndex = activeItemIndex;\n    this.activeItemIndexChange.emit(activeItemIndex);\n    this.refresh();\n  }\n  get activeElement() {\n    return this.el.children.item(this.activeItemIndex);\n  }\n  refresh() {\n    const el = this.activeElement;\n    if (!tuiIsHTMLElement(el)) {\n      return;\n    }\n    Array.from(this.el.children).forEach(e => e.classList.remove('tui-segmented_active'));\n    el.classList.add('tui-segmented_active');\n    const {\n      offsetWidth = 0,\n      offsetLeft = 0,\n      offsetTop = 0\n    } = el;\n    this.el.style.setProperty('--t-top', tuiPx(offsetTop));\n    this.el.style.setProperty('--t-left', tuiPx(offsetLeft));\n    this.el.style.setProperty('--t-width', tuiPx(offsetWidth));\n  }\n  static {\n    this.ɵfac = function TuiSegmented_Factory(t) {\n      return new (t || TuiSegmented)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiSegmented,\n      selectors: [[\"tui-segmented\"]],\n      hostVars: 1,\n      hostBindings: function TuiSegmented_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n        }\n      },\n      inputs: {\n        size: \"size\",\n        activeItemIndex: \"activeItemIndex\"\n      },\n      outputs: {\n        activeItemIndexChange: \"activeItemIndexChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([ResizeObserverService, tuiBadgeNotificationOptionsProvider({\n        size: 's'\n      })]), i0.ɵɵHostDirectivesFeature([TuiSegmentedDirective]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function TuiSegmented_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\"tui-segmented{position:relative;display:flex;color:var(--tui-background-base);background:var(--tui-background-neutral-1);overflow:hidden;-webkit-mask-image:linear-gradient(black,black);mask-image:linear-gradient(#000,#000)}tui-segmented[data-size=s]{--t-padding: .5rem;--t-gap: .5rem;--t-margin: -.125rem;--t-height: var(--tui-height-s);font:var(--tui-font-text-s);border-radius:var(--tui-radius-m)}tui-segmented[data-size=s] tui-icon{font-size:1rem}tui-segmented[data-size=m]{--t-padding: .875rem;--t-gap: 1rem;--t-margin: -.375rem;--t-height: var(--tui-height-m);font:var(--tui-font-text-m);border-radius:var(--tui-radius-m)}tui-segmented[data-size=l]{--t-padding: 1.25rem;--t-gap: 1rem;--t-margin: -.375rem;--t-height: var(--tui-height-l);font:var(--tui-font-text-l);border-radius:var(--tui-radius-l)}tui-segmented[data-size=l]>*:before{block-size:1.25rem}tui-segmented>a,tui-segmented>button,tui-segmented>label,tui-segmented>label>input:not([tuiRadio]){transition-property:color,background,opacity,border-image;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;line-height:inherit;text-decoration:none;position:relative;display:flex;align-items:center;justify-content:center;block-size:var(--t-height);white-space:nowrap;gap:var(--t-gap);margin:0;padding:0 var(--t-padding);color:var(--tui-text-primary);overflow:hidden;cursor:pointer;font:inherit;border-radius:inherit;border:.125rem solid transparent;background-clip:padding-box;box-sizing:border-box;border-image:linear-gradient(var(--tui-border-normal),transparent) 1 / 0 0 25% .5 / 0 0 100 .5;clip-path:polygon(-1rem calc(50% - .5rem),1px calc(50% - .5rem),1px 0,100% 0,100% 100%,1px 100%,1px calc(50% + .5rem),-1rem calc(50% + .5rem))}tui-segmented>a>*,tui-segmented>button>*,tui-segmented>label>*,tui-segmented>label>input:not([tuiRadio])>*{margin:0 var(--t-margin)}tui-segmented>a:focus-visible,tui-segmented>button:focus-visible,tui-segmented>label:focus-visible,tui-segmented>label>input:not([tuiRadio]):focus-visible{outline:.125rem solid var(--tui-border-focus);outline-offset:-.25rem}@media (hover: hover) and (pointer: fine){tui-segmented>a:not(.tui-segmented_active):not(:active):hover,tui-segmented>button:not(.tui-segmented_active):not(:active):hover,tui-segmented>label:not(.tui-segmented_active):not(:active):hover,tui-segmented>label>input:not([tuiRadio]):not(.tui-segmented_active):not(:active):hover{background-color:var(--tui-background-neutral-1-hover)}tui-segmented>a:not(.tui-segmented_active):not(:active):hover,tui-segmented>button:not(.tui-segmented_active):not(:active):hover,tui-segmented>label:not(.tui-segmented_active):not(:active):hover,tui-segmented>label>input:not([tuiRadio]):not(.tui-segmented_active):not(:active):hover,tui-segmented>a:not(.tui-segmented_active):not(:active):hover+*,tui-segmented>button:not(.tui-segmented_active):not(:active):hover+*,tui-segmented>label:not(.tui-segmented_active):not(:active):hover+*,tui-segmented>label>input:not([tuiRadio]):not(.tui-segmented_active):not(:active):hover+*{border-image:linear-gradient(var(--tui-border-normal),transparent) 1 / 0 0 25% .5 / 100 0 0 .5}}tui-segmented>a:active,tui-segmented>button:active,tui-segmented>label:active,tui-segmented>label>input:not([tuiRadio]):active,tui-segmented>a:active+*,tui-segmented>button:active+*,tui-segmented>label:active+*,tui-segmented>label>input:not([tuiRadio]):active+*,tui-segmented>a.tui-segmented_active,tui-segmented>button.tui-segmented_active,tui-segmented>label.tui-segmented_active,tui-segmented>label>input:not([tuiRadio]).tui-segmented_active,tui-segmented>a.tui-segmented_active+*,tui-segmented>button.tui-segmented_active+*,tui-segmented>label.tui-segmented_active+*,tui-segmented>label>input:not([tuiRadio]).tui-segmented_active+*{border-image:linear-gradient(var(--tui-border-normal),transparent) 1 / 0 0 25% .5 / 100 0 0 .5}tui-segmented>label>input:not([tuiRadio]){position:absolute;top:-.125rem;left:-.125rem;right:-.125rem;bottom:-.125rem;background:transparent!important}tui-segmented:before{transition-property:top,left,width;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;left:var(--t-left);top:var(--t-top);block-size:var(--t-height);inline-size:var(--t-width);border-radius:inherit;background:currentColor;background-clip:padding-box;border:.125rem solid transparent;box-sizing:border-box;filter:drop-shadow(0 .25rem 1.25rem rgba(0,0,0,.1))}[tuiTheme=dark] tui-segmented,tui-segmented[tuiTheme=dark]{--tui-background-base: var(--tui-background-neutral-1-hover)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSegmented, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-segmented',\n      template: '<ng-content />',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [ResizeObserverService, tuiBadgeNotificationOptionsProvider({\n        size: 's'\n      })],\n      hostDirectives: [TuiSegmentedDirective],\n      host: {\n        '[attr.data-size]': 'size'\n      },\n      styles: [\"tui-segmented{position:relative;display:flex;color:var(--tui-background-base);background:var(--tui-background-neutral-1);overflow:hidden;-webkit-mask-image:linear-gradient(black,black);mask-image:linear-gradient(#000,#000)}tui-segmented[data-size=s]{--t-padding: .5rem;--t-gap: .5rem;--t-margin: -.125rem;--t-height: var(--tui-height-s);font:var(--tui-font-text-s);border-radius:var(--tui-radius-m)}tui-segmented[data-size=s] tui-icon{font-size:1rem}tui-segmented[data-size=m]{--t-padding: .875rem;--t-gap: 1rem;--t-margin: -.375rem;--t-height: var(--tui-height-m);font:var(--tui-font-text-m);border-radius:var(--tui-radius-m)}tui-segmented[data-size=l]{--t-padding: 1.25rem;--t-gap: 1rem;--t-margin: -.375rem;--t-height: var(--tui-height-l);font:var(--tui-font-text-l);border-radius:var(--tui-radius-l)}tui-segmented[data-size=l]>*:before{block-size:1.25rem}tui-segmented>a,tui-segmented>button,tui-segmented>label,tui-segmented>label>input:not([tuiRadio]){transition-property:color,background,opacity,border-image;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;line-height:inherit;text-decoration:none;position:relative;display:flex;align-items:center;justify-content:center;block-size:var(--t-height);white-space:nowrap;gap:var(--t-gap);margin:0;padding:0 var(--t-padding);color:var(--tui-text-primary);overflow:hidden;cursor:pointer;font:inherit;border-radius:inherit;border:.125rem solid transparent;background-clip:padding-box;box-sizing:border-box;border-image:linear-gradient(var(--tui-border-normal),transparent) 1 / 0 0 25% .5 / 0 0 100 .5;clip-path:polygon(-1rem calc(50% - .5rem),1px calc(50% - .5rem),1px 0,100% 0,100% 100%,1px 100%,1px calc(50% + .5rem),-1rem calc(50% + .5rem))}tui-segmented>a>*,tui-segmented>button>*,tui-segmented>label>*,tui-segmented>label>input:not([tuiRadio])>*{margin:0 var(--t-margin)}tui-segmented>a:focus-visible,tui-segmented>button:focus-visible,tui-segmented>label:focus-visible,tui-segmented>label>input:not([tuiRadio]):focus-visible{outline:.125rem solid var(--tui-border-focus);outline-offset:-.25rem}@media (hover: hover) and (pointer: fine){tui-segmented>a:not(.tui-segmented_active):not(:active):hover,tui-segmented>button:not(.tui-segmented_active):not(:active):hover,tui-segmented>label:not(.tui-segmented_active):not(:active):hover,tui-segmented>label>input:not([tuiRadio]):not(.tui-segmented_active):not(:active):hover{background-color:var(--tui-background-neutral-1-hover)}tui-segmented>a:not(.tui-segmented_active):not(:active):hover,tui-segmented>button:not(.tui-segmented_active):not(:active):hover,tui-segmented>label:not(.tui-segmented_active):not(:active):hover,tui-segmented>label>input:not([tuiRadio]):not(.tui-segmented_active):not(:active):hover,tui-segmented>a:not(.tui-segmented_active):not(:active):hover+*,tui-segmented>button:not(.tui-segmented_active):not(:active):hover+*,tui-segmented>label:not(.tui-segmented_active):not(:active):hover+*,tui-segmented>label>input:not([tuiRadio]):not(.tui-segmented_active):not(:active):hover+*{border-image:linear-gradient(var(--tui-border-normal),transparent) 1 / 0 0 25% .5 / 100 0 0 .5}}tui-segmented>a:active,tui-segmented>button:active,tui-segmented>label:active,tui-segmented>label>input:not([tuiRadio]):active,tui-segmented>a:active+*,tui-segmented>button:active+*,tui-segmented>label:active+*,tui-segmented>label>input:not([tuiRadio]):active+*,tui-segmented>a.tui-segmented_active,tui-segmented>button.tui-segmented_active,tui-segmented>label.tui-segmented_active,tui-segmented>label>input:not([tuiRadio]).tui-segmented_active,tui-segmented>a.tui-segmented_active+*,tui-segmented>button.tui-segmented_active+*,tui-segmented>label.tui-segmented_active+*,tui-segmented>label>input:not([tuiRadio]).tui-segmented_active+*{border-image:linear-gradient(var(--tui-border-normal),transparent) 1 / 0 0 25% .5 / 100 0 0 .5}tui-segmented>label>input:not([tuiRadio]){position:absolute;top:-.125rem;left:-.125rem;right:-.125rem;bottom:-.125rem;background:transparent!important}tui-segmented:before{transition-property:top,left,width;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;left:var(--t-left);top:var(--t-top);block-size:var(--t-height);inline-size:var(--t-width);border-radius:inherit;background:currentColor;background-clip:padding-box;border:.125rem solid transparent;box-sizing:border-box;filter:drop-shadow(0 .25rem 1.25rem rgba(0,0,0,.1))}[tuiTheme=dark] tui-segmented,tui-segmented[tuiTheme=dark]{--tui-background-base: var(--tui-background-neutral-1-hover)}\\n\"]\n    }]\n  }], null, {\n    size: [{\n      type: Input\n    }],\n    activeItemIndex: [{\n      type: Input\n    }],\n    activeItemIndexChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_SEGMENTED_OPTIONS, TuiSegmented, TuiSegmentedDirective, tuiSegmentedOptionsProvider };", "map": {"version": 3, "names": ["i0", "inject", "ElementRef", "Directive", "ContentChildren", "EventEmitter", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "Output", "takeUntilDestroyed", "ResizeObserverService", "tui<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tuiControlValue", "tuiZonefree", "tuiCreateOptions", "tuiInjectElement", "tuiIsHTMLElement", "tuiPx", "tuiBadgeNotificationOptionsProvider", "NgControl", "RadioControlValueAccessor", "RouterLinkActive", "EMPTY_QUERY", "switchMap", "map", "_c0", "TuiSegmentedDirective", "constructor", "controls", "radios", "links", "elements", "component", "TuiSegmented", "el", "ngAfterContentInit", "pipe", "first", "value", "toArray", "findIndex", "c", "subscribe", "index", "update", "ngAfterContentChecked", "length", "get", "linkIndex", "nativeElement", "target", "getIndex", "link", "isActive", "element", "Array", "from", "children", "tab", "contains", "ɵfac", "TuiSegmentedDirective_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "contentQueries", "TuiSegmentedDirective_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostBindings", "TuiSegmentedDirective_HostBindings", "ɵɵlistener", "TuiSegmentedDirective_click_HostBindingHandler", "$event", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "host", "descendants", "read", "TUI_SEGMENTED_OPTIONS", "tuiSegmentedOptionsProvider", "size", "sub", "self", "refresh", "activeItemIndex", "activeItemIndexChange", "ngOnChanges", "emit", "activeElement", "item", "for<PERSON>ach", "e", "classList", "remove", "add", "offsetWidth", "offsetLeft", "offsetTop", "style", "setProperty", "TuiSegmented_Factory", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostVars", "TuiSegmented_HostBindings", "ɵɵattribute", "inputs", "outputs", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "template", "TuiSegmented_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "selector", "None", "OnPush", "providers", "hostDirectives"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-segmented.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, ElementRef, Directive, ContentChildren, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { tuiQueryListChanges, tuiControlValue, tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { tuiInjectElement, tuiIsHTMLElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiPx } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiBadgeNotificationOptionsProvider } from '@taiga-ui/kit/components/badge-notification';\nimport { NgControl, RadioControlValueAccessor } from '@angular/forms';\nimport { RouterLinkActive } from '@angular/router';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { switchMap, map } from 'rxjs';\n\nclass TuiSegmentedDirective {\n    constructor() {\n        this.controls = EMPTY_QUERY;\n        this.radios = EMPTY_QUERY;\n        this.links = EMPTY_QUERY;\n        this.elements = EMPTY_QUERY;\n        this.component = inject(TuiSegmented);\n        this.el = tuiInjectElement();\n    }\n    ngAfterContentInit() {\n        tuiQueryListChanges(this.controls)\n            .pipe(switchMap(() => tuiControlValue(this.controls.first)), map((value) => this.radios.toArray().findIndex((c) => c.value === value)))\n            .subscribe((index) => {\n            this.component.update(index);\n        });\n    }\n    ngAfterContentChecked() {\n        if (this.links.length) {\n            this.update(this.elements.get(this.linkIndex)?.nativeElement || null);\n        }\n    }\n    update(target) {\n        this.component.update(this.getIndex(target));\n    }\n    get linkIndex() {\n        return this.links.toArray().findIndex((link) => link.isActive);\n    }\n    getIndex(element) {\n        return Array.from(this.el.children).findIndex((tab) => tab.contains(element));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSegmentedDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSegmentedDirective, isStandalone: true, host: { listeners: { \"click\": \"update($event.target)\" } }, queries: [{ propertyName: \"controls\", predicate: NgControl, descendants: true }, { propertyName: \"radios\", predicate: RadioControlValueAccessor, descendants: true }, { propertyName: \"links\", predicate: RouterLinkActive }, { propertyName: \"elements\", predicate: RouterLinkActive, read: ElementRef }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSegmentedDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    host: {\n                        '(click)': 'update($event.target)',\n                    },\n                }]\n        }], propDecorators: { controls: [{\n                type: ContentChildren,\n                args: [NgControl, { descendants: true }]\n            }], radios: [{\n                type: ContentChildren,\n                args: [RadioControlValueAccessor, { descendants: true }]\n            }], links: [{\n                type: ContentChildren,\n                args: [RouterLinkActive]\n            }], elements: [{\n                type: ContentChildren,\n                args: [RouterLinkActive, { read: ElementRef }]\n            }] } });\n\nconst [TUI_SEGMENTED_OPTIONS, tuiSegmentedOptionsProvider] = tuiCreateOptions({\n    size: 's',\n});\nclass TuiSegmented {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.sub = inject(ResizeObserverService, { self: true })\n            .pipe(tuiZonefree(), takeUntilDestroyed())\n            .subscribe(() => this.refresh());\n        this.size = inject(TUI_SEGMENTED_OPTIONS).size;\n        this.activeItemIndex = 0;\n        this.activeItemIndexChange = new EventEmitter();\n    }\n    ngOnChanges() {\n        this.refresh();\n    }\n    update(activeItemIndex) {\n        if (activeItemIndex === this.activeItemIndex || activeItemIndex < 0) {\n            return;\n        }\n        this.activeItemIndex = activeItemIndex;\n        this.activeItemIndexChange.emit(activeItemIndex);\n        this.refresh();\n    }\n    get activeElement() {\n        return this.el.children.item(this.activeItemIndex);\n    }\n    refresh() {\n        const el = this.activeElement;\n        if (!tuiIsHTMLElement(el)) {\n            return;\n        }\n        Array.from(this.el.children).forEach((e) => e.classList.remove('tui-segmented_active'));\n        el.classList.add('tui-segmented_active');\n        const { offsetWidth = 0, offsetLeft = 0, offsetTop = 0 } = el;\n        this.el.style.setProperty('--t-top', tuiPx(offsetTop));\n        this.el.style.setProperty('--t-left', tuiPx(offsetLeft));\n        this.el.style.setProperty('--t-width', tuiPx(offsetWidth));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSegmented, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSegmented, isStandalone: true, selector: \"tui-segmented\", inputs: { size: \"size\", activeItemIndex: \"activeItemIndex\" }, outputs: { activeItemIndexChange: \"activeItemIndexChange\" }, host: { properties: { \"attr.data-size\": \"size\" } }, providers: [ResizeObserverService, tuiBadgeNotificationOptionsProvider({ size: 's' })], usesOnChanges: true, hostDirectives: [{ directive: TuiSegmentedDirective }], ngImport: i0, template: '<ng-content />', isInline: true, styles: [\"tui-segmented{position:relative;display:flex;color:var(--tui-background-base);background:var(--tui-background-neutral-1);overflow:hidden;-webkit-mask-image:linear-gradient(black,black);mask-image:linear-gradient(#000,#000)}tui-segmented[data-size=s]{--t-padding: .5rem;--t-gap: .5rem;--t-margin: -.125rem;--t-height: var(--tui-height-s);font:var(--tui-font-text-s);border-radius:var(--tui-radius-m)}tui-segmented[data-size=s] tui-icon{font-size:1rem}tui-segmented[data-size=m]{--t-padding: .875rem;--t-gap: 1rem;--t-margin: -.375rem;--t-height: var(--tui-height-m);font:var(--tui-font-text-m);border-radius:var(--tui-radius-m)}tui-segmented[data-size=l]{--t-padding: 1.25rem;--t-gap: 1rem;--t-margin: -.375rem;--t-height: var(--tui-height-l);font:var(--tui-font-text-l);border-radius:var(--tui-radius-l)}tui-segmented[data-size=l]>*:before{block-size:1.25rem}tui-segmented>a,tui-segmented>button,tui-segmented>label,tui-segmented>label>input:not([tuiRadio]){transition-property:color,background,opacity,border-image;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;line-height:inherit;text-decoration:none;position:relative;display:flex;align-items:center;justify-content:center;block-size:var(--t-height);white-space:nowrap;gap:var(--t-gap);margin:0;padding:0 var(--t-padding);color:var(--tui-text-primary);overflow:hidden;cursor:pointer;font:inherit;border-radius:inherit;border:.125rem solid transparent;background-clip:padding-box;box-sizing:border-box;border-image:linear-gradient(var(--tui-border-normal),transparent) 1 / 0 0 25% .5 / 0 0 100 .5;clip-path:polygon(-1rem calc(50% - .5rem),1px calc(50% - .5rem),1px 0,100% 0,100% 100%,1px 100%,1px calc(50% + .5rem),-1rem calc(50% + .5rem))}tui-segmented>a>*,tui-segmented>button>*,tui-segmented>label>*,tui-segmented>label>input:not([tuiRadio])>*{margin:0 var(--t-margin)}tui-segmented>a:focus-visible,tui-segmented>button:focus-visible,tui-segmented>label:focus-visible,tui-segmented>label>input:not([tuiRadio]):focus-visible{outline:.125rem solid var(--tui-border-focus);outline-offset:-.25rem}@media (hover: hover) and (pointer: fine){tui-segmented>a:not(.tui-segmented_active):not(:active):hover,tui-segmented>button:not(.tui-segmented_active):not(:active):hover,tui-segmented>label:not(.tui-segmented_active):not(:active):hover,tui-segmented>label>input:not([tuiRadio]):not(.tui-segmented_active):not(:active):hover{background-color:var(--tui-background-neutral-1-hover)}tui-segmented>a:not(.tui-segmented_active):not(:active):hover,tui-segmented>button:not(.tui-segmented_active):not(:active):hover,tui-segmented>label:not(.tui-segmented_active):not(:active):hover,tui-segmented>label>input:not([tuiRadio]):not(.tui-segmented_active):not(:active):hover,tui-segmented>a:not(.tui-segmented_active):not(:active):hover+*,tui-segmented>button:not(.tui-segmented_active):not(:active):hover+*,tui-segmented>label:not(.tui-segmented_active):not(:active):hover+*,tui-segmented>label>input:not([tuiRadio]):not(.tui-segmented_active):not(:active):hover+*{border-image:linear-gradient(var(--tui-border-normal),transparent) 1 / 0 0 25% .5 / 100 0 0 .5}}tui-segmented>a:active,tui-segmented>button:active,tui-segmented>label:active,tui-segmented>label>input:not([tuiRadio]):active,tui-segmented>a:active+*,tui-segmented>button:active+*,tui-segmented>label:active+*,tui-segmented>label>input:not([tuiRadio]):active+*,tui-segmented>a.tui-segmented_active,tui-segmented>button.tui-segmented_active,tui-segmented>label.tui-segmented_active,tui-segmented>label>input:not([tuiRadio]).tui-segmented_active,tui-segmented>a.tui-segmented_active+*,tui-segmented>button.tui-segmented_active+*,tui-segmented>label.tui-segmented_active+*,tui-segmented>label>input:not([tuiRadio]).tui-segmented_active+*{border-image:linear-gradient(var(--tui-border-normal),transparent) 1 / 0 0 25% .5 / 100 0 0 .5}tui-segmented>label>input:not([tuiRadio]){position:absolute;top:-.125rem;left:-.125rem;right:-.125rem;bottom:-.125rem;background:transparent!important}tui-segmented:before{transition-property:top,left,width;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;left:var(--t-left);top:var(--t-top);block-size:var(--t-height);inline-size:var(--t-width);border-radius:inherit;background:currentColor;background-clip:padding-box;border:.125rem solid transparent;box-sizing:border-box;filter:drop-shadow(0 .25rem 1.25rem rgba(0,0,0,.1))}[tuiTheme=dark] tui-segmented,tui-segmented[tuiTheme=dark]{--tui-background-base: var(--tui-background-neutral-1-hover)}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSegmented, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-segmented', template: '<ng-content />', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [ResizeObserverService, tuiBadgeNotificationOptionsProvider({ size: 's' })], hostDirectives: [TuiSegmentedDirective], host: {\n                        '[attr.data-size]': 'size',\n                    }, styles: [\"tui-segmented{position:relative;display:flex;color:var(--tui-background-base);background:var(--tui-background-neutral-1);overflow:hidden;-webkit-mask-image:linear-gradient(black,black);mask-image:linear-gradient(#000,#000)}tui-segmented[data-size=s]{--t-padding: .5rem;--t-gap: .5rem;--t-margin: -.125rem;--t-height: var(--tui-height-s);font:var(--tui-font-text-s);border-radius:var(--tui-radius-m)}tui-segmented[data-size=s] tui-icon{font-size:1rem}tui-segmented[data-size=m]{--t-padding: .875rem;--t-gap: 1rem;--t-margin: -.375rem;--t-height: var(--tui-height-m);font:var(--tui-font-text-m);border-radius:var(--tui-radius-m)}tui-segmented[data-size=l]{--t-padding: 1.25rem;--t-gap: 1rem;--t-margin: -.375rem;--t-height: var(--tui-height-l);font:var(--tui-font-text-l);border-radius:var(--tui-radius-l)}tui-segmented[data-size=l]>*:before{block-size:1.25rem}tui-segmented>a,tui-segmented>button,tui-segmented>label,tui-segmented>label>input:not([tuiRadio]){transition-property:color,background,opacity,border-image;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;line-height:inherit;text-decoration:none;position:relative;display:flex;align-items:center;justify-content:center;block-size:var(--t-height);white-space:nowrap;gap:var(--t-gap);margin:0;padding:0 var(--t-padding);color:var(--tui-text-primary);overflow:hidden;cursor:pointer;font:inherit;border-radius:inherit;border:.125rem solid transparent;background-clip:padding-box;box-sizing:border-box;border-image:linear-gradient(var(--tui-border-normal),transparent) 1 / 0 0 25% .5 / 0 0 100 .5;clip-path:polygon(-1rem calc(50% - .5rem),1px calc(50% - .5rem),1px 0,100% 0,100% 100%,1px 100%,1px calc(50% + .5rem),-1rem calc(50% + .5rem))}tui-segmented>a>*,tui-segmented>button>*,tui-segmented>label>*,tui-segmented>label>input:not([tuiRadio])>*{margin:0 var(--t-margin)}tui-segmented>a:focus-visible,tui-segmented>button:focus-visible,tui-segmented>label:focus-visible,tui-segmented>label>input:not([tuiRadio]):focus-visible{outline:.125rem solid var(--tui-border-focus);outline-offset:-.25rem}@media (hover: hover) and (pointer: fine){tui-segmented>a:not(.tui-segmented_active):not(:active):hover,tui-segmented>button:not(.tui-segmented_active):not(:active):hover,tui-segmented>label:not(.tui-segmented_active):not(:active):hover,tui-segmented>label>input:not([tuiRadio]):not(.tui-segmented_active):not(:active):hover{background-color:var(--tui-background-neutral-1-hover)}tui-segmented>a:not(.tui-segmented_active):not(:active):hover,tui-segmented>button:not(.tui-segmented_active):not(:active):hover,tui-segmented>label:not(.tui-segmented_active):not(:active):hover,tui-segmented>label>input:not([tuiRadio]):not(.tui-segmented_active):not(:active):hover,tui-segmented>a:not(.tui-segmented_active):not(:active):hover+*,tui-segmented>button:not(.tui-segmented_active):not(:active):hover+*,tui-segmented>label:not(.tui-segmented_active):not(:active):hover+*,tui-segmented>label>input:not([tuiRadio]):not(.tui-segmented_active):not(:active):hover+*{border-image:linear-gradient(var(--tui-border-normal),transparent) 1 / 0 0 25% .5 / 100 0 0 .5}}tui-segmented>a:active,tui-segmented>button:active,tui-segmented>label:active,tui-segmented>label>input:not([tuiRadio]):active,tui-segmented>a:active+*,tui-segmented>button:active+*,tui-segmented>label:active+*,tui-segmented>label>input:not([tuiRadio]):active+*,tui-segmented>a.tui-segmented_active,tui-segmented>button.tui-segmented_active,tui-segmented>label.tui-segmented_active,tui-segmented>label>input:not([tuiRadio]).tui-segmented_active,tui-segmented>a.tui-segmented_active+*,tui-segmented>button.tui-segmented_active+*,tui-segmented>label.tui-segmented_active+*,tui-segmented>label>input:not([tuiRadio]).tui-segmented_active+*{border-image:linear-gradient(var(--tui-border-normal),transparent) 1 / 0 0 25% .5 / 100 0 0 .5}tui-segmented>label>input:not([tuiRadio]){position:absolute;top:-.125rem;left:-.125rem;right:-.125rem;bottom:-.125rem;background:transparent!important}tui-segmented:before{transition-property:top,left,width;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;content:\\\"\\\";position:absolute;left:var(--t-left);top:var(--t-top);block-size:var(--t-height);inline-size:var(--t-width);border-radius:inherit;background:currentColor;background-clip:padding-box;border:.125rem solid transparent;box-sizing:border-box;filter:drop-shadow(0 .25rem 1.25rem rgba(0,0,0,.1))}[tuiTheme=dark] tui-segmented,tui-segmented[tuiTheme=dark]{--tui-background-base: var(--tui-background-neutral-1-hover)}\\n\"] }]\n        }], propDecorators: { size: [{\n                type: Input\n            }], activeItemIndex: [{\n                type: Input\n            }], activeItemIndexChange: [{\n                type: Output\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_SEGMENTED_OPTIONS, TuiSegmented, TuiSegmentedDirective, tuiSegmentedOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,eAAe,EAAEC,YAAY,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAClK,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,mBAAmB,EAAEC,eAAe,EAAEC,WAAW,QAAQ,2BAA2B;AAC7F,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,yBAAyB;AAC5E,SAASC,KAAK,QAAQ,mCAAmC;AACzD,SAASC,mCAAmC,QAAQ,6CAA6C;AACjG,SAASC,SAAS,EAAEC,yBAAyB,QAAQ,gBAAgB;AACrE,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AAAC,MAAAC,GAAA;AAEtC,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAGN,WAAW;IAC3B,IAAI,CAACO,MAAM,GAAGP,WAAW;IACzB,IAAI,CAACQ,KAAK,GAAGR,WAAW;IACxB,IAAI,CAACS,QAAQ,GAAGT,WAAW;IAC3B,IAAI,CAACU,SAAS,GAAGjC,MAAM,CAACkC,YAAY,CAAC;IACrC,IAAI,CAACC,EAAE,GAAGnB,gBAAgB,CAAC,CAAC;EAChC;EACAoB,kBAAkBA,CAAA,EAAG;IACjBxB,mBAAmB,CAAC,IAAI,CAACiB,QAAQ,CAAC,CAC7BQ,IAAI,CAACb,SAAS,CAAC,MAAMX,eAAe,CAAC,IAAI,CAACgB,QAAQ,CAACS,KAAK,CAAC,CAAC,EAAEb,GAAG,CAAEc,KAAK,IAAK,IAAI,CAACT,MAAM,CAACU,OAAO,CAAC,CAAC,CAACC,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACH,KAAK,KAAKA,KAAK,CAAC,CAAC,CAAC,CACtII,SAAS,CAAEC,KAAK,IAAK;MACtB,IAAI,CAACX,SAAS,CAACY,MAAM,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC;EACN;EACAE,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACf,KAAK,CAACgB,MAAM,EAAE;MACnB,IAAI,CAACF,MAAM,CAAC,IAAI,CAACb,QAAQ,CAACgB,GAAG,CAAC,IAAI,CAACC,SAAS,CAAC,EAAEC,aAAa,IAAI,IAAI,CAAC;IACzE;EACJ;EACAL,MAAMA,CAACM,MAAM,EAAE;IACX,IAAI,CAAClB,SAAS,CAACY,MAAM,CAAC,IAAI,CAACO,QAAQ,CAACD,MAAM,CAAC,CAAC;EAChD;EACA,IAAIF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAClB,KAAK,CAACS,OAAO,CAAC,CAAC,CAACC,SAAS,CAAEY,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAAC;EAClE;EACAF,QAAQA,CAACG,OAAO,EAAE;IACd,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtB,EAAE,CAACuB,QAAQ,CAAC,CAACjB,SAAS,CAAEkB,GAAG,IAAKA,GAAG,CAACC,QAAQ,CAACL,OAAO,CAAC,CAAC;EACjF;EACA;IAAS,IAAI,CAACM,IAAI,YAAAC,8BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFpC,qBAAqB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAACqC,IAAI,kBAD+EjE,EAAE,CAAAkE,iBAAA;MAAAC,IAAA,EACJvC,qBAAqB;MAAAwC,cAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,IAAAF,EAAA;UADnBtE,EAAE,CAAAyE,cAAA,CAAAD,QAAA,EACmJnD,SAAS;UAD9JrB,EAAE,CAAAyE,cAAA,CAAAD,QAAA,EACwNlD,yBAAyB;UADnPtB,EAAE,CAAAyE,cAAA,CAAAD,QAAA,EAC4SjD,gBAAgB;UAD9TvB,EAAE,CAAAyE,cAAA,CAAAD,QAAA,EACuWjD,gBAAgB,KAAQrB,UAAU;QAAA;QAAA,IAAAoE,EAAA;UAAA,IAAAI,EAAA;UAD3Y1E,EAAE,CAAA2E,cAAA,CAAAD,EAAA,GAAF1E,EAAE,CAAA4E,WAAA,QAAAL,GAAA,CAAAzC,QAAA,GAAA4C,EAAA;UAAF1E,EAAE,CAAA2E,cAAA,CAAAD,EAAA,GAAF1E,EAAE,CAAA4E,WAAA,QAAAL,GAAA,CAAAxC,MAAA,GAAA2C,EAAA;UAAF1E,EAAE,CAAA2E,cAAA,CAAAD,EAAA,GAAF1E,EAAE,CAAA4E,WAAA,QAAAL,GAAA,CAAAvC,KAAA,GAAA0C,EAAA;UAAF1E,EAAE,CAAA2E,cAAA,CAAAD,EAAA,GAAF1E,EAAE,CAAA4E,WAAA,QAAAL,GAAA,CAAAtC,QAAA,GAAAyC,EAAA;QAAA;MAAA;MAAAG,YAAA,WAAAC,mCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFtE,EAAE,CAAA+E,UAAA,mBAAAC,+CAAAC,MAAA;YAAA,OACJV,GAAA,CAAAzB,MAAA,CAAAmC,MAAA,CAAA7B,MAAoB,CAAC;UAAA,CAAD,CAAC;QAAA;MAAA;MAAA8B,UAAA;IAAA,EAA4Y;EAAE;AACtgB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGnF,EAAE,CAAAoF,iBAAA,CAGXxD,qBAAqB,EAAc,CAAC;IACpHuC,IAAI,EAAEhE,SAAS;IACfkF,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,IAAI,EAAE;QACF,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAExD,QAAQ,EAAE,CAAC;MACzBqC,IAAI,EAAE/D,eAAe;MACrBiF,IAAI,EAAE,CAAChE,SAAS,EAAE;QAAEkE,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAExD,MAAM,EAAE,CAAC;MACToC,IAAI,EAAE/D,eAAe;MACrBiF,IAAI,EAAE,CAAC/D,yBAAyB,EAAE;QAAEiE,WAAW,EAAE;MAAK,CAAC;IAC3D,CAAC,CAAC;IAAEvD,KAAK,EAAE,CAAC;MACRmC,IAAI,EAAE/D,eAAe;MACrBiF,IAAI,EAAE,CAAC9D,gBAAgB;IAC3B,CAAC,CAAC;IAAEU,QAAQ,EAAE,CAAC;MACXkC,IAAI,EAAE/D,eAAe;MACrBiF,IAAI,EAAE,CAAC9D,gBAAgB,EAAE;QAAEiE,IAAI,EAAEtF;MAAW,CAAC;IACjD,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM,CAACuF,qBAAqB,EAAEC,2BAA2B,CAAC,GAAG1E,gBAAgB,CAAC;EAC1E2E,IAAI,EAAE;AACV,CAAC,CAAC;AACF,MAAMxD,YAAY,CAAC;EACfN,WAAWA,CAAA,EAAG;IACV,IAAI,CAACO,EAAE,GAAGnB,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAAC2E,GAAG,GAAG3F,MAAM,CAACW,qBAAqB,EAAE;MAAEiF,IAAI,EAAE;IAAK,CAAC,CAAC,CACnDvD,IAAI,CAACvB,WAAW,CAAC,CAAC,EAAEJ,kBAAkB,CAAC,CAAC,CAAC,CACzCiC,SAAS,CAAC,MAAM,IAAI,CAACkD,OAAO,CAAC,CAAC,CAAC;IACpC,IAAI,CAACH,IAAI,GAAG1F,MAAM,CAACwF,qBAAqB,CAAC,CAACE,IAAI;IAC9C,IAAI,CAACI,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,qBAAqB,GAAG,IAAI3F,YAAY,CAAC,CAAC;EACnD;EACA4F,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,OAAO,CAAC,CAAC;EAClB;EACAhD,MAAMA,CAACiD,eAAe,EAAE;IACpB,IAAIA,eAAe,KAAK,IAAI,CAACA,eAAe,IAAIA,eAAe,GAAG,CAAC,EAAE;MACjE;IACJ;IACA,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,qBAAqB,CAACE,IAAI,CAACH,eAAe,CAAC;IAChD,IAAI,CAACD,OAAO,CAAC,CAAC;EAClB;EACA,IAAIK,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC/D,EAAE,CAACuB,QAAQ,CAACyC,IAAI,CAAC,IAAI,CAACL,eAAe,CAAC;EACtD;EACAD,OAAOA,CAAA,EAAG;IACN,MAAM1D,EAAE,GAAG,IAAI,CAAC+D,aAAa;IAC7B,IAAI,CAACjF,gBAAgB,CAACkB,EAAE,CAAC,EAAE;MACvB;IACJ;IACAqB,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtB,EAAE,CAACuB,QAAQ,CAAC,CAAC0C,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,SAAS,CAACC,MAAM,CAAC,sBAAsB,CAAC,CAAC;IACvFpE,EAAE,CAACmE,SAAS,CAACE,GAAG,CAAC,sBAAsB,CAAC;IACxC,MAAM;MAAEC,WAAW,GAAG,CAAC;MAAEC,UAAU,GAAG,CAAC;MAAEC,SAAS,GAAG;IAAE,CAAC,GAAGxE,EAAE;IAC7D,IAAI,CAACA,EAAE,CAACyE,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE3F,KAAK,CAACyF,SAAS,CAAC,CAAC;IACtD,IAAI,CAACxE,EAAE,CAACyE,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE3F,KAAK,CAACwF,UAAU,CAAC,CAAC;IACxD,IAAI,CAACvE,EAAE,CAACyE,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE3F,KAAK,CAACuF,WAAW,CAAC,CAAC;EAC9D;EACA;IAAS,IAAI,CAAC5C,IAAI,YAAAiD,qBAAA/C,CAAA;MAAA,YAAAA,CAAA,IAAyF7B,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAAC6E,IAAI,kBAjE+EhH,EAAE,CAAAiH,iBAAA;MAAA9C,IAAA,EAiEJhC,YAAY;MAAA+E,SAAA;MAAAC,QAAA;MAAAtC,YAAA,WAAAuC,0BAAA9C,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjEVtE,EAAE,CAAAqH,WAAA,cAAA9C,GAAA,CAAAoB,IAAA;QAAA;MAAA;MAAA2B,MAAA;QAAA3B,IAAA;QAAAI,eAAA;MAAA;MAAAwB,OAAA;QAAAvB,qBAAA;MAAA;MAAAd,UAAA;MAAAsC,QAAA,GAAFxH,EAAE,CAAAyH,kBAAA,CAiEmP,CAAC7G,qBAAqB,EAAEQ,mCAAmC,CAAC;QAAEuE,IAAI,EAAE;MAAI,CAAC,CAAC,CAAC,GAjEhU3F,EAAE,CAAA0H,uBAAA,EAiEmX9F,qBAAqB,IAjE1Y5B,EAAE,CAAA2H,oBAAA,EAAF3H,EAAE,CAAA4H,mBAAA;MAAAC,kBAAA,EAAAlG,GAAA;MAAAmG,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAA3D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFtE,EAAE,CAAAkI,eAAA;UAAFlI,EAAE,CAAAmI,YAAA,EAiEmb,CAAC;QAAA;MAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAirJ;EAAE;AAC9sK;AACA;EAAA,QAAAnD,SAAA,oBAAAA,SAAA,KAnEqGnF,EAAE,CAAAoF,iBAAA,CAmEXjD,YAAY,EAAc,CAAC;IAC3GgC,IAAI,EAAE7D,SAAS;IACf+E,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,IAAI;MAAEqD,QAAQ,EAAE,eAAe;MAAEP,QAAQ,EAAE,gBAAgB;MAAEK,aAAa,EAAE9H,iBAAiB,CAACiI,IAAI;MAAEF,eAAe,EAAE9H,uBAAuB,CAACiI,MAAM;MAAEC,SAAS,EAAE,CAAC9H,qBAAqB,EAAEQ,mCAAmC,CAAC;QAAEuE,IAAI,EAAE;MAAI,CAAC,CAAC,CAAC;MAAEgD,cAAc,EAAE,CAAC/G,qBAAqB,CAAC;MAAE0D,IAAI,EAAE;QACrS,kBAAkB,EAAE;MACxB,CAAC;MAAE8C,MAAM,EAAE,CAAC,mjJAAmjJ;IAAE,CAAC;EAC9kJ,CAAC,CAAC,QAAkB;IAAEzC,IAAI,EAAE,CAAC;MACrBxB,IAAI,EAAE1D;IACV,CAAC,CAAC;IAAEsF,eAAe,EAAE,CAAC;MAClB5B,IAAI,EAAE1D;IACV,CAAC,CAAC;IAAEuF,qBAAqB,EAAE,CAAC;MACxB7B,IAAI,EAAEzD;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAAS+E,qBAAqB,EAAEtD,YAAY,EAAEP,qBAAqB,EAAE8D,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}