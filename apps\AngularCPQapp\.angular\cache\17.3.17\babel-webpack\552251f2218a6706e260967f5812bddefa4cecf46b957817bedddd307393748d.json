{"ast": null, "code": "/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Clamps a value between two inclusive limits\n *\n * @param value\n * @param min lower limit\n * @param max upper limit\n */\nfunction tuiClamp(value, min, max) {\n  ngDevMode && console.assert(!Number.isNaN(value));\n  ngDevMode && console.assert(!Number.isNaN(min));\n  ngDevMode && console.assert(!Number.isNaN(max));\n  ngDevMode && console.assert(max >= min);\n  return Math.min(max, Math.max(min, value));\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nfunction tuiInRange(value, fromInclude, toExclude) {\n  ngDevMode && console.assert(!Number.isNaN(value));\n  ngDevMode && console.assert(!Number.isNaN(fromInclude));\n  ngDevMode && console.assert(!Number.isNaN(toExclude));\n  ngDevMode && console.assert(fromInclude < toExclude);\n  return value >= fromInclude && value < toExclude;\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Normalizes any number to an integer within inclusive range\n *\n * @param value\n * @param min lower inclusive integer\n * @param max upper inclusive integer\n * @return an integer between min and max inclusive\n */\nfunction tuiNormalizeToIntNumber(value, min, max) {\n  ngDevMode && console.assert(Number.isInteger(min));\n  ngDevMode && console.assert(Number.isInteger(max));\n  ngDevMode && console.assert(min <= max);\n  if (Number.isNaN(value) || value <= min) {\n    return min;\n  }\n  if (value >= max) {\n    return max;\n  }\n  return Math.round(value);\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Rounds a number to the closest value in a fixed discrete series\n *\n * @param value\n * @param quantum series step\n */\nfunction tuiQuantize(value, quantum) {\n  ngDevMode && console.assert(Number.isFinite(value));\n  ngDevMode && console.assert(Number.isFinite(quantum));\n  ngDevMode && console.assert(quantum > 0);\n  const remainder = value % quantum;\n  return remainder < quantum / 2 ? value - remainder : value + quantum - remainder;\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nconst MAX_PRECISION = 292;\n/**\n * Rounding number to the set precision\n *\n * @param value\n * @param precision number of digits in a float part\n * @param func rounding function (round, floor, ceil)\n */\nfunction calculate(value, precision, func) {\n  if (value === Infinity) {\n    return value;\n  }\n  ngDevMode && console.assert(!Number.isNaN(value), 'Value must be number');\n  ngDevMode && console.assert(Number.isInteger(precision), 'Precision must be integer');\n  precision = Math.min(precision, MAX_PRECISION);\n  const [significand, exponent = ''] = `${value}`.split('e');\n  const roundedInt = func(Number(`${significand}e${Number(exponent) + precision}`));\n  /**\n   * TODO: use BigInt after bumping Safari to 14+\n   */\n  ngDevMode && console.assert(Number.isSafeInteger(roundedInt), 'Impossible to correctly round such a large number');\n  const processedPair = `${roundedInt}e`.split('e');\n  return Number(`${processedPair[0]}e${Number(processedPair[1]) - precision}`);\n}\nfunction tuiRound(value, precision = 0) {\n  return calculate(value, precision, Math.round);\n}\nfunction tuiCeil(value, precision = 0) {\n  return calculate(value, precision, Math.ceil);\n}\nfunction tuiFloor(value, precision = 0) {\n  return calculate(value, precision, Math.floor);\n}\nfunction tuiTrunc(value, precision = 0) {\n  return calculate(value, precision, Math.trunc);\n}\nfunction tuiIsSafeToRound(value, precision = 0) {\n  return Number.isSafeInteger(Math.trunc(value * 10 ** precision));\n}\nfunction tuiRoundWith({\n  value,\n  precision,\n  method\n}) {\n  switch (method) {\n    case 'ceil':\n      return tuiCeil(value, precision);\n    case 'floor':\n      return tuiFloor(value, precision);\n    case 'round':\n      return tuiRound(value, precision);\n    default:\n      return tuiTrunc(value, precision);\n  }\n}\n\n/**\n * Calculates sum of any number of passed arguments\n */\nfunction tuiSum(...args) {\n  return args.reduce((a, b) => a + b, 0);\n}\nfunction tuiToInt(bool) {\n  return bool ? 1 : 0;\n}\nfunction tuiToInteger(value) {\n  return parseInt(value, 10);\n}\n\n/**\n * Converts angle in degrees to radians\n */\nfunction tuiToRadians(deg) {\n  return deg * Math.PI / 180;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiCeil, tuiClamp, tuiFloor, tuiInRange, tuiIsSafeToRound, tuiNormalizeToIntNumber, tuiQuantize, tuiRound, tuiRoundWith, tuiSum, tuiToInt, tuiToInteger, tuiToRadians, tuiTrunc };", "map": {"version": 3, "names": ["tui<PERSON><PERSON>", "value", "min", "max", "ngDevMode", "console", "assert", "Number", "isNaN", "Math", "tuiInRange", "fromInclude", "toExclude", "tuiNormalizeToIntNumber", "isInteger", "round", "tuiQuantize", "quantum", "isFinite", "remainder", "MAX_PRECISION", "calculate", "precision", "func", "Infinity", "significand", "exponent", "split", "roundedInt", "isSafeInteger", "processedPair", "tuiRound", "<PERSON>ui<PERSON><PERSON>", "ceil", "tuiFloor", "floor", "tui<PERSON><PERSON><PERSON>", "trunc", "tuiIsSafeToRound", "tuiRoundWith", "method", "tuiSum", "args", "reduce", "a", "b", "tuiToInt", "bool", "tuiToInteger", "parseInt", "tuiToRadians", "deg", "PI"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-utils-math.mjs"], "sourcesContent": ["/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Clamps a value between two inclusive limits\n *\n * @param value\n * @param min lower limit\n * @param max upper limit\n */\nfunction tuiClamp(value, min, max) {\n    ngDevMode && console.assert(!Number.isNaN(value));\n    ngDevMode && console.assert(!Number.isNaN(min));\n    ngDevMode && console.assert(!Number.isNaN(max));\n    ngDevMode && console.assert(max >= min);\n    return Math.min(max, Math.max(min, value));\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nfunction tuiInRange(value, fromInclude, toExclude) {\n    ngDevMode && console.assert(!Number.isNaN(value));\n    ngDevMode && console.assert(!Number.isNaN(fromInclude));\n    ngDevMode && console.assert(!Number.isNaN(toExclude));\n    ngDevMode && console.assert(fromInclude < toExclude);\n    return value >= fromInclude && value < toExclude;\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Normalizes any number to an integer within inclusive range\n *\n * @param value\n * @param min lower inclusive integer\n * @param max upper inclusive integer\n * @return an integer between min and max inclusive\n */\nfunction tuiNormalizeToIntNumber(value, min, max) {\n    ngDevMode && console.assert(Number.isInteger(min));\n    ngDevMode && console.assert(Number.isInteger(max));\n    ngDevMode && console.assert(min <= max);\n    if (Number.isNaN(value) || value <= min) {\n        return min;\n    }\n    if (value >= max) {\n        return max;\n    }\n    return Math.round(value);\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Rounds a number to the closest value in a fixed discrete series\n *\n * @param value\n * @param quantum series step\n */\nfunction tuiQuantize(value, quantum) {\n    ngDevMode && console.assert(Number.isFinite(value));\n    ngDevMode && console.assert(Number.isFinite(quantum));\n    ngDevMode && console.assert(quantum > 0);\n    const remainder = value % quantum;\n    return remainder < quantum / 2 ? value - remainder : value + quantum - remainder;\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nconst MAX_PRECISION = 292;\n/**\n * Rounding number to the set precision\n *\n * @param value\n * @param precision number of digits in a float part\n * @param func rounding function (round, floor, ceil)\n */\nfunction calculate(value, precision, func) {\n    if (value === Infinity) {\n        return value;\n    }\n    ngDevMode && console.assert(!Number.isNaN(value), 'Value must be number');\n    ngDevMode && console.assert(Number.isInteger(precision), 'Precision must be integer');\n    precision = Math.min(precision, MAX_PRECISION);\n    const [significand, exponent = ''] = `${value}`.split('e');\n    const roundedInt = func(Number(`${significand}e${Number(exponent) + precision}`));\n    /**\n     * TODO: use BigInt after bumping Safari to 14+\n     */\n    ngDevMode &&\n        console.assert(Number.isSafeInteger(roundedInt), 'Impossible to correctly round such a large number');\n    const processedPair = `${roundedInt}e`.split('e');\n    return Number(`${processedPair[0]}e${Number(processedPair[1]) - precision}`);\n}\nfunction tuiRound(value, precision = 0) {\n    return calculate(value, precision, Math.round);\n}\nfunction tuiCeil(value, precision = 0) {\n    return calculate(value, precision, Math.ceil);\n}\nfunction tuiFloor(value, precision = 0) {\n    return calculate(value, precision, Math.floor);\n}\nfunction tuiTrunc(value, precision = 0) {\n    return calculate(value, precision, Math.trunc);\n}\nfunction tuiIsSafeToRound(value, precision = 0) {\n    return Number.isSafeInteger(Math.trunc(value * 10 ** precision));\n}\n\nfunction tuiRoundWith({ value, precision, method, }) {\n    switch (method) {\n        case 'ceil':\n            return tuiCeil(value, precision);\n        case 'floor':\n            return tuiFloor(value, precision);\n        case 'round':\n            return tuiRound(value, precision);\n        default:\n            return tuiTrunc(value, precision);\n    }\n}\n\n/**\n * Calculates sum of any number of passed arguments\n */\nfunction tuiSum(...args) {\n    return args.reduce((a, b) => a + b, 0);\n}\n\nfunction tuiToInt(bool) {\n    return bool ? 1 : 0;\n}\n\nfunction tuiToInteger(value) {\n    return parseInt(value, 10);\n}\n\n/**\n * Converts angle in degrees to radians\n */\nfunction tuiToRadians(deg) {\n    return (deg * Math.PI) / 180;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiCeil, tuiClamp, tuiFloor, tuiInRange, tuiIsSafeToRound, tuiNormalizeToIntNumber, tuiQuantize, tuiRound, tuiRoundWith, tuiSum, tuiToInt, tuiToInteger, tuiToRadians, tuiTrunc };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC/BC,SAAS,IAAIC,OAAO,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,KAAK,CAACP,KAAK,CAAC,CAAC;EACjDG,SAAS,IAAIC,OAAO,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,KAAK,CAACN,GAAG,CAAC,CAAC;EAC/CE,SAAS,IAAIC,OAAO,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,KAAK,CAACL,GAAG,CAAC,CAAC;EAC/CC,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACH,GAAG,IAAID,GAAG,CAAC;EACvC,OAAOO,IAAI,CAACP,GAAG,CAACC,GAAG,EAAEM,IAAI,CAACN,GAAG,CAACD,GAAG,EAAED,KAAK,CAAC,CAAC;AAC9C;;AAEA;AACA,SAASS,UAAUA,CAACT,KAAK,EAAEU,WAAW,EAAEC,SAAS,EAAE;EAC/CR,SAAS,IAAIC,OAAO,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,KAAK,CAACP,KAAK,CAAC,CAAC;EACjDG,SAAS,IAAIC,OAAO,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,KAAK,CAACG,WAAW,CAAC,CAAC;EACvDP,SAAS,IAAIC,OAAO,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,KAAK,CAACI,SAAS,CAAC,CAAC;EACrDR,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACK,WAAW,GAAGC,SAAS,CAAC;EACpD,OAAOX,KAAK,IAAIU,WAAW,IAAIV,KAAK,GAAGW,SAAS;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,uBAAuBA,CAACZ,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC9CC,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACC,MAAM,CAACO,SAAS,CAACZ,GAAG,CAAC,CAAC;EAClDE,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACC,MAAM,CAACO,SAAS,CAACX,GAAG,CAAC,CAAC;EAClDC,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACJ,GAAG,IAAIC,GAAG,CAAC;EACvC,IAAII,MAAM,CAACC,KAAK,CAACP,KAAK,CAAC,IAAIA,KAAK,IAAIC,GAAG,EAAE;IACrC,OAAOA,GAAG;EACd;EACA,IAAID,KAAK,IAAIE,GAAG,EAAE;IACd,OAAOA,GAAG;EACd;EACA,OAAOM,IAAI,CAACM,KAAK,CAACd,KAAK,CAAC;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,WAAWA,CAACf,KAAK,EAAEgB,OAAO,EAAE;EACjCb,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACC,MAAM,CAACW,QAAQ,CAACjB,KAAK,CAAC,CAAC;EACnDG,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACC,MAAM,CAACW,QAAQ,CAACD,OAAO,CAAC,CAAC;EACrDb,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACW,OAAO,GAAG,CAAC,CAAC;EACxC,MAAME,SAAS,GAAGlB,KAAK,GAAGgB,OAAO;EACjC,OAAOE,SAAS,GAAGF,OAAO,GAAG,CAAC,GAAGhB,KAAK,GAAGkB,SAAS,GAAGlB,KAAK,GAAGgB,OAAO,GAAGE,SAAS;AACpF;;AAEA;AACA,MAAMC,aAAa,GAAG,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACpB,KAAK,EAAEqB,SAAS,EAAEC,IAAI,EAAE;EACvC,IAAItB,KAAK,KAAKuB,QAAQ,EAAE;IACpB,OAAOvB,KAAK;EAChB;EACAG,SAAS,IAAIC,OAAO,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,KAAK,CAACP,KAAK,CAAC,EAAE,sBAAsB,CAAC;EACzEG,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACC,MAAM,CAACO,SAAS,CAACQ,SAAS,CAAC,EAAE,2BAA2B,CAAC;EACrFA,SAAS,GAAGb,IAAI,CAACP,GAAG,CAACoB,SAAS,EAAEF,aAAa,CAAC;EAC9C,MAAM,CAACK,WAAW,EAAEC,QAAQ,GAAG,EAAE,CAAC,GAAG,GAAGzB,KAAK,EAAE,CAAC0B,KAAK,CAAC,GAAG,CAAC;EAC1D,MAAMC,UAAU,GAAGL,IAAI,CAAChB,MAAM,CAAC,GAAGkB,WAAW,IAAIlB,MAAM,CAACmB,QAAQ,CAAC,GAAGJ,SAAS,EAAE,CAAC,CAAC;EACjF;AACJ;AACA;EACIlB,SAAS,IACLC,OAAO,CAACC,MAAM,CAACC,MAAM,CAACsB,aAAa,CAACD,UAAU,CAAC,EAAE,mDAAmD,CAAC;EACzG,MAAME,aAAa,GAAG,GAAGF,UAAU,GAAG,CAACD,KAAK,CAAC,GAAG,CAAC;EACjD,OAAOpB,MAAM,CAAC,GAAGuB,aAAa,CAAC,CAAC,CAAC,IAAIvB,MAAM,CAACuB,aAAa,CAAC,CAAC,CAAC,CAAC,GAAGR,SAAS,EAAE,CAAC;AAChF;AACA,SAASS,QAAQA,CAAC9B,KAAK,EAAEqB,SAAS,GAAG,CAAC,EAAE;EACpC,OAAOD,SAAS,CAACpB,KAAK,EAAEqB,SAAS,EAAEb,IAAI,CAACM,KAAK,CAAC;AAClD;AACA,SAASiB,OAAOA,CAAC/B,KAAK,EAAEqB,SAAS,GAAG,CAAC,EAAE;EACnC,OAAOD,SAAS,CAACpB,KAAK,EAAEqB,SAAS,EAAEb,IAAI,CAACwB,IAAI,CAAC;AACjD;AACA,SAASC,QAAQA,CAACjC,KAAK,EAAEqB,SAAS,GAAG,CAAC,EAAE;EACpC,OAAOD,SAAS,CAACpB,KAAK,EAAEqB,SAAS,EAAEb,IAAI,CAAC0B,KAAK,CAAC;AAClD;AACA,SAASC,QAAQA,CAACnC,KAAK,EAAEqB,SAAS,GAAG,CAAC,EAAE;EACpC,OAAOD,SAAS,CAACpB,KAAK,EAAEqB,SAAS,EAAEb,IAAI,CAAC4B,KAAK,CAAC;AAClD;AACA,SAASC,gBAAgBA,CAACrC,KAAK,EAAEqB,SAAS,GAAG,CAAC,EAAE;EAC5C,OAAOf,MAAM,CAACsB,aAAa,CAACpB,IAAI,CAAC4B,KAAK,CAACpC,KAAK,GAAG,EAAE,IAAIqB,SAAS,CAAC,CAAC;AACpE;AAEA,SAASiB,YAAYA,CAAC;EAAEtC,KAAK;EAAEqB,SAAS;EAAEkB;AAAQ,CAAC,EAAE;EACjD,QAAQA,MAAM;IACV,KAAK,MAAM;MACP,OAAOR,OAAO,CAAC/B,KAAK,EAAEqB,SAAS,CAAC;IACpC,KAAK,OAAO;MACR,OAAOY,QAAQ,CAACjC,KAAK,EAAEqB,SAAS,CAAC;IACrC,KAAK,OAAO;MACR,OAAOS,QAAQ,CAAC9B,KAAK,EAAEqB,SAAS,CAAC;IACrC;MACI,OAAOc,QAAQ,CAACnC,KAAK,EAAEqB,SAAS,CAAC;EACzC;AACJ;;AAEA;AACA;AACA;AACA,SAASmB,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACrB,OAAOA,IAAI,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;AAC1C;AAEA,SAASC,QAAQA,CAACC,IAAI,EAAE;EACpB,OAAOA,IAAI,GAAG,CAAC,GAAG,CAAC;AACvB;AAEA,SAASC,YAAYA,CAAC/C,KAAK,EAAE;EACzB,OAAOgD,QAAQ,CAAChD,KAAK,EAAE,EAAE,CAAC;AAC9B;;AAEA;AACA;AACA;AACA,SAASiD,YAAYA,CAACC,GAAG,EAAE;EACvB,OAAQA,GAAG,GAAG1C,IAAI,CAAC2C,EAAE,GAAI,GAAG;AAChC;;AAEA;AACA;AACA;;AAEA,SAASpB,OAAO,EAAEhC,QAAQ,EAAEkC,QAAQ,EAAExB,UAAU,EAAE4B,gBAAgB,EAAEzB,uBAAuB,EAAEG,WAAW,EAAEe,QAAQ,EAAEQ,YAAY,EAAEE,MAAM,EAAEK,QAAQ,EAAEE,YAAY,EAAEE,YAAY,EAAEd,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}