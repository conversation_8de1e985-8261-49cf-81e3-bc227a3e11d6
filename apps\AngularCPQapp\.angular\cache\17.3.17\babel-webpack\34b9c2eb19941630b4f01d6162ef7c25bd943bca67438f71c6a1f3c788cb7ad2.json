{"ast": null, "code": "import { NgI<PERSON>, NgTemplateOutlet, As<PERSON><PERSON><PERSON><PERSON>, NgForOf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, inject, forwardRef, Component, ChangeDetectionStrategy, SkipSelf, ContentChildren, ViewChild, EventEmitter, Output, Injectable } from '@angular/core';\nimport { TuiLet } from '@taiga-ui/cdk/directives/let';\nimport { tuiCreateToken, tuiProvide, tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { injectContext, PolymorpheusComponent, PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { Subject, startWith, map, distinctUntilChanged, mergeMap, tap } from 'rxjs';\nimport { EMPTY_ARRAY, TUI_TRUE_HANDLER, EMPTY_FUNCTION, EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { TuiExpandComponent } from '@taiga-ui/core/components/expand';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { TUI_MORE_WORD } from '@taiga-ui/kit/tokens';\nfunction TuiTreeItemContent_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function TuiTreeItemContent_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClick());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"border-radius\", 100, \"%\");\n    i0.ɵɵclassProp(\"t-button_expanded\", ctx_r1.expanded());\n    i0.ɵɵproperty(\"iconStart\", ctx_r1.icons.more);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.more(), \"\\n\");\n  }\n}\nconst _c0 = [\"*\", [[\"tui-tree-item\"]], [[\"tui-tree\"]]];\nconst _c1 = [\"*\", \"tui-tree-item\", \"tui-tree\"];\nconst _c2 = (a0, a1) => ({\n  $implicit: a0,\n  template: a1\n});\nfunction TuiTreeItem_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction TuiTreeItem_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r1, \"\\n\");\n  }\n}\nfunction TuiTreeItem_tui_expand_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tui-expand\", 4)(1, \"div\");\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵprojection(3, 2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"expanded\", ctx_r1.expanded());\n  }\n}\nfunction TuiTreeItem_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c3 = (a0, a1) => ({\n  $implicit: a0,\n  node: a1\n});\nfunction TuiTreeComponent_tui_tree_item_0_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r1, \" \");\n  }\n}\nfunction TuiTreeComponent_tui_tree_item_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiTreeComponent_tui_tree_item_0_ng_container_2_ng_container_1_Template, 2, 1, \"ng-container\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const view_r2 = i0.ɵɵreference(1);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r2.content)(\"polymorpheusOutletContext\", i0.ɵɵpureFunction2(2, _c3, ctx_r2.value, view_r2));\n  }\n}\nfunction TuiTreeComponent_tui_tree_item_0_tui_tree_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-tree\", 6);\n  }\n  if (rf & 2) {\n    const child_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"content\", ctx_r2.content)(\"trackBy\", ctx_r2.trackBy)(\"value\", child_r4);\n  }\n}\nfunction TuiTreeComponent_tui_tree_item_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tui-tree-item\", 2, 0);\n    i0.ɵɵtemplate(2, TuiTreeComponent_tui_tree_item_0_ng_container_2_Template, 2, 5, \"ng-container\", 3)(3, TuiTreeComponent_tui_tree_item_0_tui_tree_3_Template, 1, 3, \"tui-tree\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const children_r5 = ctx.tuiLet;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"tuiTreeNode\", ctx_r2.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.value !== children_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", children_r5)(\"ngForTrackBy\", ctx_r2.trackBy);\n  }\n}\nclass TuiTreeChildren {\n  constructor() {\n    this.childrenHandler = TuiTreeChildren.defaultHandler;\n  }\n  static defaultHandler(item) {\n    return Array.isArray(item) ? item : EMPTY_ARRAY;\n  }\n  static {\n    this.ɵfac = function TuiTreeChildren_Factory(t) {\n      return new (t || TuiTreeChildren)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTreeChildren,\n      selectors: [[\"tui-tree\", \"childrenHandler\", \"\"]],\n      inputs: {\n        childrenHandler: \"childrenHandler\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTreeChildren, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'tui-tree[childrenHandler]'\n    }]\n  }], null, {\n    childrenHandler: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiTreeItemContent {\n  constructor() {\n    this.controller = inject(forwardRef(() => TUI_TREE_CONTROLLER));\n    this.change$ = new Subject();\n    this.icons = inject(TUI_COMMON_ICONS);\n    this.more = toSignal(inject(TUI_MORE_WORD));\n    this.context = injectContext();\n    this.expanded = toSignal(this.change$.pipe(startWith(null), map(() => this.isExpanded), distinctUntilChanged()), {\n      initialValue: this.isExpanded\n    });\n  }\n  ngDoCheck() {\n    this.change$.next();\n  }\n  get isExpandable() {\n    return this.context.$implicit.isExpandable && this.controller !== TUI_DEFAULT_TREE_CONTROLLER;\n  }\n  get isExpanded() {\n    return this.context.$implicit.isExpanded;\n  }\n  onClick() {\n    this.controller.toggle(this.context.$implicit);\n  }\n  static {\n    this.ɵfac = function TuiTreeItemContent_Factory(t) {\n      return new (t || TuiTreeItemContent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTreeItemContent,\n      selectors: [[\"ng-component\"]],\n      hostVars: 2,\n      hostBindings: function TuiTreeItemContent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_expandable\", ctx.isExpandable);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"appearance\", \"flat\", \"size\", \"xs\", \"tuiIconButton\", \"\", \"type\", \"button\", \"class\", \"t-button\", 3, \"t-button_expanded\", \"iconStart\", \"border-radius\", \"click\", 4, \"ngIf\"], [3, \"ngTemplateOutlet\"], [\"appearance\", \"flat\", \"size\", \"xs\", \"tuiIconButton\", \"\", \"type\", \"button\", 1, \"t-button\", 3, \"click\", \"iconStart\"]],\n      template: function TuiTreeItemContent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiTreeItemContent_button_0_Template, 2, 6, \"button\", 0);\n          i0.ɵɵelementContainer(1, 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isExpandable);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.context.template);\n        }\n      },\n      dependencies: [NgIf, NgTemplateOutlet, TuiButton],\n      styles: [\"[_nghost-%COMP%]{display:flex;align-items:center}[_nghost-%COMP%]   tui-tree-item._expandable[_nghost-%COMP%]:not(._expandable), tui-tree-item._expandable   [_nghost-%COMP%]:not(._expandable){padding-left:2rem}.t-button[_ngcontent-%COMP%]{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-right:.5rem}.t-button_expanded[_ngcontent-%COMP%]{transform:rotate(90deg)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTreeItemContent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      imports: [NgIf, NgTemplateOutlet, TuiButton],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class._expandable]': 'isExpandable'\n      },\n      template: \"<button\\n    *ngIf=\\\"isExpandable\\\"\\n    appearance=\\\"flat\\\"\\n    size=\\\"xs\\\"\\n    tuiIconButton\\n    type=\\\"button\\\"\\n    class=\\\"t-button\\\"\\n    [class.t-button_expanded]=\\\"expanded()\\\"\\n    [iconStart]=\\\"icons.more\\\"\\n    [style.border-radius.%]=\\\"100\\\"\\n    (click)=\\\"onClick()\\\"\\n>\\n    {{ more() }}\\n</button>\\n<ng-container [ngTemplateOutlet]=\\\"context.template\\\" />\\n\",\n      styles: [\":host{display:flex;align-items:center}:host :host-context(tui-tree-item._expandable):not(._expandable){padding-left:2rem}.t-button{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-right:.5rem}.t-button_expanded{transform:rotate(90deg)}\\n\"]\n    }]\n  }], null, null);\n})();\nconst TUI_TREE_ITEM_CONTENT = new PolymorpheusComponent(TuiTreeItemContent);\nconst TUI_DEFAULT_TREE_CONTROLLER = {\n  isExpanded: TUI_TRUE_HANDLER,\n  toggle: EMPTY_FUNCTION\n};\n\n/**\n * Controller for tracking value - TuiTreeItemComponent pairs\n */\nconst TUI_TREE_ACCESSOR = tuiCreateToken();\n/**\n * Controller for expanding the tree\n */\nconst TUI_TREE_CONTROLLER = tuiCreateToken(TUI_DEFAULT_TREE_CONTROLLER);\n/**\n * A node of a tree view\n */\nconst TUI_TREE_NODE = tuiCreateToken();\n/**\n * A tree node placeholder for loading\n */\nconst TUI_TREE_LOADING = tuiCreateToken({});\n/**\n * A tree node starting point\n */\nconst TUI_TREE_START = tuiCreateToken();\n/**\n * A service to load tree progressively\n */\nconst TUI_TREE_LOADER = tuiCreateToken();\n/**\n * Content for a tree item\n */\nconst TUI_TREE_CONTENT = tuiCreateToken(TUI_TREE_ITEM_CONTENT);\n/**\n * Nesting level of current TreeView node\n */\nconst TUI_TREE_LEVEL = tuiCreateToken(-1);\nclass TuiTreeItem {\n  constructor() {\n    this.nested = EMPTY_QUERY;\n    this.el = tuiInjectElement();\n    this.controller = inject(forwardRef(() => TUI_TREE_CONTROLLER));\n    this.change$ = new Subject();\n    this.level = inject(forwardRef(() => TUI_TREE_LEVEL));\n    this.content = inject(forwardRef(() => TUI_TREE_CONTENT));\n    this.expanded = toSignal(this.change$.pipe(startWith(null), map(() => this.isExpanded)), {\n      initialValue: this.isExpanded\n    });\n    this.attached = toSignal(this.change$.pipe(map(() => this.el.isConnected), distinctUntilChanged()), {\n      initialValue: this.el.isConnected\n    });\n  }\n  get isExpandable() {\n    return !!this.nested.length;\n  }\n  get isExpanded() {\n    return this.controller.isExpanded(this);\n  }\n  ngDoCheck() {\n    this.checkChanges();\n  }\n  checkChanges() {\n    this.change$.next();\n  }\n  static {\n    this.ɵfac = function TuiTreeItem_Factory(t) {\n      return new (t || TuiTreeItem)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTreeItem,\n      selectors: [[\"tui-tree-item\"]],\n      contentQueries: function TuiTreeItem_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TUI_TREE_NODE, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nested = _t);\n        }\n      },\n      hostAttrs: [\"role\", \"treeitem\"],\n      hostVars: 2,\n      hostBindings: function TuiTreeItem_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_expandable\", ctx.isExpandable);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TUI_TREE_NODE, TuiTreeItem), {\n        provide: TUI_TREE_LEVEL,\n        deps: [[new SkipSelf(), TUI_TREE_LEVEL]],\n        useFactory: level => ++level\n      }]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 5,\n      vars: 7,\n      consts: [[\"template\", \"\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [\"role\", \"group\", \"class\", \"t-children\", 3, \"expanded\", 4, \"ngIf\"], [4, \"ngIf\"], [\"role\", \"group\", 1, \"t-children\", 3, \"expanded\"]],\n      template: function TuiTreeItem_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵtemplate(0, TuiTreeItem_ng_template_0_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, TuiTreeItem_ng_container_2_Template, 2, 1, \"ng-container\", 1)(3, TuiTreeItem_tui_expand_3_Template, 4, 1, \"tui-expand\", 2)(4, TuiTreeItem_ng_container_4_Template, 1, 0, \"ng-container\", 3);\n        }\n        if (rf & 2) {\n          const template_r3 = i0.ɵɵreference(1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.content)(\"polymorpheusOutletContext\", i0.ɵɵpureFunction2(4, _c2, ctx, template_r3));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isExpandable);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.attached());\n        }\n      },\n      dependencies: [NgIf, PolymorpheusOutlet, TuiExpandComponent],\n      styles: [\"[_nghost-%COMP%]{display:block}.t-children[_ngcontent-%COMP%]{position:relative;margin-left:var(--tui-tree-item-indent, 1.5rem)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTreeItem, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-tree-item',\n      imports: [NgIf, PolymorpheusOutlet, TuiExpandComponent],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiProvide(TUI_TREE_NODE, TuiTreeItem), {\n        provide: TUI_TREE_LEVEL,\n        deps: [[new SkipSelf(), TUI_TREE_LEVEL]],\n        useFactory: level => ++level\n      }],\n      host: {\n        role: 'treeitem',\n        '[class._expandable]': 'isExpandable'\n      },\n      template: \"<ng-template #template>\\n    <ng-content />\\n</ng-template>\\n<ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: this, template: template}\\\">\\n    {{ text }}\\n</ng-container>\\n<tui-expand\\n    *ngIf=\\\"isExpandable\\\"\\n    role=\\\"group\\\"\\n    class=\\\"t-children\\\"\\n    [expanded]=\\\"expanded()\\\"\\n>\\n    <div>\\n        <ng-content select=\\\"tui-tree-item\\\" />\\n        <ng-content select=\\\"tui-tree\\\" />\\n    </div>\\n</tui-expand>\\n<ng-container *ngIf=\\\"attached()\\\" />\\n\",\n      styles: [\":host{display:block}.t-children{position:relative;margin-left:var(--tui-tree-item-indent, 1.5rem)}\\n\"]\n    }]\n  }], null, {\n    nested: [{\n      type: ContentChildren,\n      args: [TUI_TREE_NODE]\n    }]\n  });\n})();\nclass TuiTreeNode {\n  constructor() {\n    this.component = inject(TuiTreeItem);\n    this.directive = inject(TUI_TREE_ACCESSOR, {\n      optional: true\n    });\n  }\n  set value(value) {\n    this.directive?.register(this.component, value);\n  }\n  ngOnDestroy() {\n    this.directive?.unregister(this.component);\n  }\n  static {\n    this.ɵfac = function TuiTreeNode_Factory(t) {\n      return new (t || TuiTreeNode)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTreeNode,\n      selectors: [[\"tui-tree-item\", \"tuiTreeNode\", \"\"]],\n      inputs: {\n        value: [i0.ɵɵInputFlags.None, \"tuiTreeNode\", \"value\"]\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTreeNode, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'tui-tree-item[tuiTreeNode]'\n    }]\n  }], null, {\n    value: [{\n      type: Input,\n      args: ['tuiTreeNode']\n    }]\n  });\n})();\nclass TuiTreeComponent {\n  constructor() {\n    this.check$ = new Subject();\n    this.children$ = this.check$.pipe(startWith(null), map(() => this.handler(this.value)), distinctUntilChanged());\n    this.directive = inject(TuiTreeChildren, {\n      optional: true\n    });\n    this.trackBy = (_, item) => item;\n    this.content = ({\n      $implicit\n    }) => String($implicit);\n  }\n  ngDoCheck() {\n    this.checkChanges();\n  }\n  checkChanges() {\n    this.check$.next();\n    this.item?.checkChanges();\n    this.child?.checkChanges();\n  }\n  get handler() {\n    return this.directive?.childrenHandler || TuiTreeChildren.defaultHandler;\n  }\n  static {\n    this.ɵfac = function TuiTreeComponent_Factory(t) {\n      return new (t || TuiTreeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTreeComponent,\n      selectors: [[\"tui-tree\"]],\n      viewQuery: function TuiTreeComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TuiTreeItem, 5);\n          i0.ɵɵviewQuery(TuiTreeComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.item = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.child = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"tree\"],\n      inputs: {\n        value: \"value\",\n        trackBy: \"trackBy\",\n        content: \"content\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TUI_TREE_NODE, TuiTreeComponent)]), i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 3,\n      consts: [[\"view\", \"\"], [3, \"tuiTreeNode\", 4, \"tuiLet\"], [3, \"tuiTreeNode\"], [4, \"ngIf\"], [3, \"content\", \"trackBy\", \"value\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [3, \"content\", \"trackBy\", \"value\"]],\n      template: function TuiTreeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiTreeComponent_tui_tree_item_0_Template, 4, 4, \"tui-tree-item\", 1);\n          i0.ɵɵpipe(1, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"tuiLet\", i0.ɵɵpipeBind1(1, 1, ctx.children$));\n        }\n      },\n      dependencies: [TuiTreeComponent, AsyncPipe, NgForOf, NgIf, PolymorpheusOutlet, TuiLet, TuiTreeItem, TuiTreeNode],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:block}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTreeComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-tree',\n      imports: [AsyncPipe, NgForOf, NgIf, PolymorpheusOutlet, TuiLet, TuiTreeItem, TuiTreeNode],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiProvide(TUI_TREE_NODE, TuiTreeComponent)],\n      host: {\n        role: 'tree'\n      },\n      template: \"<tui-tree-item\\n    *tuiLet=\\\"children$ | async as children\\\"\\n    #view\\n    [tuiTreeNode]=\\\"value\\\"\\n>\\n    <ng-container *ngIf=\\\"value !== children\\\">\\n        <ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: value, node: view}\\\">\\n            {{ text }}\\n        </ng-container>\\n    </ng-container>\\n    <tui-tree\\n        *ngFor=\\\"let child of children; trackBy: trackBy\\\"\\n        [content]=\\\"content\\\"\\n        [trackBy]=\\\"trackBy\\\"\\n        [value]=\\\"child\\\"\\n    />\\n</tui-tree-item>\\n\",\n      styles: [\":host{position:relative;display:block}\\n\"]\n    }]\n  }], null, {\n    item: [{\n      type: ViewChild,\n      args: [forwardRef(() => TuiTreeItem)]\n    }],\n    child: [{\n      type: ViewChild,\n      args: [forwardRef(() => TuiTreeComponent)]\n    }],\n    value: [{\n      type: Input,\n      args: [{\n        required: true\n      }]\n    }],\n    trackBy: [{\n      type: Input\n    }],\n    content: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiTreeControllerDirective {\n  constructor() {\n    this.items = new Map();\n    this.fallback = true;\n    this.map = new Map();\n    this.toggled = new EventEmitter();\n  }\n  register(item, value) {\n    this.items.set(item, value);\n  }\n  unregister(item) {\n    this.items.delete(item);\n  }\n  isExpanded(item) {\n    const value = this.items.get(item);\n    return (value && this.map.get(value)) ?? this.fallback;\n  }\n  toggle(item) {\n    const value = this.items.get(item);\n    const expanded = this.isExpanded(item);\n    if (!tuiIsPresent(value)) {\n      return;\n    }\n    this.toggled.emit(value);\n    this.map.set(value, !expanded);\n  }\n  static {\n    this.ɵfac = function TuiTreeControllerDirective_Factory(t) {\n      return new (t || TuiTreeControllerDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTreeControllerDirective,\n      selectors: [[\"\", \"tuiTreeController\", \"\", \"map\", \"\"]],\n      inputs: {\n        fallback: [i0.ɵɵInputFlags.None, \"tuiTreeController\", \"fallback\"],\n        map: \"map\"\n      },\n      outputs: {\n        toggled: \"toggled\"\n      },\n      exportAs: [\"tuiTreeController\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TUI_TREE_ACCESSOR, TuiTreeControllerDirective), tuiProvide(TUI_TREE_CONTROLLER, TuiTreeControllerDirective)])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTreeControllerDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiTreeController][map]',\n      providers: [tuiProvide(TUI_TREE_ACCESSOR, TuiTreeControllerDirective), tuiProvide(TUI_TREE_CONTROLLER, TuiTreeControllerDirective)],\n      exportAs: 'tuiTreeController'\n    }]\n  }], null, {\n    fallback: [{\n      type: Input,\n      args: ['tuiTreeController']\n    }],\n    map: [{\n      type: Input\n    }],\n    toggled: [{\n      type: Output\n    }]\n  });\n})();\nclass TuiTreeItemController {\n  constructor() {\n    this.map = new WeakMap();\n    this.fallback = true;\n  }\n  isExpanded(item) {\n    return this.map.get(item) ?? this.fallback;\n  }\n  toggle(item) {\n    this.map.set(item, !this.isExpanded(item));\n  }\n  static {\n    this.ɵfac = function TuiTreeItemController_Factory(t) {\n      return new (t || TuiTreeItemController)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTreeItemController,\n      selectors: [[\"\", \"tuiTreeController\", \"\", 3, \"map\", \"\"]],\n      inputs: {\n        fallback: [i0.ɵɵInputFlags.None, \"tuiTreeController\", \"fallback\"]\n      },\n      exportAs: [\"tuiTreeController\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TUI_TREE_CONTROLLER, TuiTreeItemController)])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTreeItemController, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiTreeController]:not([map])',\n      providers: [tuiProvide(TUI_TREE_CONTROLLER, TuiTreeItemController)],\n      exportAs: 'tuiTreeController'\n    }]\n  }], null, {\n    fallback: [{\n      type: Input,\n      args: ['tuiTreeController']\n    }]\n  });\n})();\nclass TuiTreeService {\n  constructor() {\n    this.loading = inject(TUI_TREE_LOADING);\n    this.start = inject(TUI_TREE_START);\n    this.loader = inject(TUI_TREE_LOADER);\n    this.map = new Map([[this.loading, []]]);\n    this.load$ = new Subject();\n    this.data$ = this.load$.pipe(mergeMap(item => this.loader.loadChildren(item).pipe(tap(children => this.map.set(item, children)), map(children => children.filter(item => !this.loader.hasChildren(item))), tap(children => children.forEach(child => this.map.set(child, []))))), startWith(null), map(() => this.start));\n  }\n  getChildren(item) {\n    return this.map.get(item) || [this.loading];\n  }\n  loadChildren(item) {\n    if (this.map.get(item)) {\n      return;\n    }\n    this.map.set(item, [this.loading]);\n    this.load$.next(item);\n  }\n  static {\n    this.ɵfac = function TuiTreeService_Factory(t) {\n      return new (t || TuiTreeService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiTreeService,\n      factory: TuiTreeService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTreeService, [{\n    type: Injectable\n  }], null, null);\n})();\nconst TuiTree = [TuiTreeComponent, TuiTreeItem, TuiTreeItemContent, TuiTreeChildren, TuiTreeItemController, TuiTreeControllerDirective, TuiTreeNode];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DEFAULT_TREE_CONTROLLER, TUI_TREE_ACCESSOR, TUI_TREE_CONTENT, TUI_TREE_CONTROLLER, TUI_TREE_ITEM_CONTENT, TUI_TREE_LEVEL, TUI_TREE_LOADER, TUI_TREE_LOADING, TUI_TREE_NODE, TUI_TREE_START, TuiTree, TuiTreeChildren, TuiTreeComponent, TuiTreeControllerDirective, TuiTreeItem, TuiTreeItemContent, TuiTreeItemController, TuiTreeNode, TuiTreeService };", "map": {"version": 3, "names": ["NgIf", "NgTemplateOutlet", "AsyncPipe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i0", "Directive", "Input", "inject", "forwardRef", "Component", "ChangeDetectionStrategy", "SkipSelf", "ContentChildren", "ViewChild", "EventEmitter", "Output", "Injectable", "TuiLet", "tuiCreateToken", "tui<PERSON><PERSON><PERSON>", "tuiIsPresent", "injectContext", "PolymorpheusComponent", "Polymorpheus<PERSON><PERSON>let", "Subject", "startWith", "map", "distinctUntilChanged", "mergeMap", "tap", "EMPTY_ARRAY", "TUI_TRUE_HANDLER", "EMPTY_FUNCTION", "EMPTY_QUERY", "toSignal", "tuiInjectElement", "TuiExpandComponent", "TuiButton", "TUI_COMMON_ICONS", "TUI_MORE_WORD", "TuiTreeItemContent_button_0_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiTreeItemContent_button_0_Template_button_click_0_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onClick", "ɵɵtext", "ɵɵelementEnd", "ɵɵstyleProp", "ɵɵclassProp", "expanded", "ɵɵproperty", "icons", "more", "ɵɵadvance", "ɵɵtextInterpolate1", "_c0", "_c1", "_c2", "a0", "a1", "$implicit", "template", "TuiTreeItem_ng_template_0_Template", "ɵɵprojection", "TuiTreeItem_ng_container_2_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "text_r1", "polymorpheusOutlet", "TuiTreeItem_tui_expand_3_Template", "TuiTreeItem_ng_container_4_Template", "ɵɵelementContainer", "_c3", "node", "TuiTreeComponent_tui_tree_item_0_ng_container_2_ng_container_1_Template", "TuiTreeComponent_tui_tree_item_0_ng_container_2_Template", "ɵɵtemplate", "view_r2", "ɵɵreference", "ctx_r2", "content", "ɵɵpureFunction2", "value", "TuiTreeComponent_tui_tree_item_0_tui_tree_3_Template", "ɵɵelement", "child_r4", "trackBy", "TuiTreeComponent_tui_tree_item_0_Template", "children_r5", "tuiLet", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "defaultHandler", "item", "Array", "isArray", "ɵfac", "TuiTreeChildren_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "TuiTreeItemContent", "controller", "TUI_TREE_CONTROLLER", "change$", "context", "pipe", "isExpanded", "initialValue", "ngDoCheck", "next", "isExpandable", "TUI_DEFAULT_TREE_CONTROLLER", "toggle", "TuiTreeItemContent_Factory", "ɵcmp", "ɵɵdefineComponent", "hostVars", "hostBindings", "TuiTreeItemContent_HostBindings", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TuiTreeItemContent_Template", "dependencies", "styles", "changeDetection", "imports", "OnPush", "host", "TUI_TREE_ITEM_CONTENT", "TUI_TREE_ACCESSOR", "TUI_TREE_NODE", "TUI_TREE_LOADING", "TUI_TREE_START", "TUI_TREE_LOADER", "TUI_TREE_CONTENT", "TUI_TREE_LEVEL", "TuiTreeItem", "nested", "el", "level", "attached", "isConnected", "length", "checkChanges", "TuiTreeItem_Factory", "contentQueries", "TuiTreeItem_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostAttrs", "TuiTreeItem_HostBindings", "ɵɵProvidersFeature", "provide", "deps", "useFactory", "ngContentSelectors", "TuiTreeItem_Template", "ɵɵprojectionDef", "ɵɵtemplateRefExtractor", "template_r3", "providers", "role", "TuiTreeNode", "component", "directive", "optional", "register", "ngOnDestroy", "unregister", "TuiTreeNode_Factory", "ɵɵInputFlags", "None", "TuiTreeComponent", "check$", "children$", "handler", "_", "String", "child", "TuiTreeComponent_Factory", "viewQuery", "TuiTreeComponent_Query", "ɵɵviewQuery", "first", "TuiTreeComponent_Template", "ɵɵpipe", "ɵɵpipeBind1", "required", "TuiTreeControllerDirective", "items", "Map", "fallback", "toggled", "set", "delete", "get", "emit", "TuiTreeControllerDirective_Factory", "outputs", "exportAs", "TuiTreeItemController", "WeakMap", "TuiTreeItemController_Factory", "TuiTreeService", "loading", "start", "loader", "load$", "data$", "loadChildren", "children", "filter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TuiTreeService_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "TuiTree"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-tree.mjs"], "sourcesContent": ["import { NgIf, NgTemplateOutlet, As<PERSON><PERSON><PERSON><PERSON>, NgForOf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, inject, forwardRef, Component, ChangeDetectionStrategy, SkipSelf, ContentChildren, ViewChild, EventEmitter, Output, Injectable } from '@angular/core';\nimport { TuiLet } from '@taiga-ui/cdk/directives/let';\nimport { tuiCreateToken, tuiProvide, tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { injectContext, PolymorpheusComponent, PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { Subject, startWith, map, distinctUntilChanged, mergeMap, tap } from 'rxjs';\nimport { EMPTY_ARRAY, TUI_TRUE_HANDLER, EMPTY_FUNCTION, EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { TuiExpandComponent } from '@taiga-ui/core/components/expand';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { TUI_MORE_WORD } from '@taiga-ui/kit/tokens';\n\nclass TuiTreeChildren {\n    constructor() {\n        this.childrenHandler = TuiTreeChildren.defaultHandler;\n    }\n    static defaultHandler(item) {\n        return Array.isArray(item) ? item : EMPTY_ARRAY;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeChildren, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTreeChildren, isStandalone: true, selector: \"tui-tree[childrenHandler]\", inputs: { childrenHandler: \"childrenHandler\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeChildren, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-tree[childrenHandler]',\n                }]\n        }], propDecorators: { childrenHandler: [{\n                type: Input\n            }] } });\n\nclass TuiTreeItemContent {\n    constructor() {\n        this.controller = inject(forwardRef(() => TUI_TREE_CONTROLLER));\n        this.change$ = new Subject();\n        this.icons = inject(TUI_COMMON_ICONS);\n        this.more = toSignal(inject(TUI_MORE_WORD));\n        this.context = injectContext();\n        this.expanded = toSignal(this.change$.pipe(startWith(null), map(() => this.isExpanded), distinctUntilChanged()), { initialValue: this.isExpanded });\n    }\n    ngDoCheck() {\n        this.change$.next();\n    }\n    get isExpandable() {\n        return (this.context.$implicit.isExpandable &&\n            this.controller !== TUI_DEFAULT_TREE_CONTROLLER);\n    }\n    get isExpanded() {\n        return this.context.$implicit.isExpanded;\n    }\n    onClick() {\n        this.controller.toggle(this.context.$implicit);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeItemContent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTreeItemContent, isStandalone: true, selector: \"ng-component\", host: { properties: { \"class._expandable\": \"isExpandable\" } }, ngImport: i0, template: \"<button\\n    *ngIf=\\\"isExpandable\\\"\\n    appearance=\\\"flat\\\"\\n    size=\\\"xs\\\"\\n    tuiIconButton\\n    type=\\\"button\\\"\\n    class=\\\"t-button\\\"\\n    [class.t-button_expanded]=\\\"expanded()\\\"\\n    [iconStart]=\\\"icons.more\\\"\\n    [style.border-radius.%]=\\\"100\\\"\\n    (click)=\\\"onClick()\\\"\\n>\\n    {{ more() }}\\n</button>\\n<ng-container [ngTemplateOutlet]=\\\"context.template\\\" />\\n\", styles: [\":host{display:flex;align-items:center}:host :host-context(tui-tree-item._expandable):not(._expandable){padding-left:2rem}.t-button{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-right:.5rem}.t-button_expanded{transform:rotate(90deg)}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeItemContent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, imports: [NgIf, NgTemplateOutlet, TuiButton], changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[class._expandable]': 'isExpandable',\n                    }, template: \"<button\\n    *ngIf=\\\"isExpandable\\\"\\n    appearance=\\\"flat\\\"\\n    size=\\\"xs\\\"\\n    tuiIconButton\\n    type=\\\"button\\\"\\n    class=\\\"t-button\\\"\\n    [class.t-button_expanded]=\\\"expanded()\\\"\\n    [iconStart]=\\\"icons.more\\\"\\n    [style.border-radius.%]=\\\"100\\\"\\n    (click)=\\\"onClick()\\\"\\n>\\n    {{ more() }}\\n</button>\\n<ng-container [ngTemplateOutlet]=\\\"context.template\\\" />\\n\", styles: [\":host{display:flex;align-items:center}:host :host-context(tui-tree-item._expandable):not(._expandable){padding-left:2rem}.t-button{transition-property:transform;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;margin-right:.5rem}.t-button_expanded{transform:rotate(90deg)}\\n\"] }]\n        }] });\n\nconst TUI_TREE_ITEM_CONTENT = new PolymorpheusComponent(TuiTreeItemContent);\nconst TUI_DEFAULT_TREE_CONTROLLER = {\n    isExpanded: TUI_TRUE_HANDLER,\n    toggle: EMPTY_FUNCTION,\n};\n\n/**\n * Controller for tracking value - TuiTreeItemComponent pairs\n */\nconst TUI_TREE_ACCESSOR = tuiCreateToken();\n/**\n * Controller for expanding the tree\n */\nconst TUI_TREE_CONTROLLER = tuiCreateToken(TUI_DEFAULT_TREE_CONTROLLER);\n/**\n * A node of a tree view\n */\nconst TUI_TREE_NODE = tuiCreateToken();\n/**\n * A tree node placeholder for loading\n */\nconst TUI_TREE_LOADING = tuiCreateToken({});\n/**\n * A tree node starting point\n */\nconst TUI_TREE_START = tuiCreateToken();\n/**\n * A service to load tree progressively\n */\nconst TUI_TREE_LOADER = tuiCreateToken();\n/**\n * Content for a tree item\n */\nconst TUI_TREE_CONTENT = tuiCreateToken(TUI_TREE_ITEM_CONTENT);\n/**\n * Nesting level of current TreeView node\n */\nconst TUI_TREE_LEVEL = tuiCreateToken(-1);\n\nclass TuiTreeItem {\n    constructor() {\n        this.nested = EMPTY_QUERY;\n        this.el = tuiInjectElement();\n        this.controller = inject(forwardRef(() => TUI_TREE_CONTROLLER));\n        this.change$ = new Subject();\n        this.level = inject(forwardRef(() => TUI_TREE_LEVEL));\n        this.content = inject(forwardRef(() => TUI_TREE_CONTENT));\n        this.expanded = toSignal(this.change$.pipe(startWith(null), map(() => this.isExpanded)), { initialValue: this.isExpanded });\n        this.attached = toSignal(this.change$.pipe(map(() => this.el.isConnected), distinctUntilChanged()), { initialValue: this.el.isConnected });\n    }\n    get isExpandable() {\n        return !!this.nested.length;\n    }\n    get isExpanded() {\n        return this.controller.isExpanded(this);\n    }\n    ngDoCheck() {\n        this.checkChanges();\n    }\n    checkChanges() {\n        this.change$.next();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeItem, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTreeItem, isStandalone: true, selector: \"tui-tree-item\", host: { attributes: { \"role\": \"treeitem\" }, properties: { \"class._expandable\": \"isExpandable\" } }, providers: [\n            tuiProvide(TUI_TREE_NODE, TuiTreeItem),\n            {\n                provide: TUI_TREE_LEVEL,\n                deps: [[new SkipSelf(), TUI_TREE_LEVEL]],\n                useFactory: (level) => ++level,\n            },\n        ], queries: [{ propertyName: \"nested\", predicate: TUI_TREE_NODE }], ngImport: i0, template: \"<ng-template #template>\\n    <ng-content />\\n</ng-template>\\n<ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: this, template: template}\\\">\\n    {{ text }}\\n</ng-container>\\n<tui-expand\\n    *ngIf=\\\"isExpandable\\\"\\n    role=\\\"group\\\"\\n    class=\\\"t-children\\\"\\n    [expanded]=\\\"expanded()\\\"\\n>\\n    <div>\\n        <ng-content select=\\\"tui-tree-item\\\" />\\n        <ng-content select=\\\"tui-tree\\\" />\\n    </div>\\n</tui-expand>\\n<ng-container *ngIf=\\\"attached()\\\" />\\n\", styles: [\":host{display:block}.t-children{position:relative;margin-left:var(--tui-tree-item-indent, 1.5rem)}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"component\", type: TuiExpandComponent, selector: \"tui-expand\", inputs: [\"async\", \"expanded\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeItem, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-tree-item', imports: [NgIf, PolymorpheusOutlet, TuiExpandComponent], changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        tuiProvide(TUI_TREE_NODE, TuiTreeItem),\n                        {\n                            provide: TUI_TREE_LEVEL,\n                            deps: [[new SkipSelf(), TUI_TREE_LEVEL]],\n                            useFactory: (level) => ++level,\n                        },\n                    ], host: {\n                        role: 'treeitem',\n                        '[class._expandable]': 'isExpandable',\n                    }, template: \"<ng-template #template>\\n    <ng-content />\\n</ng-template>\\n<ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: this, template: template}\\\">\\n    {{ text }}\\n</ng-container>\\n<tui-expand\\n    *ngIf=\\\"isExpandable\\\"\\n    role=\\\"group\\\"\\n    class=\\\"t-children\\\"\\n    [expanded]=\\\"expanded()\\\"\\n>\\n    <div>\\n        <ng-content select=\\\"tui-tree-item\\\" />\\n        <ng-content select=\\\"tui-tree\\\" />\\n    </div>\\n</tui-expand>\\n<ng-container *ngIf=\\\"attached()\\\" />\\n\", styles: [\":host{display:block}.t-children{position:relative;margin-left:var(--tui-tree-item-indent, 1.5rem)}\\n\"] }]\n        }], propDecorators: { nested: [{\n                type: ContentChildren,\n                args: [TUI_TREE_NODE]\n            }] } });\n\nclass TuiTreeNode {\n    constructor() {\n        this.component = inject(TuiTreeItem);\n        this.directive = inject(TUI_TREE_ACCESSOR, {\n            optional: true,\n        });\n    }\n    set value(value) {\n        this.directive?.register(this.component, value);\n    }\n    ngOnDestroy() {\n        this.directive?.unregister(this.component);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeNode, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTreeNode, isStandalone: true, selector: \"tui-tree-item[tuiTreeNode]\", inputs: { value: [\"tuiTreeNode\", \"value\"] }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeNode, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-tree-item[tuiTreeNode]',\n                }]\n        }], propDecorators: { value: [{\n                type: Input,\n                args: ['tuiTreeNode']\n            }] } });\n\nclass TuiTreeComponent {\n    constructor() {\n        this.check$ = new Subject();\n        this.children$ = this.check$.pipe(startWith(null), map(() => this.handler(this.value)), distinctUntilChanged());\n        this.directive = inject(TuiTreeChildren, {\n            optional: true,\n        });\n        this.trackBy = (_, item) => item;\n        this.content = ({ $implicit }) => String($implicit);\n    }\n    ngDoCheck() {\n        this.checkChanges();\n    }\n    checkChanges() {\n        this.check$.next();\n        this.item?.checkChanges();\n        this.child?.checkChanges();\n    }\n    get handler() {\n        return this.directive?.childrenHandler || TuiTreeChildren.defaultHandler;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTreeComponent, isStandalone: true, selector: \"tui-tree\", inputs: { value: \"value\", trackBy: \"trackBy\", content: \"content\" }, host: { attributes: { \"role\": \"tree\" } }, providers: [tuiProvide(TUI_TREE_NODE, TuiTreeComponent)], viewQueries: [{ propertyName: \"item\", first: true, predicate: i0.forwardRef(function () { return TuiTreeItem; }), descendants: true }, { propertyName: \"child\", first: true, predicate: i0.forwardRef(function () { return TuiTreeComponent; }), descendants: true }], ngImport: i0, template: \"<tui-tree-item\\n    *tuiLet=\\\"children$ | async as children\\\"\\n    #view\\n    [tuiTreeNode]=\\\"value\\\"\\n>\\n    <ng-container *ngIf=\\\"value !== children\\\">\\n        <ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: value, node: view}\\\">\\n            {{ text }}\\n        </ng-container>\\n    </ng-container>\\n    <tui-tree\\n        *ngFor=\\\"let child of children; trackBy: trackBy\\\"\\n        [content]=\\\"content\\\"\\n        [trackBy]=\\\"trackBy\\\"\\n        [value]=\\\"child\\\"\\n    />\\n</tui-tree-item>\\n\", styles: [\":host{position:relative;display:block}\\n\"], dependencies: [{ kind: \"component\", type: TuiTreeComponent, selector: \"tui-tree\", inputs: [\"value\", \"trackBy\", \"content\"] }, { kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: TuiLet, selector: \"[tuiLet]\", inputs: [\"tuiLet\"] }, { kind: \"component\", type: TuiTreeItem, selector: \"tui-tree-item\" }, { kind: \"directive\", type: TuiTreeNode, selector: \"tui-tree-item[tuiTreeNode]\", inputs: [\"tuiTreeNode\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-tree', imports: [\n                        AsyncPipe,\n                        NgForOf,\n                        NgIf,\n                        PolymorpheusOutlet,\n                        TuiLet,\n                        TuiTreeItem,\n                        TuiTreeNode,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiProvide(TUI_TREE_NODE, TuiTreeComponent)], host: { role: 'tree' }, template: \"<tui-tree-item\\n    *tuiLet=\\\"children$ | async as children\\\"\\n    #view\\n    [tuiTreeNode]=\\\"value\\\"\\n>\\n    <ng-container *ngIf=\\\"value !== children\\\">\\n        <ng-container *polymorpheusOutlet=\\\"content as text; context: {$implicit: value, node: view}\\\">\\n            {{ text }}\\n        </ng-container>\\n    </ng-container>\\n    <tui-tree\\n        *ngFor=\\\"let child of children; trackBy: trackBy\\\"\\n        [content]=\\\"content\\\"\\n        [trackBy]=\\\"trackBy\\\"\\n        [value]=\\\"child\\\"\\n    />\\n</tui-tree-item>\\n\", styles: [\":host{position:relative;display:block}\\n\"] }]\n        }], propDecorators: { item: [{\n                type: ViewChild,\n                args: [forwardRef(() => TuiTreeItem)]\n            }], child: [{\n                type: ViewChild,\n                args: [forwardRef(() => TuiTreeComponent)]\n            }], value: [{\n                type: Input,\n                args: [{\n                        required: true,\n                    }]\n            }], trackBy: [{\n                type: Input\n            }], content: [{\n                type: Input\n            }] } });\n\nclass TuiTreeControllerDirective {\n    constructor() {\n        this.items = new Map();\n        this.fallback = true;\n        this.map = new Map();\n        this.toggled = new EventEmitter();\n    }\n    register(item, value) {\n        this.items.set(item, value);\n    }\n    unregister(item) {\n        this.items.delete(item);\n    }\n    isExpanded(item) {\n        const value = this.items.get(item);\n        return (value && this.map.get(value)) ?? this.fallback;\n    }\n    toggle(item) {\n        const value = this.items.get(item);\n        const expanded = this.isExpanded(item);\n        if (!tuiIsPresent(value)) {\n            return;\n        }\n        this.toggled.emit(value);\n        this.map.set(value, !expanded);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeControllerDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTreeControllerDirective, isStandalone: true, selector: \"[tuiTreeController][map]\", inputs: { fallback: [\"tuiTreeController\", \"fallback\"], map: \"map\" }, outputs: { toggled: \"toggled\" }, providers: [\n            tuiProvide(TUI_TREE_ACCESSOR, TuiTreeControllerDirective),\n            tuiProvide(TUI_TREE_CONTROLLER, TuiTreeControllerDirective),\n        ], exportAs: [\"tuiTreeController\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeControllerDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiTreeController][map]',\n                    providers: [\n                        tuiProvide(TUI_TREE_ACCESSOR, TuiTreeControllerDirective),\n                        tuiProvide(TUI_TREE_CONTROLLER, TuiTreeControllerDirective),\n                    ],\n                    exportAs: 'tuiTreeController',\n                }]\n        }], propDecorators: { fallback: [{\n                type: Input,\n                args: ['tuiTreeController']\n            }], map: [{\n                type: Input\n            }], toggled: [{\n                type: Output\n            }] } });\n\nclass TuiTreeItemController {\n    constructor() {\n        this.map = new WeakMap();\n        this.fallback = true;\n    }\n    isExpanded(item) {\n        return this.map.get(item) ?? this.fallback;\n    }\n    toggle(item) {\n        this.map.set(item, !this.isExpanded(item));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeItemController, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTreeItemController, isStandalone: true, selector: \"[tuiTreeController]:not([map])\", inputs: { fallback: [\"tuiTreeController\", \"fallback\"] }, providers: [tuiProvide(TUI_TREE_CONTROLLER, TuiTreeItemController)], exportAs: [\"tuiTreeController\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeItemController, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiTreeController]:not([map])',\n                    providers: [tuiProvide(TUI_TREE_CONTROLLER, TuiTreeItemController)],\n                    exportAs: 'tuiTreeController',\n                }]\n        }], propDecorators: { fallback: [{\n                type: Input,\n                args: ['tuiTreeController']\n            }] } });\n\nclass TuiTreeService {\n    constructor() {\n        this.loading = inject(TUI_TREE_LOADING);\n        this.start = inject(TUI_TREE_START);\n        this.loader = inject(TUI_TREE_LOADER);\n        this.map = new Map([[this.loading, []]]);\n        this.load$ = new Subject();\n        this.data$ = this.load$.pipe(mergeMap((item) => this.loader.loadChildren(item).pipe(tap((children) => this.map.set(item, children)), map((children) => children.filter((item) => !this.loader.hasChildren(item))), tap((children) => children.forEach((child) => this.map.set(child, []))))), startWith(null), map(() => this.start));\n    }\n    getChildren(item) {\n        return this.map.get(item) || [this.loading];\n    }\n    loadChildren(item) {\n        if (this.map.get(item)) {\n            return;\n        }\n        this.map.set(item, [this.loading]);\n        this.load$.next(item);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTreeService, decorators: [{\n            type: Injectable\n        }] });\n\nconst TuiTree = [\n    TuiTreeComponent,\n    TuiTreeItem,\n    TuiTreeItemContent,\n    TuiTreeChildren,\n    TuiTreeItemController,\n    TuiTreeControllerDirective,\n    TuiTreeNode,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_DEFAULT_TREE_CONTROLLER, TUI_TREE_ACCESSOR, TUI_TREE_CONTENT, TUI_TREE_CONTROLLER, TUI_TREE_ITEM_CONTENT, TUI_TREE_LEVEL, TUI_TREE_LOADER, TUI_TREE_LOADING, TUI_TREE_NODE, TUI_TREE_START, TuiTree, TuiTreeChildren, TuiTreeComponent, TuiTreeControllerDirective, TuiTreeItem, TuiTreeItemContent, TuiTreeItemController, TuiTreeNode, TuiTreeService };\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,OAAO,QAAQ,iBAAiB;AAC5E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,UAAU,QAAQ,eAAe;AAChL,SAASC,MAAM,QAAQ,8BAA8B;AACrD,SAASC,cAAc,EAAEC,UAAU,EAAEC,YAAY,QAAQ,mCAAmC;AAC5F,SAASC,aAAa,EAAEC,qBAAqB,EAAEC,kBAAkB,QAAQ,wBAAwB;AACjG,SAASC,OAAO,EAAEC,SAAS,EAAEC,GAAG,EAAEC,oBAAoB,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AACnF,SAASC,WAAW,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,WAAW,QAAQ,yBAAyB;AACpG,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,aAAa,QAAQ,sBAAsB;AAAC,SAAAC,qCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GASgDvC,EAAE,CAAAwC,gBAAA;IAAFxC,EAAE,CAAAyC,cAAA,eAoCmb,CAAC;IApCtbzC,EAAE,CAAA0C,UAAA,mBAAAC,6DAAA;MAAF3C,EAAE,CAAA4C,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CAoCsaF,MAAA,CAAAG,OAAA,CAAQ,CAAC;IAAA,CAAC,CAAC;IApCnbhD,EAAE,CAAAiD,MAAA,EAoCuc,CAAC;IApC1cjD,EAAE,CAAAkD,YAAA,CAoCgd,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAQ,MAAA,GApCnd7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAmD,WAAA,0BAoCqZ,CAAC;IApCxZnD,EAAE,CAAAoD,WAAA,sBAAAP,MAAA,CAAAQ,QAAA,EAoCgV,CAAC;IApCnVrD,EAAE,CAAAsD,UAAA,cAAAT,MAAA,CAAAU,KAAA,CAAAC,IAoCgX,CAAC;IApCnXxD,EAAE,CAAAyD,SAAA,CAoCuc,CAAC;IApC1czD,EAAE,CAAA0D,kBAAA,MAAAb,MAAA,CAAAW,IAAA,QAoCuc,CAAC;EAAA;AAAA;AAAA,MAAAG,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA;EAAAC,SAAA,EAAAF,EAAA;EAAAG,QAAA,EAAAF;AAAA;AAAA,SAAAG,mCAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApC1crC,EAAE,CAAAmE,YAAA,EAmHwC,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnH3CrC,EAAE,CAAAqE,uBAAA,EAmHgK,CAAC;IAnHnKrE,EAAE,CAAAiD,MAAA,EAmHkL,CAAC;IAnHrLjD,EAAE,CAAAsE,qBAAA;EAAA;EAAA,IAAAjC,EAAA;IAAA,MAAAkC,OAAA,GAAAjC,GAAA,CAAAkC,kBAAA;IAAFxE,EAAE,CAAAyD,SAAA,CAmHkL,CAAC;IAnHrLzD,EAAE,CAAA0D,kBAAA,MAAAa,OAAA,MAmHkL,CAAC;EAAA;AAAA;AAAA,SAAAE,kCAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnHrLrC,EAAE,CAAAyC,cAAA,mBAmH0T,CAAC,SAAU,CAAC;IAnHxUzC,EAAE,CAAAmE,YAAA,KAmHsX,CAAC;IAnHzXnE,EAAE,CAAAmE,YAAA,KAmHka,CAAC;IAnHranE,EAAE,CAAAkD,YAAA,CAmH8a,CAAC,CAAc,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAQ,MAAA,GAnHhc7C,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAsD,UAAA,aAAAT,MAAA,CAAAQ,QAAA,EAmHuT,CAAC;EAAA;AAAA;AAAA,SAAAqB,oCAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnH1TrC,EAAE,CAAA2E,kBAAA,EAmHoe,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAd,EAAA,EAAAC,EAAA;EAAAC,SAAA,EAAAF,EAAA;EAAAe,IAAA,EAAAd;AAAA;AAAA,SAAAe,wEAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnHverC,EAAE,CAAAqE,uBAAA,EAwLiwB,CAAC;IAxLpwBrE,EAAE,CAAAiD,MAAA,EAwLmyB,CAAC;IAxLtyBjD,EAAE,CAAAsE,qBAAA;EAAA;EAAA,IAAAjC,EAAA;IAAA,MAAAkC,OAAA,GAAAjC,GAAA,CAAAkC,kBAAA;IAAFxE,EAAE,CAAAyD,SAAA,CAwLmyB,CAAC;IAxLtyBzD,EAAE,CAAA0D,kBAAA,MAAAa,OAAA,KAwLmyB,CAAC;EAAA;AAAA;AAAA,SAAAQ,yDAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxLtyBrC,EAAE,CAAAqE,uBAAA,EAwLwpB,CAAC;IAxL3pBrE,EAAE,CAAAgF,UAAA,IAAAF,uEAAA,yBAwLiwB,CAAC;IAxLpwB9E,EAAE,CAAAsE,qBAAA;EAAA;EAAA,IAAAjC,EAAA;IAAFrC,EAAE,CAAA8C,aAAA;IAAA,MAAAmC,OAAA,GAAFjF,EAAE,CAAAkF,WAAA;IAAA,MAAAC,MAAA,GAAFnF,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAyD,SAAA,CAwL8sB,CAAC;IAxLjtBzD,EAAE,CAAAsD,UAAA,uBAAA6B,MAAA,CAAAC,OAwL8sB,CAAC,8BAxLjtBpF,EAAE,CAAAqF,eAAA,IAAAT,GAAA,EAAAO,MAAA,CAAAG,KAAA,EAAAL,OAAA,CAwL8vB,CAAC;EAAA;AAAA;AAAA,SAAAM,qDAAAlD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxLjwBrC,EAAE,CAAAwF,SAAA,iBAwLm/B,CAAC;EAAA;EAAA,IAAAnD,EAAA;IAAA,MAAAoD,QAAA,GAAAnD,GAAA,CAAA0B,SAAA;IAAA,MAAAmB,MAAA,GAxLt/BnF,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAsD,UAAA,YAAA6B,MAAA,CAAAC,OAwLi7B,CAAC,YAAAD,MAAA,CAAAO,OAA8B,CAAC,UAAAD,QAA0B,CAAC;EAAA;AAAA;AAAA,SAAAE,0CAAAtD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxL9+BrC,EAAE,CAAAyC,cAAA,yBAwLumB,CAAC;IAxL1mBzC,EAAE,CAAAgF,UAAA,IAAAD,wDAAA,yBAwLwpB,CAAC,IAAAQ,oDAAA,qBAA0V,CAAC;IAxLt/BvF,EAAE,CAAAkD,YAAA,CAwLqgC,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAuD,WAAA,GAAAtD,GAAA,CAAAuD,MAAA;IAAA,MAAAV,MAAA,GAxLxgCnF,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAsD,UAAA,gBAAA6B,MAAA,CAAAG,KAwLomB,CAAC;IAxLvmBtF,EAAE,CAAAyD,SAAA,EAwLqpB,CAAC;IAxLxpBzD,EAAE,CAAAsD,UAAA,SAAA6B,MAAA,CAAAG,KAAA,KAAAM,WAwLqpB,CAAC;IAxLxpB5F,EAAE,CAAAyD,SAAA,CAwLg4B,CAAC;IAxLn4BzD,EAAE,CAAAsD,UAAA,YAAAsC,WAwLg4B,CAAC,iBAAAT,MAAA,CAAAO,OAAe,CAAC;EAAA;AAAA;AA/Lx/B,MAAMI,eAAe,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,eAAe,GAAGF,eAAe,CAACG,cAAc;EACzD;EACA,OAAOA,cAAcA,CAACC,IAAI,EAAE;IACxB,OAAOC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAGxE,WAAW;EACnD;EACA;IAAS,IAAI,CAAC2E,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFT,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACU,IAAI,kBAD+ExG,EAAE,CAAAyG,iBAAA;MAAAC,IAAA,EACJZ,eAAe;MAAAa,SAAA;MAAAC,MAAA;QAAAZ,eAAA;MAAA;MAAAa,UAAA;IAAA,EAA4H;EAAE;AAChP;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG9G,EAAE,CAAA+G,iBAAA,CAGXjB,eAAe,EAAc,CAAC;IAC9GY,IAAI,EAAEzG,SAAS;IACf+G,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEjB,eAAe,EAAE,CAAC;MAChCU,IAAI,EAAExG;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgH,kBAAkB,CAAC;EACrBnB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoB,UAAU,GAAGhH,MAAM,CAACC,UAAU,CAAC,MAAMgH,mBAAmB,CAAC,CAAC;IAC/D,IAAI,CAACC,OAAO,GAAG,IAAIjG,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACmC,KAAK,GAAGpD,MAAM,CAAC+B,gBAAgB,CAAC;IACrC,IAAI,CAACsB,IAAI,GAAG1B,QAAQ,CAAC3B,MAAM,CAACgC,aAAa,CAAC,CAAC;IAC3C,IAAI,CAACmF,OAAO,GAAGrG,aAAa,CAAC,CAAC;IAC9B,IAAI,CAACoC,QAAQ,GAAGvB,QAAQ,CAAC,IAAI,CAACuF,OAAO,CAACE,IAAI,CAAClG,SAAS,CAAC,IAAI,CAAC,EAAEC,GAAG,CAAC,MAAM,IAAI,CAACkG,UAAU,CAAC,EAAEjG,oBAAoB,CAAC,CAAC,CAAC,EAAE;MAAEkG,YAAY,EAAE,IAAI,CAACD;IAAW,CAAC,CAAC;EACvJ;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,CAACL,OAAO,CAACM,IAAI,CAAC,CAAC;EACvB;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAQ,IAAI,CAACN,OAAO,CAACtD,SAAS,CAAC4D,YAAY,IACvC,IAAI,CAACT,UAAU,KAAKU,2BAA2B;EACvD;EACA,IAAIL,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACF,OAAO,CAACtD,SAAS,CAACwD,UAAU;EAC5C;EACAxE,OAAOA,CAAA,EAAG;IACN,IAAI,CAACmE,UAAU,CAACW,MAAM,CAAC,IAAI,CAACR,OAAO,CAACtD,SAAS,CAAC;EAClD;EACA;IAAS,IAAI,CAACqC,IAAI,YAAA0B,2BAAAxB,CAAA;MAAA,YAAAA,CAAA,IAAyFW,kBAAkB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAACc,IAAI,kBApC+EhI,EAAE,CAAAiI,iBAAA;MAAAvB,IAAA,EAoCJQ,kBAAkB;MAAAP,SAAA;MAAAuB,QAAA;MAAAC,YAAA,WAAAC,gCAAA/F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApChBrC,EAAE,CAAAoD,WAAA,gBAAAd,GAAA,CAAAsF,YAoCa,CAAC;QAAA;MAAA;MAAAf,UAAA;MAAAwB,QAAA,GApChBrI,EAAE,CAAAsI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAxE,QAAA,WAAAyE,4BAAArG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrC,EAAE,CAAAgF,UAAA,IAAA5C,oCAAA,mBAoCmb,CAAC;UApCtbpC,EAAE,CAAA2E,kBAAA,KAoC0gB,CAAC;QAAA;QAAA,IAAAtC,EAAA;UApC7gBrC,EAAE,CAAAsD,UAAA,SAAAhB,GAAA,CAAAsF,YAoCsL,CAAC;UApCzL5H,EAAE,CAAAyD,SAAA,CAoCugB,CAAC;UApC1gBzD,EAAE,CAAAsD,UAAA,qBAAAhB,GAAA,CAAAgF,OAAA,CAAArD,QAoCugB,CAAC;QAAA;MAAA;MAAA0E,YAAA,GAAqX/I,IAAI,EAA6FC,gBAAgB,EAAoJoC,SAAS;MAAA2G,MAAA;MAAAC,eAAA;IAAA,EAA+J;EAAE;AACn5C;AACA;EAAA,QAAA/B,SAAA,oBAAAA,SAAA,KAtCqG9G,EAAE,CAAA+G,iBAAA,CAsCXG,kBAAkB,EAAc,CAAC;IACjHR,IAAI,EAAErG,SAAS;IACf2G,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,IAAI;MAAEiC,OAAO,EAAE,CAAClJ,IAAI,EAAEC,gBAAgB,EAAEoC,SAAS,CAAC;MAAE4G,eAAe,EAAEvI,uBAAuB,CAACyI,MAAM;MAAEC,IAAI,EAAE;QACpH,qBAAqB,EAAE;MAC3B,CAAC;MAAE/E,QAAQ,EAAE,yXAAyX;MAAE2E,MAAM,EAAE,CAAC,uTAAuT;IAAE,CAAC;EACvtB,CAAC,CAAC;AAAA;AAEV,MAAMK,qBAAqB,GAAG,IAAI/H,qBAAqB,CAACgG,kBAAkB,CAAC;AAC3E,MAAMW,2BAA2B,GAAG;EAChCL,UAAU,EAAE7F,gBAAgB;EAC5BmG,MAAM,EAAElG;AACZ,CAAC;;AAED;AACA;AACA;AACA,MAAMsH,iBAAiB,GAAGpI,cAAc,CAAC,CAAC;AAC1C;AACA;AACA;AACA,MAAMsG,mBAAmB,GAAGtG,cAAc,CAAC+G,2BAA2B,CAAC;AACvE;AACA;AACA;AACA,MAAMsB,aAAa,GAAGrI,cAAc,CAAC,CAAC;AACtC;AACA;AACA;AACA,MAAMsI,gBAAgB,GAAGtI,cAAc,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA;AACA;AACA,MAAMuI,cAAc,GAAGvI,cAAc,CAAC,CAAC;AACvC;AACA;AACA;AACA,MAAMwI,eAAe,GAAGxI,cAAc,CAAC,CAAC;AACxC;AACA;AACA;AACA,MAAMyI,gBAAgB,GAAGzI,cAAc,CAACmI,qBAAqB,CAAC;AAC9D;AACA;AACA;AACA,MAAMO,cAAc,GAAG1I,cAAc,CAAC,CAAC,CAAC,CAAC;AAEzC,MAAM2I,WAAW,CAAC;EACd1D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2D,MAAM,GAAG7H,WAAW;IACzB,IAAI,CAAC8H,EAAE,GAAG5H,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACoF,UAAU,GAAGhH,MAAM,CAACC,UAAU,CAAC,MAAMgH,mBAAmB,CAAC,CAAC;IAC/D,IAAI,CAACC,OAAO,GAAG,IAAIjG,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACwI,KAAK,GAAGzJ,MAAM,CAACC,UAAU,CAAC,MAAMoJ,cAAc,CAAC,CAAC;IACrD,IAAI,CAACpE,OAAO,GAAGjF,MAAM,CAACC,UAAU,CAAC,MAAMmJ,gBAAgB,CAAC,CAAC;IACzD,IAAI,CAAClG,QAAQ,GAAGvB,QAAQ,CAAC,IAAI,CAACuF,OAAO,CAACE,IAAI,CAAClG,SAAS,CAAC,IAAI,CAAC,EAAEC,GAAG,CAAC,MAAM,IAAI,CAACkG,UAAU,CAAC,CAAC,EAAE;MAAEC,YAAY,EAAE,IAAI,CAACD;IAAW,CAAC,CAAC;IAC3H,IAAI,CAACqC,QAAQ,GAAG/H,QAAQ,CAAC,IAAI,CAACuF,OAAO,CAACE,IAAI,CAACjG,GAAG,CAAC,MAAM,IAAI,CAACqI,EAAE,CAACG,WAAW,CAAC,EAAEvI,oBAAoB,CAAC,CAAC,CAAC,EAAE;MAAEkG,YAAY,EAAE,IAAI,CAACkC,EAAE,CAACG;IAAY,CAAC,CAAC;EAC9I;EACA,IAAIlC,YAAYA,CAAA,EAAG;IACf,OAAO,CAAC,CAAC,IAAI,CAAC8B,MAAM,CAACK,MAAM;EAC/B;EACA,IAAIvC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACL,UAAU,CAACK,UAAU,CAAC,IAAI,CAAC;EAC3C;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,CAACsC,YAAY,CAAC,CAAC;EACvB;EACAA,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC3C,OAAO,CAACM,IAAI,CAAC,CAAC;EACvB;EACA;IAAS,IAAI,CAACtB,IAAI,YAAA4D,oBAAA1D,CAAA;MAAA,YAAAA,CAAA,IAAyFkD,WAAW;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACzB,IAAI,kBA5G+EhI,EAAE,CAAAiI,iBAAA;MAAAvB,IAAA,EA4GJ+C,WAAW;MAAA9C,SAAA;MAAAuD,cAAA,WAAAC,2BAAA9H,EAAA,EAAAC,GAAA,EAAA8H,QAAA;QAAA,IAAA/H,EAAA;UA5GTrC,EAAE,CAAAqK,cAAA,CAAAD,QAAA,EAmH7CjB,aAAa;QAAA;QAAA,IAAA9G,EAAA;UAAA,IAAAiI,EAAA;UAnH8BtK,EAAE,CAAAuK,cAAA,CAAAD,EAAA,GAAFtK,EAAE,CAAAwK,WAAA,QAAAlI,GAAA,CAAAoH,MAAA,GAAAY,EAAA;QAAA;MAAA;MAAAG,SAAA,WA4GsF,UAAU;MAAAvC,QAAA;MAAAC,YAAA,WAAAuC,yBAAArI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5GlGrC,EAAE,CAAAoD,WAAA,gBAAAd,GAAA,CAAAsF,YA4GM,CAAC;QAAA;MAAA;MAAAf,UAAA;MAAAwB,QAAA,GA5GTrI,EAAE,CAAA2K,kBAAA,CA4GsK,CACjQ5J,UAAU,CAACoI,aAAa,EAAEM,WAAW,CAAC,EACtC;QACImB,OAAO,EAAEpB,cAAc;QACvBqB,IAAI,EAAE,CAAC,CAAC,IAAItK,QAAQ,CAAC,CAAC,EAAEiJ,cAAc,CAAC,CAAC;QACxCsB,UAAU,EAAGlB,KAAK,IAAK,EAAEA;MAC7B,CAAC,CACJ,GAnH4F5J,EAAE,CAAAsI,mBAAA;MAAAyC,kBAAA,EAAAnH,GAAA;MAAA2E,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAxE,QAAA,WAAA+G,qBAAA3I,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrC,EAAE,CAAAiL,eAAA,CAAAtH,GAAA;UAAF3D,EAAE,CAAAgF,UAAA,IAAAd,kCAAA,gCAAFlE,EAAE,CAAAkL,sBAmHoB,CAAC,IAAA9G,mCAAA,yBAA2I,CAAC,IAAAK,iCAAA,uBAAyJ,CAAC,IAAAC,mCAAA,yBAAyK,CAAC;QAAA;QAAA,IAAArC,EAAA;UAAA,MAAA8I,WAAA,GAnHvenL,EAAE,CAAAkF,WAAA;UAAFlF,EAAE,CAAAyD,SAAA,EAmHsG,CAAC;UAnHzGzD,EAAE,CAAAsD,UAAA,uBAAAhB,GAAA,CAAA8C,OAmHsG,CAAC,8BAnHzGpF,EAAE,CAAAqF,eAAA,IAAAxB,GAAA,EAAAvB,GAAA,EAAA6I,WAAA,CAmH6J,CAAC;UAnHhKnL,EAAE,CAAAyD,SAAA,CAmHwO,CAAC;UAnH3OzD,EAAE,CAAAsD,UAAA,SAAAhB,GAAA,CAAAsF,YAmHwO,CAAC;UAnH3O5H,EAAE,CAAAyD,SAAA,CAmH+d,CAAC;UAnHlezD,EAAE,CAAAsD,UAAA,SAAAhB,GAAA,CAAAuH,QAAA,EAmH+d,CAAC;QAAA;MAAA;MAAAlB,YAAA,GAAsK/I,IAAI,EAA6FuB,kBAAkB,EAA8Ha,kBAAkB;MAAA4G,MAAA;MAAAC,eAAA;IAAA,EAAiH;EAAE;AACnmC;AACA;EAAA,QAAA/B,SAAA,oBAAAA,SAAA,KArHqG9G,EAAE,CAAA+G,iBAAA,CAqHX0C,WAAW,EAAc,CAAC;IAC1G/C,IAAI,EAAErG,SAAS;IACf2G,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,IAAI;MAAEI,QAAQ,EAAE,eAAe;MAAE6B,OAAO,EAAE,CAAClJ,IAAI,EAAEuB,kBAAkB,EAAEa,kBAAkB,CAAC;MAAE6G,eAAe,EAAEvI,uBAAuB,CAACyI,MAAM;MAAEqC,SAAS,EAAE,CAC/JrK,UAAU,CAACoI,aAAa,EAAEM,WAAW,CAAC,EACtC;QACImB,OAAO,EAAEpB,cAAc;QACvBqB,IAAI,EAAE,CAAC,CAAC,IAAItK,QAAQ,CAAC,CAAC,EAAEiJ,cAAc,CAAC,CAAC;QACxCsB,UAAU,EAAGlB,KAAK,IAAK,EAAEA;MAC7B,CAAC,CACJ;MAAEZ,IAAI,EAAE;QACLqC,IAAI,EAAE,UAAU;QAChB,qBAAqB,EAAE;MAC3B,CAAC;MAAEpH,QAAQ,EAAE,2eAA2e;MAAE2E,MAAM,EAAE,CAAC,sGAAsG;IAAE,CAAC;EACxnB,CAAC,CAAC,QAAkB;IAAEc,MAAM,EAAE,CAAC;MACvBhD,IAAI,EAAElG,eAAe;MACrBwG,IAAI,EAAE,CAACmC,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmC,WAAW,CAAC;EACdvF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACwF,SAAS,GAAGpL,MAAM,CAACsJ,WAAW,CAAC;IACpC,IAAI,CAAC+B,SAAS,GAAGrL,MAAM,CAAC+I,iBAAiB,EAAE;MACvCuC,QAAQ,EAAE;IACd,CAAC,CAAC;EACN;EACA,IAAInG,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACkG,SAAS,EAAEE,QAAQ,CAAC,IAAI,CAACH,SAAS,EAAEjG,KAAK,CAAC;EACnD;EACAqG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,SAAS,EAAEI,UAAU,CAAC,IAAI,CAACL,SAAS,CAAC;EAC9C;EACA;IAAS,IAAI,CAAClF,IAAI,YAAAwF,oBAAAtF,CAAA;MAAA,YAAAA,CAAA,IAAyF+E,WAAW;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAAC9E,IAAI,kBArJ+ExG,EAAE,CAAAyG,iBAAA;MAAAC,IAAA,EAqJJ4E,WAAW;MAAA3E,SAAA;MAAAC,MAAA;QAAAtB,KAAA,GArJTtF,EAAE,CAAA8L,YAAA,CAAAC,IAAA;MAAA;MAAAlF,UAAA;IAAA,EAqJiI;EAAE;AAC1O;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvJqG9G,EAAE,CAAA+G,iBAAA,CAuJXuE,WAAW,EAAc,CAAC;IAC1G5E,IAAI,EAAEzG,SAAS;IACf+G,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE3B,KAAK,EAAE,CAAC;MACtBoB,IAAI,EAAExG,KAAK;MACX8G,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgF,gBAAgB,CAAC;EACnBjG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkG,MAAM,GAAG,IAAI7K,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAC8K,SAAS,GAAG,IAAI,CAACD,MAAM,CAAC1E,IAAI,CAAClG,SAAS,CAAC,IAAI,CAAC,EAAEC,GAAG,CAAC,MAAM,IAAI,CAAC6K,OAAO,CAAC,IAAI,CAAC7G,KAAK,CAAC,CAAC,EAAE/D,oBAAoB,CAAC,CAAC,CAAC;IAC/G,IAAI,CAACiK,SAAS,GAAGrL,MAAM,CAAC2F,eAAe,EAAE;MACrC2F,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,CAAC/F,OAAO,GAAG,CAAC0G,CAAC,EAAElG,IAAI,KAAKA,IAAI;IAChC,IAAI,CAACd,OAAO,GAAG,CAAC;MAAEpB;IAAU,CAAC,KAAKqI,MAAM,CAACrI,SAAS,CAAC;EACvD;EACA0D,SAASA,CAAA,EAAG;IACR,IAAI,CAACsC,YAAY,CAAC,CAAC;EACvB;EACAA,YAAYA,CAAA,EAAG;IACX,IAAI,CAACiC,MAAM,CAACtE,IAAI,CAAC,CAAC;IAClB,IAAI,CAACzB,IAAI,EAAE8D,YAAY,CAAC,CAAC;IACzB,IAAI,CAACsC,KAAK,EAAEtC,YAAY,CAAC,CAAC;EAC9B;EACA,IAAImC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACX,SAAS,EAAExF,eAAe,IAAIF,eAAe,CAACG,cAAc;EAC5E;EACA;IAAS,IAAI,CAACI,IAAI,YAAAkG,yBAAAhG,CAAA;MAAA,YAAAA,CAAA,IAAyFyF,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAAChE,IAAI,kBAxL+EhI,EAAE,CAAAiI,iBAAA;MAAAvB,IAAA,EAwLJsF,gBAAgB;MAAArF,SAAA;MAAA6F,SAAA,WAAAC,uBAAApK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxLdrC,EAAE,CAAA0M,WAAA,CAwLiUjD,WAAW;UAxL9UzJ,EAAE,CAAA0M,WAAA,CAwL2bV,gBAAgB;QAAA;QAAA,IAAA3J,EAAA;UAAA,IAAAiI,EAAA;UAxL7ctK,EAAE,CAAAuK,cAAA,CAAAD,EAAA,GAAFtK,EAAE,CAAAwK,WAAA,QAAAlI,GAAA,CAAA4D,IAAA,GAAAoE,EAAA,CAAAqC,KAAA;UAAF3M,EAAE,CAAAuK,cAAA,CAAAD,EAAA,GAAFtK,EAAE,CAAAwK,WAAA,QAAAlI,GAAA,CAAAgK,KAAA,GAAAhC,EAAA,CAAAqC,KAAA;QAAA;MAAA;MAAAlC,SAAA,WAwL0J,MAAM;MAAA7D,MAAA;QAAAtB,KAAA;QAAAI,OAAA;QAAAN,OAAA;MAAA;MAAAyB,UAAA;MAAAwB,QAAA,GAxLlKrI,EAAE,CAAA2K,kBAAA,CAwLiL,CAAC5J,UAAU,CAACoI,aAAa,EAAE6C,gBAAgB,CAAC,CAAC,GAxLhOhM,EAAE,CAAAsI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAxE,QAAA,WAAA2I,0BAAAvK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrC,EAAE,CAAAgF,UAAA,IAAAW,yCAAA,0BAwLumB,CAAC;UAxL1mB3F,EAAE,CAAA6M,MAAA;QAAA;QAAA,IAAAxK,EAAA;UAAFrC,EAAE,CAAAsD,UAAA,WAAFtD,EAAE,CAAA8M,WAAA,OAAAxK,GAAA,CAAA4J,SAAA,CAwL+iB,CAAC;QAAA;MAAA;MAAAvD,YAAA,GAA2jBqD,gBAAgB,EAAyFlM,SAAS,EAA8CC,OAAO,EAAmHH,IAAI,EAA6FuB,kBAAkB,EAA8HN,MAAM,EAAyE4I,WAAW,EAA0D6B,WAAW;MAAA1C,MAAA;MAAAC,eAAA;IAAA,EAA2H;EAAE;AACz/D;AACA;EAAA,QAAA/B,SAAA,oBAAAA,SAAA,KA1LqG9G,EAAE,CAAA+G,iBAAA,CA0LXiF,gBAAgB,EAAc,CAAC;IAC/GtF,IAAI,EAAErG,SAAS;IACf2G,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,IAAI;MAAEI,QAAQ,EAAE,UAAU;MAAE6B,OAAO,EAAE,CAC9ChJ,SAAS,EACTC,OAAO,EACPH,IAAI,EACJuB,kBAAkB,EAClBN,MAAM,EACN4I,WAAW,EACX6B,WAAW,CACd;MAAEzC,eAAe,EAAEvI,uBAAuB,CAACyI,MAAM;MAAEqC,SAAS,EAAE,CAACrK,UAAU,CAACoI,aAAa,EAAE6C,gBAAgB,CAAC,CAAC;MAAEhD,IAAI,EAAE;QAAEqC,IAAI,EAAE;MAAO,CAAC;MAAEpH,QAAQ,EAAE,0gBAA0gB;MAAE2E,MAAM,EAAE,CAAC,0CAA0C;IAAE,CAAC;EAC9tB,CAAC,CAAC,QAAkB;IAAE1C,IAAI,EAAE,CAAC;MACrBQ,IAAI,EAAEjG,SAAS;MACfuG,IAAI,EAAE,CAAC5G,UAAU,CAAC,MAAMqJ,WAAW,CAAC;IACxC,CAAC,CAAC;IAAE6C,KAAK,EAAE,CAAC;MACR5F,IAAI,EAAEjG,SAAS;MACfuG,IAAI,EAAE,CAAC5G,UAAU,CAAC,MAAM4L,gBAAgB,CAAC;IAC7C,CAAC,CAAC;IAAE1G,KAAK,EAAE,CAAC;MACRoB,IAAI,EAAExG,KAAK;MACX8G,IAAI,EAAE,CAAC;QACC+F,QAAQ,EAAE;MACd,CAAC;IACT,CAAC,CAAC;IAAErH,OAAO,EAAE,CAAC;MACVgB,IAAI,EAAExG;IACV,CAAC,CAAC;IAAEkF,OAAO,EAAE,CAAC;MACVsB,IAAI,EAAExG;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8M,0BAA0B,CAAC;EAC7BjH,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkH,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC7L,GAAG,GAAG,IAAI4L,GAAG,CAAC,CAAC;IACpB,IAAI,CAACE,OAAO,GAAG,IAAI1M,YAAY,CAAC,CAAC;EACrC;EACAgL,QAAQA,CAACxF,IAAI,EAAEZ,KAAK,EAAE;IAClB,IAAI,CAAC2H,KAAK,CAACI,GAAG,CAACnH,IAAI,EAAEZ,KAAK,CAAC;EAC/B;EACAsG,UAAUA,CAAC1F,IAAI,EAAE;IACb,IAAI,CAAC+G,KAAK,CAACK,MAAM,CAACpH,IAAI,CAAC;EAC3B;EACAsB,UAAUA,CAACtB,IAAI,EAAE;IACb,MAAMZ,KAAK,GAAG,IAAI,CAAC2H,KAAK,CAACM,GAAG,CAACrH,IAAI,CAAC;IAClC,OAAO,CAACZ,KAAK,IAAI,IAAI,CAAChE,GAAG,CAACiM,GAAG,CAACjI,KAAK,CAAC,KAAK,IAAI,CAAC6H,QAAQ;EAC1D;EACArF,MAAMA,CAAC5B,IAAI,EAAE;IACT,MAAMZ,KAAK,GAAG,IAAI,CAAC2H,KAAK,CAACM,GAAG,CAACrH,IAAI,CAAC;IAClC,MAAM7C,QAAQ,GAAG,IAAI,CAACmE,UAAU,CAACtB,IAAI,CAAC;IACtC,IAAI,CAAClF,YAAY,CAACsE,KAAK,CAAC,EAAE;MACtB;IACJ;IACA,IAAI,CAAC8H,OAAO,CAACI,IAAI,CAAClI,KAAK,CAAC;IACxB,IAAI,CAAChE,GAAG,CAAC+L,GAAG,CAAC/H,KAAK,EAAE,CAACjC,QAAQ,CAAC;EAClC;EACA;IAAS,IAAI,CAACgD,IAAI,YAAAoH,mCAAAlH,CAAA;MAAA,YAAAA,CAAA,IAAyFyG,0BAA0B;IAAA,CAAmD;EAAE;EAC1L;IAAS,IAAI,CAACxG,IAAI,kBAjP+ExG,EAAE,CAAAyG,iBAAA;MAAAC,IAAA,EAiPJsG,0BAA0B;MAAArG,SAAA;MAAAC,MAAA;QAAAuG,QAAA,GAjPxBnN,EAAE,CAAA8L,YAAA,CAAAC,IAAA;QAAAzK,GAAA;MAAA;MAAAoM,OAAA;QAAAN,OAAA;MAAA;MAAAO,QAAA;MAAA9G,UAAA;MAAAwB,QAAA,GAAFrI,EAAE,CAAA2K,kBAAA,CAiPmM,CAC9R5J,UAAU,CAACmI,iBAAiB,EAAE8D,0BAA0B,CAAC,EACzDjM,UAAU,CAACqG,mBAAmB,EAAE4F,0BAA0B,CAAC,CAC9D;IAAA,EAAkD;EAAE;AAC7D;AACA;EAAA,QAAAlG,SAAA,oBAAAA,SAAA,KAtPqG9G,EAAE,CAAA+G,iBAAA,CAsPXiG,0BAA0B,EAAc,CAAC;IACzHtG,IAAI,EAAEzG,SAAS;IACf+G,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,0BAA0B;MACpCmE,SAAS,EAAE,CACPrK,UAAU,CAACmI,iBAAiB,EAAE8D,0BAA0B,CAAC,EACzDjM,UAAU,CAACqG,mBAAmB,EAAE4F,0BAA0B,CAAC,CAC9D;MACDW,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAER,QAAQ,EAAE,CAAC;MACzBzG,IAAI,EAAExG,KAAK;MACX8G,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE1F,GAAG,EAAE,CAAC;MACNoF,IAAI,EAAExG;IACV,CAAC,CAAC;IAAEkN,OAAO,EAAE,CAAC;MACV1G,IAAI,EAAE/F;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiN,qBAAqB,CAAC;EACxB7H,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzE,GAAG,GAAG,IAAIuM,OAAO,CAAC,CAAC;IACxB,IAAI,CAACV,QAAQ,GAAG,IAAI;EACxB;EACA3F,UAAUA,CAACtB,IAAI,EAAE;IACb,OAAO,IAAI,CAAC5E,GAAG,CAACiM,GAAG,CAACrH,IAAI,CAAC,IAAI,IAAI,CAACiH,QAAQ;EAC9C;EACArF,MAAMA,CAAC5B,IAAI,EAAE;IACT,IAAI,CAAC5E,GAAG,CAAC+L,GAAG,CAACnH,IAAI,EAAE,CAAC,IAAI,CAACsB,UAAU,CAACtB,IAAI,CAAC,CAAC;EAC9C;EACA;IAAS,IAAI,CAACG,IAAI,YAAAyH,8BAAAvH,CAAA;MAAA,YAAAA,CAAA,IAAyFqH,qBAAqB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAACpH,IAAI,kBAtR+ExG,EAAE,CAAAyG,iBAAA;MAAAC,IAAA,EAsRJkH,qBAAqB;MAAAjH,SAAA;MAAAC,MAAA;QAAAuG,QAAA,GAtRnBnN,EAAE,CAAA8L,YAAA,CAAAC,IAAA;MAAA;MAAA4B,QAAA;MAAA9G,UAAA;MAAAwB,QAAA,GAAFrI,EAAE,CAAA2K,kBAAA,CAsRuJ,CAAC5J,UAAU,CAACqG,mBAAmB,EAAEwG,qBAAqB,CAAC,CAAC;IAAA,EAAkD;EAAE;AAC1W;AACA;EAAA,QAAA9G,SAAA,oBAAAA,SAAA,KAxRqG9G,EAAE,CAAA+G,iBAAA,CAwRX6G,qBAAqB,EAAc,CAAC;IACpHlH,IAAI,EAAEzG,SAAS;IACf+G,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,gCAAgC;MAC1CmE,SAAS,EAAE,CAACrK,UAAU,CAACqG,mBAAmB,EAAEwG,qBAAqB,CAAC,CAAC;MACnED,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAER,QAAQ,EAAE,CAAC;MACzBzG,IAAI,EAAExG,KAAK;MACX8G,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+G,cAAc,CAAC;EACjBhI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiI,OAAO,GAAG7N,MAAM,CAACiJ,gBAAgB,CAAC;IACvC,IAAI,CAAC6E,KAAK,GAAG9N,MAAM,CAACkJ,cAAc,CAAC;IACnC,IAAI,CAAC6E,MAAM,GAAG/N,MAAM,CAACmJ,eAAe,CAAC;IACrC,IAAI,CAAChI,GAAG,GAAG,IAAI4L,GAAG,CAAC,CAAC,CAAC,IAAI,CAACc,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACxC,IAAI,CAACG,KAAK,GAAG,IAAI/M,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACgN,KAAK,GAAG,IAAI,CAACD,KAAK,CAAC5G,IAAI,CAAC/F,QAAQ,CAAE0E,IAAI,IAAK,IAAI,CAACgI,MAAM,CAACG,YAAY,CAACnI,IAAI,CAAC,CAACqB,IAAI,CAAC9F,GAAG,CAAE6M,QAAQ,IAAK,IAAI,CAAChN,GAAG,CAAC+L,GAAG,CAACnH,IAAI,EAAEoI,QAAQ,CAAC,CAAC,EAAEhN,GAAG,CAAEgN,QAAQ,IAAKA,QAAQ,CAACC,MAAM,CAAErI,IAAI,IAAK,CAAC,IAAI,CAACgI,MAAM,CAACM,WAAW,CAACtI,IAAI,CAAC,CAAC,CAAC,EAAEzE,GAAG,CAAE6M,QAAQ,IAAKA,QAAQ,CAACG,OAAO,CAAEnC,KAAK,IAAK,IAAI,CAAChL,GAAG,CAAC+L,GAAG,CAACf,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEjL,SAAS,CAAC,IAAI,CAAC,EAAEC,GAAG,CAAC,MAAM,IAAI,CAAC2M,KAAK,CAAC,CAAC;EACzU;EACAS,WAAWA,CAACxI,IAAI,EAAE;IACd,OAAO,IAAI,CAAC5E,GAAG,CAACiM,GAAG,CAACrH,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC8H,OAAO,CAAC;EAC/C;EACAK,YAAYA,CAACnI,IAAI,EAAE;IACf,IAAI,IAAI,CAAC5E,GAAG,CAACiM,GAAG,CAACrH,IAAI,CAAC,EAAE;MACpB;IACJ;IACA,IAAI,CAAC5E,GAAG,CAAC+L,GAAG,CAACnH,IAAI,EAAE,CAAC,IAAI,CAAC8H,OAAO,CAAC,CAAC;IAClC,IAAI,CAACG,KAAK,CAACxG,IAAI,CAACzB,IAAI,CAAC;EACzB;EACA;IAAS,IAAI,CAACG,IAAI,YAAAsI,uBAAApI,CAAA;MAAA,YAAAA,CAAA,IAAyFwH,cAAc;IAAA,CAAoD;EAAE;EAC/K;IAAS,IAAI,CAACa,KAAK,kBAzT8E5O,EAAE,CAAA6O,kBAAA;MAAAC,KAAA,EAyTYf,cAAc;MAAAgB,OAAA,EAAdhB,cAAc,CAAA1H;IAAA,EAAG;EAAE;AACtI;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA3TqG9G,EAAE,CAAA+G,iBAAA,CA2TXgH,cAAc,EAAc,CAAC;IAC7GrH,IAAI,EAAE9F;EACV,CAAC,CAAC;AAAA;AAEV,MAAMoO,OAAO,GAAG,CACZhD,gBAAgB,EAChBvC,WAAW,EACXvC,kBAAkB,EAClBpB,eAAe,EACf8H,qBAAqB,EACrBZ,0BAA0B,EAC1B1B,WAAW,CACd;;AAED;AACA;AACA;;AAEA,SAASzD,2BAA2B,EAAEqB,iBAAiB,EAAEK,gBAAgB,EAAEnC,mBAAmB,EAAE6B,qBAAqB,EAAEO,cAAc,EAAEF,eAAe,EAAEF,gBAAgB,EAAED,aAAa,EAAEE,cAAc,EAAE2F,OAAO,EAAElJ,eAAe,EAAEkG,gBAAgB,EAAEgB,0BAA0B,EAAEvD,WAAW,EAAEvC,kBAAkB,EAAE0G,qBAAqB,EAAEtC,WAAW,EAAEyC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}