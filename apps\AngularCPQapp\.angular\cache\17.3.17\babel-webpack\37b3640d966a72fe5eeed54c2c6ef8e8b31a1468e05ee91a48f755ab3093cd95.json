{"ast": null, "code": "import { isPlatformServer } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Injectable, PLATFORM_ID, signal, Directive, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { IntersectionObserverService } from '@ng-web-apis/intersection-observer';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiWatch } from '@taiga-ui/cdk/observables';\nimport { Observable, Subject, switchMap, filter, map, take } from 'rxjs';\nclass TuiLazyLoadingService extends Observable {\n  constructor() {\n    super(subscriber => this.stream$.subscribe(subscriber));\n    this.src$ = new Subject();\n    this.intersections$ = inject(IntersectionObserverService);\n    this.stream$ = this.src$.pipe(switchMap(src => this.intersections$.pipe(filter(entry => !!entry[0]?.isIntersecting), map(() => src), take(1))), tuiWatch());\n  }\n  next(src) {\n    this.src$.next(src);\n  }\n  static {\n    this.ɵfac = function TuiLazyLoadingService_Factory(t) {\n      return new (t || TuiLazyLoadingService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiLazyLoadingService,\n      factory: TuiLazyLoadingService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiLazyLoadingService, [{\n    type: Injectable\n  }], function () {\n    return [];\n  }, null);\n})();\n\n/**\n * @deprecated: Drop in v5.0\n */\nclass TuiImgLazyLoading {\n  constructor() {\n    this.isServer = isPlatformServer(inject(PLATFORM_ID));\n    this.loading$ = inject(TuiLazyLoadingService);\n    this.supported = 'loading' in tuiInjectElement();\n    this.src = signal(null);\n    this.background = signal(this.isServer ? '' : 'var(--tui-background-neutral-2)');\n    this.animation = signal(this.isServer ? '' : 'tuiSkeletonVibe ease-in-out 1s infinite alternate');\n    this.$ = !this.supported && this.loading$.pipe(takeUntilDestroyed()).subscribe(src => this.src.set(src));\n  }\n  set srcSetter(src) {\n    if (this.supported) {\n      this.src.set(src);\n    } else {\n      this.loading$.next(src);\n    }\n  }\n  unset() {\n    this.background.set('');\n    this.animation.set('');\n  }\n  static {\n    this.ɵfac = function TuiImgLazyLoading_Factory(t) {\n      return new (t || TuiImgLazyLoading)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiImgLazyLoading,\n      selectors: [[\"img\", \"loading\", \"lazy\"], [\"img\", \"tuiLoading\", \"lazy\"]],\n      hostVars: 6,\n      hostBindings: function TuiImgLazyLoading_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"load\", function TuiImgLazyLoading_load_HostBindingHandler() {\n            return ctx.unset();\n          })(\"error\", function TuiImgLazyLoading_error_HostBindingHandler() {\n            return ctx.unset();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"loading\", ctx.supported ? \"lazy\" : null)(\"src\", ctx.src(), i0.ɵɵsanitizeUrl);\n          i0.ɵɵstyleProp(\"animation\", ctx.animation())(\"background\", ctx.background());\n        }\n      },\n      inputs: {\n        srcSetter: [i0.ɵɵInputFlags.None, \"src\", \"srcSetter\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiLazyLoadingService, IntersectionObserverService])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiImgLazyLoading, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'img[loading=\"lazy\"],img[tuiLoading=\"lazy\"]',\n      providers: [TuiLazyLoadingService, IntersectionObserverService],\n      host: {\n        '[style.animation]': 'animation()',\n        '[style.background]': 'background()',\n        '[attr.loading]': 'supported ? \"lazy\" : null',\n        '[attr.src]': 'src()',\n        '(load)': 'unset()',\n        '(error)': 'unset()'\n      }\n    }]\n  }], null, {\n    srcSetter: [{\n      type: Input,\n      args: ['src']\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiImgLazyLoading, TuiLazyLoadingService };", "map": {"version": 3, "names": ["isPlatformServer", "i0", "inject", "Injectable", "PLATFORM_ID", "signal", "Directive", "Input", "takeUntilDestroyed", "IntersectionObserverService", "tuiInjectElement", "tuiWatch", "Observable", "Subject", "switchMap", "filter", "map", "take", "TuiLazyLoadingService", "constructor", "subscriber", "stream$", "subscribe", "src$", "intersections$", "pipe", "src", "entry", "isIntersecting", "next", "ɵfac", "TuiLazyLoadingService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "TuiImgLazyLoading", "isServer", "loading$", "supported", "background", "animation", "$", "set", "srcSetter", "unset", "TuiImgLazyLoading_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "hostVars", "hostBindings", "TuiImgLazyLoading_HostBindings", "rf", "ctx", "ɵɵlistener", "TuiImgLazyLoading_load_HostBindingHandler", "TuiImgLazyLoading_error_HostBindingHandler", "ɵɵattribute", "ɵɵsanitizeUrl", "ɵɵstyleProp", "inputs", "ɵɵInputFlags", "None", "standalone", "features", "ɵɵProvidersFeature", "args", "selector", "providers", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-lazy-loading.mjs"], "sourcesContent": ["import { isPlatformServer } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Injectable, PLATFORM_ID, signal, Directive, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { IntersectionObserverService } from '@ng-web-apis/intersection-observer';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiWatch } from '@taiga-ui/cdk/observables';\nimport { Observable, Subject, switchMap, filter, map, take } from 'rxjs';\n\nclass TuiLazyLoadingService extends Observable {\n    constructor() {\n        super((subscriber) => this.stream$.subscribe(subscriber));\n        this.src$ = new Subject();\n        this.intersections$ = inject(IntersectionObserverService);\n        this.stream$ = this.src$.pipe(switchMap((src) => this.intersections$.pipe(filter((entry) => !!entry[0]?.isIntersecting), map(() => src), take(1))), tuiWatch());\n    }\n    next(src) {\n        this.src$.next(src);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLazyLoadingService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLazyLoadingService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiLazyLoadingService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return []; } });\n\n/**\n * @deprecated: Drop in v5.0\n */\nclass TuiImgLazyLoading {\n    constructor() {\n        this.isServer = isPlatformServer(inject(PLATFORM_ID));\n        this.loading$ = inject(TuiLazyLoadingService);\n        this.supported = 'loading' in tuiInjectElement();\n        this.src = signal(null);\n        this.background = signal(this.isServer ? '' : 'var(--tui-background-neutral-2)');\n        this.animation = signal(this.isServer ? '' : 'tuiSkeletonVibe ease-in-out 1s infinite alternate');\n        this.$ = !this.supported &&\n            this.loading$.pipe(takeUntilDestroyed()).subscribe((src) => this.src.set(src));\n    }\n    set srcSetter(src) {\n        if (this.supported) {\n            this.src.set(src);\n        }\n        else {\n            this.loading$.next(src);\n        }\n    }\n    unset() {\n        this.background.set('');\n        this.animation.set('');\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiImgLazyLoading, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiImgLazyLoading, isStandalone: true, selector: \"img[loading=\\\"lazy\\\"],img[tuiLoading=\\\"lazy\\\"]\", inputs: { srcSetter: [\"src\", \"srcSetter\"] }, host: { listeners: { \"load\": \"unset()\", \"error\": \"unset()\" }, properties: { \"style.animation\": \"animation()\", \"style.background\": \"background()\", \"attr.loading\": \"supported ? \\\"lazy\\\" : null\", \"attr.src\": \"src()\" } }, providers: [TuiLazyLoadingService, IntersectionObserverService], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiImgLazyLoading, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'img[loading=\"lazy\"],img[tuiLoading=\"lazy\"]',\n                    providers: [TuiLazyLoadingService, IntersectionObserverService],\n                    host: {\n                        '[style.animation]': 'animation()',\n                        '[style.background]': 'background()',\n                        '[attr.loading]': 'supported ? \"lazy\" : null',\n                        '[attr.src]': 'src()',\n                        '(load)': 'unset()',\n                        '(error)': 'unset()',\n                    },\n                }]\n        }], propDecorators: { srcSetter: [{\n                type: Input,\n                args: ['src']\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiImgLazyLoading, TuiLazyLoadingService };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,iBAAiB;AAClD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACzF,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,2BAA2B,QAAQ,oCAAoC;AAChF,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,UAAU,EAAEC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,QAAQ,MAAM;AAExE,MAAMC,qBAAqB,SAASN,UAAU,CAAC;EAC3CO,WAAWA,CAAA,EAAG;IACV,KAAK,CAAEC,UAAU,IAAK,IAAI,CAACC,OAAO,CAACC,SAAS,CAACF,UAAU,CAAC,CAAC;IACzD,IAAI,CAACG,IAAI,GAAG,IAAIV,OAAO,CAAC,CAAC;IACzB,IAAI,CAACW,cAAc,GAAGtB,MAAM,CAACO,2BAA2B,CAAC;IACzD,IAAI,CAACY,OAAO,GAAG,IAAI,CAACE,IAAI,CAACE,IAAI,CAACX,SAAS,CAAEY,GAAG,IAAK,IAAI,CAACF,cAAc,CAACC,IAAI,CAACV,MAAM,CAAEY,KAAK,IAAK,CAAC,CAACA,KAAK,CAAC,CAAC,CAAC,EAAEC,cAAc,CAAC,EAAEZ,GAAG,CAAC,MAAMU,GAAG,CAAC,EAAET,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEN,QAAQ,CAAC,CAAC,CAAC;EACnK;EACAkB,IAAIA,CAACH,GAAG,EAAE;IACN,IAAI,CAACH,IAAI,CAACM,IAAI,CAACH,GAAG,CAAC;EACvB;EACA;IAAS,IAAI,CAACI,IAAI,YAAAC,8BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFd,qBAAqB;IAAA,CAAoD;EAAE;EACtL;IAAS,IAAI,CAACe,KAAK,kBAD8EhC,EAAE,CAAAiC,kBAAA;MAAAC,KAAA,EACYjB,qBAAqB;MAAAkB,OAAA,EAArBlB,qBAAqB,CAAAY;IAAA,EAAG;EAAE;AAC7I;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAHqGpC,EAAE,CAAAqC,iBAAA,CAGXpB,qBAAqB,EAAc,CAAC;IACpHqB,IAAI,EAAEpC;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;;AAEtD;AACA;AACA;AACA,MAAMqC,iBAAiB,CAAC;EACpBrB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsB,QAAQ,GAAGzC,gBAAgB,CAACE,MAAM,CAACE,WAAW,CAAC,CAAC;IACrD,IAAI,CAACsC,QAAQ,GAAGxC,MAAM,CAACgB,qBAAqB,CAAC;IAC7C,IAAI,CAACyB,SAAS,GAAG,SAAS,IAAIjC,gBAAgB,CAAC,CAAC;IAChD,IAAI,CAACgB,GAAG,GAAGrB,MAAM,CAAC,IAAI,CAAC;IACvB,IAAI,CAACuC,UAAU,GAAGvC,MAAM,CAAC,IAAI,CAACoC,QAAQ,GAAG,EAAE,GAAG,iCAAiC,CAAC;IAChF,IAAI,CAACI,SAAS,GAAGxC,MAAM,CAAC,IAAI,CAACoC,QAAQ,GAAG,EAAE,GAAG,mDAAmD,CAAC;IACjG,IAAI,CAACK,CAAC,GAAG,CAAC,IAAI,CAACH,SAAS,IACpB,IAAI,CAACD,QAAQ,CAACjB,IAAI,CAACjB,kBAAkB,CAAC,CAAC,CAAC,CAACc,SAAS,CAAEI,GAAG,IAAK,IAAI,CAACA,GAAG,CAACqB,GAAG,CAACrB,GAAG,CAAC,CAAC;EACtF;EACA,IAAIsB,SAASA,CAACtB,GAAG,EAAE;IACf,IAAI,IAAI,CAACiB,SAAS,EAAE;MAChB,IAAI,CAACjB,GAAG,CAACqB,GAAG,CAACrB,GAAG,CAAC;IACrB,CAAC,MACI;MACD,IAAI,CAACgB,QAAQ,CAACb,IAAI,CAACH,GAAG,CAAC;IAC3B;EACJ;EACAuB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACL,UAAU,CAACG,GAAG,CAAC,EAAE,CAAC;IACvB,IAAI,CAACF,SAAS,CAACE,GAAG,CAAC,EAAE,CAAC;EAC1B;EACA;IAAS,IAAI,CAACjB,IAAI,YAAAoB,0BAAAlB,CAAA;MAAA,YAAAA,CAAA,IAAyFQ,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAACW,IAAI,kBAlC+ElD,EAAE,CAAAmD,iBAAA;MAAAb,IAAA,EAkCJC,iBAAiB;MAAAa,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlCfxD,EAAE,CAAA0D,UAAA,kBAAAC,0CAAA;YAAA,OAkCJF,GAAA,CAAAT,KAAA,CAAM,CAAC;UAAA,CAAS,CAAC,mBAAAY,2CAAA;YAAA,OAAjBH,GAAA,CAAAT,KAAA,CAAM,CAAC;UAAA,CAAS,CAAC;QAAA;QAAA,IAAAQ,EAAA;UAlCfxD,EAAE,CAAA6D,WAAA,YAAAJ,GAAA,CAAAf,SAAA,GAkCQ,MAAM,GAAG,IAAI,SAAzBe,GAAA,CAAAhC,GAAA,CAAI,CAAC,EAlCHzB,EAAE,CAAA8D,aAAA;UAAF9D,EAAE,CAAA+D,WAAA,cAkCJN,GAAA,CAAAb,SAAA,CAAU,CAAM,CAAC,eAAjBa,GAAA,CAAAd,UAAA,CAAW,CAAK,CAAC;QAAA;MAAA;MAAAqB,MAAA;QAAAjB,SAAA,GAlCf/C,EAAE,CAAAiE,YAAA,CAAAC,IAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFpE,EAAE,CAAAqE,kBAAA,CAkCiX,CAACpD,qBAAqB,EAAET,2BAA2B,CAAC;IAAA,EAAiB;EAAE;AAC/hB;AACA;EAAA,QAAA4B,SAAA,oBAAAA,SAAA,KApCqGpC,EAAE,CAAAqC,iBAAA,CAoCXE,iBAAiB,EAAc,CAAC;IAChHD,IAAI,EAAEjC,SAAS;IACfiE,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,4CAA4C;MACtDC,SAAS,EAAE,CAACvD,qBAAqB,EAAET,2BAA2B,CAAC;MAC/DiE,IAAI,EAAE;QACF,mBAAmB,EAAE,aAAa;QAClC,oBAAoB,EAAE,cAAc;QACpC,gBAAgB,EAAE,2BAA2B;QAC7C,YAAY,EAAE,OAAO;QACrB,QAAQ,EAAE,SAAS;QACnB,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE1B,SAAS,EAAE,CAAC;MAC1BT,IAAI,EAAEhC,KAAK;MACXgE,IAAI,EAAE,CAAC,KAAK;IAChB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAAS/B,iBAAiB,EAAEtB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}