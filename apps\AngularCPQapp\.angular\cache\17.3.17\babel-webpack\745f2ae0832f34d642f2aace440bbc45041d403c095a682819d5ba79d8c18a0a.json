{"ast": null, "code": "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiTextfieldComponent } from '@taiga-ui/core/components/textfield';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { tuiIsFlat } from '@taiga-ui/kit/utils';\nclass TuiHideSelectedPipe {\n  constructor() {\n    this.textfield = inject(TuiTextfieldComponent);\n    this.handlers = inject(TUI_ITEMS_HANDLERS);\n  }\n  transform(items) {\n    if (!items) {\n      return null;\n    }\n    const value = this.textfield.control?.value || [];\n    return tuiIsFlat(items) ? this.filter(items, value, this.handlers.identityMatcher()) : this.filter2d(items, value, this.handlers.identityMatcher());\n  }\n  filter2d(items, value, matcher) {\n    return items.map(subItems => this.filter(subItems, value, matcher));\n  }\n  filter(items, value, matcher) {\n    return items.filter(item => value.every(selected => !matcher(selected, item)));\n  }\n  static {\n    this.ɵfac = function TuiHideSelectedPipe_Factory(t) {\n      return new (t || TuiHideSelectedPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiHideSelected\",\n      type: TuiHideSelectedPipe,\n      pure: false,\n      standalone: true\n    });\n  }\n}\n__decorate([tuiPure], TuiHideSelectedPipe.prototype, \"filter2d\", null);\n__decorate([tuiPure], TuiHideSelectedPipe.prototype, \"filter\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHideSelectedPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiHideSelected',\n      pure: false\n    }]\n  }], null, {\n    filter2d: [],\n    filter: []\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiHideSelectedPipe };", "map": {"version": 3, "names": ["__decorate", "i0", "inject", "<PERSON><PERSON>", "tuiPure", "TuiTextfieldComponent", "TUI_ITEMS_HANDLERS", "tui<PERSON>sFlat", "TuiHideSelectedPipe", "constructor", "textfield", "handlers", "transform", "items", "value", "control", "filter", "identityMatcher", "filter2d", "matcher", "map", "subItems", "item", "every", "selected", "ɵfac", "TuiHideSelectedPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "prototype", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-pipes-hide-selected.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiTextfieldComponent } from '@taiga-ui/core/components/textfield';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { tuiIsFlat } from '@taiga-ui/kit/utils';\n\nclass TuiHideSelectedPipe {\n    constructor() {\n        this.textfield = inject(TuiTextfieldComponent);\n        this.handlers = inject(TUI_ITEMS_HANDLERS);\n    }\n    transform(items) {\n        if (!items) {\n            return null;\n        }\n        const value = this.textfield.control?.value || [];\n        return tuiIsFlat(items)\n            ? this.filter(items, value, this.handlers.identityMatcher())\n            : this.filter2d(items, value, this.handlers.identityMatcher());\n    }\n    filter2d(items, value, matcher) {\n        return items.map((subItems) => this.filter(subItems, value, matcher));\n    }\n    filter(items, value, matcher) {\n        return items.filter((item) => value.every((selected) => !matcher(selected, item)));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHideSelectedPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHideSelectedPipe, isStandalone: true, name: \"tuiHideSelected\", pure: false }); }\n}\n__decorate([\n    tuiPure\n], TuiHideSelectedPipe.prototype, \"filter2d\", null);\n__decorate([\n    tuiPure\n], TuiHideSelectedPipe.prototype, \"filter\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHideSelectedPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiHideSelected',\n                    pure: false,\n                }]\n        }], propDecorators: { filter2d: [], filter: [] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiHideSelectedPipe };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,IAAI,QAAQ,eAAe;AAC5C,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,qBAAqB,QAAQ,qCAAqC;AAC3E,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,SAAS,QAAQ,qBAAqB;AAE/C,MAAMC,mBAAmB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,SAAS,GAAGR,MAAM,CAACG,qBAAqB,CAAC;IAC9C,IAAI,CAACM,QAAQ,GAAGT,MAAM,CAACI,kBAAkB,CAAC;EAC9C;EACAM,SAASA,CAACC,KAAK,EAAE;IACb,IAAI,CAACA,KAAK,EAAE;MACR,OAAO,IAAI;IACf;IACA,MAAMC,KAAK,GAAG,IAAI,CAACJ,SAAS,CAACK,OAAO,EAAED,KAAK,IAAI,EAAE;IACjD,OAAOP,SAAS,CAACM,KAAK,CAAC,GACjB,IAAI,CAACG,MAAM,CAACH,KAAK,EAAEC,KAAK,EAAE,IAAI,CAACH,QAAQ,CAACM,eAAe,CAAC,CAAC,CAAC,GAC1D,IAAI,CAACC,QAAQ,CAACL,KAAK,EAAEC,KAAK,EAAE,IAAI,CAACH,QAAQ,CAACM,eAAe,CAAC,CAAC,CAAC;EACtE;EACAC,QAAQA,CAACL,KAAK,EAAEC,KAAK,EAAEK,OAAO,EAAE;IAC5B,OAAON,KAAK,CAACO,GAAG,CAAEC,QAAQ,IAAK,IAAI,CAACL,MAAM,CAACK,QAAQ,EAAEP,KAAK,EAAEK,OAAO,CAAC,CAAC;EACzE;EACAH,MAAMA,CAACH,KAAK,EAAEC,KAAK,EAAEK,OAAO,EAAE;IAC1B,OAAON,KAAK,CAACG,MAAM,CAAEM,IAAI,IAAKR,KAAK,CAACS,KAAK,CAAEC,QAAQ,IAAK,CAACL,OAAO,CAACK,QAAQ,EAAEF,IAAI,CAAC,CAAC,CAAC;EACtF;EACA;IAAS,IAAI,CAACG,IAAI,YAAAC,4BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFnB,mBAAmB;IAAA,CAA8C;EAAE;EAC9K;IAAS,IAAI,CAACoB,KAAK,kBAD8E3B,EAAE,CAAA4B,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMvB,mBAAmB;MAAAwB,IAAA;MAAAC,UAAA;IAAA,EAA6D;EAAE;AAC/L;AACAjC,UAAU,CAAC,CACPI,OAAO,CACV,EAAEI,mBAAmB,CAAC0B,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC;AACnDlC,UAAU,CAAC,CACPI,OAAO,CACV,EAAEI,mBAAmB,CAAC0B,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;AACjD;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KATqGlC,EAAE,CAAAmC,iBAAA,CASX5B,mBAAmB,EAAc,CAAC;IAClHuB,IAAI,EAAE5B,IAAI;IACVkC,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE,iBAAiB;MACvBE,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEd,QAAQ,EAAE,EAAE;IAAEF,MAAM,EAAE;EAAG,CAAC;AAAA;;AAExD;AACA;AACA;;AAEA,SAASR,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}