{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { TUI_MONTHS } from '@taiga-ui/core/tokens';\nimport { map } from 'rxjs';\nclass TuiMonthPipe {\n  constructor() {\n    this.months$ = inject(TUI_MONTHS);\n  }\n  transform({\n    month\n  }) {\n    return this.months$.pipe(map(months => months[month] || months[0]));\n  }\n  static {\n    this.ɵfac = function TuiMonthPipe_Factory(t) {\n      return new (t || TuiMonthPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiMont<PERSON>\",\n      type: TuiMonthPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiMonthPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tui<PERSON><PERSON><PERSON>'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiMonthPipe };", "map": {"version": 3, "names": ["i0", "inject", "<PERSON><PERSON>", "TUI_MONTHS", "map", "TuiMonthPipe", "constructor", "months$", "transform", "month", "pipe", "months", "ɵfac", "TuiMonthPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-pipes-month.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { TUI_MONTHS } from '@taiga-ui/core/tokens';\nimport { map } from 'rxjs';\n\nclass TuiMonthPipe {\n    constructor() {\n        this.months$ = inject(TUI_MONTHS);\n    }\n    transform({ month }) {\n        return this.months$.pipe(map((months) => months[month] || months[0]));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Tui<PERSON>onthPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMonthPipe, isStandalone: true, name: \"tui<PERSON>onth\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: <PERSON><PERSON><PERSON><PERSON>h<PERSON>ipe, decorators: [{\n            type: <PERSON><PERSON>,\n            args: [{\n                    standalone: true,\n                    name: 'tui<PERSON>onth',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiMonthPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,IAAI,QAAQ,eAAe;AAC5C,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,GAAG,QAAQ,MAAM;AAE1B,MAAMC,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGN,MAAM,CAACE,UAAU,CAAC;EACrC;EACAK,SAASA,CAAC;IAAEC;EAAM,CAAC,EAAE;IACjB,OAAO,IAAI,CAACF,OAAO,CAACG,IAAI,CAACN,GAAG,CAAEO,MAAM,IAAKA,MAAM,CAACF,KAAK,CAAC,IAAIE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACzE;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,qBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFT,YAAY;IAAA,CAA8C;EAAE;EACvK;IAAS,IAAI,CAACU,KAAK,kBAD8Ef,EAAE,CAAAgB,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMb,YAAY;MAAAc,IAAA;MAAAC,UAAA;IAAA,EAAyC;EAAE;AACpK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGrB,EAAE,CAAAsB,iBAAA,CAGXjB,YAAY,EAAc,CAAC;IAC3Ga,IAAI,EAAEhB,IAAI;IACVqB,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASZ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}