{"ast": null, "code": "import { __decorate } from 'tslib';\nimport { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, computed, EventEmitter, signal, Component, ChangeDetectionStrategy, Input, Output } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { TuiDay, TuiMonthRange, TuiMonth, TUI_FIRST_DAY, TUI_LAST_DAY, TuiYear } from '@taiga-ui/cdk/date-time';\nimport { TuiHovered } from '@taiga-ui/cdk/directives/hovered';\nimport { TuiLet } from '@taiga-ui/cdk/directives/let';\nimport { TuiRepeatTimes } from '@taiga-ui/cdk/directives/repeat-times';\nimport { tuiNullableSame, tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiCalendarYear } from '@taiga-ui/core/components/calendar';\nimport { TuiLink } from '@taiga-ui/core/components/link';\nimport { TuiScrollbar } from '@taiga-ui/core/components/scrollbar';\nimport { TuiSpinButton } from '@taiga-ui/core/components/spin-button';\nimport { tuiAsAuxiliary } from '@taiga-ui/core/tokens';\nimport { TUI_CALENDAR_MONTHS } from '@taiga-ui/kit/tokens';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nfunction TuiCalendarMonth_tui_scrollbar_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tui-scrollbar\", 2)(1, \"tui-calendar-year\", 3);\n    i0.ɵɵlistener(\"yearClick\", function TuiCalendarMonth_tui_scrollbar_0_Template_tui_calendar_year_yearClick_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPickerYearClick($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"initialItem\", ctx_r1.year.year)(\"max\", ctx_r1.max().year)(\"min\", ctx_r1.min().year)(\"rangeMode\", ctx_r1.options.rangeMode)(\"value\", ctx_r1.value());\n  }\n}\nfunction TuiCalendarMonth_ng_template_1_div_3_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function TuiCalendarMonth_ng_template_1_div_3_ng_container_1_div_1_Template_div_click_0_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).tuiLet;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onItemClick(item_r5));\n    })(\"tuiHoveredChange\", function TuiCalendarMonth_ng_template_1_div_3_ng_container_1_div_1_Template_div_tuiHoveredChange_0_listener($event) {\n      const item_r5 = i0.ɵɵrestoreView(_r4).tuiLet;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onItemHovered($event, item_r5));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    const item_r5 = ctx.tuiLet;\n    const column_r6 = i0.ɵɵnextContext().$implicit;\n    const row_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"t-cell_disabled\", ctx_r1.disabledItemHandlerWithMinMax(item_r5))(\"t-cell_today\", ctx_r1.isItemToday(item_r5));\n    i0.ɵɵattribute(\"data-range\", ctx_r1.getItemRange(item_r5));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_11_0 = ctx_r1.months()) == null ? null : tmp_11_0[row_r7 * 4 + column_r6], \" \");\n  }\n}\nfunction TuiCalendarMonth_ng_template_1_div_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiCalendarMonth_ng_template_1_div_3_ng_container_1_div_1_Template, 2, 6, \"div\", 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r6 = ctx.$implicit;\n    const row_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tuiLet\", ctx_r1.getTuiMonth(row_r7 * 4 + column_r6, ctx_r1.year.year));\n  }\n}\nfunction TuiCalendarMonth_ng_template_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, TuiCalendarMonth_ng_template_1_div_3_ng_container_1_Template, 2, 1, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tuiRepeatTimesOf\", 4);\n  }\n}\nfunction TuiCalendarMonth_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tui-spin-button\", 4);\n    i0.ɵɵlistener(\"leftClick\", function TuiCalendarMonth_ng_template_1_Template_tui_spin_button_leftClick_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPreviousYear());\n    })(\"rightClick\", function TuiCalendarMonth_ng_template_1_Template_tui_spin_button_rightClick_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onNextYear());\n    });\n    i0.ɵɵelementStart(1, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function TuiCalendarMonth_ng_template_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onYearClick());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(3, TuiCalendarMonth_ng_template_1_div_3_Template, 2, 1, \"div\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"focusable\", false)(\"leftDisabled\", ctx_r1.year.yearSameOrBefore(ctx_r1.min()))(\"rightDisabled\", ctx_r1.year.yearSameOrAfter(ctx_r1.max()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.year.formattedYear, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tuiRepeatTimesOf\", 3);\n  }\n}\nconst TUI_CALENDAR_MONTH_DEFAULT_OPTIONS = {\n  rangeMode: false\n};\nconst [TUI_CALENDAR_MONTH_OPTIONS, tuiCalendarMonthOptionsProvider] = tuiCreateOptions(TUI_CALENDAR_MONTH_DEFAULT_OPTIONS);\nconst TODAY = TuiDay.currentLocal();\nclass TuiCalendarMonth {\n  constructor() {\n    this.isYearPickerShown = false;\n    this.months = toSignal(inject(TUI_CALENDAR_MONTHS));\n    this.isRangePicking = computed((x = this.value()) => !this.options.rangeMode && x instanceof TuiMonthRange && x.isSingleMonth ||\n    // TODO(v5): remove this condition\n    this.options.rangeMode && x instanceof TuiMonth);\n    this.year = TODAY;\n    this.disabledItemHandler = TUI_FALSE_HANDLER;\n    this.minLength = null;\n    this.maxLength = null;\n    this.monthClick = new EventEmitter();\n    this.hoveredItemChange = new EventEmitter();\n    this.yearChange = new EventEmitter();\n    this.options = inject(TUI_CALENDAR_MONTH_OPTIONS);\n    this.min = signal(TUI_FIRST_DAY);\n    this.max = signal(TUI_LAST_DAY);\n    this.value = signal(null);\n    this.hoveredItem = null;\n  }\n  // TODO(v5): use signal inputs\n  set minSetter(x) {\n    this.min.set(x);\n  }\n  // TODO(v5): use signal inputs\n  set maxSetter(x) {\n    this.max.set(x);\n  }\n  // TODO(v5): use signal inputs\n  set valueSetter(x) {\n    this.value.set(x);\n  }\n  onNextYear() {\n    this.updateActiveYear(this.year.append({\n      year: 1\n    }));\n  }\n  onPreviousYear() {\n    this.updateActiveYear(this.year.append({\n      year: -1\n    }));\n  }\n  getItemRange(item) {\n    const value = this.value();\n    const {\n      hoveredItem\n    } = this;\n    if (!value) {\n      return null;\n    }\n    if (!this.options.rangeMode && value instanceof TuiMonth) {\n      return value?.monthSame(item) ? 'active' : null;\n    }\n    const selectedRange = value instanceof TuiMonth ? new TuiMonthRange(value, value) : value;\n    const months = item.month + item.year * 12;\n    const hovered = hoveredItem ? hoveredItem.month + hoveredItem.year * 12 : null;\n    const from = selectedRange.from.month + selectedRange.from.year * 12;\n    const to = selectedRange.to.month + selectedRange.to.year * 12;\n    const picking = this.isRangePicking() ? hovered : null;\n    const min = Math.min(from, to, picking ?? from);\n    const max = Math.max(from, to, picking ?? from);\n    if (min === max && min === months) {\n      return 'active';\n    }\n    if (min === months) {\n      return 'start';\n    }\n    if (max === months) {\n      return 'end';\n    }\n    return min < months && months < max ? 'middle' : null;\n  }\n  get disabledItemHandlerWithMinMax() {\n    return this.calculateDisabledItemHandlerWithMinMax(this.disabledItemHandler, this.value(), this.isRangePicking(), this.min(), this.max(), this.minLength, this.maxLength);\n  }\n  getTuiMonth(monthNumber, yearNumber) {\n    return new TuiMonth(yearNumber, monthNumber);\n  }\n  isItemToday(item) {\n    return TODAY.monthSame(item);\n  }\n  onPickerYearClick(year) {\n    this.isYearPickerShown = false;\n    if (this.year.year !== year) {\n      this.updateActiveYear(new TuiYear(year));\n    }\n  }\n  onItemClick(month) {\n    if (!this.disabledItemHandlerWithMinMax(month)) {\n      this.monthClick.emit(month);\n    }\n  }\n  onYearClick() {\n    this.isYearPickerShown = true;\n  }\n  onItemHovered(hovered, item) {\n    this.updateHoveredItem(hovered ? item : null);\n  }\n  // eslint-disable-next-line @typescript-eslint/max-params,max-params\n  calculateDisabledItemHandlerWithMinMax(disabledItemHandler, value, isRangePicking, min, max, minLength, maxLength) {\n    return item => {\n      const selectedMonth = value instanceof TuiMonthRange ? value.from : value;\n      const delta = isRangePicking && selectedMonth ? Math.abs(item.year * 12 + item.month - selectedMonth.year * 12 - selectedMonth.month) : 0;\n      const tooLong = delta && maxLength && delta > maxLength;\n      const tooShort = delta && minLength && delta < minLength;\n      return tooLong || tooShort || item.monthBefore(min) || item.monthAfter(max) || disabledItemHandler(item);\n    };\n  }\n  updateHoveredItem(month) {\n    if (tuiNullableSame(this.hoveredItem, month, (a, b) => a.monthSame(b))) {\n      return;\n    }\n    this.hoveredItem = month;\n    this.hoveredItemChange.emit(month);\n  }\n  updateActiveYear(year) {\n    this.year = year;\n    this.yearChange.emit(year);\n  }\n  static {\n    this.ɵfac = function TuiCalendarMonth_Factory(t) {\n      return new (t || TuiCalendarMonth)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiCalendarMonth,\n      selectors: [[\"tui-calendar-month\"]],\n      hostVars: 2,\n      hostBindings: function TuiCalendarMonth_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_picking\", ctx.isRangePicking());\n        }\n      },\n      inputs: {\n        year: \"year\",\n        disabledItemHandler: \"disabledItemHandler\",\n        minLength: \"minLength\",\n        maxLength: \"maxLength\",\n        minSetter: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"min\", \"minSetter\", x => x ?? TUI_FIRST_DAY],\n        maxSetter: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"max\", \"maxSetter\", x => x ?? TUI_LAST_DAY],\n        valueSetter: [i0.ɵɵInputFlags.None, \"value\", \"valueSetter\"]\n      },\n      outputs: {\n        monthClick: \"monthClick\",\n        hoveredItemChange: \"hoveredItemChange\",\n        yearChange: \"yearChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsAuxiliary(TuiCalendarMonth)]), i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[\"monthSelect\", \"\"], [\"class\", \"t-scrollbar\", 4, \"ngIf\", \"ngIfElse\"], [1, \"t-scrollbar\"], [3, \"yearClick\", \"initialItem\", \"max\", \"min\", \"rangeMode\", \"value\"], [1, \"t-spin\", 3, \"leftClick\", \"rightClick\", \"focusable\", \"leftDisabled\", \"rightDisabled\"], [\"automation-id\", \"tui-calendar-month__active-year\", \"tabIndex\", \"-1\", \"tuiLink\", \"\", \"type\", \"button\", 3, \"click\"], [\"class\", \"t-row\", 4, \"tuiRepeatTimes\", \"tuiRepeatTimesOf\"], [1, \"t-row\"], [4, \"tuiRepeatTimes\", \"tuiRepeatTimesOf\"], [\"class\", \"t-cell\", 3, \"t-cell_disabled\", \"t-cell_today\", \"click\", \"tuiHoveredChange\", 4, \"tuiLet\"], [1, \"t-cell\", 3, \"click\", \"tuiHoveredChange\"]],\n      template: function TuiCalendarMonth_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiCalendarMonth_tui_scrollbar_0_Template, 2, 5, \"tui-scrollbar\", 1)(1, TuiCalendarMonth_ng_template_1_Template, 4, 5, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const monthSelect_r8 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isYearPickerShown)(\"ngIfElse\", monthSelect_r8);\n        }\n      },\n      dependencies: [NgIf, TuiCalendarYear, TuiHovered, TuiLet, TuiLink, TuiRepeatTimes, TuiScrollbar, TuiSpinButton],\n      styles: [\".t-row[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;font:var(--tui-font-text-m)}.t-row[_ngcontent-%COMP%]:first-child{justify-content:flex-end}.t-row[_ngcontent-%COMP%]:last-child{justify-content:flex-start}.t-cell[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;justify-content:center;line-height:2rem;isolation:isolate;cursor:pointer;overflow:hidden;border:.125rem solid transparent;box-sizing:border-box;-webkit-mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem));mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem))}.t-cell[_ngcontent-%COMP%]:first-child{border-inline-start-color:transparent!important}.t-cell[_ngcontent-%COMP%]:last-child{border-inline-end-color:transparent!important}.t-cell[_ngcontent-%COMP%]:before, .t-cell[_ngcontent-%COMP%]:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";z-index:-1;border-radius:var(--tui-radius-m)}.t-cell[_ngcontent-%COMP%]:after{-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat}.t-cell[data-range][_ngcontent-%COMP%]:before{background:var(--tui-background-neutral-1)}._picking[_nghost-%COMP%]   .t-cell[data-range][_ngcontent-%COMP%]:before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle][_ngcontent-%COMP%]{border-color:var(--tui-background-neutral-1)}._picking[_nghost-%COMP%]   .t-cell[data-range=middle][_ngcontent-%COMP%]{border-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle][_ngcontent-%COMP%]:not(:first-child):before{border-top-left-radius:0;border-bottom-left-radius:0}.t-cell[data-range=middle][_ngcontent-%COMP%]:not(:last-child):before{border-top-right-radius:0;border-bottom-right-radius:0}.t-cell[data-range=start][_ngcontent-%COMP%]{border-inline-end-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}._picking[_nghost-%COMP%]   .t-cell[data-range=start][_ngcontent-%COMP%]{border-inline-end-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start][_ngcontent-%COMP%]:not(:last-child):before{right:-1rem}.t-cell[data-range=start][_ngcontent-%COMP%]:after{background:var(--tui-background-accent-1)}.t-cell[data-range=end][_ngcontent-%COMP%]{border-inline-start-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}._picking[_nghost-%COMP%]   .t-cell[data-range=end][_ngcontent-%COMP%]{border-inline-start-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=end][_ngcontent-%COMP%]:not(:first-child):before{left:-1rem}.t-cell[data-range=end][_ngcontent-%COMP%]:after{background:var(--tui-background-accent-1);transform:scaleX(-1)}.t-cell[data-range=active][_ngcontent-%COMP%]{color:var(--tui-text-primary-on-accent-1)}.t-cell[data-range=active][_ngcontent-%COMP%]:after{background:var(--tui-background-accent-1);-webkit-mask:none;mask:none}.t-cell_disabled[_ngcontent-%COMP%]{opacity:var(--tui-disabled-opacity);pointer-events:none}.t-cell_today[_ngcontent-%COMP%]{text-decoration:underline;text-underline-offset:.25rem}@media (hover: hover) and (pointer: fine){.t-cell[_ngcontent-%COMP%]:hover:not([data-range=start]):not([data-range=end]):before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start][_ngcontent-%COMP%]:hover:after, .t-cell[data-range=end][_ngcontent-%COMP%]:hover:after, .t-cell[data-range=active][_ngcontent-%COMP%]:hover:after{background:var(--tui-background-accent-1-hover)}}[_nghost-%COMP%]{display:block;block-size:12rem;inline-size:16rem;padding:1.125rem;box-sizing:content-box}.t-spin[_ngcontent-%COMP%]{margin-block-end:1rem}.t-cell[_ngcontent-%COMP%]{inline-size:4rem;border-block-start-width:.75rem;border-block-end-width:.75rem}.t-scrollbar[_ngcontent-%COMP%]{block-size:inherit;inline-size:inherit}\"],\n      changeDetection: 0\n    });\n  }\n}\n__decorate([tuiPure], TuiCalendarMonth.prototype, \"calculateDisabledItemHandlerWithMinMax\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCalendarMonth, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-calendar-month',\n      imports: [NgIf, TuiCalendarYear, TuiHovered, TuiLet, TuiLink, TuiRepeatTimes, TuiScrollbar, TuiSpinButton],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAsAuxiliary(TuiCalendarMonth)],\n      host: {\n        '[class._picking]': 'isRangePicking()'\n      },\n      template: \"<tui-scrollbar\\n    *ngIf=\\\"isYearPickerShown; else monthSelect\\\"\\n    class=\\\"t-scrollbar\\\"\\n>\\n    <tui-calendar-year\\n        [initialItem]=\\\"year.year\\\"\\n        [max]=\\\"max().year\\\"\\n        [min]=\\\"min().year\\\"\\n        [rangeMode]=\\\"options.rangeMode\\\"\\n        [value]=\\\"value()\\\"\\n        (yearClick)=\\\"onPickerYearClick($event)\\\"\\n    />\\n</tui-scrollbar>\\n<ng-template #monthSelect>\\n    <tui-spin-button\\n        class=\\\"t-spin\\\"\\n        [focusable]=\\\"false\\\"\\n        [leftDisabled]=\\\"year.yearSameOrBefore(min())\\\"\\n        [rightDisabled]=\\\"year.yearSameOrAfter(max())\\\"\\n        (leftClick)=\\\"onPreviousYear()\\\"\\n        (rightClick)=\\\"onNextYear()\\\"\\n    >\\n        <button\\n            automation-id=\\\"tui-calendar-month__active-year\\\"\\n            tabIndex=\\\"-1\\\"\\n            tuiLink\\n            type=\\\"button\\\"\\n            (click)=\\\"onYearClick()\\\"\\n        >\\n            {{ year.formattedYear }}\\n        </button>\\n    </tui-spin-button>\\n    <div\\n        *tuiRepeatTimes=\\\"let row of 3\\\"\\n        class=\\\"t-row\\\"\\n    >\\n        <ng-container *tuiRepeatTimes=\\\"let column of 4\\\">\\n            <div\\n                *tuiLet=\\\"getTuiMonth(row * 4 + column, year.year) as item\\\"\\n                class=\\\"t-cell\\\"\\n                [attr.data-range]=\\\"getItemRange(item)\\\"\\n                [class.t-cell_disabled]=\\\"disabledItemHandlerWithMinMax(item)\\\"\\n                [class.t-cell_today]=\\\"isItemToday(item)\\\"\\n                (click)=\\\"onItemClick(item)\\\"\\n                (tuiHoveredChange)=\\\"onItemHovered($event, item)\\\"\\n            >\\n                {{ months()?.[row * 4 + column] }}\\n            </div>\\n        </ng-container>\\n    </div>\\n</ng-template>\\n\",\n      styles: [\".t-row{display:flex;justify-content:flex-start;font:var(--tui-font-text-m)}.t-row:first-child{justify-content:flex-end}.t-row:last-child{justify-content:flex-start}.t-cell{position:relative;display:flex;align-items:center;justify-content:center;line-height:2rem;isolation:isolate;cursor:pointer;overflow:hidden;border:.125rem solid transparent;box-sizing:border-box;-webkit-mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem));mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem))}.t-cell:first-child{border-inline-start-color:transparent!important}.t-cell:last-child{border-inline-end-color:transparent!important}.t-cell:before,.t-cell:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";z-index:-1;border-radius:var(--tui-radius-m)}.t-cell:after{-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat}.t-cell[data-range]:before{background:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range]:before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]:not(:first-child):before{border-top-left-radius:0;border-bottom-left-radius:0}.t-cell[data-range=middle]:not(:last-child):before{border-top-right-radius:0;border-bottom-right-radius:0}.t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:not(:last-child):before{right:-1rem}.t-cell[data-range=start]:after{background:var(--tui-background-accent-1)}.t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=end]:not(:first-child):before{left:-1rem}.t-cell[data-range=end]:after{background:var(--tui-background-accent-1);transform:scaleX(-1)}.t-cell[data-range=active]{color:var(--tui-text-primary-on-accent-1)}.t-cell[data-range=active]:after{background:var(--tui-background-accent-1);-webkit-mask:none;mask:none}.t-cell_disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}.t-cell_today{text-decoration:underline;text-underline-offset:.25rem}@media (hover: hover) and (pointer: fine){.t-cell:hover:not([data-range=start]):not([data-range=end]):before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:hover:after,.t-cell[data-range=end]:hover:after,.t-cell[data-range=active]:hover:after{background:var(--tui-background-accent-1-hover)}}:host{display:block;block-size:12rem;inline-size:16rem;padding:1.125rem;box-sizing:content-box}.t-spin{margin-block-end:1rem}.t-cell{inline-size:4rem;border-block-start-width:.75rem;border-block-end-width:.75rem}.t-scrollbar{block-size:inherit;inline-size:inherit}\\n\"]\n    }]\n  }], null, {\n    year: [{\n      type: Input\n    }],\n    disabledItemHandler: [{\n      type: Input\n    }],\n    minLength: [{\n      type: Input\n    }],\n    maxLength: [{\n      type: Input\n    }],\n    monthClick: [{\n      type: Output\n    }],\n    hoveredItemChange: [{\n      type: Output\n    }],\n    yearChange: [{\n      type: Output\n    }],\n    minSetter: [{\n      type: Input,\n      args: [{\n        alias: 'min',\n        transform: x => x ?? TUI_FIRST_DAY\n      }]\n    }],\n    maxSetter: [{\n      type: Input,\n      args: [{\n        alias: 'max',\n        transform: x => x ?? TUI_LAST_DAY\n      }]\n    }],\n    valueSetter: [{\n      type: Input,\n      args: ['value']\n    }],\n    calculateDisabledItemHandlerWithMinMax: []\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_CALENDAR_MONTH_DEFAULT_OPTIONS, TUI_CALENDAR_MONTH_OPTIONS, TuiCalendarMonth, tuiCalendarMonthOptionsProvider };", "map": {"version": 3, "names": ["__decorate", "NgIf", "i0", "inject", "computed", "EventEmitter", "signal", "Component", "ChangeDetectionStrategy", "Input", "Output", "toSignal", "TUI_FALSE_HANDLER", "TuiDay", "TuiMonthRange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TUI_FIRST_DAY", "TUI_LAST_DAY", "<PERSON><PERSON><PERSON><PERSON>", "TuiHovered", "TuiLet", "TuiRepeatTimes", "tuiNullable<PERSON>ame", "tuiPure", "TuiCalendarYear", "TuiLink", "TuiScrollbar", "TuiSpinButton", "tuiAsAuxiliary", "TUI_CALENDAR_MONTHS", "tuiCreateOptions", "TuiCalendarMonth_tui_scrollbar_0_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiCalendarMonth_tui_scrollbar_0_Template_tui_calendar_year_yearClick_1_listener", "$event", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onPickerYearClick", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "year", "max", "min", "options", "rangeMode", "value", "TuiCalendarMonth_ng_template_1_div_3_ng_container_1_div_1_Template", "_r4", "TuiCalendarMonth_ng_template_1_div_3_ng_container_1_div_1_Template_div_click_0_listener", "item_r5", "tuiLet", "onItemClick", "TuiCalendarMonth_ng_template_1_div_3_ng_container_1_div_1_Template_div_tuiHoveredChange_0_listener", "onItemHovered", "ɵɵtext", "tmp_11_0", "column_r6", "$implicit", "row_r7", "ɵɵclassProp", "disabledItemHandlerWithMinMax", "isItemToday", "ɵɵattribute", "getItemRange", "ɵɵtextInterpolate1", "months", "TuiCalendarMonth_ng_template_1_div_3_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "getTuiMonth", "TuiCalendarMonth_ng_template_1_div_3_Template", "TuiCalendarMonth_ng_template_1_Template", "_r3", "TuiCalendarMonth_ng_template_1_Template_tui_spin_button_leftClick_0_listener", "onPreviousYear", "TuiCalendarMonth_ng_template_1_Template_tui_spin_button_rightClick_0_listener", "onNextYear", "TuiCalendarMonth_ng_template_1_Template_button_click_1_listener", "onYearClick", "yearSameOrBefore", "yearSameOrAfter", "formattedYear", "TUI_CALENDAR_MONTH_DEFAULT_OPTIONS", "TUI_CALENDAR_MONTH_OPTIONS", "tuiCalendarMonthOptionsProvider", "TODAY", "currentLocal", "TuiCalendarMonth", "constructor", "isYearPickerShown", "isRangePicking", "x", "isSingleMonth", "disabledItemHandler", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "monthClick", "hoveredItemChange", "yearChange", "hoveredItem", "minSetter", "set", "maxSetter", "valueSetter", "updateActiveYear", "append", "item", "monthSame", "<PERSON><PERSON><PERSON><PERSON>", "month", "hovered", "from", "to", "picking", "Math", "calculateDisabledItemHandlerWithMinMax", "monthNumber", "yearNumber", "emit", "updateHoveredItem", "<PERSON><PERSON><PERSON><PERSON>", "delta", "abs", "tooLong", "tooShort", "monthBefore", "monthAfter", "a", "b", "ɵfac", "TuiCalendarMonth_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "TuiCalendarMonth_HostBindings", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "None", "outputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInputTransformsFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiCalendarMonth_Template", "ɵɵtemplateRefExtractor", "monthSelect_r8", "ɵɵreference", "dependencies", "styles", "changeDetection", "prototype", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "host", "alias", "transform"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-calendar-month.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, computed, EventEmitter, signal, Component, ChangeDetectionStrategy, Input, Output } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { TuiDay, TuiMonthRange, TuiMonth, TUI_FIRST_DAY, TUI_LAST_DAY, TuiYear } from '@taiga-ui/cdk/date-time';\nimport { TuiHovered } from '@taiga-ui/cdk/directives/hovered';\nimport { TuiLet } from '@taiga-ui/cdk/directives/let';\nimport { TuiRepeatTimes } from '@taiga-ui/cdk/directives/repeat-times';\nimport { tuiNullableSame, tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiCalendarYear } from '@taiga-ui/core/components/calendar';\nimport { TuiLink } from '@taiga-ui/core/components/link';\nimport { TuiScrollbar } from '@taiga-ui/core/components/scrollbar';\nimport { TuiSpinButton } from '@taiga-ui/core/components/spin-button';\nimport { tuiAsAuxiliary } from '@taiga-ui/core/tokens';\nimport { TUI_CALENDAR_MONTHS } from '@taiga-ui/kit/tokens';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\n\nconst TUI_CALENDAR_MONTH_DEFAULT_OPTIONS = {\n    rangeMode: false,\n};\nconst [TUI_CALENDAR_MONTH_OPTIONS, tuiCalendarMonthOptionsProvider] = tuiCreateOptions(TUI_CALENDAR_MONTH_DEFAULT_OPTIONS);\n\nconst TODAY = TuiDay.currentLocal();\nclass TuiCalendarMonth {\n    constructor() {\n        this.isYearPickerShown = false;\n        this.months = toSignal(inject(TUI_CALENDAR_MONTHS));\n        this.isRangePicking = computed((x = this.value()) => (!this.options.rangeMode && x instanceof TuiMonthRange && x.isSingleMonth) || // TODO(v5): remove this condition\n            (this.options.rangeMode && x instanceof TuiMonth));\n        this.year = TODAY;\n        this.disabledItemHandler = TUI_FALSE_HANDLER;\n        this.minLength = null;\n        this.maxLength = null;\n        this.monthClick = new EventEmitter();\n        this.hoveredItemChange = new EventEmitter();\n        this.yearChange = new EventEmitter();\n        this.options = inject(TUI_CALENDAR_MONTH_OPTIONS);\n        this.min = signal(TUI_FIRST_DAY);\n        this.max = signal(TUI_LAST_DAY);\n        this.value = signal(null);\n        this.hoveredItem = null;\n    }\n    // TODO(v5): use signal inputs\n    set minSetter(x) {\n        this.min.set(x);\n    }\n    // TODO(v5): use signal inputs\n    set maxSetter(x) {\n        this.max.set(x);\n    }\n    // TODO(v5): use signal inputs\n    set valueSetter(x) {\n        this.value.set(x);\n    }\n    onNextYear() {\n        this.updateActiveYear(this.year.append({ year: 1 }));\n    }\n    onPreviousYear() {\n        this.updateActiveYear(this.year.append({ year: -1 }));\n    }\n    getItemRange(item) {\n        const value = this.value();\n        const { hoveredItem } = this;\n        if (!value) {\n            return null;\n        }\n        if (!this.options.rangeMode && value instanceof TuiMonth) {\n            return value?.monthSame(item) ? 'active' : null;\n        }\n        const selectedRange = value instanceof TuiMonth ? new TuiMonthRange(value, value) : value;\n        const months = item.month + item.year * 12;\n        const hovered = hoveredItem ? hoveredItem.month + hoveredItem.year * 12 : null;\n        const from = selectedRange.from.month + selectedRange.from.year * 12;\n        const to = selectedRange.to.month + selectedRange.to.year * 12;\n        const picking = this.isRangePicking() ? hovered : null;\n        const min = Math.min(from, to, picking ?? from);\n        const max = Math.max(from, to, picking ?? from);\n        if (min === max && min === months) {\n            return 'active';\n        }\n        if (min === months) {\n            return 'start';\n        }\n        if (max === months) {\n            return 'end';\n        }\n        return min < months && months < max ? 'middle' : null;\n    }\n    get disabledItemHandlerWithMinMax() {\n        return this.calculateDisabledItemHandlerWithMinMax(this.disabledItemHandler, this.value(), this.isRangePicking(), this.min(), this.max(), this.minLength, this.maxLength);\n    }\n    getTuiMonth(monthNumber, yearNumber) {\n        return new TuiMonth(yearNumber, monthNumber);\n    }\n    isItemToday(item) {\n        return TODAY.monthSame(item);\n    }\n    onPickerYearClick(year) {\n        this.isYearPickerShown = false;\n        if (this.year.year !== year) {\n            this.updateActiveYear(new TuiYear(year));\n        }\n    }\n    onItemClick(month) {\n        if (!this.disabledItemHandlerWithMinMax(month)) {\n            this.monthClick.emit(month);\n        }\n    }\n    onYearClick() {\n        this.isYearPickerShown = true;\n    }\n    onItemHovered(hovered, item) {\n        this.updateHoveredItem(hovered ? item : null);\n    }\n    // eslint-disable-next-line @typescript-eslint/max-params,max-params\n    calculateDisabledItemHandlerWithMinMax(disabledItemHandler, value, isRangePicking, min, max, minLength, maxLength) {\n        return (item) => {\n            const selectedMonth = value instanceof TuiMonthRange ? value.from : value;\n            const delta = isRangePicking && selectedMonth\n                ? Math.abs(item.year * 12 +\n                    item.month -\n                    selectedMonth.year * 12 -\n                    selectedMonth.month)\n                : 0;\n            const tooLong = delta && maxLength && delta > maxLength;\n            const tooShort = delta && minLength && delta < minLength;\n            return (tooLong ||\n                tooShort ||\n                item.monthBefore(min) ||\n                item.monthAfter(max) ||\n                disabledItemHandler(item));\n        };\n    }\n    updateHoveredItem(month) {\n        if (tuiNullableSame(this.hoveredItem, month, (a, b) => a.monthSame(b))) {\n            return;\n        }\n        this.hoveredItem = month;\n        this.hoveredItemChange.emit(month);\n    }\n    updateActiveYear(year) {\n        this.year = year;\n        this.yearChange.emit(year);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendarMonth, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"16.2.12\", type: TuiCalendarMonth, isStandalone: true, selector: \"tui-calendar-month\", inputs: { year: \"year\", disabledItemHandler: \"disabledItemHandler\", minLength: \"minLength\", maxLength: \"maxLength\", minSetter: [\"min\", \"minSetter\", (x) => x ?? TUI_FIRST_DAY], maxSetter: [\"max\", \"maxSetter\", (x) => x ?? TUI_LAST_DAY], valueSetter: [\"value\", \"valueSetter\"] }, outputs: { monthClick: \"monthClick\", hoveredItemChange: \"hoveredItemChange\", yearChange: \"yearChange\" }, host: { properties: { \"class._picking\": \"isRangePicking()\" } }, providers: [tuiAsAuxiliary(TuiCalendarMonth)], ngImport: i0, template: \"<tui-scrollbar\\n    *ngIf=\\\"isYearPickerShown; else monthSelect\\\"\\n    class=\\\"t-scrollbar\\\"\\n>\\n    <tui-calendar-year\\n        [initialItem]=\\\"year.year\\\"\\n        [max]=\\\"max().year\\\"\\n        [min]=\\\"min().year\\\"\\n        [rangeMode]=\\\"options.rangeMode\\\"\\n        [value]=\\\"value()\\\"\\n        (yearClick)=\\\"onPickerYearClick($event)\\\"\\n    />\\n</tui-scrollbar>\\n<ng-template #monthSelect>\\n    <tui-spin-button\\n        class=\\\"t-spin\\\"\\n        [focusable]=\\\"false\\\"\\n        [leftDisabled]=\\\"year.yearSameOrBefore(min())\\\"\\n        [rightDisabled]=\\\"year.yearSameOrAfter(max())\\\"\\n        (leftClick)=\\\"onPreviousYear()\\\"\\n        (rightClick)=\\\"onNextYear()\\\"\\n    >\\n        <button\\n            automation-id=\\\"tui-calendar-month__active-year\\\"\\n            tabIndex=\\\"-1\\\"\\n            tuiLink\\n            type=\\\"button\\\"\\n            (click)=\\\"onYearClick()\\\"\\n        >\\n            {{ year.formattedYear }}\\n        </button>\\n    </tui-spin-button>\\n    <div\\n        *tuiRepeatTimes=\\\"let row of 3\\\"\\n        class=\\\"t-row\\\"\\n    >\\n        <ng-container *tuiRepeatTimes=\\\"let column of 4\\\">\\n            <div\\n                *tuiLet=\\\"getTuiMonth(row * 4 + column, year.year) as item\\\"\\n                class=\\\"t-cell\\\"\\n                [attr.data-range]=\\\"getItemRange(item)\\\"\\n                [class.t-cell_disabled]=\\\"disabledItemHandlerWithMinMax(item)\\\"\\n                [class.t-cell_today]=\\\"isItemToday(item)\\\"\\n                (click)=\\\"onItemClick(item)\\\"\\n                (tuiHoveredChange)=\\\"onItemHovered($event, item)\\\"\\n            >\\n                {{ months()?.[row * 4 + column] }}\\n            </div>\\n        </ng-container>\\n    </div>\\n</ng-template>\\n\", styles: [\".t-row{display:flex;justify-content:flex-start;font:var(--tui-font-text-m)}.t-row:first-child{justify-content:flex-end}.t-row:last-child{justify-content:flex-start}.t-cell{position:relative;display:flex;align-items:center;justify-content:center;line-height:2rem;isolation:isolate;cursor:pointer;overflow:hidden;border:.125rem solid transparent;box-sizing:border-box;-webkit-mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem));mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem))}.t-cell:first-child{border-inline-start-color:transparent!important}.t-cell:last-child{border-inline-end-color:transparent!important}.t-cell:before,.t-cell:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";z-index:-1;border-radius:var(--tui-radius-m)}.t-cell:after{-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat}.t-cell[data-range]:before{background:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range]:before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]:not(:first-child):before{border-top-left-radius:0;border-bottom-left-radius:0}.t-cell[data-range=middle]:not(:last-child):before{border-top-right-radius:0;border-bottom-right-radius:0}.t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:not(:last-child):before{right:-1rem}.t-cell[data-range=start]:after{background:var(--tui-background-accent-1)}.t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=end]:not(:first-child):before{left:-1rem}.t-cell[data-range=end]:after{background:var(--tui-background-accent-1);transform:scaleX(-1)}.t-cell[data-range=active]{color:var(--tui-text-primary-on-accent-1)}.t-cell[data-range=active]:after{background:var(--tui-background-accent-1);-webkit-mask:none;mask:none}.t-cell_disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}.t-cell_today{text-decoration:underline;text-underline-offset:.25rem}@media (hover: hover) and (pointer: fine){.t-cell:hover:not([data-range=start]):not([data-range=end]):before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:hover:after,.t-cell[data-range=end]:hover:after,.t-cell[data-range=active]:hover:after{background:var(--tui-background-accent-1-hover)}}:host{display:block;block-size:12rem;inline-size:16rem;padding:1.125rem;box-sizing:content-box}.t-spin{margin-block-end:1rem}.t-cell{inline-size:4rem;border-block-start-width:.75rem;border-block-end-width:.75rem}.t-scrollbar{block-size:inherit;inline-size:inherit}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: TuiCalendarYear, selector: \"tui-calendar-year\", inputs: [\"value\", \"initialItem\", \"min\", \"max\", \"rangeMode\", \"disabledItemHandler\"], outputs: [\"yearClick\"] }, { kind: \"directive\", type: TuiHovered, selector: \"[tuiHoveredChange]\", outputs: [\"tuiHoveredChange\"] }, { kind: \"directive\", type: TuiLet, selector: \"[tuiLet]\", inputs: [\"tuiLet\"] }, { kind: \"directive\", type: TuiLink, selector: \"a[tuiLink], button[tuiLink]\", inputs: [\"pseudo\"] }, { kind: \"directive\", type: TuiRepeatTimes, selector: \"[tuiRepeatTimes][tuiRepeatTimesOf]\", inputs: [\"tuiRepeatTimesOf\"] }, { kind: \"component\", type: TuiScrollbar, selector: \"tui-scrollbar\", inputs: [\"hidden\"] }, { kind: \"component\", type: TuiSpinButton, selector: \"tui-spin-button\", inputs: [\"focusable\", \"disabled\", \"leftDisabled\", \"rightDisabled\"], outputs: [\"leftClick\", \"rightClick\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\n__decorate([\n    tuiPure\n], TuiCalendarMonth.prototype, \"calculateDisabledItemHandlerWithMinMax\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCalendarMonth, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-calendar-month', imports: [\n                        NgIf,\n                        TuiCalendarYear,\n                        TuiHovered,\n                        TuiLet,\n                        TuiLink,\n                        TuiRepeatTimes,\n                        TuiScrollbar,\n                        TuiSpinButton,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiAsAuxiliary(TuiCalendarMonth)], host: {\n                        '[class._picking]': 'isRangePicking()',\n                    }, template: \"<tui-scrollbar\\n    *ngIf=\\\"isYearPickerShown; else monthSelect\\\"\\n    class=\\\"t-scrollbar\\\"\\n>\\n    <tui-calendar-year\\n        [initialItem]=\\\"year.year\\\"\\n        [max]=\\\"max().year\\\"\\n        [min]=\\\"min().year\\\"\\n        [rangeMode]=\\\"options.rangeMode\\\"\\n        [value]=\\\"value()\\\"\\n        (yearClick)=\\\"onPickerYearClick($event)\\\"\\n    />\\n</tui-scrollbar>\\n<ng-template #monthSelect>\\n    <tui-spin-button\\n        class=\\\"t-spin\\\"\\n        [focusable]=\\\"false\\\"\\n        [leftDisabled]=\\\"year.yearSameOrBefore(min())\\\"\\n        [rightDisabled]=\\\"year.yearSameOrAfter(max())\\\"\\n        (leftClick)=\\\"onPreviousYear()\\\"\\n        (rightClick)=\\\"onNextYear()\\\"\\n    >\\n        <button\\n            automation-id=\\\"tui-calendar-month__active-year\\\"\\n            tabIndex=\\\"-1\\\"\\n            tuiLink\\n            type=\\\"button\\\"\\n            (click)=\\\"onYearClick()\\\"\\n        >\\n            {{ year.formattedYear }}\\n        </button>\\n    </tui-spin-button>\\n    <div\\n        *tuiRepeatTimes=\\\"let row of 3\\\"\\n        class=\\\"t-row\\\"\\n    >\\n        <ng-container *tuiRepeatTimes=\\\"let column of 4\\\">\\n            <div\\n                *tuiLet=\\\"getTuiMonth(row * 4 + column, year.year) as item\\\"\\n                class=\\\"t-cell\\\"\\n                [attr.data-range]=\\\"getItemRange(item)\\\"\\n                [class.t-cell_disabled]=\\\"disabledItemHandlerWithMinMax(item)\\\"\\n                [class.t-cell_today]=\\\"isItemToday(item)\\\"\\n                (click)=\\\"onItemClick(item)\\\"\\n                (tuiHoveredChange)=\\\"onItemHovered($event, item)\\\"\\n            >\\n                {{ months()?.[row * 4 + column] }}\\n            </div>\\n        </ng-container>\\n    </div>\\n</ng-template>\\n\", styles: [\".t-row{display:flex;justify-content:flex-start;font:var(--tui-font-text-m)}.t-row:first-child{justify-content:flex-end}.t-row:last-child{justify-content:flex-start}.t-cell{position:relative;display:flex;align-items:center;justify-content:center;line-height:2rem;isolation:isolate;cursor:pointer;overflow:hidden;border:.125rem solid transparent;box-sizing:border-box;-webkit-mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem));mask:linear-gradient(transparent calc(50% - 1rem),#000 calc(50% - 1rem),#000 calc(50% + 1rem),transparent calc(50% + 1rem))}.t-cell:first-child{border-inline-start-color:transparent!important}.t-cell:last-child{border-inline-end-color:transparent!important}.t-cell:before,.t-cell:after{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";z-index:-1;border-radius:var(--tui-radius-m)}.t-cell:after{-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 32\\\"><path d=\\\"M0.2856 0L0.6763 0C2.9265 0 4.9876 1.259 6.0147 3.2611L10.2442 11.5048C11.5301 14.0113 11.5683 16.9754 10.3472 19.5141L5.9766 28.6007C4.9772 30.6786 2.8754 32 0.5696 32H0.285645V0Z\\\"></path></svg>') right / .75rem 100% no-repeat,linear-gradient(#000,#000) left / calc(100% - .7rem) 100% no-repeat}.t-cell[data-range]:before{background:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range]:before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1)}:host._picking .t-cell[data-range=middle]{border-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=middle]:not(:first-child):before{border-top-left-radius:0;border-bottom-left-radius:0}.t-cell[data-range=middle]:not(:last-child):before{border-top-right-radius:0;border-bottom-right-radius:0}.t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=start]{border-inline-end-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:not(:last-child):before{right:-1rem}.t-cell[data-range=start]:after{background:var(--tui-background-accent-1)}.t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1);color:var(--tui-text-primary-on-accent-1)}:host._picking .t-cell[data-range=end]{border-inline-start-color:var(--tui-background-neutral-1-hover)}.t-cell[data-range=end]:not(:first-child):before{left:-1rem}.t-cell[data-range=end]:after{background:var(--tui-background-accent-1);transform:scaleX(-1)}.t-cell[data-range=active]{color:var(--tui-text-primary-on-accent-1)}.t-cell[data-range=active]:after{background:var(--tui-background-accent-1);-webkit-mask:none;mask:none}.t-cell_disabled{opacity:var(--tui-disabled-opacity);pointer-events:none}.t-cell_today{text-decoration:underline;text-underline-offset:.25rem}@media (hover: hover) and (pointer: fine){.t-cell:hover:not([data-range=start]):not([data-range=end]):before{background:var(--tui-background-neutral-1-hover)}.t-cell[data-range=start]:hover:after,.t-cell[data-range=end]:hover:after,.t-cell[data-range=active]:hover:after{background:var(--tui-background-accent-1-hover)}}:host{display:block;block-size:12rem;inline-size:16rem;padding:1.125rem;box-sizing:content-box}.t-spin{margin-block-end:1rem}.t-cell{inline-size:4rem;border-block-start-width:.75rem;border-block-end-width:.75rem}.t-scrollbar{block-size:inherit;inline-size:inherit}\\n\"] }]\n        }], propDecorators: { year: [{\n                type: Input\n            }], disabledItemHandler: [{\n                type: Input\n            }], minLength: [{\n                type: Input\n            }], maxLength: [{\n                type: Input\n            }], monthClick: [{\n                type: Output\n            }], hoveredItemChange: [{\n                type: Output\n            }], yearChange: [{\n                type: Output\n            }], minSetter: [{\n                type: Input,\n                args: [{ alias: 'min', transform: (x) => x ?? TUI_FIRST_DAY }]\n            }], maxSetter: [{\n                type: Input,\n                args: [{ alias: 'max', transform: (x) => x ?? TUI_LAST_DAY }]\n            }], valueSetter: [{\n                type: Input,\n                args: ['value']\n            }], calculateDisabledItemHandlerWithMinMax: [] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_CALENDAR_MONTH_DEFAULT_OPTIONS, TUI_CALENDAR_MONTH_OPTIONS, TuiCalendarMonth, tuiCalendarMonthOptionsProvider };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,IAAI,QAAQ,iBAAiB;AACtC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AACzH,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,MAAM,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,YAAY,EAAEC,OAAO,QAAQ,yBAAyB;AAC/G,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,MAAM,QAAQ,8BAA8B;AACrD,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,eAAe,EAAEC,OAAO,QAAQ,mCAAmC;AAC5E,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,gBAAgB,QAAQ,wBAAwB;AAAC,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAiI2ChC,EAAE,CAAAiC,gBAAA;IAAFjC,EAAE,CAAAkC,cAAA,sBACqqB,CAAC,0BAA2P,CAAC;IADp6BlC,EAAE,CAAAmC,UAAA,uBAAAC,iFAAAC,MAAA;MAAFrC,EAAE,CAAAsC,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAFvC,EAAE,CAAAwC,aAAA;MAAA,OAAFxC,EAAE,CAAAyC,WAAA,CAC+3BF,MAAA,CAAAG,iBAAA,CAAAL,MAAwB,CAAC;IAAA,CAAC,CAAC;IAD55BrC,EAAE,CAAA2C,YAAA,CACi6B,CAAC,CAAiB,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAS,MAAA,GADt7BvC,EAAE,CAAAwC,aAAA;IAAFxC,EAAE,CAAA4C,SAAA,CACkuB,CAAC;IADruB5C,EAAE,CAAA6C,UAAA,gBAAAN,MAAA,CAAAO,IAAA,CAAAA,IACkuB,CAAC,QAAAP,MAAA,CAAAQ,GAAA,GAAAD,IAA6B,CAAC,QAAAP,MAAA,CAAAS,GAAA,GAAAF,IAA6B,CAAC,cAAAP,MAAA,CAAAU,OAAA,CAAAC,SAA0C,CAAC,UAAAX,MAAA,CAAAY,KAAA,EAA4B,CAAC;EAAA;AAAA;AAAA,SAAAC,mEAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuB,GAAA,GADz2BrD,EAAE,CAAAiC,gBAAA;IAAFjC,EAAE,CAAAkC,cAAA,aACqmE,CAAC;IADxmElC,EAAE,CAAAmC,UAAA,mBAAAmB,wFAAA;MAAA,MAAAC,OAAA,GAAFvD,EAAE,CAAAsC,aAAA,CAAAe,GAAA,EAAAG,MAAA;MAAA,MAAAjB,MAAA,GAAFvC,EAAE,CAAAwC,aAAA;MAAA,OAAFxC,EAAE,CAAAyC,WAAA,CACggEF,MAAA,CAAAkB,WAAA,CAAAF,OAAgB,CAAC;IAAA,CAAC,CAAC,8BAAAG,mGAAArB,MAAA;MAAA,MAAAkB,OAAA,GADrhEvD,EAAE,CAAAsC,aAAA,CAAAe,GAAA,EAAAG,MAAA;MAAA,MAAAjB,MAAA,GAAFvC,EAAE,CAAAwC,aAAA;MAAA,OAAFxC,EAAE,CAAAyC,WAAA,CAC0jEF,MAAA,CAAAoB,aAAA,CAAAtB,MAAA,EAAAkB,OAA0B,CAAC;IAAA,CAAC,CAAC;IADzlEvD,EAAE,CAAA4D,MAAA,EACuqE,CAAC;IAD1qE5D,EAAE,CAAA2C,YAAA,CAC6qE,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,IAAA+B,QAAA;IAAA,MAAAN,OAAA,GAAAxB,GAAA,CAAAyB,MAAA;IAAA,MAAAM,SAAA,GADhrE9D,EAAE,CAAAwC,aAAA,GAAAuB,SAAA;IAAA,MAAAC,MAAA,GAAFhE,EAAE,CAAAwC,aAAA,GAAAuB,SAAA;IAAA,MAAAxB,MAAA,GAAFvC,EAAE,CAAAwC,aAAA;IAAFxC,EAAE,CAAAiE,WAAA,oBAAA1B,MAAA,CAAA2B,6BAAA,CAAAX,OAAA,CACu6D,CAAC,iBAAAhB,MAAA,CAAA4B,WAAA,CAAAZ,OAAA,CAA2D,CAAC;IADt+DvD,EAAE,CAAAoE,WAAA,eAAA7B,MAAA,CAAA8B,YAAA,CAAAd,OAAA;IAAFvD,EAAE,CAAA4C,SAAA,CACuqE,CAAC;IAD1qE5C,EAAE,CAAAsE,kBAAA,OAAAT,QAAA,GAAAtB,MAAA,CAAAgC,MAAA,qBAAAV,QAAA,CAAAG,MAAA,OAAAF,SAAA,MACuqE,CAAC;EAAA;AAAA;AAAA,SAAAU,6DAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD1qE9B,EAAE,CAAAyE,uBAAA,EAC0pD,CAAC;IAD7pDzE,EAAE,CAAA0E,UAAA,IAAAtB,kEAAA,gBACqmE,CAAC;IADxmEpD,EAAE,CAAA2E,qBAAA;EAAA;EAAA,IAAA7C,EAAA;IAAA,MAAAgC,SAAA,GAAA/B,GAAA,CAAAgC,SAAA;IAAA,MAAAC,MAAA,GAAFhE,EAAE,CAAAwC,aAAA,GAAAuB,SAAA;IAAA,MAAAxB,MAAA,GAAFvC,EAAE,CAAAwC,aAAA;IAAFxC,EAAE,CAAA4C,SAAA,CACivD,CAAC;IADpvD5C,EAAE,CAAA6C,UAAA,WAAAN,MAAA,CAAAqC,WAAA,CAAAZ,MAAA,OAAAF,SAAA,EAAAvB,MAAA,CAAAO,IAAA,CAAAA,IAAA,CACivD,CAAC;EAAA;AAAA;AAAA,SAAA+B,8CAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADpvD9B,EAAE,CAAAkC,cAAA,YAC8lD,CAAC;IADjmDlC,EAAE,CAAA0E,UAAA,IAAAF,4DAAA,yBAC0pD,CAAC;IAD7pDxE,EAAE,CAAA2C,YAAA,CACktE,CAAC;EAAA;EAAA,IAAAb,EAAA;IADrtE9B,EAAE,CAAA4C,SAAA,CACupD,CAAC;IAD1pD5C,EAAE,CAAA6C,UAAA,sBACupD,CAAC;EAAA;AAAA;AAAA,SAAAiC,wCAAAhD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiD,GAAA,GAD1pD/E,EAAE,CAAAiC,gBAAA;IAAFjC,EAAE,CAAAkC,cAAA,wBACwuC,CAAC;IAD3uClC,EAAE,CAAAmC,UAAA,uBAAA6C,6EAAA;MAAFhF,EAAE,CAAAsC,aAAA,CAAAyC,GAAA;MAAA,MAAAxC,MAAA,GAAFvC,EAAE,CAAAwC,aAAA;MAAA,OAAFxC,EAAE,CAAAyC,WAAA,CACyqCF,MAAA,CAAA0C,cAAA,CAAe,CAAC;IAAA,CAAC,CAAC,wBAAAC,8EAAA;MAD7rClF,EAAE,CAAAsC,aAAA,CAAAyC,GAAA;MAAA,MAAAxC,MAAA,GAAFvC,EAAE,CAAAwC,aAAA;MAAA,OAAFxC,EAAE,CAAAyC,WAAA,CACotCF,MAAA,CAAA4C,UAAA,CAAW,CAAC;IAAA,CAAC,CAAC;IADpuCnF,EAAE,CAAAkC,cAAA,eACy7C,CAAC;IAD57ClC,EAAE,CAAAmC,UAAA,mBAAAiD,gEAAA;MAAFpF,EAAE,CAAAsC,aAAA,CAAAyC,GAAA;MAAA,MAAAxC,MAAA,GAAFvC,EAAE,CAAAwC,aAAA;MAAA,OAAFxC,EAAE,CAAAyC,WAAA,CACg6CF,MAAA,CAAA8C,WAAA,CAAY,CAAC;IAAA,CAAC,CAAC;IADj7CrF,EAAE,CAAA4D,MAAA,EACy+C,CAAC;IAD5+C5D,EAAE,CAAA2C,YAAA,CACk/C,CAAC,CAAuB,CAAC;IAD7gD3C,EAAE,CAAA0E,UAAA,IAAAG,6CAAA,gBAC8lD,CAAC;EAAA;EAAA,IAAA/C,EAAA;IAAA,MAAAS,MAAA,GADjmDvC,EAAE,CAAAwC,aAAA;IAAFxC,EAAE,CAAA6C,UAAA,mBAC8hC,CAAC,iBAAAN,MAAA,CAAAO,IAAA,CAAAwC,gBAAA,CAAA/C,MAAA,CAAAS,GAAA,GAAwD,CAAC,kBAAAT,MAAA,CAAAO,IAAA,CAAAyC,eAAA,CAAAhD,MAAA,CAAAQ,GAAA,GAAwD,CAAC;IADnpC/C,EAAE,CAAA4C,SAAA,EACy+C,CAAC;IAD5+C5C,EAAE,CAAAsE,kBAAA,MAAA/B,MAAA,CAAAO,IAAA,CAAA0C,aAAA,KACy+C,CAAC;IAD5+CxF,EAAE,CAAA4C,SAAA,CAC4jD,CAAC;IAD/jD5C,EAAE,CAAA6C,UAAA,sBAC4jD,CAAC;EAAA;AAAA;AAhIpqD,MAAM4C,kCAAkC,GAAG;EACvCvC,SAAS,EAAE;AACf,CAAC;AACD,MAAM,CAACwC,0BAA0B,EAAEC,+BAA+B,CAAC,GAAG/D,gBAAgB,CAAC6D,kCAAkC,CAAC;AAE1H,MAAMG,KAAK,GAAGjF,MAAM,CAACkF,YAAY,CAAC,CAAC;AACnC,MAAMC,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACzB,MAAM,GAAG9D,QAAQ,CAACR,MAAM,CAAC0B,mBAAmB,CAAC,CAAC;IACnD,IAAI,CAACsE,cAAc,GAAG/F,QAAQ,CAAC,CAACgG,CAAC,GAAG,IAAI,CAAC/C,KAAK,CAAC,CAAC,KAAM,CAAC,IAAI,CAACF,OAAO,CAACC,SAAS,IAAIgD,CAAC,YAAYtF,aAAa,IAAIsF,CAAC,CAACC,aAAa;IAAK;IAC9H,IAAI,CAAClD,OAAO,CAACC,SAAS,IAAIgD,CAAC,YAAYrF,QAAS,CAAC;IACtD,IAAI,CAACiC,IAAI,GAAG8C,KAAK;IACjB,IAAI,CAACQ,mBAAmB,GAAG1F,iBAAiB;IAC5C,IAAI,CAAC2F,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,UAAU,GAAG,IAAIpG,YAAY,CAAC,CAAC;IACpC,IAAI,CAACqG,iBAAiB,GAAG,IAAIrG,YAAY,CAAC,CAAC;IAC3C,IAAI,CAACsG,UAAU,GAAG,IAAItG,YAAY,CAAC,CAAC;IACpC,IAAI,CAAC8C,OAAO,GAAGhD,MAAM,CAACyF,0BAA0B,CAAC;IACjD,IAAI,CAAC1C,GAAG,GAAG5C,MAAM,CAACU,aAAa,CAAC;IAChC,IAAI,CAACiC,GAAG,GAAG3C,MAAM,CAACW,YAAY,CAAC;IAC/B,IAAI,CAACoC,KAAK,GAAG/C,MAAM,CAAC,IAAI,CAAC;IACzB,IAAI,CAACsG,WAAW,GAAG,IAAI;EAC3B;EACA;EACA,IAAIC,SAASA,CAACT,CAAC,EAAE;IACb,IAAI,CAAClD,GAAG,CAAC4D,GAAG,CAACV,CAAC,CAAC;EACnB;EACA;EACA,IAAIW,SAASA,CAACX,CAAC,EAAE;IACb,IAAI,CAACnD,GAAG,CAAC6D,GAAG,CAACV,CAAC,CAAC;EACnB;EACA;EACA,IAAIY,WAAWA,CAACZ,CAAC,EAAE;IACf,IAAI,CAAC/C,KAAK,CAACyD,GAAG,CAACV,CAAC,CAAC;EACrB;EACAf,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC4B,gBAAgB,CAAC,IAAI,CAACjE,IAAI,CAACkE,MAAM,CAAC;MAAElE,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;EACxD;EACAmC,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC8B,gBAAgB,CAAC,IAAI,CAACjE,IAAI,CAACkE,MAAM,CAAC;MAAElE,IAAI,EAAE,CAAC;IAAE,CAAC,CAAC,CAAC;EACzD;EACAuB,YAAYA,CAAC4C,IAAI,EAAE;IACf,MAAM9D,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;IAC1B,MAAM;MAAEuD;IAAY,CAAC,GAAG,IAAI;IAC5B,IAAI,CAACvD,KAAK,EAAE;MACR,OAAO,IAAI;IACf;IACA,IAAI,CAAC,IAAI,CAACF,OAAO,CAACC,SAAS,IAAIC,KAAK,YAAYtC,QAAQ,EAAE;MACtD,OAAOsC,KAAK,EAAE+D,SAAS,CAACD,IAAI,CAAC,GAAG,QAAQ,GAAG,IAAI;IACnD;IACA,MAAME,aAAa,GAAGhE,KAAK,YAAYtC,QAAQ,GAAG,IAAID,aAAa,CAACuC,KAAK,EAAEA,KAAK,CAAC,GAAGA,KAAK;IACzF,MAAMoB,MAAM,GAAG0C,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACnE,IAAI,GAAG,EAAE;IAC1C,MAAMuE,OAAO,GAAGX,WAAW,GAAGA,WAAW,CAACU,KAAK,GAAGV,WAAW,CAAC5D,IAAI,GAAG,EAAE,GAAG,IAAI;IAC9E,MAAMwE,IAAI,GAAGH,aAAa,CAACG,IAAI,CAACF,KAAK,GAAGD,aAAa,CAACG,IAAI,CAACxE,IAAI,GAAG,EAAE;IACpE,MAAMyE,EAAE,GAAGJ,aAAa,CAACI,EAAE,CAACH,KAAK,GAAGD,aAAa,CAACI,EAAE,CAACzE,IAAI,GAAG,EAAE;IAC9D,MAAM0E,OAAO,GAAG,IAAI,CAACvB,cAAc,CAAC,CAAC,GAAGoB,OAAO,GAAG,IAAI;IACtD,MAAMrE,GAAG,GAAGyE,IAAI,CAACzE,GAAG,CAACsE,IAAI,EAAEC,EAAE,EAAEC,OAAO,IAAIF,IAAI,CAAC;IAC/C,MAAMvE,GAAG,GAAG0E,IAAI,CAAC1E,GAAG,CAACuE,IAAI,EAAEC,EAAE,EAAEC,OAAO,IAAIF,IAAI,CAAC;IAC/C,IAAItE,GAAG,KAAKD,GAAG,IAAIC,GAAG,KAAKuB,MAAM,EAAE;MAC/B,OAAO,QAAQ;IACnB;IACA,IAAIvB,GAAG,KAAKuB,MAAM,EAAE;MAChB,OAAO,OAAO;IAClB;IACA,IAAIxB,GAAG,KAAKwB,MAAM,EAAE;MAChB,OAAO,KAAK;IAChB;IACA,OAAOvB,GAAG,GAAGuB,MAAM,IAAIA,MAAM,GAAGxB,GAAG,GAAG,QAAQ,GAAG,IAAI;EACzD;EACA,IAAImB,6BAA6BA,CAAA,EAAG;IAChC,OAAO,IAAI,CAACwD,sCAAsC,CAAC,IAAI,CAACtB,mBAAmB,EAAE,IAAI,CAACjD,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC8C,cAAc,CAAC,CAAC,EAAE,IAAI,CAACjD,GAAG,CAAC,CAAC,EAAE,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE,IAAI,CAACsD,SAAS,EAAE,IAAI,CAACC,SAAS,CAAC;EAC7K;EACA1B,WAAWA,CAAC+C,WAAW,EAAEC,UAAU,EAAE;IACjC,OAAO,IAAI/G,QAAQ,CAAC+G,UAAU,EAAED,WAAW,CAAC;EAChD;EACAxD,WAAWA,CAAC8C,IAAI,EAAE;IACd,OAAOrB,KAAK,CAACsB,SAAS,CAACD,IAAI,CAAC;EAChC;EACAvE,iBAAiBA,CAACI,IAAI,EAAE;IACpB,IAAI,CAACkD,iBAAiB,GAAG,KAAK;IAC9B,IAAI,IAAI,CAAClD,IAAI,CAACA,IAAI,KAAKA,IAAI,EAAE;MACzB,IAAI,CAACiE,gBAAgB,CAAC,IAAI/F,OAAO,CAAC8B,IAAI,CAAC,CAAC;IAC5C;EACJ;EACAW,WAAWA,CAAC2D,KAAK,EAAE;IACf,IAAI,CAAC,IAAI,CAAClD,6BAA6B,CAACkD,KAAK,CAAC,EAAE;MAC5C,IAAI,CAACb,UAAU,CAACsB,IAAI,CAACT,KAAK,CAAC;IAC/B;EACJ;EACA/B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACW,iBAAiB,GAAG,IAAI;EACjC;EACArC,aAAaA,CAAC0D,OAAO,EAAEJ,IAAI,EAAE;IACzB,IAAI,CAACa,iBAAiB,CAACT,OAAO,GAAGJ,IAAI,GAAG,IAAI,CAAC;EACjD;EACA;EACAS,sCAAsCA,CAACtB,mBAAmB,EAAEjD,KAAK,EAAE8C,cAAc,EAAEjD,GAAG,EAAED,GAAG,EAAEsD,SAAS,EAAEC,SAAS,EAAE;IAC/G,OAAQW,IAAI,IAAK;MACb,MAAMc,aAAa,GAAG5E,KAAK,YAAYvC,aAAa,GAAGuC,KAAK,CAACmE,IAAI,GAAGnE,KAAK;MACzE,MAAM6E,KAAK,GAAG/B,cAAc,IAAI8B,aAAa,GACvCN,IAAI,CAACQ,GAAG,CAAChB,IAAI,CAACnE,IAAI,GAAG,EAAE,GACrBmE,IAAI,CAACG,KAAK,GACVW,aAAa,CAACjF,IAAI,GAAG,EAAE,GACvBiF,aAAa,CAACX,KAAK,CAAC,GACtB,CAAC;MACP,MAAMc,OAAO,GAAGF,KAAK,IAAI1B,SAAS,IAAI0B,KAAK,GAAG1B,SAAS;MACvD,MAAM6B,QAAQ,GAAGH,KAAK,IAAI3B,SAAS,IAAI2B,KAAK,GAAG3B,SAAS;MACxD,OAAQ6B,OAAO,IACXC,QAAQ,IACRlB,IAAI,CAACmB,WAAW,CAACpF,GAAG,CAAC,IACrBiE,IAAI,CAACoB,UAAU,CAACtF,GAAG,CAAC,IACpBqD,mBAAmB,CAACa,IAAI,CAAC;IACjC,CAAC;EACL;EACAa,iBAAiBA,CAACV,KAAK,EAAE;IACrB,IAAIhG,eAAe,CAAC,IAAI,CAACsF,WAAW,EAAEU,KAAK,EAAE,CAACkB,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACpB,SAAS,CAACqB,CAAC,CAAC,CAAC,EAAE;MACpE;IACJ;IACA,IAAI,CAAC7B,WAAW,GAAGU,KAAK;IACxB,IAAI,CAACZ,iBAAiB,CAACqB,IAAI,CAACT,KAAK,CAAC;EACtC;EACAL,gBAAgBA,CAACjE,IAAI,EAAE;IACnB,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC2D,UAAU,CAACoB,IAAI,CAAC/E,IAAI,CAAC;EAC9B;EACA;IAAS,IAAI,CAAC0F,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyF5C,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAAC6C,IAAI,kBAD+E3I,EAAE,CAAA4I,iBAAA;MAAAC,IAAA,EACJ/C,gBAAgB;MAAAgD,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,8BAAAnH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADd9B,EAAE,CAAAiE,WAAA,aACJlC,GAAA,CAAAkE,cAAA,CAAe,EAAC;QAAA;MAAA;MAAAiD,MAAA;QAAApG,IAAA;QAAAsD,mBAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAK,SAAA,GADd3G,EAAE,CAAAmJ,YAAA,CAAAC,0BAAA,sBACuNlD,CAAC,IAAKA,CAAC,IAAIpF,aAAa;QAAA+F,SAAA,GADjP7G,EAAE,CAAAmJ,YAAA,CAAAC,0BAAA,sBACmRlD,CAAC,IAAKA,CAAC,IAAInF,YAAY;QAAA+F,WAAA,GAD5S9G,EAAE,CAAAmJ,YAAA,CAAAE,IAAA;MAAA;MAAAC,OAAA;QAAA/C,UAAA;QAAAC,iBAAA;QAAAC,UAAA;MAAA;MAAA8C,UAAA;MAAAC,QAAA,GAAFxJ,EAAE,CAAAyJ,kBAAA,CAC0gB,CAAC/H,cAAc,CAACoE,gBAAgB,CAAC,CAAC,GAD9iB9F,EAAE,CAAA0J,wBAAA,EAAF1J,EAAE,CAAA2J,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAlI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9B,EAAE,CAAA0E,UAAA,IAAA7C,yCAAA,0BACqqB,CAAC,IAAAiD,uCAAA,gCADxqB9E,EAAE,CAAAiK,sBAC+8B,CAAC;QAAA;QAAA,IAAAnI,EAAA;UAAA,MAAAoI,cAAA,GADl9BlK,EAAE,CAAAmK,WAAA;UAAFnK,EAAE,CAAA6C,UAAA,SAAAd,GAAA,CAAAiE,iBACqnB,CAAC,aAAAkE,cAAe,CAAC;QAAA;MAAA;MAAAE,YAAA,GAA88KrK,IAAI,EAA6FuB,eAAe,EAA0KL,UAAU,EAA8FC,MAAM,EAAyEK,OAAO,EAA4FJ,cAAc,EAA6GK,YAAY,EAA8EC,aAAa;MAAA4I,MAAA;MAAAC,eAAA;IAAA,EAAiM;EAAE;AACppO;AACAxK,UAAU,CAAC,CACPuB,OAAO,CACV,EAAEyE,gBAAgB,CAACyE,SAAS,EAAE,wCAAwC,EAAE,IAAI,CAAC;AAC9E;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KANqGxK,EAAE,CAAAyK,iBAAA,CAMX3E,gBAAgB,EAAc,CAAC;IAC/G+C,IAAI,EAAExI,SAAS;IACfqK,IAAI,EAAE,CAAC;MAAEnB,UAAU,EAAE,IAAI;MAAEoB,QAAQ,EAAE,oBAAoB;MAAEC,OAAO,EAAE,CACxD7K,IAAI,EACJuB,eAAe,EACfL,UAAU,EACVC,MAAM,EACNK,OAAO,EACPJ,cAAc,EACdK,YAAY,EACZC,aAAa,CAChB;MAAE6I,eAAe,EAAEhK,uBAAuB,CAACuK,MAAM;MAAEC,SAAS,EAAE,CAACpJ,cAAc,CAACoE,gBAAgB,CAAC,CAAC;MAAEiF,IAAI,EAAE;QACrG,kBAAkB,EAAE;MACxB,CAAC;MAAEhB,QAAQ,EAAE,gqDAAgqD;MAAEM,MAAM,EAAE,CAAC,szHAAszH;IAAE,CAAC;EAC7/K,CAAC,CAAC,QAAkB;IAAEvH,IAAI,EAAE,CAAC;MACrB+F,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAE6F,mBAAmB,EAAE,CAAC;MACtByC,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAE8F,SAAS,EAAE,CAAC;MACZwC,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAE+F,SAAS,EAAE,CAAC;MACZuC,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEgG,UAAU,EAAE,CAAC;MACbsC,IAAI,EAAErI;IACV,CAAC,CAAC;IAAEgG,iBAAiB,EAAE,CAAC;MACpBqC,IAAI,EAAErI;IACV,CAAC,CAAC;IAAEiG,UAAU,EAAE,CAAC;MACboC,IAAI,EAAErI;IACV,CAAC,CAAC;IAAEmG,SAAS,EAAE,CAAC;MACZkC,IAAI,EAAEtI,KAAK;MACXmK,IAAI,EAAE,CAAC;QAAEM,KAAK,EAAE,KAAK;QAAEC,SAAS,EAAG/E,CAAC,IAAKA,CAAC,IAAIpF;MAAc,CAAC;IACjE,CAAC,CAAC;IAAE+F,SAAS,EAAE,CAAC;MACZgC,IAAI,EAAEtI,KAAK;MACXmK,IAAI,EAAE,CAAC;QAAEM,KAAK,EAAE,KAAK;QAAEC,SAAS,EAAG/E,CAAC,IAAKA,CAAC,IAAInF;MAAa,CAAC;IAChE,CAAC,CAAC;IAAE+F,WAAW,EAAE,CAAC;MACd+B,IAAI,EAAEtI,KAAK;MACXmK,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEhD,sCAAsC,EAAE;EAAG,CAAC;AAAA;;AAE5D;AACA;AACA;;AAEA,SAASjC,kCAAkC,EAAEC,0BAA0B,EAAEI,gBAAgB,EAAEH,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}