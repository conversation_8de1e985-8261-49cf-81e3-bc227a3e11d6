{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, ChangeDetectorRef, DestroyRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { NgControl, NgModel } from '@angular/forms';\nimport * as i2 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport { tuiControlValue, tuiWatch } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiCreateToken, tuiProvideOptions, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { TuiAppearance } from '@taiga-ui/core/directives/appearance';\nimport { tuiInjectIconResolver } from '@taiga-ui/core/tokens';\nconst _c0 = [\"type\", \"checkbox\", \"tuiCheckbox\", \"\"];\nconst TUI_CHECKBOX_DEFAULT_OPTIONS = {\n  size: 'm',\n  appearance: el => el.checked || el.indeterminate ? 'primary' : 'outline-grayscale',\n  icons: {\n    checked: '@tui.check',\n    indeterminate: '@tui.minus'\n  }\n};\nconst TUI_CHECKBOX_OPTIONS = tuiCreateToken(TUI_CHECKBOX_DEFAULT_OPTIONS);\nfunction tuiCheckboxOptionsProvider(options) {\n  return tuiProvideOptions(TUI_CHECKBOX_OPTIONS, options, TUI_CHECKBOX_DEFAULT_OPTIONS);\n}\nclass TuiCheckbox {\n  constructor() {\n    this.appearance = inject(TuiAppearance);\n    this.options = inject(TUI_CHECKBOX_OPTIONS);\n    this.cdr = inject(ChangeDetectorRef);\n    this.resolver = tuiInjectIconResolver();\n    this.destroyRef = inject(DestroyRef);\n    this.el = tuiInjectElement();\n    this.size = this.options.size;\n    this.control = inject(NgControl, {\n      optional: true,\n      self: true\n    });\n  }\n  ngOnInit() {\n    if (!this.control?.valueChanges) {\n      return;\n    }\n    tuiControlValue(this.control).pipe(tuiWatch(this.cdr), takeUntilDestroyed(this.destroyRef)).subscribe(value => {\n      // https://github.com/angular/angular/issues/14988\n      const fix = this.control instanceof NgModel && value === null ? this.control.model : value;\n      this.el.indeterminate = fix === null;\n    });\n  }\n  ngDoCheck() {\n    this.appearance.tuiAppearance = tuiIsString(this.options.appearance) ? this.options.appearance : this.options.appearance(this.el);\n  }\n  getIcon(state) {\n    const option = this.options.icons[state];\n    const icon = tuiIsString(option) ? option : option(this.size);\n    return icon && `url(${this.resolver(icon)})`;\n  }\n  static {\n    this.ɵfac = function TuiCheckbox_Factory(t) {\n      return new (t || TuiCheckbox)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiCheckbox,\n      selectors: [[\"input\", \"type\", \"checkbox\", \"tuiCheckbox\", \"\"]],\n      hostVars: 8,\n      hostBindings: function TuiCheckbox_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"disabled\", !ctx.control || ctx.control.disabled);\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n          i0.ɵɵstyleProp(\"--t-checked-icon\", ctx.getIcon(\"checked\"))(\"--t-indeterminate-icon\", ctx.getIcon(\"indeterminate\"));\n          i0.ɵɵclassProp(\"_readonly\", !ctx.control);\n        }\n      },\n      inputs: {\n        size: \"size\"\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([{\n        directive: i1.TuiAppearance,\n        inputs: [\"tuiAppearanceState\", \"tuiAppearanceState\", \"tuiAppearanceFocus\", \"tuiAppearanceFocus\", \"tuiAppearanceMode\", \"tuiAppearanceMode\"]\n      }, i2.TuiNativeValidator]), i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 0,\n      vars: 0,\n      template: function TuiCheckbox_Template(rf, ctx) {},\n      styles: [\"[tuiCheckbox]{--t-size: 1.5rem;--t-radius: var(--tui-radius-s);inline-size:var(--t-size);block-size:var(--t-size);border-radius:var(--t-radius);cursor:pointer;margin:0;flex-shrink:0}[tuiCheckbox]:before{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";inline-size:1rem;block-size:1rem;margin:auto;background:currentColor;-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"></svg>') center / 100%;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"></svg>') center / 100%;transform:scale(0);transition:transform var(--tui-duration) ease-in-out,-webkit-mask 0s var(--tui-duration) ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s var(--tui-duration) ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s var(--tui-duration) ease-in-out,-webkit-mask 0s var(--tui-duration) ease-in-out}[tuiCheckbox]:disabled._readonly{opacity:1;pointer-events:none}[tuiCheckbox]:checked:before,[tuiCheckbox]:indeterminate:before{-webkit-mask-image:var(--t-checked-icon);mask-image:var(--t-checked-icon);transform:scale(1);transition:transform var(--tui-duration) ease-in-out,-webkit-mask 0s ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s ease-in-out,-webkit-mask 0s ease-in-out}[tuiCheckbox]:indeterminate:before{-webkit-mask-image:var(--t-indeterminate-icon);mask-image:var(--t-indeterminate-icon)}[tuiCheckbox][data-size=s]{--t-size: 1rem;--t-radius: var(--tui-radius-xs)}[tuiCheckbox][data-size=s]:before{inline-size:.875rem;block-size:.875rem}[tuiCheckbox]:invalid:not([data-mode]),[tuiCheckbox][data-mode~=invalid]{color:#fff}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCheckbox, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'input[type=\"checkbox\"][tuiCheckbox]',\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      hostDirectives: [{\n        directive: TuiAppearance,\n        inputs: ['tuiAppearanceState', 'tuiAppearanceFocus', 'tuiAppearanceMode']\n      }, TuiNativeValidator],\n      host: {\n        '[disabled]': '!control || control.disabled',\n        '[attr.data-size]': 'size',\n        '[class._readonly]': '!control',\n        '[style.--t-checked-icon]': 'getIcon(\"checked\")',\n        '[style.--t-indeterminate-icon]': 'getIcon(\"indeterminate\")'\n      },\n      styles: [\"[tuiCheckbox]{--t-size: 1.5rem;--t-radius: var(--tui-radius-s);inline-size:var(--t-size);block-size:var(--t-size);border-radius:var(--t-radius);cursor:pointer;margin:0;flex-shrink:0}[tuiCheckbox]:before{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";inline-size:1rem;block-size:1rem;margin:auto;background:currentColor;-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"></svg>') center / 100%;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"></svg>') center / 100%;transform:scale(0);transition:transform var(--tui-duration) ease-in-out,-webkit-mask 0s var(--tui-duration) ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s var(--tui-duration) ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s var(--tui-duration) ease-in-out,-webkit-mask 0s var(--tui-duration) ease-in-out}[tuiCheckbox]:disabled._readonly{opacity:1;pointer-events:none}[tuiCheckbox]:checked:before,[tuiCheckbox]:indeterminate:before{-webkit-mask-image:var(--t-checked-icon);mask-image:var(--t-checked-icon);transform:scale(1);transition:transform var(--tui-duration) ease-in-out,-webkit-mask 0s ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s ease-in-out,-webkit-mask 0s ease-in-out}[tuiCheckbox]:indeterminate:before{-webkit-mask-image:var(--t-indeterminate-icon);mask-image:var(--t-indeterminate-icon)}[tuiCheckbox][data-size=s]{--t-size: 1rem;--t-radius: var(--tui-radius-xs)}[tuiCheckbox][data-size=s]:before{inline-size:.875rem;block-size:.875rem}[tuiCheckbox]:invalid:not([data-mode]),[tuiCheckbox][data-mode~=invalid]{color:#fff}\\n\"]\n    }]\n  }], null, {\n    size: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_CHECKBOX_DEFAULT_OPTIONS, TUI_CHECKBOX_OPTIONS, TuiCheckbox, tuiCheckboxOptionsProvider };", "map": {"version": 3, "names": ["i0", "inject", "ChangeDetectorRef", "DestroyRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "takeUntilDestroyed", "NgControl", "NgModel", "i2", "TuiNativeValidator", "tuiControlValue", "tuiWatch", "tuiInjectElement", "tuiCreateToken", "tuiProvideOptions", "tuiIsString", "i1", "TuiAppearan<PERSON>", "tuiInjectIconResolver", "_c0", "TUI_CHECKBOX_DEFAULT_OPTIONS", "size", "appearance", "el", "checked", "indeterminate", "icons", "TUI_CHECKBOX_OPTIONS", "tuiCheckboxOptionsProvider", "options", "TuiCheckbox", "constructor", "cdr", "resolver", "destroyRef", "control", "optional", "self", "ngOnInit", "valueChanges", "pipe", "subscribe", "value", "fix", "model", "ngDoCheck", "tui<PERSON><PERSON><PERSON><PERSON>", "getIcon", "state", "option", "icon", "ɵfac", "TuiCheckbox_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "TuiCheckbox_HostBindings", "rf", "ctx", "ɵɵhostProperty", "disabled", "ɵɵattribute", "ɵɵstyleProp", "ɵɵclassProp", "inputs", "standalone", "features", "ɵɵHostDirectivesFeature", "directive", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "template", "TuiCheckbox_Template", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "hostDirectives", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-checkbox.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, ChangeDetectorRef, DestroyRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { NgControl, NgModel } from '@angular/forms';\nimport * as i2 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport { tuiControlValue, tuiWatch } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiCreateToken, tuiProvideOptions, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { TuiAppearance } from '@taiga-ui/core/directives/appearance';\nimport { tuiInjectIconResolver } from '@taiga-ui/core/tokens';\n\nconst TUI_CHECKBOX_DEFAULT_OPTIONS = {\n    size: 'm',\n    appearance: (el) => el.checked || el.indeterminate ? 'primary' : 'outline-grayscale',\n    icons: {\n        checked: '@tui.check',\n        indeterminate: '@tui.minus',\n    },\n};\nconst TUI_CHECKBOX_OPTIONS = tuiCreateToken(TUI_CHECKBOX_DEFAULT_OPTIONS);\nfunction tuiCheckboxOptionsProvider(options) {\n    return tuiProvideOptions(TUI_CHECKBOX_OPTIONS, options, TUI_CHECKBOX_DEFAULT_OPTIONS);\n}\n\nclass TuiCheckbox {\n    constructor() {\n        this.appearance = inject(TuiAppearance);\n        this.options = inject(TUI_CHECKBOX_OPTIONS);\n        this.cdr = inject(ChangeDetectorRef);\n        this.resolver = tuiInjectIconResolver();\n        this.destroyRef = inject(DestroyRef);\n        this.el = tuiInjectElement();\n        this.size = this.options.size;\n        this.control = inject(NgControl, {\n            optional: true,\n            self: true,\n        });\n    }\n    ngOnInit() {\n        if (!this.control?.valueChanges) {\n            return;\n        }\n        tuiControlValue(this.control)\n            .pipe(tuiWatch(this.cdr), takeUntilDestroyed(this.destroyRef))\n            .subscribe((value) => {\n            // https://github.com/angular/angular/issues/14988\n            const fix = this.control instanceof NgModel && value === null\n                ? this.control.model\n                : value;\n            this.el.indeterminate = fix === null;\n        });\n    }\n    ngDoCheck() {\n        this.appearance.tuiAppearance = tuiIsString(this.options.appearance)\n            ? this.options.appearance\n            : this.options.appearance(this.el);\n    }\n    getIcon(state) {\n        const option = this.options.icons[state];\n        const icon = tuiIsString(option) ? option : option(this.size);\n        return icon && `url(${this.resolver(icon)})`;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCheckbox, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCheckbox, isStandalone: true, selector: \"input[type=\\\"checkbox\\\"][tuiCheckbox]\", inputs: { size: \"size\" }, host: { properties: { \"disabled\": \"!control || control.disabled\", \"attr.data-size\": \"size\", \"class._readonly\": \"!control\", \"style.--t-checked-icon\": \"getIcon(\\\"checked\\\")\", \"style.--t-indeterminate-icon\": \"getIcon(\\\"indeterminate\\\")\" } }, hostDirectives: [{ directive: i1.TuiAppearance, inputs: [\"tuiAppearanceState\", \"tuiAppearanceState\", \"tuiAppearanceFocus\", \"tuiAppearanceFocus\", \"tuiAppearanceMode\", \"tuiAppearanceMode\"] }, { directive: i2.TuiNativeValidator }], ngImport: i0, template: '', isInline: true, styles: [\"[tuiCheckbox]{--t-size: 1.5rem;--t-radius: var(--tui-radius-s);inline-size:var(--t-size);block-size:var(--t-size);border-radius:var(--t-radius);cursor:pointer;margin:0;flex-shrink:0}[tuiCheckbox]:before{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";inline-size:1rem;block-size:1rem;margin:auto;background:currentColor;-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"></svg>') center / 100%;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"></svg>') center / 100%;transform:scale(0);transition:transform var(--tui-duration) ease-in-out,-webkit-mask 0s var(--tui-duration) ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s var(--tui-duration) ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s var(--tui-duration) ease-in-out,-webkit-mask 0s var(--tui-duration) ease-in-out}[tuiCheckbox]:disabled._readonly{opacity:1;pointer-events:none}[tuiCheckbox]:checked:before,[tuiCheckbox]:indeterminate:before{-webkit-mask-image:var(--t-checked-icon);mask-image:var(--t-checked-icon);transform:scale(1);transition:transform var(--tui-duration) ease-in-out,-webkit-mask 0s ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s ease-in-out,-webkit-mask 0s ease-in-out}[tuiCheckbox]:indeterminate:before{-webkit-mask-image:var(--t-indeterminate-icon);mask-image:var(--t-indeterminate-icon)}[tuiCheckbox][data-size=s]{--t-size: 1rem;--t-radius: var(--tui-radius-xs)}[tuiCheckbox][data-size=s]:before{inline-size:.875rem;block-size:.875rem}[tuiCheckbox]:invalid:not([data-mode]),[tuiCheckbox][data-mode~=invalid]{color:#fff}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCheckbox, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'input[type=\"checkbox\"][tuiCheckbox]', template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, hostDirectives: [\n                        {\n                            directive: TuiAppearance,\n                            inputs: ['tuiAppearanceState', 'tuiAppearanceFocus', 'tuiAppearanceMode'],\n                        },\n                        TuiNativeValidator,\n                    ], host: {\n                        '[disabled]': '!control || control.disabled',\n                        '[attr.data-size]': 'size',\n                        '[class._readonly]': '!control',\n                        '[style.--t-checked-icon]': 'getIcon(\"checked\")',\n                        '[style.--t-indeterminate-icon]': 'getIcon(\"indeterminate\")',\n                    }, styles: [\"[tuiCheckbox]{--t-size: 1.5rem;--t-radius: var(--tui-radius-s);inline-size:var(--t-size);block-size:var(--t-size);border-radius:var(--t-radius);cursor:pointer;margin:0;flex-shrink:0}[tuiCheckbox]:before{position:absolute;top:0;left:0;bottom:0;right:0;content:\\\"\\\";inline-size:1rem;block-size:1rem;margin:auto;background:currentColor;-webkit-mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"></svg>') center / 100%;mask:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\"></svg>') center / 100%;transform:scale(0);transition:transform var(--tui-duration) ease-in-out,-webkit-mask 0s var(--tui-duration) ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s var(--tui-duration) ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s var(--tui-duration) ease-in-out,-webkit-mask 0s var(--tui-duration) ease-in-out}[tuiCheckbox]:disabled._readonly{opacity:1;pointer-events:none}[tuiCheckbox]:checked:before,[tuiCheckbox]:indeterminate:before{-webkit-mask-image:var(--t-checked-icon);mask-image:var(--t-checked-icon);transform:scale(1);transition:transform var(--tui-duration) ease-in-out,-webkit-mask 0s ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s ease-in-out;transition:transform var(--tui-duration) ease-in-out,mask 0s ease-in-out,-webkit-mask 0s ease-in-out}[tuiCheckbox]:indeterminate:before{-webkit-mask-image:var(--t-indeterminate-icon);mask-image:var(--t-indeterminate-icon)}[tuiCheckbox][data-size=s]{--t-size: 1rem;--t-radius: var(--tui-radius-xs)}[tuiCheckbox][data-size=s]:before{inline-size:.875rem;block-size:.875rem}[tuiCheckbox]:invalid:not([data-mode]),[tuiCheckbox][data-mode~=invalid]{color:#fff}\\n\"] }]\n        }], propDecorators: { size: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_CHECKBOX_DEFAULT_OPTIONS, TUI_CHECKBOX_OPTIONS, TuiCheckbox, tuiCheckboxOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,QAAQ,eAAe;AACnI,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,SAAS,EAAEC,OAAO,QAAQ,gBAAgB;AACnD,OAAO,KAAKC,EAAE,MAAM,2CAA2C;AAC/D,SAASC,kBAAkB,QAAQ,2CAA2C;AAC9E,SAASC,eAAe,EAAEC,QAAQ,QAAQ,2BAA2B;AACrE,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,WAAW,QAAQ,mCAAmC;AAClG,OAAO,KAAKC,EAAE,MAAM,sCAAsC;AAC1D,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,qBAAqB,QAAQ,uBAAuB;AAAC,MAAAC,GAAA;AAE9D,MAAMC,4BAA4B,GAAG;EACjCC,IAAI,EAAE,GAAG;EACTC,UAAU,EAAGC,EAAE,IAAKA,EAAE,CAACC,OAAO,IAAID,EAAE,CAACE,aAAa,GAAG,SAAS,GAAG,mBAAmB;EACpFC,KAAK,EAAE;IACHF,OAAO,EAAE,YAAY;IACrBC,aAAa,EAAE;EACnB;AACJ,CAAC;AACD,MAAME,oBAAoB,GAAGd,cAAc,CAACO,4BAA4B,CAAC;AACzE,SAASQ,0BAA0BA,CAACC,OAAO,EAAE;EACzC,OAAOf,iBAAiB,CAACa,oBAAoB,EAAEE,OAAO,EAAET,4BAA4B,CAAC;AACzF;AAEA,MAAMU,WAAW,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACT,UAAU,GAAGxB,MAAM,CAACmB,aAAa,CAAC;IACvC,IAAI,CAACY,OAAO,GAAG/B,MAAM,CAAC6B,oBAAoB,CAAC;IAC3C,IAAI,CAACK,GAAG,GAAGlC,MAAM,CAACC,iBAAiB,CAAC;IACpC,IAAI,CAACkC,QAAQ,GAAGf,qBAAqB,CAAC,CAAC;IACvC,IAAI,CAACgB,UAAU,GAAGpC,MAAM,CAACE,UAAU,CAAC;IACpC,IAAI,CAACuB,EAAE,GAAGX,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACS,IAAI,GAAG,IAAI,CAACQ,OAAO,CAACR,IAAI;IAC7B,IAAI,CAACc,OAAO,GAAGrC,MAAM,CAACQ,SAAS,EAAE;MAC7B8B,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;IACV,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACH,OAAO,EAAEI,YAAY,EAAE;MAC7B;IACJ;IACA7B,eAAe,CAAC,IAAI,CAACyB,OAAO,CAAC,CACxBK,IAAI,CAAC7B,QAAQ,CAAC,IAAI,CAACqB,GAAG,CAAC,EAAE3B,kBAAkB,CAAC,IAAI,CAAC6B,UAAU,CAAC,CAAC,CAC7DO,SAAS,CAAEC,KAAK,IAAK;MACtB;MACA,MAAMC,GAAG,GAAG,IAAI,CAACR,OAAO,YAAY5B,OAAO,IAAImC,KAAK,KAAK,IAAI,GACvD,IAAI,CAACP,OAAO,CAACS,KAAK,GAClBF,KAAK;MACX,IAAI,CAACnB,EAAE,CAACE,aAAa,GAAGkB,GAAG,KAAK,IAAI;IACxC,CAAC,CAAC;EACN;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,CAACvB,UAAU,CAACwB,aAAa,GAAG/B,WAAW,CAAC,IAAI,CAACc,OAAO,CAACP,UAAU,CAAC,GAC9D,IAAI,CAACO,OAAO,CAACP,UAAU,GACvB,IAAI,CAACO,OAAO,CAACP,UAAU,CAAC,IAAI,CAACC,EAAE,CAAC;EAC1C;EACAwB,OAAOA,CAACC,KAAK,EAAE;IACX,MAAMC,MAAM,GAAG,IAAI,CAACpB,OAAO,CAACH,KAAK,CAACsB,KAAK,CAAC;IACxC,MAAME,IAAI,GAAGnC,WAAW,CAACkC,MAAM,CAAC,GAAGA,MAAM,GAAGA,MAAM,CAAC,IAAI,CAAC5B,IAAI,CAAC;IAC7D,OAAO6B,IAAI,IAAI,OAAO,IAAI,CAACjB,QAAQ,CAACiB,IAAI,CAAC,GAAG;EAChD;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFvB,WAAW;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACwB,IAAI,kBAD+EzD,EAAE,CAAA0D,iBAAA;MAAAC,IAAA,EACJ1B,WAAW;MAAA2B,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADThE,EAAE,CAAAkE,cAAA,cAAAD,GAAA,CAAA3B,OAAA,IAAA2B,GAAA,CAAA3B,OAAA,CAAA6B,QACM,CAAC;UADTnE,EAAE,CAAAoE,WAAA,cAAAH,GAAA,CAAAzC,IAAA;UAAFxB,EAAE,CAAAqE,WAAA,qBACJJ,GAAA,CAAAf,OAAA,CAAQ,SAAS,CAAP,CAAC,2BAAXe,GAAA,CAAAf,OAAA,CAAQ,eAAe,CAAb,CAAC;UADTlD,EAAE,CAAAsE,WAAA,eAAAL,GAAA,CAAA3B,OACM,CAAC;QAAA;MAAA;MAAAiC,MAAA;QAAA/C,IAAA;MAAA;MAAAgD,UAAA;MAAAC,QAAA,GADTzE,EAAE,CAAA0E,uBAAA;QAAAC,SAAA,EACuXxD,EAAE,CAACC,aAAa;QAAAmD,MAAA;MAAA,GAA6J5D,EAAE,CAACC,kBAAkB,IAD3jBZ,EAAE,CAAA4E,mBAAA;MAAAC,KAAA,EAAAvD,GAAA;MAAAwD,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,qBAAAjB,EAAA,EAAAC,GAAA;MAAAiB,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACw4E;EAAE;AACj/E;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGrF,EAAE,CAAAsF,iBAAA,CAGXrD,WAAW,EAAc,CAAC;IAC1G0B,IAAI,EAAEvD,SAAS;IACfmF,IAAI,EAAE,CAAC;MAAEf,UAAU,EAAE,IAAI;MAAEgB,QAAQ,EAAE,qCAAqC;MAAER,QAAQ,EAAE,EAAE;MAAEG,aAAa,EAAE9E,iBAAiB,CAACoF,IAAI;MAAEL,eAAe,EAAE9E,uBAAuB,CAACoF,MAAM;MAAEC,cAAc,EAAE,CACtL;QACIhB,SAAS,EAAEvD,aAAa;QACxBmD,MAAM,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,mBAAmB;MAC5E,CAAC,EACD3D,kBAAkB,CACrB;MAAEgF,IAAI,EAAE;QACL,YAAY,EAAE,8BAA8B;QAC5C,kBAAkB,EAAE,MAAM;QAC1B,mBAAmB,EAAE,UAAU;QAC/B,0BAA0B,EAAE,oBAAoB;QAChD,gCAAgC,EAAE;MACtC,CAAC;MAAEV,MAAM,EAAE,CAAC,mrDAAmrD;IAAE,CAAC;EAC9sD,CAAC,CAAC,QAAkB;IAAE1D,IAAI,EAAE,CAAC;MACrBmC,IAAI,EAAEpD;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASgB,4BAA4B,EAAEO,oBAAoB,EAAEG,WAAW,EAAEF,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}