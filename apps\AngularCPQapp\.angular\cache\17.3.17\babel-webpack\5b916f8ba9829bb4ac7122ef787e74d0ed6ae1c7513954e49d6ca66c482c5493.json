{"ast": null, "code": "import { tuiIsHTMLElement } from '@taiga-ui/cdk/utils/dom';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { TUI_TRUE_HANDLER, TUI_FALSE_HANDLER, svgNodeFilter } from '@taiga-ui/cdk/constants';\nimport { tuiUntrackedScheduler } from '@taiga-ui/cdk/observables';\nimport { merge, fromEvent, map, observeOn } from 'rxjs';\n\n/**\n * Returns current active element, including shadow dom\n *\n * @return element or null\n */\nfunction tuiGetNativeFocused({\n  activeElement\n}) {\n  if (!activeElement?.shadowRoot) {\n    return activeElement;\n  }\n  let element = activeElement.shadowRoot.activeElement;\n  while (element?.shadowRoot) {\n    element = element.shadowRoot.activeElement;\n  }\n  return element;\n}\n\n/**\n * Finds and blurs current active element, including shadow DOM\n */\nfunction tuiBlurNativeFocused(doc) {\n  const activeElement = tuiGetNativeFocused(doc);\n  if (tuiIsHTMLElement(activeElement)) {\n    activeElement.blur();\n  }\n}\nfunction tuiFocusedIn(node) {\n  return toSignal(merge(fromEvent(node, 'focus', {\n    capture: true\n  }).pipe(map(TUI_TRUE_HANDLER)), fromEvent(node, 'blur', {\n    capture: true\n  }).pipe(map(TUI_FALSE_HANDLER))).pipe(observeOn(tuiUntrackedScheduler)), {\n    initialValue: false\n  });\n}\n\n/**\n * Checks for signs that element can be focused with keyboard. tabIndex above 0 is ignored to\n * only target natural focus order. Not checking the possibility of an element to\n * be focused, for example element can have display: none applied to it or any other\n * circumstances could prevent actual focus.\n */\nfunction tuiIsNativeKeyboardFocusable(element) {\n  if (element.hasAttribute('disabled') || element.getAttribute('tabIndex') === '-1') {\n    return false;\n  }\n  if (tuiIsHTMLElement(element) && element.isContentEditable || element.getAttribute('tabIndex') === '0') {\n    return true;\n  }\n  switch (element.tagName) {\n    case 'A':\n    case 'LINK':\n      return element.hasAttribute('href');\n    case 'AUDIO':\n    case 'VIDEO':\n      return element.hasAttribute('controls');\n    case 'BUTTON':\n    case 'SELECT':\n    case 'TEXTAREA':\n      return true;\n    case 'INPUT':\n      return element.getAttribute('type') !== 'hidden';\n    default:\n      return false;\n  }\n}\nfunction tuiIsNativeMouseFocusable(element) {\n  return !element.hasAttribute('disabled') && (element.getAttribute('tabIndex') === '-1' || tuiIsNativeKeyboardFocusable(element));\n}\n\n/**\n * @description:\n * Finds the closest element that can be focused with a keyboard or mouse in theory\n */\nfunction tuiGetClosestFocusable({\n  initial,\n  root,\n  previous = false,\n  keyboard = true\n}) {\n  if (!root.ownerDocument) {\n    return null;\n  }\n  const check = keyboard ? tuiIsNativeKeyboardFocusable : tuiIsNativeMouseFocusable;\n  const treeWalker = root.ownerDocument.createTreeWalker(root, NodeFilter.SHOW_ELEMENT, svgNodeFilter);\n  treeWalker.currentNode = initial;\n  do {\n    if (tuiIsHTMLElement(treeWalker.currentNode)) {\n      initial = treeWalker.currentNode;\n    }\n    if (tuiIsHTMLElement(initial) && check(initial)) {\n      return initial;\n    }\n  } while (previous ? treeWalker.previousNode() : treeWalker.nextNode());\n  return null;\n}\n\n/**\n * Checks if element is focused.\n *\n * Could return true even after blur since element remains focused if you switch away from a browser tab.\n *\n * @param node or null (as a common return value of DOM nodes walking)\n * @return true if focused\n */\nfunction tuiIsNativeFocused(node) {\n  return !!node?.ownerDocument && tuiGetNativeFocused(node.ownerDocument) === node && node.ownerDocument.hasFocus();\n}\n\n/**\n * Checks if focused element is within given element.\n *\n * @param node\n * @return true if focused node is contained within element\n */\nfunction tuiIsNativeFocusedIn(node) {\n  const focused = node?.ownerDocument && tuiGetNativeFocused(node.ownerDocument);\n  return !!focused && node.contains(focused) && node.ownerDocument?.hasFocus();\n}\n\n/**\n * Utility method for moving focus in a list of elements\n *\n * @param currentIndex currently focused index\n * @param elements array of focusable elements\n * @param step a step to move focus by, typically -1 or 1\n */\nfunction tuiMoveFocus(currentIndex, elements, step) {\n  currentIndex += step;\n  while (currentIndex >= 0 && currentIndex < elements.length) {\n    elements[currentIndex]?.focus();\n    if (tuiIsNativeFocused(elements[currentIndex])) {\n      return;\n    }\n    currentIndex += step;\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiBlurNativeFocused, tuiFocusedIn, tuiGetClosestFocusable, tuiGetNativeFocused, tuiIsNativeFocused, tuiIsNativeFocusedIn, tuiIsNativeKeyboardFocusable, tuiIsNativeMouseFocusable, tuiMoveFocus };", "map": {"version": 3, "names": ["tuiIsHTMLElement", "toSignal", "TUI_TRUE_HANDLER", "TUI_FALSE_HANDLER", "svgNodeFilter", "tuiUntrackedScheduler", "merge", "fromEvent", "map", "observeOn", "tuiGetNativeFocused", "activeElement", "shadowRoot", "element", "tuiBlurNativeFocused", "doc", "blur", "tuiFocusedIn", "node", "capture", "pipe", "initialValue", "tuiIsNativeKeyboardFocusable", "hasAttribute", "getAttribute", "isContentEditable", "tagName", "tuiIsNativeMouseFocusable", "tuiGetClosestFocusable", "initial", "root", "previous", "keyboard", "ownerDocument", "check", "<PERSON><PERSON><PERSON><PERSON>", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "currentNode", "previousNode", "nextNode", "tuiIsNativeFocused", "hasFocus", "tuiIsNativeFocusedIn", "focused", "contains", "tuiMoveFocus", "currentIndex", "elements", "step", "length", "focus"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-utils-focus.mjs"], "sourcesContent": ["import { tuiIsHTMLElement } from '@taiga-ui/cdk/utils/dom';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { TUI_TRUE_HANDLER, TUI_FALSE_HANDLER, svgNodeFilter } from '@taiga-ui/cdk/constants';\nimport { tuiUntrackedScheduler } from '@taiga-ui/cdk/observables';\nimport { merge, fromEvent, map, observeOn } from 'rxjs';\n\n/**\n * Returns current active element, including shadow dom\n *\n * @return element or null\n */\nfunction tuiGetNativeFocused({ activeElement }) {\n    if (!activeElement?.shadowRoot) {\n        return activeElement;\n    }\n    let element = activeElement.shadowRoot.activeElement;\n    while (element?.shadowRoot) {\n        element = element.shadowRoot.activeElement;\n    }\n    return element;\n}\n\n/**\n * Finds and blurs current active element, including shadow DOM\n */\nfunction tuiBlurNativeFocused(doc) {\n    const activeElement = tuiGetNativeFocused(doc);\n    if (tuiIsHTMLElement(activeElement)) {\n        activeElement.blur();\n    }\n}\n\nfunction tuiFocusedIn(node) {\n    return toSignal(merge(fromEvent(node, 'focus', { capture: true }).pipe(map(TUI_TRUE_HANDLER)), fromEvent(node, 'blur', { capture: true }).pipe(map(TUI_FALSE_HANDLER))).pipe(observeOn(tuiUntrackedScheduler)), { initialValue: false });\n}\n\n/**\n * Checks for signs that element can be focused with keyboard. tabIndex above 0 is ignored to\n * only target natural focus order. Not checking the possibility of an element to\n * be focused, for example element can have display: none applied to it or any other\n * circumstances could prevent actual focus.\n */\nfunction tuiIsNativeKeyboardFocusable(element) {\n    if (element.hasAttribute('disabled') || element.getAttribute('tabIndex') === '-1') {\n        return false;\n    }\n    if ((tuiIsHTMLElement(element) && element.isContentEditable) ||\n        element.getAttribute('tabIndex') === '0') {\n        return true;\n    }\n    switch (element.tagName) {\n        case 'A':\n        case 'LINK':\n            return element.hasAttribute('href');\n        case 'AUDIO':\n        case 'VIDEO':\n            return element.hasAttribute('controls');\n        case 'BUTTON':\n        case 'SELECT':\n        case 'TEXTAREA':\n            return true;\n        case 'INPUT':\n            return element.getAttribute('type') !== 'hidden';\n        default:\n            return false;\n    }\n}\n\nfunction tuiIsNativeMouseFocusable(element) {\n    return (!element.hasAttribute('disabled') &&\n        (element.getAttribute('tabIndex') === '-1' ||\n            tuiIsNativeKeyboardFocusable(element)));\n}\n\n/**\n * @description:\n * Finds the closest element that can be focused with a keyboard or mouse in theory\n */\nfunction tuiGetClosestFocusable({ initial, root, previous = false, keyboard = true, }) {\n    if (!root.ownerDocument) {\n        return null;\n    }\n    const check = keyboard ? tuiIsNativeKeyboardFocusable : tuiIsNativeMouseFocusable;\n    const treeWalker = root.ownerDocument.createTreeWalker(root, NodeFilter.SHOW_ELEMENT, svgNodeFilter);\n    treeWalker.currentNode = initial;\n    do {\n        if (tuiIsHTMLElement(treeWalker.currentNode)) {\n            initial = treeWalker.currentNode;\n        }\n        if (tuiIsHTMLElement(initial) && check(initial)) {\n            return initial;\n        }\n    } while (previous ? treeWalker.previousNode() : treeWalker.nextNode());\n    return null;\n}\n\n/**\n * Checks if element is focused.\n *\n * Could return true even after blur since element remains focused if you switch away from a browser tab.\n *\n * @param node or null (as a common return value of DOM nodes walking)\n * @return true if focused\n */\nfunction tuiIsNativeFocused(node) {\n    return (!!node?.ownerDocument &&\n        tuiGetNativeFocused(node.ownerDocument) === node &&\n        node.ownerDocument.hasFocus());\n}\n\n/**\n * Checks if focused element is within given element.\n *\n * @param node\n * @return true if focused node is contained within element\n */\nfunction tuiIsNativeFocusedIn(node) {\n    const focused = node?.ownerDocument && tuiGetNativeFocused(node.ownerDocument);\n    return !!focused && node.contains(focused) && node.ownerDocument?.hasFocus();\n}\n\n/**\n * Utility method for moving focus in a list of elements\n *\n * @param currentIndex currently focused index\n * @param elements array of focusable elements\n * @param step a step to move focus by, typically -1 or 1\n */\nfunction tuiMoveFocus(currentIndex, elements, step) {\n    currentIndex += step;\n    while (currentIndex >= 0 && currentIndex < elements.length) {\n        elements[currentIndex]?.focus();\n        if (tuiIsNativeFocused(elements[currentIndex])) {\n            return;\n        }\n        currentIndex += step;\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiBlurNativeFocused, tuiFocusedIn, tuiGetClosestFocusable, tuiGetNativeFocused, tuiIsNativeFocused, tuiIsNativeFocusedIn, tuiIsNativeKeyboardFocusable, tuiIsNativeMouseFocusable, tuiMoveFocus };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,gBAAgB,EAAEC,iBAAiB,EAAEC,aAAa,QAAQ,yBAAyB;AAC5F,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,KAAK,EAAEC,SAAS,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;;AAEvD;AACA;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAAC;EAAEC;AAAc,CAAC,EAAE;EAC5C,IAAI,CAACA,aAAa,EAAEC,UAAU,EAAE;IAC5B,OAAOD,aAAa;EACxB;EACA,IAAIE,OAAO,GAAGF,aAAa,CAACC,UAAU,CAACD,aAAa;EACpD,OAAOE,OAAO,EAAED,UAAU,EAAE;IACxBC,OAAO,GAAGA,OAAO,CAACD,UAAU,CAACD,aAAa;EAC9C;EACA,OAAOE,OAAO;AAClB;;AAEA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACC,GAAG,EAAE;EAC/B,MAAMJ,aAAa,GAAGD,mBAAmB,CAACK,GAAG,CAAC;EAC9C,IAAIf,gBAAgB,CAACW,aAAa,CAAC,EAAE;IACjCA,aAAa,CAACK,IAAI,CAAC,CAAC;EACxB;AACJ;AAEA,SAASC,YAAYA,CAACC,IAAI,EAAE;EACxB,OAAOjB,QAAQ,CAACK,KAAK,CAACC,SAAS,CAACW,IAAI,EAAE,OAAO,EAAE;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC,CAACC,IAAI,CAACZ,GAAG,CAACN,gBAAgB,CAAC,CAAC,EAAEK,SAAS,CAACW,IAAI,EAAE,MAAM,EAAE;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC,CAACC,IAAI,CAACZ,GAAG,CAACL,iBAAiB,CAAC,CAAC,CAAC,CAACiB,IAAI,CAACX,SAAS,CAACJ,qBAAqB,CAAC,CAAC,EAAE;IAAEgB,YAAY,EAAE;EAAM,CAAC,CAAC;AAC5O;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAACT,OAAO,EAAE;EAC3C,IAAIA,OAAO,CAACU,YAAY,CAAC,UAAU,CAAC,IAAIV,OAAO,CAACW,YAAY,CAAC,UAAU,CAAC,KAAK,IAAI,EAAE;IAC/E,OAAO,KAAK;EAChB;EACA,IAAKxB,gBAAgB,CAACa,OAAO,CAAC,IAAIA,OAAO,CAACY,iBAAiB,IACvDZ,OAAO,CAACW,YAAY,CAAC,UAAU,CAAC,KAAK,GAAG,EAAE;IAC1C,OAAO,IAAI;EACf;EACA,QAAQX,OAAO,CAACa,OAAO;IACnB,KAAK,GAAG;IACR,KAAK,MAAM;MACP,OAAOb,OAAO,CAACU,YAAY,CAAC,MAAM,CAAC;IACvC,KAAK,OAAO;IACZ,KAAK,OAAO;MACR,OAAOV,OAAO,CAACU,YAAY,CAAC,UAAU,CAAC;IAC3C,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,UAAU;MACX,OAAO,IAAI;IACf,KAAK,OAAO;MACR,OAAOV,OAAO,CAACW,YAAY,CAAC,MAAM,CAAC,KAAK,QAAQ;IACpD;MACI,OAAO,KAAK;EACpB;AACJ;AAEA,SAASG,yBAAyBA,CAACd,OAAO,EAAE;EACxC,OAAQ,CAACA,OAAO,CAACU,YAAY,CAAC,UAAU,CAAC,KACpCV,OAAO,CAACW,YAAY,CAAC,UAAU,CAAC,KAAK,IAAI,IACtCF,4BAA4B,CAACT,OAAO,CAAC,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA,SAASe,sBAAsBA,CAAC;EAAEC,OAAO;EAAEC,IAAI;EAAEC,QAAQ,GAAG,KAAK;EAAEC,QAAQ,GAAG;AAAM,CAAC,EAAE;EACnF,IAAI,CAACF,IAAI,CAACG,aAAa,EAAE;IACrB,OAAO,IAAI;EACf;EACA,MAAMC,KAAK,GAAGF,QAAQ,GAAGV,4BAA4B,GAAGK,yBAAyB;EACjF,MAAMQ,UAAU,GAAGL,IAAI,CAACG,aAAa,CAACG,gBAAgB,CAACN,IAAI,EAAEO,UAAU,CAACC,YAAY,EAAElC,aAAa,CAAC;EACpG+B,UAAU,CAACI,WAAW,GAAGV,OAAO;EAChC,GAAG;IACC,IAAI7B,gBAAgB,CAACmC,UAAU,CAACI,WAAW,CAAC,EAAE;MAC1CV,OAAO,GAAGM,UAAU,CAACI,WAAW;IACpC;IACA,IAAIvC,gBAAgB,CAAC6B,OAAO,CAAC,IAAIK,KAAK,CAACL,OAAO,CAAC,EAAE;MAC7C,OAAOA,OAAO;IAClB;EACJ,CAAC,QAAQE,QAAQ,GAAGI,UAAU,CAACK,YAAY,CAAC,CAAC,GAAGL,UAAU,CAACM,QAAQ,CAAC,CAAC;EACrE,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACxB,IAAI,EAAE;EAC9B,OAAQ,CAAC,CAACA,IAAI,EAAEe,aAAa,IACzBvB,mBAAmB,CAACQ,IAAI,CAACe,aAAa,CAAC,KAAKf,IAAI,IAChDA,IAAI,CAACe,aAAa,CAACU,QAAQ,CAAC,CAAC;AACrC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAC1B,IAAI,EAAE;EAChC,MAAM2B,OAAO,GAAG3B,IAAI,EAAEe,aAAa,IAAIvB,mBAAmB,CAACQ,IAAI,CAACe,aAAa,CAAC;EAC9E,OAAO,CAAC,CAACY,OAAO,IAAI3B,IAAI,CAAC4B,QAAQ,CAACD,OAAO,CAAC,IAAI3B,IAAI,CAACe,aAAa,EAAEU,QAAQ,CAAC,CAAC;AAChF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,YAAYA,CAACC,YAAY,EAAEC,QAAQ,EAAEC,IAAI,EAAE;EAChDF,YAAY,IAAIE,IAAI;EACpB,OAAOF,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAGC,QAAQ,CAACE,MAAM,EAAE;IACxDF,QAAQ,CAACD,YAAY,CAAC,EAAEI,KAAK,CAAC,CAAC;IAC/B,IAAIV,kBAAkB,CAACO,QAAQ,CAACD,YAAY,CAAC,CAAC,EAAE;MAC5C;IACJ;IACAA,YAAY,IAAIE,IAAI;EACxB;AACJ;;AAEA;AACA;AACA;;AAEA,SAASpC,oBAAoB,EAAEG,YAAY,EAAEW,sBAAsB,EAAElB,mBAAmB,EAAEgC,kBAAkB,EAAEE,oBAAoB,EAAEtB,4BAA4B,EAAEK,yBAAyB,EAAEoB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}