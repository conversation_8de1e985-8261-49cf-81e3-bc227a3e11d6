{"ast": null, "code": "import { TuiCalendarMonth } from '@taiga-ui/kit/components/calendar-month';\nimport * as i0 from '@angular/core';\nimport { inject, signal, effect, Directive } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { TUI_IDENTITY_VALUE_TRANSFORMER, TuiControl, tuiAsControl, tuiValueTransformerFrom } from '@taiga-ui/cdk/classes';\nimport { TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { RANGE_SEPARATOR_CHAR, TuiMonthRange } from '@taiga-ui/cdk/date-time';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldDirective, tuiInjectAuxiliary, tuiTextfieldIconBinding, TuiWithTextfield, TuiSelectLike } from '@taiga-ui/core/components/textfield';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { tuiDropdownOpen, tuiDropdownEnabled, TuiDropdownAuto } from '@taiga-ui/core/directives/dropdown';\nimport { TUI_MONTH_FORMATTER } from '@taiga-ui/kit/tokens';\nimport { tuiCreateTokenFromFactory, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW } from '@taiga-ui/kit/components/input-date';\nimport { TUI_INPUT_MONTH_OPTIONS } from '@taiga-ui/kit/components/input-month';\nconst TUI_INPUT_MONTH_RANGE_OPTIONS = tuiCreateTokenFromFactory(() => ({\n  ...inject(TUI_INPUT_MONTH_OPTIONS),\n  valueTransformer: TUI_IDENTITY_VALUE_TRANSFORMER\n}));\nconst tuiInputMonthRangeOptionsProvider = options => tuiProvideOptions(TUI_INPUT_MONTH_RANGE_OPTIONS, options, TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW);\nclass TuiInputMonthRangeDirective extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.textfield = inject(TuiTextfieldDirective);\n    this.formatter = toSignal(inject(TUI_MONTH_FORMATTER));\n    this.open = tuiDropdownOpen();\n    this.intermediateValue = signal(null);\n    this.calendar = tuiInjectAuxiliary(x => x instanceof TuiCalendarMonth);\n    this.icon = tuiTextfieldIconBinding(TUI_INPUT_MONTH_RANGE_OPTIONS);\n    this.dropdownEnabled = tuiDropdownEnabled(this.interactive);\n    this.valueEffect = effect(() => {\n      const value = this.value();\n      const format = this.formatter() || (() => '');\n      const string = value ? format(value.from) + RANGE_SEPARATOR_CHAR + format(value.to) : '';\n      this.textfield.value.set(string);\n    }, TUI_ALLOW_SIGNAL_WRITES);\n    this.calendarInit = effect(() => {\n      const calendar = this.calendar();\n      if (calendar) {\n        calendar.options.rangeMode = true;\n      }\n    });\n    this.calendarSync = effect(() => {\n      this.calendar()?.value.set(this.intermediateValue() ?? this.value());\n    }, TUI_ALLOW_SIGNAL_WRITES);\n    // TODO: use linked signal (Angular 19+)\n    this.resetIntermediateValue = effect(() => {\n      this.intermediateValue.set(this.value() && null);\n    }, TUI_ALLOW_SIGNAL_WRITES);\n    this.onMonthClickEffect = effect(onCleanup => {\n      const subscription = this.calendar()?.monthClick.subscribe(month => {\n        const intermediateValue = this.intermediateValue();\n        if (!intermediateValue) {\n          this.intermediateValue.set(month);\n        } else {\n          this.onChange(TuiMonthRange.sort(intermediateValue, month));\n          this.open.set(false);\n        }\n      });\n      onCleanup(() => subscription?.unsubscribe());\n    });\n  }\n  clear() {\n    this.onChange(null);\n    this.open.set(true);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiInputMonthRangeDirective_BaseFactory;\n      return function TuiInputMonthRangeDirective_Factory(t) {\n        return (ɵTuiInputMonthRangeDirective_BaseFactory || (ɵTuiInputMonthRangeDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiInputMonthRangeDirective)))(t || TuiInputMonthRangeDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiInputMonthRangeDirective,\n      selectors: [[\"input\", \"tuiInputMonthRange\", \"\"]],\n      hostVars: 1,\n      hostBindings: function TuiInputMonthRangeDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function TuiInputMonthRangeDirective_input_HostBindingHandler($event) {\n            return ($event.inputType == null ? null : $event.inputType.includes(\"delete\")) && ctx.clear();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"disabled\", ctx.disabled());\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsControl(TuiInputMonthRangeDirective), tuiValueTransformerFrom(TUI_INPUT_MONTH_RANGE_OPTIONS)]), i0.ɵɵHostDirectivesFeature([i1.TuiWithTextfield, i1.TuiSelectLike, i2.TuiDropdownAuto]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputMonthRangeDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiInputMonthRange]',\n      providers: [tuiAsControl(TuiInputMonthRangeDirective), tuiValueTransformerFrom(TUI_INPUT_MONTH_RANGE_OPTIONS)],\n      hostDirectives: [TuiWithTextfield, TuiSelectLike, TuiDropdownAuto],\n      host: {\n        '[disabled]': 'disabled()',\n        '(input)': '$event.inputType?.includes(\"delete\") && clear()'\n      }\n    }]\n  }], null, null);\n})();\nconst TuiInputMonthRange = [TuiInputMonthRangeDirective, TuiCalendarMonth];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_MONTH_RANGE_OPTIONS, TuiInputMonthRange, TuiInputMonthRangeDirective, tuiInputMonthRangeOptionsProvider };", "map": {"version": 3, "names": ["TuiCalendarMonth", "i0", "inject", "signal", "effect", "Directive", "toSignal", "TUI_IDENTITY_VALUE_TRANSFORMER", "TuiControl", "tuiAsControl", "tuiValueTransformerFrom", "TUI_ALLOW_SIGNAL_WRITES", "RANGE_SEPARATOR_CHAR", "TuiMonthRange", "i1", "TuiTextfieldDirective", "tuiInjectAuxiliary", "tuiTextfieldIconBinding", "TuiWithTextfield", "TuiSelectLike", "i2", "tuiDropdownOpen", "tuiDropdownEnabled", "TuiDropdownAuto", "TUI_MONTH_FORMATTER", "tuiCreateTokenFromFactory", "tuiProvideOptions", "TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW", "TUI_INPUT_MONTH_OPTIONS", "TUI_INPUT_MONTH_RANGE_OPTIONS", "valueTransformer", "tuiInputMonthRangeOptionsProvider", "options", "TuiInputMonthRangeDirective", "constructor", "arguments", "textfield", "formatter", "open", "intermediateValue", "calendar", "x", "icon", "dropdownEnabled", "interactive", "valueEffect", "value", "format", "string", "from", "to", "set", "calendarInit", "rangeMode", "calendarSync", "resetIntermediateValue", "onMonthClickEffect", "onCleanup", "subscription", "monthClick", "subscribe", "month", "onChange", "sort", "unsubscribe", "clear", "ɵfac", "ɵTuiInputMonthRangeDirective_BaseFactory", "TuiInputMonthRangeDirective_Factory", "t", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostVars", "hostBindings", "TuiInputMonthRangeDirective_HostBindings", "rf", "ctx", "ɵɵlistener", "TuiInputMonthRangeDirective_input_HostBindingHandler", "$event", "inputType", "includes", "ɵɵhostProperty", "disabled", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "hostDirectives", "host", "TuiInputMonthRange"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-input-month-range.mjs"], "sourcesContent": ["import { TuiCalendarMonth } from '@taiga-ui/kit/components/calendar-month';\nimport * as i0 from '@angular/core';\nimport { inject, signal, effect, Directive } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { TUI_IDENTITY_VALUE_TRANSFORMER, TuiControl, tuiAsControl, tuiValueTransformerFrom } from '@taiga-ui/cdk/classes';\nimport { TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { RANGE_SEPARATOR_CHAR, TuiMonthRange } from '@taiga-ui/cdk/date-time';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldDirective, tuiInjectAuxiliary, tuiTextfieldIconBinding, TuiWithTextfield, TuiSelectLike } from '@taiga-ui/core/components/textfield';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { tuiDropdownOpen, tuiDropdownEnabled, TuiDropdownAuto } from '@taiga-ui/core/directives/dropdown';\nimport { TUI_MONTH_FORMATTER } from '@taiga-ui/kit/tokens';\nimport { tuiCreateTokenFromFactory, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW } from '@taiga-ui/kit/components/input-date';\nimport { TUI_INPUT_MONTH_OPTIONS } from '@taiga-ui/kit/components/input-month';\n\nconst TUI_INPUT_MONTH_RANGE_OPTIONS = tuiCreateTokenFromFactory(() => ({\n    ...inject(TUI_INPUT_MONTH_OPTIONS),\n    valueTransformer: TUI_IDENTITY_VALUE_TRANSFORMER,\n}));\nconst tuiInputMonthRangeOptionsProvider = (options) => tuiProvideOptions(TUI_INPUT_MONTH_RANGE_OPTIONS, options, TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW);\n\nclass TuiInputMonthRangeDirective extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.textfield = inject(TuiTextfieldDirective);\n        this.formatter = toSignal(inject(TUI_MONTH_FORMATTER));\n        this.open = tuiDropdownOpen();\n        this.intermediateValue = signal(null);\n        this.calendar = tuiInjectAuxiliary((x) => x instanceof TuiCalendarMonth);\n        this.icon = tuiTextfieldIconBinding(TUI_INPUT_MONTH_RANGE_OPTIONS);\n        this.dropdownEnabled = tuiDropdownEnabled(this.interactive);\n        this.valueEffect = effect(() => {\n            const value = this.value();\n            const format = this.formatter() || (() => '');\n            const string = value\n                ? format(value.from) + RANGE_SEPARATOR_CHAR + format(value.to)\n                : '';\n            this.textfield.value.set(string);\n        }, TUI_ALLOW_SIGNAL_WRITES);\n        this.calendarInit = effect(() => {\n            const calendar = this.calendar();\n            if (calendar) {\n                calendar.options.rangeMode = true;\n            }\n        });\n        this.calendarSync = effect(() => {\n            this.calendar()?.value.set(this.intermediateValue() ?? this.value());\n        }, TUI_ALLOW_SIGNAL_WRITES);\n        // TODO: use linked signal (Angular 19+)\n        this.resetIntermediateValue = effect(() => {\n            this.intermediateValue.set(this.value() && null);\n        }, TUI_ALLOW_SIGNAL_WRITES);\n        this.onMonthClickEffect = effect((onCleanup) => {\n            const subscription = this.calendar()?.monthClick.subscribe((month) => {\n                const intermediateValue = this.intermediateValue();\n                if (!intermediateValue) {\n                    this.intermediateValue.set(month);\n                }\n                else {\n                    this.onChange(TuiMonthRange.sort(intermediateValue, month));\n                    this.open.set(false);\n                }\n            });\n            onCleanup(() => subscription?.unsubscribe());\n        });\n    }\n    clear() {\n        this.onChange(null);\n        this.open.set(true);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputMonthRangeDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputMonthRangeDirective, isStandalone: true, selector: \"input[tuiInputMonthRange]\", host: { listeners: { \"input\": \"$event.inputType?.includes(\\\"delete\\\") && clear()\" }, properties: { \"disabled\": \"disabled()\" } }, providers: [\n            tuiAsControl(TuiInputMonthRangeDirective),\n            tuiValueTransformerFrom(TUI_INPUT_MONTH_RANGE_OPTIONS),\n        ], usesInheritance: true, hostDirectives: [{ directive: i1.TuiWithTextfield }, { directive: i1.TuiSelectLike }, { directive: i2.TuiDropdownAuto }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputMonthRangeDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiInputMonthRange]',\n                    providers: [\n                        tuiAsControl(TuiInputMonthRangeDirective),\n                        tuiValueTransformerFrom(TUI_INPUT_MONTH_RANGE_OPTIONS),\n                    ],\n                    hostDirectives: [TuiWithTextfield, TuiSelectLike, TuiDropdownAuto],\n                    host: {\n                        '[disabled]': 'disabled()',\n                        '(input)': '$event.inputType?.includes(\"delete\") && clear()',\n                    },\n                }]\n        }] });\n\nconst TuiInputMonthRange = [\n    TuiInputMonthRangeDirective,\n    TuiCalendarMonth,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_MONTH_RANGE_OPTIONS, TuiInputMonthRange, TuiInputMonthRangeDirective, tuiInputMonthRangeOptionsProvider };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,yCAAyC;AAC1E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACjE,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,8BAA8B,EAAEC,UAAU,EAAEC,YAAY,EAAEC,uBAAuB,QAAQ,uBAAuB;AACzH,SAASC,uBAAuB,QAAQ,yBAAyB;AACjE,SAASC,oBAAoB,EAAEC,aAAa,QAAQ,yBAAyB;AAC7E,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,qBAAqB,EAAEC,kBAAkB,EAAEC,uBAAuB,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,qCAAqC;AACzJ,OAAO,KAAKC,EAAE,MAAM,oCAAoC;AACxD,SAASC,eAAe,EAAEC,kBAAkB,EAAEC,eAAe,QAAQ,oCAAoC;AACzG,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,yBAAyB,EAAEC,iBAAiB,QAAQ,mCAAmC;AAChG,SAASC,kCAAkC,QAAQ,qCAAqC;AACxF,SAASC,uBAAuB,QAAQ,sCAAsC;AAE9E,MAAMC,6BAA6B,GAAGJ,yBAAyB,CAAC,OAAO;EACnE,GAAGvB,MAAM,CAAC0B,uBAAuB,CAAC;EAClCE,gBAAgB,EAAEvB;AACtB,CAAC,CAAC,CAAC;AACH,MAAMwB,iCAAiC,GAAIC,OAAO,IAAKN,iBAAiB,CAACG,6BAA6B,EAAEG,OAAO,EAAEL,kCAAkC,CAAC;AAEpJ,MAAMM,2BAA2B,SAASzB,UAAU,CAAC;EACjD0B,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,SAAS,GAAGlC,MAAM,CAACa,qBAAqB,CAAC;IAC9C,IAAI,CAACsB,SAAS,GAAG/B,QAAQ,CAACJ,MAAM,CAACsB,mBAAmB,CAAC,CAAC;IACtD,IAAI,CAACc,IAAI,GAAGjB,eAAe,CAAC,CAAC;IAC7B,IAAI,CAACkB,iBAAiB,GAAGpC,MAAM,CAAC,IAAI,CAAC;IACrC,IAAI,CAACqC,QAAQ,GAAGxB,kBAAkB,CAAEyB,CAAC,IAAKA,CAAC,YAAYzC,gBAAgB,CAAC;IACxE,IAAI,CAAC0C,IAAI,GAAGzB,uBAAuB,CAACY,6BAA6B,CAAC;IAClE,IAAI,CAACc,eAAe,GAAGrB,kBAAkB,CAAC,IAAI,CAACsB,WAAW,CAAC;IAC3D,IAAI,CAACC,WAAW,GAAGzC,MAAM,CAAC,MAAM;MAC5B,MAAM0C,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;MAC1B,MAAMC,MAAM,GAAG,IAAI,CAACV,SAAS,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC;MAC7C,MAAMW,MAAM,GAAGF,KAAK,GACdC,MAAM,CAACD,KAAK,CAACG,IAAI,CAAC,GAAGrC,oBAAoB,GAAGmC,MAAM,CAACD,KAAK,CAACI,EAAE,CAAC,GAC5D,EAAE;MACR,IAAI,CAACd,SAAS,CAACU,KAAK,CAACK,GAAG,CAACH,MAAM,CAAC;IACpC,CAAC,EAAErC,uBAAuB,CAAC;IAC3B,IAAI,CAACyC,YAAY,GAAGhD,MAAM,CAAC,MAAM;MAC7B,MAAMoC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;MAChC,IAAIA,QAAQ,EAAE;QACVA,QAAQ,CAACR,OAAO,CAACqB,SAAS,GAAG,IAAI;MACrC;IACJ,CAAC,CAAC;IACF,IAAI,CAACC,YAAY,GAAGlD,MAAM,CAAC,MAAM;MAC7B,IAAI,CAACoC,QAAQ,CAAC,CAAC,EAAEM,KAAK,CAACK,GAAG,CAAC,IAAI,CAACZ,iBAAiB,CAAC,CAAC,IAAI,IAAI,CAACO,KAAK,CAAC,CAAC,CAAC;IACxE,CAAC,EAAEnC,uBAAuB,CAAC;IAC3B;IACA,IAAI,CAAC4C,sBAAsB,GAAGnD,MAAM,CAAC,MAAM;MACvC,IAAI,CAACmC,iBAAiB,CAACY,GAAG,CAAC,IAAI,CAACL,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC;IACpD,CAAC,EAAEnC,uBAAuB,CAAC;IAC3B,IAAI,CAAC6C,kBAAkB,GAAGpD,MAAM,CAAEqD,SAAS,IAAK;MAC5C,MAAMC,YAAY,GAAG,IAAI,CAAClB,QAAQ,CAAC,CAAC,EAAEmB,UAAU,CAACC,SAAS,CAAEC,KAAK,IAAK;QAClE,MAAMtB,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC,CAAC;QAClD,IAAI,CAACA,iBAAiB,EAAE;UACpB,IAAI,CAACA,iBAAiB,CAACY,GAAG,CAACU,KAAK,CAAC;QACrC,CAAC,MACI;UACD,IAAI,CAACC,QAAQ,CAACjD,aAAa,CAACkD,IAAI,CAACxB,iBAAiB,EAAEsB,KAAK,CAAC,CAAC;UAC3D,IAAI,CAACvB,IAAI,CAACa,GAAG,CAAC,KAAK,CAAC;QACxB;MACJ,CAAC,CAAC;MACFM,SAAS,CAAC,MAAMC,YAAY,EAAEM,WAAW,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC;EACN;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACH,QAAQ,CAAC,IAAI,CAAC;IACnB,IAAI,CAACxB,IAAI,CAACa,GAAG,CAAC,IAAI,CAAC;EACvB;EACA;IAAS,IAAI,CAACe,IAAI;MAAA,IAAAC,wCAAA;MAAA,gBAAAC,oCAAAC,CAAA;QAAA,QAAAF,wCAAA,KAAAA,wCAAA,GAA+ElE,EAAE,CAAAqE,qBAAA,CAAQrC,2BAA2B,IAAAoC,CAAA,IAA3BpC,2BAA2B;MAAA;IAAA,IAAqD;EAAE;EAC7L;IAAS,IAAI,CAACsC,IAAI,kBAD+EtE,EAAE,CAAAuE,iBAAA;MAAAC,IAAA,EACJxC,2BAA2B;MAAAyC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADzB7E,EAAE,CAAA+E,UAAA,mBAAAC,qDAAAC,MAAA;YAAA,QAAAA,MAAA,CAAAC,SAAA,kBAAAD,MAAA,CAAAC,SAAA,CAAAC,QAAA,CACuB,QAAQ,MAAKL,GAAA,CAAAd,KAAA,CAAM,CAAC;UAAA,CAArB,CAAC;QAAA;QAAA,IAAAa,EAAA;UADzB7E,EAAE,CAAAoF,cAAA,aACJN,GAAA,CAAAO,QAAA,CAAS,CAAiB,CAAC;QAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GADzBvF,EAAE,CAAAwF,kBAAA,CACgO,CAC3ThF,YAAY,CAACwB,2BAA2B,CAAC,EACzCvB,uBAAuB,CAACmB,6BAA6B,CAAC,CACzD,GAJ4F5B,EAAE,CAAAyF,uBAAA,EAIvC5E,EAAE,CAACI,gBAAgB,EAAiBJ,EAAE,CAACK,aAAa,EAAiBC,EAAE,CAACG,eAAe,IAJlDtB,EAAE,CAAA0F,0BAAA;IAAA,EAIoE;EAAE;AAC7K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KANqG3F,EAAE,CAAA4F,iBAAA,CAMX5D,2BAA2B,EAAc,CAAC;IAC1HwC,IAAI,EAAEpE,SAAS;IACfyF,IAAI,EAAE,CAAC;MACCP,UAAU,EAAE,IAAI;MAChBQ,QAAQ,EAAE,2BAA2B;MACrCC,SAAS,EAAE,CACPvF,YAAY,CAACwB,2BAA2B,CAAC,EACzCvB,uBAAuB,CAACmB,6BAA6B,CAAC,CACzD;MACDoE,cAAc,EAAE,CAAC/E,gBAAgB,EAAEC,aAAa,EAAEI,eAAe,CAAC;MAClE2E,IAAI,EAAE;QACF,YAAY,EAAE,YAAY;QAC1B,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMC,kBAAkB,GAAG,CACvBlE,2BAA2B,EAC3BjC,gBAAgB,CACnB;;AAED;AACA;AACA;;AAEA,SAAS6B,6BAA6B,EAAEsE,kBAAkB,EAAElE,2BAA2B,EAAEF,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}