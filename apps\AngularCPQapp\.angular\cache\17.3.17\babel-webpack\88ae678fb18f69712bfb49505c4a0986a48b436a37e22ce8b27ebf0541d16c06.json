{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Pi<PERSON> } from '@angular/core';\nimport { TuiFormatDateService } from '@taiga-ui/core/services';\nclass TuiFormatDatePipe {\n  constructor() {\n    this.service = inject(TuiFormatDateService);\n  }\n  transform(timestampOrDate) {\n    return this.service.format(timestampOrDate.valueOf());\n  }\n  static {\n    this.ɵfac = function TuiFormatDatePipe_Factory(t) {\n      return new (t || TuiFormatDatePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiFormatDate\",\n      type: TuiFormatDatePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFormatDatePipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiFormatDate'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFormatDatePipe };", "map": {"version": 3, "names": ["i0", "inject", "<PERSON><PERSON>", "TuiFormatDateService", "TuiFormatDatePipe", "constructor", "service", "transform", "timestampOrDate", "format", "valueOf", "ɵfac", "TuiFormatDatePipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-pipes-format-date.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { TuiFormatDateService } from '@taiga-ui/core/services';\n\nclass TuiFormatDatePipe {\n    constructor() {\n        this.service = inject(TuiFormatDateService);\n    }\n    transform(timestampOrDate) {\n        return this.service.format(timestampOrDate.valueOf());\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFormatDatePipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFormatDatePipe, isStandalone: true, name: \"tuiFormatDate\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFormatDatePipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiFormatDate',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFormatDatePipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,IAAI,QAAQ,eAAe;AAC5C,SAASC,oBAAoB,QAAQ,yBAAyB;AAE9D,MAAMC,iBAAiB,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGL,MAAM,CAACE,oBAAoB,CAAC;EAC/C;EACAI,SAASA,CAACC,eAAe,EAAE;IACvB,OAAO,IAAI,CAACF,OAAO,CAACG,MAAM,CAACD,eAAe,CAACE,OAAO,CAAC,CAAC,CAAC;EACzD;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,0BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFT,iBAAiB;IAAA,CAA8C;EAAE;EAC5K;IAAS,IAAI,CAACU,KAAK,kBAD8Ed,EAAE,CAAAe,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMb,iBAAiB;MAAAc,IAAA;MAAAC,UAAA;IAAA,EAA8C;EAAE;AAC9K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGpB,EAAE,CAAAqB,iBAAA,CAGXjB,iBAAiB,EAAc,CAAC;IAChHa,IAAI,EAAEf,IAAI;IACVoB,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASZ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}