{"ast": null, "code": "import { Optional, Self, inject } from '@angular/core';\nimport { NgControl } from '@angular/forms';\nimport { tuiControlValue } from '@taiga-ui/cdk/observables';\nimport { tuiCreateToken, tuiCreateTokenFromFactory, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { map, of } from 'rxjs';\nimport { TUI_IDENTITY_VALUE_TRANSFORMER } from '@taiga-ui/cdk/classes';\nimport { tuiExtractI18n } from '@taiga-ui/i18n/utils';\nimport { TUI_FIRST_DAY, TUI_LAST_DAY } from '@taiga-ui/cdk/date-time';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { TUI_DEFAULT_IDENTITY_MATCHER, TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { TUI_DROPDOWN_COMPONENT } from '@taiga-ui/core/directives/dropdown';\nimport { TUI_MONTHS } from '@taiga-ui/core/tokens';\n\n/**\n * Stream that emits calendar data change\n * @deprecated this should be rewritten in v5\n */\nconst TUI_CALENDAR_DATE_STREAM = tuiCreateToken();\nfunction tuiDateStreamWithTransformer(transformer) {\n  return {\n    provide: TUI_CALENDAR_DATE_STREAM,\n    deps: [[new Optional(), new Self(), NgControl], [new Optional(), transformer]],\n    useFactory: tuiControlValueFactory\n  };\n}\nfunction tuiControlValueFactory(control, transformer) {\n  return control ? tuiControlValue(control).pipe(map(value => transformer ? transformer?.fromControlValue(value) : value)) : of(null);\n}\n\n/**\n * Control value transformer of TuiDay to custom value format for InputDate* components\n */\nconst TUI_DATE_VALUE_TRANSFORMER = tuiCreateToken(TUI_IDENTITY_VALUE_TRANSFORMER);\n/**\n * Control value transformer for InputDateRange component\n */\nconst TUI_DATE_RANGE_VALUE_TRANSFORMER = tuiCreateToken();\n/**\n * Control value transformer for InputDateTime component\n */\nconst TUI_DATE_TIME_VALUE_TRANSFORMER = tuiCreateToken();\n/**\n * Control value transformer for InputTime component\n */\nconst TUI_TIME_VALUE_TRANSFORMER = tuiCreateToken();\nconst TUI_CONFIRM_WORDS = tuiCreateTokenFromFactory(tuiExtractI18n('confirm'));\nconst TUI_CANCEL_WORD = tuiCreateTokenFromFactory(tuiExtractI18n('cancel'));\nconst TUI_DONE_WORD = tuiCreateTokenFromFactory(tuiExtractI18n('done'));\nconst TUI_MORE_WORD = tuiCreateTokenFromFactory(tuiExtractI18n('more'));\nconst TUI_HIDE_TEXT = tuiCreateTokenFromFactory(tuiExtractI18n('hide'));\nconst TUI_SHOW_ALL_TEXT = tuiCreateTokenFromFactory(tuiExtractI18n('showAll'));\nconst TUI_OTHER_DATE_TEXT = tuiCreateTokenFromFactory(tuiExtractI18n('otherDate'));\nconst TUI_CHOOSE_DAY_OR_RANGE_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('mobileCalendarTexts'));\nconst TUI_FROM_TO_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('range'));\nconst TUI_PLUS_MINUS_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('countTexts'));\nconst TUI_TIME_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('time'));\nconst TUI_DATE_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('dateTexts'));\nconst TUI_DIGITAL_INFORMATION_UNITS = tuiCreateTokenFromFactory(tuiExtractI18n('digitalInformationUnits'));\nconst TUI_COPY_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('copyTexts'));\nconst TUI_PASSWORD_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('passwordTexts'));\nconst TUI_CALENDAR_MONTHS = tuiCreateTokenFromFactory(tuiExtractI18n('shortCalendarMonths'));\nconst TUI_FILE_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('fileTexts'));\nconst TUI_PAGINATION_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('pagination'));\nconst TUI_INPUT_FILE_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('inputFileTexts'));\nconst TUI_MULTI_SELECT_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('multiSelectTexts'));\nconst TUI_COUNTRIES = tuiCreateTokenFromFactory(tuiExtractI18n('countries'));\nconst TUI_PREVIEW_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('previewTexts'));\nconst TUI_PREVIEW_ZOOM_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('zoomTexts'));\nconst TUI_INTERNATIONAL_SEARCH = tuiCreateTokenFromFactory(tuiExtractI18n('phoneSearch'));\nconst TUI_INPUT_DATE_DEFAULT_OPTIONS = {\n  icon: () => '@tui.calendar',\n  min: TUI_FIRST_DAY,\n  max: TUI_LAST_DAY,\n  nativePicker: false\n};\n/**\n * Default parameters for InputDate component\n */\nconst [TUI_INPUT_DATE_OPTIONS, tuiInputDateOptionsProvider] = tuiCreateOptions(TUI_INPUT_DATE_DEFAULT_OPTIONS);\n\n/**\n * @deprecated use it only for LEGACY controls. For new controls use the same entity from `@taiga-ui/core`.\n * TODO(v5): delete\n */\nconst TUI_DEFAULT_ITEMS_HANDLERS = {\n  stringify: String,\n  identityMatcher: TUI_DEFAULT_IDENTITY_MATCHER,\n  disabledItemHandler: TUI_FALSE_HANDLER\n};\n/**\n * @deprecated use it only for LEGACY controls. For new controls use the same entity from `@taiga-ui/core`.\n * TODO(v5): delete\n */\nconst TUI_ITEMS_HANDLERS = tuiCreateToken(TUI_DEFAULT_ITEMS_HANDLERS);\n/**\n * @deprecated use it only for LEGACY controls. For new controls use the same entity from `@taiga-ui/core`.\n * TODO(v5): delete\n */\nfunction tuiItemsHandlersProvider(options) {\n  return tuiProvideOptions(TUI_ITEMS_HANDLERS, options, TUI_DEFAULT_ITEMS_HANDLERS);\n}\n\n/**\n * A component for mobile data picker\n */\nconst TUI_MOBILE_CALENDAR = tuiCreateToken();\nconst TUI_MOBILE_CALENDAR_PROVIDER = {\n  provide: TUI_DROPDOWN_COMPONENT,\n  useFactory: () => inject(TUI_IS_MOBILE) && inject(TUI_MOBILE_CALENDAR, {\n    optional: true\n  }) || inject(TUI_DROPDOWN_COMPONENT, {\n    skipSelf: true\n  })\n};\nconst TUI_MONTH_FORMATTER = tuiCreateTokenFromFactory(() => inject(TUI_MONTHS).pipe(map(months => date => {\n  if (!date) {\n    return '';\n  }\n  return `${months[date.month] ?? ''} ${date.formattedYear}`;\n})));\nconst TUI_PREVIEW_ICONS_DEFAULT = {\n  rotate: '@tui.rotate-ccw-square',\n  prev: '@tui.arrow-left',\n  next: '@tui.arrow-right',\n  zoomIn: '@tui.plus',\n  zoomOut: '@tui.minus',\n  zoomReset: '@tui.minimize'\n};\nconst TUI_PREVIEW_ICONS = tuiCreateToken(TUI_PREVIEW_ICONS_DEFAULT);\nfunction tuiPreviewIconsProvider(icons) {\n  return tuiProvideOptions(TUI_PREVIEW_ICONS, icons, TUI_PREVIEW_ICONS_DEFAULT);\n}\nconst TUI_VALIDATION_ERRORS = tuiCreateToken({});\nconst tuiValidationErrorsProvider = useValue => ({\n  provide: TUI_VALIDATION_ERRORS,\n  useValue\n});\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_CALENDAR_DATE_STREAM, TUI_CALENDAR_MONTHS, TUI_CANCEL_WORD, TUI_CHOOSE_DAY_OR_RANGE_TEXTS, TUI_CONFIRM_WORDS, TUI_COPY_TEXTS, TUI_COUNTRIES, TUI_DATE_RANGE_VALUE_TRANSFORMER, TUI_DATE_TEXTS, TUI_DATE_TIME_VALUE_TRANSFORMER, TUI_DATE_VALUE_TRANSFORMER, TUI_DEFAULT_ITEMS_HANDLERS, TUI_DIGITAL_INFORMATION_UNITS, TUI_DONE_WORD, TUI_FILE_TEXTS, TUI_FROM_TO_TEXTS, TUI_HIDE_TEXT, TUI_INPUT_DATE_DEFAULT_OPTIONS, TUI_INPUT_DATE_OPTIONS, TUI_INPUT_FILE_TEXTS, TUI_INTERNATIONAL_SEARCH, TUI_ITEMS_HANDLERS, TUI_MOBILE_CALENDAR, TUI_MOBILE_CALENDAR_PROVIDER, TUI_MONTH_FORMATTER, TUI_MORE_WORD, TUI_MULTI_SELECT_TEXTS, TUI_OTHER_DATE_TEXT, TUI_PAGINATION_TEXTS, TUI_PASSWORD_TEXTS, TUI_PLUS_MINUS_TEXTS, TUI_PREVIEW_ICONS, TUI_PREVIEW_ICONS_DEFAULT, TUI_PREVIEW_TEXTS, TUI_PREVIEW_ZOOM_TEXTS, TUI_SHOW_ALL_TEXT, TUI_TIME_TEXTS, TUI_TIME_VALUE_TRANSFORMER, TUI_VALIDATION_ERRORS, tuiDateStreamWithTransformer, tuiInputDateOptionsProvider, tuiItemsHandlersProvider, tuiPreviewIconsProvider, tuiValidationErrorsProvider };", "map": {"version": 3, "names": ["Optional", "Self", "inject", "NgControl", "tuiControlValue", "tuiCreateToken", "tuiCreateTokenFromFactory", "tuiProvideOptions", "map", "of", "TUI_IDENTITY_VALUE_TRANSFORMER", "tuiExtractI18n", "TUI_FIRST_DAY", "TUI_LAST_DAY", "tuiCreateOptions", "TUI_DEFAULT_IDENTITY_MATCHER", "TUI_FALSE_HANDLER", "TUI_IS_MOBILE", "TUI_DROPDOWN_COMPONENT", "TUI_MONTHS", "TUI_CALENDAR_DATE_STREAM", "tuiDateStreamWithTransformer", "transformer", "provide", "deps", "useFactory", "tuiControlValueFactory", "control", "pipe", "value", "fromControlValue", "TUI_DATE_VALUE_TRANSFORMER", "TUI_DATE_RANGE_VALUE_TRANSFORMER", "TUI_DATE_TIME_VALUE_TRANSFORMER", "TUI_TIME_VALUE_TRANSFORMER", "TUI_CONFIRM_WORDS", "TUI_CANCEL_WORD", "TUI_DONE_WORD", "TUI_MORE_WORD", "TUI_HIDE_TEXT", "TUI_SHOW_ALL_TEXT", "TUI_OTHER_DATE_TEXT", "TUI_CHOOSE_DAY_OR_RANGE_TEXTS", "TUI_FROM_TO_TEXTS", "TUI_PLUS_MINUS_TEXTS", "TUI_TIME_TEXTS", "TUI_DATE_TEXTS", "TUI_DIGITAL_INFORMATION_UNITS", "TUI_COPY_TEXTS", "TUI_PASSWORD_TEXTS", "TUI_CALENDAR_MONTHS", "TUI_FILE_TEXTS", "TUI_PAGINATION_TEXTS", "TUI_INPUT_FILE_TEXTS", "TUI_MULTI_SELECT_TEXTS", "TUI_COUNTRIES", "TUI_PREVIEW_TEXTS", "TUI_PREVIEW_ZOOM_TEXTS", "TUI_INTERNATIONAL_SEARCH", "TUI_INPUT_DATE_DEFAULT_OPTIONS", "icon", "min", "max", "nativePicker", "TUI_INPUT_DATE_OPTIONS", "tuiInputDateOptionsProvider", "TUI_DEFAULT_ITEMS_HANDLERS", "stringify", "String", "identityMatcher", "disabledItemHandler", "TUI_ITEMS_HANDLERS", "tuiItemsHandlersProvider", "options", "TUI_MOBILE_CALENDAR", "TUI_MOBILE_CALENDAR_PROVIDER", "optional", "skipSelf", "TUI_MONTH_FORMATTER", "months", "date", "month", "formattedYear", "TUI_PREVIEW_ICONS_DEFAULT", "rotate", "prev", "next", "zoomIn", "zoomOut", "zoomReset", "TUI_PREVIEW_ICONS", "tuiPreviewIconsProvider", "icons", "TUI_VALIDATION_ERRORS", "tuiValidationErrorsProvider", "useValue"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-tokens.mjs"], "sourcesContent": ["import { Optional, Self, inject } from '@angular/core';\nimport { NgControl } from '@angular/forms';\nimport { tuiControlValue } from '@taiga-ui/cdk/observables';\nimport { tuiCreateToken, tuiCreateTokenFromFactory, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { map, of } from 'rxjs';\nimport { TUI_IDENTITY_VALUE_TRANSFORMER } from '@taiga-ui/cdk/classes';\nimport { tuiExtractI18n } from '@taiga-ui/i18n/utils';\nimport { TUI_FIRST_DAY, TUI_LAST_DAY } from '@taiga-ui/cdk/date-time';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { TUI_DEFAULT_IDENTITY_MATCHER, TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { TUI_DROPDOWN_COMPONENT } from '@taiga-ui/core/directives/dropdown';\nimport { TUI_MONTHS } from '@taiga-ui/core/tokens';\n\n/**\n * Stream that emits calendar data change\n * @deprecated this should be rewritten in v5\n */\nconst TUI_CALENDAR_DATE_STREAM = tuiCreateToken();\nfunction tuiDateStreamWithTransformer(transformer) {\n    return {\n        provide: TUI_CALENDAR_DATE_STREAM,\n        deps: [\n            [new Optional(), new Self(), NgControl],\n            [new Optional(), transformer],\n        ],\n        useFactory: tuiControlValueFactory,\n    };\n}\nfunction tuiControlValueFactory(control, transformer) {\n    return control\n        ? tuiControlValue(control).pipe(map((value) => transformer ? transformer?.fromControlValue(value) : value))\n        : of(null);\n}\n\n/**\n * Control value transformer of TuiDay to custom value format for InputDate* components\n */\nconst TUI_DATE_VALUE_TRANSFORMER = tuiCreateToken(TUI_IDENTITY_VALUE_TRANSFORMER);\n/**\n * Control value transformer for InputDateRange component\n */\nconst TUI_DATE_RANGE_VALUE_TRANSFORMER = tuiCreateToken();\n/**\n * Control value transformer for InputDateTime component\n */\nconst TUI_DATE_TIME_VALUE_TRANSFORMER = tuiCreateToken();\n/**\n * Control value transformer for InputTime component\n */\nconst TUI_TIME_VALUE_TRANSFORMER = tuiCreateToken();\n\nconst TUI_CONFIRM_WORDS = tuiCreateTokenFromFactory(tuiExtractI18n('confirm'));\nconst TUI_CANCEL_WORD = tuiCreateTokenFromFactory(tuiExtractI18n('cancel'));\nconst TUI_DONE_WORD = tuiCreateTokenFromFactory(tuiExtractI18n('done'));\nconst TUI_MORE_WORD = tuiCreateTokenFromFactory(tuiExtractI18n('more'));\nconst TUI_HIDE_TEXT = tuiCreateTokenFromFactory(tuiExtractI18n('hide'));\nconst TUI_SHOW_ALL_TEXT = tuiCreateTokenFromFactory(tuiExtractI18n('showAll'));\nconst TUI_OTHER_DATE_TEXT = tuiCreateTokenFromFactory(tuiExtractI18n('otherDate'));\nconst TUI_CHOOSE_DAY_OR_RANGE_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('mobileCalendarTexts'));\nconst TUI_FROM_TO_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('range'));\nconst TUI_PLUS_MINUS_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('countTexts'));\nconst TUI_TIME_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('time'));\nconst TUI_DATE_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('dateTexts'));\nconst TUI_DIGITAL_INFORMATION_UNITS = tuiCreateTokenFromFactory(tuiExtractI18n('digitalInformationUnits'));\nconst TUI_COPY_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('copyTexts'));\nconst TUI_PASSWORD_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('passwordTexts'));\nconst TUI_CALENDAR_MONTHS = tuiCreateTokenFromFactory(tuiExtractI18n('shortCalendarMonths'));\nconst TUI_FILE_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('fileTexts'));\nconst TUI_PAGINATION_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('pagination'));\nconst TUI_INPUT_FILE_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('inputFileTexts'));\nconst TUI_MULTI_SELECT_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('multiSelectTexts'));\nconst TUI_COUNTRIES = tuiCreateTokenFromFactory(tuiExtractI18n('countries'));\nconst TUI_PREVIEW_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('previewTexts'));\nconst TUI_PREVIEW_ZOOM_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('zoomTexts'));\nconst TUI_INTERNATIONAL_SEARCH = tuiCreateTokenFromFactory(tuiExtractI18n('phoneSearch'));\n\nconst TUI_INPUT_DATE_DEFAULT_OPTIONS = {\n    icon: () => '@tui.calendar',\n    min: TUI_FIRST_DAY,\n    max: TUI_LAST_DAY,\n    nativePicker: false,\n};\n/**\n * Default parameters for InputDate component\n */\nconst [TUI_INPUT_DATE_OPTIONS, tuiInputDateOptionsProvider] = tuiCreateOptions(TUI_INPUT_DATE_DEFAULT_OPTIONS);\n\n/**\n * @deprecated use it only for LEGACY controls. For new controls use the same entity from `@taiga-ui/core`.\n * TODO(v5): delete\n */\nconst TUI_DEFAULT_ITEMS_HANDLERS = {\n    stringify: String,\n    identityMatcher: TUI_DEFAULT_IDENTITY_MATCHER,\n    disabledItemHandler: TUI_FALSE_HANDLER,\n};\n/**\n * @deprecated use it only for LEGACY controls. For new controls use the same entity from `@taiga-ui/core`.\n * TODO(v5): delete\n */\nconst TUI_ITEMS_HANDLERS = tuiCreateToken(TUI_DEFAULT_ITEMS_HANDLERS);\n/**\n * @deprecated use it only for LEGACY controls. For new controls use the same entity from `@taiga-ui/core`.\n * TODO(v5): delete\n */\nfunction tuiItemsHandlersProvider(options) {\n    return tuiProvideOptions(TUI_ITEMS_HANDLERS, options, TUI_DEFAULT_ITEMS_HANDLERS);\n}\n\n/**\n * A component for mobile data picker\n */\nconst TUI_MOBILE_CALENDAR = tuiCreateToken();\nconst TUI_MOBILE_CALENDAR_PROVIDER = {\n    provide: TUI_DROPDOWN_COMPONENT,\n    useFactory: () => (inject(TUI_IS_MOBILE) && inject(TUI_MOBILE_CALENDAR, { optional: true })) ||\n        inject(TUI_DROPDOWN_COMPONENT, { skipSelf: true }),\n};\n\nconst TUI_MONTH_FORMATTER = tuiCreateTokenFromFactory(() => inject(TUI_MONTHS).pipe(map((months) => (date) => {\n    if (!date) {\n        return '';\n    }\n    return `${months[date.month] ?? ''} ${date.formattedYear}`;\n})));\n\nconst TUI_PREVIEW_ICONS_DEFAULT = {\n    rotate: '@tui.rotate-ccw-square',\n    prev: '@tui.arrow-left',\n    next: '@tui.arrow-right',\n    zoomIn: '@tui.plus',\n    zoomOut: '@tui.minus',\n    zoomReset: '@tui.minimize',\n};\nconst TUI_PREVIEW_ICONS = tuiCreateToken(TUI_PREVIEW_ICONS_DEFAULT);\nfunction tuiPreviewIconsProvider(icons) {\n    return tuiProvideOptions(TUI_PREVIEW_ICONS, icons, TUI_PREVIEW_ICONS_DEFAULT);\n}\n\nconst TUI_VALIDATION_ERRORS = tuiCreateToken({});\nconst tuiValidationErrorsProvider = (useValue) => ({ provide: TUI_VALIDATION_ERRORS, useValue });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_CALENDAR_DATE_STREAM, TUI_CALENDAR_MONTHS, TUI_CANCEL_WORD, TUI_CHOOSE_DAY_OR_RANGE_TEXTS, TUI_CONFIRM_WORDS, TUI_COPY_TEXTS, TUI_COUNTRIES, TUI_DATE_RANGE_VALUE_TRANSFORMER, TUI_DATE_TEXTS, TUI_DATE_TIME_VALUE_TRANSFORMER, TUI_DATE_VALUE_TRANSFORMER, TUI_DEFAULT_ITEMS_HANDLERS, TUI_DIGITAL_INFORMATION_UNITS, TUI_DONE_WORD, TUI_FILE_TEXTS, TUI_FROM_TO_TEXTS, TUI_HIDE_TEXT, TUI_INPUT_DATE_DEFAULT_OPTIONS, TUI_INPUT_DATE_OPTIONS, TUI_INPUT_FILE_TEXTS, TUI_INTERNATIONAL_SEARCH, TUI_ITEMS_HANDLERS, TUI_MOBILE_CALENDAR, TUI_MOBILE_CALENDAR_PROVIDER, TUI_MONTH_FORMATTER, TUI_MORE_WORD, TUI_MULTI_SELECT_TEXTS, TUI_OTHER_DATE_TEXT, TUI_PAGINATION_TEXTS, TUI_PASSWORD_TEXTS, TUI_PLUS_MINUS_TEXTS, TUI_PREVIEW_ICONS, TUI_PREVIEW_ICONS_DEFAULT, TUI_PREVIEW_TEXTS, TUI_PREVIEW_ZOOM_TEXTS, TUI_SHOW_ALL_TEXT, TUI_TIME_TEXTS, TUI_TIME_VALUE_TRANSFORMER, TUI_VALIDATION_ERRORS, tuiDateStreamWithTransformer, tuiInputDateOptionsProvider, tuiItemsHandlersProvider, tuiPreviewIconsProvider, tuiValidationErrorsProvider };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,IAAI,EAAEC,MAAM,QAAQ,eAAe;AACtD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,cAAc,EAAEC,yBAAyB,EAAEC,iBAAiB,QAAQ,mCAAmC;AAChH,SAASC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAC9B,SAASC,8BAA8B,QAAQ,uBAAuB;AACtE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,aAAa,EAAEC,YAAY,QAAQ,yBAAyB;AACrE,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,4BAA4B,EAAEC,iBAAiB,QAAQ,yBAAyB;AACzF,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,UAAU,QAAQ,uBAAuB;;AAElD;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAGf,cAAc,CAAC,CAAC;AACjD,SAASgB,4BAA4BA,CAACC,WAAW,EAAE;EAC/C,OAAO;IACHC,OAAO,EAAEH,wBAAwB;IACjCI,IAAI,EAAE,CACF,CAAC,IAAIxB,QAAQ,CAAC,CAAC,EAAE,IAAIC,IAAI,CAAC,CAAC,EAAEE,SAAS,CAAC,EACvC,CAAC,IAAIH,QAAQ,CAAC,CAAC,EAAEsB,WAAW,CAAC,CAChC;IACDG,UAAU,EAAEC;EAChB,CAAC;AACL;AACA,SAASA,sBAAsBA,CAACC,OAAO,EAAEL,WAAW,EAAE;EAClD,OAAOK,OAAO,GACRvB,eAAe,CAACuB,OAAO,CAAC,CAACC,IAAI,CAACpB,GAAG,CAAEqB,KAAK,IAAKP,WAAW,GAAGA,WAAW,EAAEQ,gBAAgB,CAACD,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,GACzGpB,EAAE,CAAC,IAAI,CAAC;AAClB;;AAEA;AACA;AACA;AACA,MAAMsB,0BAA0B,GAAG1B,cAAc,CAACK,8BAA8B,CAAC;AACjF;AACA;AACA;AACA,MAAMsB,gCAAgC,GAAG3B,cAAc,CAAC,CAAC;AACzD;AACA;AACA;AACA,MAAM4B,+BAA+B,GAAG5B,cAAc,CAAC,CAAC;AACxD;AACA;AACA;AACA,MAAM6B,0BAA0B,GAAG7B,cAAc,CAAC,CAAC;AAEnD,MAAM8B,iBAAiB,GAAG7B,yBAAyB,CAACK,cAAc,CAAC,SAAS,CAAC,CAAC;AAC9E,MAAMyB,eAAe,GAAG9B,yBAAyB,CAACK,cAAc,CAAC,QAAQ,CAAC,CAAC;AAC3E,MAAM0B,aAAa,GAAG/B,yBAAyB,CAACK,cAAc,CAAC,MAAM,CAAC,CAAC;AACvE,MAAM2B,aAAa,GAAGhC,yBAAyB,CAACK,cAAc,CAAC,MAAM,CAAC,CAAC;AACvE,MAAM4B,aAAa,GAAGjC,yBAAyB,CAACK,cAAc,CAAC,MAAM,CAAC,CAAC;AACvE,MAAM6B,iBAAiB,GAAGlC,yBAAyB,CAACK,cAAc,CAAC,SAAS,CAAC,CAAC;AAC9E,MAAM8B,mBAAmB,GAAGnC,yBAAyB,CAACK,cAAc,CAAC,WAAW,CAAC,CAAC;AAClF,MAAM+B,6BAA6B,GAAGpC,yBAAyB,CAACK,cAAc,CAAC,qBAAqB,CAAC,CAAC;AACtG,MAAMgC,iBAAiB,GAAGrC,yBAAyB,CAACK,cAAc,CAAC,OAAO,CAAC,CAAC;AAC5E,MAAMiC,oBAAoB,GAAGtC,yBAAyB,CAACK,cAAc,CAAC,YAAY,CAAC,CAAC;AACpF,MAAMkC,cAAc,GAAGvC,yBAAyB,CAACK,cAAc,CAAC,MAAM,CAAC,CAAC;AACxE,MAAMmC,cAAc,GAAGxC,yBAAyB,CAACK,cAAc,CAAC,WAAW,CAAC,CAAC;AAC7E,MAAMoC,6BAA6B,GAAGzC,yBAAyB,CAACK,cAAc,CAAC,yBAAyB,CAAC,CAAC;AAC1G,MAAMqC,cAAc,GAAG1C,yBAAyB,CAACK,cAAc,CAAC,WAAW,CAAC,CAAC;AAC7E,MAAMsC,kBAAkB,GAAG3C,yBAAyB,CAACK,cAAc,CAAC,eAAe,CAAC,CAAC;AACrF,MAAMuC,mBAAmB,GAAG5C,yBAAyB,CAACK,cAAc,CAAC,qBAAqB,CAAC,CAAC;AAC5F,MAAMwC,cAAc,GAAG7C,yBAAyB,CAACK,cAAc,CAAC,WAAW,CAAC,CAAC;AAC7E,MAAMyC,oBAAoB,GAAG9C,yBAAyB,CAACK,cAAc,CAAC,YAAY,CAAC,CAAC;AACpF,MAAM0C,oBAAoB,GAAG/C,yBAAyB,CAACK,cAAc,CAAC,gBAAgB,CAAC,CAAC;AACxF,MAAM2C,sBAAsB,GAAGhD,yBAAyB,CAACK,cAAc,CAAC,kBAAkB,CAAC,CAAC;AAC5F,MAAM4C,aAAa,GAAGjD,yBAAyB,CAACK,cAAc,CAAC,WAAW,CAAC,CAAC;AAC5E,MAAM6C,iBAAiB,GAAGlD,yBAAyB,CAACK,cAAc,CAAC,cAAc,CAAC,CAAC;AACnF,MAAM8C,sBAAsB,GAAGnD,yBAAyB,CAACK,cAAc,CAAC,WAAW,CAAC,CAAC;AACrF,MAAM+C,wBAAwB,GAAGpD,yBAAyB,CAACK,cAAc,CAAC,aAAa,CAAC,CAAC;AAEzF,MAAMgD,8BAA8B,GAAG;EACnCC,IAAI,EAAEA,CAAA,KAAM,eAAe;EAC3BC,GAAG,EAAEjD,aAAa;EAClBkD,GAAG,EAAEjD,YAAY;EACjBkD,YAAY,EAAE;AAClB,CAAC;AACD;AACA;AACA;AACA,MAAM,CAACC,sBAAsB,EAAEC,2BAA2B,CAAC,GAAGnD,gBAAgB,CAAC6C,8BAA8B,CAAC;;AAE9G;AACA;AACA;AACA;AACA,MAAMO,0BAA0B,GAAG;EAC/BC,SAAS,EAAEC,MAAM;EACjBC,eAAe,EAAEtD,4BAA4B;EAC7CuD,mBAAmB,EAAEtD;AACzB,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMuD,kBAAkB,GAAGlE,cAAc,CAAC6D,0BAA0B,CAAC;AACrE;AACA;AACA;AACA;AACA,SAASM,wBAAwBA,CAACC,OAAO,EAAE;EACvC,OAAOlE,iBAAiB,CAACgE,kBAAkB,EAAEE,OAAO,EAAEP,0BAA0B,CAAC;AACrF;;AAEA;AACA;AACA;AACA,MAAMQ,mBAAmB,GAAGrE,cAAc,CAAC,CAAC;AAC5C,MAAMsE,4BAA4B,GAAG;EACjCpD,OAAO,EAAEL,sBAAsB;EAC/BO,UAAU,EAAEA,CAAA,KAAOvB,MAAM,CAACe,aAAa,CAAC,IAAIf,MAAM,CAACwE,mBAAmB,EAAE;IAAEE,QAAQ,EAAE;EAAK,CAAC,CAAC,IACvF1E,MAAM,CAACgB,sBAAsB,EAAE;IAAE2D,QAAQ,EAAE;EAAK,CAAC;AACzD,CAAC;AAED,MAAMC,mBAAmB,GAAGxE,yBAAyB,CAAC,MAAMJ,MAAM,CAACiB,UAAU,CAAC,CAACS,IAAI,CAACpB,GAAG,CAAEuE,MAAM,IAAMC,IAAI,IAAK;EAC1G,IAAI,CAACA,IAAI,EAAE;IACP,OAAO,EAAE;EACb;EACA,OAAO,GAAGD,MAAM,CAACC,IAAI,CAACC,KAAK,CAAC,IAAI,EAAE,IAAID,IAAI,CAACE,aAAa,EAAE;AAC9D,CAAC,CAAC,CAAC,CAAC;AAEJ,MAAMC,yBAAyB,GAAG;EAC9BC,MAAM,EAAE,wBAAwB;EAChCC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,kBAAkB;EACxBC,MAAM,EAAE,WAAW;EACnBC,OAAO,EAAE,YAAY;EACrBC,SAAS,EAAE;AACf,CAAC;AACD,MAAMC,iBAAiB,GAAGrF,cAAc,CAAC8E,yBAAyB,CAAC;AACnE,SAASQ,uBAAuBA,CAACC,KAAK,EAAE;EACpC,OAAOrF,iBAAiB,CAACmF,iBAAiB,EAAEE,KAAK,EAAET,yBAAyB,CAAC;AACjF;AAEA,MAAMU,qBAAqB,GAAGxF,cAAc,CAAC,CAAC,CAAC,CAAC;AAChD,MAAMyF,2BAA2B,GAAIC,QAAQ,KAAM;EAAExE,OAAO,EAAEsE,qBAAqB;EAAEE;AAAS,CAAC,CAAC;;AAEhG;AACA;AACA;;AAEA,SAAS3E,wBAAwB,EAAE8B,mBAAmB,EAAEd,eAAe,EAAEM,6BAA6B,EAAEP,iBAAiB,EAAEa,cAAc,EAAEO,aAAa,EAAEvB,gCAAgC,EAAEc,cAAc,EAAEb,+BAA+B,EAAEF,0BAA0B,EAAEmC,0BAA0B,EAAEnB,6BAA6B,EAAEV,aAAa,EAAEc,cAAc,EAAER,iBAAiB,EAAEJ,aAAa,EAAEoB,8BAA8B,EAAEK,sBAAsB,EAAEX,oBAAoB,EAAEK,wBAAwB,EAAEa,kBAAkB,EAAEG,mBAAmB,EAAEC,4BAA4B,EAAEG,mBAAmB,EAAExC,aAAa,EAAEgB,sBAAsB,EAAEb,mBAAmB,EAAEW,oBAAoB,EAAEH,kBAAkB,EAAEL,oBAAoB,EAAE8C,iBAAiB,EAAEP,yBAAyB,EAAE3B,iBAAiB,EAAEC,sBAAsB,EAAEjB,iBAAiB,EAAEK,cAAc,EAAEX,0BAA0B,EAAE2D,qBAAqB,EAAExE,4BAA4B,EAAE4C,2BAA2B,EAAEO,wBAAwB,EAAEmB,uBAAuB,EAAEG,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}