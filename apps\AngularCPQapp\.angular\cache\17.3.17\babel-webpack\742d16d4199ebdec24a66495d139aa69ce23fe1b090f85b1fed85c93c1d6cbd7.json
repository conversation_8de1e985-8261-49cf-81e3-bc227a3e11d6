{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { TUI_COUNTRIES } from '@taiga-ui/kit/tokens';\nimport { map } from 'rxjs';\nclass TuiSortCountriesPipe {\n  constructor() {\n    this.countriesNames$ = inject(TUI_COUNTRIES);\n  }\n  transform(countries) {\n    return this.countriesNames$.pipe(map(names => [...countries].sort((a, b) => names[a].localeCompare(names[b]))));\n  }\n  static {\n    this.ɵfac = function TuiSortCountriesPipe_Factory(t) {\n      return new (t || TuiSortCountriesPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiSortCountries\",\n      type: TuiSortCountriesPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSortCountriesPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiSortCountries'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiSortCountriesPipe };", "map": {"version": 3, "names": ["i0", "inject", "<PERSON><PERSON>", "TUI_COUNTRIES", "map", "TuiSortCountriesPipe", "constructor", "countriesNames$", "transform", "countries", "pipe", "names", "sort", "a", "b", "localeCompare", "ɵfac", "TuiSortCountriesPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-pipes-sort-countries.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\nimport { TUI_COUNTRIES } from '@taiga-ui/kit/tokens';\nimport { map } from 'rxjs';\n\nclass TuiSortCountriesPipe {\n    constructor() {\n        this.countriesNames$ = inject(TUI_COUNTRIES);\n    }\n    transform(countries) {\n        return this.countriesNames$.pipe(map((names) => [...countries].sort((a, b) => names[a].localeCompare(names[b]))));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSortCountriesPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSortCountriesPipe, isStandalone: true, name: \"tuiSortCountries\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSortCountriesPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiSortCountries',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiSortCountriesPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,IAAI,QAAQ,eAAe;AAC5C,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,GAAG,QAAQ,MAAM;AAE1B,MAAMC,oBAAoB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,eAAe,GAAGN,MAAM,CAACE,aAAa,CAAC;EAChD;EACAK,SAASA,CAACC,SAAS,EAAE;IACjB,OAAO,IAAI,CAACF,eAAe,CAACG,IAAI,CAACN,GAAG,CAAEO,KAAK,IAAK,CAAC,GAAGF,SAAS,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKH,KAAK,CAACE,CAAC,CAAC,CAACE,aAAa,CAACJ,KAAK,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrH;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFb,oBAAoB;IAAA,CAA8C;EAAE;EAC/K;IAAS,IAAI,CAACc,KAAK,kBAD8EnB,EAAE,CAAAoB,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMjB,oBAAoB;MAAAkB,IAAA;MAAAC,UAAA;IAAA,EAAiD;EAAE;AACpL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGzB,EAAE,CAAA0B,iBAAA,CAGXrB,oBAAoB,EAAc,CAAC;IACnHiB,IAAI,EAAEpB,IAAI;IACVyB,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAShB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}