{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, NgZone, Injectable, Directive, Output } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { TUI_TRUE_HANDLER, TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { tuiTypedFromEvent, tuiZoneOptimized, tuiWatch } from '@taiga-ui/cdk/observables';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiIsElement, tuiInjectElement } from '@taiga-ui/cdk/utils';\nimport { Observable, merge, map, filter, distinctUntilChanged, of } from 'rxjs';\nfunction movedOut({\n  currentTarget,\n  relatedTarget\n}) {\n  return !tuiIsElement(relatedTarget) || !tuiIsElement(currentTarget) || !currentTarget.contains(relatedTarget);\n}\nclass TuiHoveredService extends Observable {\n  constructor() {\n    super(subscriber => this.stream$.subscribe(subscriber));\n    this.el = tuiInjectElement();\n    this.zone = inject(NgZone);\n    this.stream$ = merge(tuiTypedFromEvent(this.el, 'mouseenter').pipe(map(TUI_TRUE_HANDLER)), tuiTypedFromEvent(this.el, 'mouseleave').pipe(map(TUI_FALSE_HANDLER)),\n    // Hello, Safari\n    tuiTypedFromEvent(this.el, 'mouseout').pipe(filter(movedOut), map(TUI_FALSE_HANDLER))).pipe(distinctUntilChanged(), tuiZoneOptimized(this.zone));\n  }\n  static {\n    this.ɵfac = function TuiHoveredService_Factory(t) {\n      return new (t || TuiHoveredService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiHoveredService,\n      factory: TuiHoveredService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHoveredService, [{\n    type: Injectable\n  }], function () {\n    return [];\n  }, null);\n})();\nfunction tuiHovered() {\n  return toSignal(inject(TUI_IS_MOBILE) ? of(false) : inject(TuiHoveredService).pipe(tuiWatch()), {\n    initialValue: false\n  });\n}\nclass TuiHovered {\n  constructor() {\n    this.tuiHoveredChange = inject(TuiHoveredService);\n  }\n  static {\n    this.ɵfac = function TuiHovered_Factory(t) {\n      return new (t || TuiHovered)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiHovered,\n      selectors: [[\"\", \"tuiHoveredChange\", \"\"]],\n      outputs: {\n        tuiHoveredChange: \"tuiHoveredChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TuiHoveredService])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiHovered, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiHoveredChange]',\n      providers: [TuiHoveredService]\n    }]\n  }], null, {\n    tuiHoveredChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiHovered, TuiHoveredService, tuiHovered };", "map": {"version": 3, "names": ["i0", "inject", "NgZone", "Injectable", "Directive", "Output", "toSignal", "TUI_TRUE_HANDLER", "TUI_FALSE_HANDLER", "tuiTypedFromEvent", "tuiZoneOptimized", "tuiWatch", "TUI_IS_MOBILE", "tuiIsElement", "tuiInjectElement", "Observable", "merge", "map", "filter", "distinctUntilChanged", "of", "movedOut", "currentTarget", "relatedTarget", "contains", "TuiHoveredService", "constructor", "subscriber", "stream$", "subscribe", "el", "zone", "pipe", "ɵfac", "TuiHoveredService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "tuiHovered", "initialValue", "TuiHovered", "tuiHoveredChange", "TuiHovered_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "outputs", "standalone", "features", "ɵɵProvidersFeature", "args", "selector", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-hovered.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, NgZone, Injectable, Directive, Output } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { TUI_TRUE_HANDLER, TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { tuiTypedFromEvent, tuiZoneOptimized, tuiWatch } from '@taiga-ui/cdk/observables';\nimport { TUI_IS_MOBILE } from '@taiga-ui/cdk/tokens';\nimport { tuiIsElement, tuiInjectElement } from '@taiga-ui/cdk/utils';\nimport { Observable, merge, map, filter, distinctUntilChanged, of } from 'rxjs';\n\nfunction movedOut({ currentTarget, relatedTarget }) {\n    return (!tuiIsElement(relatedTarget) ||\n        !tuiIsElement(currentTarget) ||\n        !currentTarget.contains(relatedTarget));\n}\nclass TuiHoveredService extends Observable {\n    constructor() {\n        super((subscriber) => this.stream$.subscribe(subscriber));\n        this.el = tuiInjectElement();\n        this.zone = inject(NgZone);\n        this.stream$ = merge(tuiTypedFromEvent(this.el, 'mouseenter').pipe(map(TUI_TRUE_HANDLER)), tuiTypedFromEvent(this.el, 'mouseleave').pipe(map(TUI_FALSE_HANDLER)), \n        // Hello, Safari\n        tuiTypedFromEvent(this.el, 'mouseout').pipe(filter(movedOut), map(TUI_FALSE_HANDLER))).pipe(distinctUntilChanged(), tuiZoneOptimized(this.zone));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHoveredService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHoveredService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHoveredService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return []; } });\nfunction tuiHovered() {\n    return toSignal(inject(TUI_IS_MOBILE) ? of(false) : inject(TuiHoveredService).pipe(tuiWatch()), {\n        initialValue: false,\n    });\n}\n\nclass TuiHovered {\n    constructor() {\n        this.tuiHoveredChange = inject(TuiHoveredService);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHovered, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiHovered, isStandalone: true, selector: \"[tuiHoveredChange]\", outputs: { tuiHoveredChange: \"tuiHoveredChange\" }, providers: [TuiHoveredService], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiHovered, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiHoveredChange]',\n                    providers: [TuiHoveredService],\n                }]\n        }], propDecorators: { tuiHoveredChange: [{\n                type: Output\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiHovered, TuiHoveredService, tuiHovered };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AAC7E,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,yBAAyB;AAC7E,SAASC,iBAAiB,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,2BAA2B;AACzF,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,YAAY,EAAEC,gBAAgB,QAAQ,qBAAqB;AACpE,SAASC,UAAU,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,oBAAoB,EAAEC,EAAE,QAAQ,MAAM;AAE/E,SAASC,QAAQA,CAAC;EAAEC,aAAa;EAAEC;AAAc,CAAC,EAAE;EAChD,OAAQ,CAACV,YAAY,CAACU,aAAa,CAAC,IAChC,CAACV,YAAY,CAACS,aAAa,CAAC,IAC5B,CAACA,aAAa,CAACE,QAAQ,CAACD,aAAa,CAAC;AAC9C;AACA,MAAME,iBAAiB,SAASV,UAAU,CAAC;EACvCW,WAAWA,CAAA,EAAG;IACV,KAAK,CAAEC,UAAU,IAAK,IAAI,CAACC,OAAO,CAACC,SAAS,CAACF,UAAU,CAAC,CAAC;IACzD,IAAI,CAACG,EAAE,GAAGhB,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACiB,IAAI,GAAG9B,MAAM,CAACC,MAAM,CAAC;IAC1B,IAAI,CAAC0B,OAAO,GAAGZ,KAAK,CAACP,iBAAiB,CAAC,IAAI,CAACqB,EAAE,EAAE,YAAY,CAAC,CAACE,IAAI,CAACf,GAAG,CAACV,gBAAgB,CAAC,CAAC,EAAEE,iBAAiB,CAAC,IAAI,CAACqB,EAAE,EAAE,YAAY,CAAC,CAACE,IAAI,CAACf,GAAG,CAACT,iBAAiB,CAAC,CAAC;IAChK;IACAC,iBAAiB,CAAC,IAAI,CAACqB,EAAE,EAAE,UAAU,CAAC,CAACE,IAAI,CAACd,MAAM,CAACG,QAAQ,CAAC,EAAEJ,GAAG,CAACT,iBAAiB,CAAC,CAAC,CAAC,CAACwB,IAAI,CAACb,oBAAoB,CAAC,CAAC,EAAET,gBAAgB,CAAC,IAAI,CAACqB,IAAI,CAAC,CAAC;EACpJ;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,0BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFV,iBAAiB;IAAA,CAAoD;EAAE;EAClL;IAAS,IAAI,CAACW,KAAK,kBAD8EpC,EAAE,CAAAqC,kBAAA;MAAAC,KAAA,EACYb,iBAAiB;MAAAc,OAAA,EAAjBd,iBAAiB,CAAAQ;IAAA,EAAG;EAAE;AACzI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAHqGxC,EAAE,CAAAyC,iBAAA,CAGXhB,iBAAiB,EAAc,CAAC;IAChHiB,IAAI,EAAEvC;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AACtD,SAASwC,UAAUA,CAAA,EAAG;EAClB,OAAOrC,QAAQ,CAACL,MAAM,CAACW,aAAa,CAAC,GAAGQ,EAAE,CAAC,KAAK,CAAC,GAAGnB,MAAM,CAACwB,iBAAiB,CAAC,CAACO,IAAI,CAACrB,QAAQ,CAAC,CAAC,CAAC,EAAE;IAC5FiC,YAAY,EAAE;EAClB,CAAC,CAAC;AACN;AAEA,MAAMC,UAAU,CAAC;EACbnB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoB,gBAAgB,GAAG7C,MAAM,CAACwB,iBAAiB,CAAC;EACrD;EACA;IAAS,IAAI,CAACQ,IAAI,YAAAc,mBAAAZ,CAAA;MAAA,YAAAA,CAAA,IAAyFU,UAAU;IAAA,CAAmD;EAAE;EAC1K;IAAS,IAAI,CAACG,IAAI,kBAjB+EhD,EAAE,CAAAiD,iBAAA;MAAAP,IAAA,EAiBJG,UAAU;MAAAK,SAAA;MAAAC,OAAA;QAAAL,gBAAA;MAAA;MAAAM,UAAA;MAAAC,QAAA,GAjBRrD,EAAE,CAAAsD,kBAAA,CAiB0H,CAAC7B,iBAAiB,CAAC;IAAA,EAAiB;EAAE;AACvQ;AACA;EAAA,QAAAe,SAAA,oBAAAA,SAAA,KAnBqGxC,EAAE,CAAAyC,iBAAA,CAmBXI,UAAU,EAAc,CAAC;IACzGH,IAAI,EAAEtC,SAAS;IACfmD,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CAAChC,iBAAiB;IACjC,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEqB,gBAAgB,EAAE,CAAC;MACjCJ,IAAI,EAAErC;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASwC,UAAU,EAAEpB,iBAAiB,EAAEkB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}