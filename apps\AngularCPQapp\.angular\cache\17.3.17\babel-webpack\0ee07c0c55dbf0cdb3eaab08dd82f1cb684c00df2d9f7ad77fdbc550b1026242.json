{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Input } from '@angular/core';\nimport * as i1 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport { TuiControl } from '@taiga-ui/cdk/classes';\nimport { TuiRepeatTimes } from '@taiga-ui/cdk/directives/repeat-times';\nimport { tuiFallbackValueProvider } from '@taiga-ui/cdk/tokens';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nconst _c0 = () => ({\n  standalone: true\n});\nconst _c1 = (a0, a1) => ({\n  $implicit: a0,\n  value: a1\n});\nfunction TuiRating_div_2_tui_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-icon\", 6);\n  }\n  if (rf & 2) {\n    const src_r4 = ctx.polymorpheusOutlet;\n    const index_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"--t-cut\", 100 - ctx_r2.getCut(index_r2), \"%\");\n    i0.ɵɵclassProp(\"t-icon_fraction\", ctx_r2.isFraction(index_r2));\n    i0.ɵɵproperty(\"icon\", src_r4.toString());\n  }\n}\nfunction TuiRating_div_2_tui_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-icon\", 7);\n  }\n  if (rf & 2) {\n    const src_r5 = ctx.polymorpheusOutlet;\n    const index_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"--t-cut\", ctx_r2.getCut(index_r2), \"%\");\n    i0.ɵɵproperty(\"icon\", src_r5.toString());\n  }\n}\nfunction TuiRating_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵlistener(\"pointerup\", function TuiRating_div_2_Template_div_pointerup_0_listener() {\n      const index_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClick(ctx_r2.max - index_r2));\n    });\n    i0.ɵɵtemplate(1, TuiRating_div_2_tui_icon_1_Template, 1, 5, \"tui-icon\", 4)(2, TuiRating_div_2_tui_icon_2_Template, 1, 3, \"tui-icon\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const index_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"t-item_active\", ctx_r2.isActive(index_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r2.icon)(\"polymorpheusOutletContext\", i0.ɵɵpureFunction2(6, _c1, ctx_r2.max - index_r2, ctx_r2.value()));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r2.icon)(\"polymorpheusOutletContext\", i0.ɵɵpureFunction2(9, _c1, ctx_r2.max - index_r2, ctx_r2.value()));\n  }\n}\nconst TUI_RATING_DEFAULT_OPTIONS = {\n  icon: '@tui.star',\n  max: 5\n};\nconst TUI_RATING_OPTIONS = tuiCreateToken(TUI_RATING_DEFAULT_OPTIONS);\nfunction tuiRatingOptionsProvider(options) {\n  return tuiProvideOptions(TUI_RATING_OPTIONS, options, TUI_RATING_DEFAULT_OPTIONS);\n}\nclass TuiRating extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.options = inject(TUI_RATING_OPTIONS);\n    this.active = 0;\n    this.icon = this.options.icon;\n    this.max = this.options.max;\n  }\n  onKeyDown(event) {\n    if (this.readOnly()) {\n      event.preventDefault();\n    }\n  }\n  onPointer(delta) {\n    this.active = tuiClamp(this.active + delta, 0, 1);\n  }\n  onClick(value) {\n    if (this.active) {\n      this.onChange(value);\n    }\n  }\n  isActive(index) {\n    return Math.ceil(this.value()) >= this.max - index;\n  }\n  isFraction(index) {\n    return this.value() > this.max - index - 1 && this.value() < this.max - index;\n  }\n  getCut(index) {\n    return this.isFraction(index) ? 100 * Math.max(this.max - index - this.value(), 0) : 0;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiRating_BaseFactory;\n      return function TuiRating_Factory(t) {\n        return (ɵTuiRating_BaseFactory || (ɵTuiRating_BaseFactory = i0.ɵɵgetInheritedFactory(TuiRating)))(t || TuiRating);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiRating,\n      selectors: [[\"tui-rating\"]],\n      hostVars: 6,\n      hostBindings: function TuiRating_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.capture\", function TuiRating_keydown_capture_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          })(\"pointerdown\", function TuiRating_pointerdown_HostBindingHandler() {\n            return ctx.onPointer(1);\n          })(\"pointercancel\", function TuiRating_pointercancel_HostBindingHandler() {\n            return ctx.onPointer(-1);\n          })(\"pointerup\", function TuiRating_pointerup_HostBindingHandler() {\n            return ctx.onPointer(-1);\n          }, false, i0.ɵɵresolveDocument);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_disabled\", ctx.disabled())(\"_readonly\", ctx.readOnly())(\"_active\", ctx.active);\n        }\n      },\n      inputs: {\n        icon: \"icon\",\n        max: \"max\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiFallbackValueProvider(0)]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 7,\n      consts: [[\"min\", \"1\", \"type\", \"range\", 1, \"t-range\", 3, \"blur\", \"ngModelChange\", \"disabled\", \"max\", \"ngModel\", \"ngModelOptions\"], [1, \"t-items\"], [\"class\", \"t-item\", 3, \"t-item_active\", \"pointerup\", 4, \"tuiRepeatTimes\", \"tuiRepeatTimesOf\"], [1, \"t-item\", 3, \"pointerup\"], [\"class\", \"t-icon t-icon_blank\", 3, \"t-icon_fraction\", \"icon\", \"--t-cut\", 4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [\"class\", \"t-icon\", 3, \"icon\", \"--t-cut\", 4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [1, \"t-icon\", \"t-icon_blank\", 3, \"icon\"], [1, \"t-icon\", 3, \"icon\"]],\n      template: function TuiRating_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"input\", 0);\n          i0.ɵɵlistener(\"blur\", function TuiRating_Template_input_blur_0_listener() {\n            return ctx.onTouched();\n          })(\"ngModelChange\", function TuiRating_Template_input_ngModelChange_0_listener($event) {\n            return ctx.onChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵtemplate(2, TuiRating_div_2_Template, 3, 12, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"disabled\", ctx.disabled())(\"max\", ctx.max)(\"ngModel\", ctx.value())(\"ngModelOptions\", i0.ɵɵpureFunction0(6, _c0));\n          i0.ɵɵattribute(\"aria-disabled\", ctx.readOnly());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"tuiRepeatTimesOf\", ctx.max);\n        }\n      },\n      dependencies: [CommonModule, FormsModule, i1.DefaultValueAccessor, i1.RangeValueAccessor, i1.NgControlStatus, i1.NgModel, PolymorpheusOutlet, TuiIcon, TuiRepeatTimes],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:block;inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;min-inline-size:-webkit-fit-content;min-inline-size:-moz-fit-content;min-inline-size:fit-content;font-size:1rem;color:var(--tui-chart-categorical-12);cursor:pointer;-webkit-user-select:none;user-select:none}._readonly[_nghost-%COMP%]{pointer-events:none}._disabled[_nghost-%COMP%]{pointer-events:none;opacity:var(--tui-disabled-opacity)}.t-range[_ngcontent-%COMP%]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;opacity:0;box-sizing:border-box;padding:0 .75em;pointer-events:none}.t-range[_ngcontent-%COMP%]:focus-visible + .t-items[_ngcontent-%COMP%]{box-shadow:inset 0 0 0 .125rem var(--tui-border-focus)}.t-items[_ngcontent-%COMP%]{position:relative;display:flex;flex-direction:row-reverse}.t-items[_ngcontent-%COMP%]:hover   .t-icon[_ngcontent-%COMP%]{clip-path:inset(0 0 0 0)}.t-items[_ngcontent-%COMP%]:hover   .t-icon_blank[_ngcontent-%COMP%]{clip-path:inset(0 0 0 100%)}.t-item[_ngcontent-%COMP%]{position:relative;flex:1 0 0;color:var(--tui-text-tertiary);transition-property:color,transform;transition-duration:var(--tui-duration);transition-timing-function:ease-in-out,cubic-bezier(.35,1.5,.4,2.5)}.t-item[_ngcontent-%COMP%]:first-child{max-inline-size:2.5em}.t-item[_ngcontent-%COMP%]:hover, .t-item[_ngcontent-%COMP%]:hover ~ .t-item[_ngcontent-%COMP%], .t-items[_ngcontent-%COMP%]:not(:hover)   .t-item_active[_ngcontent-%COMP%]{color:currentColor}.t-items[_ngcontent-%COMP%]:active   .t-item[_ngcontent-%COMP%]:hover, .t-items[_ngcontent-%COMP%]:active   .t-item[_ngcontent-%COMP%]:hover ~ .t-item[_ngcontent-%COMP%]{transform:scale(.85);transition-duration:var(--tui-duration),calc(var(--tui-duration) / 3);transition-timing-function:ease-in-out}.t-icon[_ngcontent-%COMP%]{transition-property:clip-path;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;inline-size:2.5em;block-size:2.5em;font-size:inherit;clip-path:inset(0 var(--t-cut) 0 0)}.t-icon[_ngcontent-%COMP%]    >*{font-size:1rem}.t-icon_blank[_ngcontent-%COMP%]{position:absolute;top:0;left:0;display:none;color:var(--tui-text-tertiary);clip-path:inset(0 0 0 var(--t-cut))}.t-icon_fraction[_ngcontent-%COMP%]{display:block}@media (any-pointer: coarse){._active[_nghost-%COMP%]   .t-item_active[_ngcontent-%COMP%]{transform:scale(.85);transition-timing-function:ease-in-out}[_nghost-%COMP%]:not(._readonly)   .t-range[_ngcontent-%COMP%]{pointer-events:auto}.t-icon[_ngcontent-%COMP%]{transition:none}}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiRating, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-rating',\n      imports: [CommonModule, FormsModule, PolymorpheusOutlet, TuiIcon, TuiRepeatTimes],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiFallbackValueProvider(0)],\n      host: {\n        '[class._disabled]': 'disabled()',\n        '[class._readonly]': 'readOnly()',\n        '[class._active]': 'active',\n        '(keydown.capture)': 'onKeyDown($event)',\n        '(pointerdown)': 'onPointer(1)',\n        '(pointercancel)': 'onPointer(-1)',\n        '(document:pointerup)': 'onPointer(-1)'\n      },\n      template: \"<input\\n    min=\\\"1\\\"\\n    type=\\\"range\\\"\\n    class=\\\"t-range\\\"\\n    [attr.aria-disabled]=\\\"readOnly()\\\"\\n    [disabled]=\\\"disabled()\\\"\\n    [max]=\\\"max\\\"\\n    [ngModel]=\\\"value()\\\"\\n    [ngModelOptions]=\\\"{standalone: true}\\\"\\n    (blur)=\\\"onTouched()\\\"\\n    (ngModelChange)=\\\"onChange($event)\\\"\\n/>\\n<div class=\\\"t-items\\\">\\n    <div\\n        *tuiRepeatTimes=\\\"let index of max\\\"\\n        class=\\\"t-item\\\"\\n        [class.t-item_active]=\\\"isActive(index)\\\"\\n        (pointerup)=\\\"onClick(max - index)\\\"\\n    >\\n        <tui-icon\\n            *polymorpheusOutlet=\\\"icon as src; context: {$implicit: max - index, value: value()}\\\"\\n            class=\\\"t-icon t-icon_blank\\\"\\n            [class.t-icon_fraction]=\\\"isFraction(index)\\\"\\n            [icon]=\\\"src.toString()\\\"\\n            [style.--t-cut.%]=\\\"100 - getCut(index)\\\"\\n        />\\n        <tui-icon\\n            *polymorpheusOutlet=\\\"icon as src; context: {$implicit: max - index, value: value()}\\\"\\n            class=\\\"t-icon\\\"\\n            [icon]=\\\"src.toString()\\\"\\n            [style.--t-cut.%]=\\\"getCut(index)\\\"\\n        />\\n    </div>\\n</div>\\n\",\n      styles: [\":host{position:relative;display:block;inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;min-inline-size:-webkit-fit-content;min-inline-size:-moz-fit-content;min-inline-size:fit-content;font-size:1rem;color:var(--tui-chart-categorical-12);cursor:pointer;-webkit-user-select:none;user-select:none}:host._readonly{pointer-events:none}:host._disabled{pointer-events:none;opacity:var(--tui-disabled-opacity)}.t-range{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;opacity:0;box-sizing:border-box;padding:0 .75em;pointer-events:none}.t-range:focus-visible+.t-items{box-shadow:inset 0 0 0 .125rem var(--tui-border-focus)}.t-items{position:relative;display:flex;flex-direction:row-reverse}.t-items:hover .t-icon{clip-path:inset(0 0 0 0)}.t-items:hover .t-icon_blank{clip-path:inset(0 0 0 100%)}.t-item{position:relative;flex:1 0 0;color:var(--tui-text-tertiary);transition-property:color,transform;transition-duration:var(--tui-duration);transition-timing-function:ease-in-out,cubic-bezier(.35,1.5,.4,2.5)}.t-item:first-child{max-inline-size:2.5em}.t-item:hover,.t-item:hover~.t-item,.t-items:not(:hover) .t-item_active{color:currentColor}.t-items:active .t-item:hover,.t-items:active .t-item:hover~.t-item{transform:scale(.85);transition-duration:var(--tui-duration),calc(var(--tui-duration) / 3);transition-timing-function:ease-in-out}.t-icon{transition-property:clip-path;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;inline-size:2.5em;block-size:2.5em;font-size:inherit;clip-path:inset(0 var(--t-cut) 0 0)}.t-icon ::ng-deep>*{font-size:1rem}.t-icon_blank{position:absolute;top:0;left:0;display:none;color:var(--tui-text-tertiary);clip-path:inset(0 0 0 var(--t-cut))}.t-icon_fraction{display:block}@media (any-pointer: coarse){:host._active .t-item_active{transform:scale(.85);transition-timing-function:ease-in-out}:host:not(._readonly) .t-range{pointer-events:auto}.t-icon{transition:none}}\\n\"]\n    }]\n  }], null, {\n    icon: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_RATING_DEFAULT_OPTIONS, TUI_RATING_OPTIONS, TuiRating, tuiRatingOptionsProvider };", "map": {"version": 3, "names": ["CommonModule", "i0", "inject", "Component", "ChangeDetectionStrategy", "Input", "i1", "FormsModule", "TuiControl", "TuiRepeatTimes", "tuiFallback<PERSON><PERSON>ue<PERSON>", "tui<PERSON><PERSON>", "TuiIcon", "Polymorpheus<PERSON><PERSON>let", "tuiCreateToken", "tuiProvideOptions", "_c0", "standalone", "_c1", "a0", "a1", "$implicit", "value", "TuiRating_div_2_tui_icon_1_Template", "rf", "ctx", "ɵɵelement", "src_r4", "polymorpheusOutlet", "index_r2", "ɵɵnextContext", "ctx_r2", "ɵɵstyleProp", "getCut", "ɵɵclassProp", "isFraction", "ɵɵproperty", "toString", "TuiRating_div_2_tui_icon_2_Template", "src_r5", "TuiRating_div_2_Template", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiRating_div_2_Template_div_pointerup_0_listener", "ɵɵrestoreView", "ɵɵresetView", "onClick", "max", "ɵɵtemplate", "ɵɵelementEnd", "isActive", "ɵɵadvance", "icon", "ɵɵpureFunction2", "TUI_RATING_DEFAULT_OPTIONS", "TUI_RATING_OPTIONS", "tuiRatingOptionsProvider", "options", "TuiRating", "constructor", "arguments", "active", "onKeyDown", "event", "readOnly", "preventDefault", "onPointer", "delta", "onChange", "index", "Math", "ceil", "ɵfac", "ɵTuiRating_BaseFactory", "TuiRating_Factory", "t", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "TuiRating_HostBindings", "TuiRating_keydown_capture_HostBindingHandler", "$event", "TuiRating_pointerdown_HostBindingHandler", "TuiRating_pointercancel_HostBindingHandler", "TuiRating_pointerup_HostBindingHandler", "ɵɵresolveDocument", "disabled", "inputs", "features", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiRating_Template", "TuiRating_Template_input_blur_0_listener", "onTouched", "TuiRating_Template_input_ngModelChange_0_listener", "ɵɵpureFunction0", "ɵɵattribute", "dependencies", "DefaultValueAccessor", "RangeValueAccessor", "NgControlStatus", "NgModel", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "host"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-rating.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Component, ChangeDetectionStrategy, Input } from '@angular/core';\nimport * as i1 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport { TuiControl } from '@taiga-ui/cdk/classes';\nimport { TuiRepeatTimes } from '@taiga-ui/cdk/directives/repeat-times';\nimport { tuiFallbackValueProvider } from '@taiga-ui/cdk/tokens';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\n\nconst TUI_RATING_DEFAULT_OPTIONS = {\n    icon: '@tui.star',\n    max: 5,\n};\nconst TUI_RATING_OPTIONS = tuiCreateToken(TUI_RATING_DEFAULT_OPTIONS);\nfunction tuiRatingOptionsProvider(options) {\n    return tuiProvideOptions(TUI_RATING_OPTIONS, options, TUI_RATING_DEFAULT_OPTIONS);\n}\n\nclass TuiRating extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.options = inject(TUI_RATING_OPTIONS);\n        this.active = 0;\n        this.icon = this.options.icon;\n        this.max = this.options.max;\n    }\n    onKeyDown(event) {\n        if (this.readOnly()) {\n            event.preventDefault();\n        }\n    }\n    onPointer(delta) {\n        this.active = tuiClamp(this.active + delta, 0, 1);\n    }\n    onClick(value) {\n        if (this.active) {\n            this.onChange(value);\n        }\n    }\n    isActive(index) {\n        return Math.ceil(this.value()) >= this.max - index;\n    }\n    isFraction(index) {\n        return this.value() > this.max - index - 1 && this.value() < this.max - index;\n    }\n    getCut(index) {\n        return this.isFraction(index)\n            ? 100 * Math.max(this.max - index - this.value(), 0)\n            : 0;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiRating, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiRating, isStandalone: true, selector: \"tui-rating\", inputs: { icon: \"icon\", max: \"max\" }, host: { listeners: { \"keydown.capture\": \"onKeyDown($event)\", \"pointerdown\": \"onPointer(1)\", \"pointercancel\": \"onPointer(-1)\", \"document:pointerup\": \"onPointer(-1)\" }, properties: { \"class._disabled\": \"disabled()\", \"class._readonly\": \"readOnly()\", \"class._active\": \"active\" } }, providers: [tuiFallbackValueProvider(0)], usesInheritance: true, ngImport: i0, template: \"<input\\n    min=\\\"1\\\"\\n    type=\\\"range\\\"\\n    class=\\\"t-range\\\"\\n    [attr.aria-disabled]=\\\"readOnly()\\\"\\n    [disabled]=\\\"disabled()\\\"\\n    [max]=\\\"max\\\"\\n    [ngModel]=\\\"value()\\\"\\n    [ngModelOptions]=\\\"{standalone: true}\\\"\\n    (blur)=\\\"onTouched()\\\"\\n    (ngModelChange)=\\\"onChange($event)\\\"\\n/>\\n<div class=\\\"t-items\\\">\\n    <div\\n        *tuiRepeatTimes=\\\"let index of max\\\"\\n        class=\\\"t-item\\\"\\n        [class.t-item_active]=\\\"isActive(index)\\\"\\n        (pointerup)=\\\"onClick(max - index)\\\"\\n    >\\n        <tui-icon\\n            *polymorpheusOutlet=\\\"icon as src; context: {$implicit: max - index, value: value()}\\\"\\n            class=\\\"t-icon t-icon_blank\\\"\\n            [class.t-icon_fraction]=\\\"isFraction(index)\\\"\\n            [icon]=\\\"src.toString()\\\"\\n            [style.--t-cut.%]=\\\"100 - getCut(index)\\\"\\n        />\\n        <tui-icon\\n            *polymorpheusOutlet=\\\"icon as src; context: {$implicit: max - index, value: value()}\\\"\\n            class=\\\"t-icon\\\"\\n            [icon]=\\\"src.toString()\\\"\\n            [style.--t-cut.%]=\\\"getCut(index)\\\"\\n        />\\n    </div>\\n</div>\\n\", styles: [\":host{position:relative;display:block;inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;min-inline-size:-webkit-fit-content;min-inline-size:-moz-fit-content;min-inline-size:fit-content;font-size:1rem;color:var(--tui-chart-categorical-12);cursor:pointer;-webkit-user-select:none;user-select:none}:host._readonly{pointer-events:none}:host._disabled{pointer-events:none;opacity:var(--tui-disabled-opacity)}.t-range{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;opacity:0;box-sizing:border-box;padding:0 .75em;pointer-events:none}.t-range:focus-visible+.t-items{box-shadow:inset 0 0 0 .125rem var(--tui-border-focus)}.t-items{position:relative;display:flex;flex-direction:row-reverse}.t-items:hover .t-icon{clip-path:inset(0 0 0 0)}.t-items:hover .t-icon_blank{clip-path:inset(0 0 0 100%)}.t-item{position:relative;flex:1 0 0;color:var(--tui-text-tertiary);transition-property:color,transform;transition-duration:var(--tui-duration);transition-timing-function:ease-in-out,cubic-bezier(.35,1.5,.4,2.5)}.t-item:first-child{max-inline-size:2.5em}.t-item:hover,.t-item:hover~.t-item,.t-items:not(:hover) .t-item_active{color:currentColor}.t-items:active .t-item:hover,.t-items:active .t-item:hover~.t-item{transform:scale(.85);transition-duration:var(--tui-duration),calc(var(--tui-duration) / 3);transition-timing-function:ease-in-out}.t-icon{transition-property:clip-path;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;inline-size:2.5em;block-size:2.5em;font-size:inherit;clip-path:inset(0 var(--t-cut) 0 0)}.t-icon ::ng-deep>*{font-size:1rem}.t-icon_blank{position:absolute;top:0;left:0;display:none;color:var(--tui-text-tertiary);clip-path:inset(0 0 0 var(--t-cut))}.t-icon_fraction{display:block}@media (any-pointer: coarse){:host._active .t-item_active{transform:scale(.85);transition-timing-function:ease-in-out}:host:not(._readonly) .t-range{pointer-events:auto}.t-icon{transition:none}}\\n\"], dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"ngmodule\", type: FormsModule }, { kind: \"directive\", type: i1.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { kind: \"directive\", type: i1.RangeValueAccessor, selector: \"input[type=range][formControlName],input[type=range][formControl],input[type=range][ngModel]\" }, { kind: \"directive\", type: i1.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i1.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"component\", type: TuiIcon, selector: \"tui-icon\", inputs: [\"icon\", \"background\"] }, { kind: \"directive\", type: TuiRepeatTimes, selector: \"[tuiRepeatTimes][tuiRepeatTimesOf]\", inputs: [\"tuiRepeatTimesOf\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiRating, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-rating', imports: [CommonModule, FormsModule, PolymorpheusOutlet, TuiIcon, TuiRepeatTimes], changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiFallbackValueProvider(0)], host: {\n                        '[class._disabled]': 'disabled()',\n                        '[class._readonly]': 'readOnly()',\n                        '[class._active]': 'active',\n                        '(keydown.capture)': 'onKeyDown($event)',\n                        '(pointerdown)': 'onPointer(1)',\n                        '(pointercancel)': 'onPointer(-1)',\n                        '(document:pointerup)': 'onPointer(-1)',\n                    }, template: \"<input\\n    min=\\\"1\\\"\\n    type=\\\"range\\\"\\n    class=\\\"t-range\\\"\\n    [attr.aria-disabled]=\\\"readOnly()\\\"\\n    [disabled]=\\\"disabled()\\\"\\n    [max]=\\\"max\\\"\\n    [ngModel]=\\\"value()\\\"\\n    [ngModelOptions]=\\\"{standalone: true}\\\"\\n    (blur)=\\\"onTouched()\\\"\\n    (ngModelChange)=\\\"onChange($event)\\\"\\n/>\\n<div class=\\\"t-items\\\">\\n    <div\\n        *tuiRepeatTimes=\\\"let index of max\\\"\\n        class=\\\"t-item\\\"\\n        [class.t-item_active]=\\\"isActive(index)\\\"\\n        (pointerup)=\\\"onClick(max - index)\\\"\\n    >\\n        <tui-icon\\n            *polymorpheusOutlet=\\\"icon as src; context: {$implicit: max - index, value: value()}\\\"\\n            class=\\\"t-icon t-icon_blank\\\"\\n            [class.t-icon_fraction]=\\\"isFraction(index)\\\"\\n            [icon]=\\\"src.toString()\\\"\\n            [style.--t-cut.%]=\\\"100 - getCut(index)\\\"\\n        />\\n        <tui-icon\\n            *polymorpheusOutlet=\\\"icon as src; context: {$implicit: max - index, value: value()}\\\"\\n            class=\\\"t-icon\\\"\\n            [icon]=\\\"src.toString()\\\"\\n            [style.--t-cut.%]=\\\"getCut(index)\\\"\\n        />\\n    </div>\\n</div>\\n\", styles: [\":host{position:relative;display:block;inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;min-inline-size:-webkit-fit-content;min-inline-size:-moz-fit-content;min-inline-size:fit-content;font-size:1rem;color:var(--tui-chart-categorical-12);cursor:pointer;-webkit-user-select:none;user-select:none}:host._readonly{pointer-events:none}:host._disabled{pointer-events:none;opacity:var(--tui-disabled-opacity)}.t-range{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;opacity:0;box-sizing:border-box;padding:0 .75em;pointer-events:none}.t-range:focus-visible+.t-items{box-shadow:inset 0 0 0 .125rem var(--tui-border-focus)}.t-items{position:relative;display:flex;flex-direction:row-reverse}.t-items:hover .t-icon{clip-path:inset(0 0 0 0)}.t-items:hover .t-icon_blank{clip-path:inset(0 0 0 100%)}.t-item{position:relative;flex:1 0 0;color:var(--tui-text-tertiary);transition-property:color,transform;transition-duration:var(--tui-duration);transition-timing-function:ease-in-out,cubic-bezier(.35,1.5,.4,2.5)}.t-item:first-child{max-inline-size:2.5em}.t-item:hover,.t-item:hover~.t-item,.t-items:not(:hover) .t-item_active{color:currentColor}.t-items:active .t-item:hover,.t-items:active .t-item:hover~.t-item{transform:scale(.85);transition-duration:var(--tui-duration),calc(var(--tui-duration) / 3);transition-timing-function:ease-in-out}.t-icon{transition-property:clip-path;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;inline-size:2.5em;block-size:2.5em;font-size:inherit;clip-path:inset(0 var(--t-cut) 0 0)}.t-icon ::ng-deep>*{font-size:1rem}.t-icon_blank{position:absolute;top:0;left:0;display:none;color:var(--tui-text-tertiary);clip-path:inset(0 0 0 var(--t-cut))}.t-icon_fraction{display:block}@media (any-pointer: coarse){:host._active .t-item_active{transform:scale(.85);transition-timing-function:ease-in-out}:host:not(._readonly) .t-range{pointer-events:auto}.t-icon{transition:none}}\\n\"] }]\n        }], propDecorators: { icon: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_RATING_DEFAULT_OPTIONS, TUI_RATING_OPTIONS, TuiRating, tuiRatingOptionsProvider };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,QAAQ,eAAe;AACjF,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,mCAAmC;AAAC,MAAAC,GAAA,GAAAA,CAAA;EAAAC,UAAA;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA;EAAAC,SAAA,EAAAF,EAAA;EAAAG,KAAA,EAAAF;AAAA;AAAA,SAAAG,oCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA2CevB,EAAE,CAAAyB,SAAA,iBAC+wC,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAAAF,GAAA,CAAAG,kBAAA;IAAA,MAAAC,QAAA,GADlxC5B,EAAE,CAAA6B,aAAA,GAAAT,SAAA;IAAA,MAAAU,MAAA,GAAF9B,EAAE,CAAA6B,aAAA;IAAF7B,EAAE,CAAA+B,WAAA,kBAAAD,MAAA,CAAAE,MAAA,CAAAJ,QAAA,MACmwC,CAAC;IADtwC5B,EAAE,CAAAiC,WAAA,oBAAAH,MAAA,CAAAI,UAAA,CAAAN,QAAA,CACqqC,CAAC;IADxqC5B,EAAE,CAAAmC,UAAA,SAAAT,MAAA,CAAAU,QAAA,EAC4sC,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD/sCvB,EAAE,CAAAyB,SAAA,iBACwgD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAe,MAAA,GAAAd,GAAA,CAAAG,kBAAA;IAAA,MAAAC,QAAA,GAD3gD5B,EAAE,CAAA6B,aAAA,GAAAT,SAAA;IAAA,MAAAU,MAAA,GAAF9B,EAAE,CAAA6B,aAAA;IAAF7B,EAAE,CAAA+B,WAAA,YAAAD,MAAA,CAAAE,MAAA,CAAAJ,QAAA,MAC4/C,CAAC;IAD//C5B,EAAE,CAAAmC,UAAA,SAAAG,MAAA,CAAAF,QAAA,EAC28C,CAAC;EAAA;AAAA;AAAA,SAAAG,yBAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiB,GAAA,GAD98CxC,EAAE,CAAAyC,gBAAA;IAAFzC,EAAE,CAAA0C,cAAA,YACw8B,CAAC;IAD38B1C,EAAE,CAAA2C,UAAA,uBAAAC,kDAAA;MAAA,MAAAhB,QAAA,GAAF5B,EAAE,CAAA6C,aAAA,CAAAL,GAAA,EAAApB,SAAA;MAAA,MAAAU,MAAA,GAAF9B,EAAE,CAAA6B,aAAA;MAAA,OAAF7B,EAAE,CAAA8C,WAAA,CAC46BhB,MAAA,CAAAiB,OAAA,CAAAjB,MAAA,CAAAkB,GAAA,GAAApB,QAAmB,CAAC;IAAA,CAAC,CAAC;IADp8B5B,EAAE,CAAAiD,UAAA,IAAA3B,mCAAA,qBAC+wC,CAAC,IAAAe,mCAAA,qBAAwP,CAAC;IAD3gDrC,EAAE,CAAAkD,YAAA,CACohD,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAK,QAAA,GAAAJ,GAAA,CAAAJ,SAAA;IAAA,MAAAU,MAAA,GADvhD9B,EAAE,CAAA6B,aAAA;IAAF7B,EAAE,CAAAiC,WAAA,kBAAAH,MAAA,CAAAqB,QAAA,CAAAvB,QAAA,CACm5B,CAAC;IADt5B5B,EAAE,CAAAoD,SAAA,CACogC,CAAC;IADvgCpD,EAAE,CAAAmC,UAAA,uBAAAL,MAAA,CAAAuB,IACogC,CAAC,8BADvgCrD,EAAE,CAAAsD,eAAA,IAAArC,GAAA,EAAAa,MAAA,CAAAkB,GAAA,GAAApB,QAAA,EAAAE,MAAA,CAAAT,KAAA,GAC6jC,CAAC;IADhkCrB,EAAE,CAAAoD,SAAA,CAC20C,CAAC;IAD90CpD,EAAE,CAAAmC,UAAA,uBAAAL,MAAA,CAAAuB,IAC20C,CAAC,8BAD90CrD,EAAE,CAAAsD,eAAA,IAAArC,GAAA,EAAAa,MAAA,CAAAkB,GAAA,GAAApB,QAAA,EAAAE,MAAA,CAAAT,KAAA,GACo4C,CAAC;EAAA;AAAA;AA1C5+C,MAAMkC,0BAA0B,GAAG;EAC/BF,IAAI,EAAE,WAAW;EACjBL,GAAG,EAAE;AACT,CAAC;AACD,MAAMQ,kBAAkB,GAAG3C,cAAc,CAAC0C,0BAA0B,CAAC;AACrE,SAASE,wBAAwBA,CAACC,OAAO,EAAE;EACvC,OAAO5C,iBAAiB,CAAC0C,kBAAkB,EAAEE,OAAO,EAAEH,0BAA0B,CAAC;AACrF;AAEA,MAAMI,SAAS,SAASpD,UAAU,CAAC;EAC/BqD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACH,OAAO,GAAGzD,MAAM,CAACuD,kBAAkB,CAAC;IACzC,IAAI,CAACM,MAAM,GAAG,CAAC;IACf,IAAI,CAACT,IAAI,GAAG,IAAI,CAACK,OAAO,CAACL,IAAI;IAC7B,IAAI,CAACL,GAAG,GAAG,IAAI,CAACU,OAAO,CAACV,GAAG;EAC/B;EACAe,SAASA,CAACC,KAAK,EAAE;IACb,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;MACjBD,KAAK,CAACE,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAC,SAASA,CAACC,KAAK,EAAE;IACb,IAAI,CAACN,MAAM,GAAGpD,QAAQ,CAAC,IAAI,CAACoD,MAAM,GAAGM,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;EACrD;EACArB,OAAOA,CAAC1B,KAAK,EAAE;IACX,IAAI,IAAI,CAACyC,MAAM,EAAE;MACb,IAAI,CAACO,QAAQ,CAAChD,KAAK,CAAC;IACxB;EACJ;EACA8B,QAAQA,CAACmB,KAAK,EAAE;IACZ,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACnD,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC2B,GAAG,GAAGsB,KAAK;EACtD;EACApC,UAAUA,CAACoC,KAAK,EAAE;IACd,OAAO,IAAI,CAACjD,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC2B,GAAG,GAAGsB,KAAK,GAAG,CAAC,IAAI,IAAI,CAACjD,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC2B,GAAG,GAAGsB,KAAK;EACjF;EACAtC,MAAMA,CAACsC,KAAK,EAAE;IACV,OAAO,IAAI,CAACpC,UAAU,CAACoC,KAAK,CAAC,GACvB,GAAG,GAAGC,IAAI,CAACvB,GAAG,CAAC,IAAI,CAACA,GAAG,GAAGsB,KAAK,GAAG,IAAI,CAACjD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAClD,CAAC;EACX;EACA;IAAS,IAAI,CAACoD,IAAI;MAAA,IAAAC,sBAAA;MAAA,gBAAAC,kBAAAC,CAAA;QAAA,QAAAF,sBAAA,KAAAA,sBAAA,GAA+E1E,EAAE,CAAA6E,qBAAA,CAAQlB,SAAS,IAAAiB,CAAA,IAATjB,SAAS;MAAA;IAAA,IAAqD;EAAE;EAC3K;IAAS,IAAI,CAACmB,IAAI,kBAD+E9E,EAAE,CAAA+E,iBAAA;MAAAC,IAAA,EACJrB,SAAS;MAAAsB,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,uBAAA7D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADPvB,EAAE,CAAA2C,UAAA,6BAAA0C,6CAAAC,MAAA;YAAA,OACJ9D,GAAA,CAAAuC,SAAA,CAAAuB,MAAgB,CAAC;UAAA,CAAT,CAAC,yBAAAC,yCAAA;YAAA,OAAT/D,GAAA,CAAA2C,SAAA,CAAU,CAAC,CAAC;UAAA,CAAJ,CAAC,2BAAAqB,2CAAA;YAAA,OAAThE,GAAA,CAAA2C,SAAA,EAAW,CAAC,CAAC;UAAA,CAAL,CAAC,uBAAAsB,uCAAA;YAAA,OAATjE,GAAA,CAAA2C,SAAA,EAAW,CAAC,CAAC;UAAA,UADXnE,EAAE,CAAA0F,iBACI,CAAC;QAAA;QAAA,IAAAnE,EAAA;UADPvB,EAAE,CAAAiC,WAAA,cACJT,GAAA,CAAAmE,QAAA,CAAS,CAAD,CAAC,cAATnE,GAAA,CAAAyC,QAAA,CAAS,CAAD,CAAC,YAAAzC,GAAA,CAAAsC,MAAD,CAAC;QAAA;MAAA;MAAA8B,MAAA;QAAAvC,IAAA;QAAAL,GAAA;MAAA;MAAAhC,UAAA;MAAA6E,QAAA,GADP7F,EAAE,CAAA8F,kBAAA,CAC0X,CAACrF,wBAAwB,CAAC,CAAC,CAAC,CAAC,GADzZT,EAAE,CAAA+F,0BAAA,EAAF/F,EAAE,CAAAgG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mBAAA9E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvB,EAAE,CAAA0C,cAAA,cACqvB,CAAC;UADxvB1C,EAAE,CAAA2C,UAAA,kBAAA2D,yCAAA;YAAA,OAC2rB9E,GAAA,CAAA+E,SAAA,CAAU,CAAC;UAAA,CAAC,CAAC,2BAAAC,kDAAAlB,MAAA;YAAA,OAAwB9D,GAAA,CAAA6C,QAAA,CAAAiB,MAAe,CAAC;UAAA,CAAC,CAAC;UADpvBtF,EAAE,CAAAkD,YAAA,CACqvB,CAAC;UADxvBlD,EAAE,CAAA0C,cAAA,YAC8wB,CAAC;UADjxB1C,EAAE,CAAAiD,UAAA,IAAAV,wBAAA,iBACw8B,CAAC;UAD38BvC,EAAE,CAAAkD,YAAA,CAC4hD,CAAC;QAAA;QAAA,IAAA3B,EAAA;UAD/hDvB,EAAE,CAAAmC,UAAA,aAAAX,GAAA,CAAAmE,QAAA,EACglB,CAAC,QAAAnE,GAAA,CAAAwB,GAAkB,CAAC,YAAAxB,GAAA,CAAAH,KAAA,EAA0B,CAAC,mBADjoBrB,EAAE,CAAAyG,eAAA,IAAA1F,GAAA,CAC2qB,CAAC;UAD9qBf,EAAE,CAAA0G,WAAA,kBAAAlF,GAAA,CAAAyC,QAAA;UAAFjE,EAAE,CAAAoD,SAAA,EACo0B,CAAC;UADv0BpD,EAAE,CAAAmC,UAAA,qBAAAX,GAAA,CAAAwB,GACo0B,CAAC;QAAA;MAAA;MAAA2D,YAAA,GAA+sF5G,YAAY,EAA8BO,WAAW,EAA+BD,EAAE,CAACuG,oBAAoB,EAAyPvG,EAAE,CAACwG,kBAAkB,EAAyIxG,EAAE,CAACyG,eAAe,EAAsFzG,EAAE,CAAC0G,OAAO,EAA8MnG,kBAAkB,EAA8HD,OAAO,EAAqFH,cAAc;MAAAwG,MAAA;MAAAC,eAAA;IAAA,EAAwI;EAAE;AACj0J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGlH,EAAE,CAAAmH,iBAAA,CAGXxD,SAAS,EAAc,CAAC;IACxGqB,IAAI,EAAE9E,SAAS;IACfkH,IAAI,EAAE,CAAC;MAAEpG,UAAU,EAAE,IAAI;MAAEqG,QAAQ,EAAE,YAAY;MAAEC,OAAO,EAAE,CAACvH,YAAY,EAAEO,WAAW,EAAEM,kBAAkB,EAAED,OAAO,EAAEH,cAAc,CAAC;MAAEyG,eAAe,EAAE9G,uBAAuB,CAACoH,MAAM;MAAEC,SAAS,EAAE,CAAC/G,wBAAwB,CAAC,CAAC,CAAC,CAAC;MAAEgH,IAAI,EAAE;QAC3N,mBAAmB,EAAE,YAAY;QACjC,mBAAmB,EAAE,YAAY;QACjC,iBAAiB,EAAE,QAAQ;QAC3B,mBAAmB,EAAE,mBAAmB;QACxC,eAAe,EAAE,cAAc;QAC/B,iBAAiB,EAAE,eAAe;QAClC,sBAAsB,EAAE;MAC5B,CAAC;MAAErB,QAAQ,EAAE,wlCAAwlC;MAAEY,MAAM,EAAE,CAAC,67DAA67D;IAAE,CAAC;EAC5jG,CAAC,CAAC,QAAkB;IAAE3D,IAAI,EAAE,CAAC;MACrB2B,IAAI,EAAE5E;IACV,CAAC,CAAC;IAAE4C,GAAG,EAAE,CAAC;MACNgC,IAAI,EAAE5E;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASmD,0BAA0B,EAAEC,kBAAkB,EAAEG,SAAS,EAAEF,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}