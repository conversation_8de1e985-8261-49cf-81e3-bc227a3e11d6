{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Pipe } from '@angular/core';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { fromEvent, map, startWith } from 'rxjs';\nclass TuiFallbackSrcPipe {\n  constructor() {\n    this.el = tuiInjectElement();\n  }\n  transform(src, fallback) {\n    return fromEvent(this.el, 'error', {\n      capture: true\n    }).pipe(map(() => fallback), startWith(src || fallback));\n  }\n  static {\n    this.ɵfac = function TuiFallbackSrcPipe_Factory(t) {\n      return new (t || TuiFallbackSrcPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiFallbackSrc\",\n      type: TuiFallbackSrcPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFallbackSrcPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiFallbackSrc'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFallbackSrcPipe };", "map": {"version": 3, "names": ["i0", "<PERSON><PERSON>", "tuiInjectElement", "fromEvent", "map", "startWith", "TuiFallbackSrcPipe", "constructor", "el", "transform", "src", "fallback", "capture", "pipe", "ɵfac", "TuiFallbackSrcPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-pipes-fallback-src.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Pipe } from '@angular/core';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { fromEvent, map, startWith } from 'rxjs';\n\nclass TuiFallbackSrcPipe {\n    constructor() {\n        this.el = tuiInjectElement();\n    }\n    transform(src, fallback) {\n        return fromEvent(this.el, 'error', { capture: true }).pipe(map(() => fallback), startWith(src || fallback));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFallbackSrcPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFallbackSrcPipe, isStandalone: true, name: \"tuiFallbackSrc\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFallbackSrcPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiFallbackSrc',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFallbackSrcPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,IAAI,QAAQ,eAAe;AACpC,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,SAAS,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;AAEhD,MAAMC,kBAAkB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAGN,gBAAgB,CAAC,CAAC;EAChC;EACAO,SAASA,CAACC,GAAG,EAAEC,QAAQ,EAAE;IACrB,OAAOR,SAAS,CAAC,IAAI,CAACK,EAAE,EAAE,OAAO,EAAE;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC,CAACC,IAAI,CAACT,GAAG,CAAC,MAAMO,QAAQ,CAAC,EAAEN,SAAS,CAACK,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/G;EACA;IAAS,IAAI,CAACG,IAAI,YAAAC,2BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFV,kBAAkB;IAAA,CAA8C;EAAE;EAC7K;IAAS,IAAI,CAACW,KAAK,kBAD8EjB,EAAE,CAAAkB,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMd,kBAAkB;MAAAe,IAAA;MAAAC,UAAA;IAAA,EAA+C;EAAE;AAChL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGvB,EAAE,CAAAwB,iBAAA,CAGXlB,kBAAkB,EAAc,CAAC;IACjHc,IAAI,EAAEnB,IAAI;IACVwB,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASb,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}