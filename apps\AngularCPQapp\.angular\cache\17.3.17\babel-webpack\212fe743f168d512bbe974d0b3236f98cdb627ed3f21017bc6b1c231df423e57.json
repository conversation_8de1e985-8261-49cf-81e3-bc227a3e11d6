{"ast": null, "code": "import { trigger, transition, query, animateChild, style, animate, stagger } from '@angular/animations';\nconst TRANSITION = '{{duration}}ms ease-in-out';\nconst DURATION = {\n  params: {\n    duration: 300\n  }\n};\nconst STAGGER = 300;\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiParentAnimation = trigger('tuiParentAnimation', [transition(':leave', [query(':scope > *', [animateChild()], {\n  optional: true\n})])]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiParentStop = trigger('tuiParentStop', [transition(':enter', [])]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiHost = trigger('tuiHost', [transition(':enter', [style({\n  overflow: 'clip'\n}), query(':scope > *', [animateChild()], {\n  optional: true\n})]), transition(':leave', [query(':scope > *', [animateChild()], {\n  optional: true\n})])]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiHeightCollapse = trigger('tuiHeightCollapse', [transition(':enter', [style({\n  height: 0\n}), animate(TRANSITION, style({\n  height: '*'\n}))], DURATION), transition(':leave', [style({\n  height: '*'\n}), animate(TRANSITION, style({\n  height: 0\n}))], DURATION)]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiHeightCollapseList = trigger('tuiHeightCollapseList', [transition('* => *', [query(':enter', [style({\n  height: 0\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  height: '*'\n}))])], {\n  optional: true\n}), query(':leave', [style({\n  height: '*'\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  height: 0\n}))])], {\n  optional: true\n})], DURATION)]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiWidthCollapse = trigger('tuiWidthCollapse', [transition(':enter', [style({\n  width: 0\n}), animate(TRANSITION, style({\n  width: '*'\n}))], DURATION), transition(':leave', [style({\n  width: '*'\n}), animate(TRANSITION, style({\n  width: 0\n}))], DURATION)]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiWidthCollapseList = trigger('tuiWidthCollapseList', [transition('* => *', [query(':enter', [style({\n  width: 0\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  width: '*'\n}))])], {\n  optional: true\n}), query(':leave', [style({\n  width: '*'\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  width: 0\n}))])], {\n  optional: true\n})], DURATION)]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiCrossFade = trigger('tuiCrossFade', [transition(':enter', [style({\n  opacity: 0\n}), animate(TRANSITION, style({\n  opacity: 1\n}))], {\n  params: {\n    duration: 300\n  }\n}), transition(':leave', [style({\n  opacity: 1,\n  position: 'absolute',\n  left: '{{left}}',\n  right: '{{right}}',\n  bottom: '{{bottom}}',\n  top: '{{top}}'\n}), animate(TRANSITION, style({\n  opacity: 0\n}))], {\n  params: {\n    duration: 300,\n    left: 'auto',\n    right: 'auto',\n    bottom: 'auto',\n    top: 'auto'\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiFadeIn = trigger('tuiFadeIn', [transition(':enter', [style({\n  opacity: 0\n}), animate(TRANSITION, style({\n  opacity: 1\n}))], DURATION), transition(':leave', [style({\n  opacity: 1\n}), animate(TRANSITION, style({\n  opacity: 0\n}))], DURATION)]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiFadeInList = trigger('tuiFadeInList', [transition('* => *', [query(':enter', [style({\n  opacity: 0\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  opacity: 1\n}))])], {\n  optional: true\n}), query(':leave', [style({\n  opacity: 1\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  opacity: 0\n}))])], {\n  optional: true\n})], DURATION)]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiFadeInTop = trigger('tuiFadeInTop', [transition(':enter', [style({\n  transform: 'translateY(-{{start}}px)',\n  opacity: 0\n}), animate(TRANSITION, style({\n  transform: 'translateY({{end}})',\n  opacity: 1\n}))], {\n  params: {\n    end: 0,\n    start: 10,\n    duration: 300\n  }\n}), transition(':leave', [style({\n  transform: 'translateY({{end}})',\n  opacity: 1\n}), animate(TRANSITION, style({\n  transform: 'translateY(-{{start}}px)',\n  opacity: 0\n}))], {\n  params: {\n    end: 0,\n    start: 10,\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiFadeInBottom = trigger('tuiFadeInBottom', [transition(':enter', [style({\n  transform: 'translateY({{start}}px)',\n  opacity: 0\n}), animate(TRANSITION, style({\n  transform: 'translateY({{end}})',\n  opacity: 1\n}))], {\n  params: {\n    end: 0,\n    start: 10,\n    duration: 300\n  }\n}), transition(':leave', [style({\n  transform: 'translateY({{end}})',\n  opacity: 1\n}), animate(TRANSITION, style({\n  transform: 'translateY({{start}}px)',\n  opacity: 0\n}))], {\n  params: {\n    end: 0,\n    start: 10,\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiDropdownAnimation = trigger('tuiDropdownAnimation', [transition(':enter', [style({\n  transform: 'translateY(-{{start}}px)',\n  opacity: 0\n}), animate(TRANSITION, style({\n  transform: 'translateY({{end}})',\n  opacity: 1\n}))], {\n  params: {\n    end: 0,\n    start: 10,\n    duration: 300\n  }\n}), transition(':leave', [style({\n  transform: 'translateY({{end}})',\n  opacity: 1\n}), animate(TRANSITION, style({\n  transform: 'translateY(-{{start}}px)',\n  opacity: 0\n}))], {\n  params: {\n    end: 0,\n    start: 10,\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiScaleIn = trigger('tuiScaleIn', [transition(':enter', [style({\n  transform: 'scale({{start}})'\n}), animate('{{duration}}ms {{easing}}', style({\n  transform: 'scale({{end}})'\n}))], {\n  params: {\n    end: 1,\n    start: 0,\n    duration: 300,\n    easing: 'ease-in-out'\n  }\n}), transition(':leave', [style({\n  transform: 'scale({{end}})'\n}), animate(TRANSITION, style({\n  transform: 'scale({{start}})'\n}))], {\n  params: {\n    end: 1,\n    start: 0,\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiPop = trigger('tuiPop', [transition(':enter', [style({\n  transform: 'scale({{start}})'\n}), animate(TRANSITION, style({\n  transform: 'scale({{middle}})'\n})), animate(TRANSITION, style({\n  transform: 'scale({{end}})'\n}))], {\n  params: {\n    end: 1,\n    middle: 1.1,\n    start: 0,\n    duration: 300\n  }\n}), transition(':leave', [style({\n  transform: 'scale({{end}})'\n}), animate(TRANSITION, style({\n  transform: 'scale({{middle}})'\n})), animate(TRANSITION, style({\n  transform: 'scale({{start}})'\n}))], {\n  params: {\n    end: 1,\n    middle: 1.1,\n    start: 0,\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiScaleInList = trigger('tuiScaleInList', [transition('* => *', [query(':enter', [style({\n  transform: 'scale({{start}})'\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  transform: 'scale({{end}})'\n}))])], {\n  optional: true\n}), query(':leave', [style({\n  transform: 'scale({{end}})'\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  transform: 'scale({{start}})'\n}))])], {\n  optional: true\n})], {\n  params: {\n    end: 1,\n    start: 0,\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideIn = trigger('tuiSlideIn', [transition('* => left', [style({\n  transform: 'translateX(-{{start}})'\n}), animate(TRANSITION, style({\n  transform: 'translateX({{end}})'\n}))], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n}), transition('left => *', [style({\n  transform: 'translateX({{end}})'\n}), animate(TRANSITION, style({\n  transform: 'translateX(-{{start}})'\n}))], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n}), transition('* => right', [style({\n  transform: 'translateX({{start}})'\n}), animate(TRANSITION, style({\n  transform: 'translateX({{end}})'\n}))], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n}), transition('right => *', [style({\n  transform: 'translateX({{end}})'\n}), animate(TRANSITION, style({\n  transform: 'translateX({{start}})'\n}))], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInLeft = trigger('tuiSlideInLeft', [transition(':enter', [style({\n  transform: 'translateX(-{{start}})'\n}), animate(TRANSITION, style({\n  transform: 'translateX({{end}})'\n}))], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n}), transition(':leave', [style({\n  transform: 'translateX({{end}})'\n}), animate(TRANSITION, style({\n  transform: 'translateX(-{{start}})'\n}))], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInLeftList = trigger('tuiSlideInLeftList', [transition('* => *', [query(':enter', [style({\n  transform: 'translateX(-{{start}})'\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  transform: 'translateX({{end}})'\n}))])], {\n  optional: true\n}), query(':leave', [style({\n  transform: 'translateX({{end}})'\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  transform: 'translateX(-{{start}})'\n}))])], {\n  optional: true\n})], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInRight = trigger('tuiSlideInRight', [transition(':enter', [style({\n  transform: 'translateX({{start}})'\n}), animate(TRANSITION, style({\n  transform: 'translateX({{end}})'\n}))], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n}), transition(':leave', [style({\n  transform: 'translateX({{end}})'\n}), animate(TRANSITION, style({\n  transform: 'translateX({{start}})'\n}))], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInRightList = trigger('tuiSlideInRightList', [transition('* => *', [query(':enter', [style({\n  transform: 'translateX({{start}})'\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  transform: 'translateX({{end}})'\n}))])], {\n  optional: true\n}), query(':leave', [style({\n  transform: 'translateX({{end}})'\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  transform: 'translateX({{start}})'\n}))])], {\n  optional: true\n})], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInTop = trigger('tuiSlideInTop', [transition(':enter', [style({\n  transform: 'translate3d(0,{{start}},0)',\n  pointerEvents: 'none'\n}), animate(TRANSITION, style({\n  transform: 'translate3d(0,{{end}},0)'\n}))], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n}), transition(':leave', [style({\n  transform: 'translate3d(0,{{end}},0)'\n}), animate(TRANSITION, style({\n  transform: 'translate3d(0,{{start}},0)'\n}))], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInTopList = trigger('tuiSlideInTopList', [transition('* => *', [query(':enter', [style({\n  transform: 'translateY({{start}})'\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  transform: 'translateY({{end}})'\n}))])], {\n  optional: true\n}), query(':leave', [style({\n  transform: 'translateY({{end}})'\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  transform: 'translateY({{start}})'\n}))])], {\n  optional: true\n})], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInBottom = trigger('tuiSlideInBottom', [transition(':enter', [style({\n  transform: 'translateY(-{{start}})'\n}), animate(TRANSITION, style({\n  transform: 'translateY({{end}})'\n}))], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n}), transition(':leave', [style({\n  transform: 'translateY({{end}})'\n}), animate(TRANSITION, style({\n  transform: 'translateY(-{{start}})'\n}))], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n})]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInBottomList = trigger('tuiSlideInBottomList', [transition('* => *', [query(':enter', [style({\n  transform: 'translateY(-{{start}})'\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  transform: 'translateY({{end}})'\n}))])], {\n  optional: true\n}), query(':leave', [style({\n  transform: 'translateY({{end}})'\n}), stagger(STAGGER, [animate(TRANSITION, style({\n  transform: 'translateY(-{{start}})'\n}))])], {\n  optional: true\n})], {\n  params: {\n    end: 0,\n    start: '100%',\n    duration: 300\n  }\n})]);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiCrossFade, tuiDropdownAnimation, tuiFadeIn, tuiFadeInBottom, tuiFadeInList, tuiFadeInTop, tuiHeightCollapse, tuiHeightCollapseList, tuiHost, tuiParentAnimation, tuiParentStop, tuiPop, tuiScaleIn, tuiScaleInList, tuiSlideIn, tuiSlideInBottom, tuiSlideInBottomList, tuiSlideInLeft, tuiSlideInLeftList, tuiSlideInRight, tuiSlideInRightList, tuiSlideInTop, tuiSlideInTopList, tuiWidthCollapse, tuiWidthCollapseList };", "map": {"version": 3, "names": ["trigger", "transition", "query", "animate<PERSON><PERSON><PERSON>", "style", "animate", "stagger", "TRANSITION", "DURATION", "params", "duration", "STAGGER", "tuiParentAnimation", "optional", "tuiParentStop", "tuiHost", "overflow", "tuiHeightCollapse", "height", "tuiHeightCollapseList", "tuiWidthCollapse", "width", "tuiWidthCollapseList", "tuiCrossFade", "opacity", "position", "left", "right", "bottom", "top", "tuiFadeIn", "tuiFadeInList", "tuiFadeInTop", "transform", "end", "start", "tuiFadeInBottom", "tuiDropdownAnimation", "tuiScaleIn", "easing", "tui<PERSON><PERSON>", "middle", "tuiScaleInList", "tuiSlideIn", "tuiSlideInLeft", "tuiSlideInLeftList", "tuiSlideInRight", "tuiSlideInRightList", "tuiSlideInTop", "pointerEvents", "tuiSlideInTopList", "tuiSlideInBottom", "tuiSlideInBottomList"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-animations.mjs"], "sourcesContent": ["import { trigger, transition, query, animateChild, style, animate, stagger } from '@angular/animations';\n\nconst TRANSITION = '{{duration}}ms ease-in-out';\nconst DURATION = { params: { duration: 300 } };\nconst STAGGER = 300;\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiParentAnimation = trigger('tuiParentAnimation', [\n    transition(':leave', [query(':scope > *', [animateChild()], { optional: true })]),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiParentStop = trigger('tuiParentStop', [transition(':enter', [])]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiHost = trigger('tuiHost', [\n    transition(':enter', [\n        style({ overflow: 'clip' }),\n        query(':scope > *', [animateChild()], { optional: true }),\n    ]),\n    transition(':leave', [query(':scope > *', [animateChild()], { optional: true })]),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiHeightCollapse = trigger('tuiHeightCollapse', [\n    transition(':enter', [style({ height: 0 }), animate(TRANSITION, style({ height: '*' }))], DURATION),\n    transition(':leave', [style({ height: '*' }), animate(TRANSITION, style({ height: 0 }))], DURATION),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiHeightCollapseList = trigger('tuiHeightCollapseList', [\n    transition('* => *', [\n        query(':enter', [\n            style({ height: 0 }),\n            stagger(STAGGER, [animate(TRANSITION, style({ height: '*' }))]),\n        ], {\n            optional: true,\n        }),\n        query(':leave', [\n            style({ height: '*' }),\n            stagger(STAGGER, [animate(TRANSITION, style({ height: 0 }))]),\n        ], {\n            optional: true,\n        }),\n    ], DURATION),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiWidthCollapse = trigger('tuiWidthCollapse', [\n    transition(':enter', [style({ width: 0 }), animate(TRANSITION, style({ width: '*' }))], DURATION),\n    transition(':leave', [style({ width: '*' }), animate(TRANSITION, style({ width: 0 }))], DURATION),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiWidthCollapseList = trigger('tuiWidthCollapseList', [\n    transition('* => *', [\n        query(':enter', [\n            style({ width: 0 }),\n            stagger(STAGGER, [animate(TRANSITION, style({ width: '*' }))]),\n        ], {\n            optional: true,\n        }),\n        query(':leave', [\n            style({ width: '*' }),\n            stagger(STAGGER, [animate(TRANSITION, style({ width: 0 }))]),\n        ], {\n            optional: true,\n        }),\n    ], DURATION),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiCrossFade = trigger('tuiCrossFade', [\n    transition(':enter', [style({ opacity: 0 }), animate(TRANSITION, style({ opacity: 1 }))], { params: { duration: 300 } }),\n    transition(':leave', [\n        style({\n            opacity: 1,\n            position: 'absolute',\n            left: '{{left}}',\n            right: '{{right}}',\n            bottom: '{{bottom}}',\n            top: '{{top}}',\n        }),\n        animate(TRANSITION, style({ opacity: 0 })),\n    ], {\n        params: {\n            duration: 300,\n            left: 'auto',\n            right: 'auto',\n            bottom: 'auto',\n            top: 'auto',\n        },\n    }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiFadeIn = trigger('tuiFadeIn', [\n    transition(':enter', [style({ opacity: 0 }), animate(TRANSITION, style({ opacity: 1 }))], DURATION),\n    transition(':leave', [style({ opacity: 1 }), animate(TRANSITION, style({ opacity: 0 }))], DURATION),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiFadeInList = trigger('tuiFadeInList', [\n    transition('* => *', [\n        query(':enter', [\n            style({ opacity: 0 }),\n            stagger(STAGGER, [animate(TRANSITION, style({ opacity: 1 }))]),\n        ], {\n            optional: true,\n        }),\n        query(':leave', [\n            style({ opacity: 1 }),\n            stagger(STAGGER, [animate(TRANSITION, style({ opacity: 0 }))]),\n        ], {\n            optional: true,\n        }),\n    ], DURATION),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiFadeInTop = trigger('tuiFadeInTop', [\n    transition(':enter', [\n        style({ transform: 'translateY(-{{start}}px)', opacity: 0 }),\n        animate(TRANSITION, style({ transform: 'translateY({{end}})', opacity: 1 })),\n    ], { params: { end: 0, start: 10, duration: 300 } }),\n    transition(':leave', [\n        style({ transform: 'translateY({{end}})', opacity: 1 }),\n        animate(TRANSITION, style({ transform: 'translateY(-{{start}}px)', opacity: 0 })),\n    ], { params: { end: 0, start: 10, duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiFadeInBottom = trigger('tuiFadeInBottom', [\n    transition(':enter', [\n        style({ transform: 'translateY({{start}}px)', opacity: 0 }),\n        animate(TRANSITION, style({ transform: 'translateY({{end}})', opacity: 1 })),\n    ], { params: { end: 0, start: 10, duration: 300 } }),\n    transition(':leave', [\n        style({ transform: 'translateY({{end}})', opacity: 1 }),\n        animate(TRANSITION, style({ transform: 'translateY({{start}}px)', opacity: 0 })),\n    ], { params: { end: 0, start: 10, duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiDropdownAnimation = trigger('tuiDropdownAnimation', [\n    transition(':enter', [\n        style({ transform: 'translateY(-{{start}}px)', opacity: 0 }),\n        animate(TRANSITION, style({ transform: 'translateY({{end}})', opacity: 1 })),\n    ], { params: { end: 0, start: 10, duration: 300 } }),\n    transition(':leave', [\n        style({ transform: 'translateY({{end}})', opacity: 1 }),\n        animate(TRANSITION, style({ transform: 'translateY(-{{start}}px)', opacity: 0 })),\n    ], { params: { end: 0, start: 10, duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiScaleIn = trigger('tuiScaleIn', [\n    transition(':enter', [\n        style({ transform: 'scale({{start}})' }),\n        animate('{{duration}}ms {{easing}}', style({ transform: 'scale({{end}})' })),\n    ], { params: { end: 1, start: 0, duration: 300, easing: 'ease-in-out' } }),\n    transition(':leave', [\n        style({ transform: 'scale({{end}})' }),\n        animate(TRANSITION, style({ transform: 'scale({{start}})' })),\n    ], { params: { end: 1, start: 0, duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiPop = trigger('tuiPop', [\n    transition(':enter', [\n        style({ transform: 'scale({{start}})' }),\n        animate(TRANSITION, style({ transform: 'scale({{middle}})' })),\n        animate(TRANSITION, style({ transform: 'scale({{end}})' })),\n    ], { params: { end: 1, middle: 1.1, start: 0, duration: 300 } }),\n    transition(':leave', [\n        style({ transform: 'scale({{end}})' }),\n        animate(TRANSITION, style({ transform: 'scale({{middle}})' })),\n        animate(TRANSITION, style({ transform: 'scale({{start}})' })),\n    ], { params: { end: 1, middle: 1.1, start: 0, duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiScaleInList = trigger('tuiScaleInList', [\n    transition('* => *', [\n        query(':enter', [\n            style({ transform: 'scale({{start}})' }),\n            stagger(STAGGER, [\n                animate(TRANSITION, style({ transform: 'scale({{end}})' })),\n            ]),\n        ], { optional: true }),\n        query(':leave', [\n            style({ transform: 'scale({{end}})' }),\n            stagger(STAGGER, [\n                animate(TRANSITION, style({ transform: 'scale({{start}})' })),\n            ]),\n        ], { optional: true }),\n    ], { params: { end: 1, start: 0, duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideIn = trigger('tuiSlideIn', [\n    transition('* => left', [\n        style({ transform: 'translateX(-{{start}})' }),\n        animate(TRANSITION, style({ transform: 'translateX({{end}})' })),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n    transition('left => *', [\n        style({ transform: 'translateX({{end}})' }),\n        animate(TRANSITION, style({ transform: 'translateX(-{{start}})' })),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n    transition('* => right', [\n        style({ transform: 'translateX({{start}})' }),\n        animate(TRANSITION, style({ transform: 'translateX({{end}})' })),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n    transition('right => *', [\n        style({ transform: 'translateX({{end}})' }),\n        animate(TRANSITION, style({ transform: 'translateX({{start}})' })),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInLeft = trigger('tuiSlideInLeft', [\n    transition(':enter', [\n        style({ transform: 'translateX(-{{start}})' }),\n        animate(TRANSITION, style({ transform: 'translateX({{end}})' })),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n    transition(':leave', [\n        style({ transform: 'translateX({{end}})' }),\n        animate(TRANSITION, style({ transform: 'translateX(-{{start}})' })),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInLeftList = trigger('tuiSlideInLeftList', [\n    transition('* => *', [\n        query(':enter', [\n            style({ transform: 'translateX(-{{start}})' }),\n            stagger(STAGGER, [\n                animate(TRANSITION, style({ transform: 'translateX({{end}})' })),\n            ]),\n        ], { optional: true }),\n        query(':leave', [\n            style({ transform: 'translateX({{end}})' }),\n            stagger(STAGGER, [\n                animate(TRANSITION, style({ transform: 'translateX(-{{start}})' })),\n            ]),\n        ], { optional: true }),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInRight = trigger('tuiSlideInRight', [\n    transition(':enter', [\n        style({ transform: 'translateX({{start}})' }),\n        animate(TRANSITION, style({ transform: 'translateX({{end}})' })),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n    transition(':leave', [\n        style({ transform: 'translateX({{end}})' }),\n        animate(TRANSITION, style({ transform: 'translateX({{start}})' })),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInRightList = trigger('tuiSlideInRightList', [\n    transition('* => *', [\n        query(':enter', [\n            style({ transform: 'translateX({{start}})' }),\n            stagger(STAGGER, [\n                animate(TRANSITION, style({ transform: 'translateX({{end}})' })),\n            ]),\n        ], { optional: true }),\n        query(':leave', [\n            style({ transform: 'translateX({{end}})' }),\n            stagger(STAGGER, [\n                animate(TRANSITION, style({ transform: 'translateX({{start}})' })),\n            ]),\n        ], { optional: true }),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInTop = trigger('tuiSlideInTop', [\n    transition(':enter', [\n        style({ transform: 'translate3d(0,{{start}},0)', pointerEvents: 'none' }),\n        animate(TRANSITION, style({ transform: 'translate3d(0,{{end}},0)' })),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n    transition(':leave', [\n        style({ transform: 'translate3d(0,{{end}},0)' }),\n        animate(TRANSITION, style({ transform: 'translate3d(0,{{start}},0)' })),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInTopList = trigger('tuiSlideInTopList', [\n    transition('* => *', [\n        query(':enter', [\n            style({ transform: 'translateY({{start}})' }),\n            stagger(STAGGER, [\n                animate(TRANSITION, style({ transform: 'translateY({{end}})' })),\n            ]),\n        ], { optional: true }),\n        query(':leave', [\n            style({ transform: 'translateY({{end}})' }),\n            stagger(STAGGER, [\n                animate(TRANSITION, style({ transform: 'translateY({{start}})' })),\n            ]),\n        ], { optional: true }),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInBottom = trigger('tuiSlideInBottom', [\n    transition(':enter', [\n        style({ transform: 'translateY(-{{start}})' }),\n        animate(TRANSITION, style({ transform: 'translateY({{end}})' })),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n    transition(':leave', [\n        style({ transform: 'translateY({{end}})' }),\n        animate(TRANSITION, style({ transform: 'translateY(-{{start}})' })),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n]);\n/**\n * @deprecated Angular animations are deprecated, use {@link TuiAnimated} directive and CSS instead\n */\nconst tuiSlideInBottomList = trigger('tuiSlideInBottomList', [\n    transition('* => *', [\n        query(':enter', [\n            style({ transform: 'translateY(-{{start}})' }),\n            stagger(STAGGER, [\n                animate(TRANSITION, style({ transform: 'translateY({{end}})' })),\n            ]),\n        ], { optional: true }),\n        query(':leave', [\n            style({ transform: 'translateY({{end}})' }),\n            stagger(STAGGER, [\n                animate(TRANSITION, style({ transform: 'translateY(-{{start}})' })),\n            ]),\n        ], { optional: true }),\n    ], { params: { end: 0, start: '100%', duration: 300 } }),\n]);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiCrossFade, tuiDropdownAnimation, tuiFadeIn, tuiFadeInBottom, tuiFadeInList, tuiFadeInTop, tuiHeightCollapse, tuiHeightCollapseList, tuiHost, tuiParentAnimation, tuiParentStop, tuiPop, tuiScaleIn, tuiScaleInList, tuiSlideIn, tuiSlideInBottom, tuiSlideInBottomList, tuiSlideInLeft, tuiSlideInLeftList, tuiSlideInRight, tuiSlideInRightList, tuiSlideInTop, tuiSlideInTopList, tuiWidthCollapse, tuiWidthCollapseList };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,YAAY,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,QAAQ,qBAAqB;AAEvG,MAAMC,UAAU,GAAG,4BAA4B;AAC/C,MAAMC,QAAQ,GAAG;EAAEC,MAAM,EAAE;IAAEC,QAAQ,EAAE;EAAI;AAAE,CAAC;AAC9C,MAAMC,OAAO,GAAG,GAAG;AACnB;AACA;AACA;AACA,MAAMC,kBAAkB,GAAGZ,OAAO,CAAC,oBAAoB,EAAE,CACrDC,UAAU,CAAC,QAAQ,EAAE,CAACC,KAAK,CAAC,YAAY,EAAE,CAACC,YAAY,CAAC,CAAC,CAAC,EAAE;EAAEU,QAAQ,EAAE;AAAK,CAAC,CAAC,CAAC,CAAC,CACpF,CAAC;AACF;AACA;AACA;AACA,MAAMC,aAAa,GAAGd,OAAO,CAAC,eAAe,EAAE,CAACC,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;AAC1E;AACA;AACA;AACA,MAAMc,OAAO,GAAGf,OAAO,CAAC,SAAS,EAAE,CAC/BC,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAEY,QAAQ,EAAE;AAAO,CAAC,CAAC,EAC3Bd,KAAK,CAAC,YAAY,EAAE,CAACC,YAAY,CAAC,CAAC,CAAC,EAAE;EAAEU,QAAQ,EAAE;AAAK,CAAC,CAAC,CAC5D,CAAC,EACFZ,UAAU,CAAC,QAAQ,EAAE,CAACC,KAAK,CAAC,YAAY,EAAE,CAACC,YAAY,CAAC,CAAC,CAAC,EAAE;EAAEU,QAAQ,EAAE;AAAK,CAAC,CAAC,CAAC,CAAC,CACpF,CAAC;AACF;AACA;AACA;AACA,MAAMI,iBAAiB,GAAGjB,OAAO,CAAC,mBAAmB,EAAE,CACnDC,UAAU,CAAC,QAAQ,EAAE,CAACG,KAAK,CAAC;EAAEc,MAAM,EAAE;AAAE,CAAC,CAAC,EAAEb,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEc,MAAM,EAAE;AAAI,CAAC,CAAC,CAAC,CAAC,EAAEV,QAAQ,CAAC,EACnGP,UAAU,CAAC,QAAQ,EAAE,CAACG,KAAK,CAAC;EAAEc,MAAM,EAAE;AAAI,CAAC,CAAC,EAAEb,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEc,MAAM,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,EAAEV,QAAQ,CAAC,CACtG,CAAC;AACF;AACA;AACA;AACA,MAAMW,qBAAqB,GAAGnB,OAAO,CAAC,uBAAuB,EAAE,CAC3DC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAEc,MAAM,EAAE;AAAE,CAAC,CAAC,EACpBZ,OAAO,CAACK,OAAO,EAAE,CAACN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEc,MAAM,EAAE;AAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAClE,EAAE;EACCL,QAAQ,EAAE;AACd,CAAC,CAAC,EACFX,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAEc,MAAM,EAAE;AAAI,CAAC,CAAC,EACtBZ,OAAO,CAACK,OAAO,EAAE,CAACN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEc,MAAM,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAChE,EAAE;EACCL,QAAQ,EAAE;AACd,CAAC,CAAC,CACL,EAAEL,QAAQ,CAAC,CACf,CAAC;AACF;AACA;AACA;AACA,MAAMY,gBAAgB,GAAGpB,OAAO,CAAC,kBAAkB,EAAE,CACjDC,UAAU,CAAC,QAAQ,EAAE,CAACG,KAAK,CAAC;EAAEiB,KAAK,EAAE;AAAE,CAAC,CAAC,EAAEhB,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEiB,KAAK,EAAE;AAAI,CAAC,CAAC,CAAC,CAAC,EAAEb,QAAQ,CAAC,EACjGP,UAAU,CAAC,QAAQ,EAAE,CAACG,KAAK,CAAC;EAAEiB,KAAK,EAAE;AAAI,CAAC,CAAC,EAAEhB,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEiB,KAAK,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,EAAEb,QAAQ,CAAC,CACpG,CAAC;AACF;AACA;AACA;AACA,MAAMc,oBAAoB,GAAGtB,OAAO,CAAC,sBAAsB,EAAE,CACzDC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAEiB,KAAK,EAAE;AAAE,CAAC,CAAC,EACnBf,OAAO,CAACK,OAAO,EAAE,CAACN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEiB,KAAK,EAAE;AAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CACjE,EAAE;EACCR,QAAQ,EAAE;AACd,CAAC,CAAC,EACFX,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAEiB,KAAK,EAAE;AAAI,CAAC,CAAC,EACrBf,OAAO,CAACK,OAAO,EAAE,CAACN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEiB,KAAK,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/D,EAAE;EACCR,QAAQ,EAAE;AACd,CAAC,CAAC,CACL,EAAEL,QAAQ,CAAC,CACf,CAAC;AACF;AACA;AACA;AACA,MAAMe,YAAY,GAAGvB,OAAO,CAAC,cAAc,EAAE,CACzCC,UAAU,CAAC,QAAQ,EAAE,CAACG,KAAK,CAAC;EAAEoB,OAAO,EAAE;AAAE,CAAC,CAAC,EAAEnB,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEoB,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,EAAE;EAAEf,MAAM,EAAE;IAAEC,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,EACxHT,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EACFoB,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE,WAAW;EAClBC,MAAM,EAAE,YAAY;EACpBC,GAAG,EAAE;AACT,CAAC,CAAC,EACFxB,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEoB,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAC7C,EAAE;EACCf,MAAM,EAAE;IACJC,QAAQ,EAAE,GAAG;IACbgB,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,GAAG,EAAE;EACT;AACJ,CAAC,CAAC,CACL,CAAC;AACF;AACA;AACA;AACA,MAAMC,SAAS,GAAG9B,OAAO,CAAC,WAAW,EAAE,CACnCC,UAAU,CAAC,QAAQ,EAAE,CAACG,KAAK,CAAC;EAAEoB,OAAO,EAAE;AAAE,CAAC,CAAC,EAAEnB,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEoB,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,EAAEhB,QAAQ,CAAC,EACnGP,UAAU,CAAC,QAAQ,EAAE,CAACG,KAAK,CAAC;EAAEoB,OAAO,EAAE;AAAE,CAAC,CAAC,EAAEnB,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEoB,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,EAAEhB,QAAQ,CAAC,CACtG,CAAC;AACF;AACA;AACA;AACA,MAAMuB,aAAa,GAAG/B,OAAO,CAAC,eAAe,EAAE,CAC3CC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAEoB,OAAO,EAAE;AAAE,CAAC,CAAC,EACrBlB,OAAO,CAACK,OAAO,EAAE,CAACN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEoB,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CACjE,EAAE;EACCX,QAAQ,EAAE;AACd,CAAC,CAAC,EACFX,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAEoB,OAAO,EAAE;AAAE,CAAC,CAAC,EACrBlB,OAAO,CAACK,OAAO,EAAE,CAACN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAEoB,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CACjE,EAAE;EACCX,QAAQ,EAAE;AACd,CAAC,CAAC,CACL,EAAEL,QAAQ,CAAC,CACf,CAAC;AACF;AACA;AACA;AACA,MAAMwB,YAAY,GAAGhC,OAAO,CAAC,cAAc,EAAE,CACzCC,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE,0BAA0B;EAAET,OAAO,EAAE;AAAE,CAAC,CAAC,EAC5DnB,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE,qBAAqB;EAAET,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAC/E,EAAE;EAAEf,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,EAAE;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,EACpDT,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE,qBAAqB;EAAET,OAAO,EAAE;AAAE,CAAC,CAAC,EACvDnB,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE,0BAA0B;EAAET,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CACpF,EAAE;EAAEf,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,EAAE;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CACvD,CAAC;AACF;AACA;AACA;AACA,MAAM0B,eAAe,GAAGpC,OAAO,CAAC,iBAAiB,EAAE,CAC/CC,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE,yBAAyB;EAAET,OAAO,EAAE;AAAE,CAAC,CAAC,EAC3DnB,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE,qBAAqB;EAAET,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAC/E,EAAE;EAAEf,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,EAAE;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,EACpDT,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE,qBAAqB;EAAET,OAAO,EAAE;AAAE,CAAC,CAAC,EACvDnB,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE,yBAAyB;EAAET,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CACnF,EAAE;EAAEf,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,EAAE;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CACvD,CAAC;AACF;AACA;AACA;AACA,MAAM2B,oBAAoB,GAAGrC,OAAO,CAAC,sBAAsB,EAAE,CACzDC,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE,0BAA0B;EAAET,OAAO,EAAE;AAAE,CAAC,CAAC,EAC5DnB,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE,qBAAqB;EAAET,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAC/E,EAAE;EAAEf,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,EAAE;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,EACpDT,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE,qBAAqB;EAAET,OAAO,EAAE;AAAE,CAAC,CAAC,EACvDnB,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE,0BAA0B;EAAET,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CACpF,EAAE;EAAEf,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,EAAE;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CACvD,CAAC;AACF;AACA;AACA;AACA,MAAM4B,UAAU,GAAGtC,OAAO,CAAC,YAAY,EAAE,CACrCC,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAmB,CAAC,CAAC,EACxC5B,OAAO,CAAC,2BAA2B,EAAED,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAiB,CAAC,CAAC,CAAC,CAC/E,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEzB,QAAQ,EAAE,GAAG;IAAE6B,MAAM,EAAE;EAAc;AAAE,CAAC,CAAC,EAC1EtC,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAiB,CAAC,CAAC,EACtC5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAmB,CAAC,CAAC,CAAC,CAChE,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CACtD,CAAC;AACF;AACA;AACA;AACA,MAAM8B,MAAM,GAAGxC,OAAO,CAAC,QAAQ,EAAE,CAC7BC,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAmB,CAAC,CAAC,EACxC5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAoB,CAAC,CAAC,CAAC,EAC9D5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAiB,CAAC,CAAC,CAAC,CAC9D,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEO,MAAM,EAAE,GAAG;IAAEN,KAAK,EAAE,CAAC;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,EAChET,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAiB,CAAC,CAAC,EACtC5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAoB,CAAC,CAAC,CAAC,EAC9D5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAmB,CAAC,CAAC,CAAC,CAChE,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEO,MAAM,EAAE,GAAG;IAAEN,KAAK,EAAE,CAAC;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CACnE,CAAC;AACF;AACA;AACA;AACA,MAAMgC,cAAc,GAAG1C,OAAO,CAAC,gBAAgB,EAAE,CAC7CC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAmB,CAAC,CAAC,EACxC3B,OAAO,CAACK,OAAO,EAAE,CACbN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAiB,CAAC,CAAC,CAAC,CAC9D,CAAC,CACL,EAAE;EAAEpB,QAAQ,EAAE;AAAK,CAAC,CAAC,EACtBX,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAiB,CAAC,CAAC,EACtC3B,OAAO,CAACK,OAAO,EAAE,CACbN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAmB,CAAC,CAAC,CAAC,CAChE,CAAC,CACL,EAAE;EAAEpB,QAAQ,EAAE;AAAK,CAAC,CAAC,CACzB,EAAE;EAAEJ,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CACtD,CAAC;AACF;AACA;AACA;AACA,MAAMiC,UAAU,GAAG3C,OAAO,CAAC,YAAY,EAAE,CACrCC,UAAU,CAAC,WAAW,EAAE,CACpBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAyB,CAAC,CAAC,EAC9C5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,CAAC,CACnE,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,EACxDT,UAAU,CAAC,WAAW,EAAE,CACpBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,EAC3C5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAyB,CAAC,CAAC,CAAC,CACtE,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,EACxDT,UAAU,CAAC,YAAY,EAAE,CACrBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAwB,CAAC,CAAC,EAC7C5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,CAAC,CACnE,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,EACxDT,UAAU,CAAC,YAAY,EAAE,CACrBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,EAC3C5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAwB,CAAC,CAAC,CAAC,CACrE,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CAC3D,CAAC;AACF;AACA;AACA;AACA,MAAMkC,cAAc,GAAG5C,OAAO,CAAC,gBAAgB,EAAE,CAC7CC,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAyB,CAAC,CAAC,EAC9C5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,CAAC,CACnE,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,EACxDT,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,EAC3C5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAyB,CAAC,CAAC,CAAC,CACtE,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CAC3D,CAAC;AACF;AACA;AACA;AACA,MAAMmC,kBAAkB,GAAG7C,OAAO,CAAC,oBAAoB,EAAE,CACrDC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAyB,CAAC,CAAC,EAC9C3B,OAAO,CAACK,OAAO,EAAE,CACbN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,CAAC,CACnE,CAAC,CACL,EAAE;EAAEpB,QAAQ,EAAE;AAAK,CAAC,CAAC,EACtBX,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,EAC3C3B,OAAO,CAACK,OAAO,EAAE,CACbN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAyB,CAAC,CAAC,CAAC,CACtE,CAAC,CACL,EAAE;EAAEpB,QAAQ,EAAE;AAAK,CAAC,CAAC,CACzB,EAAE;EAAEJ,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CAC3D,CAAC;AACF;AACA;AACA;AACA,MAAMoC,eAAe,GAAG9C,OAAO,CAAC,iBAAiB,EAAE,CAC/CC,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAwB,CAAC,CAAC,EAC7C5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,CAAC,CACnE,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,EACxDT,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,EAC3C5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAwB,CAAC,CAAC,CAAC,CACrE,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CAC3D,CAAC;AACF;AACA;AACA;AACA,MAAMqC,mBAAmB,GAAG/C,OAAO,CAAC,qBAAqB,EAAE,CACvDC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAwB,CAAC,CAAC,EAC7C3B,OAAO,CAACK,OAAO,EAAE,CACbN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,CAAC,CACnE,CAAC,CACL,EAAE;EAAEpB,QAAQ,EAAE;AAAK,CAAC,CAAC,EACtBX,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,EAC3C3B,OAAO,CAACK,OAAO,EAAE,CACbN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAwB,CAAC,CAAC,CAAC,CACrE,CAAC,CACL,EAAE;EAAEpB,QAAQ,EAAE;AAAK,CAAC,CAAC,CACzB,EAAE;EAAEJ,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CAC3D,CAAC;AACF;AACA;AACA;AACA,MAAMsC,aAAa,GAAGhD,OAAO,CAAC,eAAe,EAAE,CAC3CC,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE,4BAA4B;EAAEgB,aAAa,EAAE;AAAO,CAAC,CAAC,EACzE5C,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAA2B,CAAC,CAAC,CAAC,CACxE,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,EACxDT,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAA2B,CAAC,CAAC,EAChD5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAA6B,CAAC,CAAC,CAAC,CAC1E,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CAC3D,CAAC;AACF;AACA;AACA;AACA,MAAMwC,iBAAiB,GAAGlD,OAAO,CAAC,mBAAmB,EAAE,CACnDC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAwB,CAAC,CAAC,EAC7C3B,OAAO,CAACK,OAAO,EAAE,CACbN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,CAAC,CACnE,CAAC,CACL,EAAE;EAAEpB,QAAQ,EAAE;AAAK,CAAC,CAAC,EACtBX,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,EAC3C3B,OAAO,CAACK,OAAO,EAAE,CACbN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAwB,CAAC,CAAC,CAAC,CACrE,CAAC,CACL,EAAE;EAAEpB,QAAQ,EAAE;AAAK,CAAC,CAAC,CACzB,EAAE;EAAEJ,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CAC3D,CAAC;AACF;AACA;AACA;AACA,MAAMyC,gBAAgB,GAAGnD,OAAO,CAAC,kBAAkB,EAAE,CACjDC,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAyB,CAAC,CAAC,EAC9C5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,CAAC,CACnE,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,EACxDT,UAAU,CAAC,QAAQ,EAAE,CACjBG,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,EAC3C5B,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAyB,CAAC,CAAC,CAAC,CACtE,EAAE;EAAExB,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CAC3D,CAAC;AACF;AACA;AACA;AACA,MAAM0C,oBAAoB,GAAGpD,OAAO,CAAC,sBAAsB,EAAE,CACzDC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAyB,CAAC,CAAC,EAC9C3B,OAAO,CAACK,OAAO,EAAE,CACbN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,CAAC,CACnE,CAAC,CACL,EAAE;EAAEpB,QAAQ,EAAE;AAAK,CAAC,CAAC,EACtBX,KAAK,CAAC,QAAQ,EAAE,CACZE,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAsB,CAAC,CAAC,EAC3C3B,OAAO,CAACK,OAAO,EAAE,CACbN,OAAO,CAACE,UAAU,EAAEH,KAAK,CAAC;EAAE6B,SAAS,EAAE;AAAyB,CAAC,CAAC,CAAC,CACtE,CAAC,CACL,EAAE;EAAEpB,QAAQ,EAAE;AAAK,CAAC,CAAC,CACzB,EAAE;EAAEJ,MAAM,EAAE;IAAEyB,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,MAAM;IAAEzB,QAAQ,EAAE;EAAI;AAAE,CAAC,CAAC,CAC3D,CAAC;;AAEF;AACA;AACA;;AAEA,SAASa,YAAY,EAAEc,oBAAoB,EAAEP,SAAS,EAAEM,eAAe,EAAEL,aAAa,EAAEC,YAAY,EAAEf,iBAAiB,EAAEE,qBAAqB,EAAEJ,OAAO,EAAEH,kBAAkB,EAAEE,aAAa,EAAE0B,MAAM,EAAEF,UAAU,EAAEI,cAAc,EAAEC,UAAU,EAAEQ,gBAAgB,EAAEC,oBAAoB,EAAER,cAAc,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,aAAa,EAAEE,iBAAiB,EAAE9B,gBAAgB,EAAEE,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}