{"ast": null, "code": "import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { ElementRef, Renderer2, NgZone, inject, DestroyRef, Directive, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { race, timer, throttleTime, map, skipWhile, take } from 'rxjs';\nimport { WA_ANIMATION_FRAME, WA_WINDOW } from '@ng-web-apis/common';\nimport { TUI_IS_IOS } from '@taiga-ui/cdk/tokens';\nimport { tuiPx, tuiIsPresent, tuiCreateOptions, tuiCreateToken } from '@taiga-ui/cdk/utils';\nimport { tuiZonefreeScheduler } from '@taiga-ui/cdk/observables';\nclass AbstractTuiAutofocusHandler {\n  constructor(el, options) {\n    this.el = el;\n    this.options = options;\n  }\n  get element() {\n    // TODO: Remove when legacy controls are dropped\n    const el = this.el.nativeElement.tagName.includes('-') ? this.el.nativeElement.querySelector(this.options.query) : this.el.nativeElement;\n    return el || this.el.nativeElement;\n  }\n  get isTextFieldElement() {\n    return this.element.matches(this.options.query);\n  }\n}\nconst TIMEOUT = 1000;\nconst NG_ANIMATION_SELECTOR = '.ng-animating';\nclass TuiDefaultAutofocusHandler extends AbstractTuiAutofocusHandler {\n  constructor(el, animationFrame$, zone, options) {\n    super(el, options);\n    this.animationFrame$ = animationFrame$;\n    this.zone = zone;\n  }\n  setFocus() {\n    if (this.isTextFieldElement) {\n      race(timer(this.options.delay || TIMEOUT), this.animationFrame$.pipe(throttleTime(100, tuiZonefreeScheduler(this.zone)), map(() => this.element.closest(NG_ANIMATION_SELECTOR)), skipWhile(Boolean), take(1))).subscribe(() => this.element.focus({\n        preventScroll: this.options.preventScroll\n      }));\n    } else {\n      this.element.focus({\n        preventScroll: true\n      });\n    }\n  }\n}\nconst TEXTFIELD_ATTRS = ['type', 'inputMode', 'autocomplete', 'accept', 'min', 'max', 'step', 'pattern', 'size', 'maxlength'];\nclass TuiIosAutofocusHandler extends AbstractTuiAutofocusHandler {\n  constructor(el, renderer, zone, win, options) {\n    super(el, options);\n    this.renderer = renderer;\n    this.zone = zone;\n    this.win = win;\n  }\n  setFocus() {\n    if (this.isTextFieldElement) {\n      this.zone.runOutsideAngular(() => this.iosWebkitAutofocus());\n    } else {\n      this.element.focus({\n        preventScroll: true\n      });\n    }\n  }\n  iosWebkitAutofocus() {\n    const fakeInput = this.makeFakeInput();\n    const duration = this.getDurationTimeBeforeFocus();\n    let fakeFocusTimeoutId = 0;\n    let elementFocusTimeoutId = 0;\n    const blurHandler = () => fakeInput.focus({\n      preventScroll: true\n    });\n    const focusHandler = () => {\n      clearTimeout(fakeFocusTimeoutId);\n      fakeFocusTimeoutId = this.win.setTimeout(() => {\n        clearTimeout(elementFocusTimeoutId);\n        fakeInput.removeEventListener('blur', blurHandler);\n        fakeInput.removeEventListener('focus', focusHandler);\n        elementFocusTimeoutId = this.win.setTimeout(() => {\n          this.element.focus({\n            preventScroll: this.options.preventScroll\n          });\n          fakeInput.remove();\n        }, duration);\n      });\n    };\n    fakeInput.addEventListener('blur', blurHandler, {\n      once: true\n    });\n    fakeInput.addEventListener('focus', focusHandler);\n    if (this.insideDialog()) {\n      this.win.document.body.appendChild(fakeInput);\n    } else {\n      this.element.parentElement?.appendChild(fakeInput);\n    }\n    fakeInput.focus({\n      preventScroll: true\n    });\n  }\n  /**\n   * @note:\n   * emulate textfield position in layout with cursor\n   * before focus to real textfield element\n   *\n   * required note:\n   * [fakeInput.readOnly = true] ~\n   * don't use {readOnly: true} value, it's doesn't work for emulate autofill\n   *\n   * [fakeInput.style.opacity = 0] ~\n   * don't use {opacity: 0}, sometimes it's doesn't work for emulate real input\n   *\n   * [fakeInput.style.fontSize = 16px] ~\n   * disable possible auto zoom\n   *\n   * [fakeInput.style.top/left] ~\n   * emulate position cursor before focus to real textfield element\n   */\n  makeFakeInput() {\n    const fakeInput = this.renderer.createElement('input');\n    const rect = this.element.getBoundingClientRect();\n    this.patchFakeInputFromFocusableElement(fakeInput);\n    fakeInput.style.height = tuiPx(rect.height);\n    fakeInput.style.width = tuiPx(rect.width / 2);\n    fakeInput.style.position = 'fixed';\n    fakeInput.style.zIndex = '-99999999';\n    fakeInput.style.caretColor = 'transparent';\n    fakeInput.style.border = 'none';\n    fakeInput.style.outline = 'none';\n    fakeInput.style.color = 'transparent';\n    fakeInput.style.background = 'transparent';\n    fakeInput.style.cursor = 'none';\n    fakeInput.style.fontSize = tuiPx(16);\n    fakeInput.style.top = tuiPx(rect.top);\n    fakeInput.style.left = tuiPx(rect.left);\n    return fakeInput;\n  }\n  getDurationTimeBeforeFocus() {\n    return parseFloat(this.win.getComputedStyle(this.element).getPropertyValue('--tui-duration')) || 0;\n  }\n  /**\n   * @note:\n   * unfortunately, in older versions of iOS\n   * there is a bug that the fake input cursor\n   * will move along with the dialog animation\n   * and then that dialog will be shaking\n   */\n  insideDialog() {\n    return !!this.element.closest('tui-dialog');\n  }\n  /**\n   * @note:\n   * inherit basic attributes values from real input\n   * for help iOS detect what do you want see on keyboard,\n   * for example [inputMode=numeric, autocomplete=cc-number]\n   */\n  patchFakeInputFromFocusableElement(fakeInput) {\n    TEXTFIELD_ATTRS.forEach(attr => {\n      const value = this.element.getAttribute(attr);\n      if (tuiIsPresent(value)) {\n        fakeInput.setAttribute(attr, value);\n      }\n    });\n  }\n}\nconst [TUI_AUTOFOCUS_OPTIONS, tuiAutoFocusOptionsProvider] = tuiCreateOptions({\n  delay: NaN,\n  query: 'input, textarea, select, [contenteditable]',\n  preventScroll: false\n});\nconst TUI_AUTOFOCUS_HANDLER = tuiCreateToken();\nconst TUI_AUTOFOCUS_PROVIDERS = [{\n  provide: TUI_AUTOFOCUS_HANDLER,\n  deps: [ElementRef, WA_ANIMATION_FRAME, Renderer2, NgZone, WA_WINDOW, TUI_IS_IOS, TUI_AUTOFOCUS_OPTIONS],\n  // eslint-disable-next-line @typescript-eslint/max-params,max-params\n  useFactory: (el, animationFrame$, renderer, zone, win, isIos, options) => isIos ? new TuiIosAutofocusHandler(el, renderer, zone, win, options) : new TuiDefaultAutofocusHandler(el, animationFrame$, zone, options)\n}];\nclass TuiAutoFocus {\n  constructor() {\n    this.handler = inject(TUI_AUTOFOCUS_HANDLER);\n    this.options = inject(TUI_AUTOFOCUS_OPTIONS);\n    this.destroyRef = inject(DestroyRef);\n  }\n  ngAfterViewInit() {\n    if (this.autoFocus) {\n      this.focus();\n    }\n  }\n  focus() {\n    if (Number.isNaN(this.options.delay)) {\n      void Promise.resolve().then(() => this.handler.setFocus());\n    } else {\n      timer(this.options.delay).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => this.handler.setFocus());\n    }\n  }\n  static {\n    this.ɵfac = function TuiAutoFocus_Factory(t) {\n      return new (t || TuiAutoFocus)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiAutoFocus,\n      selectors: [[\"\", \"tuiAutoFocus\", \"\"]],\n      inputs: {\n        autoFocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tuiAutoFocus\", \"autoFocus\", coerceBooleanProperty]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature(TUI_AUTOFOCUS_PROVIDERS), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiAutoFocus, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiAutoFocus]',\n      providers: TUI_AUTOFOCUS_PROVIDERS\n    }]\n  }], null, {\n    autoFocus: [{\n      type: Input,\n      args: [{\n        alias: 'tuiAutoFocus',\n        transform: coerceBooleanProperty\n      }]\n    }]\n  });\n})();\nclass TuiSynchronousAutofocusHandler extends AbstractTuiAutofocusHandler {\n  setFocus() {\n    this.element.focus({\n      preventScroll: true\n    });\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AbstractTuiAutofocusHandler, TUI_AUTOFOCUS_HANDLER, TUI_AUTOFOCUS_OPTIONS, TUI_AUTOFOCUS_PROVIDERS, TuiAutoFocus, TuiDefaultAutofocusHandler, TuiIosAutofocusHandler, TuiSynchronousAutofocusHandler, tuiAutoFocusOptionsProvider };", "map": {"version": 3, "names": ["coerceBooleanProperty", "i0", "ElementRef", "Renderer2", "NgZone", "inject", "DestroyRef", "Directive", "Input", "takeUntilDestroyed", "race", "timer", "throttleTime", "map", "<PERSON><PERSON><PERSON><PERSON>", "take", "WA_ANIMATION_FRAME", "WA_WINDOW", "TUI_IS_IOS", "tuiPx", "tuiIsPresent", "tuiCreateOptions", "tuiCreateToken", "tuiZonefreeScheduler", "AbstractTuiAutofocusHandler", "constructor", "el", "options", "element", "nativeElement", "tagName", "includes", "querySelector", "query", "isTextFieldElement", "matches", "TIMEOUT", "NG_ANIMATION_SELECTOR", "TuiDefaultAutofocusHandler", "animationFrame$", "zone", "setFocus", "delay", "pipe", "closest", "Boolean", "subscribe", "focus", "preventScroll", "TEXTFIELD_ATTRS", "TuiIosAutofocusHandler", "renderer", "win", "runOutsideAngular", "iosWebkitAutofocus", "fakeInput", "makeFakeInput", "duration", "getDurationTimeBeforeFocus", "fakeFocusTimeoutId", "elementFocusTimeoutId", "<PERSON><PERSON><PERSON><PERSON>", "focusHandler", "clearTimeout", "setTimeout", "removeEventListener", "remove", "addEventListener", "once", "insideDialog", "document", "body", "append<PERSON><PERSON><PERSON>", "parentElement", "createElement", "rect", "getBoundingClientRect", "patchFakeInputFromFocusableElement", "style", "height", "width", "position", "zIndex", "caretColor", "border", "outline", "color", "background", "cursor", "fontSize", "top", "left", "parseFloat", "getComputedStyle", "getPropertyValue", "for<PERSON>ach", "attr", "value", "getAttribute", "setAttribute", "TUI_AUTOFOCUS_OPTIONS", "tuiAutoFocusOptionsProvider", "NaN", "TUI_AUTOFOCUS_HANDLER", "TUI_AUTOFOCUS_PROVIDERS", "provide", "deps", "useFactory", "isIos", "TuiAutoFocus", "handler", "destroyRef", "ngAfterViewInit", "autoFocus", "Number", "isNaN", "Promise", "resolve", "then", "ɵfac", "TuiAutoFocus_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInputTransformsFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "alias", "transform", "TuiSynchronousAutofocusHandler"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-auto-focus.mjs"], "sourcesContent": ["import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { ElementRef, Renderer2, NgZone, inject, DestroyRef, Directive, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { race, timer, throttleTime, map, skipWhile, take } from 'rxjs';\nimport { WA_ANIMATION_FRAME, WA_WINDOW } from '@ng-web-apis/common';\nimport { TUI_IS_IOS } from '@taiga-ui/cdk/tokens';\nimport { tuiPx, tuiIsPresent, tuiCreateOptions, tuiCreateToken } from '@taiga-ui/cdk/utils';\nimport { tuiZonefreeScheduler } from '@taiga-ui/cdk/observables';\n\nclass AbstractTuiAutofocusHandler {\n    constructor(el, options) {\n        this.el = el;\n        this.options = options;\n    }\n    get element() {\n        // TODO: Remove when legacy controls are dropped\n        const el = this.el.nativeElement.tagName.includes('-')\n            ? this.el.nativeElement.querySelector(this.options.query)\n            : this.el.nativeElement;\n        return el || this.el.nativeElement;\n    }\n    get isTextFieldElement() {\n        return this.element.matches(this.options.query);\n    }\n}\n\nconst TIMEOUT = 1000;\nconst NG_ANIMATION_SELECTOR = '.ng-animating';\nclass TuiDefaultAutofocusHandler extends AbstractTuiAutofocusHandler {\n    constructor(el, animationFrame$, zone, options) {\n        super(el, options);\n        this.animationFrame$ = animationFrame$;\n        this.zone = zone;\n    }\n    setFocus() {\n        if (this.isTextFieldElement) {\n            race(timer(this.options.delay || TIMEOUT), this.animationFrame$.pipe(throttleTime(100, tuiZonefreeScheduler(this.zone)), map(() => this.element.closest(NG_ANIMATION_SELECTOR)), skipWhile(Boolean), take(1))).subscribe(() => this.element.focus({ preventScroll: this.options.preventScroll }));\n        }\n        else {\n            this.element.focus({ preventScroll: true });\n        }\n    }\n}\n\nconst TEXTFIELD_ATTRS = [\n    'type',\n    'inputMode',\n    'autocomplete',\n    'accept',\n    'min',\n    'max',\n    'step',\n    'pattern',\n    'size',\n    'maxlength',\n];\nclass TuiIosAutofocusHandler extends AbstractTuiAutofocusHandler {\n    constructor(el, renderer, zone, win, options) {\n        super(el, options);\n        this.renderer = renderer;\n        this.zone = zone;\n        this.win = win;\n    }\n    setFocus() {\n        if (this.isTextFieldElement) {\n            this.zone.runOutsideAngular(() => this.iosWebkitAutofocus());\n        }\n        else {\n            this.element.focus({ preventScroll: true });\n        }\n    }\n    iosWebkitAutofocus() {\n        const fakeInput = this.makeFakeInput();\n        const duration = this.getDurationTimeBeforeFocus();\n        let fakeFocusTimeoutId = 0;\n        let elementFocusTimeoutId = 0;\n        const blurHandler = () => fakeInput.focus({ preventScroll: true });\n        const focusHandler = () => {\n            clearTimeout(fakeFocusTimeoutId);\n            fakeFocusTimeoutId = this.win.setTimeout(() => {\n                clearTimeout(elementFocusTimeoutId);\n                fakeInput.removeEventListener('blur', blurHandler);\n                fakeInput.removeEventListener('focus', focusHandler);\n                elementFocusTimeoutId = this.win.setTimeout(() => {\n                    this.element.focus({ preventScroll: this.options.preventScroll });\n                    fakeInput.remove();\n                }, duration);\n            });\n        };\n        fakeInput.addEventListener('blur', blurHandler, { once: true });\n        fakeInput.addEventListener('focus', focusHandler);\n        if (this.insideDialog()) {\n            this.win.document.body.appendChild(fakeInput);\n        }\n        else {\n            this.element.parentElement?.appendChild(fakeInput);\n        }\n        fakeInput.focus({ preventScroll: true });\n    }\n    /**\n     * @note:\n     * emulate textfield position in layout with cursor\n     * before focus to real textfield element\n     *\n     * required note:\n     * [fakeInput.readOnly = true] ~\n     * don't use {readOnly: true} value, it's doesn't work for emulate autofill\n     *\n     * [fakeInput.style.opacity = 0] ~\n     * don't use {opacity: 0}, sometimes it's doesn't work for emulate real input\n     *\n     * [fakeInput.style.fontSize = 16px] ~\n     * disable possible auto zoom\n     *\n     * [fakeInput.style.top/left] ~\n     * emulate position cursor before focus to real textfield element\n     */\n    makeFakeInput() {\n        const fakeInput = this.renderer.createElement('input');\n        const rect = this.element.getBoundingClientRect();\n        this.patchFakeInputFromFocusableElement(fakeInput);\n        fakeInput.style.height = tuiPx(rect.height);\n        fakeInput.style.width = tuiPx(rect.width / 2);\n        fakeInput.style.position = 'fixed';\n        fakeInput.style.zIndex = '-99999999';\n        fakeInput.style.caretColor = 'transparent';\n        fakeInput.style.border = 'none';\n        fakeInput.style.outline = 'none';\n        fakeInput.style.color = 'transparent';\n        fakeInput.style.background = 'transparent';\n        fakeInput.style.cursor = 'none';\n        fakeInput.style.fontSize = tuiPx(16);\n        fakeInput.style.top = tuiPx(rect.top);\n        fakeInput.style.left = tuiPx(rect.left);\n        return fakeInput;\n    }\n    getDurationTimeBeforeFocus() {\n        return (parseFloat(this.win\n            .getComputedStyle(this.element)\n            .getPropertyValue('--tui-duration')) || 0);\n    }\n    /**\n     * @note:\n     * unfortunately, in older versions of iOS\n     * there is a bug that the fake input cursor\n     * will move along with the dialog animation\n     * and then that dialog will be shaking\n     */\n    insideDialog() {\n        return !!this.element.closest('tui-dialog');\n    }\n    /**\n     * @note:\n     * inherit basic attributes values from real input\n     * for help iOS detect what do you want see on keyboard,\n     * for example [inputMode=numeric, autocomplete=cc-number]\n     */\n    patchFakeInputFromFocusableElement(fakeInput) {\n        TEXTFIELD_ATTRS.forEach((attr) => {\n            const value = this.element.getAttribute(attr);\n            if (tuiIsPresent(value)) {\n                fakeInput.setAttribute(attr, value);\n            }\n        });\n    }\n}\n\nconst [TUI_AUTOFOCUS_OPTIONS, tuiAutoFocusOptionsProvider] = tuiCreateOptions({\n    delay: NaN,\n    query: 'input, textarea, select, [contenteditable]',\n    preventScroll: false,\n});\nconst TUI_AUTOFOCUS_HANDLER = tuiCreateToken();\nconst TUI_AUTOFOCUS_PROVIDERS = [\n    {\n        provide: TUI_AUTOFOCUS_HANDLER,\n        deps: [\n            ElementRef,\n            WA_ANIMATION_FRAME,\n            Renderer2,\n            NgZone,\n            WA_WINDOW,\n            TUI_IS_IOS,\n            TUI_AUTOFOCUS_OPTIONS,\n        ],\n        // eslint-disable-next-line @typescript-eslint/max-params,max-params\n        useFactory: (el, animationFrame$, renderer, zone, win, isIos, options) => isIos\n            ? new TuiIosAutofocusHandler(el, renderer, zone, win, options)\n            : new TuiDefaultAutofocusHandler(el, animationFrame$, zone, options),\n    },\n];\n\nclass TuiAutoFocus {\n    constructor() {\n        this.handler = inject(TUI_AUTOFOCUS_HANDLER);\n        this.options = inject(TUI_AUTOFOCUS_OPTIONS);\n        this.destroyRef = inject(DestroyRef);\n    }\n    ngAfterViewInit() {\n        if (this.autoFocus) {\n            this.focus();\n        }\n    }\n    focus() {\n        if (Number.isNaN(this.options.delay)) {\n            void Promise.resolve().then(() => this.handler.setFocus());\n        }\n        else {\n            timer(this.options.delay)\n                .pipe(takeUntilDestroyed(this.destroyRef))\n                .subscribe(() => this.handler.setFocus());\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAutoFocus, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"16.2.12\", type: TuiAutoFocus, isStandalone: true, selector: \"[tuiAutoFocus]\", inputs: { autoFocus: [\"tuiAutoFocus\", \"autoFocus\", coerceBooleanProperty] }, providers: TUI_AUTOFOCUS_PROVIDERS, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiAutoFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiAutoFocus]',\n                    providers: TUI_AUTOFOCUS_PROVIDERS,\n                }]\n        }], propDecorators: { autoFocus: [{\n                type: Input,\n                args: [{\n                        alias: 'tuiAutoFocus',\n                        transform: coerceBooleanProperty,\n                    }]\n            }] } });\n\nclass TuiSynchronousAutofocusHandler extends AbstractTuiAutofocusHandler {\n    setFocus() {\n        this.element.focus({ preventScroll: true });\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AbstractTuiAutofocusHandler, TUI_AUTOFOCUS_HANDLER, TUI_AUTOFOCUS_OPTIONS, TUI_AUTOFOCUS_PROVIDERS, TuiAutoFocus, TuiDefaultAutofocusHandler, TuiIosAutofocusHandler, TuiSynchronousAutofocusHandler, tuiAutoFocusOptionsProvider };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,uBAAuB;AAC7D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACnG,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,IAAI,EAAEC,KAAK,EAAEC,YAAY,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,QAAQ,MAAM;AACtE,SAASC,kBAAkB,EAAEC,SAAS,QAAQ,qBAAqB;AACnE,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,KAAK,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,qBAAqB;AAC3F,SAASC,oBAAoB,QAAQ,2BAA2B;AAEhE,MAAMC,2BAA2B,CAAC;EAC9BC,WAAWA,CAACC,EAAE,EAAEC,OAAO,EAAE;IACrB,IAAI,CAACD,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV;IACA,MAAMF,EAAE,GAAG,IAAI,CAACA,EAAE,CAACG,aAAa,CAACC,OAAO,CAACC,QAAQ,CAAC,GAAG,CAAC,GAChD,IAAI,CAACL,EAAE,CAACG,aAAa,CAACG,aAAa,CAAC,IAAI,CAACL,OAAO,CAACM,KAAK,CAAC,GACvD,IAAI,CAACP,EAAE,CAACG,aAAa;IAC3B,OAAOH,EAAE,IAAI,IAAI,CAACA,EAAE,CAACG,aAAa;EACtC;EACA,IAAIK,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACN,OAAO,CAACO,OAAO,CAAC,IAAI,CAACR,OAAO,CAACM,KAAK,CAAC;EACnD;AACJ;AAEA,MAAMG,OAAO,GAAG,IAAI;AACpB,MAAMC,qBAAqB,GAAG,eAAe;AAC7C,MAAMC,0BAA0B,SAASd,2BAA2B,CAAC;EACjEC,WAAWA,CAACC,EAAE,EAAEa,eAAe,EAAEC,IAAI,EAAEb,OAAO,EAAE;IAC5C,KAAK,CAACD,EAAE,EAAEC,OAAO,CAAC;IAClB,IAAI,CAACY,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACP,kBAAkB,EAAE;MACzBxB,IAAI,CAACC,KAAK,CAAC,IAAI,CAACgB,OAAO,CAACe,KAAK,IAAIN,OAAO,CAAC,EAAE,IAAI,CAACG,eAAe,CAACI,IAAI,CAAC/B,YAAY,CAAC,GAAG,EAAEW,oBAAoB,CAAC,IAAI,CAACiB,IAAI,CAAC,CAAC,EAAE3B,GAAG,CAAC,MAAM,IAAI,CAACe,OAAO,CAACgB,OAAO,CAACP,qBAAqB,CAAC,CAAC,EAAEvB,SAAS,CAAC+B,OAAO,CAAC,EAAE9B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC+B,SAAS,CAAC,MAAM,IAAI,CAAClB,OAAO,CAACmB,KAAK,CAAC;QAAEC,aAAa,EAAE,IAAI,CAACrB,OAAO,CAACqB;MAAc,CAAC,CAAC,CAAC;IACrS,CAAC,MACI;MACD,IAAI,CAACpB,OAAO,CAACmB,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;IAC/C;EACJ;AACJ;AAEA,MAAMC,eAAe,GAAG,CACpB,MAAM,EACN,WAAW,EACX,cAAc,EACd,QAAQ,EACR,KAAK,EACL,KAAK,EACL,MAAM,EACN,SAAS,EACT,MAAM,EACN,WAAW,CACd;AACD,MAAMC,sBAAsB,SAAS1B,2BAA2B,CAAC;EAC7DC,WAAWA,CAACC,EAAE,EAAEyB,QAAQ,EAAEX,IAAI,EAAEY,GAAG,EAAEzB,OAAO,EAAE;IAC1C,KAAK,CAACD,EAAE,EAAEC,OAAO,CAAC;IAClB,IAAI,CAACwB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACX,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACY,GAAG,GAAGA,GAAG;EAClB;EACAX,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACP,kBAAkB,EAAE;MACzB,IAAI,CAACM,IAAI,CAACa,iBAAiB,CAAC,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAAC;IAChE,CAAC,MACI;MACD,IAAI,CAAC1B,OAAO,CAACmB,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;IAC/C;EACJ;EACAM,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACtC,MAAMC,QAAQ,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAC;IAClD,IAAIC,kBAAkB,GAAG,CAAC;IAC1B,IAAIC,qBAAqB,GAAG,CAAC;IAC7B,MAAMC,WAAW,GAAGA,CAAA,KAAMN,SAAS,CAACR,KAAK,CAAC;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC;IAClE,MAAMc,YAAY,GAAGA,CAAA,KAAM;MACvBC,YAAY,CAACJ,kBAAkB,CAAC;MAChCA,kBAAkB,GAAG,IAAI,CAACP,GAAG,CAACY,UAAU,CAAC,MAAM;QAC3CD,YAAY,CAACH,qBAAqB,CAAC;QACnCL,SAAS,CAACU,mBAAmB,CAAC,MAAM,EAAEJ,WAAW,CAAC;QAClDN,SAAS,CAACU,mBAAmB,CAAC,OAAO,EAAEH,YAAY,CAAC;QACpDF,qBAAqB,GAAG,IAAI,CAACR,GAAG,CAACY,UAAU,CAAC,MAAM;UAC9C,IAAI,CAACpC,OAAO,CAACmB,KAAK,CAAC;YAAEC,aAAa,EAAE,IAAI,CAACrB,OAAO,CAACqB;UAAc,CAAC,CAAC;UACjEO,SAAS,CAACW,MAAM,CAAC,CAAC;QACtB,CAAC,EAAET,QAAQ,CAAC;MAChB,CAAC,CAAC;IACN,CAAC;IACDF,SAAS,CAACY,gBAAgB,CAAC,MAAM,EAAEN,WAAW,EAAE;MAAEO,IAAI,EAAE;IAAK,CAAC,CAAC;IAC/Db,SAAS,CAACY,gBAAgB,CAAC,OAAO,EAAEL,YAAY,CAAC;IACjD,IAAI,IAAI,CAACO,YAAY,CAAC,CAAC,EAAE;MACrB,IAAI,CAACjB,GAAG,CAACkB,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACjB,SAAS,CAAC;IACjD,CAAC,MACI;MACD,IAAI,CAAC3B,OAAO,CAAC6C,aAAa,EAAED,WAAW,CAACjB,SAAS,CAAC;IACtD;IACAA,SAAS,CAACR,KAAK,CAAC;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIQ,aAAaA,CAAA,EAAG;IACZ,MAAMD,SAAS,GAAG,IAAI,CAACJ,QAAQ,CAACuB,aAAa,CAAC,OAAO,CAAC;IACtD,MAAMC,IAAI,GAAG,IAAI,CAAC/C,OAAO,CAACgD,qBAAqB,CAAC,CAAC;IACjD,IAAI,CAACC,kCAAkC,CAACtB,SAAS,CAAC;IAClDA,SAAS,CAACuB,KAAK,CAACC,MAAM,GAAG5D,KAAK,CAACwD,IAAI,CAACI,MAAM,CAAC;IAC3CxB,SAAS,CAACuB,KAAK,CAACE,KAAK,GAAG7D,KAAK,CAACwD,IAAI,CAACK,KAAK,GAAG,CAAC,CAAC;IAC7CzB,SAAS,CAACuB,KAAK,CAACG,QAAQ,GAAG,OAAO;IAClC1B,SAAS,CAACuB,KAAK,CAACI,MAAM,GAAG,WAAW;IACpC3B,SAAS,CAACuB,KAAK,CAACK,UAAU,GAAG,aAAa;IAC1C5B,SAAS,CAACuB,KAAK,CAACM,MAAM,GAAG,MAAM;IAC/B7B,SAAS,CAACuB,KAAK,CAACO,OAAO,GAAG,MAAM;IAChC9B,SAAS,CAACuB,KAAK,CAACQ,KAAK,GAAG,aAAa;IACrC/B,SAAS,CAACuB,KAAK,CAACS,UAAU,GAAG,aAAa;IAC1ChC,SAAS,CAACuB,KAAK,CAACU,MAAM,GAAG,MAAM;IAC/BjC,SAAS,CAACuB,KAAK,CAACW,QAAQ,GAAGtE,KAAK,CAAC,EAAE,CAAC;IACpCoC,SAAS,CAACuB,KAAK,CAACY,GAAG,GAAGvE,KAAK,CAACwD,IAAI,CAACe,GAAG,CAAC;IACrCnC,SAAS,CAACuB,KAAK,CAACa,IAAI,GAAGxE,KAAK,CAACwD,IAAI,CAACgB,IAAI,CAAC;IACvC,OAAOpC,SAAS;EACpB;EACAG,0BAA0BA,CAAA,EAAG;IACzB,OAAQkC,UAAU,CAAC,IAAI,CAACxC,GAAG,CACtByC,gBAAgB,CAAC,IAAI,CAACjE,OAAO,CAAC,CAC9BkE,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIzB,YAAYA,CAAA,EAAG;IACX,OAAO,CAAC,CAAC,IAAI,CAACzC,OAAO,CAACgB,OAAO,CAAC,YAAY,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiC,kCAAkCA,CAACtB,SAAS,EAAE;IAC1CN,eAAe,CAAC8C,OAAO,CAAEC,IAAI,IAAK;MAC9B,MAAMC,KAAK,GAAG,IAAI,CAACrE,OAAO,CAACsE,YAAY,CAACF,IAAI,CAAC;MAC7C,IAAI5E,YAAY,CAAC6E,KAAK,CAAC,EAAE;QACrB1C,SAAS,CAAC4C,YAAY,CAACH,IAAI,EAAEC,KAAK,CAAC;MACvC;IACJ,CAAC,CAAC;EACN;AACJ;AAEA,MAAM,CAACG,qBAAqB,EAAEC,2BAA2B,CAAC,GAAGhF,gBAAgB,CAAC;EAC1EqB,KAAK,EAAE4D,GAAG;EACVrE,KAAK,EAAE,4CAA4C;EACnDe,aAAa,EAAE;AACnB,CAAC,CAAC;AACF,MAAMuD,qBAAqB,GAAGjF,cAAc,CAAC,CAAC;AAC9C,MAAMkF,uBAAuB,GAAG,CAC5B;EACIC,OAAO,EAAEF,qBAAqB;EAC9BG,IAAI,EAAE,CACFxG,UAAU,EACVc,kBAAkB,EAClBb,SAAS,EACTC,MAAM,EACNa,SAAS,EACTC,UAAU,EACVkF,qBAAqB,CACxB;EACD;EACAO,UAAU,EAAEA,CAACjF,EAAE,EAAEa,eAAe,EAAEY,QAAQ,EAAEX,IAAI,EAAEY,GAAG,EAAEwD,KAAK,EAAEjF,OAAO,KAAKiF,KAAK,GACzE,IAAI1D,sBAAsB,CAACxB,EAAE,EAAEyB,QAAQ,EAAEX,IAAI,EAAEY,GAAG,EAAEzB,OAAO,CAAC,GAC5D,IAAIW,0BAA0B,CAACZ,EAAE,EAAEa,eAAe,EAAEC,IAAI,EAAEb,OAAO;AAC3E,CAAC,CACJ;AAED,MAAMkF,YAAY,CAAC;EACfpF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqF,OAAO,GAAGzG,MAAM,CAACkG,qBAAqB,CAAC;IAC5C,IAAI,CAAC5E,OAAO,GAAGtB,MAAM,CAAC+F,qBAAqB,CAAC;IAC5C,IAAI,CAACW,UAAU,GAAG1G,MAAM,CAACC,UAAU,CAAC;EACxC;EACA0G,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,SAAS,EAAE;MAChB,IAAI,CAAClE,KAAK,CAAC,CAAC;IAChB;EACJ;EACAA,KAAKA,CAAA,EAAG;IACJ,IAAImE,MAAM,CAACC,KAAK,CAAC,IAAI,CAACxF,OAAO,CAACe,KAAK,CAAC,EAAE;MAClC,KAAK0E,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACR,OAAO,CAACrE,QAAQ,CAAC,CAAC,CAAC;IAC9D,CAAC,MACI;MACD9B,KAAK,CAAC,IAAI,CAACgB,OAAO,CAACe,KAAK,CAAC,CACpBC,IAAI,CAAClC,kBAAkB,CAAC,IAAI,CAACsG,UAAU,CAAC,CAAC,CACzCjE,SAAS,CAAC,MAAM,IAAI,CAACgE,OAAO,CAACrE,QAAQ,CAAC,CAAC,CAAC;IACjD;EACJ;EACA;IAAS,IAAI,CAAC8E,IAAI,YAAAC,qBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFZ,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACa,IAAI,kBAD+EzH,EAAE,CAAA0H,iBAAA;MAAAC,IAAA,EACJf,YAAY;MAAAgB,SAAA;MAAAC,MAAA;QAAAb,SAAA,GADVhH,EAAE,CAAA8H,YAAA,CAAAC,0BAAA,+BAC6GhI,qBAAqB;MAAA;MAAAiI,UAAA;MAAAC,QAAA,GADpIjI,EAAE,CAAAkI,kBAAA,CACkJ3B,uBAAuB,GAD3KvG,EAAE,CAAAmI,wBAAA;IAAA,EAC0L;EAAE;AACnS;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGpI,EAAE,CAAAqI,iBAAA,CAGXzB,YAAY,EAAc,CAAC;IAC3Ge,IAAI,EAAErH,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCN,UAAU,EAAE,IAAI;MAChBO,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAEjC;IACf,CAAC;EACT,CAAC,CAAC,QAAkB;IAAES,SAAS,EAAE,CAAC;MAC1BW,IAAI,EAAEpH,KAAK;MACX+H,IAAI,EAAE,CAAC;QACCG,KAAK,EAAE,cAAc;QACrBC,SAAS,EAAE3I;MACf,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM4I,8BAA8B,SAASpH,2BAA2B,CAAC;EACrEiB,QAAQA,CAAA,EAAG;IACP,IAAI,CAACb,OAAO,CAACmB,KAAK,CAAC;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC;EAC/C;AACJ;;AAEA;AACA;AACA;;AAEA,SAASxB,2BAA2B,EAAE+E,qBAAqB,EAAEH,qBAAqB,EAAEI,uBAAuB,EAAEK,YAAY,EAAEvE,0BAA0B,EAAEY,sBAAsB,EAAE0F,8BAA8B,EAAEvC,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}