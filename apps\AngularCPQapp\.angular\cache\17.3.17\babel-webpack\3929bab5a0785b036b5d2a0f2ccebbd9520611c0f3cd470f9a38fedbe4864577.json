{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive, Input } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nclass TuiTitleStyles {\n  static {\n    this.ɵfac = function TuiTitleStyles_Factory(t) {\n      return new (t || TuiTitleStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiTitleStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-title\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiTitleStyles_Template(rf, ctx) {},\n      styles: [\"[tuiTitle]{position:relative;display:flex;min-inline-size:0;flex-direction:column;text-align:start;gap:.25rem;margin:0;font:var(--tui-font-text-ui-m)}[tuiTitle][data-size=s]{gap:.125rem;font:var(--tui-font-text-s)}[tuiTitle][data-size=s] [tuiSubtitle]{font:var(--tui-font-text-xs)}[tuiTitle][data-size=m]{gap:.125rem;font:var(--tui-font-heading-5)}[tuiTitle][data-size=m] [tuiSubtitle]{font:var(--tui-font-text-m)}[tuiTitle][data-size=l]{gap:.5rem;font:var(--tui-font-heading-3)}[tuiTitle][data-size=l] [tuiSubtitle]{font:var(--tui-font-text-m)}[tuiTitle] h1,[tuiTitle] h2,[tuiTitle] h3,[tuiTitle] h4,[tuiTitle] h5,[tuiTitle] h6{margin:0;font:inherit}[tuiSubtitle]{font:var(--tui-font-text-ui-s);margin:0}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTitleStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-title'\n      },\n      styles: [\"[tuiTitle]{position:relative;display:flex;min-inline-size:0;flex-direction:column;text-align:start;gap:.25rem;margin:0;font:var(--tui-font-text-ui-m)}[tuiTitle][data-size=s]{gap:.125rem;font:var(--tui-font-text-s)}[tuiTitle][data-size=s] [tuiSubtitle]{font:var(--tui-font-text-xs)}[tuiTitle][data-size=m]{gap:.125rem;font:var(--tui-font-heading-5)}[tuiTitle][data-size=m] [tuiSubtitle]{font:var(--tui-font-text-m)}[tuiTitle][data-size=l]{gap:.5rem;font:var(--tui-font-heading-3)}[tuiTitle][data-size=l] [tuiSubtitle]{font:var(--tui-font-text-m)}[tuiTitle] h1,[tuiTitle] h2,[tuiTitle] h3,[tuiTitle] h4,[tuiTitle] h5,[tuiTitle] h6{margin:0;font:inherit}[tuiSubtitle]{font:var(--tui-font-text-ui-s);margin:0}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiTitle {\n  constructor() {\n    this.nothing = tuiWithStyles(TuiTitleStyles);\n    this.size = '';\n  }\n  static {\n    this.ɵfac = function TuiTitle_Factory(t) {\n      return new (t || TuiTitle)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiTitle,\n      selectors: [[\"\", \"tuiTitle\", \"\"]],\n      hostAttrs: [\"tuiTitle\", \"\"],\n      hostVars: 1,\n      hostBindings: function TuiTitle_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size || null);\n        }\n      },\n      inputs: {\n        size: [i0.ɵɵInputFlags.None, \"tuiTitle\", \"size\"]\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiTitle, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiTitle]',\n      host: {\n        tuiTitle: '',\n        '[attr.data-size]': 'size || null'\n      }\n    }]\n  }], null, {\n    size: [{\n      type: Input,\n      args: ['tuiTitle']\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiTitle };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Directive", "Input", "tuiWithStyles", "TuiTitleStyles", "ɵfac", "TuiTitleStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiTitleStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "Tui<PERSON>it<PERSON>", "constructor", "nothing", "size", "TuiTitle_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiTitle_HostBindings", "ɵɵattribute", "inputs", "ɵɵInputFlags", "selector", "tui<PERSON><PERSON>le"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-directives-title.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive, Input } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\n\nclass TuiTitleStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTitleStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTitleStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-title\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiTitle]{position:relative;display:flex;min-inline-size:0;flex-direction:column;text-align:start;gap:.25rem;margin:0;font:var(--tui-font-text-ui-m)}[tuiTitle][data-size=s]{gap:.125rem;font:var(--tui-font-text-s)}[tuiTitle][data-size=s] [tuiSubtitle]{font:var(--tui-font-text-xs)}[tuiTitle][data-size=m]{gap:.125rem;font:var(--tui-font-heading-5)}[tuiTitle][data-size=m] [tuiSubtitle]{font:var(--tui-font-text-m)}[tuiTitle][data-size=l]{gap:.5rem;font:var(--tui-font-heading-3)}[tuiTitle][data-size=l] [tuiSubtitle]{font:var(--tui-font-text-m)}[tuiTitle] h1,[tuiTitle] h2,[tuiTitle] h3,[tuiTitle] h4,[tuiTitle] h5,[tuiTitle] h6{margin:0;font:inherit}[tuiSubtitle]{font:var(--tui-font-text-ui-s);margin:0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTitleStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-title',\n                    }, styles: [\"[tuiTitle]{position:relative;display:flex;min-inline-size:0;flex-direction:column;text-align:start;gap:.25rem;margin:0;font:var(--tui-font-text-ui-m)}[tuiTitle][data-size=s]{gap:.125rem;font:var(--tui-font-text-s)}[tuiTitle][data-size=s] [tuiSubtitle]{font:var(--tui-font-text-xs)}[tuiTitle][data-size=m]{gap:.125rem;font:var(--tui-font-heading-5)}[tuiTitle][data-size=m] [tuiSubtitle]{font:var(--tui-font-text-m)}[tuiTitle][data-size=l]{gap:.5rem;font:var(--tui-font-heading-3)}[tuiTitle][data-size=l] [tuiSubtitle]{font:var(--tui-font-text-m)}[tuiTitle] h1,[tuiTitle] h2,[tuiTitle] h3,[tuiTitle] h4,[tuiTitle] h5,[tuiTitle] h6{margin:0;font:inherit}[tuiSubtitle]{font:var(--tui-font-text-ui-s);margin:0}\\n\"] }]\n        }] });\nclass TuiTitle {\n    constructor() {\n        this.nothing = tuiWithStyles(TuiTitleStyles);\n        this.size = '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiTitle, isStandalone: true, selector: \"[tuiTitle]\", inputs: { size: [\"tuiTitle\", \"size\"] }, host: { attributes: { \"tuiTitle\": \"\" }, properties: { \"attr.data-size\": \"size || null\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiTitle]',\n                    host: {\n                        tuiTitle: '',\n                        '[attr.data-size]': 'size || null',\n                    },\n                }]\n        }], propDecorators: { size: [{\n                type: Input,\n                args: ['tuiTitle']\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiTitle };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACvG,SAASC,aAAa,QAAQ,mCAAmC;AAEjE,MAAMC,cAAc,CAAC;EACjB;IAAS,IAAI,CAACC,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACI,IAAI,kBAD+EX,EAAE,CAAAY,iBAAA;MAAAC,IAAA,EACJN,cAAc;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADZjB,EAAE,CAAAkB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC67B;EAAE;AACtiC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG5B,EAAE,CAAA6B,iBAAA,CAGXtB,cAAc,EAAc,CAAC;IAC7GM,IAAI,EAAEZ,SAAS;IACf6B,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAExB,iBAAiB,CAAC6B,IAAI;MAAEJ,eAAe,EAAExB,uBAAuB,CAAC6B,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,qsBAAqsB;IAAE,CAAC;EAChuB,CAAC,CAAC;AAAA;AACV,MAAMU,QAAQ,CAAC;EACXC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG/B,aAAa,CAACC,cAAc,CAAC;IAC5C,IAAI,CAAC+B,IAAI,GAAG,EAAE;EAClB;EACA;IAAS,IAAI,CAAC9B,IAAI,YAAA+B,iBAAA7B,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,QAAQ;IAAA,CAAmD;EAAE;EACxK;IAAS,IAAI,CAACK,IAAI,kBAf+ExC,EAAE,CAAAyC,iBAAA;MAAA5B,IAAA,EAeJsB,QAAQ;MAAArB,SAAA;MAAAC,SAAA,eAAwH,EAAE;MAAA2B,QAAA;MAAAC,YAAA,WAAAC,sBAAArB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAfhIvB,EAAE,CAAA6C,WAAA,cAAArB,GAAA,CAAAc,IAAA,IAeI,IAAI;QAAA;MAAA;MAAAQ,MAAA;QAAAR,IAAA,GAfVtC,EAAE,CAAA+C,YAAA,CAAAhB,IAAA;MAAA;MAAAf,UAAA;IAAA,EAeqM;EAAE;AAC9S;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAjBqG5B,EAAE,CAAA6B,iBAAA,CAiBXM,QAAQ,EAAc,CAAC;IACvGtB,IAAI,EAAET,SAAS;IACf0B,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBgC,QAAQ,EAAE,YAAY;MACtBf,IAAI,EAAE;QACFgB,QAAQ,EAAE,EAAE;QACZ,kBAAkB,EAAE;MACxB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEX,IAAI,EAAE,CAAC;MACrBzB,IAAI,EAAER,KAAK;MACXyB,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASK,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}