{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, TemplateRef, Component, ChangeDetectionStrategy, ContentChildren, Input } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport * as i4 from '@taiga-ui/cdk/directives/item';\nimport { TuiItem } from '@taiga-ui/cdk/directives/item';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport * as i1 from '@taiga-ui/core/components/data-list';\nimport { TuiDataList } from '@taiga-ui/core/components/data-list';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { tuiLinkOptionsProvider } from '@taiga-ui/core/components/link';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { TuiDropdown } from '@taiga-ui/core/directives/dropdown';\nimport { tuiHintOptionsProvider } from '@taiga-ui/core/directives/hint';\nimport { TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport * as i3 from '@taiga-ui/kit/components/items-with-more';\nimport { TuiItemsWithMore } from '@taiga-ui/kit/components/items-with-more';\nimport { TUI_MORE_WORD } from '@taiga-ui/kit/tokens';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nfunction TuiBreadcrumbs_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiBreadcrumbs_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementContainer(1, 9)(2, 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const separator_r2 = i0.ɵɵreference(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.items.first);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", separator_r2);\n  }\n}\nfunction TuiBreadcrumbs_ng_container_2_ng_container_3_ng_container_1_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 9);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(5);\n    const separator_r2 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", separator_r2);\n  }\n}\nfunction TuiBreadcrumbs_ng_container_2_ng_container_3_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementContainer(1, 9);\n    i0.ɵɵtemplate(2, TuiBreadcrumbs_ng_container_2_ng_container_3_ng_container_1_ng_container_1_ng_container_2_Template, 1, 1, \"ng-container\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    const item_r4 = ctx_r2.$implicit;\n    const last_r5 = ctx_r2.last;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", item_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r5);\n  }\n}\nfunction TuiBreadcrumbs_ng_container_2_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiBreadcrumbs_ng_container_2_ng_container_3_ng_container_1_ng_container_1_Template, 3, 2, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction TuiBreadcrumbs_ng_container_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiBreadcrumbs_ng_container_2_ng_container_3_ng_container_1_Template, 2, 0, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r4 !== ctx_r0.items.first || ctx_r0.itemsLimit === 2);\n  }\n}\nfunction TuiBreadcrumbs_ng_container_2_ng_template_4_ng_template_4_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 16);\n    i0.ɵɵelementContainer(1, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", item_r6);\n  }\n}\nfunction TuiBreadcrumbs_ng_container_2_ng_template_4_ng_template_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiBreadcrumbs_ng_container_2_ng_template_4_ng_template_4_ng_container_1_span_1_Template, 2, 1, \"span\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const index_r8 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r7 + ctx_r0.offset && i_r7 <= index_r8 && item_r6 !== ctx_r0.items.last);\n  }\n}\nfunction TuiBreadcrumbs_ng_container_2_ng_template_4_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tui-data-list\", 14);\n    i0.ɵɵtemplate(1, TuiBreadcrumbs_ng_container_2_ng_template_4_ng_template_4_ng_container_1_Template, 2, 1, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.items);\n  }\n}\nfunction TuiBreadcrumbs_ng_container_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12)(1, \"button\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainer(3, 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TuiBreadcrumbs_ng_container_2_ng_template_4_ng_template_4_Template, 2, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const dropdown_r9 = i0.ɵɵreference(5);\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const separator_r2 = i0.ɵɵreference(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconStart\", ctx_r0.icons.ellipsis)(\"tuiDropdown\", dropdown_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.more(), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", separator_r2);\n  }\n}\nfunction TuiBreadcrumbs_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiBreadcrumbs_ng_container_2_ng_container_1_Template, 3, 2, \"ng-container\", 4);\n    i0.ɵɵelementStart(2, \"tui-items-with-more\", 6);\n    i0.ɵɵtemplate(3, TuiBreadcrumbs_ng_container_2_ng_container_3_Template, 2, 1, \"ng-container\", 7)(4, TuiBreadcrumbs_ng_container_2_ng_template_4_Template, 6, 4, \"ng-template\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.itemsLimit !== 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"itemsLimit\", ctx_r0.itemsLimit - 2)(\"required\", ctx_r0.items.length + ctx_r0.offset - 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.items);\n  }\n}\nfunction TuiBreadcrumbs_ng_template_3_tui_icon_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-icon\", 18);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"icon\", ctx_r0.options.icon);\n  }\n}\nfunction TuiBreadcrumbs_ng_template_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.options.icon);\n  }\n}\nfunction TuiBreadcrumbs_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiBreadcrumbs_ng_template_3_tui_icon_0_Template, 1, 1, \"tui-icon\", 17)(1, TuiBreadcrumbs_ng_template_3_ng_template_1_Template, 2, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const char_r10 = i0.ɵɵreference(2);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.options.icon.length > 1)(\"ngIfElse\", char_r10);\n  }\n}\nfunction TuiBreadcrumbs_ng_template_5_ng_container_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 9);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(3);\n    const separator_r2 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", separator_r2);\n  }\n}\nfunction TuiBreadcrumbs_ng_template_5_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementContainer(1, 9);\n    i0.ɵɵtemplate(2, TuiBreadcrumbs_ng_template_5_ng_container_0_ng_container_2_Template, 1, 1, \"ng-container\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const last_r12 = ctx.last;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", item_r11);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r12);\n  }\n}\nfunction TuiBreadcrumbs_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiBreadcrumbs_ng_template_5_ng_container_0_Template, 3, 2, \"ng-container\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.items);\n  }\n}\nconst TUI_BREADCRUMBS_DEFAULT_OPTIONS = {\n  icon: '@tui.chevron-right',\n  size: 'm',\n  itemsLimit: 0\n};\nconst TUI_BREADCRUMBS_OPTIONS = tuiCreateToken(TUI_BREADCRUMBS_DEFAULT_OPTIONS);\nfunction tuiBreadcrumbsOptionsProvider(options) {\n  return tuiProvideOptions(TUI_BREADCRUMBS_OPTIONS, options, TUI_BREADCRUMBS_DEFAULT_OPTIONS);\n}\nclass TuiBreadcrumbs {\n  constructor() {\n    this.items = EMPTY_QUERY;\n    this.options = inject(TUI_BREADCRUMBS_OPTIONS);\n    this.icons = inject(TUI_COMMON_ICONS);\n    this.more = toSignal(inject(TUI_MORE_WORD), {\n      initialValue: ''\n    });\n    this.size = this.options.size;\n    this.itemsLimit = this.options.itemsLimit;\n  }\n  get limit() {\n    return this.itemsLimit ? this.itemsLimit - 2 : Infinity;\n  }\n  get offset() {\n    return this.itemsLimit === 2 ? 1 : 0;\n  }\n  static {\n    this.ɵfac = function TuiBreadcrumbs_Factory(t) {\n      return new (t || TuiBreadcrumbs)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiBreadcrumbs,\n      selectors: [[\"tui-breadcrumbs\"]],\n      contentQueries: function TuiBreadcrumbs_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiItem, 4, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      hostVars: 1,\n      hostBindings: function TuiBreadcrumbs_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n        }\n      },\n      inputs: {\n        size: \"size\",\n        itemsLimit: \"itemsLimit\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiLinkOptionsProvider({\n        appearance: 'action-grayscale'\n      }), tuiHintOptionsProvider({\n        direction: 'bottom'\n      })]), i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 5,\n      consts: [[\"separator\", \"\"], [\"plain\", \"\"], [\"dropdown\", \"\"], [\"char\", \"\"], [4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"side\", \"start\", 3, \"itemsLimit\", \"required\"], [4, \"ngFor\", \"ngForOf\"], [\"tuiMore\", \"\"], [3, \"ngTemplateOutlet\"], [4, \"tuiItem\"], [3, \"ngTemplateOutlet\", 4, \"ngIf\"], [1, \"t-more\"], [\"appearance\", \"flat\", \"size\", \"xs\", \"tuiDropdownOpen\", \"\", \"tuiIconButton\", \"\", \"type\", \"button\", 3, \"iconStart\", \"tuiDropdown\"], [\"size\", \"s\"], [\"tuiOption\", \"\", \"class\", \"t-option\", 4, \"ngIf\"], [\"tuiOption\", \"\", 1, \"t-option\"], [\"class\", \"t-icon\", 3, \"icon\", 4, \"ngIf\", \"ngIfElse\"], [1, \"t-icon\", 3, \"icon\"], [1, \"t-char\"]],\n      template: function TuiBreadcrumbs_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiBreadcrumbs_ng_container_0_Template, 1, 0, \"ng-container\", 4);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵtemplate(2, TuiBreadcrumbs_ng_container_2_Template, 5, 4, \"ng-container\", 5)(3, TuiBreadcrumbs_ng_template_3_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(5, TuiBreadcrumbs_ng_template_5_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const plain_r13 = i0.ɵɵreference(6);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 3, ctx.items.changes));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.itemsLimit > 1)(\"ngIfElse\", plain_r13);\n        }\n      },\n      dependencies: [AsyncPipe, NgForOf, NgIf, NgTemplateOutlet, TuiButton, i1.TuiDataListComponent, i2.TuiDropdownDirective, i2.TuiDropdownOpen, TuiIcon, i3.TuiItemsWithMoreComponent, i3.TuiMore, i4.TuiItem],\n      styles: [\"[_nghost-%COMP%]{display:flex;align-items:center;white-space:nowrap;color:var(--tui-text-secondary)}[data-size=m][_nghost-%COMP%]{font:var(--tui-font-text-s);line-height:1.5rem;block-size:1.5rem}[data-size=l][_nghost-%COMP%]{font:var(--tui-font-text-m);line-height:2.5rem;block-size:2.5rem}[_nghost-%COMP%]     [tuiLink]{text-decoration:none}.t-more[_ngcontent-%COMP%]{display:flex;align-items:center}.t-option[_ngcontent-%COMP%]    >*{color:var(--tui-text-primary)!important;background:transparent!important;text-decoration:none}.t-icon[_ngcontent-%COMP%]{margin:0 .5rem;font-size:1rem}.t-char[_ngcontent-%COMP%]{margin:0 .375rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiBreadcrumbs, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-breadcrumbs',\n      imports: [AsyncPipe, NgForOf, NgIf, NgTemplateOutlet, TuiButton, TuiDataList, TuiDropdown, TuiIcon, TuiItemsWithMore],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiLinkOptionsProvider({\n        appearance: 'action-grayscale'\n      }), tuiHintOptionsProvider({\n        direction: 'bottom'\n      })],\n      host: {\n        '[attr.data-size]': 'size'\n      },\n      template: \"<ng-container *ngIf=\\\"items.changes | async\\\" />\\n<ng-container *ngIf=\\\"itemsLimit > 1; else plain\\\">\\n    <ng-container *ngIf=\\\"itemsLimit !== 2\\\">\\n        <ng-container [ngTemplateOutlet]=\\\"items.first\\\" />\\n        <ng-container [ngTemplateOutlet]=\\\"separator\\\" />\\n    </ng-container>\\n    <tui-items-with-more\\n        side=\\\"start\\\"\\n        [itemsLimit]=\\\"itemsLimit - 2\\\"\\n        [required]=\\\"items.length + offset - 2\\\"\\n    >\\n        <ng-container *ngFor=\\\"let item of items; let last = last\\\">\\n            <ng-container *ngIf=\\\"item !== items.first || itemsLimit === 2\\\">\\n                <ng-container *tuiItem>\\n                    <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n                    <ng-container\\n                        *ngIf=\\\"!last\\\"\\n                        [ngTemplateOutlet]=\\\"separator\\\"\\n                    />\\n                </ng-container>\\n            </ng-container>\\n        </ng-container>\\n        <ng-template\\n            let-index\\n            tuiMore\\n        >\\n            <span class=\\\"t-more\\\">\\n                <button\\n                    appearance=\\\"flat\\\"\\n                    size=\\\"xs\\\"\\n                    tuiDropdownOpen\\n                    tuiIconButton\\n                    type=\\\"button\\\"\\n                    [iconStart]=\\\"icons.ellipsis\\\"\\n                    [tuiDropdown]=\\\"dropdown\\\"\\n                >\\n                    {{ more() }}\\n                </button>\\n                <ng-container [ngTemplateOutlet]=\\\"separator\\\" />\\n            </span>\\n            <ng-template #dropdown>\\n                <tui-data-list size=\\\"s\\\">\\n                    <ng-container *ngFor=\\\"let item of items; let i = index\\\">\\n                        <span\\n                            *ngIf=\\\"i + offset && i <= index && item !== items.last\\\"\\n                            tuiOption\\n                            class=\\\"t-option\\\"\\n                        >\\n                            <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n                        </span>\\n                    </ng-container>\\n                </tui-data-list>\\n            </ng-template>\\n        </ng-template>\\n    </tui-items-with-more>\\n</ng-container>\\n<ng-template #separator>\\n    <tui-icon\\n        *ngIf=\\\"options.icon.length > 1; else char\\\"\\n        class=\\\"t-icon\\\"\\n        [icon]=\\\"options.icon\\\"\\n    />\\n    <ng-template #char>\\n        <span class=\\\"t-char\\\">{{ options.icon }}</span>\\n    </ng-template>\\n</ng-template>\\n<ng-template #plain>\\n    <ng-container *ngFor=\\\"let item of items; let last = last\\\">\\n        <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n        <ng-container\\n            *ngIf=\\\"!last\\\"\\n            [ngTemplateOutlet]=\\\"separator\\\"\\n        />\\n    </ng-container>\\n</ng-template>\\n\",\n      styles: [\":host{display:flex;align-items:center;white-space:nowrap;color:var(--tui-text-secondary)}:host[data-size=m]{font:var(--tui-font-text-s);line-height:1.5rem;block-size:1.5rem}:host[data-size=l]{font:var(--tui-font-text-m);line-height:2.5rem;block-size:2.5rem}:host ::ng-deep [tuiLink]{text-decoration:none}.t-more{display:flex;align-items:center}.t-option ::ng-deep>*{color:var(--tui-text-primary)!important;background:transparent!important;text-decoration:none}.t-icon{margin:0 .5rem;font-size:1rem}.t-char{margin:0 .375rem}\\n\"]\n    }]\n  }], null, {\n    items: [{\n      type: ContentChildren,\n      args: [TuiItem, {\n        read: TemplateRef\n      }]\n    }],\n    size: [{\n      type: Input\n    }],\n    itemsLimit: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_BREADCRUMBS_DEFAULT_OPTIONS, TUI_BREADCRUMBS_OPTIONS, TuiBreadcrumbs, tuiBreadcrumbsOptionsProvider };", "map": {"version": 3, "names": ["AsyncPipe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "i0", "inject", "TemplateRef", "Component", "ChangeDetectionStrategy", "ContentChildren", "Input", "toSignal", "EMPTY_QUERY", "i4", "TuiItem", "TuiButton", "i1", "TuiDataList", "TuiIcon", "tuiLinkOptionsProvider", "i2", "TuiDropdown", "tuiHintOptionsProvider", "TUI_COMMON_ICONS", "i3", "TuiItemsWithMore", "TUI_MORE_WORD", "tuiCreateToken", "tuiProvideOptions", "TuiBreadcrumbs_ng_container_0_Template", "rf", "ctx", "ɵɵelementContainer", "TuiBreadcrumbs_ng_container_2_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ctx_r0", "ɵɵnextContext", "separator_r2", "ɵɵreference", "ɵɵadvance", "ɵɵproperty", "items", "first", "TuiBreadcrumbs_ng_container_2_ng_container_3_ng_container_1_ng_container_1_ng_container_2_Template", "TuiBreadcrumbs_ng_container_2_ng_container_3_ng_container_1_ng_container_1_Template", "ɵɵtemplate", "ctx_r2", "item_r4", "$implicit", "last_r5", "last", "TuiBreadcrumbs_ng_container_2_ng_container_3_ng_container_1_Template", "TuiBreadcrumbs_ng_container_2_ng_container_3_Template", "itemsLimit", "TuiBreadcrumbs_ng_container_2_ng_template_4_ng_template_4_ng_container_1_span_1_Template", "ɵɵelementStart", "ɵɵelementEnd", "item_r6", "TuiBreadcrumbs_ng_container_2_ng_template_4_ng_template_4_ng_container_1_Template", "i_r7", "index", "index_r8", "offset", "TuiBreadcrumbs_ng_container_2_ng_template_4_ng_template_4_Template", "TuiBreadcrumbs_ng_container_2_ng_template_4_Template", "ɵɵtext", "ɵɵtemplateRefExtractor", "dropdown_r9", "icons", "ellipsis", "ɵɵtextInterpolate1", "more", "TuiBreadcrumbs_ng_container_2_Template", "length", "TuiBreadcrumbs_ng_template_3_tui_icon_0_Template", "ɵɵelement", "options", "icon", "TuiBreadcrumbs_ng_template_3_ng_template_1_Template", "ɵɵtextInterpolate", "TuiBreadcrumbs_ng_template_3_Template", "char_r10", "TuiBreadcrumbs_ng_template_5_ng_container_0_ng_container_2_Template", "TuiBreadcrumbs_ng_template_5_ng_container_0_Template", "item_r11", "last_r12", "TuiBreadcrumbs_ng_template_5_Template", "TUI_BREADCRUMBS_DEFAULT_OPTIONS", "size", "TUI_BREADCRUMBS_OPTIONS", "tuiBreadcrumbsOptionsProvider", "TuiBreadcrumbs", "constructor", "initialValue", "limit", "Infinity", "ɵfac", "TuiBreadcrumbs_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "TuiBreadcrumbs_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostVars", "hostBindings", "TuiBreadcrumbs_HostBindings", "ɵɵattribute", "inputs", "standalone", "features", "ɵɵProvidersFeature", "appearance", "direction", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiBreadcrumbs_Template", "ɵɵpipe", "plain_r13", "ɵɵpipeBind1", "changes", "dependencies", "TuiDataListComponent", "TuiDropdownDirective", "TuiDropdownOpen", "TuiItemsWithMoreComponent", "TuiMore", "styles", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "host", "read"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-breadcrumbs.mjs"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, TemplateRef, Component, ChangeDetectionStrategy, ContentChildren, Input } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport * as i4 from '@taiga-ui/cdk/directives/item';\nimport { TuiItem } from '@taiga-ui/cdk/directives/item';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport * as i1 from '@taiga-ui/core/components/data-list';\nimport { TuiDataList } from '@taiga-ui/core/components/data-list';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { tuiLinkOptionsProvider } from '@taiga-ui/core/components/link';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { TuiDropdown } from '@taiga-ui/core/directives/dropdown';\nimport { tuiHintOptionsProvider } from '@taiga-ui/core/directives/hint';\nimport { TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport * as i3 from '@taiga-ui/kit/components/items-with-more';\nimport { TuiItemsWithMore } from '@taiga-ui/kit/components/items-with-more';\nimport { TUI_MORE_WORD } from '@taiga-ui/kit/tokens';\nimport { tuiCreateToken, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\n\nconst TUI_BREADCRUMBS_DEFAULT_OPTIONS = {\n    icon: '@tui.chevron-right',\n    size: 'm',\n    itemsLimit: 0,\n};\nconst TUI_BREADCRUMBS_OPTIONS = tuiCreateToken(TUI_BREADCRUMBS_DEFAULT_OPTIONS);\nfunction tuiBreadcrumbsOptionsProvider(options) {\n    return tuiProvideOptions(TUI_BREADCRUMBS_OPTIONS, options, TUI_BREADCRUMBS_DEFAULT_OPTIONS);\n}\n\nclass TuiBreadcrumbs {\n    constructor() {\n        this.items = EMPTY_QUERY;\n        this.options = inject(TUI_BREADCRUMBS_OPTIONS);\n        this.icons = inject(TUI_COMMON_ICONS);\n        this.more = toSignal(inject(TUI_MORE_WORD), { initialValue: '' });\n        this.size = this.options.size;\n        this.itemsLimit = this.options.itemsLimit;\n    }\n    get limit() {\n        return this.itemsLimit ? this.itemsLimit - 2 : Infinity;\n    }\n    get offset() {\n        return this.itemsLimit === 2 ? 1 : 0;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBreadcrumbs, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiBreadcrumbs, isStandalone: true, selector: \"tui-breadcrumbs\", inputs: { size: \"size\", itemsLimit: \"itemsLimit\" }, host: { properties: { \"attr.data-size\": \"size\" } }, providers: [\n            tuiLinkOptionsProvider({ appearance: 'action-grayscale' }),\n            tuiHintOptionsProvider({ direction: 'bottom' }),\n        ], queries: [{ propertyName: \"items\", predicate: TuiItem, read: TemplateRef }], ngImport: i0, template: \"<ng-container *ngIf=\\\"items.changes | async\\\" />\\n<ng-container *ngIf=\\\"itemsLimit > 1; else plain\\\">\\n    <ng-container *ngIf=\\\"itemsLimit !== 2\\\">\\n        <ng-container [ngTemplateOutlet]=\\\"items.first\\\" />\\n        <ng-container [ngTemplateOutlet]=\\\"separator\\\" />\\n    </ng-container>\\n    <tui-items-with-more\\n        side=\\\"start\\\"\\n        [itemsLimit]=\\\"itemsLimit - 2\\\"\\n        [required]=\\\"items.length + offset - 2\\\"\\n    >\\n        <ng-container *ngFor=\\\"let item of items; let last = last\\\">\\n            <ng-container *ngIf=\\\"item !== items.first || itemsLimit === 2\\\">\\n                <ng-container *tuiItem>\\n                    <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n                    <ng-container\\n                        *ngIf=\\\"!last\\\"\\n                        [ngTemplateOutlet]=\\\"separator\\\"\\n                    />\\n                </ng-container>\\n            </ng-container>\\n        </ng-container>\\n        <ng-template\\n            let-index\\n            tuiMore\\n        >\\n            <span class=\\\"t-more\\\">\\n                <button\\n                    appearance=\\\"flat\\\"\\n                    size=\\\"xs\\\"\\n                    tuiDropdownOpen\\n                    tuiIconButton\\n                    type=\\\"button\\\"\\n                    [iconStart]=\\\"icons.ellipsis\\\"\\n                    [tuiDropdown]=\\\"dropdown\\\"\\n                >\\n                    {{ more() }}\\n                </button>\\n                <ng-container [ngTemplateOutlet]=\\\"separator\\\" />\\n            </span>\\n            <ng-template #dropdown>\\n                <tui-data-list size=\\\"s\\\">\\n                    <ng-container *ngFor=\\\"let item of items; let i = index\\\">\\n                        <span\\n                            *ngIf=\\\"i + offset && i <= index && item !== items.last\\\"\\n                            tuiOption\\n                            class=\\\"t-option\\\"\\n                        >\\n                            <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n                        </span>\\n                    </ng-container>\\n                </tui-data-list>\\n            </ng-template>\\n        </ng-template>\\n    </tui-items-with-more>\\n</ng-container>\\n<ng-template #separator>\\n    <tui-icon\\n        *ngIf=\\\"options.icon.length > 1; else char\\\"\\n        class=\\\"t-icon\\\"\\n        [icon]=\\\"options.icon\\\"\\n    />\\n    <ng-template #char>\\n        <span class=\\\"t-char\\\">{{ options.icon }}</span>\\n    </ng-template>\\n</ng-template>\\n<ng-template #plain>\\n    <ng-container *ngFor=\\\"let item of items; let last = last\\\">\\n        <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n        <ng-container\\n            *ngIf=\\\"!last\\\"\\n            [ngTemplateOutlet]=\\\"separator\\\"\\n        />\\n    </ng-container>\\n</ng-template>\\n\", styles: [\":host{display:flex;align-items:center;white-space:nowrap;color:var(--tui-text-secondary)}:host[data-size=m]{font:var(--tui-font-text-s);line-height:1.5rem;block-size:1.5rem}:host[data-size=l]{font:var(--tui-font-text-m);line-height:2.5rem;block-size:2.5rem}:host ::ng-deep [tuiLink]{text-decoration:none}.t-more{display:flex;align-items:center}.t-option ::ng-deep>*{color:var(--tui-text-primary)!important;background:transparent!important;text-decoration:none}.t-icon{margin:0 .5rem;font-size:1rem}.t-char{margin:0 .375rem}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"component\", type: i1.TuiDataListComponent, selector: \"tui-data-list\", inputs: [\"emptyContent\", \"size\"] }, { kind: \"directive\", type: i2.TuiDropdownDirective, selector: \"[tuiDropdown]:not(ng-container):not(ng-template)\", inputs: [\"tuiDropdown\"], exportAs: [\"tuiDropdown\"] }, { kind: \"directive\", type: i2.TuiDropdownOpen, selector: \"[tuiDropdown][tuiDropdownOpen],[tuiDropdown][tuiDropdownOpenChange]\", inputs: [\"tuiDropdownEnabled\", \"tuiDropdownOpen\"], outputs: [\"tuiDropdownOpenChange\"] }, { kind: \"component\", type: TuiIcon, selector: \"tui-icon\", inputs: [\"icon\", \"background\"] }, { kind: \"component\", type: i3.TuiItemsWithMoreComponent, selector: \"tui-items-with-more\", outputs: [\"lastIndexChange\"] }, { kind: \"directive\", type: i3.TuiMore, selector: \"[tuiMore]\" }, { kind: \"directive\", type: i4.TuiItem, selector: \"[tuiItem]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBreadcrumbs, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-breadcrumbs', imports: [\n                        AsyncPipe,\n                        NgForOf,\n                        NgIf,\n                        NgTemplateOutlet,\n                        TuiButton,\n                        TuiDataList,\n                        TuiDropdown,\n                        TuiIcon,\n                        TuiItemsWithMore,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        tuiLinkOptionsProvider({ appearance: 'action-grayscale' }),\n                        tuiHintOptionsProvider({ direction: 'bottom' }),\n                    ], host: {\n                        '[attr.data-size]': 'size',\n                    }, template: \"<ng-container *ngIf=\\\"items.changes | async\\\" />\\n<ng-container *ngIf=\\\"itemsLimit > 1; else plain\\\">\\n    <ng-container *ngIf=\\\"itemsLimit !== 2\\\">\\n        <ng-container [ngTemplateOutlet]=\\\"items.first\\\" />\\n        <ng-container [ngTemplateOutlet]=\\\"separator\\\" />\\n    </ng-container>\\n    <tui-items-with-more\\n        side=\\\"start\\\"\\n        [itemsLimit]=\\\"itemsLimit - 2\\\"\\n        [required]=\\\"items.length + offset - 2\\\"\\n    >\\n        <ng-container *ngFor=\\\"let item of items; let last = last\\\">\\n            <ng-container *ngIf=\\\"item !== items.first || itemsLimit === 2\\\">\\n                <ng-container *tuiItem>\\n                    <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n                    <ng-container\\n                        *ngIf=\\\"!last\\\"\\n                        [ngTemplateOutlet]=\\\"separator\\\"\\n                    />\\n                </ng-container>\\n            </ng-container>\\n        </ng-container>\\n        <ng-template\\n            let-index\\n            tuiMore\\n        >\\n            <span class=\\\"t-more\\\">\\n                <button\\n                    appearance=\\\"flat\\\"\\n                    size=\\\"xs\\\"\\n                    tuiDropdownOpen\\n                    tuiIconButton\\n                    type=\\\"button\\\"\\n                    [iconStart]=\\\"icons.ellipsis\\\"\\n                    [tuiDropdown]=\\\"dropdown\\\"\\n                >\\n                    {{ more() }}\\n                </button>\\n                <ng-container [ngTemplateOutlet]=\\\"separator\\\" />\\n            </span>\\n            <ng-template #dropdown>\\n                <tui-data-list size=\\\"s\\\">\\n                    <ng-container *ngFor=\\\"let item of items; let i = index\\\">\\n                        <span\\n                            *ngIf=\\\"i + offset && i <= index && item !== items.last\\\"\\n                            tuiOption\\n                            class=\\\"t-option\\\"\\n                        >\\n                            <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n                        </span>\\n                    </ng-container>\\n                </tui-data-list>\\n            </ng-template>\\n        </ng-template>\\n    </tui-items-with-more>\\n</ng-container>\\n<ng-template #separator>\\n    <tui-icon\\n        *ngIf=\\\"options.icon.length > 1; else char\\\"\\n        class=\\\"t-icon\\\"\\n        [icon]=\\\"options.icon\\\"\\n    />\\n    <ng-template #char>\\n        <span class=\\\"t-char\\\">{{ options.icon }}</span>\\n    </ng-template>\\n</ng-template>\\n<ng-template #plain>\\n    <ng-container *ngFor=\\\"let item of items; let last = last\\\">\\n        <ng-container [ngTemplateOutlet]=\\\"item\\\" />\\n        <ng-container\\n            *ngIf=\\\"!last\\\"\\n            [ngTemplateOutlet]=\\\"separator\\\"\\n        />\\n    </ng-container>\\n</ng-template>\\n\", styles: [\":host{display:flex;align-items:center;white-space:nowrap;color:var(--tui-text-secondary)}:host[data-size=m]{font:var(--tui-font-text-s);line-height:1.5rem;block-size:1.5rem}:host[data-size=l]{font:var(--tui-font-text-m);line-height:2.5rem;block-size:2.5rem}:host ::ng-deep [tuiLink]{text-decoration:none}.t-more{display:flex;align-items:center}.t-option ::ng-deep>*{color:var(--tui-text-primary)!important;background:transparent!important;text-decoration:none}.t-icon{margin:0 .5rem;font-size:1rem}.t-char{margin:0 .375rem}\\n\"] }]\n        }], propDecorators: { items: [{\n                type: ContentChildren,\n                args: [TuiItem, { read: TemplateRef }]\n            }], size: [{\n                type: Input\n            }], itemsLimit: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_BREADCRUMBS_DEFAULT_OPTIONS, TUI_BREADCRUMBS_OPTIONS, TuiBreadcrumbs, tuiBreadcrumbsOptionsProvider };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,OAAO,EAAEC,IAAI,EAAEC,gBAAgB,QAAQ,iBAAiB;AAC5E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,KAAK,QAAQ,eAAe;AAC/G,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,KAAKC,EAAE,MAAM,+BAA+B;AACnD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,WAAW,QAAQ,qCAAqC;AACjE,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,OAAO,KAAKC,EAAE,MAAM,oCAAoC;AACxD,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,OAAO,KAAKC,EAAE,MAAM,0CAA0C;AAC9D,SAASC,gBAAgB,QAAQ,0CAA0C;AAC3E,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,mCAAmC;AAAC,SAAAC,uCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA2Be1B,EAAE,CAAA4B,kBAAA,EAIyD,CAAC;EAAA;AAAA;AAAA,SAAAC,sDAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJ5D1B,EAAE,CAAA8B,uBAAA,EAI6J,CAAC;IAJhK9B,EAAE,CAAA4B,kBAAA,KAI0N,CAAC,KAA0D,CAAC;IAJxR5B,EAAE,CAAA+B,qBAAA;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;IAAA,MAAAC,YAAA,GAAFlC,EAAE,CAAAmC,WAAA;IAAFnC,EAAE,CAAAoC,SAAA,CAIuN,CAAC;IAJ1NpC,EAAE,CAAAqC,UAAA,qBAAAL,MAAA,CAAAM,KAAA,CAAAC,KAIuN,CAAC;IAJ1NvC,EAAE,CAAAoC,SAAA,CAIkR,CAAC;IAJrRpC,EAAE,CAAAqC,UAAA,qBAAAH,YAIkR,CAAC;EAAA;AAAA;AAAA,SAAAM,mGAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJrR1B,EAAE,CAAA4B,kBAAA,KAI41B,CAAC;EAAA;EAAA,IAAAF,EAAA;IAJ/1B1B,EAAE,CAAAiC,aAAA;IAAA,MAAAC,YAAA,GAAFlC,EAAE,CAAAmC,WAAA;IAAFnC,EAAE,CAAAqC,UAAA,qBAAAH,YAIo0B,CAAC;EAAA;AAAA;AAAA,SAAAO,oFAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJv0B1B,EAAE,CAAA8B,uBAAA,EAI4nB,CAAC;IAJ/nB9B,EAAE,CAAA4B,kBAAA,KAI8rB,CAAC;IAJjsB5B,EAAE,CAAA0C,UAAA,IAAAF,kGAAA,0BAI41B,CAAC;IAJ/1BxC,EAAE,CAAA+B,qBAAA;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAiB,MAAA,GAAF3C,EAAE,CAAAiC,aAAA;IAAA,MAAAW,OAAA,GAAAD,MAAA,CAAAE,SAAA;IAAA,MAAAC,OAAA,GAAAH,MAAA,CAAAI,IAAA;IAAF/C,EAAE,CAAAoC,SAAA,CAI2rB,CAAC;IAJ9rBpC,EAAE,CAAAqC,UAAA,qBAAAO,OAI2rB,CAAC;IAJ9rB5C,EAAE,CAAAoC,SAAA,CAIwwB,CAAC;IAJ3wBpC,EAAE,CAAAqC,UAAA,UAAAS,OAIwwB,CAAC;EAAA;AAAA;AAAA,SAAAE,qEAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJ3wB1B,EAAE,CAAA8B,uBAAA,EAImlB,CAAC;IAJtlB9B,EAAE,CAAA0C,UAAA,IAAAD,mFAAA,0BAI4nB,CAAC;IAJ/nBzC,EAAE,CAAA+B,qBAAA;EAAA;AAAA;AAAA,SAAAkB,sDAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF1B,EAAE,CAAA8B,uBAAA,EAIogB,CAAC;IAJvgB9B,EAAE,CAAA0C,UAAA,IAAAM,oEAAA,yBAImlB,CAAC;IAJtlBhD,EAAE,CAAA+B,qBAAA;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAkB,OAAA,GAAAjB,GAAA,CAAAkB,SAAA;IAAA,MAAAb,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAoC,SAAA,CAIglB,CAAC;IAJnlBpC,EAAE,CAAAqC,UAAA,SAAAO,OAAA,KAAAZ,MAAA,CAAAM,KAAA,CAAAC,KAAA,IAAAP,MAAA,CAAAkB,UAAA,MAIglB,CAAC;EAAA;AAAA;AAAA,SAAAC,yFAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJnlB1B,EAAE,CAAAoD,cAAA,cAI04D,CAAC;IAJ74DpD,EAAE,CAAA4B,kBAAA,KAIo9D,CAAC;IAJv9D5B,EAAE,CAAAqD,YAAA,CAIq/D,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAA4B,OAAA,GAJx/DtD,EAAE,CAAAiC,aAAA,GAAAY,SAAA;IAAF7C,EAAE,CAAAoC,SAAA,CAIi9D,CAAC;IAJp9DpC,EAAE,CAAAqC,UAAA,qBAAAiB,OAIi9D,CAAC;EAAA;AAAA;AAAA,SAAAC,kFAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJp9D1B,EAAE,CAAA8B,uBAAA,EAIkqD,CAAC;IAJrqD9B,EAAE,CAAA0C,UAAA,IAAAS,wFAAA,kBAI04D,CAAC;IAJ74DnD,EAAE,CAAA+B,qBAAA;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAA4B,OAAA,GAAA3B,GAAA,CAAAkB,SAAA;IAAA,MAAAW,IAAA,GAAA7B,GAAA,CAAA8B,KAAA;IAAA,MAAAC,QAAA,GAAF1D,EAAE,CAAAiC,aAAA,IAAAY,SAAA;IAAA,MAAAb,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAoC,SAAA,CAIsxD,CAAC;IAJzxDpC,EAAE,CAAAqC,UAAA,SAAAmB,IAAA,GAAAxB,MAAA,CAAA2B,MAAA,IAAAH,IAAA,IAAAE,QAAA,IAAAJ,OAAA,KAAAtB,MAAA,CAAAM,KAAA,CAAAS,IAIsxD,CAAC;EAAA;AAAA;AAAA,SAAAa,mEAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJzxD1B,EAAE,CAAAoD,cAAA,uBAIklD,CAAC;IAJrlDpD,EAAE,CAAA0C,UAAA,IAAAa,iFAAA,yBAIkqD,CAAC;IAJrqDvD,EAAE,CAAAqD,YAAA,CAI4jE,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAM,MAAA,GAJ/jEhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAoC,SAAA,CAIkpD,CAAC;IAJrpDpC,EAAE,CAAAqC,UAAA,YAAAL,MAAA,CAAAM,KAIkpD,CAAC;EAAA;AAAA;AAAA,SAAAuB,qDAAAnC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJrpD1B,EAAE,CAAAoD,cAAA,cAIqiC,CAAC,gBAAsU,CAAC;IAJ/2CpD,EAAE,CAAA8D,MAAA,EAIg6C,CAAC;IAJn6C9D,EAAE,CAAAqD,YAAA,CAIy6C,CAAC;IAJ56CrD,EAAE,CAAA4B,kBAAA,KAI4+C,CAAC;IAJ/+C5B,EAAE,CAAAqD,YAAA,CAIigD,CAAC;IAJpgDrD,EAAE,CAAA0C,UAAA,IAAAkB,kEAAA,gCAAF5D,EAAE,CAAA+D,sBAIsiD,CAAC;EAAA;EAAA,IAAArC,EAAA;IAAA,MAAAsC,WAAA,GAJziDhE,EAAE,CAAAmC,WAAA;IAAA,MAAAH,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;IAAA,MAAAC,YAAA,GAAFlC,EAAE,CAAAmC,WAAA;IAAFnC,EAAE,CAAAoC,SAAA,CAIyyC,CAAC;IAJ5yCpC,EAAE,CAAAqC,UAAA,cAAAL,MAAA,CAAAiC,KAAA,CAAAC,QAIyyC,CAAC,gBAAAF,WAA+C,CAAC;IAJ51ChE,EAAE,CAAAoC,SAAA,CAIg6C,CAAC;IAJn6CpC,EAAE,CAAAmE,kBAAA,MAAAnC,MAAA,CAAAoC,IAAA,OAIg6C,CAAC;IAJn6CpE,EAAE,CAAAoC,SAAA,CAIy+C,CAAC;IAJ5+CpC,EAAE,CAAAqC,UAAA,qBAAAH,YAIy+C,CAAC;EAAA;AAAA;AAAA,SAAAmC,uCAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJ5+C1B,EAAE,CAAA8B,uBAAA,EAI8G,CAAC;IAJjH9B,EAAE,CAAA0C,UAAA,IAAAb,qDAAA,yBAI6J,CAAC;IAJhK7B,EAAE,CAAAoD,cAAA,4BAI8b,CAAC;IAJjcpD,EAAE,CAAA0C,UAAA,IAAAO,qDAAA,yBAIogB,CAAC,IAAAY,oDAAA,wBAA2f,CAAC;IAJngC7D,EAAE,CAAAqD,YAAA,CAI4oE,CAAC;IAJ/oErD,EAAE,CAAA+B,qBAAA;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAoC,SAAA,CAI0J,CAAC;IAJ7JpC,EAAE,CAAAqC,UAAA,SAAAL,MAAA,CAAAkB,UAAA,MAI0J,CAAC;IAJ7JlD,EAAE,CAAAoC,SAAA,CAIqY,CAAC;IAJxYpC,EAAE,CAAAqC,UAAA,eAAAL,MAAA,CAAAkB,UAAA,IAIqY,CAAC,aAAAlB,MAAA,CAAAM,KAAA,CAAAgC,MAAA,GAAAtC,MAAA,CAAA2B,MAAA,IAAiD,CAAC;IAJ1b3D,EAAE,CAAAoC,SAAA,CAIkf,CAAC;IAJrfpC,EAAE,CAAAqC,UAAA,YAAAL,MAAA,CAAAM,KAIkf,CAAC;EAAA;AAAA;AAAA,SAAAiC,iDAAA7C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJrf1B,EAAE,CAAAwE,SAAA,kBAI+zE,CAAC;EAAA;EAAA,IAAA9C,EAAA;IAAA,MAAAM,MAAA,GAJl0EhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAqC,UAAA,SAAAL,MAAA,CAAAyC,OAAA,CAAAC,IAIuzE,CAAC;EAAA;AAAA;AAAA,SAAAC,oDAAAjD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJ1zE1B,EAAE,CAAAoD,cAAA,cAIy3E,CAAC;IAJ53EpD,EAAE,CAAA8D,MAAA,EAI24E,CAAC;IAJ94E9D,EAAE,CAAAqD,YAAA,CAIk5E,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAM,MAAA,GAJr5EhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAoC,SAAA,CAI24E,CAAC;IAJ94EpC,EAAE,CAAA4E,iBAAA,CAAA5C,MAAA,CAAAyC,OAAA,CAAAC,IAI24E,CAAC;EAAA;AAAA;AAAA,SAAAG,sCAAAnD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJ94E1B,EAAE,CAAA0C,UAAA,IAAA6B,gDAAA,sBAI+zE,CAAC,IAAAI,mDAAA,gCAJl0E3E,EAAE,CAAA+D,sBAIw1E,CAAC;EAAA;EAAA,IAAArC,EAAA;IAAA,MAAAoD,QAAA,GAJ31E9E,EAAE,CAAAmC,WAAA;IAAA,MAAAH,MAAA,GAAFhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAqC,UAAA,SAAAL,MAAA,CAAAyC,OAAA,CAAAC,IAAA,CAAAJ,MAAA,IAIivE,CAAC,aAAAQ,QAAQ,CAAC;EAAA;AAAA;AAAA,SAAAC,oEAAArD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJ7vE1B,EAAE,CAAA4B,kBAAA,KAIkrF,CAAC;EAAA;EAAA,IAAAF,EAAA;IAJrrF1B,EAAE,CAAAiC,aAAA;IAAA,MAAAC,YAAA,GAAFlC,EAAE,CAAAmC,WAAA;IAAFnC,EAAE,CAAAqC,UAAA,qBAAAH,YAIsqF,CAAC;EAAA;AAAA;AAAA,SAAA8C,qDAAAtD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJzqF1B,EAAE,CAAA8B,uBAAA,EAI8gF,CAAC;IAJjhF9B,EAAE,CAAA4B,kBAAA,KAIokF,CAAC;IAJvkF5B,EAAE,CAAA0C,UAAA,IAAAqC,mEAAA,0BAIkrF,CAAC;IAJrrF/E,EAAE,CAAA+B,qBAAA;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAuD,QAAA,GAAAtD,GAAA,CAAAkB,SAAA;IAAA,MAAAqC,QAAA,GAAAvD,GAAA,CAAAoB,IAAA;IAAF/C,EAAE,CAAAoC,SAAA,CAIikF,CAAC;IAJpkFpC,EAAE,CAAAqC,UAAA,qBAAA4C,QAIikF,CAAC;IAJpkFjF,EAAE,CAAAoC,SAAA,CAIsnF,CAAC;IAJznFpC,EAAE,CAAAqC,UAAA,UAAA6C,QAIsnF,CAAC;EAAA;AAAA;AAAA,SAAAC,sCAAAzD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJznF1B,EAAE,CAAA0C,UAAA,IAAAsC,oDAAA,yBAI8gF,CAAC;EAAA;EAAA,IAAAtD,EAAA;IAAA,MAAAM,MAAA,GAJjhFhC,EAAE,CAAAiC,aAAA;IAAFjC,EAAE,CAAAqC,UAAA,YAAAL,MAAA,CAAAM,KAI4/E,CAAC;EAAA;AAAA;AA7BpmF,MAAM8C,+BAA+B,GAAG;EACpCV,IAAI,EAAE,oBAAoB;EAC1BW,IAAI,EAAE,GAAG;EACTnC,UAAU,EAAE;AAChB,CAAC;AACD,MAAMoC,uBAAuB,GAAG/D,cAAc,CAAC6D,+BAA+B,CAAC;AAC/E,SAASG,6BAA6BA,CAACd,OAAO,EAAE;EAC5C,OAAOjD,iBAAiB,CAAC8D,uBAAuB,EAAEb,OAAO,EAAEW,+BAA+B,CAAC;AAC/F;AAEA,MAAMI,cAAc,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnD,KAAK,GAAG9B,WAAW;IACxB,IAAI,CAACiE,OAAO,GAAGxE,MAAM,CAACqF,uBAAuB,CAAC;IAC9C,IAAI,CAACrB,KAAK,GAAGhE,MAAM,CAACkB,gBAAgB,CAAC;IACrC,IAAI,CAACiD,IAAI,GAAG7D,QAAQ,CAACN,MAAM,CAACqB,aAAa,CAAC,EAAE;MAAEoE,YAAY,EAAE;IAAG,CAAC,CAAC;IACjE,IAAI,CAACL,IAAI,GAAG,IAAI,CAACZ,OAAO,CAACY,IAAI;IAC7B,IAAI,CAACnC,UAAU,GAAG,IAAI,CAACuB,OAAO,CAACvB,UAAU;EAC7C;EACA,IAAIyC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACzC,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG,CAAC,GAAG0C,QAAQ;EAC3D;EACA,IAAIjC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACT,UAAU,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;EACxC;EACA;IAAS,IAAI,CAAC2C,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFP,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACQ,IAAI,kBAD+EhG,EAAE,CAAAiG,iBAAA;MAAAC,IAAA,EACJV,cAAc;MAAAW,SAAA;MAAAC,cAAA,WAAAC,8BAAA3E,EAAA,EAAAC,GAAA,EAAA2E,QAAA;QAAA,IAAA5E,EAAA;UADZ1B,EAAE,CAAAuG,cAAA,CAAAD,QAAA,EAI9C5F,OAAO,KAAQR,WAAW;QAAA;QAAA,IAAAwB,EAAA;UAAA,IAAA8E,EAAA;UAJkBxG,EAAE,CAAAyG,cAAA,CAAAD,EAAA,GAAFxG,EAAE,CAAA0G,WAAA,QAAA/E,GAAA,CAAAW,KAAA,GAAAkE,EAAA;QAAA;MAAA;MAAAG,QAAA;MAAAC,YAAA,WAAAC,4BAAAnF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1B,EAAE,CAAA8G,WAAA,cAAAnF,GAAA,CAAA0D,IAAA;QAAA;MAAA;MAAA0B,MAAA;QAAA1B,IAAA;QAAAnC,UAAA;MAAA;MAAA8D,UAAA;MAAAC,QAAA,GAAFjH,EAAE,CAAAkH,kBAAA,CACgL,CAC3QnG,sBAAsB,CAAC;QAAEoG,UAAU,EAAE;MAAmB,CAAC,CAAC,EAC1DjG,sBAAsB,CAAC;QAAEkG,SAAS,EAAE;MAAS,CAAC,CAAC,CAClD,GAJ4FpH,EAAE,CAAAqH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAhG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1B,EAAE,CAAA0C,UAAA,IAAAjB,sCAAA,yBAIyD,CAAC;UAJ5DzB,EAAE,CAAA2H,MAAA;UAAF3H,EAAE,CAAA0C,UAAA,IAAA2B,sCAAA,yBAI8G,CAAC,IAAAQ,qCAAA,gCAJjH7E,EAAE,CAAA+D,sBAIurE,CAAC,IAAAoB,qCAAA,gCAJ1rEnF,EAAE,CAAA+D,sBAI48E,CAAC;QAAA;QAAA,IAAArC,EAAA;UAAA,MAAAkG,SAAA,GAJ/8E5H,EAAE,CAAAmC,WAAA;UAAFnC,EAAE,CAAAqC,UAAA,SAAFrC,EAAE,CAAA6H,WAAA,OAAAlG,GAAA,CAAAW,KAAA,CAAAwF,OAAA,CAIoD,CAAC;UAJvD9H,EAAE,CAAAoC,SAAA,EAIiG,CAAC;UAJpGpC,EAAE,CAAAqC,UAAA,SAAAV,GAAA,CAAAuB,UAAA,IAIiG,CAAC,aAAA0E,SAAS,CAAC;QAAA;MAAA;MAAAG,YAAA,GAAirGnI,SAAS,EAA8CC,OAAO,EAAmHC,IAAI,EAA6FC,gBAAgB,EAAoJY,SAAS,EAAoIC,EAAE,CAACoH,oBAAoB,EAA4FhH,EAAE,CAACiH,oBAAoB,EAAiJjH,EAAE,CAACkH,eAAe,EAAuMpH,OAAO,EAAqFM,EAAE,CAAC+G,yBAAyB,EAA8F/G,EAAE,CAACgH,OAAO,EAAsD3H,EAAE,CAACC,OAAO;MAAA2H,MAAA;MAAAC,eAAA;IAAA,EAAiF;EAAE;AAC9yJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KANqGvI,EAAE,CAAAwI,iBAAA,CAMXhD,cAAc,EAAc,CAAC;IAC7GU,IAAI,EAAE/F,SAAS;IACfsI,IAAI,EAAE,CAAC;MAAEzB,UAAU,EAAE,IAAI;MAAE0B,QAAQ,EAAE,iBAAiB;MAAEC,OAAO,EAAE,CACrD/I,SAAS,EACTC,OAAO,EACPC,IAAI,EACJC,gBAAgB,EAChBY,SAAS,EACTE,WAAW,EACXI,WAAW,EACXH,OAAO,EACPO,gBAAgB,CACnB;MAAEiH,eAAe,EAAElI,uBAAuB,CAACwI,MAAM;MAAEC,SAAS,EAAE,CAC3D9H,sBAAsB,CAAC;QAAEoG,UAAU,EAAE;MAAmB,CAAC,CAAC,EAC1DjG,sBAAsB,CAAC;QAAEkG,SAAS,EAAE;MAAS,CAAC,CAAC,CAClD;MAAE0B,IAAI,EAAE;QACL,kBAAkB,EAAE;MACxB,CAAC;MAAErB,QAAQ,EAAE,ktFAAktF;MAAEY,MAAM,EAAE,CAAC,+gBAA+gB;IAAE,CAAC;EACxwG,CAAC,CAAC,QAAkB;IAAE/F,KAAK,EAAE,CAAC;MACtB4D,IAAI,EAAE7F,eAAe;MACrBoI,IAAI,EAAE,CAAC/H,OAAO,EAAE;QAAEqI,IAAI,EAAE7I;MAAY,CAAC;IACzC,CAAC,CAAC;IAAEmF,IAAI,EAAE,CAAC;MACPa,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAE4C,UAAU,EAAE,CAAC;MACbgD,IAAI,EAAE5F;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAAS8E,+BAA+B,EAAEE,uBAAuB,EAAEE,cAAc,EAAED,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}