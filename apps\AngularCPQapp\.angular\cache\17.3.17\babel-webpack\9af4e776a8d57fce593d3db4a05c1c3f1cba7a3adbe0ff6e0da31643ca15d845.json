{"ast": null, "code": "import { TuiCalendarRange } from '@taiga-ui/kit/components/calendar-range';\nimport * as i0 from '@angular/core';\nimport { inject, computed, signal, Directive, Input } from '@angular/core';\nimport * as i3 from '@maskito/angular';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { maskitoDateRangeOptionsGenerator } from '@maskito/kit';\nimport { TUI_IDENTITY_VALUE_TRANSFORMER, tuiAsControl, tuiValueTransformerFrom } from '@taiga-ui/cdk/classes';\nimport { RANGE_SEPARATOR_CHAR, DATE_RANGE_FILLER_LENGTH, TuiDayRange } from '@taiga-ui/cdk/date-time';\nimport { tuiCreateTokenFromFactory, tuiProvideOptions, tuiDirectiveBinding, tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldComponent, TuiWithTextfield } from '@taiga-ui/core/components/textfield';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { TuiDropdownAuto } from '@taiga-ui/core/directives/dropdown';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { TUI_INPUT_DATE_OPTIONS_NEW, TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW, TuiInputDateBase, TUI_DATE_ADAPTER } from '@taiga-ui/kit/components/input-date';\nimport { tuiMaskito } from '@taiga-ui/kit/utils';\nconst TUI_INPUT_DATE_RANGE_OPTIONS = tuiCreateTokenFromFactory(() => ({\n  ...inject(TUI_INPUT_DATE_OPTIONS_NEW),\n  valueTransformer: TUI_IDENTITY_VALUE_TRANSFORMER\n}));\nconst tuiInputDateRangeOptionsProvider = options => tuiProvideOptions(TUI_INPUT_DATE_RANGE_OPTIONS, options, TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW);\nclass TuiInputDateRangeDirective extends TuiInputDateBase {\n  constructor() {\n    super(...arguments);\n    this.identity = inject(TUI_ITEMS_HANDLERS).identityMatcher.set((a, b) => a.daySame(b));\n    this.rangeFiller = tuiDirectiveBinding(TuiTextfieldComponent, 'fillerSetter', computed((filler = this.filler()) => `${filler}${RANGE_SEPARATOR_CHAR}${filler}`), {});\n    this.mask = tuiMaskito(computed(() => maskitoDateRangeOptionsGenerator({\n      dateSeparator: this.format().separator,\n      mode: TUI_DATE_ADAPTER[this.format().mode],\n      min: this.min().toLocalNativeDate(),\n      max: this.max().toLocalNativeDate(),\n      minLength: this.minLength() || {},\n      maxLength: this.maxLength() || {}\n    })));\n    this.minLength = signal(null);\n    this.maxLength = signal(null);\n  }\n  set minLengthSetter(minLength) {\n    this.minLength.set(minLength);\n  }\n  set maxLengthSetter(maxLength) {\n    this.maxLength.set(maxLength);\n  }\n  processCalendar(calendar) {\n    super.processCalendar(calendar);\n    calendar.minLength = this.minLength();\n    calendar.maxLength = this.maxLength();\n  }\n  onValueChange(value) {\n    this.control?.control?.updateValueAndValidity({\n      emitEvent: false\n    });\n    this.onChange(value.length === DATE_RANGE_FILLER_LENGTH ? TuiDayRange.normalizeParse(value, this.format().mode) : null);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiInputDateRangeDirective_BaseFactory;\n      return function TuiInputDateRangeDirective_Factory(t) {\n        return (ɵTuiInputDateRangeDirective_BaseFactory || (ɵTuiInputDateRangeDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiInputDateRangeDirective)))(t || TuiInputDateRangeDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiInputDateRangeDirective,\n      selectors: [[\"input\", \"tuiInputDateRange\", \"\"]],\n      inputs: {\n        minLengthSetter: [i0.ɵɵInputFlags.None, \"minLength\", \"minLengthSetter\"],\n        maxLengthSetter: [i0.ɵɵInputFlags.None, \"maxLength\", \"maxLengthSetter\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([\n      // TODO: Add SelectOption after data-list in calendar-range is refactored\n      tuiAsControl(TuiInputDateRangeDirective), tuiValueTransformerFrom(TUI_INPUT_DATE_RANGE_OPTIONS), tuiProvide(TUI_INPUT_DATE_OPTIONS_NEW, TUI_INPUT_DATE_RANGE_OPTIONS)]), i0.ɵɵHostDirectivesFeature([i1.TuiWithTextfield, i2.TuiDropdownAuto, i3.MaskitoDirective]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputDateRangeDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiInputDateRange]',\n      providers: [\n      // TODO: Add SelectOption after data-list in calendar-range is refactored\n      tuiAsControl(TuiInputDateRangeDirective), tuiValueTransformerFrom(TUI_INPUT_DATE_RANGE_OPTIONS), tuiProvide(TUI_INPUT_DATE_OPTIONS_NEW, TUI_INPUT_DATE_RANGE_OPTIONS)],\n      hostDirectives: [TuiWithTextfield, TuiDropdownAuto, MaskitoDirective]\n    }]\n  }], null, {\n    minLengthSetter: [{\n      type: Input,\n      args: ['minLength']\n    }],\n    maxLengthSetter: [{\n      type: Input,\n      args: ['maxLength']\n    }]\n  });\n})();\nconst TuiInputDateRange = [TuiInputDateRangeDirective, TuiCalendarRange];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_DATE_RANGE_OPTIONS, TuiInputDateRange, TuiInputDateRangeDirective, tuiInputDateRangeOptionsProvider };", "map": {"version": 3, "names": ["TuiCalendarRange", "i0", "inject", "computed", "signal", "Directive", "Input", "i3", "MaskitoDirective", "maskitoDateRangeOptionsGenerator", "TUI_IDENTITY_VALUE_TRANSFORMER", "tuiAsControl", "tuiValueTransformerFrom", "RANGE_SEPARATOR_CHAR", "DATE_RANGE_FILLER_LENGTH", "TuiDayRange", "tuiCreateTokenFromFactory", "tuiProvideOptions", "tuiDirectiveBinding", "tui<PERSON><PERSON><PERSON>", "i1", "TuiTextfieldComponent", "TuiWithTextfield", "i2", "TuiDropdownAuto", "TUI_ITEMS_HANDLERS", "TUI_INPUT_DATE_OPTIONS_NEW", "TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW", "TuiInputDateBase", "TUI_DATE_ADAPTER", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TUI_INPUT_DATE_RANGE_OPTIONS", "valueTransformer", "tuiInputDateRangeOptionsProvider", "options", "TuiInputDateRangeDirective", "constructor", "arguments", "identity", "identityMatcher", "set", "a", "b", "daySame", "rangeFiller", "filler", "mask", "dateSeparator", "format", "separator", "mode", "min", "toLocalNativeDate", "max", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minLengthSetter", "maxLengthSetter", "processCalendar", "calendar", "onValueChange", "value", "control", "updateValueAndValidity", "emitEvent", "onChange", "length", "normalizeParse", "ɵfac", "ɵTuiInputDateRangeDirective_BaseFactory", "TuiInputDateRangeDirective_Factory", "t", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "ɵɵInputFlags", "None", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "hostDirectives", "TuiInputDateRange"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-input-date-range.mjs"], "sourcesContent": ["import { TuiCalendarRange } from '@taiga-ui/kit/components/calendar-range';\nimport * as i0 from '@angular/core';\nimport { inject, computed, signal, Directive, Input } from '@angular/core';\nimport * as i3 from '@maskito/angular';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { maskitoDateRangeOptionsGenerator } from '@maskito/kit';\nimport { TUI_IDENTITY_VALUE_TRANSFORMER, tuiAsControl, tuiValueTransformerFrom } from '@taiga-ui/cdk/classes';\nimport { RANGE_SEPARATOR_CHAR, DATE_RANGE_FILLER_LENGTH, TuiDayRange } from '@taiga-ui/cdk/date-time';\nimport { tuiCreateTokenFromFactory, tuiProvideOptions, tuiDirectiveBinding, tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldComponent, TuiWithTextfield } from '@taiga-ui/core/components/textfield';\nimport * as i2 from '@taiga-ui/core/directives/dropdown';\nimport { TuiDropdownAuto } from '@taiga-ui/core/directives/dropdown';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { TUI_INPUT_DATE_OPTIONS_NEW, TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW, TuiInputDateBase, TUI_DATE_ADAPTER } from '@taiga-ui/kit/components/input-date';\nimport { tuiMaskito } from '@taiga-ui/kit/utils';\n\nconst TUI_INPUT_DATE_RANGE_OPTIONS = tuiCreateTokenFromFactory(() => ({\n    ...inject(TUI_INPUT_DATE_OPTIONS_NEW),\n    valueTransformer: TUI_IDENTITY_VALUE_TRANSFORMER,\n}));\nconst tuiInputDateRangeOptionsProvider = (options) => tuiProvideOptions(TUI_INPUT_DATE_RANGE_OPTIONS, options, TUI_INPUT_DATE_DEFAULT_OPTIONS_NEW);\n\nclass TuiInputDateRangeDirective extends TuiInputDateBase {\n    constructor() {\n        super(...arguments);\n        this.identity = inject(TUI_ITEMS_HANDLERS).identityMatcher.set((a, b) => a.daySame(b));\n        this.rangeFiller = tuiDirectiveBinding(TuiTextfieldComponent, 'fillerSetter', computed((filler = this.filler()) => `${filler}${RANGE_SEPARATOR_CHAR}${filler}`), {});\n        this.mask = tuiMaskito(computed(() => maskitoDateRangeOptionsGenerator({\n            dateSeparator: this.format().separator,\n            mode: TUI_DATE_ADAPTER[this.format().mode],\n            min: this.min().toLocalNativeDate(),\n            max: this.max().toLocalNativeDate(),\n            minLength: this.minLength() || {},\n            maxLength: this.maxLength() || {},\n        })));\n        this.minLength = signal(null);\n        this.maxLength = signal(null);\n    }\n    set minLengthSetter(minLength) {\n        this.minLength.set(minLength);\n    }\n    set maxLengthSetter(maxLength) {\n        this.maxLength.set(maxLength);\n    }\n    processCalendar(calendar) {\n        super.processCalendar(calendar);\n        calendar.minLength = this.minLength();\n        calendar.maxLength = this.maxLength();\n    }\n    onValueChange(value) {\n        this.control?.control?.updateValueAndValidity({ emitEvent: false });\n        this.onChange(value.length === DATE_RANGE_FILLER_LENGTH\n            ? TuiDayRange.normalizeParse(value, this.format().mode)\n            : null);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputDateRangeDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputDateRangeDirective, isStandalone: true, selector: \"input[tuiInputDateRange]\", inputs: { minLengthSetter: [\"minLength\", \"minLengthSetter\"], maxLengthSetter: [\"maxLength\", \"maxLengthSetter\"] }, providers: [\n            // TODO: Add SelectOption after data-list in calendar-range is refactored\n            tuiAsControl(TuiInputDateRangeDirective),\n            tuiValueTransformerFrom(TUI_INPUT_DATE_RANGE_OPTIONS),\n            tuiProvide(TUI_INPUT_DATE_OPTIONS_NEW, TUI_INPUT_DATE_RANGE_OPTIONS),\n        ], usesInheritance: true, hostDirectives: [{ directive: i1.TuiWithTextfield }, { directive: i2.TuiDropdownAuto }, { directive: i3.MaskitoDirective }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputDateRangeDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiInputDateRange]',\n                    providers: [\n                        // TODO: Add SelectOption after data-list in calendar-range is refactored\n                        tuiAsControl(TuiInputDateRangeDirective),\n                        tuiValueTransformerFrom(TUI_INPUT_DATE_RANGE_OPTIONS),\n                        tuiProvide(TUI_INPUT_DATE_OPTIONS_NEW, TUI_INPUT_DATE_RANGE_OPTIONS),\n                    ],\n                    hostDirectives: [TuiWithTextfield, TuiDropdownAuto, MaskitoDirective],\n                }]\n        }], propDecorators: { minLengthSetter: [{\n                type: Input,\n                args: ['minLength']\n            }], maxLengthSetter: [{\n                type: Input,\n                args: ['maxLength']\n            }] } });\n\nconst TuiInputDateRange = [TuiInputDateRangeDirective, TuiCalendarRange];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_DATE_RANGE_OPTIONS, TuiInputDateRange, TuiInputDateRangeDirective, tuiInputDateRangeOptionsProvider };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,yCAAyC;AAC1E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAC1E,OAAO,KAAKC,EAAE,MAAM,kBAAkB;AACtC,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,gCAAgC,QAAQ,cAAc;AAC/D,SAASC,8BAA8B,EAAEC,YAAY,EAAEC,uBAAuB,QAAQ,uBAAuB;AAC7G,SAASC,oBAAoB,EAAEC,wBAAwB,EAAEC,WAAW,QAAQ,yBAAyB;AACrG,SAASC,yBAAyB,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,mCAAmC;AACjI,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,qBAAqB,EAAEC,gBAAgB,QAAQ,qCAAqC;AAC7F,OAAO,KAAKC,EAAE,MAAM,oCAAoC;AACxD,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,0BAA0B,EAAEC,kCAAkC,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,qCAAqC;AACxJ,SAASC,UAAU,QAAQ,qBAAqB;AAEhD,MAAMC,4BAA4B,GAAGf,yBAAyB,CAAC,OAAO;EAClE,GAAGd,MAAM,CAACwB,0BAA0B,CAAC;EACrCM,gBAAgB,EAAEtB;AACtB,CAAC,CAAC,CAAC;AACH,MAAMuB,gCAAgC,GAAIC,OAAO,IAAKjB,iBAAiB,CAACc,4BAA4B,EAAEG,OAAO,EAAEP,kCAAkC,CAAC;AAElJ,MAAMQ,0BAA0B,SAASP,gBAAgB,CAAC;EACtDQ,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAGpC,MAAM,CAACuB,kBAAkB,CAAC,CAACc,eAAe,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,OAAO,CAACD,CAAC,CAAC,CAAC;IACtF,IAAI,CAACE,WAAW,GAAG1B,mBAAmB,CAACG,qBAAqB,EAAE,cAAc,EAAElB,QAAQ,CAAC,CAAC0C,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC,KAAK,GAAGA,MAAM,GAAGhC,oBAAoB,GAAGgC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACpK,IAAI,CAACC,IAAI,GAAGhB,UAAU,CAAC3B,QAAQ,CAAC,MAAMM,gCAAgC,CAAC;MACnEsC,aAAa,EAAE,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,SAAS;MACtCC,IAAI,EAAErB,gBAAgB,CAAC,IAAI,CAACmB,MAAM,CAAC,CAAC,CAACE,IAAI,CAAC;MAC1CC,GAAG,EAAE,IAAI,CAACA,GAAG,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MACnCC,GAAG,EAAE,IAAI,CAACA,GAAG,CAAC,CAAC,CAACD,iBAAiB,CAAC,CAAC;MACnCE,SAAS,EAAE,IAAI,CAACA,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC;MACjCC,SAAS,EAAE,IAAI,CAACA,SAAS,CAAC,CAAC,IAAI,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC;IACJ,IAAI,CAACD,SAAS,GAAGlD,MAAM,CAAC,IAAI,CAAC;IAC7B,IAAI,CAACmD,SAAS,GAAGnD,MAAM,CAAC,IAAI,CAAC;EACjC;EACA,IAAIoD,eAAeA,CAACF,SAAS,EAAE;IAC3B,IAAI,CAACA,SAAS,CAACd,GAAG,CAACc,SAAS,CAAC;EACjC;EACA,IAAIG,eAAeA,CAACF,SAAS,EAAE;IAC3B,IAAI,CAACA,SAAS,CAACf,GAAG,CAACe,SAAS,CAAC;EACjC;EACAG,eAAeA,CAACC,QAAQ,EAAE;IACtB,KAAK,CAACD,eAAe,CAACC,QAAQ,CAAC;IAC/BA,QAAQ,CAACL,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC;IACrCK,QAAQ,CAACJ,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC;EACzC;EACAK,aAAaA,CAACC,KAAK,EAAE;IACjB,IAAI,CAACC,OAAO,EAAEA,OAAO,EAAEC,sBAAsB,CAAC;MAAEC,SAAS,EAAE;IAAM,CAAC,CAAC;IACnE,IAAI,CAACC,QAAQ,CAACJ,KAAK,CAACK,MAAM,KAAKpD,wBAAwB,GACjDC,WAAW,CAACoD,cAAc,CAACN,KAAK,EAAE,IAAI,CAACb,MAAM,CAAC,CAAC,CAACE,IAAI,CAAC,GACrD,IAAI,CAAC;EACf;EACA;IAAS,IAAI,CAACkB,IAAI;MAAA,IAAAC,uCAAA;MAAA,gBAAAC,mCAAAC,CAAA;QAAA,QAAAF,uCAAA,KAAAA,uCAAA,GAA+EpE,EAAE,CAAAuE,qBAAA,CAAQrC,0BAA0B,IAAAoC,CAAA,IAA1BpC,0BAA0B;MAAA;IAAA,IAAqD;EAAE;EAC5L;IAAS,IAAI,CAACsC,IAAI,kBAD+ExE,EAAE,CAAAyE,iBAAA;MAAAC,IAAA,EACJxC,0BAA0B;MAAAyC,SAAA;MAAAC,MAAA;QAAArB,eAAA,GADxBvD,EAAE,CAAA6E,YAAA,CAAAC,IAAA;QAAAtB,eAAA,GAAFxD,EAAE,CAAA6E,YAAA,CAAAC,IAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFhF,EAAE,CAAAiF,kBAAA,CAC+M;MAC1S;MACAvE,YAAY,CAACwB,0BAA0B,CAAC,EACxCvB,uBAAuB,CAACmB,4BAA4B,CAAC,EACrDZ,UAAU,CAACO,0BAA0B,EAAEK,4BAA4B,CAAC,CACvE,GAN4F9B,EAAE,CAAAkF,uBAAA,EAMvC/D,EAAE,CAACE,gBAAgB,EAAiBC,EAAE,CAACC,eAAe,EAAiBjB,EAAE,CAACC,gBAAgB,IANrDP,EAAE,CAAAmF,0BAAA;IAAA,EAMuE;EAAE;AAChL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KARqGpF,EAAE,CAAAqF,iBAAA,CAQXnD,0BAA0B,EAAc,CAAC;IACzHwC,IAAI,EAAEtE,SAAS;IACfkF,IAAI,EAAE,CAAC;MACCP,UAAU,EAAE,IAAI;MAChBQ,QAAQ,EAAE,0BAA0B;MACpCC,SAAS,EAAE;MACP;MACA9E,YAAY,CAACwB,0BAA0B,CAAC,EACxCvB,uBAAuB,CAACmB,4BAA4B,CAAC,EACrDZ,UAAU,CAACO,0BAA0B,EAAEK,4BAA4B,CAAC,CACvE;MACD2D,cAAc,EAAE,CAACpE,gBAAgB,EAAEE,eAAe,EAAEhB,gBAAgB;IACxE,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEgD,eAAe,EAAE,CAAC;MAChCmB,IAAI,EAAErE,KAAK;MACXiF,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAE9B,eAAe,EAAE,CAAC;MAClBkB,IAAI,EAAErE,KAAK;MACXiF,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMI,iBAAiB,GAAG,CAACxD,0BAA0B,EAAEnC,gBAAgB,CAAC;;AAExE;AACA;AACA;;AAEA,SAAS+B,4BAA4B,EAAE4D,iBAAiB,EAAExD,0BAA0B,EAAEF,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}