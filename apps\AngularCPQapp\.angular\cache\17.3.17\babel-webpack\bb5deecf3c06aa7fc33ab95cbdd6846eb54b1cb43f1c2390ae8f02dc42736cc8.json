{"ast": null, "code": "import { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { inject, ElementRef, PLATFORM_ID, isSignal, signal, untracked, DestroyRef, effect, INJECTOR } from '@angular/core';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { isPlatformBrowser } from '@angular/common';\nimport { WA_WINDOW } from '@ng-web-apis/common';\nimport { TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nfunction tuiContainsOrAfter(current, node) {\n  try {\n    return current.contains(node) || !!(node.compareDocumentPosition(current) & Node.DOCUMENT_POSITION_PRECEDING);\n  } catch {\n    return false;\n  }\n}\nfunction tuiIsInput(element) {\n  return element.matches('input');\n}\nfunction tuiIsTextarea(element) {\n  return element.matches('textarea');\n}\nfunction tuiIsTextfield(element) {\n  return tuiIsInput(element) || tuiIsTextarea(element);\n}\nfunction tuiIsElement(node) {\n  return !!node && 'nodeType' in node && node.nodeType === Node.ELEMENT_NODE;\n}\nfunction tuiIsHTMLElement(node) {\n  const defaultView = node?.ownerDocument.defaultView;\n  return !!node && !!defaultView && node instanceof defaultView.HTMLElement;\n}\nfunction tuiIsTextNode(node) {\n  return node.nodeType === Node.TEXT_NODE;\n}\nfunction tuiIsInputEvent(event) {\n  return 'data' in event && 'inputType' in event;\n}\n\n/**\n * Gets actual target from open Shadow DOM if event happened within it\n */\nfunction tuiGetActualTarget(event) {\n  return event.composedPath()[0];\n}\nconst DEFAULT_FORMAT = 'text/plain';\n/**\n * Gets text from data of clipboardEvent, it also works in IE and Edge browsers\n */\nfunction tuiGetClipboardDataText(event, format = DEFAULT_FORMAT) {\n  return 'clipboardData' in event && event.clipboardData !== null ? event.clipboardData.getData(format) || event.clipboardData.getData(DEFAULT_FORMAT) : event.target.ownerDocument.defaultView.clipboardData.getData('text');\n}\nfunction tuiGetDocumentOrShadowRoot(node) {\n  return 'getRootNode' in node && node.isConnected ? node.getRootNode() : node.ownerDocument;\n}\n\n/**\n * Returns array of Elements covering edges of given element or null if at least one edge middle point is visible\n *\n * CAUTION: Empty array means element if offscreen i.e. covered by no elements, rather than not covered\n * ```ts\n * function tuiGetElementObscures(element: Element): readonly [Element, Element, Element, Element] | [] | null\n * ```\n */\nfunction tuiGetElementObscures(element) {\n  const {\n    ownerDocument\n  } = element;\n  if (!ownerDocument?.defaultView || !element.getBoundingClientRect) {\n    return null;\n  }\n  const {\n    innerWidth,\n    innerHeight\n  } = ownerDocument.defaultView;\n  const doc = tuiGetDocumentOrShadowRoot(element);\n  const rect = element.getBoundingClientRect();\n  if (rect.width === 0 && rect.height === 0) {\n    return null;\n  }\n  const left = tuiClamp(Math.round(rect.left) + 2, 0, innerWidth);\n  const top = tuiClamp(Math.round(rect.top) + 2, 0, innerHeight);\n  const right = tuiClamp(Math.round(rect.right) - 2, 0, innerWidth);\n  const bottom = tuiClamp(Math.round(rect.bottom) - 2, 0, innerHeight);\n  const horizontalMiddle = tuiClamp(Math.round(rect.left + rect.width / 2), 0, innerWidth);\n  const verticalMiddle = tuiClamp(Math.round(rect.top + rect.height / 2), 0, innerHeight);\n  const elements = [doc.elementFromPoint(horizontalMiddle, top), doc.elementFromPoint(horizontalMiddle, bottom), doc.elementFromPoint(left, verticalMiddle), doc.elementFromPoint(right, verticalMiddle)];\n  const nonNull = elements.filter(tuiIsPresent);\n  if (!nonNull.length) {\n    return [];\n  }\n  const filtered = nonNull.filter(el => !element.contains(el) && !el.contains(element));\n  return filtered.length === 4 ? filtered : null;\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Calculates offset for an element relative to it's parent several levels above\n *\n * @param host parent element\n * @param element\n * @return object with offsetTop and offsetLeft number properties\n */\nfunction tuiGetElementOffset(host, element) {\n  ngDevMode && console.assert(host.contains(element), 'Host must contain element');\n  let {\n    offsetTop,\n    offsetLeft,\n    offsetParent\n  } = element;\n  while (tuiIsHTMLElement(offsetParent) && offsetParent !== host) {\n    offsetTop += offsetParent.offsetTop;\n    offsetLeft += offsetParent.offsetLeft;\n    offsetParent = offsetParent.offsetParent;\n  }\n  return {\n    offsetTop,\n    offsetLeft\n  };\n}\nfunction tuiGetElementPoint(x, y, element) {\n  const {\n    left,\n    top,\n    width,\n    height\n  } = element.getBoundingClientRect();\n  return [tuiClamp(x - left, 0, width) / width, tuiClamp(y - top, 0, height) / height];\n}\n\n/**\n * @description:\n * cross browser way to get selected text\n *\n * History:\n * BUG - window.getSelection() fails when text selected in a form field\n * https://bugzilla.mozilla.org/show_bug.cgi?id=85686\n */\nfunction tuiGetSelectedText({\n  getSelection,\n  document\n}) {\n  return document.activeElement && tuiIsTextfield(document.activeElement) ? document.activeElement.value.slice(document.activeElement.selectionStart || 0, document.activeElement.selectionEnd || 0) : getSelection()?.toString() || null;\n}\nfunction tuiInjectElement() {\n  return inject(ElementRef).nativeElement;\n}\nfunction tuiIsCurrentTarget({\n  target,\n  currentTarget\n}) {\n  return target === currentTarget;\n}\nfunction tuiIsElementEditable(element) {\n  return tuiIsTextfield(element) && !element.readOnly && element.inputMode !== 'none' || Boolean(element.isContentEditable);\n}\n\n/**\n * Checks if an app is running inside <iframe /> tag\n */\nfunction tuiIsInsideIframe(win) {\n  return win.parent !== win;\n}\n\n/**\n * Checks if node is inside a specific selector\n *\n * @param node\n * @param selector\n * @return true if node is inside a particular selector\n */\nfunction tuiIsNodeIn(node, selector) {\n  return tuiIsTextNode(node) ? !!node.parentElement?.closest(selector) : tuiIsElement(node) && !!node.closest(selector);\n}\nfunction tuiPointToClientRect(x = 0, y = 0) {\n  const rect = {\n    x,\n    y,\n    left: x,\n    right: x,\n    top: y,\n    bottom: y,\n    width: 0,\n    height: 0\n  };\n  return {\n    ...rect,\n    toJSON: () => rect\n  };\n}\nfunction tuiRetargetedBoundaryCrossing(event) {\n  // firefox\n  if ('explicitOriginalTarget' in event) {\n    return event?.explicitOriginalTarget !== event.target;\n  }\n  // chrome\n  if ('pointerId' in event) {\n    return event.pointerId === -1;\n  }\n  // safari\n  if ('detail' in event && 'webkitForce' in event) {\n    return event?.detail === 0;\n  }\n  return false;\n}\nfunction tuiValue(input, injector = inject(INJECTOR)) {\n  const win = injector.get(WA_WINDOW);\n  if (!win.tuiInputPatched && isPlatformBrowser(injector.get(PLATFORM_ID))) {\n    win.tuiInputPatched = true;\n    patch(win.HTMLInputElement.prototype);\n    patch(win.HTMLTextAreaElement.prototype);\n    patch(win.HTMLSelectElement.prototype);\n  }\n  let element = isSignal(input) ? undefined : coerceElement(input);\n  let cleanup = () => {};\n  const options = {\n    injector,\n    ...TUI_ALLOW_SIGNAL_WRITES\n  };\n  const value = signal(element?.value || '');\n  const process = el => {\n    const update = () => untracked(() => value.set(el.value));\n    el.addEventListener('input', update, {\n      capture: true\n    });\n    el.addEventListener('tui-input', update, {\n      capture: true\n    });\n    return () => {\n      el.removeEventListener('input', update, {\n        capture: true\n      });\n      el.removeEventListener('tui-input', update, {\n        capture: true\n      });\n    };\n  };\n  injector.get(DestroyRef).onDestroy(() => cleanup());\n  if (isSignal(input)) {\n    effect(() => {\n      element = coerceElement(input());\n      cleanup();\n      if (element) {\n        value.set(element.value);\n        cleanup = process(element);\n      }\n    }, options);\n  } else if (element) {\n    cleanup = process(element);\n  }\n  effect(() => {\n    const v = value();\n    if (element?.matches(':focus') && 'selectionStart' in element) {\n      const {\n        selectionStart,\n        selectionEnd\n      } = element;\n      /**\n       * After programmatic updates of input's value, caret is automatically placed at the end –\n       * revert to the previous position\n       */\n      element.value = v;\n      element.setSelectionRange(selectionStart, selectionEnd);\n    } else if (element) {\n      element.value = v;\n    }\n  }, options);\n  return value;\n}\nfunction patch(prototype) {\n  const {\n    set\n  } = Object.getOwnPropertyDescriptor(prototype, 'value');\n  Object.defineProperty(prototype, 'value', {\n    set(detail) {\n      const value = this.value;\n      const event = new CustomEvent('tui-input', {\n        detail,\n        bubbles: true\n      });\n      set.call(this, detail);\n      if (value !== detail) {\n        this.dispatchEvent(event);\n      }\n    }\n  });\n}\n\n/**\n * Host binding `host: {'[value]': 'yourSignal()'}` is not an option for our textfields –\n * they use {@link TuiTextfieldDirective} as a host directive.\n * `TuiTextfieldDirective` has host binding which depends on native input's value.\n * Host bindings of the host directives are re-calculated BEFORE component's ones –\n * native input's value should be updated SYNCHRONOUSLY before next change detection iteration.\n * @deprecated\n */\nfunction tuiValueBinding(value = signal(tuiInjectElement().value || '')) {\n  const el = tuiInjectElement();\n  effect(() => {\n    if (el.value === value()) {\n      return;\n    }\n    const {\n      selectionStart,\n      selectionEnd\n    } = el;\n    el.value = value();\n    if (el.matches(':focus')) {\n      /**\n       * After programmatic updates of input's value, caret is automatically placed at the end –\n       * revert to the previous position\n       */\n      el.setSelectionRange(selectionStart, selectionEnd);\n    }\n  });\n  return value;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiContainsOrAfter, tuiGetActualTarget, tuiGetClipboardDataText, tuiGetDocumentOrShadowRoot, tuiGetElementObscures, tuiGetElementOffset, tuiGetElementPoint, tuiGetSelectedText, tuiInjectElement, tuiIsCurrentTarget, tuiIsElement, tuiIsElementEditable, tuiIsHTMLElement, tuiIsInput, tuiIsInputEvent, tuiIsInsideIframe, tuiIsNodeIn, tuiIsTextNode, tuiIsTextarea, tuiIsTextfield, tuiPointToClientRect, tuiRetargetedBoundaryCrossing, tuiValue, tuiValueBinding };", "map": {"version": 3, "names": ["tui<PERSON><PERSON>", "tuiIsPresent", "inject", "ElementRef", "PLATFORM_ID", "isSignal", "signal", "untracked", "DestroyRef", "effect", "INJECTOR", "coerceElement", "isPlatformBrowser", "WA_WINDOW", "TUI_ALLOW_SIGNAL_WRITES", "tuiContainsOrAfter", "current", "node", "contains", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_PRECEDING", "tuiIsInput", "element", "matches", "tuiIsTextarea", "tuiIsTextfield", "tuiIsElement", "nodeType", "ELEMENT_NODE", "tuiIsHTMLElement", "defaultView", "ownerDocument", "HTMLElement", "tuiIsTextNode", "TEXT_NODE", "tuiIsInputEvent", "event", "tuiGetActualTarget", "<PERSON><PERSON><PERSON>", "DEFAULT_FORMAT", "tuiGetClipboardDataText", "format", "clipboardData", "getData", "target", "tuiGetDocumentOrShadowRoot", "isConnected", "getRootNode", "tuiGetElementObscures", "getBoundingClientRect", "innerWidth", "innerHeight", "doc", "rect", "width", "height", "left", "Math", "round", "top", "right", "bottom", "horizontalMiddle", "verticalMiddle", "elements", "elementFromPoint", "nonNull", "filter", "length", "filtered", "el", "tuiGetElementOffset", "host", "ngDevMode", "console", "assert", "offsetTop", "offsetLeft", "offsetParent", "tuiGetElementPoint", "x", "y", "tuiGetSelectedText", "getSelection", "document", "activeElement", "value", "slice", "selectionStart", "selectionEnd", "toString", "tuiInjectElement", "nativeElement", "tuiIsCurrentTarget", "currentTarget", "tuiIsElementEditable", "readOnly", "inputMode", "Boolean", "isContentEditable", "tuiIsInsideIframe", "win", "parent", "tuiIsNodeIn", "selector", "parentElement", "closest", "tuiPointToClientRect", "toJSON", "tuiRetargetedBoundaryCrossing", "explicitOriginalTarget", "pointerId", "detail", "tuiValue", "input", "injector", "get", "tuiInputPatched", "patch", "HTMLInputElement", "prototype", "HTMLTextAreaElement", "HTMLSelectElement", "undefined", "cleanup", "options", "process", "update", "set", "addEventListener", "capture", "removeEventListener", "onDestroy", "v", "setSelectionRange", "Object", "getOwnPropertyDescriptor", "defineProperty", "CustomEvent", "bubbles", "call", "dispatchEvent", "tuiV<PERSON>ue<PERSON><PERSON>ing"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-utils-dom.mjs"], "sourcesContent": ["import { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { tuiIsPresent } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { inject, ElementRef, PLATFORM_ID, isSignal, signal, untracked, DestroyRef, effect, INJECTOR } from '@angular/core';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { isPlatformBrowser } from '@angular/common';\nimport { WA_WINDOW } from '@ng-web-apis/common';\nimport { TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\n\nfunction tuiContainsOrAfter(current, node) {\n    try {\n        return (current.contains(node) ||\n            !!(node.compareDocumentPosition(current) & Node.DOCUMENT_POSITION_PRECEDING));\n    }\n    catch {\n        return false;\n    }\n}\n\nfunction tuiIsInput(element) {\n    return element.matches('input');\n}\nfunction tuiIsTextarea(element) {\n    return element.matches('textarea');\n}\nfunction tuiIsTextfield(element) {\n    return tuiIsInput(element) || tuiIsTextarea(element);\n}\nfunction tuiIsElement(node) {\n    return !!node && 'nodeType' in node && node.nodeType === Node.ELEMENT_NODE;\n}\nfunction tuiIsHTMLElement(node) {\n    const defaultView = node?.ownerDocument.defaultView;\n    return !!node && !!defaultView && node instanceof defaultView.HTMLElement;\n}\nfunction tuiIsTextNode(node) {\n    return node.nodeType === Node.TEXT_NODE;\n}\n\nfunction tuiIsInputEvent(event) {\n    return 'data' in event && 'inputType' in event;\n}\n\n/**\n * Gets actual target from open Shadow DOM if event happened within it\n */\nfunction tuiGetActualTarget(event) {\n    return event.composedPath()[0];\n}\n\nconst DEFAULT_FORMAT = 'text/plain';\n/**\n * Gets text from data of clipboardEvent, it also works in IE and Edge browsers\n */\nfunction tuiGetClipboardDataText(event, format = DEFAULT_FORMAT) {\n    return 'clipboardData' in event && event.clipboardData !== null\n        ? event.clipboardData.getData(format) ||\n            event.clipboardData.getData(DEFAULT_FORMAT)\n        : event.target.ownerDocument.defaultView.clipboardData.getData('text');\n}\n\nfunction tuiGetDocumentOrShadowRoot(node) {\n    return 'getRootNode' in node && node.isConnected\n        ? node.getRootNode()\n        : node.ownerDocument;\n}\n\n/**\n * Returns array of Elements covering edges of given element or null if at least one edge middle point is visible\n *\n * CAUTION: Empty array means element if offscreen i.e. covered by no elements, rather than not covered\n * ```ts\n * function tuiGetElementObscures(element: Element): readonly [Element, Element, Element, Element] | [] | null\n * ```\n */\nfunction tuiGetElementObscures(element) {\n    const { ownerDocument } = element;\n    if (!ownerDocument?.defaultView || !element.getBoundingClientRect) {\n        return null;\n    }\n    const { innerWidth, innerHeight } = ownerDocument.defaultView;\n    const doc = tuiGetDocumentOrShadowRoot(element);\n    const rect = element.getBoundingClientRect();\n    if (rect.width === 0 && rect.height === 0) {\n        return null;\n    }\n    const left = tuiClamp(Math.round(rect.left) + 2, 0, innerWidth);\n    const top = tuiClamp(Math.round(rect.top) + 2, 0, innerHeight);\n    const right = tuiClamp(Math.round(rect.right) - 2, 0, innerWidth);\n    const bottom = tuiClamp(Math.round(rect.bottom) - 2, 0, innerHeight);\n    const horizontalMiddle = tuiClamp(Math.round(rect.left + rect.width / 2), 0, innerWidth);\n    const verticalMiddle = tuiClamp(Math.round(rect.top + rect.height / 2), 0, innerHeight);\n    const elements = [\n        doc.elementFromPoint(horizontalMiddle, top),\n        doc.elementFromPoint(horizontalMiddle, bottom),\n        doc.elementFromPoint(left, verticalMiddle),\n        doc.elementFromPoint(right, verticalMiddle),\n    ];\n    const nonNull = elements.filter(tuiIsPresent);\n    if (!nonNull.length) {\n        return [];\n    }\n    const filtered = nonNull.filter((el) => !element.contains(el) && !el.contains(element));\n    return filtered.length === 4\n        ? filtered\n        : null;\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Calculates offset for an element relative to it's parent several levels above\n *\n * @param host parent element\n * @param element\n * @return object with offsetTop and offsetLeft number properties\n */\nfunction tuiGetElementOffset(host, element) {\n    ngDevMode && console.assert(host.contains(element), 'Host must contain element');\n    let { offsetTop, offsetLeft, offsetParent } = element;\n    while (tuiIsHTMLElement(offsetParent) && offsetParent !== host) {\n        offsetTop += offsetParent.offsetTop;\n        offsetLeft += offsetParent.offsetLeft;\n        offsetParent = offsetParent.offsetParent;\n    }\n    return { offsetTop, offsetLeft };\n}\n\nfunction tuiGetElementPoint(x, y, element) {\n    const { left, top, width, height } = element.getBoundingClientRect();\n    return [tuiClamp(x - left, 0, width) / width, tuiClamp(y - top, 0, height) / height];\n}\n\n/**\n * @description:\n * cross browser way to get selected text\n *\n * History:\n * BUG - window.getSelection() fails when text selected in a form field\n * https://bugzilla.mozilla.org/show_bug.cgi?id=85686\n */\nfunction tuiGetSelectedText({ getSelection, document }) {\n    return document.activeElement && tuiIsTextfield(document.activeElement)\n        ? document.activeElement.value.slice(document.activeElement.selectionStart || 0, document.activeElement.selectionEnd || 0)\n        : getSelection()?.toString() || null;\n}\n\nfunction tuiInjectElement() {\n    return inject(ElementRef).nativeElement;\n}\n\nfunction tuiIsCurrentTarget({ target, currentTarget }) {\n    return target === currentTarget;\n}\n\nfunction tuiIsElementEditable(element) {\n    return ((tuiIsTextfield(element) && !element.readOnly && element.inputMode !== 'none') ||\n        Boolean(element.isContentEditable));\n}\n\n/**\n * Checks if an app is running inside <iframe /> tag\n */\nfunction tuiIsInsideIframe(win) {\n    return win.parent !== win;\n}\n\n/**\n * Checks if node is inside a specific selector\n *\n * @param node\n * @param selector\n * @return true if node is inside a particular selector\n */\nfunction tuiIsNodeIn(node, selector) {\n    return tuiIsTextNode(node)\n        ? !!node.parentElement?.closest(selector)\n        : tuiIsElement(node) && !!node.closest(selector);\n}\n\nfunction tuiPointToClientRect(x = 0, y = 0) {\n    const rect = {\n        x,\n        y,\n        left: x,\n        right: x,\n        top: y,\n        bottom: y,\n        width: 0,\n        height: 0,\n    };\n    return {\n        ...rect,\n        toJSON: () => rect,\n    };\n}\n\nfunction tuiRetargetedBoundaryCrossing(event) {\n    // firefox\n    if ('explicitOriginalTarget' in event) {\n        return event?.explicitOriginalTarget !== event.target;\n    }\n    // chrome\n    if ('pointerId' in event) {\n        return event.pointerId === -1;\n    }\n    // safari\n    if ('detail' in event && 'webkitForce' in event) {\n        return event?.detail === 0;\n    }\n    return false;\n}\n\nfunction tuiValue(input, injector = inject(INJECTOR)) {\n    const win = injector.get(WA_WINDOW);\n    if (!win.tuiInputPatched && isPlatformBrowser(injector.get(PLATFORM_ID))) {\n        win.tuiInputPatched = true;\n        patch(win.HTMLInputElement.prototype);\n        patch(win.HTMLTextAreaElement.prototype);\n        patch(win.HTMLSelectElement.prototype);\n    }\n    let element = isSignal(input) ? undefined : coerceElement(input);\n    let cleanup = () => { };\n    const options = { injector, ...TUI_ALLOW_SIGNAL_WRITES };\n    const value = signal(element?.value || '');\n    const process = (el) => {\n        const update = () => untracked(() => value.set(el.value));\n        el.addEventListener('input', update, { capture: true });\n        el.addEventListener('tui-input', update, { capture: true });\n        return () => {\n            el.removeEventListener('input', update, { capture: true });\n            el.removeEventListener('tui-input', update, { capture: true });\n        };\n    };\n    injector.get(DestroyRef).onDestroy(() => cleanup());\n    if (isSignal(input)) {\n        effect(() => {\n            element = coerceElement(input());\n            cleanup();\n            if (element) {\n                value.set(element.value);\n                cleanup = process(element);\n            }\n        }, options);\n    }\n    else if (element) {\n        cleanup = process(element);\n    }\n    effect(() => {\n        const v = value();\n        if (element?.matches(':focus') && 'selectionStart' in element) {\n            const { selectionStart, selectionEnd } = element;\n            /**\n             * After programmatic updates of input's value, caret is automatically placed at the end –\n             * revert to the previous position\n             */\n            element.value = v;\n            element.setSelectionRange(selectionStart, selectionEnd);\n        }\n        else if (element) {\n            element.value = v;\n        }\n    }, options);\n    return value;\n}\nfunction patch(prototype) {\n    const { set } = Object.getOwnPropertyDescriptor(prototype, 'value');\n    Object.defineProperty(prototype, 'value', {\n        set(detail) {\n            const value = this.value;\n            const event = new CustomEvent('tui-input', { detail, bubbles: true });\n            set.call(this, detail);\n            if (value !== detail) {\n                this.dispatchEvent(event);\n            }\n        },\n    });\n}\n\n/**\n * Host binding `host: {'[value]': 'yourSignal()'}` is not an option for our textfields –\n * they use {@link TuiTextfieldDirective} as a host directive.\n * `TuiTextfieldDirective` has host binding which depends on native input's value.\n * Host bindings of the host directives are re-calculated BEFORE component's ones –\n * native input's value should be updated SYNCHRONOUSLY before next change detection iteration.\n * @deprecated\n */\nfunction tuiValueBinding(value = signal(tuiInjectElement().value || '')) {\n    const el = tuiInjectElement();\n    effect(() => {\n        if (el.value === value()) {\n            return;\n        }\n        const { selectionStart, selectionEnd } = el;\n        el.value = value();\n        if (el.matches(':focus')) {\n            /**\n             * After programmatic updates of input's value, caret is automatically placed at the end –\n             * revert to the previous position\n             */\n            el.setSelectionRange(selectionStart, selectionEnd);\n        }\n    });\n    return value;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiContainsOrAfter, tuiGetActualTarget, tuiGetClipboardDataText, tuiGetDocumentOrShadowRoot, tuiGetElementObscures, tuiGetElementOffset, tuiGetElementPoint, tuiGetSelectedText, tuiInjectElement, tuiIsCurrentTarget, tuiIsElement, tuiIsElementEditable, tuiIsHTMLElement, tuiIsInput, tuiIsInputEvent, tuiIsInsideIframe, tuiIsNodeIn, tuiIsTextNode, tuiIsTextarea, tuiIsTextfield, tuiPointToClientRect, tuiRetargetedBoundaryCrossing, tuiValue, tuiValueBinding };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC1H,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,uBAAuB,QAAQ,yBAAyB;AAEjE,SAASC,kBAAkBA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACvC,IAAI;IACA,OAAQD,OAAO,CAACE,QAAQ,CAACD,IAAI,CAAC,IAC1B,CAAC,EAAEA,IAAI,CAACE,uBAAuB,CAACH,OAAO,CAAC,GAAGI,IAAI,CAACC,2BAA2B,CAAC;EACpF,CAAC,CACD,MAAM;IACF,OAAO,KAAK;EAChB;AACJ;AAEA,SAASC,UAAUA,CAACC,OAAO,EAAE;EACzB,OAAOA,OAAO,CAACC,OAAO,CAAC,OAAO,CAAC;AACnC;AACA,SAASC,aAAaA,CAACF,OAAO,EAAE;EAC5B,OAAOA,OAAO,CAACC,OAAO,CAAC,UAAU,CAAC;AACtC;AACA,SAASE,cAAcA,CAACH,OAAO,EAAE;EAC7B,OAAOD,UAAU,CAACC,OAAO,CAAC,IAAIE,aAAa,CAACF,OAAO,CAAC;AACxD;AACA,SAASI,YAAYA,CAACV,IAAI,EAAE;EACxB,OAAO,CAAC,CAACA,IAAI,IAAI,UAAU,IAAIA,IAAI,IAAIA,IAAI,CAACW,QAAQ,KAAKR,IAAI,CAACS,YAAY;AAC9E;AACA,SAASC,gBAAgBA,CAACb,IAAI,EAAE;EAC5B,MAAMc,WAAW,GAAGd,IAAI,EAAEe,aAAa,CAACD,WAAW;EACnD,OAAO,CAAC,CAACd,IAAI,IAAI,CAAC,CAACc,WAAW,IAAId,IAAI,YAAYc,WAAW,CAACE,WAAW;AAC7E;AACA,SAASC,aAAaA,CAACjB,IAAI,EAAE;EACzB,OAAOA,IAAI,CAACW,QAAQ,KAAKR,IAAI,CAACe,SAAS;AAC3C;AAEA,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC5B,OAAO,MAAM,IAAIA,KAAK,IAAI,WAAW,IAAIA,KAAK;AAClD;;AAEA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACD,KAAK,EAAE;EAC/B,OAAOA,KAAK,CAACE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC;AAEA,MAAMC,cAAc,GAAG,YAAY;AACnC;AACA;AACA;AACA,SAASC,uBAAuBA,CAACJ,KAAK,EAAEK,MAAM,GAAGF,cAAc,EAAE;EAC7D,OAAO,eAAe,IAAIH,KAAK,IAAIA,KAAK,CAACM,aAAa,KAAK,IAAI,GACzDN,KAAK,CAACM,aAAa,CAACC,OAAO,CAACF,MAAM,CAAC,IACjCL,KAAK,CAACM,aAAa,CAACC,OAAO,CAACJ,cAAc,CAAC,GAC7CH,KAAK,CAACQ,MAAM,CAACb,aAAa,CAACD,WAAW,CAACY,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;AAC9E;AAEA,SAASE,0BAA0BA,CAAC7B,IAAI,EAAE;EACtC,OAAO,aAAa,IAAIA,IAAI,IAAIA,IAAI,CAAC8B,WAAW,GAC1C9B,IAAI,CAAC+B,WAAW,CAAC,CAAC,GAClB/B,IAAI,CAACe,aAAa;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiB,qBAAqBA,CAAC1B,OAAO,EAAE;EACpC,MAAM;IAAES;EAAc,CAAC,GAAGT,OAAO;EACjC,IAAI,CAACS,aAAa,EAAED,WAAW,IAAI,CAACR,OAAO,CAAC2B,qBAAqB,EAAE;IAC/D,OAAO,IAAI;EACf;EACA,MAAM;IAAEC,UAAU;IAAEC;EAAY,CAAC,GAAGpB,aAAa,CAACD,WAAW;EAC7D,MAAMsB,GAAG,GAAGP,0BAA0B,CAACvB,OAAO,CAAC;EAC/C,MAAM+B,IAAI,GAAG/B,OAAO,CAAC2B,qBAAqB,CAAC,CAAC;EAC5C,IAAII,IAAI,CAACC,KAAK,KAAK,CAAC,IAAID,IAAI,CAACE,MAAM,KAAK,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,MAAMC,IAAI,GAAGzD,QAAQ,CAAC0D,IAAI,CAACC,KAAK,CAACL,IAAI,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAEN,UAAU,CAAC;EAC/D,MAAMS,GAAG,GAAG5D,QAAQ,CAAC0D,IAAI,CAACC,KAAK,CAACL,IAAI,CAACM,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAER,WAAW,CAAC;EAC9D,MAAMS,KAAK,GAAG7D,QAAQ,CAAC0D,IAAI,CAACC,KAAK,CAACL,IAAI,CAACO,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAEV,UAAU,CAAC;EACjE,MAAMW,MAAM,GAAG9D,QAAQ,CAAC0D,IAAI,CAACC,KAAK,CAACL,IAAI,CAACQ,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,EAAEV,WAAW,CAAC;EACpE,MAAMW,gBAAgB,GAAG/D,QAAQ,CAAC0D,IAAI,CAACC,KAAK,CAACL,IAAI,CAACG,IAAI,GAAGH,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAEJ,UAAU,CAAC;EACxF,MAAMa,cAAc,GAAGhE,QAAQ,CAAC0D,IAAI,CAACC,KAAK,CAACL,IAAI,CAACM,GAAG,GAAGN,IAAI,CAACE,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAEJ,WAAW,CAAC;EACvF,MAAMa,QAAQ,GAAG,CACbZ,GAAG,CAACa,gBAAgB,CAACH,gBAAgB,EAAEH,GAAG,CAAC,EAC3CP,GAAG,CAACa,gBAAgB,CAACH,gBAAgB,EAAED,MAAM,CAAC,EAC9CT,GAAG,CAACa,gBAAgB,CAACT,IAAI,EAAEO,cAAc,CAAC,EAC1CX,GAAG,CAACa,gBAAgB,CAACL,KAAK,EAAEG,cAAc,CAAC,CAC9C;EACD,MAAMG,OAAO,GAAGF,QAAQ,CAACG,MAAM,CAACnE,YAAY,CAAC;EAC7C,IAAI,CAACkE,OAAO,CAACE,MAAM,EAAE;IACjB,OAAO,EAAE;EACb;EACA,MAAMC,QAAQ,GAAGH,OAAO,CAACC,MAAM,CAAEG,EAAE,IAAK,CAAChD,OAAO,CAACL,QAAQ,CAACqD,EAAE,CAAC,IAAI,CAACA,EAAE,CAACrD,QAAQ,CAACK,OAAO,CAAC,CAAC;EACvF,OAAO+C,QAAQ,CAACD,MAAM,KAAK,CAAC,GACtBC,QAAQ,GACR,IAAI;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,mBAAmBA,CAACC,IAAI,EAAElD,OAAO,EAAE;EACxCmD,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACH,IAAI,CAACvD,QAAQ,CAACK,OAAO,CAAC,EAAE,2BAA2B,CAAC;EAChF,IAAI;IAAEsD,SAAS;IAAEC,UAAU;IAAEC;EAAa,CAAC,GAAGxD,OAAO;EACrD,OAAOO,gBAAgB,CAACiD,YAAY,CAAC,IAAIA,YAAY,KAAKN,IAAI,EAAE;IAC5DI,SAAS,IAAIE,YAAY,CAACF,SAAS;IACnCC,UAAU,IAAIC,YAAY,CAACD,UAAU;IACrCC,YAAY,GAAGA,YAAY,CAACA,YAAY;EAC5C;EACA,OAAO;IAAEF,SAAS;IAAEC;EAAW,CAAC;AACpC;AAEA,SAASE,kBAAkBA,CAACC,CAAC,EAAEC,CAAC,EAAE3D,OAAO,EAAE;EACvC,MAAM;IAAEkC,IAAI;IAAEG,GAAG;IAAEL,KAAK;IAAEC;EAAO,CAAC,GAAGjC,OAAO,CAAC2B,qBAAqB,CAAC,CAAC;EACpE,OAAO,CAAClD,QAAQ,CAACiF,CAAC,GAAGxB,IAAI,EAAE,CAAC,EAAEF,KAAK,CAAC,GAAGA,KAAK,EAAEvD,QAAQ,CAACkF,CAAC,GAAGtB,GAAG,EAAE,CAAC,EAAEJ,MAAM,CAAC,GAAGA,MAAM,CAAC;AACxF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2B,kBAAkBA,CAAC;EAAEC,YAAY;EAAEC;AAAS,CAAC,EAAE;EACpD,OAAOA,QAAQ,CAACC,aAAa,IAAI5D,cAAc,CAAC2D,QAAQ,CAACC,aAAa,CAAC,GACjED,QAAQ,CAACC,aAAa,CAACC,KAAK,CAACC,KAAK,CAACH,QAAQ,CAACC,aAAa,CAACG,cAAc,IAAI,CAAC,EAAEJ,QAAQ,CAACC,aAAa,CAACI,YAAY,IAAI,CAAC,CAAC,GACxHN,YAAY,CAAC,CAAC,EAAEO,QAAQ,CAAC,CAAC,IAAI,IAAI;AAC5C;AAEA,SAASC,gBAAgBA,CAAA,EAAG;EACxB,OAAO1F,MAAM,CAACC,UAAU,CAAC,CAAC0F,aAAa;AAC3C;AAEA,SAASC,kBAAkBA,CAAC;EAAEjD,MAAM;EAAEkD;AAAc,CAAC,EAAE;EACnD,OAAOlD,MAAM,KAAKkD,aAAa;AACnC;AAEA,SAASC,oBAAoBA,CAACzE,OAAO,EAAE;EACnC,OAASG,cAAc,CAACH,OAAO,CAAC,IAAI,CAACA,OAAO,CAAC0E,QAAQ,IAAI1E,OAAO,CAAC2E,SAAS,KAAK,MAAM,IACjFC,OAAO,CAAC5E,OAAO,CAAC6E,iBAAiB,CAAC;AAC1C;;AAEA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,GAAG,EAAE;EAC5B,OAAOA,GAAG,CAACC,MAAM,KAAKD,GAAG;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACvF,IAAI,EAAEwF,QAAQ,EAAE;EACjC,OAAOvE,aAAa,CAACjB,IAAI,CAAC,GACpB,CAAC,CAACA,IAAI,CAACyF,aAAa,EAAEC,OAAO,CAACF,QAAQ,CAAC,GACvC9E,YAAY,CAACV,IAAI,CAAC,IAAI,CAAC,CAACA,IAAI,CAAC0F,OAAO,CAACF,QAAQ,CAAC;AACxD;AAEA,SAASG,oBAAoBA,CAAC3B,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAE;EACxC,MAAM5B,IAAI,GAAG;IACT2B,CAAC;IACDC,CAAC;IACDzB,IAAI,EAAEwB,CAAC;IACPpB,KAAK,EAAEoB,CAAC;IACRrB,GAAG,EAAEsB,CAAC;IACNpB,MAAM,EAAEoB,CAAC;IACT3B,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACZ,CAAC;EACD,OAAO;IACH,GAAGF,IAAI;IACPuD,MAAM,EAAEA,CAAA,KAAMvD;EAClB,CAAC;AACL;AAEA,SAASwD,6BAA6BA,CAACzE,KAAK,EAAE;EAC1C;EACA,IAAI,wBAAwB,IAAIA,KAAK,EAAE;IACnC,OAAOA,KAAK,EAAE0E,sBAAsB,KAAK1E,KAAK,CAACQ,MAAM;EACzD;EACA;EACA,IAAI,WAAW,IAAIR,KAAK,EAAE;IACtB,OAAOA,KAAK,CAAC2E,SAAS,KAAK,CAAC,CAAC;EACjC;EACA;EACA,IAAI,QAAQ,IAAI3E,KAAK,IAAI,aAAa,IAAIA,KAAK,EAAE;IAC7C,OAAOA,KAAK,EAAE4E,MAAM,KAAK,CAAC;EAC9B;EACA,OAAO,KAAK;AAChB;AAEA,SAASC,QAAQA,CAACC,KAAK,EAAEC,QAAQ,GAAGlH,MAAM,CAACQ,QAAQ,CAAC,EAAE;EAClD,MAAM4F,GAAG,GAAGc,QAAQ,CAACC,GAAG,CAACxG,SAAS,CAAC;EACnC,IAAI,CAACyF,GAAG,CAACgB,eAAe,IAAI1G,iBAAiB,CAACwG,QAAQ,CAACC,GAAG,CAACjH,WAAW,CAAC,CAAC,EAAE;IACtEkG,GAAG,CAACgB,eAAe,GAAG,IAAI;IAC1BC,KAAK,CAACjB,GAAG,CAACkB,gBAAgB,CAACC,SAAS,CAAC;IACrCF,KAAK,CAACjB,GAAG,CAACoB,mBAAmB,CAACD,SAAS,CAAC;IACxCF,KAAK,CAACjB,GAAG,CAACqB,iBAAiB,CAACF,SAAS,CAAC;EAC1C;EACA,IAAIlG,OAAO,GAAGlB,QAAQ,CAAC8G,KAAK,CAAC,GAAGS,SAAS,GAAGjH,aAAa,CAACwG,KAAK,CAAC;EAChE,IAAIU,OAAO,GAAGA,CAAA,KAAM,CAAE,CAAC;EACvB,MAAMC,OAAO,GAAG;IAAEV,QAAQ;IAAE,GAAGtG;EAAwB,CAAC;EACxD,MAAMyE,KAAK,GAAGjF,MAAM,CAACiB,OAAO,EAAEgE,KAAK,IAAI,EAAE,CAAC;EAC1C,MAAMwC,OAAO,GAAIxD,EAAE,IAAK;IACpB,MAAMyD,MAAM,GAAGA,CAAA,KAAMzH,SAAS,CAAC,MAAMgF,KAAK,CAAC0C,GAAG,CAAC1D,EAAE,CAACgB,KAAK,CAAC,CAAC;IACzDhB,EAAE,CAAC2D,gBAAgB,CAAC,OAAO,EAAEF,MAAM,EAAE;MAAEG,OAAO,EAAE;IAAK,CAAC,CAAC;IACvD5D,EAAE,CAAC2D,gBAAgB,CAAC,WAAW,EAAEF,MAAM,EAAE;MAAEG,OAAO,EAAE;IAAK,CAAC,CAAC;IAC3D,OAAO,MAAM;MACT5D,EAAE,CAAC6D,mBAAmB,CAAC,OAAO,EAAEJ,MAAM,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;MAC1D5D,EAAE,CAAC6D,mBAAmB,CAAC,WAAW,EAAEJ,MAAM,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IAClE,CAAC;EACL,CAAC;EACDf,QAAQ,CAACC,GAAG,CAAC7G,UAAU,CAAC,CAAC6H,SAAS,CAAC,MAAMR,OAAO,CAAC,CAAC,CAAC;EACnD,IAAIxH,QAAQ,CAAC8G,KAAK,CAAC,EAAE;IACjB1G,MAAM,CAAC,MAAM;MACTc,OAAO,GAAGZ,aAAa,CAACwG,KAAK,CAAC,CAAC,CAAC;MAChCU,OAAO,CAAC,CAAC;MACT,IAAItG,OAAO,EAAE;QACTgE,KAAK,CAAC0C,GAAG,CAAC1G,OAAO,CAACgE,KAAK,CAAC;QACxBsC,OAAO,GAAGE,OAAO,CAACxG,OAAO,CAAC;MAC9B;IACJ,CAAC,EAAEuG,OAAO,CAAC;EACf,CAAC,MACI,IAAIvG,OAAO,EAAE;IACdsG,OAAO,GAAGE,OAAO,CAACxG,OAAO,CAAC;EAC9B;EACAd,MAAM,CAAC,MAAM;IACT,MAAM6H,CAAC,GAAG/C,KAAK,CAAC,CAAC;IACjB,IAAIhE,OAAO,EAAEC,OAAO,CAAC,QAAQ,CAAC,IAAI,gBAAgB,IAAID,OAAO,EAAE;MAC3D,MAAM;QAAEkE,cAAc;QAAEC;MAAa,CAAC,GAAGnE,OAAO;MAChD;AACZ;AACA;AACA;MACYA,OAAO,CAACgE,KAAK,GAAG+C,CAAC;MACjB/G,OAAO,CAACgH,iBAAiB,CAAC9C,cAAc,EAAEC,YAAY,CAAC;IAC3D,CAAC,MACI,IAAInE,OAAO,EAAE;MACdA,OAAO,CAACgE,KAAK,GAAG+C,CAAC;IACrB;EACJ,CAAC,EAAER,OAAO,CAAC;EACX,OAAOvC,KAAK;AAChB;AACA,SAASgC,KAAKA,CAACE,SAAS,EAAE;EACtB,MAAM;IAAEQ;EAAI,CAAC,GAAGO,MAAM,CAACC,wBAAwB,CAAChB,SAAS,EAAE,OAAO,CAAC;EACnEe,MAAM,CAACE,cAAc,CAACjB,SAAS,EAAE,OAAO,EAAE;IACtCQ,GAAGA,CAAChB,MAAM,EAAE;MACR,MAAM1B,KAAK,GAAG,IAAI,CAACA,KAAK;MACxB,MAAMlD,KAAK,GAAG,IAAIsG,WAAW,CAAC,WAAW,EAAE;QAAE1B,MAAM;QAAE2B,OAAO,EAAE;MAAK,CAAC,CAAC;MACrEX,GAAG,CAACY,IAAI,CAAC,IAAI,EAAE5B,MAAM,CAAC;MACtB,IAAI1B,KAAK,KAAK0B,MAAM,EAAE;QAClB,IAAI,CAAC6B,aAAa,CAACzG,KAAK,CAAC;MAC7B;IACJ;EACJ,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0G,eAAeA,CAACxD,KAAK,GAAGjF,MAAM,CAACsF,gBAAgB,CAAC,CAAC,CAACL,KAAK,IAAI,EAAE,CAAC,EAAE;EACrE,MAAMhB,EAAE,GAAGqB,gBAAgB,CAAC,CAAC;EAC7BnF,MAAM,CAAC,MAAM;IACT,IAAI8D,EAAE,CAACgB,KAAK,KAAKA,KAAK,CAAC,CAAC,EAAE;MACtB;IACJ;IACA,MAAM;MAAEE,cAAc;MAAEC;IAAa,CAAC,GAAGnB,EAAE;IAC3CA,EAAE,CAACgB,KAAK,GAAGA,KAAK,CAAC,CAAC;IAClB,IAAIhB,EAAE,CAAC/C,OAAO,CAAC,QAAQ,CAAC,EAAE;MACtB;AACZ;AACA;AACA;MACY+C,EAAE,CAACgE,iBAAiB,CAAC9C,cAAc,EAAEC,YAAY,CAAC;IACtD;EACJ,CAAC,CAAC;EACF,OAAOH,KAAK;AAChB;;AAEA;AACA;AACA;;AAEA,SAASxE,kBAAkB,EAAEuB,kBAAkB,EAAEG,uBAAuB,EAAEK,0BAA0B,EAAEG,qBAAqB,EAAEuB,mBAAmB,EAAEQ,kBAAkB,EAAEG,kBAAkB,EAAES,gBAAgB,EAAEE,kBAAkB,EAAEnE,YAAY,EAAEqE,oBAAoB,EAAElE,gBAAgB,EAAER,UAAU,EAAEc,eAAe,EAAEiE,iBAAiB,EAAEG,WAAW,EAAEtE,aAAa,EAAET,aAAa,EAAEC,cAAc,EAAEkF,oBAAoB,EAAEE,6BAA6B,EAAEI,QAAQ,EAAE6B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}