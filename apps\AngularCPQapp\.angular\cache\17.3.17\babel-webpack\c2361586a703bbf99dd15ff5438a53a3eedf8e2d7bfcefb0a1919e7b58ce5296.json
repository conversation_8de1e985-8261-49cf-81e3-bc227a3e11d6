{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, signal, computed, Component, ChangeDetectionStrategy } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { tuiFallbackValueProvider } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiCreateToken, tuiProvideOptions, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TUI_TEXTFIELD_OPTIONS, TuiTextfieldContent, TuiWithTextfield } from '@taiga-ui/core/components/textfield';\nimport { TuiTooltip } from '@taiga-ui/kit/directives';\nimport { TUI_PASSWORD_TEXTS } from '@taiga-ui/kit/tokens';\n\n/**\n * @deprecated use {@link TuiPassword} with {@link TuiTextfield}\n */\nconst _c0 = [\"tuiInputPassword\", \"\"];\nfunction TuiInputPassword_tui_icon_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tui-icon\", 1);\n    i0.ɵɵlistener(\"click\", function TuiInputPassword_tui_icon_0_Template_tui_icon_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hidden.set(!ctx_r1.hidden()));\n    })(\"mousedown.capture.prevent.stop\", function TuiInputPassword_tui_icon_0_Template_tui_icon_mousedown_capture_prevent_stop_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.el.focus());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"border\", ctx_r1.size() === \"s\" ? null : \"none\");\n    i0.ɵɵproperty(\"icon\", ctx_r1.icon())(\"tuiTooltip\", ctx_r1.text());\n  }\n}\nconst TUI_INPUT_PASSWORD_DEFAULT_OPTIONS = {\n  icons: {\n    hide: '@tui.eye-off',\n    show: '@tui.eye'\n  }\n};\n/**\n * @deprecated use {@link TuiPassword} with {@link TuiTextfield}\n */\nconst TUI_INPUT_PASSWORD_OPTIONS = tuiCreateToken(TUI_INPUT_PASSWORD_DEFAULT_OPTIONS);\n/**\n * @deprecated use {@link TuiPassword} with {@link TuiTextfield}\n */\nfunction tuiInputPasswordOptionsProvider(options) {\n  return tuiProvideOptions(TUI_INPUT_PASSWORD_OPTIONS, options, TUI_INPUT_PASSWORD_DEFAULT_OPTIONS);\n}\n\n/**\n * @deprecated use {@link TuiPassword} with {@link TuiTextfield}\n */\nclass TuiInputPassword {\n  constructor() {\n    this.options = inject(TUI_INPUT_PASSWORD_OPTIONS);\n    this.texts = toSignal(inject(TUI_PASSWORD_TEXTS), {\n      initialValue: ['', '']\n    });\n    this.el = tuiInjectElement();\n    this.size = inject(TUI_TEXTFIELD_OPTIONS).size;\n    this.hidden = signal(true);\n    this.text = computed(() => this.hidden() ? this.texts()[0] : this.texts()[1]);\n    this.icon = computed((size = this.size()) => {\n      const icon = this.hidden() ? this.options.icons.show : this.options.icons.hide;\n      return tuiIsString(icon) ? icon : icon(size);\n    });\n  }\n  static {\n    this.ɵfac = function TuiInputPassword_Factory(t) {\n      return new (t || TuiInputPassword)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiInputPassword,\n      selectors: [[\"input\", \"tuiInputPassword\", \"\"]],\n      hostAttrs: [\"ngSkipHydration\", \"true\"],\n      hostVars: 1,\n      hostBindings: function TuiInputPassword_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"type\", ctx.hidden() ? \"password\" : \"text\");\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiFallbackValueProvider('')]), i0.ɵɵHostDirectivesFeature([i1.TuiWithTextfield]), i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 1,\n      vars: 0,\n      consts: [[3, \"icon\", \"border\", \"tuiTooltip\", \"click\", \"mousedown.capture.prevent.stop\", 4, \"tuiTextfieldContent\"], [3, \"click\", \"mousedown.capture.prevent.stop\", \"icon\", \"tuiTooltip\"]],\n      template: function TuiInputPassword_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiInputPassword_tui_icon_0_Template, 1, 4, \"tui-icon\", 0);\n        }\n      },\n      dependencies: [TuiIcon, TuiTextfieldContent, TuiTooltip],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputPassword, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiInputPassword]',\n      imports: [TuiIcon, TuiTextfieldContent, TuiTooltip],\n      template: `\n        <tui-icon\n            *tuiTextfieldContent\n            [icon]=\"icon()\"\n            [style.border]=\"size() === 's' ? null : 'none'\"\n            [tuiTooltip]=\"text()\"\n            (click)=\"hidden.set(!hidden())\"\n            (mousedown.capture.prevent.stop)=\"el.focus()\"\n        />\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiFallbackValueProvider('')],\n      hostDirectives: [TuiWithTextfield],\n      host: {\n        ngSkipHydration: 'true',\n        '[type]': 'hidden() ? \"password\" : \"text\"'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_PASSWORD_DEFAULT_OPTIONS, TUI_INPUT_PASSWORD_OPTIONS, TuiInputPassword, tuiInputPasswordOptionsProvider };", "map": {"version": 3, "names": ["i0", "inject", "signal", "computed", "Component", "ChangeDetectionStrategy", "toSignal", "tuiFallback<PERSON><PERSON>ue<PERSON>", "tuiInjectElement", "tuiCreateToken", "tuiProvideOptions", "tuiIsString", "TuiIcon", "i1", "TUI_TEXTFIELD_OPTIONS", "TuiTextfieldContent", "TuiWithTextfield", "TuiTooltip", "TUI_PASSWORD_TEXTS", "_c0", "TuiInputPassword_tui_icon_0_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TuiInputPassword_tui_icon_0_Template_tui_icon_click_0_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "hidden", "set", "TuiInputPassword_tui_icon_0_Template_tui_icon_mousedown_capture_prevent_stop_0_listener", "el", "focus", "ɵɵelementEnd", "ɵɵstyleProp", "size", "ɵɵproperty", "icon", "text", "TUI_INPUT_PASSWORD_DEFAULT_OPTIONS", "icons", "hide", "show", "TUI_INPUT_PASSWORD_OPTIONS", "tuiInputPasswordOptionsProvider", "options", "TuiInputPassword", "constructor", "texts", "initialValue", "ɵfac", "TuiInputPassword_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "TuiInputPassword_HostBindings", "ɵɵhostProperty", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "consts", "template", "TuiInputPassword_Template", "ɵɵtemplate", "dependencies", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "hostDirectives", "host", "ngSkipHydration"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-input-password.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, signal, computed, Component, ChangeDetectionStrategy } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { tuiFallbackValueProvider } from '@taiga-ui/cdk/tokens';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiCreateToken, tuiProvideOptions, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TUI_TEXTFIELD_OPTIONS, TuiTextfieldContent, TuiWithTextfield } from '@taiga-ui/core/components/textfield';\nimport { TuiTooltip } from '@taiga-ui/kit/directives';\nimport { TUI_PASSWORD_TEXTS } from '@taiga-ui/kit/tokens';\n\n/**\n * @deprecated use {@link TuiPassword} with {@link TuiTextfield}\n */\nconst TUI_INPUT_PASSWORD_DEFAULT_OPTIONS = {\n    icons: {\n        hide: '@tui.eye-off',\n        show: '@tui.eye',\n    },\n};\n/**\n * @deprecated use {@link TuiPassword} with {@link TuiTextfield}\n */\nconst TUI_INPUT_PASSWORD_OPTIONS = tuiCreateToken(TUI_INPUT_PASSWORD_DEFAULT_OPTIONS);\n/**\n * @deprecated use {@link TuiPassword} with {@link TuiTextfield}\n */\nfunction tuiInputPasswordOptionsProvider(options) {\n    return tuiProvideOptions(TUI_INPUT_PASSWORD_OPTIONS, options, TUI_INPUT_PASSWORD_DEFAULT_OPTIONS);\n}\n\n/**\n * @deprecated use {@link TuiPassword} with {@link TuiTextfield}\n */\nclass TuiInputPassword {\n    constructor() {\n        this.options = inject(TUI_INPUT_PASSWORD_OPTIONS);\n        this.texts = toSignal(inject(TUI_PASSWORD_TEXTS), {\n            initialValue: ['', ''],\n        });\n        this.el = tuiInjectElement();\n        this.size = inject(TUI_TEXTFIELD_OPTIONS).size;\n        this.hidden = signal(true);\n        this.text = computed(() => this.hidden() ? this.texts()[0] : this.texts()[1]);\n        this.icon = computed((size = this.size()) => {\n            const icon = this.hidden() ? this.options.icons.show : this.options.icons.hide;\n            return tuiIsString(icon) ? icon : icon(size);\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputPassword, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputPassword, isStandalone: true, selector: \"input[tuiInputPassword]\", host: { attributes: { \"ngSkipHydration\": \"true\" }, properties: { \"type\": \"hidden() ? \\\"password\\\" : \\\"text\\\"\" } }, providers: [tuiFallbackValueProvider('')], hostDirectives: [{ directive: i1.TuiWithTextfield }], ngImport: i0, template: `\n        <tui-icon\n            *tuiTextfieldContent\n            [icon]=\"icon()\"\n            [style.border]=\"size() === 's' ? null : 'none'\"\n            [tuiTooltip]=\"text()\"\n            (click)=\"hidden.set(!hidden())\"\n            (mousedown.capture.prevent.stop)=\"el.focus()\"\n        />\n    `, isInline: true, dependencies: [{ kind: \"component\", type: TuiIcon, selector: \"tui-icon\", inputs: [\"icon\", \"background\"] }, { kind: \"directive\", type: TuiTextfieldContent, selector: \"ng-template[tuiTextfieldContent]\" }, { kind: \"directive\", type: TuiTooltip, selector: \"tui-icon[tuiTooltip]\", inputs: [\"size\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputPassword, decorators: [{\n            type: Component,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiInputPassword]',\n                    imports: [TuiIcon, TuiTextfieldContent, TuiTooltip],\n                    template: `\n        <tui-icon\n            *tuiTextfieldContent\n            [icon]=\"icon()\"\n            [style.border]=\"size() === 's' ? null : 'none'\"\n            [tuiTooltip]=\"text()\"\n            (click)=\"hidden.set(!hidden())\"\n            (mousedown.capture.prevent.stop)=\"el.focus()\"\n        />\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    providers: [tuiFallbackValueProvider('')],\n                    hostDirectives: [TuiWithTextfield],\n                    host: {\n                        ngSkipHydration: 'true',\n                        '[type]': 'hidden() ? \"password\" : \"text\"',\n                    },\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_INPUT_PASSWORD_DEFAULT_OPTIONS, TUI_INPUT_PASSWORD_OPTIONS, TuiInputPassword, tuiInputPasswordOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,uBAAuB,QAAQ,eAAe;AAC5F,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,WAAW,QAAQ,mCAAmC;AAClG,SAASC,OAAO,QAAQ,gCAAgC;AACxD,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,qBAAqB,EAAEC,mBAAmB,EAAEC,gBAAgB,QAAQ,qCAAqC;AAClH,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,kBAAkB,QAAQ,sBAAsB;;AAEzD;AACA;AACA;AAFA,MAAAC,GAAA;AAAA,SAAAC,qCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAsCqGvB,EAAE,CAAAwB,gBAAA;IAAFxB,EAAE,CAAAyB,cAAA,iBAS9F,CAAC;IAT2FzB,EAAE,CAAA0B,UAAA,mBAAAC,+DAAA;MAAF3B,EAAE,CAAA4B,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAF7B,EAAE,CAAA8B,aAAA;MAAA,OAAF9B,EAAE,CAAA+B,WAAA,CAOlFF,MAAA,CAAAG,MAAA,CAAAC,GAAA,EAAYJ,MAAA,CAAAG,MAAA,CAAO,CAAC,CAAC;IAAA,EAAC,4CAAAE,wFAAA;MAP0DlC,EAAE,CAAA4B,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAF7B,EAAE,CAAA8B,aAAA;MAAA,OAAF9B,EAAE,CAAA+B,WAAA,CAQzDF,MAAA,CAAAM,EAAA,CAAAC,KAAA,CAAS,CAAC;IAAA,EAAC;IAR4CpC,EAAE,CAAAqC,YAAA,CAS9F,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAQ,MAAA,GAT2F7B,EAAE,CAAA8B,aAAA;IAAF9B,EAAE,CAAAsC,WAAA,WAAAT,MAAA,CAAAU,IAAA,0BAK7C,CAAC;IAL0CvC,EAAE,CAAAwC,UAAA,SAAAX,MAAA,CAAAY,IAAA,EAI7E,CAAC,eAAAZ,MAAA,CAAAa,IAAA,EAEK,CAAC;EAAA;AAAA;AAzCjC,MAAMC,kCAAkC,GAAG;EACvCC,KAAK,EAAE;IACHC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE;EACV;AACJ,CAAC;AACD;AACA;AACA;AACA,MAAMC,0BAA0B,GAAGtC,cAAc,CAACkC,kCAAkC,CAAC;AACrF;AACA;AACA;AACA,SAASK,+BAA+BA,CAACC,OAAO,EAAE;EAC9C,OAAOvC,iBAAiB,CAACqC,0BAA0B,EAAEE,OAAO,EAAEN,kCAAkC,CAAC;AACrG;;AAEA;AACA;AACA;AACA,MAAMO,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,OAAO,GAAGhD,MAAM,CAAC8C,0BAA0B,CAAC;IACjD,IAAI,CAACK,KAAK,GAAG9C,QAAQ,CAACL,MAAM,CAACiB,kBAAkB,CAAC,EAAE;MAC9CmC,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE;IACzB,CAAC,CAAC;IACF,IAAI,CAAClB,EAAE,GAAG3B,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAAC+B,IAAI,GAAGtC,MAAM,CAACa,qBAAqB,CAAC,CAACyB,IAAI;IAC9C,IAAI,CAACP,MAAM,GAAG9B,MAAM,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACwC,IAAI,GAAGvC,QAAQ,CAAC,MAAM,IAAI,CAAC6B,MAAM,CAAC,CAAC,GAAG,IAAI,CAACoB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7E,IAAI,CAACX,IAAI,GAAGtC,QAAQ,CAAC,CAACoC,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC,KAAK;MACzC,MAAME,IAAI,GAAG,IAAI,CAACT,MAAM,CAAC,CAAC,GAAG,IAAI,CAACiB,OAAO,CAACL,KAAK,CAACE,IAAI,GAAG,IAAI,CAACG,OAAO,CAACL,KAAK,CAACC,IAAI;MAC9E,OAAOlC,WAAW,CAAC8B,IAAI,CAAC,GAAGA,IAAI,GAAGA,IAAI,CAACF,IAAI,CAAC;IAChD,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACe,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFN,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACO,IAAI,kBAD+EzD,EAAE,CAAA0D,iBAAA;MAAAC,IAAA,EACJT,gBAAgB;MAAAU,SAAA;MAAAC,SAAA,sBAAoG,MAAM;MAAAC,QAAA;MAAAC,YAAA,WAAAC,8BAAA3C,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADxHrB,EAAE,CAAAiE,cAAA,SACJ3C,GAAA,CAAAU,MAAA,CAAO,CAAC,GAAG,UAAU,GAAG,MAAT,CAAC;QAAA;MAAA;MAAAkC,UAAA;MAAAC,QAAA,GADdnE,EAAE,CAAAoE,kBAAA,CACqM,CAAC7D,wBAAwB,CAAC,EAAE,CAAC,CAAC,GADrOP,EAAE,CAAAqE,uBAAA,EACmQxD,EAAE,CAACG,gBAAgB,IADxRhB,EAAE,CAAAsE,mBAAA;MAAAC,KAAA,EAAApD,GAAA;MAAAqD,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAvD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrB,EAAE,CAAA6E,UAAA,IAAAzD,oCAAA,qBAS9F,CAAC;QAAA;MAAA;MAAA0D,YAAA,GACuDlE,OAAO,EAAqFG,mBAAmB,EAA6EE,UAAU;MAAA8D,aAAA;MAAAC,eAAA;IAAA,EAA8G;EAAE;AACvX;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAZqGjF,EAAE,CAAAkF,iBAAA,CAYXhC,gBAAgB,EAAc,CAAC;IAC/GS,IAAI,EAAEvD,SAAS;IACf+E,IAAI,EAAE,CAAC;MACCjB,UAAU,EAAE,IAAI;MAChBkB,QAAQ,EAAE,yBAAyB;MACnCC,OAAO,EAAE,CAACzE,OAAO,EAAEG,mBAAmB,EAAEE,UAAU,CAAC;MACnD0D,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MACeK,eAAe,EAAE3E,uBAAuB,CAACiF,MAAM;MAC/CC,SAAS,EAAE,CAAChF,wBAAwB,CAAC,EAAE,CAAC,CAAC;MACzCiF,cAAc,EAAE,CAACxE,gBAAgB,CAAC;MAClCyE,IAAI,EAAE;QACFC,eAAe,EAAE,MAAM;QACvB,QAAQ,EAAE;MACd;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS/C,kCAAkC,EAAEI,0BAA0B,EAAEG,gBAAgB,EAAEF,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}