{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, Input } from '@angular/core';\nimport { tuiCreateToken, tuiProvideOptions, tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nconst TUI_GROUP_DEFAULT_OPTIONS = {\n  size: 'l',\n  collapsed: false,\n  rounded: true,\n  orientation: 'horizontal'\n};\nconst TUI_GROUP_OPTIONS = tuiCreateToken(TUI_GROUP_DEFAULT_OPTIONS);\nfunction tuiGroupOptionsProvider(options) {\n  return tuiProvideOptions(TUI_GROUP_OPTIONS, options, TUI_GROUP_DEFAULT_OPTIONS);\n}\nclass TuiGroupStyles {\n  static {\n    this.ɵfac = function TuiGroupStyles_Factory(t) {\n      return new (t || TuiGroupStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiGroupStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-group\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiGroupStyles_Template(rf, ctx) {},\n      styles: [\"[tuiGroup]{position:relative;display:flex;transform:translateZ(0);--t-group-radius: var(--tui-radius-l);--t-group-margin: -1px;--t-group-mask: linear-gradient(to right, rgba(0, 0, 0, .5) 1px, #000 2px, #000 calc(100% - 2px) , rgba(0, 0, 0, .5));--t-group-mask-end: linear-gradient(to right, rgba(0, 0, 0, .5) 1px, #000 2px);--t-group-mask-start: linear-gradient(to right, #000 calc(100% - 2px) , rgba(0, 0, 0, .5) calc(100% - 1px) )}[tuiGroup]>*{z-index:1;flex:1 1 0;min-inline-size:0;-webkit-mask:var(--t-group-mask);mask:var(--t-group-mask);-webkit-mask-clip:no-clip;mask-clip:no-clip}[tuiGroup]>*:disabled,[tuiGroup]>*._disabled{z-index:0}[tuiGroup]>*:invalid:not([data-mode]),[tuiGroup]>*[data-mode~=invalid]{z-index:2;--t-group-mask: none}[tuiGroup]>*:has(:invalid:not([data-mode])),[tuiGroup]>*:has([data-mode~=invalid]){z-index:2;--t-group-mask: none}[tuiGroup]>*:has(:focus-visible){z-index:3;--t-group-mask: none}[tuiGroup]>*:has([data-focus=true]){z-index:3;--t-group-mask: none}[tuiGroup]>*:checked:not([data-mode]),[tuiGroup]>*[data-mode~=checked]{z-index:4;--t-group-mask: none}[tuiGroup]>*:has([tuiBlock]:checked){z-index:4;--t-group-mask: none}[tuiGroup]>*:not(:last-child){margin-inline-end:var(--t-group-margin)}[tuiGroup]>*:nth-child(n){border-radius:0}[tuiGroup]>*:first-child{border-radius:var(--t-group-radius) 0 0 var(--t-group-radius);-webkit-mask-image:var(--t-group-mask-start);mask-image:var(--t-group-mask-start)}[tuiGroup]>*:last-child{border-radius:0 var(--t-group-radius) var(--t-group-radius) 0;-webkit-mask-image:var(--t-group-mask-end);mask-image:var(--t-group-mask-end)}[tuiGroup]>*:only-child{border-radius:var(--t-group-radius);-webkit-mask:none;mask:none}[tuiGroup][data-size=s],[tuiGroup][data-size=m]{--t-group-radius: var(--tui-radius-m)}[tuiGroup][data-orientation=vertical]{display:inline-flex;flex-direction:column;--t-group-mask: linear-gradient(to bottom, rgba(0, 0, 0, .5) 1px, #000 2px, #000 calc(100% - 2px) , rgba(0, 0, 0, .5));--t-group-mask-start: linear-gradient(to bottom, #000 calc(100% - 2px) , rgba(0, 0, 0, .5) calc(100% - 1px) );--t-group-mask-end: linear-gradient(to bottom, rgba(0, 0, 0, .5) 1px, #000 2px)}[tuiGroup][data-orientation=vertical]>*{min-block-size:auto;flex:0 0 auto}[tuiGroup][data-orientation=vertical]>*:not(:last-child){margin-inline-end:0;margin-block-end:var(--t-group-margin)}[tuiGroup][data-orientation=vertical]>*:first-child{border-radius:var(--t-group-radius) var(--t-group-radius) 0 0}[tuiGroup][data-orientation=vertical]>*:last-child{border-radius:0 0 var(--t-group-radius) var(--t-group-radius)}[tuiGroup][data-orientation=vertical]>*:only-child{border-radius:var(--t-group-radius)}.tui-group{position:relative;display:flex;isolation:isolate;--t-group-radius: var(--tui-radius-m)}.tui-group>*{flex:1 1 0;min-inline-size:0}.tui-group>*:not(:last-child){margin-right:.125rem}.tui-group.tui-group>*:nth-child(n){border-radius:0}.tui-group_radius_large{--t-group-radius: var(--tui-radius-l)}.tui-group_collapsed>*{z-index:1}.tui-group_collapsed>*:not(:last-child){margin:0 -1px 0 0}.tui-group_collapsed>*._readonly,.tui-group_collapsed>*._disabled,.tui-group_collapsed>*._readonly:hover,.tui-group_collapsed>*._disabled:hover{z-index:0}.tui-group_collapsed>*._invalid{z-index:2}.tui-group_collapsed>*._invalid:hover,.tui-group_collapsed>*._invalid._hovered,.tui-group_collapsed>*._invalid._pressed{z-index:4}.tui-group_collapsed>*:hover,.tui-group_collapsed>*._hovered,.tui-group_collapsed>*._pressed{z-index:3}.tui-group_collapsed>*._hosted_dropdown_focused,.tui-group_collapsed>*._focus-visible,.tui-group_collapsed>*._focused.ng-touched,.tui-group_collapsed>*._focused.ng-untouched{z-index:5}.tui-group_collapsed>*._active,.tui-group_collapsed>*[data-appearance=whiteblock-active]{z-index:6}.tui-group_collapsed>*:has([tuiBlock]:checked){z-index:6}.tui-group_collapsed>*._focus-visible._focused,.tui-group_collapsed>*._focus-visible._active,.tui-group_collapsed>*._focus-visible[data-appearance=whiteblock-active]{z-index:7}.tui-group_orientation_vertical{display:inline-flex;flex-direction:column}.tui-group_orientation_vertical>*{min-block-size:auto;flex:0 0 auto}.tui-group_orientation_vertical>*:not(:last-child){margin-right:0;margin-bottom:.125rem}.tui-group_orientation_vertical.tui-group_collapsed>*:not(:last-child){margin:0 0 -1px}.tui-group_rounded.tui-group_orientation_horizontal>*:first-child{border-top-left-radius:var(--t-group-radius);border-bottom-left-radius:var(--t-group-radius)}.tui-group_rounded.tui-group_orientation_horizontal>*:last-child{border-top-right-radius:var(--t-group-radius);border-bottom-right-radius:var(--t-group-radius)}.tui-group_rounded.tui-group_orientation_vertical>*:first-child{border-top-left-radius:var(--t-group-radius);border-top-right-radius:var(--t-group-radius)}.tui-group_rounded.tui-group_orientation_vertical>*:last-child{border-bottom-left-radius:var(--t-group-radius);border-bottom-right-radius:var(--t-group-radius)}.tui-group__auto-width-item{min-inline-size:auto;flex:0 0 auto}.tui-group__inherit-item{border-radius:inherit!important}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiGroupStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-group'\n      },\n      styles: [\"[tuiGroup]{position:relative;display:flex;transform:translateZ(0);--t-group-radius: var(--tui-radius-l);--t-group-margin: -1px;--t-group-mask: linear-gradient(to right, rgba(0, 0, 0, .5) 1px, #000 2px, #000 calc(100% - 2px) , rgba(0, 0, 0, .5));--t-group-mask-end: linear-gradient(to right, rgba(0, 0, 0, .5) 1px, #000 2px);--t-group-mask-start: linear-gradient(to right, #000 calc(100% - 2px) , rgba(0, 0, 0, .5) calc(100% - 1px) )}[tuiGroup]>*{z-index:1;flex:1 1 0;min-inline-size:0;-webkit-mask:var(--t-group-mask);mask:var(--t-group-mask);-webkit-mask-clip:no-clip;mask-clip:no-clip}[tuiGroup]>*:disabled,[tuiGroup]>*._disabled{z-index:0}[tuiGroup]>*:invalid:not([data-mode]),[tuiGroup]>*[data-mode~=invalid]{z-index:2;--t-group-mask: none}[tuiGroup]>*:has(:invalid:not([data-mode])),[tuiGroup]>*:has([data-mode~=invalid]){z-index:2;--t-group-mask: none}[tuiGroup]>*:has(:focus-visible){z-index:3;--t-group-mask: none}[tuiGroup]>*:has([data-focus=true]){z-index:3;--t-group-mask: none}[tuiGroup]>*:checked:not([data-mode]),[tuiGroup]>*[data-mode~=checked]{z-index:4;--t-group-mask: none}[tuiGroup]>*:has([tuiBlock]:checked){z-index:4;--t-group-mask: none}[tuiGroup]>*:not(:last-child){margin-inline-end:var(--t-group-margin)}[tuiGroup]>*:nth-child(n){border-radius:0}[tuiGroup]>*:first-child{border-radius:var(--t-group-radius) 0 0 var(--t-group-radius);-webkit-mask-image:var(--t-group-mask-start);mask-image:var(--t-group-mask-start)}[tuiGroup]>*:last-child{border-radius:0 var(--t-group-radius) var(--t-group-radius) 0;-webkit-mask-image:var(--t-group-mask-end);mask-image:var(--t-group-mask-end)}[tuiGroup]>*:only-child{border-radius:var(--t-group-radius);-webkit-mask:none;mask:none}[tuiGroup][data-size=s],[tuiGroup][data-size=m]{--t-group-radius: var(--tui-radius-m)}[tuiGroup][data-orientation=vertical]{display:inline-flex;flex-direction:column;--t-group-mask: linear-gradient(to bottom, rgba(0, 0, 0, .5) 1px, #000 2px, #000 calc(100% - 2px) , rgba(0, 0, 0, .5));--t-group-mask-start: linear-gradient(to bottom, #000 calc(100% - 2px) , rgba(0, 0, 0, .5) calc(100% - 1px) );--t-group-mask-end: linear-gradient(to bottom, rgba(0, 0, 0, .5) 1px, #000 2px)}[tuiGroup][data-orientation=vertical]>*{min-block-size:auto;flex:0 0 auto}[tuiGroup][data-orientation=vertical]>*:not(:last-child){margin-inline-end:0;margin-block-end:var(--t-group-margin)}[tuiGroup][data-orientation=vertical]>*:first-child{border-radius:var(--t-group-radius) var(--t-group-radius) 0 0}[tuiGroup][data-orientation=vertical]>*:last-child{border-radius:0 0 var(--t-group-radius) var(--t-group-radius)}[tuiGroup][data-orientation=vertical]>*:only-child{border-radius:var(--t-group-radius)}.tui-group{position:relative;display:flex;isolation:isolate;--t-group-radius: var(--tui-radius-m)}.tui-group>*{flex:1 1 0;min-inline-size:0}.tui-group>*:not(:last-child){margin-right:.125rem}.tui-group.tui-group>*:nth-child(n){border-radius:0}.tui-group_radius_large{--t-group-radius: var(--tui-radius-l)}.tui-group_collapsed>*{z-index:1}.tui-group_collapsed>*:not(:last-child){margin:0 -1px 0 0}.tui-group_collapsed>*._readonly,.tui-group_collapsed>*._disabled,.tui-group_collapsed>*._readonly:hover,.tui-group_collapsed>*._disabled:hover{z-index:0}.tui-group_collapsed>*._invalid{z-index:2}.tui-group_collapsed>*._invalid:hover,.tui-group_collapsed>*._invalid._hovered,.tui-group_collapsed>*._invalid._pressed{z-index:4}.tui-group_collapsed>*:hover,.tui-group_collapsed>*._hovered,.tui-group_collapsed>*._pressed{z-index:3}.tui-group_collapsed>*._hosted_dropdown_focused,.tui-group_collapsed>*._focus-visible,.tui-group_collapsed>*._focused.ng-touched,.tui-group_collapsed>*._focused.ng-untouched{z-index:5}.tui-group_collapsed>*._active,.tui-group_collapsed>*[data-appearance=whiteblock-active]{z-index:6}.tui-group_collapsed>*:has([tuiBlock]:checked){z-index:6}.tui-group_collapsed>*._focus-visible._focused,.tui-group_collapsed>*._focus-visible._active,.tui-group_collapsed>*._focus-visible[data-appearance=whiteblock-active]{z-index:7}.tui-group_orientation_vertical{display:inline-flex;flex-direction:column}.tui-group_orientation_vertical>*{min-block-size:auto;flex:0 0 auto}.tui-group_orientation_vertical>*:not(:last-child){margin-right:0;margin-bottom:.125rem}.tui-group_orientation_vertical.tui-group_collapsed>*:not(:last-child){margin:0 0 -1px}.tui-group_rounded.tui-group_orientation_horizontal>*:first-child{border-top-left-radius:var(--t-group-radius);border-bottom-left-radius:var(--t-group-radius)}.tui-group_rounded.tui-group_orientation_horizontal>*:last-child{border-top-right-radius:var(--t-group-radius);border-bottom-right-radius:var(--t-group-radius)}.tui-group_rounded.tui-group_orientation_vertical>*:first-child{border-top-left-radius:var(--t-group-radius);border-top-right-radius:var(--t-group-radius)}.tui-group_rounded.tui-group_orientation_vertical>*:last-child{border-bottom-left-radius:var(--t-group-radius);border-bottom-right-radius:var(--t-group-radius)}.tui-group__auto-width-item{min-inline-size:auto;flex:0 0 auto}.tui-group__inherit-item{border-radius:inherit!important}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiGroup {\n  constructor() {\n    this.options = inject(TUI_GROUP_OPTIONS);\n    this.nothing = tuiWithStyles(TuiGroupStyles);\n    this.orientation = this.options.orientation;\n    this.collapsed = this.options.collapsed;\n    this.rounded = this.options.rounded;\n    this.size = this.options.size;\n  }\n  static {\n    this.ɵfac = function TuiGroup_Factory(t) {\n      return new (t || TuiGroup)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiGroup,\n      selectors: [[\"\", \"tuiGroup\", \"\", 5, \"ng-container\"]],\n      hostAttrs: [\"tuiGroup\", \"\"],\n      hostVars: 12,\n      hostBindings: function TuiGroup_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-orientation\", ctx.orientation)(\"data-size\", ctx.size);\n          i0.ɵɵstyleProp(\"--t-group-radius\", ctx.rounded ? null : 0)(\"--t-group-margin\", ctx.collapsed ? null : 0.125, \"rem\")(\"--t-group-mask\", ctx.collapsed ? null : \"none\")(\"--t-group-mask-start\", ctx.collapsed ? null : \"none\")(\"--t-group-mask-end\", ctx.collapsed ? null : \"none\");\n        }\n      },\n      inputs: {\n        orientation: \"orientation\",\n        collapsed: \"collapsed\",\n        rounded: \"rounded\",\n        size: \"size\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiGroup, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiGroup]:not(ng-container)',\n      host: {\n        tuiGroup: '',\n        '[attr.data-orientation]': 'orientation',\n        '[attr.data-size]': 'size',\n        '[style.--t-group-radius]': 'rounded ? null : 0',\n        '[style.--t-group-margin.rem]': 'collapsed ? null : 0.125',\n        '[style.--t-group-mask]': 'collapsed ? null : \"none\"',\n        '[style.--t-group-mask-start]': 'collapsed ? null : \"none\"',\n        '[style.--t-group-mask-end]': 'collapsed ? null : \"none\"'\n      }\n    }]\n  }], null, {\n    orientation: [{\n      type: Input\n    }],\n    collapsed: [{\n      type: Input\n    }],\n    rounded: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_GROUP_DEFAULT_OPTIONS, TUI_GROUP_OPTIONS, TuiGroup, tuiGroupOptionsProvider };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "Directive", "Input", "tuiCreateToken", "tuiProvideOptions", "tuiWithStyles", "TUI_GROUP_DEFAULT_OPTIONS", "size", "collapsed", "rounded", "orientation", "TUI_GROUP_OPTIONS", "tuiGroupOptionsProvider", "options", "TuiGroupStyles", "ɵfac", "TuiGroupStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiGroupStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiGroup", "constructor", "nothing", "TuiGroup_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiGroup_HostBindings", "ɵɵattribute", "ɵɵstyleProp", "inputs", "selector", "tuiGroup"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-directives-group.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, Input } from '@angular/core';\nimport { tuiCreateToken, tuiProvideOptions, tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\n\nconst TUI_GROUP_DEFAULT_OPTIONS = {\n    size: 'l',\n    collapsed: false,\n    rounded: true,\n    orientation: 'horizontal',\n};\nconst TUI_GROUP_OPTIONS = tuiCreateToken(TUI_GROUP_DEFAULT_OPTIONS);\nfunction tuiGroupOptionsProvider(options) {\n    return tuiProvideOptions(TUI_GROUP_OPTIONS, options, TUI_GROUP_DEFAULT_OPTIONS);\n}\n\nclass TuiGroupStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiGroupStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiGroupStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-group\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiGroup]{position:relative;display:flex;transform:translateZ(0);--t-group-radius: var(--tui-radius-l);--t-group-margin: -1px;--t-group-mask: linear-gradient(to right, rgba(0, 0, 0, .5) 1px, #000 2px, #000 calc(100% - 2px) , rgba(0, 0, 0, .5));--t-group-mask-end: linear-gradient(to right, rgba(0, 0, 0, .5) 1px, #000 2px);--t-group-mask-start: linear-gradient(to right, #000 calc(100% - 2px) , rgba(0, 0, 0, .5) calc(100% - 1px) )}[tuiGroup]>*{z-index:1;flex:1 1 0;min-inline-size:0;-webkit-mask:var(--t-group-mask);mask:var(--t-group-mask);-webkit-mask-clip:no-clip;mask-clip:no-clip}[tuiGroup]>*:disabled,[tuiGroup]>*._disabled{z-index:0}[tuiGroup]>*:invalid:not([data-mode]),[tuiGroup]>*[data-mode~=invalid]{z-index:2;--t-group-mask: none}[tuiGroup]>*:has(:invalid:not([data-mode])),[tuiGroup]>*:has([data-mode~=invalid]){z-index:2;--t-group-mask: none}[tuiGroup]>*:has(:focus-visible){z-index:3;--t-group-mask: none}[tuiGroup]>*:has([data-focus=true]){z-index:3;--t-group-mask: none}[tuiGroup]>*:checked:not([data-mode]),[tuiGroup]>*[data-mode~=checked]{z-index:4;--t-group-mask: none}[tuiGroup]>*:has([tuiBlock]:checked){z-index:4;--t-group-mask: none}[tuiGroup]>*:not(:last-child){margin-inline-end:var(--t-group-margin)}[tuiGroup]>*:nth-child(n){border-radius:0}[tuiGroup]>*:first-child{border-radius:var(--t-group-radius) 0 0 var(--t-group-radius);-webkit-mask-image:var(--t-group-mask-start);mask-image:var(--t-group-mask-start)}[tuiGroup]>*:last-child{border-radius:0 var(--t-group-radius) var(--t-group-radius) 0;-webkit-mask-image:var(--t-group-mask-end);mask-image:var(--t-group-mask-end)}[tuiGroup]>*:only-child{border-radius:var(--t-group-radius);-webkit-mask:none;mask:none}[tuiGroup][data-size=s],[tuiGroup][data-size=m]{--t-group-radius: var(--tui-radius-m)}[tuiGroup][data-orientation=vertical]{display:inline-flex;flex-direction:column;--t-group-mask: linear-gradient(to bottom, rgba(0, 0, 0, .5) 1px, #000 2px, #000 calc(100% - 2px) , rgba(0, 0, 0, .5));--t-group-mask-start: linear-gradient(to bottom, #000 calc(100% - 2px) , rgba(0, 0, 0, .5) calc(100% - 1px) );--t-group-mask-end: linear-gradient(to bottom, rgba(0, 0, 0, .5) 1px, #000 2px)}[tuiGroup][data-orientation=vertical]>*{min-block-size:auto;flex:0 0 auto}[tuiGroup][data-orientation=vertical]>*:not(:last-child){margin-inline-end:0;margin-block-end:var(--t-group-margin)}[tuiGroup][data-orientation=vertical]>*:first-child{border-radius:var(--t-group-radius) var(--t-group-radius) 0 0}[tuiGroup][data-orientation=vertical]>*:last-child{border-radius:0 0 var(--t-group-radius) var(--t-group-radius)}[tuiGroup][data-orientation=vertical]>*:only-child{border-radius:var(--t-group-radius)}.tui-group{position:relative;display:flex;isolation:isolate;--t-group-radius: var(--tui-radius-m)}.tui-group>*{flex:1 1 0;min-inline-size:0}.tui-group>*:not(:last-child){margin-right:.125rem}.tui-group.tui-group>*:nth-child(n){border-radius:0}.tui-group_radius_large{--t-group-radius: var(--tui-radius-l)}.tui-group_collapsed>*{z-index:1}.tui-group_collapsed>*:not(:last-child){margin:0 -1px 0 0}.tui-group_collapsed>*._readonly,.tui-group_collapsed>*._disabled,.tui-group_collapsed>*._readonly:hover,.tui-group_collapsed>*._disabled:hover{z-index:0}.tui-group_collapsed>*._invalid{z-index:2}.tui-group_collapsed>*._invalid:hover,.tui-group_collapsed>*._invalid._hovered,.tui-group_collapsed>*._invalid._pressed{z-index:4}.tui-group_collapsed>*:hover,.tui-group_collapsed>*._hovered,.tui-group_collapsed>*._pressed{z-index:3}.tui-group_collapsed>*._hosted_dropdown_focused,.tui-group_collapsed>*._focus-visible,.tui-group_collapsed>*._focused.ng-touched,.tui-group_collapsed>*._focused.ng-untouched{z-index:5}.tui-group_collapsed>*._active,.tui-group_collapsed>*[data-appearance=whiteblock-active]{z-index:6}.tui-group_collapsed>*:has([tuiBlock]:checked){z-index:6}.tui-group_collapsed>*._focus-visible._focused,.tui-group_collapsed>*._focus-visible._active,.tui-group_collapsed>*._focus-visible[data-appearance=whiteblock-active]{z-index:7}.tui-group_orientation_vertical{display:inline-flex;flex-direction:column}.tui-group_orientation_vertical>*{min-block-size:auto;flex:0 0 auto}.tui-group_orientation_vertical>*:not(:last-child){margin-right:0;margin-bottom:.125rem}.tui-group_orientation_vertical.tui-group_collapsed>*:not(:last-child){margin:0 0 -1px}.tui-group_rounded.tui-group_orientation_horizontal>*:first-child{border-top-left-radius:var(--t-group-radius);border-bottom-left-radius:var(--t-group-radius)}.tui-group_rounded.tui-group_orientation_horizontal>*:last-child{border-top-right-radius:var(--t-group-radius);border-bottom-right-radius:var(--t-group-radius)}.tui-group_rounded.tui-group_orientation_vertical>*:first-child{border-top-left-radius:var(--t-group-radius);border-top-right-radius:var(--t-group-radius)}.tui-group_rounded.tui-group_orientation_vertical>*:last-child{border-bottom-left-radius:var(--t-group-radius);border-bottom-right-radius:var(--t-group-radius)}.tui-group__auto-width-item{min-inline-size:auto;flex:0 0 auto}.tui-group__inherit-item{border-radius:inherit!important}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiGroupStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-group',\n                    }, styles: [\"[tuiGroup]{position:relative;display:flex;transform:translateZ(0);--t-group-radius: var(--tui-radius-l);--t-group-margin: -1px;--t-group-mask: linear-gradient(to right, rgba(0, 0, 0, .5) 1px, #000 2px, #000 calc(100% - 2px) , rgba(0, 0, 0, .5));--t-group-mask-end: linear-gradient(to right, rgba(0, 0, 0, .5) 1px, #000 2px);--t-group-mask-start: linear-gradient(to right, #000 calc(100% - 2px) , rgba(0, 0, 0, .5) calc(100% - 1px) )}[tuiGroup]>*{z-index:1;flex:1 1 0;min-inline-size:0;-webkit-mask:var(--t-group-mask);mask:var(--t-group-mask);-webkit-mask-clip:no-clip;mask-clip:no-clip}[tuiGroup]>*:disabled,[tuiGroup]>*._disabled{z-index:0}[tuiGroup]>*:invalid:not([data-mode]),[tuiGroup]>*[data-mode~=invalid]{z-index:2;--t-group-mask: none}[tuiGroup]>*:has(:invalid:not([data-mode])),[tuiGroup]>*:has([data-mode~=invalid]){z-index:2;--t-group-mask: none}[tuiGroup]>*:has(:focus-visible){z-index:3;--t-group-mask: none}[tuiGroup]>*:has([data-focus=true]){z-index:3;--t-group-mask: none}[tuiGroup]>*:checked:not([data-mode]),[tuiGroup]>*[data-mode~=checked]{z-index:4;--t-group-mask: none}[tuiGroup]>*:has([tuiBlock]:checked){z-index:4;--t-group-mask: none}[tuiGroup]>*:not(:last-child){margin-inline-end:var(--t-group-margin)}[tuiGroup]>*:nth-child(n){border-radius:0}[tuiGroup]>*:first-child{border-radius:var(--t-group-radius) 0 0 var(--t-group-radius);-webkit-mask-image:var(--t-group-mask-start);mask-image:var(--t-group-mask-start)}[tuiGroup]>*:last-child{border-radius:0 var(--t-group-radius) var(--t-group-radius) 0;-webkit-mask-image:var(--t-group-mask-end);mask-image:var(--t-group-mask-end)}[tuiGroup]>*:only-child{border-radius:var(--t-group-radius);-webkit-mask:none;mask:none}[tuiGroup][data-size=s],[tuiGroup][data-size=m]{--t-group-radius: var(--tui-radius-m)}[tuiGroup][data-orientation=vertical]{display:inline-flex;flex-direction:column;--t-group-mask: linear-gradient(to bottom, rgba(0, 0, 0, .5) 1px, #000 2px, #000 calc(100% - 2px) , rgba(0, 0, 0, .5));--t-group-mask-start: linear-gradient(to bottom, #000 calc(100% - 2px) , rgba(0, 0, 0, .5) calc(100% - 1px) );--t-group-mask-end: linear-gradient(to bottom, rgba(0, 0, 0, .5) 1px, #000 2px)}[tuiGroup][data-orientation=vertical]>*{min-block-size:auto;flex:0 0 auto}[tuiGroup][data-orientation=vertical]>*:not(:last-child){margin-inline-end:0;margin-block-end:var(--t-group-margin)}[tuiGroup][data-orientation=vertical]>*:first-child{border-radius:var(--t-group-radius) var(--t-group-radius) 0 0}[tuiGroup][data-orientation=vertical]>*:last-child{border-radius:0 0 var(--t-group-radius) var(--t-group-radius)}[tuiGroup][data-orientation=vertical]>*:only-child{border-radius:var(--t-group-radius)}.tui-group{position:relative;display:flex;isolation:isolate;--t-group-radius: var(--tui-radius-m)}.tui-group>*{flex:1 1 0;min-inline-size:0}.tui-group>*:not(:last-child){margin-right:.125rem}.tui-group.tui-group>*:nth-child(n){border-radius:0}.tui-group_radius_large{--t-group-radius: var(--tui-radius-l)}.tui-group_collapsed>*{z-index:1}.tui-group_collapsed>*:not(:last-child){margin:0 -1px 0 0}.tui-group_collapsed>*._readonly,.tui-group_collapsed>*._disabled,.tui-group_collapsed>*._readonly:hover,.tui-group_collapsed>*._disabled:hover{z-index:0}.tui-group_collapsed>*._invalid{z-index:2}.tui-group_collapsed>*._invalid:hover,.tui-group_collapsed>*._invalid._hovered,.tui-group_collapsed>*._invalid._pressed{z-index:4}.tui-group_collapsed>*:hover,.tui-group_collapsed>*._hovered,.tui-group_collapsed>*._pressed{z-index:3}.tui-group_collapsed>*._hosted_dropdown_focused,.tui-group_collapsed>*._focus-visible,.tui-group_collapsed>*._focused.ng-touched,.tui-group_collapsed>*._focused.ng-untouched{z-index:5}.tui-group_collapsed>*._active,.tui-group_collapsed>*[data-appearance=whiteblock-active]{z-index:6}.tui-group_collapsed>*:has([tuiBlock]:checked){z-index:6}.tui-group_collapsed>*._focus-visible._focused,.tui-group_collapsed>*._focus-visible._active,.tui-group_collapsed>*._focus-visible[data-appearance=whiteblock-active]{z-index:7}.tui-group_orientation_vertical{display:inline-flex;flex-direction:column}.tui-group_orientation_vertical>*{min-block-size:auto;flex:0 0 auto}.tui-group_orientation_vertical>*:not(:last-child){margin-right:0;margin-bottom:.125rem}.tui-group_orientation_vertical.tui-group_collapsed>*:not(:last-child){margin:0 0 -1px}.tui-group_rounded.tui-group_orientation_horizontal>*:first-child{border-top-left-radius:var(--t-group-radius);border-bottom-left-radius:var(--t-group-radius)}.tui-group_rounded.tui-group_orientation_horizontal>*:last-child{border-top-right-radius:var(--t-group-radius);border-bottom-right-radius:var(--t-group-radius)}.tui-group_rounded.tui-group_orientation_vertical>*:first-child{border-top-left-radius:var(--t-group-radius);border-top-right-radius:var(--t-group-radius)}.tui-group_rounded.tui-group_orientation_vertical>*:last-child{border-bottom-left-radius:var(--t-group-radius);border-bottom-right-radius:var(--t-group-radius)}.tui-group__auto-width-item{min-inline-size:auto;flex:0 0 auto}.tui-group__inherit-item{border-radius:inherit!important}\\n\"] }]\n        }] });\nclass TuiGroup {\n    constructor() {\n        this.options = inject(TUI_GROUP_OPTIONS);\n        this.nothing = tuiWithStyles(TuiGroupStyles);\n        this.orientation = this.options.orientation;\n        this.collapsed = this.options.collapsed;\n        this.rounded = this.options.rounded;\n        this.size = this.options.size;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiGroup, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiGroup, isStandalone: true, selector: \"[tuiGroup]:not(ng-container)\", inputs: { orientation: \"orientation\", collapsed: \"collapsed\", rounded: \"rounded\", size: \"size\" }, host: { attributes: { \"tuiGroup\": \"\" }, properties: { \"attr.data-orientation\": \"orientation\", \"attr.data-size\": \"size\", \"style.--t-group-radius\": \"rounded ? null : 0\", \"style.--t-group-margin.rem\": \"collapsed ? null : 0.125\", \"style.--t-group-mask\": \"collapsed ? null : \\\"none\\\"\", \"style.--t-group-mask-start\": \"collapsed ? null : \\\"none\\\"\", \"style.--t-group-mask-end\": \"collapsed ? null : \\\"none\\\"\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiGroup, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiGroup]:not(ng-container)',\n                    host: {\n                        tuiGroup: '',\n                        '[attr.data-orientation]': 'orientation',\n                        '[attr.data-size]': 'size',\n                        '[style.--t-group-radius]': 'rounded ? null : 0',\n                        '[style.--t-group-margin.rem]': 'collapsed ? null : 0.125',\n                        '[style.--t-group-mask]': 'collapsed ? null : \"none\"',\n                        '[style.--t-group-mask-start]': 'collapsed ? null : \"none\"',\n                        '[style.--t-group-mask-end]': 'collapsed ? null : \"none\"',\n                    },\n                }]\n        }], propDecorators: { orientation: [{\n                type: Input\n            }], collapsed: [{\n                type: Input\n            }], rounded: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_GROUP_DEFAULT_OPTIONS, TUI_GROUP_OPTIONS, TuiGroup, tuiGroupOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAC/G,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,aAAa,QAAQ,mCAAmC;AAEpG,MAAMC,yBAAyB,GAAG;EAC9BC,IAAI,EAAE,GAAG;EACTC,SAAS,EAAE,KAAK;EAChBC,OAAO,EAAE,IAAI;EACbC,WAAW,EAAE;AACjB,CAAC;AACD,MAAMC,iBAAiB,GAAGR,cAAc,CAACG,yBAAyB,CAAC;AACnE,SAASM,uBAAuBA,CAACC,OAAO,EAAE;EACtC,OAAOT,iBAAiB,CAACO,iBAAiB,EAAEE,OAAO,EAAEP,yBAAyB,CAAC;AACnF;AAEA,MAAMQ,cAAc,CAAC;EACjB;IAAS,IAAI,CAACC,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACI,IAAI,kBAD+EtB,EAAE,CAAAuB,iBAAA;MAAAC,IAAA,EACJN,cAAc;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADZ5B,EAAE,CAAA6B,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACutK;EAAE;AACh0K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGvC,EAAE,CAAAwC,iBAAA,CAGXtB,cAAc,EAAc,CAAC;IAC7GM,IAAI,EAAEvB,SAAS;IACfwC,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAEnC,iBAAiB,CAACwC,IAAI;MAAEJ,eAAe,EAAEnC,uBAAuB,CAACwC,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,+9JAA+9J;IAAE,CAAC;EAC1/J,CAAC,CAAC;AAAA;AACV,MAAMU,QAAQ,CAAC;EACXC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9B,OAAO,GAAGb,MAAM,CAACW,iBAAiB,CAAC;IACxC,IAAI,CAACiC,OAAO,GAAGvC,aAAa,CAACS,cAAc,CAAC;IAC5C,IAAI,CAACJ,WAAW,GAAG,IAAI,CAACG,OAAO,CAACH,WAAW;IAC3C,IAAI,CAACF,SAAS,GAAG,IAAI,CAACK,OAAO,CAACL,SAAS;IACvC,IAAI,CAACC,OAAO,GAAG,IAAI,CAACI,OAAO,CAACJ,OAAO;IACnC,IAAI,CAACF,IAAI,GAAG,IAAI,CAACM,OAAO,CAACN,IAAI;EACjC;EACA;IAAS,IAAI,CAACQ,IAAI,YAAA8B,iBAAA5B,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,QAAQ;IAAA,CAAmD;EAAE;EACxK;IAAS,IAAI,CAACI,IAAI,kBAnB+ElD,EAAE,CAAAmD,iBAAA;MAAA3B,IAAA,EAmBJsB,QAAQ;MAAArB,SAAA;MAAAC,SAAA,eAAoM,EAAE;MAAA0B,QAAA;MAAAC,YAAA,WAAAC,sBAAApB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnB5MlC,EAAE,CAAAuD,WAAA,qBAAApB,GAAA,CAAArB,WAAA,eAAAqB,GAAA,CAAAxB,IAAA;UAAFX,EAAE,CAAAwD,WAAA,qBAAArB,GAAA,CAAAtB,OAAA,GAmBM,IAAI,GAAG,CAAV,CAAC,qBAAAsB,GAAA,CAAAvB,SAAA,GAAI,IAAI,GAAG,KAAK,OAAjB,CAAC,mBAAAuB,GAAA,CAAAvB,SAAA,GAAI,IAAI,GAAG,MAAZ,CAAC,yBAAAuB,GAAA,CAAAvB,SAAA,GAAI,IAAI,GAAG,MAAZ,CAAC,uBAAAuB,GAAA,CAAAvB,SAAA,GAAI,IAAI,GAAG,MAAZ,CAAC;QAAA;MAAA;MAAA6C,MAAA;QAAA3C,WAAA;QAAAF,SAAA;QAAAC,OAAA;QAAAF,IAAA;MAAA;MAAAgB,UAAA;IAAA,EAAskB;EAAE;AACnrB;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KArBqGvC,EAAE,CAAAwC,iBAAA,CAqBXM,QAAQ,EAAc,CAAC;IACvGtB,IAAI,EAAEnB,SAAS;IACfoC,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChB+B,QAAQ,EAAE,8BAA8B;MACxCd,IAAI,EAAE;QACFe,QAAQ,EAAE,EAAE;QACZ,yBAAyB,EAAE,aAAa;QACxC,kBAAkB,EAAE,MAAM;QAC1B,0BAA0B,EAAE,oBAAoB;QAChD,8BAA8B,EAAE,0BAA0B;QAC1D,wBAAwB,EAAE,2BAA2B;QACrD,8BAA8B,EAAE,2BAA2B;QAC3D,4BAA4B,EAAE;MAClC;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE7C,WAAW,EAAE,CAAC;MAC5BU,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEM,SAAS,EAAE,CAAC;MACZY,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEO,OAAO,EAAE,CAAC;MACVW,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEK,IAAI,EAAE,CAAC;MACPa,IAAI,EAAElB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASI,yBAAyB,EAAEK,iBAAiB,EAAE+B,QAAQ,EAAE9B,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}