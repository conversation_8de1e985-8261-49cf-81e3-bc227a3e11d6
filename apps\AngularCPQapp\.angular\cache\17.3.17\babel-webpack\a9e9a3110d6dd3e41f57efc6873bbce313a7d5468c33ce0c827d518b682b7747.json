{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON><PERSON>, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChild } from '@angular/core';\nimport { NgControl } from '@angular/forms';\nimport { TuiLet } from '@taiga-ui/cdk/directives/let';\nimport { tuiControlValue } from '@taiga-ui/cdk/observables';\nimport { defer } from 'rxjs';\nconst _c0 = [[[\"input\"]], \"*\"];\nconst _c1 = [\"input\", \"*\"];\nfunction TuiInputInline_ng_container_0_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TuiInputInline_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 1);\n    i0.ɵɵtemplate(2, TuiInputInline_ng_container_0_span_2_Template, 2, 0, \"span\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const value_r1 = ctx.tuiLet;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"textContent\", value_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !value_r1);\n  }\n}\nclass TuiInputInline {\n  constructor() {\n    this.value$ = defer(() => tuiControlValue(this.control));\n  }\n  static {\n    this.ɵfac = function TuiInputInline_Factory(t) {\n      return new (t || TuiInputInline)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiInputInline,\n      selectors: [[\"tui-input-inline\"]],\n      contentQueries: function TuiInputInline_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NgControl, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.control = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 3,\n      vars: 3,\n      consts: [[4, \"tuiLet\"], [1, \"t-before\", 3, \"textContent\"], [\"automation-id\", \"tui-input-inline__placeholder\", \"class\", \"t-placeholder\", 4, \"ngIf\"], [\"automation-id\", \"tui-input-inline__placeholder\", 1, \"t-placeholder\"]],\n      template: function TuiInputInline_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵtemplate(0, TuiInputInline_ng_container_0_Template, 3, 2, \"ng-container\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵprojection(2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"tuiLet\", i0.ɵɵpipeBind1(1, 1, ctx.value$));\n        }\n      },\n      dependencies: [AsyncPipe, NgIf, TuiLet],\n      styles: [\"tui-input-inline{position:relative;display:inline-block;white-space:nowrap;box-sizing:border-box}tui-input-inline>.t-before{padding-right:.02em;margin-left:1px;white-space:pre;visibility:hidden}tui-input-inline>.t-placeholder{display:inline-block;min-inline-size:1px;margin-left:-1px}tui-input-inline>input{position:absolute;top:0;left:0;background-color:transparent;padding:inherit;font:inherit;color:inherit;box-sizing:border-box;inline-size:100%;block-size:100%;border-width:0;text-align:inherit;letter-spacing:inherit;text-indent:inherit;text-transform:inherit;outline:none}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputInline, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-input-inline',\n      imports: [AsyncPipe, NgIf, TuiLet],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-container *tuiLet=\\\"value$ | async as value\\\">\\n    <span\\n        class=\\\"t-before\\\"\\n        [textContent]=\\\"value\\\"\\n    ></span>\\n\\n    <span\\n        *ngIf=\\\"!value\\\"\\n        automation-id=\\\"tui-input-inline__placeholder\\\"\\n        class=\\\"t-placeholder\\\"\\n    >\\n        <ng-content />\\n    </span>\\n</ng-container>\\n<ng-content select=\\\"input\\\" />\\n\",\n      styles: [\"tui-input-inline{position:relative;display:inline-block;white-space:nowrap;box-sizing:border-box}tui-input-inline>.t-before{padding-right:.02em;margin-left:1px;white-space:pre;visibility:hidden}tui-input-inline>.t-placeholder{display:inline-block;min-inline-size:1px;margin-left:-1px}tui-input-inline>input{position:absolute;top:0;left:0;background-color:transparent;padding:inherit;font:inherit;color:inherit;box-sizing:border-box;inline-size:100%;block-size:100%;border-width:0;text-align:inherit;letter-spacing:inherit;text-indent:inherit;text-transform:inherit;outline:none}\\n\"]\n    }]\n  }], null, {\n    control: [{\n      type: ContentChild,\n      args: [NgControl]\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiInputInline };", "map": {"version": 3, "names": ["AsyncPipe", "NgIf", "i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ContentChild", "NgControl", "TuiLet", "tuiControlValue", "defer", "_c0", "_c1", "TuiInputInline_ng_container_0_span_2_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "TuiInputInline_ng_container_0_Template", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵtemplate", "ɵɵelementContainerEnd", "value_r1", "tuiLet", "ɵɵadvance", "ɵɵproperty", "TuiInputInline", "constructor", "value$", "control", "ɵfac", "TuiInputInline_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "TuiInputInline_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "TuiInputInline_Template", "ɵɵprojectionDef", "ɵɵpipe", "ɵɵpipeBind1", "dependencies", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "None", "OnPush"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-input-inline.mjs"], "sourcesContent": ["import { As<PERSON><PERSON><PERSON><PERSON>, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChild } from '@angular/core';\nimport { NgControl } from '@angular/forms';\nimport { TuiLet } from '@taiga-ui/cdk/directives/let';\nimport { tuiControlValue } from '@taiga-ui/cdk/observables';\nimport { defer } from 'rxjs';\n\nclass TuiInputInline {\n    constructor() {\n        this.value$ = defer(() => tuiControlValue(this.control));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputInline, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputInline, isStandalone: true, selector: \"tui-input-inline\", queries: [{ propertyName: \"control\", first: true, predicate: NgControl, descendants: true }], ngImport: i0, template: \"<ng-container *tuiLet=\\\"value$ | async as value\\\">\\n    <span\\n        class=\\\"t-before\\\"\\n        [textContent]=\\\"value\\\"\\n    ></span>\\n\\n    <span\\n        *ngIf=\\\"!value\\\"\\n        automation-id=\\\"tui-input-inline__placeholder\\\"\\n        class=\\\"t-placeholder\\\"\\n    >\\n        <ng-content />\\n    </span>\\n</ng-container>\\n<ng-content select=\\\"input\\\" />\\n\", styles: [\"tui-input-inline{position:relative;display:inline-block;white-space:nowrap;box-sizing:border-box}tui-input-inline>.t-before{padding-right:.02em;margin-left:1px;white-space:pre;visibility:hidden}tui-input-inline>.t-placeholder{display:inline-block;min-inline-size:1px;margin-left:-1px}tui-input-inline>input{position:absolute;top:0;left:0;background-color:transparent;padding:inherit;font:inherit;color:inherit;box-sizing:border-box;inline-size:100%;block-size:100%;border-width:0;text-align:inherit;letter-spacing:inherit;text-indent:inherit;text-transform:inherit;outline:none}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: TuiLet, selector: \"[tuiLet]\", inputs: [\"tuiLet\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputInline, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-input-inline', imports: [AsyncPipe, NgIf, TuiLet], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-container *tuiLet=\\\"value$ | async as value\\\">\\n    <span\\n        class=\\\"t-before\\\"\\n        [textContent]=\\\"value\\\"\\n    ></span>\\n\\n    <span\\n        *ngIf=\\\"!value\\\"\\n        automation-id=\\\"tui-input-inline__placeholder\\\"\\n        class=\\\"t-placeholder\\\"\\n    >\\n        <ng-content />\\n    </span>\\n</ng-container>\\n<ng-content select=\\\"input\\\" />\\n\", styles: [\"tui-input-inline{position:relative;display:inline-block;white-space:nowrap;box-sizing:border-box}tui-input-inline>.t-before{padding-right:.02em;margin-left:1px;white-space:pre;visibility:hidden}tui-input-inline>.t-placeholder{display:inline-block;min-inline-size:1px;margin-left:-1px}tui-input-inline>input{position:absolute;top:0;left:0;background-color:transparent;padding:inherit;font:inherit;color:inherit;box-sizing:border-box;inline-size:100%;block-size:100%;border-width:0;text-align:inherit;letter-spacing:inherit;text-indent:inherit;text-transform:inherit;outline:none}\\n\"] }]\n        }], propDecorators: { control: [{\n                type: ContentChild,\n                args: [NgControl]\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiInputInline };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,IAAI,QAAQ,iBAAiB;AACjD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,YAAY,QAAQ,eAAe;AACnG,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,MAAM,QAAQ,8BAA8B;AACrD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,KAAK,QAAQ,MAAM;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,8CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAMwEZ,EAAE,CAAAc,cAAA,aACoc,CAAC;IADvcd,EAAE,CAAAe,YAAA,KAC4d,CAAC;IAD/df,EAAE,CAAAgB,YAAA,CACye,CAAC;EAAA;AAAA;AAAA,SAAAC,uCAAAL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD5eZ,EAAE,CAAAkB,uBAAA,EACsO,CAAC;IADzOlB,EAAE,CAAAmB,SAAA,aAC4T,CAAC;IAD/TnB,EAAE,CAAAoB,UAAA,IAAAT,6CAAA,iBACoc,CAAC;IADvcX,EAAE,CAAAqB,qBAAA;EAAA;EAAA,IAAAT,EAAA;IAAA,MAAAU,QAAA,GAAAT,GAAA,CAAAU,MAAA;IAAFvB,EAAE,CAAAwB,SAAA,CAC8S,CAAC;IADjTxB,EAAE,CAAAyB,UAAA,gBAAAH,QAC8S,CAAC;IADjTtB,EAAE,CAAAwB,SAAA,CACiW,CAAC;IADpWxB,EAAE,CAAAyB,UAAA,UAAAH,QACiW,CAAC;EAAA;AAAA;AALzc,MAAMI,cAAc,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAGpB,KAAK,CAAC,MAAMD,eAAe,CAAC,IAAI,CAACsB,OAAO,CAAC,CAAC;EAC5D;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFN,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACO,IAAI,kBAD+EjC,EAAE,CAAAkC,iBAAA;MAAAC,IAAA,EACJT,cAAc;MAAAU,SAAA;MAAAC,cAAA,WAAAC,8BAAA1B,EAAA,EAAAC,GAAA,EAAA0B,QAAA;QAAA,IAAA3B,EAAA;UADZZ,EAAE,CAAAwC,cAAA,CAAAD,QAAA,EAC2HlC,SAAS;QAAA;QAAA,IAAAO,EAAA;UAAA,IAAA6B,EAAA;UADtIzC,EAAE,CAAA0C,cAAA,CAAAD,EAAA,GAAFzC,EAAE,CAAA2C,WAAA,QAAA9B,GAAA,CAAAgB,OAAA,GAAAY,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAF9C,EAAE,CAAA+C,mBAAA;MAAAC,kBAAA,EAAAtC,GAAA;MAAAuC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAzC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFZ,EAAE,CAAAsD,eAAA,CAAA7C,GAAA;UAAFT,EAAE,CAAAoB,UAAA,IAAAH,sCAAA,yBACsO,CAAC;UADzOjB,EAAE,CAAAuD,MAAA;UAAFvD,EAAE,CAAAe,YAAA,EAC2hB,CAAC;QAAA;QAAA,IAAAH,EAAA;UAD9hBZ,EAAE,CAAAyB,UAAA,WAAFzB,EAAE,CAAAwD,WAAA,OAAA3C,GAAA,CAAAe,MAAA,CAC2N,CAAC;QAAA;MAAA;MAAA6B,YAAA,GAA47B3D,SAAS,EAA8CC,IAAI,EAA6FO,MAAM;MAAAoD,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAA8I;EAAE;AAC7iD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG7D,EAAE,CAAA8D,iBAAA,CAGXpC,cAAc,EAAc,CAAC;IAC7GS,IAAI,EAAElC,SAAS;IACf8D,IAAI,EAAE,CAAC;MAAElB,UAAU,EAAE,IAAI;MAAEmB,QAAQ,EAAE,kBAAkB;MAAEC,OAAO,EAAE,CAACnE,SAAS,EAAEC,IAAI,EAAEO,MAAM,CAAC;MAAEqD,aAAa,EAAEzD,iBAAiB,CAACgE,IAAI;MAAEN,eAAe,EAAEzD,uBAAuB,CAACgE,MAAM;MAAEf,QAAQ,EAAE,2WAA2W;MAAEM,MAAM,EAAE,CAAC,skBAAskB;IAAE,CAAC;EACloC,CAAC,CAAC,QAAkB;IAAE7B,OAAO,EAAE,CAAC;MACxBM,IAAI,EAAE/B,YAAY;MAClB2D,IAAI,EAAE,CAAC1D,SAAS;IACpB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASqB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}