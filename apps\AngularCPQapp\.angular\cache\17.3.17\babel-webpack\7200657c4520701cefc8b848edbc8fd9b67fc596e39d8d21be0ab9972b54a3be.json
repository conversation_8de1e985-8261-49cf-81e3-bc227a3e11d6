{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nclass TuiButtonGroupStyles {\n  static {\n    this.ɵfac = function TuiButtonGroupStyles_Factory(t) {\n      return new (t || TuiButtonGroupStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiButtonGroupStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-button-group-styles\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiButtonGroupStyles_Template(rf, ctx) {},\n      styles: [\"[tuiButtonGroup],[tuiButtonGroup] tui-elastic-container{transition-property:background,height,border-radius;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;border-radius:var(--tui-radius-xl);overflow:hidden}[tuiButtonGroup],[tuiButtonGroup] tui-elastic-container .t-wrapper{display:flex;justify-content:center}[tuiButtonGroup] tui-elastic-container{inline-size:100%}[tuiButtonGroup] button,[tuiButtonGroup] a{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:relative;display:flex;border:none;outline:none;background:transparent;align-items:center;flex:1;flex-direction:column;padding:1.125rem .25rem;gap:.5rem;font:var(--tui-font-text-ui-s);max-inline-size:calc(50% - 1.75rem);cursor:pointer;color:var(--tui-text-action);text-align:center;text-decoration:none;clip-path:inset(0)}[tuiButtonGroup] button:first-child,[tuiButtonGroup] a:first-child{clip-path:inset(0 0 0 -10rem)}[tuiButtonGroup] button:last-child,[tuiButtonGroup] a:last-child{clip-path:inset(0 -10rem 0 0)}[tuiButtonGroup] button:active,[tuiButtonGroup] a:active{background:var(--tui-background-neutral-1)}[tuiButtonGroup] button:before,[tuiButtonGroup] a:before,[tuiButtonGroup] button:after,[tuiButtonGroup] a:after{position:absolute;top:0;background:inherit;inline-size:1.75rem;block-size:100%}[tuiButtonGroup] button:first-child:before,[tuiButtonGroup] a:first-child:before{content:\\\"\\\";left:-1.75rem}[tuiButtonGroup] button:last-child:after,[tuiButtonGroup] a:last-child:after{content:\\\"\\\";right:-1.75rem}[tuiButtonGroup] button tui-icon,[tuiButtonGroup] a tui-icon{font-size:1.75rem}[tuiButtonGroup]:has(button:only-child){border-radius:1rem}[tuiButtonGroup] button:only-child,[tuiButtonGroup] a:only-child{inline-size:100%;flex-direction:row;font:var(--tui-font-text-ui-l);max-inline-size:100%;justify-content:center}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiButtonGroupStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-button-group-styles'\n      },\n      styles: [\"[tuiButtonGroup],[tuiButtonGroup] tui-elastic-container{transition-property:background,height,border-radius;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;border-radius:var(--tui-radius-xl);overflow:hidden}[tuiButtonGroup],[tuiButtonGroup] tui-elastic-container .t-wrapper{display:flex;justify-content:center}[tuiButtonGroup] tui-elastic-container{inline-size:100%}[tuiButtonGroup] button,[tuiButtonGroup] a{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:relative;display:flex;border:none;outline:none;background:transparent;align-items:center;flex:1;flex-direction:column;padding:1.125rem .25rem;gap:.5rem;font:var(--tui-font-text-ui-s);max-inline-size:calc(50% - 1.75rem);cursor:pointer;color:var(--tui-text-action);text-align:center;text-decoration:none;clip-path:inset(0)}[tuiButtonGroup] button:first-child,[tuiButtonGroup] a:first-child{clip-path:inset(0 0 0 -10rem)}[tuiButtonGroup] button:last-child,[tuiButtonGroup] a:last-child{clip-path:inset(0 -10rem 0 0)}[tuiButtonGroup] button:active,[tuiButtonGroup] a:active{background:var(--tui-background-neutral-1)}[tuiButtonGroup] button:before,[tuiButtonGroup] a:before,[tuiButtonGroup] button:after,[tuiButtonGroup] a:after{position:absolute;top:0;background:inherit;inline-size:1.75rem;block-size:100%}[tuiButtonGroup] button:first-child:before,[tuiButtonGroup] a:first-child:before{content:\\\"\\\";left:-1.75rem}[tuiButtonGroup] button:last-child:after,[tuiButtonGroup] a:last-child:after{content:\\\"\\\";right:-1.75rem}[tuiButtonGroup] button tui-icon,[tuiButtonGroup] a tui-icon{font-size:1.75rem}[tuiButtonGroup]:has(button:only-child){border-radius:1rem}[tuiButtonGroup] button:only-child,[tuiButtonGroup] a:only-child{inline-size:100%;flex-direction:row;font:var(--tui-font-text-ui-l);max-inline-size:100%;justify-content:center}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiButtonGroup {\n  constructor() {\n    this.nothing = tuiWithStyles(TuiButtonGroupStyles);\n  }\n  static {\n    this.ɵfac = function TuiButtonGroup_Factory(t) {\n      return new (t || TuiButtonGroup)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiButtonGroup,\n      selectors: [[\"\", \"tuiButtonGroup\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiButtonGroup, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiButtonGroup]'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiButtonGroup };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Directive", "tuiWithStyles", "TuiButtonGroupStyles", "ɵfac", "TuiButtonGroupStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiButtonGroupStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiButtonGroup", "constructor", "nothing", "TuiButtonGroup_Factory", "ɵdir", "ɵɵdefineDirective", "selector"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-button-group.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\n\nclass TuiButtonGroupStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiButtonGroupStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiButtonGroupStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-button-group-styles\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiButtonGroup],[tuiButtonGroup] tui-elastic-container{transition-property:background,height,border-radius;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;border-radius:var(--tui-radius-xl);overflow:hidden}[tuiButtonGroup],[tuiButtonGroup] tui-elastic-container .t-wrapper{display:flex;justify-content:center}[tuiButtonGroup] tui-elastic-container{inline-size:100%}[tuiButtonGroup] button,[tuiButtonGroup] a{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:relative;display:flex;border:none;outline:none;background:transparent;align-items:center;flex:1;flex-direction:column;padding:1.125rem .25rem;gap:.5rem;font:var(--tui-font-text-ui-s);max-inline-size:calc(50% - 1.75rem);cursor:pointer;color:var(--tui-text-action);text-align:center;text-decoration:none;clip-path:inset(0)}[tuiButtonGroup] button:first-child,[tuiButtonGroup] a:first-child{clip-path:inset(0 0 0 -10rem)}[tuiButtonGroup] button:last-child,[tuiButtonGroup] a:last-child{clip-path:inset(0 -10rem 0 0)}[tuiButtonGroup] button:active,[tuiButtonGroup] a:active{background:var(--tui-background-neutral-1)}[tuiButtonGroup] button:before,[tuiButtonGroup] a:before,[tuiButtonGroup] button:after,[tuiButtonGroup] a:after{position:absolute;top:0;background:inherit;inline-size:1.75rem;block-size:100%}[tuiButtonGroup] button:first-child:before,[tuiButtonGroup] a:first-child:before{content:\\\"\\\";left:-1.75rem}[tuiButtonGroup] button:last-child:after,[tuiButtonGroup] a:last-child:after{content:\\\"\\\";right:-1.75rem}[tuiButtonGroup] button tui-icon,[tuiButtonGroup] a tui-icon{font-size:1.75rem}[tuiButtonGroup]:has(button:only-child){border-radius:1rem}[tuiButtonGroup] button:only-child,[tuiButtonGroup] a:only-child{inline-size:100%;flex-direction:row;font:var(--tui-font-text-ui-l);max-inline-size:100%;justify-content:center}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiButtonGroupStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-button-group-styles',\n                    }, styles: [\"[tuiButtonGroup],[tuiButtonGroup] tui-elastic-container{transition-property:background,height,border-radius;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;border-radius:var(--tui-radius-xl);overflow:hidden}[tuiButtonGroup],[tuiButtonGroup] tui-elastic-container .t-wrapper{display:flex;justify-content:center}[tuiButtonGroup] tui-elastic-container{inline-size:100%}[tuiButtonGroup] button,[tuiButtonGroup] a{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:relative;display:flex;border:none;outline:none;background:transparent;align-items:center;flex:1;flex-direction:column;padding:1.125rem .25rem;gap:.5rem;font:var(--tui-font-text-ui-s);max-inline-size:calc(50% - 1.75rem);cursor:pointer;color:var(--tui-text-action);text-align:center;text-decoration:none;clip-path:inset(0)}[tuiButtonGroup] button:first-child,[tuiButtonGroup] a:first-child{clip-path:inset(0 0 0 -10rem)}[tuiButtonGroup] button:last-child,[tuiButtonGroup] a:last-child{clip-path:inset(0 -10rem 0 0)}[tuiButtonGroup] button:active,[tuiButtonGroup] a:active{background:var(--tui-background-neutral-1)}[tuiButtonGroup] button:before,[tuiButtonGroup] a:before,[tuiButtonGroup] button:after,[tuiButtonGroup] a:after{position:absolute;top:0;background:inherit;inline-size:1.75rem;block-size:100%}[tuiButtonGroup] button:first-child:before,[tuiButtonGroup] a:first-child:before{content:\\\"\\\";left:-1.75rem}[tuiButtonGroup] button:last-child:after,[tuiButtonGroup] a:last-child:after{content:\\\"\\\";right:-1.75rem}[tuiButtonGroup] button tui-icon,[tuiButtonGroup] a tui-icon{font-size:1.75rem}[tuiButtonGroup]:has(button:only-child){border-radius:1rem}[tuiButtonGroup] button:only-child,[tuiButtonGroup] a:only-child{inline-size:100%;flex-direction:row;font:var(--tui-font-text-ui-l);max-inline-size:100%;justify-content:center}\\n\"] }]\n        }] });\nclass TuiButtonGroup {\n    constructor() {\n        this.nothing = tuiWithStyles(TuiButtonGroupStyles);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiButtonGroup, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiButtonGroup, isStandalone: true, selector: \"[tuiButtonGroup]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiButtonGroup, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiButtonGroup]',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiButtonGroup };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,QAAQ,eAAe;AAChG,SAASC,aAAa,QAAQ,mCAAmC;AAEjE,MAAMC,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAACC,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACI,IAAI,kBAD+EV,EAAE,CAAAW,iBAAA;MAAAC,IAAA,EACJN,oBAAoB;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADlBhB,EAAE,CAAAiB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC4nE;EAAE;AACruE;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG3B,EAAE,CAAA4B,iBAAA,CAGXtB,oBAAoB,EAAc,CAAC;IACnHM,IAAI,EAAEX,SAAS;IACf4B,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAEvB,iBAAiB,CAAC4B,IAAI;MAAEJ,eAAe,EAAEvB,uBAAuB,CAAC4B,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,g3DAAg3D;IAAE,CAAC;EAC34D,CAAC,CAAC;AAAA;AACV,MAAMU,cAAc,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG/B,aAAa,CAACC,oBAAoB,CAAC;EACtD;EACA;IAAS,IAAI,CAACC,IAAI,YAAA8B,uBAAA5B,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACI,IAAI,kBAd+EtC,EAAE,CAAAuC,iBAAA;MAAA3B,IAAA,EAcJsB,cAAc;MAAArB,SAAA;MAAAE,UAAA;IAAA,EAAmE;EAAE;AACtL;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAhBqG3B,EAAE,CAAA4B,iBAAA,CAgBXM,cAAc,EAAc,CAAC;IAC7GtB,IAAI,EAAER,SAAS;IACfyB,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChByB,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}