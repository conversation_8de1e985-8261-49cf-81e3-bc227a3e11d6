{"ast": null, "code": "import { NgIf, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, inject, ChangeDetectorRef, DestroyRef, TemplateRef, Component, ChangeDetectionStrategy, ViewChild, ContentChild, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { tuiParentAnimation } from '@taiga-ui/core/animations';\nimport { TuiLoader } from '@taiga-ui/core/components/loader';\nimport { timer } from 'rxjs';\n\n/**\n * @deprecated use {@link TuiExpand} from @taiga-ui/experimental\n */\nconst _c0 = [\"wrapper\"];\nconst _c1 = [\"*\"];\nfunction TuiExpandComponent_ng_container_2_tui_loader_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tui-loader\", 4);\n    i0.ɵɵelementContainer(1, 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"overlay\", true)(\"showLoader\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.content);\n  }\n}\nfunction TuiExpandComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, TuiExpandComponent_ng_container_2_tui_loader_2_Template, 2, 3, \"tui-loader\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.async)(\"ngIfElse\", ctx_r0.content);\n  }\n}\nclass TuiExpandContent {\n  static {\n    this.ɵfac = function TuiExpandContent_Factory(t) {\n      return new (t || TuiExpandContent)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiExpandContent,\n      selectors: [[\"\", \"tuiExpandContent\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiExpandContent, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiExpandContent]'\n    }]\n  }], null, null);\n})();\nconst State = {\n  Idle: 0,\n  Loading: 1,\n  Prepared: 2,\n  Animated: 3\n};\nconst LOADER_HEIGHT = 48;\n/**\n * An event indicating that async data for expand has finished loading.\n * Dispatch to finish loading states for {@link TuiExpandComponent}.\n */\nconst TUI_EXPAND_LOADED = 'tui-expand-loaded';\n/**\n * @deprecated use {@link TuiExpand} from @taiga-ui/experimental\n */\nclass TuiExpandComponent {\n  constructor() {\n    this.cdr = inject(ChangeDetectorRef);\n    this.destroyRef = inject(DestroyRef);\n    this.state = State.Idle;\n    this.content = null;\n    this.expanded = null;\n    this.async = false;\n  }\n  set expandedSetter(expanded) {\n    if (this.expanded === null) {\n      this.expanded = expanded;\n      return;\n    }\n    if (this.state !== State.Idle) {\n      this.expanded = expanded;\n      this.state = State.Animated;\n      return;\n    }\n    this.expanded = expanded;\n    this.retrigger(this.async && expanded ? State.Loading : State.Animated);\n  }\n  get contentVisible() {\n    return this.expanded || this.state !== State.Idle;\n  }\n  get overflow() {\n    return this.state !== State.Idle;\n  }\n  get loading() {\n    return !!this.expanded && this.async && this.state === State.Loading;\n  }\n  get height() {\n    const {\n      expanded,\n      state,\n      contentWrapper\n    } = this;\n    if (expanded && state === State.Prepared || !expanded && state === State.Animated) {\n      return 0;\n    }\n    if (contentWrapper && (!expanded && state === State.Prepared || expanded && state === State.Animated)) {\n      return contentWrapper.nativeElement.offsetHeight;\n    }\n    if (contentWrapper && expanded && state === State.Loading) {\n      return Math.max(contentWrapper.nativeElement.offsetHeight, LOADER_HEIGHT);\n    }\n    return null;\n  }\n  onTransitionEnd({\n    propertyName,\n    pseudoElement\n  }) {\n    if (propertyName === 'opacity' && !pseudoElement && this.state === State.Animated) {\n      this.state = State.Idle;\n    }\n  }\n  onExpandLoaded(event) {\n    event.stopPropagation();\n    if (this.state === State.Loading) {\n      this.retrigger(State.Animated);\n    }\n  }\n  retrigger(state) {\n    this.state = State.Prepared;\n    timer(0).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {\n      // We need delay to re-trigger CSS height transition from the correct number\n      if (this.state !== State.Prepared) {\n        return;\n      }\n      this.state = state;\n      this.cdr.markForCheck();\n    });\n  }\n  static {\n    this.ɵfac = function TuiExpandComponent_Factory(t) {\n      return new (t || TuiExpandComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiExpandComponent,\n      selectors: [[\"tui-expand\"]],\n      contentQueries: function TuiExpandComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiExpandContent, 5, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n        }\n      },\n      viewQuery: function TuiExpandComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentWrapper = _t.first);\n        }\n      },\n      hostVars: 9,\n      hostBindings: function TuiExpandComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"transitionend.self\", function TuiExpandComponent_transitionend_self_HostBindingHandler($event) {\n            return ctx.onTransitionEnd($event);\n          })(\"tui-expand-loaded\", function TuiExpandComponent_tui_expand_loaded_HostBindingHandler($event) {\n            return ctx.onExpandLoaded($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-expanded\", ctx.expanded);\n          i0.ɵɵstyleProp(\"height\", ctx.height, \"px\");\n          i0.ɵɵclassProp(\"_loading\", ctx.loading)(\"_overflow\", ctx.overflow)(\"_expanded\", ctx.expanded);\n        }\n      },\n      inputs: {\n        async: \"async\",\n        expandedSetter: [i0.ɵɵInputFlags.None, \"expanded\", \"expandedSetter\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 3,\n      vars: 3,\n      consts: [[\"wrapper\", \"\"], [1, \"t-wrapper\"], [4, \"ngIf\"], [\"size\", \"l\", 3, \"overlay\", \"showLoader\", 4, \"ngIf\", \"ngIfElse\"], [\"size\", \"l\", 3, \"overlay\", \"showLoader\"], [3, \"ngTemplateOutlet\"]],\n      template: function TuiExpandComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 1, 0);\n          i0.ɵɵtemplate(2, TuiExpandComponent_ng_container_2_Template, 3, 2, \"ng-container\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"@tuiParentAnimation\", undefined)(\"@.disabled\", ctx.overflow);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.contentVisible);\n        }\n      },\n      dependencies: [NgIf, NgTemplateOutlet, TuiLoader],\n      styles: [\"[_nghost-%COMP%]{transition-property:opacity,height,visibility;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:block;opacity:0;transition-delay:1ms}._overflow[_nghost-%COMP%]{overflow:hidden}._expanded[_nghost-%COMP%]{opacity:1}._loading[_nghost-%COMP%]{opacity:.99}.t-wrapper[_ngcontent-%COMP%]:before, .t-wrapper[_ngcontent-%COMP%]:after{content:\\\"\\\";display:table}\"],\n      data: {\n        animation: [tuiParentAnimation]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiExpandComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-expand',\n      imports: [NgIf, NgTemplateOutlet, TuiLoader],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [tuiParentAnimation],\n      host: {\n        '[style.height.px]': 'height',\n        '[class._loading]': 'loading',\n        '[class._overflow]': 'overflow',\n        '[class._expanded]': 'expanded',\n        '[attr.aria-expanded]': 'expanded',\n        '(transitionend.self)': 'onTransitionEnd($event)',\n        [`(${TUI_EXPAND_LOADED})`]: 'onExpandLoaded($event)'\n      },\n      template: \"<div\\n    #wrapper\\n    class=\\\"t-wrapper\\\"\\n    @tuiParentAnimation\\n    [@.disabled]=\\\"overflow\\\"\\n>\\n    <ng-container *ngIf=\\\"contentVisible\\\">\\n        <ng-content />\\n        <tui-loader\\n            *ngIf=\\\"async; else content\\\"\\n            size=\\\"l\\\"\\n            [overlay]=\\\"true\\\"\\n            [showLoader]=\\\"loading\\\"\\n        >\\n            <ng-container [ngTemplateOutlet]=\\\"content\\\" />\\n        </tui-loader>\\n    </ng-container>\\n</div>\\n\",\n      styles: [\":host{transition-property:opacity,height,visibility;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:block;opacity:0;transition-delay:1ms}:host._overflow{overflow:hidden}:host._expanded{opacity:1}:host._loading{opacity:.99}.t-wrapper:before,.t-wrapper:after{content:\\\"\\\";display:table}\\n\"]\n    }]\n  }], null, {\n    contentWrapper: [{\n      type: ViewChild,\n      args: ['wrapper']\n    }],\n    content: [{\n      type: ContentChild,\n      args: [TuiExpandContent, {\n        read: TemplateRef\n      }]\n    }],\n    async: [{\n      type: Input\n    }],\n    expandedSetter: [{\n      type: Input,\n      args: ['expanded']\n    }]\n  });\n})();\n\n/**\n * @deprecated use {@link TuiExpand} from @taiga-ui/experimental\n */\nconst TuiExpand = [TuiExpandComponent, TuiExpandContent];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_EXPAND_LOADED, TuiExpand, TuiExpandComponent, TuiExpandContent };", "map": {"version": 3, "names": ["NgIf", "NgTemplateOutlet", "i0", "Directive", "inject", "ChangeDetectorRef", "DestroyRef", "TemplateRef", "Component", "ChangeDetectionStrategy", "ViewChild", "ContentChild", "Input", "takeUntilDestroyed", "tuiParentAnimation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timer", "_c0", "_c1", "TuiExpandComponent_ng_container_2_tui_loader_2_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelementContainer", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "loading", "ɵɵadvance", "content", "TuiExpandComponent_ng_container_2_Template", "ɵɵelementContainerStart", "ɵɵprojection", "ɵɵtemplate", "ɵɵelementContainerEnd", "async", "TuiExpandContent", "ɵfac", "TuiExpandContent_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "State", "Idle", "Loading", "Prepared", "Animated", "LOADER_HEIGHT", "TUI_EXPAND_LOADED", "TuiExpandComponent", "constructor", "cdr", "destroyRef", "state", "expanded", "expandedSetter", "retrigger", "contentVisible", "overflow", "height", "contentWrapper", "nativeElement", "offsetHeight", "Math", "max", "onTransitionEnd", "propertyName", "pseudoElement", "onExpandLoaded", "event", "stopPropagation", "pipe", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TuiExpandComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "TuiExpandComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "TuiExpandComponent_Query", "ɵɵviewQuery", "hostVars", "hostBindings", "TuiExpandComponent_HostBindings", "ɵɵlistener", "TuiExpandComponent_transitionend_self_HostBindingHandler", "$event", "TuiExpandComponent_tui_expand_loaded_HostBindingHandler", "ɵɵattribute", "ɵɵstyleProp", "ɵɵclassProp", "inputs", "ɵɵInputFlags", "None", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "TuiExpandComponent_Template", "ɵɵprojectionDef", "undefined", "dependencies", "styles", "data", "animation", "changeDetection", "imports", "OnPush", "animations", "host", "read", "TuiExpand"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-components-expand.mjs"], "sourcesContent": ["import { NgIf, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, inject, ChangeDetectorRef, DestroyRef, TemplateRef, Component, ChangeDetectionStrategy, ViewChild, ContentChild, Input } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { tuiParentAnimation } from '@taiga-ui/core/animations';\nimport { TuiLoader } from '@taiga-ui/core/components/loader';\nimport { timer } from 'rxjs';\n\n/**\n * @deprecated use {@link TuiExpand} from @taiga-ui/experimental\n */\nclass TuiExpandContent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiExpandContent, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiExpandContent, isStandalone: true, selector: \"[tuiExpandContent]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiExpandContent, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiExpandContent]',\n                }]\n        }] });\n\nconst State = {\n    Idle: 0,\n    Loading: 1,\n    Prepared: 2,\n    Animated: 3,\n};\nconst LOADER_HEIGHT = 48;\n/**\n * An event indicating that async data for expand has finished loading.\n * Dispatch to finish loading states for {@link TuiExpandComponent}.\n */\nconst TUI_EXPAND_LOADED = 'tui-expand-loaded';\n/**\n * @deprecated use {@link TuiExpand} from @taiga-ui/experimental\n */\nclass TuiExpandComponent {\n    constructor() {\n        this.cdr = inject(ChangeDetectorRef);\n        this.destroyRef = inject(DestroyRef);\n        this.state = State.Idle;\n        this.content = null;\n        this.expanded = null;\n        this.async = false;\n    }\n    set expandedSetter(expanded) {\n        if (this.expanded === null) {\n            this.expanded = expanded;\n            return;\n        }\n        if (this.state !== State.Idle) {\n            this.expanded = expanded;\n            this.state = State.Animated;\n            return;\n        }\n        this.expanded = expanded;\n        this.retrigger(this.async && expanded ? State.Loading : State.Animated);\n    }\n    get contentVisible() {\n        return this.expanded || this.state !== State.Idle;\n    }\n    get overflow() {\n        return this.state !== State.Idle;\n    }\n    get loading() {\n        return !!this.expanded && this.async && this.state === State.Loading;\n    }\n    get height() {\n        const { expanded, state, contentWrapper } = this;\n        if ((expanded && state === State.Prepared) ||\n            (!expanded && state === State.Animated)) {\n            return 0;\n        }\n        if (contentWrapper &&\n            ((!expanded && state === State.Prepared) ||\n                (expanded && state === State.Animated))) {\n            return contentWrapper.nativeElement.offsetHeight;\n        }\n        if (contentWrapper && expanded && state === State.Loading) {\n            return Math.max(contentWrapper.nativeElement.offsetHeight, LOADER_HEIGHT);\n        }\n        return null;\n    }\n    onTransitionEnd({ propertyName, pseudoElement }) {\n        if (propertyName === 'opacity' &&\n            !pseudoElement &&\n            this.state === State.Animated) {\n            this.state = State.Idle;\n        }\n    }\n    onExpandLoaded(event) {\n        event.stopPropagation();\n        if (this.state === State.Loading) {\n            this.retrigger(State.Animated);\n        }\n    }\n    retrigger(state) {\n        this.state = State.Prepared;\n        timer(0)\n            .pipe(takeUntilDestroyed(this.destroyRef))\n            .subscribe(() => {\n            // We need delay to re-trigger CSS height transition from the correct number\n            if (this.state !== State.Prepared) {\n                return;\n            }\n            this.state = state;\n            this.cdr.markForCheck();\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiExpandComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiExpandComponent, isStandalone: true, selector: \"tui-expand\", inputs: { async: \"async\", expandedSetter: [\"expanded\", \"expandedSetter\"] }, host: { listeners: { \"transitionend.self\": \"onTransitionEnd($event)\", \"tui-expand-loaded\": \"onExpandLoaded($event)\" }, properties: { \"style.height.px\": \"height\", \"class._loading\": \"loading\", \"class._overflow\": \"overflow\", \"class._expanded\": \"expanded\", \"attr.aria-expanded\": \"expanded\" } }, queries: [{ propertyName: \"content\", first: true, predicate: TuiExpandContent, descendants: true, read: TemplateRef }], viewQueries: [{ propertyName: \"contentWrapper\", first: true, predicate: [\"wrapper\"], descendants: true }], ngImport: i0, template: \"<div\\n    #wrapper\\n    class=\\\"t-wrapper\\\"\\n    @tuiParentAnimation\\n    [@.disabled]=\\\"overflow\\\"\\n>\\n    <ng-container *ngIf=\\\"contentVisible\\\">\\n        <ng-content />\\n        <tui-loader\\n            *ngIf=\\\"async; else content\\\"\\n            size=\\\"l\\\"\\n            [overlay]=\\\"true\\\"\\n            [showLoader]=\\\"loading\\\"\\n        >\\n            <ng-container [ngTemplateOutlet]=\\\"content\\\" />\\n        </tui-loader>\\n    </ng-container>\\n</div>\\n\", styles: [\":host{transition-property:opacity,height,visibility;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:block;opacity:0;transition-delay:1ms}:host._overflow{overflow:hidden}:host._expanded{opacity:1}:host._loading{opacity:.99}.t-wrapper:before,.t-wrapper:after{content:\\\"\\\";display:table}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"component\", type: TuiLoader, selector: \"tui-loader\", inputs: [\"size\", \"inheritColor\", \"overlay\", \"textContent\", \"showLoader\"] }], animations: [tuiParentAnimation], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiExpandComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-expand', imports: [NgIf, NgTemplateOutlet, TuiLoader], changeDetection: ChangeDetectionStrategy.OnPush, animations: [tuiParentAnimation], host: {\n                        '[style.height.px]': 'height',\n                        '[class._loading]': 'loading',\n                        '[class._overflow]': 'overflow',\n                        '[class._expanded]': 'expanded',\n                        '[attr.aria-expanded]': 'expanded',\n                        '(transitionend.self)': 'onTransitionEnd($event)',\n                        [`(${TUI_EXPAND_LOADED})`]: 'onExpandLoaded($event)',\n                    }, template: \"<div\\n    #wrapper\\n    class=\\\"t-wrapper\\\"\\n    @tuiParentAnimation\\n    [@.disabled]=\\\"overflow\\\"\\n>\\n    <ng-container *ngIf=\\\"contentVisible\\\">\\n        <ng-content />\\n        <tui-loader\\n            *ngIf=\\\"async; else content\\\"\\n            size=\\\"l\\\"\\n            [overlay]=\\\"true\\\"\\n            [showLoader]=\\\"loading\\\"\\n        >\\n            <ng-container [ngTemplateOutlet]=\\\"content\\\" />\\n        </tui-loader>\\n    </ng-container>\\n</div>\\n\", styles: [\":host{transition-property:opacity,height,visibility;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;display:block;opacity:0;transition-delay:1ms}:host._overflow{overflow:hidden}:host._expanded{opacity:1}:host._loading{opacity:.99}.t-wrapper:before,.t-wrapper:after{content:\\\"\\\";display:table}\\n\"] }]\n        }], propDecorators: { contentWrapper: [{\n                type: ViewChild,\n                args: ['wrapper']\n            }], content: [{\n                type: ContentChild,\n                args: [TuiExpandContent, { read: TemplateRef }]\n            }], async: [{\n                type: Input\n            }], expandedSetter: [{\n                type: Input,\n                args: ['expanded']\n            }] } });\n\n/**\n * @deprecated use {@link TuiExpand} from @taiga-ui/experimental\n */\nconst TuiExpand = [TuiExpandComponent, TuiExpandContent];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_EXPAND_LOADED, TuiExpand, TuiExpandComponent, TuiExpandContent };\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,gBAAgB,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,KAAK,QAAQ,eAAe;AACjK,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,kBAAkB,QAAQ,2BAA2B;AAC9D,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,KAAK,QAAQ,MAAM;;AAE5B;AACA;AACA;AAFA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,wDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAIqGlB,EAAE,CAAAoB,cAAA,mBAoG0/B,CAAC;IApG7/BpB,EAAE,CAAAqB,kBAAA,KAoGujC,CAAC;IApG1jCrB,EAAE,CAAAsB,YAAA,CAoG8kC,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GApGjlCvB,EAAE,CAAAwB,aAAA;IAAFxB,EAAE,CAAAyB,UAAA,gBAoGy8B,CAAC,eAAAF,MAAA,CAAAG,OAAqC,CAAC;IApGl/B1B,EAAE,CAAA2B,SAAA,CAoGojC,CAAC;IApGvjC3B,EAAE,CAAAyB,UAAA,qBAAAF,MAAA,CAAAK,OAoGojC,CAAC;EAAA;AAAA;AAAA,SAAAC,2CAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApGvjClB,EAAE,CAAA8B,uBAAA,EAoGyzB,CAAC;IApG5zB9B,EAAE,CAAA+B,YAAA,EAoGi1B,CAAC;IApGp1B/B,EAAE,CAAAgC,UAAA,IAAAf,uDAAA,uBAoG0/B,CAAC;IApG7/BjB,EAAE,CAAAiC,qBAAA;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAK,MAAA,GAAFvB,EAAE,CAAAwB,aAAA;IAAFxB,EAAE,CAAA2B,SAAA,EAoGm4B,CAAC;IApGt4B3B,EAAE,CAAAyB,UAAA,SAAAF,MAAA,CAAAW,KAoGm4B,CAAC,aAAAX,MAAA,CAAAK,OAAW,CAAC;EAAA;AAAA;AArGv/B,MAAMO,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACI,IAAI,kBAD+EvC,EAAE,CAAAwC,iBAAA;MAAAC,IAAA,EACJN,gBAAgB;MAAAO,SAAA;MAAAC,UAAA;IAAA,EAAqE;EAAE;AAC1L;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG5C,EAAE,CAAA6C,iBAAA,CAGXV,gBAAgB,EAAc,CAAC;IAC/GM,IAAI,EAAExC,SAAS;IACf6C,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMC,KAAK,GAAG;EACVC,IAAI,EAAE,CAAC;EACPC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE;AACd,CAAC;AACD,MAAMC,aAAa,GAAG,EAAE;AACxB;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,mBAAmB;AAC7C;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,GAAG,GAAGvD,MAAM,CAACC,iBAAiB,CAAC;IACpC,IAAI,CAACuD,UAAU,GAAGxD,MAAM,CAACE,UAAU,CAAC;IACpC,IAAI,CAACuD,KAAK,GAAGX,KAAK,CAACC,IAAI;IACvB,IAAI,CAACrB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACgC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC1B,KAAK,GAAG,KAAK;EACtB;EACA,IAAI2B,cAAcA,CAACD,QAAQ,EAAE;IACzB,IAAI,IAAI,CAACA,QAAQ,KAAK,IAAI,EAAE;MACxB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB;IACJ;IACA,IAAI,IAAI,CAACD,KAAK,KAAKX,KAAK,CAACC,IAAI,EAAE;MAC3B,IAAI,CAACW,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACD,KAAK,GAAGX,KAAK,CAACI,QAAQ;MAC3B;IACJ;IACA,IAAI,CAACQ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,SAAS,CAAC,IAAI,CAAC5B,KAAK,IAAI0B,QAAQ,GAAGZ,KAAK,CAACE,OAAO,GAAGF,KAAK,CAACI,QAAQ,CAAC;EAC3E;EACA,IAAIW,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACH,QAAQ,IAAI,IAAI,CAACD,KAAK,KAAKX,KAAK,CAACC,IAAI;EACrD;EACA,IAAIe,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACL,KAAK,KAAKX,KAAK,CAACC,IAAI;EACpC;EACA,IAAIvB,OAAOA,CAAA,EAAG;IACV,OAAO,CAAC,CAAC,IAAI,CAACkC,QAAQ,IAAI,IAAI,CAAC1B,KAAK,IAAI,IAAI,CAACyB,KAAK,KAAKX,KAAK,CAACE,OAAO;EACxE;EACA,IAAIe,MAAMA,CAAA,EAAG;IACT,MAAM;MAAEL,QAAQ;MAAED,KAAK;MAAEO;IAAe,CAAC,GAAG,IAAI;IAChD,IAAKN,QAAQ,IAAID,KAAK,KAAKX,KAAK,CAACG,QAAQ,IACpC,CAACS,QAAQ,IAAID,KAAK,KAAKX,KAAK,CAACI,QAAS,EAAE;MACzC,OAAO,CAAC;IACZ;IACA,IAAIc,cAAc,KACZ,CAACN,QAAQ,IAAID,KAAK,KAAKX,KAAK,CAACG,QAAQ,IAClCS,QAAQ,IAAID,KAAK,KAAKX,KAAK,CAACI,QAAS,CAAC,EAAE;MAC7C,OAAOc,cAAc,CAACC,aAAa,CAACC,YAAY;IACpD;IACA,IAAIF,cAAc,IAAIN,QAAQ,IAAID,KAAK,KAAKX,KAAK,CAACE,OAAO,EAAE;MACvD,OAAOmB,IAAI,CAACC,GAAG,CAACJ,cAAc,CAACC,aAAa,CAACC,YAAY,EAAEf,aAAa,CAAC;IAC7E;IACA,OAAO,IAAI;EACf;EACAkB,eAAeA,CAAC;IAAEC,YAAY;IAAEC;EAAc,CAAC,EAAE;IAC7C,IAAID,YAAY,KAAK,SAAS,IAC1B,CAACC,aAAa,IACd,IAAI,CAACd,KAAK,KAAKX,KAAK,CAACI,QAAQ,EAAE;MAC/B,IAAI,CAACO,KAAK,GAAGX,KAAK,CAACC,IAAI;IAC3B;EACJ;EACAyB,cAAcA,CAACC,KAAK,EAAE;IAClBA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACjB,KAAK,KAAKX,KAAK,CAACE,OAAO,EAAE;MAC9B,IAAI,CAACY,SAAS,CAACd,KAAK,CAACI,QAAQ,CAAC;IAClC;EACJ;EACAU,SAASA,CAACH,KAAK,EAAE;IACb,IAAI,CAACA,KAAK,GAAGX,KAAK,CAACG,QAAQ;IAC3BrC,KAAK,CAAC,CAAC,CAAC,CACH+D,IAAI,CAAClE,kBAAkB,CAAC,IAAI,CAAC+C,UAAU,CAAC,CAAC,CACzCoB,SAAS,CAAC,MAAM;MACjB;MACA,IAAI,IAAI,CAACnB,KAAK,KAAKX,KAAK,CAACG,QAAQ,EAAE;QAC/B;MACJ;MACA,IAAI,CAACQ,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACF,GAAG,CAACsB,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC3C,IAAI,YAAA4C,2BAAA1C,CAAA;MAAA,YAAAA,CAAA,IAAyFiB,kBAAkB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAAC0B,IAAI,kBApG+EjF,EAAE,CAAAkF,iBAAA;MAAAzC,IAAA,EAoGJc,kBAAkB;MAAAb,SAAA;MAAAyC,cAAA,WAAAC,kCAAAlE,EAAA,EAAAC,GAAA,EAAAkE,QAAA;QAAA,IAAAnE,EAAA;UApGhBlB,EAAE,CAAAsF,cAAA,CAAAD,QAAA,EAoGwelD,gBAAgB,KAA2B9B,WAAW;QAAA;QAAA,IAAAa,EAAA;UAAA,IAAAqE,EAAA;UApGhiBvF,EAAE,CAAAwF,cAAA,CAAAD,EAAA,GAAFvF,EAAE,CAAAyF,WAAA,QAAAtE,GAAA,CAAAS,OAAA,GAAA2D,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA,WAAAC,yBAAA1E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlB,EAAE,CAAA6F,WAAA,CAAA9E,GAAA;QAAA;QAAA,IAAAG,EAAA;UAAA,IAAAqE,EAAA;UAAFvF,EAAE,CAAAwF,cAAA,CAAAD,EAAA,GAAFvF,EAAE,CAAAyF,WAAA,QAAAtE,GAAA,CAAA+C,cAAA,GAAAqB,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,QAAA;MAAAC,YAAA,WAAAC,gCAAA9E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlB,EAAE,CAAAiG,UAAA,gCAAAC,yDAAAC,MAAA;YAAA,OAoGJhF,GAAA,CAAAoD,eAAA,CAAA4B,MAAsB,CAAC;UAAA,CAAN,CAAC,+BAAAC,wDAAAD,MAAA;YAAA,OAAlBhF,GAAA,CAAAuD,cAAA,CAAAyB,MAAqB,CAAC;UAAA,CAAL,CAAC;QAAA;QAAA,IAAAjF,EAAA;UApGhBlB,EAAE,CAAAqG,WAAA,kBAAAlF,GAAA,CAAAyC,QAAA;UAAF5D,EAAE,CAAAsG,WAAA,WAAAnF,GAAA,CAAA8C,MAAA,MAoGa,CAAC;UApGhBjE,EAAE,CAAAuG,WAAA,aAAApF,GAAA,CAAAO,OAoGa,CAAC,cAAAP,GAAA,CAAA6C,QAAD,CAAC,cAAA7C,GAAA,CAAAyC,QAAD,CAAC;QAAA;MAAA;MAAA4C,MAAA;QAAAtE,KAAA;QAAA2B,cAAA,GApGhB7D,EAAE,CAAAyG,YAAA,CAAAC,IAAA;MAAA;MAAA/D,UAAA;MAAAgE,QAAA,GAAF3G,EAAE,CAAA4G,mBAAA;MAAAC,kBAAA,EAAA7F,GAAA;MAAA8F,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAhG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlB,EAAE,CAAAmH,eAAA;UAAFnH,EAAE,CAAAoB,cAAA,eAoG4wB,CAAC;UApG/wBpB,EAAE,CAAAgC,UAAA,IAAAH,0CAAA,yBAoGyzB,CAAC;UApG5zB7B,EAAE,CAAAsB,YAAA,CAoG2mC,CAAC;QAAA;QAAA,IAAAJ,EAAA;UApG9mClB,EAAE,CAAAyB,UAAA,wBAAA2F,SAoG0uB,CAAC,eAAAjG,GAAA,CAAA6C,QAA8B,CAAC;UApG5wBhE,EAAE,CAAA2B,SAAA,EAoGszB,CAAC;UApGzzB3B,EAAE,CAAAyB,UAAA,SAAAN,GAAA,CAAA4C,cAoGszB,CAAC;QAAA;MAAA;MAAAsD,YAAA,GAA4rBvH,IAAI,EAA6FC,gBAAgB,EAAoJc,SAAS;MAAAyG,MAAA;MAAAC,IAAA;QAAAC,SAAA,EAAmH,CAAC5G,kBAAkB;MAAC;MAAA6G,eAAA;IAAA,EAAuD;EAAE;AACxiE;AACA;EAAA,QAAA7E,SAAA,oBAAAA,SAAA,KAtGqG5C,EAAE,CAAA6C,iBAAA,CAsGXU,kBAAkB,EAAc,CAAC;IACjHd,IAAI,EAAEnC,SAAS;IACfwC,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,IAAI;MAAEI,QAAQ,EAAE,YAAY;MAAE2E,OAAO,EAAE,CAAC5H,IAAI,EAAEC,gBAAgB,EAAEc,SAAS,CAAC;MAAE4G,eAAe,EAAElH,uBAAuB,CAACoH,MAAM;MAAEC,UAAU,EAAE,CAAChH,kBAAkB,CAAC;MAAEiH,IAAI,EAAE;QAC9K,mBAAmB,EAAE,QAAQ;QAC7B,kBAAkB,EAAE,SAAS;QAC7B,mBAAmB,EAAE,UAAU;QAC/B,mBAAmB,EAAE,UAAU;QAC/B,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,yBAAyB;QACjD,CAAC,IAAIvE,iBAAiB,GAAG,GAAG;MAChC,CAAC;MAAE2D,QAAQ,EAAE,ycAAyc;MAAEK,MAAM,EAAE,CAAC,4UAA4U;IAAE,CAAC;EAC5zB,CAAC,CAAC,QAAkB;IAAEpD,cAAc,EAAE,CAAC;MAC/BzB,IAAI,EAAEjC,SAAS;MACfsC,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAElB,OAAO,EAAE,CAAC;MACVa,IAAI,EAAEhC,YAAY;MAClBqC,IAAI,EAAE,CAACX,gBAAgB,EAAE;QAAE2F,IAAI,EAAEzH;MAAY,CAAC;IAClD,CAAC,CAAC;IAAE6B,KAAK,EAAE,CAAC;MACRO,IAAI,EAAE/B;IACV,CAAC,CAAC;IAAEmD,cAAc,EAAE,CAAC;MACjBpB,IAAI,EAAE/B,KAAK;MACXoC,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMiF,SAAS,GAAG,CAACxE,kBAAkB,EAAEpB,gBAAgB,CAAC;;AAExD;AACA;AACA;;AAEA,SAASmB,iBAAiB,EAAEyE,SAAS,EAAExE,kBAAkB,EAAEpB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}