{"ast": null, "code": "import { inject, signal, effect, Optional, SkipSelf, ElementRef } from '@angular/core';\nimport { tuiCreateTokenFromFactory, tuiCreateToken, tuiProvide, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { DOCUMENT } from '@angular/common';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { WA_LOCAL_STORAGE, WA_WINDOW } from '@ng-web-apis/common';\nimport { fromEvent, filter, of, map, merge, switchMap, takeUntil, share } from 'rxjs';\nimport { TuiDayOfWeek } from '@taiga-ui/cdk/date-time';\nimport { tuiExtractI18n } from '@taiga-ui/i18n/utils';\nimport { CHAR_NO_BREAK_SPACE } from '@taiga-ui/cdk/constants';\nimport { tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nconst TUI_REDUCED_MOTION = tuiCreateTokenFromFactory(() => inject(DOCUMENT).defaultView?.matchMedia?.('(prefers-reduced-motion: reduce)').matches ?? false);\n\n/**\n * Speed of all Taiga UI animations. 1 equals 300ms.\n */\nconst TUI_ANIMATIONS_SPEED = tuiCreateTokenFromFactory(() => inject(TUI_REDUCED_MOTION) ? 0 : 1);\nconst TUI_ASSETS_PATH = tuiCreateToken('assets/taiga-ui/icons');\nfunction tuiAssetsPathProvider(useValue) {\n  return {\n    provide: TUI_ASSETS_PATH,\n    useValue\n  };\n}\nconst TUI_AUXILIARY = tuiCreateToken(null);\nfunction tuiAsAuxiliary(x) {\n  return tuiProvide(TUI_AUXILIARY, x);\n}\n\n// TODO: Rename `ellipsis` to `more` in the next major version\nconst COMMON_ICONS = {\n  check: '@tui.check',\n  close: '@tui.x',\n  error: '@tui.circle-alert',\n  more: '@tui.chevron-right',\n  search: '@tui.search',\n  ellipsis: '@tui.ellipsis'\n};\nconst TUI_COMMON_ICONS = tuiCreateToken(COMMON_ICONS);\nfunction tuiCommonIconsProvider(icons) {\n  return tuiProvideOptions(TUI_COMMON_ICONS, icons, COMMON_ICONS);\n}\nconst TUI_DARK_MODE_DEFAULT_KEY = 'tuiDark';\nconst TUI_DARK_MODE_KEY = tuiCreateToken(TUI_DARK_MODE_DEFAULT_KEY);\nconst TUI_DARK_MODE = tuiCreateTokenFromFactory(() => {\n  let automatic = true;\n  const storage = inject(WA_LOCAL_STORAGE);\n  const key = inject(TUI_DARK_MODE_KEY);\n  const saved = storage?.getItem(key);\n  const media = inject(WA_WINDOW).matchMedia('(prefers-color-scheme: dark)');\n  const result = signal(Boolean((saved && JSON.parse(saved)) ?? media.matches));\n  fromEvent(media, 'change').pipe(filter(() => !storage?.getItem(key)), takeUntilDestroyed()).subscribe(() => {\n    automatic = true;\n    result.set(media.matches);\n  });\n  effect(() => {\n    const value = String(result());\n    if (automatic) {\n      automatic = false;\n    } else {\n      storage?.setItem(key, value);\n    }\n  });\n  return Object.assign(result, {\n    reset: () => {\n      storage?.removeItem(key);\n      automatic = true;\n      result.set(media.matches);\n    }\n  });\n});\nconst TUI_DEFAULT_DATE_FORMAT = {\n  mode: 'DMY',\n  separator: '.'\n};\n/**\n * Formatting configuration for displayed dates\n */\nconst TUI_DATE_FORMAT = tuiCreateToken(of(TUI_DEFAULT_DATE_FORMAT));\nfunction tuiDateFormatProvider(options) {\n  return {\n    provide: TUI_DATE_FORMAT,\n    deps: [[new Optional(), new SkipSelf(), TUI_DATE_FORMAT]],\n    useFactory: parent => (parent || of(TUI_DEFAULT_DATE_FORMAT)).pipe(map(format => ({\n      ...format,\n      ...options\n    })))\n  };\n}\n\n/**\n * Token for adding data-type attribute to calendar cell\n */\nconst TUI_DAY_TYPE_HANDLER = tuiCreateToken(day => day.isWeekend ? 'weekend' : 'weekday');\n\n/**\n * The first day of the week index\n */\nconst TUI_FIRST_DAY_OF_WEEK = tuiCreateToken(TuiDayOfWeek.Monday);\n\n/**\n * Localized months names\n */\nconst TUI_MONTHS = tuiCreateTokenFromFactory(tuiExtractI18n('months'));\n/**\n * i18n 'close' word\n */\nconst TUI_CLOSE_WORD = tuiCreateTokenFromFactory(tuiExtractI18n('close'));\n/**\n * i18n 'clear' word\n */\nconst TUI_CLEAR_WORD = tuiCreateTokenFromFactory(tuiExtractI18n('clear'));\n/**\n * i18n 'Nothing found' message\n */\nconst TUI_NOTHING_FOUND_MESSAGE = tuiCreateTokenFromFactory(tuiExtractI18n('nothingFoundMessage'));\n/**\n * i18n of error message\n */\nconst TUI_DEFAULT_ERROR_MESSAGE = tuiCreateTokenFromFactory(tuiExtractI18n('defaultErrorMessage'));\n/**\n * spin i18n texts\n */\nconst TUI_SPIN_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('spinTexts'));\n/**\n * calendars i18n texts\n */\nconst TUI_SHORT_WEEK_DAYS = tuiCreateTokenFromFactory(tuiExtractI18n('shortWeekDays'));\nconst TUI_ICON_START = tuiCreateToken('');\nconst TUI_ICON_END = tuiCreateToken('');\nconst TUI_ICON_REGISTRY = tuiCreateToken({});\n/**\n * @deprecated: use {@link TUI_ICON_REGISTRY}\n */\nconst TUI_ICON_STARTS = TUI_ICON_REGISTRY;\nfunction tuiIconsProvider(icons) {\n  return {\n    provide: TUI_ICON_REGISTRY,\n    useFactory: () => ({\n      ...(inject(TUI_ICON_REGISTRY, {\n        skipSelf: true,\n        optional: true\n      }) || {}),\n      ...Object.fromEntries(Object.entries(icons).map(([key, value]) => [key, `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(value)}`]))\n    })\n  };\n}\nconst TUI_ICON_RESOLVER = tuiCreateTokenFromFactory(() => {\n  const path = inject(TUI_ASSETS_PATH);\n  return icon => `${path}/${icon.replace('@tui.', '').split('.').join('/')}.svg`;\n});\n/**\n * @deprecated use {@link TUI_ICON_RESOLVER}\n */\nconst TUI_ICON_START_RESOLVER = TUI_ICON_RESOLVER;\nfunction tuiInjectIconResolver() {\n  const icons = inject(TUI_ICON_REGISTRY);\n  const resolver = inject(TUI_ICON_RESOLVER);\n  return icon => !icon || icon.includes('/') ? icon : icons[icon] ?? resolver(icon);\n}\nfunction tuiIconResolverProvider(useValue) {\n  return {\n    provide: TUI_ICON_RESOLVER,\n    useValue\n  };\n}\n\n/**\n * Token for media constant\n */\nconst TUI_MEDIA = tuiCreateToken({\n  mobile: 768,\n  desktopSmall: 1024,\n  desktopLarge: 1280\n});\nconst TUI_DEFAULT_NUMBER_FORMAT = {\n  precision: NaN,\n  decimalSeparator: '.',\n  thousandSeparator: CHAR_NO_BREAK_SPACE,\n  rounding: 'truncate',\n  decimalMode: 'pad'\n};\n/**\n * Formatting configuration for displayed numbers\n */\nconst TUI_NUMBER_FORMAT = tuiCreateToken(of(TUI_DEFAULT_NUMBER_FORMAT));\nfunction tuiNumberFormatProvider(options) {\n  return {\n    provide: TUI_NUMBER_FORMAT,\n    deps: [[new Optional(), new SkipSelf(), TUI_NUMBER_FORMAT]],\n    useFactory: parent => (parent || of(TUI_DEFAULT_NUMBER_FORMAT)).pipe(map(format => ({\n      ...format,\n      ...options\n    })))\n  };\n}\nconst TUI_SCROLL_REF = tuiCreateTokenFromFactory(() => new ElementRef(inject(DOCUMENT).documentElement));\n\n/**\n * A stream of possible selection changes\n */\nconst TUI_SELECTION_STREAM = tuiCreateTokenFromFactory(() => {\n  const doc = inject(DOCUMENT);\n  return merge(tuiTypedFromEvent(doc, 'selectionchange'), tuiTypedFromEvent(doc, 'mouseup'), tuiTypedFromEvent(doc, 'mousedown').pipe(switchMap(() => tuiTypedFromEvent(doc, 'mousemove').pipe(takeUntil(tuiTypedFromEvent(doc, 'mouseup'))))), tuiTypedFromEvent(doc, 'keydown'), tuiTypedFromEvent(doc, 'keyup')).pipe(share());\n});\nconst TUI_SPIN_ICONS = tuiCreateToken({\n  decrement: '@tui.chevron-left',\n  increment: '@tui.chevron-right'\n});\nconst TUI_THEME = tuiCreateToken('Taiga UI');\n\n/**\n * Viewport accessor\n */\nconst TUI_VIEWPORT = tuiCreateTokenFromFactory(() => {\n  const win = inject(WA_WINDOW);\n  return {\n    type: 'viewport',\n    getClientRect() {\n      const {\n        height = 0,\n        offsetTop = 0\n      } = win.visualViewport || {};\n      const rect = {\n        top: 0,\n        left: 0,\n        right: win.innerWidth,\n        bottom: win.innerHeight,\n        width: win.innerWidth,\n        height: height + offsetTop || win.innerHeight,\n        x: 0,\n        y: 0\n      };\n      return {\n        ...rect,\n        toJSON: () => JSON.stringify(rect)\n      };\n    }\n  };\n});\nfunction tuiAsViewport(accessor) {\n  return tuiProvide(TUI_VIEWPORT, accessor);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_ANIMATIONS_SPEED, TUI_ASSETS_PATH, TUI_AUXILIARY, TUI_CLEAR_WORD, TUI_CLOSE_WORD, TUI_COMMON_ICONS, TUI_DARK_MODE, TUI_DARK_MODE_DEFAULT_KEY, TUI_DARK_MODE_KEY, TUI_DATE_FORMAT, TUI_DAY_TYPE_HANDLER, TUI_DEFAULT_DATE_FORMAT, TUI_DEFAULT_ERROR_MESSAGE, TUI_DEFAULT_NUMBER_FORMAT, TUI_FIRST_DAY_OF_WEEK, TUI_ICON_END, TUI_ICON_REGISTRY, TUI_ICON_RESOLVER, TUI_ICON_START, TUI_ICON_STARTS, TUI_ICON_START_RESOLVER, TUI_MEDIA, TUI_MONTHS, TUI_NOTHING_FOUND_MESSAGE, TUI_NUMBER_FORMAT, TUI_REDUCED_MOTION, TUI_SCROLL_REF, TUI_SELECTION_STREAM, TUI_SHORT_WEEK_DAYS, TUI_SPIN_ICONS, TUI_SPIN_TEXTS, TUI_THEME, TUI_VIEWPORT, tuiAsAuxiliary, tuiAsViewport, tuiAssetsPathProvider, tuiCommonIconsProvider, tuiDateFormatProvider, tuiIconResolverProvider, tuiIconsProvider, tuiInjectIconResolver, tuiNumberFormatProvider };", "map": {"version": 3, "names": ["inject", "signal", "effect", "Optional", "SkipSelf", "ElementRef", "tuiCreateTokenFromFactory", "tuiCreateToken", "tui<PERSON><PERSON><PERSON>", "tuiProvideOptions", "DOCUMENT", "takeUntilDestroyed", "WA_LOCAL_STORAGE", "WA_WINDOW", "fromEvent", "filter", "of", "map", "merge", "switchMap", "takeUntil", "share", "TuiDayOfWeek", "tuiExtractI18n", "CHAR_NO_BREAK_SPACE", "tuiTypedFromEvent", "TUI_REDUCED_MOTION", "defaultView", "matchMedia", "matches", "TUI_ANIMATIONS_SPEED", "TUI_ASSETS_PATH", "tui<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useValue", "provide", "TUI_AUXILIARY", "tuiAsAuxiliary", "x", "COMMON_ICONS", "check", "close", "error", "more", "search", "ellipsis", "TUI_COMMON_ICONS", "tuiCommonIconsProvider", "icons", "TUI_DARK_MODE_DEFAULT_KEY", "TUI_DARK_MODE_KEY", "TUI_DARK_MODE", "automatic", "storage", "key", "saved", "getItem", "media", "result", "Boolean", "JSON", "parse", "pipe", "subscribe", "set", "value", "String", "setItem", "Object", "assign", "reset", "removeItem", "TUI_DEFAULT_DATE_FORMAT", "mode", "separator", "TUI_DATE_FORMAT", "tuiDateFormatProvider", "options", "deps", "useFactory", "parent", "format", "TUI_DAY_TYPE_HANDLER", "day", "isWeekend", "TUI_FIRST_DAY_OF_WEEK", "Monday", "TUI_MONTHS", "TUI_CLOSE_WORD", "TUI_CLEAR_WORD", "TUI_NOTHING_FOUND_MESSAGE", "TUI_DEFAULT_ERROR_MESSAGE", "TUI_SPIN_TEXTS", "TUI_SHORT_WEEK_DAYS", "TUI_ICON_START", "TUI_ICON_END", "TUI_ICON_REGISTRY", "TUI_ICON_STARTS", "tuiIconsProvider", "skipSelf", "optional", "fromEntries", "entries", "encodeURIComponent", "TUI_ICON_RESOLVER", "path", "icon", "replace", "split", "join", "TUI_ICON_START_RESOLVER", "tuiInjectIconResolver", "resolver", "includes", "tuiIconResolverProvider", "TUI_MEDIA", "mobile", "desktopSmall", "desktopLarge", "TUI_DEFAULT_NUMBER_FORMAT", "precision", "NaN", "decimalSeparator", "thousandSeparator", "rounding", "decimalMode", "TUI_NUMBER_FORMAT", "tuiNumberFormatProvider", "TUI_SCROLL_REF", "documentElement", "TUI_SELECTION_STREAM", "doc", "TUI_SPIN_ICONS", "decrement", "increment", "TUI_THEME", "TUI_VIEWPORT", "win", "type", "getClientRect", "height", "offsetTop", "visualViewport", "rect", "top", "left", "right", "innerWidth", "bottom", "innerHeight", "width", "y", "toJSON", "stringify", "tuiAsViewport", "accessor"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-tokens.mjs"], "sourcesContent": ["import { inject, signal, effect, Optional, SkipSelf, ElementRef } from '@angular/core';\nimport { tuiCreateTokenFromFactory, tuiCreateToken, tuiProvide, tuiProvideOptions } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { DOCUMENT } from '@angular/common';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { WA_LOCAL_STORAGE, WA_WINDOW } from '@ng-web-apis/common';\nimport { fromEvent, filter, of, map, merge, switchMap, takeUntil, share } from 'rxjs';\nimport { TuiDayOfWeek } from '@taiga-ui/cdk/date-time';\nimport { tuiExtractI18n } from '@taiga-ui/i18n/utils';\nimport { CHAR_NO_BREAK_SPACE } from '@taiga-ui/cdk/constants';\nimport { tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\n\nconst TUI_REDUCED_MOTION = tuiCreateTokenFromFactory(() => inject(DOCUMENT).defaultView?.matchMedia?.('(prefers-reduced-motion: reduce)')\n    .matches ?? false);\n\n/**\n * Speed of all Taiga UI animations. 1 equals 300ms.\n */\nconst TUI_ANIMATIONS_SPEED = tuiCreateTokenFromFactory(() => inject(TUI_REDUCED_MOTION) ? 0 : 1);\n\nconst TUI_ASSETS_PATH = tuiCreateToken('assets/taiga-ui/icons');\nfunction tuiAssetsPathProvider(useValue) {\n    return {\n        provide: TUI_ASSETS_PATH,\n        useValue,\n    };\n}\n\nconst TUI_AUXILIARY = tuiCreateToken(null);\nfunction tuiAsAuxiliary(x) {\n    return tuiProvide(TUI_AUXILIARY, x);\n}\n\n// TODO: Rename `ellipsis` to `more` in the next major version\nconst COMMON_ICONS = {\n    check: '@tui.check',\n    close: '@tui.x',\n    error: '@tui.circle-alert',\n    more: '@tui.chevron-right',\n    search: '@tui.search',\n    ellipsis: '@tui.ellipsis',\n};\nconst TUI_COMMON_ICONS = tuiCreateToken(COMMON_ICONS);\nfunction tuiCommonIconsProvider(icons) {\n    return tuiProvideOptions(TUI_COMMON_ICONS, icons, COMMON_ICONS);\n}\n\nconst TUI_DARK_MODE_DEFAULT_KEY = 'tuiDark';\nconst TUI_DARK_MODE_KEY = tuiCreateToken(TUI_DARK_MODE_DEFAULT_KEY);\nconst TUI_DARK_MODE = tuiCreateTokenFromFactory(() => {\n    let automatic = true;\n    const storage = inject(WA_LOCAL_STORAGE);\n    const key = inject(TUI_DARK_MODE_KEY);\n    const saved = storage?.getItem(key);\n    const media = inject(WA_WINDOW).matchMedia('(prefers-color-scheme: dark)');\n    const result = signal(Boolean((saved && JSON.parse(saved)) ?? media.matches));\n    fromEvent(media, 'change')\n        .pipe(filter(() => !storage?.getItem(key)), takeUntilDestroyed())\n        .subscribe(() => {\n        automatic = true;\n        result.set(media.matches);\n    });\n    effect(() => {\n        const value = String(result());\n        if (automatic) {\n            automatic = false;\n        }\n        else {\n            storage?.setItem(key, value);\n        }\n    });\n    return Object.assign(result, {\n        reset: () => {\n            storage?.removeItem(key);\n            automatic = true;\n            result.set(media.matches);\n        },\n    });\n});\n\nconst TUI_DEFAULT_DATE_FORMAT = {\n    mode: 'DMY',\n    separator: '.',\n};\n/**\n * Formatting configuration for displayed dates\n */\nconst TUI_DATE_FORMAT = tuiCreateToken(of(TUI_DEFAULT_DATE_FORMAT));\nfunction tuiDateFormatProvider(options) {\n    return {\n        provide: TUI_DATE_FORMAT,\n        deps: [[new Optional(), new SkipSelf(), TUI_DATE_FORMAT]],\n        useFactory: (parent) => (parent || of(TUI_DEFAULT_DATE_FORMAT)).pipe(map((format) => ({ ...format, ...options }))),\n    };\n}\n\n/**\n * Token for adding data-type attribute to calendar cell\n */\nconst TUI_DAY_TYPE_HANDLER = tuiCreateToken((day) => day.isWeekend ? 'weekend' : 'weekday');\n\n/**\n * The first day of the week index\n */\nconst TUI_FIRST_DAY_OF_WEEK = tuiCreateToken(TuiDayOfWeek.Monday);\n\n/**\n * Localized months names\n */\nconst TUI_MONTHS = tuiCreateTokenFromFactory(tuiExtractI18n('months'));\n/**\n * i18n 'close' word\n */\nconst TUI_CLOSE_WORD = tuiCreateTokenFromFactory(tuiExtractI18n('close'));\n/**\n * i18n 'clear' word\n */\nconst TUI_CLEAR_WORD = tuiCreateTokenFromFactory(tuiExtractI18n('clear'));\n/**\n * i18n 'Nothing found' message\n */\nconst TUI_NOTHING_FOUND_MESSAGE = tuiCreateTokenFromFactory(tuiExtractI18n('nothingFoundMessage'));\n/**\n * i18n of error message\n */\nconst TUI_DEFAULT_ERROR_MESSAGE = tuiCreateTokenFromFactory(tuiExtractI18n('defaultErrorMessage'));\n/**\n * spin i18n texts\n */\nconst TUI_SPIN_TEXTS = tuiCreateTokenFromFactory(tuiExtractI18n('spinTexts'));\n/**\n * calendars i18n texts\n */\nconst TUI_SHORT_WEEK_DAYS = tuiCreateTokenFromFactory(tuiExtractI18n('shortWeekDays'));\n\nconst TUI_ICON_START = tuiCreateToken('');\nconst TUI_ICON_END = tuiCreateToken('');\n\nconst TUI_ICON_REGISTRY = tuiCreateToken({});\n/**\n * @deprecated: use {@link TUI_ICON_REGISTRY}\n */\nconst TUI_ICON_STARTS = TUI_ICON_REGISTRY;\nfunction tuiIconsProvider(icons) {\n    return {\n        provide: TUI_ICON_REGISTRY,\n        useFactory: () => ({\n            ...(inject(TUI_ICON_REGISTRY, { skipSelf: true, optional: true }) || {}),\n            ...Object.fromEntries(Object.entries(icons).map(([key, value]) => [\n                key,\n                `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(value)}`,\n            ])),\n        }),\n    };\n}\n\nconst TUI_ICON_RESOLVER = tuiCreateTokenFromFactory(() => {\n    const path = inject(TUI_ASSETS_PATH);\n    return (icon) => `${path}/${icon.replace('@tui.', '').split('.').join('/')}.svg`;\n});\n/**\n * @deprecated use {@link TUI_ICON_RESOLVER}\n */\nconst TUI_ICON_START_RESOLVER = TUI_ICON_RESOLVER;\nfunction tuiInjectIconResolver() {\n    const icons = inject(TUI_ICON_REGISTRY);\n    const resolver = inject(TUI_ICON_RESOLVER);\n    return (icon) => !icon || icon.includes('/') ? icon : (icons[icon] ?? resolver(icon));\n}\nfunction tuiIconResolverProvider(useValue) {\n    return { provide: TUI_ICON_RESOLVER, useValue };\n}\n\n/**\n * Token for media constant\n */\nconst TUI_MEDIA = tuiCreateToken({\n    mobile: 768,\n    desktopSmall: 1024,\n    desktopLarge: 1280,\n});\n\nconst TUI_DEFAULT_NUMBER_FORMAT = {\n    precision: NaN,\n    decimalSeparator: '.',\n    thousandSeparator: CHAR_NO_BREAK_SPACE,\n    rounding: 'truncate',\n    decimalMode: 'pad',\n};\n/**\n * Formatting configuration for displayed numbers\n */\nconst TUI_NUMBER_FORMAT = tuiCreateToken(of(TUI_DEFAULT_NUMBER_FORMAT));\nfunction tuiNumberFormatProvider(options) {\n    return {\n        provide: TUI_NUMBER_FORMAT,\n        deps: [[new Optional(), new SkipSelf(), TUI_NUMBER_FORMAT]],\n        useFactory: (parent) => (parent || of(TUI_DEFAULT_NUMBER_FORMAT)).pipe(map((format) => ({ ...format, ...options }))),\n    };\n}\n\nconst TUI_SCROLL_REF = tuiCreateTokenFromFactory(() => new ElementRef(inject(DOCUMENT).documentElement));\n\n/**\n * A stream of possible selection changes\n */\nconst TUI_SELECTION_STREAM = tuiCreateTokenFromFactory(() => {\n    const doc = inject(DOCUMENT);\n    return merge(tuiTypedFromEvent(doc, 'selectionchange'), tuiTypedFromEvent(doc, 'mouseup'), tuiTypedFromEvent(doc, 'mousedown').pipe(switchMap(() => tuiTypedFromEvent(doc, 'mousemove').pipe(takeUntil(tuiTypedFromEvent(doc, 'mouseup'))))), tuiTypedFromEvent(doc, 'keydown'), tuiTypedFromEvent(doc, 'keyup')).pipe(share());\n});\n\nconst TUI_SPIN_ICONS = tuiCreateToken({\n    decrement: '@tui.chevron-left',\n    increment: '@tui.chevron-right',\n});\n\nconst TUI_THEME = tuiCreateToken('Taiga UI');\n\n/**\n * Viewport accessor\n */\nconst TUI_VIEWPORT = tuiCreateTokenFromFactory(() => {\n    const win = inject(WA_WINDOW);\n    return {\n        type: 'viewport',\n        getClientRect() {\n            const { height = 0, offsetTop = 0 } = win.visualViewport || {};\n            const rect = {\n                top: 0,\n                left: 0,\n                right: win.innerWidth,\n                bottom: win.innerHeight,\n                width: win.innerWidth,\n                height: height + offsetTop || win.innerHeight,\n                x: 0,\n                y: 0,\n            };\n            return {\n                ...rect,\n                toJSON: () => JSON.stringify(rect),\n            };\n        },\n    };\n});\nfunction tuiAsViewport(accessor) {\n    return tuiProvide(TUI_VIEWPORT, accessor);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_ANIMATIONS_SPEED, TUI_ASSETS_PATH, TUI_AUXILIARY, TUI_CLEAR_WORD, TUI_CLOSE_WORD, TUI_COMMON_ICONS, TUI_DARK_MODE, TUI_DARK_MODE_DEFAULT_KEY, TUI_DARK_MODE_KEY, TUI_DATE_FORMAT, TUI_DAY_TYPE_HANDLER, TUI_DEFAULT_DATE_FORMAT, TUI_DEFAULT_ERROR_MESSAGE, TUI_DEFAULT_NUMBER_FORMAT, TUI_FIRST_DAY_OF_WEEK, TUI_ICON_END, TUI_ICON_REGISTRY, TUI_ICON_RESOLVER, TUI_ICON_START, TUI_ICON_STARTS, TUI_ICON_START_RESOLVER, TUI_MEDIA, TUI_MONTHS, TUI_NOTHING_FOUND_MESSAGE, TUI_NUMBER_FORMAT, TUI_REDUCED_MOTION, TUI_SCROLL_REF, TUI_SELECTION_STREAM, TUI_SHORT_WEEK_DAYS, TUI_SPIN_ICONS, TUI_SPIN_TEXTS, TUI_THEME, TUI_VIEWPORT, tuiAsAuxiliary, tuiAsViewport, tuiAssetsPathProvider, tuiCommonIconsProvider, tuiDateFormatProvider, tuiIconResolverProvider, tuiIconsProvider, tuiInjectIconResolver, tuiNumberFormatProvider };\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,eAAe;AACtF,SAASC,yBAAyB,EAAEC,cAAc,EAAEC,UAAU,EAAEC,iBAAiB,QAAQ,mCAAmC;AAC5H,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,gBAAgB,EAAEC,SAAS,QAAQ,qBAAqB;AACjE,SAASC,SAAS,EAAEC,MAAM,EAAEC,EAAE,EAAEC,GAAG,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,KAAK,QAAQ,MAAM;AACrF,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,iBAAiB,QAAQ,2BAA2B;AAE7D,MAAMC,kBAAkB,GAAGpB,yBAAyB,CAAC,MAAMN,MAAM,CAACU,QAAQ,CAAC,CAACiB,WAAW,EAAEC,UAAU,GAAG,kCAAkC,CAAC,CACpIC,OAAO,IAAI,KAAK,CAAC;;AAEtB;AACA;AACA;AACA,MAAMC,oBAAoB,GAAGxB,yBAAyB,CAAC,MAAMN,MAAM,CAAC0B,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAEhG,MAAMK,eAAe,GAAGxB,cAAc,CAAC,uBAAuB,CAAC;AAC/D,SAASyB,qBAAqBA,CAACC,QAAQ,EAAE;EACrC,OAAO;IACHC,OAAO,EAAEH,eAAe;IACxBE;EACJ,CAAC;AACL;AAEA,MAAME,aAAa,GAAG5B,cAAc,CAAC,IAAI,CAAC;AAC1C,SAAS6B,cAAcA,CAACC,CAAC,EAAE;EACvB,OAAO7B,UAAU,CAAC2B,aAAa,EAAEE,CAAC,CAAC;AACvC;;AAEA;AACA,MAAMC,YAAY,GAAG;EACjBC,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,mBAAmB;EAC1BC,IAAI,EAAE,oBAAoB;EAC1BC,MAAM,EAAE,aAAa;EACrBC,QAAQ,EAAE;AACd,CAAC;AACD,MAAMC,gBAAgB,GAAGtC,cAAc,CAAC+B,YAAY,CAAC;AACrD,SAASQ,sBAAsBA,CAACC,KAAK,EAAE;EACnC,OAAOtC,iBAAiB,CAACoC,gBAAgB,EAAEE,KAAK,EAAET,YAAY,CAAC;AACnE;AAEA,MAAMU,yBAAyB,GAAG,SAAS;AAC3C,MAAMC,iBAAiB,GAAG1C,cAAc,CAACyC,yBAAyB,CAAC;AACnE,MAAME,aAAa,GAAG5C,yBAAyB,CAAC,MAAM;EAClD,IAAI6C,SAAS,GAAG,IAAI;EACpB,MAAMC,OAAO,GAAGpD,MAAM,CAACY,gBAAgB,CAAC;EACxC,MAAMyC,GAAG,GAAGrD,MAAM,CAACiD,iBAAiB,CAAC;EACrC,MAAMK,KAAK,GAAGF,OAAO,EAAEG,OAAO,CAACF,GAAG,CAAC;EACnC,MAAMG,KAAK,GAAGxD,MAAM,CAACa,SAAS,CAAC,CAACe,UAAU,CAAC,8BAA8B,CAAC;EAC1E,MAAM6B,MAAM,GAAGxD,MAAM,CAACyD,OAAO,CAAC,CAACJ,KAAK,IAAIK,IAAI,CAACC,KAAK,CAACN,KAAK,CAAC,KAAKE,KAAK,CAAC3B,OAAO,CAAC,CAAC;EAC7Ef,SAAS,CAAC0C,KAAK,EAAE,QAAQ,CAAC,CACrBK,IAAI,CAAC9C,MAAM,CAAC,MAAM,CAACqC,OAAO,EAAEG,OAAO,CAACF,GAAG,CAAC,CAAC,EAAE1C,kBAAkB,CAAC,CAAC,CAAC,CAChEmD,SAAS,CAAC,MAAM;IACjBX,SAAS,GAAG,IAAI;IAChBM,MAAM,CAACM,GAAG,CAACP,KAAK,CAAC3B,OAAO,CAAC;EAC7B,CAAC,CAAC;EACF3B,MAAM,CAAC,MAAM;IACT,MAAM8D,KAAK,GAAGC,MAAM,CAACR,MAAM,CAAC,CAAC,CAAC;IAC9B,IAAIN,SAAS,EAAE;MACXA,SAAS,GAAG,KAAK;IACrB,CAAC,MACI;MACDC,OAAO,EAAEc,OAAO,CAACb,GAAG,EAAEW,KAAK,CAAC;IAChC;EACJ,CAAC,CAAC;EACF,OAAOG,MAAM,CAACC,MAAM,CAACX,MAAM,EAAE;IACzBY,KAAK,EAAEA,CAAA,KAAM;MACTjB,OAAO,EAAEkB,UAAU,CAACjB,GAAG,CAAC;MACxBF,SAAS,GAAG,IAAI;MAChBM,MAAM,CAACM,GAAG,CAACP,KAAK,CAAC3B,OAAO,CAAC;IAC7B;EACJ,CAAC,CAAC;AACN,CAAC,CAAC;AAEF,MAAM0C,uBAAuB,GAAG;EAC5BC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAE;AACf,CAAC;AACD;AACA;AACA;AACA,MAAMC,eAAe,GAAGnE,cAAc,CAACS,EAAE,CAACuD,uBAAuB,CAAC,CAAC;AACnE,SAASI,qBAAqBA,CAACC,OAAO,EAAE;EACpC,OAAO;IACH1C,OAAO,EAAEwC,eAAe;IACxBG,IAAI,EAAE,CAAC,CAAC,IAAI1E,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAEsE,eAAe,CAAC,CAAC;IACzDI,UAAU,EAAGC,MAAM,IAAK,CAACA,MAAM,IAAI/D,EAAE,CAACuD,uBAAuB,CAAC,EAAEV,IAAI,CAAC5C,GAAG,CAAE+D,MAAM,KAAM;MAAE,GAAGA,MAAM;MAAE,GAAGJ;IAAQ,CAAC,CAAC,CAAC;EACrH,CAAC;AACL;;AAEA;AACA;AACA;AACA,MAAMK,oBAAoB,GAAG1E,cAAc,CAAE2E,GAAG,IAAKA,GAAG,CAACC,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;;AAE3F;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG7E,cAAc,CAACe,YAAY,CAAC+D,MAAM,CAAC;;AAEjE;AACA;AACA;AACA,MAAMC,UAAU,GAAGhF,yBAAyB,CAACiB,cAAc,CAAC,QAAQ,CAAC,CAAC;AACtE;AACA;AACA;AACA,MAAMgE,cAAc,GAAGjF,yBAAyB,CAACiB,cAAc,CAAC,OAAO,CAAC,CAAC;AACzE;AACA;AACA;AACA,MAAMiE,cAAc,GAAGlF,yBAAyB,CAACiB,cAAc,CAAC,OAAO,CAAC,CAAC;AACzE;AACA;AACA;AACA,MAAMkE,yBAAyB,GAAGnF,yBAAyB,CAACiB,cAAc,CAAC,qBAAqB,CAAC,CAAC;AAClG;AACA;AACA;AACA,MAAMmE,yBAAyB,GAAGpF,yBAAyB,CAACiB,cAAc,CAAC,qBAAqB,CAAC,CAAC;AAClG;AACA;AACA;AACA,MAAMoE,cAAc,GAAGrF,yBAAyB,CAACiB,cAAc,CAAC,WAAW,CAAC,CAAC;AAC7E;AACA;AACA;AACA,MAAMqE,mBAAmB,GAAGtF,yBAAyB,CAACiB,cAAc,CAAC,eAAe,CAAC,CAAC;AAEtF,MAAMsE,cAAc,GAAGtF,cAAc,CAAC,EAAE,CAAC;AACzC,MAAMuF,YAAY,GAAGvF,cAAc,CAAC,EAAE,CAAC;AAEvC,MAAMwF,iBAAiB,GAAGxF,cAAc,CAAC,CAAC,CAAC,CAAC;AAC5C;AACA;AACA;AACA,MAAMyF,eAAe,GAAGD,iBAAiB;AACzC,SAASE,gBAAgBA,CAAClD,KAAK,EAAE;EAC7B,OAAO;IACHb,OAAO,EAAE6D,iBAAiB;IAC1BjB,UAAU,EAAEA,CAAA,MAAO;MACf,IAAI9E,MAAM,CAAC+F,iBAAiB,EAAE;QAAEG,QAAQ,EAAE,IAAI;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;MACxE,GAAGhC,MAAM,CAACiC,WAAW,CAACjC,MAAM,CAACkC,OAAO,CAACtD,KAAK,CAAC,CAAC9B,GAAG,CAAC,CAAC,CAACoC,GAAG,EAAEW,KAAK,CAAC,KAAK,CAC9DX,GAAG,EACH,oCAAoCiD,kBAAkB,CAACtC,KAAK,CAAC,EAAE,CAClE,CAAC;IACN,CAAC;EACL,CAAC;AACL;AAEA,MAAMuC,iBAAiB,GAAGjG,yBAAyB,CAAC,MAAM;EACtD,MAAMkG,IAAI,GAAGxG,MAAM,CAAC+B,eAAe,CAAC;EACpC,OAAQ0E,IAAI,IAAK,GAAGD,IAAI,IAAIC,IAAI,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,MAAM;AACpF,CAAC,CAAC;AACF;AACA;AACA;AACA,MAAMC,uBAAuB,GAAGN,iBAAiB;AACjD,SAASO,qBAAqBA,CAAA,EAAG;EAC7B,MAAM/D,KAAK,GAAG/C,MAAM,CAAC+F,iBAAiB,CAAC;EACvC,MAAMgB,QAAQ,GAAG/G,MAAM,CAACuG,iBAAiB,CAAC;EAC1C,OAAQE,IAAI,IAAK,CAACA,IAAI,IAAIA,IAAI,CAACO,QAAQ,CAAC,GAAG,CAAC,GAAGP,IAAI,GAAI1D,KAAK,CAAC0D,IAAI,CAAC,IAAIM,QAAQ,CAACN,IAAI,CAAE;AACzF;AACA,SAASQ,uBAAuBA,CAAChF,QAAQ,EAAE;EACvC,OAAO;IAAEC,OAAO,EAAEqE,iBAAiB;IAAEtE;EAAS,CAAC;AACnD;;AAEA;AACA;AACA;AACA,MAAMiF,SAAS,GAAG3G,cAAc,CAAC;EAC7B4G,MAAM,EAAE,GAAG;EACXC,YAAY,EAAE,IAAI;EAClBC,YAAY,EAAE;AAClB,CAAC,CAAC;AAEF,MAAMC,yBAAyB,GAAG;EAC9BC,SAAS,EAAEC,GAAG;EACdC,gBAAgB,EAAE,GAAG;EACrBC,iBAAiB,EAAElG,mBAAmB;EACtCmG,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE;AACjB,CAAC;AACD;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGtH,cAAc,CAACS,EAAE,CAACsG,yBAAyB,CAAC,CAAC;AACvE,SAASQ,uBAAuBA,CAAClD,OAAO,EAAE;EACtC,OAAO;IACH1C,OAAO,EAAE2F,iBAAiB;IAC1BhD,IAAI,EAAE,CAAC,CAAC,IAAI1E,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAEyH,iBAAiB,CAAC,CAAC;IAC3D/C,UAAU,EAAGC,MAAM,IAAK,CAACA,MAAM,IAAI/D,EAAE,CAACsG,yBAAyB,CAAC,EAAEzD,IAAI,CAAC5C,GAAG,CAAE+D,MAAM,KAAM;MAAE,GAAGA,MAAM;MAAE,GAAGJ;IAAQ,CAAC,CAAC,CAAC;EACvH,CAAC;AACL;AAEA,MAAMmD,cAAc,GAAGzH,yBAAyB,CAAC,MAAM,IAAID,UAAU,CAACL,MAAM,CAACU,QAAQ,CAAC,CAACsH,eAAe,CAAC,CAAC;;AAExG;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG3H,yBAAyB,CAAC,MAAM;EACzD,MAAM4H,GAAG,GAAGlI,MAAM,CAACU,QAAQ,CAAC;EAC5B,OAAOQ,KAAK,CAACO,iBAAiB,CAACyG,GAAG,EAAE,iBAAiB,CAAC,EAAEzG,iBAAiB,CAACyG,GAAG,EAAE,SAAS,CAAC,EAAEzG,iBAAiB,CAACyG,GAAG,EAAE,WAAW,CAAC,CAACrE,IAAI,CAAC1C,SAAS,CAAC,MAAMM,iBAAiB,CAACyG,GAAG,EAAE,WAAW,CAAC,CAACrE,IAAI,CAACzC,SAAS,CAACK,iBAAiB,CAACyG,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEzG,iBAAiB,CAACyG,GAAG,EAAE,SAAS,CAAC,EAAEzG,iBAAiB,CAACyG,GAAG,EAAE,OAAO,CAAC,CAAC,CAACrE,IAAI,CAACxC,KAAK,CAAC,CAAC,CAAC;AACnU,CAAC,CAAC;AAEF,MAAM8G,cAAc,GAAG5H,cAAc,CAAC;EAClC6H,SAAS,EAAE,mBAAmB;EAC9BC,SAAS,EAAE;AACf,CAAC,CAAC;AAEF,MAAMC,SAAS,GAAG/H,cAAc,CAAC,UAAU,CAAC;;AAE5C;AACA;AACA;AACA,MAAMgI,YAAY,GAAGjI,yBAAyB,CAAC,MAAM;EACjD,MAAMkI,GAAG,GAAGxI,MAAM,CAACa,SAAS,CAAC;EAC7B,OAAO;IACH4H,IAAI,EAAE,UAAU;IAChBC,aAAaA,CAAA,EAAG;MACZ,MAAM;QAAEC,MAAM,GAAG,CAAC;QAAEC,SAAS,GAAG;MAAE,CAAC,GAAGJ,GAAG,CAACK,cAAc,IAAI,CAAC,CAAC;MAC9D,MAAMC,IAAI,GAAG;QACTC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAET,GAAG,CAACU,UAAU;QACrBC,MAAM,EAAEX,GAAG,CAACY,WAAW;QACvBC,KAAK,EAAEb,GAAG,CAACU,UAAU;QACrBP,MAAM,EAAEA,MAAM,GAAGC,SAAS,IAAIJ,GAAG,CAACY,WAAW;QAC7C/G,CAAC,EAAE,CAAC;QACJiH,CAAC,EAAE;MACP,CAAC;MACD,OAAO;QACH,GAAGR,IAAI;QACPS,MAAM,EAAEA,CAAA,KAAM5F,IAAI,CAAC6F,SAAS,CAACV,IAAI;MACrC,CAAC;IACL;EACJ,CAAC;AACL,CAAC,CAAC;AACF,SAASW,aAAaA,CAACC,QAAQ,EAAE;EAC7B,OAAOlJ,UAAU,CAAC+H,YAAY,EAAEmB,QAAQ,CAAC;AAC7C;;AAEA;AACA;AACA;;AAEA,SAAS5H,oBAAoB,EAAEC,eAAe,EAAEI,aAAa,EAAEqD,cAAc,EAAED,cAAc,EAAE1C,gBAAgB,EAAEK,aAAa,EAAEF,yBAAyB,EAAEC,iBAAiB,EAAEyB,eAAe,EAAEO,oBAAoB,EAAEV,uBAAuB,EAAEmB,yBAAyB,EAAE4B,yBAAyB,EAAElC,qBAAqB,EAAEU,YAAY,EAAEC,iBAAiB,EAAEQ,iBAAiB,EAAEV,cAAc,EAAEG,eAAe,EAAEa,uBAAuB,EAAEK,SAAS,EAAE5B,UAAU,EAAEG,yBAAyB,EAAEoC,iBAAiB,EAAEnG,kBAAkB,EAAEqG,cAAc,EAAEE,oBAAoB,EAAErC,mBAAmB,EAAEuC,cAAc,EAAExC,cAAc,EAAE2C,SAAS,EAAEC,YAAY,EAAEnG,cAAc,EAAEqH,aAAa,EAAEzH,qBAAqB,EAAEc,sBAAsB,EAAE6B,qBAAqB,EAAEsC,uBAAuB,EAAEhB,gBAAgB,EAAEa,qBAAqB,EAAEgB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}