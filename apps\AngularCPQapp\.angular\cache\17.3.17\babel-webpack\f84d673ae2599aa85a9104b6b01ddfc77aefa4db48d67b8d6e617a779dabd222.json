{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, inject, INJECTOR, NgZone } from '@angular/core';\nimport { tuiProvide, tuiCreateTokenFromFactory } from '@taiga-ui/cdk/utils';\nimport { PolymorpheusComponent } from '@taiga-ui/polymorpheus';\nimport { Observable, of, defer, switchMap, map, takeUntil, timer, endWith, tap } from 'rxjs';\nimport { WA_PERFORMANCE, WA_ANIMATION_FRAME } from '@ng-web-apis/common';\nimport { tuiZonefreeScheduler } from '@taiga-ui/cdk/observables';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { tuiEaseInOutQuad } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { DOCUMENT } from '@angular/common';\nimport { Meta } from '@angular/platform-browser';\nclass TuiIdService {\n  static {\n    this.autoId = 0;\n  }\n  generate() {\n    return `tui_${TuiIdService.autoId++}${Date.now()}`;\n  }\n  static {\n    this.ɵfac = function TuiIdService_Factory(t) {\n      return new (t || TuiIdService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiIdService,\n      factory: TuiIdService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiIdService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nfunction tuiInjectId() {\n  return inject(TuiIdService).generate();\n}\nclass TuiPopoverService {\n  constructor(items, component, options = {}) {\n    this.options = options;\n    this.id = inject(TuiIdService);\n    this.component = new PolymorpheusComponent(component, inject(INJECTOR));\n    this.items$ = inject(items);\n  }\n  open(content, options = {}) {\n    return new Observable(observer => {\n      const item = {\n        ...this.options,\n        ...options,\n        content,\n        $implicit: observer,\n        component: this.component,\n        createdAt: Date.now(),\n        id: this.id.generate(),\n        completeWith: result => {\n          observer.next(result);\n          observer.complete();\n        }\n      };\n      this.items$.next([...this.items$.value, item]);\n      return () => {\n        this.items$.next(this.items$.value.filter(value => value !== item));\n      };\n    });\n  }\n  static {\n    this.ɵfac = function TuiPopoverService_Factory(t) {\n      i0.ɵɵinvalidFactory();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiPopoverService,\n      factory: TuiPopoverService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPopoverService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined\n    }, {\n      type: undefined\n    }, {\n      type: undefined\n    }];\n  }, null);\n})();\nfunction tuiAsPopover(popover) {\n  return tuiProvide(TuiPopoverService, popover);\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nconst SCROLL_TIME = 300;\nfunction getX(elementOrWindow) {\n  return 'scrollX' in elementOrWindow ? elementOrWindow.scrollX : elementOrWindow.scrollLeft;\n}\nfunction getY(elementOrWindow) {\n  return 'scrollY' in elementOrWindow ? elementOrWindow.scrollY : elementOrWindow.scrollTop;\n}\nclass TuiScrollService {\n  constructor() {\n    this.performanceRef = inject(WA_PERFORMANCE);\n    this.animationFrame$ = inject(WA_ANIMATION_FRAME);\n    this.zone = inject(NgZone);\n  }\n  scroll$(elementOrWindow, scrollTop, scrollLeft = getX(elementOrWindow), duration = SCROLL_TIME) {\n    ngDevMode && console.assert(duration >= 0, 'duration cannot be negative');\n    ngDevMode && console.assert(scrollTop >= 0, 'scrollTop cannot be negative');\n    ngDevMode && console.assert(scrollLeft >= 0, 'scrollLeft cannot be negative');\n    const initialTop = getY(elementOrWindow);\n    const initialLeft = getX(elementOrWindow);\n    const deltaTop = scrollTop - initialTop;\n    const deltaLeft = scrollLeft - initialLeft;\n    const observable = !duration ? of([scrollTop, scrollLeft]) : defer(() => of(this.performanceRef.now())).pipe(switchMap(start => this.animationFrame$.pipe(map(now => now - start))), map(elapsed => tuiEaseInOutQuad(tuiClamp(elapsed / duration, 0, 1))), map(percent => [initialTop + deltaTop * percent, initialLeft + deltaLeft * percent]), takeUntil(timer(duration, tuiZonefreeScheduler(this.zone))), endWith([scrollTop, scrollLeft]));\n    return observable.pipe(tap(([scrollTop, scrollLeft]) => {\n      elementOrWindow.scrollTo?.(scrollLeft, scrollTop);\n    }));\n  }\n  static {\n    this.ɵfac = function TuiScrollService_Factory(t) {\n      return new (t || TuiScrollService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiScrollService,\n      factory: TuiScrollService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiScrollService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst TUI_THEME_COLOR = tuiCreateTokenFromFactory(() => inject(Meta).getTag('name=\"theme-color\"')?.content ?? '');\nclass TuiThemeColorService {\n  constructor() {\n    this.current = inject(TUI_THEME_COLOR);\n    this.doc = inject(DOCUMENT);\n    this.meta = inject(Meta);\n    this.color = this.current;\n  }\n  get color() {\n    return this.current;\n  }\n  set color(content) {\n    this.current = content;\n    this.meta.updateTag({\n      name: 'theme-color',\n      content\n    });\n    this.doc.documentElement.style.setProperty('--tui-theme-color', content);\n  }\n  static {\n    this.ɵfac = function TuiThemeColorService_Factory(t) {\n      return new (t || TuiThemeColorService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiThemeColorService,\n      factory: TuiThemeColorService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiThemeColorService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_THEME_COLOR, TuiIdService, TuiPopoverService, TuiScrollService, TuiThemeColorService, tuiAsPopover, tuiInjectId };", "map": {"version": 3, "names": ["i0", "Injectable", "inject", "INJECTOR", "NgZone", "tui<PERSON><PERSON><PERSON>", "tuiCreateTokenFromFactory", "PolymorpheusComponent", "Observable", "of", "defer", "switchMap", "map", "takeUntil", "timer", "endWith", "tap", "WA_PERFORMANCE", "WA_ANIMATION_FRAME", "tuiZonefreeScheduler", "tui<PERSON><PERSON>", "tuiEaseInOutQuad", "DOCUMENT", "Meta", "TuiIdService", "autoId", "generate", "Date", "now", "ɵfac", "TuiIdService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "tuiInjectId", "TuiPopoverService", "constructor", "items", "component", "options", "id", "items$", "open", "content", "observer", "item", "$implicit", "createdAt", "completeWith", "result", "next", "complete", "value", "filter", "TuiPopoverService_Factory", "ɵɵinvalidFactory", "undefined", "tuiAsPopover", "popover", "SCROLL_TIME", "getX", "elementOrWindow", "scrollX", "scrollLeft", "getY", "scrollY", "scrollTop", "TuiScrollService", "performanceRef", "animationFrame$", "zone", "scroll$", "duration", "console", "assert", "initialTop", "initialLeft", "deltaTop", "deltaLeft", "observable", "pipe", "start", "elapsed", "percent", "scrollTo", "TuiScrollService_Factory", "TUI_THEME_COLOR", "getTag", "TuiThemeColorService", "current", "doc", "meta", "color", "updateTag", "name", "documentElement", "style", "setProperty", "TuiThemeColorService_Factory"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-services.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, inject, INJECTOR, NgZone } from '@angular/core';\nimport { tuiProvide, tuiCreateTokenFromFactory } from '@taiga-ui/cdk/utils';\nimport { PolymorpheusComponent } from '@taiga-ui/polymorpheus';\nimport { Observable, of, defer, switchMap, map, takeUntil, timer, endWith, tap } from 'rxjs';\nimport { WA_PERFORMANCE, WA_ANIMATION_FRAME } from '@ng-web-apis/common';\nimport { tuiZonefreeScheduler } from '@taiga-ui/cdk/observables';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nimport { tuiEaseInOutQuad } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { DOCUMENT } from '@angular/common';\nimport { Meta } from '@angular/platform-browser';\n\nclass TuiIdService {\n    static { this.autoId = 0; }\n    generate() {\n        return `tui_${TuiIdService.autoId++}${Date.now()}`;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIdService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIdService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIdService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\nfunction tuiInjectId() {\n    return inject(TuiIdService).generate();\n}\n\nclass TuiPopoverService {\n    constructor(items, component, options = {}) {\n        this.options = options;\n        this.id = inject(TuiIdService);\n        this.component = new PolymorpheusComponent(component, inject(INJECTOR));\n        this.items$ = inject(items);\n    }\n    open(content, options = {}) {\n        return new Observable((observer) => {\n            const item = {\n                ...this.options,\n                ...options,\n                content,\n                $implicit: observer,\n                component: this.component,\n                createdAt: Date.now(),\n                id: this.id.generate(),\n                completeWith: (result) => {\n                    observer.next(result);\n                    observer.complete();\n                },\n            };\n            this.items$.next([...this.items$.value, item]);\n            return () => {\n                this.items$.next(this.items$.value.filter((value) => value !== item));\n            };\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPopoverService, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPopoverService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPopoverService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined }, { type: undefined }, { type: undefined }]; } });\nfunction tuiAsPopover(popover) {\n    return tuiProvide(TuiPopoverService, popover);\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nconst SCROLL_TIME = 300;\nfunction getX(elementOrWindow) {\n    return 'scrollX' in elementOrWindow\n        ? elementOrWindow.scrollX\n        : elementOrWindow.scrollLeft;\n}\nfunction getY(elementOrWindow) {\n    return 'scrollY' in elementOrWindow\n        ? elementOrWindow.scrollY\n        : elementOrWindow.scrollTop;\n}\nclass TuiScrollService {\n    constructor() {\n        this.performanceRef = inject(WA_PERFORMANCE);\n        this.animationFrame$ = inject(WA_ANIMATION_FRAME);\n        this.zone = inject(NgZone);\n    }\n    scroll$(elementOrWindow, scrollTop, scrollLeft = getX(elementOrWindow), duration = SCROLL_TIME) {\n        ngDevMode && console.assert(duration >= 0, 'duration cannot be negative');\n        ngDevMode && console.assert(scrollTop >= 0, 'scrollTop cannot be negative');\n        ngDevMode && console.assert(scrollLeft >= 0, 'scrollLeft cannot be negative');\n        const initialTop = getY(elementOrWindow);\n        const initialLeft = getX(elementOrWindow);\n        const deltaTop = scrollTop - initialTop;\n        const deltaLeft = scrollLeft - initialLeft;\n        const observable = !duration\n            ? of([scrollTop, scrollLeft])\n            : defer(() => of(this.performanceRef.now())).pipe(switchMap((start) => this.animationFrame$.pipe(map((now) => now - start))), map((elapsed) => tuiEaseInOutQuad(tuiClamp(elapsed / duration, 0, 1))), map((percent) => [\n                initialTop + deltaTop * percent,\n                initialLeft + deltaLeft * percent,\n            ]), takeUntil(timer(duration, tuiZonefreeScheduler(this.zone))), endWith([scrollTop, scrollLeft]));\n        return observable.pipe(tap(([scrollTop, scrollLeft]) => {\n            elementOrWindow.scrollTo?.(scrollLeft, scrollTop);\n        }));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiScrollService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\nconst TUI_THEME_COLOR = tuiCreateTokenFromFactory(() => inject(Meta).getTag('name=\"theme-color\"')?.content ?? '');\nclass TuiThemeColorService {\n    constructor() {\n        this.current = inject(TUI_THEME_COLOR);\n        this.doc = inject(DOCUMENT);\n        this.meta = inject(Meta);\n        this.color = this.current;\n    }\n    get color() {\n        return this.current;\n    }\n    set color(content) {\n        this.current = content;\n        this.meta.updateTag({ name: 'theme-color', content });\n        this.doc.documentElement.style.setProperty('--tui-theme-color', content);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiThemeColorService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiThemeColorService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiThemeColorService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return []; } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_THEME_COLOR, TuiIdService, TuiPopoverService, TuiScrollService, TuiThemeColorService, tuiAsPopover, tuiInjectId };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,eAAe;AACpE,SAASC,UAAU,EAAEC,yBAAyB,QAAQ,qBAAqB;AAC3E,SAASC,qBAAqB,QAAQ,wBAAwB;AAC9D,SAASC,UAAU,EAAEC,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,GAAG,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,QAAQ,MAAM;AAC5F,SAASC,cAAc,EAAEC,kBAAkB,QAAQ,qBAAqB;AACxE,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,IAAI,QAAQ,2BAA2B;AAEhD,MAAMC,YAAY,CAAC;EACf;IAAS,IAAI,CAACC,MAAM,GAAG,CAAC;EAAE;EAC1BC,QAAQA,CAAA,EAAG;IACP,OAAO,OAAOF,YAAY,CAACC,MAAM,EAAE,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;EACtD;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,qBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFP,YAAY;IAAA,CAAoD;EAAE;EAC7K;IAAS,IAAI,CAACQ,KAAK,kBAD8EhC,EAAE,CAAAiC,kBAAA;MAAAC,KAAA,EACYV,YAAY;MAAAW,OAAA,EAAZX,YAAY,CAAAK,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AACxJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGrC,EAAE,CAAAsC,iBAAA,CAGXd,YAAY,EAAc,CAAC;IAC3Ge,IAAI,EAAEtC,UAAU;IAChBuC,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV,SAASK,WAAWA,CAAA,EAAG;EACnB,OAAOvC,MAAM,CAACsB,YAAY,CAAC,CAACE,QAAQ,CAAC,CAAC;AAC1C;AAEA,MAAMgB,iBAAiB,CAAC;EACpBC,WAAWA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxC,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,EAAE,GAAG7C,MAAM,CAACsB,YAAY,CAAC;IAC9B,IAAI,CAACqB,SAAS,GAAG,IAAItC,qBAAqB,CAACsC,SAAS,EAAE3C,MAAM,CAACC,QAAQ,CAAC,CAAC;IACvE,IAAI,CAAC6C,MAAM,GAAG9C,MAAM,CAAC0C,KAAK,CAAC;EAC/B;EACAK,IAAIA,CAACC,OAAO,EAAEJ,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,OAAO,IAAItC,UAAU,CAAE2C,QAAQ,IAAK;MAChC,MAAMC,IAAI,GAAG;QACT,GAAG,IAAI,CAACN,OAAO;QACf,GAAGA,OAAO;QACVI,OAAO;QACPG,SAAS,EAAEF,QAAQ;QACnBN,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBS,SAAS,EAAE3B,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBmB,EAAE,EAAE,IAAI,CAACA,EAAE,CAACrB,QAAQ,CAAC,CAAC;QACtB6B,YAAY,EAAGC,MAAM,IAAK;UACtBL,QAAQ,CAACM,IAAI,CAACD,MAAM,CAAC;UACrBL,QAAQ,CAACO,QAAQ,CAAC,CAAC;QACvB;MACJ,CAAC;MACD,IAAI,CAACV,MAAM,CAACS,IAAI,CAAC,CAAC,GAAG,IAAI,CAACT,MAAM,CAACW,KAAK,EAAEP,IAAI,CAAC,CAAC;MAC9C,OAAO,MAAM;QACT,IAAI,CAACJ,MAAM,CAACS,IAAI,CAAC,IAAI,CAACT,MAAM,CAACW,KAAK,CAACC,MAAM,CAAED,KAAK,IAAKA,KAAK,KAAKP,IAAI,CAAC,CAAC;MACzE,CAAC;IACL,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACvB,IAAI,YAAAgC,0BAAA9B,CAAA;MAzC+E/B,EAAE,CAAA8D,gBAAA;IAAA,CAyCoF;EAAE;EACzL;IAAS,IAAI,CAAC9B,KAAK,kBA1C8EhC,EAAE,CAAAiC,kBAAA;MAAAC,KAAA,EA0CYQ,iBAAiB;MAAAP,OAAA,EAAjBO,iBAAiB,CAAAb;IAAA,EAAG;EAAE;AACzI;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KA5CqGrC,EAAE,CAAAsC,iBAAA,CA4CXI,iBAAiB,EAAc,CAAC;IAChHH,IAAI,EAAEtC;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEsC,IAAI,EAAEwB;IAAU,CAAC,EAAE;MAAExB,IAAI,EAAEwB;IAAU,CAAC,EAAE;MAAExB,IAAI,EAAEwB;IAAU,CAAC,CAAC;EAAE,CAAC;AAAA;AACnH,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC3B,OAAO5D,UAAU,CAACqC,iBAAiB,EAAEuB,OAAO,CAAC;AACjD;;AAEA;AACA;AACA,MAAMC,WAAW,GAAG,GAAG;AACvB,SAASC,IAAIA,CAACC,eAAe,EAAE;EAC3B,OAAO,SAAS,IAAIA,eAAe,GAC7BA,eAAe,CAACC,OAAO,GACvBD,eAAe,CAACE,UAAU;AACpC;AACA,SAASC,IAAIA,CAACH,eAAe,EAAE;EAC3B,OAAO,SAAS,IAAIA,eAAe,GAC7BA,eAAe,CAACI,OAAO,GACvBJ,eAAe,CAACK,SAAS;AACnC;AACA,MAAMC,gBAAgB,CAAC;EACnB/B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgC,cAAc,GAAGzE,MAAM,CAACe,cAAc,CAAC;IAC5C,IAAI,CAAC2D,eAAe,GAAG1E,MAAM,CAACgB,kBAAkB,CAAC;IACjD,IAAI,CAAC2D,IAAI,GAAG3E,MAAM,CAACE,MAAM,CAAC;EAC9B;EACA0E,OAAOA,CAACV,eAAe,EAAEK,SAAS,EAAEH,UAAU,GAAGH,IAAI,CAACC,eAAe,CAAC,EAAEW,QAAQ,GAAGb,WAAW,EAAE;IAC5F7B,SAAS,IAAI2C,OAAO,CAACC,MAAM,CAACF,QAAQ,IAAI,CAAC,EAAE,6BAA6B,CAAC;IACzE1C,SAAS,IAAI2C,OAAO,CAACC,MAAM,CAACR,SAAS,IAAI,CAAC,EAAE,8BAA8B,CAAC;IAC3EpC,SAAS,IAAI2C,OAAO,CAACC,MAAM,CAACX,UAAU,IAAI,CAAC,EAAE,+BAA+B,CAAC;IAC7E,MAAMY,UAAU,GAAGX,IAAI,CAACH,eAAe,CAAC;IACxC,MAAMe,WAAW,GAAGhB,IAAI,CAACC,eAAe,CAAC;IACzC,MAAMgB,QAAQ,GAAGX,SAAS,GAAGS,UAAU;IACvC,MAAMG,SAAS,GAAGf,UAAU,GAAGa,WAAW;IAC1C,MAAMG,UAAU,GAAG,CAACP,QAAQ,GACtBtE,EAAE,CAAC,CAACgE,SAAS,EAAEH,UAAU,CAAC,CAAC,GAC3B5D,KAAK,CAAC,MAAMD,EAAE,CAAC,IAAI,CAACkE,cAAc,CAAC/C,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC2D,IAAI,CAAC5E,SAAS,CAAE6E,KAAK,IAAK,IAAI,CAACZ,eAAe,CAACW,IAAI,CAAC3E,GAAG,CAAEgB,GAAG,IAAKA,GAAG,GAAG4D,KAAK,CAAC,CAAC,CAAC,EAAE5E,GAAG,CAAE6E,OAAO,IAAKpE,gBAAgB,CAACD,QAAQ,CAACqE,OAAO,GAAGV,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEnE,GAAG,CAAE8E,OAAO,IAAK,CACnNR,UAAU,GAAGE,QAAQ,GAAGM,OAAO,EAC/BP,WAAW,GAAGE,SAAS,GAAGK,OAAO,CACpC,CAAC,EAAE7E,SAAS,CAACC,KAAK,CAACiE,QAAQ,EAAE5D,oBAAoB,CAAC,IAAI,CAAC0D,IAAI,CAAC,CAAC,CAAC,EAAE9D,OAAO,CAAC,CAAC0D,SAAS,EAAEH,UAAU,CAAC,CAAC,CAAC;IACtG,OAAOgB,UAAU,CAACC,IAAI,CAACvE,GAAG,CAAC,CAAC,CAACyD,SAAS,EAAEH,UAAU,CAAC,KAAK;MACpDF,eAAe,CAACuB,QAAQ,GAAGrB,UAAU,EAAEG,SAAS,CAAC;IACrD,CAAC,CAAC,CAAC;EACP;EACA;IAAS,IAAI,CAAC5C,IAAI,YAAA+D,yBAAA7D,CAAA;MAAA,YAAAA,CAAA,IAAyF2C,gBAAgB;IAAA,CAAoD;EAAE;EACjL;IAAS,IAAI,CAAC1C,KAAK,kBAzF8EhC,EAAE,CAAAiC,kBAAA;MAAAC,KAAA,EAyFYwC,gBAAgB;MAAAvC,OAAA,EAAhBuC,gBAAgB,CAAA7C,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAC5J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3FqGrC,EAAE,CAAAsC,iBAAA,CA2FXoC,gBAAgB,EAAc,CAAC;IAC/GnC,IAAI,EAAEtC,UAAU;IAChBuC,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMyD,eAAe,GAAGvF,yBAAyB,CAAC,MAAMJ,MAAM,CAACqB,IAAI,CAAC,CAACuE,MAAM,CAAC,oBAAoB,CAAC,EAAE5C,OAAO,IAAI,EAAE,CAAC;AACjH,MAAM6C,oBAAoB,CAAC;EACvBpD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqD,OAAO,GAAG9F,MAAM,CAAC2F,eAAe,CAAC;IACtC,IAAI,CAACI,GAAG,GAAG/F,MAAM,CAACoB,QAAQ,CAAC;IAC3B,IAAI,CAAC4E,IAAI,GAAGhG,MAAM,CAACqB,IAAI,CAAC;IACxB,IAAI,CAAC4E,KAAK,GAAG,IAAI,CAACH,OAAO;EAC7B;EACA,IAAIG,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACH,OAAO;EACvB;EACA,IAAIG,KAAKA,CAACjD,OAAO,EAAE;IACf,IAAI,CAAC8C,OAAO,GAAG9C,OAAO;IACtB,IAAI,CAACgD,IAAI,CAACE,SAAS,CAAC;MAAEC,IAAI,EAAE,aAAa;MAAEnD;IAAQ,CAAC,CAAC;IACrD,IAAI,CAAC+C,GAAG,CAACK,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAEtD,OAAO,CAAC;EAC5E;EACA;IAAS,IAAI,CAACrB,IAAI,YAAA4E,6BAAA1E,CAAA;MAAA,YAAAA,CAAA,IAAyFgE,oBAAoB;IAAA,CAAoD;EAAE;EACrL;IAAS,IAAI,CAAC/D,KAAK,kBAnH8EhC,EAAE,CAAAiC,kBAAA;MAAAC,KAAA,EAmHY6D,oBAAoB;MAAA5D,OAAA,EAApB4D,oBAAoB,CAAAlE,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAChK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArHqGrC,EAAE,CAAAsC,iBAAA,CAqHXyD,oBAAoB,EAAc,CAAC;IACnHxD,IAAI,EAAEtC,UAAU;IAChBuC,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;;AAEtD;AACA;AACA;;AAEA,SAASyD,eAAe,EAAErE,YAAY,EAAEkB,iBAAiB,EAAEgC,gBAAgB,EAAEqB,oBAAoB,EAAE/B,YAAY,EAAEvB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}