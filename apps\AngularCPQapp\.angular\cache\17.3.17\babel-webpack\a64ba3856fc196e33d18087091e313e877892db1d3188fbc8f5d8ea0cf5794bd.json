{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { <PERSON><PERSON> } from '@angular/core';\nclass TuiMapperPipe {\n  /**\n   * Maps object to an arbitrary result through a mapper function\n   *\n   * @param value an item to transform\n   * @param mapper a mapping function\n   * @param args arbitrary number of additional arguments\n   */\n  transform(value, mapper, ...args) {\n    return mapper(value, ...args);\n  }\n  static {\n    this.ɵfac = function TuiMapperPipe_Factory(t) {\n      return new (t || TuiMapperPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiMapper\",\n      type: TuiMapperPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiMapperPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiMapper'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiMapperPipe };", "map": {"version": 3, "names": ["i0", "<PERSON><PERSON>", "TuiMapperPipe", "transform", "value", "mapper", "args", "ɵfac", "TuiMapperPipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-pipes-mapper.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { <PERSON><PERSON> } from '@angular/core';\n\nclass TuiMapperPipe {\n    /**\n     * Maps object to an arbitrary result through a mapper function\n     *\n     * @param value an item to transform\n     * @param mapper a mapping function\n     * @param args arbitrary number of additional arguments\n     */\n    transform(value, mapper, ...args) {\n        return mapper(value, ...args);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMapperPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMapperPipe, isStandalone: true, name: \"tui<PERSON><PERSON>per\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMapperPipe, decorators: [{\n            type: <PERSON><PERSON>,\n            args: [{\n                    standalone: true,\n                    name: 'tuiM<PERSON><PERSON>',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiMapperPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,IAAI,QAAQ,eAAe;AAEpC,MAAMC,aAAa,CAAC;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACC,KAAK,EAAEC,MAAM,EAAE,GAAGC,IAAI,EAAE;IAC9B,OAAOD,MAAM,CAACD,KAAK,EAAE,GAAGE,IAAI,CAAC;EACjC;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFP,aAAa;IAAA,CAA8C;EAAE;EACxK;IAAS,IAAI,CAACQ,KAAK,kBAD8EV,EAAE,CAAAW,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMX,aAAa;MAAAY,IAAA;MAAAC,UAAA;IAAA,EAA0C;EAAE;AACtK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGhB,EAAE,CAAAiB,iBAAA,CAGXf,aAAa,EAAc,CAAC;IAC5GW,IAAI,EAAEZ,IAAI;IACVK,IAAI,EAAE,CAAC;MACCS,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASV,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}