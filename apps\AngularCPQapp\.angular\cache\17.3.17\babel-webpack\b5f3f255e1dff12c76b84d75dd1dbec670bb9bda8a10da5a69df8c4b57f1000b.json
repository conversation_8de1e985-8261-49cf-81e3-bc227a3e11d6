{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, Input } from '@angular/core';\nimport { tuiCreateToken, tuiProvideOptions, tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiButtonOptionsProvider } from '@taiga-ui/core/components/button';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearanceOptionsProvider, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport * as i2 from '@taiga-ui/core/directives/icons';\nimport { TuiWithIcons } from '@taiga-ui/core/directives/icons';\nimport { tuiAvatarOptionsProvider } from '@taiga-ui/kit/components/avatar';\nimport { tuiCheckboxOptionsProvider } from '@taiga-ui/kit/components/checkbox';\nimport { tuiSwitchOptionsProvider } from '@taiga-ui/kit/components/switch';\nconst TUI_CHIP_DEFAULT_OPTIONS = {\n  appearance: 'neutral',\n  size: 's'\n};\nconst TUI_CHIP_OPTIONS = tuiCreateToken(TUI_CHIP_DEFAULT_OPTIONS);\nfunction tuiChipOptionsProvider(options) {\n  return tuiProvideOptions(TUI_CHIP_OPTIONS, options, TUI_CHIP_DEFAULT_OPTIONS);\n}\nclass TuiChipStyles {\n  static {\n    this.ɵfac = function TuiChipStyles_Factory(t) {\n      return new (t || TuiChipStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiChipStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-chip\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiChipStyles_Template(rf, ctx) {},\n      styles: [\"tui-chip,[tuiChip]{--t-gap: .125rem;--t-margin: -.125rem;--t-icon-size: 1rem;--t-padding: 0 .625rem;--t-size: var(--tui-height-s);-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;position:relative;display:inline-flex;align-items:center;flex-shrink:0;box-sizing:border-box;white-space:nowrap;overflow:hidden;vertical-align:middle;max-inline-size:100%;gap:calc(var(--t-gap, 0rem) - 2 * var(--t-margin, 0rem));font:var(--tui-font-text-s);border-radius:var(--tui-radius-m);padding:var(--t-padding);block-size:var(--t-size);inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;isolation:isolate}tui-chip>img,[tuiChip]>img,tui-chip>tui-svg,[tuiChip]>tui-svg,tui-chip>tui-icon,[tuiChip]>tui-icon,tui-chip>tui-avatar,[tuiChip]>tui-avatar,tui-chip>tui-badge,[tuiChip]>tui-badge,tui-chip>[tuiBadge],[tuiChip]>[tuiBadge],tui-chip>[tuiRadio],[tuiChip]>[tuiRadio],tui-chip>[tuiSwitch],[tuiChip]>[tuiSwitch],tui-chip>[tuiCheckbox],[tuiChip]>[tuiCheckbox],tui-chip[tuiIcons]:before,[tuiChip][tuiIcons]:before,tui-chip[tuiIcons]:after,[tuiChip][tuiIcons]:after{margin:var(--t-margin)}tui-chip:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled),[tuiChip]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled){cursor:pointer}tui-chip:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled),[tuiChip]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled){cursor:pointer}tui-chip>tui-icon,[tuiChip]>tui-icon,tui-chip[tuiIcons]:before,[tuiChip][tuiIcons]:before,tui-chip[tuiIcons]:after,[tuiChip][tuiIcons]:after{font-size:var(--t-icon-size)!important}tui-chip>[tuiIconButton],[tuiChip]>[tuiIconButton]{margin:-.375rem}tui-chip[data-size=xxs],[tuiChip][data-size=xxs]{--t-gap: 0rem;--t-padding: 0 .25rem;--t-size: 1rem;--t-icon-size: .75rem;font:var(--tui-font-text-xs);border-radius:var(--tui-radius-xs)}tui-chip[data-size=xxs]>[tuiIconButton],[tuiChip][data-size=xxs]>[tuiIconButton]{margin:-.5rem;transform:scale(.75)}tui-chip[data-size=xs],[tuiChip][data-size=xs]{--t-padding: 0 .375rem;--t-size: var(--tui-height-xs);border-radius:var(--tui-radius-xs)}tui-chip[data-size=xs]>[tuiIconButton],[tuiChip][data-size=xs]>[tuiIconButton]{margin:-.375rem}tui-chip[data-size=m],[tuiChip][data-size=m]{--t-margin: -.375rem;--t-icon-size: 1.5rem;--t-padding: 0 1rem;--t-size: var(--tui-height-m);font:var(--tui-font-text-m)}tui-chip[data-size=m]>[tuiIconButton],[tuiChip][data-size=m]>[tuiIconButton]{margin:-.75rem}tui-chip>img,[tuiChip]>img,tui-chip tui-avatar,[tuiChip] tui-avatar{inline-size:1.5rem;margin-inline-start:-.375rem}tui-chip>[tuiFade]:first-of-type,[tuiChip]>[tuiFade]:first-of-type{flex:1 0 30%;max-inline-size:-webkit-fit-content;max-inline-size:-moz-fit-content;max-inline-size:fit-content}tui-chip>[tuiFade]:last-of-type,[tuiChip]>[tuiFade]:last-of-type{flex:0 1 auto}tui-chip>input[tuiChip],[tuiChip]>input[tuiChip]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;margin:0}tui-chip>input[tuiChip][type=checkbox],[tuiChip]>input[tuiChip][type=checkbox],tui-chip>input[tuiChip][type=radio],[tuiChip]>input[tuiChip][type=radio]{z-index:-1}tui-chip[tuiAppearance][data-appearance=error],[tuiChip][tuiAppearance][data-appearance=error],tui-chip[tuiAppearance][data-appearance=success],[tuiChip][tuiAppearance][data-appearance=success],tui-chip[tuiAppearance][data-appearance=negative],[tuiChip][tuiAppearance][data-appearance=negative],tui-chip[tuiAppearance][data-appearance=positive],[tuiChip][tuiAppearance][data-appearance=positive],tui-chip[tuiAppearance][data-appearance=warning],[tuiChip][tuiAppearance][data-appearance=warning],tui-chip[tuiAppearance][data-appearance=info],[tuiChip][tuiAppearance][data-appearance=info],tui-chip[tuiAppearance][data-appearance=neutral],[tuiChip][tuiAppearance][data-appearance=neutral]{color:var(--tui-text-primary)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiChipStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-chip'\n      },\n      styles: [\"tui-chip,[tuiChip]{--t-gap: .125rem;--t-margin: -.125rem;--t-icon-size: 1rem;--t-padding: 0 .625rem;--t-size: var(--tui-height-s);-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;position:relative;display:inline-flex;align-items:center;flex-shrink:0;box-sizing:border-box;white-space:nowrap;overflow:hidden;vertical-align:middle;max-inline-size:100%;gap:calc(var(--t-gap, 0rem) - 2 * var(--t-margin, 0rem));font:var(--tui-font-text-s);border-radius:var(--tui-radius-m);padding:var(--t-padding);block-size:var(--t-size);inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;isolation:isolate}tui-chip>img,[tuiChip]>img,tui-chip>tui-svg,[tuiChip]>tui-svg,tui-chip>tui-icon,[tuiChip]>tui-icon,tui-chip>tui-avatar,[tuiChip]>tui-avatar,tui-chip>tui-badge,[tuiChip]>tui-badge,tui-chip>[tuiBadge],[tuiChip]>[tuiBadge],tui-chip>[tuiRadio],[tuiChip]>[tuiRadio],tui-chip>[tuiSwitch],[tuiChip]>[tuiSwitch],tui-chip>[tuiCheckbox],[tuiChip]>[tuiCheckbox],tui-chip[tuiIcons]:before,[tuiChip][tuiIcons]:before,tui-chip[tuiIcons]:after,[tuiChip][tuiIcons]:after{margin:var(--t-margin)}tui-chip:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled),[tuiChip]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled){cursor:pointer}tui-chip:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled),[tuiChip]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled){cursor:pointer}tui-chip>tui-icon,[tuiChip]>tui-icon,tui-chip[tuiIcons]:before,[tuiChip][tuiIcons]:before,tui-chip[tuiIcons]:after,[tuiChip][tuiIcons]:after{font-size:var(--t-icon-size)!important}tui-chip>[tuiIconButton],[tuiChip]>[tuiIconButton]{margin:-.375rem}tui-chip[data-size=xxs],[tuiChip][data-size=xxs]{--t-gap: 0rem;--t-padding: 0 .25rem;--t-size: 1rem;--t-icon-size: .75rem;font:var(--tui-font-text-xs);border-radius:var(--tui-radius-xs)}tui-chip[data-size=xxs]>[tuiIconButton],[tuiChip][data-size=xxs]>[tuiIconButton]{margin:-.5rem;transform:scale(.75)}tui-chip[data-size=xs],[tuiChip][data-size=xs]{--t-padding: 0 .375rem;--t-size: var(--tui-height-xs);border-radius:var(--tui-radius-xs)}tui-chip[data-size=xs]>[tuiIconButton],[tuiChip][data-size=xs]>[tuiIconButton]{margin:-.375rem}tui-chip[data-size=m],[tuiChip][data-size=m]{--t-margin: -.375rem;--t-icon-size: 1.5rem;--t-padding: 0 1rem;--t-size: var(--tui-height-m);font:var(--tui-font-text-m)}tui-chip[data-size=m]>[tuiIconButton],[tuiChip][data-size=m]>[tuiIconButton]{margin:-.75rem}tui-chip>img,[tuiChip]>img,tui-chip tui-avatar,[tuiChip] tui-avatar{inline-size:1.5rem;margin-inline-start:-.375rem}tui-chip>[tuiFade]:first-of-type,[tuiChip]>[tuiFade]:first-of-type{flex:1 0 30%;max-inline-size:-webkit-fit-content;max-inline-size:-moz-fit-content;max-inline-size:fit-content}tui-chip>[tuiFade]:last-of-type,[tuiChip]>[tuiFade]:last-of-type{flex:0 1 auto}tui-chip>input[tuiChip],[tuiChip]>input[tuiChip]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;margin:0}tui-chip>input[tuiChip][type=checkbox],[tuiChip]>input[tuiChip][type=checkbox],tui-chip>input[tuiChip][type=radio],[tuiChip]>input[tuiChip][type=radio]{z-index:-1}tui-chip[tuiAppearance][data-appearance=error],[tuiChip][tuiAppearance][data-appearance=error],tui-chip[tuiAppearance][data-appearance=success],[tuiChip][tuiAppearance][data-appearance=success],tui-chip[tuiAppearance][data-appearance=negative],[tuiChip][tuiAppearance][data-appearance=negative],tui-chip[tuiAppearance][data-appearance=positive],[tuiChip][tuiAppearance][data-appearance=positive],tui-chip[tuiAppearance][data-appearance=warning],[tuiChip][tuiAppearance][data-appearance=warning],tui-chip[tuiAppearance][data-appearance=info],[tuiChip][tuiAppearance][data-appearance=info],tui-chip[tuiAppearance][data-appearance=neutral],[tuiChip][tuiAppearance][data-appearance=neutral]{color:var(--tui-text-primary)}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiChip {\n  constructor() {\n    this.options = inject(TUI_CHIP_OPTIONS);\n    this.nothing = tuiWithStyles(TuiChipStyles);\n    this.size = this.options.size;\n  }\n  static {\n    this.ɵfac = function TuiChip_Factory(t) {\n      return new (t || TuiChip)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiChip,\n      selectors: [[\"tui-chip\"], [\"\", \"tuiChip\", \"\"]],\n      hostVars: 1,\n      hostBindings: function TuiChip_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-size\", ctx.size);\n        }\n      },\n      inputs: {\n        size: \"size\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAppearanceOptionsProvider(TUI_CHIP_OPTIONS), tuiSwitchOptionsProvider({\n        size: 's'\n      }), tuiCheckboxOptionsProvider({\n        size: 's'\n      }), tuiAvatarOptionsProvider({\n        size: 'xs'\n      }), tuiButtonOptionsProvider({\n        size: 'xs',\n        appearance: 'icon'\n      })]), i0.ɵɵHostDirectivesFeature([i1.TuiWithAppearance, i2.TuiWithIcons])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiChip, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'tui-chip,[tuiChip]',\n      providers: [tuiAppearanceOptionsProvider(TUI_CHIP_OPTIONS), tuiSwitchOptionsProvider({\n        size: 's'\n      }), tuiCheckboxOptionsProvider({\n        size: 's'\n      }), tuiAvatarOptionsProvider({\n        size: 'xs'\n      }), tuiButtonOptionsProvider({\n        size: 'xs',\n        appearance: 'icon'\n      })],\n      hostDirectives: [TuiWithAppearance, TuiWithIcons],\n      host: {\n        '[attr.data-size]': 'size'\n      }\n    }]\n  }], null, {\n    size: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_CHIP_DEFAULT_OPTIONS, TUI_CHIP_OPTIONS, TuiChip, tuiChipOptionsProvider };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "Directive", "Input", "tuiCreateToken", "tuiProvideOptions", "tuiWithStyles", "tuiButtonOptionsProvider", "i1", "tuiAppearanceOptionsProvider", "TuiWithAppearance", "i2", "TuiWithIcons", "tuiAvatarOptionsProvider", "tuiCheckboxOptionsProvider", "tuiSwitchOptionsProvider", "TUI_CHIP_DEFAULT_OPTIONS", "appearance", "size", "TUI_CHIP_OPTIONS", "tuiChipOptionsProvider", "options", "TuiChipStyles", "ɵfac", "TuiChipStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiChipStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiChip", "constructor", "nothing", "TuiChip_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiChip_HostBindings", "ɵɵattribute", "inputs", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "selector", "providers", "hostDirectives"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-chip.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, Directive, Input } from '@angular/core';\nimport { tuiCreateToken, tuiProvideOptions, tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiButtonOptionsProvider } from '@taiga-ui/core/components/button';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearanceOptionsProvider, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport * as i2 from '@taiga-ui/core/directives/icons';\nimport { TuiWithIcons } from '@taiga-ui/core/directives/icons';\nimport { tuiAvatarOptionsProvider } from '@taiga-ui/kit/components/avatar';\nimport { tuiCheckboxOptionsProvider } from '@taiga-ui/kit/components/checkbox';\nimport { tuiSwitchOptionsProvider } from '@taiga-ui/kit/components/switch';\n\nconst TUI_CHIP_DEFAULT_OPTIONS = {\n    appearance: 'neutral',\n    size: 's',\n};\nconst TUI_CHIP_OPTIONS = tuiCreateToken(TUI_CHIP_DEFAULT_OPTIONS);\nfunction tuiChipOptionsProvider(options) {\n    return tuiProvideOptions(TUI_CHIP_OPTIONS, options, TUI_CHIP_DEFAULT_OPTIONS);\n}\n\nclass TuiChipStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiChipStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiChipStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-chip\" }, ngImport: i0, template: '', isInline: true, styles: [\"tui-chip,[tuiChip]{--t-gap: .125rem;--t-margin: -.125rem;--t-icon-size: 1rem;--t-padding: 0 .625rem;--t-size: var(--tui-height-s);-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;position:relative;display:inline-flex;align-items:center;flex-shrink:0;box-sizing:border-box;white-space:nowrap;overflow:hidden;vertical-align:middle;max-inline-size:100%;gap:calc(var(--t-gap, 0rem) - 2 * var(--t-margin, 0rem));font:var(--tui-font-text-s);border-radius:var(--tui-radius-m);padding:var(--t-padding);block-size:var(--t-size);inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;isolation:isolate}tui-chip>img,[tuiChip]>img,tui-chip>tui-svg,[tuiChip]>tui-svg,tui-chip>tui-icon,[tuiChip]>tui-icon,tui-chip>tui-avatar,[tuiChip]>tui-avatar,tui-chip>tui-badge,[tuiChip]>tui-badge,tui-chip>[tuiBadge],[tuiChip]>[tuiBadge],tui-chip>[tuiRadio],[tuiChip]>[tuiRadio],tui-chip>[tuiSwitch],[tuiChip]>[tuiSwitch],tui-chip>[tuiCheckbox],[tuiChip]>[tuiCheckbox],tui-chip[tuiIcons]:before,[tuiChip][tuiIcons]:before,tui-chip[tuiIcons]:after,[tuiChip][tuiIcons]:after{margin:var(--t-margin)}tui-chip:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled),[tuiChip]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled){cursor:pointer}tui-chip:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled),[tuiChip]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled){cursor:pointer}tui-chip>tui-icon,[tuiChip]>tui-icon,tui-chip[tuiIcons]:before,[tuiChip][tuiIcons]:before,tui-chip[tuiIcons]:after,[tuiChip][tuiIcons]:after{font-size:var(--t-icon-size)!important}tui-chip>[tuiIconButton],[tuiChip]>[tuiIconButton]{margin:-.375rem}tui-chip[data-size=xxs],[tuiChip][data-size=xxs]{--t-gap: 0rem;--t-padding: 0 .25rem;--t-size: 1rem;--t-icon-size: .75rem;font:var(--tui-font-text-xs);border-radius:var(--tui-radius-xs)}tui-chip[data-size=xxs]>[tuiIconButton],[tuiChip][data-size=xxs]>[tuiIconButton]{margin:-.5rem;transform:scale(.75)}tui-chip[data-size=xs],[tuiChip][data-size=xs]{--t-padding: 0 .375rem;--t-size: var(--tui-height-xs);border-radius:var(--tui-radius-xs)}tui-chip[data-size=xs]>[tuiIconButton],[tuiChip][data-size=xs]>[tuiIconButton]{margin:-.375rem}tui-chip[data-size=m],[tuiChip][data-size=m]{--t-margin: -.375rem;--t-icon-size: 1.5rem;--t-padding: 0 1rem;--t-size: var(--tui-height-m);font:var(--tui-font-text-m)}tui-chip[data-size=m]>[tuiIconButton],[tuiChip][data-size=m]>[tuiIconButton]{margin:-.75rem}tui-chip>img,[tuiChip]>img,tui-chip tui-avatar,[tuiChip] tui-avatar{inline-size:1.5rem;margin-inline-start:-.375rem}tui-chip>[tuiFade]:first-of-type,[tuiChip]>[tuiFade]:first-of-type{flex:1 0 30%;max-inline-size:-webkit-fit-content;max-inline-size:-moz-fit-content;max-inline-size:fit-content}tui-chip>[tuiFade]:last-of-type,[tuiChip]>[tuiFade]:last-of-type{flex:0 1 auto}tui-chip>input[tuiChip],[tuiChip]>input[tuiChip]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;margin:0}tui-chip>input[tuiChip][type=checkbox],[tuiChip]>input[tuiChip][type=checkbox],tui-chip>input[tuiChip][type=radio],[tuiChip]>input[tuiChip][type=radio]{z-index:-1}tui-chip[tuiAppearance][data-appearance=error],[tuiChip][tuiAppearance][data-appearance=error],tui-chip[tuiAppearance][data-appearance=success],[tuiChip][tuiAppearance][data-appearance=success],tui-chip[tuiAppearance][data-appearance=negative],[tuiChip][tuiAppearance][data-appearance=negative],tui-chip[tuiAppearance][data-appearance=positive],[tuiChip][tuiAppearance][data-appearance=positive],tui-chip[tuiAppearance][data-appearance=warning],[tuiChip][tuiAppearance][data-appearance=warning],tui-chip[tuiAppearance][data-appearance=info],[tuiChip][tuiAppearance][data-appearance=info],tui-chip[tuiAppearance][data-appearance=neutral],[tuiChip][tuiAppearance][data-appearance=neutral]{color:var(--tui-text-primary)}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiChipStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-chip',\n                    }, styles: [\"tui-chip,[tuiChip]{--t-gap: .125rem;--t-margin: -.125rem;--t-icon-size: 1rem;--t-padding: 0 .625rem;--t-size: var(--tui-height-s);-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;position:relative;display:inline-flex;align-items:center;flex-shrink:0;box-sizing:border-box;white-space:nowrap;overflow:hidden;vertical-align:middle;max-inline-size:100%;gap:calc(var(--t-gap, 0rem) - 2 * var(--t-margin, 0rem));font:var(--tui-font-text-s);border-radius:var(--tui-radius-m);padding:var(--t-padding);block-size:var(--t-size);inline-size:-webkit-fit-content;inline-size:-moz-fit-content;inline-size:fit-content;isolation:isolate}tui-chip>img,[tuiChip]>img,tui-chip>tui-svg,[tuiChip]>tui-svg,tui-chip>tui-icon,[tuiChip]>tui-icon,tui-chip>tui-avatar,[tuiChip]>tui-avatar,tui-chip>tui-badge,[tuiChip]>tui-badge,tui-chip>[tuiBadge],[tuiChip]>[tuiBadge],tui-chip>[tuiRadio],[tuiChip]>[tuiRadio],tui-chip>[tuiSwitch],[tuiChip]>[tuiSwitch],tui-chip>[tuiCheckbox],[tuiChip]>[tuiCheckbox],tui-chip[tuiIcons]:before,[tuiChip][tuiIcons]:before,tui-chip[tuiIcons]:after,[tuiChip][tuiIcons]:after{margin:var(--t-margin)}tui-chip:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled),[tuiChip]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled){cursor:pointer}tui-chip:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled),[tuiChip]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled){cursor:pointer}tui-chip>tui-icon,[tuiChip]>tui-icon,tui-chip[tuiIcons]:before,[tuiChip][tuiIcons]:before,tui-chip[tuiIcons]:after,[tuiChip][tuiIcons]:after{font-size:var(--t-icon-size)!important}tui-chip>[tuiIconButton],[tuiChip]>[tuiIconButton]{margin:-.375rem}tui-chip[data-size=xxs],[tuiChip][data-size=xxs]{--t-gap: 0rem;--t-padding: 0 .25rem;--t-size: 1rem;--t-icon-size: .75rem;font:var(--tui-font-text-xs);border-radius:var(--tui-radius-xs)}tui-chip[data-size=xxs]>[tuiIconButton],[tuiChip][data-size=xxs]>[tuiIconButton]{margin:-.5rem;transform:scale(.75)}tui-chip[data-size=xs],[tuiChip][data-size=xs]{--t-padding: 0 .375rem;--t-size: var(--tui-height-xs);border-radius:var(--tui-radius-xs)}tui-chip[data-size=xs]>[tuiIconButton],[tuiChip][data-size=xs]>[tuiIconButton]{margin:-.375rem}tui-chip[data-size=m],[tuiChip][data-size=m]{--t-margin: -.375rem;--t-icon-size: 1.5rem;--t-padding: 0 1rem;--t-size: var(--tui-height-m);font:var(--tui-font-text-m)}tui-chip[data-size=m]>[tuiIconButton],[tuiChip][data-size=m]>[tuiIconButton]{margin:-.75rem}tui-chip>img,[tuiChip]>img,tui-chip tui-avatar,[tuiChip] tui-avatar{inline-size:1.5rem;margin-inline-start:-.375rem}tui-chip>[tuiFade]:first-of-type,[tuiChip]>[tuiFade]:first-of-type{flex:1 0 30%;max-inline-size:-webkit-fit-content;max-inline-size:-moz-fit-content;max-inline-size:fit-content}tui-chip>[tuiFade]:last-of-type,[tuiChip]>[tuiFade]:last-of-type{flex:0 1 auto}tui-chip>input[tuiChip],[tuiChip]>input[tuiChip]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;margin:0}tui-chip>input[tuiChip][type=checkbox],[tuiChip]>input[tuiChip][type=checkbox],tui-chip>input[tuiChip][type=radio],[tuiChip]>input[tuiChip][type=radio]{z-index:-1}tui-chip[tuiAppearance][data-appearance=error],[tuiChip][tuiAppearance][data-appearance=error],tui-chip[tuiAppearance][data-appearance=success],[tuiChip][tuiAppearance][data-appearance=success],tui-chip[tuiAppearance][data-appearance=negative],[tuiChip][tuiAppearance][data-appearance=negative],tui-chip[tuiAppearance][data-appearance=positive],[tuiChip][tuiAppearance][data-appearance=positive],tui-chip[tuiAppearance][data-appearance=warning],[tuiChip][tuiAppearance][data-appearance=warning],tui-chip[tuiAppearance][data-appearance=info],[tuiChip][tuiAppearance][data-appearance=info],tui-chip[tuiAppearance][data-appearance=neutral],[tuiChip][tuiAppearance][data-appearance=neutral]{color:var(--tui-text-primary)}\\n\"] }]\n        }] });\nclass TuiChip {\n    constructor() {\n        this.options = inject(TUI_CHIP_OPTIONS);\n        this.nothing = tuiWithStyles(TuiChipStyles);\n        this.size = this.options.size;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiChip, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiChip, isStandalone: true, selector: \"tui-chip,[tuiChip]\", inputs: { size: \"size\" }, host: { properties: { \"attr.data-size\": \"size\" } }, providers: [\n            tuiAppearanceOptionsProvider(TUI_CHIP_OPTIONS),\n            tuiSwitchOptionsProvider({ size: 's' }),\n            tuiCheckboxOptionsProvider({ size: 's' }),\n            tuiAvatarOptionsProvider({ size: 'xs' }),\n            tuiButtonOptionsProvider({\n                size: 'xs',\n                appearance: 'icon',\n            }),\n        ], hostDirectives: [{ directive: i1.TuiWithAppearance }, { directive: i2.TuiWithIcons }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiChip, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-chip,[tuiChip]',\n                    providers: [\n                        tuiAppearanceOptionsProvider(TUI_CHIP_OPTIONS),\n                        tuiSwitchOptionsProvider({ size: 's' }),\n                        tuiCheckboxOptionsProvider({ size: 's' }),\n                        tuiAvatarOptionsProvider({ size: 'xs' }),\n                        tuiButtonOptionsProvider({\n                            size: 'xs',\n                            appearance: 'icon',\n                        }),\n                    ],\n                    hostDirectives: [TuiWithAppearance, TuiWithIcons],\n                    host: { '[attr.data-size]': 'size' },\n                }]\n        }], propDecorators: { size: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_CHIP_DEFAULT_OPTIONS, TUI_CHIP_OPTIONS, TuiChip, tuiChipOptionsProvider };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAC/G,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,aAAa,QAAQ,mCAAmC;AACpG,SAASC,wBAAwB,QAAQ,kCAAkC;AAC3E,OAAO,KAAKC,EAAE,MAAM,sCAAsC;AAC1D,SAASC,4BAA4B,EAAEC,iBAAiB,QAAQ,sCAAsC;AACtG,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,SAASC,0BAA0B,QAAQ,mCAAmC;AAC9E,SAASC,wBAAwB,QAAQ,iCAAiC;AAE1E,MAAMC,wBAAwB,GAAG;EAC7BC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE;AACV,CAAC;AACD,MAAMC,gBAAgB,GAAGf,cAAc,CAACY,wBAAwB,CAAC;AACjE,SAASI,sBAAsBA,CAACC,OAAO,EAAE;EACrC,OAAOhB,iBAAiB,CAACc,gBAAgB,EAAEE,OAAO,EAAEL,wBAAwB,CAAC;AACjF;AAEA,MAAMM,aAAa,CAAC;EAChB;IAAS,IAAI,CAACC,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,aAAa;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACI,IAAI,kBAD+E7B,EAAE,CAAA8B,iBAAA;MAAAC,IAAA,EACJN,aAAa;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADXnC,EAAE,CAAAoC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAConI;EAAE;AAC7tI;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG9C,EAAE,CAAA+C,iBAAA,CAGXtB,aAAa,EAAc,CAAC;IAC5GM,IAAI,EAAE9B,SAAS;IACf+C,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAE1C,iBAAiB,CAAC+C,IAAI;MAAEJ,eAAe,EAAE1C,uBAAuB,CAAC+C,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,83HAA83H;IAAE,CAAC;EACz5H,CAAC,CAAC;AAAA;AACV,MAAMU,OAAO,CAAC;EACVC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9B,OAAO,GAAGpB,MAAM,CAACkB,gBAAgB,CAAC;IACvC,IAAI,CAACiC,OAAO,GAAG9C,aAAa,CAACgB,aAAa,CAAC;IAC3C,IAAI,CAACJ,IAAI,GAAG,IAAI,CAACG,OAAO,CAACH,IAAI;EACjC;EACA;IAAS,IAAI,CAACK,IAAI,YAAA8B,gBAAA5B,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,OAAO;IAAA,CAAmD;EAAE;EACvK;IAAS,IAAI,CAACI,IAAI,kBAhB+EzD,EAAE,CAAA0D,iBAAA;MAAA3B,IAAA,EAgBJsB,OAAO;MAAArB,SAAA;MAAA2B,QAAA;MAAAC,YAAA,WAAAC,qBAAApB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhBLzC,EAAE,CAAA8D,WAAA,cAAApB,GAAA,CAAArB,IAAA;QAAA;MAAA;MAAA0C,MAAA;QAAA1C,IAAA;MAAA;MAAAa,UAAA;MAAAC,QAAA,GAAFnC,EAAE,CAAAgE,kBAAA,CAgBkJ,CAC7OpD,4BAA4B,CAACU,gBAAgB,CAAC,EAC9CJ,wBAAwB,CAAC;QAAEG,IAAI,EAAE;MAAI,CAAC,CAAC,EACvCJ,0BAA0B,CAAC;QAAEI,IAAI,EAAE;MAAI,CAAC,CAAC,EACzCL,wBAAwB,CAAC;QAAEK,IAAI,EAAE;MAAK,CAAC,CAAC,EACxCX,wBAAwB,CAAC;QACrBW,IAAI,EAAE,IAAI;QACVD,UAAU,EAAE;MAChB,CAAC,CAAC,CACL,GAzB4FpB,EAAE,CAAAiE,uBAAA,EAyB9DtD,EAAE,CAACE,iBAAiB,EAAiBC,EAAE,CAACC,YAAY;IAAA,EAAoB;EAAE;AACnH;AACA;EAAA,QAAA+B,SAAA,oBAAAA,SAAA,KA3BqG9C,EAAE,CAAA+C,iBAAA,CA2BXM,OAAO,EAAc,CAAC;IACtGtB,IAAI,EAAE1B,SAAS;IACf2C,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBgC,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CACPvD,4BAA4B,CAACU,gBAAgB,CAAC,EAC9CJ,wBAAwB,CAAC;QAAEG,IAAI,EAAE;MAAI,CAAC,CAAC,EACvCJ,0BAA0B,CAAC;QAAEI,IAAI,EAAE;MAAI,CAAC,CAAC,EACzCL,wBAAwB,CAAC;QAAEK,IAAI,EAAE;MAAK,CAAC,CAAC,EACxCX,wBAAwB,CAAC;QACrBW,IAAI,EAAE,IAAI;QACVD,UAAU,EAAE;MAChB,CAAC,CAAC,CACL;MACDgD,cAAc,EAAE,CAACvD,iBAAiB,EAAEE,YAAY,CAAC;MACjDoC,IAAI,EAAE;QAAE,kBAAkB,EAAE;MAAO;IACvC,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE9B,IAAI,EAAE,CAAC;MACrBU,IAAI,EAAEzB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASa,wBAAwB,EAAEG,gBAAgB,EAAE+B,OAAO,EAAE9B,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}