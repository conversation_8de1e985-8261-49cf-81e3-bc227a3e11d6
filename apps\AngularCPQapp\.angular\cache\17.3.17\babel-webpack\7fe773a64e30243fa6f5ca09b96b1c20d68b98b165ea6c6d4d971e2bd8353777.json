{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Injectable, LOCALE_ID, NgZone } from '@angular/core';\nimport { tuiZoneOptimized, tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { TUI_WINDOW_SIZE, TUI_IS_WEBKIT } from '@taiga-ui/cdk/tokens';\nimport { TUI_MEDIA } from '@taiga-ui/core/tokens';\nimport { Observable, map, distinctUntilChanged, shareReplay, fromEvent, startWith, of, finalize } from 'rxjs';\nimport { WA_WINDOW, WA_ANIMATION_FRAME } from '@ng-web-apis/common';\nimport { EMPTY_CLIENT_RECT } from '@taiga-ui/cdk/constants';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { TuiPositionAccessor } from '@taiga-ui/core/classes';\n\n/**\n * Service to provide the current breakpoint based on Taiga UI's media queries\n */\nclass TuiBreakpointService extends Observable {\n  constructor() {\n    super(subscriber => this.stream$.subscribe(subscriber));\n    this.media = inject(TUI_MEDIA);\n    this.sorted = Object.values(this.media).sort((a, b) => a - b);\n    this.invert = Object.keys(this.media).reduce((ret, key) => ({\n      ...ret,\n      [this.media[key]]: key\n    }), {});\n    this.stream$ = inject(TUI_WINDOW_SIZE).pipe(map(({\n      width\n    }) => this.sorted.find(size => size > width)), map(key => this.invert[key || this.sorted[this.sorted.length - 1] || 0] ?? null), distinctUntilChanged(), tuiZoneOptimized(), shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }));\n  }\n  static {\n    this.ɵfac = function TuiBreakpointService_Factory(t) {\n      return new (t || TuiBreakpointService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiBreakpointService,\n      factory: TuiBreakpointService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiBreakpointService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n\n/**\n * @deprecated use {@link TUI_DARK_MODE} instead\n */\nclass TuiDarkThemeService extends Observable {\n  constructor() {\n    const media = inject(WA_WINDOW).matchMedia('(prefers-color-scheme: dark)');\n    const media$ = fromEvent(media, 'change').pipe(startWith(null), map(() => media.matches), shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }));\n    super(subscriber => media$.subscribe(subscriber));\n  }\n  static {\n    this.ɵfac = function TuiDarkThemeService_Factory(t) {\n      return new (t || TuiDarkThemeService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiDarkThemeService,\n      factory: TuiDarkThemeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDarkThemeService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nclass TuiFormatDateService {\n  constructor() {\n    this.locale = inject(LOCALE_ID);\n  }\n  format(timestamp) {\n    return of(new Date(timestamp).toLocaleTimeString(this.locale, {\n      hour: 'numeric',\n      minute: '2-digit'\n    }));\n  }\n  static {\n    this.ɵfac = function TuiFormatDateService_Factory(t) {\n      return new (t || TuiFormatDateService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiFormatDateService,\n      factory: TuiFormatDateService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFormatDateService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass TuiPositionService extends Observable {\n  constructor() {\n    const animationFrame$ = inject(WA_ANIMATION_FRAME);\n    const zone = inject(NgZone);\n    super(subscriber => animationFrame$.pipe(startWith(null), map(() => this.accessor.getPosition(this.el.getBoundingClientRect(), this.el)), tuiZonefree(zone), finalize(() => this.accessor.getPosition(EMPTY_CLIENT_RECT))).subscribe(subscriber));\n    this.el = tuiInjectElement();\n    this.accessor = inject(TuiPositionAccessor);\n  }\n  static {\n    this.ɵfac = function TuiPositionService_Factory(t) {\n      return new (t || TuiPositionService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiPositionService,\n      factory: TuiPositionService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPositionService, [{\n    type: Injectable\n  }], function () {\n    return [];\n  }, null);\n})();\nclass TuiVisualViewportService {\n  constructor() {\n    this.isWebkit = inject(TUI_IS_WEBKIT);\n    this.win = inject(WA_WINDOW);\n  }\n  // https://bugs.webkit.org/show_bug.cgi?id=207089\n  correct(point) {\n    return this.isWebkit ? [point[0] + (this.win.visualViewport?.offsetTop ?? 0), point[1] + (this.win.visualViewport?.offsetLeft ?? 0)] : point;\n  }\n  static {\n    this.ɵfac = function TuiVisualViewportService_Factory(t) {\n      return new (t || TuiVisualViewportService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiVisualViewportService,\n      factory: TuiVisualViewportService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiVisualViewportService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiBreakpointService, TuiDarkThemeService, TuiFormatDateService, TuiPositionService, TuiVisualViewportService };", "map": {"version": 3, "names": ["i0", "inject", "Injectable", "LOCALE_ID", "NgZone", "tuiZoneOptimized", "tuiZonefree", "TUI_WINDOW_SIZE", "TUI_IS_WEBKIT", "TUI_MEDIA", "Observable", "map", "distinctUntilChanged", "shareReplay", "fromEvent", "startWith", "of", "finalize", "WA_WINDOW", "WA_ANIMATION_FRAME", "EMPTY_CLIENT_RECT", "tuiInjectElement", "TuiPositionAccessor", "TuiBreakpointService", "constructor", "subscriber", "stream$", "subscribe", "media", "sorted", "Object", "values", "sort", "a", "b", "invert", "keys", "reduce", "ret", "key", "pipe", "width", "find", "size", "length", "bufferSize", "refCount", "ɵfac", "TuiBreakpointService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "TuiDarkThemeService", "matchMedia", "media$", "matches", "TuiDarkThemeService_Factory", "TuiFormatDateService", "locale", "format", "timestamp", "Date", "toLocaleTimeString", "hour", "minute", "TuiFormatDateService_Factory", "TuiPositionService", "animationFrame$", "zone", "accessor", "getPosition", "el", "getBoundingClientRect", "TuiPositionService_Factory", "TuiVisualViewportService", "isWebkit", "win", "correct", "point", "visualViewport", "offsetTop", "offsetLeft", "TuiVisualViewportService_Factory"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-services.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Injectable, LOCALE_ID, NgZone } from '@angular/core';\nimport { tuiZoneOptimized, tuiZonefree } from '@taiga-ui/cdk/observables';\nimport { TUI_WINDOW_SIZE, TUI_IS_WEBKIT } from '@taiga-ui/cdk/tokens';\nimport { TUI_MEDIA } from '@taiga-ui/core/tokens';\nimport { Observable, map, distinctUntilChanged, shareReplay, fromEvent, startWith, of, finalize } from 'rxjs';\nimport { WA_WINDOW, WA_ANIMATION_FRAME } from '@ng-web-apis/common';\nimport { EMPTY_CLIENT_RECT } from '@taiga-ui/cdk/constants';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { TuiPositionAccessor } from '@taiga-ui/core/classes';\n\n/**\n * Service to provide the current breakpoint based on Taiga UI's media queries\n */\nclass TuiBreakpointService extends Observable {\n    constructor() {\n        super((subscriber) => this.stream$.subscribe(subscriber));\n        this.media = inject(TUI_MEDIA);\n        this.sorted = Object.values(this.media).sort((a, b) => a - b);\n        this.invert = Object.keys(this.media).reduce((ret, key) => ({\n            ...ret,\n            [this.media[key]]: key,\n        }), {});\n        this.stream$ = inject(TUI_WINDOW_SIZE).pipe(map(({ width }) => this.sorted.find((size) => size > width)), map((key) => this.invert[key || this.sorted[this.sorted.length - 1] || 0] ?? null), distinctUntilChanged(), tuiZoneOptimized(), shareReplay({ bufferSize: 1, refCount: true }));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBreakpointService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBreakpointService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiBreakpointService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return []; } });\n\n/**\n * @deprecated use {@link TUI_DARK_MODE} instead\n */\nclass TuiDarkThemeService extends Observable {\n    constructor() {\n        const media = inject(WA_WINDOW).matchMedia('(prefers-color-scheme: dark)');\n        const media$ = fromEvent(media, 'change').pipe(startWith(null), map(() => media.matches), shareReplay({ bufferSize: 1, refCount: true }));\n        super((subscriber) => media$.subscribe(subscriber));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDarkThemeService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDarkThemeService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDarkThemeService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return []; } });\n\nclass TuiFormatDateService {\n    constructor() {\n        this.locale = inject(LOCALE_ID);\n    }\n    format(timestamp) {\n        return of(new Date(timestamp).toLocaleTimeString(this.locale, {\n            hour: 'numeric',\n            minute: '2-digit',\n        }));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFormatDateService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFormatDateService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFormatDateService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\nclass TuiPositionService extends Observable {\n    constructor() {\n        const animationFrame$ = inject(WA_ANIMATION_FRAME);\n        const zone = inject(NgZone);\n        super((subscriber) => animationFrame$\n            .pipe(startWith(null), map(() => this.accessor.getPosition(this.el.getBoundingClientRect(), this.el)), tuiZonefree(zone), finalize(() => this.accessor.getPosition(EMPTY_CLIENT_RECT)))\n            .subscribe(subscriber));\n        this.el = tuiInjectElement();\n        this.accessor = inject(TuiPositionAccessor);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPositionService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPositionService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPositionService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return []; } });\n\nclass TuiVisualViewportService {\n    constructor() {\n        this.isWebkit = inject(TUI_IS_WEBKIT);\n        this.win = inject(WA_WINDOW);\n    }\n    // https://bugs.webkit.org/show_bug.cgi?id=207089\n    correct(point) {\n        return this.isWebkit\n            ? [\n                point[0] + (this.win.visualViewport?.offsetTop ?? 0),\n                point[1] + (this.win.visualViewport?.offsetLeft ?? 0),\n            ]\n            : point;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiVisualViewportService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiVisualViewportService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiVisualViewportService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiBreakpointService, TuiDarkThemeService, TuiFormatDateService, TuiPositionService, TuiVisualViewportService };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACrE,SAASC,gBAAgB,EAAEC,WAAW,QAAQ,2BAA2B;AACzE,SAASC,eAAe,EAAEC,aAAa,QAAQ,sBAAsB;AACrE,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,UAAU,EAAEC,GAAG,EAAEC,oBAAoB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,SAAS,EAAEC,EAAE,EAAEC,QAAQ,QAAQ,MAAM;AAC7G,SAASC,SAAS,EAAEC,kBAAkB,QAAQ,qBAAqB;AACnE,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,mBAAmB,QAAQ,wBAAwB;;AAE5D;AACA;AACA;AACA,MAAMC,oBAAoB,SAASb,UAAU,CAAC;EAC1Cc,WAAWA,CAAA,EAAG;IACV,KAAK,CAAEC,UAAU,IAAK,IAAI,CAACC,OAAO,CAACC,SAAS,CAACF,UAAU,CAAC,CAAC;IACzD,IAAI,CAACG,KAAK,GAAG3B,MAAM,CAACQ,SAAS,CAAC;IAC9B,IAAI,CAACoB,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACH,KAAK,CAAC,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;IAC7D,IAAI,CAACC,MAAM,GAAGL,MAAM,CAACM,IAAI,CAAC,IAAI,CAACR,KAAK,CAAC,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,MAAM;MACxD,GAAGD,GAAG;MACN,CAAC,IAAI,CAACV,KAAK,CAACW,GAAG,CAAC,GAAGA;IACvB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACP,IAAI,CAACb,OAAO,GAAGzB,MAAM,CAACM,eAAe,CAAC,CAACiC,IAAI,CAAC7B,GAAG,CAAC,CAAC;MAAE8B;IAAM,CAAC,KAAK,IAAI,CAACZ,MAAM,CAACa,IAAI,CAAEC,IAAI,IAAKA,IAAI,GAAGF,KAAK,CAAC,CAAC,EAAE9B,GAAG,CAAE4B,GAAG,IAAK,IAAI,CAACJ,MAAM,CAACI,GAAG,IAAI,IAAI,CAACV,MAAM,CAAC,IAAI,CAACA,MAAM,CAACe,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,EAAEhC,oBAAoB,CAAC,CAAC,EAAEP,gBAAgB,CAAC,CAAC,EAAEQ,WAAW,CAAC;MAAEgC,UAAU,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;EAC7R;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyF1B,oBAAoB;IAAA,CAAoD;EAAE;EACrL;IAAS,IAAI,CAAC2B,KAAK,kBAD8ElD,EAAE,CAAAmD,kBAAA;MAAAC,KAAA,EACY7B,oBAAoB;MAAA8B,OAAA,EAApB9B,oBAAoB,CAAAwB,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAChK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGvD,EAAE,CAAAwD,iBAAA,CAGXjC,oBAAoB,EAAc,CAAC;IACnHkC,IAAI,EAAEvD,UAAU;IAChBwD,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;;AAEtD;AACA;AACA;AACA,MAAMK,mBAAmB,SAASjD,UAAU,CAAC;EACzCc,WAAWA,CAAA,EAAG;IACV,MAAMI,KAAK,GAAG3B,MAAM,CAACiB,SAAS,CAAC,CAAC0C,UAAU,CAAC,8BAA8B,CAAC;IAC1E,MAAMC,MAAM,GAAG/C,SAAS,CAACc,KAAK,EAAE,QAAQ,CAAC,CAACY,IAAI,CAACzB,SAAS,CAAC,IAAI,CAAC,EAAEJ,GAAG,CAAC,MAAMiB,KAAK,CAACkC,OAAO,CAAC,EAAEjD,WAAW,CAAC;MAAEgC,UAAU,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;IACzI,KAAK,CAAErB,UAAU,IAAKoC,MAAM,CAAClC,SAAS,CAACF,UAAU,CAAC,CAAC;EACvD;EACA;IAAS,IAAI,CAACsB,IAAI,YAAAgB,4BAAAd,CAAA;MAAA,YAAAA,CAAA,IAAyFU,mBAAmB;IAAA,CAAoD;EAAE;EACpL;IAAS,IAAI,CAACT,KAAK,kBApB8ElD,EAAE,CAAAmD,kBAAA;MAAAC,KAAA,EAoBYO,mBAAmB;MAAAN,OAAA,EAAnBM,mBAAmB,CAAAZ,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAC/J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAtBqGvD,EAAE,CAAAwD,iBAAA,CAsBXG,mBAAmB,EAAc,CAAC;IAClHF,IAAI,EAAEvD,UAAU;IAChBwD,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAMU,oBAAoB,CAAC;EACvBxC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyC,MAAM,GAAGhE,MAAM,CAACE,SAAS,CAAC;EACnC;EACA+D,MAAMA,CAACC,SAAS,EAAE;IACd,OAAOnD,EAAE,CAAC,IAAIoD,IAAI,CAACD,SAAS,CAAC,CAACE,kBAAkB,CAAC,IAAI,CAACJ,MAAM,EAAE;MAC1DK,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC,CAAC;EACP;EACA;IAAS,IAAI,CAACxB,IAAI,YAAAyB,6BAAAvB,CAAA;MAAA,YAAAA,CAAA,IAAyFe,oBAAoB;IAAA,CAAoD;EAAE;EACrL;IAAS,IAAI,CAACd,KAAK,kBAxC8ElD,EAAE,CAAAmD,kBAAA;MAAAC,KAAA,EAwCYY,oBAAoB;MAAAX,OAAA,EAApBW,oBAAoB,CAAAjB,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAChK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA1CqGvD,EAAE,CAAAwD,iBAAA,CA0CXQ,oBAAoB,EAAc,CAAC;IACnHP,IAAI,EAAEvD,UAAU;IAChBwD,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMmB,kBAAkB,SAAS/D,UAAU,CAAC;EACxCc,WAAWA,CAAA,EAAG;IACV,MAAMkD,eAAe,GAAGzE,MAAM,CAACkB,kBAAkB,CAAC;IAClD,MAAMwD,IAAI,GAAG1E,MAAM,CAACG,MAAM,CAAC;IAC3B,KAAK,CAAEqB,UAAU,IAAKiD,eAAe,CAChClC,IAAI,CAACzB,SAAS,CAAC,IAAI,CAAC,EAAEJ,GAAG,CAAC,MAAM,IAAI,CAACiE,QAAQ,CAACC,WAAW,CAAC,IAAI,CAACC,EAAE,CAACC,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAACD,EAAE,CAAC,CAAC,EAAExE,WAAW,CAACqE,IAAI,CAAC,EAAE1D,QAAQ,CAAC,MAAM,IAAI,CAAC2D,QAAQ,CAACC,WAAW,CAACzD,iBAAiB,CAAC,CAAC,CAAC,CACtLO,SAAS,CAACF,UAAU,CAAC,CAAC;IAC3B,IAAI,CAACqD,EAAE,GAAGzD,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACuD,QAAQ,GAAG3E,MAAM,CAACqB,mBAAmB,CAAC;EAC/C;EACA;IAAS,IAAI,CAACyB,IAAI,YAAAiC,2BAAA/B,CAAA;MAAA,YAAAA,CAAA,IAAyFwB,kBAAkB;IAAA,CAAoD;EAAE;EACnL;IAAS,IAAI,CAACvB,KAAK,kBA5D8ElD,EAAE,CAAAmD,kBAAA;MAAAC,KAAA,EA4DYqB,kBAAkB;MAAApB,OAAA,EAAlBoB,kBAAkB,CAAA1B;IAAA,EAAG;EAAE;AAC1I;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KA9DqGvD,EAAE,CAAAwD,iBAAA,CA8DXiB,kBAAkB,EAAc,CAAC;IACjHhB,IAAI,EAAEvD;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAM+E,wBAAwB,CAAC;EAC3BzD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0D,QAAQ,GAAGjF,MAAM,CAACO,aAAa,CAAC;IACrC,IAAI,CAAC2E,GAAG,GAAGlF,MAAM,CAACiB,SAAS,CAAC;EAChC;EACA;EACAkE,OAAOA,CAACC,KAAK,EAAE;IACX,OAAO,IAAI,CAACH,QAAQ,GACd,CACEG,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAACF,GAAG,CAACG,cAAc,EAAEC,SAAS,IAAI,CAAC,CAAC,EACpDF,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAACF,GAAG,CAACG,cAAc,EAAEE,UAAU,IAAI,CAAC,CAAC,CACxD,GACCH,KAAK;EACf;EACA;IAAS,IAAI,CAACtC,IAAI,YAAA0C,iCAAAxC,CAAA;MAAA,YAAAA,CAAA,IAAyFgC,wBAAwB;IAAA,CAAoD;EAAE;EACzL;IAAS,IAAI,CAAC/B,KAAK,kBAjF8ElD,EAAE,CAAAmD,kBAAA;MAAAC,KAAA,EAiFY6B,wBAAwB;MAAA5B,OAAA,EAAxB4B,wBAAwB,CAAAlC,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AACpK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAnFqGvD,EAAE,CAAAwD,iBAAA,CAmFXyB,wBAAwB,EAAc,CAAC;IACvHxB,IAAI,EAAEvD,UAAU;IAChBwD,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS/B,oBAAoB,EAAEoC,mBAAmB,EAAEK,oBAAoB,EAAES,kBAAkB,EAAEQ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}