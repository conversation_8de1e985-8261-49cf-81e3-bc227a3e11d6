{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nclass TuiConnectedStyles {\n  static {\n    this.ɵfac = function TuiConnectedStyles_Factory(t) {\n      return new (t || TuiConnectedStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiConnectedStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-connected-styles\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiConnectedStyles_Template(rf, ctx) {},\n      styles: [\"[tuiConnected]{--t-image-size: 2.5rem;--t-connected-height: calc(100% - var(--t-image-size) - .5rem)}[tuiConnected][tuiCardLarge]{--t-connected-height: calc(100% - var(--t-image-size) - .25rem)}tui-accordion[tuiConnected]{--t-image-size: 2rem}[tuiConnected]>[tuiAccordion]:before,[tuiConnected]>[tuiCell]:not(:last-of-type):before,[tuiConnected]>[tuiStep]:not(:last-of-type):before{content:\\\"\\\";position:absolute;top:calc(var(--t-image-size) + .25rem);left:calc(var(--t-image-size) / 2);display:block;block-size:var(--t-connected-height);inline-size:1px;color:var(--tui-border-normal);background:linear-gradient(to bottom,currentColor 75%,transparent 75%) top center / 300% 300%;background-clip:content-box;padding:inherit}[tuiConnected]>[tuiAccordion]{gap:1rem;--t-margin: 0}[tuiConnected]>[tuiAccordion]:before{top:0;left:calc(var(--t-image-size) / 2);block-size:100%;margin:0;background:var(--tui-border-normal);-webkit-mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2),transparent calc(50% + var(--t-image-size) / 2),black calc(50% + var(--t-image-size) / 2));mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2),transparent calc(50% + var(--t-image-size) / 2),black calc(50% + var(--t-image-size) / 2))}[tuiConnected]>[tuiAccordion]:first-of-type:before{-webkit-mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2));mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2))}[tuiConnected]>[tuiAccordion]:last-of-type:before{-webkit-mask:linear-gradient(to bottom,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2));mask:linear-gradient(to bottom,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2))}[tuiConnected]>tui-expand>*{padding:0 0 0 3rem}[tuiConnected]>tui-expand:not(:last-child):before{content:\\\"\\\";position:absolute;top:0;left:calc(var(--t-image-size) / 2);block-size:100%;inline-size:1px;background:var(--tui-border-normal)}[tuiConnected]>[tuiStep]{--t-image-size: 2rem;--t-connected-height: calc(100% - var(--t-image-size) + .75rem)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiConnectedStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-connected-styles'\n      },\n      styles: [\"[tuiConnected]{--t-image-size: 2.5rem;--t-connected-height: calc(100% - var(--t-image-size) - .5rem)}[tuiConnected][tuiCardLarge]{--t-connected-height: calc(100% - var(--t-image-size) - .25rem)}tui-accordion[tuiConnected]{--t-image-size: 2rem}[tuiConnected]>[tuiAccordion]:before,[tuiConnected]>[tuiCell]:not(:last-of-type):before,[tuiConnected]>[tuiStep]:not(:last-of-type):before{content:\\\"\\\";position:absolute;top:calc(var(--t-image-size) + .25rem);left:calc(var(--t-image-size) / 2);display:block;block-size:var(--t-connected-height);inline-size:1px;color:var(--tui-border-normal);background:linear-gradient(to bottom,currentColor 75%,transparent 75%) top center / 300% 300%;background-clip:content-box;padding:inherit}[tuiConnected]>[tuiAccordion]{gap:1rem;--t-margin: 0}[tuiConnected]>[tuiAccordion]:before{top:0;left:calc(var(--t-image-size) / 2);block-size:100%;margin:0;background:var(--tui-border-normal);-webkit-mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2),transparent calc(50% + var(--t-image-size) / 2),black calc(50% + var(--t-image-size) / 2));mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2),transparent calc(50% + var(--t-image-size) / 2),black calc(50% + var(--t-image-size) / 2))}[tuiConnected]>[tuiAccordion]:first-of-type:before{-webkit-mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2));mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2))}[tuiConnected]>[tuiAccordion]:last-of-type:before{-webkit-mask:linear-gradient(to bottom,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2));mask:linear-gradient(to bottom,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2))}[tuiConnected]>tui-expand>*{padding:0 0 0 3rem}[tuiConnected]>tui-expand:not(:last-child):before{content:\\\"\\\";position:absolute;top:0;left:calc(var(--t-image-size) / 2);block-size:100%;inline-size:1px;background:var(--tui-border-normal)}[tuiConnected]>[tuiStep]{--t-image-size: 2rem;--t-connected-height: calc(100% - var(--t-image-size) + .75rem)}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiConnected {\n  constructor() {\n    this.nothing = tuiWithStyles(TuiConnectedStyles);\n  }\n  static {\n    this.ɵfac = function TuiConnected_Factory(t) {\n      return new (t || TuiConnected)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiConnected,\n      selectors: [[\"\", \"tuiConnected\", \"\"]],\n      hostAttrs: [\"tuiConnected\", \"\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiConnected, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiConnected]',\n      host: {\n        tuiConnected: ''\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiConnected };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Directive", "tuiWithStyles", "TuiConnectedStyles", "ɵfac", "TuiConnectedStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiConnectedStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiConnected", "constructor", "nothing", "TuiConnected_Factory", "ɵdir", "ɵɵdefineDirective", "selector", "tuiConnected"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-connected.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Directive } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\n\nclass TuiConnectedStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiConnectedStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiConnectedStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-connected-styles\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiConnected]{--t-image-size: 2.5rem;--t-connected-height: calc(100% - var(--t-image-size) - .5rem)}[tuiConnected][tuiCardLarge]{--t-connected-height: calc(100% - var(--t-image-size) - .25rem)}tui-accordion[tuiConnected]{--t-image-size: 2rem}[tuiConnected]>[tuiAccordion]:before,[tuiConnected]>[tuiCell]:not(:last-of-type):before,[tuiConnected]>[tuiStep]:not(:last-of-type):before{content:\\\"\\\";position:absolute;top:calc(var(--t-image-size) + .25rem);left:calc(var(--t-image-size) / 2);display:block;block-size:var(--t-connected-height);inline-size:1px;color:var(--tui-border-normal);background:linear-gradient(to bottom,currentColor 75%,transparent 75%) top center / 300% 300%;background-clip:content-box;padding:inherit}[tuiConnected]>[tuiAccordion]{gap:1rem;--t-margin: 0}[tuiConnected]>[tuiAccordion]:before{top:0;left:calc(var(--t-image-size) / 2);block-size:100%;margin:0;background:var(--tui-border-normal);-webkit-mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2),transparent calc(50% + var(--t-image-size) / 2),black calc(50% + var(--t-image-size) / 2));mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2),transparent calc(50% + var(--t-image-size) / 2),black calc(50% + var(--t-image-size) / 2))}[tuiConnected]>[tuiAccordion]:first-of-type:before{-webkit-mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2));mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2))}[tuiConnected]>[tuiAccordion]:last-of-type:before{-webkit-mask:linear-gradient(to bottom,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2));mask:linear-gradient(to bottom,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2))}[tuiConnected]>tui-expand>*{padding:0 0 0 3rem}[tuiConnected]>tui-expand:not(:last-child):before{content:\\\"\\\";position:absolute;top:0;left:calc(var(--t-image-size) / 2);block-size:100%;inline-size:1px;background:var(--tui-border-normal)}[tuiConnected]>[tuiStep]{--t-image-size: 2rem;--t-connected-height: calc(100% - var(--t-image-size) + .75rem)}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiConnectedStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-connected-styles',\n                    }, styles: [\"[tuiConnected]{--t-image-size: 2.5rem;--t-connected-height: calc(100% - var(--t-image-size) - .5rem)}[tuiConnected][tuiCardLarge]{--t-connected-height: calc(100% - var(--t-image-size) - .25rem)}tui-accordion[tuiConnected]{--t-image-size: 2rem}[tuiConnected]>[tuiAccordion]:before,[tuiConnected]>[tuiCell]:not(:last-of-type):before,[tuiConnected]>[tuiStep]:not(:last-of-type):before{content:\\\"\\\";position:absolute;top:calc(var(--t-image-size) + .25rem);left:calc(var(--t-image-size) / 2);display:block;block-size:var(--t-connected-height);inline-size:1px;color:var(--tui-border-normal);background:linear-gradient(to bottom,currentColor 75%,transparent 75%) top center / 300% 300%;background-clip:content-box;padding:inherit}[tuiConnected]>[tuiAccordion]{gap:1rem;--t-margin: 0}[tuiConnected]>[tuiAccordion]:before{top:0;left:calc(var(--t-image-size) / 2);block-size:100%;margin:0;background:var(--tui-border-normal);-webkit-mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2),transparent calc(50% + var(--t-image-size) / 2),black calc(50% + var(--t-image-size) / 2));mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2),transparent calc(50% + var(--t-image-size) / 2),black calc(50% + var(--t-image-size) / 2))}[tuiConnected]>[tuiAccordion]:first-of-type:before{-webkit-mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2));mask:linear-gradient(to top,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2))}[tuiConnected]>[tuiAccordion]:last-of-type:before{-webkit-mask:linear-gradient(to bottom,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2));mask:linear-gradient(to bottom,black calc(50% - var(--t-image-size) / 2),transparent calc(50% - var(--t-image-size) / 2))}[tuiConnected]>tui-expand>*{padding:0 0 0 3rem}[tuiConnected]>tui-expand:not(:last-child):before{content:\\\"\\\";position:absolute;top:0;left:calc(var(--t-image-size) / 2);block-size:100%;inline-size:1px;background:var(--tui-border-normal)}[tuiConnected]>[tuiStep]{--t-image-size: 2rem;--t-connected-height: calc(100% - var(--t-image-size) + .75rem)}\\n\"] }]\n        }] });\nclass TuiConnected {\n    constructor() {\n        this.nothing = tuiWithStyles(TuiConnectedStyles);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiConnected, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiConnected, isStandalone: true, selector: \"[tuiConnected]\", host: { attributes: { \"tuiConnected\": \"\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiConnected, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiConnected]',\n                    host: {\n                        tuiConnected: '',\n                    },\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiConnected };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,QAAQ,eAAe;AAChG,SAASC,aAAa,QAAQ,mCAAmC;AAEjE,MAAMC,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACC,IAAI,YAAAC,2BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,kBAAkB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAACI,IAAI,kBAD+EV,EAAE,CAAAW,iBAAA;MAAAC,IAAA,EACJN,kBAAkB;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADhBhB,EAAE,CAAAiB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC0/E;EAAE;AACnmF;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG3B,EAAE,CAAA4B,iBAAA,CAGXtB,kBAAkB,EAAc,CAAC;IACjHM,IAAI,EAAEX,SAAS;IACf4B,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAEvB,iBAAiB,CAAC4B,IAAI;MAAEJ,eAAe,EAAEvB,uBAAuB,CAAC4B,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,mvEAAmvE;IAAE,CAAC;EAC9wE,CAAC,CAAC;AAAA;AACV,MAAMU,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG/B,aAAa,CAACC,kBAAkB,CAAC;EACpD;EACA;IAAS,IAAI,CAACC,IAAI,YAAA8B,qBAAA5B,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACI,IAAI,kBAd+EtC,EAAE,CAAAuC,iBAAA;MAAA3B,IAAA,EAcJsB,YAAY;MAAArB,SAAA;MAAAC,SAAA,mBAAwF,EAAE;MAAAC,UAAA;IAAA,EAAqB;EAAE;AAChO;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAhBqG3B,EAAE,CAAA4B,iBAAA,CAgBXM,YAAY,EAAc,CAAC;IAC3GtB,IAAI,EAAER,SAAS;IACfyB,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChByB,QAAQ,EAAE,gBAAgB;MAC1BR,IAAI,EAAE;QACFS,YAAY,EAAE;MAClB;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASP,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}