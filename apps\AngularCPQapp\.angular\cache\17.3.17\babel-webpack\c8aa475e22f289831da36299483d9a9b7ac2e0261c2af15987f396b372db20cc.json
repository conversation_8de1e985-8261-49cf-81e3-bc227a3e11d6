{"ast": null, "code": "import { Ng<PERSON>orO<PERSON>, NgIf, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, signal, computed, effect, Component, ChangeDetectionStrategy, Input, Directive } from '@angular/core';\nimport { TuiControl, tuiAsControl } from '@taiga-ui/cdk/classes';\nimport { TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { tuiIsPresent, tuiDirectiveBinding } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldDirective, tuiAsTextfieldAccessor, TuiWithTextfield, TuiSelectLike } from '@taiga-ui/core/components/textfield';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { tuiIsFlat, tuiInjectValue } from '@taiga-ui/kit/utils';\nimport { TuiOptionWithValue, tuiAsOptionContent } from '@taiga-ui/core/components/data-list';\nimport { tuiDropdownOpen, tuiDropdownEnabled } from '@taiga-ui/core/directives/dropdown';\nimport * as i1$1 from '@taiga-ui/core/components/scrollbar';\nimport { TuiScrollIntoView } from '@taiga-ui/core/components/scrollbar';\nimport { TuiIcons } from '@taiga-ui/core/directives/icons';\nimport { TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nconst _c0 = [\"tuiSelect\", \"\"];\nconst _c1 = a0 => ({\n  $implicit: a0\n});\nfunction TuiNativeSelect_option_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.placeholder(), \"\\n\");\n  }\n}\nfunction TuiNativeSelect_ng_container_1_optgroup_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiNativeSelect_ng_container_1_optgroup_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"optgroup\", 6);\n    i0.ɵɵtemplate(1, TuiNativeSelect_ng_container_1_optgroup_1_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const index_r2 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const options_r3 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"label\", ctx_r0.labels[index_r2]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", options_r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c1, ctx_r0.items[index_r2]));\n  }\n}\nfunction TuiNativeSelect_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiNativeSelect_ng_container_1_optgroup_1_Template, 2, 5, \"optgroup\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.labels);\n  }\n}\nfunction TuiNativeSelect_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiNativeSelect_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiNativeSelect_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const options_r3 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", options_r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c1, ctx_r0.items));\n  }\n}\nfunction TuiNativeSelect_ng_template_4_option_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.itemsHandlers.disabledItemHandler()(option_r4))(\"selected\", ctx_r0.isSelected()(option_r4))(\"value\", ctx_r0.itemsHandlers.stringify()(option_r4));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.itemsHandlers.stringify()(option_r4), \" \");\n  }\n}\nfunction TuiNativeSelect_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiNativeSelect_ng_template_4_option_0_Template, 2, 4, \"option\", 8);\n  }\n  if (rf & 2) {\n    const items_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngForOf\", items_r5);\n  }\n}\nclass TuiNativeSelect extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.textfield = inject(TuiTextfieldDirective);\n    this.isFlat = tuiIsFlat;\n    this.placeholder = signal('');\n    this.itemsHandlers = inject(TUI_ITEMS_HANDLERS);\n    this.stringified = computed((value = this.value()) => tuiIsPresent(value) ? this.itemsHandlers.stringify()(value) : '');\n    this.showPlaceholder = computed(() => this.placeholder() && !this.stringified());\n    this.isSelected = computed((value = this.value()) => x => tuiIsPresent(value) && this.itemsHandlers.identityMatcher()(x, value));\n    this.valueEffect = effect(() => {\n      this.textfield.value.set(this.stringified());\n    }, TUI_ALLOW_SIGNAL_WRITES);\n    this.items = [];\n    this.labels = [];\n  }\n  // TODO(v5): use signal inputs\n  set placeholderSetter(x) {\n    this.placeholder.set(x);\n  }\n  setValue(value) {\n    this.onChange(value);\n  }\n  selectOption(index) {\n    const items = this.items?.flat() ?? [];\n    this.onChange(items[index - (this.showPlaceholder() ? 1 : 0)] ?? null);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiNativeSelect_BaseFactory;\n      return function TuiNativeSelect_Factory(t) {\n        return (ɵTuiNativeSelect_BaseFactory || (ɵTuiNativeSelect_BaseFactory = i0.ɵɵgetInheritedFactory(TuiNativeSelect)))(t || TuiNativeSelect);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiNativeSelect,\n      selectors: [[\"select\", \"tuiSelect\", \"\"]],\n      hostVars: 2,\n      hostBindings: function TuiNativeSelect_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"change\", function TuiNativeSelect_change_HostBindingHandler($event) {\n            return ctx.selectOption($event.target.options.selectedIndex);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"disabled\", !ctx.interactive());\n          i0.ɵɵattribute(\"aria-invalid\", ctx.invalid());\n        }\n      },\n      inputs: {\n        items: \"items\",\n        labels: \"labels\",\n        placeholderSetter: [i0.ɵɵInputFlags.None, \"placeholder\", \"placeholderSetter\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsTextfieldAccessor(TuiNativeSelect), tuiAsControl(TuiNativeSelect)]), i0.ɵɵHostDirectivesFeature([i1.TuiWithTextfield]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 6,\n      vars: 3,\n      consts: [[\"flatItems\", \"\"], [\"options\", \"\"], [\"disabled\", \"\", \"selected\", \"\", \"value\", \"\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"disabled\", \"\", \"selected\", \"\", \"value\", \"\"], [3, \"label\", 4, \"ngFor\", \"ngForOf\"], [3, \"label\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"disabled\", \"selected\", \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"disabled\", \"selected\", \"value\"]],\n      template: function TuiNativeSelect_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiNativeSelect_option_0_Template, 2, 1, \"option\", 2)(1, TuiNativeSelect_ng_container_1_Template, 2, 1, \"ng-container\", 3)(2, TuiNativeSelect_ng_template_2_Template, 1, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, TuiNativeSelect_ng_template_4_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const flatItems_r6 = i0.ɵɵreference(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.showPlaceholder());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.items && !ctx.isFlat(ctx.items))(\"ngIfElse\", flatItems_r6);\n        }\n      },\n      dependencies: [NgForOf, NgIf, NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiNativeSelect, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'select[tuiSelect]',\n      imports: [NgForOf, NgIf, NgTemplateOutlet],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAsTextfieldAccessor(TuiNativeSelect), tuiAsControl(TuiNativeSelect)],\n      hostDirectives: [TuiWithTextfield],\n      host: {\n        '[attr.aria-invalid]': 'invalid()',\n        '[disabled]': '!interactive()',\n        '(change)': 'selectOption($event.target.options.selectedIndex)'\n      },\n      template: \"<option\\n    *ngIf=\\\"showPlaceholder()\\\"\\n    disabled\\n    selected\\n    value=\\\"\\\"\\n>\\n    {{ placeholder() }}\\n</option>\\n\\n<ng-container *ngIf=\\\"items && !isFlat(items); else flatItems\\\">\\n    <optgroup\\n        *ngFor=\\\"let group of labels; let index = index\\\"\\n        [label]=\\\"labels[index]\\\"\\n    >\\n        <ng-container *ngTemplateOutlet=\\\"options; context: {$implicit: items[index]}\\\" />\\n    </optgroup>\\n</ng-container>\\n\\n<ng-template #flatItems>\\n    <ng-container *ngTemplateOutlet=\\\"options; context: {$implicit: items}\\\" />\\n</ng-template>\\n\\n<ng-template\\n    #options\\n    let-items\\n>\\n    <option\\n        *ngFor=\\\"let option of items\\\"\\n        [disabled]=\\\"itemsHandlers.disabledItemHandler()(option)\\\"\\n        [selected]=\\\"isSelected()(option)\\\"\\n        [value]=\\\"itemsHandlers.stringify()(option)\\\"\\n    >\\n        {{ itemsHandlers.stringify()(option) }}\\n    </option>\\n</ng-template>\\n\"\n    }]\n  }], null, {\n    items: [{\n      type: Input\n    }],\n    labels: [{\n      type: Input\n    }],\n    placeholderSetter: [{\n      type: Input,\n      args: ['placeholder']\n    }]\n  });\n})();\nclass TuiSelectOption {\n  constructor() {\n    this.handlers = inject(TUI_ITEMS_HANDLERS);\n    this.value = tuiInjectValue();\n    this.option = inject(TuiOptionWithValue, {\n      optional: true\n    });\n    this.icon = this.option && tuiDirectiveBinding(TuiIcons, 'iconEnd', inject(TUI_COMMON_ICONS).check, {});\n    this.selected = computed((controlValue = this.value(), optionValue = this.option?.value()) => tuiIsPresent(optionValue) && tuiIsPresent(controlValue) && this.handlers.identityMatcher()(controlValue, optionValue));\n    this.scrolled = tuiDirectiveBinding(TuiScrollIntoView, 'tuiScrollIntoView', this.selected);\n  }\n  static {\n    this.ɵfac = function TuiSelectOption_Factory(t) {\n      return new (t || TuiSelectOption)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiSelectOption,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [\"tuiSelectOption\", \"\"],\n      hostVars: 4,\n      hostBindings: function TuiSelectOption_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_value\", ctx.option)(\"_selected\", ctx.selected());\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([i1$1.TuiScrollIntoView]), i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiSelectOption_Template(rf, ctx) {},\n      styles: [\"._value[_nghost-%COMP%]:after{color:var(--tui-text-action);opacity:0}tui-data-list[data-size=\\\"s\\\"]   ._value[_nghost-%COMP%]:after{margin-inline-end:-.625rem}tui-data-list[data-size=\\\"m\\\"]   ._value[_nghost-%COMP%]:after, tui-data-list[data-size=\\\"l\\\"]   ._value[_nghost-%COMP%]:after{margin-inline-end:-.5rem}tui-data-list[data-size=\\\"l\\\"]   ._value[_nghost-%COMP%]:after{font-size:1.5rem}._value._selected[_nghost-%COMP%]:after{opacity:1}._value._selected[_nghost-%COMP%] ~   [tuiSelectOption]:after{opacity:0}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSelectOption, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      hostDirectives: [TuiScrollIntoView],\n      host: {\n        tuiSelectOption: '',\n        '[class._value]': 'option',\n        '[class._selected]': 'selected()'\n      },\n      styles: [\":host._value:after{color:var(--tui-text-action);opacity:0}:host-context(tui-data-list[data-size=\\\"s\\\"]) :host._value:after{margin-inline-end:-.625rem}:host-context(tui-data-list[data-size=\\\"m\\\"]) :host._value:after,:host-context(tui-data-list[data-size=\\\"l\\\"]) :host._value:after{margin-inline-end:-.5rem}:host-context(tui-data-list[data-size=\\\"l\\\"]) :host._value:after{font-size:1.5rem}:host._value._selected:after{opacity:1}:host._value._selected~::ng-deep [tuiSelectOption]:after{opacity:0}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiSelectDirective extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.textfield = inject(TuiTextfieldDirective);\n    this.open = tuiDropdownOpen();\n    this.itemsHandlers = inject(TUI_ITEMS_HANDLERS);\n    this.dropdownEnabled = tuiDropdownEnabled(this.interactive);\n    this.valueEffect = effect(() => {\n      const value = this.value();\n      const string = tuiIsPresent(value) ? this.itemsHandlers.stringify()(value) : '';\n      this.textfield.value.set(string);\n    }, TUI_ALLOW_SIGNAL_WRITES);\n  }\n  setValue(value) {\n    this.onChange(value);\n    if (!value) {\n      this.open.set(true);\n    }\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiSelectDirective_BaseFactory;\n      return function TuiSelectDirective_Factory(t) {\n        return (ɵTuiSelectDirective_BaseFactory || (ɵTuiSelectDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiSelectDirective)))(t || TuiSelectDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiSelectDirective,\n      selectors: [[\"input\", \"tuiSelect\", \"\"]],\n      hostVars: 1,\n      hostBindings: function TuiSelectDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function TuiSelectDirective_input_HostBindingHandler($event) {\n            return ($event.inputType == null ? null : $event.inputType.includes(\"delete\")) && ctx.setValue(null);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"disabled\", ctx.disabled());\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsOptionContent(TuiSelectOption), tuiAsTextfieldAccessor(TuiSelectDirective), tuiAsControl(TuiSelectDirective)]), i0.ɵɵHostDirectivesFeature([i1.TuiWithTextfield, i1.TuiSelectLike]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiSelectDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiSelect]',\n      providers: [tuiAsOptionContent(TuiSelectOption), tuiAsTextfieldAccessor(TuiSelectDirective), tuiAsControl(TuiSelectDirective)],\n      hostDirectives: [TuiWithTextfield, TuiSelectLike],\n      host: {\n        '[disabled]': 'disabled()',\n        '(input)': '$event.inputType?.includes(\"delete\") && setValue(null)'\n      }\n    }]\n  }], null, null);\n})();\nconst TuiSelect = [TuiSelectDirective, TuiNativeSelect];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiNativeSelect, TuiSelect, TuiSelectDirective, TuiSelectOption };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "i0", "inject", "signal", "computed", "effect", "Component", "ChangeDetectionStrategy", "Input", "Directive", "TuiControl", "tuiAsControl", "TUI_ALLOW_SIGNAL_WRITES", "tuiIsPresent", "tuiDirectiveBinding", "i1", "TuiTextfieldDirective", "tuiAsTextfieldAccessor", "TuiWithTextfield", "TuiSelectLike", "TUI_ITEMS_HANDLERS", "tui<PERSON>sFlat", "tuiInjectValue", "TuiOptionWithValue", "tuiAsOptionContent", "tuiDropdownOpen", "tuiDropdownEnabled", "i1$1", "TuiScrollIntoView", "TuiIcons", "TUI_COMMON_ICONS", "_c0", "_c1", "a0", "$implicit", "TuiNativeSelect_option_0_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵadvance", "ɵɵtextInterpolate1", "placeholder", "TuiNativeSelect_ng_container_1_optgroup_1_ng_container_1_Template", "ɵɵelementContainer", "TuiNativeSelect_ng_container_1_optgroup_1_Template", "ɵɵtemplate", "index_r2", "index", "options_r3", "ɵɵreference", "ɵɵproperty", "labels", "ɵɵpureFunction1", "items", "TuiNativeSelect_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "TuiNativeSelect_ng_template_2_ng_container_0_Template", "TuiNativeSelect_ng_template_2_Template", "TuiNativeSelect_ng_template_4_option_0_Template", "option_r4", "itemsHandlers", "disabledItemHandler", "isSelected", "stringify", "TuiNativeSelect_ng_template_4_Template", "items_r5", "TuiNativeSelect", "constructor", "arguments", "textfield", "is<PERSON><PERSON>", "stringified", "value", "showPlaceholder", "x", "identityMatcher", "valueEffect", "set", "placeholder<PERSON>etter", "setValue", "onChange", "selectOption", "flat", "ɵfac", "ɵTuiNativeSelect_BaseFactory", "TuiNativeSelect_Factory", "t", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "TuiNativeSelect_HostBindings", "ɵɵlistener", "TuiNativeSelect_change_HostBindingHandler", "$event", "target", "options", "selectedIndex", "ɵɵhostProperty", "interactive", "ɵɵattribute", "invalid", "inputs", "ɵɵInputFlags", "None", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "consts", "template", "TuiNativeSelect_Template", "ɵɵtemplateRefExtractor", "flatItems_r6", "dependencies", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "hostDirectives", "host", "TuiSelectOption", "handlers", "option", "optional", "icon", "check", "selected", "controlValue", "optionValue", "scrolled", "TuiSelectOption_Factory", "hostAttrs", "TuiSelectOption_HostBindings", "ɵɵclassProp", "TuiSelectOption_Template", "styles", "tuiSelectOption", "TuiSelectDirective", "open", "dropdownEnabled", "string", "ɵTuiSelectDirective_BaseFactory", "TuiSelectDirective_Factory", "ɵdir", "ɵɵdefineDirective", "TuiSelectDirective_HostBindings", "TuiSelectDirective_input_HostBindingHandler", "inputType", "includes", "disabled", "TuiSelect"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-select.mjs"], "sourcesContent": ["import { Ng<PERSON>orO<PERSON>, NgIf, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, signal, computed, effect, Component, ChangeDetectionStrategy, Input, Directive } from '@angular/core';\nimport { TuiControl, tuiAsControl } from '@taiga-ui/cdk/classes';\nimport { TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { tuiIsPresent, tuiDirectiveBinding } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i1 from '@taiga-ui/core/components/textfield';\nimport { TuiTextfieldDirective, tuiAsTextfieldAccessor, TuiWithTextfield, TuiSelectLike } from '@taiga-ui/core/components/textfield';\nimport { TUI_ITEMS_HANDLERS } from '@taiga-ui/core/directives/items-handlers';\nimport { tuiIsFlat, tuiInjectValue } from '@taiga-ui/kit/utils';\nimport { TuiOptionWithValue, tuiAsOptionContent } from '@taiga-ui/core/components/data-list';\nimport { tuiDropdownOpen, tuiDropdownEnabled } from '@taiga-ui/core/directives/dropdown';\nimport * as i1$1 from '@taiga-ui/core/components/scrollbar';\nimport { TuiScrollIntoView } from '@taiga-ui/core/components/scrollbar';\nimport { TuiIcons } from '@taiga-ui/core/directives/icons';\nimport { TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\n\nclass TuiNativeSelect extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.textfield = inject(TuiTextfieldDirective);\n        this.isFlat = tuiIsFlat;\n        this.placeholder = signal('');\n        this.itemsHandlers = inject(TUI_ITEMS_HANDLERS);\n        this.stringified = computed((value = this.value()) => tuiIsPresent(value) ? this.itemsHandlers.stringify()(value) : '');\n        this.showPlaceholder = computed(() => this.placeholder() && !this.stringified());\n        this.isSelected = computed((value = this.value()) => (x) => tuiIsPresent(value) && this.itemsHandlers.identityMatcher()(x, value));\n        this.valueEffect = effect(() => {\n            this.textfield.value.set(this.stringified());\n        }, TUI_ALLOW_SIGNAL_WRITES);\n        this.items = [];\n        this.labels = [];\n    }\n    // TODO(v5): use signal inputs\n    set placeholderSetter(x) {\n        this.placeholder.set(x);\n    }\n    setValue(value) {\n        this.onChange(value);\n    }\n    selectOption(index) {\n        const items = (this.items?.flat() ?? []);\n        this.onChange(items[index - (this.showPlaceholder() ? 1 : 0)] ?? null);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiNativeSelect, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiNativeSelect, isStandalone: true, selector: \"select[tuiSelect]\", inputs: { items: \"items\", labels: \"labels\", placeholderSetter: [\"placeholder\", \"placeholderSetter\"] }, host: { listeners: { \"change\": \"selectOption($event.target.options.selectedIndex)\" }, properties: { \"attr.aria-invalid\": \"invalid()\", \"disabled\": \"!interactive()\" } }, providers: [tuiAsTextfieldAccessor(TuiNativeSelect), tuiAsControl(TuiNativeSelect)], usesInheritance: true, hostDirectives: [{ directive: i1.TuiWithTextfield }], ngImport: i0, template: \"<option\\n    *ngIf=\\\"showPlaceholder()\\\"\\n    disabled\\n    selected\\n    value=\\\"\\\"\\n>\\n    {{ placeholder() }}\\n</option>\\n\\n<ng-container *ngIf=\\\"items && !isFlat(items); else flatItems\\\">\\n    <optgroup\\n        *ngFor=\\\"let group of labels; let index = index\\\"\\n        [label]=\\\"labels[index]\\\"\\n    >\\n        <ng-container *ngTemplateOutlet=\\\"options; context: {$implicit: items[index]}\\\" />\\n    </optgroup>\\n</ng-container>\\n\\n<ng-template #flatItems>\\n    <ng-container *ngTemplateOutlet=\\\"options; context: {$implicit: items}\\\" />\\n</ng-template>\\n\\n<ng-template\\n    #options\\n    let-items\\n>\\n    <option\\n        *ngFor=\\\"let option of items\\\"\\n        [disabled]=\\\"itemsHandlers.disabledItemHandler()(option)\\\"\\n        [selected]=\\\"isSelected()(option)\\\"\\n        [value]=\\\"itemsHandlers.stringify()(option)\\\"\\n    >\\n        {{ itemsHandlers.stringify()(option) }}\\n    </option>\\n</ng-template>\\n\", dependencies: [{ kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiNativeSelect, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'select[tuiSelect]', imports: [NgForOf, NgIf, NgTemplateOutlet], changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiAsTextfieldAccessor(TuiNativeSelect), tuiAsControl(TuiNativeSelect)], hostDirectives: [TuiWithTextfield], host: {\n                        '[attr.aria-invalid]': 'invalid()',\n                        '[disabled]': '!interactive()',\n                        '(change)': 'selectOption($event.target.options.selectedIndex)',\n                    }, template: \"<option\\n    *ngIf=\\\"showPlaceholder()\\\"\\n    disabled\\n    selected\\n    value=\\\"\\\"\\n>\\n    {{ placeholder() }}\\n</option>\\n\\n<ng-container *ngIf=\\\"items && !isFlat(items); else flatItems\\\">\\n    <optgroup\\n        *ngFor=\\\"let group of labels; let index = index\\\"\\n        [label]=\\\"labels[index]\\\"\\n    >\\n        <ng-container *ngTemplateOutlet=\\\"options; context: {$implicit: items[index]}\\\" />\\n    </optgroup>\\n</ng-container>\\n\\n<ng-template #flatItems>\\n    <ng-container *ngTemplateOutlet=\\\"options; context: {$implicit: items}\\\" />\\n</ng-template>\\n\\n<ng-template\\n    #options\\n    let-items\\n>\\n    <option\\n        *ngFor=\\\"let option of items\\\"\\n        [disabled]=\\\"itemsHandlers.disabledItemHandler()(option)\\\"\\n        [selected]=\\\"isSelected()(option)\\\"\\n        [value]=\\\"itemsHandlers.stringify()(option)\\\"\\n    >\\n        {{ itemsHandlers.stringify()(option) }}\\n    </option>\\n</ng-template>\\n\" }]\n        }], propDecorators: { items: [{\n                type: Input\n            }], labels: [{\n                type: Input\n            }], placeholderSetter: [{\n                type: Input,\n                args: ['placeholder']\n            }] } });\n\nclass TuiSelectOption {\n    constructor() {\n        this.handlers = inject(TUI_ITEMS_HANDLERS);\n        this.value = tuiInjectValue();\n        this.option = inject(TuiOptionWithValue, {\n            optional: true,\n        });\n        this.icon = this.option &&\n            tuiDirectiveBinding(TuiIcons, 'iconEnd', inject(TUI_COMMON_ICONS).check, {});\n        this.selected = computed((controlValue = this.value(), optionValue = this.option?.value()) => tuiIsPresent(optionValue) &&\n            tuiIsPresent(controlValue) &&\n            this.handlers.identityMatcher()(controlValue, optionValue));\n        this.scrolled = tuiDirectiveBinding(TuiScrollIntoView, 'tuiScrollIntoView', this.selected);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSelectOption, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSelectOption, isStandalone: true, selector: \"ng-component\", host: { attributes: { \"tuiSelectOption\": \"\" }, properties: { \"class._value\": \"option\", \"class._selected\": \"selected()\" } }, hostDirectives: [{ directive: i1$1.TuiScrollIntoView }], ngImport: i0, template: '', isInline: true, styles: [\":host._value:after{color:var(--tui-text-action);opacity:0}:host-context(tui-data-list[data-size=\\\"s\\\"]) :host._value:after{margin-inline-end:-.625rem}:host-context(tui-data-list[data-size=\\\"m\\\"]) :host._value:after,:host-context(tui-data-list[data-size=\\\"l\\\"]) :host._value:after{margin-inline-end:-.5rem}:host-context(tui-data-list[data-size=\\\"l\\\"]) :host._value:after{font-size:1.5rem}:host._value._selected:after{opacity:1}:host._value._selected~::ng-deep [tuiSelectOption]:after{opacity:0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSelectOption, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', changeDetection: ChangeDetectionStrategy.OnPush, hostDirectives: [TuiScrollIntoView], host: {\n                        tuiSelectOption: '',\n                        '[class._value]': 'option',\n                        '[class._selected]': 'selected()',\n                    }, styles: [\":host._value:after{color:var(--tui-text-action);opacity:0}:host-context(tui-data-list[data-size=\\\"s\\\"]) :host._value:after{margin-inline-end:-.625rem}:host-context(tui-data-list[data-size=\\\"m\\\"]) :host._value:after,:host-context(tui-data-list[data-size=\\\"l\\\"]) :host._value:after{margin-inline-end:-.5rem}:host-context(tui-data-list[data-size=\\\"l\\\"]) :host._value:after{font-size:1.5rem}:host._value._selected:after{opacity:1}:host._value._selected~::ng-deep [tuiSelectOption]:after{opacity:0}\\n\"] }]\n        }] });\n\nclass TuiSelectDirective extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.textfield = inject(TuiTextfieldDirective);\n        this.open = tuiDropdownOpen();\n        this.itemsHandlers = inject(TUI_ITEMS_HANDLERS);\n        this.dropdownEnabled = tuiDropdownEnabled(this.interactive);\n        this.valueEffect = effect(() => {\n            const value = this.value();\n            const string = tuiIsPresent(value) ? this.itemsHandlers.stringify()(value) : '';\n            this.textfield.value.set(string);\n        }, TUI_ALLOW_SIGNAL_WRITES);\n    }\n    setValue(value) {\n        this.onChange(value);\n        if (!value) {\n            this.open.set(true);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSelectDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiSelectDirective, isStandalone: true, selector: \"input[tuiSelect]\", host: { listeners: { \"input\": \"$event.inputType?.includes(\\\"delete\\\") && setValue(null)\" }, properties: { \"disabled\": \"disabled()\" } }, providers: [\n            tuiAsOptionContent(TuiSelectOption),\n            tuiAsTextfieldAccessor(TuiSelectDirective),\n            tuiAsControl(TuiSelectDirective),\n        ], usesInheritance: true, hostDirectives: [{ directive: i1.TuiWithTextfield }, { directive: i1.TuiSelectLike }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiSelectDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiSelect]',\n                    providers: [\n                        tuiAsOptionContent(TuiSelectOption),\n                        tuiAsTextfieldAccessor(TuiSelectDirective),\n                        tuiAsControl(TuiSelectDirective),\n                    ],\n                    hostDirectives: [TuiWithTextfield, TuiSelectLike],\n                    host: {\n                        '[disabled]': 'disabled()',\n                        '(input)': '$event.inputType?.includes(\"delete\") && setValue(null)',\n                    },\n                }]\n        }] });\n\nconst TuiSelect = [TuiSelectDirective, TuiNativeSelect];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiNativeSelect, TuiSelect, TuiSelectDirective, TuiSelectOption };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,IAAI,EAAEC,gBAAgB,QAAQ,iBAAiB;AACjE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,SAAS,QAAQ,eAAe;AACtH,SAASC,UAAU,EAAEC,YAAY,QAAQ,uBAAuB;AAChE,SAASC,uBAAuB,QAAQ,yBAAyB;AACjE,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,mCAAmC;AACrF,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,qBAAqB,EAAEC,sBAAsB,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,qCAAqC;AACpI,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,SAAS,EAAEC,cAAc,QAAQ,qBAAqB;AAC/D,SAASC,kBAAkB,EAAEC,kBAAkB,QAAQ,qCAAqC;AAC5F,SAASC,eAAe,EAAEC,kBAAkB,QAAQ,oCAAoC;AACxF,OAAO,KAAKC,IAAI,MAAM,qCAAqC;AAC3D,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,QAAQ,QAAQ,iCAAiC;AAC1D,SAASC,gBAAgB,QAAQ,uBAAuB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,SAAAE,kCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA6B4CnC,EAAE,CAAAqC,cAAA,eACgmB,CAAC;IADnmBrC,EAAE,CAAAsC,MAAA,EAC2nB,CAAC;IAD9nBtC,EAAE,CAAAuC,YAAA,CACooB,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GADvoBxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAA0C,SAAA,CAC2nB,CAAC;IAD9nB1C,EAAE,CAAA2C,kBAAA,MAAAH,MAAA,CAAAI,WAAA,QAC2nB,CAAC;EAAA;AAAA;AAAA,SAAAC,kEAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD9nBnC,EAAE,CAAA8C,kBAAA,EACw5B,CAAC;EAAA;AAAA;AAAA,SAAAC,mDAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD35BnC,EAAE,CAAAqC,cAAA,iBAC4zB,CAAC;IAD/zBrC,EAAE,CAAAgD,UAAA,IAAAH,iEAAA,yBACw5B,CAAC;IAD35B7C,EAAE,CAAAuC,YAAA,CACy6B,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAc,QAAA,GAAAb,GAAA,CAAAc,KAAA;IAAA,MAAAV,MAAA,GAD56BxC,EAAE,CAAAyC,aAAA;IAAA,MAAAU,UAAA,GAAFnD,EAAE,CAAAoD,WAAA;IAAFpD,EAAE,CAAAqD,UAAA,UAAAb,MAAA,CAAAc,MAAA,CAAAL,QAAA,CACqzB,CAAC;IADxzBjD,EAAE,CAAA0C,SAAA,CACi3B,CAAC;IADp3B1C,EAAE,CAAAqD,UAAA,qBAAAF,UACi3B,CAAC,4BADp3BnD,EAAE,CAAAuD,eAAA,IAAAxB,GAAA,EAAAS,MAAA,CAAAgB,KAAA,CAAAP,QAAA,EACm5B,CAAC;EAAA;AAAA;AAAA,SAAAQ,wCAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADt5BnC,EAAE,CAAA0D,uBAAA,EACwsB,CAAC;IAD3sB1D,EAAE,CAAAgD,UAAA,IAAAD,kDAAA,qBAC4zB,CAAC;IAD/zB/C,EAAE,CAAA2D,qBAAA;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAK,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAA0C,SAAA,CAC+vB,CAAC;IADlwB1C,EAAE,CAAAqD,UAAA,YAAAb,MAAA,CAAAc,MAC+vB,CAAC;EAAA;AAAA;AAAA,SAAAM,sDAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADlwBnC,EAAE,CAAA8C,kBAAA,EACuiC,CAAC;EAAA;AAAA;AAAA,SAAAe,uCAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD1iCnC,EAAE,CAAAgD,UAAA,IAAAY,qDAAA,yBACuiC,CAAC;EAAA;EAAA,IAAAzB,EAAA;IAAA,MAAAK,MAAA,GAD1iCxC,EAAE,CAAAyC,aAAA;IAAA,MAAAU,UAAA,GAAFnD,EAAE,CAAAoD,WAAA;IAAFpD,EAAE,CAAAqD,UAAA,qBAAAF,UACugC,CAAC,4BAD1gCnD,EAAE,CAAAuD,eAAA,IAAAxB,GAAA,EAAAS,MAAA,CAAAgB,KAAA,CACkiC,CAAC;EAAA;AAAA;AAAA,SAAAM,gDAAA3B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADriCnC,EAAE,CAAAqC,cAAA,eAC20C,CAAC;IAD90CrC,EAAE,CAAAsC,MAAA,EACk4C,CAAC;IADr4CtC,EAAE,CAAAuC,YAAA,CAC24C,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAA4B,SAAA,GAAA3B,GAAA,CAAAH,SAAA;IAAA,MAAAO,MAAA,GAD94CxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAAqD,UAAA,aAAAb,MAAA,CAAAwB,aAAA,CAAAC,mBAAA,GAAAF,SAAA,CACguC,CAAC,aAAAvB,MAAA,CAAA0B,UAAA,GAAAH,SAAA,CAA4C,CAAC,UAAAvB,MAAA,CAAAwB,aAAA,CAAAG,SAAA,GAAAJ,SAAA,CAAsD,CAAC;IADv0C/D,EAAE,CAAA0C,SAAA,CACk4C,CAAC;IADr4C1C,EAAE,CAAA2C,kBAAA,MAAAH,MAAA,CAAAwB,aAAA,CAAAG,SAAA,GAAAJ,SAAA,MACk4C,CAAC;EAAA;AAAA;AAAA,SAAAK,uCAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADr4CnC,EAAE,CAAAgD,UAAA,IAAAc,+CAAA,mBAC20C,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAkC,QAAA,GAAAjC,GAAA,CAAAH,SAAA;IAD90CjC,EAAE,CAAAqD,UAAA,YAAAgB,QAC0pC,CAAC;EAAA;AAAA;AA5BlwC,MAAMC,eAAe,SAAS7D,UAAU,CAAC;EACrC8D,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,SAAS,GAAGxE,MAAM,CAACc,qBAAqB,CAAC;IAC9C,IAAI,CAAC2D,MAAM,GAAGtD,SAAS;IACvB,IAAI,CAACwB,WAAW,GAAG1C,MAAM,CAAC,EAAE,CAAC;IAC7B,IAAI,CAAC8D,aAAa,GAAG/D,MAAM,CAACkB,kBAAkB,CAAC;IAC/C,IAAI,CAACwD,WAAW,GAAGxE,QAAQ,CAAC,CAACyE,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,KAAKhE,YAAY,CAACgE,KAAK,CAAC,GAAG,IAAI,CAACZ,aAAa,CAACG,SAAS,CAAC,CAAC,CAACS,KAAK,CAAC,GAAG,EAAE,CAAC;IACvH,IAAI,CAACC,eAAe,GAAG1E,QAAQ,CAAC,MAAM,IAAI,CAACyC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC+B,WAAW,CAAC,CAAC,CAAC;IAChF,IAAI,CAACT,UAAU,GAAG/D,QAAQ,CAAC,CAACyE,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC,KAAME,CAAC,IAAKlE,YAAY,CAACgE,KAAK,CAAC,IAAI,IAAI,CAACZ,aAAa,CAACe,eAAe,CAAC,CAAC,CAACD,CAAC,EAAEF,KAAK,CAAC,CAAC;IAClI,IAAI,CAACI,WAAW,GAAG5E,MAAM,CAAC,MAAM;MAC5B,IAAI,CAACqE,SAAS,CAACG,KAAK,CAACK,GAAG,CAAC,IAAI,CAACN,WAAW,CAAC,CAAC,CAAC;IAChD,CAAC,EAAEhE,uBAAuB,CAAC;IAC3B,IAAI,CAAC6C,KAAK,GAAG,EAAE;IACf,IAAI,CAACF,MAAM,GAAG,EAAE;EACpB;EACA;EACA,IAAI4B,iBAAiBA,CAACJ,CAAC,EAAE;IACrB,IAAI,CAAClC,WAAW,CAACqC,GAAG,CAACH,CAAC,CAAC;EAC3B;EACAK,QAAQA,CAACP,KAAK,EAAE;IACZ,IAAI,CAACQ,QAAQ,CAACR,KAAK,CAAC;EACxB;EACAS,YAAYA,CAACnC,KAAK,EAAE;IAChB,MAAMM,KAAK,GAAI,IAAI,CAACA,KAAK,EAAE8B,IAAI,CAAC,CAAC,IAAI,EAAG;IACxC,IAAI,CAACF,QAAQ,CAAC5B,KAAK,CAACN,KAAK,IAAI,IAAI,CAAC2B,eAAe,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;EAC1E;EACA;IAAS,IAAI,CAACU,IAAI;MAAA,IAAAC,4BAAA;MAAA,gBAAAC,wBAAAC,CAAA;QAAA,QAAAF,4BAAA,KAAAA,4BAAA,GAA+ExF,EAAE,CAAA2F,qBAAA,CAAQrB,eAAe,IAAAoB,CAAA,IAAfpB,eAAe;MAAA;IAAA,IAAqD;EAAE;EACjL;IAAS,IAAI,CAACsB,IAAI,kBAD+E5F,EAAE,CAAA6F,iBAAA;MAAAC,IAAA,EACJxB,eAAe;MAAAyB,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,6BAAA/D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADbnC,EAAE,CAAAmG,UAAA,oBAAAC,0CAAAC,MAAA;YAAA,OACJjE,GAAA,CAAAiD,YAAA,CAAAgB,MAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAC,aAAgD,CAAC;UAAA,CAAnC,CAAC;QAAA;QAAA,IAAArE,EAAA;UADbnC,EAAE,CAAAyG,cAAA,cACHrE,GAAA,CAAAsE,WAAA,CAAY,CAAC,CAAC;UADb1G,EAAE,CAAA2G,WAAA,iBACJvE,GAAA,CAAAwE,OAAA,CAAQ,CAAC;QAAA;MAAA;MAAAC,MAAA;QAAArD,KAAA;QAAAF,MAAA;QAAA4B,iBAAA,GADPlF,EAAE,CAAA8G,YAAA,CAAAC,IAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFjH,EAAE,CAAAkH,kBAAA,CAC0V,CAAClG,sBAAsB,CAACsD,eAAe,CAAC,EAAE5D,YAAY,CAAC4D,eAAe,CAAC,CAAC,GADpatE,EAAE,CAAAmH,uBAAA,EACydrG,EAAE,CAACG,gBAAgB,IAD9ejB,EAAE,CAAAoH,0BAAA,EAAFpH,EAAE,CAAAqH,mBAAA;MAAAC,KAAA,EAAAxF,GAAA;MAAAyF,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAxF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnC,EAAE,CAAAgD,UAAA,IAAAd,iCAAA,mBACgmB,CAAC,IAAAuB,uCAAA,yBAAuG,CAAC,IAAAI,sCAAA,gCAD3sB7D,EAAE,CAAA4H,sBACs9B,CAAC,IAAAxD,sCAAA,gCADz9BpE,EAAE,CAAA4H,sBACumC,CAAC;QAAA;QAAA,IAAAzF,EAAA;UAAA,MAAA0F,YAAA,GAD1mC7H,EAAE,CAAAoD,WAAA;UAAFpD,EAAE,CAAAqD,UAAA,SAAAjB,GAAA,CAAAyC,eAAA,EAC+iB,CAAC;UADljB7E,EAAE,CAAA0C,SAAA,CACurB,CAAC;UAD1rB1C,EAAE,CAAAqD,UAAA,SAAAjB,GAAA,CAAAoB,KAAA,KAAApB,GAAA,CAAAsC,MAAA,CAAAtC,GAAA,CAAAoB,KAAA,CACurB,CAAC,aAAAqE,YAAa,CAAC;QAAA;MAAA;MAAAC,YAAA,GAAqwBjI,OAAO,EAAmHC,IAAI,EAA6FC,gBAAgB;MAAAgI,aAAA;MAAAC,eAAA;IAAA,EAA+K;EAAE;AAC98D;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGjI,EAAE,CAAAkI,iBAAA,CAGX5D,eAAe,EAAc,CAAC;IAC9GwB,IAAI,EAAEzF,SAAS;IACf8H,IAAI,EAAE,CAAC;MAAEnB,UAAU,EAAE,IAAI;MAAEoB,QAAQ,EAAE,mBAAmB;MAAEC,OAAO,EAAE,CAACxI,OAAO,EAAEC,IAAI,EAAEC,gBAAgB,CAAC;MAAEiI,eAAe,EAAE1H,uBAAuB,CAACgI,MAAM;MAAEC,SAAS,EAAE,CAACvH,sBAAsB,CAACsD,eAAe,CAAC,EAAE5D,YAAY,CAAC4D,eAAe,CAAC,CAAC;MAAEkE,cAAc,EAAE,CAACvH,gBAAgB,CAAC;MAAEwH,IAAI,EAAE;QAC1Q,qBAAqB,EAAE,WAAW;QAClC,YAAY,EAAE,gBAAgB;QAC9B,UAAU,EAAE;MAChB,CAAC;MAAEf,QAAQ,EAAE;IAAu5B,CAAC;EACj7B,CAAC,CAAC,QAAkB;IAAElE,KAAK,EAAE,CAAC;MACtBsC,IAAI,EAAEvF;IACV,CAAC,CAAC;IAAE+C,MAAM,EAAE,CAAC;MACTwC,IAAI,EAAEvF;IACV,CAAC,CAAC;IAAE2E,iBAAiB,EAAE,CAAC;MACpBY,IAAI,EAAEvF,KAAK;MACX4H,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMO,eAAe,CAAC;EAClBnE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoE,QAAQ,GAAG1I,MAAM,CAACkB,kBAAkB,CAAC;IAC1C,IAAI,CAACyD,KAAK,GAAGvD,cAAc,CAAC,CAAC;IAC7B,IAAI,CAACuH,MAAM,GAAG3I,MAAM,CAACqB,kBAAkB,EAAE;MACrCuH,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,CAACC,IAAI,GAAG,IAAI,CAACF,MAAM,IACnB/H,mBAAmB,CAACe,QAAQ,EAAE,SAAS,EAAE3B,MAAM,CAAC4B,gBAAgB,CAAC,CAACkH,KAAK,EAAE,CAAC,CAAC,CAAC;IAChF,IAAI,CAACC,QAAQ,GAAG7I,QAAQ,CAAC,CAAC8I,YAAY,GAAG,IAAI,CAACrE,KAAK,CAAC,CAAC,EAAEsE,WAAW,GAAG,IAAI,CAACN,MAAM,EAAEhE,KAAK,CAAC,CAAC,KAAKhE,YAAY,CAACsI,WAAW,CAAC,IACnHtI,YAAY,CAACqI,YAAY,CAAC,IAC1B,IAAI,CAACN,QAAQ,CAAC5D,eAAe,CAAC,CAAC,CAACkE,YAAY,EAAEC,WAAW,CAAC,CAAC;IAC/D,IAAI,CAACC,QAAQ,GAAGtI,mBAAmB,CAACc,iBAAiB,EAAE,mBAAmB,EAAE,IAAI,CAACqH,QAAQ,CAAC;EAC9F;EACA;IAAS,IAAI,CAACzD,IAAI,YAAA6D,wBAAA1D,CAAA;MAAA,YAAAA,CAAA,IAAyFgD,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAAC9C,IAAI,kBAlC+E5F,EAAE,CAAA6F,iBAAA;MAAAC,IAAA,EAkCJ4C,eAAe;MAAA3C,SAAA;MAAAsD,SAAA,sBAAyF,EAAE;MAAArD,QAAA;MAAAC,YAAA,WAAAqD,6BAAAnH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlCxGnC,EAAE,CAAAuJ,WAAA,WAAAnH,GAAA,CAAAwG,MAkCU,CAAC,cAAfxG,GAAA,CAAA4G,QAAA,CAAS,CAAK,CAAC;QAAA;MAAA;MAAAhC,UAAA;MAAAC,QAAA,GAlCbjH,EAAE,CAAAmH,uBAAA,EAkCqNzF,IAAI,CAACC,iBAAiB,IAlC7O3B,EAAE,CAAAqH,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAA8B,yBAAArH,EAAA,EAAAC,GAAA;MAAAqH,MAAA;MAAAzB,eAAA;IAAA,EAkC80B;EAAE;AACv7B;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApCqGjI,EAAE,CAAAkI,iBAAA,CAoCXQ,eAAe,EAAc,CAAC;IAC9G5C,IAAI,EAAEzF,SAAS;IACf8H,IAAI,EAAE,CAAC;MAAEnB,UAAU,EAAE,IAAI;MAAEU,QAAQ,EAAE,EAAE;MAAEM,eAAe,EAAE1H,uBAAuB,CAACgI,MAAM;MAAEE,cAAc,EAAE,CAAC7G,iBAAiB,CAAC;MAAE8G,IAAI,EAAE;QACzHiB,eAAe,EAAE,EAAE;QACnB,gBAAgB,EAAE,QAAQ;QAC1B,mBAAmB,EAAE;MACzB,CAAC;MAAED,MAAM,EAAE,CAAC,ifAAif;IAAE,CAAC;EAC5gB,CAAC,CAAC;AAAA;AAEV,MAAME,kBAAkB,SAASlJ,UAAU,CAAC;EACxC8D,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,SAAS,GAAGxE,MAAM,CAACc,qBAAqB,CAAC;IAC9C,IAAI,CAAC6I,IAAI,GAAGpI,eAAe,CAAC,CAAC;IAC7B,IAAI,CAACwC,aAAa,GAAG/D,MAAM,CAACkB,kBAAkB,CAAC;IAC/C,IAAI,CAAC0I,eAAe,GAAGpI,kBAAkB,CAAC,IAAI,CAACiF,WAAW,CAAC;IAC3D,IAAI,CAAC1B,WAAW,GAAG5E,MAAM,CAAC,MAAM;MAC5B,MAAMwE,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;MAC1B,MAAMkF,MAAM,GAAGlJ,YAAY,CAACgE,KAAK,CAAC,GAAG,IAAI,CAACZ,aAAa,CAACG,SAAS,CAAC,CAAC,CAACS,KAAK,CAAC,GAAG,EAAE;MAC/E,IAAI,CAACH,SAAS,CAACG,KAAK,CAACK,GAAG,CAAC6E,MAAM,CAAC;IACpC,CAAC,EAAEnJ,uBAAuB,CAAC;EAC/B;EACAwE,QAAQA,CAACP,KAAK,EAAE;IACZ,IAAI,CAACQ,QAAQ,CAACR,KAAK,CAAC;IACpB,IAAI,CAACA,KAAK,EAAE;MACR,IAAI,CAACgF,IAAI,CAAC3E,GAAG,CAAC,IAAI,CAAC;IACvB;EACJ;EACA;IAAS,IAAI,CAACM,IAAI;MAAA,IAAAwE,+BAAA;MAAA,gBAAAC,2BAAAtE,CAAA;QAAA,QAAAqE,+BAAA,KAAAA,+BAAA,GAhE+E/J,EAAE,CAAA2F,qBAAA,CAgEQgE,kBAAkB,IAAAjE,CAAA,IAAlBiE,kBAAkB;MAAA;IAAA,IAAqD;EAAE;EACpL;IAAS,IAAI,CAACM,IAAI,kBAjE+EjK,EAAE,CAAAkK,iBAAA;MAAApE,IAAA,EAiEJ6D,kBAAkB;MAAA5D,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAkE,gCAAAhI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjEhBnC,EAAE,CAAAmG,UAAA,mBAAAiE,4CAAA/D,MAAA;YAAA,QAAAA,MAAA,CAAAgE,SAAA,kBAAAhE,MAAA,CAAAgE,SAAA,CAAAC,QAAA,CAiEuB,QAAQ,MAAKlI,GAAA,CAAA+C,QAAA,CAAS,IAAI,CAAC;UAAA,CAArC,CAAC;QAAA;QAAA,IAAAhD,EAAA;UAjEhBnC,EAAE,CAAAyG,cAAA,aAiEJrE,GAAA,CAAAmI,QAAA,CAAS,CAAQ,CAAC;QAAA;MAAA;MAAAvD,UAAA;MAAAC,QAAA,GAjEhBjH,EAAE,CAAAkH,kBAAA,CAiEqN,CAChT3F,kBAAkB,CAACmH,eAAe,CAAC,EACnC1H,sBAAsB,CAAC2I,kBAAkB,CAAC,EAC1CjJ,YAAY,CAACiJ,kBAAkB,CAAC,CACnC,GArE4F3J,EAAE,CAAAmH,uBAAA,EAqEvCrG,EAAE,CAACG,gBAAgB,EAAiBH,EAAE,CAACI,aAAa,IArEflB,EAAE,CAAAoH,0BAAA;IAAA,EAqEiC;EAAE;AAC1I;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAvEqGjI,EAAE,CAAAkI,iBAAA,CAuEXyB,kBAAkB,EAAc,CAAC;IACjH7D,IAAI,EAAEtF,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCnB,UAAU,EAAE,IAAI;MAChBoB,QAAQ,EAAE,kBAAkB;MAC5BG,SAAS,EAAE,CACPhH,kBAAkB,CAACmH,eAAe,CAAC,EACnC1H,sBAAsB,CAAC2I,kBAAkB,CAAC,EAC1CjJ,YAAY,CAACiJ,kBAAkB,CAAC,CACnC;MACDnB,cAAc,EAAE,CAACvH,gBAAgB,EAAEC,aAAa,CAAC;MACjDuH,IAAI,EAAE;QACF,YAAY,EAAE,YAAY;QAC1B,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM+B,SAAS,GAAG,CAACb,kBAAkB,EAAErF,eAAe,CAAC;;AAEvD;AACA;AACA;;AAEA,SAASA,eAAe,EAAEkG,SAAS,EAAEb,kBAAkB,EAAEjB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}