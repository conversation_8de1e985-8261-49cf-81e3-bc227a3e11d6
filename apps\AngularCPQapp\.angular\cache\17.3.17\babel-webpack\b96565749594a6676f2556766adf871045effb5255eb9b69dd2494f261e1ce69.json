{"ast": null, "code": "import { __decorate } from 'tslib';\nimport * as i2 from '@angular/common';\nimport { CommonModule, AsyncPipe } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, LOCALE_ID, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, TemplateRef, ViewEncapsulation, ContentChildren, Directive, forwardRef, ContentChild, Pipe } from '@angular/core';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { WA_WINDOW } from '@ng-web-apis/common';\nimport { tuiPure, tuiCreateToken, tuiProvideOptions, tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TuiLoader } from '@taiga-ui/core/components/loader';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearanceOptionsProvider, TuiAppearance, Tui<PERSON>ithAppearance } from '@taiga-ui/core/directives/appearance';\nimport { TuiHintOverflow } from '@taiga-ui/core/directives/hint';\nimport { TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { TUI_DIGITAL_INFORMATION_UNITS, TUI_FILE_TEXTS, TUI_HIDE_TEXT, TUI_SHOW_ALL_TEXT, TUI_INPUT_FILE_TEXTS } from '@taiga-ui/kit/tokens';\nimport { PolymorpheusOutlet, PolymorpheusTemplate, injectContext, PolymorpheusComponent } from '@taiga-ui/polymorpheus';\nimport { map, of, combineLatest, timer, switchMap, filter } from 'rxjs';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { tuiRound } from '@taiga-ui/cdk/utils/math';\nimport { TuiItem } from '@taiga-ui/cdk/directives/item';\nimport { EMPTY_QUERY, EMPTY_ARRAY, CHAR_NO_BREAK_SPACE } from '@taiga-ui/cdk/constants';\nimport { TuiExpandComponent } from '@taiga-ui/core/components/expand';\nimport * as i1$1 from '@taiga-ui/core/directives/group';\nimport { tuiGroupOptionsProvider, TuiGroup } from '@taiga-ui/core/directives/group';\nimport { TuiLink } from '@taiga-ui/core/components/link';\nimport { TuiBreakpointService } from '@taiga-ui/core/services';\nimport { TuiControl, tuiAsControl } from '@taiga-ui/cdk/classes';\nimport * as i1$2 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport { tuiZonefreeScheduler, tuiControlValue } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { Validators, NG_VALIDATORS, FormControl } from '@angular/forms';\nimport { TuiValidator } from '@taiga-ui/cdk/directives/validator';\nconst _c0 = [\"*\"];\nconst _c1 = a0 => ({\n  $implicit: a0\n});\nfunction TuiFile_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r1, \" \");\n  }\n}\nfunction TuiFile_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fileSize_r2 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", fileSize_r2, \" \");\n  }\n}\nfunction TuiFile_div_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r3 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r3, \" \");\n  }\n}\nfunction TuiFile_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, TuiFile_div_10_ng_container_1_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const content_r4 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"polymorpheusOutlet\", content_r4);\n  }\n}\nfunction TuiFile_ng_container_13_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click.prevent\", function TuiFile_ng_container_13_button_1_Template_button_click_prevent_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.remove.emit());\n    })(\"mousedown.prevent.zoneless\", function TuiFile_ng_container_13_button_1_Template_button_mousedown_prevent_zoneless_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      return i0.ɵɵresetView(0);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const texts_r7 = ctx.ngIf;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"iconStart\", ctx_r5.icons.close);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", texts_r7.remove, \" \");\n  }\n}\nfunction TuiFile_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiFile_ng_container_13_button_1_Template, 2, 2, \"button\", 14);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(2, 1, ctx_r5.fileTexts$));\n  }\n}\nfunction TuiFile_ng_template_14_img_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.preview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction TuiFile_ng_template_14_ng_template_1_tui_loader_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-loader\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"inheritColor\", ctx_r5.isBig);\n  }\n}\nfunction TuiFile_ng_template_14_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiFile_ng_template_14_ng_template_1_tui_loader_0_Template, 1, 1, \"tui-loader\", 18);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const svg_r8 = i0.ɵɵreference(4);\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isLoading)(\"ngIfElse\", svg_r8);\n  }\n}\nfunction TuiFile_ng_template_14_ng_template_3_tui_icon_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-icon\", 21);\n  }\n  if (rf & 2) {\n    const src_r9 = ctx.polymorpheusOutlet;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"t-icon_blank\", ctx_r5.isBig || ctx_r5.isDeleted)(\"t-icon_error\", ctx_r5.isError);\n    i0.ɵɵproperty(\"icon\", src_r9.toString());\n  }\n}\nfunction TuiFile_ng_template_14_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiFile_ng_template_14_ng_template_3_tui_icon_0_Template, 1, 5, \"tui-icon\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"polymorpheusOutlet\", ctx_r5.icon)(\"polymorpheusOutletContext\", i0.ɵɵpureFunction1(2, _c1, ctx_r5.size));\n  }\n}\nfunction TuiFile_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiFile_ng_template_14_img_0_Template, 1, 1, \"img\", 16)(1, TuiFile_ng_template_14_ng_template_1_Template, 1, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(3, TuiFile_ng_template_14_ng_template_3_Template, 1, 4, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const loader_r10 = i0.ɵɵreference(2);\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.preview)(\"ngIfElse\", loader_r10);\n  }\n}\nfunction TuiFilesComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiFilesComponent_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 5);\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngTemplateOutlet\", item_r1);\n  }\n}\nfunction TuiFilesComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiFilesComponent_ng_container_3_ng_container_1_Template, 1, 1, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const index_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.max || index_r2 < ctx_r2.max);\n  }\n}\nfunction TuiFilesComponent_tui_expand_4_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 5);\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngTemplateOutlet\", item_r4);\n  }\n}\nfunction TuiFilesComponent_tui_expand_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TuiFilesComponent_tui_expand_4_ng_container_2_ng_container_1_Template, 1, 1, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const index_r5 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.max && index_r5 >= ctx_r2.max);\n  }\n}\nfunction TuiFilesComponent_tui_expand_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tui-expand\", 6)(1, \"div\", 7);\n    i0.ɵɵtemplate(2, TuiFilesComponent_tui_expand_4_ng_container_2_Template, 2, 1, \"ng-container\", 1);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"expanded\", ctx_r2.expanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.items);\n  }\n}\nfunction TuiFilesComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function TuiFilesComponent_div_5_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggle());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"t-bottom_collapsed\", !ctx_r2.expanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, ctx_r2.expanded ? ctx_r2.hideText$ : ctx_r2.showAllText$), \" \");\n  }\n}\nconst _c2 = [\"tuiInputFiles\", \"\"];\nfunction TuiInputFiles_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r1, \" \");\n  }\n}\nconst TUI_SIZE_ERROR = 'tuiSize';\nconst TUI_FORMAT_ERROR = 'tuiFormat';\nfunction tuiCreateFileSizeValidator(size) {\n  return ({\n    value\n  }) => {\n    const files = value && coerceArray(value);\n    const $implicit = value && files?.filter(file => file.size > size);\n    return $implicit?.length ? {\n      [TUI_SIZE_ERROR]: {\n        $implicit,\n        size\n      }\n    } : null;\n  };\n}\nfunction tuiCreateFileFormatValidator(accept) {\n  return ({\n    value\n  }) => {\n    const files = value && coerceArray(value);\n    const formats = toArray$1(accept);\n    const $implicit = value && files?.filter(file => !checkFormat(file, formats));\n    return $implicit?.length && accept ? {\n      [TUI_FORMAT_ERROR]: {\n        $implicit\n      }\n    } : null;\n  };\n}\nfunction checkFormat({\n  name,\n  type\n}, formats) {\n  const extension = `.${(name.split('.').pop() || '').toLowerCase()}`;\n  return formats.some(format => format === extension || format === type || format.split('/')[1] === '*' && type?.split('/')[0] === format.split('/')[0]);\n}\nfunction toArray$1(accept) {\n  return accept.toLowerCase().split(',').map(format => format.trim().toLowerCase());\n}\nconst BYTES_PER_KIB = 1024;\nconst BYTES_PER_MIB = 1024 * BYTES_PER_KIB;\nfunction tuiFilesRejected(control) {\n  const format = control?.getError(TUI_FORMAT_ERROR)?.$implicit || [];\n  const size = control?.getError(TUI_SIZE_ERROR)?.$implicit || [];\n  return Array.from(new Set([...format, ...size]));\n}\nfunction tuiFilesAccepted(control) {\n  const value = control?.value || [];\n  const files = coerceArray(value);\n  const size = control?.getError(TUI_SIZE_ERROR)?.$implicit || [];\n  const format = control?.getError(TUI_FORMAT_ERROR)?.$implicit || [];\n  return files.filter(file => !size.includes(file) && !format.includes(file));\n}\nfunction tuiFormatSize(units, size, locale) {\n  if (size === undefined) {\n    return null;\n  }\n  if (size < BYTES_PER_KIB) {\n    return `${size} ${units[0]}`;\n  }\n  if (size < BYTES_PER_MIB) {\n    return `${(size / BYTES_PER_KIB).toFixed(0)} ${units[1]}`;\n  }\n  return `${tuiRound(size / BYTES_PER_MIB, 2).toLocaleString(locale)} ${units[2]}`;\n}\nconst TUI_FILE_DEFAULT_OPTIONS = {\n  appearance: 'outline',\n  formatSize: tuiFormatSize,\n  icons: {\n    normal: ({\n      $implicit\n    }) => $implicit === 'l' ? '@tui.file' : '@tui.circle-check',\n    error: '@tui.circle-alert',\n    deleted: '@tui.trash'\n  }\n};\n/**\n * Default parameters for file component\n */\nconst [TUI_FILE_OPTIONS, tuiFileOptionsProvider] = tuiCreateOptions(TUI_FILE_DEFAULT_OPTIONS);\nclass TuiFile {\n  constructor() {\n    this.sanitizer = inject(DomSanitizer);\n    this.options = inject(TUI_FILE_OPTIONS);\n    this.locale = inject(LOCALE_ID);\n    this.units$ = inject(TUI_DIGITAL_INFORMATION_UNITS);\n    this.win = inject(WA_WINDOW);\n    this.icons = inject(TUI_COMMON_ICONS);\n    this.fileTexts$ = inject(TUI_FILE_TEXTS);\n    this.file = {\n      name: ''\n    };\n    this.state = 'normal';\n    this.size = 'm';\n    this.showDelete = true;\n    this.showSize = true;\n    this.remove = new EventEmitter();\n  }\n  get preview() {\n    return this.isBig ? this.createPreview(this.file) : '';\n  }\n  get isBig() {\n    return this.size === 'l';\n  }\n  get isLoading() {\n    return this.state === 'loading';\n  }\n  get isError() {\n    return this.state === 'error';\n  }\n  get isDeleted() {\n    return this.state === 'deleted';\n  }\n  get allowDelete() {\n    return this.showDelete && this.remove.observed;\n  }\n  get icon() {\n    return this.state === 'loading' ? '' : this.options.icons[this.state];\n  }\n  get name() {\n    return this.getName(this.file);\n  }\n  get type() {\n    return this.getType(this.file);\n  }\n  get content$() {\n    return this.calculateContent$(this.state, this.file, this.fileTexts$);\n  }\n  get fileSize$() {\n    return this.calculateFileSize$(this.file, this.units$);\n  }\n  calculateContent$(state, file, fileTexts$) {\n    return state === 'error' && !file.content ? fileTexts$.pipe(map(texts => texts.loadingError)) : of(this.file.content || '');\n  }\n  calculateFileSize$(file, units$) {\n    return units$.pipe(map(units => this.options.formatSize(units, file.size, this.locale)));\n  }\n  createPreview(file) {\n    if (file.src) {\n      return file.src;\n    }\n    if (this.win.File && file instanceof this.win.File && file.type?.startsWith('image/')) {\n      return this.sanitizer.bypassSecurityTrustUrl(URL.createObjectURL(file));\n    }\n    return '';\n  }\n  getName(file) {\n    const dot = file.name.lastIndexOf('.');\n    // a dot at position 0 means a “hidden” file, not an extension\n    return dot > 0 ? file.name.slice(0, dot) : file.name;\n  }\n  getType(file) {\n    const dot = file.name.lastIndexOf('.');\n    // only return an extension when there is one\n    return dot > 0 ? file.name.slice(dot) : '';\n  }\n  static {\n    this.ɵfac = function TuiFile_Factory(t) {\n      return new (t || TuiFile)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiFile,\n      selectors: [[\"tui-file\"], [\"a\", \"tuiFile\", \"\"], [\"button\", \"tuiFile\", \"\"]],\n      hostVars: 1,\n      hostBindings: function TuiFile_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-delete\", ctx.showDelete);\n        }\n      },\n      inputs: {\n        file: \"file\",\n        state: \"state\",\n        size: \"size\",\n        showDelete: \"showDelete\",\n        showSize: \"showSize\",\n        leftContent: \"leftContent\"\n      },\n      outputs: {\n        remove: \"remove\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAppearanceOptionsProvider(TUI_FILE_OPTIONS)]), i0.ɵɵHostDirectivesFeature([i1.TuiAppearance]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 16,\n      vars: 12,\n      consts: [[\"defaultLeftContent\", \"\"], [\"loader\", \"\"], [\"svg\", \"\"], [1, \"t-preview\"], [4, \"polymorpheusOutlet\"], [1, \"t-wrapper\"], [1, \"t-text\"], [\"tuiHintOverflow\", \"\", 1, \"t-name\"], [1, \"t-type\"], [\"class\", \"t-size\", 4, \"ngIf\"], [\"class\", \"t-content\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"t-size\"], [1, \"t-content\"], [\"appearance\", \"icon\", \"size\", \"xs\", \"tuiIconButton\", \"\", \"type\", \"button\", \"class\", \"t-remove\", 3, \"iconStart\", \"click.prevent\", \"mousedown.prevent.zoneless\", 4, \"ngIf\"], [\"appearance\", \"icon\", \"size\", \"xs\", \"tuiIconButton\", \"\", \"type\", \"button\", 1, \"t-remove\", 3, \"click.prevent\", \"mousedown.prevent.zoneless\", \"iconStart\"], [\"alt\", \"file preview\", \"class\", \"t-image\", 3, \"src\", 4, \"ngIf\", \"ngIfElse\"], [\"alt\", \"file preview\", 1, \"t-image\", 3, \"src\"], [\"class\", \"t-loader\", 3, \"inheritColor\", 4, \"ngIf\", \"ngIfElse\"], [1, \"t-loader\", 3, \"inheritColor\"], [\"class\", \"t-icon\", 3, \"t-icon_blank\", \"t-icon_error\", \"icon\", 4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"], [1, \"t-icon\", 3, \"icon\"]],\n      template: function TuiFile_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 3);\n          i0.ɵɵtemplate(1, TuiFile_ng_container_1_Template, 2, 1, \"ng-container\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 5)(3, \"div\", 6)(4, \"div\", 7);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 8);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, TuiFile_div_8_Template, 2, 1, \"div\", 9);\n          i0.ɵɵpipe(9, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, TuiFile_div_10_Template, 2, 1, \"div\", 10);\n          i0.ɵɵpipe(11, \"async\");\n          i0.ɵɵprojection(12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, TuiFile_ng_container_13_Template, 3, 3, \"ng-container\", 11)(14, TuiFile_ng_template_14_Template, 5, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const defaultLeftContent_r11 = i0.ɵɵreference(15);\n          i0.ɵɵclassProp(\"t-preview_big\", ctx.isBig);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.leftContent || defaultLeftContent_r11);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.name, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.type);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSize && i0.ɵɵpipeBind1(9, 8, ctx.fileSize$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(11, 10, ctx.content$));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.allowDelete);\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf, i2.AsyncPipe, PolymorpheusOutlet, TuiButton, TuiHintOverflow, TuiIcon, TuiLoader],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:flex;align-items:center;font:var(--tui-font-text-m);padding:.625rem 2.25rem .625rem .625rem;text-decoration:none;border-radius:var(--tui-radius-m)}[_nghost-%COMP%]:hover   .t-remove[_ngcontent-%COMP%], [data-delete=always][_nghost-%COMP%]   .t-remove[_ngcontent-%COMP%]{opacity:1}.t-preview[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;justify-content:center;flex-shrink:0;inline-size:1.5rem;block-size:1.5rem;margin-right:.75rem;border-radius:var(--tui-radius-m);overflow:hidden;color:var(--tui-text-tertiary)}.t-preview_big[_ngcontent-%COMP%]{inline-size:4rem;block-size:4rem;margin-right:1rem}.t-preview_big[_ngcontent-%COMP%]:before{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";background:var(--tui-background-neutral-1)}.t-image[_ngcontent-%COMP%]{max-inline-size:100%;max-block-size:100%}.t-loader[_ngcontent-%COMP%]{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}.t-icon[_ngcontent-%COMP%]{position:absolute;top:0;left:0;bottom:0;right:0;color:var(--tui-status-positive);margin:auto}.t-icon_blank[_ngcontent-%COMP%]{color:var(--tui-text-tertiary)}.t-icon_error[_ngcontent-%COMP%]{color:var(--tui-text-negative)}.t-remove[_ngcontent-%COMP%]{transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:.625rem;right:.625rem}.t-remove[_ngcontent-%COMP%]:focus{opacity:1}.t-remove[_ngcontent-%COMP%]:focus-visible{box-shadow:inset 0 0 0 2px var(--tui-border-focus)}@media (hover: hover) and (pointer: fine){.t-remove[_ngcontent-%COMP%]{opacity:0}}.t-wrapper[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:flex-start;overflow:hidden;color:var(--tui-text-primary)}.t-text[_ngcontent-%COMP%]{display:flex}.t-size[_ngcontent-%COMP%]{flex-shrink:0;opacity:var(--tui-disabled-opacity);margin-left:.5rem}.t-type[_ngcontent-%COMP%]{flex-shrink:0}.t-name[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.t-content[_ngcontent-%COMP%]{font:var(--tui-font-text-s);color:var(--tui-text-negative)}\"],\n      changeDetection: 0\n    });\n  }\n}\n__decorate([tuiPure], TuiFile.prototype, \"calculateContent$\", null);\n__decorate([tuiPure], TuiFile.prototype, \"calculateFileSize$\", null);\n__decorate([tuiPure], TuiFile.prototype, \"createPreview\", null);\n__decorate([tuiPure], TuiFile.prototype, \"getName\", null);\n__decorate([tuiPure], TuiFile.prototype, \"getType\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFile, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-file,a[tuiFile],button[tuiFile]',\n      imports: [CommonModule, PolymorpheusOutlet, PolymorpheusTemplate, TuiButton, TuiHintOverflow, TuiIcon, TuiLoader],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiAppearanceOptionsProvider(TUI_FILE_OPTIONS)],\n      hostDirectives: [TuiAppearance],\n      host: {\n        '[attr.data-delete]': 'showDelete'\n      },\n      template: \"<div\\n    class=\\\"t-preview\\\"\\n    [class.t-preview_big]=\\\"isBig\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"leftContent || defaultLeftContent as text\\\">\\n        {{ text }}\\n    </ng-container>\\n</div>\\n<div class=\\\"t-wrapper\\\">\\n    <div class=\\\"t-text\\\">\\n        <div\\n            tuiHintOverflow\\n            class=\\\"t-name\\\"\\n        >\\n            {{ name }}\\n        </div>\\n        <div class=\\\"t-type\\\">{{ type }}</div>\\n        <div\\n            *ngIf=\\\"showSize && (fileSize$ | async) as fileSize\\\"\\n            class=\\\"t-size\\\"\\n        >\\n            {{ fileSize }}\\n        </div>\\n    </div>\\n    <div\\n        *ngIf=\\\"content$ | async as content\\\"\\n        class=\\\"t-content\\\"\\n    >\\n        <ng-container *polymorpheusOutlet=\\\"content as text\\\">\\n            {{ text }}\\n        </ng-container>\\n    </div>\\n    <ng-content />\\n</div>\\n<ng-container *ngIf=\\\"allowDelete\\\">\\n    <button\\n        *ngIf=\\\"fileTexts$ | async as texts\\\"\\n        appearance=\\\"icon\\\"\\n        size=\\\"xs\\\"\\n        tuiIconButton\\n        type=\\\"button\\\"\\n        class=\\\"t-remove\\\"\\n        [iconStart]=\\\"icons.close\\\"\\n        (click.prevent)=\\\"remove.emit()\\\"\\n        (mousedown.prevent.zoneless)=\\\"(0)\\\"\\n    >\\n        {{ texts.remove }}\\n    </button>\\n</ng-container>\\n\\n<ng-template #defaultLeftContent>\\n    <img\\n        *ngIf=\\\"preview; else loader\\\"\\n        alt=\\\"file preview\\\"\\n        class=\\\"t-image\\\"\\n        [src]=\\\"preview\\\"\\n    />\\n    <ng-template #loader>\\n        <tui-loader\\n            *ngIf=\\\"isLoading; else svg\\\"\\n            class=\\\"t-loader\\\"\\n            [inheritColor]=\\\"isBig\\\"\\n        />\\n    </ng-template>\\n    <ng-template #svg>\\n        <tui-icon\\n            *polymorpheusOutlet=\\\"icon as src; context: {$implicit: size}\\\"\\n            class=\\\"t-icon\\\"\\n            [class.t-icon_blank]=\\\"isBig || isDeleted\\\"\\n            [class.t-icon_error]=\\\"isError\\\"\\n            [icon]=\\\"src.toString()\\\"\\n        />\\n    </ng-template>\\n</ng-template>\\n\",\n      styles: [\":host{position:relative;display:flex;align-items:center;font:var(--tui-font-text-m);padding:.625rem 2.25rem .625rem .625rem;text-decoration:none;border-radius:var(--tui-radius-m)}:host:hover .t-remove,:host[data-delete=always] .t-remove{opacity:1}.t-preview{position:relative;display:flex;align-items:center;justify-content:center;flex-shrink:0;inline-size:1.5rem;block-size:1.5rem;margin-right:.75rem;border-radius:var(--tui-radius-m);overflow:hidden;color:var(--tui-text-tertiary)}.t-preview_big{inline-size:4rem;block-size:4rem;margin-right:1rem}.t-preview_big:before{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";background:var(--tui-background-neutral-1)}.t-image{max-inline-size:100%;max-block-size:100%}.t-loader{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}.t-icon{position:absolute;top:0;left:0;bottom:0;right:0;color:var(--tui-status-positive);margin:auto}.t-icon_blank{color:var(--tui-text-tertiary)}.t-icon_error{color:var(--tui-text-negative)}.t-remove{transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:.625rem;right:.625rem}.t-remove:focus{opacity:1}.t-remove:focus-visible{box-shadow:inset 0 0 0 2px var(--tui-border-focus)}@media (hover: hover) and (pointer: fine){.t-remove{opacity:0}}.t-wrapper{display:flex;flex-direction:column;justify-content:center;align-items:flex-start;overflow:hidden;color:var(--tui-text-primary)}.t-text{display:flex}.t-size{flex-shrink:0;opacity:var(--tui-disabled-opacity);margin-left:.5rem}.t-type{flex-shrink:0}.t-name{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.t-content{font:var(--tui-font-text-s);color:var(--tui-text-negative)}\\n\"]\n    }]\n  }], null, {\n    file: [{\n      type: Input\n    }],\n    state: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    showDelete: [{\n      type: Input\n    }],\n    showSize: [{\n      type: Input\n    }],\n    leftContent: [{\n      type: Input\n    }],\n    remove: [{\n      type: Output\n    }],\n    calculateContent$: [],\n    calculateFileSize$: [],\n    createPreview: [],\n    getName: [],\n    getType: []\n  });\n})();\nclass TuiFilesComponent {\n  constructor() {\n    this.items = EMPTY_QUERY;\n    this.hideText$ = inject(TUI_HIDE_TEXT);\n    this.showAllText$ = inject(TUI_SHOW_ALL_TEXT);\n    this.max = 0;\n    this.expanded = false;\n    this.expandedChange = new EventEmitter();\n  }\n  get hasExtraItems() {\n    return !!this.max && this.items.length > this.max;\n  }\n  toggle() {\n    this.expanded = !this.expanded;\n    this.expandedChange.emit(this.expanded);\n  }\n  static {\n    this.ɵfac = function TuiFilesComponent_Factory(t) {\n      return new (t || TuiFilesComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiFilesComponent,\n      selectors: [[\"tui-files\"]],\n      contentQueries: function TuiFilesComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiItem, 4, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      inputs: {\n        max: \"max\",\n        expanded: \"expanded\"\n      },\n      outputs: {\n        expandedChange: \"expandedChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiGroupOptionsProvider({\n        size: 'm',\n        collapsed: true,\n        orientation: 'vertical'\n      })]), i0.ɵɵHostDirectivesFeature([i1$1.TuiGroup]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 6,\n      vars: 6,\n      consts: [[4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [3, \"expanded\", 4, \"ngIf\"], [\"class\", \"t-bottom\", 3, \"t-bottom_collapsed\", 4, \"ngIf\"], [3, \"ngTemplateOutlet\", 4, \"ngIf\"], [3, \"ngTemplateOutlet\"], [3, \"expanded\"], [\"tuiGroup\", \"\", 1, \"t-extra-items\"], [1, \"t-bottom\"], [\"appearance\", \"outline\", \"size\", \"m\", \"tuiButton\", \"\", \"type\", \"button\", 1, \"t-button\", 3, \"click\"]],\n      template: function TuiFilesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, TuiFilesComponent_ng_container_0_Template, 1, 0, \"ng-container\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵprojection(2);\n          i0.ɵɵtemplate(3, TuiFilesComponent_ng_container_3_Template, 2, 1, \"ng-container\", 1)(4, TuiFilesComponent_tui_expand_4_Template, 3, 2, \"tui-expand\", 2)(5, TuiFilesComponent_div_5_Template, 4, 5, \"div\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 4, ctx.items == null ? null : ctx.items.changes));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasExtraItems);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasExtraItems);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.AsyncPipe, TuiButton, TuiExpandComponent, TuiGroup],\n      styles: [\"tui-files{inline-size:100%;overflow:hidden;border-radius:var(--tui-radius-m)}tui-files:empty:empty{display:none}tui-files .t-files{position:relative;display:block;inline-size:100%;block-size:100%;border-radius:var(--tui-radius-m);overflow:hidden}tui-files .t-button{inline-size:100%;border-radius:inherit}tui-files .t-bottom{z-index:3;inline-size:100%;background:var(--tui-background-base);-webkit-mask:none!important;mask:none!important}tui-files .t-bottom_collapsed{box-shadow:var(--tui-shadow-popup);margin-top:-1.5rem}tui-files .t-extra-items{inline-size:100%}tui-files .t-extra-items>*{border-radius:0!important}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFilesComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-files',\n      imports: [CommonModule, TuiButton, TuiExpandComponent, TuiGroup],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [tuiGroupOptionsProvider({\n        size: 'm',\n        collapsed: true,\n        orientation: 'vertical'\n      })],\n      hostDirectives: [TuiGroup],\n      template: \"<ng-container *ngIf=\\\"items?.changes | async\\\" />\\n<ng-content />\\n<ng-container *ngFor=\\\"let item of items; let index = index\\\">\\n    <ng-container\\n        *ngIf=\\\"!max || index < max\\\"\\n        [ngTemplateOutlet]=\\\"item\\\"\\n    />\\n</ng-container>\\n<tui-expand\\n    *ngIf=\\\"hasExtraItems\\\"\\n    [expanded]=\\\"expanded\\\"\\n>\\n    <div\\n        tuiGroup\\n        class=\\\"t-extra-items\\\"\\n    >\\n        <ng-container *ngFor=\\\"let item of items; let index = index\\\">\\n            <ng-container\\n                *ngIf=\\\"max && index >= max\\\"\\n                [ngTemplateOutlet]=\\\"item\\\"\\n            />\\n        </ng-container>\\n    </div>\\n</tui-expand>\\n<div\\n    *ngIf=\\\"hasExtraItems\\\"\\n    class=\\\"t-bottom\\\"\\n    [class.t-bottom_collapsed]=\\\"!expanded\\\"\\n>\\n    <button\\n        appearance=\\\"outline\\\"\\n        size=\\\"m\\\"\\n        tuiButton\\n        type=\\\"button\\\"\\n        class=\\\"t-button\\\"\\n        (click)=\\\"toggle()\\\"\\n    >\\n        {{ (expanded ? hideText$ : showAllText$) | async }}\\n    </button>\\n</div>\\n\",\n      styles: [\"tui-files{inline-size:100%;overflow:hidden;border-radius:var(--tui-radius-m)}tui-files:empty:empty{display:none}tui-files .t-files{position:relative;display:block;inline-size:100%;block-size:100%;border-radius:var(--tui-radius-m);overflow:hidden}tui-files .t-button{inline-size:100%;border-radius:inherit}tui-files .t-bottom{z-index:3;inline-size:100%;background:var(--tui-background-base);-webkit-mask:none!important;mask:none!important}tui-files .t-bottom_collapsed{box-shadow:var(--tui-shadow-popup);margin-top:-1.5rem}tui-files .t-extra-items{inline-size:100%}tui-files .t-extra-items>*{border-radius:0!important}\\n\"]\n    }]\n  }], null, {\n    items: [{\n      type: ContentChildren,\n      args: [TuiItem, {\n        read: TemplateRef\n      }]\n    }],\n    max: [{\n      type: Input\n    }],\n    expanded: [{\n      type: Input\n    }],\n    expandedChange: [{\n      type: Output\n    }]\n  });\n})();\nclass TuiInputFilesContent {\n  constructor() {\n    this.breakpoint$ = inject(TuiBreakpointService);\n    this.text$ = inject(TUI_INPUT_FILE_TEXTS);\n    this.context = injectContext();\n    this.component = inject(TuiInputFiles);\n  }\n  get link$() {\n    return this.computeLink$(this.context.$implicit, !!this.component.input?.input.multiple);\n  }\n  get label$() {\n    return this.computeLabel$(this.context.$implicit, !!this.component.input?.input.multiple);\n  }\n  computeLink$(fileDragged, multiple) {\n    return fileDragged ? of('') : this.text$.pipe(map(t => multiple ? t.defaultLinkMultiple : t.defaultLinkSingle));\n  }\n  computeLabel$(fileDragged, multiple) {\n    return fileDragged ? combineLatest([this.breakpoint$, this.text$]).pipe(map(([breakpoint, text]) => {\n      if (breakpoint === 'mobile') {\n        return '';\n      }\n      return multiple ? text.dropMultiple : text.drop;\n    })) : combineLatest([this.breakpoint$, this.text$]).pipe(map(([breakpoint, text]) => {\n      if (breakpoint === 'mobile') {\n        return '';\n      }\n      return multiple ? text.defaultLabelMultiple : text.defaultLabelSingle;\n    }));\n  }\n  static {\n    this.ɵfac = function TuiInputFilesContent_Factory(t) {\n      return new (t || TuiInputFilesContent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiInputFilesContent,\n      selectors: [[\"ng-component\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 6,\n      consts: [[\"tuiLink\", \"\"]],\n      template: function TuiInputFilesContent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"a\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵpipe(2, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, ctx.link$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 4, ctx.label$), \" \");\n        }\n      },\n      dependencies: [AsyncPipe, TuiLink],\n      encapsulation: 2\n    });\n  }\n}\n__decorate([tuiPure], TuiInputFilesContent.prototype, \"computeLink$\", null);\n__decorate([tuiPure], TuiInputFilesContent.prototype, \"computeLabel$\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputFilesContent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      imports: [AsyncPipe, TuiLink],\n      template: `\n        <a tuiLink>{{ link$ | async }}</a>\n        {{ label$ | async }}\n    `,\n      // eslint-disable-next-line @angular-eslint/prefer-on-push-component-change-detection\n      changeDetection: ChangeDetectionStrategy.Default\n    }]\n  }], null, {\n    computeLink$: [],\n    computeLabel$: []\n  });\n})();\nconst TUI_INPUT_FILES_DEFAULT_OPTIONS = {\n  accept: '',\n  multiple: false,\n  size: 'm',\n  maxFileSize: 30 * 1024 * 1024 // 30 MiB\n};\n/**\n * Default parameters for input files component\n */\nconst TUI_INPUT_FILES_OPTIONS = tuiCreateToken(TUI_INPUT_FILES_DEFAULT_OPTIONS);\nfunction tuiInputFilesOptionsProvider(options) {\n  return tuiProvideOptions(TUI_INPUT_FILES_OPTIONS, options, TUI_INPUT_FILES_DEFAULT_OPTIONS);\n}\nclass TuiInputFilesValidator extends TuiValidator {\n  constructor() {\n    super(...arguments);\n    this.options = inject(TUI_INPUT_FILES_OPTIONS);\n    this.accept = this.options.accept;\n    this.maxFileSize = this.options.maxFileSize;\n  }\n  ngOnChanges() {\n    this.update();\n  }\n  ngOnInit() {\n    this.update();\n  }\n  update() {\n    this.validate = Validators.compose([tuiCreateFileFormatValidator(this.accept), tuiCreateFileSizeValidator(this.maxFileSize)]) || Validators.nullValidator;\n    this.onChange();\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiInputFilesValidator_BaseFactory;\n      return function TuiInputFilesValidator_Factory(t) {\n        return (ɵTuiInputFilesValidator_BaseFactory || (ɵTuiInputFilesValidator_BaseFactory = i0.ɵɵgetInheritedFactory(TuiInputFilesValidator)))(t || TuiInputFilesValidator);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiInputFilesValidator,\n      hostVars: 1,\n      hostBindings: function TuiInputFilesValidator_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"accept\", ctx.accept);\n        }\n      },\n      inputs: {\n        accept: \"accept\",\n        maxFileSize: \"maxFileSize\"\n      },\n      exportAs: [\"tuiInputFilesValidator\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(NG_VALIDATORS, TuiInputFilesValidator, true)]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputFilesValidator, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      inputs: ['accept', 'maxFileSize'],\n      providers: [tuiProvide(NG_VALIDATORS, TuiInputFilesValidator, true)],\n      exportAs: 'tuiInputFilesValidator',\n      host: {\n        '[accept]': 'accept'\n      }\n    }]\n  }], null, null);\n})();\nclass TuiInputFilesDirective extends TuiControl {\n  constructor() {\n    super(...arguments);\n    this.host = inject(forwardRef(() => TuiInputFiles));\n    this.reject = timer(0, tuiZonefreeScheduler()).pipe(switchMap(() => tuiControlValue(this.control.control)), map(() => tuiFilesRejected(this.control.control)), filter(({\n      length\n    }) => !!length));\n    this.appearance = 'file';\n    this.input = tuiInjectElement();\n  }\n  process(files) {\n    const fileOrFiles = this.input.multiple ? [...toArray(this.value()), ...Array.from(files)] : files[0];\n    if (fileOrFiles) {\n      this.onChange(fileOrFiles);\n    }\n  }\n  onClick(event) {\n    if (this.input.readOnly) {\n      event.preventDefault();\n    }\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiInputFilesDirective_BaseFactory;\n      return function TuiInputFilesDirective_Factory(t) {\n        return (ɵTuiInputFilesDirective_BaseFactory || (ɵTuiInputFilesDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiInputFilesDirective)))(t || TuiInputFilesDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiInputFilesDirective,\n      selectors: [[\"input\", \"tuiInputFiles\", \"\"]],\n      hostAttrs: [\"title\", \"\", \"type\", \"file\"],\n      hostVars: 1,\n      hostBindings: function TuiInputFilesDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"blur\", function TuiInputFilesDirective_blur_HostBindingHandler() {\n            return ctx.onTouched();\n          })(\"click\", function TuiInputFilesDirective_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"disabled\", ctx.disabled());\n        }\n      },\n      outputs: {\n        reject: \"reject\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsControl(TuiInputFilesDirective), tuiAppearanceOptionsProvider(TuiInputFilesDirective)]), i0.ɵɵHostDirectivesFeature([i1$2.TuiNativeValidator, i1.TuiWithAppearance, {\n        directive: TuiInputFilesValidator,\n        inputs: [\"accept\", \"accept\", \"maxFileSize\", \"maxFileSize\"]\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputFilesDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[tuiInputFiles]',\n      providers: [tuiAsControl(TuiInputFilesDirective), tuiAppearanceOptionsProvider(TuiInputFilesDirective)],\n      hostDirectives: [TuiNativeValidator, TuiWithAppearance, {\n        directive: TuiInputFilesValidator,\n        inputs: ['accept', 'maxFileSize']\n      }],\n      host: {\n        title: '',\n        type: 'file',\n        '[disabled]': 'disabled()',\n        '(blur)': 'onTouched()',\n        '(click)': 'onClick($event)'\n      }\n    }]\n  }], null, {\n    reject: [{\n      type: Output\n    }]\n  });\n})();\nfunction toArray(value) {\n  return value ? coerceArray(value) : EMPTY_ARRAY;\n}\nclass TuiInputFiles {\n  constructor() {\n    this.content = new PolymorpheusComponent(TuiInputFilesContent);\n  }\n  get fileDragged() {\n    return !!this.files && !this.input?.disabled();\n  }\n  onFilesSelected(input) {\n    if (!input?.files) {\n      return;\n    }\n    this.input?.process(input.files);\n    input.value = '';\n  }\n  onDropped({\n    dataTransfer\n  }) {\n    this.files = null;\n    if (dataTransfer?.files && !this.input?.disabled()) {\n      this.input?.process(dataTransfer.files);\n    }\n  }\n  onDrag(dataTransfer) {\n    this.files = dataTransfer?.files;\n  }\n  static {\n    this.ɵfac = function TuiInputFiles_Factory(t) {\n      return new (t || TuiInputFiles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiInputFiles,\n      selectors: [[\"label\", \"tuiInputFiles\", \"\"]],\n      contentQueries: function TuiInputFiles_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TemplateRef, 5);\n          i0.ɵɵcontentQuery(dirIndex, TuiInputFilesDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n        }\n      },\n      hostVars: 2,\n      hostBindings: function TuiInputFiles_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"dragover.prevent.zoneless\", function TuiInputFiles_dragover_prevent_zoneless_HostBindingHandler() {\n            return 0;\n          })(\"drop.prevent\", function TuiInputFiles_drop_prevent_HostBindingHandler($event) {\n            return ctx.onDropped($event);\n          })(\"dragenter\", function TuiInputFiles_dragenter_HostBindingHandler($event) {\n            return ctx.onDrag($event.dataTransfer);\n          })(\"dragleave\", function TuiInputFiles_dragleave_HostBindingHandler() {\n            return ctx.onDrag(null);\n          })(\"change\", function TuiInputFiles_change_HostBindingHandler($event) {\n            return ctx.onFilesSelected($event.target);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"_dragged\", ctx.fileDragged);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 4,\n      consts: [[4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"]],\n      template: function TuiInputFiles_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵtemplate(1, TuiInputFiles_span_1_Template, 2, 1, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.template || ctx.content)(\"polymorpheusOutletContext\", i0.ɵɵpureFunction1(2, _c1, ctx.fileDragged));\n        }\n      },\n      dependencies: [PolymorpheusOutlet],\n      styles: [\"label[tuiInputFiles]{position:relative;display:flex;min-block-size:var(--tui-height-l);justify-content:center;align-items:center;text-align:center;border-radius:var(--tui-radius-m);font:var(--tui-font-text-m);overflow-wrap:break-word;padding:0 .5rem}label[tuiInputFiles]>:not(input){position:relative;pointer-events:none}label[tuiInputFiles] input{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;color:transparent;cursor:pointer}label[tuiInputFiles] input:disabled~*{opacity:var(--tui-disabled-opacity)}label[tuiInputFiles] input::-webkit-file-upload-button{display:none}label[tuiInputFiles] input::file-selector-button{display:none}*:disabled label[tuiInputFiles]{pointer-events:none}[tuiAppearance][data-appearance=file]{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;border-radius:inherit;box-sizing:border-box;border:1px dashed var(--tui-text-action);outline:none}tui-root._mobile [tuiAppearance][data-appearance=file]{border-style:solid}[tuiInputFiles]._dragged [tuiAppearance][data-appearance=file]{background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=file]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=file]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}}[tuiAppearance][data-appearance=file][data-state=hover]{background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=file][tuiWrapper]:hover:not(._no-hover),[tuiAppearance][data-appearance=file][tuiWrapper][data-state=hover]{background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}}[tuiAppearance][data-appearance=file]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file][data-state=active]{background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file][tuiWrapper]:active:not(._no-active),[tuiAppearance][data-appearance=file][tuiWrapper][data-state=active],[tuiAppearance][data-appearance=file][tuiWrapper][data-state=active]:hover{background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file]:disabled:not([data-state]),[tuiAppearance][data-appearance=file][data-state=disabled]{background:transparent;border-color:var(--tui-text-tertiary)}[tuiAppearance][data-appearance=file][tuiWrapper]:disabled:not([data-state]),[tuiAppearance][data-appearance=file][tuiWrapper][data-state=disabled]{background:transparent;border-color:var(--tui-text-tertiary)}[tuiAppearance][data-appearance=file]:focus-visible:not([data-focus=false]){border:.125rem solid var(--tui-border-focus)}[tuiAppearance][data-appearance=file][data-focus=true]{border:.125rem solid var(--tui-border-focus)}[tuiAppearance][data-appearance=file][tuiWrapper]:not(._focused):has(:focus-visible),[tuiAppearance][data-appearance=file][tuiWrapper]._focused{border:.125rem solid var(--tui-border-focus)}[tuiAppearance][data-appearance=file]:invalid{border-color:var(--tui-status-negative)!important}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiInputFiles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'label[tuiInputFiles]',\n      imports: [PolymorpheusOutlet, PolymorpheusTemplate],\n      template: `\n        <ng-content />\n        <span\n            *polymorpheusOutlet=\"\n                template || content as text;\n                context: {$implicit: fileDragged}\n            \"\n        >\n            {{ text }}\n        </span>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '(dragover.prevent.zoneless)': '0',\n        '(drop.prevent)': 'onDropped($event)',\n        '(dragenter)': 'onDrag($event.dataTransfer)',\n        '(dragleave)': 'onDrag(null)',\n        '[class._dragged]': 'fileDragged',\n        '(change)': 'onFilesSelected($event.target)'\n      },\n      styles: [\"label[tuiInputFiles]{position:relative;display:flex;min-block-size:var(--tui-height-l);justify-content:center;align-items:center;text-align:center;border-radius:var(--tui-radius-m);font:var(--tui-font-text-m);overflow-wrap:break-word;padding:0 .5rem}label[tuiInputFiles]>:not(input){position:relative;pointer-events:none}label[tuiInputFiles] input{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;color:transparent;cursor:pointer}label[tuiInputFiles] input:disabled~*{opacity:var(--tui-disabled-opacity)}label[tuiInputFiles] input::-webkit-file-upload-button{display:none}label[tuiInputFiles] input::file-selector-button{display:none}*:disabled label[tuiInputFiles]{pointer-events:none}[tuiAppearance][data-appearance=file]{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;border-radius:inherit;box-sizing:border-box;border:1px dashed var(--tui-text-action);outline:none}tui-root._mobile [tuiAppearance][data-appearance=file]{border-style:solid}[tuiInputFiles]._dragged [tuiAppearance][data-appearance=file]{background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=file]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=file]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}}[tuiAppearance][data-appearance=file][data-state=hover]{background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=file][tuiWrapper]:hover:not(._no-hover),[tuiAppearance][data-appearance=file][tuiWrapper][data-state=hover]{background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}}[tuiAppearance][data-appearance=file]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file][data-state=active]{background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file][tuiWrapper]:active:not(._no-active),[tuiAppearance][data-appearance=file][tuiWrapper][data-state=active],[tuiAppearance][data-appearance=file][tuiWrapper][data-state=active]:hover{background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file]:disabled:not([data-state]),[tuiAppearance][data-appearance=file][data-state=disabled]{background:transparent;border-color:var(--tui-text-tertiary)}[tuiAppearance][data-appearance=file][tuiWrapper]:disabled:not([data-state]),[tuiAppearance][data-appearance=file][tuiWrapper][data-state=disabled]{background:transparent;border-color:var(--tui-text-tertiary)}[tuiAppearance][data-appearance=file]:focus-visible:not([data-focus=false]){border:.125rem solid var(--tui-border-focus)}[tuiAppearance][data-appearance=file][data-focus=true]{border:.125rem solid var(--tui-border-focus)}[tuiAppearance][data-appearance=file][tuiWrapper]:not(._focused):has(:focus-visible),[tuiAppearance][data-appearance=file][tuiWrapper]._focused{border:.125rem solid var(--tui-border-focus)}[tuiAppearance][data-appearance=file]:invalid{border-color:var(--tui-status-negative)!important}\\n\"]\n    }]\n  }], null, {\n    template: [{\n      type: ContentChild,\n      args: [TemplateRef]\n    }],\n    input: [{\n      type: ContentChild,\n      args: [TuiInputFilesDirective]\n    }]\n  });\n})();\nclass TuiFileRejectedPipe {\n  constructor() {\n    this.options = inject(TUI_INPUT_FILES_OPTIONS);\n    this.formatSize = inject(TUI_FILE_OPTIONS).formatSize;\n    this.locale = inject(LOCALE_ID);\n    this.text$ = inject(TUI_INPUT_FILE_TEXTS);\n    this.unit$ = inject(TUI_DIGITAL_INFORMATION_UNITS);\n  }\n  transform(file, {\n    accept = this.options.accept,\n    maxFileSize = this.options.maxFileSize\n  } = this.options) {\n    const sizeValidator = tuiCreateFileSizeValidator(maxFileSize);\n    const formatValidator = tuiCreateFileFormatValidator(accept);\n    const control = new FormControl(file);\n    return combineLatest([this.text$, this.unit$]).pipe(map(([{\n      maxSizeRejectionReason,\n      formatRejectionReason\n    }, units]) => {\n      if (file && formatValidator(control)) {\n        return {\n          name: file.name,\n          size: file.size,\n          content: formatRejectionReason\n        };\n      }\n      if (file && sizeValidator(control)) {\n        return {\n          name: file.name,\n          size: file.size,\n          content: `${maxSizeRejectionReason}${CHAR_NO_BREAK_SPACE}${this.formatSize(units, maxFileSize, this.locale)}`\n        };\n      }\n      return null;\n    }));\n  }\n  static {\n    this.ɵfac = function TuiFileRejectedPipe_Factory(t) {\n      return new (t || TuiFileRejectedPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tuiFileRejected\",\n      type: TuiFileRejectedPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFileRejectedPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'tuiFileRejected'\n    }]\n  }], null, null);\n})();\nconst TuiFiles = [TuiItem, TuiFile, TuiInputFiles, TuiFilesComponent, TuiFileRejectedPipe, TuiInputFilesDirective];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_FILE_DEFAULT_OPTIONS, TUI_FILE_OPTIONS, TUI_FORMAT_ERROR, TUI_INPUT_FILES_DEFAULT_OPTIONS, TUI_INPUT_FILES_OPTIONS, TUI_SIZE_ERROR, TuiFile, TuiFileRejectedPipe, TuiFiles, TuiFilesComponent, TuiInputFiles, TuiInputFilesContent, TuiInputFilesDirective, TuiInputFilesValidator, tuiCreateFileFormatValidator, tuiCreateFileSizeValidator, tuiFileOptionsProvider, tuiFilesAccepted, tuiFilesRejected, tuiFormatSize, tuiInputFilesOptionsProvider };", "map": {"version": 3, "names": ["__decorate", "i2", "CommonModule", "AsyncPipe", "i0", "inject", "LOCALE_ID", "EventEmitter", "Component", "ChangeDetectionStrategy", "Input", "Output", "TemplateRef", "ViewEncapsulation", "ContentChildren", "Directive", "forwardRef", "ContentChild", "<PERSON><PERSON>", "Dom<PERSON><PERSON><PERSON>zer", "WA_WINDOW", "tuiPure", "tuiCreateToken", "tuiProvideOptions", "tui<PERSON><PERSON><PERSON>", "TuiButton", "TuiIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i1", "tuiAppearanceOptionsProvider", "TuiAppearan<PERSON>", "TuiWithAppearance", "TuiHintOverflow", "TUI_COMMON_ICONS", "TUI_DIGITAL_INFORMATION_UNITS", "TUI_FILE_TEXTS", "TUI_HIDE_TEXT", "TUI_SHOW_ALL_TEXT", "TUI_INPUT_FILE_TEXTS", "Polymorpheus<PERSON><PERSON>let", "PolymorpheusTemplate", "injectContext", "PolymorpheusComponent", "map", "of", "combineLatest", "timer", "switchMap", "filter", "tuiCreateOptions", "coerce<PERSON><PERSON><PERSON>", "tuiRound", "TuiItem", "EMPTY_QUERY", "EMPTY_ARRAY", "CHAR_NO_BREAK_SPACE", "TuiExpandComponent", "i1$1", "tuiGroupOptionsProvider", "TuiGroup", "TuiLink", "TuiBreakpointService", "TuiControl", "tuiAsControl", "i1$2", "TuiNativeValidator", "tuiZonefreeScheduler", "tuiControlValue", "tuiInjectElement", "Validators", "NG_VALIDATORS", "FormControl", "TuiValida<PERSON>", "_c0", "_c1", "a0", "$implicit", "TuiFile_ng_container_1_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "text_r1", "polymorpheusOutlet", "ɵɵadvance", "ɵɵtextInterpolate1", "TuiFile_div_8_Template", "ɵɵelementStart", "ɵɵelementEnd", "fileSize_r2", "ngIf", "TuiFile_div_10_ng_container_1_Template", "text_r3", "TuiFile_div_10_Template", "ɵɵtemplate", "content_r4", "ɵɵproperty", "TuiFile_ng_container_13_button_1_Template", "_r5", "ɵɵgetCurrentView", "ɵɵlistener", "TuiFile_ng_container_13_button_1_Template_button_click_prevent_0_listener", "ɵɵrestoreView", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "remove", "emit", "TuiFile_ng_container_13_button_1_Template_button_mousedown_prevent_zoneless_0_listener", "texts_r7", "icons", "close", "TuiFile_ng_container_13_Template", "ɵɵpipe", "ɵɵpipeBind1", "fileTexts$", "TuiFile_ng_template_14_img_0_Template", "ɵɵelement", "preview", "ɵɵsanitizeUrl", "TuiFile_ng_template_14_ng_template_1_tui_loader_0_Template", "isBig", "TuiFile_ng_template_14_ng_template_1_Template", "svg_r8", "ɵɵreference", "isLoading", "TuiFile_ng_template_14_ng_template_3_tui_icon_0_Template", "src_r9", "ɵɵclassProp", "isDeleted", "isError", "toString", "TuiFile_ng_template_14_ng_template_3_Template", "icon", "ɵɵpureFunction1", "size", "TuiFile_ng_template_14_Template", "ɵɵtemplateRefExtractor", "loader_r10", "TuiFilesComponent_ng_container_0_Template", "ɵɵelementContainer", "TuiFilesComponent_ng_container_3_ng_container_1_Template", "item_r1", "TuiFilesComponent_ng_container_3_Template", "index_r2", "index", "ctx_r2", "max", "TuiFilesComponent_tui_expand_4_ng_container_2_ng_container_1_Template", "item_r4", "TuiFilesComponent_tui_expand_4_ng_container_2_Template", "index_r5", "TuiFilesComponent_tui_expand_4_Template", "expanded", "items", "TuiFilesComponent_div_5_Template", "_r6", "TuiFilesComponent_div_5_Template_button_click_1_listener", "toggle", "hideText$", "showAllText$", "_c2", "TuiInputFiles_span_1_Template", "TUI_SIZE_ERROR", "TUI_FORMAT_ERROR", "tuiCreateFileSizeValidator", "value", "files", "file", "length", "tuiCreateFileFormatValidator", "accept", "formats", "toArray$1", "checkFormat", "name", "type", "extension", "split", "pop", "toLowerCase", "some", "format", "trim", "BYTES_PER_KIB", "BYTES_PER_MIB", "tuiFilesRejected", "control", "getError", "Array", "from", "Set", "tuiFilesAccepted", "includes", "tuiFormatSize", "units", "locale", "undefined", "toFixed", "toLocaleString", "TUI_FILE_DEFAULT_OPTIONS", "appearance", "formatSize", "normal", "error", "deleted", "TUI_FILE_OPTIONS", "tuiFileOptionsProvider", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "sanitizer", "options", "units$", "win", "state", "showDelete", "showSize", "createPreview", "allowDelete", "observed", "getName", "getType", "content$", "calculateContent$", "fileSize$", "calculateFileSize$", "content", "pipe", "texts", "loadingError", "src", "File", "startsWith", "bypassSecurityTrustUrl", "URL", "createObjectURL", "dot", "lastIndexOf", "slice", "ɵfac", "TuiFile_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostVars", "hostBindings", "TuiFile_HostBindings", "ɵɵattribute", "inputs", "leftContent", "outputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵHostDirectivesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "TuiFile_Template", "ɵɵprojectionDef", "ɵɵprojection", "defaultLeftContent_r11", "ɵɵtextInterpolate", "dependencies", "NgIf", "styles", "changeDetection", "prototype", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "hostDirectives", "host", "TuiFilesComponent", "expandedChange", "hasExtraItems", "TuiFilesComponent_Factory", "contentQueries", "TuiFilesComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "collapsed", "orientation", "TuiFilesComponent_Template", "changes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgTemplateOutlet", "encapsulation", "None", "read", "TuiInputFilesContent", "breakpoint$", "text$", "context", "component", "TuiInputFiles", "link$", "computeLink$", "input", "multiple", "label$", "computeLabel$", "fileDragged", "defaultLinkMultiple", "defaultLinkSingle", "breakpoint", "text", "dropMultiple", "drop", "defaultLabelMultiple", "defaultLabelSingle", "TuiInputFilesContent_Factory", "TuiInputFilesContent_Template", "<PERSON><PERSON><PERSON>", "TUI_INPUT_FILES_DEFAULT_OPTIONS", "maxFileSize", "TUI_INPUT_FILES_OPTIONS", "tuiInputFilesOptionsProvider", "TuiInputFilesValidator", "arguments", "ngOnChanges", "update", "ngOnInit", "validate", "compose", "nullValidator", "onChange", "ɵTuiInputFilesValidator_BaseFactory", "TuiInputFilesValidator_Factory", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "TuiInputFilesValidator_HostBindings", "ɵɵhostProperty", "exportAs", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "TuiInputFilesDirective", "reject", "process", "fileOrFiles", "toArray", "onClick", "event", "readOnly", "preventDefault", "ɵTuiInputFilesDirective_BaseFactory", "TuiInputFilesDirective_Factory", "hostAttrs", "TuiInputFilesDirective_HostBindings", "TuiInputFilesDirective_blur_HostBindingHandler", "onTouched", "TuiInputFilesDirective_click_HostBindingHandler", "$event", "disabled", "directive", "title", "onFilesSelected", "onDropped", "dataTransfer", "onDrag", "TuiInputFiles_Factory", "TuiInputFiles_ContentQueries", "first", "TuiInputFiles_HostBindings", "TuiInputFiles_dragover_prevent_zoneless_HostBindingHandler", "TuiInputFiles_drop_prevent_HostBindingHandler", "TuiInputFiles_dragenter_HostBindingHandler", "TuiInputFiles_dragleave_HostBindingHandler", "TuiInputFiles_change_HostBindingHandler", "target", "attrs", "TuiInputFiles_Template", "TuiFileRejectedPipe", "unit$", "transform", "sizeValidator", "formatValidator", "maxSizeRejectionReason", "formatRejectionReason", "TuiFileRejectedPipe_Factory", "ɵpipe", "ɵɵdefinePipe", "pure", "TuiFiles"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-files.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i2 from '@angular/common';\nimport { CommonModule, AsyncPipe } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, LOCALE_ID, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, TemplateRef, ViewEncapsulation, ContentChildren, Directive, forwardRef, ContentChild, Pipe } from '@angular/core';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { WA_WINDOW } from '@ng-web-apis/common';\nimport { tuiPure, tuiCreateToken, tuiProvideOptions, tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiButton } from '@taiga-ui/core/components/button';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TuiLoader } from '@taiga-ui/core/components/loader';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { tuiAppearanceOptionsProvider, TuiAppearance, Tui<PERSON>ithAppearance } from '@taiga-ui/core/directives/appearance';\nimport { TuiHintOverflow } from '@taiga-ui/core/directives/hint';\nimport { TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { TUI_DIGITAL_INFORMATION_UNITS, TUI_FILE_TEXTS, TUI_HIDE_TEXT, TUI_SHOW_ALL_TEXT, TUI_INPUT_FILE_TEXTS } from '@taiga-ui/kit/tokens';\nimport { PolymorpheusOutlet, PolymorpheusTemplate, injectContext, PolymorpheusComponent } from '@taiga-ui/polymorpheus';\nimport { map, of, combineLatest, timer, switchMap, filter } from 'rxjs';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { tuiRound } from '@taiga-ui/cdk/utils/math';\nimport { TuiItem } from '@taiga-ui/cdk/directives/item';\nimport { EMPTY_QUERY, EMPTY_ARRAY, CHAR_NO_BREAK_SPACE } from '@taiga-ui/cdk/constants';\nimport { TuiExpandComponent } from '@taiga-ui/core/components/expand';\nimport * as i1$1 from '@taiga-ui/core/directives/group';\nimport { tuiGroupOptionsProvider, TuiGroup } from '@taiga-ui/core/directives/group';\nimport { TuiLink } from '@taiga-ui/core/components/link';\nimport { TuiBreakpointService } from '@taiga-ui/core/services';\nimport { TuiControl, tuiAsControl } from '@taiga-ui/cdk/classes';\nimport * as i1$2 from '@taiga-ui/cdk/directives/native-validator';\nimport { TuiNativeValidator } from '@taiga-ui/cdk/directives/native-validator';\nimport { tuiZonefreeScheduler, tuiControlValue } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { Validators, NG_VALIDATORS, FormControl } from '@angular/forms';\nimport { TuiValidator } from '@taiga-ui/cdk/directives/validator';\n\nconst TUI_SIZE_ERROR = 'tuiSize';\nconst TUI_FORMAT_ERROR = 'tuiFormat';\nfunction tuiCreateFileSizeValidator(size) {\n    return ({ value }) => {\n        const files = value && coerceArray(value);\n        const $implicit = value && files?.filter((file) => file.size > size);\n        return $implicit?.length ? { [TUI_SIZE_ERROR]: { $implicit, size } } : null;\n    };\n}\nfunction tuiCreateFileFormatValidator(accept) {\n    return ({ value }) => {\n        const files = value && coerceArray(value);\n        const formats = toArray$1(accept);\n        const $implicit = value && files?.filter((file) => !checkFormat(file, formats));\n        return $implicit?.length && accept ? { [TUI_FORMAT_ERROR]: { $implicit } } : null;\n    };\n}\nfunction checkFormat({ name, type }, formats) {\n    const extension = `.${(name.split('.').pop() || '').toLowerCase()}`;\n    return formats.some((format) => format === extension ||\n        format === type ||\n        (format.split('/')[1] === '*' &&\n            type?.split('/')[0] === format.split('/')[0]));\n}\nfunction toArray$1(accept) {\n    return accept\n        .toLowerCase()\n        .split(',')\n        .map((format) => format.trim().toLowerCase());\n}\n\nconst BYTES_PER_KIB = 1024;\nconst BYTES_PER_MIB = 1024 * BYTES_PER_KIB;\nfunction tuiFilesRejected(control) {\n    const format = control?.getError(TUI_FORMAT_ERROR)?.$implicit || [];\n    const size = control?.getError(TUI_SIZE_ERROR)?.$implicit || [];\n    return Array.from(new Set([...format, ...size]));\n}\nfunction tuiFilesAccepted(control) {\n    const value = control?.value || [];\n    const files = coerceArray(value);\n    const size = control?.getError(TUI_SIZE_ERROR)?.$implicit || [];\n    const format = control?.getError(TUI_FORMAT_ERROR)?.$implicit || [];\n    return files.filter((file) => !size.includes(file) && !format.includes(file));\n}\nfunction tuiFormatSize(units, size, locale) {\n    if (size === undefined) {\n        return null;\n    }\n    if (size < BYTES_PER_KIB) {\n        return `${size} ${units[0]}`;\n    }\n    if (size < BYTES_PER_MIB) {\n        return `${(size / BYTES_PER_KIB).toFixed(0)} ${units[1]}`;\n    }\n    return `${tuiRound(size / BYTES_PER_MIB, 2).toLocaleString(locale)} ${units[2]}`;\n}\n\nconst TUI_FILE_DEFAULT_OPTIONS = {\n    appearance: 'outline',\n    formatSize: tuiFormatSize,\n    icons: {\n        normal: ({ $implicit }) => ($implicit === 'l' ? '@tui.file' : '@tui.circle-check'),\n        error: '@tui.circle-alert',\n        deleted: '@tui.trash',\n    },\n};\n/**\n * Default parameters for file component\n */\nconst [TUI_FILE_OPTIONS, tuiFileOptionsProvider] = tuiCreateOptions(TUI_FILE_DEFAULT_OPTIONS);\n\nclass TuiFile {\n    constructor() {\n        this.sanitizer = inject(DomSanitizer);\n        this.options = inject(TUI_FILE_OPTIONS);\n        this.locale = inject(LOCALE_ID);\n        this.units$ = inject(TUI_DIGITAL_INFORMATION_UNITS);\n        this.win = inject(WA_WINDOW);\n        this.icons = inject(TUI_COMMON_ICONS);\n        this.fileTexts$ = inject(TUI_FILE_TEXTS);\n        this.file = { name: '' };\n        this.state = 'normal';\n        this.size = 'm';\n        this.showDelete = true;\n        this.showSize = true;\n        this.remove = new EventEmitter();\n    }\n    get preview() {\n        return this.isBig ? this.createPreview(this.file) : '';\n    }\n    get isBig() {\n        return this.size === 'l';\n    }\n    get isLoading() {\n        return this.state === 'loading';\n    }\n    get isError() {\n        return this.state === 'error';\n    }\n    get isDeleted() {\n        return this.state === 'deleted';\n    }\n    get allowDelete() {\n        return this.showDelete && this.remove.observed;\n    }\n    get icon() {\n        return this.state === 'loading' ? '' : this.options.icons[this.state];\n    }\n    get name() {\n        return this.getName(this.file);\n    }\n    get type() {\n        return this.getType(this.file);\n    }\n    get content$() {\n        return this.calculateContent$(this.state, this.file, this.fileTexts$);\n    }\n    get fileSize$() {\n        return this.calculateFileSize$(this.file, this.units$);\n    }\n    calculateContent$(state, file, fileTexts$) {\n        return state === 'error' && !file.content\n            ? fileTexts$.pipe(map((texts) => texts.loadingError))\n            : of(this.file.content || '');\n    }\n    calculateFileSize$(file, units$) {\n        return units$.pipe(map((units) => this.options.formatSize(units, file.size, this.locale)));\n    }\n    createPreview(file) {\n        if (file.src) {\n            return file.src;\n        }\n        if (this.win.File &&\n            file instanceof this.win.File &&\n            file.type?.startsWith('image/')) {\n            return this.sanitizer.bypassSecurityTrustUrl(URL.createObjectURL(file));\n        }\n        return '';\n    }\n    getName(file) {\n        const dot = file.name.lastIndexOf('.');\n        // a dot at position 0 means a “hidden” file, not an extension\n        return dot > 0 ? file.name.slice(0, dot) : file.name;\n    }\n    getType(file) {\n        const dot = file.name.lastIndexOf('.');\n        // only return an extension when there is one\n        return dot > 0 ? file.name.slice(dot) : '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFile, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiFile, isStandalone: true, selector: \"tui-file,a[tuiFile],button[tuiFile]\", inputs: { file: \"file\", state: \"state\", size: \"size\", showDelete: \"showDelete\", showSize: \"showSize\", leftContent: \"leftContent\" }, outputs: { remove: \"remove\" }, host: { properties: { \"attr.data-delete\": \"showDelete\" } }, providers: [tuiAppearanceOptionsProvider(TUI_FILE_OPTIONS)], hostDirectives: [{ directive: i1.TuiAppearance }], ngImport: i0, template: \"<div\\n    class=\\\"t-preview\\\"\\n    [class.t-preview_big]=\\\"isBig\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"leftContent || defaultLeftContent as text\\\">\\n        {{ text }}\\n    </ng-container>\\n</div>\\n<div class=\\\"t-wrapper\\\">\\n    <div class=\\\"t-text\\\">\\n        <div\\n            tuiHintOverflow\\n            class=\\\"t-name\\\"\\n        >\\n            {{ name }}\\n        </div>\\n        <div class=\\\"t-type\\\">{{ type }}</div>\\n        <div\\n            *ngIf=\\\"showSize && (fileSize$ | async) as fileSize\\\"\\n            class=\\\"t-size\\\"\\n        >\\n            {{ fileSize }}\\n        </div>\\n    </div>\\n    <div\\n        *ngIf=\\\"content$ | async as content\\\"\\n        class=\\\"t-content\\\"\\n    >\\n        <ng-container *polymorpheusOutlet=\\\"content as text\\\">\\n            {{ text }}\\n        </ng-container>\\n    </div>\\n    <ng-content />\\n</div>\\n<ng-container *ngIf=\\\"allowDelete\\\">\\n    <button\\n        *ngIf=\\\"fileTexts$ | async as texts\\\"\\n        appearance=\\\"icon\\\"\\n        size=\\\"xs\\\"\\n        tuiIconButton\\n        type=\\\"button\\\"\\n        class=\\\"t-remove\\\"\\n        [iconStart]=\\\"icons.close\\\"\\n        (click.prevent)=\\\"remove.emit()\\\"\\n        (mousedown.prevent.zoneless)=\\\"(0)\\\"\\n    >\\n        {{ texts.remove }}\\n    </button>\\n</ng-container>\\n\\n<ng-template #defaultLeftContent>\\n    <img\\n        *ngIf=\\\"preview; else loader\\\"\\n        alt=\\\"file preview\\\"\\n        class=\\\"t-image\\\"\\n        [src]=\\\"preview\\\"\\n    />\\n    <ng-template #loader>\\n        <tui-loader\\n            *ngIf=\\\"isLoading; else svg\\\"\\n            class=\\\"t-loader\\\"\\n            [inheritColor]=\\\"isBig\\\"\\n        />\\n    </ng-template>\\n    <ng-template #svg>\\n        <tui-icon\\n            *polymorpheusOutlet=\\\"icon as src; context: {$implicit: size}\\\"\\n            class=\\\"t-icon\\\"\\n            [class.t-icon_blank]=\\\"isBig || isDeleted\\\"\\n            [class.t-icon_error]=\\\"isError\\\"\\n            [icon]=\\\"src.toString()\\\"\\n        />\\n    </ng-template>\\n</ng-template>\\n\", styles: [\":host{position:relative;display:flex;align-items:center;font:var(--tui-font-text-m);padding:.625rem 2.25rem .625rem .625rem;text-decoration:none;border-radius:var(--tui-radius-m)}:host:hover .t-remove,:host[data-delete=always] .t-remove{opacity:1}.t-preview{position:relative;display:flex;align-items:center;justify-content:center;flex-shrink:0;inline-size:1.5rem;block-size:1.5rem;margin-right:.75rem;border-radius:var(--tui-radius-m);overflow:hidden;color:var(--tui-text-tertiary)}.t-preview_big{inline-size:4rem;block-size:4rem;margin-right:1rem}.t-preview_big:before{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";background:var(--tui-background-neutral-1)}.t-image{max-inline-size:100%;max-block-size:100%}.t-loader{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}.t-icon{position:absolute;top:0;left:0;bottom:0;right:0;color:var(--tui-status-positive);margin:auto}.t-icon_blank{color:var(--tui-text-tertiary)}.t-icon_error{color:var(--tui-text-negative)}.t-remove{transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:.625rem;right:.625rem}.t-remove:focus{opacity:1}.t-remove:focus-visible{box-shadow:inset 0 0 0 2px var(--tui-border-focus)}@media (hover: hover) and (pointer: fine){.t-remove{opacity:0}}.t-wrapper{display:flex;flex-direction:column;justify-content:center;align-items:flex-start;overflow:hidden;color:var(--tui-text-primary)}.t-text{display:flex}.t-size{flex-shrink:0;opacity:var(--tui-disabled-opacity);margin-left:.5rem}.t-type{flex-shrink:0}.t-name{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.t-content{font:var(--tui-font-text-s);color:var(--tui-text-negative)}\\n\"], dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"pipe\", type: i2.AsyncPipe, name: \"async\" }, { kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"directive\", type: TuiHintOverflow, selector: \"[tuiHintOverflow]\", inputs: [\"tuiHintOverflow\"] }, { kind: \"component\", type: TuiIcon, selector: \"tui-icon\", inputs: [\"icon\", \"background\"] }, { kind: \"component\", type: TuiLoader, selector: \"tui-loader\", inputs: [\"size\", \"inheritColor\", \"overlay\", \"textContent\", \"showLoader\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\n__decorate([\n    tuiPure\n], TuiFile.prototype, \"calculateContent$\", null);\n__decorate([\n    tuiPure\n], TuiFile.prototype, \"calculateFileSize$\", null);\n__decorate([\n    tuiPure\n], TuiFile.prototype, \"createPreview\", null);\n__decorate([\n    tuiPure\n], TuiFile.prototype, \"getName\", null);\n__decorate([\n    tuiPure\n], TuiFile.prototype, \"getType\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFile, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-file,a[tuiFile],button[tuiFile]', imports: [\n                        CommonModule,\n                        PolymorpheusOutlet,\n                        PolymorpheusTemplate,\n                        TuiButton,\n                        TuiHintOverflow,\n                        TuiIcon,\n                        TuiLoader,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, providers: [tuiAppearanceOptionsProvider(TUI_FILE_OPTIONS)], hostDirectives: [TuiAppearance], host: {\n                        '[attr.data-delete]': 'showDelete',\n                    }, template: \"<div\\n    class=\\\"t-preview\\\"\\n    [class.t-preview_big]=\\\"isBig\\\"\\n>\\n    <ng-container *polymorpheusOutlet=\\\"leftContent || defaultLeftContent as text\\\">\\n        {{ text }}\\n    </ng-container>\\n</div>\\n<div class=\\\"t-wrapper\\\">\\n    <div class=\\\"t-text\\\">\\n        <div\\n            tuiHintOverflow\\n            class=\\\"t-name\\\"\\n        >\\n            {{ name }}\\n        </div>\\n        <div class=\\\"t-type\\\">{{ type }}</div>\\n        <div\\n            *ngIf=\\\"showSize && (fileSize$ | async) as fileSize\\\"\\n            class=\\\"t-size\\\"\\n        >\\n            {{ fileSize }}\\n        </div>\\n    </div>\\n    <div\\n        *ngIf=\\\"content$ | async as content\\\"\\n        class=\\\"t-content\\\"\\n    >\\n        <ng-container *polymorpheusOutlet=\\\"content as text\\\">\\n            {{ text }}\\n        </ng-container>\\n    </div>\\n    <ng-content />\\n</div>\\n<ng-container *ngIf=\\\"allowDelete\\\">\\n    <button\\n        *ngIf=\\\"fileTexts$ | async as texts\\\"\\n        appearance=\\\"icon\\\"\\n        size=\\\"xs\\\"\\n        tuiIconButton\\n        type=\\\"button\\\"\\n        class=\\\"t-remove\\\"\\n        [iconStart]=\\\"icons.close\\\"\\n        (click.prevent)=\\\"remove.emit()\\\"\\n        (mousedown.prevent.zoneless)=\\\"(0)\\\"\\n    >\\n        {{ texts.remove }}\\n    </button>\\n</ng-container>\\n\\n<ng-template #defaultLeftContent>\\n    <img\\n        *ngIf=\\\"preview; else loader\\\"\\n        alt=\\\"file preview\\\"\\n        class=\\\"t-image\\\"\\n        [src]=\\\"preview\\\"\\n    />\\n    <ng-template #loader>\\n        <tui-loader\\n            *ngIf=\\\"isLoading; else svg\\\"\\n            class=\\\"t-loader\\\"\\n            [inheritColor]=\\\"isBig\\\"\\n        />\\n    </ng-template>\\n    <ng-template #svg>\\n        <tui-icon\\n            *polymorpheusOutlet=\\\"icon as src; context: {$implicit: size}\\\"\\n            class=\\\"t-icon\\\"\\n            [class.t-icon_blank]=\\\"isBig || isDeleted\\\"\\n            [class.t-icon_error]=\\\"isError\\\"\\n            [icon]=\\\"src.toString()\\\"\\n        />\\n    </ng-template>\\n</ng-template>\\n\", styles: [\":host{position:relative;display:flex;align-items:center;font:var(--tui-font-text-m);padding:.625rem 2.25rem .625rem .625rem;text-decoration:none;border-radius:var(--tui-radius-m)}:host:hover .t-remove,:host[data-delete=always] .t-remove{opacity:1}.t-preview{position:relative;display:flex;align-items:center;justify-content:center;flex-shrink:0;inline-size:1.5rem;block-size:1.5rem;margin-right:.75rem;border-radius:var(--tui-radius-m);overflow:hidden;color:var(--tui-text-tertiary)}.t-preview_big{inline-size:4rem;block-size:4rem;margin-right:1rem}.t-preview_big:before{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;content:\\\"\\\";background:var(--tui-background-neutral-1)}.t-image{max-inline-size:100%;max-block-size:100%}.t-loader{position:absolute;top:0;left:0;inline-size:100%;block-size:100%}.t-icon{position:absolute;top:0;left:0;bottom:0;right:0;color:var(--tui-status-positive);margin:auto}.t-icon_blank{color:var(--tui-text-tertiary)}.t-icon_error{color:var(--tui-text-negative)}.t-remove{transition-property:opacity;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:absolute;top:.625rem;right:.625rem}.t-remove:focus{opacity:1}.t-remove:focus-visible{box-shadow:inset 0 0 0 2px var(--tui-border-focus)}@media (hover: hover) and (pointer: fine){.t-remove{opacity:0}}.t-wrapper{display:flex;flex-direction:column;justify-content:center;align-items:flex-start;overflow:hidden;color:var(--tui-text-primary)}.t-text{display:flex}.t-size{flex-shrink:0;opacity:var(--tui-disabled-opacity);margin-left:.5rem}.t-type{flex-shrink:0}.t-name{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.t-content{font:var(--tui-font-text-s);color:var(--tui-text-negative)}\\n\"] }]\n        }], propDecorators: { file: [{\n                type: Input\n            }], state: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], showDelete: [{\n                type: Input\n            }], showSize: [{\n                type: Input\n            }], leftContent: [{\n                type: Input\n            }], remove: [{\n                type: Output\n            }], calculateContent$: [], calculateFileSize$: [], createPreview: [], getName: [], getType: [] } });\n\nclass TuiFilesComponent {\n    constructor() {\n        this.items = EMPTY_QUERY;\n        this.hideText$ = inject(TUI_HIDE_TEXT);\n        this.showAllText$ = inject(TUI_SHOW_ALL_TEXT);\n        this.max = 0;\n        this.expanded = false;\n        this.expandedChange = new EventEmitter();\n    }\n    get hasExtraItems() {\n        return !!this.max && this.items.length > this.max;\n    }\n    toggle() {\n        this.expanded = !this.expanded;\n        this.expandedChange.emit(this.expanded);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFilesComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiFilesComponent, isStandalone: true, selector: \"tui-files\", inputs: { max: \"max\", expanded: \"expanded\" }, outputs: { expandedChange: \"expandedChange\" }, providers: [\n            tuiGroupOptionsProvider({ size: 'm', collapsed: true, orientation: 'vertical' }),\n        ], queries: [{ propertyName: \"items\", predicate: TuiItem, read: TemplateRef }], hostDirectives: [{ directive: i1$1.TuiGroup }], ngImport: i0, template: \"<ng-container *ngIf=\\\"items?.changes | async\\\" />\\n<ng-content />\\n<ng-container *ngFor=\\\"let item of items; let index = index\\\">\\n    <ng-container\\n        *ngIf=\\\"!max || index < max\\\"\\n        [ngTemplateOutlet]=\\\"item\\\"\\n    />\\n</ng-container>\\n<tui-expand\\n    *ngIf=\\\"hasExtraItems\\\"\\n    [expanded]=\\\"expanded\\\"\\n>\\n    <div\\n        tuiGroup\\n        class=\\\"t-extra-items\\\"\\n    >\\n        <ng-container *ngFor=\\\"let item of items; let index = index\\\">\\n            <ng-container\\n                *ngIf=\\\"max && index >= max\\\"\\n                [ngTemplateOutlet]=\\\"item\\\"\\n            />\\n        </ng-container>\\n    </div>\\n</tui-expand>\\n<div\\n    *ngIf=\\\"hasExtraItems\\\"\\n    class=\\\"t-bottom\\\"\\n    [class.t-bottom_collapsed]=\\\"!expanded\\\"\\n>\\n    <button\\n        appearance=\\\"outline\\\"\\n        size=\\\"m\\\"\\n        tuiButton\\n        type=\\\"button\\\"\\n        class=\\\"t-button\\\"\\n        (click)=\\\"toggle()\\\"\\n    >\\n        {{ (expanded ? hideText$ : showAllText$) | async }}\\n    </button>\\n</div>\\n\", styles: [\"tui-files{inline-size:100%;overflow:hidden;border-radius:var(--tui-radius-m)}tui-files:empty:empty{display:none}tui-files .t-files{position:relative;display:block;inline-size:100%;block-size:100%;border-radius:var(--tui-radius-m);overflow:hidden}tui-files .t-button{inline-size:100%;border-radius:inherit}tui-files .t-bottom{z-index:3;inline-size:100%;background:var(--tui-background-base);-webkit-mask:none!important;mask:none!important}tui-files .t-bottom_collapsed{box-shadow:var(--tui-shadow-popup);margin-top:-1.5rem}tui-files .t-extra-items{inline-size:100%}tui-files .t-extra-items>*{border-radius:0!important}\\n\"], dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"pipe\", type: i2.AsyncPipe, name: \"async\" }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"component\", type: TuiExpandComponent, selector: \"tui-expand\", inputs: [\"async\", \"expanded\"] }, { kind: \"directive\", type: TuiGroup, selector: \"[tuiGroup]:not(ng-container)\", inputs: [\"orientation\", \"collapsed\", \"rounded\", \"size\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFilesComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-files', imports: [CommonModule, TuiButton, TuiExpandComponent, TuiGroup], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        tuiGroupOptionsProvider({ size: 'm', collapsed: true, orientation: 'vertical' }),\n                    ], hostDirectives: [TuiGroup], template: \"<ng-container *ngIf=\\\"items?.changes | async\\\" />\\n<ng-content />\\n<ng-container *ngFor=\\\"let item of items; let index = index\\\">\\n    <ng-container\\n        *ngIf=\\\"!max || index < max\\\"\\n        [ngTemplateOutlet]=\\\"item\\\"\\n    />\\n</ng-container>\\n<tui-expand\\n    *ngIf=\\\"hasExtraItems\\\"\\n    [expanded]=\\\"expanded\\\"\\n>\\n    <div\\n        tuiGroup\\n        class=\\\"t-extra-items\\\"\\n    >\\n        <ng-container *ngFor=\\\"let item of items; let index = index\\\">\\n            <ng-container\\n                *ngIf=\\\"max && index >= max\\\"\\n                [ngTemplateOutlet]=\\\"item\\\"\\n            />\\n        </ng-container>\\n    </div>\\n</tui-expand>\\n<div\\n    *ngIf=\\\"hasExtraItems\\\"\\n    class=\\\"t-bottom\\\"\\n    [class.t-bottom_collapsed]=\\\"!expanded\\\"\\n>\\n    <button\\n        appearance=\\\"outline\\\"\\n        size=\\\"m\\\"\\n        tuiButton\\n        type=\\\"button\\\"\\n        class=\\\"t-button\\\"\\n        (click)=\\\"toggle()\\\"\\n    >\\n        {{ (expanded ? hideText$ : showAllText$) | async }}\\n    </button>\\n</div>\\n\", styles: [\"tui-files{inline-size:100%;overflow:hidden;border-radius:var(--tui-radius-m)}tui-files:empty:empty{display:none}tui-files .t-files{position:relative;display:block;inline-size:100%;block-size:100%;border-radius:var(--tui-radius-m);overflow:hidden}tui-files .t-button{inline-size:100%;border-radius:inherit}tui-files .t-bottom{z-index:3;inline-size:100%;background:var(--tui-background-base);-webkit-mask:none!important;mask:none!important}tui-files .t-bottom_collapsed{box-shadow:var(--tui-shadow-popup);margin-top:-1.5rem}tui-files .t-extra-items{inline-size:100%}tui-files .t-extra-items>*{border-radius:0!important}\\n\"] }]\n        }], propDecorators: { items: [{\n                type: ContentChildren,\n                args: [TuiItem, { read: TemplateRef }]\n            }], max: [{\n                type: Input\n            }], expanded: [{\n                type: Input\n            }], expandedChange: [{\n                type: Output\n            }] } });\n\nclass TuiInputFilesContent {\n    constructor() {\n        this.breakpoint$ = inject(TuiBreakpointService);\n        this.text$ = inject(TUI_INPUT_FILE_TEXTS);\n        this.context = injectContext();\n        this.component = inject(TuiInputFiles);\n    }\n    get link$() {\n        return this.computeLink$(this.context.$implicit, !!this.component.input?.input.multiple);\n    }\n    get label$() {\n        return this.computeLabel$(this.context.$implicit, !!this.component.input?.input.multiple);\n    }\n    computeLink$(fileDragged, multiple) {\n        return fileDragged\n            ? of('')\n            : this.text$.pipe(map((t) => (multiple ? t.defaultLinkMultiple : t.defaultLinkSingle)));\n    }\n    computeLabel$(fileDragged, multiple) {\n        return fileDragged\n            ? combineLatest([this.breakpoint$, this.text$]).pipe(map(([breakpoint, text]) => {\n                if (breakpoint === 'mobile') {\n                    return '';\n                }\n                return multiple ? text.dropMultiple : text.drop;\n            }))\n            : combineLatest([this.breakpoint$, this.text$]).pipe(map(([breakpoint, text]) => {\n                if (breakpoint === 'mobile') {\n                    return '';\n                }\n                return multiple\n                    ? text.defaultLabelMultiple\n                    : text.defaultLabelSingle;\n            }));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputFilesContent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputFilesContent, isStandalone: true, selector: \"ng-component\", ngImport: i0, template: `\n        <a tuiLink>{{ link$ | async }}</a>\n        {{ label$ | async }}\n    `, isInline: true, dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: TuiLink, selector: \"a[tuiLink], button[tuiLink]\", inputs: [\"pseudo\"] }], changeDetection: i0.ChangeDetectionStrategy.Default }); }\n}\n__decorate([\n    tuiPure\n], TuiInputFilesContent.prototype, \"computeLink$\", null);\n__decorate([\n    tuiPure\n], TuiInputFilesContent.prototype, \"computeLabel$\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputFilesContent, decorators: [{\n            type: Component,\n            args: [{\n                    standalone: true,\n                    imports: [AsyncPipe, TuiLink],\n                    template: `\n        <a tuiLink>{{ link$ | async }}</a>\n        {{ label$ | async }}\n    `,\n                    // eslint-disable-next-line @angular-eslint/prefer-on-push-component-change-detection\n                    changeDetection: ChangeDetectionStrategy.Default,\n                }]\n        }], propDecorators: { computeLink$: [], computeLabel$: [] } });\n\nconst TUI_INPUT_FILES_DEFAULT_OPTIONS = {\n    accept: '',\n    multiple: false,\n    size: 'm',\n    maxFileSize: 30 * 1024 * 1024, // 30 MiB\n};\n/**\n * Default parameters for input files component\n */\nconst TUI_INPUT_FILES_OPTIONS = tuiCreateToken(TUI_INPUT_FILES_DEFAULT_OPTIONS);\nfunction tuiInputFilesOptionsProvider(options) {\n    return tuiProvideOptions(TUI_INPUT_FILES_OPTIONS, options, TUI_INPUT_FILES_DEFAULT_OPTIONS);\n}\n\nclass TuiInputFilesValidator extends TuiValidator {\n    constructor() {\n        super(...arguments);\n        this.options = inject(TUI_INPUT_FILES_OPTIONS);\n        this.accept = this.options.accept;\n        this.maxFileSize = this.options.maxFileSize;\n    }\n    ngOnChanges() {\n        this.update();\n    }\n    ngOnInit() {\n        this.update();\n    }\n    update() {\n        this.validate =\n            Validators.compose([\n                tuiCreateFileFormatValidator(this.accept),\n                tuiCreateFileSizeValidator(this.maxFileSize),\n            ]) || Validators.nullValidator;\n        this.onChange();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputFilesValidator, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputFilesValidator, isStandalone: true, inputs: { accept: \"accept\", maxFileSize: \"maxFileSize\" }, host: { properties: { \"accept\": \"accept\" } }, providers: [tuiProvide(NG_VALIDATORS, TuiInputFilesValidator, true)], exportAs: [\"tuiInputFilesValidator\"], usesInheritance: true, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputFilesValidator, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    inputs: ['accept', 'maxFileSize'],\n                    providers: [tuiProvide(NG_VALIDATORS, TuiInputFilesValidator, true)],\n                    exportAs: 'tuiInputFilesValidator',\n                    host: {\n                        '[accept]': 'accept',\n                    },\n                }]\n        }] });\n\nclass TuiInputFilesDirective extends TuiControl {\n    constructor() {\n        super(...arguments);\n        this.host = inject(forwardRef(() => TuiInputFiles));\n        this.reject = timer(0, tuiZonefreeScheduler()).pipe(switchMap(() => tuiControlValue(this.control.control)), map(() => tuiFilesRejected(this.control.control)), filter(({ length }) => !!length));\n        this.appearance = 'file';\n        this.input = tuiInjectElement();\n    }\n    process(files) {\n        const fileOrFiles = this.input.multiple\n            ? [...toArray(this.value()), ...Array.from(files)]\n            : files[0];\n        if (fileOrFiles) {\n            this.onChange(fileOrFiles);\n        }\n    }\n    onClick(event) {\n        if (this.input.readOnly) {\n            event.preventDefault();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputFilesDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputFilesDirective, isStandalone: true, selector: \"input[tuiInputFiles]\", outputs: { reject: \"reject\" }, host: { attributes: { \"title\": \"\", \"type\": \"file\" }, listeners: { \"blur\": \"onTouched()\", \"click\": \"onClick($event)\" }, properties: { \"disabled\": \"disabled()\" } }, providers: [\n            tuiAsControl(TuiInputFilesDirective),\n            tuiAppearanceOptionsProvider(TuiInputFilesDirective),\n        ], usesInheritance: true, hostDirectives: [{ directive: i1$2.TuiNativeValidator }, { directive: i1.TuiWithAppearance }, { directive: TuiInputFilesValidator, inputs: [\"accept\", \"accept\", \"maxFileSize\", \"maxFileSize\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputFilesDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'input[tuiInputFiles]',\n                    providers: [\n                        tuiAsControl(TuiInputFilesDirective),\n                        tuiAppearanceOptionsProvider(TuiInputFilesDirective),\n                    ],\n                    hostDirectives: [\n                        TuiNativeValidator,\n                        TuiWithAppearance,\n                        {\n                            directive: TuiInputFilesValidator,\n                            inputs: ['accept', 'maxFileSize'],\n                        },\n                    ],\n                    host: {\n                        title: '',\n                        type: 'file',\n                        '[disabled]': 'disabled()',\n                        '(blur)': 'onTouched()',\n                        '(click)': 'onClick($event)',\n                    },\n                }]\n        }], propDecorators: { reject: [{\n                type: Output\n            }] } });\nfunction toArray(value) {\n    return value ? coerceArray(value) : EMPTY_ARRAY;\n}\n\nclass TuiInputFiles {\n    constructor() {\n        this.content = new PolymorpheusComponent(TuiInputFilesContent);\n    }\n    get fileDragged() {\n        return !!this.files && !this.input?.disabled();\n    }\n    onFilesSelected(input) {\n        if (!input?.files) {\n            return;\n        }\n        this.input?.process(input.files);\n        input.value = '';\n    }\n    onDropped({ dataTransfer }) {\n        this.files = null;\n        if (dataTransfer?.files && !this.input?.disabled()) {\n            this.input?.process(dataTransfer.files);\n        }\n    }\n    onDrag(dataTransfer) {\n        this.files = dataTransfer?.files;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputFiles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiInputFiles, isStandalone: true, selector: \"label[tuiInputFiles]\", host: { listeners: { \"dragover.prevent.zoneless\": \"0\", \"drop.prevent\": \"onDropped($event)\", \"dragenter\": \"onDrag($event.dataTransfer)\", \"dragleave\": \"onDrag(null)\", \"change\": \"onFilesSelected($event.target)\" }, properties: { \"class._dragged\": \"fileDragged\" } }, queries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true }, { propertyName: \"input\", first: true, predicate: TuiInputFilesDirective, descendants: true }], ngImport: i0, template: `\n        <ng-content />\n        <span\n            *polymorpheusOutlet=\"\n                template || content as text;\n                context: {$implicit: fileDragged}\n            \"\n        >\n            {{ text }}\n        </span>\n    `, isInline: true, styles: [\"label[tuiInputFiles]{position:relative;display:flex;min-block-size:var(--tui-height-l);justify-content:center;align-items:center;text-align:center;border-radius:var(--tui-radius-m);font:var(--tui-font-text-m);overflow-wrap:break-word;padding:0 .5rem}label[tuiInputFiles]>:not(input){position:relative;pointer-events:none}label[tuiInputFiles] input{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;color:transparent;cursor:pointer}label[tuiInputFiles] input:disabled~*{opacity:var(--tui-disabled-opacity)}label[tuiInputFiles] input::-webkit-file-upload-button{display:none}label[tuiInputFiles] input::file-selector-button{display:none}*:disabled label[tuiInputFiles]{pointer-events:none}[tuiAppearance][data-appearance=file]{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;border-radius:inherit;box-sizing:border-box;border:1px dashed var(--tui-text-action);outline:none}tui-root._mobile [tuiAppearance][data-appearance=file]{border-style:solid}[tuiInputFiles]._dragged [tuiAppearance][data-appearance=file]{background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=file]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=file]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}}[tuiAppearance][data-appearance=file][data-state=hover]{background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=file][tuiWrapper]:hover:not(._no-hover),[tuiAppearance][data-appearance=file][tuiWrapper][data-state=hover]{background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}}[tuiAppearance][data-appearance=file]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file][data-state=active]{background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file][tuiWrapper]:active:not(._no-active),[tuiAppearance][data-appearance=file][tuiWrapper][data-state=active],[tuiAppearance][data-appearance=file][tuiWrapper][data-state=active]:hover{background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file]:disabled:not([data-state]),[tuiAppearance][data-appearance=file][data-state=disabled]{background:transparent;border-color:var(--tui-text-tertiary)}[tuiAppearance][data-appearance=file][tuiWrapper]:disabled:not([data-state]),[tuiAppearance][data-appearance=file][tuiWrapper][data-state=disabled]{background:transparent;border-color:var(--tui-text-tertiary)}[tuiAppearance][data-appearance=file]:focus-visible:not([data-focus=false]){border:.125rem solid var(--tui-border-focus)}[tuiAppearance][data-appearance=file][data-focus=true]{border:.125rem solid var(--tui-border-focus)}[tuiAppearance][data-appearance=file][tuiWrapper]:not(._focused):has(:focus-visible),[tuiAppearance][data-appearance=file][tuiWrapper]._focused{border:.125rem solid var(--tui-border-focus)}[tuiAppearance][data-appearance=file]:invalid{border-color:var(--tui-status-negative)!important}\\n\"], dependencies: [{ kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiInputFiles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'label[tuiInputFiles]', imports: [PolymorpheusOutlet, PolymorpheusTemplate], template: `\n        <ng-content />\n        <span\n            *polymorpheusOutlet=\"\n                template || content as text;\n                context: {$implicit: fileDragged}\n            \"\n        >\n            {{ text }}\n        </span>\n    `, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '(dragover.prevent.zoneless)': '0',\n                        '(drop.prevent)': 'onDropped($event)',\n                        '(dragenter)': 'onDrag($event.dataTransfer)',\n                        '(dragleave)': 'onDrag(null)',\n                        '[class._dragged]': 'fileDragged',\n                        '(change)': 'onFilesSelected($event.target)',\n                    }, styles: [\"label[tuiInputFiles]{position:relative;display:flex;min-block-size:var(--tui-height-l);justify-content:center;align-items:center;text-align:center;border-radius:var(--tui-radius-m);font:var(--tui-font-text-m);overflow-wrap:break-word;padding:0 .5rem}label[tuiInputFiles]>:not(input){position:relative;pointer-events:none}label[tuiInputFiles] input{position:absolute;top:0;left:0;inline-size:100%;block-size:100%;color:transparent;cursor:pointer}label[tuiInputFiles] input:disabled~*{opacity:var(--tui-disabled-opacity)}label[tuiInputFiles] input::-webkit-file-upload-button{display:none}label[tuiInputFiles] input::file-selector-button{display:none}*:disabled label[tuiInputFiles]{pointer-events:none}[tuiAppearance][data-appearance=file]{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;border-radius:inherit;box-sizing:border-box;border:1px dashed var(--tui-text-action);outline:none}tui-root._mobile [tuiAppearance][data-appearance=file]{border-style:solid}[tuiInputFiles]._dragged [tuiAppearance][data-appearance=file]{background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=file]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=file]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}}[tuiAppearance][data-appearance=file][data-state=hover]{background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=file][tuiWrapper]:hover:not(._no-hover),[tuiAppearance][data-appearance=file][tuiWrapper][data-state=hover]{background:var(--tui-background-neutral-1);border-color:var(--tui-text-action-hover)}}[tuiAppearance][data-appearance=file]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file][data-state=active]{background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file][tuiWrapper]:active:not(._no-active),[tuiAppearance][data-appearance=file][tuiWrapper][data-state=active],[tuiAppearance][data-appearance=file][tuiWrapper][data-state=active]:hover{background:var(--tui-background-neutral-1-hover)}[tuiAppearance][data-appearance=file]:disabled:not([data-state]),[tuiAppearance][data-appearance=file][data-state=disabled]{background:transparent;border-color:var(--tui-text-tertiary)}[tuiAppearance][data-appearance=file][tuiWrapper]:disabled:not([data-state]),[tuiAppearance][data-appearance=file][tuiWrapper][data-state=disabled]{background:transparent;border-color:var(--tui-text-tertiary)}[tuiAppearance][data-appearance=file]:focus-visible:not([data-focus=false]){border:.125rem solid var(--tui-border-focus)}[tuiAppearance][data-appearance=file][data-focus=true]{border:.125rem solid var(--tui-border-focus)}[tuiAppearance][data-appearance=file][tuiWrapper]:not(._focused):has(:focus-visible),[tuiAppearance][data-appearance=file][tuiWrapper]._focused{border:.125rem solid var(--tui-border-focus)}[tuiAppearance][data-appearance=file]:invalid{border-color:var(--tui-status-negative)!important}\\n\"] }]\n        }], propDecorators: { template: [{\n                type: ContentChild,\n                args: [TemplateRef]\n            }], input: [{\n                type: ContentChild,\n                args: [TuiInputFilesDirective]\n            }] } });\n\nclass TuiFileRejectedPipe {\n    constructor() {\n        this.options = inject(TUI_INPUT_FILES_OPTIONS);\n        this.formatSize = inject(TUI_FILE_OPTIONS).formatSize;\n        this.locale = inject(LOCALE_ID);\n        this.text$ = inject(TUI_INPUT_FILE_TEXTS);\n        this.unit$ = inject(TUI_DIGITAL_INFORMATION_UNITS);\n    }\n    transform(file, { accept = this.options.accept, maxFileSize = this.options.maxFileSize, } = this.options) {\n        const sizeValidator = tuiCreateFileSizeValidator(maxFileSize);\n        const formatValidator = tuiCreateFileFormatValidator(accept);\n        const control = new FormControl(file);\n        return combineLatest([this.text$, this.unit$]).pipe(map(([{ maxSizeRejectionReason, formatRejectionReason }, units]) => {\n            if (file && formatValidator(control)) {\n                return {\n                    name: file.name,\n                    size: file.size,\n                    content: formatRejectionReason,\n                };\n            }\n            if (file && sizeValidator(control)) {\n                return {\n                    name: file.name,\n                    size: file.size,\n                    content: `${maxSizeRejectionReason}${CHAR_NO_BREAK_SPACE}${this.formatSize(units, maxFileSize, this.locale)}`,\n                };\n            }\n            return null;\n        }));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFileRejectedPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFileRejectedPipe, isStandalone: true, name: \"tuiFileRejected\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFileRejectedPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'tuiFileRejected',\n                }]\n        }] });\n\nconst TuiFiles = [\n    TuiItem,\n    TuiFile,\n    TuiInputFiles,\n    TuiFilesComponent,\n    TuiFileRejectedPipe,\n    TuiInputFilesDirective,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_FILE_DEFAULT_OPTIONS, TUI_FILE_OPTIONS, TUI_FORMAT_ERROR, TUI_INPUT_FILES_DEFAULT_OPTIONS, TUI_INPUT_FILES_OPTIONS, TUI_SIZE_ERROR, TuiFile, TuiFileRejectedPipe, TuiFiles, TuiFilesComponent, TuiInputFiles, TuiInputFilesContent, TuiInputFilesDirective, TuiInputFilesValidator, tuiCreateFileFormatValidator, tuiCreateFileSizeValidator, tuiFileOptionsProvider, tuiFilesAccepted, tuiFilesRejected, tuiFormatSize, tuiInputFilesOptionsProvider };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,EAAEC,SAAS,QAAQ,iBAAiB;AACzD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,UAAU,EAAEC,YAAY,EAAEC,IAAI,QAAQ,eAAe;AAC9M,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,OAAO,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,mCAAmC;AAC1G,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,OAAO,KAAKC,EAAE,MAAM,sCAAsC;AAC1D,SAASC,4BAA4B,EAAEC,aAAa,EAAEC,iBAAiB,QAAQ,sCAAsC;AACrH,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,6BAA6B,EAAEC,cAAc,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,oBAAoB,QAAQ,sBAAsB;AAC5I,SAASC,kBAAkB,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,qBAAqB,QAAQ,wBAAwB;AACvH,SAASC,GAAG,EAAEC,EAAE,EAAEC,aAAa,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,QAAQ,MAAM;AACvE,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,WAAW,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,yBAAyB;AACvF,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,OAAO,KAAKC,IAAI,MAAM,iCAAiC;AACvD,SAASC,uBAAuB,EAAEC,QAAQ,QAAQ,iCAAiC;AACnF,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,UAAU,EAAEC,YAAY,QAAQ,uBAAuB;AAChE,OAAO,KAAKC,IAAI,MAAM,2CAA2C;AACjE,SAASC,kBAAkB,QAAQ,2CAA2C;AAC9E,SAASC,oBAAoB,EAAEC,eAAe,QAAQ,2BAA2B;AACjF,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,UAAU,EAAEC,aAAa,EAAEC,WAAW,QAAQ,gBAAgB;AACvE,SAASC,YAAY,QAAQ,oCAAoC;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,SAAAE,gCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAwJmC1E,EAAE,CAAA4E,uBAAA,EAC4kB,CAAC;IAD/kB5E,EAAE,CAAA6E,MAAA,EACsmB,CAAC;IADzmB7E,EAAE,CAAA8E,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAAFhF,EAAE,CAAAiF,SAAA,CACsmB,CAAC;IADzmBjF,EAAE,CAAAkF,kBAAA,MAAAH,OAAA,KACsmB,CAAC;EAAA;AAAA;AAAA,SAAAI,uBAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADzmB1E,EAAE,CAAAoF,cAAA,aAC09B,CAAC;IAD79BpF,EAAE,CAAA6E,MAAA,EACggC,CAAC;IADngC7E,EAAE,CAAAqF,YAAA,CACsgC,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAY,WAAA,GAAAX,GAAA,CAAAY,IAAA;IADzgCvF,EAAE,CAAAiF,SAAA,CACggC,CAAC;IADngCjF,EAAE,CAAAkF,kBAAA,MAAAI,WAAA,KACggC,CAAC;EAAA;AAAA;AAAA,SAAAE,uCAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADngC1E,EAAE,CAAA4E,uBAAA,EAC+qC,CAAC;IADlrC5E,EAAE,CAAA6E,MAAA,EACitC,CAAC;IADptC7E,EAAE,CAAA8E,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAe,OAAA,GAAAd,GAAA,CAAAK,kBAAA;IAAFhF,EAAE,CAAAiF,SAAA,CACitC,CAAC;IADptCjF,EAAE,CAAAkF,kBAAA,MAAAO,OAAA,KACitC,CAAC;EAAA;AAAA;AAAA,SAAAC,wBAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADptC1E,EAAE,CAAAoF,cAAA,aAC+mC,CAAC;IADlnCpF,EAAE,CAAA2F,UAAA,IAAAH,sCAAA,yBAC+qC,CAAC;IADlrCxF,EAAE,CAAAqF,YAAA,CAC4uC,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAkB,UAAA,GAAAjB,GAAA,CAAAY,IAAA;IAD/uCvF,EAAE,CAAAiF,SAAA,CACqqC,CAAC;IADxqCjF,EAAE,CAAA6F,UAAA,uBAAAD,UACqqC,CAAC;EAAA;AAAA;AAAA,SAAAE,0CAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqB,GAAA,GADxqC/F,EAAE,CAAAgG,gBAAA;IAAFhG,EAAE,CAAAoF,cAAA,gBAC6mD,CAAC;IADhnDpF,EAAE,CAAAiG,UAAA,2BAAAC,0EAAA;MAAFlG,EAAE,CAAAmG,aAAA,CAAAJ,GAAA;MAAA,MAAAK,MAAA,GAAFpG,EAAE,CAAAqG,aAAA;MAAA,OAAFrG,EAAE,CAAAsG,WAAA,CAC0iDF,MAAA,CAAAG,MAAA,CAAAC,IAAA,CAAY,CAAC;IAAA,CAAC,CAAC,wCAAAC,uFAAA;MAD3jDzG,EAAE,CAAAmG,aAAA,CAAAJ,GAAA;MAAA,OAAF/F,EAAE,CAAAsG,WAAA,CACmmD,CAAC;IAAA,CAAE,CAAC;IADzmDtG,EAAE,CAAA6E,MAAA,EAC+oD,CAAC;IADlpD7E,EAAE,CAAAqF,YAAA,CACwpD,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAgC,QAAA,GAAA/B,GAAA,CAAAY,IAAA;IAAA,MAAAa,MAAA,GAD3pDpG,EAAE,CAAAqG,aAAA;IAAFrG,EAAE,CAAA6F,UAAA,cAAAO,MAAA,CAAAO,KAAA,CAAAC,KAC6gD,CAAC;IADhhD5G,EAAE,CAAAiF,SAAA,CAC+oD,CAAC;IADlpDjF,EAAE,CAAAkF,kBAAA,MAAAwB,QAAA,CAAAH,MAAA,KAC+oD,CAAC;EAAA;AAAA;AAAA,SAAAM,iCAAAnC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADlpD1E,EAAE,CAAA4E,uBAAA,EAC8yC,CAAC;IADjzC5E,EAAE,CAAA2F,UAAA,IAAAG,yCAAA,oBAC6mD,CAAC;IADhnD9F,EAAE,CAAA8G,MAAA;IAAF9G,EAAE,CAAA8E,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAA0B,MAAA,GAAFpG,EAAE,CAAAqG,aAAA;IAAFrG,EAAE,CAAAiF,SAAA,CACg2C,CAAC;IADn2CjF,EAAE,CAAA6F,UAAA,SAAF7F,EAAE,CAAA+G,WAAA,OAAAX,MAAA,CAAAY,UAAA,CACg2C,CAAC;EAAA;AAAA;AAAA,SAAAC,sCAAAvC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADn2C1E,EAAE,CAAAkH,SAAA,aAC41D,CAAC;EAAA;EAAA,IAAAxC,EAAA;IAAA,MAAA0B,MAAA,GAD/1DpG,EAAE,CAAAqG,aAAA;IAAFrG,EAAE,CAAA6F,UAAA,QAAAO,MAAA,CAAAe,OAAA,EAAFnH,EAAE,CAAAoH,aACo1D,CAAC;EAAA;AAAA;AAAA,SAAAC,2DAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADv1D1E,EAAE,CAAAkH,SAAA,oBACygE,CAAC;EAAA;EAAA,IAAAxC,EAAA;IAAA,MAAA0B,MAAA,GAD5gEpG,EAAE,CAAAqG,aAAA;IAAFrG,EAAE,CAAA6F,UAAA,iBAAAO,MAAA,CAAAkB,KAC6/D,CAAC;EAAA;AAAA;AAAA,SAAAC,8CAAA7C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADhgE1E,EAAE,CAAA2F,UAAA,IAAA0B,0DAAA,wBACygE,CAAC;EAAA;EAAA,IAAA3C,EAAA;IAD5gE1E,EAAE,CAAAqG,aAAA;IAAA,MAAAmB,MAAA,GAAFxH,EAAE,CAAAyH,WAAA;IAAA,MAAArB,MAAA,GAAFpG,EAAE,CAAAqG,aAAA;IAAFrG,EAAE,CAAA6F,UAAA,SAAAO,MAAA,CAAAsB,SAC66D,CAAC,aAAAF,MAAO,CAAC;EAAA;AAAA;AAAA,SAAAG,yDAAAjD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADx7D1E,EAAE,CAAAkH,SAAA,kBAC60E,CAAC;EAAA;EAAA,IAAAxC,EAAA;IAAA,MAAAkD,MAAA,GAAAjD,GAAA,CAAAK,kBAAA;IAAA,MAAAoB,MAAA,GADh1EpG,EAAE,CAAAqG,aAAA;IAAFrG,EAAE,CAAA6H,WAAA,iBAAAzB,MAAA,CAAAkB,KAAA,IAAAlB,MAAA,CAAA0B,SAC4uE,CAAC,iBAAA1B,MAAA,CAAA2B,OAA6C,CAAC;IAD7xE/H,EAAE,CAAA6F,UAAA,SAAA+B,MAAA,CAAAI,QAAA,EACi0E,CAAC;EAAA;AAAA;AAAA,SAAAC,8CAAAvD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADp0E1E,EAAE,CAAA2F,UAAA,IAAAgC,wDAAA,sBAC60E,CAAC;EAAA;EAAA,IAAAjD,EAAA;IAAA,MAAA0B,MAAA,GADh1EpG,EAAE,CAAAqG,aAAA;IAAFrG,EAAE,CAAA6F,UAAA,uBAAAO,MAAA,CAAA8B,IACinE,CAAC,8BADpnElI,EAAE,CAAAmI,eAAA,IAAA7D,GAAA,EAAA8B,MAAA,CAAAgC,IAAA,CACmpE,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAA3D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADtpE1E,EAAE,CAAA2F,UAAA,IAAAsB,qCAAA,iBAC41D,CAAC,IAAAM,6CAAA,gCAD/1DvH,EAAE,CAAAsI,sBACu3D,CAAC,IAAAL,6CAAA,gCAD13DjI,EAAE,CAAAsI,sBACqjE,CAAC;EAAA;EAAA,IAAA5D,EAAA;IAAA,MAAA6D,UAAA,GADxjEvI,EAAE,CAAAyH,WAAA;IAAA,MAAArB,MAAA,GAAFpG,EAAE,CAAAqG,aAAA;IAAFrG,EAAE,CAAA6F,UAAA,SAAAO,MAAA,CAAAe,OACmvD,CAAC,aAAAoB,UAAU,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAA9D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADjwD1E,EAAE,CAAAyI,kBAAA,EAkE0G,CAAC;EAAA;AAAA;AAAA,SAAAC,yDAAAhE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlE7G1E,EAAE,CAAAyI,kBAAA,KAkEiS,CAAC;EAAA;EAAA,IAAA/D,EAAA;IAAA,MAAAiE,OAAA,GAlEpS3I,EAAE,CAAAqG,aAAA,GAAA7B,SAAA;IAAFxE,EAAE,CAAA6F,UAAA,qBAAA8C,OAkEyR,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAAlE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlE5R1E,EAAE,CAAA4E,uBAAA,EAkE0L,CAAC;IAlE7L5E,EAAE,CAAA2F,UAAA,IAAA+C,wDAAA,yBAkEiS,CAAC;IAlEpS1I,EAAE,CAAA8E,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAmE,QAAA,GAAAlE,GAAA,CAAAmE,KAAA;IAAA,MAAAC,MAAA,GAAF/I,EAAE,CAAAqG,aAAA;IAAFrG,EAAE,CAAAiF,SAAA,CAkEkP,CAAC;IAlErPjF,EAAE,CAAA6F,UAAA,UAAAkD,MAAA,CAAAC,GAAA,IAAAH,QAAA,GAAAE,MAAA,CAAAC,GAkEkP,CAAC;EAAA;AAAA;AAAA,SAAAC,sEAAAvE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlErP1E,EAAE,CAAAyI,kBAAA,KAkE+oB,CAAC;EAAA;EAAA,IAAA/D,EAAA;IAAA,MAAAwE,OAAA,GAlElpBlJ,EAAE,CAAAqG,aAAA,GAAA7B,SAAA;IAAFxE,EAAE,CAAA6F,UAAA,qBAAAqD,OAkE+nB,CAAC;EAAA;AAAA;AAAA,SAAAC,uDAAAzE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlEloB1E,EAAE,CAAA4E,uBAAA,EAkEwgB,CAAC;IAlE3gB5E,EAAE,CAAA2F,UAAA,IAAAsD,qEAAA,yBAkE+oB,CAAC;IAlElpBjJ,EAAE,CAAA8E,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAA0E,QAAA,GAAAzE,GAAA,CAAAmE,KAAA;IAAA,MAAAC,MAAA,GAAF/I,EAAE,CAAAqG,aAAA;IAAFrG,EAAE,CAAAiF,SAAA,CAkEglB,CAAC;IAlEnlBjF,EAAE,CAAA6F,UAAA,SAAAkD,MAAA,CAAAC,GAAA,IAAAI,QAAA,IAAAL,MAAA,CAAAC,GAkEglB,CAAC;EAAA;AAAA;AAAA,SAAAK,wCAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlEnlB1E,EAAE,CAAAoF,cAAA,mBAkE4X,CAAC,YAAmE,CAAC;IAlEncpF,EAAE,CAAA2F,UAAA,IAAAwD,sDAAA,yBAkEwgB,CAAC;IAlE3gBnJ,EAAE,CAAAqF,YAAA,CAkEorB,CAAC,CAAc,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAqE,MAAA,GAlEtsB/I,EAAE,CAAAqG,aAAA;IAAFrG,EAAE,CAAA6F,UAAA,aAAAkD,MAAA,CAAAO,QAkEyX,CAAC;IAlE5XtJ,EAAE,CAAAiF,SAAA,EAkEof,CAAC;IAlEvfjF,EAAE,CAAA6F,UAAA,YAAAkD,MAAA,CAAAQ,KAkEof,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAA9E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+E,GAAA,GAlEvfzJ,EAAE,CAAAgG,gBAAA;IAAFhG,EAAE,CAAAoF,cAAA,YAkE+yB,CAAC,eAA6K,CAAC;IAlEh+BpF,EAAE,CAAAiG,UAAA,mBAAAyD,yDAAA;MAAF1J,EAAE,CAAAmG,aAAA,CAAAsD,GAAA;MAAA,MAAAV,MAAA,GAAF/I,EAAE,CAAAqG,aAAA;MAAA,OAAFrG,EAAE,CAAAsG,WAAA,CAkE68ByC,MAAA,CAAAY,MAAA,CAAO,CAAC;IAAA,CAAC,CAAC;IAlEz9B3J,EAAE,CAAA6E,MAAA,EAkEgiC,CAAC;IAlEniC7E,EAAE,CAAA8G,MAAA;IAAF9G,EAAE,CAAAqF,YAAA,CAkEyiC,CAAC,CAAO,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAqE,MAAA,GAlEpjC/I,EAAE,CAAAqG,aAAA;IAAFrG,EAAE,CAAA6H,WAAA,wBAAAkB,MAAA,CAAAO,QAkE4yB,CAAC;IAlE/yBtJ,EAAE,CAAAiF,SAAA,EAkEgiC,CAAC;IAlEniCjF,EAAE,CAAAkF,kBAAA,MAAFlF,EAAE,CAAA+G,WAAA,OAAAgC,MAAA,CAAAO,QAAA,GAAAP,MAAA,CAAAa,SAAA,GAAAb,MAAA,CAAAc,YAAA,MAkEgiC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,8BAAArF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlEniC1E,EAAE,CAAAoF,cAAA,UA8R/F,CAAC;IA9R4FpF,EAAE,CAAA6E,MAAA,EAgShG,CAAC;IAhS6F7E,EAAE,CAAAqF,YAAA,CAgSzF,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAK,OAAA,GAAAJ,GAAA,CAAAK,kBAAA;IAhSsFhF,EAAE,CAAAiF,SAAA,CAgShG,CAAC;IAhS6FjF,EAAE,CAAAkF,kBAAA,MAAAH,OAAA,KAgShG,CAAC;EAAA;AAAA;AAtbR,MAAMiF,cAAc,GAAG,SAAS;AAChC,MAAMC,gBAAgB,GAAG,WAAW;AACpC,SAASC,0BAA0BA,CAAC9B,IAAI,EAAE;EACtC,OAAO,CAAC;IAAE+B;EAAM,CAAC,KAAK;IAClB,MAAMC,KAAK,GAAGD,KAAK,IAAIrH,WAAW,CAACqH,KAAK,CAAC;IACzC,MAAM3F,SAAS,GAAG2F,KAAK,IAAIC,KAAK,EAAExH,MAAM,CAAEyH,IAAI,IAAKA,IAAI,CAACjC,IAAI,GAAGA,IAAI,CAAC;IACpE,OAAO5D,SAAS,EAAE8F,MAAM,GAAG;MAAE,CAACN,cAAc,GAAG;QAAExF,SAAS;QAAE4D;MAAK;IAAE,CAAC,GAAG,IAAI;EAC/E,CAAC;AACL;AACA,SAASmC,4BAA4BA,CAACC,MAAM,EAAE;EAC1C,OAAO,CAAC;IAAEL;EAAM,CAAC,KAAK;IAClB,MAAMC,KAAK,GAAGD,KAAK,IAAIrH,WAAW,CAACqH,KAAK,CAAC;IACzC,MAAMM,OAAO,GAAGC,SAAS,CAACF,MAAM,CAAC;IACjC,MAAMhG,SAAS,GAAG2F,KAAK,IAAIC,KAAK,EAAExH,MAAM,CAAEyH,IAAI,IAAK,CAACM,WAAW,CAACN,IAAI,EAAEI,OAAO,CAAC,CAAC;IAC/E,OAAOjG,SAAS,EAAE8F,MAAM,IAAIE,MAAM,GAAG;MAAE,CAACP,gBAAgB,GAAG;QAAEzF;MAAU;IAAE,CAAC,GAAG,IAAI;EACrF,CAAC;AACL;AACA,SAASmG,WAAWA,CAAC;EAAEC,IAAI;EAAEC;AAAK,CAAC,EAAEJ,OAAO,EAAE;EAC1C,MAAMK,SAAS,GAAG,IAAI,CAACF,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC,EAAE;EACnE,OAAOR,OAAO,CAACS,IAAI,CAAEC,MAAM,IAAKA,MAAM,KAAKL,SAAS,IAChDK,MAAM,KAAKN,IAAI,IACdM,MAAM,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IACzBF,IAAI,EAAEE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKI,MAAM,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC;AAC1D;AACA,SAASL,SAASA,CAACF,MAAM,EAAE;EACvB,OAAOA,MAAM,CACRS,WAAW,CAAC,CAAC,CACbF,KAAK,CAAC,GAAG,CAAC,CACVxI,GAAG,CAAE4I,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,CAAC,CAACH,WAAW,CAAC,CAAC,CAAC;AACrD;AAEA,MAAMI,aAAa,GAAG,IAAI;AAC1B,MAAMC,aAAa,GAAG,IAAI,GAAGD,aAAa;AAC1C,SAASE,gBAAgBA,CAACC,OAAO,EAAE;EAC/B,MAAML,MAAM,GAAGK,OAAO,EAAEC,QAAQ,CAACxB,gBAAgB,CAAC,EAAEzF,SAAS,IAAI,EAAE;EACnE,MAAM4D,IAAI,GAAGoD,OAAO,EAAEC,QAAQ,CAACzB,cAAc,CAAC,EAAExF,SAAS,IAAI,EAAE;EAC/D,OAAOkH,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,CAAC,GAAGT,MAAM,EAAE,GAAG/C,IAAI,CAAC,CAAC,CAAC;AACpD;AACA,SAASyD,gBAAgBA,CAACL,OAAO,EAAE;EAC/B,MAAMrB,KAAK,GAAGqB,OAAO,EAAErB,KAAK,IAAI,EAAE;EAClC,MAAMC,KAAK,GAAGtH,WAAW,CAACqH,KAAK,CAAC;EAChC,MAAM/B,IAAI,GAAGoD,OAAO,EAAEC,QAAQ,CAACzB,cAAc,CAAC,EAAExF,SAAS,IAAI,EAAE;EAC/D,MAAM2G,MAAM,GAAGK,OAAO,EAAEC,QAAQ,CAACxB,gBAAgB,CAAC,EAAEzF,SAAS,IAAI,EAAE;EACnE,OAAO4F,KAAK,CAACxH,MAAM,CAAEyH,IAAI,IAAK,CAACjC,IAAI,CAAC0D,QAAQ,CAACzB,IAAI,CAAC,IAAI,CAACc,MAAM,CAACW,QAAQ,CAACzB,IAAI,CAAC,CAAC;AACjF;AACA,SAAS0B,aAAaA,CAACC,KAAK,EAAE5D,IAAI,EAAE6D,MAAM,EAAE;EACxC,IAAI7D,IAAI,KAAK8D,SAAS,EAAE;IACpB,OAAO,IAAI;EACf;EACA,IAAI9D,IAAI,GAAGiD,aAAa,EAAE;IACtB,OAAO,GAAGjD,IAAI,IAAI4D,KAAK,CAAC,CAAC,CAAC,EAAE;EAChC;EACA,IAAI5D,IAAI,GAAGkD,aAAa,EAAE;IACtB,OAAO,GAAG,CAAClD,IAAI,GAAGiD,aAAa,EAAEc,OAAO,CAAC,CAAC,CAAC,IAAIH,KAAK,CAAC,CAAC,CAAC,EAAE;EAC7D;EACA,OAAO,GAAGjJ,QAAQ,CAACqF,IAAI,GAAGkD,aAAa,EAAE,CAAC,CAAC,CAACc,cAAc,CAACH,MAAM,CAAC,IAAID,KAAK,CAAC,CAAC,CAAC,EAAE;AACpF;AAEA,MAAMK,wBAAwB,GAAG;EAC7BC,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAER,aAAa;EACzBpF,KAAK,EAAE;IACH6F,MAAM,EAAEA,CAAC;MAAEhI;IAAU,CAAC,KAAMA,SAAS,KAAK,GAAG,GAAG,WAAW,GAAG,mBAAoB;IAClFiI,KAAK,EAAE,mBAAmB;IAC1BC,OAAO,EAAE;EACb;AACJ,CAAC;AACD;AACA;AACA;AACA,MAAM,CAACC,gBAAgB,EAAEC,sBAAsB,CAAC,GAAG/J,gBAAgB,CAACwJ,wBAAwB,CAAC;AAE7F,MAAMQ,OAAO,CAAC;EACVC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,SAAS,GAAG9M,MAAM,CAACc,YAAY,CAAC;IACrC,IAAI,CAACiM,OAAO,GAAG/M,MAAM,CAAC0M,gBAAgB,CAAC;IACvC,IAAI,CAACV,MAAM,GAAGhM,MAAM,CAACC,SAAS,CAAC;IAC/B,IAAI,CAAC+M,MAAM,GAAGhN,MAAM,CAAC6B,6BAA6B,CAAC;IACnD,IAAI,CAACoL,GAAG,GAAGjN,MAAM,CAACe,SAAS,CAAC;IAC5B,IAAI,CAAC2F,KAAK,GAAG1G,MAAM,CAAC4B,gBAAgB,CAAC;IACrC,IAAI,CAACmF,UAAU,GAAG/G,MAAM,CAAC8B,cAAc,CAAC;IACxC,IAAI,CAACsI,IAAI,GAAG;MAAEO,IAAI,EAAE;IAAG,CAAC;IACxB,IAAI,CAACuC,KAAK,GAAG,QAAQ;IACrB,IAAI,CAAC/E,IAAI,GAAG,GAAG;IACf,IAAI,CAACgF,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC9G,MAAM,GAAG,IAAIpG,YAAY,CAAC,CAAC;EACpC;EACA,IAAIgH,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACG,KAAK,GAAG,IAAI,CAACgG,aAAa,CAAC,IAAI,CAACjD,IAAI,CAAC,GAAG,EAAE;EAC1D;EACA,IAAI/C,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACc,IAAI,KAAK,GAAG;EAC5B;EACA,IAAIV,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACyF,KAAK,KAAK,SAAS;EACnC;EACA,IAAIpF,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoF,KAAK,KAAK,OAAO;EACjC;EACA,IAAIrF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACqF,KAAK,KAAK,SAAS;EACnC;EACA,IAAII,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACH,UAAU,IAAI,IAAI,CAAC7G,MAAM,CAACiH,QAAQ;EAClD;EACA,IAAItF,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACiF,KAAK,KAAK,SAAS,GAAG,EAAE,GAAG,IAAI,CAACH,OAAO,CAACrG,KAAK,CAAC,IAAI,CAACwG,KAAK,CAAC;EACzE;EACA,IAAIvC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC6C,OAAO,CAAC,IAAI,CAACpD,IAAI,CAAC;EAClC;EACA,IAAIQ,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC6C,OAAO,CAAC,IAAI,CAACrD,IAAI,CAAC;EAClC;EACA,IAAIsD,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACT,KAAK,EAAE,IAAI,CAAC9C,IAAI,EAAE,IAAI,CAACrD,UAAU,CAAC;EACzE;EACA,IAAI6G,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACzD,IAAI,EAAE,IAAI,CAAC4C,MAAM,CAAC;EAC1D;EACAW,iBAAiBA,CAACT,KAAK,EAAE9C,IAAI,EAAErD,UAAU,EAAE;IACvC,OAAOmG,KAAK,KAAK,OAAO,IAAI,CAAC9C,IAAI,CAAC0D,OAAO,GACnC/G,UAAU,CAACgH,IAAI,CAACzL,GAAG,CAAE0L,KAAK,IAAKA,KAAK,CAACC,YAAY,CAAC,CAAC,GACnD1L,EAAE,CAAC,IAAI,CAAC6H,IAAI,CAAC0D,OAAO,IAAI,EAAE,CAAC;EACrC;EACAD,kBAAkBA,CAACzD,IAAI,EAAE4C,MAAM,EAAE;IAC7B,OAAOA,MAAM,CAACe,IAAI,CAACzL,GAAG,CAAEyJ,KAAK,IAAK,IAAI,CAACgB,OAAO,CAACT,UAAU,CAACP,KAAK,EAAE3B,IAAI,CAACjC,IAAI,EAAE,IAAI,CAAC6D,MAAM,CAAC,CAAC,CAAC;EAC9F;EACAqB,aAAaA,CAACjD,IAAI,EAAE;IAChB,IAAIA,IAAI,CAAC8D,GAAG,EAAE;MACV,OAAO9D,IAAI,CAAC8D,GAAG;IACnB;IACA,IAAI,IAAI,CAACjB,GAAG,CAACkB,IAAI,IACb/D,IAAI,YAAY,IAAI,CAAC6C,GAAG,CAACkB,IAAI,IAC7B/D,IAAI,CAACQ,IAAI,EAAEwD,UAAU,CAAC,QAAQ,CAAC,EAAE;MACjC,OAAO,IAAI,CAACtB,SAAS,CAACuB,sBAAsB,CAACC,GAAG,CAACC,eAAe,CAACnE,IAAI,CAAC,CAAC;IAC3E;IACA,OAAO,EAAE;EACb;EACAoD,OAAOA,CAACpD,IAAI,EAAE;IACV,MAAMoE,GAAG,GAAGpE,IAAI,CAACO,IAAI,CAAC8D,WAAW,CAAC,GAAG,CAAC;IACtC;IACA,OAAOD,GAAG,GAAG,CAAC,GAAGpE,IAAI,CAACO,IAAI,CAAC+D,KAAK,CAAC,CAAC,EAAEF,GAAG,CAAC,GAAGpE,IAAI,CAACO,IAAI;EACxD;EACA8C,OAAOA,CAACrD,IAAI,EAAE;IACV,MAAMoE,GAAG,GAAGpE,IAAI,CAACO,IAAI,CAAC8D,WAAW,CAAC,GAAG,CAAC;IACtC;IACA,OAAOD,GAAG,GAAG,CAAC,GAAGpE,IAAI,CAACO,IAAI,CAAC+D,KAAK,CAACF,GAAG,CAAC,GAAG,EAAE;EAC9C;EACA;IAAS,IAAI,CAACG,IAAI,YAAAC,gBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFjC,OAAO;IAAA,CAAmD;EAAE;EACvK;IAAS,IAAI,CAACkC,IAAI,kBAD+E/O,EAAE,CAAAgP,iBAAA;MAAAnE,IAAA,EACJgC,OAAO;MAAAoC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,qBAAA1K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADL1E,EAAE,CAAAqP,WAAA,gBAAA1K,GAAA,CAAAyI,UAAA;QAAA;MAAA;MAAAkC,MAAA;QAAAjF,IAAA;QAAA8C,KAAA;QAAA/E,IAAA;QAAAgF,UAAA;QAAAC,QAAA;QAAAkC,WAAA;MAAA;MAAAC,OAAA;QAAAjJ,MAAA;MAAA;MAAAkJ,UAAA;MAAAC,QAAA,GAAF1P,EAAE,CAAA2P,kBAAA,CACoT,CAAClO,4BAA4B,CAACkL,gBAAgB,CAAC,CAAC,GADtW3M,EAAE,CAAA4P,uBAAA,EACoYpO,EAAE,CAACE,aAAa,IADtZ1B,EAAE,CAAA6P,mBAAA;MAAAC,kBAAA,EAAAzL,GAAA;MAAA0L,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iBAAAzL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1E,EAAE,CAAAoQ,eAAA;UAAFpQ,EAAE,CAAAoF,cAAA,YACsf,CAAC;UADzfpF,EAAE,CAAA2F,UAAA,IAAAlB,+BAAA,yBAC4kB,CAAC;UAD/kBzE,EAAE,CAAAqF,YAAA,CAC6nB,CAAC;UADhoBrF,EAAE,CAAAoF,cAAA,YACwpB,CAAC,YAA2B,CAAC,YAAmF,CAAC;UAD3wBpF,EAAE,CAAA6E,MAAA,EAC0yB,CAAC;UAD7yB7E,EAAE,CAAAqF,YAAA,CACgzB,CAAC;UADnzBrF,EAAE,CAAAoF,cAAA,YACg1B,CAAC;UADn1BpF,EAAE,CAAA6E,MAAA,EAC01B,CAAC;UAD71B7E,EAAE,CAAAqF,YAAA,CACg2B,CAAC;UADn2BrF,EAAE,CAAA2F,UAAA,IAAAR,sBAAA,gBAC09B,CAAC;UAD79BnF,EAAE,CAAA8G,MAAA;UAAF9G,EAAE,CAAAqF,YAAA,CACkhC,CAAC;UADrhCrF,EAAE,CAAA2F,UAAA,KAAAD,uBAAA,iBAC+mC,CAAC;UADlnC1F,EAAE,CAAA8G,MAAA;UAAF9G,EAAE,CAAAqQ,YAAA,GACgwC,CAAC;UADnwCrQ,EAAE,CAAAqF,YAAA,CACwwC,CAAC;UAD3wCrF,EAAE,CAAA2F,UAAA,KAAAkB,gCAAA,0BAC8yC,CAAC,KAAAwB,+BAAA,gCADjzCrI,EAAE,CAAAsI,sBAC8sD,CAAC;QAAA;QAAA,IAAA5D,EAAA;UAAA,MAAA4L,sBAAA,GADjtDtQ,EAAE,CAAAyH,WAAA;UAAFzH,EAAE,CAAA6H,WAAA,kBAAAlD,GAAA,CAAA2C,KACmf,CAAC;UADtftH,EAAE,CAAAiF,SAAA,CACkkB,CAAC;UADrkBjF,EAAE,CAAA6F,UAAA,uBAAAlB,GAAA,CAAA4K,WAAA,IAAAe,sBACkkB,CAAC;UADrkBtQ,EAAE,CAAAiF,SAAA,EAC0yB,CAAC;UAD7yBjF,EAAE,CAAAkF,kBAAA,MAAAP,GAAA,CAAAiG,IAAA,KAC0yB,CAAC;UAD7yB5K,EAAE,CAAAiF,SAAA,EAC01B,CAAC;UAD71BjF,EAAE,CAAAuQ,iBAAA,CAAA5L,GAAA,CAAAkG,IAC01B,CAAC;UAD71B7K,EAAE,CAAAiF,SAAA,CACo6B,CAAC;UADv6BjF,EAAE,CAAA6F,UAAA,SAAAlB,GAAA,CAAA0I,QAAA,IAAFrN,EAAE,CAAA+G,WAAA,OAAApC,GAAA,CAAAkJ,SAAA,CACo6B,CAAC;UADv6B7N,EAAE,CAAAiF,SAAA,EAC+jC,CAAC;UADlkCjF,EAAE,CAAA6F,UAAA,SAAF7F,EAAE,CAAA+G,WAAA,SAAApC,GAAA,CAAAgJ,QAAA,CAC+jC,CAAC;UADlkC3N,EAAE,CAAAiF,SAAA,EAC2yC,CAAC;UAD9yCjF,EAAE,CAAA6F,UAAA,SAAAlB,GAAA,CAAA4I,WAC2yC,CAAC;QAAA;MAAA;MAAAiD,YAAA,GAAq0F1Q,YAAY,EAA+BD,EAAE,CAAC4Q,IAAI,EAAwF5Q,EAAE,CAACE,SAAS,EAA8CoC,kBAAkB,EAA8Hd,SAAS,EAAoIO,eAAe,EAA2FN,OAAO,EAAqFC,SAAS;MAAAmP,MAAA;MAAAC,eAAA;IAAA,EAA4J;EAAE;AACtiK;AACA/Q,UAAU,CAAC,CACPqB,OAAO,CACV,EAAE4L,OAAO,CAAC+D,SAAS,EAAE,mBAAmB,EAAE,IAAI,CAAC;AAChDhR,UAAU,CAAC,CACPqB,OAAO,CACV,EAAE4L,OAAO,CAAC+D,SAAS,EAAE,oBAAoB,EAAE,IAAI,CAAC;AACjDhR,UAAU,CAAC,CACPqB,OAAO,CACV,EAAE4L,OAAO,CAAC+D,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC;AAC5ChR,UAAU,CAAC,CACPqB,OAAO,CACV,EAAE4L,OAAO,CAAC+D,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC;AACtChR,UAAU,CAAC,CACPqB,OAAO,CACV,EAAE4L,OAAO,CAAC+D,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC;AACtC;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlBqG7Q,EAAE,CAAA8Q,iBAAA,CAkBXjE,OAAO,EAAc,CAAC;IACtGhC,IAAI,EAAEzK,SAAS;IACf2Q,IAAI,EAAE,CAAC;MAAEtB,UAAU,EAAE,IAAI;MAAEuB,QAAQ,EAAE,qCAAqC;MAAEC,OAAO,EAAE,CACzEnR,YAAY,EACZqC,kBAAkB,EAClBC,oBAAoB,EACpBf,SAAS,EACTO,eAAe,EACfN,OAAO,EACPC,SAAS,CACZ;MAAEoP,eAAe,EAAEtQ,uBAAuB,CAAC6Q,MAAM;MAAEC,SAAS,EAAE,CAAC1P,4BAA4B,CAACkL,gBAAgB,CAAC,CAAC;MAAEyE,cAAc,EAAE,CAAC1P,aAAa,CAAC;MAAE2P,IAAI,EAAE;QACpJ,oBAAoB,EAAE;MAC1B,CAAC;MAAEnB,QAAQ,EAAE,o8DAAo8D;MAAEQ,MAAM,EAAE,CAAC,qsDAAqsD;IAAE,CAAC;EAChrH,CAAC,CAAC,QAAkB;IAAErG,IAAI,EAAE,CAAC;MACrBQ,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAE6M,KAAK,EAAE,CAAC;MACRtC,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAE8H,IAAI,EAAE,CAAC;MACPyC,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAE8M,UAAU,EAAE,CAAC;MACbvC,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAE+M,QAAQ,EAAE,CAAC;MACXxC,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEiP,WAAW,EAAE,CAAC;MACd1E,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEiG,MAAM,EAAE,CAAC;MACTsE,IAAI,EAAEtK;IACV,CAAC,CAAC;IAAEqN,iBAAiB,EAAE,EAAE;IAAEE,kBAAkB,EAAE,EAAE;IAAER,aAAa,EAAE,EAAE;IAAEG,OAAO,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG,CAAC;AAAA;AAE5G,MAAM4D,iBAAiB,CAAC;EACpBxE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACvD,KAAK,GAAGtG,WAAW;IACxB,IAAI,CAAC2G,SAAS,GAAG3J,MAAM,CAAC+B,aAAa,CAAC;IACtC,IAAI,CAAC6H,YAAY,GAAG5J,MAAM,CAACgC,iBAAiB,CAAC;IAC7C,IAAI,CAAC+G,GAAG,GAAG,CAAC;IACZ,IAAI,CAACM,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACiI,cAAc,GAAG,IAAIpR,YAAY,CAAC,CAAC;EAC5C;EACA,IAAIqR,aAAaA,CAAA,EAAG;IAChB,OAAO,CAAC,CAAC,IAAI,CAACxI,GAAG,IAAI,IAAI,CAACO,KAAK,CAACe,MAAM,GAAG,IAAI,CAACtB,GAAG;EACrD;EACAW,MAAMA,CAAA,EAAG;IACL,IAAI,CAACL,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;IAC9B,IAAI,CAACiI,cAAc,CAAC/K,IAAI,CAAC,IAAI,CAAC8C,QAAQ,CAAC;EAC3C;EACA;IAAS,IAAI,CAACsF,IAAI,YAAA6C,0BAAA3C,CAAA;MAAA,YAAAA,CAAA,IAAyFwC,iBAAiB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAACvC,IAAI,kBAhE+E/O,EAAE,CAAAgP,iBAAA;MAAAnE,IAAA,EAgEJyG,iBAAiB;MAAArC,SAAA;MAAAyC,cAAA,WAAAC,iCAAAjN,EAAA,EAAAC,GAAA,EAAAiN,QAAA;QAAA,IAAAlN,EAAA;UAhEf1E,EAAE,CAAA6R,cAAA,CAAAD,QAAA,EAkE9C5O,OAAO,KAAQxC,WAAW;QAAA;QAAA,IAAAkE,EAAA;UAAA,IAAAoN,EAAA;UAlEkB9R,EAAE,CAAA+R,cAAA,CAAAD,EAAA,GAAF9R,EAAE,CAAAgS,WAAA,QAAArN,GAAA,CAAA4E,KAAA,GAAAuI,EAAA;QAAA;MAAA;MAAAxC,MAAA;QAAAtG,GAAA;QAAAM,QAAA;MAAA;MAAAkG,OAAA;QAAA+B,cAAA;MAAA;MAAA9B,UAAA;MAAAC,QAAA,GAAF1P,EAAE,CAAA2P,kBAAA,CAgEkK,CAC7PrM,uBAAuB,CAAC;QAAE8E,IAAI,EAAE,GAAG;QAAE6J,SAAS,EAAE,IAAI;QAAEC,WAAW,EAAE;MAAW,CAAC,CAAC,CACnF,GAlE4FlS,EAAE,CAAA4P,uBAAA,EAkEevM,IAAI,CAACE,QAAQ,IAlE9BvD,EAAE,CAAA6P,mBAAA;MAAAC,kBAAA,EAAAzL,GAAA;MAAA0L,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAiC,2BAAAzN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1E,EAAE,CAAAoQ,eAAA;UAAFpQ,EAAE,CAAA2F,UAAA,IAAA6C,yCAAA,yBAkE0G,CAAC;UAlE7GxI,EAAE,CAAA8G,MAAA;UAAF9G,EAAE,CAAAqQ,YAAA,EAkE0H,CAAC;UAlE7HrQ,EAAE,CAAA2F,UAAA,IAAAiD,yCAAA,yBAkE0L,CAAC,IAAAS,uCAAA,uBAAiM,CAAC,IAAAG,gCAAA,gBAAkb,CAAC;QAAA;QAAA,IAAA9E,EAAA;UAlElzB1E,EAAE,CAAA6F,UAAA,SAAF7F,EAAE,CAAA+G,WAAA,OAAApC,GAAA,CAAA4E,KAAA,kBAAA5E,GAAA,CAAA4E,KAAA,CAAA6I,OAAA,CAkEqG,CAAC;UAlExGpS,EAAE,CAAAiF,SAAA,EAkEsK,CAAC;UAlEzKjF,EAAE,CAAA6F,UAAA,YAAAlB,GAAA,CAAA4E,KAkEsK,CAAC;UAlEzKvJ,EAAE,CAAAiF,SAAA,CAkE0V,CAAC;UAlE7VjF,EAAE,CAAA6F,UAAA,SAAAlB,GAAA,CAAA6M,aAkE0V,CAAC;UAlE7VxR,EAAE,CAAAiF,SAAA,CAkEouB,CAAC;UAlEvuBjF,EAAE,CAAA6F,UAAA,SAAAlB,GAAA,CAAA6M,aAkEouB,CAAC;QAAA;MAAA;MAAAhB,YAAA,GAAo/B1Q,YAAY,EAA+BD,EAAE,CAACwS,OAAO,EAAmHxS,EAAE,CAAC4Q,IAAI,EAA6F5Q,EAAE,CAACyS,gBAAgB,EAA+IzS,EAAE,CAACE,SAAS,EAA8CsB,SAAS,EAAoI+B,kBAAkB,EAAsFG,QAAQ;MAAAmN,MAAA;MAAA6B,aAAA;MAAA5B,eAAA;IAAA,EAAuM;EAAE;AAC9uF;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KApEqG7Q,EAAE,CAAA8Q,iBAAA,CAoEXQ,iBAAiB,EAAc,CAAC;IAChHzG,IAAI,EAAEzK,SAAS;IACf2Q,IAAI,EAAE,CAAC;MAAEtB,UAAU,EAAE,IAAI;MAAEuB,QAAQ,EAAE,WAAW;MAAEC,OAAO,EAAE,CAACnR,YAAY,EAAEuB,SAAS,EAAE+B,kBAAkB,EAAEG,QAAQ,CAAC;MAAEgP,aAAa,EAAE9R,iBAAiB,CAAC+R,IAAI;MAAE7B,eAAe,EAAEtQ,uBAAuB,CAAC6Q,MAAM;MAAEC,SAAS,EAAE,CAC3M7N,uBAAuB,CAAC;QAAE8E,IAAI,EAAE,GAAG;QAAE6J,SAAS,EAAE,IAAI;QAAEC,WAAW,EAAE;MAAW,CAAC,CAAC,CACnF;MAAEd,cAAc,EAAE,CAAC7N,QAAQ,CAAC;MAAE2M,QAAQ,EAAE,4/BAA4/B;MAAEQ,MAAM,EAAE,CAAC,6mBAA6mB;IAAE,CAAC;EAC5qD,CAAC,CAAC,QAAkB;IAAEnH,KAAK,EAAE,CAAC;MACtBsB,IAAI,EAAEnK,eAAe;MACrBqQ,IAAI,EAAE,CAAC/N,OAAO,EAAE;QAAEyP,IAAI,EAAEjS;MAAY,CAAC;IACzC,CAAC,CAAC;IAAEwI,GAAG,EAAE,CAAC;MACN6B,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEgJ,QAAQ,EAAE,CAAC;MACXuB,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEiR,cAAc,EAAE,CAAC;MACjB1G,IAAI,EAAEtK;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmS,oBAAoB,CAAC;EACvB5F,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6F,WAAW,GAAG1S,MAAM,CAACwD,oBAAoB,CAAC;IAC/C,IAAI,CAACmP,KAAK,GAAG3S,MAAM,CAACiC,oBAAoB,CAAC;IACzC,IAAI,CAAC2Q,OAAO,GAAGxQ,aAAa,CAAC,CAAC;IAC9B,IAAI,CAACyQ,SAAS,GAAG7S,MAAM,CAAC8S,aAAa,CAAC;EAC1C;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,YAAY,CAAC,IAAI,CAACJ,OAAO,CAACrO,SAAS,EAAE,CAAC,CAAC,IAAI,CAACsO,SAAS,CAACI,KAAK,EAAEA,KAAK,CAACC,QAAQ,CAAC;EAC5F;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,aAAa,CAAC,IAAI,CAACR,OAAO,CAACrO,SAAS,EAAE,CAAC,CAAC,IAAI,CAACsO,SAAS,CAACI,KAAK,EAAEA,KAAK,CAACC,QAAQ,CAAC;EAC7F;EACAF,YAAYA,CAACK,WAAW,EAAEH,QAAQ,EAAE;IAChC,OAAOG,WAAW,GACZ9Q,EAAE,CAAC,EAAE,CAAC,GACN,IAAI,CAACoQ,KAAK,CAAC5E,IAAI,CAACzL,GAAG,CAAEuM,CAAC,IAAMqE,QAAQ,GAAGrE,CAAC,CAACyE,mBAAmB,GAAGzE,CAAC,CAAC0E,iBAAkB,CAAC,CAAC;EAC/F;EACAH,aAAaA,CAACC,WAAW,EAAEH,QAAQ,EAAE;IACjC,OAAOG,WAAW,GACZ7Q,aAAa,CAAC,CAAC,IAAI,CAACkQ,WAAW,EAAE,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC5E,IAAI,CAACzL,GAAG,CAAC,CAAC,CAACkR,UAAU,EAAEC,IAAI,CAAC,KAAK;MAC7E,IAAID,UAAU,KAAK,QAAQ,EAAE;QACzB,OAAO,EAAE;MACb;MACA,OAAON,QAAQ,GAAGO,IAAI,CAACC,YAAY,GAAGD,IAAI,CAACE,IAAI;IACnD,CAAC,CAAC,CAAC,GACDnR,aAAa,CAAC,CAAC,IAAI,CAACkQ,WAAW,EAAE,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC5E,IAAI,CAACzL,GAAG,CAAC,CAAC,CAACkR,UAAU,EAAEC,IAAI,CAAC,KAAK;MAC7E,IAAID,UAAU,KAAK,QAAQ,EAAE;QACzB,OAAO,EAAE;MACb;MACA,OAAON,QAAQ,GACTO,IAAI,CAACG,oBAAoB,GACzBH,IAAI,CAACI,kBAAkB;IACjC,CAAC,CAAC,CAAC;EACX;EACA;IAAS,IAAI,CAAClF,IAAI,YAAAmF,6BAAAjF,CAAA;MAAA,YAAAA,CAAA,IAAyF4D,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAAC3D,IAAI,kBAxH+E/O,EAAE,CAAAgP,iBAAA;MAAAnE,IAAA,EAwHJ6H,oBAAoB;MAAAzD,SAAA;MAAAQ,UAAA;MAAAC,QAAA,GAxHlB1P,EAAE,CAAA6P,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA8D,8BAAAtP,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1E,EAAE,CAAAoF,cAAA,UAyHrF,CAAC;UAzHkFpF,EAAE,CAAA6E,MAAA,EAyHlE,CAAC;UAzH+D7E,EAAE,CAAA8G,MAAA;UAAF9G,EAAE,CAAAqF,YAAA,CAyH9D,CAAC;UAzH2DrF,EAAE,CAAA6E,MAAA,EA2HpG,CAAC;UA3HiG7E,EAAE,CAAA8G,MAAA;QAAA;QAAA,IAAApC,EAAA;UAAF1E,EAAE,CAAAiF,SAAA,CAyHlE,CAAC;UAzH+DjF,EAAE,CAAAuQ,iBAAA,CAAFvQ,EAAE,CAAA+G,WAAA,OAAApC,GAAA,CAAAqO,KAAA,CAyHlE,CAAC;UAzH+DhT,EAAE,CAAAiF,SAAA,EA2HpG,CAAC;UA3HiGjF,EAAE,CAAAkF,kBAAA,MAAFlF,EAAE,CAAA+G,WAAA,OAAApC,GAAA,CAAAyO,MAAA,MA2HpG,CAAC;QAAA;MAAA;MAAA5C,YAAA,GAAwDzQ,SAAS,EAA8CyD,OAAO;MAAA+O,aAAA;IAAA,EAAwH;EAAE;AACpP;AACA3S,UAAU,CAAC,CACPqB,OAAO,CACV,EAAEyR,oBAAoB,CAAC9B,SAAS,EAAE,cAAc,EAAE,IAAI,CAAC;AACxDhR,UAAU,CAAC,CACPqB,OAAO,CACV,EAAEyR,oBAAoB,CAAC9B,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC;AACzD;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAnIqG7Q,EAAE,CAAA8Q,iBAAA,CAmIX4B,oBAAoB,EAAc,CAAC;IACnH7H,IAAI,EAAEzK,SAAS;IACf2Q,IAAI,EAAE,CAAC;MACCtB,UAAU,EAAE,IAAI;MAChBwB,OAAO,EAAE,CAAClR,SAAS,EAAEyD,OAAO,CAAC;MAC7B0M,QAAQ,EAAE;AAC9B;AACA;AACA,KAAK;MACe;MACAS,eAAe,EAAEtQ,uBAAuB,CAAC4T;IAC7C,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEhB,YAAY,EAAE,EAAE;IAAEI,aAAa,EAAE;EAAG,CAAC;AAAA;AAEnE,MAAMa,+BAA+B,GAAG;EACpC1J,MAAM,EAAE,EAAE;EACV2I,QAAQ,EAAE,KAAK;EACf/K,IAAI,EAAE,GAAG;EACT+L,WAAW,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,CAAE;AACnC,CAAC;AACD;AACA;AACA;AACA,MAAMC,uBAAuB,GAAGlT,cAAc,CAACgT,+BAA+B,CAAC;AAC/E,SAASG,4BAA4BA,CAACrH,OAAO,EAAE;EAC3C,OAAO7L,iBAAiB,CAACiT,uBAAuB,EAAEpH,OAAO,EAAEkH,+BAA+B,CAAC;AAC/F;AAEA,MAAMI,sBAAsB,SAASlQ,YAAY,CAAC;EAC9C0I,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGyH,SAAS,CAAC;IACnB,IAAI,CAACvH,OAAO,GAAG/M,MAAM,CAACmU,uBAAuB,CAAC;IAC9C,IAAI,CAAC5J,MAAM,GAAG,IAAI,CAACwC,OAAO,CAACxC,MAAM;IACjC,IAAI,CAAC2J,WAAW,GAAG,IAAI,CAACnH,OAAO,CAACmH,WAAW;EAC/C;EACAK,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,CAAC,CAAC;EACjB;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACD,MAAM,CAAC,CAAC;EACjB;EACAA,MAAMA,CAAA,EAAG;IACL,IAAI,CAACE,QAAQ,GACT1Q,UAAU,CAAC2Q,OAAO,CAAC,CACfrK,4BAA4B,CAAC,IAAI,CAACC,MAAM,CAAC,EACzCN,0BAA0B,CAAC,IAAI,CAACiK,WAAW,CAAC,CAC/C,CAAC,IAAIlQ,UAAU,CAAC4Q,aAAa;IAClC,IAAI,CAACC,QAAQ,CAAC,CAAC;EACnB;EACA;IAAS,IAAI,CAAClG,IAAI;MAAA,IAAAmG,mCAAA;MAAA,gBAAAC,+BAAAlG,CAAA;QAAA,QAAAiG,mCAAA,KAAAA,mCAAA,GApL+E/U,EAAE,CAAAiV,qBAAA,CAoLQX,sBAAsB,IAAAxF,CAAA,IAAtBwF,sBAAsB;MAAA;IAAA,IAAqD;EAAE;EACxL;IAAS,IAAI,CAACY,IAAI,kBArL+ElV,EAAE,CAAAmV,iBAAA;MAAAtK,IAAA,EAqLJyJ,sBAAsB;MAAApF,QAAA;MAAAC,YAAA,WAAAiG,oCAAA1Q,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArLpB1E,EAAE,CAAAqV,cAAA,WAAA1Q,GAAA,CAAA6F,MAqLiB,CAAC;QAAA;MAAA;MAAA8E,MAAA;QAAA9E,MAAA;QAAA2J,WAAA;MAAA;MAAAmB,QAAA;MAAA7F,UAAA;MAAAC,QAAA,GArLpB1P,EAAE,CAAA2P,kBAAA,CAqL2J,CAACvO,UAAU,CAAC8C,aAAa,EAAEoQ,sBAAsB,EAAE,IAAI,CAAC,CAAC,GArLtNtU,EAAE,CAAAuV,0BAAA,EAAFvV,EAAE,CAAAwV,oBAAA;IAAA,EAqLuT;EAAE;AACha;AACA;EAAA,QAAA3E,SAAA,oBAAAA,SAAA,KAvLqG7Q,EAAE,CAAA8Q,iBAAA,CAuLXwD,sBAAsB,EAAc,CAAC;IACrHzJ,IAAI,EAAElK,SAAS;IACfoQ,IAAI,EAAE,CAAC;MACCtB,UAAU,EAAE,IAAI;MAChBH,MAAM,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC;MACjC6B,SAAS,EAAE,CAAC/P,UAAU,CAAC8C,aAAa,EAAEoQ,sBAAsB,EAAE,IAAI,CAAC,CAAC;MACpEgB,QAAQ,EAAE,wBAAwB;MAClCjE,IAAI,EAAE;QACF,UAAU,EAAE;MAChB;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMoE,sBAAsB,SAAS/R,UAAU,CAAC;EAC5CoJ,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGyH,SAAS,CAAC;IACnB,IAAI,CAAClD,IAAI,GAAGpR,MAAM,CAACW,UAAU,CAAC,MAAMmS,aAAa,CAAC,CAAC;IACnD,IAAI,CAAC2C,MAAM,GAAGhT,KAAK,CAAC,CAAC,EAAEoB,oBAAoB,CAAC,CAAC,CAAC,CAACkK,IAAI,CAACrL,SAAS,CAAC,MAAMoB,eAAe,CAAC,IAAI,CAACyH,OAAO,CAACA,OAAO,CAAC,CAAC,EAAEjJ,GAAG,CAAC,MAAMgJ,gBAAgB,CAAC,IAAI,CAACC,OAAO,CAACA,OAAO,CAAC,CAAC,EAAE5I,MAAM,CAAC,CAAC;MAAE0H;IAAO,CAAC,KAAK,CAAC,CAACA,MAAM,CAAC,CAAC;IAChM,IAAI,CAACgC,UAAU,GAAG,MAAM;IACxB,IAAI,CAAC4G,KAAK,GAAGlP,gBAAgB,CAAC,CAAC;EACnC;EACA2R,OAAOA,CAACvL,KAAK,EAAE;IACX,MAAMwL,WAAW,GAAG,IAAI,CAAC1C,KAAK,CAACC,QAAQ,GACjC,CAAC,GAAG0C,OAAO,CAAC,IAAI,CAAC1L,KAAK,CAAC,CAAC,CAAC,EAAE,GAAGuB,KAAK,CAACC,IAAI,CAACvB,KAAK,CAAC,CAAC,GAChDA,KAAK,CAAC,CAAC,CAAC;IACd,IAAIwL,WAAW,EAAE;MACb,IAAI,CAACd,QAAQ,CAACc,WAAW,CAAC;IAC9B;EACJ;EACAE,OAAOA,CAACC,KAAK,EAAE;IACX,IAAI,IAAI,CAAC7C,KAAK,CAAC8C,QAAQ,EAAE;MACrBD,KAAK,CAACE,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA;IAAS,IAAI,CAACrH,IAAI;MAAA,IAAAsH,mCAAA;MAAA,gBAAAC,+BAAArH,CAAA;QAAA,QAAAoH,mCAAA,KAAAA,mCAAA,GAzN+ElW,EAAE,CAAAiV,qBAAA,CAyNQQ,sBAAsB,IAAA3G,CAAA,IAAtB2G,sBAAsB;MAAA;IAAA,IAAqD;EAAE;EACxL;IAAS,IAAI,CAACP,IAAI,kBA1N+ElV,EAAE,CAAAmV,iBAAA;MAAAtK,IAAA,EA0NJ4K,sBAAsB;MAAAxG,SAAA;MAAAmH,SAAA,YAAsH,EAAE,UAAU,MAAM;MAAAlH,QAAA;MAAAC,YAAA,WAAAkH,oCAAA3R,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1N5J1E,EAAE,CAAAiG,UAAA,kBAAAqQ,+CAAA;YAAA,OA0NJ3R,GAAA,CAAA4R,SAAA,CAAU,CAAC;UAAA,CAAU,CAAC,mBAAAC,gDAAAC,MAAA;YAAA,OAAtB9R,GAAA,CAAAmR,OAAA,CAAAW,MAAc,CAAC;UAAA,CAAM,CAAC;QAAA;QAAA,IAAA/R,EAAA;UA1NpB1E,EAAE,CAAAqV,cAAA,aA0NJ1Q,GAAA,CAAA+R,QAAA,CAAS,CAAY,CAAC;QAAA;MAAA;MAAAlH,OAAA;QAAAkG,MAAA;MAAA;MAAAjG,UAAA;MAAAC,QAAA,GA1NpB1P,EAAE,CAAA2P,kBAAA,CA0NuR,CAClXhM,YAAY,CAAC8R,sBAAsB,CAAC,EACpChU,4BAA4B,CAACgU,sBAAsB,CAAC,CACvD,GA7N4FzV,EAAE,CAAA4P,uBAAA,EA6NvChM,IAAI,CAACC,kBAAkB,EAAiBrC,EAAE,CAACG,iBAAiB;QAAAgV,SAAA,EAAiBrC,sBAAsB;QAAAhF,MAAA;MAAA,KA7N9DtP,EAAE,CAAAuV,0BAAA;IAAA,EA6N4I;EAAE;AACrP;AACA;EAAA,QAAA1E,SAAA,oBAAAA,SAAA,KA/NqG7Q,EAAE,CAAA8Q,iBAAA,CA+NX2E,sBAAsB,EAAc,CAAC;IACrH5K,IAAI,EAAElK,SAAS;IACfoQ,IAAI,EAAE,CAAC;MACCtB,UAAU,EAAE,IAAI;MAChBuB,QAAQ,EAAE,sBAAsB;MAChCG,SAAS,EAAE,CACPxN,YAAY,CAAC8R,sBAAsB,CAAC,EACpChU,4BAA4B,CAACgU,sBAAsB,CAAC,CACvD;MACDrE,cAAc,EAAE,CACZvN,kBAAkB,EAClBlC,iBAAiB,EACjB;QACIgV,SAAS,EAAErC,sBAAsB;QACjChF,MAAM,EAAE,CAAC,QAAQ,EAAE,aAAa;MACpC,CAAC,CACJ;MACD+B,IAAI,EAAE;QACFuF,KAAK,EAAE,EAAE;QACT/L,IAAI,EAAE,MAAM;QACZ,YAAY,EAAE,YAAY;QAC1B,QAAQ,EAAE,aAAa;QACvB,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE6K,MAAM,EAAE,CAAC;MACvB7K,IAAI,EAAEtK;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,SAASsV,OAAOA,CAAC1L,KAAK,EAAE;EACpB,OAAOA,KAAK,GAAGrH,WAAW,CAACqH,KAAK,CAAC,GAAGjH,WAAW;AACnD;AAEA,MAAM6P,aAAa,CAAC;EAChBjG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiB,OAAO,GAAG,IAAIzL,qBAAqB,CAACoQ,oBAAoB,CAAC;EAClE;EACA,IAAIY,WAAWA,CAAA,EAAG;IACd,OAAO,CAAC,CAAC,IAAI,CAAClJ,KAAK,IAAI,CAAC,IAAI,CAAC8I,KAAK,EAAEwD,QAAQ,CAAC,CAAC;EAClD;EACAG,eAAeA,CAAC3D,KAAK,EAAE;IACnB,IAAI,CAACA,KAAK,EAAE9I,KAAK,EAAE;MACf;IACJ;IACA,IAAI,CAAC8I,KAAK,EAAEyC,OAAO,CAACzC,KAAK,CAAC9I,KAAK,CAAC;IAChC8I,KAAK,CAAC/I,KAAK,GAAG,EAAE;EACpB;EACA2M,SAASA,CAAC;IAAEC;EAAa,CAAC,EAAE;IACxB,IAAI,CAAC3M,KAAK,GAAG,IAAI;IACjB,IAAI2M,YAAY,EAAE3M,KAAK,IAAI,CAAC,IAAI,CAAC8I,KAAK,EAAEwD,QAAQ,CAAC,CAAC,EAAE;MAChD,IAAI,CAACxD,KAAK,EAAEyC,OAAO,CAACoB,YAAY,CAAC3M,KAAK,CAAC;IAC3C;EACJ;EACA4M,MAAMA,CAACD,YAAY,EAAE;IACjB,IAAI,CAAC3M,KAAK,GAAG2M,YAAY,EAAE3M,KAAK;EACpC;EACA;IAAS,IAAI,CAACwE,IAAI,YAAAqI,sBAAAnI,CAAA;MAAA,YAAAA,CAAA,IAAyFiE,aAAa;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAAChE,IAAI,kBAvR+E/O,EAAE,CAAAgP,iBAAA;MAAAnE,IAAA,EAuRJkI,aAAa;MAAA9D,SAAA;MAAAyC,cAAA,WAAAwF,6BAAAxS,EAAA,EAAAC,GAAA,EAAAiN,QAAA;QAAA,IAAAlN,EAAA;UAvRX1E,EAAE,CAAA6R,cAAA,CAAAD,QAAA,EAuRqYpR,WAAW;UAvRlZR,EAAE,CAAA6R,cAAA,CAAAD,QAAA,EAuRwd6D,sBAAsB;QAAA;QAAA,IAAA/Q,EAAA;UAAA,IAAAoN,EAAA;UAvRhf9R,EAAE,CAAA+R,cAAA,CAAAD,EAAA,GAAF9R,EAAE,CAAAgS,WAAA,QAAArN,GAAA,CAAAuL,QAAA,GAAA4B,EAAA,CAAAqF,KAAA;UAAFnX,EAAE,CAAA+R,cAAA,CAAAD,EAAA,GAAF9R,EAAE,CAAAgS,WAAA,QAAArN,GAAA,CAAAuO,KAAA,GAAApB,EAAA,CAAAqF,KAAA;QAAA;MAAA;MAAAjI,QAAA;MAAAC,YAAA,WAAAiI,2BAAA1S,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1E,EAAE,CAAAiG,UAAA,uCAAAoR,2DAAA;YAAA,OAuRJ,CAAC;UAAA,CAAW,CAAC,0BAAAC,8CAAAb,MAAA;YAAA,OAAb9R,GAAA,CAAAmS,SAAA,CAAAL,MAAgB,CAAC;UAAA,CAAL,CAAC,uBAAAc,2CAAAd,MAAA;YAAA,OAAb9R,GAAA,CAAAqS,MAAA,CAAAP,MAAA,CAAAM,YAA0B,CAAC;UAAA,CAAf,CAAC,uBAAAS,2CAAA;YAAA,OAAb7S,GAAA,CAAAqS,MAAA,CAAO,IAAI,CAAC;UAAA,EAAC,oBAAAS,wCAAAhB,MAAA;YAAA,OAAb9R,GAAA,CAAAkS,eAAA,CAAAJ,MAAA,CAAAiB,MAA6B,CAAC;UAAA,CAAlB,CAAC;QAAA;QAAA,IAAAhT,EAAA;UAvRX1E,EAAE,CAAA6H,WAAA,aAAAlD,GAAA,CAAA2O,WAuRQ,CAAC;QAAA;MAAA;MAAA7D,UAAA;MAAAC,QAAA,GAvRX1P,EAAE,CAAA6P,mBAAA;MAAA8H,KAAA,EAAA7N,GAAA;MAAAgG,kBAAA,EAAAzL,GAAA;MAAA0L,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA0H,uBAAAlT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1E,EAAE,CAAAoQ,eAAA;UAAFpQ,EAAE,CAAAqQ,YAAA,EAwRlF,CAAC;UAxR+ErQ,EAAE,CAAA2F,UAAA,IAAAoE,6BAAA,iBA8R/F,CAAC;QAAA;QAAA,IAAArF,EAAA;UA9R4F1E,EAAE,CAAAiF,SAAA,CA4R/F,CAAC;UA5R4FjF,EAAE,CAAA6F,UAAA,uBAAAlB,GAAA,CAAAuL,QAAA,IAAAvL,GAAA,CAAAoJ,OA4R/F,CAAC,8BA5R4F/N,EAAE,CAAAmI,eAAA,IAAA7D,GAAA,EAAAK,GAAA,CAAA2O,WAAA,CA8RtG,CAAC;QAAA;MAAA;MAAA9C,YAAA,GAG6zHrO,kBAAkB;MAAAuO,MAAA;MAAA6B,aAAA;MAAA5B,eAAA;IAAA,EAAmM;EAAE;AACthI;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KAnSqG7Q,EAAE,CAAA8Q,iBAAA,CAmSXiC,aAAa,EAAc,CAAC;IAC5GlI,IAAI,EAAEzK,SAAS;IACf2Q,IAAI,EAAE,CAAC;MAAEtB,UAAU,EAAE,IAAI;MAAEuB,QAAQ,EAAE,sBAAsB;MAAEC,OAAO,EAAE,CAAC9O,kBAAkB,EAAEC,oBAAoB,CAAC;MAAE8N,QAAQ,EAAE;AACxI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEqC,aAAa,EAAE9R,iBAAiB,CAAC+R,IAAI;MAAE7B,eAAe,EAAEtQ,uBAAuB,CAAC6Q,MAAM;MAAEG,IAAI,EAAE;QAC7E,6BAA6B,EAAE,GAAG;QAClC,gBAAgB,EAAE,mBAAmB;QACrC,aAAa,EAAE,6BAA6B;QAC5C,aAAa,EAAE,cAAc;QAC7B,kBAAkB,EAAE,aAAa;QACjC,UAAU,EAAE;MAChB,CAAC;MAAEX,MAAM,EAAE,CAAC,kvHAAkvH;IAAE,CAAC;EAC7wH,CAAC,CAAC,QAAkB;IAAER,QAAQ,EAAE,CAAC;MACzBrF,IAAI,EAAEhK,YAAY;MAClBkQ,IAAI,EAAE,CAACvQ,WAAW;IACtB,CAAC,CAAC;IAAE0S,KAAK,EAAE,CAAC;MACRrI,IAAI,EAAEhK,YAAY;MAClBkQ,IAAI,EAAE,CAAC0E,sBAAsB;IACjC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMoC,mBAAmB,CAAC;EACtB/K,WAAWA,CAAA,EAAG;IACV,IAAI,CAACE,OAAO,GAAG/M,MAAM,CAACmU,uBAAuB,CAAC;IAC9C,IAAI,CAAC7H,UAAU,GAAGtM,MAAM,CAAC0M,gBAAgB,CAAC,CAACJ,UAAU;IACrD,IAAI,CAACN,MAAM,GAAGhM,MAAM,CAACC,SAAS,CAAC;IAC/B,IAAI,CAAC0S,KAAK,GAAG3S,MAAM,CAACiC,oBAAoB,CAAC;IACzC,IAAI,CAAC4V,KAAK,GAAG7X,MAAM,CAAC6B,6BAA6B,CAAC;EACtD;EACAiW,SAASA,CAAC1N,IAAI,EAAE;IAAEG,MAAM,GAAG,IAAI,CAACwC,OAAO,CAACxC,MAAM;IAAE2J,WAAW,GAAG,IAAI,CAACnH,OAAO,CAACmH;EAAa,CAAC,GAAG,IAAI,CAACnH,OAAO,EAAE;IACtG,MAAMgL,aAAa,GAAG9N,0BAA0B,CAACiK,WAAW,CAAC;IAC7D,MAAM8D,eAAe,GAAG1N,4BAA4B,CAACC,MAAM,CAAC;IAC5D,MAAMgB,OAAO,GAAG,IAAIrH,WAAW,CAACkG,IAAI,CAAC;IACrC,OAAO5H,aAAa,CAAC,CAAC,IAAI,CAACmQ,KAAK,EAAE,IAAI,CAACkF,KAAK,CAAC,CAAC,CAAC9J,IAAI,CAACzL,GAAG,CAAC,CAAC,CAAC;MAAE2V,sBAAsB;MAAEC;IAAsB,CAAC,EAAEnM,KAAK,CAAC,KAAK;MACpH,IAAI3B,IAAI,IAAI4N,eAAe,CAACzM,OAAO,CAAC,EAAE;QAClC,OAAO;UACHZ,IAAI,EAAEP,IAAI,CAACO,IAAI;UACfxC,IAAI,EAAEiC,IAAI,CAACjC,IAAI;UACf2F,OAAO,EAAEoK;QACb,CAAC;MACL;MACA,IAAI9N,IAAI,IAAI2N,aAAa,CAACxM,OAAO,CAAC,EAAE;QAChC,OAAO;UACHZ,IAAI,EAAEP,IAAI,CAACO,IAAI;UACfxC,IAAI,EAAEiC,IAAI,CAACjC,IAAI;UACf2F,OAAO,EAAE,GAAGmK,sBAAsB,GAAG/U,mBAAmB,GAAG,IAAI,CAACoJ,UAAU,CAACP,KAAK,EAAEmI,WAAW,EAAE,IAAI,CAAClI,MAAM,CAAC;QAC/G,CAAC;MACL;MACA,OAAO,IAAI;IACf,CAAC,CAAC,CAAC;EACP;EACA;IAAS,IAAI,CAAC2C,IAAI,YAAAwJ,4BAAAtJ,CAAA;MAAA,YAAAA,CAAA,IAAyF+I,mBAAmB;IAAA,CAA8C;EAAE;EAC9K;IAAS,IAAI,CAACQ,KAAK,kBA9V8ErY,EAAE,CAAAsY,YAAA;MAAA1N,IAAA;MAAAC,IAAA,EA8VMgN,mBAAmB;MAAAU,IAAA;MAAA9I,UAAA;IAAA,EAAgD;EAAE;AAClL;AACA;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KAhWqG7Q,EAAE,CAAA8Q,iBAAA,CAgWX+G,mBAAmB,EAAc,CAAC;IAClHhN,IAAI,EAAE/J,IAAI;IACViQ,IAAI,EAAE,CAAC;MACCtB,UAAU,EAAE,IAAI;MAChB7E,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM4N,QAAQ,GAAG,CACbxV,OAAO,EACP6J,OAAO,EACPkG,aAAa,EACbzB,iBAAiB,EACjBuG,mBAAmB,EACnBpC,sBAAsB,CACzB;;AAED;AACA;AACA;;AAEA,SAASpJ,wBAAwB,EAAEM,gBAAgB,EAAE1C,gBAAgB,EAAEiK,+BAA+B,EAAEE,uBAAuB,EAAEpK,cAAc,EAAE6C,OAAO,EAAEgL,mBAAmB,EAAEW,QAAQ,EAAElH,iBAAiB,EAAEyB,aAAa,EAAEL,oBAAoB,EAAE+C,sBAAsB,EAAEnB,sBAAsB,EAAE/J,4BAA4B,EAAEL,0BAA0B,EAAE0C,sBAAsB,EAAEf,gBAAgB,EAAEN,gBAAgB,EAAEQ,aAAa,EAAEsI,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}