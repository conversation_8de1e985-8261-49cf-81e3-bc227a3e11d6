{"ast": null, "code": "import { Async<PERSON>ipe, NgIf, PercentPipe } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, inject, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, ChangeDetectorRef, ViewEncapsulation, Injectable } from '@angular/core';\nimport { tuiClamp, tuiRound } from '@taiga-ui/cdk/utils/math';\nimport { tuiButtonOptionsProvider, TuiButton } from '@taiga-ui/core/components/button';\nimport { TUI_PREVIEW_ICONS, TUI_PAGINATION_TEXTS, TUI_PREVIEW_ZOOM_TEXTS, TUI_PREVIEW_TEXTS } from '@taiga-ui/kit/tokens';\nimport { WaMutationObserver } from '@ng-web-apis/mutation-observer';\nimport { WaResizeObserver } from '@ng-web-apis/resize-observer';\nimport { TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { TuiPan } from '@taiga-ui/cdk/directives/pan';\nimport { TuiZoom } from '@taiga-ui/cdk/directives/zoom';\nimport { tuiDragAndDropFrom, tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiPx } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i2 from '@taiga-ui/core/directives/hint';\nimport { TuiHint } from '@taiga-ui/core/directives/hint';\nimport { switchMap, merge, of, timer, map, startWith, BehaviorSubject, combineLatest } from 'rxjs';\nimport * as i1 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport * as i3 from '@taiga-ui/kit/components/slider';\nimport { TuiSlider } from '@taiga-ui/kit/components/slider';\nimport * as i1$1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { injectContext, PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { TuiPopoverDirective } from '@taiga-ui/cdk/directives/popover';\nimport { TuiPopoverService, tuiAsPopover } from '@taiga-ui/cdk/services';\nimport { TUI_DIALOGS } from '@taiga-ui/core/components/dialog';\nfunction TuiPreviewPagination_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 1);\n    i0.ɵɵlistener(\"click\", function TuiPreviewPagination_ng_container_0_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onArrowClick(-1));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementStart(4, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function TuiPreviewPagination_ng_container_0_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onArrowClick(1));\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const texts_r3 = ctx.ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.leftButtonDisabled)(\"iconStart\", ctx_r1.icons.prev);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", texts_r3[0], \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.index + 1, \"/\", ctx_r1.length, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.rightButtonDisabled)(\"iconStart\", ctx_r1.icons.next);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", texts_r3[1], \" \");\n  }\n}\nconst _c0 = () => ({\n  standalone: true\n});\nfunction TuiPreviewZoom_ng_container_0_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵpipe(2, \"percent\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, i0.ɵɵpipeBind1(1, 1, ctx_r1.valueChange) || ctx_r1.value), \" \");\n  }\n}\nfunction TuiPreviewZoom_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"section\", 2)(2, \"button\", 3);\n    i0.ɵɵlistener(\"click\", function TuiPreviewZoom_ng_container_0_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMinus());\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"label\", 4);\n    i0.ɵɵelement(5, \"div\", 5);\n    i0.ɵɵpipe(6, \"async\");\n    i0.ɵɵtemplate(7, TuiPreviewZoom_ng_container_0_ng_template_7_Template, 3, 5, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(9, \"input\", 6);\n    i0.ɵɵlistener(\"ngModelChange\", function TuiPreviewZoom_ng_container_0_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onModelChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function TuiPreviewZoom_ng_container_0_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPlus());\n    });\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function TuiPreviewZoom_ng_container_0_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onReset());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const texts_r3 = ctx.ngIf;\n    const hint_r4 = i0.ɵɵreference(8);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.leftButtonDisabled)(\"iconStart\", ctx_r1.icons.zoomOut);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", texts_r3.zoomOut, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"tuiHint\", hint_r4)(\"tuiHintManual\", !!i0.ɵɵpipeBind1(6, 16, ctx_r1.hintShow$));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"max\", ctx_r1.max)(\"min\", ctx_r1.min)(\"ngModel\", ctx_r1.value)(\"ngModelOptions\", i0.ɵɵpureFunction0(18, _c0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.rightButtonDisabled)(\"iconStart\", ctx_r1.icons.zoomIn);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", texts_r3.zoomIn, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"t-invisible\", !ctx_r1.collapseVisible);\n    i0.ɵɵproperty(\"iconStart\", ctx_r1.icons.zoomReset)(\"tuiHint\", texts_r3.reset);\n  }\n}\nconst _c1 = [\"*\", [[\"tui-preview-title\"]], [[\"tui-preview-pagination\"]], [[\"\", \"tuiPreviewAction\", \"\"]]];\nconst _c2 = [\"*\", \"tui-preview-title\", \"tui-preview-pagination\", \"[tuiPreviewAction]\"];\nfunction TuiPreviewComponent_ng_container_0_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function TuiPreviewComponent_ng_container_0_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.rotate());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const texts_r5 = i0.ɵɵnextContext().ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"iconStart\", ctx_r1.icons.rotate)(\"tuiHint\", texts_r5.rotate);\n  }\n}\nfunction TuiPreviewComponent_ng_container_0_tui_preview_zoom_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tui-preview-zoom\", 10);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵlistener(\"reset\", function TuiPreviewComponent_ng_container_0_tui_preview_zoom_15_Template_tui_preview_zoom_reset_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.reset());\n    })(\"valueChange\", function TuiPreviewComponent_ng_container_0_tui_preview_zoom_15_Template_tui_preview_zoom_valueChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setZoom($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"min\", ctx_r1.minZoom)(\"value\", i0.ɵɵpipeBind1(1, 2, ctx_r1.zoom$) || 1);\n  }\n}\nfunction TuiPreviewComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"section\", 2, 0);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵpipe(5, \"async\");\n    i0.ɵɵlistener(\"tuiPan\", function TuiPreviewComponent_ng_container_0_Template_section_tuiPan_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPan($event));\n    })(\"tuiZoom\", function TuiPreviewComponent_ng_container_0_Template_section_tuiZoom_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onZoom($event));\n    })(\"waMutationObserver\", function TuiPreviewComponent_ng_container_0_Template_section_waMutationObserver_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const contentWrapper_r3 = i0.ɵɵreference(2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMutation(contentWrapper_r3));\n    })(\"waResizeObserver\", function TuiPreviewComponent_ng_container_0_Template_section_waResizeObserver_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onResize($event));\n    });\n    i0.ɵɵprojection(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"header\", 3)(8, \"div\", 4);\n    i0.ɵɵprojection(9, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵprojection(10, 2);\n    i0.ɵɵelementStart(11, \"div\", 5);\n    i0.ɵɵprojection(12, 3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"footer\", 6);\n    i0.ɵɵtemplate(14, TuiPreviewComponent_ng_container_0_button_14_Template, 1, 2, \"button\", 7)(15, TuiPreviewComponent_ng_container_0_tui_preview_zoom_15_Template, 2, 4, \"tui-preview-zoom\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"cursor\", i0.ɵɵpipeBind1(3, 10, ctx_r1.cursor$))(\"transform\", i0.ɵɵpipeBind1(4, 12, ctx_r1.wrapperTransform$));\n    i0.ɵɵclassProp(\"t-not-interactive-content\", ctx_r1.zoomable)(\"t-transitive\", i0.ɵɵpipeBind1(5, 14, ctx_r1.transitioned$));\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.rotatable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.zoomable);\n  }\n}\nconst _c3 = [\"*\"];\nfunction TuiPreviewDialog_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.polymorpheusOutlet;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", text_r1, \" \");\n  }\n}\nclass TuiPreviewAction {\n  static {\n    this.ɵfac = function TuiPreviewAction_Factory(t) {\n      return new (t || TuiPreviewAction)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiPreviewAction,\n      selectors: [[\"\", \"tuiPreviewAction\", \"\"]],\n      hostVars: 2,\n      hostBindings: function TuiPreviewAction_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"border-radius\", 100, \"rem\");\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiButtonOptionsProvider({\n        appearance: 'preview-action',\n        size: 's'\n      })])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPreviewAction, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiPreviewAction]',\n      providers: [tuiButtonOptionsProvider({\n        appearance: 'preview-action',\n        size: 's'\n      })],\n      host: {\n        '[style.border-radius.rem]': '100'\n      }\n    }]\n  }], null, null);\n})();\nclass TuiPreviewPagination {\n  constructor() {\n    this.icons = inject(TUI_PREVIEW_ICONS);\n    this.texts$ = inject(TUI_PAGINATION_TEXTS);\n    this.length = 1;\n    this.index = 0;\n    this.indexChange = new EventEmitter();\n  }\n  onArrowClick(step) {\n    this.updateIndex(tuiClamp(this.index + step, 0, this.length - 1));\n  }\n  get leftButtonDisabled() {\n    return this.index === 0;\n  }\n  get rightButtonDisabled() {\n    return this.index === this.length - 1;\n  }\n  updateIndex(index) {\n    if (this.index === index) {\n      return;\n    }\n    this.index = index;\n    this.indexChange.emit(index);\n  }\n  static {\n    this.ɵfac = function TuiPreviewPagination_Factory(t) {\n      return new (t || TuiPreviewPagination)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiPreviewPagination,\n      selectors: [[\"tui-preview-pagination\"]],\n      hostBindings: function TuiPreviewPagination_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.arrowRight.prevent\", function TuiPreviewPagination_keydown_arrowRight_prevent_HostBindingHandler() {\n            return ctx.onArrowClick(1);\n          }, false, i0.ɵɵresolveDocument)(\"keydown.arrowLeft.prevent\", function TuiPreviewPagination_keydown_arrowLeft_prevent_HostBindingHandler() {\n            return ctx.onArrowClick(-1);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        length: \"length\",\n        index: \"index\"\n      },\n      outputs: {\n        indexChange: \"indexChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 3,\n      consts: [[4, \"ngIf\"], [\"tuiIconButton\", \"\", \"tuiPreviewAction\", \"\", \"type\", \"button\", 1, \"t-arrow_left\", 3, \"click\", \"disabled\", \"iconStart\"], [\"tuiIconButton\", \"\", \"tuiPreviewAction\", \"\", \"type\", \"button\", 1, \"t-arrow_right\", 3, \"click\", \"disabled\", \"iconStart\"]],\n      template: function TuiPreviewPagination_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiPreviewPagination_ng_container_0_Template, 6, 8, \"ng-container\", 0);\n          i0.ɵɵpipe(1, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.texts$));\n        }\n      },\n      dependencies: [AsyncPipe, NgIf, TuiButton, TuiPreviewAction],\n      styles: [\"[_nghost-%COMP%]{border-radius:1rem;background:#686868f5;color:#fff;display:flex;font:var(--tui-font-text-s);justify-content:space-between;align-items:center;inline-size:6.25rem}.t-arrow_left[_ngcontent-%COMP%]{border-top-right-radius:0;border-bottom-right-radius:0}.t-arrow_right[_ngcontent-%COMP%]{border-top-left-radius:0;border-bottom-left-radius:0}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPreviewPagination, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-preview-pagination',\n      imports: [AsyncPipe, NgIf, TuiButton, TuiPreviewAction],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '(document:keydown.arrowRight.prevent)': 'onArrowClick(1)',\n        '(document:keydown.arrowLeft.prevent)': 'onArrowClick(-1)'\n      },\n      template: \"<ng-container *ngIf=\\\"texts$ | async as texts\\\">\\n    <button\\n        tuiIconButton\\n        tuiPreviewAction\\n        type=\\\"button\\\"\\n        class=\\\"t-arrow_left\\\"\\n        [disabled]=\\\"leftButtonDisabled\\\"\\n        [iconStart]=\\\"icons.prev\\\"\\n        (click)=\\\"onArrowClick(-1)\\\"\\n    >\\n        {{ texts[0] }}\\n    </button>\\n    {{ index + 1 }}/{{ length }}\\n    <button\\n        tuiIconButton\\n        tuiPreviewAction\\n        type=\\\"button\\\"\\n        class=\\\"t-arrow_right\\\"\\n        [disabled]=\\\"rightButtonDisabled\\\"\\n        [iconStart]=\\\"icons.next\\\"\\n        (click)=\\\"onArrowClick(1)\\\"\\n    >\\n        {{ texts[1] }}\\n    </button>\\n</ng-container>\\n\",\n      styles: [\":host{border-radius:1rem;background:#686868f5;color:#fff;display:flex;font:var(--tui-font-text-s);justify-content:space-between;align-items:center;inline-size:6.25rem}.t-arrow_left{border-top-right-radius:0;border-bottom-right-radius:0}.t-arrow_right{border-top-left-radius:0;border-bottom-left-radius:0}\\n\"]\n    }]\n  }], null, {\n    length: [{\n      type: Input\n    }],\n    index: [{\n      type: Input\n    }],\n    indexChange: [{\n      type: Output\n    }]\n  });\n})();\nconst STEP = 0.5;\nclass TuiPreviewZoom {\n  constructor() {\n    this.icons = inject(TUI_PREVIEW_ICONS);\n    this.zoomTexts$ = inject(TUI_PREVIEW_ZOOM_TEXTS);\n    this.min = 0.5;\n    this.max = 2;\n    this.value = 1;\n    this.valueChange = new EventEmitter();\n    this.reset = new EventEmitter();\n    this.hintShow$ = this.valueChange.pipe(switchMap(() => merge(of(true), timer(1000).pipe(map(TUI_FALSE_HANDLER)))), startWith(false));\n  }\n  get leftButtonDisabled() {\n    return this.value === this.min;\n  }\n  get rightButtonDisabled() {\n    return this.value === this.max;\n  }\n  get collapseVisible() {\n    return this.value > this.min;\n  }\n  onModelChange(value) {\n    const clamped = tuiClamp(value, this.min, this.max);\n    if (clamped === this.value) {\n      return;\n    }\n    this.value = clamped;\n    this.valueChange.emit(clamped);\n  }\n  onReset() {\n    this.reset.emit();\n  }\n  onMinus() {\n    this.onModelChange(this.value - STEP);\n  }\n  onPlus() {\n    this.onModelChange(this.value < 1 ? 1 : this.value + STEP);\n  }\n  static {\n    this.ɵfac = function TuiPreviewZoom_Factory(t) {\n      return new (t || TuiPreviewZoom)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiPreviewZoom,\n      selectors: [[\"tui-preview-zoom\"]],\n      inputs: {\n        min: \"min\",\n        max: \"max\",\n        value: \"value\"\n      },\n      outputs: {\n        valueChange: \"valueChange\",\n        reset: \"reset\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 3,\n      consts: [[\"hint\", \"\"], [4, \"ngIf\"], [1, \"t-zoom\"], [\"tuiIconButton\", \"\", \"tuiPreviewAction\", \"\", \"type\", \"button\", 1, \"t-sign_minus\", 3, \"click\", \"disabled\", \"iconStart\"], [\"tuiSliderThumbLabel\", \"\"], [\"tuiHintAppearance\", \"dark\", \"tuiHintDirection\", \"top-right\", 3, \"tuiHint\", \"tuiHintManual\"], [\"step\", \"any\", \"tuiSlider\", \"\", \"tuiTheme\", \"dark\", \"type\", \"range\", 1, \"t-slider\", 3, \"ngModelChange\", \"max\", \"min\", \"ngModel\", \"ngModelOptions\"], [\"tuiIconButton\", \"\", \"tuiPreviewAction\", \"\", \"type\", \"button\", 1, \"t-sign_plus\", 3, \"click\", \"disabled\", \"iconStart\"], [\"tuiHintAppearance\", \"dark\", \"tuiHintDescribe\", \"\", \"tuiHintDirection\", \"top-right\", \"tuiIconButton\", \"\", \"tuiPreviewAction\", \"\", \"type\", \"button\", 1, \"t-reset-button\", 3, \"click\", \"iconStart\", \"tuiHint\"]],\n      template: function TuiPreviewZoom_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiPreviewZoom_ng_container_0_Template, 13, 19, \"ng-container\", 1);\n          i0.ɵɵpipe(1, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.zoomTexts$));\n        }\n      },\n      dependencies: [AsyncPipe, FormsModule, i1.DefaultValueAccessor, i1.RangeValueAccessor, i1.NgControlStatus, i1.NgModel, NgIf, PercentPipe, TuiButton, i2.TuiHintDirective, i2.TuiHintDescribe, i2.TuiHintManual, TuiPreviewAction, i3.TuiSliderComponent, i3.TuiSliderThumbLabel],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:flex}.t-zoom[_ngcontent-%COMP%]{border-radius:1rem;background:#686868f5;color:#fff;display:flex;font:var(--tui-font-text-s);justify-content:space-between;align-items:center;inline-size:12rem}.t-slider[_ngcontent-%COMP%]{inline-size:7.5rem}.t-sign_minus[_ngcontent-%COMP%]{border-top-right-radius:0;border-bottom-right-radius:0}.t-sign_plus[_ngcontent-%COMP%]{border-top-left-radius:0;border-bottom-left-radius:0}.t-invisible[_ngcontent-%COMP%]{visibility:hidden}.t-reset-button[_ngcontent-%COMP%]{margin-left:.3125rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPreviewZoom, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-preview-zoom',\n      imports: [AsyncPipe, FormsModule, NgIf, PercentPipe, TuiButton, TuiHint, TuiPreviewAction, TuiSlider],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-container *ngIf=\\\"zoomTexts$ | async as texts\\\">\\n    <section class=\\\"t-zoom\\\">\\n        <button\\n            tuiIconButton\\n            tuiPreviewAction\\n            type=\\\"button\\\"\\n            class=\\\"t-sign_minus\\\"\\n            [disabled]=\\\"leftButtonDisabled\\\"\\n            [iconStart]=\\\"icons.zoomOut\\\"\\n            (click)=\\\"onMinus()\\\"\\n        >\\n            {{ texts.zoomOut }}\\n        </button>\\n        <label tuiSliderThumbLabel>\\n            <div\\n                tuiHintAppearance=\\\"dark\\\"\\n                tuiHintDirection=\\\"top-right\\\"\\n                [tuiHint]=\\\"hint\\\"\\n                [tuiHintManual]=\\\"!!(hintShow$ | async)\\\"\\n            ></div>\\n\\n            <ng-template #hint>\\n                {{ (valueChange | async) || value | percent }}\\n            </ng-template>\\n\\n            <input\\n                step=\\\"any\\\"\\n                tuiSlider\\n                tuiTheme=\\\"dark\\\"\\n                type=\\\"range\\\"\\n                class=\\\"t-slider\\\"\\n                [max]=\\\"max\\\"\\n                [min]=\\\"min\\\"\\n                [ngModel]=\\\"value\\\"\\n                [ngModelOptions]=\\\"{standalone: true}\\\"\\n                (ngModelChange)=\\\"onModelChange($event)\\\"\\n            />\\n        </label>\\n        <button\\n            tuiIconButton\\n            tuiPreviewAction\\n            type=\\\"button\\\"\\n            class=\\\"t-sign_plus\\\"\\n            [disabled]=\\\"rightButtonDisabled\\\"\\n            [iconStart]=\\\"icons.zoomIn\\\"\\n            (click)=\\\"onPlus()\\\"\\n        >\\n            {{ texts.zoomIn }}\\n        </button>\\n    </section>\\n\\n    <button\\n        tuiHintAppearance=\\\"dark\\\"\\n        tuiHintDescribe\\n        tuiHintDirection=\\\"top-right\\\"\\n        tuiIconButton\\n        tuiPreviewAction\\n        type=\\\"button\\\"\\n        class=\\\"t-reset-button\\\"\\n        [class.t-invisible]=\\\"!collapseVisible\\\"\\n        [iconStart]=\\\"icons.zoomReset\\\"\\n        [tuiHint]=\\\"texts.reset\\\"\\n        (click)=\\\"onReset()\\\"\\n    ></button>\\n</ng-container>\\n\",\n      styles: [\":host{position:relative;display:flex}.t-zoom{border-radius:1rem;background:#686868f5;color:#fff;display:flex;font:var(--tui-font-text-s);justify-content:space-between;align-items:center;inline-size:12rem}.t-slider{inline-size:7.5rem}.t-sign_minus{border-top-right-radius:0;border-bottom-right-radius:0}.t-sign_plus{border-top-left-radius:0;border-bottom-left-radius:0}.t-invisible{visibility:hidden}.t-reset-button{margin-left:.3125rem}\\n\"]\n    }]\n  }], null, {\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    reset: [{\n      type: Output\n    }]\n  });\n})();\nconst INITIAL_SCALE_COEF = 0.8;\nconst EMPTY_COORDINATES = [0, 0];\nconst ROTATION_ANGLE = 90;\nclass TuiPreviewComponent {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.minZoom = 1;\n    this.width = 0;\n    this.height = 0;\n    this.texts$ = inject(TUI_PREVIEW_TEXTS);\n    this.icons = inject(TUI_PREVIEW_ICONS);\n    this.cdr = inject(ChangeDetectorRef);\n    this.zoom$ = new BehaviorSubject(this.minZoom);\n    this.rotation$ = new BehaviorSubject(0);\n    this.coordinates$ = new BehaviorSubject(EMPTY_COORDINATES);\n    this.transitioned$ = merge(tuiDragAndDropFrom(this.el).pipe(map(({\n      stage\n    }) => stage !== 'continues')), tuiTypedFromEvent(this.el, 'touchmove', {\n      passive: true\n    }).pipe(map(TUI_FALSE_HANDLER)), tuiTypedFromEvent(this.el, 'wheel', {\n      passive: true\n    }).pipe(map(TUI_FALSE_HANDLER)));\n    this.cursor$ = tuiDragAndDropFrom(this.el).pipe(map(({\n      stage\n    }) => stage === 'continues' ? 'grabbing' : 'initial'), startWith('initial'));\n    this.wrapperTransform$ = combineLatest([this.coordinates$.pipe(map(([x, y]) => `${tuiPx(x)}, ${tuiPx(y)}`)), this.zoom$, this.rotation$]).pipe(map(([translate, zoom, rotation]) => `translate(${translate}) scale(${zoom}) rotate(${rotation}deg)`));\n    this.zoomable = true;\n    this.rotatable = false;\n  }\n  rotate() {\n    this.rotation$.next(this.rotation$.value - ROTATION_ANGLE);\n  }\n  onPan(delta) {\n    this.coordinates$.next(this.getGuardedCoordinates(this.coordinates$.value[0] + delta[0], this.coordinates$.value[1] + delta[1]));\n  }\n  onMutation(contentWrapper) {\n    const {\n      clientWidth,\n      clientHeight\n    } = contentWrapper;\n    this.refresh(clientWidth, clientHeight);\n  }\n  onZoom({\n    clientX,\n    clientY,\n    delta\n  }) {\n    if (this.zoomable) {\n      this.processZoom(clientX, clientY, delta);\n    }\n  }\n  onResize([entry]) {\n    if (entry?.contentRect) {\n      this.refresh(entry.contentRect.width, entry.contentRect.height);\n      this.cdr.detectChanges();\n    }\n  }\n  reset() {\n    this.zoom$.next(this.minZoom);\n    this.coordinates$.next(EMPTY_COORDINATES);\n  }\n  setZoom(zoom) {\n    this.zoom$.next(zoom);\n    const [x, y] = this.coordinates$.value;\n    this.coordinates$.next(this.getGuardedCoordinates(x, y));\n  }\n  get offsets() {\n    const offsetX = (this.zoom$.value - this.minZoom) * this.width / 2;\n    const offsetY = (this.zoom$.value - this.minZoom) * this.height / 2;\n    return {\n      offsetX,\n      offsetY\n    };\n  }\n  calculateMinZoom(contentHeight, contentWidth, boxHeight, boxWidth) {\n    const bigSize = contentHeight > boxHeight * INITIAL_SCALE_COEF || contentWidth > boxWidth * INITIAL_SCALE_COEF;\n    const {\n      clientHeight,\n      clientWidth\n    } = this.el;\n    return bigSize ? tuiRound(Math.min(clientHeight * INITIAL_SCALE_COEF / contentHeight, clientWidth * INITIAL_SCALE_COEF / contentWidth), 2) : 1;\n  }\n  refresh(width, height) {\n    this.width = width;\n    this.height = height;\n    this.minZoom = this.calculateMinZoom(height, width, this.el.clientHeight, this.el.clientWidth);\n    this.zoom$.next(this.minZoom);\n    this.coordinates$.next(EMPTY_COORDINATES);\n    this.rotation$.next(0);\n  }\n  processZoom(clientX, clientY, delta) {\n    const oldScale = this.zoom$.value;\n    const newScale = tuiClamp(oldScale + delta, this.minZoom, 2);\n    const center = this.getScaleCenter({\n      clientX,\n      clientY\n    }, this.coordinates$.value, this.zoom$.value);\n    const moveX = center[0] * oldScale - center[0] * newScale;\n    const moveY = center[1] * oldScale - center[1] * newScale;\n    this.zoom$.next(newScale);\n    this.coordinates$.next(this.getGuardedCoordinates(this.coordinates$.value[0] + moveX, this.coordinates$.value[1] + moveY));\n  }\n  getGuardedCoordinates(x, y) {\n    const {\n      offsetX,\n      offsetY\n    } = this.offsets;\n    return [tuiClamp(x, -offsetX, offsetX), tuiClamp(y, -offsetY, offsetY)];\n  }\n  getScaleCenter({\n    clientX,\n    clientY\n  }, [x, y], scale) {\n    return [(clientX - x - this.el.offsetWidth / 2) / scale, (clientY - y - this.el.offsetHeight / 2) / scale];\n  }\n  static {\n    this.ɵfac = function TuiPreviewComponent_Factory(t) {\n      return new (t || TuiPreviewComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiPreviewComponent,\n      selectors: [[\"tui-preview\"]],\n      inputs: {\n        zoomable: \"zoomable\",\n        rotatable: \"rotatable\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 2,\n      vars: 3,\n      consts: [[\"contentWrapper\", \"\"], [4, \"ngIf\"], [\"attributeFilter\", \"src\", \"characterData\", \"\", \"childList\", \"\", \"subtree\", \"\", 1, \"t-wrapper\", 3, \"tuiPan\", \"tuiZoom\", \"waMutationObserver\", \"waResizeObserver\"], [1, \"t-header\"], [1, \"t-title\"], [1, \"t-actions\"], [1, \"t-footer\"], [\"tuiHintAppearance\", \"dark\", \"tuiHintDescribe\", \"\", \"tuiHintDirection\", \"top-right\", \"tuiIconButton\", \"\", \"tuiPreviewAction\", \"\", \"type\", \"button\", \"class\", \"t-rotate-button\", 3, \"iconStart\", \"tuiHint\", \"click\", 4, \"ngIf\"], [3, \"min\", \"value\", \"reset\", \"valueChange\", 4, \"ngIf\"], [\"tuiHintAppearance\", \"dark\", \"tuiHintDescribe\", \"\", \"tuiHintDirection\", \"top-right\", \"tuiIconButton\", \"\", \"tuiPreviewAction\", \"\", \"type\", \"button\", 1, \"t-rotate-button\", 3, \"click\", \"iconStart\", \"tuiHint\"], [3, \"reset\", \"valueChange\", \"min\", \"value\"]],\n      template: function TuiPreviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵtemplate(0, TuiPreviewComponent_ng_container_0_Template, 16, 16, \"ng-container\", 1);\n          i0.ɵɵpipe(1, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.texts$));\n        }\n      },\n      dependencies: [AsyncPipe, NgIf, TuiButton, i2.TuiHintDirective, i2.TuiHintDescribe, TuiPan, TuiPreviewAction, TuiPreviewZoom, TuiZoom, WaMutationObserver, WaResizeObserver],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:flex;justify-content:center;align-items:center;inline-size:100%;block-size:100%;-webkit-user-select:none;user-select:none}.t-header[_ngcontent-%COMP%]{position:fixed;top:1rem;display:flex;inline-size:100%;padding:0 1rem;box-sizing:border-box}.t-footer[_ngcontent-%COMP%]{position:absolute;bottom:1rem;display:flex;inline-size:100%;padding:0 1rem;box-sizing:border-box;justify-content:center}.t-actions[_ngcontent-%COMP%]{display:flex;flex:1;justify-content:flex-end}.t-actions[_ngcontent-%COMP%]    >*{margin-left:.625rem}.t-rotate-button[_ngcontent-%COMP%]{margin-right:.3125rem}.t-title[_ngcontent-%COMP%]{flex:1}tui-root._mobile[_nghost-%COMP%]   .t-title[_ngcontent-%COMP%], tui-root._mobile   [_nghost-%COMP%]   .t-title[_ngcontent-%COMP%]{display:none}.t-not-interactive-content[_ngcontent-%COMP%]    >*{pointer-events:none}.t-wrapper[_ngcontent-%COMP%]{will-change:transform}.t-transitive[_ngcontent-%COMP%]{transition-duration:.3s}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPreviewComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-preview',\n      imports: [AsyncPipe, NgIf, TuiButton, TuiHint, TuiPan, TuiPreviewAction, TuiPreviewZoom, TuiZoom, WaMutationObserver, WaResizeObserver],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-container *ngIf=\\\"texts$ | async as texts\\\">\\n    <section\\n        #contentWrapper\\n        attributeFilter=\\\"src\\\"\\n        characterData\\n        childList\\n        subtree\\n        class=\\\"t-wrapper\\\"\\n        [class.t-not-interactive-content]=\\\"zoomable\\\"\\n        [class.t-transitive]=\\\"transitioned$ | async\\\"\\n        [style.cursor]=\\\"cursor$ | async\\\"\\n        [style.transform]=\\\"wrapperTransform$ | async\\\"\\n        (tuiPan)=\\\"onPan($event)\\\"\\n        (tuiZoom)=\\\"onZoom($event)\\\"\\n        (waMutationObserver)=\\\"onMutation(contentWrapper)\\\"\\n        (waResizeObserver)=\\\"onResize($event)\\\"\\n    >\\n        <ng-content />\\n    </section>\\n\\n    <header class=\\\"t-header\\\">\\n        <div class=\\\"t-title\\\">\\n            <ng-content select=\\\"tui-preview-title\\\" />\\n        </div>\\n\\n        <ng-content select=\\\"tui-preview-pagination\\\" />\\n\\n        <div class=\\\"t-actions\\\">\\n            <ng-content select=\\\"[tuiPreviewAction]\\\" />\\n        </div>\\n    </header>\\n\\n    <footer class=\\\"t-footer\\\">\\n        <button\\n            *ngIf=\\\"rotatable\\\"\\n            tuiHintAppearance=\\\"dark\\\"\\n            tuiHintDescribe\\n            tuiHintDirection=\\\"top-right\\\"\\n            tuiIconButton\\n            tuiPreviewAction\\n            type=\\\"button\\\"\\n            class=\\\"t-rotate-button\\\"\\n            [iconStart]=\\\"icons.rotate\\\"\\n            [tuiHint]=\\\"texts.rotate\\\"\\n            (click)=\\\"rotate()\\\"\\n        ></button>\\n\\n        <tui-preview-zoom\\n            *ngIf=\\\"zoomable\\\"\\n            [min]=\\\"minZoom\\\"\\n            [value]=\\\"(zoom$ | async) || 1\\\"\\n            (reset)=\\\"reset()\\\"\\n            (valueChange)=\\\"setZoom($event)\\\"\\n        />\\n    </footer>\\n</ng-container>\\n\",\n      styles: [\":host{position:relative;display:flex;justify-content:center;align-items:center;inline-size:100%;block-size:100%;-webkit-user-select:none;user-select:none}.t-header{position:fixed;top:1rem;display:flex;inline-size:100%;padding:0 1rem;box-sizing:border-box}.t-footer{position:absolute;bottom:1rem;display:flex;inline-size:100%;padding:0 1rem;box-sizing:border-box;justify-content:center}.t-actions{display:flex;flex:1;justify-content:flex-end}.t-actions ::ng-deep>*{margin-left:.625rem}.t-rotate-button{margin-right:.3125rem}.t-title{flex:1}:host-context(tui-root._mobile) .t-title{display:none}.t-not-interactive-content ::ng-deep>*{pointer-events:none}.t-wrapper{will-change:transform}.t-transitive{transition-duration:.3s}\\n\"]\n    }]\n  }], null, {\n    zoomable: [{\n      type: Input\n    }],\n    rotatable: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiPreviewTitle {\n  static {\n    this.ɵfac = function TuiPreviewTitle_Factory(t) {\n      return new (t || TuiPreviewTitle)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiPreviewTitle,\n      selectors: [[\"tui-preview-title\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c3,\n      decls: 1,\n      vars: 0,\n      template: function TuiPreviewTitle_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\"[_nghost-%COMP%]{border-radius:1rem;background:#686868f5;color:#fff;display:inline-block;font:var(--tui-font-text-s);padding:.375rem .75rem}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPreviewTitle, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-preview-title',\n      template: `\n        <ng-content />\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\":host{border-radius:1rem;background:#686868f5;color:#fff;display:inline-block;font:var(--tui-font-text-s);padding:.375rem .75rem}\\n\"]\n    }]\n  }], null, null);\n})();\nconst TuiPreview = [TuiPreviewComponent, TuiPreviewTitle, TuiPreviewPagination, TuiPreviewAction, TuiPreviewZoom];\nclass TuiPreviewDialog {\n  constructor() {\n    this.context = injectContext();\n  }\n  static {\n    this.ɵfac = function TuiPreviewDialog_Factory(t) {\n      return new (t || TuiPreviewDialog)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiPreviewDialog,\n      selectors: [[\"tui-preview-dialog\"]],\n      hostBindings: function TuiPreviewDialog_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.esc\", function TuiPreviewDialog_keydown_esc_HostBindingHandler() {\n            return ctx.context.$implicit.complete();\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([i1$1.TuiAnimated]), i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 2,\n      consts: [[4, \"polymorpheusOutlet\", \"polymorpheusOutletContext\"]],\n      template: function TuiPreviewDialog_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiPreviewDialog_ng_container_0_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"polymorpheusOutlet\", ctx.context.content)(\"polymorpheusOutletContext\", ctx.context);\n        }\n      },\n      dependencies: [PolymorpheusOutlet],\n      styles: [\"tui-preview-dialog{inline-size:100%;block-size:100%}tui-preview-dialog.tui-enter,tui-preview-dialog.tui-leave{animation-name:tuiFade,tuiSlide}[tuiAppearance][data-appearance=preview-action]{background:#686868f5;color:#fff}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=preview-action]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:#9f9f9fdb}}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=preview-action]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:#9f9f9fdb}}[tuiAppearance][data-appearance=preview-action][data-state=hover]{background:#9f9f9fdb}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=preview-action][tuiWrapper]:hover:not(._no-hover),[tuiAppearance][data-appearance=preview-action][tuiWrapper][data-state=hover]{background:#9f9f9fdb}}[tuiAppearance][data-appearance=preview-action]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:#9f9f9fbf}[tuiAppearance][data-appearance=preview-action]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:#9f9f9fbf}[tuiAppearance][data-appearance=preview-action][data-state=active]{background:#9f9f9fbf}[tuiAppearance][data-appearance=preview-action][tuiWrapper]:active:not(._no-active),[tuiAppearance][data-appearance=preview-action][tuiWrapper][data-state=active],[tuiAppearance][data-appearance=preview-action][tuiWrapper][data-state=active]:hover{background:#9f9f9fbf}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPreviewDialog, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-preview-dialog',\n      imports: [PolymorpheusOutlet],\n      template: `\n        <ng-container *polymorpheusOutlet=\"context.content as text; context: context\">\n            {{ text }}\n        </ng-container>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      hostDirectives: [TuiAnimated],\n      host: {\n        '(document:keydown.esc)': 'context.$implicit.complete()'\n      },\n      styles: [\"tui-preview-dialog{inline-size:100%;block-size:100%}tui-preview-dialog.tui-enter,tui-preview-dialog.tui-leave{animation-name:tuiFade,tuiSlide}[tuiAppearance][data-appearance=preview-action]{background:#686868f5;color:#fff}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=preview-action]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:#9f9f9fdb}}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=preview-action]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:#9f9f9fdb}}[tuiAppearance][data-appearance=preview-action][data-state=hover]{background:#9f9f9fdb}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=preview-action][tuiWrapper]:hover:not(._no-hover),[tuiAppearance][data-appearance=preview-action][tuiWrapper][data-state=hover]{background:#9f9f9fdb}}[tuiAppearance][data-appearance=preview-action]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:#9f9f9fbf}[tuiAppearance][data-appearance=preview-action]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:#9f9f9fbf}[tuiAppearance][data-appearance=preview-action][data-state=active]{background:#9f9f9fbf}[tuiAppearance][data-appearance=preview-action][tuiWrapper]:active:not(._no-active),[tuiAppearance][data-appearance=preview-action][tuiWrapper][data-state=active],[tuiAppearance][data-appearance=preview-action][tuiWrapper][data-state=active]:hover{background:#9f9f9fbf}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiPreviewDialogService extends TuiPopoverService {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiPreviewDialogService_BaseFactory;\n      return function TuiPreviewDialogService_Factory(t) {\n        return (ɵTuiPreviewDialogService_BaseFactory || (ɵTuiPreviewDialogService_BaseFactory = i0.ɵɵgetInheritedFactory(TuiPreviewDialogService)))(t || TuiPreviewDialogService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiPreviewDialogService,\n      factory: () => (() => new TuiPreviewDialogService(TUI_DIALOGS, TuiPreviewDialog))(),\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPreviewDialogService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => new TuiPreviewDialogService(TUI_DIALOGS, TuiPreviewDialog)\n    }]\n  }], null, null);\n})();\nclass TuiPreviewDialogDirective extends TuiPopoverDirective {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTuiPreviewDialogDirective_BaseFactory;\n      return function TuiPreviewDialogDirective_Factory(t) {\n        return (ɵTuiPreviewDialogDirective_BaseFactory || (ɵTuiPreviewDialogDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TuiPreviewDialogDirective)))(t || TuiPreviewDialogDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiPreviewDialogDirective,\n      selectors: [[\"ng-template\", \"tuiPreviewDialog\", \"\"]],\n      inputs: {\n        open: [i0.ɵɵInputFlags.None, \"tuiPreviewDialog\", \"open\"]\n      },\n      outputs: {\n        openChange: \"tuiPreviewDialogChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiAsPopover(TuiPreviewDialogService)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPreviewDialogDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'ng-template[tuiPreviewDialog]',\n      inputs: ['open: tuiPreviewDialog'],\n      outputs: ['openChange: tuiPreviewDialogChange'],\n      providers: [tuiAsPopover(TuiPreviewDialogService)]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPreview, TuiPreviewAction, TuiPreviewComponent, TuiPreviewDialog, TuiPreviewDialogDirective, TuiPreviewDialogService, TuiPreviewPagination, TuiPreviewTitle, TuiPreviewZoom };", "map": {"version": 3, "names": ["AsyncPipe", "NgIf", "Percent<PERSON><PERSON>e", "i0", "Directive", "inject", "EventEmitter", "Component", "ChangeDetectionStrategy", "Input", "Output", "ChangeDetectorRef", "ViewEncapsulation", "Injectable", "tui<PERSON><PERSON>", "tuiRound", "tuiButtonOptionsProvider", "TuiButton", "TUI_PREVIEW_ICONS", "TUI_PAGINATION_TEXTS", "TUI_PREVIEW_ZOOM_TEXTS", "TUI_PREVIEW_TEXTS", "WaMutationObserver", "WaResizeObserver", "TUI_FALSE_HANDLER", "<PERSON><PERSON><PERSON><PERSON>", "TuiZoom", "tuiDragAndDropFrom", "tuiTypedFromEvent", "tuiInjectElement", "tuiPx", "i2", "TuiHint", "switchMap", "merge", "of", "timer", "map", "startWith", "BehaviorSubject", "combineLatest", "i1", "FormsModule", "i3", "TuiSlider", "i1$1", "TuiAnimated", "injectContext", "Polymorpheus<PERSON><PERSON>let", "TuiPopoverDirective", "TuiPopoverService", "tuiAsPopover", "TUI_DIALOGS", "TuiPreviewPagination_ng_container_0_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "TuiPreviewPagination_ng_container_0_Template_button_click_1_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onArrowClick", "ɵɵtext", "ɵɵelementEnd", "TuiPreviewPagination_ng_container_0_Template_button_click_4_listener", "ɵɵelementContainerEnd", "texts_r3", "ngIf", "ɵɵadvance", "ɵɵproperty", "leftButtonDisabled", "icons", "prev", "ɵɵtextInterpolate1", "ɵɵtextInterpolate2", "index", "length", "rightButtonDisabled", "next", "_c0", "standalone", "TuiPreviewZoom_ng_container_0_ng_template_7_Template", "ɵɵpipe", "ɵɵpipeBind1", "valueChange", "value", "TuiPreviewZoom_ng_container_0_Template", "TuiPreviewZoom_ng_container_0_Template_button_click_2_listener", "onMinus", "ɵɵelement", "ɵɵtemplate", "ɵɵtemplateRefExtractor", "TuiPreviewZoom_ng_container_0_Template_input_ngModelChange_9_listener", "$event", "onModelChange", "TuiPreviewZoom_ng_container_0_Template_button_click_10_listener", "onPlus", "TuiPreviewZoom_ng_container_0_Template_button_click_12_listener", "onReset", "hint_r4", "ɵɵreference", "zoomOut", "hintShow$", "max", "min", "ɵɵpureFunction0", "zoomIn", "ɵɵclassProp", "collapseVisible", "zoomReset", "reset", "_c1", "_c2", "TuiPreviewComponent_ng_container_0_button_14_Template", "_r4", "TuiPreviewComponent_ng_container_0_button_14_Template_button_click_0_listener", "rotate", "texts_r5", "TuiPreviewComponent_ng_container_0_tui_preview_zoom_15_Template", "_r6", "TuiPreviewComponent_ng_container_0_tui_preview_zoom_15_Template_tui_preview_zoom_reset_0_listener", "TuiPreviewComponent_ng_container_0_tui_preview_zoom_15_Template_tui_preview_zoom_valueChange_0_listener", "setZoom", "minZoom", "zoom$", "TuiPreviewComponent_ng_container_0_Template", "TuiPreviewComponent_ng_container_0_Template_section_tuiPan_1_listener", "onPan", "TuiPreviewComponent_ng_container_0_Template_section_tuiZoom_1_listener", "onZoom", "TuiPreviewComponent_ng_container_0_Template_section_waMutationObserver_1_listener", "contentWrapper_r3", "onMutation", "TuiPreviewComponent_ng_container_0_Template_section_waResizeObserver_1_listener", "onResize", "ɵɵprojection", "ɵɵstyleProp", "cursor$", "wrapperTransform$", "zoomable", "transitioned$", "rotatable", "_c3", "TuiPreviewDialog_ng_container_0_Template", "text_r1", "polymorpheusOutlet", "TuiPreviewAction", "ɵfac", "TuiPreviewAction_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostVars", "hostBindings", "TuiPreviewAction_HostBindings", "features", "ɵɵProvidersFeature", "appearance", "size", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "host", "TuiPreviewPagination", "constructor", "texts$", "indexChange", "step", "updateIndex", "emit", "TuiPreviewPagination_Factory", "ɵcmp", "ɵɵdefineComponent", "TuiPreviewPagination_HostBindings", "TuiPreviewPagination_keydown_arrowRight_prevent_HostBindingHandler", "ɵɵresolveDocument", "TuiPreviewPagination_keydown_arrowLeft_prevent_HostBindingHandler", "inputs", "outputs", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiPreviewPagination_Template", "dependencies", "styles", "changeDetection", "imports", "OnPush", "STEP", "TuiPreviewZoom", "zoomTexts$", "pipe", "clamped", "TuiPreviewZoom_Factory", "TuiPreviewZoom_Template", "DefaultValueAccessor", "RangeValueAccessor", "NgControlStatus", "NgModel", "TuiHintDirective", "TuiHintDescribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TuiSliderComponent", "TuiSliderThumbLabel", "INITIAL_SCALE_COEF", "EMPTY_COORDINATES", "ROTATION_ANGLE", "TuiPreviewComponent", "el", "width", "height", "cdr", "rotation$", "coordinates$", "stage", "passive", "x", "y", "translate", "zoom", "rotation", "delta", "getGuardedCoordinates", "contentWrapper", "clientWidth", "clientHeight", "refresh", "clientX", "clientY", "processZoom", "entry", "contentRect", "detectChanges", "offsets", "offsetX", "offsetY", "calculateMinZoom", "contentHeight", "contentWidth", "boxHeight", "boxWidth", "bigSize", "Math", "oldScale", "newScale", "center", "getScaleCenter", "moveX", "moveY", "scale", "offsetWidth", "offsetHeight", "TuiPreviewComponent_Factory", "ngContentSelectors", "TuiPreviewComponent_Template", "ɵɵprojectionDef", "TuiPreviewTitle", "TuiPreviewTitle_Factory", "TuiPreviewTitle_Template", "TuiPreview", "TuiPreviewDialog", "context", "TuiPreviewDialog_Factory", "TuiPreviewDialog_HostBindings", "TuiPreviewDialog_keydown_esc_HostBindingHandler", "$implicit", "complete", "ɵɵHostDirectivesFeature", "TuiPreviewDialog_Template", "content", "encapsulation", "None", "hostDirectives", "TuiPreviewDialogService", "ɵTuiPreviewDialogService_BaseFactory", "TuiPreviewDialogService_Factory", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "useFactory", "TuiPreviewDialogDirective", "ɵTuiPreviewDialogDirective_BaseFactory", "TuiPreviewDialogDirective_Factory", "open", "ɵɵInputFlags", "openChange", "ɵɵInheritDefinitionFeature"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-preview.mjs"], "sourcesContent": ["import { Async<PERSON>ipe, NgIf, PercentPipe } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, inject, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, ChangeDetectorRef, ViewEncapsulation, Injectable } from '@angular/core';\nimport { tuiClamp, tuiRound } from '@taiga-ui/cdk/utils/math';\nimport { tuiButtonOptionsProvider, TuiButton } from '@taiga-ui/core/components/button';\nimport { TUI_PREVIEW_ICONS, TUI_PAGINATION_TEXTS, TUI_PREVIEW_ZOOM_TEXTS, TUI_PREVIEW_TEXTS } from '@taiga-ui/kit/tokens';\nimport { WaMutationObserver } from '@ng-web-apis/mutation-observer';\nimport { WaResizeObserver } from '@ng-web-apis/resize-observer';\nimport { TUI_FALSE_HANDLER } from '@taiga-ui/cdk/constants';\nimport { TuiPan } from '@taiga-ui/cdk/directives/pan';\nimport { TuiZoom } from '@taiga-ui/cdk/directives/zoom';\nimport { tuiDragAndDropFrom, tuiTypedFromEvent } from '@taiga-ui/cdk/observables';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiPx } from '@taiga-ui/cdk/utils/miscellaneous';\nimport * as i2 from '@taiga-ui/core/directives/hint';\nimport { TuiHint } from '@taiga-ui/core/directives/hint';\nimport { switchMap, merge, of, timer, map, startWith, BehaviorSubject, combineLatest } from 'rxjs';\nimport * as i1 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport * as i3 from '@taiga-ui/kit/components/slider';\nimport { TuiSlider } from '@taiga-ui/kit/components/slider';\nimport * as i1$1 from '@taiga-ui/cdk/directives/animated';\nimport { TuiAnimated } from '@taiga-ui/cdk/directives/animated';\nimport { injectContext, PolymorpheusOutlet } from '@taiga-ui/polymorpheus';\nimport { TuiPopoverDirective } from '@taiga-ui/cdk/directives/popover';\nimport { TuiPopoverService, tuiAsPopover } from '@taiga-ui/cdk/services';\nimport { TUI_DIALOGS } from '@taiga-ui/core/components/dialog';\n\nclass TuiPreviewAction {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewAction, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPreviewAction, isStandalone: true, selector: \"[tuiPreviewAction]\", host: { properties: { \"style.border-radius.rem\": \"100\" } }, providers: [\n            tuiButtonOptionsProvider({\n                appearance: 'preview-action',\n                size: 's',\n            }),\n        ], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewAction, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiPreviewAction]',\n                    providers: [\n                        tuiButtonOptionsProvider({\n                            appearance: 'preview-action',\n                            size: 's',\n                        }),\n                    ],\n                    host: {\n                        '[style.border-radius.rem]': '100',\n                    },\n                }]\n        }] });\n\nclass TuiPreviewPagination {\n    constructor() {\n        this.icons = inject(TUI_PREVIEW_ICONS);\n        this.texts$ = inject(TUI_PAGINATION_TEXTS);\n        this.length = 1;\n        this.index = 0;\n        this.indexChange = new EventEmitter();\n    }\n    onArrowClick(step) {\n        this.updateIndex(tuiClamp(this.index + step, 0, this.length - 1));\n    }\n    get leftButtonDisabled() {\n        return this.index === 0;\n    }\n    get rightButtonDisabled() {\n        return this.index === this.length - 1;\n    }\n    updateIndex(index) {\n        if (this.index === index) {\n            return;\n        }\n        this.index = index;\n        this.indexChange.emit(index);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewPagination, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPreviewPagination, isStandalone: true, selector: \"tui-preview-pagination\", inputs: { length: \"length\", index: \"index\" }, outputs: { indexChange: \"indexChange\" }, host: { listeners: { \"document:keydown.arrowRight.prevent\": \"onArrowClick(1)\", \"document:keydown.arrowLeft.prevent\": \"onArrowClick(-1)\" } }, ngImport: i0, template: \"<ng-container *ngIf=\\\"texts$ | async as texts\\\">\\n    <button\\n        tuiIconButton\\n        tuiPreviewAction\\n        type=\\\"button\\\"\\n        class=\\\"t-arrow_left\\\"\\n        [disabled]=\\\"leftButtonDisabled\\\"\\n        [iconStart]=\\\"icons.prev\\\"\\n        (click)=\\\"onArrowClick(-1)\\\"\\n    >\\n        {{ texts[0] }}\\n    </button>\\n    {{ index + 1 }}/{{ length }}\\n    <button\\n        tuiIconButton\\n        tuiPreviewAction\\n        type=\\\"button\\\"\\n        class=\\\"t-arrow_right\\\"\\n        [disabled]=\\\"rightButtonDisabled\\\"\\n        [iconStart]=\\\"icons.next\\\"\\n        (click)=\\\"onArrowClick(1)\\\"\\n    >\\n        {{ texts[1] }}\\n    </button>\\n</ng-container>\\n\", styles: [\":host{border-radius:1rem;background:#686868f5;color:#fff;display:flex;font:var(--tui-font-text-s);justify-content:space-between;align-items:center;inline-size:6.25rem}.t-arrow_left{border-top-right-radius:0;border-bottom-right-radius:0}.t-arrow_right{border-top-left-radius:0;border-bottom-left-radius:0}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"directive\", type: TuiPreviewAction, selector: \"[tuiPreviewAction]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewPagination, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-preview-pagination', imports: [AsyncPipe, NgIf, TuiButton, TuiPreviewAction], changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '(document:keydown.arrowRight.prevent)': 'onArrowClick(1)',\n                        '(document:keydown.arrowLeft.prevent)': 'onArrowClick(-1)',\n                    }, template: \"<ng-container *ngIf=\\\"texts$ | async as texts\\\">\\n    <button\\n        tuiIconButton\\n        tuiPreviewAction\\n        type=\\\"button\\\"\\n        class=\\\"t-arrow_left\\\"\\n        [disabled]=\\\"leftButtonDisabled\\\"\\n        [iconStart]=\\\"icons.prev\\\"\\n        (click)=\\\"onArrowClick(-1)\\\"\\n    >\\n        {{ texts[0] }}\\n    </button>\\n    {{ index + 1 }}/{{ length }}\\n    <button\\n        tuiIconButton\\n        tuiPreviewAction\\n        type=\\\"button\\\"\\n        class=\\\"t-arrow_right\\\"\\n        [disabled]=\\\"rightButtonDisabled\\\"\\n        [iconStart]=\\\"icons.next\\\"\\n        (click)=\\\"onArrowClick(1)\\\"\\n    >\\n        {{ texts[1] }}\\n    </button>\\n</ng-container>\\n\", styles: [\":host{border-radius:1rem;background:#686868f5;color:#fff;display:flex;font:var(--tui-font-text-s);justify-content:space-between;align-items:center;inline-size:6.25rem}.t-arrow_left{border-top-right-radius:0;border-bottom-right-radius:0}.t-arrow_right{border-top-left-radius:0;border-bottom-left-radius:0}\\n\"] }]\n        }], propDecorators: { length: [{\n                type: Input\n            }], index: [{\n                type: Input\n            }], indexChange: [{\n                type: Output\n            }] } });\n\nconst STEP = 0.5;\nclass TuiPreviewZoom {\n    constructor() {\n        this.icons = inject(TUI_PREVIEW_ICONS);\n        this.zoomTexts$ = inject(TUI_PREVIEW_ZOOM_TEXTS);\n        this.min = 0.5;\n        this.max = 2;\n        this.value = 1;\n        this.valueChange = new EventEmitter();\n        this.reset = new EventEmitter();\n        this.hintShow$ = this.valueChange.pipe(switchMap(() => merge(of(true), timer(1000).pipe(map(TUI_FALSE_HANDLER)))), startWith(false));\n    }\n    get leftButtonDisabled() {\n        return this.value === this.min;\n    }\n    get rightButtonDisabled() {\n        return this.value === this.max;\n    }\n    get collapseVisible() {\n        return this.value > this.min;\n    }\n    onModelChange(value) {\n        const clamped = tuiClamp(value, this.min, this.max);\n        if (clamped === this.value) {\n            return;\n        }\n        this.value = clamped;\n        this.valueChange.emit(clamped);\n    }\n    onReset() {\n        this.reset.emit();\n    }\n    onMinus() {\n        this.onModelChange(this.value - STEP);\n    }\n    onPlus() {\n        this.onModelChange(this.value < 1 ? 1 : this.value + STEP);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewZoom, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPreviewZoom, isStandalone: true, selector: \"tui-preview-zoom\", inputs: { min: \"min\", max: \"max\", value: \"value\" }, outputs: { valueChange: \"valueChange\", reset: \"reset\" }, ngImport: i0, template: \"<ng-container *ngIf=\\\"zoomTexts$ | async as texts\\\">\\n    <section class=\\\"t-zoom\\\">\\n        <button\\n            tuiIconButton\\n            tuiPreviewAction\\n            type=\\\"button\\\"\\n            class=\\\"t-sign_minus\\\"\\n            [disabled]=\\\"leftButtonDisabled\\\"\\n            [iconStart]=\\\"icons.zoomOut\\\"\\n            (click)=\\\"onMinus()\\\"\\n        >\\n            {{ texts.zoomOut }}\\n        </button>\\n        <label tuiSliderThumbLabel>\\n            <div\\n                tuiHintAppearance=\\\"dark\\\"\\n                tuiHintDirection=\\\"top-right\\\"\\n                [tuiHint]=\\\"hint\\\"\\n                [tuiHintManual]=\\\"!!(hintShow$ | async)\\\"\\n            ></div>\\n\\n            <ng-template #hint>\\n                {{ (valueChange | async) || value | percent }}\\n            </ng-template>\\n\\n            <input\\n                step=\\\"any\\\"\\n                tuiSlider\\n                tuiTheme=\\\"dark\\\"\\n                type=\\\"range\\\"\\n                class=\\\"t-slider\\\"\\n                [max]=\\\"max\\\"\\n                [min]=\\\"min\\\"\\n                [ngModel]=\\\"value\\\"\\n                [ngModelOptions]=\\\"{standalone: true}\\\"\\n                (ngModelChange)=\\\"onModelChange($event)\\\"\\n            />\\n        </label>\\n        <button\\n            tuiIconButton\\n            tuiPreviewAction\\n            type=\\\"button\\\"\\n            class=\\\"t-sign_plus\\\"\\n            [disabled]=\\\"rightButtonDisabled\\\"\\n            [iconStart]=\\\"icons.zoomIn\\\"\\n            (click)=\\\"onPlus()\\\"\\n        >\\n            {{ texts.zoomIn }}\\n        </button>\\n    </section>\\n\\n    <button\\n        tuiHintAppearance=\\\"dark\\\"\\n        tuiHintDescribe\\n        tuiHintDirection=\\\"top-right\\\"\\n        tuiIconButton\\n        tuiPreviewAction\\n        type=\\\"button\\\"\\n        class=\\\"t-reset-button\\\"\\n        [class.t-invisible]=\\\"!collapseVisible\\\"\\n        [iconStart]=\\\"icons.zoomReset\\\"\\n        [tuiHint]=\\\"texts.reset\\\"\\n        (click)=\\\"onReset()\\\"\\n    ></button>\\n</ng-container>\\n\", styles: [\":host{position:relative;display:flex}.t-zoom{border-radius:1rem;background:#686868f5;color:#fff;display:flex;font:var(--tui-font-text-s);justify-content:space-between;align-items:center;inline-size:12rem}.t-slider{inline-size:7.5rem}.t-sign_minus{border-top-right-radius:0;border-bottom-right-radius:0}.t-sign_plus{border-top-left-radius:0;border-bottom-left-radius:0}.t-invisible{visibility:hidden}.t-reset-button{margin-left:.3125rem}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"ngmodule\", type: FormsModule }, { kind: \"directive\", type: i1.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { kind: \"directive\", type: i1.RangeValueAccessor, selector: \"input[type=range][formControlName],input[type=range][formControl],input[type=range][ngModel]\" }, { kind: \"directive\", type: i1.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i1.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"pipe\", type: PercentPipe, name: \"percent\" }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"directive\", type: i2.TuiHintDirective, selector: \"[tuiHint]:not(ng-container):not(ng-template)\", inputs: [\"tuiHintContext\", \"tuiHintAppearance\", \"tuiHint\"] }, { kind: \"directive\", type: i2.TuiHintDescribe, selector: \"[tuiHintDescribe]\", inputs: [\"tuiHintDescribe\"] }, { kind: \"directive\", type: i2.TuiHintManual, selector: \"[tuiHint][tuiHintManual]\", inputs: [\"tuiHintManual\"] }, { kind: \"directive\", type: TuiPreviewAction, selector: \"[tuiPreviewAction]\" }, { kind: \"component\", type: i3.TuiSliderComponent, selector: \"input[type=range][tuiSlider]\", inputs: [\"size\", \"segments\"] }, { kind: \"component\", type: i3.TuiSliderThumbLabel, selector: \"[tuiSliderThumbLabel]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewZoom, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-preview-zoom', imports: [\n                        AsyncPipe,\n                        FormsModule,\n                        NgIf,\n                        PercentPipe,\n                        TuiButton,\n                        TuiHint,\n                        TuiPreviewAction,\n                        TuiSlider,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-container *ngIf=\\\"zoomTexts$ | async as texts\\\">\\n    <section class=\\\"t-zoom\\\">\\n        <button\\n            tuiIconButton\\n            tuiPreviewAction\\n            type=\\\"button\\\"\\n            class=\\\"t-sign_minus\\\"\\n            [disabled]=\\\"leftButtonDisabled\\\"\\n            [iconStart]=\\\"icons.zoomOut\\\"\\n            (click)=\\\"onMinus()\\\"\\n        >\\n            {{ texts.zoomOut }}\\n        </button>\\n        <label tuiSliderThumbLabel>\\n            <div\\n                tuiHintAppearance=\\\"dark\\\"\\n                tuiHintDirection=\\\"top-right\\\"\\n                [tuiHint]=\\\"hint\\\"\\n                [tuiHintManual]=\\\"!!(hintShow$ | async)\\\"\\n            ></div>\\n\\n            <ng-template #hint>\\n                {{ (valueChange | async) || value | percent }}\\n            </ng-template>\\n\\n            <input\\n                step=\\\"any\\\"\\n                tuiSlider\\n                tuiTheme=\\\"dark\\\"\\n                type=\\\"range\\\"\\n                class=\\\"t-slider\\\"\\n                [max]=\\\"max\\\"\\n                [min]=\\\"min\\\"\\n                [ngModel]=\\\"value\\\"\\n                [ngModelOptions]=\\\"{standalone: true}\\\"\\n                (ngModelChange)=\\\"onModelChange($event)\\\"\\n            />\\n        </label>\\n        <button\\n            tuiIconButton\\n            tuiPreviewAction\\n            type=\\\"button\\\"\\n            class=\\\"t-sign_plus\\\"\\n            [disabled]=\\\"rightButtonDisabled\\\"\\n            [iconStart]=\\\"icons.zoomIn\\\"\\n            (click)=\\\"onPlus()\\\"\\n        >\\n            {{ texts.zoomIn }}\\n        </button>\\n    </section>\\n\\n    <button\\n        tuiHintAppearance=\\\"dark\\\"\\n        tuiHintDescribe\\n        tuiHintDirection=\\\"top-right\\\"\\n        tuiIconButton\\n        tuiPreviewAction\\n        type=\\\"button\\\"\\n        class=\\\"t-reset-button\\\"\\n        [class.t-invisible]=\\\"!collapseVisible\\\"\\n        [iconStart]=\\\"icons.zoomReset\\\"\\n        [tuiHint]=\\\"texts.reset\\\"\\n        (click)=\\\"onReset()\\\"\\n    ></button>\\n</ng-container>\\n\", styles: [\":host{position:relative;display:flex}.t-zoom{border-radius:1rem;background:#686868f5;color:#fff;display:flex;font:var(--tui-font-text-s);justify-content:space-between;align-items:center;inline-size:12rem}.t-slider{inline-size:7.5rem}.t-sign_minus{border-top-right-radius:0;border-bottom-right-radius:0}.t-sign_plus{border-top-left-radius:0;border-bottom-left-radius:0}.t-invisible{visibility:hidden}.t-reset-button{margin-left:.3125rem}\\n\"] }]\n        }], propDecorators: { min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], valueChange: [{\n                type: Output\n            }], reset: [{\n                type: Output\n            }] } });\n\nconst INITIAL_SCALE_COEF = 0.8;\nconst EMPTY_COORDINATES = [0, 0];\nconst ROTATION_ANGLE = 90;\nclass TuiPreviewComponent {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.minZoom = 1;\n        this.width = 0;\n        this.height = 0;\n        this.texts$ = inject(TUI_PREVIEW_TEXTS);\n        this.icons = inject(TUI_PREVIEW_ICONS);\n        this.cdr = inject(ChangeDetectorRef);\n        this.zoom$ = new BehaviorSubject(this.minZoom);\n        this.rotation$ = new BehaviorSubject(0);\n        this.coordinates$ = new BehaviorSubject(EMPTY_COORDINATES);\n        this.transitioned$ = merge(tuiDragAndDropFrom(this.el).pipe(map(({ stage }) => stage !== 'continues')), tuiTypedFromEvent(this.el, 'touchmove', {\n            passive: true,\n        }).pipe(map(TUI_FALSE_HANDLER)), tuiTypedFromEvent(this.el, 'wheel', { passive: true }).pipe(map(TUI_FALSE_HANDLER)));\n        this.cursor$ = tuiDragAndDropFrom(this.el).pipe(map(({ stage }) => (stage === 'continues' ? 'grabbing' : 'initial')), startWith('initial'));\n        this.wrapperTransform$ = combineLatest([\n            this.coordinates$.pipe(map(([x, y]) => `${tuiPx(x)}, ${tuiPx(y)}`)),\n            this.zoom$,\n            this.rotation$,\n        ]).pipe(map(([translate, zoom, rotation]) => `translate(${translate}) scale(${zoom}) rotate(${rotation}deg)`));\n        this.zoomable = true;\n        this.rotatable = false;\n    }\n    rotate() {\n        this.rotation$.next(this.rotation$.value - ROTATION_ANGLE);\n    }\n    onPan(delta) {\n        this.coordinates$.next(this.getGuardedCoordinates(this.coordinates$.value[0] + delta[0], this.coordinates$.value[1] + delta[1]));\n    }\n    onMutation(contentWrapper) {\n        const { clientWidth, clientHeight } = contentWrapper;\n        this.refresh(clientWidth, clientHeight);\n    }\n    onZoom({ clientX, clientY, delta }) {\n        if (this.zoomable) {\n            this.processZoom(clientX, clientY, delta);\n        }\n    }\n    onResize([entry]) {\n        if (entry?.contentRect) {\n            this.refresh(entry.contentRect.width, entry.contentRect.height);\n            this.cdr.detectChanges();\n        }\n    }\n    reset() {\n        this.zoom$.next(this.minZoom);\n        this.coordinates$.next(EMPTY_COORDINATES);\n    }\n    setZoom(zoom) {\n        this.zoom$.next(zoom);\n        const [x, y] = this.coordinates$.value;\n        this.coordinates$.next(this.getGuardedCoordinates(x, y));\n    }\n    get offsets() {\n        const offsetX = ((this.zoom$.value - this.minZoom) * this.width) / 2;\n        const offsetY = ((this.zoom$.value - this.minZoom) * this.height) / 2;\n        return { offsetX, offsetY };\n    }\n    calculateMinZoom(contentHeight, contentWidth, boxHeight, boxWidth) {\n        const bigSize = contentHeight > boxHeight * INITIAL_SCALE_COEF ||\n            contentWidth > boxWidth * INITIAL_SCALE_COEF;\n        const { clientHeight, clientWidth } = this.el;\n        return bigSize\n            ? tuiRound(Math.min((clientHeight * INITIAL_SCALE_COEF) / contentHeight, (clientWidth * INITIAL_SCALE_COEF) / contentWidth), 2)\n            : 1;\n    }\n    refresh(width, height) {\n        this.width = width;\n        this.height = height;\n        this.minZoom = this.calculateMinZoom(height, width, this.el.clientHeight, this.el.clientWidth);\n        this.zoom$.next(this.minZoom);\n        this.coordinates$.next(EMPTY_COORDINATES);\n        this.rotation$.next(0);\n    }\n    processZoom(clientX, clientY, delta) {\n        const oldScale = this.zoom$.value;\n        const newScale = tuiClamp(oldScale + delta, this.minZoom, 2);\n        const center = this.getScaleCenter({ clientX, clientY }, this.coordinates$.value, this.zoom$.value);\n        const moveX = center[0] * oldScale - center[0] * newScale;\n        const moveY = center[1] * oldScale - center[1] * newScale;\n        this.zoom$.next(newScale);\n        this.coordinates$.next(this.getGuardedCoordinates(this.coordinates$.value[0] + moveX, this.coordinates$.value[1] + moveY));\n    }\n    getGuardedCoordinates(x, y) {\n        const { offsetX, offsetY } = this.offsets;\n        return [tuiClamp(x, -offsetX, offsetX), tuiClamp(y, -offsetY, offsetY)];\n    }\n    getScaleCenter({ clientX, clientY }, [x, y], scale) {\n        return [\n            (clientX - x - this.el.offsetWidth / 2) / scale,\n            (clientY - y - this.el.offsetHeight / 2) / scale,\n        ];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPreviewComponent, isStandalone: true, selector: \"tui-preview\", inputs: { zoomable: \"zoomable\", rotatable: \"rotatable\" }, ngImport: i0, template: \"<ng-container *ngIf=\\\"texts$ | async as texts\\\">\\n    <section\\n        #contentWrapper\\n        attributeFilter=\\\"src\\\"\\n        characterData\\n        childList\\n        subtree\\n        class=\\\"t-wrapper\\\"\\n        [class.t-not-interactive-content]=\\\"zoomable\\\"\\n        [class.t-transitive]=\\\"transitioned$ | async\\\"\\n        [style.cursor]=\\\"cursor$ | async\\\"\\n        [style.transform]=\\\"wrapperTransform$ | async\\\"\\n        (tuiPan)=\\\"onPan($event)\\\"\\n        (tuiZoom)=\\\"onZoom($event)\\\"\\n        (waMutationObserver)=\\\"onMutation(contentWrapper)\\\"\\n        (waResizeObserver)=\\\"onResize($event)\\\"\\n    >\\n        <ng-content />\\n    </section>\\n\\n    <header class=\\\"t-header\\\">\\n        <div class=\\\"t-title\\\">\\n            <ng-content select=\\\"tui-preview-title\\\" />\\n        </div>\\n\\n        <ng-content select=\\\"tui-preview-pagination\\\" />\\n\\n        <div class=\\\"t-actions\\\">\\n            <ng-content select=\\\"[tuiPreviewAction]\\\" />\\n        </div>\\n    </header>\\n\\n    <footer class=\\\"t-footer\\\">\\n        <button\\n            *ngIf=\\\"rotatable\\\"\\n            tuiHintAppearance=\\\"dark\\\"\\n            tuiHintDescribe\\n            tuiHintDirection=\\\"top-right\\\"\\n            tuiIconButton\\n            tuiPreviewAction\\n            type=\\\"button\\\"\\n            class=\\\"t-rotate-button\\\"\\n            [iconStart]=\\\"icons.rotate\\\"\\n            [tuiHint]=\\\"texts.rotate\\\"\\n            (click)=\\\"rotate()\\\"\\n        ></button>\\n\\n        <tui-preview-zoom\\n            *ngIf=\\\"zoomable\\\"\\n            [min]=\\\"minZoom\\\"\\n            [value]=\\\"(zoom$ | async) || 1\\\"\\n            (reset)=\\\"reset()\\\"\\n            (valueChange)=\\\"setZoom($event)\\\"\\n        />\\n    </footer>\\n</ng-container>\\n\", styles: [\":host{position:relative;display:flex;justify-content:center;align-items:center;inline-size:100%;block-size:100%;-webkit-user-select:none;user-select:none}.t-header{position:fixed;top:1rem;display:flex;inline-size:100%;padding:0 1rem;box-sizing:border-box}.t-footer{position:absolute;bottom:1rem;display:flex;inline-size:100%;padding:0 1rem;box-sizing:border-box;justify-content:center}.t-actions{display:flex;flex:1;justify-content:flex-end}.t-actions ::ng-deep>*{margin-left:.625rem}.t-rotate-button{margin-right:.3125rem}.t-title{flex:1}:host-context(tui-root._mobile) .t-title{display:none}.t-not-interactive-content ::ng-deep>*{pointer-events:none}.t-wrapper{will-change:transform}.t-transitive{transition-duration:.3s}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: TuiButton, selector: \"a[tuiButton],button[tuiButton],a[tuiIconButton],button[tuiIconButton]\", inputs: [\"size\"] }, { kind: \"directive\", type: i2.TuiHintDirective, selector: \"[tuiHint]:not(ng-container):not(ng-template)\", inputs: [\"tuiHintContext\", \"tuiHintAppearance\", \"tuiHint\"] }, { kind: \"directive\", type: i2.TuiHintDescribe, selector: \"[tuiHintDescribe]\", inputs: [\"tuiHintDescribe\"] }, { kind: \"directive\", type: TuiPan, selector: \"[tuiPan]\", outputs: [\"tuiPan\"] }, { kind: \"directive\", type: TuiPreviewAction, selector: \"[tuiPreviewAction]\" }, { kind: \"component\", type: TuiPreviewZoom, selector: \"tui-preview-zoom\", inputs: [\"min\", \"max\", \"value\"], outputs: [\"valueChange\", \"reset\"] }, { kind: \"directive\", type: TuiZoom, selector: \"[tuiZoom]\", outputs: [\"tuiZoom\"] }, { kind: \"directive\", type: WaMutationObserver, selector: \"[waMutationObserver]\", inputs: [\"attributeFilter\", \"attributeOldValue\", \"attributes\", \"characterData\", \"characterDataOldValue\", \"childList\", \"subtree\"], outputs: [\"waMutationObserver\"], exportAs: [\"MutationObserver\"] }, { kind: \"directive\", type: WaResizeObserver, selector: \"[waResizeObserver]\", inputs: [\"box\"], outputs: [\"waResizeObserver\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-preview', imports: [\n                        AsyncPipe,\n                        NgIf,\n                        TuiButton,\n                        TuiHint,\n                        TuiPan,\n                        TuiPreviewAction,\n                        TuiPreviewZoom,\n                        TuiZoom,\n                        WaMutationObserver,\n                        WaResizeObserver,\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-container *ngIf=\\\"texts$ | async as texts\\\">\\n    <section\\n        #contentWrapper\\n        attributeFilter=\\\"src\\\"\\n        characterData\\n        childList\\n        subtree\\n        class=\\\"t-wrapper\\\"\\n        [class.t-not-interactive-content]=\\\"zoomable\\\"\\n        [class.t-transitive]=\\\"transitioned$ | async\\\"\\n        [style.cursor]=\\\"cursor$ | async\\\"\\n        [style.transform]=\\\"wrapperTransform$ | async\\\"\\n        (tuiPan)=\\\"onPan($event)\\\"\\n        (tuiZoom)=\\\"onZoom($event)\\\"\\n        (waMutationObserver)=\\\"onMutation(contentWrapper)\\\"\\n        (waResizeObserver)=\\\"onResize($event)\\\"\\n    >\\n        <ng-content />\\n    </section>\\n\\n    <header class=\\\"t-header\\\">\\n        <div class=\\\"t-title\\\">\\n            <ng-content select=\\\"tui-preview-title\\\" />\\n        </div>\\n\\n        <ng-content select=\\\"tui-preview-pagination\\\" />\\n\\n        <div class=\\\"t-actions\\\">\\n            <ng-content select=\\\"[tuiPreviewAction]\\\" />\\n        </div>\\n    </header>\\n\\n    <footer class=\\\"t-footer\\\">\\n        <button\\n            *ngIf=\\\"rotatable\\\"\\n            tuiHintAppearance=\\\"dark\\\"\\n            tuiHintDescribe\\n            tuiHintDirection=\\\"top-right\\\"\\n            tuiIconButton\\n            tuiPreviewAction\\n            type=\\\"button\\\"\\n            class=\\\"t-rotate-button\\\"\\n            [iconStart]=\\\"icons.rotate\\\"\\n            [tuiHint]=\\\"texts.rotate\\\"\\n            (click)=\\\"rotate()\\\"\\n        ></button>\\n\\n        <tui-preview-zoom\\n            *ngIf=\\\"zoomable\\\"\\n            [min]=\\\"minZoom\\\"\\n            [value]=\\\"(zoom$ | async) || 1\\\"\\n            (reset)=\\\"reset()\\\"\\n            (valueChange)=\\\"setZoom($event)\\\"\\n        />\\n    </footer>\\n</ng-container>\\n\", styles: [\":host{position:relative;display:flex;justify-content:center;align-items:center;inline-size:100%;block-size:100%;-webkit-user-select:none;user-select:none}.t-header{position:fixed;top:1rem;display:flex;inline-size:100%;padding:0 1rem;box-sizing:border-box}.t-footer{position:absolute;bottom:1rem;display:flex;inline-size:100%;padding:0 1rem;box-sizing:border-box;justify-content:center}.t-actions{display:flex;flex:1;justify-content:flex-end}.t-actions ::ng-deep>*{margin-left:.625rem}.t-rotate-button{margin-right:.3125rem}.t-title{flex:1}:host-context(tui-root._mobile) .t-title{display:none}.t-not-interactive-content ::ng-deep>*{pointer-events:none}.t-wrapper{will-change:transform}.t-transitive{transition-duration:.3s}\\n\"] }]\n        }], propDecorators: { zoomable: [{\n                type: Input\n            }], rotatable: [{\n                type: Input\n            }] } });\n\nclass TuiPreviewTitle {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewTitle, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPreviewTitle, isStandalone: true, selector: \"tui-preview-title\", ngImport: i0, template: `\n        <ng-content />\n    `, isInline: true, styles: [\":host{border-radius:1rem;background:#686868f5;color:#fff;display:inline-block;font:var(--tui-font-text-s);padding:.375rem .75rem}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewTitle, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-preview-title', template: `\n        <ng-content />\n    `, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\":host{border-radius:1rem;background:#686868f5;color:#fff;display:inline-block;font:var(--tui-font-text-s);padding:.375rem .75rem}\\n\"] }]\n        }] });\n\nconst TuiPreview = [\n    TuiPreviewComponent,\n    TuiPreviewTitle,\n    TuiPreviewPagination,\n    TuiPreviewAction,\n    TuiPreviewZoom,\n];\n\nclass TuiPreviewDialog {\n    constructor() {\n        this.context = injectContext();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewDialog, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPreviewDialog, isStandalone: true, selector: \"tui-preview-dialog\", host: { listeners: { \"document:keydown.esc\": \"context.$implicit.complete()\" } }, hostDirectives: [{ directive: i1$1.TuiAnimated }], ngImport: i0, template: `\n        <ng-container *polymorpheusOutlet=\"context.content as text; context: context\">\n            {{ text }}\n        </ng-container>\n    `, isInline: true, styles: [\"tui-preview-dialog{inline-size:100%;block-size:100%}tui-preview-dialog.tui-enter,tui-preview-dialog.tui-leave{animation-name:tuiFade,tuiSlide}[tuiAppearance][data-appearance=preview-action]{background:#686868f5;color:#fff}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=preview-action]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:#9f9f9fdb}}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=preview-action]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:#9f9f9fdb}}[tuiAppearance][data-appearance=preview-action][data-state=hover]{background:#9f9f9fdb}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=preview-action][tuiWrapper]:hover:not(._no-hover),[tuiAppearance][data-appearance=preview-action][tuiWrapper][data-state=hover]{background:#9f9f9fdb}}[tuiAppearance][data-appearance=preview-action]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:#9f9f9fbf}[tuiAppearance][data-appearance=preview-action]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:#9f9f9fbf}[tuiAppearance][data-appearance=preview-action][data-state=active]{background:#9f9f9fbf}[tuiAppearance][data-appearance=preview-action][tuiWrapper]:active:not(._no-active),[tuiAppearance][data-appearance=preview-action][tuiWrapper][data-state=active],[tuiAppearance][data-appearance=preview-action][tuiWrapper][data-state=active]:hover{background:#9f9f9fbf}\\n\"], dependencies: [{ kind: \"directive\", type: PolymorpheusOutlet, selector: \"[polymorpheusOutlet]\", inputs: [\"polymorpheusOutlet\", \"polymorpheusOutletContext\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewDialog, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-preview-dialog', imports: [PolymorpheusOutlet], template: `\n        <ng-container *polymorpheusOutlet=\"context.content as text; context: context\">\n            {{ text }}\n        </ng-container>\n    `, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, hostDirectives: [TuiAnimated], host: {\n                        '(document:keydown.esc)': 'context.$implicit.complete()',\n                    }, styles: [\"tui-preview-dialog{inline-size:100%;block-size:100%}tui-preview-dialog.tui-enter,tui-preview-dialog.tui-leave{animation-name:tuiFade,tuiSlide}[tuiAppearance][data-appearance=preview-action]{background:#686868f5;color:#fff}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=preview-action]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:#9f9f9fdb}}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=preview-action]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):hover:not(:disabled):not([data-state]){background:#9f9f9fdb}}[tuiAppearance][data-appearance=preview-action][data-state=hover]{background:#9f9f9fdb}@media (hover: hover) and (pointer: fine){[tuiAppearance][data-appearance=preview-action][tuiWrapper]:hover:not(._no-hover),[tuiAppearance][data-appearance=preview-action][tuiWrapper][data-state=hover]{background:#9f9f9fdb}}[tuiAppearance][data-appearance=preview-action]:matches(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:#9f9f9fbf}[tuiAppearance][data-appearance=preview-action]:is(a,button,select,textarea,input,label,.tui-interactive):not(:disabled):active:not(:disabled):not([data-state]){background:#9f9f9fbf}[tuiAppearance][data-appearance=preview-action][data-state=active]{background:#9f9f9fbf}[tuiAppearance][data-appearance=preview-action][tuiWrapper]:active:not(._no-active),[tuiAppearance][data-appearance=preview-action][tuiWrapper][data-state=active],[tuiAppearance][data-appearance=preview-action][tuiWrapper][data-state=active]:hover{background:#9f9f9fbf}\\n\"] }]\n        }] });\n\nclass TuiPreviewDialogService extends TuiPopoverService {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewDialogService, deps: null, target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewDialogService, providedIn: 'root', useFactory: () => new TuiPreviewDialogService(TUI_DIALOGS, TuiPreviewDialog) }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewDialogService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                    useFactory: () => new TuiPreviewDialogService(TUI_DIALOGS, TuiPreviewDialog),\n                }]\n        }] });\n\nclass TuiPreviewDialogDirective extends TuiPopoverDirective {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewDialogDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPreviewDialogDirective, isStandalone: true, selector: \"ng-template[tuiPreviewDialog]\", inputs: { open: [\"tuiPreviewDialog\", \"open\"] }, outputs: { openChange: \"tuiPreviewDialogChange\" }, providers: [tuiAsPopover(TuiPreviewDialogService)], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPreviewDialogDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'ng-template[tuiPreviewDialog]',\n                    inputs: ['open: tuiPreviewDialog'],\n                    outputs: ['openChange: tuiPreviewDialogChange'],\n                    providers: [tuiAsPopover(TuiPreviewDialogService)],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPreview, TuiPreviewAction, TuiPreviewComponent, TuiPreviewDialog, TuiPreviewDialogDirective, TuiPreviewDialogService, TuiPreviewPagination, TuiPreviewTitle, TuiPreviewZoom };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,IAAI,EAAEC,WAAW,QAAQ,iBAAiB;AAC9D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,eAAe;AACpK,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,0BAA0B;AAC7D,SAASC,wBAAwB,EAAEC,SAAS,QAAQ,kCAAkC;AACtF,SAASC,iBAAiB,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,iBAAiB,QAAQ,sBAAsB;AACzH,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,MAAM,QAAQ,8BAA8B;AACrD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,2BAA2B;AACjF,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,KAAK,QAAQ,mCAAmC;AACzD,OAAO,KAAKC,EAAE,MAAM,gCAAgC;AACpD,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,SAAS,EAAEC,KAAK,EAAEC,EAAE,EAAEC,KAAK,EAAEC,GAAG,EAAEC,SAAS,EAAEC,eAAe,EAAEC,aAAa,QAAQ,MAAM;AAClG,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,OAAO,KAAKC,IAAI,MAAM,mCAAmC;AACzD,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,wBAAwB;AAC1E,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,wBAAwB;AACxE,SAASC,WAAW,QAAQ,kCAAkC;AAAC,SAAAC,6CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAGsCrD,EAAE,CAAAsD,gBAAA;IAAFtD,EAAE,CAAAuD,uBAAA,EAkDsX,CAAC;IAlDzXvD,EAAE,CAAAwD,cAAA,eAkDymB,CAAC;IAlD5mBxD,EAAE,CAAAyD,UAAA,mBAAAC,qEAAA;MAAF1D,EAAE,CAAA2D,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;MAAA,OAAF7D,EAAE,CAAA8D,WAAA,CAkDilBF,MAAA,CAAAG,YAAA,EAAc,CAAC,CAAC;IAAA,CAAC,CAAC;IAlDrmB/D,EAAE,CAAAgE,MAAA,EAkDuoB,CAAC;IAlD1oBhE,EAAE,CAAAiE,YAAA,CAkDgpB,CAAC;IAlDnpBjE,EAAE,CAAAgE,MAAA,EAkDwrB,CAAC;IAlD3rBhE,EAAE,CAAAwD,cAAA,eAkDs6B,CAAC;IAlDz6BxD,EAAE,CAAAyD,UAAA,mBAAAS,qEAAA;MAAFlE,EAAE,CAAA2D,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;MAAA,OAAF7D,EAAE,CAAA8D,WAAA,CAkD+4BF,MAAA,CAAAG,YAAA,CAAa,CAAC,CAAC;IAAA,CAAC,CAAC;IAlDl6B/D,EAAE,CAAAgE,MAAA,EAkDo8B,CAAC;IAlDv8BhE,EAAE,CAAAiE,YAAA,CAkD68B,CAAC;IAlDh9BjE,EAAE,CAAAmE,qBAAA;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAiB,QAAA,GAAAhB,GAAA,CAAAiB,IAAA;IAAA,MAAAT,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;IAAF7D,EAAE,CAAAsE,SAAA,CAkDwhB,CAAC;IAlD3hBtE,EAAE,CAAAuE,UAAA,aAAAX,MAAA,CAAAY,kBAkDwhB,CAAC,cAAAZ,MAAA,CAAAa,KAAA,CAAAC,IAAmC,CAAC;IAlD/jB1E,EAAE,CAAAsE,SAAA,CAkDuoB,CAAC;IAlD1oBtE,EAAE,CAAA2E,kBAAA,MAAAP,QAAA,QAkDuoB,CAAC;IAlD1oBpE,EAAE,CAAAsE,SAAA,CAkDwrB,CAAC;IAlD3rBtE,EAAE,CAAA4E,kBAAA,MAAAhB,MAAA,CAAAiB,KAAA,WAAAjB,MAAA,CAAAkB,MAAA,KAkDwrB,CAAC;IAlD3rB9E,EAAE,CAAAsE,SAAA,CAkDs1B,CAAC;IAlDz1BtE,EAAE,CAAAuE,UAAA,aAAAX,MAAA,CAAAmB,mBAkDs1B,CAAC,cAAAnB,MAAA,CAAAa,KAAA,CAAAO,IAAmC,CAAC;IAlD73BhF,EAAE,CAAAsE,SAAA,CAkDo8B,CAAC;IAlDv8BtE,EAAE,CAAA2E,kBAAA,MAAAP,QAAA,QAkDo8B,CAAC;EAAA;AAAA;AAAA,MAAAa,GAAA,GAAAA,CAAA;EAAAC,UAAA;AAAA;AAAA,SAAAC,qDAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlDv8BnD,EAAE,CAAAgE,MAAA,EAyGs9B,CAAC;IAzGz9BhE,EAAE,CAAAoF,MAAA;IAAFpF,EAAE,CAAAoF,MAAA;EAAA;EAAA,IAAAjC,EAAA;IAAA,MAAAS,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;IAAF7D,EAAE,CAAA2E,kBAAA,MAAF3E,EAAE,CAAAqF,WAAA,OAAFrF,EAAE,CAAAqF,WAAA,OAAAzB,MAAA,CAAA0B,WAAA,KAAA1B,MAAA,CAAA2B,KAAA,MAyGs9B,CAAC;EAAA;AAAA;AAAA,SAAAC,uCAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAzGz9BrD,EAAE,CAAAsD,gBAAA;IAAFtD,EAAE,CAAAuD,uBAAA,EAyGuP,CAAC;IAzG1PvD,EAAE,CAAAwD,cAAA,gBAyGuR,CAAC,eAAkR,CAAC;IAzG7iBxD,EAAE,CAAAyD,UAAA,mBAAAgC,+DAAA;MAAFzF,EAAE,CAAA2D,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;MAAA,OAAF7D,EAAE,CAAA8D,WAAA,CAyGqhBF,MAAA,CAAA8B,OAAA,CAAQ,CAAC;IAAA,CAAC,CAAC;IAzGliB1F,EAAE,CAAAgE,MAAA,EAyGqlB,CAAC;IAzGxlBhE,EAAE,CAAAiE,YAAA,CAyG8lB,CAAC;IAzGjmBjE,EAAE,CAAAwD,cAAA,cAyGmoB,CAAC;IAzGtoBxD,EAAE,CAAA2F,SAAA,YAyGq2B,CAAC;IAzGx2B3F,EAAE,CAAAoF,MAAA;IAAFpF,EAAE,CAAA4F,UAAA,IAAAT,oDAAA,gCAAFnF,EAAE,CAAA6F,sBAyGw4B,CAAC;IAzG34B7F,EAAE,CAAAwD,cAAA,cAyGi4C,CAAC;IAzGp4CxD,EAAE,CAAAyD,UAAA,2BAAAqC,sEAAAC,MAAA;MAAF/F,EAAE,CAAA2D,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;MAAA,OAAF7D,EAAE,CAAA8D,WAAA,CAyG21CF,MAAA,CAAAoC,aAAA,CAAAD,MAAoB,CAAC;IAAA,CAAC,CAAC;IAzGp3C/F,EAAE,CAAAiE,YAAA,CAyGi4C,CAAC,CAAiB,CAAC;IAzGt5CjE,EAAE,CAAAwD,cAAA,gBAyGoqD,CAAC;IAzGvqDxD,EAAE,CAAAyD,UAAA,mBAAAwC,gEAAA;MAAFjG,EAAE,CAAA2D,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;MAAA,OAAF7D,EAAE,CAAA8D,WAAA,CAyGgpDF,MAAA,CAAAsC,MAAA,CAAO,CAAC;IAAA,CAAC,CAAC;IAzG5pDlG,EAAE,CAAAgE,MAAA,GAyG8sD,CAAC;IAzGjtDhE,EAAE,CAAAiE,YAAA,CAyGutD,CAAC,CAAe,CAAC;IAzG1uDjE,EAAE,CAAAwD,cAAA,gBAyG2mE,CAAC;IAzG9mExD,EAAE,CAAAyD,UAAA,mBAAA0C,gEAAA;MAAFnG,EAAE,CAAA2D,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;MAAA,OAAF7D,EAAE,CAAA8D,WAAA,CAyG0lEF,MAAA,CAAAwC,OAAA,CAAQ,CAAC;IAAA,CAAC,CAAC;IAzGvmEpG,EAAE,CAAAiE,YAAA,CAyGonE,CAAC;IAzGvnEjE,EAAE,CAAAmE,qBAAA;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAiB,QAAA,GAAAhB,GAAA,CAAAiB,IAAA;IAAA,MAAAgC,OAAA,GAAFrG,EAAE,CAAAsG,WAAA;IAAA,MAAA1C,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;IAAF7D,EAAE,CAAAsE,SAAA,EAyGid,CAAC;IAzGpdtE,EAAE,CAAAuE,UAAA,aAAAX,MAAA,CAAAY,kBAyGid,CAAC,cAAAZ,MAAA,CAAAa,KAAA,CAAA8B,OAA0C,CAAC;IAzG/fvG,EAAE,CAAAsE,SAAA,CAyGqlB,CAAC;IAzGxlBtE,EAAE,CAAA2E,kBAAA,MAAAP,QAAA,CAAAmC,OAAA,KAyGqlB,CAAC;IAzGxlBvG,EAAE,CAAAsE,SAAA,EAyGqxB,CAAC;IAzGxxBtE,EAAE,CAAAuE,UAAA,YAAA8B,OAyGqxB,CAAC,oBAzGxxBrG,EAAE,CAAAqF,WAAA,QAAAzB,MAAA,CAAA4C,SAAA,CAyGg1B,CAAC;IAzGn1BxG,EAAE,CAAAsE,SAAA,EAyGyrC,CAAC;IAzG5rCtE,EAAE,CAAAuE,UAAA,QAAAX,MAAA,CAAA6C,GAyGyrC,CAAC,QAAA7C,MAAA,CAAA8C,GAA8B,CAAC,YAAA9C,MAAA,CAAA2B,KAAoC,CAAC,mBAzGhwCvF,EAAE,CAAA2G,eAAA,KAAA1B,GAAA,CAyGszC,CAAC;IAzGzzCjF,EAAE,CAAAsE,SAAA,CAyG6kD,CAAC;IAzGhlDtE,EAAE,CAAAuE,UAAA,aAAAX,MAAA,CAAAmB,mBAyG6kD,CAAC,cAAAnB,MAAA,CAAAa,KAAA,CAAAmC,MAAyC,CAAC;IAzG1nD5G,EAAE,CAAAsE,SAAA,CAyG8sD,CAAC;IAzGjtDtE,EAAE,CAAA2E,kBAAA,MAAAP,QAAA,CAAAwC,MAAA,KAyG8sD,CAAC;IAzGjtD5G,EAAE,CAAAsE,SAAA,CAyGy/D,CAAC;IAzG5/DtE,EAAE,CAAA6G,WAAA,iBAAAjD,MAAA,CAAAkD,eAyGy/D,CAAC;IAzG5/D9G,EAAE,CAAAuE,UAAA,cAAAX,MAAA,CAAAa,KAAA,CAAAsC,SAyGkiE,CAAC,YAAA3C,QAAA,CAAA4C,KAAkC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,sDAAAhE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiE,GAAA,GAzGxkEpH,EAAE,CAAAsD,gBAAA;IAAFtD,EAAE,CAAAwD,cAAA,eAqOqiD,CAAC;IArOxiDxD,EAAE,CAAAyD,UAAA,mBAAA4D,8EAAA;MAAFrH,EAAE,CAAA2D,aAAA,CAAAyD,GAAA;MAAA,MAAAxD,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;MAAA,OAAF7D,EAAE,CAAA8D,WAAA,CAqOihDF,MAAA,CAAA0D,MAAA,CAAO,CAAC;IAAA,CAAC,CAAC;IArO7hDtH,EAAE,CAAAiE,YAAA,CAqO8iD,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAoE,QAAA,GArOjjDvH,EAAE,CAAA6D,aAAA,GAAAQ,IAAA;IAAA,MAAAT,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;IAAF7D,EAAE,CAAAuE,UAAA,cAAAX,MAAA,CAAAa,KAAA,CAAA6C,MAqOg9C,CAAC,YAAAC,QAAA,CAAAD,MAAuC,CAAC;EAAA;AAAA;AAAA,SAAAE,gEAAArE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAsE,GAAA,GArO3/CzH,EAAE,CAAAsD,gBAAA;IAAFtD,EAAE,CAAAwD,cAAA,0BAqOoxD,CAAC;IArOvxDxD,EAAE,CAAAoF,MAAA;IAAFpF,EAAE,CAAAyD,UAAA,mBAAAiE,kGAAA;MAAF1H,EAAE,CAAA2D,aAAA,CAAA8D,GAAA;MAAA,MAAA7D,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;MAAA,OAAF7D,EAAE,CAAA8D,WAAA,CAqOitDF,MAAA,CAAAoD,KAAA,CAAM,CAAC;IAAA,CAAC,CAAC,yBAAAW,wGAAA5B,MAAA;MArO5tD/F,EAAE,CAAA2D,aAAA,CAAA8D,GAAA;MAAA,MAAA7D,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;MAAA,OAAF7D,EAAE,CAAA8D,WAAA,CAqOwvDF,MAAA,CAAAgE,OAAA,CAAA7B,MAAc,CAAC;IAAA,CAAC,CAAC;IArO3wD/F,EAAE,CAAAiE,YAAA,CAqOoxD,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAS,MAAA,GArOvxD5D,EAAE,CAAA6D,aAAA;IAAF7D,EAAE,CAAAuE,UAAA,QAAAX,MAAA,CAAAiE,OAqO0oD,CAAC,UArO7oD7H,EAAE,CAAAqF,WAAA,OAAAzB,MAAA,CAAAkE,KAAA,MAqOwrD,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAA5E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GArO3rDrD,EAAE,CAAAsD,gBAAA;IAAFtD,EAAE,CAAAuD,uBAAA,EAqOgM,CAAC;IArOnMvD,EAAE,CAAAwD,cAAA,mBAqOovB,CAAC;IArOvvBxD,EAAE,CAAAoF,MAAA;IAAFpF,EAAE,CAAAoF,MAAA;IAAFpF,EAAE,CAAAoF,MAAA;IAAFpF,EAAE,CAAAyD,UAAA,oBAAAuE,sEAAAjC,MAAA;MAAF/F,EAAE,CAAA2D,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;MAAA,OAAF7D,EAAE,CAAA8D,WAAA,CAqO2kBF,MAAA,CAAAqE,KAAA,CAAAlC,MAAY,CAAC;IAAA,CAAC,CAAC,qBAAAmC,uEAAAnC,MAAA;MArO5lB/F,EAAE,CAAA2D,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;MAAA,OAAF7D,EAAE,CAAA8D,WAAA,CAqOgnBF,MAAA,CAAAuE,MAAA,CAAApC,MAAa,CAAC;IAAA,CAAC,CAAC,gCAAAqC,kFAAA;MArOloBpI,EAAE,CAAA2D,aAAA,CAAAN,GAAA;MAAA,MAAAgF,iBAAA,GAAFrI,EAAE,CAAAsG,WAAA;MAAA,MAAA1C,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;MAAA,OAAF7D,EAAE,CAAA8D,WAAA,CAqOiqBF,MAAA,CAAA0E,UAAA,CAAAD,iBAAyB,CAAC;IAAA,CAAC,CAAC,8BAAAE,gFAAAxC,MAAA;MArO/rB/F,EAAE,CAAA2D,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;MAAA,OAAF7D,EAAE,CAAA8D,WAAA,CAqO4tBF,MAAA,CAAA4E,QAAA,CAAAzC,MAAe,CAAC;IAAA,CAAC,CAAC;IArOhvB/F,EAAE,CAAAyI,YAAA,EAqO4wB,CAAC;IArO/wBzI,EAAE,CAAAiE,YAAA,CAqO4xB,CAAC;IArO/xBjE,EAAE,CAAAwD,cAAA,eAqO+zB,CAAC,YAAgC,CAAC;IArOn2BxD,EAAE,CAAAyI,YAAA,KAqOy5B,CAAC;IArO55BzI,EAAE,CAAAiE,YAAA,CAqOy6B,CAAC;IArO56BjE,EAAE,CAAAyI,YAAA,MAqOq+B,CAAC;IArOx+BzI,EAAE,CAAAwD,cAAA,aAqO0gC,CAAC;IArO7gCxD,EAAE,CAAAyI,YAAA,MAqOokC,CAAC;IArOvkCzI,EAAE,CAAAiE,YAAA,CAqOolC,CAAC,CAAc,CAAC;IArOtmCjE,EAAE,CAAAwD,cAAA,gBAqOsoC,CAAC;IArOzoCxD,EAAE,CAAA4F,UAAA,KAAAuB,qDAAA,mBAqOqiD,CAAC,KAAAK,+DAAA,6BAA8O,CAAC;IArOvxDxH,EAAE,CAAAiE,YAAA,CAqOmyD,CAAC;IArOtyDjE,EAAE,CAAAmE,qBAAA;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAS,MAAA,GAAF5D,EAAE,CAAA6D,aAAA;IAAF7D,EAAE,CAAAsE,SAAA,CAqO4f,CAAC;IArO/ftE,EAAE,CAAA0I,WAAA,WAAF1I,EAAE,CAAAqF,WAAA,QAAAzB,MAAA,CAAA+E,OAAA,CAqO4f,CAAC,cArO/f3I,EAAE,CAAAqF,WAAA,QAAAzB,MAAA,CAAAgF,iBAAA,CAqOqjB,CAAC;IArOxjB5I,EAAE,CAAA6G,WAAA,8BAAAjD,MAAA,CAAAiF,QAqOwZ,CAAC,iBArO3Z7I,EAAE,CAAAqF,WAAA,QAAAzB,MAAA,CAAAkF,aAAA,CAqOgd,CAAC;IArOnd9I,EAAE,CAAAsE,SAAA,GAqOsrC,CAAC;IArOzrCtE,EAAE,CAAAuE,UAAA,SAAAX,MAAA,CAAAmF,SAqOsrC,CAAC;IArOzrC/I,EAAE,CAAAsE,SAAA,CAqOymD,CAAC;IArO5mDtE,EAAE,CAAAuE,UAAA,SAAAX,MAAA,CAAAiF,QAqOymD,CAAC;EAAA;AAAA;AAAA,MAAAG,GAAA;AAAA,SAAAC,yCAAA9F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArO5mDnD,EAAE,CAAAuD,uBAAA,EAsRlB,CAAC;IAtRevD,EAAE,CAAAgE,MAAA,EAwRhG,CAAC;IAxR6FhE,EAAE,CAAAmE,qBAAA;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAA+F,OAAA,GAAA9F,GAAA,CAAA+F,kBAAA;IAAFnJ,EAAE,CAAAsE,SAAA,CAwRhG,CAAC;IAxR6FtE,EAAE,CAAA2E,kBAAA,MAAAuE,OAAA,KAwRhG,CAAC;EAAA;AAAA;AAzRR,MAAME,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACI,IAAI,kBAD+ExJ,EAAE,CAAAyJ,iBAAA;MAAAC,IAAA,EACJN,gBAAgB;MAAAO,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,8BAAA3G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADdnD,EAAE,CAAA0I,WAAA,kBACJ,GAAG,OAAY,CAAC;QAAA;MAAA;MAAAxD,UAAA;MAAA6E,QAAA,GADd/J,EAAE,CAAAgK,kBAAA,CACyI,CACpOnJ,wBAAwB,CAAC;QACrBoJ,UAAU,EAAE,gBAAgB;QAC5BC,IAAI,EAAE;MACV,CAAC,CAAC,CACL;IAAA,EAAiB;EAAE;AAC5B;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KARqGnK,EAAE,CAAAoK,iBAAA,CAQXhB,gBAAgB,EAAc,CAAC;IAC/GM,IAAI,EAAEzJ,SAAS;IACfoK,IAAI,EAAE,CAAC;MACCnF,UAAU,EAAE,IAAI;MAChBoF,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CACP1J,wBAAwB,CAAC;QACrBoJ,UAAU,EAAE,gBAAgB;QAC5BC,IAAI,EAAE;MACV,CAAC,CAAC,CACL;MACDM,IAAI,EAAE;QACF,2BAA2B,EAAE;MACjC;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMC,oBAAoB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjG,KAAK,GAAGvE,MAAM,CAACa,iBAAiB,CAAC;IACtC,IAAI,CAAC4J,MAAM,GAAGzK,MAAM,CAACc,oBAAoB,CAAC;IAC1C,IAAI,CAAC8D,MAAM,GAAG,CAAC;IACf,IAAI,CAACD,KAAK,GAAG,CAAC;IACd,IAAI,CAAC+F,WAAW,GAAG,IAAIzK,YAAY,CAAC,CAAC;EACzC;EACA4D,YAAYA,CAAC8G,IAAI,EAAE;IACf,IAAI,CAACC,WAAW,CAACnK,QAAQ,CAAC,IAAI,CAACkE,KAAK,GAAGgG,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC/F,MAAM,GAAG,CAAC,CAAC,CAAC;EACrE;EACA,IAAIN,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACK,KAAK,KAAK,CAAC;EAC3B;EACA,IAAIE,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACF,KAAK,KAAK,IAAI,CAACC,MAAM,GAAG,CAAC;EACzC;EACAgG,WAAWA,CAACjG,KAAK,EAAE;IACf,IAAI,IAAI,CAACA,KAAK,KAAKA,KAAK,EAAE;MACtB;IACJ;IACA,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC+F,WAAW,CAACG,IAAI,CAAClG,KAAK,CAAC;EAChC;EACA;IAAS,IAAI,CAACwE,IAAI,YAAA2B,6BAAAzB,CAAA;MAAA,YAAAA,CAAA,IAAyFkB,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACQ,IAAI,kBAlD+EjL,EAAE,CAAAkL,iBAAA;MAAAxB,IAAA,EAkDJe,oBAAoB;MAAAd,SAAA;MAAAE,YAAA,WAAAsB,kCAAAhI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlDlBnD,EAAE,CAAAyD,UAAA,wCAAA2H,mEAAA;YAAA,OAkDJhI,GAAA,CAAAW,YAAA,CAAa,CAAC,CAAC;UAAA,UAlDb/D,EAAE,CAAAqL,iBAkDe,CAAC,uCAAAC,kEAAA;YAAA,OAApBlI,GAAA,CAAAW,YAAA,EAAc,CAAC,CAAC;UAAA,UAlDd/D,EAAE,CAAAqL,iBAkDe,CAAC;QAAA;MAAA;MAAAE,MAAA;QAAAzG,MAAA;QAAAD,KAAA;MAAA;MAAA2G,OAAA;QAAAZ,WAAA;MAAA;MAAA1F,UAAA;MAAA6E,QAAA,GAlDlB/J,EAAE,CAAAyL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAA3I,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnD,EAAE,CAAA4F,UAAA,IAAA1C,4CAAA,yBAkDsX,CAAC;UAlDzXlD,EAAE,CAAAoF,MAAA;QAAA;QAAA,IAAAjC,EAAA;UAAFnD,EAAE,CAAAuE,UAAA,SAAFvE,EAAE,CAAAqF,WAAA,OAAAjC,GAAA,CAAAuH,MAAA,CAkD2W,CAAC;QAAA;MAAA;MAAAoB,YAAA,GAA69BlM,SAAS,EAA8CC,IAAI,EAA6FgB,SAAS,EAAoIsI,gBAAgB;MAAA4C,MAAA;MAAAC,eAAA;IAAA,EAA0F;EAAE;AACj0D;AACA;EAAA,QAAA9B,SAAA,oBAAAA,SAAA,KApDqGnK,EAAE,CAAAoK,iBAAA,CAoDXK,oBAAoB,EAAc,CAAC;IACnHf,IAAI,EAAEtJ,SAAS;IACfiK,IAAI,EAAE,CAAC;MAAEnF,UAAU,EAAE,IAAI;MAAEoF,QAAQ,EAAE,wBAAwB;MAAE4B,OAAO,EAAE,CAACrM,SAAS,EAAEC,IAAI,EAAEgB,SAAS,EAAEsI,gBAAgB,CAAC;MAAE6C,eAAe,EAAE5L,uBAAuB,CAAC8L,MAAM;MAAE3B,IAAI,EAAE;QACnK,uCAAuC,EAAE,iBAAiB;QAC1D,sCAAsC,EAAE;MAC5C,CAAC;MAAEqB,QAAQ,EAAE,4pBAA4pB;MAAEG,MAAM,EAAE,CAAC,oTAAoT;IAAE,CAAC;EACv/B,CAAC,CAAC,QAAkB;IAAElH,MAAM,EAAE,CAAC;MACvB4E,IAAI,EAAEpJ;IACV,CAAC,CAAC;IAAEuE,KAAK,EAAE,CAAC;MACR6E,IAAI,EAAEpJ;IACV,CAAC,CAAC;IAAEsK,WAAW,EAAE,CAAC;MACdlB,IAAI,EAAEnJ;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6L,IAAI,GAAG,GAAG;AAChB,MAAMC,cAAc,CAAC;EACjB3B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjG,KAAK,GAAGvE,MAAM,CAACa,iBAAiB,CAAC;IACtC,IAAI,CAACuL,UAAU,GAAGpM,MAAM,CAACe,sBAAsB,CAAC;IAChD,IAAI,CAACyF,GAAG,GAAG,GAAG;IACd,IAAI,CAACD,GAAG,GAAG,CAAC;IACZ,IAAI,CAAClB,KAAK,GAAG,CAAC;IACd,IAAI,CAACD,WAAW,GAAG,IAAInF,YAAY,CAAC,CAAC;IACrC,IAAI,CAAC6G,KAAK,GAAG,IAAI7G,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACqG,SAAS,GAAG,IAAI,CAAClB,WAAW,CAACiH,IAAI,CAACzK,SAAS,CAAC,MAAMC,KAAK,CAACC,EAAE,CAAC,IAAI,CAAC,EAAEC,KAAK,CAAC,IAAI,CAAC,CAACsK,IAAI,CAACrK,GAAG,CAACb,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAEc,SAAS,CAAC,KAAK,CAAC,CAAC;EACxI;EACA,IAAIqC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACe,KAAK,KAAK,IAAI,CAACmB,GAAG;EAClC;EACA,IAAI3B,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACQ,KAAK,KAAK,IAAI,CAACkB,GAAG;EAClC;EACA,IAAIK,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACvB,KAAK,GAAG,IAAI,CAACmB,GAAG;EAChC;EACAV,aAAaA,CAACT,KAAK,EAAE;IACjB,MAAMiH,OAAO,GAAG7L,QAAQ,CAAC4E,KAAK,EAAE,IAAI,CAACmB,GAAG,EAAE,IAAI,CAACD,GAAG,CAAC;IACnD,IAAI+F,OAAO,KAAK,IAAI,CAACjH,KAAK,EAAE;MACxB;IACJ;IACA,IAAI,CAACA,KAAK,GAAGiH,OAAO;IACpB,IAAI,CAAClH,WAAW,CAACyF,IAAI,CAACyB,OAAO,CAAC;EAClC;EACApG,OAAOA,CAAA,EAAG;IACN,IAAI,CAACY,KAAK,CAAC+D,IAAI,CAAC,CAAC;EACrB;EACArF,OAAOA,CAAA,EAAG;IACN,IAAI,CAACM,aAAa,CAAC,IAAI,CAACT,KAAK,GAAG6G,IAAI,CAAC;EACzC;EACAlG,MAAMA,CAAA,EAAG;IACL,IAAI,CAACF,aAAa,CAAC,IAAI,CAACT,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG6G,IAAI,CAAC;EAC9D;EACA;IAAS,IAAI,CAAC/C,IAAI,YAAAoD,uBAAAlD,CAAA;MAAA,YAAAA,CAAA,IAAyF8C,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACpB,IAAI,kBAzG+EjL,EAAE,CAAAkL,iBAAA;MAAAxB,IAAA,EAyGJ2C,cAAc;MAAA1C,SAAA;MAAA4B,MAAA;QAAA7E,GAAA;QAAAD,GAAA;QAAAlB,KAAA;MAAA;MAAAiG,OAAA;QAAAlG,WAAA;QAAA0B,KAAA;MAAA;MAAA9B,UAAA;MAAA6E,QAAA,GAzGZ/J,EAAE,CAAAyL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAa,wBAAAvJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnD,EAAE,CAAA4F,UAAA,IAAAJ,sCAAA,2BAyGuP,CAAC;UAzG1PxF,EAAE,CAAAoF,MAAA;QAAA;QAAA,IAAAjC,EAAA;UAAFnD,EAAE,CAAAuE,UAAA,SAAFvE,EAAE,CAAAqF,WAAA,OAAAjC,GAAA,CAAAkJ,UAAA,CAyG4O,CAAC;QAAA;MAAA;MAAAP,YAAA,GAAu4ElM,SAAS,EAA6C0C,WAAW,EAA+BD,EAAE,CAACqK,oBAAoB,EAAyPrK,EAAE,CAACsK,kBAAkB,EAAyItK,EAAE,CAACuK,eAAe,EAAsFvK,EAAE,CAACwK,OAAO,EAA8MhN,IAAI,EAAwFC,WAAW,EAAgDe,SAAS,EAAoIc,EAAE,CAACmL,gBAAgB,EAAqJnL,EAAE,CAACoL,eAAe,EAA2FpL,EAAE,CAACqL,aAAa,EAAgG7D,gBAAgB,EAA+D5G,EAAE,CAAC0K,kBAAkB,EAAuG1K,EAAE,CAAC2K,mBAAmB;MAAAnB,MAAA;MAAAC,eAAA;IAAA,EAA6F;EAAE;AAClhJ;AACA;EAAA,QAAA9B,SAAA,oBAAAA,SAAA,KA3GqGnK,EAAE,CAAAoK,iBAAA,CA2GXiC,cAAc,EAAc,CAAC;IAC7G3C,IAAI,EAAEtJ,SAAS;IACfiK,IAAI,EAAE,CAAC;MAAEnF,UAAU,EAAE,IAAI;MAAEoF,QAAQ,EAAE,kBAAkB;MAAE4B,OAAO,EAAE,CACtDrM,SAAS,EACT0C,WAAW,EACXzC,IAAI,EACJC,WAAW,EACXe,SAAS,EACTe,OAAO,EACPuH,gBAAgB,EAChB3G,SAAS,CACZ;MAAEwJ,eAAe,EAAE5L,uBAAuB,CAAC8L,MAAM;MAAEN,QAAQ,EAAE,s8DAAs8D;MAAEG,MAAM,EAAE,CAAC,wbAAwb;IAAE,CAAC;EACt9E,CAAC,CAAC,QAAkB;IAAEtF,GAAG,EAAE,CAAC;MACpBgD,IAAI,EAAEpJ;IACV,CAAC,CAAC;IAAEmG,GAAG,EAAE,CAAC;MACNiD,IAAI,EAAEpJ;IACV,CAAC,CAAC;IAAEiF,KAAK,EAAE,CAAC;MACRmE,IAAI,EAAEpJ;IACV,CAAC,CAAC;IAAEgF,WAAW,EAAE,CAAC;MACdoE,IAAI,EAAEnJ;IACV,CAAC,CAAC;IAAEyG,KAAK,EAAE,CAAC;MACR0C,IAAI,EAAEnJ;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6M,kBAAkB,GAAG,GAAG;AAC9B,MAAMC,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAChC,MAAMC,cAAc,GAAG,EAAE;AACzB,MAAMC,mBAAmB,CAAC;EACtB7C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8C,EAAE,GAAG9L,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACmG,OAAO,GAAG,CAAC;IAChB,IAAI,CAAC4F,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAAC/C,MAAM,GAAGzK,MAAM,CAACgB,iBAAiB,CAAC;IACvC,IAAI,CAACuD,KAAK,GAAGvE,MAAM,CAACa,iBAAiB,CAAC;IACtC,IAAI,CAAC4M,GAAG,GAAGzN,MAAM,CAACM,iBAAiB,CAAC;IACpC,IAAI,CAACsH,KAAK,GAAG,IAAI1F,eAAe,CAAC,IAAI,CAACyF,OAAO,CAAC;IAC9C,IAAI,CAAC+F,SAAS,GAAG,IAAIxL,eAAe,CAAC,CAAC,CAAC;IACvC,IAAI,CAACyL,YAAY,GAAG,IAAIzL,eAAe,CAACiL,iBAAiB,CAAC;IAC1D,IAAI,CAACvE,aAAa,GAAG/G,KAAK,CAACP,kBAAkB,CAAC,IAAI,CAACgM,EAAE,CAAC,CAACjB,IAAI,CAACrK,GAAG,CAAC,CAAC;MAAE4L;IAAM,CAAC,KAAKA,KAAK,KAAK,WAAW,CAAC,CAAC,EAAErM,iBAAiB,CAAC,IAAI,CAAC+L,EAAE,EAAE,WAAW,EAAE;MAC5IO,OAAO,EAAE;IACb,CAAC,CAAC,CAACxB,IAAI,CAACrK,GAAG,CAACb,iBAAiB,CAAC,CAAC,EAAEI,iBAAiB,CAAC,IAAI,CAAC+L,EAAE,EAAE,OAAO,EAAE;MAAEO,OAAO,EAAE;IAAK,CAAC,CAAC,CAACxB,IAAI,CAACrK,GAAG,CAACb,iBAAiB,CAAC,CAAC,CAAC;IACrH,IAAI,CAACsH,OAAO,GAAGnH,kBAAkB,CAAC,IAAI,CAACgM,EAAE,CAAC,CAACjB,IAAI,CAACrK,GAAG,CAAC,CAAC;MAAE4L;IAAM,CAAC,KAAMA,KAAK,KAAK,WAAW,GAAG,UAAU,GAAG,SAAU,CAAC,EAAE3L,SAAS,CAAC,SAAS,CAAC,CAAC;IAC3I,IAAI,CAACyG,iBAAiB,GAAGvG,aAAa,CAAC,CACnC,IAAI,CAACwL,YAAY,CAACtB,IAAI,CAACrK,GAAG,CAAC,CAAC,CAAC8L,CAAC,EAAEC,CAAC,CAAC,KAAK,GAAGtM,KAAK,CAACqM,CAAC,CAAC,KAAKrM,KAAK,CAACsM,CAAC,CAAC,EAAE,CAAC,CAAC,EACnE,IAAI,CAACnG,KAAK,EACV,IAAI,CAAC8F,SAAS,CACjB,CAAC,CAACrB,IAAI,CAACrK,GAAG,CAAC,CAAC,CAACgM,SAAS,EAAEC,IAAI,EAAEC,QAAQ,CAAC,KAAK,aAAaF,SAAS,WAAWC,IAAI,YAAYC,QAAQ,MAAM,CAAC,CAAC;IAC9G,IAAI,CAACvF,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACE,SAAS,GAAG,KAAK;EAC1B;EACAzB,MAAMA,CAAA,EAAG;IACL,IAAI,CAACsG,SAAS,CAAC5I,IAAI,CAAC,IAAI,CAAC4I,SAAS,CAACrI,KAAK,GAAG+H,cAAc,CAAC;EAC9D;EACArF,KAAKA,CAACoG,KAAK,EAAE;IACT,IAAI,CAACR,YAAY,CAAC7I,IAAI,CAAC,IAAI,CAACsJ,qBAAqB,CAAC,IAAI,CAACT,YAAY,CAACtI,KAAK,CAAC,CAAC,CAAC,GAAG8I,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAACR,YAAY,CAACtI,KAAK,CAAC,CAAC,CAAC,GAAG8I,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACpI;EACA/F,UAAUA,CAACiG,cAAc,EAAE;IACvB,MAAM;MAAEC,WAAW;MAAEC;IAAa,CAAC,GAAGF,cAAc;IACpD,IAAI,CAACG,OAAO,CAACF,WAAW,EAAEC,YAAY,CAAC;EAC3C;EACAtG,MAAMA,CAAC;IAAEwG,OAAO;IAAEC,OAAO;IAAEP;EAAM,CAAC,EAAE;IAChC,IAAI,IAAI,CAACxF,QAAQ,EAAE;MACf,IAAI,CAACgG,WAAW,CAACF,OAAO,EAAEC,OAAO,EAAEP,KAAK,CAAC;IAC7C;EACJ;EACA7F,QAAQA,CAAC,CAACsG,KAAK,CAAC,EAAE;IACd,IAAIA,KAAK,EAAEC,WAAW,EAAE;MACpB,IAAI,CAACL,OAAO,CAACI,KAAK,CAACC,WAAW,CAACtB,KAAK,EAAEqB,KAAK,CAACC,WAAW,CAACrB,MAAM,CAAC;MAC/D,IAAI,CAACC,GAAG,CAACqB,aAAa,CAAC,CAAC;IAC5B;EACJ;EACAhI,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACc,KAAK,CAAC9C,IAAI,CAAC,IAAI,CAAC6C,OAAO,CAAC;IAC7B,IAAI,CAACgG,YAAY,CAAC7I,IAAI,CAACqI,iBAAiB,CAAC;EAC7C;EACAzF,OAAOA,CAACuG,IAAI,EAAE;IACV,IAAI,CAACrG,KAAK,CAAC9C,IAAI,CAACmJ,IAAI,CAAC;IACrB,MAAM,CAACH,CAAC,EAAEC,CAAC,CAAC,GAAG,IAAI,CAACJ,YAAY,CAACtI,KAAK;IACtC,IAAI,CAACsI,YAAY,CAAC7I,IAAI,CAAC,IAAI,CAACsJ,qBAAqB,CAACN,CAAC,EAAEC,CAAC,CAAC,CAAC;EAC5D;EACA,IAAIgB,OAAOA,CAAA,EAAG;IACV,MAAMC,OAAO,GAAI,CAAC,IAAI,CAACpH,KAAK,CAACvC,KAAK,GAAG,IAAI,CAACsC,OAAO,IAAI,IAAI,CAAC4F,KAAK,GAAI,CAAC;IACpE,MAAM0B,OAAO,GAAI,CAAC,IAAI,CAACrH,KAAK,CAACvC,KAAK,GAAG,IAAI,CAACsC,OAAO,IAAI,IAAI,CAAC6F,MAAM,GAAI,CAAC;IACrE,OAAO;MAAEwB,OAAO;MAAEC;IAAQ,CAAC;EAC/B;EACAC,gBAAgBA,CAACC,aAAa,EAAEC,YAAY,EAAEC,SAAS,EAAEC,QAAQ,EAAE;IAC/D,MAAMC,OAAO,GAAGJ,aAAa,GAAGE,SAAS,GAAGnC,kBAAkB,IAC1DkC,YAAY,GAAGE,QAAQ,GAAGpC,kBAAkB;IAChD,MAAM;MAAEqB,YAAY;MAAED;IAAY,CAAC,GAAG,IAAI,CAAChB,EAAE;IAC7C,OAAOiC,OAAO,GACR7O,QAAQ,CAAC8O,IAAI,CAAChJ,GAAG,CAAE+H,YAAY,GAAGrB,kBAAkB,GAAIiC,aAAa,EAAGb,WAAW,GAAGpB,kBAAkB,GAAIkC,YAAY,CAAC,EAAE,CAAC,CAAC,GAC7H,CAAC;EACX;EACAZ,OAAOA,CAACjB,KAAK,EAAEC,MAAM,EAAE;IACnB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC7F,OAAO,GAAG,IAAI,CAACuH,gBAAgB,CAAC1B,MAAM,EAAED,KAAK,EAAE,IAAI,CAACD,EAAE,CAACiB,YAAY,EAAE,IAAI,CAACjB,EAAE,CAACgB,WAAW,CAAC;IAC9F,IAAI,CAAC1G,KAAK,CAAC9C,IAAI,CAAC,IAAI,CAAC6C,OAAO,CAAC;IAC7B,IAAI,CAACgG,YAAY,CAAC7I,IAAI,CAACqI,iBAAiB,CAAC;IACzC,IAAI,CAACO,SAAS,CAAC5I,IAAI,CAAC,CAAC,CAAC;EAC1B;EACA6J,WAAWA,CAACF,OAAO,EAAEC,OAAO,EAAEP,KAAK,EAAE;IACjC,MAAMsB,QAAQ,GAAG,IAAI,CAAC7H,KAAK,CAACvC,KAAK;IACjC,MAAMqK,QAAQ,GAAGjP,QAAQ,CAACgP,QAAQ,GAAGtB,KAAK,EAAE,IAAI,CAACxG,OAAO,EAAE,CAAC,CAAC;IAC5D,MAAMgI,MAAM,GAAG,IAAI,CAACC,cAAc,CAAC;MAAEnB,OAAO;MAAEC;IAAQ,CAAC,EAAE,IAAI,CAACf,YAAY,CAACtI,KAAK,EAAE,IAAI,CAACuC,KAAK,CAACvC,KAAK,CAAC;IACnG,MAAMwK,KAAK,GAAGF,MAAM,CAAC,CAAC,CAAC,GAAGF,QAAQ,GAAGE,MAAM,CAAC,CAAC,CAAC,GAAGD,QAAQ;IACzD,MAAMI,KAAK,GAAGH,MAAM,CAAC,CAAC,CAAC,GAAGF,QAAQ,GAAGE,MAAM,CAAC,CAAC,CAAC,GAAGD,QAAQ;IACzD,IAAI,CAAC9H,KAAK,CAAC9C,IAAI,CAAC4K,QAAQ,CAAC;IACzB,IAAI,CAAC/B,YAAY,CAAC7I,IAAI,CAAC,IAAI,CAACsJ,qBAAqB,CAAC,IAAI,CAACT,YAAY,CAACtI,KAAK,CAAC,CAAC,CAAC,GAAGwK,KAAK,EAAE,IAAI,CAAClC,YAAY,CAACtI,KAAK,CAAC,CAAC,CAAC,GAAGyK,KAAK,CAAC,CAAC;EAC9H;EACA1B,qBAAqBA,CAACN,CAAC,EAAEC,CAAC,EAAE;IACxB,MAAM;MAAEiB,OAAO;MAAEC;IAAQ,CAAC,GAAG,IAAI,CAACF,OAAO;IACzC,OAAO,CAACtO,QAAQ,CAACqN,CAAC,EAAE,CAACkB,OAAO,EAAEA,OAAO,CAAC,EAAEvO,QAAQ,CAACsN,CAAC,EAAE,CAACkB,OAAO,EAAEA,OAAO,CAAC,CAAC;EAC3E;EACAW,cAAcA,CAAC;IAAEnB,OAAO;IAAEC;EAAQ,CAAC,EAAE,CAACZ,CAAC,EAAEC,CAAC,CAAC,EAAEgC,KAAK,EAAE;IAChD,OAAO,CACH,CAACtB,OAAO,GAAGX,CAAC,GAAG,IAAI,CAACR,EAAE,CAAC0C,WAAW,GAAG,CAAC,IAAID,KAAK,EAC/C,CAACrB,OAAO,GAAGX,CAAC,GAAG,IAAI,CAACT,EAAE,CAAC2C,YAAY,GAAG,CAAC,IAAIF,KAAK,CACnD;EACL;EACA;IAAS,IAAI,CAAC5G,IAAI,YAAA+G,4BAAA7G,CAAA;MAAA,YAAAA,CAAA,IAAyFgE,mBAAmB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAACtC,IAAI,kBArO+EjL,EAAE,CAAAkL,iBAAA;MAAAxB,IAAA,EAqOJ6D,mBAAmB;MAAA5D,SAAA;MAAA4B,MAAA;QAAA1C,QAAA;QAAAE,SAAA;MAAA;MAAA7D,UAAA;MAAA6E,QAAA,GArOjB/J,EAAE,CAAAyL,mBAAA;MAAA4E,kBAAA,EAAAnJ,GAAA;MAAAwE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAyE,6BAAAnN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnD,EAAE,CAAAuQ,eAAA,CAAAtJ,GAAA;UAAFjH,EAAE,CAAA4F,UAAA,IAAAmC,2CAAA,2BAqOgM,CAAC;UArOnM/H,EAAE,CAAAoF,MAAA;QAAA;QAAA,IAAAjC,EAAA;UAAFnD,EAAE,CAAAuE,UAAA,SAAFvE,EAAE,CAAAqF,WAAA,OAAAjC,GAAA,CAAAuH,MAAA,CAqOqL,CAAC;QAAA;MAAA;MAAAoB,YAAA,GAA44ElM,SAAS,EAA8CC,IAAI,EAA6FgB,SAAS,EAAoIc,EAAE,CAACmL,gBAAgB,EAAqJnL,EAAE,CAACoL,eAAe,EAA2F1L,MAAM,EAA0E8H,gBAAgB,EAA+DiD,cAAc,EAAiI9K,OAAO,EAA4EJ,kBAAkB,EAAoQC,gBAAgB;MAAA4K,MAAA;MAAAC,eAAA;IAAA,EAA0I;EAAE;AACthI;AACA;EAAA,QAAA9B,SAAA,oBAAAA,SAAA,KAvOqGnK,EAAE,CAAAoK,iBAAA,CAuOXmD,mBAAmB,EAAc,CAAC;IAClH7D,IAAI,EAAEtJ,SAAS;IACfiK,IAAI,EAAE,CAAC;MAAEnF,UAAU,EAAE,IAAI;MAAEoF,QAAQ,EAAE,aAAa;MAAE4B,OAAO,EAAE,CACjDrM,SAAS,EACTC,IAAI,EACJgB,SAAS,EACTe,OAAO,EACPP,MAAM,EACN8H,gBAAgB,EAChBiD,cAAc,EACd9K,OAAO,EACPJ,kBAAkB,EAClBC,gBAAgB,CACnB;MAAE6K,eAAe,EAAE5L,uBAAuB,CAAC8L,MAAM;MAAEN,QAAQ,EAAE,wqDAAwqD;MAAEG,MAAM,EAAE,CAAC,utBAAutB;IAAE,CAAC;EACv9E,CAAC,CAAC,QAAkB;IAAEnD,QAAQ,EAAE,CAAC;MACzBa,IAAI,EAAEpJ;IACV,CAAC,CAAC;IAAEyI,SAAS,EAAE,CAAC;MACZW,IAAI,EAAEpJ;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkQ,eAAe,CAAC;EAClB;IAAS,IAAI,CAACnH,IAAI,YAAAoH,wBAAAlH,CAAA;MAAA,YAAAA,CAAA,IAAyFiH,eAAe;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACvF,IAAI,kBA7P+EjL,EAAE,CAAAkL,iBAAA;MAAAxB,IAAA,EA6PJ8G,eAAe;MAAA7G,SAAA;MAAAzE,UAAA;MAAA6E,QAAA,GA7Pb/J,EAAE,CAAAyL,mBAAA;MAAA4E,kBAAA,EAAArH,GAAA;MAAA0C,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAA6E,yBAAAvN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnD,EAAE,CAAAuQ,eAAA;UAAFvQ,EAAE,CAAAyI,YAAA,EA8PlF,CAAC;QAAA;MAAA;MAAAuD,MAAA;MAAAC,eAAA;IAAA,EACuM;EAAE;AAC/N;AACA;EAAA,QAAA9B,SAAA,oBAAAA,SAAA,KAjQqGnK,EAAE,CAAAoK,iBAAA,CAiQXoG,eAAe,EAAc,CAAC;IAC9G9G,IAAI,EAAEtJ,SAAS;IACfiK,IAAI,EAAE,CAAC;MAAEnF,UAAU,EAAE,IAAI;MAAEoF,QAAQ,EAAE,mBAAmB;MAAEuB,QAAQ,EAAE;AAChF;AACA,KAAK;MAAEI,eAAe,EAAE5L,uBAAuB,CAAC8L,MAAM;MAAEH,MAAM,EAAE,CAAC,qIAAqI;IAAE,CAAC;EACjM,CAAC,CAAC;AAAA;AAEV,MAAM2E,UAAU,GAAG,CACfpD,mBAAmB,EACnBiD,eAAe,EACf/F,oBAAoB,EACpBrB,gBAAgB,EAChBiD,cAAc,CACjB;AAED,MAAMuE,gBAAgB,CAAC;EACnBlG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmG,OAAO,GAAGjO,aAAa,CAAC,CAAC;EAClC;EACA;IAAS,IAAI,CAACyG,IAAI,YAAAyH,yBAAAvH,CAAA;MAAA,YAAAA,CAAA,IAAyFqH,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAAC3F,IAAI,kBArR+EjL,EAAE,CAAAkL,iBAAA;MAAAxB,IAAA,EAqRJkH,gBAAgB;MAAAjH,SAAA;MAAAE,YAAA,WAAAkH,8BAAA5N,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArRdnD,EAAE,CAAAyD,UAAA,yBAAAuN,gDAAA;YAAA,OAqRJ5N,GAAA,CAAAyN,OAAA,CAAAI,SAAA,CAAAC,QAAA,CAA2B,CAAC;UAAA,UArR1BlR,EAAE,CAAAqL,iBAqRW,CAAC;QAAA;MAAA;MAAAnG,UAAA;MAAA6E,QAAA,GArRd/J,EAAE,CAAAmR,uBAAA,EAqRiLzO,IAAI,CAACC,WAAW,IArRnM3C,EAAE,CAAAyL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAuF,0BAAAjO,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnD,EAAE,CAAA4F,UAAA,IAAAqD,wCAAA,yBAsRlB,CAAC;QAAA;QAAA,IAAA9F,EAAA;UAtRenD,EAAE,CAAAuE,UAAA,uBAAAnB,GAAA,CAAAyN,OAAA,CAAAQ,OAsR7C,CAAC,8BAAAjO,GAAA,CAAAyN,OAAwB,CAAC;QAAA;MAAA;MAAA9E,YAAA,GAG6qDlJ,kBAAkB;MAAAmJ,MAAA;MAAAsF,aAAA;MAAArF,eAAA;IAAA,EAAmM;EAAE;AACx9D;AACA;EAAA,QAAA9B,SAAA,oBAAAA,SAAA,KA3RqGnK,EAAE,CAAAoK,iBAAA,CA2RXwG,gBAAgB,EAAc,CAAC;IAC/GlH,IAAI,EAAEtJ,SAAS;IACfiK,IAAI,EAAE,CAAC;MAAEnF,UAAU,EAAE,IAAI;MAAEoF,QAAQ,EAAE,oBAAoB;MAAE4B,OAAO,EAAE,CAACrJ,kBAAkB,CAAC;MAAEgJ,QAAQ,EAAE;AAChH;AACA;AACA;AACA,KAAK;MAAEyF,aAAa,EAAE7Q,iBAAiB,CAAC8Q,IAAI;MAAEtF,eAAe,EAAE5L,uBAAuB,CAAC8L,MAAM;MAAEqF,cAAc,EAAE,CAAC7O,WAAW,CAAC;MAAE6H,IAAI,EAAE;QAC5G,wBAAwB,EAAE;MAC9B,CAAC;MAAEwB,MAAM,EAAE,CAAC,orDAAorD;IAAE,CAAC;EAC/sD,CAAC,CAAC;AAAA;AAEV,MAAMyF,uBAAuB,SAAS1O,iBAAiB,CAAC;EACpD;IAAS,IAAI,CAACsG,IAAI;MAAA,IAAAqI,oCAAA;MAAA,gBAAAC,gCAAApI,CAAA;QAAA,QAAAmI,oCAAA,KAAAA,oCAAA,GAvS+E1R,EAAE,CAAA4R,qBAAA,CAuSQH,uBAAuB,IAAAlI,CAAA,IAAvBkI,uBAAuB;MAAA;IAAA,IAAsD;EAAE;EAC1L;IAAS,IAAI,CAACI,KAAK,kBAxS8E7R,EAAE,CAAA8R,kBAAA;MAAAC,KAAA,EAwSYN,uBAAuB;MAAAO,OAAA,EAAAA,CAAA,MAAkC,MAAM,IAAIP,uBAAuB,CAACxO,WAAW,EAAE2N,gBAAgB,CAAC;MAAAqB,UAAA,EAApF;IAAM,EAAiF;EAAE;AACjP;AACA;EAAA,QAAA9H,SAAA,oBAAAA,SAAA,KA1SqGnK,EAAE,CAAAoK,iBAAA,CA0SXqH,uBAAuB,EAAc,CAAC;IACtH/H,IAAI,EAAEhJ,UAAU;IAChB2J,IAAI,EAAE,CAAC;MACC4H,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAEA,CAAA,KAAM,IAAIT,uBAAuB,CAACxO,WAAW,EAAE2N,gBAAgB;IAC/E,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMuB,yBAAyB,SAASrP,mBAAmB,CAAC;EACxD;IAAS,IAAI,CAACuG,IAAI;MAAA,IAAA+I,sCAAA;MAAA,gBAAAC,kCAAA9I,CAAA;QAAA,QAAA6I,sCAAA,KAAAA,sCAAA,GAnT+EpS,EAAE,CAAA4R,qBAAA,CAmTQO,yBAAyB,IAAA5I,CAAA,IAAzB4I,yBAAyB;MAAA;IAAA,IAAqD;EAAE;EAC3L;IAAS,IAAI,CAAC3I,IAAI,kBApT+ExJ,EAAE,CAAAyJ,iBAAA;MAAAC,IAAA,EAoTJyI,yBAAyB;MAAAxI,SAAA;MAAA4B,MAAA;QAAA+G,IAAA,GApTvBtS,EAAE,CAAAuS,YAAA,CAAAhB,IAAA;MAAA;MAAA/F,OAAA;QAAAgH,UAAA;MAAA;MAAAtN,UAAA;MAAA6E,QAAA,GAAF/J,EAAE,CAAAgK,kBAAA,CAoToM,CAAChH,YAAY,CAACyO,uBAAuB,CAAC,CAAC,GApT7OzR,EAAE,CAAAyS,0BAAA;IAAA,EAoTmR;EAAE;AAC5X;AACA;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KAtTqGnK,EAAE,CAAAoK,iBAAA,CAsTX+H,yBAAyB,EAAc,CAAC;IACxHzI,IAAI,EAAEzJ,SAAS;IACfoK,IAAI,EAAE,CAAC;MACCnF,UAAU,EAAE,IAAI;MAChBoF,QAAQ,EAAE,+BAA+B;MACzCiB,MAAM,EAAE,CAAC,wBAAwB,CAAC;MAClCC,OAAO,EAAE,CAAC,oCAAoC,CAAC;MAC/CjB,SAAS,EAAE,CAACvH,YAAY,CAACyO,uBAAuB,CAAC;IACrD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASd,UAAU,EAAEvH,gBAAgB,EAAEmE,mBAAmB,EAAEqD,gBAAgB,EAAEuB,yBAAyB,EAAEV,uBAAuB,EAAEhH,oBAAoB,EAAE+F,eAAe,EAAEnE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}