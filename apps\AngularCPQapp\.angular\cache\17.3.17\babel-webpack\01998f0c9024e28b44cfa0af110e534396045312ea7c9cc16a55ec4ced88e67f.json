{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, effect, signal, Directive, Input } from '@angular/core';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiCreateToken, tuiWithStyles, tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiDropdownDirective } from '@taiga-ui/core/directives/dropdown';\nimport { TUI_ICON_END } from '@taiga-ui/core/tokens';\nconst TUI_CHEVRON = tuiCreateToken('@tui.chevron-down');\nclass TuiChevronStyles {\n  static {\n    this.ɵfac = function TuiChevronStyles_Factory(t) {\n      return new (t || TuiChevronStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiChevronStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-chevron\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiChevronStyles_Template(rf, ctx) {},\n      styles: [\"[tuiChevron][tuiIcons]:after,tui-icon[tuiChevron]:after{transition-property:transform,color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;cursor:pointer}tui-textfield[tuiChevron][tuiIcons]:after{font-size:1rem}tui-textfield[data-size=s][tuiChevron][tuiIcons]:after{margin-inline-end:calc(-.125rem - var(--t-padding))}tui-textfield[data-size=m][tuiChevron][tuiIcons]:after{margin-inline-end:calc(.125rem - var(--t-padding))}tui-textfield[data-size=l][tuiChevron][tuiIcons]:after{margin-inline-end:calc(.25rem - var(--t-padding))}[tuiChevron][tuiIcons]._chevron-rotated:after,tui-icon[tuiChevron]._chevron-rotated:after{transform:rotate(180deg)}[tuiChevron]:has(input[tuiAppearance]:read-only):after{cursor:default}tui-textfield[tuiChevron][style*=\\\"--t-icon-end:\\\"][data-size=l]{--t-end: 1.875rem}tui-textfield[tuiChevron][style*=\\\"--t-icon-end:\\\"][data-size=m]{--t-end: 1.5rem}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiChevronStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-chevron'\n      },\n      styles: [\"[tuiChevron][tuiIcons]:after,tui-icon[tuiChevron]:after{transition-property:transform,color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;cursor:pointer}tui-textfield[tuiChevron][tuiIcons]:after{font-size:1rem}tui-textfield[data-size=s][tuiChevron][tuiIcons]:after{margin-inline-end:calc(-.125rem - var(--t-padding))}tui-textfield[data-size=m][tuiChevron][tuiIcons]:after{margin-inline-end:calc(.125rem - var(--t-padding))}tui-textfield[data-size=l][tuiChevron][tuiIcons]:after{margin-inline-end:calc(.25rem - var(--t-padding))}[tuiChevron][tuiIcons]._chevron-rotated:after,tui-icon[tuiChevron]._chevron-rotated:after{transform:rotate(180deg)}[tuiChevron]:has(input[tuiAppearance]:read-only):after{cursor:default}tui-textfield[tuiChevron][style*=\\\"--t-icon-end:\\\"][data-size=l]{--t-end: 1.875rem}tui-textfield[tuiChevron][style*=\\\"--t-icon-end:\\\"][data-size=m]{--t-end: 1.5rem}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiChevron {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.dropdown = inject(TuiDropdownDirective, {\n      optional: true\n    });\n    this.nothing = tuiWithStyles(TuiChevronStyles);\n    this.toggle = effect(() => this.el.classList.toggle('_chevron-rotated', this.chevron() || this.chevron() === '' && !!this.dropdown?.ref()));\n    // TODO: refactor to signal inputs after Angular update\n    this.chevron = signal('');\n  }\n  set tuiChevron(chevron) {\n    this.chevron.set(chevron);\n  }\n  static {\n    this.ɵfac = function TuiChevron_Factory(t) {\n      return new (t || TuiChevron)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiChevron,\n      selectors: [[\"\", \"tuiChevron\", \"\"]],\n      hostAttrs: [\"tuiChevron\", \"\"],\n      inputs: {\n        tuiChevron: \"tuiChevron\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TUI_ICON_END, TUI_CHEVRON)])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiChevron, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiChevron]',\n      providers: [tuiProvide(TUI_ICON_END, TUI_CHEVRON)],\n      host: {\n        tuiChevron: ''\n      }\n    }]\n  }], null, {\n    tuiChevron: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_CHEVRON, TuiChevron };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "effect", "signal", "Directive", "Input", "tuiInjectElement", "tuiCreateToken", "tuiWithStyles", "tui<PERSON><PERSON><PERSON>", "TuiDropdownDirective", "TUI_ICON_END", "TUI_CHEVRON", "TuiChevronStyles", "ɵfac", "TuiChevronStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiChevronStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "el", "dropdown", "optional", "nothing", "toggle", "classList", "chevron", "ref", "tui<PERSON><PERSON><PERSON><PERSON>", "set", "TuiChevron_Factory", "ɵdir", "ɵɵdefineDirective", "inputs", "ɵɵProvidersFeature", "selector", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-chevron.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, effect, signal, Directive, Input } from '@angular/core';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { tuiCreateToken, tuiWithStyles, tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiDropdownDirective } from '@taiga-ui/core/directives/dropdown';\nimport { TUI_ICON_END } from '@taiga-ui/core/tokens';\n\nconst TUI_CHEVRON = tuiCreateToken('@tui.chevron-down');\nclass TuiChevronStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiChevronStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiChevronStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-chevron\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiChevron][tuiIcons]:after,tui-icon[tuiChevron]:after{transition-property:transform,color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;cursor:pointer}tui-textfield[tuiChevron][tuiIcons]:after{font-size:1rem}tui-textfield[data-size=s][tuiChevron][tuiIcons]:after{margin-inline-end:calc(-.125rem - var(--t-padding))}tui-textfield[data-size=m][tuiChevron][tuiIcons]:after{margin-inline-end:calc(.125rem - var(--t-padding))}tui-textfield[data-size=l][tuiChevron][tuiIcons]:after{margin-inline-end:calc(.25rem - var(--t-padding))}[tuiChevron][tuiIcons]._chevron-rotated:after,tui-icon[tuiChevron]._chevron-rotated:after{transform:rotate(180deg)}[tuiChevron]:has(input[tuiAppearance]:read-only):after{cursor:default}tui-textfield[tuiChevron][style*=\\\"--t-icon-end:\\\"][data-size=l]{--t-end: 1.875rem}tui-textfield[tuiChevron][style*=\\\"--t-icon-end:\\\"][data-size=m]{--t-end: 1.5rem}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiChevronStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { class: 'tui-chevron' }, styles: [\"[tuiChevron][tuiIcons]:after,tui-icon[tuiChevron]:after{transition-property:transform,color;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;cursor:pointer}tui-textfield[tuiChevron][tuiIcons]:after{font-size:1rem}tui-textfield[data-size=s][tuiChevron][tuiIcons]:after{margin-inline-end:calc(-.125rem - var(--t-padding))}tui-textfield[data-size=m][tuiChevron][tuiIcons]:after{margin-inline-end:calc(.125rem - var(--t-padding))}tui-textfield[data-size=l][tuiChevron][tuiIcons]:after{margin-inline-end:calc(.25rem - var(--t-padding))}[tuiChevron][tuiIcons]._chevron-rotated:after,tui-icon[tuiChevron]._chevron-rotated:after{transform:rotate(180deg)}[tuiChevron]:has(input[tuiAppearance]:read-only):after{cursor:default}tui-textfield[tuiChevron][style*=\\\"--t-icon-end:\\\"][data-size=l]{--t-end: 1.875rem}tui-textfield[tuiChevron][style*=\\\"--t-icon-end:\\\"][data-size=m]{--t-end: 1.5rem}\\n\"] }]\n        }] });\nclass TuiChevron {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.dropdown = inject(TuiDropdownDirective, { optional: true });\n        this.nothing = tuiWithStyles(TuiChevronStyles);\n        this.toggle = effect(() => this.el.classList.toggle('_chevron-rotated', this.chevron() || (this.chevron() === '' && !!this.dropdown?.ref())));\n        // TODO: refactor to signal inputs after Angular update\n        this.chevron = signal('');\n    }\n    set tuiChevron(chevron) {\n        this.chevron.set(chevron);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiChevron, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiChevron, isStandalone: true, selector: \"[tuiChevron]\", inputs: { tuiChevron: \"tuiChevron\" }, host: { attributes: { \"tuiChevron\": \"\" } }, providers: [tuiProvide(TUI_ICON_END, TUI_CHEVRON)], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiChevron, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiChevron]',\n                    providers: [tuiProvide(TUI_ICON_END, TUI_CHEVRON)],\n                    host: { tuiChevron: '' },\n                }]\n        }], propDecorators: { tuiChevron: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_CHEVRON, TuiChevron };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAC/H,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,cAAc,EAAEC,aAAa,EAAEC,UAAU,QAAQ,mCAAmC;AAC7F,SAASC,oBAAoB,QAAQ,oCAAoC;AACzE,SAASC,YAAY,QAAQ,uBAAuB;AAEpD,MAAMC,WAAW,GAAGL,cAAc,CAAC,mBAAmB,CAAC;AACvD,MAAMM,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,gBAAgB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACI,IAAI,kBAD+EpB,EAAE,CAAAqB,iBAAA;MAAAC,IAAA,EACJN,gBAAgB;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADd1B,EAAE,CAAA2B,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACmpC;EAAE;AAC5vC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGrC,EAAE,CAAAsC,iBAAA,CAGXtB,gBAAgB,EAAc,CAAC;IAC/GM,IAAI,EAAErB,SAAS;IACfsC,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAEjC,iBAAiB,CAACsC,IAAI;MAAEJ,eAAe,EAAEjC,uBAAuB,CAACsC,MAAM;MAAEC,IAAI,EAAE;QAAEC,KAAK,EAAE;MAAc,CAAC;MAAET,MAAM,EAAE,CAAC,u5BAAu5B;IAAE,CAAC;EACxkC,CAAC,CAAC;AAAA;AACV,MAAMU,UAAU,CAAC;EACbC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAGrC,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACsC,QAAQ,GAAG3C,MAAM,CAACS,oBAAoB,EAAE;MAAEmC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChE,IAAI,CAACC,OAAO,GAAGtC,aAAa,CAACK,gBAAgB,CAAC;IAC9C,IAAI,CAACkC,MAAM,GAAG7C,MAAM,CAAC,MAAM,IAAI,CAACyC,EAAE,CAACK,SAAS,CAACD,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAACE,OAAO,CAAC,CAAC,IAAK,IAAI,CAACA,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,IAAI,CAACL,QAAQ,EAAEM,GAAG,CAAC,CAAE,CAAC,CAAC;IAC7I;IACA,IAAI,CAACD,OAAO,GAAG9C,MAAM,CAAC,EAAE,CAAC;EAC7B;EACA,IAAIgD,UAAUA,CAACF,OAAO,EAAE;IACpB,IAAI,CAACA,OAAO,CAACG,GAAG,CAACH,OAAO,CAAC;EAC7B;EACA;IAAS,IAAI,CAACnC,IAAI,YAAAuC,mBAAArC,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,UAAU;IAAA,CAAmD;EAAE;EAC1K;IAAS,IAAI,CAACa,IAAI,kBApB+EzD,EAAE,CAAA0D,iBAAA;MAAApC,IAAA,EAoBJsB,UAAU;MAAArB,SAAA;MAAAC,SAAA,iBAA0H,EAAE;MAAAmC,MAAA;QAAAL,UAAA;MAAA;MAAA7B,UAAA;MAAAC,QAAA,GApBpI1B,EAAE,CAAA4D,kBAAA,CAoBmJ,CAAChD,UAAU,CAACE,YAAY,EAAEC,WAAW,CAAC,CAAC;IAAA,EAAiB;EAAE;AACpT;AACA;EAAA,QAAAsB,SAAA,oBAAAA,SAAA,KAtBqGrC,EAAE,CAAAsC,iBAAA,CAsBXM,UAAU,EAAc,CAAC;IACzGtB,IAAI,EAAEf,SAAS;IACfgC,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBoC,QAAQ,EAAE,cAAc;MACxBC,SAAS,EAAE,CAAClD,UAAU,CAACE,YAAY,EAAEC,WAAW,CAAC,CAAC;MAClD2B,IAAI,EAAE;QAAEY,UAAU,EAAE;MAAG;IAC3B,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEA,UAAU,EAAE,CAAC;MAC3BhC,IAAI,EAAEd;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASO,WAAW,EAAE6B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}