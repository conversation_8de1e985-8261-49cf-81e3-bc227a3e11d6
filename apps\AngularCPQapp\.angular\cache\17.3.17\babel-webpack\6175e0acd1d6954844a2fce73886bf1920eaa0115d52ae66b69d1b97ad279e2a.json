{"ast": null, "code": "// Import des constantes\nimport { LUXURY_CARS_CATEGORY, CAR_FAMILIES, LUXURY_CARS_PRODUCTS } from '../../constants/cpq.constants';\nimport { PropertyUtils } from '../../models/property/property';\nimport { PRODUCT_PROPERTIES, PRODUCT_PROPERTY_ASSOCIATIONS } from '../../constants/property.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../product-tree/product-tree.component\";\nconst _c0 = (a0, a1) => ({\n  \"bg-primary\": a0,\n  \"bg-info\": a1\n});\nconst _c1 = (a0, a1, a2) => ({\n  \"btn-outline-primary\": a0,\n  \"btn-warning\": a1,\n  \"btn-success\": a2\n});\nconst _c2 = (a0, a1, a2) => ({\n  \"bi-plus-lg\": a0,\n  \"bi-dash-lg\": a1,\n  \"bi-check-lg\": a2\n});\nconst _c3 = (a0, a1) => ({\n  \"bi-arrows-expand\": a0,\n  \"bi-arrows-collapse\": a1\n});\nconst _c4 = () => [];\nconst _c5 = (a0, a1) => ({\n  \"bi-folder-fill\": a0,\n  \"bi-gear-fill\": a1\n});\nconst _c6 = (a0, a1, a2, a3) => ({\n  \"bi-palette\": a0,\n  \"bi-circle\": a1,\n  \"bi-texture\": a2,\n  \"bi-star\": a3\n});\nconst _c7 = (a0, a1) => ({\n  \"bg-success\": a0,\n  \"bg-warning text-dark\": a1\n});\nconst _c8 = (a0, a1) => ({\n  \"bi-chevron-down\": a0,\n  \"bi-chevron-right\": a1\n});\nconst _c9 = (a0, a1) => ({\n  \"bi-folder-fill text-warning\": a0,\n  \"bi-file-earmark text-primary\": a1\n});\nfunction CpqConfiguratorComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"span\", 57);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CpqConfiguratorComponent_div_20_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_20_div_13_Template_div_click_1_listener() {\n      const family_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.currentStep === 1 && ctx_r2.toggleFamilySelection(family_r2.id));\n    });\n    i0.ɵɵelementStart(2, \"div\", 5)(3, \"div\", 69)(4, \"div\", 70);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 71)(7, \"h6\", 72);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 73);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 74)(12, \"span\", 75);\n    i0.ɵɵtext(13, \" Famille de produits \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 76)(15, \"input\", 77);\n    i0.ɵɵlistener(\"change\", function CpqConfiguratorComponent_div_20_div_13_Template_input_change_15_listener() {\n      const family_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.currentStep === 1 && ctx_r2.toggleFamilySelection(family_r2.id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"label\", 78);\n    i0.ɵɵtext(17, \" S\\u00E9lectionner \");\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const family_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", ctx_r2.isFamilySelected(family_r2.id))(\"disabled\", ctx_r2.currentStep > 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(family_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(family_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(family_r2.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"checked\", ctx_r2.isFamilySelected(family_r2.id))(\"disabled\", ctx_r2.currentStep > 1);\n  }\n}\nfunction CpqConfiguratorComponent_div_20_div_14_span_6_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_20_div_14_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\")(1, \"strong\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CpqConfiguratorComponent_div_20_div_14_span_6_span_3_Template, 2, 0, \"span\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const familyId_r4 = ctx.$implicit;\n    const last_r5 = ctx.last;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getFamilyName(familyId_r4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r5);\n  }\n}\nfunction CpqConfiguratorComponent_div_20_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"div\", 80);\n    i0.ɵɵelement(2, \"i\", 34);\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" famille(s) s\\u00E9lectionn\\u00E9e(s) : \");\n    i0.ɵɵtemplate(6, CpqConfiguratorComponent_div_20_div_14_span_6_Template, 4, 2, \"span\", 81);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedFamilyIds.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedFamilyIds);\n  }\n}\nfunction CpqConfiguratorComponent_div_20_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"div\", 83)(2, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_20_div_15_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.nextStep());\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelement(4, \"i\", 85);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Voir les produits (\", ctx_r2.selectedFamilyIds.length, \") \");\n  }\n}\nfunction CpqConfiguratorComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 58)(3, \"div\", 59)(4, \"h4\", 7);\n    i0.ɵɵelement(5, \"i\", 60);\n    i0.ɵɵtext(6, \" S\\u00E9lectionnez les familles de produits \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 61);\n    i0.ɵɵtext(8, \"Choisissez une ou plusieurs familles dans la cat\\u00E9gorie \");\n    i0.ɵɵelementStart(9, \"strong\", 62);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 5)(12, \"div\", 63);\n    i0.ɵɵtemplate(13, CpqConfiguratorComponent_div_20_div_13_Template, 18, 10, \"div\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, CpqConfiguratorComponent_div_20_div_14_Template, 7, 2, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, CpqConfiguratorComponent_div_20_div_15_Template, 5, 1, \"div\", 66);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedCategory.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.carFamilies);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFamilyIds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFamilyIds.length > 0 && ctx_r2.currentStep === 1);\n  }\n}\nfunction CpqConfiguratorComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelement(1, \"i\", 87);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵelement(1, \"i\", 93);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.errorMessage, \" \");\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵelement(1, \"i\", 94);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.successMessage, \" \");\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_22_div_10_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.clearAllFilters());\n    });\n    i0.ɵɵelement(1, \"i\", 133);\n    i0.ɵɵtext(2, \" Effacer filtres \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_22_div_10_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      ctx_r2.productFilters.searchTerm = \"\";\n      return i0.ɵɵresetView(ctx_r2.onFilterChange());\n    });\n    i0.ɵɵelement(1, \"i\", 135);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 117);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const family_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", family_r10.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", family_r10.name, \" \");\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_48_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 157);\n    i0.ɵɵelement(1, \"i\", 94);\n    i0.ɵɵtext(2, \" Confirm\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_48_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 158);\n    i0.ɵɵelement(1, \"i\", 159);\n    i0.ɵɵtext(2, \" S\\u00E9lectionn\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_48_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"S\\u00E9lectionner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_48_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"D\\u00E9s\\u00E9lectionner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_48_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Confirm\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_48_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 160);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_22_div_10_div_48_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const product_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.showAssociatedProducts(product_r12));\n    });\n    i0.ɵɵelement(1, \"i\", 161);\n    i0.ɵɵtext(2, \" Voir les produits associ\\u00E9s \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 136)(1, \"div\", 137)(2, \"div\", 138)(3, \"div\", 139)(4, \"div\", 140);\n    i0.ɵɵelement(5, \"i\", 141);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 142);\n    i0.ɵɵtemplate(7, CpqConfiguratorComponent_div_22_div_10_div_48_span_7_Template, 3, 0, \"span\", 143)(8, CpqConfiguratorComponent_div_22_div_10_div_48_span_8_Template, 3, 0, \"span\", 144);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"h6\", 145);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 146);\n    i0.ɵɵelement(12, \"i\", 147);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 148);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 149)(17, \"div\", 150)(18, \"span\", 151);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"strong\", 152);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 153)(23, \"button\", 154);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_22_div_10_div_48_Template_button_click_23_listener() {\n      const product_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.toggleTempProductSelection(product_r12));\n    });\n    i0.ɵɵelement(24, \"i\", 155);\n    i0.ɵɵtemplate(25, CpqConfiguratorComponent_div_22_div_10_div_48_span_25_Template, 2, 0, \"span\", 41)(26, CpqConfiguratorComponent_div_22_div_10_div_48_span_26_Template, 2, 0, \"span\", 41)(27, CpqConfiguratorComponent_div_22_div_10_div_48_span_27_Template, 2, 0, \"span\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, CpqConfiguratorComponent_div_22_div_10_div_48_button_28_Template, 3, 0, \"button\", 156);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const product_r12 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"temp-selected\", ctx_r2.isTempProductSelected(product_r12))(\"confirmed\", ctx_r2.isProductConfirmed(product_r12));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isProductConfirmed(product_r12));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isTempProductSelected(product_r12) && !ctx_r2.isProductConfirmed(product_r12));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r12.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", product_r12.productid, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r12.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(19, _c0, !product_r12.parentproductid, product_r12.parentproductid));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", product_r12.parentproductid ? \"Option\" : \"Principal\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice((product_r12.price || 0) + ctx_r2.getPropertiesPriceForProduct(product_r12.productid)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(22, _c1, !ctx_r2.isTempProductSelected(product_r12) && !ctx_r2.isProductConfirmed(product_r12), ctx_r2.isTempProductSelected(product_r12) && !ctx_r2.isProductConfirmed(product_r12), ctx_r2.isProductConfirmed(product_r12)))(\"disabled\", ctx_r2.isProductConfirmed(product_r12) || ctx_r2.currentStep > 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(26, _c2, !ctx_r2.isTempProductSelected(product_r12) && !ctx_r2.isProductConfirmed(product_r12), ctx_r2.isTempProductSelected(product_r12) && !ctx_r2.isProductConfirmed(product_r12), ctx_r2.isProductConfirmed(product_r12)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isTempProductSelected(product_r12) && !ctx_r2.isProductConfirmed(product_r12));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isTempProductSelected(product_r12) && !ctx_r2.isProductConfirmed(product_r12));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isProductConfirmed(product_r12));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasDirectChildren(product_r12.productid));\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 162)(1, \"div\", 163);\n    i0.ɵɵelement(2, \"i\", 164);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 165);\n    i0.ɵɵtext(4, \"Aucun produit trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 166);\n    i0.ɵɵtext(6, \" Essayez de modifier vos crit\\u00E8res de recherche ou vos filtres \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_50_li_6_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 171);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_22_div_10_div_50_li_6_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const page_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onPageChange(page_r16));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const page_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", page_r16, \" \");\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_50_li_6_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 177);\n    i0.ɵɵtext(1, \" ... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_50_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 170);\n    i0.ɵɵtemplate(1, CpqConfiguratorComponent_div_22_div_10_div_50_li_6_button_1_Template, 2, 1, \"button\", 175)(2, CpqConfiguratorComponent_div_22_div_10_div_50_li_6_span_2_Template, 2, 0, \"span\", 176);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const page_r16 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"active\", page_r16 === ctx_r2.pagination.currentPage)(\"disabled\", page_r16 === -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", page_r16 !== -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", page_r16 === -1);\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 167)(1, \"nav\", 168)(2, \"ul\", 169)(3, \"li\", 170)(4, \"button\", 171);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_22_div_10_div_50_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onPageChange(ctx_r2.pagination.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 172);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, CpqConfiguratorComponent_div_22_div_10_div_50_li_6_Template, 3, 6, \"li\", 173);\n    i0.ɵɵelementStart(7, \"li\", 170)(8, \"button\", 171);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_22_div_10_div_50_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onPageChange(ctx_r2.pagination.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"i\", 174);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r2.pagination.currentPage === 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getPageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r2.pagination.currentPage === ctx_r2.getTotalPages());\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_button_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_22_div_10_button_63_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.toggleAllProductsExpansion());\n    });\n    i0.ɵɵelement(1, \"i\", 155);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c3, ctx_r2.expandedProducts.size === 0, ctx_r2.expandedProducts.size > 0));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.expandedProducts.size === 0 ? \"Tout \\u00E9tendre\" : \"Tout r\\u00E9duire\", \" \");\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 162)(1, \"div\", 163);\n    i0.ɵɵelement(2, \"i\", 178);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 165);\n    i0.ɵɵtext(4, \"Votre panier est vide\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 166);\n    i0.ɵɵtext(6, \"S\\u00E9lectionnez des produits pour les ajouter \\u00E0 votre panier\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_66_div_1_hr_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\");\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 180)(1, \"app-product-tree\", 181);\n    i0.ɵɵlistener(\"productToggle\", function CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_productToggle_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.removeConfirmedProduct($event));\n    })(\"productInclusionToggle\", function CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_productInclusionToggle_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.toggleProductInclusion($event));\n    })(\"quantityUpdate\", function CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_quantityUpdate_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.updateProductQuantity($event.productId, $event.event));\n    })(\"quantityIncrease\", function CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_quantityIncrease_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.increaseQuantity($event));\n    })(\"quantityDecrease\", function CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_quantityDecrease_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.decreaseQuantity($event));\n    })(\"expansionToggle\", function CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_expansionToggle_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.toggleProductExpansion($event));\n    })(\"editProperties\", function CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_editProperties_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.openPropertiesModal($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, CpqConfiguratorComponent_div_22_div_10_div_66_div_1_hr_2_Template, 1, 0, \"hr\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const selection_r19 = ctx.$implicit;\n    const i_r20 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"product\", selection_r19.product)(\"level\", 0)(\"isSelected\", true)(\"isMainProduct\", true)(\"isIncluded\", false)(\"isExpanded\", ctx_r2.isProductExpanded(selection_r19.product.productid))(\"quantity\", selection_r19.quantity)(\"directChildren\", i0.ɵɵpureFunction0(16, _c4))(\"hasChildren\", ctx_r2.hasDirectChildren(selection_r19.product.productid))(\"hasSelectedProperties\", ctx_r2.hasProductProperties(selection_r19.product.productid))(\"selectedPropertiesCount\", ctx_r2.getSelectedPropertiesCountForProduct(selection_r19.product.productid))(\"freeProperties\", ctx_r2.getProductFreeProperties(selection_r19.product.productid))(\"paidProperties\", ctx_r2.getProductPaidProperties(selection_r19.product.productid))(\"requiredProperties\", ctx_r2.getProductRequiredProperties(selection_r19.product.productid))(\"isLocked\", ctx_r2.currentStep > 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r20 < ctx_r2.confirmedSelectedProducts.length - 1);\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template, 3, 17, \"div\", 179);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.confirmedSelectedProducts);\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"div\", 6)(2, \"div\", 182)(3, \"span\", 130);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementStart(5, \"strong\", 183);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 184)(8, \"button\", 185);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_22_div_10_div_67_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.clearAllConfirmedProducts());\n    });\n    i0.ɵɵelement(9, \"i\", 186);\n    i0.ɵɵtext(10, \" Tout supprimer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.confirmedSelectedProducts.length, \" produit(s) \\u2022 \", ctx_r2.getTotalConfirmedQuantity(), \" article(s) \\u2022 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(ctx_r2.totalPrice));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.currentStep > 2);\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_button_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 187);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_22_div_10_button_70_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.previousStep());\n    });\n    i0.ɵɵelement(1, \"i\", 188);\n    i0.ɵɵtext(2, \" Retour aux familles \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\");\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_button_80_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 189);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_22_div_10_button_80_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.nextStep());\n    });\n    i0.ɵɵtext(1, \" Suivant \");\n    i0.ɵɵelement(2, \"i\", 85);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.confirmedSelectedProducts.length === 0);\n  }\n}\nfunction CpqConfiguratorComponent_div_22_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 95)(2, \"div\", 96)(3, \"div\", 6)(4, \"div\")(5, \"h5\", 61);\n    i0.ɵɵelement(6, \"i\", 97);\n    i0.ɵɵtext(7, \" Produits disponibles \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"small\");\n    i0.ɵɵtext(9, \"S\\u00E9lectionnez les produits que vous souhaitez ajouter\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 98)(11, \"span\", 99);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, CpqConfiguratorComponent_div_22_div_10_button_13_Template, 3, 0, \"button\", 100);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 5)(15, \"div\", 101)(16, \"div\", 102)(17, \"div\", 103)(18, \"div\", 104)(19, \"span\", 105);\n    i0.ɵɵelement(20, \"i\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 107);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CpqConfiguratorComponent_div_22_div_10_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.productFilters.searchTerm, $event) || (ctx_r2.productFilters.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function CpqConfiguratorComponent_div_22_div_10_Template_input_input_21_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onFilterChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, CpqConfiguratorComponent_div_22_div_10_button_22_Template, 2, 0, \"button\", 108);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 109)(24, \"select\", 110);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CpqConfiguratorComponent_div_22_div_10_Template_select_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.productFilters.familyFilter, $event) || (ctx_r2.productFilters.familyFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function CpqConfiguratorComponent_div_22_div_10_Template_select_change_24_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onFilterChange());\n    });\n    i0.ɵɵelementStart(25, \"option\", 111);\n    i0.ɵɵtext(26, \"Toutes les familles\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, CpqConfiguratorComponent_div_22_div_10_option_27_Template, 2, 2, \"option\", 112);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 109)(29, \"select\", 110);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CpqConfiguratorComponent_div_22_div_10_Template_select_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.productFilters.typeFilter, $event) || (ctx_r2.productFilters.typeFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function CpqConfiguratorComponent_div_22_div_10_Template_select_change_29_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onFilterChange());\n    });\n    i0.ɵɵelementStart(30, \"option\", 111);\n    i0.ɵɵtext(31, \"Tous les types\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"option\", 113);\n    i0.ɵɵtext(33, \"Produits principaux\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"option\", 114);\n    i0.ɵɵtext(35, \"Options\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"div\", 115)(37, \"select\", 116);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CpqConfiguratorComponent_div_22_div_10_Template_select_ngModelChange_37_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.pagination.itemsPerPage, $event) || (ctx_r2.pagination.itemsPerPage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function CpqConfiguratorComponent_div_22_div_10_Template_select_change_37_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onFilterChange());\n    });\n    i0.ɵɵelementStart(38, \"option\", 117);\n    i0.ɵɵtext(39, \"25 par page\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"option\", 117);\n    i0.ɵɵtext(41, \"50 par page\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"option\", 117);\n    i0.ɵɵtext(43, \"75 par page\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"option\", 117);\n    i0.ɵɵtext(45, \"Tout afficher\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(46, \"div\", 118)(47, \"div\", 102);\n    i0.ɵɵtemplate(48, CpqConfiguratorComponent_div_22_div_10_div_48_Template, 29, 30, \"div\", 119);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(49, CpqConfiguratorComponent_div_22_div_10_div_49_Template, 7, 0, \"div\", 120);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(50, CpqConfiguratorComponent_div_22_div_10_div_50_Template, 10, 5, \"div\", 121);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 122)(52, \"div\", 123)(53, \"div\", 6)(54, \"div\")(55, \"h5\", 61);\n    i0.ɵɵelement(56, \"i\", 124);\n    i0.ɵɵtext(57, \" Panier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"small\");\n    i0.ɵɵtext(59, \"G\\u00E9rez vos produits s\\u00E9lectionn\\u00E9s, leurs propri\\u00E9t\\u00E9s et quantit\\u00E9s\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 98)(61, \"span\", 99);\n    i0.ɵɵtext(62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(63, CpqConfiguratorComponent_div_22_div_10_button_63_Template, 3, 5, \"button\", 100);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(64, \"div\", 5);\n    i0.ɵɵtemplate(65, CpqConfiguratorComponent_div_22_div_10_div_65_Template, 7, 0, \"div\", 120)(66, CpqConfiguratorComponent_div_22_div_10_div_66_Template, 2, 1, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(67, CpqConfiguratorComponent_div_22_div_10_div_67_Template, 11, 4, \"div\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"div\", 125)(69, \"div\", 6);\n    i0.ɵɵtemplate(70, CpqConfiguratorComponent_div_22_div_10_button_70_Template, 3, 0, \"button\", 126)(71, CpqConfiguratorComponent_div_22_div_10_div_71_Template, 1, 0, \"div\", 41);\n    i0.ɵɵelementStart(72, \"div\", 127)(73, \"div\", 128)(74, \"span\", 129);\n    i0.ɵɵtext(75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"span\", 130);\n    i0.ɵɵtext(77, \"Total: \");\n    i0.ɵɵelementStart(78, \"strong\");\n    i0.ɵɵtext(79);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(80, CpqConfiguratorComponent_div_22_div_10_button_80_Template, 3, 1, \"button\", 131);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.pagination.totalItems, \" produit(s) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.productFilters.searchTerm || ctx_r2.productFilters.familyFilter !== \"all\" || ctx_r2.productFilters.typeFilter !== \"all\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.productFilters.searchTerm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.productFilters.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.productFilters.familyFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getAvailableFamiliesForFilter());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.productFilters.typeFilter);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.pagination.itemsPerPage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 25);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 50);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 75);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredProducts);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filteredProducts.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getTotalPages() > 1);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.confirmedSelectedProducts.length, \" produit(s) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.confirmedSelectedProducts.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.confirmedSelectedProducts.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.confirmedSelectedProducts.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.confirmedSelectedProducts.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep > 2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.confirmedSelectedProducts.length, \" produits\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(ctx_r2.totalPrice));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 2);\n  }\n}\nfunction CpqConfiguratorComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 59)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 89);\n    i0.ɵɵtext(4, \" S\\u00E9lection des produits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 61);\n    i0.ɵɵtext(6, \"Choisissez les produits et leurs quantit\\u00E9s\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 5);\n    i0.ɵɵtemplate(8, CpqConfiguratorComponent_div_22_div_8_Template, 3, 1, \"div\", 90)(9, CpqConfiguratorComponent_div_22_div_9_Template, 3, 1, \"div\", 91)(10, CpqConfiguratorComponent_div_22_div_10_Template, 81, 25, \"div\", 41);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.errorMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.successMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep >= 2);\n  }\n}\nfunction CpqConfiguratorComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 191);\n    i0.ɵɵelement(2, \"i\", 192);\n    i0.ɵɵelementStart(3, \"div\")(4, \"h5\", 7);\n    i0.ɵɵtext(5, \"Propri\\u00E9t\\u00E9s requises manquantes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 61);\n    i0.ɵɵtext(7, \"Certaines propri\\u00E9t\\u00E9s requises n'ont pas \\u00E9t\\u00E9 s\\u00E9lectionn\\u00E9es. Utilisez le bouton \\\"Propri\\u00E9t\\u00E9s\\\" sur chaque produit pour les configurer.\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction CpqConfiguratorComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelement(1, \"i\", 87);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_25_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const familyId_r25 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getFamilyName(familyId_r25), \" \");\n  }\n}\nfunction CpqConfiguratorComponent_div_25_div_21_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 213)(1, \"span\");\n    i0.ɵɵelement(2, \"i\", 230);\n    i0.ɵɵtext(3, \"Options:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 215);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(ctx_r2.getOptionsTotal()));\n  }\n}\nfunction CpqConfiguratorComponent_div_25_div_21_ng_container_40_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 233);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_25_div_21_ng_container_40_i_5_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const item_r27 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.toggleTableRowExpansion(item_r27.selection.product.productid));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r27.expanded ? \"bi-chevron-down\" : \"bi-chevron-right\");\n  }\n}\nfunction CpqConfiguratorComponent_div_25_div_21_ng_container_40_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 234);\n    i0.ɵɵtext(1, \"\\u00A0\\u00A0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_25_div_21_ng_container_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\")(2, \"td\")(3, \"div\", 191)(4, \"span\");\n    i0.ɵɵtemplate(5, CpqConfiguratorComponent_div_25_div_21_ng_container_40_i_5_Template, 1, 1, \"i\", 231)(6, CpqConfiguratorComponent_div_25_div_21_ng_container_40_span_6_Template, 2, 0, \"span\", 232);\n    i0.ɵɵelement(7, \"i\", 155);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"td\", 227);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 10);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 10);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 223);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r27 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"level-\" + item_r27.level);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"padding-left\", item_r27.level * 20, \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r27.hasChildren);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r27.hasChildren);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(12, _c5, item_r27.selection.selectionSource === \"MAIN_LIST\", item_r27.selection.selectionSource === \"TREE_INCLUSION\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r27.selection.product.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r27.selection.quantity);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(item_r27.selection.product.price || 0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatPrice(ctx_r2.getPropertiesPriceForProduct(item_r27.selection.product.productid)), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatPrice((item_r27.selection.product.price || 0) * item_r27.selection.quantity + ctx_r2.getPropertiesPriceForProduct(item_r27.selection.product.productid) * item_r27.selection.quantity + (item_r27.childrenPrice || 0)), \" \");\n  }\n}\nfunction CpqConfiguratorComponent_div_25_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 2)(2, \"div\", 211)(3, \"div\", 212)(4, \"div\", 213)(5, \"span\");\n    i0.ɵɵelement(6, \"i\", 214);\n    i0.ɵɵtext(7, \"Produits de base:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 215);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 213)(11, \"span\");\n    i0.ɵɵelement(12, \"i\", 216);\n    i0.ɵɵtext(13, \"personnalisations:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 215);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, CpqConfiguratorComponent_div_25_div_21_div_16_Template, 6, 1, \"div\", 217);\n    i0.ɵɵelementStart(17, \"div\", 218)(18, \"span\");\n    i0.ɵɵelement(19, \"i\", 199);\n    i0.ɵɵtext(20, \"Quantit\\u00E9 totale:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 215);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 79)(24, \"h6\", 180);\n    i0.ɵɵtext(25, \"D\\u00E9tail des produits et options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"table\", 219)(27, \"thead\", 220)(28, \"tr\")(29, \"th\");\n    i0.ɵɵtext(30, \"Produit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"th\");\n    i0.ɵɵtext(32, \"Quantit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"th\");\n    i0.ɵɵtext(34, \"Prix unitaire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"th\");\n    i0.ɵɵtext(36, \"Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"th\");\n    i0.ɵɵtext(38, \"Total\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"tbody\");\n    i0.ɵɵtemplate(40, CpqConfiguratorComponent_div_25_div_21_ng_container_40_Template, 17, 15, \"ng-container\", 81);\n    i0.ɵɵelementStart(41, \"tr\", 221)(42, \"td\", 222);\n    i0.ɵɵtext(43, \"Total g\\u00E9n\\u00E9ral:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"td\", 223);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(46, \"div\", 103)(47, \"div\", 224)(48, \"div\", 150)(49, \"strong\", 225);\n    i0.ɵɵtext(50, \"Total g\\u00E9n\\u00E9ral:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"strong\", 226);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 227)(54, \"small\", 130);\n    i0.ɵɵtext(55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 228)(57, \"small\", 229);\n    i0.ɵɵtext(58, \"Configuration valid\\u00E9e\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(ctx_r2.getMainProductsTotal()));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(ctx_r2.propertiesPrice));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getOptionsTotal() > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.getTotalConfirmedQuantity(), \" article(s)\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getHierarchicalProductsForTable());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(ctx_r2.totalPrice));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(ctx_r2.totalPrice));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"TVA incluse \\u2022 \", ctx_r2.confirmedSelectedProducts.length, \" produit(s)\");\n  }\n}\nfunction CpqConfiguratorComponent_div_25_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 187);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_25_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.previousStep());\n    });\n    i0.ɵɵelement(1, \"i\", 235);\n    i0.ɵɵtext(2, \" Retour \\u00E0 la s\\u00E9lection \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_25_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\");\n  }\n}\nfunction CpqConfiguratorComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 59)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 193);\n    i0.ɵɵtext(4, \" R\\u00E9capitulatif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 61);\n    i0.ɵɵtext(6, \"V\\u00E9rifiez votre configuration avant de g\\u00E9n\\u00E9rer le devis\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 194)(9, \"h5\");\n    i0.ɵɵtext(10, \"Familles s\\u00E9lectionn\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 195);\n    i0.ɵɵtemplate(12, CpqConfiguratorComponent_div_25_span_12_Template, 2, 1, \"span\", 196);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 194)(14, \"div\", 197)(15, \"div\", 198)(16, \"h5\", 61);\n    i0.ɵɵelement(17, \"i\", 199);\n    i0.ɵɵtext(18, \"R\\u00E9capitulatif des prix\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 200);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_25_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.togglePricePanel());\n    });\n    i0.ɵɵelement(20, \"i\", 155);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, CpqConfiguratorComponent_div_25_div_21_Template, 59, 8, \"div\", 201);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 202);\n    i0.ɵɵtemplate(23, CpqConfiguratorComponent_div_25_button_23_Template, 3, 0, \"button\", 126)(24, CpqConfiguratorComponent_div_25_div_24_Template, 1, 0, \"div\", 41);\n    i0.ɵɵelementStart(25, \"div\", 184)(26, \"button\", 203);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_25_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showResetConfirmation());\n    });\n    i0.ɵɵelement(27, \"i\", 204);\n    i0.ɵɵtext(28, \" R\\u00E9initialiser \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 205);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_25_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.exportPriceSummaryToPdf());\n    });\n    i0.ɵɵelement(30, \"i\", 206);\n    i0.ɵɵtext(31, \" T\\u00E9l\\u00E9charger en PDF \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 207);\n    i0.ɵɵelement(33, \"i\", 208);\n    i0.ɵɵtext(34, \" Sauvegarder le brouillon \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 209);\n    i0.ɵɵelement(36, \"i\", 210);\n    i0.ɵɵtext(37, \" G\\u00E9n\\u00E9rer le devis final \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedFamilyIds);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.isPricePanelExpanded ? \"bi-chevron-up\" : \"bi-chevron-down\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isPricePanelExpanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep > 3);\n  }\n}\nfunction CpqConfiguratorComponent_div_43_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 241)(1, \"div\", 6)(2, \"div\")(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"small\", 242);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"span\", 75);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const selection_r29 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(selection_r29.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(selection_r29.product.productid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(selection_r29.product.price || 0));\n  }\n}\nfunction CpqConfiguratorComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 236)(1, \"h6\", 237);\n    i0.ɵɵelement(2, \"i\", 238);\n    i0.ɵɵtext(3, \"Produits qui seront d\\u00E9s\\u00E9lectionn\\u00E9s :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 239);\n    i0.ɵɵtemplate(5, CpqConfiguratorComponent_div_43_li_5_Template, 9, 3, \"li\", 240);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedChildrenProducts);\n  }\n}\nfunction CpqConfiguratorComponent_div_61_div_17_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 268);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_61_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 260)(1, \"div\", 261);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_61_div_17_Template_div_click_1_listener() {\n      const propertySelection_r31 = i0.ɵɵrestoreView(_r30).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.togglePropertySelection(ctx_r2.selectedProductForProperties.productid, propertySelection_r31.propertyid));\n    });\n    i0.ɵɵelementStart(2, \"div\", 5)(3, \"div\", 69)(4, \"div\", 262);\n    i0.ɵɵelement(5, \"i\", 155);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 71)(7, \"h6\", 263);\n    i0.ɵɵtext(8);\n    i0.ɵɵtemplate(9, CpqConfiguratorComponent_div_61_div_17_span_9_Template, 2, 0, \"span\", 264);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 265);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 6)(13, \"span\", 266);\n    i0.ɵɵtext(14, \"Inclus\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 76)(16, \"input\", 267);\n    i0.ɵɵlistener(\"change\", function CpqConfiguratorComponent_div_61_div_17_Template_input_change_16_listener() {\n      const propertySelection_r31 = i0.ɵɵrestoreView(_r30).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.togglePropertySelection(ctx_r2.selectedProductForProperties.productid, propertySelection_r31.propertyid));\n    });\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const propertySelection_r31 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", propertySelection_r31.selected)(\"required-property\", propertySelection_r31.property == null ? null : propertySelection_r31.property.isrequired);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(10, _c6, (propertySelection_r31.property == null ? null : propertySelection_r31.property.propertytype) === 1, (propertySelection_r31.property == null ? null : propertySelection_r31.property.propertytype) === 2, (propertySelection_r31.property == null ? null : propertySelection_r31.property.propertytype) === 3, (propertySelection_r31.property == null ? null : propertySelection_r31.property.propertytype) === 4));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", propertySelection_r31.property == null ? null : propertySelection_r31.property.name, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", propertySelection_r31.property == null ? null : propertySelection_r31.property.isrequired);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", propertySelection_r31.property == null ? null : propertySelection_r31.property.description, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"name\", \"modal-property-\" + (propertySelection_r31.property == null ? null : propertySelection_r31.property.propertytype) + \"-\" + ctx_r2.selectedProductForProperties.productid)(\"checked\", propertySelection_r31.selected);\n  }\n}\nfunction CpqConfiguratorComponent_div_61_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 27);\n    i0.ɵɵelement(2, \"i\", 28);\n    i0.ɵɵtext(3, \" Aucune propri\\u00E9t\\u00E9 gratuite disponible pour ce produit. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CpqConfiguratorComponent_div_61_div_21_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 268);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CpqConfiguratorComponent_div_61_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 260)(1, \"div\", 261);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_61_div_21_Template_div_click_1_listener() {\n      const propertySelection_r33 = i0.ɵɵrestoreView(_r32).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.togglePropertySelection(ctx_r2.selectedProductForProperties.productid, propertySelection_r33.propertyid));\n    });\n    i0.ɵɵelementStart(2, \"div\", 5)(3, \"div\", 69)(4, \"div\", 262);\n    i0.ɵɵelement(5, \"i\", 155);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 71)(7, \"h6\", 263);\n    i0.ɵɵtext(8);\n    i0.ɵɵtemplate(9, CpqConfiguratorComponent_div_61_div_21_span_9_Template, 2, 0, \"span\", 264);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 265);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 6)(13, \"span\", 269);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 76)(16, \"input\", 270);\n    i0.ɵɵlistener(\"change\", function CpqConfiguratorComponent_div_61_div_21_Template_input_change_16_listener() {\n      const propertySelection_r33 = i0.ɵɵrestoreView(_r32).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.togglePropertySelection(ctx_r2.selectedProductForProperties.productid, propertySelection_r33.propertyid));\n    });\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const propertySelection_r33 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", propertySelection_r33.selected)(\"required-property\", propertySelection_r33.property == null ? null : propertySelection_r33.property.isrequired);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(12, _c6, (propertySelection_r33.property == null ? null : propertySelection_r33.property.propertytype) === 1, (propertySelection_r33.property == null ? null : propertySelection_r33.property.propertytype) === 2, (propertySelection_r33.property == null ? null : propertySelection_r33.property.propertytype) === 3, (propertySelection_r33.property == null ? null : propertySelection_r33.property.propertytype) === 4));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", propertySelection_r33.property == null ? null : propertySelection_r33.property.name, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", propertySelection_r33.property == null ? null : propertySelection_r33.property.isrequired);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", propertySelection_r33.property == null ? null : propertySelection_r33.property.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatPrice((propertySelection_r33.property == null ? null : propertySelection_r33.property.price) || 0), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"type\", (propertySelection_r33.property == null ? null : propertySelection_r33.property.isexclusive) ? \"radio\" : \"checkbox\")(\"name\", (propertySelection_r33.property == null ? null : propertySelection_r33.property.isexclusive) ? \"modal-property-\" + (propertySelection_r33.property == null ? null : propertySelection_r33.property.propertytype) + \"-\" + ctx_r2.selectedProductForProperties.productid : \"\")(\"checked\", propertySelection_r33.selected);\n  }\n}\nfunction CpqConfiguratorComponent_div_61_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 27);\n    i0.ɵɵelement(2, \"i\", 28);\n    i0.ɵɵtext(3, \" Aucune propri\\u00E9t\\u00E9 payante disponible pour ce produit. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CpqConfiguratorComponent_div_61_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 260)(1, \"div\", 271)(2, \"div\", 5)(3, \"div\", 69)(4, \"div\", 272);\n    i0.ɵɵelement(5, \"i\", 273);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 71)(7, \"h6\", 263);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementStart(9, \"span\", 268);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"p\", 265);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 6)(14, \"span\", 274);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 76);\n    i0.ɵɵelement(17, \"input\", 275);\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const propertySelection_r34 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", propertySelection_r34.selected);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", propertySelection_r34.property == null ? null : propertySelection_r34.property.name, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", propertySelection_r34.property == null ? null : propertySelection_r34.property.description, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c7, (propertySelection_r34.property == null ? null : propertySelection_r34.property.price) === 0, (propertySelection_r34.property == null ? null : propertySelection_r34.property.price) > 0));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (propertySelection_r34.property == null ? null : propertySelection_r34.property.price) === 0 ? \"Inclus\" : ctx_r2.formatPrice((propertySelection_r34.property == null ? null : propertySelection_r34.property.price) || 0), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", true);\n  }\n}\nfunction CpqConfiguratorComponent_div_61_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 27);\n    i0.ɵɵelement(2, \"i\", 28);\n    i0.ɵɵtext(3, \" Aucune propri\\u00E9t\\u00E9 requise pour ce produit. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CpqConfiguratorComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"ul\", 243)(2, \"li\", 244)(3, \"button\", 245);\n    i0.ɵɵelement(4, \"i\", 246);\n    i0.ɵɵtext(5, \" Propri\\u00E9t\\u00E9s gratuites \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"li\", 244)(7, \"button\", 247);\n    i0.ɵɵelement(8, \"i\", 248);\n    i0.ɵɵtext(9, \" Propri\\u00E9t\\u00E9s payantes \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"li\", 244)(11, \"button\", 249);\n    i0.ɵɵelement(12, \"i\", 250);\n    i0.ɵɵtext(13, \" Propri\\u00E9t\\u00E9s requises \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 251)(15, \"div\", 252)(16, \"div\", 102);\n    i0.ɵɵtemplate(17, CpqConfiguratorComponent_div_61_div_17_Template, 17, 15, \"div\", 253)(18, CpqConfiguratorComponent_div_61_div_18_Template, 4, 0, \"div\", 254);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 255)(20, \"div\", 102);\n    i0.ɵɵtemplate(21, CpqConfiguratorComponent_div_61_div_21_Template, 17, 17, \"div\", 253)(22, CpqConfiguratorComponent_div_61_div_22_Template, 4, 0, \"div\", 254);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 256)(24, \"div\", 102);\n    i0.ɵɵtemplate(25, CpqConfiguratorComponent_div_61_div_25_Template, 18, 10, \"div\", 253)(26, CpqConfiguratorComponent_div_61_div_26_Template, 4, 0, \"div\", 254);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 257)(28, \"div\", 6)(29, \"div\")(30, \"h6\", 61);\n    i0.ɵɵtext(31, \"Propri\\u00E9t\\u00E9s s\\u00E9lectionn\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\", 258);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 10)(35, \"span\", 259);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getGroupedPropertiesForProduct(ctx_r2.selectedProductForProperties.productid).free);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getGroupedPropertiesForProduct(ctx_r2.selectedProductForProperties.productid).free.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getGroupedPropertiesForProduct(ctx_r2.selectedProductForProperties.productid).paid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getGroupedPropertiesForProduct(ctx_r2.selectedProductForProperties.productid).paid.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getRequiredPropertiesForProduct(ctx_r2.selectedProductForProperties.productid));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getRequiredPropertiesForProduct(ctx_r2.selectedProductForProperties.productid).length === 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getSelectedPropertiesCountForProduct(ctx_r2.selectedProductForProperties.productid), \" propri\\u00E9t\\u00E9(s) s\\u00E9lectionn\\u00E9e(s) \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(ctx_r2.getPropertiesPriceForProduct(ctx_r2.selectedProductForProperties.productid)));\n  }\n}\nfunction CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 281);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const child_r38 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.toggleNodeExpanded(child_r38.productid));\n    });\n    i0.ɵɵelement(1, \"i\", 155);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const child_r38 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c8, ctx_r2.isNodeExpanded(child_r38.productid), !ctx_r2.isNodeExpanded(child_r38.productid)));\n  }\n}\nfunction CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 294);\n  }\n}\nfunction CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 281);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const grandchild_r41 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r2.toggleNodeExpanded(grandchild_r41.productid));\n    });\n    i0.ɵɵelement(1, \"i\", 155);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const grandchild_r41 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c8, ctx_r2.isNodeExpanded(grandchild_r41.productid), !ctx_r2.isNodeExpanded(grandchild_r41.productid)));\n  }\n}\nfunction CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 294);\n  }\n}\nfunction CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_15_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 291)(2, \"div\", 278)(3, \"div\", 279)(4, \"div\", 280);\n    i0.ɵɵelement(5, \"div\", 294);\n    i0.ɵɵelementStart(6, \"div\", 282);\n    i0.ɵɵelement(7, \"i\", 295);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 284)(9, \"input\", 285);\n    i0.ɵɵlistener(\"change\", function CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_15_ng_container_1_Template_input_change_9_listener() {\n      const greatgrandchild_r43 = i0.ɵɵrestoreView(_r42).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(7);\n      return i0.ɵɵresetView(ctx_r2.toggleProductInclusion(greatgrandchild_r43));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 286);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 287);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const greatgrandchild_r43 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(7);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"checked\", ctx_r2.isProductConfirmed(greatgrandchild_r43));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(greatgrandchild_r43.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(greatgrandchild_r43.price || 0));\n  }\n}\nfunction CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 290);\n    i0.ɵɵtemplate(1, CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_15_ng_container_1_Template, 14, 3, \"ng-container\", 81);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const grandchild_r41 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getDirectChildren(grandchild_r41.productid));\n  }\n}\nfunction CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 291)(2, \"div\", 278)(3, \"div\", 279)(4, \"div\", 280);\n    i0.ɵɵtemplate(5, CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_5_Template, 2, 4, \"div\", 292)(6, CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_6_Template, 1, 0, \"div\", 293);\n    i0.ɵɵelementStart(7, \"div\", 282);\n    i0.ɵɵelement(8, \"i\", 155);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 284)(10, \"input\", 285);\n    i0.ɵɵlistener(\"change\", function CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_Template_input_change_10_listener() {\n      const grandchild_r41 = i0.ɵɵrestoreView(_r39).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r2.toggleProductInclusion(grandchild_r41));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 286);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 287);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(15, CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_15_Template, 2, 1, \"div\", 288);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const grandchild_r41 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasDirectChildren(grandchild_r41.productid));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.hasDirectChildren(grandchild_r41.productid));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c9, ctx_r2.hasDirectChildren(grandchild_r41.productid), !ctx_r2.hasDirectChildren(grandchild_r41.productid)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r2.isProductConfirmed(grandchild_r41));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(grandchild_r41.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(grandchild_r41.price || 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasDirectChildren(grandchild_r41.productid) && ctx_r2.isNodeExpanded(grandchild_r41.productid));\n  }\n}\nfunction CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 290);\n    i0.ɵɵtemplate(1, CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_Template, 16, 10, \"ng-container\", 81);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const child_r38 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getDirectChildren(child_r38.productid));\n  }\n}\nfunction CpqConfiguratorComponent_div_97_div_16_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 291)(2, \"div\", 278)(3, \"div\", 279)(4, \"div\", 280);\n    i0.ɵɵtemplate(5, CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_5_Template, 2, 4, \"div\", 292)(6, CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_6_Template, 1, 0, \"div\", 293);\n    i0.ɵɵelementStart(7, \"div\", 282);\n    i0.ɵɵelement(8, \"i\", 155);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 284)(10, \"input\", 285);\n    i0.ɵɵlistener(\"change\", function CpqConfiguratorComponent_div_97_div_16_ng_container_1_Template_input_change_10_listener() {\n      const child_r38 = i0.ɵɵrestoreView(_r36).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.toggleProductInclusion(child_r38));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 286);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 287);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(15, CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_Template, 2, 1, \"div\", 288);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const child_r38 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasDirectChildren(child_r38.productid));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.hasDirectChildren(child_r38.productid));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c9, ctx_r2.hasDirectChildren(child_r38.productid), !ctx_r2.hasDirectChildren(child_r38.productid)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r2.isProductConfirmed(child_r38));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(child_r38.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(child_r38.price || 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasDirectChildren(child_r38.productid) && ctx_r2.isNodeExpanded(child_r38.productid));\n  }\n}\nfunction CpqConfiguratorComponent_div_97_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 290);\n    i0.ɵɵtemplate(1, CpqConfiguratorComponent_div_97_div_16_ng_container_1_Template, 16, 10, \"ng-container\", 81);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getDirectChildren((ctx_r2.currentRootProduct == null ? null : ctx_r2.currentRootProduct.productid) || \"\"));\n  }\n}\nfunction CpqConfiguratorComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 276)(2, \"div\", 277)(3, \"div\", 278)(4, \"div\", 279)(5, \"div\", 280)(6, \"div\", 281);\n    i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_div_97_Template_div_click_6_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleRootExpanded());\n    });\n    i0.ɵɵelement(7, \"i\", 155);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 282);\n    i0.ɵɵelement(9, \"i\", 283);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 284)(11, \"input\", 285);\n    i0.ɵɵlistener(\"change\", function CpqConfiguratorComponent_div_97_Template_input_change_11_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleProductInclusion(ctx_r2.currentRootProduct));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 286);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 287);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(16, CpqConfiguratorComponent_div_97_div_16_Template, 2, 1, \"div\", 288);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 289);\n    i0.ɵɵelement(18, \"i\", 28);\n    i0.ɵɵelementStart(19, \"strong\");\n    i0.ɵɵtext(20, \"Comment utiliser :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Cochez les cases \\u00E0 c\\u00F4t\\u00E9 des produits que vous souhaitez s\\u00E9lectionner. Cliquez sur les fl\\u00E8ches pour \\u00E9tendre ou r\\u00E9duire les cat\\u00E9gories. \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c8, ctx_r2.isRootExpanded, !ctx_r2.isRootExpanded));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"checked\", ctx_r2.isProductConfirmed(ctx_r2.currentRootProduct));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.currentRootProduct == null ? null : ctx_r2.currentRootProduct.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice((ctx_r2.currentRootProduct == null ? null : ctx_r2.currentRootProduct.price) || 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isRootExpanded);\n  }\n}\nexport class CpqConfiguratorComponent {\n  // ===== CONSTRUCTEUR =====\n  constructor() {\n    // ===== PROPRIÉTÉS PRINCIPALES =====\n    this.allProducts = LUXURY_CARS_PRODUCTS;\n    this.availableProducts = [];\n    this.filteredProducts = [];\n    // Indicateur de chargement\n    this.isLoading = false;\n    // Cache pour les descendants des produits\n    this.descendantsCache = new Map();\n    // Map des enfants directs par ID de parent pour améliorer les performances\n    this.directChildrenMap = new Map();\n    // Données fixes\n    this.FIXED_CATEGORY = LUXURY_CARS_CATEGORY;\n    this.CAR_FAMILIES = CAR_FAMILIES;\n    // Données dynamiques depuis D365\n    this.opportunityCategory = 0;\n    this.carFamilies = CAR_FAMILIES;\n    // État de sélection\n    // selectedCategory = this.FIXED_CATEGORY; // Commenté mais conservé pour référence\n    this.selectedCategory = {\n      id: '0',\n      name: 'Chargement...',\n      description: 'Chargement de la catégorie...'\n    };\n    this.selectedFamilyIds = [];\n    this.tempSelectedProducts = [];\n    this.confirmedSelectedProducts = [];\n    // Propriétés pour l'arborescence\n    this.productTreeNodes = [];\n    this.selectedTreeNodes = [];\n    this.showAssociatedProductsDialog = false;\n    this.currentRootProduct = null;\n    // Gestion des conflits et modals\n    this.conflictProduct = null;\n    this.selectedChildrenProducts = [];\n    this.selectedProductForProperties = null;\n    // État des panneaux réductibles\n    this.isPricePanelExpanded = true;\n    this.isProductsPanelExpanded = true;\n    this.expandedProductDetails = new Set();\n    // ===== NOUVELLES PROPRIÉTÉS POUR LES PROPRIÉTÉS =====\n    this.allProperties = PRODUCT_PROPERTIES;\n    this.selectedProperties = [];\n    this.expandedPropertySections = new Map();\n    this.propertiesPrice = 0;\n    // État de l'interface\n    this.currentStep = 1;\n    this.maxSteps = 3;\n    // Stocke l'état d'expansion des lignes du tableau\n    this.expandedTableRows = new Set();\n    // Messages\n    this.errorMessage = '';\n    this.successMessage = '';\n    // Hiérarchie et expansion\n    this.expandedProducts = new Map();\n    this.totalPrice = 0;\n    // Configuration finale\n    this.finalConfiguration = null;\n    // ===== NOUVELLES PROPRIÉTÉS POUR FILTRES ET RECHERCHE =====\n    this.productFilters = {\n      searchTerm: '',\n      familyFilter: 'all',\n      typeFilter: 'all'\n    };\n    this.pagination = {\n      currentPage: 1,\n      itemsPerPage: 25,\n      totalItems: 0,\n      availablePageSizes: [25, 50, 75, 0] // 0 = tous les éléments\n    };\n    // Variables pour l'expansion des noeuds dans l'arborescence\n    this.isRootExpanded = true;\n    this.expandedNodes = new Set();\n    // Cache pour les informations de généalogie\n    this.genealogyInfoCache = new Map();\n    // Cache pour les prix totaux de généalogie\n    this.genealogyPriceCache = new Map();\n    // Cache pour les numéros de page\n    this.pageNumbersCache = {\n      totalPages: 0,\n      currentPage: 0,\n      pages: []\n    };\n    // Cache pour les prix des propriétés par produit\n    this.propertiesPriceCache = new Map();\n    // Cache pour le formatteur de prix\n    this.priceFormatter = new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'EUR'\n    });\n  }\n  /**\n   * Obtient le pourcentage de progression\n   */\n  getProgressPercentage() {\n    return Math.round(this.currentStep / this.maxSteps * 100);\n  }\n  /**\n   * Bascule la sélection d'une famille\n   */\n  toggleFamilySelection(familyId) {\n    const index = this.selectedFamilyIds.indexOf(familyId);\n    if (index > -1) {\n      this.selectedFamilyIds.splice(index, 1);\n    } else {\n      this.selectedFamilyIds.push(familyId);\n    }\n    this.loadAvailableProducts();\n  }\n  /**\n   * Vérifie si une famille est sélectionnée\n   */\n  isFamilySelected(familyId) {\n    return this.selectedFamilyIds.includes(familyId);\n  }\n  /**\n   * Obtient le nom d'une famille par son ID\n   */\n  getFamilyName(familyId) {\n    const family = this.carFamilies.find(f => f.id === familyId);\n    return family ? family.name : familyId;\n  }\n  /**\n   * Charge les produits disponibles selon les familles sélectionnées\n   */\n  loadAvailableProducts() {\n    // Utiliser directement tous les produits de LUXURY_CARS_PRODUCTS\n    console.log('Affichage de tous les produits LUXURY_CARS_PRODUCTS');\n    // Utiliser directement tous les produits sans filtrage\n    this.availableProducts = LUXURY_CARS_PRODUCTS;\n    console.log(`${this.availableProducts.length} produits disponibles`);\n    this.applyFiltersAndPagination();\n    this.isLoading = false;\n  }\n  // ===== LIFECYCLE HOOKS =====\n  ngOnInit() {\n    console.log('🚗 Initialisation du configurateur Voitures de Luxe');\n    this.loadOpportunityCategory();\n  }\n  /**\n   * Ouvre le dialogue des produits associés pour un produit racine\n   */\n  showAssociatedProducts(product) {\n    this.currentRootProduct = product;\n    this.isRootExpanded = true;\n    this.expandedNodes.clear();\n    // Pré-étendre les premiers niveaux pour une meilleure expérience utilisateur\n    const directChildren = this.getDirectChildren(product.productid);\n    directChildren.forEach(child => {\n      if (this.hasDirectChildren(child.productid)) {\n        this.expandedNodes.add(child.productid);\n      }\n    });\n    // Utiliser Bootstrap pour afficher le modal\n    const modalElement = document.getElementById('associatedProductsModal');\n    if (modalElement) {\n      // @ts-ignore: Ignorer l'erreur TS car nous savons que bootstrap est disponible\n      const modal = new bootstrap.Modal(modalElement);\n      modal.show();\n      this.modalRef = modal;\n    }\n  }\n  /**\n   * Bascule l'expansion du noeud racine\n   */\n  toggleRootExpanded() {\n    this.isRootExpanded = !this.isRootExpanded;\n  }\n  /**\n   * Bascule l'expansion d'un noeud\n   */\n  toggleNodeExpanded(nodeId) {\n    if (this.expandedNodes.has(nodeId)) {\n      this.expandedNodes.delete(nodeId);\n    } else {\n      this.expandedNodes.add(nodeId);\n    }\n  }\n  /**\n   * Vérifie si un noeud est étendu\n   */\n  isNodeExpanded(nodeId) {\n    return this.expandedNodes.has(nodeId);\n  }\n  /**\n   * Construit l'arborescence des produits associés\n   */\n  buildProductTree(rootProduct) {\n    // Réinitialiser l'arborescence\n    this.productTreeNodes = [];\n    this.selectedTreeNodes = [];\n    // Créer le noeud racine\n    const rootNode = {\n      key: rootProduct.productid,\n      label: rootProduct.name,\n      data: rootProduct,\n      icon: 'pi pi-folder',\n      expanded: true,\n      children: []\n    };\n    // Ajouter les enfants directs\n    const directChildren = this.getDirectChildren(rootProduct.productid);\n    if (directChildren.length > 0) {\n      rootNode.children = this.buildChildNodes(directChildren, 1);\n    }\n    this.productTreeNodes = [rootNode];\n  }\n  /**\n   * Construit récursivement les noeuds enfants\n   */\n  buildChildNodes(products, level) {\n    return products.map(product => {\n      const node = {\n        key: product.productid,\n        label: product.name,\n        data: product,\n        icon: level === 1 ? 'pi pi-file' : 'pi pi-cog',\n        expanded: level < 2,\n        children: []\n      };\n      // Ajouter les enfants de ce produit\n      const children = this.getDirectChildren(product.productid);\n      if (children.length > 0) {\n        node.children = this.buildChildNodes(children, level + 1);\n      }\n      return node;\n    });\n  }\n  /**\n   * Confirme les produits associés sélectionnés dans l'arborescence\n   */\n  confirmAssociatedProducts() {\n    // Fermer le modal\n    if (this.modalRef) {\n      this.modalRef.hide();\n    }\n    // Mettre à jour le prix total\n    this.updateTotalPrice();\n  }\n  /**\n   * Charge la catégorie de l'opportunité courante\n   */\n  loadOpportunityCategory() {\n    console.log('🚗 Initialisation du configurateur Voitures de Luxe');\n    // Utiliser directement les constantes\n    this.opportunityCategory = parseInt(this.FIXED_CATEGORY.id);\n    this.selectedCategory = this.FIXED_CATEGORY;\n    console.log('Utilisation de la catégorie fixe:', this.selectedCategory);\n    // Pas besoin de charger les familles car on utilise CAR_FAMILIES directement\n    this.buildProductRelationships();\n    this.isLoading = false;\n  }\n  /**\n   * Précalcule les relations parent-enfant entre les produits\n   */\n  buildProductRelationships() {\n    // Parcourir tous les produits une seule fois\n    this.allProducts.forEach(product => {\n      if (product.parentproductid) {\n        // Ajouter ce produit comme enfant de son parent\n        const parentId = product.parentproductid;\n        if (!this.directChildrenMap.has(parentId)) {\n          this.directChildrenMap.set(parentId, []);\n        }\n        this.directChildrenMap.get(parentId).push(product);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.clearMessages();\n    // Libérer les ressources\n    this.descendantsCache.clear();\n    this.directChildrenMap.clear();\n    this.genealogyInfoCache.clear();\n    this.genealogyPriceCache.clear();\n    this.propertiesPriceCache.clear();\n    this.pageNumbersCache = {\n      totalPages: 0,\n      currentPage: 0,\n      pages: []\n    };\n  }\n  // Méthode pour effacer les messages\n  clearMessages() {\n    this.errorMessage = '';\n    this.successMessage = '';\n  }\n  // Méthode pour obtenir les enfants directs\n  getDirectChildren(productId) {\n    return this.directChildrenMap.get(productId) || [];\n  }\n  // Méthode pour vérifier si un produit a des enfants directs\n  hasDirectChildren(productId) {\n    return this.directChildrenMap.has(productId) && this.directChildrenMap.get(productId).length > 0;\n  }\n  // Méthode pour initialiser les propriétés d'un produit\n  initializePropertiesForProduct(product) {\n    const productProperties = this.getAvailablePropertiesForProduct(product.productid);\n    productProperties.forEach(property => {\n      const propertySelection = {\n        productid: product.productid,\n        propertyid: property.propertyid,\n        selected: this.isDefaultProperty(product.productid, property.propertyid) || property.isrequired,\n        selectiondate: new Date(),\n        product: product,\n        property: property\n      };\n      this.selectedProperties.push(propertySelection);\n    });\n  }\n  // Méthode pour mettre à jour le prix total\n  updateTotalPrice() {\n    // Vider les caches de prix car les sélections ont changé\n    this.propertiesPriceCache.clear();\n    this.genealogyPriceCache.clear();\n    // Calculer le prix des produits avec leurs propriétés\n    let productsPrice = 0;\n    this.confirmedSelectedProducts.forEach(selection => {\n      // Prix de base du produit\n      const basePrice = (selection.product.price || 0) * selection.quantity;\n      // Prix des propriétés pour ce produit\n      const propertiesPrice = this.getPropertiesPriceForProduct(selection.product.productid) * selection.quantity;\n      productsPrice += basePrice + propertiesPrice;\n    });\n    this.propertiesPrice = PropertyUtils.calculatePropertiesTotal(this.selectedProperties, this.allProperties);\n    this.totalPrice = productsPrice;\n  }\n  /**\n   * Formate un prix en devise\n   */\n  formatPrice(price) {\n    return this.priceFormatter.format(price);\n  }\n  /**\n   * Obtient les propriétés disponibles pour un produit\n   */\n  getAvailablePropertiesForProduct(productId) {\n    const associations = PRODUCT_PROPERTY_ASSOCIATIONS.filter(a => a.productid === productId);\n    return associations.map(association => this.allProperties.find(p => p.propertyid === association.propertyid)).filter(p => p !== undefined);\n  }\n  /**\n   * Vérifie si une propriété est par défaut pour un produit\n   */\n  isDefaultProperty(productId, propertyId) {\n    const association = PRODUCT_PROPERTY_ASSOCIATIONS.find(a => a.productid === productId && a.propertyid === propertyId);\n    return association?.isdefault || false;\n  }\n  /**\n   * Obtient le prix total des propriétés pour un produit\n   */\n  getPropertiesPriceForProduct(productId) {\n    // Vérifier si le résultat est déjà en cache\n    if (this.propertiesPriceCache.has(productId)) {\n      return this.propertiesPriceCache.get(productId);\n    }\n    const totalPrice = this.selectedProperties.filter(ps => ps.productid === productId && ps.selected).reduce((total, ps) => total + (ps.property?.price || 0), 0);\n    // Stocker le résultat dans le cache\n    this.propertiesPriceCache.set(productId, totalPrice);\n    return totalPrice;\n  }\n  /**\n   * Obtient tous les descendants d'un produit\n   */\n  getAllDescendants(productId) {\n    // Vérifier si le résultat est déjà en cache\n    if (this.descendantsCache.has(productId)) {\n      return this.descendantsCache.get(productId);\n    }\n    const descendants = [];\n    const directChildren = this.allProducts.filter(p => p.parentproductid === productId);\n    directChildren.forEach(child => {\n      descendants.push(child.productid);\n      // Récursion pour obtenir les descendants des enfants\n      const childDescendants = this.getAllDescendants(child.productid);\n      descendants.push(...childDescendants);\n    });\n    // Stocker le résultat dans le cache\n    this.descendantsCache.set(productId, descendants);\n    return descendants;\n  }\n  // Méthodes pour la pagination\n  getTotalPages() {\n    if (this.pagination.itemsPerPage === 0) return 1;\n    return Math.ceil(this.pagination.totalItems / this.pagination.itemsPerPage);\n  }\n  onPageChange(page) {\n    this.pagination.currentPage = page;\n    this.applyFiltersAndPagination();\n  }\n  getPageNumbers() {\n    const totalPages = this.getTotalPages();\n    const current = this.pagination.currentPage;\n    // Vérifier si le résultat est déjà en cache\n    if (this.pageNumbersCache.totalPages === totalPages && this.pageNumbersCache.currentPage === current) {\n      return this.pageNumbersCache.pages;\n    }\n    const pages = [];\n    if (totalPages <= 7) {\n      for (let i = 1; i <= totalPages; i++) {\n        pages.push(i);\n      }\n    } else {\n      if (current <= 4) {\n        for (let i = 1; i <= 5; i++) pages.push(i);\n        pages.push(-1); // Ellipsis\n        pages.push(totalPages);\n      } else if (current >= totalPages - 3) {\n        pages.push(1);\n        pages.push(-1); // Ellipsis\n        for (let i = totalPages - 4; i <= totalPages; i++) pages.push(i);\n      } else {\n        pages.push(1);\n        pages.push(-1); // Ellipsis\n        for (let i = current - 1; i <= current + 1; i++) pages.push(i);\n        pages.push(-1); // Ellipsis\n        pages.push(totalPages);\n      }\n    }\n    // Mettre à jour le cache\n    this.pageNumbersCache = {\n      totalPages,\n      currentPage: current,\n      pages: [...pages]\n    };\n    return pages;\n  }\n  // Méthodes pour les filtres\n  getAvailableFamiliesForFilter() {\n    const familyIds = new Set(this.availableProducts.map(p => p.familyId).filter(id => id));\n    return this.carFamilies.filter(f => familyIds.has(f.id));\n  }\n  /**\n   * Efface tous les filtres\n   */\n  clearAllFilters() {\n    this.productFilters = {\n      searchTerm: '',\n      familyFilter: 'all',\n      typeFilter: 'all'\n    };\n    this.pagination.currentPage = 1;\n    this.applyFiltersAndPagination();\n  }\n  /**\n   * Gère les changements de filtres\n   */\n  onFilterChange() {\n    this.pagination.currentPage = 1;\n    this.applyFiltersAndPagination();\n  }\n  applyFiltersAndPagination() {\n    // Utiliser une seule itération pour appliquer tous les filtres\n    const searchTerm = this.productFilters.searchTerm.trim().toLowerCase();\n    const familyFilter = this.productFilters.familyFilter;\n    const typeFilter = this.productFilters.typeFilter;\n    const hasSearchTerm = searchTerm.length > 0;\n    // Créer un ensemble des IDs de produits à masquer (généalogie)\n    const productsToHide = new Set();\n    if (this.tempSelectedProducts.length > 0 || this.confirmedSelectedProducts.length > 0) {\n      const allSelectedProducts = [...this.tempSelectedProducts, ...this.confirmedSelectedProducts];\n      allSelectedProducts.forEach(selection => {\n        const descendants = this.getAllDescendants(selection.product.productid);\n        descendants.forEach(id => productsToHide.add(id));\n      });\n    }\n    // Appliquer tous les filtres en une seule passe\n    const filtered = this.availableProducts.filter(product => {\n      // Filtre par terme de recherche\n      if (hasSearchTerm && !product.name.toLowerCase().includes(searchTerm) && !product.description?.toLowerCase().includes(searchTerm) && !product.productid.toLowerCase().includes(searchTerm)) {\n        return false;\n      }\n      // Filtre par famille\n      if (familyFilter !== 'all' && product.familyId !== familyFilter) {\n        return false;\n      }\n      // Filtre par type\n      if (typeFilter === 'main' && product.parentproductid) {\n        return false;\n      }\n      if (typeFilter === 'option' && !product.parentproductid) {\n        return false;\n      }\n      // Masquer les produits de la généalogie\n      if (productsToHide.has(product.productid)) {\n        return false;\n      }\n      return true;\n    });\n    // Mettre à jour le total\n    this.pagination.totalItems = filtered.length;\n    // Appliquer la pagination\n    if (this.pagination.itemsPerPage > 0) {\n      const startIndex = (this.pagination.currentPage - 1) * this.pagination.itemsPerPage;\n      const endIndex = startIndex + this.pagination.itemsPerPage;\n      this.filteredProducts = filtered.slice(startIndex, endIndex);\n    } else {\n      this.filteredProducts = filtered;\n    }\n  }\n  // Méthodes pour la généalogie\n  getGenealogyCount(productId) {\n    // Utiliser directement le cache des descendants s'il existe\n    if (this.descendantsCache.has(productId)) {\n      return this.descendantsCache.get(productId).length;\n    }\n    return this.getAllDescendants(productId).length;\n  }\n  getGenealogyInfo(productId) {\n    // Vérifier si le résultat est déjà en cache\n    if (this.genealogyInfoCache.has(productId)) {\n      return this.genealogyInfoCache.get(productId);\n    }\n    const descendantIds = this.getAllDescendants(productId);\n    const products = descendantIds.map(id => this.allProducts.find(p => p.productid === id)).filter(p => p !== undefined);\n    const result = {\n      count: descendantIds.length,\n      products: products\n    };\n    // Stocker le résultat dans le cache\n    this.genealogyInfoCache.set(productId, result);\n    return result;\n  }\n  getGenealogyTotalPrice(productId) {\n    // Vérifier si le résultat est déjà en cache\n    if (this.genealogyPriceCache.has(productId)) {\n      return this.genealogyPriceCache.get(productId);\n    }\n    const genealogyInfo = this.getGenealogyInfo(productId);\n    const totalPrice = genealogyInfo.products.reduce((sum, p) => sum + (p.price || 0), 0);\n    // Stocker le résultat dans le cache\n    this.genealogyPriceCache.set(productId, totalPrice);\n    return totalPrice;\n  }\n  // Méthodes pour la sélection temporaire\n  clearTempSelection() {\n    this.tempSelectedProducts = [];\n    // Réappliquer les filtres pour réafficher les produits masqués\n    this.applyFiltersAndPagination();\n  }\n  removeTempProduct(product) {\n    const index = this.tempSelectedProducts.findIndex(selection => selection.product.productid === product.productid);\n    if (index >= 0) {\n      this.tempSelectedProducts.splice(index, 1);\n      // Réappliquer les filtres pour réafficher les produits de la généalogie\n      this.applyFiltersAndPagination();\n    }\n  }\n  getTempSelectionTotal() {\n    return this.tempSelectedProducts.reduce((total, selection) => {\n      return total + (selection.product.price || 0) * selection.quantity;\n    }, 0);\n  }\n  confirmTempSelection() {\n    this.confirmedSelectedProducts = this.tempSelectedProducts.map(temp => ({\n      ...temp,\n      selectionSource: temp.selectionSource || 'MAIN_LIST'\n    }));\n    this.tempSelectedProducts = [];\n    // Initialiser les propriétés pour les nouveaux produits confirmés\n    this.initializePropertiesSelection();\n    this.updateTotalPrice();\n    this.applyFiltersAndPagination();\n  }\n  // Méthodes pour les produits confirmés\n  getMainProducts() {\n    return this.confirmedSelectedProducts.filter(s => s.selectionSource === 'MAIN_LIST');\n  }\n  getMainProductsTotal() {\n    return this.getMainProducts().reduce((total, selection) => {\n      return total + (selection.product.price || 0) * selection.quantity;\n    }, 0);\n  }\n  getOptionsTotal() {\n    return this.confirmedSelectedProducts.filter(s => s.selectionSource === 'TREE_INCLUSION').reduce((total, selection) => {\n      return total + (selection.product.price || 0) * selection.quantity;\n    }, 0);\n  }\n  getTotalConfirmedQuantity() {\n    return this.confirmedSelectedProducts.reduce((total, selection) => {\n      return total + selection.quantity;\n    }, 0);\n  }\n  clearAllConfirmedProducts() {\n    this.confirmedSelectedProducts = [];\n    this.updateTotalPrice();\n    // Réappliquer les filtres pour réafficher tous les produits\n    this.applyFiltersAndPagination();\n  }\n  removeConfirmedProduct(product) {\n    // Supprimer le produit principal\n    const mainIndex = this.confirmedSelectedProducts.findIndex(selection => selection.product.productid === product.productid && selection.selectionSource === 'MAIN_LIST');\n    if (mainIndex >= 0) {\n      this.confirmedSelectedProducts.splice(mainIndex, 1);\n    }\n    // Supprimer aussi tous ses enfants inclus\n    this.confirmedSelectedProducts = this.confirmedSelectedProducts.filter(selection => {\n      if (selection.selectionSource === 'TREE_INCLUSION') {\n        // Vérifier si c'est un enfant du produit supprimé\n        return !this.isChildOf(selection.product, product.productid);\n      }\n      return true;\n    });\n    this.updateTotalPrice();\n  }\n  // Méthode pour vérifier si un produit est enfant d'un autre\n  isChildOf(product, parentId) {\n    if (product.parentproductid === parentId) {\n      return true;\n    }\n    // Vérification récursive\n    const parent = this.allProducts.find(p => p.productid === product.parentproductid);\n    if (parent) {\n      return this.isChildOf(parent, parentId);\n    }\n    return false;\n  }\n  /**\n   * Vérifie si un produit est temporairement sélectionné\n   */\n  isTempProductSelected(product) {\n    return this.tempSelectedProducts.some(selection => selection.product.productid === product.productid);\n  }\n  /**\n   * Sélectionne directement un produit (sans passer par la sélection temporaire)\n   */\n  toggleTempProductSelection(product) {\n    // Vérifier si le produit est déjà sélectionné\n    const existingIndex = this.confirmedSelectedProducts.findIndex(selection => selection.product.productid === product.productid);\n    if (existingIndex >= 0) {\n      // Désélectionner le produit\n      this.confirmedSelectedProducts.splice(existingIndex, 1);\n    } else {\n      // Ajouter directement le produit au panier\n      this.confirmedSelectedProducts.push({\n        product: product,\n        quantity: 1,\n        selectionSource: 'MAIN_LIST'\n      });\n      // Initialiser les propriétés pour ce produit\n      this.initializePropertiesForProduct(product);\n    }\n    // Mettre à jour le prix total\n    this.updateTotalPrice();\n    // Mettre à jour l'affichage\n    this.applyFiltersAndPagination();\n  }\n  // Méthodes pour la gestion des quantités\n  updateProductQuantity(productId, event) {\n    let newQuantity = parseInt(event.target.value);\n    if (isNaN(newQuantity) || newQuantity < 1) newQuantity = 1;\n    if (newQuantity > 99) newQuantity = 99;\n    const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);\n    if (selection) {\n      selection.quantity = newQuantity;\n      this.updateTotalPrice();\n    }\n  }\n  increaseQuantity(productId) {\n    const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);\n    if (selection && selection.quantity < 99) {\n      selection.quantity++;\n      this.updateTotalPrice();\n    }\n  }\n  decreaseQuantity(productId) {\n    const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);\n    if (selection && selection.quantity > 1) {\n      selection.quantity--;\n      this.updateTotalPrice();\n    }\n  }\n  // Méthodes pour l'expansion des produits\n  toggleProductExpansion(productId) {\n    const isExpanded = this.expandedProducts.get(productId) || false;\n    this.expandedProducts.set(productId, !isExpanded);\n  }\n  isProductExpanded(productId) {\n    return this.expandedProducts.get(productId) || false;\n  }\n  toggleAllProductsExpansion() {\n    if (this.expandedProducts.size > 0) {\n      // Si des produits sont étendus, tout réduire\n      this.expandedProducts.clear();\n    } else {\n      // Si aucun produit n'est étendu, tout étendre\n      this.confirmedSelectedProducts.forEach(selection => {\n        if (this.hasDirectChildren(selection.product.productid)) {\n          this.expandedProducts.set(selection.product.productid, true);\n        }\n      });\n    }\n  }\n  // Méthodes pour les propriétés\n  hasProductProperties(productId) {\n    return this.getSelectedPropertiesCountForProduct(productId) > 0;\n  }\n  getSelectedPropertiesCountForProduct(productId) {\n    return this.selectedProperties.filter(ps => ps.productid === productId && ps.selected).length;\n  }\n  getProductFreeProperties(productId) {\n    return this.selectedProperties.filter(ps => ps.productid === productId && ps.selected && ps.property?.price === 0 && !ps.property?.isrequired).map(ps => ({\n      name: ps.property?.name,\n      isRequired: ps.property?.isrequired\n    }));\n  }\n  getProductPaidProperties(productId) {\n    return this.selectedProperties.filter(ps => ps.productid === productId && ps.selected && (ps.property?.price || 0) > 0 && !ps.property?.isrequired).map(ps => ({\n      name: ps.property?.name,\n      price: ps.property?.price,\n      isRequired: ps.property?.isrequired\n    }));\n  }\n  getProductRequiredProperties(productId) {\n    return this.selectedProperties.filter(ps => ps.productid === productId && ps.property?.isrequired).map(ps => ({\n      name: ps.property?.name,\n      price: ps.property?.price,\n      isRequired: true\n    }));\n  }\n  // Méthodes pour l'arborescence des produits\n  getProductTreeData(parentProductId) {\n    const children = this.getDirectChildren(parentProductId);\n    return children.map(child => {\n      return {\n        product: child,\n        isSelected: this.isProductConfirmed(child),\n        isExpanded: this.isProductExpanded(child.productid),\n        quantity: this.getProductQuantity(child.productid),\n        directChildren: this.getProductTreeData(child.productid),\n        hasChildren: this.hasDirectChildren(child.productid),\n        hasSelectedProperties: this.hasProductProperties(child.productid),\n        selectedPropertiesCount: this.getSelectedPropertiesCountForProduct(child.productid),\n        freeProperties: this.getProductFreeProperties(child.productid),\n        paidProperties: this.getProductPaidProperties(child.productid),\n        requiredProperties: this.getProductRequiredProperties(child.productid)\n      };\n    });\n  }\n  // Méthodes pour la gestion des propriétés\n  initializePropertiesSelection() {\n    this.selectedProperties = [];\n    // Pour chaque produit confirmé, initialiser ses propriétés\n    this.confirmedSelectedProducts.forEach(selection => {\n      this.initializePropertiesForProduct(selection.product);\n    });\n    this.updateTotalPrice();\n  }\n  togglePropertySelection(productId, propertyId) {\n    const propertySelection = this.selectedProperties.find(ps => ps.productid === productId && ps.propertyid === propertyId);\n    if (propertySelection) {\n      const property = propertySelection.property;\n      // Ne pas permettre de désélectionner les propriétés requises\n      if (property.isrequired && propertySelection.selected) {\n        return;\n      }\n      // Si la propriété est exclusive, désélectionner les autres du même type\n      if (property.isexclusive) {\n        this.selectedProperties.filter(ps => ps.productid === productId && ps.property.propertytype === property.propertytype && ps.propertyid !== propertyId).forEach(ps => ps.selected = false);\n      }\n      propertySelection.selected = !propertySelection.selected;\n      this.updateTotalPrice();\n    }\n  }\n  getGroupedPropertiesForProduct(productId) {\n    const properties = this.getPropertiesForProduct(productId);\n    return {\n      free: properties.filter(ps => PropertyUtils.isFreeProperty(ps.property)),\n      paid: properties.filter(ps => PropertyUtils.isPaidProperty(ps.property))\n    };\n  }\n  getPropertiesForProduct(productId) {\n    return this.selectedProperties.filter(ps => ps.productid === productId);\n  }\n  getRequiredPropertiesForProduct(productId) {\n    return this.selectedProperties.filter(ps => ps.productid === productId && ps.property?.isrequired);\n  }\n  // Méthodes pour la gestion des inclusions\n  toggleProductInclusion(product) {\n    const existingIndex = this.confirmedSelectedProducts.findIndex(selection => selection.product.productid === product.productid);\n    if (existingIndex >= 0) {\n      // Retirer le produit de la sélection\n      this.confirmedSelectedProducts.splice(existingIndex, 1);\n    } else {\n      // Ajouter le produit au panier\n      const newSelection = {\n        product: product,\n        quantity: 1,\n        selectionSource: 'MAIN_LIST' // Tous les produits sont maintenant au même niveau\n      };\n      this.confirmedSelectedProducts.push(newSelection);\n      // Initialiser les propriétés pour ce produit\n      this.initializePropertiesForProduct(product);\n    }\n    this.updateTotalPrice();\n  }\n  isProductConfirmed(product) {\n    return this.confirmedSelectedProducts.some(selection => selection.product.productid === product.productid);\n  }\n  getProductQuantity(productId) {\n    const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);\n    return selection ? selection.quantity : 1;\n  }\n  // Méthodes pour le tableau hiérarchique\n  getHierarchicalProductsForTable() {\n    const result = [];\n    const processedIds = new Set();\n    // Séparer les produits sélectionnés directement et les produits inclus\n    const mainProducts = this.confirmedSelectedProducts.filter(s => s.selectionSource === 'MAIN_LIST');\n    // Ajouter tous les produits principaux (niveau 0) et leurs enfants visibles\n    mainProducts.sort((a, b) => a.product.name.localeCompare(b.product.name)).forEach(selection => {\n      if (!processedIds.has(selection.product.productid)) {\n        result.push({\n          selection,\n          level: 0,\n          hasChildren: this.hasDirectChildren(selection.product.productid),\n          expanded: this.isTableRowExpanded(selection.product.productid),\n          childrenPrice: 0\n        });\n        processedIds.add(selection.product.productid);\n      }\n    });\n    return result;\n  }\n  toggleTableRowExpansion(productId) {\n    if (this.expandedTableRows.has(productId)) {\n      this.expandedTableRows.delete(productId);\n    } else {\n      this.expandedTableRows.add(productId);\n    }\n  }\n  isTableRowExpanded(productId) {\n    return this.expandedTableRows.has(productId);\n  }\n  // Méthodes pour les modals\n  openPropertiesModal(product) {\n    this.selectedProductForProperties = product;\n  }\n  showResetConfirmation() {\n    // Afficher le modal de confirmation\n  }\n  exportPriceSummaryToPdf() {\n    // Exporter le récapitulatif en PDF\n  }\n  confirmParentChildConflict() {\n    // Confirmer le conflit parent-enfant\n  }\n  // Méthodes pour la navigation entre étapes\n  nextStep() {\n    switch (this.currentStep) {\n      case 1:\n        if (this.selectedFamilyIds.length === 0) {\n          this.setErrorMessage('Veuillez sélectionner au moins une famille de véhicules');\n          return;\n        }\n        this.currentStep = 2;\n        this.loadAvailableProducts();\n        break;\n      case 2:\n        if (this.confirmedSelectedProducts.length === 0) {\n          this.setErrorMessage('Veuillez sélectionner au moins un produit');\n          return;\n        }\n        if (!this.validatePropertiesSelection()) {\n          const missingProperties = this.getMissingRequiredProperties();\n          const missingNames = missingProperties.map(prop => prop.name).join(', ');\n          this.setErrorMessage(`Veuillez sélectionner les propriétés requises suivantes : ${missingNames}`);\n          return;\n        }\n        // Passer directement à l'étape 3 (récapitulatif)\n        this.currentStep = 3;\n        this.generateFinalConfiguration();\n        break;\n    }\n    this.clearMessages();\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      // Si on est à l'étape 3 (récapitulatif), revenir directement à l'étape 2 (sélection)\n      if (this.currentStep === 3) {\n        this.currentStep = 2;\n        this.backToSelection();\n        return;\n      }\n      this.currentStep--;\n      this.updateStepInterface();\n    }\n  }\n  /**\n   * Met à jour l'interface après changement d'étape\n   */\n  updateStepInterface() {\n    this.clearMessages();\n    if (this.currentStep === 2) {\n      this.loadAvailableProducts();\n      this.pagination.currentPage = 1;\n    }\n  }\n  /**\n   * Retourne à la sélection des produits\n   */\n  backToSelection() {\n    this.tempSelectedProducts = [...this.confirmedSelectedProducts];\n    this.confirmedSelectedProducts = [];\n    this.currentStep = 2;\n  }\n  /**\n   * Génère la configuration finale\n   */\n  generateFinalConfiguration() {\n    // Initialiser tous les panneaux de détails de produits comme étendus\n    this.confirmedSelectedProducts.forEach(selection => {\n      this.expandedProductDetails.add(selection.product.productid);\n    });\n    // Initialiser l'expansion des produits principaux dans le tableau\n    this.confirmedSelectedProducts.filter(s => s.selectionSource === 'MAIN_LIST').forEach(s => this.expandedTableRows.add(s.product.productid));\n    const mainProductsPrice = this.confirmedSelectedProducts.filter(s => s.selectionSource === 'MAIN_LIST').reduce((sum, s) => sum + (s.product.price || 0) * s.quantity, 0);\n    this.finalConfiguration = {\n      selectedCategory: this.selectedCategory,\n      selectedFamilies: this.selectedFamilyIds.map(id => this.carFamilies.find(f => f.id === id)).filter(f => f !== undefined),\n      selectedProducts: this.confirmedSelectedProducts.map(s => ({\n        product: s.product,\n        quantity: s.quantity,\n        subtotal: (s.product.price || 0) * s.quantity,\n        selectionSource: s.selectionSource,\n        level: this.getProductLevel(s.product.productid),\n        hierarchyPath: this.getProductHierarchyPath(s.product.productid)\n      })),\n      selectedProperties: this.selectedProperties.filter(ps => ps.selected),\n      priceBreakdown: {\n        mainProducts: mainProductsPrice,\n        properties: this.propertiesPrice,\n        total: this.totalPrice\n      },\n      totalPrice: this.totalPrice,\n      timestamp: new Date().toISOString()\n    };\n  }\n  /**\n   * Obtient le niveau hiérarchique d'un produit\n   */\n  getProductLevel(productId) {\n    const product = this.allProducts.find(p => p.productid === productId);\n    if (!product) return 0;\n    let level = 0;\n    let currentProduct = product;\n    while (currentProduct.parentproductid) {\n      level++;\n      const parent = this.allProducts.find(p => p.productid === currentProduct.parentproductid);\n      if (!parent) break;\n      currentProduct = parent;\n    }\n    return level;\n  }\n  /**\n   * Obtient le chemin hiérarchique d'un produit\n   */\n  getProductHierarchyPath(productId) {\n    const path = [];\n    const product = this.allProducts.find(p => p.productid === productId);\n    if (!product) return path;\n    let currentProduct = product;\n    path.unshift(currentProduct);\n    while (currentProduct.parentproductid) {\n      const parent = this.allProducts.find(p => p.productid === currentProduct.parentproductid);\n      if (!parent) break;\n      path.unshift(parent);\n      currentProduct = parent;\n    }\n    return path;\n  }\n  /**\n   * Vérifie si la configuration est valide pour générer un devis\n   */\n  validatePropertiesSelection() {\n    return this.areAllRequiredPropertiesSelected();\n  }\n  /**\n   * Définit un message d'erreur\n   */\n  setErrorMessage(message) {\n    this.errorMessage = message;\n    this.successMessage = '';\n    setTimeout(() => this.clearMessages(), 5000);\n  }\n  /**\n   * Définit un message de succès\n   */\n  setSuccessMessage(message) {\n    this.successMessage = message;\n    this.errorMessage = '';\n    setTimeout(() => this.clearMessages(), 3000);\n  }\n  // Méthodes pour la validation\n  areAllRequiredPropertiesSelected() {\n    // Vérifier uniquement les produits confirmés qui ont des propriétés requises\n    for (const selection of this.confirmedSelectedProducts) {\n      const productId = selection.product.productid;\n      const productRequiredProperties = this.getAvailablePropertiesForProduct(productId).filter(prop => prop.isrequired);\n      // Si ce produit n'a pas de propriétés requises, passer au suivant\n      if (productRequiredProperties.length === 0) {\n        continue;\n      }\n      // Vérifier si toutes les propriétés requises de ce produit sont sélectionnées\n      for (const requiredProp of productRequiredProperties) {\n        const isSelected = this.selectedProperties.some(ps => ps.productid === productId && ps.propertyid === requiredProp.propertyid && ps.selected);\n        if (!isSelected) {\n          return false;\n        }\n      }\n    }\n    return true;\n  }\n  getMissingRequiredProperties() {\n    const missingProperties = [];\n    // Vérifier uniquement les produits confirmés\n    for (const selection of this.confirmedSelectedProducts) {\n      const productId = selection.product.productid;\n      const productRequiredProperties = this.getAvailablePropertiesForProduct(productId).filter(prop => prop.isrequired);\n      // Pour chaque propriété requise de ce produit\n      for (const requiredProp of productRequiredProperties) {\n        const isSelected = this.selectedProperties.some(ps => ps.productid === productId && ps.propertyid === requiredProp.propertyid && ps.selected);\n        if (!isSelected) {\n          missingProperties.push({\n            ...requiredProp,\n            productName: selection.product.name\n          });\n        }\n      }\n    }\n    return missingProperties;\n  }\n  // Méthodes pour les panneaux\n  togglePricePanel() {\n    this.isPricePanelExpanded = !this.isPricePanelExpanded;\n  }\n  // Méthode pour la configuration\n  resetConfiguration() {\n    // Réinitialiser toutes les sélections\n    this.selectedFamilyIds = [];\n    this.tempSelectedProducts = [];\n    this.confirmedSelectedProducts = [];\n    this.selectedProperties = [];\n    // Réinitialiser les filtres et la pagination\n    this.productFilters = {\n      searchTerm: '',\n      familyFilter: 'all',\n      typeFilter: 'all'\n    };\n    this.pagination = {\n      currentPage: 1,\n      itemsPerPage: 25,\n      totalItems: 0,\n      availablePageSizes: [25, 50, 75, 0]\n    };\n    // Réinitialiser les états\n    this.expandedProducts = new Map();\n    this.totalPrice = 0;\n    this.propertiesPrice = 0;\n    // Revenir à l'étape 1\n    this.currentStep = 1;\n  }\n  /**\n   * Vérifie si un produit a des enfants sélectionnés\n   */\n  hasChildrenSelected(product) {\n    if (!product || !product.productid) return false;\n    const allSelected = [...this.tempSelectedProducts, ...this.confirmedSelectedProducts];\n    const descendants = this.getAllDescendants(product.productid);\n    return descendants.some(descendantId => allSelected.some(sel => sel.product.productid === descendantId));\n  }\n  /**\n   * Affiche le modal de conflit parent-enfant\n   */\n  showParentChildConflictModal(product) {\n    console.log('Conflit détecté pour le produit:', product.name);\n    this.conflictProduct = product;\n    // Obtenir la liste des produits enfants sélectionnés pour l'affichage dans le modal\n    const descendants = this.getAllDescendants(product.productid);\n    const allSelected = [...this.tempSelectedProducts, ...this.confirmedSelectedProducts];\n    this.selectedChildrenProducts = allSelected.filter(sel => descendants.includes(sel.product.productid));\n  }\n  static {\n    this.ɵfac = function CpqConfiguratorComponent_Factory(t) {\n      return new (t || CpqConfiguratorComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CpqConfiguratorComponent,\n      selectors: [[\"app-cpq-configurator\"]],\n      decls: 104,\n      vars: 18,\n      consts: [[1, \"container-fluid\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-12\"], [1, \"cpq-header\"], [1, \"card-body\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-1\"], [1, \"bi\", \"bi-gear-fill\", \"me-2\"], [1, \"text-muted\", \"mb-0\"], [1, \"text-end\"], [1, \"badge\", \"bg-primary\", \"fs-6\"], [1, \"text-muted\", \"small\", \"mt-1\"], [1, \"progress\", \"mt-3\"], [\"role\", \"progressbar\", 1, \"progress-bar\"], [\"class\", \"row\", 4, \"ngIf\"], [\"class\", \"step-separator text-center my-4\", 4, \"ngIf\"], [\"class\", \"card mb-4\", 4, \"ngIf\"], [\"class\", \"alert alert-warning mb-4\", 4, \"ngIf\"], [\"id\", \"parentChildConflictModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"conflictModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-dialog-centered\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-warning\", \"text-dark\"], [\"id\", \"conflictModalLabel\", 1, \"modal-title\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-2\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Fermer\", 1, \"btn-close\"], [1, \"modal-body\"], [1, \"alert\", \"alert-info\"], [1, \"bi\", \"bi-info-circle\", \"me-2\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [1, \"mt-3\", \"mb-0\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-outline-secondary\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-warning\", 3, \"click\"], [1, \"bi\", \"bi-check-circle\", \"me-2\"], [\"id\", \"propertiesModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"propertiesModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-dialog-centered\", \"modal-lg\"], [1, \"modal-header\", \"bg-primary\", \"text-white\"], [\"id\", \"propertiesModalLabel\", 1, \"modal-title\"], [1, \"bi\", \"bi-pencil-square\", \"me-2\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Fermer\", 1, \"btn-close\", \"btn-close-white\"], [4, \"ngIf\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\"], [\"id\", \"resetConfirmationModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"resetConfirmationModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-header\", \"bg-danger\", \"text-white\"], [\"id\", \"resetConfirmationModalLabel\", 1, \"modal-title\"], [1, \"alert\", \"alert-warning\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"bi\", \"bi-arrow-counterclockwise\", \"me-2\"], [\"id\", \"associatedProductsModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"associatedProductsModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-dialog-centered\", \"modal-xl\"], [1, \"modal-header\", \"bg-info\", \"text-white\"], [\"id\", \"associatedProductsModalLabel\", 1, \"modal-title\"], [1, \"bi\", \"bi-diagram-3\", \"me-2\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"loading-overlay\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"cpq-card\"], [1, \"card-header\"], [1, \"bi\", \"bi-collection-fill\", \"me-2\"], [1, \"mb-0\"], [1, \"text-white\"], [1, \"row\", \"g-4\", \"px-4\", \"py-4\"], [\"class\", \"col-lg-4 col-md-6\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [\"class\", \"card-footer\", 4, \"ngIf\"], [1, \"col-lg-4\", \"col-md-6\"], [1, \"card\", \"family-card\", 3, \"click\"], [1, \"d-flex\", \"align-items-start\"], [1, \"family-icon\", \"me-3\"], [1, \"flex-grow-1\"], [1, \"card-title\"], [1, \"card-text\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mt-3\"], [1, \"badge\", \"bg-secondary\"], [1, \"form-check\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"checked\", \"disabled\"], [1, \"form-check-label\"], [1, \"mt-4\"], [1, \"alert\", \"alert-success\"], [4, \"ngFor\", \"ngForOf\"], [1, \"card-footer\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-primary\", \"btn-lg\", 3, \"click\"], [1, \"bi\", \"bi-arrow-right\", \"ms-2\"], [1, \"step-separator\", \"text-center\", \"my-4\"], [1, \"bi\", \"bi-arrow-down-circle-fill\", \"fs-1\", \"text-primary\"], [1, \"card\", \"mb-4\"], [1, \"bi\", \"bi-box-seam\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [\"class\", \"alert alert-success\", 4, \"ngIf\"], [1, \"alert\", \"alert-danger\"], [1, \"bi\", \"bi-exclamation-triangle\"], [1, \"bi\", \"bi-check-circle\"], [1, \"card\", \"product-selection-card\", \"mb-4\"], [1, \"card-header\", \"bg-info\", \"text-white\"], [1, \"bi\", \"bi-search\", \"me-2\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\"], [1, \"badge\", \"bg-light\", \"text-dark\"], [\"class\", \"btn btn-sm btn-outline-light\", 3, \"click\", 4, \"ngIf\"], [1, \"search-filters-section\", \"mb-4\"], [1, \"row\", \"g-3\"], [1, \"col-md-4\"], [1, \"search-input-group\", \"position-relative\"], [1, \"position-absolute\", \"top-50\", \"start-0\", \"translate-middle-y\", \"ms-3\"], [1, \"bi\", \"bi-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher un produit...\", 1, \"form-control\", \"search-input\", \"ps-5\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"class\", \"btn btn-outline-secondary clear-search-btn position-absolute top-50 end-0 translate-middle-y me-2\", 3, \"click\", 4, \"ngIf\"], [1, \"col-md-3\"], [1, \"form-select\", \"filter-select\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"all\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"main\"], [\"value\", \"option\"], [1, \"col-md-2\"], [1, \"form-select\", \"pagination-select\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [3, \"value\"], [1, \"products-grid\"], [\"class\", \"col-lg-6 col-xl-4 mb-3\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"pagination-section mt-4\", 4, \"ngIf\"], [1, \"card\", \"confirmed-products-card\", \"mb-4\"], [1, \"card-header\", \"bg-success\", \"text-white\"], [1, \"bi\", \"bi-cart-check\", \"me-2\"], [1, \"step-navigation-actions\"], [\"class\", \"btn btn-outline-secondary\", 3, \"click\", 4, \"ngIf\"], [1, \"step-info\", \"d-flex\", \"align-items-center\", \"gap-3\"], [1, \"selection-summary\"], [1, \"badge\", \"bg-info\", \"me-2\"], [1, \"text-muted\"], [\"class\", \"btn btn-primary btn-lg\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-outline-light\", 3, \"click\"], [1, \"bi\", \"bi-x-circle\"], [1, \"btn\", \"btn-outline-secondary\", \"clear-search-btn\", \"position-absolute\", \"top-50\", \"end-0\", \"translate-middle-y\", \"me-2\", 3, \"click\"], [1, \"bi\", \"bi-x\"], [1, \"col-lg-6\", \"col-xl-4\", \"mb-3\"], [1, \"card\", \"product-card\", \"h-100\"], [1, \"card-body\", \"d-flex\", \"flex-column\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\", \"mb-2\"], [1, \"product-icon\"], [1, \"bi\", \"bi-box-seam\", \"fs-2\"], [1, \"product-status-indicators\"], [\"class\", \"badge bg-success me-1\", 4, \"ngIf\"], [\"class\", \"badge bg-warning\", 4, \"ngIf\"], [1, \"card-title\", \"fw-bold\"], [1, \"card-text\", \"text-muted\", \"small\", \"mb-1\"], [1, \"bi\", \"bi-hash\"], [1, \"card-text\", \"flex-grow-1\", \"product-description\"], [1, \"mt-auto\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [1, \"badge\", \"product-type-badge\", 3, \"ngClass\"], [1, \"text-success\", \"fs-6\", \"product-price\"], [1, \"d-grid\", \"gap-2\"], [1, \"btn\", \"btn-sm\", \"product-action-btn\", 3, \"click\", \"ngClass\", \"disabled\"], [1, \"bi\", 3, \"ngClass\"], [\"class\", \"btn btn-sm btn-outline-info mt-2 w-100\", 3, \"click\", 4, \"ngIf\"], [1, \"badge\", \"bg-success\", \"me-1\"], [1, \"badge\", \"bg-warning\"], [1, \"bi\", \"bi-clock\"], [1, \"btn\", \"btn-sm\", \"btn-outline-info\", \"mt-2\", \"w-100\", 3, \"click\"], [1, \"bi\", \"bi-diagram-3\", \"me-1\"], [1, \"empty-state\"], [1, \"empty-state-icon\"], [1, \"bi\", \"bi-inbox\"], [1, \"empty-state-title\"], [1, \"empty-state-text\"], [1, \"pagination-section\", \"mt-4\"], [\"aria-label\", \"Navigation des produits\"], [1, \"pagination\", \"justify-content-center\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\"], [1, \"bi\", \"bi-chevron-left\"], [\"class\", \"page-item\", 3, \"active\", \"disabled\", 4, \"ngFor\", \"ngForOf\"], [1, \"bi\", \"bi-chevron-right\"], [\"class\", \"page-link\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"page-link\", 4, \"ngIf\"], [1, \"page-link\"], [1, \"bi\", \"bi-cart-x\"], [\"class\", \"mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"mb-3\"], [3, \"productToggle\", \"productInclusionToggle\", \"quantityUpdate\", \"quantityIncrease\", \"quantityDecrease\", \"expansionToggle\", \"editProperties\", \"product\", \"level\", \"isSelected\", \"isMainProduct\", \"isIncluded\", \"isExpanded\", \"quantity\", \"directChildren\", \"hasChildren\", \"hasSelectedProperties\", \"selectedPropertiesCount\", \"freeProperties\", \"paidProperties\", \"requiredProperties\", \"isLocked\"], [1, \"confirmed-products-summary\"], [1, \"text-success\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"bi\", \"bi-trash\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-left\", \"me-2\"], [1, \"btn\", \"btn-primary\", \"btn-lg\", 3, \"click\", \"disabled\"], [1, \"alert\", \"alert-warning\", \"mb-4\"], [1, \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"fs-4\", \"me-2\"], [1, \"bi\", \"bi-clipboard-check\"], [1, \"mb-4\"], [1, \"d-flex\", \"flex-wrap\", \"gap-2\"], [\"class\", \"badge bg-primary fs-6\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"price-summary-card\", 1, \"card\", \"bg-light\"], [1, \"card-header\", \"bg-dark\", \"text-white\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"bi\", \"bi-calculator\", \"me-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\"], [\"class\", \"card-body\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"btn\", \"btn-outline-danger\", 3, \"click\"], [1, \"bi\", \"bi-arrow-counterclockwise\"], [1, \"btn\", \"btn-outline-info\", \"download-btn\", 3, \"click\"], [1, \"bi\", \"bi-file-pdf\"], [1, \"btn\", \"btn-outline-primary\"], [1, \"bi\", \"bi-save\"], [1, \"btn\", \"btn-success\", \"btn-lg\"], [1, \"bi\", \"bi-file-earmark-text\"], [1, \"col-md-8\"], [1, \"price-breakdown-details\"], [1, \"d-flex\", \"justify-content-between\", \"py-2\", \"border-bottom\"], [1, \"bi\", \"bi-folder\", \"me-2\"], [1, \"fw-bold\"], [1, \"bi\", \"bi-gear\", \"me-2\"], [\"class\", \"d-flex justify-content-between py-2 border-bottom\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"py-2\"], [1, \"table\", \"table-sm\", \"table-bordered\", \"tree-table\"], [1, \"table-light\"], [1, \"table-success\"], [\"colspan\", \"4\", 1, \"text-end\", \"fw-bold\"], [1, \"text-end\", \"fw-bold\"], [1, \"final-total-box\"], [1, \"fs-4\"], [1, \"text-success\", \"fs-3\"], [1, \"text-center\"], [1, \"text-center\", \"mt-2\"], [1, \"badge\", \"bg-info\"], [1, \"bi\", \"bi-wrench\", \"me-2\"], [\"class\", \"bi expand-icon me-1\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"class\", \"expand-placeholder me-1\", 4, \"ngIf\"], [1, \"bi\", \"expand-icon\", \"me-1\", 3, \"click\", \"ngClass\"], [1, \"expand-placeholder\", \"me-1\"], [1, \"bi\", \"bi-arrow-left\"], [1, \"mt-3\"], [1, \"text-danger\"], [1, \"bi\", \"bi-exclamation-circle\", \"me-2\"], [1, \"list-group\", \"mt-2\"], [\"class\", \"list-group-item list-group-item-danger\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\", \"list-group-item-danger\"], [1, \"d-block\", \"text-muted\"], [\"id\", \"propertiesTabs\", \"role\", \"tablist\", 1, \"nav\", \"nav-tabs\", \"mb-3\"], [\"role\", \"presentation\", 1, \"nav-item\"], [\"id\", \"free-tab\", \"data-bs-toggle\", \"tab\", \"data-bs-target\", \"#free-properties\", \"type\", \"button\", \"role\", \"tab\", \"aria-controls\", \"free-properties\", \"aria-selected\", \"true\", 1, \"nav-link\", \"active\"], [1, \"bi\", \"bi-gift\", \"me-1\", \"text-success\"], [\"id\", \"paid-tab\", \"data-bs-toggle\", \"tab\", \"data-bs-target\", \"#paid-properties\", \"type\", \"button\", \"role\", \"tab\", \"aria-controls\", \"paid-properties\", \"aria-selected\", \"false\", 1, \"nav-link\"], [1, \"bi\", \"bi-currency-euro\", \"me-1\", \"text-warning\"], [\"id\", \"required-tab\", \"data-bs-toggle\", \"tab\", \"data-bs-target\", \"#required-properties\", \"type\", \"button\", \"role\", \"tab\", \"aria-controls\", \"required-properties\", \"aria-selected\", \"false\", 1, \"nav-link\"], [1, \"bi\", \"bi-asterisk\", \"me-1\", \"text-danger\"], [\"id\", \"propertiesTabsContent\", 1, \"tab-content\"], [\"id\", \"free-properties\", \"role\", \"tabpanel\", \"aria-labelledby\", \"free-tab\", 1, \"tab-pane\", \"fade\", \"show\", \"active\"], [\"class\", \"col-md-6\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-12\", 4, \"ngIf\"], [\"id\", \"paid-properties\", \"role\", \"tabpanel\", \"aria-labelledby\", \"paid-tab\", 1, \"tab-pane\", \"fade\"], [\"id\", \"required-properties\", \"role\", \"tabpanel\", \"aria-labelledby\", \"required-tab\", 1, \"tab-pane\", \"fade\"], [1, \"mt-4\", \"pt-3\", \"border-top\"], [1, \"text-muted\", \"small\", \"mb-0\"], [1, \"badge\", \"bg-success\", \"fs-6\"], [1, \"col-md-6\"], [1, \"card\", \"property-option-card\", \"h-100\", 3, \"click\"], [1, \"property-option-icon\", \"me-3\"], [1, \"property-option-title\"], [\"class\", \"required-indicator\", 4, \"ngIf\"], [1, \"property-option-description\", \"small\", \"text-muted\", \"mb-2\"], [1, \"badge\", \"bg-success\"], [\"type\", \"radio\", 1, \"form-check-input\", 3, \"change\", \"name\", \"checked\"], [1, \"required-indicator\"], [1, \"badge\", \"bg-warning\", \"text-dark\"], [1, \"form-check-input\", 3, \"change\", \"type\", \"name\", \"checked\"], [1, \"card\", \"property-option-card\", \"h-100\", \"required-property\"], [1, \"property-option-icon\", \"me-3\", \"bg-danger\", \"text-white\"], [1, \"bi\", \"bi-asterisk\"], [1, \"badge\", 3, \"ngClass\"], [\"type\", \"checkbox\", \"disabled\", \"\", 1, \"form-check-input\", 3, \"checked\"], [1, \"tui-tree-container\"], [1, \"tui-tree-node\", \"root-node\"], [1, \"tui-tree-item\"], [1, \"tui-tree-item-content\"], [1, \"tui-tree-item-content-wrapper\"], [1, \"tui-tree-expander\", 3, \"click\"], [1, \"tui-tree-icon\"], [1, \"bi\", \"bi-folder-fill\", \"text-warning\"], [1, \"tui-tree-checkbox\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"checked\"], [1, \"tui-tree-label\"], [1, \"tui-tree-price\"], [\"class\", \"tui-tree-children\", 4, \"ngIf\"], [1, \"alert\", \"alert-info\", \"mt-3\"], [1, \"tui-tree-children\"], [1, \"tui-tree-node\"], [\"class\", \"tui-tree-expander\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"tui-tree-expander\", 4, \"ngIf\"], [1, \"tui-tree-expander\"], [1, \"bi\", \"bi-file-earmark\", \"text-primary\"]],\n      template: function CpqConfiguratorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, CpqConfiguratorComponent_div_1_Template, 4, 0, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\")(8, \"h2\", 7);\n          i0.ɵɵelement(9, \"i\", 8);\n          i0.ɵɵtext(10, \" Configurateur CPQ \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 9);\n          i0.ɵɵtext(12, \"Configurez votre devis personnalis\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"span\", 11);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 12);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 13);\n          i0.ɵɵelement(19, \"div\", 14);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(20, CpqConfiguratorComponent_div_20_Template, 16, 4, \"div\", 15)(21, CpqConfiguratorComponent_div_21_Template, 2, 0, \"div\", 16)(22, CpqConfiguratorComponent_div_22_Template, 11, 3, \"div\", 17)(23, CpqConfiguratorComponent_div_23_Template, 8, 0, \"div\", 18)(24, CpqConfiguratorComponent_div_24_Template, 2, 0, \"div\", 16)(25, CpqConfiguratorComponent_div_25_Template, 38, 5, \"div\", 17);\n          i0.ɵɵelementStart(26, \"div\", 19)(27, \"div\", 20)(28, \"div\", 21)(29, \"div\", 22)(30, \"h5\", 23);\n          i0.ɵɵelement(31, \"i\", 24);\n          i0.ɵɵtext(32, \" Conflit de hi\\u00E9rarchie d\\u00E9tect\\u00E9 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"button\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 26)(35, \"p\");\n          i0.ɵɵtext(36, \" Vous tentez de s\\u00E9lectionner \");\n          i0.ɵɵelementStart(37, \"strong\");\n          i0.ɵɵtext(38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(39, \" qui fait partie d'une hi\\u00E9rarchie de produits. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 27);\n          i0.ɵɵelement(41, \"i\", 28);\n          i0.ɵɵtext(42, \" En confirmant, tous les produits enfants de cette hi\\u00E9rarchie seront d\\u00E9s\\u00E9lectionn\\u00E9s et masqu\\u00E9s. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(43, CpqConfiguratorComponent_div_43_Template, 6, 1, \"div\", 29);\n          i0.ɵɵelementStart(44, \"p\", 30);\n          i0.ɵɵtext(45, \"Voulez-vous continuer ?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 31)(47, \"button\", 32);\n          i0.ɵɵtext(48, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_Template_button_click_49_listener() {\n            return ctx.confirmParentChildConflict();\n          });\n          i0.ɵɵelement(50, \"i\", 34);\n          i0.ɵɵtext(51, \" Confirmer et continuer \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(52, \"div\", 35)(53, \"div\", 36)(54, \"div\", 21)(55, \"div\", 37)(56, \"h5\", 38);\n          i0.ɵɵelement(57, \"i\", 39);\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(59, \"button\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 26);\n          i0.ɵɵtemplate(61, CpqConfiguratorComponent_div_61_Template, 37, 8, \"div\", 41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"div\", 31)(63, \"button\", 32);\n          i0.ɵɵtext(64, \"Fermer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"button\", 42);\n          i0.ɵɵelement(66, \"i\", 34);\n          i0.ɵɵtext(67, \" Confirmer les propri\\u00E9t\\u00E9s \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(68, \"div\", 43)(69, \"div\", 20)(70, \"div\", 21)(71, \"div\", 44)(72, \"h5\", 45);\n          i0.ɵɵelement(73, \"i\", 24);\n          i0.ɵɵtext(74, \" Confirmation de r\\u00E9initialisation \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(75, \"button\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"div\", 26)(77, \"p\");\n          i0.ɵɵtext(78, \" \\u00CAtes-vous s\\u00FBr de vouloir r\\u00E9initialiser compl\\u00E8tement la configuration ? \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 46);\n          i0.ɵɵelement(80, \"i\", 28);\n          i0.ɵɵtext(81, \" Cette action supprimera toutes vos s\\u00E9lections et vous ram\\u00E8nera \\u00E0 l'\\u00E9tape 1. Cette action est irr\\u00E9versible. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 31)(83, \"button\", 32);\n          i0.ɵɵtext(84, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"button\", 47);\n          i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_Template_button_click_85_listener() {\n            return ctx.resetConfiguration();\n          });\n          i0.ɵɵelement(86, \"i\", 48);\n          i0.ɵɵtext(87, \" R\\u00E9initialiser \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(88, \"div\", 49)(89, \"div\", 50)(90, \"div\", 21)(91, \"div\", 51)(92, \"h5\", 52);\n          i0.ɵɵelement(93, \"i\", 53);\n          i0.ɵɵtext(94);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(95, \"button\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"div\", 26);\n          i0.ɵɵtemplate(97, CpqConfiguratorComponent_div_97_Template, 22, 8, \"div\", 41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"div\", 31)(99, \"button\", 32);\n          i0.ɵɵtext(100, \"Fermer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"button\", 54);\n          i0.ɵɵlistener(\"click\", function CpqConfiguratorComponent_Template_button_click_101_listener() {\n            return ctx.confirmAssociatedProducts();\n          });\n          i0.ɵɵelement(102, \"i\", 34);\n          i0.ɵɵtext(103, \" Confirmer la s\\u00E9lection \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(14);\n          i0.ɵɵtextInterpolate1(\"\\u00C9tape \", ctx.currentStep, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate2(\"\\u00C9tape \", ctx.currentStep, \"/\", ctx.maxSteps, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"width\", ctx.getProgressPercentage(), \"%\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep >= 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep >= 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2 && !ctx.areAllRequiredPropertiesSelected() && ctx.getMissingRequiredProperties().length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep >= 3);\n          i0.ɵɵadvance(13);\n          i0.ɵɵtextInterpolate(ctx.conflictProduct == null ? null : ctx.conflictProduct.name);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedChildrenProducts.length > 0);\n          i0.ɵɵadvance(15);\n          i0.ɵɵtextInterpolate1(\" Propri\\u00E9t\\u00E9s du produit: \", ctx.selectedProductForProperties == null ? null : ctx.selectedProductForProperties.name, \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedProductForProperties);\n          i0.ɵɵadvance(33);\n          i0.ɵɵtextInterpolate1(\" Produits associ\\u00E9s \\u00E0 \", ctx.currentRootProduct == null ? null : ctx.currentRootProduct.name, \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentRootProduct);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgModel, i3.ProductTreeComponent],\n      styles: [\"\\n\\n[_ngcontent-%COMP%]:root {\\n  --cpq-primary: #2c3e50;\\n  --cpq-primary-light: #34495e;\\n  --cpq-secondary: #7f8c8d;\\n  --cpq-success: #27ae60;\\n  --cpq-info: #3498db;\\n  --cpq-warning: #f39c12;\\n  --cpq-danger: #e74c3c;\\n  --cpq-dark: #2c3e50;\\n  --cpq-light: #ecf0f1;\\n  --cpq-card-bg: #ffffff;\\n  --cpq-border-radius: 12px;\\n  --cpq-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  --cpq-shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);\\n  --cpq-transition: all 0.3s ease;\\n  \\n  --level-0-bg: #f8f9fa;\\n  --level-0-border: #2c3e50;\\n  --level-1-bg: #f1f3f4;\\n  --level-1-border: #5d6d7e;\\n  --level-2-bg: #eaecee;\\n  --level-2-border: #85929e;\\n  --level-3-bg: #e5e7e9;\\n  --level-3-border: #a6acaf;\\n  --level-4-bg: #e0e2e4;\\n  --level-4-border: #bdc3c7;\\n  --level-5-bg: #dcdde0;\\n  --level-5-border: #d5dbdb;\\n}\\n\\n\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  min-height: 100vh;\\n  padding: 2rem 1rem;\\n}\\n\\n\\n\\n.card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: white !important;\\n  font-weight: 600;\\n  margin: 0 !important;\\n  text-shadow: 0 2px 4px rgba(0,0,0,0.3);\\n  font-size: 1.5rem;\\n}\\n\\n.card-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n  margin-bottom: 0;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.search-filters-section[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 10px;\\n  padding: 1.5rem;\\n  border: 1px solid #e9ecef;\\n}\\n\\n\\n\\n.pagination-section[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 10px;\\n  padding: 1.5rem;\\n}\\n\\n.pagination[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  margin: 0 2px;\\n  border: 2px solid #e9ecef;\\n  color: var(--cpq-primary);\\n  transition: var(--cpq-transition);\\n}\\n\\n\\n\\n.section-separator[_ngcontent-%COMP%] {\\n  border: none;\\n  height: 3px;\\n  background: linear-gradient(90deg, var(--cpq-primary), var(--cpq-info), var(--cpq-success));\\n  border-radius: 2px;\\n  margin: 2rem 0;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  font-weight: 500;\\n  padding: 0.75rem 1.5rem;\\n  transition: var(--cpq-transition);\\n  border: none;\\n}\\n\\n.btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--cpq-primary), var(--cpq-primary-light));\\n  color: white;\\n}\\n\\n.btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--cpq-success), #2ecc71);\\n  color: white;\\n}\\n\\n\\n\\n.form-control[_ngcontent-%COMP%], .form-select[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  border: 2px solid #e9ecef;\\n  padding: 0.75rem 1rem;\\n  transition: var(--cpq-transition);\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus, .form-select[_ngcontent-%COMP%]:focus {\\n  border-color: var(--cpq-primary);\\n  box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.15);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .container-fluid[_ngcontent-%COMP%] {\\n    padding: 1rem 0.5rem;\\n  }\\n  \\n  .search-filters-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_slideInUp {\\n  from { \\n    opacity: 0; \\n    transform: translateY(30px); \\n  }\\n  to { \\n    opacity: 1; \\n    transform: translateY(0); \\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from { opacity: 0; }\\n  to { opacity: 1; }\\n}\\n\\n.cpq-card[_ngcontent-%COMP%], .product-selection-card[_ngcontent-%COMP%], .product-confirmed-card[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInUp 0.5s ease-out;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\\n\\n.card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--cpq-primary), var(--cpq-primary-light)) !important;\\n  color: white !important;\\n  border-radius: var(--cpq-border-radius) var(--cpq-border-radius) 0 0;\\n  border: none !important;\\n  padding: 2rem;\\n}\\n\\n\\n\\n.step-separator[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 60px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.step-separator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: color 0.3s ease;\\n}\\n\\n.step-separator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:hover {\\n  color: #63b3ed !important; \\n\\n}\\n\\n.step-separator[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 50%;\\n  left: 10%;\\n  right: 10%;\\n  border-top: 2px dashed #dee2e6;\\n  z-index: -1;\\n}\\n\\n\\n\\n.step-separator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.2);\\n  transition: transform 0.3s ease;\\n}\\n\\n\\n\\n.alert[_ngcontent-%COMP%] {\\n  border-radius: var(--cpq-border-radius);\\n  border: none;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  border-radius: var(--cpq-border-radius);\\n  border: none;\\n  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  border-radius: var(--cpq-border-radius) var(--cpq-border-radius) 0 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\\n\\n.cpq-header[_ngcontent-%COMP%] {\\n  background: var(--cpq-card-bg);\\n  border-radius: var(--cpq-border-radius);\\n  box-shadow: var(--cpq-shadow);\\n  margin-bottom: 2rem;\\n  border: none;\\n  transition: var(--cpq-transition);\\n}\\n\\n.cpq-card[_ngcontent-%COMP%] {\\n  background: var(--cpq-card-bg);\\n  border-radius: var(--cpq-border-radius);\\n  box-shadow: var(--cpq-shadow);\\n  margin-bottom: 2rem;\\n  border: none;\\n  transition: var(--cpq-transition);\\n}\\n\\n.family-card[_ngcontent-%COMP%] {\\n  transition: var(--cpq-transition);\\n  cursor: pointer;\\n  border: 2px solid #e9ecef;\\n}\\n\\n.family-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 15px rgba(0,0,0,0.1);\\n  border-color: var(--cpq-primary);\\n}\\n\\n.family-card.selected[_ngcontent-%COMP%] {\\n  border-color: var(--cpq-primary);\\n  background-color: rgba(44, 62, 80, 0.05);\\n}\\n\\n.family-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: var(--cpq-primary);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9jcHEtY29uZmlndXJhdG9yL3N0ZXAxLXN0eWxlcy5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsa0RBQWtEO0FBQ2xEO0VBQ0UsOEJBQThCO0VBQzlCLHVDQUF1QztFQUN2Qyw2QkFBNkI7RUFDN0IsbUJBQW1CO0VBQ25CLFlBQVk7RUFDWixpQ0FBaUM7QUFDbkM7O0FBRUE7RUFDRSw4QkFBOEI7RUFDOUIsdUNBQXVDO0VBQ3ZDLDZCQUE2QjtFQUM3QixtQkFBbUI7RUFDbkIsWUFBWTtFQUNaLGlDQUFpQztBQUNuQzs7QUFFQTtFQUNFLGlDQUFpQztFQUNqQyxlQUFlO0VBQ2YseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UsMkJBQTJCO0VBQzNCLHNDQUFzQztFQUN0QyxnQ0FBZ0M7QUFDbEM7O0FBRUE7RUFDRSxnQ0FBZ0M7RUFDaEMsd0NBQXdDO0FBQzFDOztBQUVBO0VBQ0UsZUFBZTtFQUNmLHlCQUF5QjtBQUMzQiIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBwb3VyIGwnw4PCqXRhcGUgMTogU8ODwqlsZWN0aW9uIGRlcyBmYW1pbGxlcyAqL1xyXG4uY3BxLWhlYWRlciB7XHJcbiAgYmFja2dyb3VuZDogdmFyKC0tY3BxLWNhcmQtYmcpO1xyXG4gIGJvcmRlci1yYWRpdXM6IHZhcigtLWNwcS1ib3JkZXItcmFkaXVzKTtcclxuICBib3gtc2hhZG93OiB2YXIoLS1jcHEtc2hhZG93KTtcclxuICBtYXJnaW4tYm90dG9tOiAycmVtO1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICB0cmFuc2l0aW9uOiB2YXIoLS1jcHEtdHJhbnNpdGlvbik7XHJcbn1cclxuXHJcbi5jcHEtY2FyZCB7XHJcbiAgYmFja2dyb3VuZDogdmFyKC0tY3BxLWNhcmQtYmcpO1xyXG4gIGJvcmRlci1yYWRpdXM6IHZhcigtLWNwcS1ib3JkZXItcmFkaXVzKTtcclxuICBib3gtc2hhZG93OiB2YXIoLS1jcHEtc2hhZG93KTtcclxuICBtYXJnaW4tYm90dG9tOiAycmVtO1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICB0cmFuc2l0aW9uOiB2YXIoLS1jcHEtdHJhbnNpdGlvbik7XHJcbn1cclxuXHJcbi5mYW1pbHktY2FyZCB7XHJcbiAgdHJhbnNpdGlvbjogdmFyKC0tY3BxLXRyYW5zaXRpb24pO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxuICBib3JkZXI6IDJweCBzb2xpZCAjZTllY2VmO1xyXG59XHJcblxyXG4uZmFtaWx5LWNhcmQ6aG92ZXIge1xyXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtM3B4KTtcclxuICBib3gtc2hhZG93OiAwIDhweCAxNXB4IHJnYmEoMCwwLDAsMC4xKTtcclxuICBib3JkZXItY29sb3I6IHZhcigtLWNwcS1wcmltYXJ5KTtcclxufVxyXG5cclxuLmZhbWlseS1jYXJkLnNlbGVjdGVkIHtcclxuICBib3JkZXItY29sb3I6IHZhcigtLWNwcS1wcmltYXJ5KTtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDQ0LCA2MiwgODAsIDAuMDUpO1xyXG59XHJcblxyXG4uZmFtaWx5LWljb24ge1xyXG4gIGZvbnQtc2l6ZTogMnJlbTtcclxuICBjb2xvcjogdmFyKC0tY3BxLXByaW1hcnkpO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\\n\\n.product-selection-card[_ngcontent-%COMP%], .product-confirmed-card[_ngcontent-%COMP%] {\\n  background: var(--cpq-card-bg);\\n  border-radius: var(--cpq-border-radius);\\n  box-shadow: var(--cpq-shadow);\\n  margin-bottom: 2rem;\\n  border: none;\\n  transition: var(--cpq-transition);\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  transition: var(--cpq-transition);\\n  cursor: pointer;\\n  border: 2px solid #e9ecef;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 25px rgba(0,123,255,0.15);\\n  border-color: var(--cpq-info);\\n}\\n\\n.product-card.temp-selected[_ngcontent-%COMP%] {\\n  border-color: var(--cpq-warning);\\n  background: linear-gradient(135deg, #fff8e1 0%, #fff3c4 100%);\\n  box-shadow: 0 0 0 0.2rem rgba(243, 156, 18, 0.25);\\n}\\n\\n.product-card.confirmed[_ngcontent-%COMP%] {\\n  border-color: var(--cpq-success);\\n  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);\\n  box-shadow: 0 0 0 0.2rem rgba(39, 174, 96, 0.25);\\n}\\n\\n\\n\\n.temp-selection-card[_ngcontent-%COMP%] {\\n  border-left: 4px solid var(--cpq-warning);\\n}\\n\\n.temp-product-item[_ngcontent-%COMP%] {\\n  transition: var(--cpq-transition);\\n}\\n\\n.temp-product-item[_ngcontent-%COMP%]:hover {\\n  background-color: #fff8e1;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\\n\\n.download-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3498db, #2980b9) !important;\\n  color: white !important;\\n  border: none !important;\\n  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);\\n  transition: all 0.3s ease;\\n}\\n\\n.download-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);\\n}\\n\\n.download-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.final-total-box[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: var(--cpq-border-radius);\\n  padding: 1.5rem;\\n  box-shadow: var(--cpq-shadow);\\n  border: 1px solid #dee2e6;\\n}\\n\\n.tree-table[_ngcontent-%COMP%] {\\n  border-collapse: separate;\\n  border-spacing: 0;\\n}\\n\\n.tree-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-color: #dee2e6;\\n}\\n\\n.tree-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  vertical-align: middle;\\n}\\n\\n\\n\\n.level-0[_ngcontent-%COMP%] {\\n  background-color: var(--level-0-bg);\\n}\\n\\n.level-1[_ngcontent-%COMP%] {\\n  background-color: var(--level-1-bg);\\n}\\n\\n.level-2[_ngcontent-%COMP%] {\\n  background-color: var(--level-2-bg);\\n}\\n\\n.level-3[_ngcontent-%COMP%] {\\n  background-color: var(--level-3-bg);\\n}\\n\\n.level-4[_ngcontent-%COMP%] {\\n  background-color: var(--level-4-bg);\\n}\\n\\n.level-5[_ngcontent-%COMP%] {\\n  background-color: var(--level-5-bg);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */.genealogy-accordion[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n\\n.genealogy-header[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n}\\n\\n.genealogy-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n.genealogy-content[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.genealogy-products[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n  scrollbar-color: #6c757d #e9ecef;\\n}\\n\\n.genealogy-products[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n\\n.genealogy-products[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #e9ecef;\\n  border-radius: 4px;\\n}\\n\\n.genealogy-products[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: #6c757d;\\n  border-radius: 4px;\\n  border: 2px solid #e9ecef;\\n}\\n\\n.genealogy-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none !important;\\n}\\n\\n.step-separator[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 60px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.step-separator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: color 0.3s ease, transform 0.3s ease;\\n}\\n\\n.step-separator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:hover {\\n  color: #63b3ed;\\n  transform: scale(1.2);\\n}\\n\\n.step-separator[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 50%;\\n  left: 10%;\\n  right: 10%;\\n  border-top: 2px dashed #dee2e6;\\n  z-index: -1;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(255, 255, 255, 0.7);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 9999;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9jcHEtY29uZmlndXJhdG9yL2xvYWRpbmctc3R5bGVzLmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGVBQWU7RUFDZixNQUFNO0VBQ04sT0FBTztFQUNQLFdBQVc7RUFDWCxZQUFZO0VBQ1osMENBQTBDO0VBQzFDLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsbUJBQW1CO0VBQ25CLGFBQWE7QUFDZiIsInNvdXJjZXNDb250ZW50IjpbIi5sb2FkaW5nLW92ZXJsYXkge1xyXG4gIHBvc2l0aW9uOiBmaXhlZDtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogMDtcclxuICB3aWR0aDogMTAwJTtcclxuICBoZWlnaHQ6IDEwMCU7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICB6LWluZGV4OiA5OTk5O1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\\n\\n.tree-table[_ngcontent-%COMP%] {\\n  border-collapse: separate;\\n  border-spacing: 0;\\n}\\n\\n.tree-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-color: #dee2e6;\\n  position: sticky;\\n  top: 0;\\n  z-index: 1;\\n}\\n\\n.tree-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  vertical-align: middle;\\n}\\n\\n\\n\\n.level-0[_ngcontent-%COMP%] {\\n  background-color: var(--level-0-bg);\\n}\\n\\n.level-1[_ngcontent-%COMP%] {\\n  background-color: var(--level-1-bg);\\n}\\n\\n.level-2[_ngcontent-%COMP%] {\\n  background-color: var(--level-2-bg);\\n}\\n\\n.level-3[_ngcontent-%COMP%] {\\n  background-color: var(--level-3-bg);\\n}\\n\\n.level-4[_ngcontent-%COMP%] {\\n  background-color: var(--level-4-bg);\\n}\\n\\n.level-5[_ngcontent-%COMP%] {\\n  background-color: var(--level-5-bg);\\n}\\n\\n\\n\\n.expand-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  width: 16px;\\n  height: 16px;\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 3px;\\n  transition: all 0.2s;\\n}\\n\\n.expand-icon[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.1);\\n  color: var(--cpq-primary);\\n}\\n\\n.expand-placeholder[_ngcontent-%COMP%] {\\n  width: 16px;\\n  display: inline-block;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInRows {\\n  from { opacity: 0; transform: translateY(-5px); }\\n  to { opacity: 1; transform: translateY(0); }\\n}\\n\\n.tree-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:not(.level-0) {\\n  animation: _ngcontent-%COMP%_fadeInRows 0.3s ease-out;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9jcHEtY29uZmlndXJhdG9yL2NwcS1jb25maWd1cmF0b3IuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxnQ0FBZ0MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBJbXBvcnQgZGVzIHN0eWxlcyBwYXIgw4PCqXRhcGUgKi9cclxuQGltcG9ydCAnLi9jcHEtY29uZmlndXJhdG9yLWNvcmUuY3NzJztcclxuQGltcG9ydCAnLi9jb21tb24tc3R5bGVzLmNzcyc7XHJcbkBpbXBvcnQgJy4vc3RlcDEtc3R5bGVzLmNzcyc7XHJcbkBpbXBvcnQgJy4vc3RlcDItc3R5bGVzLmNzcyc7XHJcbkBpbXBvcnQgJy4vc3RlcDMtc3R5bGVzLmNzcyc7XHJcbkBpbXBvcnQgJy4vYWNjb3JkaW9uLXN0eWxlcy5jc3MnO1xyXG5AaW1wb3J0ICcuL2xvYWRpbmctc3R5bGVzLmNzcyc7XHJcbkBpbXBvcnQgJy4vdHJlZXRhYmxlLXN0eWxlcy5jc3MnOyJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["LUXURY_CARS_CATEGORY", "CAR_FAMILIES", "LUXURY_CARS_PRODUCTS", "PropertyUtils", "PRODUCT_PROPERTIES", "PRODUCT_PROPERTY_ASSOCIATIONS", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "CpqConfiguratorComponent_div_20_div_13_Template_div_click_1_listener", "family_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "currentStep", "toggleFamilySelection", "id", "ɵɵelement", "CpqConfiguratorComponent_div_20_div_13_Template_input_change_15_listener", "ɵɵadvance", "ɵɵclassProp", "isFamilySelected", "ɵɵclassMap", "icon", "ɵɵtextInterpolate", "name", "description", "ɵɵproperty", "ɵɵtemplate", "CpqConfiguratorComponent_div_20_div_14_span_6_span_3_Template", "getFamilyName", "familyId_r4", "last_r5", "CpqConfiguratorComponent_div_20_div_14_span_6_Template", "selectedFamilyIds", "length", "CpqConfiguratorComponent_div_20_div_15_Template_button_click_2_listener", "_r6", "nextStep", "ɵɵtextInterpolate1", "CpqConfiguratorComponent_div_20_div_13_Template", "CpqConfiguratorComponent_div_20_div_14_Template", "CpqConfiguratorComponent_div_20_div_15_Template", "selectedCate<PERSON><PERSON>", "carFamilies", "errorMessage", "successMessage", "CpqConfiguratorComponent_div_22_div_10_button_13_Template_button_click_0_listener", "_r8", "clearAllFilters", "CpqConfiguratorComponent_div_22_div_10_button_22_Template_button_click_0_listener", "_r9", "productFilters", "searchTerm", "onFilterChange", "family_r10", "CpqConfiguratorComponent_div_22_div_10_div_48_button_28_Template_button_click_0_listener", "_r13", "product_r12", "showAssociatedProducts", "CpqConfiguratorComponent_div_22_div_10_div_48_span_7_Template", "CpqConfiguratorComponent_div_22_div_10_div_48_span_8_Template", "CpqConfiguratorComponent_div_22_div_10_div_48_Template_button_click_23_listener", "_r11", "toggleTempProductSelection", "CpqConfiguratorComponent_div_22_div_10_div_48_span_25_Template", "CpqConfiguratorComponent_div_22_div_10_div_48_span_26_Template", "CpqConfiguratorComponent_div_22_div_10_div_48_span_27_Template", "CpqConfiguratorComponent_div_22_div_10_div_48_button_28_Template", "isTempProductSelected", "isProductConfirmed", "productid", "ɵɵpureFunction2", "_c0", "parentproductid", "formatPrice", "price", "getPropertiesPriceForProduct", "ɵɵpureFunction3", "_c1", "_c2", "hasDirectChildren", "CpqConfiguratorComponent_div_22_div_10_div_50_li_6_button_1_Template_button_click_0_listener", "_r15", "page_r16", "onPageChange", "CpqConfiguratorComponent_div_22_div_10_div_50_li_6_button_1_Template", "CpqConfiguratorComponent_div_22_div_10_div_50_li_6_span_2_Template", "pagination", "currentPage", "CpqConfiguratorComponent_div_22_div_10_div_50_Template_button_click_4_listener", "_r14", "CpqConfiguratorComponent_div_22_div_10_div_50_li_6_Template", "CpqConfiguratorComponent_div_22_div_10_div_50_Template_button_click_8_listener", "getPageNumbers", "getTotalPages", "CpqConfiguratorComponent_div_22_div_10_button_63_Template_button_click_0_listener", "_r17", "toggleAllProductsExpansion", "_c3", "expandedProducts", "size", "CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_productToggle_1_listener", "$event", "_r18", "removeConfirmedProduct", "CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_productInclusionToggle_1_listener", "toggleProductInclusion", "CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_quantityUpdate_1_listener", "updateProductQuantity", "productId", "event", "CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_quantityIncrease_1_listener", "increaseQuantity", "CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_quantityDecrease_1_listener", "decreaseQuantity", "CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_expansionToggle_1_listener", "toggleProductExpansion", "CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template_app_product_tree_editProperties_1_listener", "openPropertiesModal", "CpqConfiguratorComponent_div_22_div_10_div_66_div_1_hr_2_Template", "selection_r19", "product", "isProductExpanded", "quantity", "ɵɵpureFunction0", "_c4", "hasProductProperties", "getSelectedPropertiesCountForProduct", "getProductFreeProperties", "getProductPaidProperties", "getProductRequiredProperties", "i_r20", "confirmedSelectedProducts", "CpqConfiguratorComponent_div_22_div_10_div_66_div_1_Template", "CpqConfiguratorComponent_div_22_div_10_div_67_Template_button_click_8_listener", "_r21", "clearAllConfirmedProducts", "ɵɵtextInterpolate2", "getTotalConfirmedQuantity", "totalPrice", "CpqConfiguratorComponent_div_22_div_10_button_70_Template_button_click_0_listener", "_r22", "previousStep", "CpqConfiguratorComponent_div_22_div_10_button_80_Template_button_click_0_listener", "_r23", "CpqConfiguratorComponent_div_22_div_10_button_13_Template", "ɵɵtwoWayListener", "CpqConfiguratorComponent_div_22_div_10_Template_input_ngModelChange_21_listener", "_r7", "ɵɵtwoWayBindingSet", "CpqConfiguratorComponent_div_22_div_10_Template_input_input_21_listener", "CpqConfiguratorComponent_div_22_div_10_button_22_Template", "CpqConfiguratorComponent_div_22_div_10_Template_select_ngModelChange_24_listener", "<PERSON><PERSON><PERSON>er", "CpqConfiguratorComponent_div_22_div_10_Template_select_change_24_listener", "CpqConfiguratorComponent_div_22_div_10_option_27_Template", "CpqConfiguratorComponent_div_22_div_10_Template_select_ngModelChange_29_listener", "typeFilter", "CpqConfiguratorComponent_div_22_div_10_Template_select_change_29_listener", "CpqConfiguratorComponent_div_22_div_10_Template_select_ngModelChange_37_listener", "itemsPerPage", "CpqConfiguratorComponent_div_22_div_10_Template_select_change_37_listener", "CpqConfiguratorComponent_div_22_div_10_div_48_Template", "CpqConfiguratorComponent_div_22_div_10_div_49_Template", "CpqConfiguratorComponent_div_22_div_10_div_50_Template", "CpqConfiguratorComponent_div_22_div_10_button_63_Template", "CpqConfiguratorComponent_div_22_div_10_div_65_Template", "CpqConfiguratorComponent_div_22_div_10_div_66_Template", "CpqConfiguratorComponent_div_22_div_10_div_67_Template", "CpqConfiguratorComponent_div_22_div_10_button_70_Template", "CpqConfiguratorComponent_div_22_div_10_div_71_Template", "CpqConfiguratorComponent_div_22_div_10_button_80_Template", "totalItems", "ɵɵtwoWayProperty", "getAvailableFamiliesForFilter", "filteredProducts", "CpqConfiguratorComponent_div_22_div_8_Template", "CpqConfiguratorComponent_div_22_div_9_Template", "CpqConfiguratorComponent_div_22_div_10_Template", "familyId_r25", "getOptionsTotal", "CpqConfiguratorComponent_div_25_div_21_ng_container_40_i_5_Template_i_click_0_listener", "_r26", "item_r27", "toggleTableRowExpansion", "selection", "expanded", "ɵɵelementContainerStart", "CpqConfiguratorComponent_div_25_div_21_ng_container_40_i_5_Template", "CpqConfiguratorComponent_div_25_div_21_ng_container_40_span_6_Template", "level", "ɵɵstyleProp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c5", "selectionSource", "children<PERSON>rice", "CpqConfiguratorComponent_div_25_div_21_div_16_Template", "CpqConfiguratorComponent_div_25_div_21_ng_container_40_Template", "getMainProductsTotal", "propertiesPrice", "getHierarchicalProductsForTable", "CpqConfiguratorComponent_div_25_button_23_Template_button_click_0_listener", "_r28", "CpqConfiguratorComponent_div_25_span_12_Template", "CpqConfiguratorComponent_div_25_Template_button_click_19_listener", "_r24", "togglePricePanel", "CpqConfiguratorComponent_div_25_div_21_Template", "CpqConfiguratorComponent_div_25_button_23_Template", "CpqConfiguratorComponent_div_25_div_24_Template", "CpqConfiguratorComponent_div_25_Template_button_click_26_listener", "showResetConfirmation", "CpqConfiguratorComponent_div_25_Template_button_click_29_listener", "exportPriceSummaryToPdf", "isPricePanelExpanded", "selection_r29", "CpqConfiguratorComponent_div_43_li_5_Template", "selectedChildrenProducts", "CpqConfiguratorComponent_div_61_div_17_Template_div_click_1_listener", "propertySelection_r31", "_r30", "togglePropertySelection", "selectedProductForProperties", "propertyid", "CpqConfiguratorComponent_div_61_div_17_span_9_Template", "CpqConfiguratorComponent_div_61_div_17_Template_input_change_16_listener", "selected", "property", "isrequired", "ɵɵpureFunction4", "_c6", "propertytype", "CpqConfiguratorComponent_div_61_div_21_Template_div_click_1_listener", "propertySelection_r33", "_r32", "CpqConfiguratorComponent_div_61_div_21_span_9_Template", "CpqConfiguratorComponent_div_61_div_21_Template_input_change_16_listener", "isexclusive", "propertySelection_r34", "_c7", "CpqConfiguratorComponent_div_61_div_17_Template", "CpqConfiguratorComponent_div_61_div_18_Template", "CpqConfiguratorComponent_div_61_div_21_Template", "CpqConfiguratorComponent_div_61_div_22_Template", "CpqConfiguratorComponent_div_61_div_25_Template", "CpqConfiguratorComponent_div_61_div_26_Template", "getGroupedPropertiesForProduct", "free", "paid", "getRequiredPropertiesForProduct", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_5_Template_div_click_0_listener", "_r37", "child_r38", "toggleNodeExpanded", "_c8", "isNodeExpanded", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_5_Template_div_click_0_listener", "_r40", "grandchild_r41", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_15_ng_container_1_Template_input_change_9_listener", "greatgrandchild_r43", "_r42", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_15_ng_container_1_Template", "getDirectChildren", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_5_Template", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_6_Template", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_Template_input_change_10_listener", "_r39", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_div_15_Template", "_c9", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_ng_container_1_Template", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_5_Template", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_6_Template", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_Template_input_change_10_listener", "_r36", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_div_15_Template", "CpqConfiguratorComponent_div_97_div_16_ng_container_1_Template", "currentRootProduct", "CpqConfiguratorComponent_div_97_Template_div_click_6_listener", "_r35", "toggleRootExpanded", "CpqConfiguratorComponent_div_97_Template_input_change_11_listener", "CpqConfiguratorComponent_div_97_div_16_Template", "isRootExpanded", "CpqConfiguratorComponent", "constructor", "allProducts", "availableProducts", "isLoading", "descendants<PERSON><PERSON>", "Map", "directChildrenMap", "FIXED_CATEGORY", "opportunityCategory", "tempSelectedProducts", "productTreeNodes", "selectedTreeNodes", "showAssociatedProductsDialog", "conflictProduct", "isProductsPanelExpanded", "expandedProductDetails", "Set", "allProperties", "selectedProperties", "expandedPropertySections", "maxSteps", "expandedTableRows", "finalConfiguration", "availablePageSizes", "expandedNodes", "genealogyInfoCache", "genealogyPriceCache", "pageNumbersCache", "totalPages", "pages", "propertiesPriceCache", "priceFormatter", "Intl", "NumberFormat", "style", "currency", "getProgressPercentage", "Math", "round", "familyId", "index", "indexOf", "splice", "push", "loadAvailableProducts", "includes", "family", "find", "f", "console", "log", "applyFiltersAndPagination", "ngOnInit", "loadOpportunityCategory", "clear", "directChildren", "for<PERSON>ach", "child", "add", "modalElement", "document", "getElementById", "modal", "bootstrap", "Modal", "show", "modalRef", "nodeId", "has", "delete", "buildProductTree", "rootProduct", "rootNode", "key", "label", "data", "children", "buildChildNodes", "products", "map", "node", "confirmAssociatedProducts", "hide", "updateTotalPrice", "parseInt", "buildProductRelationships", "parentId", "set", "get", "ngOnDestroy", "clearMessages", "initializePropertiesForProduct", "productProperties", "getAvailablePropertiesForProduct", "propertySelection", "isDefaultProperty", "selectiondate", "Date", "productsPrice", "basePrice", "calculatePropertiesTotal", "format", "associations", "filter", "a", "association", "p", "undefined", "propertyId", "isdefault", "ps", "reduce", "total", "getAllDescendants", "descendants", "childDescendants", "ceil", "page", "current", "i", "familyIds", "trim", "toLowerCase", "hasSearchTerm", "productsToHide", "allSelectedProducts", "filtered", "startIndex", "endIndex", "slice", "getGenealogyCount", "getGenealogyInfo", "descendantIds", "result", "count", "getGenealogyTotalPrice", "genealogyInfo", "sum", "clearTempSelection", "removeTempProduct", "findIndex", "getTempSelectionTotal", "confirmTempSelection", "temp", "initializePropertiesSelection", "getMainProducts", "s", "mainIndex", "isChildOf", "parent", "some", "existingIndex", "newQuantity", "target", "value", "isNaN", "isExpanded", "isRequired", "getProductTreeData", "parentProductId", "isSelected", "getProductQuantity", "hasSelectedProperties", "selectedPropertiesCount", "freeProperties", "paidProperties", "requiredProperties", "properties", "getPropertiesForProduct", "isFreeProperty", "isPaidProperty", "newSelection", "processedIds", "mainProducts", "sort", "b", "localeCompare", "isTableRowExpanded", "confirmParentChildConflict", "setErrorMessage", "validatePropertiesSelection", "missingProperties", "getMissingRequiredProperties", "missingNames", "prop", "join", "generateFinalConfiguration", "backToSelection", "updateStepInterface", "mainProductsPrice", "selectedFamilies", "selectedProducts", "subtotal", "getProductLevel", "hierarchyPath", "getProductHierarchyPath", "priceBreakdown", "timestamp", "toISOString", "currentProduct", "path", "unshift", "areAllRequiredPropertiesSelected", "message", "setTimeout", "setSuccessMessage", "productRequiredProperties", "requiredProp", "productName", "resetConfiguration", "hasChildrenSelected", "allSelected", "descendantId", "sel", "showParentChildConflictModal", "selectors", "decls", "vars", "consts", "template", "CpqConfiguratorComponent_Template", "rf", "ctx", "CpqConfiguratorComponent_div_1_Template", "CpqConfiguratorComponent_div_20_Template", "CpqConfiguratorComponent_div_21_Template", "CpqConfiguratorComponent_div_22_Template", "CpqConfiguratorComponent_div_23_Template", "CpqConfiguratorComponent_div_24_Template", "CpqConfiguratorComponent_div_25_Template", "CpqConfiguratorComponent_div_43_Template", "CpqConfiguratorComponent_Template_button_click_49_listener", "CpqConfiguratorComponent_div_61_Template", "CpqConfiguratorComponent_Template_button_click_85_listener", "CpqConfiguratorComponent_div_97_Template", "CpqConfiguratorComponent_Template_button_click_101_listener"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet_CRM\\apps\\AngularCPQapp\\src\\app\\components\\cpq-configurator\\cpq-configurator.component.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet_CRM\\apps\\AngularCPQapp\\src\\app\\components\\cpq-configurator\\cpq-configurator.component.html"], "sourcesContent": ["/**\n * @fileoverview Composant principal pour la configuration CPQ\n * @description Interface utilisateur pour sélectionner catégories, familles et produits\n */\n\nimport { Component, OnInit, OnDestroy } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Inject } from '@angular/core';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@taiga-ui/cdk';\nimport { TUI_TREE_CONTENT } from '@taiga-ui/kit';\nimport { PolymorpheusComponent } from '@taiga-ui/polymorpheus';\nimport { TreeContentComponent } from './tree-content.component';\n\nimport { Product } from '../../models/product/product';\n// Import des constantes\nimport { LUXURY_CARS_CATEGORY, CAR_FAMILIES, LUXURY_CARS_PRODUCTS } from '../../constants/cpq.constants';\n\n// import { DynamicsService } from '../../services/dynamics.service';\nimport { finalize } from 'rxjs/operators';\n\nimport { ProductProperty, PropertySelection, PropertyUtils } from '../../models/property/property';\nimport { PRODUCT_PROPERTIES, PRODUCT_PROPERTY_ASSOCIATIONS } from '../../constants/property.constants';\n\n// Définition d'une interface TreeNode pour remplacer celle de PrimeNG\ninterface TreeNode {\n  key?: string;\n  label?: string;\n  data?: any;\n  icon?: string;\n  expanded?: boolean;\n  children?: TreeNode[];\n}\n\n// ===== INTERFACES =====\ninterface ProductSelection {\n  product: Product;\n  quantity: number;\n  selectionSource: 'MAIN_LIST' | 'TREE_INCLUSION';\n}\n\ninterface FinalConfiguration {\n  selectedCategory: any;\n  selectedFamilies: any[];\n  selectedProducts: any[];\n  selectedProperties: PropertySelection[];\n  priceBreakdown: {\n    mainProducts: number;\n    properties: number;\n    total: number;\n  };\n  totalPrice: number;\n  timestamp: string;\n}\n\n// ===== INTERFACES POUR FILTRES ET RECHERCHE =====\ninterface ProductFilters {\n  searchTerm: string;\n  familyFilter: string;\n  typeFilter: 'all' | 'main' | 'option';\n}\n\ninterface PaginationConfig {\n  currentPage: number;\n  itemsPerPage: number;\n  totalItems: number;\n  availablePageSizes: number[];\n}\n\n@Component({\n  selector: 'app-cpq-configurator',\n  standalone: false,\n  templateUrl: './cpq-configurator.component.html',\n  styleUrls: ['./cpq-configurator.component.css']\n})\nexport class CpqConfiguratorComponent implements OnInit, OnDestroy {\n\n  // ===== PROPRIÉTÉS PRINCIPALES =====\n  allProducts: Product[] = LUXURY_CARS_PRODUCTS;\n  availableProducts: Product[] = [];\n  filteredProducts: Product[] = [];\n  \n  // Indicateur de chargement\n  isLoading: boolean = false;\n  \n  // Cache pour les descendants des produits\n  private descendantsCache = new Map<string, string[]>();\n  \n  // Map des enfants directs par ID de parent pour améliorer les performances\n  private directChildrenMap = new Map<string, Product[]>();\n  \n  // Données fixes\n  readonly FIXED_CATEGORY = LUXURY_CARS_CATEGORY;\n  readonly CAR_FAMILIES = CAR_FAMILIES;\n  \n  // Données dynamiques depuis D365\n  opportunityCategory: number = 0;\n  carFamilies: any[] = CAR_FAMILIES;\n  \n  // État de sélection\n  // selectedCategory = this.FIXED_CATEGORY; // Commenté mais conservé pour référence\n  selectedCategory: any = {\n    id: '0',\n    name: 'Chargement...',\n    description: 'Chargement de la catégorie...'\n  };\n  selectedFamilyIds: string[] = [];\n  tempSelectedProducts: ProductSelection[] = [];\n  confirmedSelectedProducts: ProductSelection[] = [];\n  \n  // Propriétés pour l'arborescence\n  productTreeNodes: TreeNode[] = [];\n  selectedTreeNodes: TreeNode[] = [];\n  showAssociatedProductsDialog: boolean = false;\n  currentRootProduct: Product | null = null;\n  \n  // Gestion des conflits et modals\n  conflictProduct: Product | null = null;\n  selectedChildrenProducts: ProductSelection[] = [];\n  selectedProductForProperties: Product | null = null;\n  private modalRef: any;\n  \n  // État des panneaux réductibles\n  isPricePanelExpanded: boolean = true;\n  isProductsPanelExpanded: boolean = true;\n  expandedProductDetails = new Set<string>();\n\n  // ===== NOUVELLES PROPRIÉTÉS POUR LES PROPRIÉTÉS =====\n  allProperties: ProductProperty[] = PRODUCT_PROPERTIES;\n  selectedProperties: PropertySelection[] = [];\n  expandedPropertySections = new Map<string, boolean>();\n  propertiesPrice: number = 0;\n\n  \n  // État de l'interface\n  public currentStep: number = 1;\n  public maxSteps: number = 3;\n  \n  // Stocke l'état d'expansion des lignes du tableau\n  expandedTableRows = new Set<string>();\n  \n  // Messages\n  errorMessage: string = '';\n  successMessage: string = '';\n  \n  // Hiérarchie et expansion\n  expandedProducts = new Map<string, boolean>();\n  totalPrice: number = 0;\n  \n  // Configuration finale\n  finalConfiguration: FinalConfiguration | null = null;\n\n  // ===== NOUVELLES PROPRIÉTÉS POUR FILTRES ET RECHERCHE =====\n  productFilters: ProductFilters = {\n    searchTerm: '',\n    familyFilter: 'all',\n    typeFilter: 'all'\n  };\n\n  pagination: PaginationConfig = {\n    currentPage: 1,\n    itemsPerPage: 25,\n    totalItems: 0,\n    availablePageSizes: [25, 50, 75, 0] // 0 = tous les éléments\n  };\n\n  // Variables pour l'expansion des noeuds dans l'arborescence\n  isRootExpanded: boolean = true;\n  expandedNodes = new Set<string>();\n\n  // ===== CONSTRUCTEUR =====\n  constructor() {}\n\n  /**\n   * Obtient le pourcentage de progression\n   */\n  getProgressPercentage(): number {\n    return Math.round((this.currentStep / this.maxSteps) * 100);\n  }\n\n  /**\n   * Bascule la sélection d'une famille\n   */\n  toggleFamilySelection(familyId: string): void {\n    const index = this.selectedFamilyIds.indexOf(familyId);\n    \n    if (index > -1) {\n      this.selectedFamilyIds.splice(index, 1);\n    } else {\n      this.selectedFamilyIds.push(familyId);\n    }\n    \n    this.loadAvailableProducts();\n  }\n\n  /**\n   * Vérifie si une famille est sélectionnée\n   */\n  isFamilySelected(familyId: string): boolean {\n    return this.selectedFamilyIds.includes(familyId);\n  }\n\n  /**\n   * Obtient le nom d'une famille par son ID\n   */\n  getFamilyName(familyId: string): string {\n    const family = this.carFamilies.find(f => f.id === familyId);\n    return family ? family.name : familyId;\n  }\n  \n  /**\n   * Charge les produits disponibles selon les familles sélectionnées\n   */\n  loadAvailableProducts(): void {\n    // Utiliser directement tous les produits de LUXURY_CARS_PRODUCTS\n    console.log('Affichage de tous les produits LUXURY_CARS_PRODUCTS');\n    \n    // Utiliser directement tous les produits sans filtrage\n    this.availableProducts = LUXURY_CARS_PRODUCTS;\n    \n    console.log(`${this.availableProducts.length} produits disponibles`);\n    this.applyFiltersAndPagination();\n    this.isLoading = false;\n  }\n\n  // ===== LIFECYCLE HOOKS =====\n  ngOnInit(): void {\n    console.log('🚗 Initialisation du configurateur Voitures de Luxe');\n    this.loadOpportunityCategory();\n  }\n  \n  /**\n   * Ouvre le dialogue des produits associés pour un produit racine\n   */\n  showAssociatedProducts(product: Product): void {\n    this.currentRootProduct = product;\n    this.isRootExpanded = true;\n    this.expandedNodes.clear();\n    \n    // Pré-étendre les premiers niveaux pour une meilleure expérience utilisateur\n    const directChildren = this.getDirectChildren(product.productid);\n    directChildren.forEach(child => {\n      if (this.hasDirectChildren(child.productid)) {\n        this.expandedNodes.add(child.productid);\n      }\n    });\n    \n    // Utiliser Bootstrap pour afficher le modal\n    const modalElement = document.getElementById('associatedProductsModal');\n    if (modalElement) {\n      // @ts-ignore: Ignorer l'erreur TS car nous savons que bootstrap est disponible\n      const modal = new bootstrap.Modal(modalElement);\n      modal.show();\n      this.modalRef = modal;\n    }\n  }\n  \n  /**\n   * Bascule l'expansion du noeud racine\n   */\n  toggleRootExpanded(): void {\n    this.isRootExpanded = !this.isRootExpanded;\n  }\n  \n  /**\n   * Bascule l'expansion d'un noeud\n   */\n  toggleNodeExpanded(nodeId: string): void {\n    if (this.expandedNodes.has(nodeId)) {\n      this.expandedNodes.delete(nodeId);\n    } else {\n      this.expandedNodes.add(nodeId);\n    }\n  }\n  \n  /**\n   * Vérifie si un noeud est étendu\n   */\n  isNodeExpanded(nodeId: string): boolean {\n    return this.expandedNodes.has(nodeId);\n  }\n  \n  /**\n   * Construit l'arborescence des produits associés\n   */\n  buildProductTree(rootProduct: Product): void {\n    // Réinitialiser l'arborescence\n    this.productTreeNodes = [];\n    this.selectedTreeNodes = [];\n    \n    // Créer le noeud racine\n    const rootNode: TreeNode = {\n      key: rootProduct.productid,\n      label: rootProduct.name,\n      data: rootProduct,\n      icon: 'pi pi-folder',\n      expanded: true,\n      children: []\n    };\n    \n    // Ajouter les enfants directs\n    const directChildren = this.getDirectChildren(rootProduct.productid);\n    if (directChildren.length > 0) {\n      rootNode.children = this.buildChildNodes(directChildren, 1);\n    }\n    \n    this.productTreeNodes = [rootNode];\n  }\n  \n  /**\n   * Construit récursivement les noeuds enfants\n   */\n  private buildChildNodes(products: Product[], level: number): TreeNode[] {\n    return products.map(product => {\n      const node: TreeNode = {\n        key: product.productid,\n        label: product.name,\n        data: product,\n        icon: level === 1 ? 'pi pi-file' : 'pi pi-cog',\n        expanded: level < 2,\n        children: []\n      };\n      \n      // Ajouter les enfants de ce produit\n      const children = this.getDirectChildren(product.productid);\n      if (children.length > 0) {\n        node.children = this.buildChildNodes(children, level + 1);\n      }\n      \n      return node;\n    });\n  }\n  \n  /**\n   * Confirme les produits associés sélectionnés dans l'arborescence\n   */\n  confirmAssociatedProducts(): void {\n    // Fermer le modal\n    if (this.modalRef) {\n      this.modalRef.hide();\n    }\n    \n    // Mettre à jour le prix total\n    this.updateTotalPrice();\n  }\n\n  /**\n   * Charge la catégorie de l'opportunité courante\n   */\n  loadOpportunityCategory(): void {\n    console.log('🚗 Initialisation du configurateur Voitures de Luxe');\n    \n    // Utiliser directement les constantes\n    this.opportunityCategory = parseInt(this.FIXED_CATEGORY.id);\n    this.selectedCategory = this.FIXED_CATEGORY;\n    \n    console.log('Utilisation de la catégorie fixe:', this.selectedCategory);\n    \n    // Pas besoin de charger les familles car on utilise CAR_FAMILIES directement\n    this.buildProductRelationships();\n    \n    this.isLoading = false;\n  }\n\n  /**\n   * Précalcule les relations parent-enfant entre les produits\n   */\n  private buildProductRelationships(): void {\n    // Parcourir tous les produits une seule fois\n    this.allProducts.forEach(product => {\n      if (product.parentproductid) {\n        // Ajouter ce produit comme enfant de son parent\n        const parentId = product.parentproductid;\n        if (!this.directChildrenMap.has(parentId)) {\n          this.directChildrenMap.set(parentId, []);\n        }\n        this.directChildrenMap.get(parentId)!.push(product);\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.clearMessages();\n    // Libérer les ressources\n    this.descendantsCache.clear();\n    this.directChildrenMap.clear();\n    this.genealogyInfoCache.clear();\n    this.genealogyPriceCache.clear();\n    this.propertiesPriceCache.clear();\n    this.pageNumbersCache = { totalPages: 0, currentPage: 0, pages: [] };\n  }\n\n  // Méthode pour effacer les messages\n  private clearMessages(): void {\n    this.errorMessage = '';\n    this.successMessage = '';\n  }\n\n  // Méthode pour obtenir les enfants directs\n  getDirectChildren(productId: string): Product[] {\n    return this.directChildrenMap.get(productId) || [];\n  }\n\n  // Méthode pour vérifier si un produit a des enfants directs\n  hasDirectChildren(productId: string): boolean {\n    return this.directChildrenMap.has(productId) && this.directChildrenMap.get(productId)!.length > 0;\n  }\n\n  // Méthode pour initialiser les propriétés d'un produit\n  initializePropertiesForProduct(product: Product): void {\n    const productProperties = this.getAvailablePropertiesForProduct(product.productid);\n    \n    productProperties.forEach(property => {\n      const propertySelection: PropertySelection = {\n        productid: product.productid,\n        propertyid: property.propertyid,\n        selected: this.isDefaultProperty(product.productid, property.propertyid) || property.isrequired,\n        selectiondate: new Date(),\n        product: product,\n        property: property\n      };\n      this.selectedProperties.push(propertySelection);\n    });\n  }\n\n  // Méthode pour mettre à jour le prix total\n  updateTotalPrice(): void {\n    // Vider les caches de prix car les sélections ont changé\n    this.propertiesPriceCache.clear();\n    this.genealogyPriceCache.clear();\n    \n    // Calculer le prix des produits avec leurs propriétés\n    let productsPrice = 0;\n    \n    this.confirmedSelectedProducts.forEach(selection => {\n      // Prix de base du produit\n      const basePrice = (selection.product.price || 0) * selection.quantity;\n      \n      // Prix des propriétés pour ce produit\n      const propertiesPrice = this.getPropertiesPriceForProduct(selection.product.productid) * selection.quantity;\n      \n      productsPrice += basePrice + propertiesPrice;\n    });\n    \n    this.propertiesPrice = PropertyUtils.calculatePropertiesTotal(\n      this.selectedProperties,\n      this.allProperties\n    );\n    \n    this.totalPrice = productsPrice;\n  }\n\n  // Cache pour les informations de généalogie\n  private genealogyInfoCache = new Map<string, { count: number; products: Product[] }>();\n  \n  // Cache pour les prix totaux de généalogie\n  private genealogyPriceCache = new Map<string, number>();\n\n  // Cache pour les numéros de page\n  private pageNumbersCache: { totalPages: number; currentPage: number; pages: number[] } = {\n    totalPages: 0,\n    currentPage: 0,\n    pages: []\n  };\n\n  // Cache pour les prix des propriétés par produit\n  private propertiesPriceCache = new Map<string, number>();\n  \n  // Cache pour le formatteur de prix\n  private priceFormatter = new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: 'EUR'\n  });\n  \n  /**\n   * Formate un prix en devise\n   */\n  formatPrice(price: number): string {\n    return this.priceFormatter.format(price);\n  }\n\n  /**\n   * Obtient les propriétés disponibles pour un produit\n   */\n  getAvailablePropertiesForProduct(productId: string): ProductProperty[] {\n    const associations = PRODUCT_PROPERTY_ASSOCIATIONS.filter(a => a.productid === productId);\n    return associations.map(association => \n      this.allProperties.find(p => p.propertyid === association.propertyid)\n    ).filter(p => p !== undefined) as ProductProperty[];\n  }\n\n  /**\n   * Vérifie si une propriété est par défaut pour un produit\n   */\n  isDefaultProperty(productId: string, propertyId: string): boolean {\n    const association = PRODUCT_PROPERTY_ASSOCIATIONS.find(\n      a => a.productid === productId && a.propertyid === propertyId\n    );\n    return association?.isdefault || false;\n  }\n\n  /**\n   * Obtient le prix total des propriétés pour un produit\n   */\n  getPropertiesPriceForProduct(productId: string): number {\n    // Vérifier si le résultat est déjà en cache\n    if (this.propertiesPriceCache.has(productId)) {\n      return this.propertiesPriceCache.get(productId)!;\n    }\n    \n    const totalPrice = this.selectedProperties\n      .filter(ps => ps.productid === productId && ps.selected)\n      .reduce((total, ps) => total + (ps.property?.price || 0), 0);\n    \n    // Stocker le résultat dans le cache\n    this.propertiesPriceCache.set(productId, totalPrice);\n    \n    return totalPrice;\n  }\n\n  /**\n   * Obtient tous les descendants d'un produit\n   */\n  getAllDescendants(productId: string): string[] {\n    // Vérifier si le résultat est déjà en cache\n    if (this.descendantsCache.has(productId)) {\n      return this.descendantsCache.get(productId)!;\n    }\n    \n    const descendants: string[] = [];\n    const directChildren = this.allProducts.filter(p => p.parentproductid === productId);\n    \n    directChildren.forEach(child => {\n      descendants.push(child.productid);\n      // Récursion pour obtenir les descendants des enfants\n      const childDescendants = this.getAllDescendants(child.productid);\n      descendants.push(...childDescendants);\n    });\n    \n    // Stocker le résultat dans le cache\n    this.descendantsCache.set(productId, descendants);\n    \n    return descendants;\n  }\n\n  // Méthodes pour la pagination\n  getTotalPages(): number {\n    if (this.pagination.itemsPerPage === 0) return 1;\n    return Math.ceil(this.pagination.totalItems / this.pagination.itemsPerPage);\n  }\n\n  onPageChange(page: number): void {\n    this.pagination.currentPage = page;\n    this.applyFiltersAndPagination();\n  }\n\n  getPageNumbers(): number[] {\n    const totalPages = this.getTotalPages();\n    const current = this.pagination.currentPage;\n    \n    // Vérifier si le résultat est déjà en cache\n    if (this.pageNumbersCache.totalPages === totalPages && \n        this.pageNumbersCache.currentPage === current) {\n      return this.pageNumbersCache.pages;\n    }\n    \n    const pages: number[] = [];\n\n    if (totalPages <= 7) {\n      for (let i = 1; i <= totalPages; i++) {\n        pages.push(i);\n      }\n    } else {\n      if (current <= 4) {\n        for (let i = 1; i <= 5; i++) pages.push(i);\n        pages.push(-1); // Ellipsis\n        pages.push(totalPages);\n      } else if (current >= totalPages - 3) {\n        pages.push(1);\n        pages.push(-1); // Ellipsis\n        for (let i = totalPages - 4; i <= totalPages; i++) pages.push(i);\n      } else {\n        pages.push(1);\n        pages.push(-1); // Ellipsis\n        for (let i = current - 1; i <= current + 1; i++) pages.push(i);\n        pages.push(-1); // Ellipsis\n        pages.push(totalPages);\n      }\n    }\n    \n    // Mettre à jour le cache\n    this.pageNumbersCache = {\n      totalPages,\n      currentPage: current,\n      pages: [...pages]\n    };\n\n    return pages;\n  }\n\n  // Méthodes pour les filtres\n  getAvailableFamiliesForFilter(): any[] {\n    const familyIds = new Set(this.availableProducts.map(p => p.familyId).filter(id => id));\n    return this.carFamilies.filter(f => familyIds.has(f.id));\n  }\n  \n  /**\n   * Efface tous les filtres\n   */\n  clearAllFilters(): void {\n    this.productFilters = {\n      searchTerm: '',\n      familyFilter: 'all',\n      typeFilter: 'all'\n    };\n    this.pagination.currentPage = 1;\n    this.applyFiltersAndPagination();\n  }\n  \n  /**\n   * Gère les changements de filtres\n   */\n  onFilterChange(): void {\n    this.pagination.currentPage = 1;\n    this.applyFiltersAndPagination();\n  }\n\n  applyFiltersAndPagination(): void {\n    // Utiliser une seule itération pour appliquer tous les filtres\n    const searchTerm = this.productFilters.searchTerm.trim().toLowerCase();\n    const familyFilter = this.productFilters.familyFilter;\n    const typeFilter = this.productFilters.typeFilter;\n    const hasSearchTerm = searchTerm.length > 0;\n    \n    // Créer un ensemble des IDs de produits à masquer (généalogie)\n    const productsToHide = new Set<string>();\n    if (this.tempSelectedProducts.length > 0 || this.confirmedSelectedProducts.length > 0) {\n      const allSelectedProducts = [...this.tempSelectedProducts, ...this.confirmedSelectedProducts];\n      allSelectedProducts.forEach(selection => {\n        const descendants = this.getAllDescendants(selection.product.productid);\n        descendants.forEach(id => productsToHide.add(id));\n      });\n    }\n    \n    // Appliquer tous les filtres en une seule passe\n    const filtered = this.availableProducts.filter(product => {\n      // Filtre par terme de recherche\n      if (hasSearchTerm && \n          !product.name.toLowerCase().includes(searchTerm) && \n          !(product.description?.toLowerCase().includes(searchTerm)) && \n          !product.productid.toLowerCase().includes(searchTerm)) {\n        return false;\n      }\n      \n      // Filtre par famille\n      if (familyFilter !== 'all' && product.familyId !== familyFilter) {\n        return false;\n      }\n      \n      // Filtre par type\n      if (typeFilter === 'main' && product.parentproductid) {\n        return false;\n      }\n      if (typeFilter === 'option' && !product.parentproductid) {\n        return false;\n      }\n      \n      // Masquer les produits de la généalogie\n      if (productsToHide.has(product.productid)) {\n        return false;\n      }\n      \n      return true;\n    });\n\n    // Mettre à jour le total\n    this.pagination.totalItems = filtered.length;\n\n    // Appliquer la pagination\n    if (this.pagination.itemsPerPage > 0) {\n      const startIndex = (this.pagination.currentPage - 1) * this.pagination.itemsPerPage;\n      const endIndex = startIndex + this.pagination.itemsPerPage;\n      this.filteredProducts = filtered.slice(startIndex, endIndex);\n    } else {\n      this.filteredProducts = filtered;\n    }\n  }\n\n  // Méthodes pour la généalogie\n  getGenealogyCount(productId: string): number {\n    // Utiliser directement le cache des descendants s'il existe\n    if (this.descendantsCache.has(productId)) {\n      return this.descendantsCache.get(productId)!.length;\n    }\n    return this.getAllDescendants(productId).length;\n  }\n\n  getGenealogyInfo(productId: string): { count: number; products: Product[] } {\n    // Vérifier si le résultat est déjà en cache\n    if (this.genealogyInfoCache.has(productId)) {\n      return this.genealogyInfoCache.get(productId)!;\n    }\n    \n    const descendantIds = this.getAllDescendants(productId);\n    const products = descendantIds.map(id => \n      this.allProducts.find(p => p.productid === id)\n    ).filter(p => p !== undefined) as Product[];\n    \n    const result = {\n      count: descendantIds.length,\n      products: products\n    };\n    \n    // Stocker le résultat dans le cache\n    this.genealogyInfoCache.set(productId, result);\n    \n    return result;\n  }\n\n  getGenealogyTotalPrice(productId: string): number {\n    // Vérifier si le résultat est déjà en cache\n    if (this.genealogyPriceCache.has(productId)) {\n      return this.genealogyPriceCache.get(productId)!;\n    }\n    \n    const genealogyInfo = this.getGenealogyInfo(productId);\n    const totalPrice = genealogyInfo.products.reduce((sum, p) => sum + (p.price || 0), 0);\n    \n    // Stocker le résultat dans le cache\n    this.genealogyPriceCache.set(productId, totalPrice);\n    \n    return totalPrice;\n  }\n\n  // Méthodes pour la sélection temporaire\n  clearTempSelection(): void {\n    this.tempSelectedProducts = [];\n    // Réappliquer les filtres pour réafficher les produits masqués\n    this.applyFiltersAndPagination();\n  }\n\n  removeTempProduct(product: any): void {\n    const index = this.tempSelectedProducts.findIndex(\n      selection => selection.product.productid === product.productid\n    );\n    if (index >= 0) {\n      this.tempSelectedProducts.splice(index, 1);\n      // Réappliquer les filtres pour réafficher les produits de la généalogie\n      this.applyFiltersAndPagination();\n    }\n  }\n\n  getTempSelectionTotal(): number {\n    return this.tempSelectedProducts.reduce((total, selection) => {\n      return total + ((selection.product.price || 0) * selection.quantity);\n    }, 0);\n  }\n\n  confirmTempSelection(): void {\n    this.confirmedSelectedProducts = this.tempSelectedProducts.map(temp => ({\n      ...temp,\n      selectionSource: temp.selectionSource || 'MAIN_LIST'\n    }));\n    this.tempSelectedProducts = [];\n    \n    // Initialiser les propriétés pour les nouveaux produits confirmés\n    this.initializePropertiesSelection();\n    \n    this.updateTotalPrice();\n    this.applyFiltersAndPagination();\n  }\n\n  // Méthodes pour les produits confirmés\n  getMainProducts(): ProductSelection[] {\n    return this.confirmedSelectedProducts.filter(s => s.selectionSource === 'MAIN_LIST');\n  }\n\n  getMainProductsTotal(): number {\n    return this.getMainProducts().reduce((total, selection) => {\n      return total + ((selection.product.price || 0) * selection.quantity);\n    }, 0);\n  }\n\n  getOptionsTotal(): number {\n    return this.confirmedSelectedProducts\n      .filter(s => s.selectionSource === 'TREE_INCLUSION')\n      .reduce((total, selection) => {\n        return total + ((selection.product.price || 0) * selection.quantity);\n      }, 0);\n  }\n\n  getTotalConfirmedQuantity(): number {\n    return this.confirmedSelectedProducts.reduce((total, selection) => {\n      return total + selection.quantity;\n    }, 0);\n  }\n\n  clearAllConfirmedProducts(): void {\n    this.confirmedSelectedProducts = [];\n    this.updateTotalPrice();\n    // Réappliquer les filtres pour réafficher tous les produits\n    this.applyFiltersAndPagination();\n  }\n\n  removeConfirmedProduct(product: any): void {\n    // Supprimer le produit principal\n    const mainIndex = this.confirmedSelectedProducts.findIndex(\n      selection => selection.product.productid === product.productid && \n      selection.selectionSource === 'MAIN_LIST'\n    );\n    \n    if (mainIndex >= 0) {\n      this.confirmedSelectedProducts.splice(mainIndex, 1);\n    }\n\n    // Supprimer aussi tous ses enfants inclus\n    this.confirmedSelectedProducts = this.confirmedSelectedProducts.filter(\n      selection => {\n        if (selection.selectionSource === 'TREE_INCLUSION') {\n          // Vérifier si c'est un enfant du produit supprimé\n          return !this.isChildOf(selection.product, product.productid);\n        }\n        return true;\n      }\n    );\n\n    this.updateTotalPrice();\n  }\n\n  // Méthode pour vérifier si un produit est enfant d'un autre\n  private isChildOf(product: Product, parentId: string): boolean {\n    if (product.parentproductid === parentId) {\n      return true;\n    }\n    \n    // Vérification récursive\n    const parent = this.allProducts.find(p => p.productid === product.parentproductid);\n    if (parent) {\n      return this.isChildOf(parent, parentId);\n    }\n    \n    return false;\n  }\n  \n  /**\n   * Vérifie si un produit est temporairement sélectionné\n   */\n  isTempProductSelected(product: any): boolean {\n    return this.tempSelectedProducts.some(\n      selection => selection.product.productid === product.productid\n    );\n  }\n  \n  /**\n   * Sélectionne directement un produit (sans passer par la sélection temporaire)\n   */\n  toggleTempProductSelection(product: any): void {\n    // Vérifier si le produit est déjà sélectionné\n    const existingIndex = this.confirmedSelectedProducts.findIndex(\n      selection => selection.product.productid === product.productid\n    );\n\n    if (existingIndex >= 0) {\n      // Désélectionner le produit\n      this.confirmedSelectedProducts.splice(existingIndex, 1);\n    } else {\n      // Ajouter directement le produit au panier\n      this.confirmedSelectedProducts.push({\n        product: product,\n        quantity: 1,\n        selectionSource: 'MAIN_LIST'\n      });\n      \n      // Initialiser les propriétés pour ce produit\n      this.initializePropertiesForProduct(product);\n    }\n    \n    // Mettre à jour le prix total\n    this.updateTotalPrice();\n    \n    // Mettre à jour l'affichage\n    this.applyFiltersAndPagination();\n  }\n\n  // Méthodes pour la gestion des quantités\n  updateProductQuantity(productId: string, event: any): void {\n    let newQuantity = parseInt(event.target.value);\n    \n    if (isNaN(newQuantity) || newQuantity < 1) newQuantity = 1;\n    if (newQuantity > 99) newQuantity = 99;\n    \n    const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);\n    if (selection) {\n      selection.quantity = newQuantity;\n      this.updateTotalPrice();\n    }\n  }\n\n  increaseQuantity(productId: string): void {\n    const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);\n    if (selection && selection.quantity < 99) {\n      selection.quantity++;\n      this.updateTotalPrice();\n    }\n  }\n\n  decreaseQuantity(productId: string): void {\n    const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);\n    if (selection && selection.quantity > 1) {\n      selection.quantity--;\n      this.updateTotalPrice();\n    }\n  }\n\n  // Méthodes pour l'expansion des produits\n  toggleProductExpansion(productId: string): void {\n    const isExpanded = this.expandedProducts.get(productId) || false;\n    this.expandedProducts.set(productId, !isExpanded);\n  }\n\n  isProductExpanded(productId: string): boolean {\n    return this.expandedProducts.get(productId) || false;\n  }\n\n  toggleAllProductsExpansion(): void {\n    if (this.expandedProducts.size > 0) {\n      // Si des produits sont étendus, tout réduire\n      this.expandedProducts.clear();\n    } else {\n      // Si aucun produit n'est étendu, tout étendre\n      this.confirmedSelectedProducts.forEach(selection => {\n        if (this.hasDirectChildren(selection.product.productid)) {\n          this.expandedProducts.set(selection.product.productid, true);\n        }\n      });\n    }\n  }\n\n  // Méthodes pour les propriétés\n  hasProductProperties(productId: string): boolean {\n    return this.getSelectedPropertiesCountForProduct(productId) > 0;\n  }\n\n  getSelectedPropertiesCountForProduct(productId: string): number {\n    return this.selectedProperties.filter(\n      ps => ps.productid === productId && ps.selected\n    ).length;\n  }\n\n  getProductFreeProperties(productId: string): any[] {\n    return this.selectedProperties\n      .filter(ps => ps.productid === productId && ps.selected && ps.property?.price === 0 && !ps.property?.isrequired)\n      .map(ps => ({\n        name: ps.property?.name,\n        isRequired: ps.property?.isrequired\n      }));\n  }\n\n  getProductPaidProperties(productId: string): any[] {\n    return this.selectedProperties\n      .filter(ps => ps.productid === productId && ps.selected && (ps.property?.price || 0) > 0 && !ps.property?.isrequired)\n      .map(ps => ({\n        name: ps.property?.name,\n        price: ps.property?.price,\n        isRequired: ps.property?.isrequired\n      }));\n  }\n\n  getProductRequiredProperties(productId: string): any[] {\n    return this.selectedProperties\n      .filter(ps => ps.productid === productId && ps.property?.isrequired)\n      .map(ps => ({\n        name: ps.property?.name,\n        price: ps.property?.price,\n        isRequired: true\n      }));\n  }\n\n  // Méthodes pour l'arborescence des produits\n  getProductTreeData(parentProductId: string): any[] {\n    const children = this.getDirectChildren(parentProductId);\n    return children.map(child => {\n      return {\n        product: child,\n        isSelected: this.isProductConfirmed(child),\n        isExpanded: this.isProductExpanded(child.productid),\n        quantity: this.getProductQuantity(child.productid),\n        directChildren: this.getProductTreeData(child.productid),\n        hasChildren: this.hasDirectChildren(child.productid),\n        hasSelectedProperties: this.hasProductProperties(child.productid),\n        selectedPropertiesCount: this.getSelectedPropertiesCountForProduct(child.productid),\n        freeProperties: this.getProductFreeProperties(child.productid),\n        paidProperties: this.getProductPaidProperties(child.productid),\n        requiredProperties: this.getProductRequiredProperties(child.productid)\n      };\n    });\n  }\n\n  // Méthodes pour la gestion des propriétés\n  initializePropertiesSelection(): void {\n    this.selectedProperties = [];\n    \n    // Pour chaque produit confirmé, initialiser ses propriétés\n    this.confirmedSelectedProducts.forEach(selection => {\n      this.initializePropertiesForProduct(selection.product);\n    });\n    \n    this.updateTotalPrice();\n  }\n\n  togglePropertySelection(productId: string, propertyId: string): void {\n    const propertySelection = this.selectedProperties.find(\n      ps => ps.productid === productId && ps.propertyid === propertyId\n    );\n    \n    if (propertySelection) {\n      const property = propertySelection.property!;\n      \n      // Ne pas permettre de désélectionner les propriétés requises\n      if (property.isrequired && propertySelection.selected) {\n        return;\n      }\n      \n      // Si la propriété est exclusive, désélectionner les autres du même type\n      if (property.isexclusive) {\n        this.selectedProperties\n          .filter(ps => \n            ps.productid === productId && \n            ps.property!.propertytype === property.propertytype &&\n            ps.propertyid !== propertyId\n          )\n          .forEach(ps => ps.selected = false);\n      }\n      \n      propertySelection.selected = !propertySelection.selected;\n      this.updateTotalPrice();\n    }\n  }\n\n  getGroupedPropertiesForProduct(productId: string): {\n    free: PropertySelection[];\n    paid: PropertySelection[];\n  } {\n    const properties = this.getPropertiesForProduct(productId);\n    return {\n      free: properties.filter(ps => PropertyUtils.isFreeProperty(ps.property!)),\n      paid: properties.filter(ps => PropertyUtils.isPaidProperty(ps.property!))\n    };\n  }\n\n  getPropertiesForProduct(productId: string): PropertySelection[] {\n    return this.selectedProperties.filter(ps => ps.productid === productId);\n  }\n\n  getRequiredPropertiesForProduct(productId: string): PropertySelection[] {\n    return this.selectedProperties\n      .filter(ps => ps.productid === productId && ps.property?.isrequired);\n  }\n\n  // Méthodes pour la gestion des inclusions\n  toggleProductInclusion(product: any): void {\n    const existingIndex = this.confirmedSelectedProducts.findIndex(\n      selection => selection.product.productid === product.productid\n    );\n\n    if (existingIndex >= 0) {\n      // Retirer le produit de la sélection\n      this.confirmedSelectedProducts.splice(existingIndex, 1);\n    } else {\n      // Ajouter le produit au panier\n      const newSelection: ProductSelection = {\n        product: product,\n        quantity: 1,\n        selectionSource: 'MAIN_LIST' // Tous les produits sont maintenant au même niveau\n      };\n      this.confirmedSelectedProducts.push(newSelection);\n      \n      // Initialiser les propriétés pour ce produit\n      this.initializePropertiesForProduct(product);\n    }\n\n    this.updateTotalPrice();\n  }\n\n  isProductConfirmed(product: any): boolean {\n    return this.confirmedSelectedProducts.some(\n      selection => selection.product.productid === product.productid\n    );\n  }\n\n  getProductQuantity(productId: string): number {\n    const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);\n    return selection ? selection.quantity : 1;\n  }\n\n  // Méthodes pour le tableau hiérarchique\n  getHierarchicalProductsForTable(): any[] {\n    const result: any[] = [];\n    const processedIds = new Set<string>();\n    \n    // Séparer les produits sélectionnés directement et les produits inclus\n    const mainProducts = this.confirmedSelectedProducts.filter(s => s.selectionSource === 'MAIN_LIST');\n    \n    // Ajouter tous les produits principaux (niveau 0) et leurs enfants visibles\n    mainProducts\n      .sort((a, b) => a.product.name.localeCompare(b.product.name))\n      .forEach(selection => {\n        if (!processedIds.has(selection.product.productid)) {\n          result.push({\n            selection,\n            level: 0,\n            hasChildren: this.hasDirectChildren(selection.product.productid),\n            expanded: this.isTableRowExpanded(selection.product.productid),\n            childrenPrice: 0\n          });\n          processedIds.add(selection.product.productid);\n        }\n      });\n    \n    return result;\n  }\n\n  toggleTableRowExpansion(productId: string): void {\n    if (this.expandedTableRows.has(productId)) {\n      this.expandedTableRows.delete(productId);\n    } else {\n      this.expandedTableRows.add(productId);\n    }\n  }\n\n  isTableRowExpanded(productId: string): boolean {\n    return this.expandedTableRows.has(productId);\n  }\n\n  // Méthodes pour les modals\n  openPropertiesModal(product: any): void {\n    this.selectedProductForProperties = product;\n  }\n\n  showResetConfirmation(): void {\n    // Afficher le modal de confirmation\n  }\n\n  exportPriceSummaryToPdf(): void {\n    // Exporter le récapitulatif en PDF\n  }\n\n  confirmParentChildConflict(): void {\n    // Confirmer le conflit parent-enfant\n  }\n\n  // Méthodes pour la navigation entre étapes\n  nextStep(): void {\n    switch (this.currentStep) {\n      case 1:\n        if (this.selectedFamilyIds.length === 0) {\n          this.setErrorMessage('Veuillez sélectionner au moins une famille de véhicules');\n          return;\n        }\n        this.currentStep = 2;\n        this.loadAvailableProducts();\n        break;\n        \n      case 2:\n        if (this.confirmedSelectedProducts.length === 0) {\n          this.setErrorMessage('Veuillez sélectionner au moins un produit');\n          return;\n        }\n        if (!this.validatePropertiesSelection()) {\n          const missingProperties = this.getMissingRequiredProperties();\n          const missingNames = missingProperties.map(prop => prop.name).join(', ');\n          this.setErrorMessage(`Veuillez sélectionner les propriétés requises suivantes : ${missingNames}`);\n          return;\n        }\n        // Passer directement à l'étape 3 (récapitulatif)\n        this.currentStep = 3;\n        this.generateFinalConfiguration();\n        break;\n    }\n    \n    this.clearMessages();\n  }\n\n  previousStep(): void {\n    if (this.currentStep > 1) {\n      // Si on est à l'étape 3 (récapitulatif), revenir directement à l'étape 2 (sélection)\n      if (this.currentStep === 3) {\n        this.currentStep = 2;\n        this.backToSelection();\n        return;\n      }\n      \n      this.currentStep--;\n      this.updateStepInterface();\n    }\n  }\n\n  /**\n   * Met à jour l'interface après changement d'étape\n   */\n  private updateStepInterface(): void {\n    this.clearMessages();\n    if (this.currentStep === 2) {\n      this.loadAvailableProducts();\n      this.pagination.currentPage = 1;\n    }\n  }\n\n  /**\n   * Retourne à la sélection des produits\n   */\n  backToSelection(): void {\n    this.tempSelectedProducts = [...this.confirmedSelectedProducts];\n    this.confirmedSelectedProducts = [];\n    this.currentStep = 2;\n  }\n\n  /**\n   * Génère la configuration finale\n   */\n  private generateFinalConfiguration(): void {\n    // Initialiser tous les panneaux de détails de produits comme étendus\n    this.confirmedSelectedProducts.forEach(selection => {\n      this.expandedProductDetails.add(selection.product.productid);\n    });\n    \n    // Initialiser l'expansion des produits principaux dans le tableau\n    this.confirmedSelectedProducts\n      .filter(s => s.selectionSource === 'MAIN_LIST')\n      .forEach(s => this.expandedTableRows.add(s.product.productid));\n    const mainProductsPrice = this.confirmedSelectedProducts\n      .filter(s => s.selectionSource === 'MAIN_LIST')\n      .reduce((sum, s) => sum + ((s.product.price || 0) * s.quantity), 0);\n\n    this.finalConfiguration = {\n      selectedCategory: this.selectedCategory,\n      selectedFamilies: this.selectedFamilyIds.map(id => \n        this.carFamilies.find(f => f.id === id)\n      ).filter(f => f !== undefined),\n      selectedProducts: this.confirmedSelectedProducts.map(s => ({\n        product: s.product,\n        quantity: s.quantity,\n        subtotal: (s.product.price || 0) * s.quantity,\n        selectionSource: s.selectionSource,\n        level: this.getProductLevel(s.product.productid),\n        hierarchyPath: this.getProductHierarchyPath(s.product.productid)\n      })),\n      selectedProperties: this.selectedProperties.filter(ps => ps.selected),\n      priceBreakdown: {\n        mainProducts: mainProductsPrice,\n        properties: this.propertiesPrice,\n        total: this.totalPrice\n      },\n      totalPrice: this.totalPrice,\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  /**\n   * Obtient le niveau hiérarchique d'un produit\n   */\n  getProductLevel(productId: string): number {\n    const product = this.allProducts.find(p => p.productid === productId);\n    if (!product) return 0;\n    \n    let level = 0;\n    let currentProduct = product;\n    \n    while (currentProduct.parentproductid) {\n      level++;\n      const parent = this.allProducts.find(p => p.productid === currentProduct.parentproductid);\n      if (!parent) break;\n      currentProduct = parent;\n    }\n    \n    return level;\n  }\n\n  /**\n   * Obtient le chemin hiérarchique d'un produit\n   */\n  private getProductHierarchyPath(productId: string): Product[] {\n    const path: Product[] = [];\n    const product = this.allProducts.find(p => p.productid === productId);\n    \n    if (!product) return path;\n    \n    let currentProduct = product;\n    path.unshift(currentProduct);\n    \n    while (currentProduct.parentproductid) {\n      const parent = this.allProducts.find(p => p.productid === currentProduct.parentproductid);\n      if (!parent) break;\n      path.unshift(parent);\n      currentProduct = parent;\n    }\n    \n    return path;\n  }\n\n  /**\n   * Vérifie si la configuration est valide pour générer un devis\n   */\n  validatePropertiesSelection(): boolean {\n    return this.areAllRequiredPropertiesSelected();\n  }\n\n  /**\n   * Définit un message d'erreur\n   */\n  private setErrorMessage(message: string): void {\n    this.errorMessage = message;\n    this.successMessage = '';\n    setTimeout(() => this.clearMessages(), 5000);\n  }\n\n  /**\n   * Définit un message de succès\n   */\n  private setSuccessMessage(message: string): void {\n    this.successMessage = message;\n    this.errorMessage = '';\n    setTimeout(() => this.clearMessages(), 3000);\n  }\n\n  // Méthodes pour la validation\n  areAllRequiredPropertiesSelected(): boolean {\n    // Vérifier uniquement les produits confirmés qui ont des propriétés requises\n    for (const selection of this.confirmedSelectedProducts) {\n      const productId = selection.product.productid;\n      const productRequiredProperties = this.getAvailablePropertiesForProduct(productId)\n        .filter(prop => prop.isrequired);\n      \n      // Si ce produit n'a pas de propriétés requises, passer au suivant\n      if (productRequiredProperties.length === 0) {\n        continue;\n      }\n      \n      // Vérifier si toutes les propriétés requises de ce produit sont sélectionnées\n      for (const requiredProp of productRequiredProperties) {\n        const isSelected = this.selectedProperties.some(\n          ps => ps.productid === productId && \n                ps.propertyid === requiredProp.propertyid && \n                ps.selected\n        );\n        \n        if (!isSelected) {\n          return false;\n        }\n      }\n    }\n    \n    return true;\n  }\n\n  getMissingRequiredProperties(): any[] {\n    const missingProperties: any[] = [];\n    \n    // Vérifier uniquement les produits confirmés\n    for (const selection of this.confirmedSelectedProducts) {\n      const productId = selection.product.productid;\n      const productRequiredProperties = this.getAvailablePropertiesForProduct(productId)\n        .filter(prop => prop.isrequired);\n      \n      // Pour chaque propriété requise de ce produit\n      for (const requiredProp of productRequiredProperties) {\n        const isSelected = this.selectedProperties.some(\n          ps => ps.productid === productId && \n                ps.propertyid === requiredProp.propertyid && \n                ps.selected\n        );\n        \n        if (!isSelected) {\n          missingProperties.push({\n            ...requiredProp,\n            productName: selection.product.name\n          });\n        }\n      }\n    }\n    \n    return missingProperties;\n  }\n\n  // Méthodes pour les panneaux\n  togglePricePanel(): void {\n    this.isPricePanelExpanded = !this.isPricePanelExpanded;\n  }\n\n  // Méthode pour la configuration\n  resetConfiguration(): void {\n    // Réinitialiser toutes les sélections\n    this.selectedFamilyIds = [];\n    this.tempSelectedProducts = [];\n    this.confirmedSelectedProducts = [];\n    this.selectedProperties = [];\n    \n    // Réinitialiser les filtres et la pagination\n    this.productFilters = {\n      searchTerm: '',\n      familyFilter: 'all',\n      typeFilter: 'all'\n    };\n    this.pagination = {\n      currentPage: 1,\n      itemsPerPage: 25,\n      totalItems: 0,\n      availablePageSizes: [25, 50, 75, 0]\n    };\n    \n    // Réinitialiser les états\n    this.expandedProducts = new Map<string, boolean>();\n    this.totalPrice = 0;\n    this.propertiesPrice = 0;\n    \n    // Revenir à l'étape 1\n    this.currentStep = 1;\n  }\n\n  /**\n   * Vérifie si un produit a des enfants sélectionnés\n   */\n  hasChildrenSelected(product: any): boolean {\n    if (!product || !product.productid) return false;\n    const allSelected = [...this.tempSelectedProducts, ...this.confirmedSelectedProducts];\n    const descendants = this.getAllDescendants(product.productid);\n    return descendants.some(descendantId => \n      allSelected.some(sel => sel.product.productid === descendantId)\n    );\n  }\n\n  /**\n   * Affiche le modal de conflit parent-enfant\n   */\n  showParentChildConflictModal(product: any): void {\n    console.log('Conflit détecté pour le produit:', product.name);\n    this.conflictProduct = product;\n    \n    // Obtenir la liste des produits enfants sélectionnés pour l'affichage dans le modal\n    const descendants = this.getAllDescendants(product.productid);\n    const allSelected = [...this.tempSelectedProducts, ...this.confirmedSelectedProducts];\n    this.selectedChildrenProducts = allSelected\n      .filter(sel => descendants.includes(sel.product.productid));\n  }\n}", "<div class=\"container-fluid\">\r\n  <!-- Indicateur de chargement -->\r\n  <div *ngIf=\"isLoading\" class=\"loading-overlay\">\r\n    <div class=\"spinner-border text-primary\" role=\"status\">\r\n      <span class=\"visually-hidden\">Chargement...</span>\r\n    </div>\r\n  </div>\r\n  \r\n  <!-- En-tête fixe sans animation -->\r\n  <div class=\"row\">\r\n    <div class=\"col-12\">\r\n      <div class=\"cpq-header\">\r\n        <div class=\"card-body\">\r\n          <div class=\"d-flex justify-content-between align-items-center\">\r\n            <div>\r\n              <h2 class=\"mb-1\">\r\n                <i class=\"bi bi-gear-fill me-2\"></i>\r\n                Configurateur CPQ\r\n              </h2>\r\n              <p class=\"text-muted mb-0\">Configurez votre devis personnalisé</p>\r\n            </div>\r\n            <div class=\"text-end\">\r\n              <span class=\"badge bg-primary fs-6\">Étape {{ currentStep }}</span>\r\n              <div class=\"text-muted small mt-1\">Étape {{ currentStep }}/{{ maxSteps }}</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- Barre de progression -->\r\n          <div class=\"progress mt-3\">\r\n            <div \r\n              class=\"progress-bar\" \r\n              role=\"progressbar\" \r\n              [style.width.%]=\"getProgressPercentage()\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- ÉTAPE 1: Sélection des familles -->\r\n  <div class=\"row\" *ngIf=\"currentStep >= 1\">\r\n    <div class=\"col-12\">\r\n      <div class=\"cpq-card\">\r\n        <div class=\"card-header\">\r\n          <h4 class=\"mb-1\">\r\n            <i class=\"bi bi-collection-fill me-2\"></i>\r\n            Sélectionnez les familles de produits\r\n          </h4>\r\n          <p class=\"mb-0\">Choisissez une ou plusieurs familles dans la catégorie <strong class=\"text-white\">{{ selectedCategory.name }}</strong></p>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          \r\n          <div class=\"row g-4 px-4 py-4\">\r\n            <div \r\n              *ngFor=\"let family of carFamilies\" \r\n              class=\"col-lg-4 col-md-6\">\r\n              \r\n              <div \r\n                class=\"card family-card\"\r\n                [class.selected]=\"isFamilySelected(family.id)\"\r\n                [class.disabled]=\"currentStep > 1\"\r\n                (click)=\"currentStep === 1 && toggleFamilySelection(family.id)\">\r\n                \r\n                <div class=\"card-body\">\r\n                  <div class=\"d-flex align-items-start\">\r\n                    <div class=\"family-icon me-3\">\r\n                      <i [class]=\"family.icon\"></i>\r\n                    </div>\r\n                    <div class=\"flex-grow-1\">\r\n                      <h6 class=\"card-title\">{{ family.name }}</h6>\r\n                      <p class=\"card-text\">{{ family.description }}</p>\r\n                      \r\n                      <div class=\"d-flex justify-content-between align-items-center mt-3\">\r\n                        <span class=\"badge bg-secondary\">\r\n                          Famille de produits\r\n                        </span>\r\n                        <div class=\"form-check\">\r\n                          <input \r\n                            class=\"form-check-input\" \r\n                            type=\"checkbox\" \r\n                            [checked]=\"isFamilySelected(family.id)\"\r\n                            [disabled]=\"currentStep > 1\"\r\n                            (change)=\"currentStep === 1 && toggleFamilySelection(family.id)\">\r\n                          <label class=\"form-check-label\">\r\n                            Sélectionner\r\n                          </label>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- Résumé des sélections -->\r\n          <div *ngIf=\"selectedFamilyIds.length > 0\" class=\"mt-4\">\r\n            <div class=\"alert alert-success\">\r\n              <i class=\"bi bi-check-circle me-2\"></i>\r\n              <strong>{{ selectedFamilyIds.length }}</strong> famille(s) sélectionnée(s) :\r\n              <span *ngFor=\"let familyId of selectedFamilyIds; let last = last\">\r\n                <strong>{{ getFamilyName(familyId) }}</strong><span *ngIf=\"!last\">, </span>\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"card-footer\" *ngIf=\"selectedFamilyIds.length > 0 && currentStep === 1\">\r\n          <div class=\"d-flex justify-content-end\">\r\n            <button \r\n              class=\"btn btn-primary btn-lg\"\r\n              (click)=\"nextStep()\">\r\n              Voir les produits ({{ selectedFamilyIds.length }})\r\n              <i class=\"bi bi-arrow-right ms-2\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Séparateur -->\r\n  <div class=\"step-separator text-center my-4\" *ngIf=\"currentStep > 1\">\r\n    <i class=\"bi bi-arrow-down-circle-fill fs-1 text-primary\"></i>\r\n  </div>\r\n\r\n    <!-- ÉTAPE 2: Sélection des produits -->\r\n  <div class=\"card mb-4\" *ngIf=\"currentStep >= 2\">\r\n    <div class=\"card-header\">\r\n      <h3><i class=\"bi bi-box-seam\"></i> Sélection des produits</h3>\r\n      <p class=\"mb-0\">Choisissez les produits et leurs quantités</p>\r\n    </div>\r\n    \r\n    <div class=\"card-body\">\r\n      <!-- Message d'erreur -->\r\n      <div class=\"alert alert-danger\" *ngIf=\"errorMessage\">\r\n        <i class=\"bi bi-exclamation-triangle\"></i> {{ errorMessage }}\r\n      </div>\r\n\r\n      <!-- Message de succès -->\r\n      <div class=\"alert alert-success\" *ngIf=\"successMessage\">\r\n        <i class=\"bi bi-check-circle\"></i> {{ successMessage }}\r\n      </div>\r\n\r\n      <!-- Visible à l'étape 2 et au-delà -->\r\n      <div *ngIf=\"currentStep >= 2\">\r\n        \r\n        <!-- CARD PRODUITS DISPONIBLES -->\r\n        <div class=\"card product-selection-card mb-4\">\r\n          <div class=\"card-header bg-info text-white\">\r\n            <div class=\"d-flex justify-content-between align-items-center\">\r\n              <div>\r\n                <h5 class=\"mb-0\">\r\n                  <i class=\"bi bi-search me-2\"></i>\r\n                  Produits disponibles\r\n                </h5>\r\n                <small>Sélectionnez les produits que vous souhaitez ajouter</small>\r\n              </div>\r\n              <div class=\"d-flex align-items-center gap-2\">\r\n                <span class=\"badge bg-light text-dark\">\r\n                  {{ pagination.totalItems }} produit(s)\r\n                </span>\r\n                <button \r\n                  class=\"btn btn-sm btn-outline-light\"\r\n                  (click)=\"clearAllFilters()\"\r\n                  *ngIf=\"productFilters.searchTerm || productFilters.familyFilter !== 'all' || productFilters.typeFilter !== 'all'\">\r\n                  <i class=\"bi bi-x-circle\"></i> Effacer filtres\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"card-body\">\r\n            <!-- Barre de recherche et filtres -->\r\n            <div class=\"search-filters-section mb-4\">\r\n              <div class=\"row g-3\">\r\n                <!-- Barre de recherche -->\r\n                <div class=\"col-md-4\">\r\n                  <div class=\"search-input-group position-relative\">\r\n                    <span class=\"position-absolute top-50 start-0 translate-middle-y ms-3\">\r\n                      <i class=\"bi bi-search\"></i>\r\n                    </span>\r\n                    <input \r\n                      type=\"text\" \r\n                      class=\"form-control search-input ps-5\" \r\n                      placeholder=\"Rechercher un produit...\"\r\n                      [(ngModel)]=\"productFilters.searchTerm\"\r\n                      (input)=\"onFilterChange()\">\r\n                    <button \r\n                      class=\"btn btn-outline-secondary clear-search-btn position-absolute top-50 end-0 translate-middle-y me-2\"\r\n                      *ngIf=\"productFilters.searchTerm\"\r\n                      (click)=\"productFilters.searchTerm = ''; onFilterChange()\">\r\n                      <i class=\"bi bi-x\"></i>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Filtre par famille -->\r\n                <div class=\"col-md-3\">\r\n                  <select \r\n                    class=\"form-select filter-select\"\r\n                    [(ngModel)]=\"productFilters.familyFilter\"\r\n                    (change)=\"onFilterChange()\">\r\n                    <option value=\"all\">Toutes les familles</option>\r\n                    <option \r\n                      *ngFor=\"let family of getAvailableFamiliesForFilter()\" \r\n                      [value]=\"family.id\">\r\n                      {{ family.name }}\r\n                    </option>\r\n                  </select>\r\n                </div>\r\n\r\n                <!-- Filtre par type -->\r\n                <div class=\"col-md-3\">\r\n                  <select \r\n                    class=\"form-select filter-select\"\r\n                    [(ngModel)]=\"productFilters.typeFilter\"\r\n                    (change)=\"onFilterChange()\">\r\n                    <option value=\"all\">Tous les types</option>\r\n                    <option value=\"main\">Produits principaux</option>\r\n                    <option value=\"option\">Options</option>\r\n                  </select>\r\n                </div>\r\n\r\n                <!-- Pagination par page -->\r\n                <div class=\"col-md-2\">\r\n                  <select \r\n                    class=\"form-select pagination-select\"\r\n                    [(ngModel)]=\"pagination.itemsPerPage\"\r\n                    (change)=\"onFilterChange()\">\r\n                    <option [value]=\"25\">25 par page</option>\r\n                    <option [value]=\"50\">50 par page</option>\r\n                    <option [value]=\"75\">75 par page</option>\r\n                    <option [value]=\"0\">Tout afficher</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Liste des produits disponibles -->\r\n            <div class=\"products-grid\">\r\n              <div class=\"row g-3\">\r\n                <div class=\"col-lg-6 col-xl-4 mb-3\" *ngFor=\"let product of filteredProducts\">\r\n                  <div class=\"card product-card h-100\" \r\n                       [class.temp-selected]=\"isTempProductSelected(product)\"\r\n                       [class.confirmed]=\"isProductConfirmed(product)\">\r\n                    <div class=\"card-body d-flex flex-column\">\r\n                      <div class=\"d-flex justify-content-between align-items-start mb-2\">\r\n                        <div class=\"product-icon\">\r\n                          <i class=\"bi bi-box-seam fs-2\"></i>\r\n                        </div>\r\n                        <div class=\"product-status-indicators\">\r\n                          <span class=\"badge bg-success me-1\" *ngIf=\"isProductConfirmed(product)\">\r\n                            <i class=\"bi bi-check-circle\"></i> Confirmé\r\n                          </span>\r\n                          <span class=\"badge bg-warning\" *ngIf=\"isTempProductSelected(product) && !isProductConfirmed(product)\">\r\n                            <i class=\"bi bi-clock\"></i> Sélectionné\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                      \r\n                      <h6 class=\"card-title fw-bold\">{{ product.name }}</h6>\r\n                      <p class=\"card-text text-muted small mb-1\">\r\n                        <i class=\"bi bi-hash\"></i> {{ product.productid }}\r\n                      </p>\r\n                      <p class=\"card-text flex-grow-1 product-description\">{{ product.description }}</p>\r\n                      \r\n                      <div class=\"mt-auto\">\r\n                        <div class=\"d-flex justify-content-between align-items-center mb-2\">\r\n                          <span class=\"badge product-type-badge\" \r\n                                [ngClass]=\"{\r\n                                  'bg-primary': !product.parentproductid,\r\n                                  'bg-info': product.parentproductid\r\n                                }\">\r\n                            {{ product.parentproductid ? 'Option' : 'Principal' }}\r\n                          </span>\r\n                          <strong class=\"text-success fs-6 product-price\">{{ formatPrice((product.price || 0) + getPropertiesPriceForProduct(product.productid)) }}</strong>\r\n                        </div>\r\n                        \r\n                        <div class=\"d-grid gap-2\">\r\n                          <button \r\n                            class=\"btn btn-sm product-action-btn\"\r\n                            [ngClass]=\"{\r\n                              'btn-outline-primary': !isTempProductSelected(product) && !isProductConfirmed(product),\r\n                              'btn-warning': isTempProductSelected(product) && !isProductConfirmed(product),\r\n                              'btn-success': isProductConfirmed(product)\r\n                            }\"\r\n                            (click)=\"toggleTempProductSelection(product)\"\r\n                            [disabled]=\"isProductConfirmed(product) || currentStep > 2\">\r\n                            <i class=\"bi\" \r\n                               [ngClass]=\"{\r\n                                 'bi-plus-lg': !isTempProductSelected(product) && !isProductConfirmed(product),\r\n                                 'bi-dash-lg': isTempProductSelected(product) && !isProductConfirmed(product),\r\n                                 'bi-check-lg': isProductConfirmed(product)\r\n                               }\"></i>\r\n                            <span *ngIf=\"!isTempProductSelected(product) && !isProductConfirmed(product)\">Sélectionner</span>\r\n                            <span *ngIf=\"isTempProductSelected(product) && !isProductConfirmed(product)\">Désélectionner</span>\r\n                            <span *ngIf=\"isProductConfirmed(product)\">Confirmé</span>\r\n                          </button>\r\n                          \r\n                          <!-- Bouton pour voir les produits associés -->\r\n                          <button \r\n                            *ngIf=\"hasDirectChildren(product.productid)\"\r\n                            class=\"btn btn-sm btn-outline-info mt-2 w-100\"\r\n                            (click)=\"showAssociatedProducts(product)\">\r\n                            <i class=\"bi bi-diagram-3 me-1\"></i> Voir les produits associés\r\n                          </button>\r\n                          \r\n                          <!-- Nous supprimons l'accordéon car nous utiliserons un modal à la place -->\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Message si aucun produit -->\r\n              <div *ngIf=\"filteredProducts.length === 0\" class=\"empty-state\">\r\n                <div class=\"empty-state-icon\">\r\n                  <i class=\"bi bi-inbox\"></i>\r\n                </div>\r\n                <div class=\"empty-state-title\">Aucun produit trouvé</div>\r\n                <div class=\"empty-state-text\">\r\n                  Essayez de modifier vos critères de recherche ou vos filtres\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Pagination -->\r\n            <div class=\"pagination-section mt-4\" *ngIf=\"getTotalPages() > 1\">\r\n              <nav aria-label=\"Navigation des produits\">\r\n                <ul class=\"pagination justify-content-center\">\r\n                  <li class=\"page-item\" [class.disabled]=\"pagination.currentPage === 1\">\r\n                    <button class=\"page-link\" (click)=\"onPageChange(pagination.currentPage - 1)\">\r\n                      <i class=\"bi bi-chevron-left\"></i>\r\n                    </button>\r\n                  </li>\r\n                  \r\n                  <li class=\"page-item\" \r\n                      *ngFor=\"let page of getPageNumbers()\"\r\n                      [class.active]=\"page === pagination.currentPage\"\r\n                      [class.disabled]=\"page === -1\">\r\n                    <button class=\"page-link\" \r\n                            *ngIf=\"page !== -1\"\r\n                            (click)=\"onPageChange(page)\">\r\n                      {{ page }}\r\n                    </button>\r\n                    <span class=\"page-link\" *ngIf=\"page === -1\">\r\n                                            ...\r\n                    </span>\r\n                  </li>\r\n                  \r\n                  <li class=\"page-item\" [class.disabled]=\"pagination.currentPage === getTotalPages()\">\r\n                    <button class=\"page-link\" (click)=\"onPageChange(pagination.currentPage + 1)\">\r\n                      <i class=\"bi bi-chevron-right\"></i>\r\n                    </button>\r\n                  </li>\r\n                </ul>\r\n              </nav>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- La carte \"Sélection temporaire\" a été supprimée -->\r\n\r\n        <!-- CARD PANIER -->\r\n        <div class=\"card confirmed-products-card mb-4\">\r\n          <div class=\"card-header bg-success text-white\">\r\n            <div class=\"d-flex justify-content-between align-items-center\">\r\n              <div>\r\n                <h5 class=\"mb-0\">\r\n                  <i class=\"bi bi-cart-check me-2\"></i>\r\n                  Panier\r\n                </h5>\r\n                <small>Gérez vos produits sélectionnés, leurs propriétés et quantités</small>\r\n              </div>\r\n              <div class=\"d-flex align-items-center gap-2\">\r\n                <span class=\"badge bg-light text-dark\">\r\n                  {{ confirmedSelectedProducts.length }} produit(s)\r\n                </span>\r\n                <button \r\n                  class=\"btn btn-sm btn-outline-light\"\r\n                  (click)=\"toggleAllProductsExpansion()\"\r\n                  *ngIf=\"confirmedSelectedProducts.length > 0\">\r\n                  <i class=\"bi\" [ngClass]=\"{\r\n                    'bi-arrows-expand': expandedProducts.size === 0,\r\n                    'bi-arrows-collapse': expandedProducts.size > 0\r\n                  }\"></i>\r\n                  {{ expandedProducts.size === 0 ? 'Tout étendre' : 'Tout réduire' }}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"card-body\">\r\n            <div *ngIf=\"confirmedSelectedProducts.length === 0\" class=\"empty-state\">\r\n              <div class=\"empty-state-icon\">\r\n                <i class=\"bi bi-cart-x\"></i>\r\n              </div>\r\n              <div class=\"empty-state-title\">Votre panier est vide</div>\r\n              <div class=\"empty-state-text\">Sélectionnez des produits pour les ajouter à votre panier</div>\r\n            </div>\r\n\r\n            <div *ngIf=\"confirmedSelectedProducts.length > 0\">\r\n              <!-- Utilisation du composant récursif pour chaque produit dans le panier -->\r\n              <div *ngFor=\"let selection of confirmedSelectedProducts; let i = index\" class=\"mb-3\">\r\n                <app-product-tree\r\n                  [product]=\"selection.product\"\r\n                  [level]=\"0\"\r\n                  [isSelected]=\"true\"\r\n                  [isMainProduct]=\"true\"\r\n                  [isIncluded]=\"false\"\r\n                  [isExpanded]=\"isProductExpanded(selection.product.productid)\"\r\n                  [quantity]=\"selection.quantity\"\r\n                  [directChildren]=\"[]\"\r\n                  [hasChildren]=\"hasDirectChildren(selection.product.productid)\"\r\n                  [hasSelectedProperties]=\"hasProductProperties(selection.product.productid)\"\r\n                  [selectedPropertiesCount]=\"getSelectedPropertiesCountForProduct(selection.product.productid)\"\r\n                  [freeProperties]=\"getProductFreeProperties(selection.product.productid)\"\r\n                  [paidProperties]=\"getProductPaidProperties(selection.product.productid)\"\r\n                  [requiredProperties]=\"getProductRequiredProperties(selection.product.productid)\"\r\n                  [isLocked]=\"currentStep > 2\"\r\n                  (productToggle)=\"removeConfirmedProduct($event)\"\r\n                  (productInclusionToggle)=\"toggleProductInclusion($event)\"\r\n                  (quantityUpdate)=\"updateProductQuantity($event.productId, $event.event)\"\r\n                  (quantityIncrease)=\"increaseQuantity($event)\"\r\n                  (quantityDecrease)=\"decreaseQuantity($event)\"\r\n                  (expansionToggle)=\"toggleProductExpansion($event)\"\r\n                  (editProperties)=\"openPropertiesModal($event)\">\r\n                </app-product-tree>\r\n                \r\n                <hr *ngIf=\"i < confirmedSelectedProducts.length - 1\">\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"card-footer\" *ngIf=\"confirmedSelectedProducts.length > 0\">\r\n            <div class=\"d-flex justify-content-between align-items-center\">\r\n              <div class=\"confirmed-products-summary\">\r\n                <span class=\"text-muted\">\r\n                  {{ confirmedSelectedProducts.length }} produit(s) • \r\n                  {{ getTotalConfirmedQuantity() }} article(s) • \r\n                  <strong class=\"text-success\">{{ formatPrice(totalPrice) }}</strong>\r\n                </span>\r\n              </div>\r\n              <div class=\"d-flex gap-2\">\r\n                <button \r\n                  class=\"btn btn-outline-danger btn-sm\" \r\n                  (click)=\"clearAllConfirmedProducts()\"\r\n                  [disabled]=\"currentStep > 2\">\r\n                  <i class=\"bi bi-trash\"></i> Tout supprimer\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Actions de navigation -->\r\n        <div class=\"step-navigation-actions\">\r\n          <div class=\"d-flex justify-content-between align-items-center\">\r\n            <button \r\n              class=\"btn btn-outline-secondary\" \r\n              (click)=\"previousStep()\"\r\n              *ngIf=\"currentStep === 2\">\r\n              <i class=\"bi bi-arrow-left me-2\"></i> Retour aux familles\r\n            </button>\r\n            <div *ngIf=\"currentStep > 2\"></div> <!-- Espace vide pour maintenir l'alignement -->\r\n            \r\n            <div class=\"step-info d-flex align-items-center gap-3\">\r\n              <div class=\"selection-summary\">\r\n                <span class=\"badge bg-info me-2\">{{ confirmedSelectedProducts.length }} produits</span>\r\n                <span class=\"text-muted\">Total: <strong>{{ formatPrice(totalPrice) }}</strong></span>\r\n              </div>\r\n              \r\n              <button \r\n                class=\"btn btn-primary btn-lg\"\r\n                [disabled]=\"confirmedSelectedProducts.length === 0\"\r\n                (click)=\"nextStep()\"\r\n                *ngIf=\"currentStep === 2\">\r\n                Suivant\r\n                <i class=\"bi bi-arrow-right ms-2\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n\r\n\r\n  <!-- Alerte pour les propriétés requises non sélectionnées (affichée à l'étape 2) -->\r\n  <div *ngIf=\"currentStep === 2 && !areAllRequiredPropertiesSelected() && getMissingRequiredProperties().length > 0\" class=\"alert alert-warning mb-4\">\r\n    <div class=\"d-flex align-items-center\">\r\n      <i class=\"bi bi-exclamation-triangle-fill fs-4 me-2\"></i>\r\n      <div>\r\n        <h5 class=\"mb-1\">Propriétés requises manquantes</h5>\r\n        <p class=\"mb-0\">Certaines propriétés requises n'ont pas été sélectionnées. Utilisez le bouton \"Propriétés\" sur chaque produit pour les configurer.</p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Séparateur -->\r\n  <div class=\"step-separator text-center my-4\" *ngIf=\"currentStep > 2\">\r\n    <i class=\"bi bi-arrow-down-circle-fill fs-1 text-primary\"></i>\r\n  </div>\r\n\r\n    <!-- ÉTAPE 3: Récapitulatif -->\r\n  <div class=\"card mb-4\" *ngIf=\"currentStep >= 3\">\r\n    <div class=\"card-header\">\r\n      <h3><i class=\"bi bi-clipboard-check\"></i> Récapitulatif</h3>\r\n      <p class=\"mb-0\">Vérifiez votre configuration avant de générer le devis</p>\r\n    </div>\r\n    \r\n    <div class=\"card-body\">\r\n      <!-- Résumé des familles -->\r\n      <div class=\"mb-4\">\r\n        <h5>Familles sélectionnées</h5>\r\n        <div class=\"d-flex flex-wrap gap-2\">\r\n          <span \r\n            *ngFor=\"let familyId of selectedFamilyIds\" \r\n            class=\"badge bg-primary fs-6\">\r\n            {{ getFamilyName(familyId) }}\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Récapitulatif des prix -->\r\n      <div class=\"mb-4\">\r\n        <div id=\"price-summary-card\" class=\"card bg-light\">\r\n          <div class=\"card-header bg-dark text-white d-flex justify-content-between align-items-center\">\r\n            <h5 class=\"mb-0\"><i class=\"bi bi-calculator me-2\"></i>Récapitulatif des prix</h5>\r\n            <button class=\"btn btn-sm btn-outline-secondary\" (click)=\"togglePricePanel()\">\r\n              <i class=\"bi\" [ngClass]=\"isPricePanelExpanded ? 'bi-chevron-up' : 'bi-chevron-down'\"></i>\r\n            </button>\r\n          </div>\r\n          <div class=\"card-body\" *ngIf=\"isPricePanelExpanded\">\r\n            <div class=\"row\">\r\n              <div class=\"col-md-8\">\r\n                <div class=\"price-breakdown-details\">\r\n                  <div class=\"d-flex justify-content-between py-2 border-bottom\">\r\n                    <span><i class=\"bi bi-folder me-2\"></i>Produits de base:</span>\r\n                    <span class=\"fw-bold\">{{ formatPrice(getMainProductsTotal()) }}</span>\r\n                  </div>\r\n                  <div class=\"d-flex justify-content-between py-2 border-bottom\">\r\n                    <span><i class=\"bi bi-gear me-2\"></i>personnalisations:</span>\r\n                    <span class=\"fw-bold\">{{ formatPrice(propertiesPrice) }}</span>\r\n                  </div>\r\n                  <div class=\"d-flex justify-content-between py-2 border-bottom\" *ngIf=\"getOptionsTotal() > 0\">\r\n                    <span><i class=\"bi bi-wrench me-2\"></i>Options:</span>\r\n                    <span class=\"fw-bold\">{{ formatPrice(getOptionsTotal()) }}</span>\r\n                  </div>\r\n                  <div class=\"d-flex justify-content-between py-2\">\r\n                    <span><i class=\"bi bi-calculator me-2\"></i>Quantité totale:</span>\r\n                    <span class=\"fw-bold\">{{ getTotalConfirmedQuantity() }} article(s)</span>\r\n                  </div>\r\n                </div>\r\n                \r\n                <!-- TreeTable pour les produits et leurs options -->\r\n                <div class=\"mt-4\">\r\n                  <h6 class=\"mb-3\">Détail des produits et options</h6>\r\n                  <table class=\"table table-sm table-bordered tree-table\">\r\n                    <thead class=\"table-light\">\r\n                      <tr>\r\n                        <th>Produit</th>\r\n                        <th>Quantité</th>\r\n                        <th>Prix unitaire</th>\r\n                        <th>Options</th>\r\n                        <th>Total</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      <!-- Produits organisés hiérarchiquement -->\r\n                      <ng-container *ngFor=\"let item of getHierarchicalProductsForTable()\">\r\n                        <tr [class]=\"'level-' + item.level\">\r\n                          <td>\r\n                            <div class=\"d-flex align-items-center\">\r\n                              <span [style.paddingLeft.px]=\"item.level * 20\">\r\n                                <!-- Flèche d'expansion si le produit a des enfants -->\r\n                                <i *ngIf=\"item.hasChildren\" \r\n                                   class=\"bi expand-icon me-1\" \r\n                                   [ngClass]=\"item.expanded ? 'bi-chevron-down' : 'bi-chevron-right'\"\r\n                                   (click)=\"toggleTableRowExpansion(item.selection.product.productid)\"></i>\r\n                                <!-- Espace pour aligner les produits sans enfants -->\r\n                                <span *ngIf=\"!item.hasChildren\" class=\"expand-placeholder me-1\">&nbsp;&nbsp;</span>\r\n                                \r\n                                <i class=\"bi\" [ngClass]=\"{\r\n                                  'bi-folder-fill': item.selection.selectionSource === 'MAIN_LIST',\r\n                                  'bi-gear-fill': item.selection.selectionSource === 'TREE_INCLUSION'\r\n                                }\"></i>\r\n                                {{ item.selection.product.name }}\r\n                              </span>\r\n                            </div>\r\n                          </td>\r\n                          <td class=\"text-center\">{{ item.selection.quantity }}</td>\r\n                          <td class=\"text-end\">{{ formatPrice(item.selection.product.price || 0) }}</td>\r\n                          <td class=\"text-end\">\r\n                            {{ formatPrice(getPropertiesPriceForProduct(item.selection.product.productid)) }}\r\n                          </td>\r\n                          <td class=\"text-end fw-bold\">\r\n                            {{ formatPrice((item.selection.product.price || 0) * item.selection.quantity + \r\n                               getPropertiesPriceForProduct(item.selection.product.productid) * item.selection.quantity + \r\n                               (item.childrenPrice || 0)) }}\r\n                          </td>\r\n                        </tr>\r\n                      </ng-container>\r\n                      <!-- Ligne de total -->\r\n                      <tr class=\"table-success\">\r\n                        <td colspan=\"4\" class=\"text-end fw-bold\">Total général:</td>\r\n                        <td class=\"text-end fw-bold\">{{ formatPrice(totalPrice) }}</td>\r\n                      </tr>\r\n                    </tbody>\r\n                  </table>\r\n                </div>\r\n              </div>\r\n              <div class=\"col-md-4\">\r\n                <div class=\"final-total-box\">\r\n                  <div class=\"d-flex justify-content-between align-items-center mb-2\">\r\n                    <strong class=\"fs-4\">Total général:</strong>\r\n                    <strong class=\"text-success fs-3\">{{ formatPrice(totalPrice) }}</strong>\r\n                  </div>\r\n                  <div class=\"text-center\">\r\n                    <small class=\"text-muted\">TVA incluse • {{ confirmedSelectedProducts.length }} produit(s)</small>\r\n                  </div>\r\n                  <div class=\"text-center mt-2\">\r\n                    <small class=\"badge bg-info\">Configuration validée</small>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Actions finales -->\r\n      <div class=\"d-flex justify-content-between\">\r\n        <button class=\"btn btn-outline-secondary\" (click)=\"previousStep()\" *ngIf=\"currentStep === 3\">\r\n          <i class=\"bi bi-arrow-left\"></i> Retour à la sélection\r\n        </button>\r\n        <div *ngIf=\"currentStep > 3\"></div> <!-- Espace vide pour maintenir l'alignement -->\r\n        <div class=\"d-flex gap-2\">\r\n          <button class=\"btn btn-outline-danger\" (click)=\"showResetConfirmation()\">\r\n            <i class=\"bi bi-arrow-counterclockwise\"></i> Réinitialiser\r\n          </button>\r\n          <button class=\"btn btn-outline-info download-btn\" (click)=\"exportPriceSummaryToPdf()\">\r\n            <i class=\"bi bi-file-pdf\"></i> Télécharger en PDF\r\n          </button>\r\n          <button class=\"btn btn-outline-primary\">\r\n            <i class=\"bi bi-save\"></i> Sauvegarder le brouillon\r\n          </button>\r\n          <button class=\"btn btn-success btn-lg\">\r\n            <i class=\"bi bi-file-earmark-text\"></i> Générer le devis final\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n    <!-- Modal de confirmation pour les conflits parent-enfant -->\r\n    <div class=\"modal fade\" id=\"parentChildConflictModal\" tabindex=\"-1\" aria-labelledby=\"conflictModalLabel\" aria-hidden=\"true\">\r\n      <div class=\"modal-dialog modal-dialog-centered\">\r\n        <div class=\"modal-content\">\r\n          <div class=\"modal-header bg-warning text-dark\">\r\n            <h5 class=\"modal-title\" id=\"conflictModalLabel\">\r\n              <i class=\"bi bi-exclamation-triangle me-2\"></i>\r\n              Conflit de hiérarchie détecté\r\n            </h5>\r\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Fermer\"></button>\r\n          </div>\r\n          <div class=\"modal-body\">\r\n            <p>\r\n              Vous tentez de sélectionner <strong>{{ conflictProduct?.name }}</strong> qui fait partie d'une hiérarchie de produits.\r\n            </p>\r\n            <div class=\"alert alert-info\">\r\n              <i class=\"bi bi-info-circle me-2\"></i>\r\n              En confirmant, tous les produits enfants de cette hiérarchie seront désélectionnés et masqués.\r\n            </div>\r\n            <div *ngIf=\"selectedChildrenProducts.length > 0\" class=\"mt-3\">\r\n              <h6 class=\"text-danger\"><i class=\"bi bi-exclamation-circle me-2\"></i>Produits qui seront désélectionnés :</h6>\r\n              <ul class=\"list-group mt-2\">\r\n                <li *ngFor=\"let selection of selectedChildrenProducts\" class=\"list-group-item list-group-item-danger\">\r\n                  <div class=\"d-flex justify-content-between align-items-center\">\r\n                    <div>\r\n                      <strong>{{ selection.product.name }}</strong>\r\n                      <small class=\"d-block text-muted\">{{ selection.product.productid }}</small>\r\n                    </div>\r\n                    <span class=\"badge bg-secondary\">{{ formatPrice(selection.product.price || 0) }}</span>\r\n                  </div>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n            <p class=\"mt-3 mb-0\">Voulez-vous continuer ?</p>\r\n          </div>\r\n          <div class=\"modal-footer\">\r\n            <button type=\"button\" class=\"btn btn-outline-secondary\" data-bs-dismiss=\"modal\">Annuler</button>\r\n            <button type=\"button\" class=\"btn btn-warning\" (click)=\"confirmParentChildConflict()\" data-bs-dismiss=\"modal\">\r\n              <i class=\"bi bi-check-circle me-2\"></i>\r\n              Confirmer et continuer\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- Modal pour éditer les propriétés d'un produit -->\r\n    <div class=\"modal fade\" id=\"propertiesModal\" tabindex=\"-1\" aria-labelledby=\"propertiesModalLabel\" aria-hidden=\"true\">\r\n      <div class=\"modal-dialog modal-dialog-centered modal-lg\">\r\n        <div class=\"modal-content\">\r\n          <div class=\"modal-header bg-primary text-white\">\r\n            <h5 class=\"modal-title\" id=\"propertiesModalLabel\">\r\n              <i class=\"bi bi-pencil-square me-2\"></i>\r\n              Propriétés du produit: {{ selectedProductForProperties?.name }}\r\n            </h5>\r\n            <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Fermer\"></button>\r\n          </div>\r\n          <div class=\"modal-body\">\r\n            <div *ngIf=\"selectedProductForProperties\">\r\n              <!-- Onglets pour les catégories de propriétés -->\r\n              <ul class=\"nav nav-tabs mb-3\" id=\"propertiesTabs\" role=\"tablist\">\r\n                <li class=\"nav-item\" role=\"presentation\">\r\n                  <button class=\"nav-link active\" id=\"free-tab\" data-bs-toggle=\"tab\" data-bs-target=\"#free-properties\" type=\"button\" role=\"tab\" aria-controls=\"free-properties\" aria-selected=\"true\">\r\n                    <i class=\"bi bi-gift me-1 text-success\"></i> Propriétés gratuites\r\n                  </button>\r\n                </li>\r\n                <li class=\"nav-item\" role=\"presentation\">\r\n                  <button class=\"nav-link\" id=\"paid-tab\" data-bs-toggle=\"tab\" data-bs-target=\"#paid-properties\" type=\"button\" role=\"tab\" aria-controls=\"paid-properties\" aria-selected=\"false\">\r\n                    <i class=\"bi bi-currency-euro me-1 text-warning\"></i> Propriétés payantes\r\n                  </button>\r\n                </li>\r\n                <li class=\"nav-item\" role=\"presentation\">\r\n                  <button class=\"nav-link\" id=\"required-tab\" data-bs-toggle=\"tab\" data-bs-target=\"#required-properties\" type=\"button\" role=\"tab\" aria-controls=\"required-properties\" aria-selected=\"false\">\r\n                    <i class=\"bi bi-asterisk me-1 text-danger\"></i> Propriétés requises\r\n                  </button>\r\n                </li>\r\n              </ul>\r\n              \r\n              <!-- Contenu des onglets -->\r\n              <div class=\"tab-content\" id=\"propertiesTabsContent\">\r\n                <!-- Propriétés gratuites -->\r\n                <div class=\"tab-pane fade show active\" id=\"free-properties\" role=\"tabpanel\" aria-labelledby=\"free-tab\">\r\n                  <div class=\"row g-3\">\r\n                    <div *ngFor=\"let propertySelection of getGroupedPropertiesForProduct(selectedProductForProperties.productid).free\" \r\n                         class=\"col-md-6\">\r\n                      <div class=\"card property-option-card h-100\"\r\n                           [class.selected]=\"propertySelection.selected\"\r\n                           [class.required-property]=\"propertySelection.property?.isrequired\"\r\n                           (click)=\"togglePropertySelection(selectedProductForProperties.productid, propertySelection.propertyid)\">\r\n                        <div class=\"card-body\">\r\n                          <div class=\"d-flex align-items-start\">\r\n                            <div class=\"property-option-icon me-3\">\r\n                              <i class=\"bi\" \r\n                                 [ngClass]=\"{\r\n                                   'bi-palette': propertySelection.property?.propertytype === 1,\r\n                                   'bi-circle': propertySelection.property?.propertytype === 2,\r\n                                   'bi-texture': propertySelection.property?.propertytype === 3,\r\n                                   'bi-star': propertySelection.property?.propertytype === 4\r\n                                 }\"></i>\r\n                            </div>\r\n                            <div class=\"flex-grow-1\">\r\n                              <h6 class=\"property-option-title\">\r\n                                {{ propertySelection.property?.name }}\r\n                                <span *ngIf=\"propertySelection.property?.isrequired\" class=\"required-indicator\">*</span>\r\n                              </h6>\r\n                              <p class=\"property-option-description small text-muted mb-2\">\r\n                                {{ propertySelection.property?.description }}\r\n                              </p>\r\n                              <div class=\"d-flex justify-content-between align-items-center\">\r\n                                <span class=\"badge bg-success\">Inclus</span>\r\n                                <div class=\"form-check\">\r\n                                  <input \r\n                                    class=\"form-check-input\" \r\n                                    type=\"radio\" \r\n                                    [name]=\"'modal-property-' + propertySelection.property?.propertytype + '-' + selectedProductForProperties.productid\"\r\n                                    [checked]=\"propertySelection.selected\"\r\n                                    (change)=\"togglePropertySelection(selectedProductForProperties.productid, propertySelection.propertyid)\">\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <!-- Message si aucune propriété gratuite -->\r\n                    <div *ngIf=\"getGroupedPropertiesForProduct(selectedProductForProperties.productid).free.length === 0\" class=\"col-12\">\r\n                      <div class=\"alert alert-info\">\r\n                        <i class=\"bi bi-info-circle me-2\"></i>\r\n                        Aucune propriété gratuite disponible pour ce produit.\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <!-- Propriétés payantes -->\r\n                <div class=\"tab-pane fade\" id=\"paid-properties\" role=\"tabpanel\" aria-labelledby=\"paid-tab\">\r\n                  <div class=\"row g-3\">\r\n                    <div *ngFor=\"let propertySelection of getGroupedPropertiesForProduct(selectedProductForProperties.productid).paid\" \r\n                         class=\"col-md-6\">\r\n                      <div class=\"card property-option-card h-100\"\r\n                           [class.selected]=\"propertySelection.selected\"\r\n                           [class.required-property]=\"propertySelection.property?.isrequired\"\r\n                           (click)=\"togglePropertySelection(selectedProductForProperties.productid, propertySelection.propertyid)\">\r\n                        <div class=\"card-body\">\r\n                          <div class=\"d-flex align-items-start\">\r\n                            <div class=\"property-option-icon me-3\">\r\n                              <i class=\"bi\" \r\n                                 [ngClass]=\"{\r\n                                   'bi-palette': propertySelection.property?.propertytype === 1,\r\n                                   'bi-circle': propertySelection.property?.propertytype === 2,\r\n                                   'bi-texture': propertySelection.property?.propertytype === 3,\r\n                                   'bi-star': propertySelection.property?.propertytype === 4\r\n                                 }\"></i>\r\n                            </div>\r\n                            <div class=\"flex-grow-1\">\r\n                              <h6 class=\"property-option-title\">\r\n                                {{ propertySelection.property?.name }}\r\n                                <span *ngIf=\"propertySelection.property?.isrequired\" class=\"required-indicator\">*</span>\r\n                              </h6>\r\n                              <p class=\"property-option-description small text-muted mb-2\">\r\n                                {{ propertySelection.property?.description }}\r\n                              </p>\r\n                              <div class=\"d-flex justify-content-between align-items-center\">\r\n                                <span class=\"badge bg-warning text-dark\">\r\n                                  {{ formatPrice(propertySelection.property?.price || 0) }}\r\n                                </span>\r\n                                <div class=\"form-check\">\r\n                                  <input \r\n                                    class=\"form-check-input\" \r\n                                    [type]=\"propertySelection.property?.isexclusive ? 'radio' : 'checkbox'\"\r\n                                    [name]=\"propertySelection.property?.isexclusive ? 'modal-property-' + propertySelection.property?.propertytype + '-' + selectedProductForProperties.productid : ''\"\r\n                                    [checked]=\"propertySelection.selected\"\r\n                                    (change)=\"togglePropertySelection(selectedProductForProperties.productid, propertySelection.propertyid)\">\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <!-- Message si aucune propriété payante -->\r\n                    <div *ngIf=\"getGroupedPropertiesForProduct(selectedProductForProperties.productid).paid.length === 0\" class=\"col-12\">\r\n                      <div class=\"alert alert-info\">\r\n                        <i class=\"bi bi-info-circle me-2\"></i>\r\n                        Aucune propriété payante disponible pour ce produit.\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <!-- Propriétés requises -->\r\n                <div class=\"tab-pane fade\" id=\"required-properties\" role=\"tabpanel\" aria-labelledby=\"required-tab\">\r\n                  <div class=\"row g-3\">\r\n                    <div *ngFor=\"let propertySelection of getRequiredPropertiesForProduct(selectedProductForProperties.productid)\" \r\n                         class=\"col-md-6\">\r\n                      <div class=\"card property-option-card h-100 required-property\"\r\n                           [class.selected]=\"propertySelection.selected\">\r\n                        <div class=\"card-body\">\r\n                          <div class=\"d-flex align-items-start\">\r\n                            <div class=\"property-option-icon me-3 bg-danger text-white\">\r\n                              <i class=\"bi bi-asterisk\"></i>\r\n                            </div>\r\n                            <div class=\"flex-grow-1\">\r\n                              <h6 class=\"property-option-title\">\r\n                                {{ propertySelection.property?.name }}\r\n                                <span class=\"required-indicator\">*</span>\r\n                              </h6>\r\n                              <p class=\"property-option-description small text-muted mb-2\">\r\n                                {{ propertySelection.property?.description }}\r\n                              </p>\r\n                              <div class=\"d-flex justify-content-between align-items-center\">\r\n                                <span class=\"badge\" [ngClass]=\"{\r\n                                  'bg-success': propertySelection.property?.price === 0,\r\n                                  'bg-warning text-dark': propertySelection.property?.price! > 0\r\n                                }\">\r\n                                  {{ propertySelection.property?.price === 0 ? 'Inclus' : formatPrice(propertySelection.property?.price || 0) }}\r\n                                </span>\r\n                                <div class=\"form-check\">\r\n                                  <input \r\n                                    class=\"form-check-input\" \r\n                                    type=\"checkbox\"\r\n                                    [checked]=\"true\"\r\n                                    disabled>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <!-- Message si aucune propriété requise -->\r\n                    <div *ngIf=\"getRequiredPropertiesForProduct(selectedProductForProperties.productid).length === 0\" class=\"col-12\">\r\n                      <div class=\"alert alert-info\">\r\n                        <i class=\"bi bi-info-circle me-2\"></i>\r\n                        Aucune propriété requise pour ce produit.\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <!-- Résumé des propriétés sélectionnées -->\r\n              <div class=\"mt-4 pt-3 border-top\">\r\n                <div class=\"d-flex justify-content-between align-items-center\">\r\n                  <div>\r\n                    <h6 class=\"mb-0\">Propriétés sélectionnées</h6>\r\n                    <p class=\"text-muted small mb-0\">\r\n                      {{ getSelectedPropertiesCountForProduct(selectedProductForProperties.productid) }} propriété(s) sélectionnée(s)\r\n                    </p>\r\n                  </div>\r\n                  <div class=\"text-end\">\r\n                    <span class=\"badge bg-success fs-6\">{{ formatPrice(getPropertiesPriceForProduct(selectedProductForProperties.productid)) }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"modal-footer\">\r\n            <button type=\"button\" class=\"btn btn-outline-secondary\" data-bs-dismiss=\"modal\">Fermer</button>\r\n            <button type=\"button\" class=\"btn btn-primary\" data-bs-dismiss=\"modal\">\r\n              <i class=\"bi bi-check-circle me-2\"></i>\r\n              Confirmer les propriétés\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- Modal de confirmation pour la réinitialisation -->\r\n    <div class=\"modal fade\" id=\"resetConfirmationModal\" tabindex=\"-1\" aria-labelledby=\"resetConfirmationModalLabel\" aria-hidden=\"true\">\r\n      <div class=\"modal-dialog modal-dialog-centered\">\r\n        <div class=\"modal-content\">\r\n          <div class=\"modal-header bg-danger text-white\">\r\n            <h5 class=\"modal-title\" id=\"resetConfirmationModalLabel\">\r\n              <i class=\"bi bi-exclamation-triangle me-2\"></i>\r\n              Confirmation de réinitialisation\r\n            </h5>\r\n            <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Fermer\"></button>\r\n          </div>\r\n          <div class=\"modal-body\">\r\n            <p>\r\n              Êtes-vous sûr de vouloir réinitialiser complètement la configuration ?\r\n            </p>\r\n            <div class=\"alert alert-warning\">\r\n              <i class=\"bi bi-info-circle me-2\"></i>\r\n              Cette action supprimera toutes vos sélections et vous ramènera à l'étape 1. Cette action est irréversible.\r\n            </div>\r\n          </div>\r\n          <div class=\"modal-footer\">\r\n            <button type=\"button\" class=\"btn btn-outline-secondary\" data-bs-dismiss=\"modal\">Annuler</button>\r\n            <button type=\"button\" class=\"btn btn-danger\" (click)=\"resetConfiguration()\" data-bs-dismiss=\"modal\">\r\n              <i class=\"bi bi-arrow-counterclockwise me-2\"></i>\r\n              Réinitialiser\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- Modal pour afficher les produits associés (style Taiga UI) -->\r\n    <div class=\"modal fade\" id=\"associatedProductsModal\" tabindex=\"-1\" aria-labelledby=\"associatedProductsModalLabel\" aria-hidden=\"true\">\r\n      <div class=\"modal-dialog modal-dialog-centered modal-xl\">\r\n        <div class=\"modal-content\">\r\n          <div class=\"modal-header bg-info text-white\">\r\n            <h5 class=\"modal-title\" id=\"associatedProductsModalLabel\">\r\n              <i class=\"bi bi-diagram-3 me-2\"></i>\r\n              Produits associés à {{ currentRootProduct?.name }}\r\n            </h5>\r\n            <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Fermer\"></button>\r\n          </div>\r\n          <div class=\"modal-body\">\r\n            <div *ngIf=\"currentRootProduct\">\r\n              <!-- Arborescence des produits associés style Taiga UI -->\r\n              <div class=\"tui-tree-container\">\r\n                <!-- Produit racine -->\r\n                <div class=\"tui-tree-node root-node\">\r\n                  <div class=\"tui-tree-item\">\r\n                    <div class=\"tui-tree-item-content\">\r\n                      <div class=\"tui-tree-item-content-wrapper\">\r\n                        <div class=\"tui-tree-expander\" (click)=\"toggleRootExpanded()\">\r\n                          <i class=\"bi\" [ngClass]=\"{'bi-chevron-down': isRootExpanded, 'bi-chevron-right': !isRootExpanded}\"></i>\r\n                        </div>\r\n                        <div class=\"tui-tree-icon\">\r\n                          <i class=\"bi bi-folder-fill text-warning\"></i>\r\n                        </div>\r\n                        <div class=\"tui-tree-checkbox\">\r\n                          <input type=\"checkbox\" class=\"form-check-input\" \r\n                                 [checked]=\"isProductConfirmed(currentRootProduct)\"\r\n                                 (change)=\"toggleProductInclusion(currentRootProduct)\">\r\n                        </div>\r\n                        <div class=\"tui-tree-label\">{{ currentRootProduct?.name }}</div>\r\n                        <div class=\"tui-tree-price\">{{ formatPrice(currentRootProduct?.price || 0) }}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <!-- Enfants directs -->\r\n                  <div class=\"tui-tree-children\" *ngIf=\"isRootExpanded\">\r\n                    <ng-container *ngFor=\"let child of getDirectChildren(currentRootProduct?.productid || '')\">\r\n                      <div class=\"tui-tree-node\">\r\n                        <div class=\"tui-tree-item\">\r\n                          <div class=\"tui-tree-item-content\">\r\n                            <div class=\"tui-tree-item-content-wrapper\">\r\n                              <div class=\"tui-tree-expander\" *ngIf=\"hasDirectChildren(child.productid)\" (click)=\"toggleNodeExpanded(child.productid)\">\r\n                                <i class=\"bi\" [ngClass]=\"{'bi-chevron-down': isNodeExpanded(child.productid), 'bi-chevron-right': !isNodeExpanded(child.productid)}\"></i>\r\n                              </div>\r\n                              <div class=\"tui-tree-expander\" *ngIf=\"!hasDirectChildren(child.productid)\"></div>\r\n                              <div class=\"tui-tree-icon\">\r\n                                <i class=\"bi\" [ngClass]=\"{'bi-folder-fill text-warning': hasDirectChildren(child.productid), 'bi-file-earmark text-primary': !hasDirectChildren(child.productid)}\"></i>\r\n                              </div>\r\n                              <div class=\"tui-tree-checkbox\">\r\n                                <input type=\"checkbox\" class=\"form-check-input\" \r\n                                       [checked]=\"isProductConfirmed(child)\"\r\n                                       (change)=\"toggleProductInclusion(child)\">\r\n                              </div>\r\n                              <div class=\"tui-tree-label\">{{ child.name }}</div>\r\n                              <div class=\"tui-tree-price\">{{ formatPrice(child.price || 0) }}</div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                        \r\n                        <!-- Petits-enfants -->\r\n                        <div class=\"tui-tree-children\" *ngIf=\"hasDirectChildren(child.productid) && isNodeExpanded(child.productid)\">\r\n                          <ng-container *ngFor=\"let grandchild of getDirectChildren(child.productid)\">\r\n                            <div class=\"tui-tree-node\">\r\n                              <div class=\"tui-tree-item\">\r\n                                <div class=\"tui-tree-item-content\">\r\n                                  <div class=\"tui-tree-item-content-wrapper\">\r\n                                    <div class=\"tui-tree-expander\" *ngIf=\"hasDirectChildren(grandchild.productid)\" (click)=\"toggleNodeExpanded(grandchild.productid)\">\r\n                                      <i class=\"bi\" [ngClass]=\"{'bi-chevron-down': isNodeExpanded(grandchild.productid), 'bi-chevron-right': !isNodeExpanded(grandchild.productid)}\"></i>\r\n                                    </div>\r\n                                    <div class=\"tui-tree-expander\" *ngIf=\"!hasDirectChildren(grandchild.productid)\"></div>\r\n                                    <div class=\"tui-tree-icon\">\r\n                                      <i class=\"bi\" [ngClass]=\"{'bi-folder-fill text-warning': hasDirectChildren(grandchild.productid), 'bi-file-earmark text-primary': !hasDirectChildren(grandchild.productid)}\"></i>\r\n                                    </div>\r\n                                    <div class=\"tui-tree-checkbox\">\r\n                                      <input type=\"checkbox\" class=\"form-check-input\" \r\n                                             [checked]=\"isProductConfirmed(grandchild)\"\r\n                                             (change)=\"toggleProductInclusion(grandchild)\">\r\n                                    </div>\r\n                                    <div class=\"tui-tree-label\">{{ grandchild.name }}</div>\r\n                                    <div class=\"tui-tree-price\">{{ formatPrice(grandchild.price || 0) }}</div>\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                              \r\n                              <!-- Arrière-petits-enfants -->\r\n                              <div class=\"tui-tree-children\" *ngIf=\"hasDirectChildren(grandchild.productid) && isNodeExpanded(grandchild.productid)\">\r\n                                <ng-container *ngFor=\"let greatgrandchild of getDirectChildren(grandchild.productid)\">\r\n                                  <div class=\"tui-tree-node\">\r\n                                    <div class=\"tui-tree-item\">\r\n                                      <div class=\"tui-tree-item-content\">\r\n                                        <div class=\"tui-tree-item-content-wrapper\">\r\n                                          <div class=\"tui-tree-expander\"></div>\r\n                                          <div class=\"tui-tree-icon\">\r\n                                            <i class=\"bi bi-file-earmark text-primary\"></i>\r\n                                          </div>\r\n                                          <div class=\"tui-tree-checkbox\">\r\n                                            <input type=\"checkbox\" class=\"form-check-input\" \r\n                                                   [checked]=\"isProductConfirmed(greatgrandchild)\"\r\n                                                   (change)=\"toggleProductInclusion(greatgrandchild)\">\r\n                                          </div>\r\n                                          <div class=\"tui-tree-label\">{{ greatgrandchild.name }}</div>\r\n                                          <div class=\"tui-tree-price\">{{ formatPrice(greatgrandchild.price || 0) }}</div>\r\n                                        </div>\r\n                                      </div>\r\n                                    </div>\r\n                                  </div>\r\n                                </ng-container>\r\n                              </div>\r\n                            </div>\r\n                          </ng-container>\r\n                        </div>\r\n                      </div>\r\n                    </ng-container>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <!-- Instructions pour l'utilisateur -->\r\n              <div class=\"alert alert-info mt-3\">\r\n                <i class=\"bi bi-info-circle me-2\"></i>\r\n                <strong>Comment utiliser :</strong> Cochez les cases à côté des produits que vous souhaitez sélectionner. \r\n                Cliquez sur les flèches pour étendre ou réduire les catégories.\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"modal-footer\">\r\n            <button type=\"button\" class=\"btn btn-outline-secondary\" data-bs-dismiss=\"modal\">Fermer</button>\r\n            <button type=\"button\" class=\"btn btn-primary\" (click)=\"confirmAssociatedProducts()\" data-bs-dismiss=\"modal\">\r\n              <i class=\"bi bi-check-circle me-2\"></i>\r\n              Confirmer la sélection\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAcA;AACA,SAASA,oBAAoB,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,+BAA+B;AAKxG,SAA6CC,aAAa,QAAQ,gCAAgC;AAClG,SAASC,kBAAkB,EAAEC,6BAA6B,QAAQ,oCAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICjBhGC,EAFJ,CAAAC,cAAA,cAA+C,cACU,eACvB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAE/CF,EAF+C,CAAAG,YAAA,EAAO,EAC9C,EACF;;;;;;IAoDMH,EAJF,CAAAC,cAAA,cAE4B,cAMwC;IAAhED,EAAA,CAAAI,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,SAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAAF,MAAA,CAAAG,WAAA,KAAyB,CAAC,IAAIH,MAAA,CAAAI,qBAAA,CAAAR,SAAA,CAAAS,EAAA,CAAgC;IAAA,EAAC;IAI3Df,EAFJ,CAAAC,cAAA,aAAuB,cACiB,cACN;IAC5BD,EAAA,CAAAgB,SAAA,QAA6B;IAC/BhB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAyB,aACA;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7CH,EAAA,CAAAC,cAAA,YAAqB;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG/CH,EADF,CAAAC,cAAA,eAAoE,gBACjC;IAC/BD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAELH,EADF,CAAAC,cAAA,eAAwB,iBAM6C;IAAjED,EAAA,CAAAI,UAAA,oBAAAa,yEAAA;MAAA,MAAAX,SAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAAF,MAAA,CAAAG,WAAA,KAA0B,CAAC,IAAIH,MAAA,CAAAI,qBAAA,CAAAR,SAAA,CAAAS,EAAA,CAAgC;IAAA,EAAC;IALlEf,EAAA,CAAAG,YAAA,EAKmE;IACnEH,EAAA,CAAAC,cAAA,iBAAgC;IAC9BD,EAAA,CAAAE,MAAA,2BACF;IAOdF,EAPc,CAAAG,YAAA,EAAQ,EACJ,EACF,EACF,EACF,EACF,EACF,EACF;;;;;IAjCFH,EAAA,CAAAkB,SAAA,EAA8C;IAC9ClB,EADA,CAAAmB,WAAA,aAAAT,MAAA,CAAAU,gBAAA,CAAAd,SAAA,CAAAS,EAAA,EAA8C,aAAAL,MAAA,CAAAG,WAAA,KACZ;IAMzBb,EAAA,CAAAkB,SAAA,GAAqB;IAArBlB,EAAA,CAAAqB,UAAA,CAAAf,SAAA,CAAAgB,IAAA,CAAqB;IAGDtB,EAAA,CAAAkB,SAAA,GAAiB;IAAjBlB,EAAA,CAAAuB,iBAAA,CAAAjB,SAAA,CAAAkB,IAAA,CAAiB;IACnBxB,EAAA,CAAAkB,SAAA,GAAwB;IAAxBlB,EAAA,CAAAuB,iBAAA,CAAAjB,SAAA,CAAAmB,WAAA,CAAwB;IAUvCzB,EAAA,CAAAkB,SAAA,GAAuC;IACvClB,EADA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAU,gBAAA,CAAAd,SAAA,CAAAS,EAAA,EAAuC,aAAAL,MAAA,CAAAG,WAAA,KACX;;;;;IAoBMb,EAAA,CAAAC,cAAA,WAAoB;IAAAD,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAA3EH,EADF,CAAAC,cAAA,WAAkE,aACxD;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAAAH,EAAA,CAAA2B,UAAA,IAAAC,6DAAA,mBAAoB;IACpE5B,EAAA,CAAAG,YAAA,EAAO;;;;;;IADGH,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAmB,aAAA,CAAAC,WAAA,EAA6B;IAAgB9B,EAAA,CAAAkB,SAAA,EAAW;IAAXlB,EAAA,CAAA0B,UAAA,UAAAK,OAAA,CAAW;;;;;IAJpE/B,EADF,CAAAC,cAAA,cAAuD,cACpB;IAC/BD,EAAA,CAAAgB,SAAA,YAAuC;IACvChB,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,+CAChD;IAAAF,EAAA,CAAA2B,UAAA,IAAAK,sDAAA,mBAAkE;IAItEhC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IALMH,EAAA,CAAAkB,SAAA,GAA8B;IAA9BlB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAuB,iBAAA,CAAAC,MAAA,CAA8B;IACXlC,EAAA,CAAAkB,SAAA,GAAsB;IAAtBlB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAuB,iBAAA,CAAsB;;;;;;IASnDjC,EAFJ,CAAAC,cAAA,cAAmF,cACzC,iBAGf;IAArBD,EAAA,CAAAI,UAAA,mBAAA+B,wEAAA;MAAAnC,EAAA,CAAAO,aAAA,CAAA6B,GAAA;MAAA,MAAA1B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA2B,QAAA,EAAU;IAAA,EAAC;IACpBrC,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAgB,SAAA,YAAsC;IAG5ChB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAJAH,EAAA,CAAAkB,SAAA,GACA;IADAlB,EAAA,CAAAsC,kBAAA,yBAAA5B,MAAA,CAAAuB,iBAAA,CAAAC,MAAA,OACA;;;;;IArEJlC,EAJR,CAAAC,cAAA,aAA0C,aACpB,cACI,cACK,YACN;IACfD,EAAA,CAAAgB,SAAA,YAA0C;IAC1ChB,EAAA,CAAAE,MAAA,mDACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAE,MAAA,mEAAuD;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAC/HF,EAD+H,CAAAG,YAAA,EAAS,EAAI,EACtI;IAGJH,EAFF,CAAAC,cAAA,cAAuB,eAEU;IAC7BD,EAAA,CAAA2B,UAAA,KAAAY,+CAAA,oBAE4B;IAsC9BvC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA2B,UAAA,KAAAa,+CAAA,kBAAuD;IASzDxC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA2B,UAAA,KAAAc,+CAAA,kBAAmF;IAYzFzC,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAvEoGH,EAAA,CAAAkB,SAAA,IAA2B;IAA3BlB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgC,gBAAA,CAAAlB,IAAA,CAA2B;IAMtGxB,EAAA,CAAAkB,SAAA,GAAc;IAAdlB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAiC,WAAA,CAAc;IA0C/B3C,EAAA,CAAAkB,SAAA,EAAkC;IAAlClB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAuB,iBAAA,CAAAC,MAAA,KAAkC;IAWhBlC,EAAA,CAAAkB,SAAA,EAAuD;IAAvDlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAuB,iBAAA,CAAAC,MAAA,QAAAxB,MAAA,CAAAG,WAAA,OAAuD;;;;;IAevFb,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAgB,SAAA,YAA8D;IAChEhB,EAAA,CAAAG,YAAA,EAAM;;;;;IAWFH,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAgB,SAAA,YAA0C;IAAChB,EAAA,CAAAE,MAAA,GAC7C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADuCH,EAAA,CAAAkB,SAAA,GAC7C;IAD6ClB,EAAA,CAAAsC,kBAAA,MAAA5B,MAAA,CAAAkC,YAAA,MAC7C;;;;;IAGA5C,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAgB,SAAA,YAAkC;IAAChB,EAAA,CAAAE,MAAA,GACrC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAD+BH,EAAA,CAAAkB,SAAA,GACrC;IADqClB,EAAA,CAAAsC,kBAAA,MAAA5B,MAAA,CAAAmC,cAAA,MACrC;;;;;;IAoBU7C,EAAA,CAAAC,cAAA,kBAGoH;IADlHD,EAAA,CAAAI,UAAA,mBAAA0C,kFAAA;MAAA9C,EAAA,CAAAO,aAAA,CAAAwC,GAAA;MAAA,MAAArC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAsC,eAAA,EAAiB;IAAA,EAAC;IAE3BhD,EAAA,CAAAgB,SAAA,aAA8B;IAAChB,EAAA,CAAAE,MAAA,wBACjC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAqBLH,EAAA,CAAAC,cAAA,kBAG6D;IAA3DD,EAAA,CAAAI,UAAA,mBAAA6C,kFAAA;MAAAjD,EAAA,CAAAO,aAAA,CAAA2C,GAAA;MAAA,MAAAxC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAD,MAAA,CAAAyC,cAAA,CAAAC,UAAA,GAAqC,EAAE;MAAA,OAAApD,EAAA,CAAAY,WAAA,CAAEF,MAAA,CAAA2C,cAAA,EAAgB;IAAA,EAAC;IAC1DrD,EAAA,CAAAgB,SAAA,aAAuB;IACzBhB,EAAA,CAAAG,YAAA,EAAS;;;;;IAWTH,EAAA,CAAAC,cAAA,kBAEsB;IACpBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAA0B,UAAA,UAAA4B,UAAA,CAAAvC,EAAA,CAAmB;IACnBf,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAsC,kBAAA,MAAAgB,UAAA,CAAA9B,IAAA,MACF;;;;;IA4CMxB,EAAA,CAAAC,cAAA,gBAAwE;IACtED,EAAA,CAAAgB,SAAA,YAAkC;IAAChB,EAAA,CAAAE,MAAA,sBACrC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAAsG;IACpGD,EAAA,CAAAgB,SAAA,aAA2B;IAAChB,EAAA,CAAAE,MAAA,8BAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAsCLH,EAAA,CAAAC,cAAA,WAA8E;IAAAD,EAAA,CAAAE,MAAA,wBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACjGH,EAAA,CAAAC,cAAA,WAA6E;IAAAD,EAAA,CAAAE,MAAA,+BAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAClGH,EAAA,CAAAC,cAAA,WAA0C;IAAAD,EAAA,CAAAE,MAAA,oBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAI3DH,EAAA,CAAAC,cAAA,kBAG4C;IAA1CD,EAAA,CAAAI,UAAA,mBAAAmD,yFAAA;MAAAvD,EAAA,CAAAO,aAAA,CAAAiD,IAAA;MAAA,MAAAC,WAAA,GAAAzD,EAAA,CAAAW,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAgD,sBAAA,CAAAD,WAAA,CAA+B;IAAA,EAAC;IACzCzD,EAAA,CAAAgB,SAAA,aAAoC;IAAChB,EAAA,CAAAE,MAAA,wCACvC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA1DXH,EANR,CAAAC,cAAA,eAA6E,eAGtB,eACT,eAC2B,eACvC;IACxBD,EAAA,CAAAgB,SAAA,aAAmC;IACrChB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAuC;IAIrCD,EAHA,CAAA2B,UAAA,IAAAgC,6DAAA,oBAAwE,IAAAC,6DAAA,oBAG8B;IAI1G5D,EADE,CAAAG,YAAA,EAAM,EACF;IAENH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtDH,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAgB,SAAA,cAA0B;IAAChB,EAAA,CAAAE,MAAA,IAC7B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAI9EH,EAFJ,CAAAC,cAAA,gBAAqB,gBACiD,iBAKzD;IACPD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,mBAAgD;IAAAD,EAAA,CAAAE,MAAA,IAAyF;IAC3IF,EAD2I,CAAAG,YAAA,EAAS,EAC9I;IAGJH,EADF,CAAAC,cAAA,gBAA0B,mBASsC;IAD5DD,EAAA,CAAAI,UAAA,mBAAAyD,gFAAA;MAAA,MAAAJ,WAAA,GAAAzD,EAAA,CAAAO,aAAA,CAAAuD,IAAA,EAAArD,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAqD,0BAAA,CAAAN,WAAA,CAAmC;IAAA,EAAC;IAE7CzD,EAAA,CAAAgB,SAAA,cAKU;IAGVhB,EAFA,CAAA2B,UAAA,KAAAqC,8DAAA,mBAA8E,KAAAC,8DAAA,mBACD,KAAAC,8DAAA,mBACnC;IAC5ClE,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAA2B,UAAA,KAAAwC,gEAAA,sBAG4C;IAStDnE,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF;;;;;IArECH,EAAA,CAAAkB,SAAA,EAAsD;IACtDlB,EADA,CAAAmB,WAAA,kBAAAT,MAAA,CAAA0D,qBAAA,CAAAX,WAAA,EAAsD,cAAA/C,MAAA,CAAA2D,kBAAA,CAAAZ,WAAA,EACP;IAOPzD,EAAA,CAAAkB,SAAA,GAAiC;IAAjClB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA2D,kBAAA,CAAAZ,WAAA,EAAiC;IAGtCzD,EAAA,CAAAkB,SAAA,EAAoE;IAApElB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA0D,qBAAA,CAAAX,WAAA,MAAA/C,MAAA,CAAA2D,kBAAA,CAAAZ,WAAA,EAAoE;IAMzEzD,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAAuB,iBAAA,CAAAkC,WAAA,CAAAjC,IAAA,CAAkB;IAEpBxB,EAAA,CAAAkB,SAAA,GAC7B;IAD6BlB,EAAA,CAAAsC,kBAAA,MAAAmB,WAAA,CAAAa,SAAA,MAC7B;IACqDtE,EAAA,CAAAkB,SAAA,GAAyB;IAAzBlB,EAAA,CAAAuB,iBAAA,CAAAkC,WAAA,CAAAhC,WAAA,CAAyB;IAKpEzB,EAAA,CAAAkB,SAAA,GAGE;IAHFlB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAuE,eAAA,KAAAC,GAAA,GAAAf,WAAA,CAAAgB,eAAA,EAAAhB,WAAA,CAAAgB,eAAA,EAGE;IACNzE,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAsC,kBAAA,MAAAmB,WAAA,CAAAgB,eAAA,+BACF;IACgDzE,EAAA,CAAAkB,SAAA,GAAyF;IAAzFlB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,EAAAjB,WAAA,CAAAkB,KAAA,SAAAjE,MAAA,CAAAkE,4BAAA,CAAAnB,WAAA,CAAAa,SAAA,GAAyF;IAMvItE,EAAA,CAAAkB,SAAA,GAIE;IAEFlB,EANA,CAAA0B,UAAA,YAAA1B,EAAA,CAAA6E,eAAA,KAAAC,GAAA,GAAApE,MAAA,CAAA0D,qBAAA,CAAAX,WAAA,MAAA/C,MAAA,CAAA2D,kBAAA,CAAAZ,WAAA,GAAA/C,MAAA,CAAA0D,qBAAA,CAAAX,WAAA,MAAA/C,MAAA,CAAA2D,kBAAA,CAAAZ,WAAA,GAAA/C,MAAA,CAAA2D,kBAAA,CAAAZ,WAAA,GAIE,aAAA/C,MAAA,CAAA2D,kBAAA,CAAAZ,WAAA,KAAA/C,MAAA,CAAAG,WAAA,KAEyD;IAExDb,EAAA,CAAAkB,SAAA,EAIE;IAJFlB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAA6E,eAAA,KAAAE,GAAA,GAAArE,MAAA,CAAA0D,qBAAA,CAAAX,WAAA,MAAA/C,MAAA,CAAA2D,kBAAA,CAAAZ,WAAA,GAAA/C,MAAA,CAAA0D,qBAAA,CAAAX,WAAA,MAAA/C,MAAA,CAAA2D,kBAAA,CAAAZ,WAAA,GAAA/C,MAAA,CAAA2D,kBAAA,CAAAZ,WAAA,GAIE;IACEzD,EAAA,CAAAkB,SAAA,EAAqE;IAArElB,EAAA,CAAA0B,UAAA,UAAAhB,MAAA,CAAA0D,qBAAA,CAAAX,WAAA,MAAA/C,MAAA,CAAA2D,kBAAA,CAAAZ,WAAA,EAAqE;IACrEzD,EAAA,CAAAkB,SAAA,EAAoE;IAApElB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA0D,qBAAA,CAAAX,WAAA,MAAA/C,MAAA,CAAA2D,kBAAA,CAAAZ,WAAA,EAAoE;IACpEzD,EAAA,CAAAkB,SAAA,EAAiC;IAAjClB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA2D,kBAAA,CAAAZ,WAAA,EAAiC;IAKvCzD,EAAA,CAAAkB,SAAA,EAA0C;IAA1ClB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAsE,iBAAA,CAAAvB,WAAA,CAAAa,SAAA,EAA0C;;;;;IAgBvDtE,EADF,CAAAC,cAAA,eAA+D,eAC/B;IAC5BD,EAAA,CAAAgB,SAAA,aAA2B;IAC7BhB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,gCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAE,MAAA,0EACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;IAiBAH,EAAA,CAAAC,cAAA,kBAEqC;IAA7BD,EAAA,CAAAI,UAAA,mBAAA6E,6FAAA;MAAAjF,EAAA,CAAAO,aAAA,CAAA2E,IAAA;MAAA,MAAAC,QAAA,GAAAnF,EAAA,CAAAW,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA0E,YAAA,CAAAD,QAAA,CAAkB;IAAA,EAAC;IAClCnF,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADPH,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAsC,kBAAA,MAAA6C,QAAA,MACF;;;;;IACAnF,EAAA,CAAAC,cAAA,gBAA4C;IACpBD,EAAA,CAAAE,MAAA,YACxB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAXTH,EAAA,CAAAC,cAAA,cAGmC;IAMjCD,EALA,CAAA2B,UAAA,IAAA0D,oEAAA,sBAEqC,IAAAC,kEAAA,oBAGO;IAG9CtF,EAAA,CAAAG,YAAA,EAAK;;;;;IATDH,EADA,CAAAmB,WAAA,WAAAgE,QAAA,KAAAzE,MAAA,CAAA6E,UAAA,CAAAC,WAAA,CAAgD,aAAAL,QAAA,QAClB;IAEvBnF,EAAA,CAAAkB,SAAA,EAAiB;IAAjBlB,EAAA,CAAA0B,UAAA,SAAAyD,QAAA,QAAiB;IAIDnF,EAAA,CAAAkB,SAAA,EAAiB;IAAjBlB,EAAA,CAAA0B,UAAA,SAAAyD,QAAA,QAAiB;;;;;;IAd1CnF,EAJR,CAAAC,cAAA,eAAiE,eACrB,cACM,cAC0B,kBACS;IAAnDD,EAAA,CAAAI,UAAA,mBAAAqF,+EAAA;MAAAzF,EAAA,CAAAO,aAAA,CAAAmF,IAAA;MAAA,MAAAhF,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA0E,YAAA,CAAA1E,MAAA,CAAA6E,UAAA,CAAAC,WAAA,GAAsC,CAAC,CAAC;IAAA,EAAC;IAC1ExF,EAAA,CAAAgB,SAAA,aAAkC;IAEtChB,EADE,CAAAG,YAAA,EAAS,EACN;IAELH,EAAA,CAAA2B,UAAA,IAAAgE,2DAAA,kBAGmC;IAYjC3F,EADF,CAAAC,cAAA,cAAoF,kBACL;IAAnDD,EAAA,CAAAI,UAAA,mBAAAwF,+EAAA;MAAA5F,EAAA,CAAAO,aAAA,CAAAmF,IAAA;MAAA,MAAAhF,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA0E,YAAA,CAAA1E,MAAA,CAAA6E,UAAA,CAAAC,WAAA,GAAsC,CAAC,CAAC;IAAA,EAAC;IAC1ExF,EAAA,CAAAgB,SAAA,aAAmC;IAK7ChB,EAJQ,CAAAG,YAAA,EAAS,EACN,EACF,EACD,EACF;;;;IA3BsBH,EAAA,CAAAkB,SAAA,GAA+C;IAA/ClB,EAAA,CAAAmB,WAAA,aAAAT,MAAA,CAAA6E,UAAA,CAAAC,WAAA,OAA+C;IAOhDxF,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAmF,cAAA,GAAmB;IAalB7F,EAAA,CAAAkB,SAAA,EAA6D;IAA7DlB,EAAA,CAAAmB,WAAA,aAAAT,MAAA,CAAA6E,UAAA,CAAAC,WAAA,KAAA9E,MAAA,CAAAoF,aAAA,GAA6D;;;;;;IA4BrF9F,EAAA,CAAAC,cAAA,kBAG+C;IAD7CD,EAAA,CAAAI,UAAA,mBAAA2F,kFAAA;MAAA/F,EAAA,CAAAO,aAAA,CAAAyF,IAAA;MAAA,MAAAtF,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAuF,0BAAA,EAA4B;IAAA,EAAC;IAEtCjG,EAAA,CAAAgB,SAAA,aAGO;IACPhB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IALOH,EAAA,CAAAkB,SAAA,EAGZ;IAHYlB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAuE,eAAA,IAAA2B,GAAA,EAAAxF,MAAA,CAAAyF,gBAAA,CAAAC,IAAA,QAAA1F,MAAA,CAAAyF,gBAAA,CAAAC,IAAA,MAGZ;IACFpG,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAsC,kBAAA,MAAA5B,MAAA,CAAAyF,gBAAA,CAAAC,IAAA,wDACF;;;;;IAOFpG,EADF,CAAAC,cAAA,eAAwE,eACxC;IAC5BD,EAAA,CAAAgB,SAAA,aAA4B;IAC9BhB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1DH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,0EAAyD;IACzFF,EADyF,CAAAG,YAAA,EAAM,EACzF;;;;;IA8BFH,EAAA,CAAAgB,SAAA,SAAqD;;;;;;IAzBrDhB,EADF,CAAAC,cAAA,eAAqF,4BAuBlC;IAA/CD,EANA,CAAAI,UAAA,2BAAAiG,uGAAAC,MAAA;MAAAtG,EAAA,CAAAO,aAAA,CAAAgG,IAAA;MAAA,MAAA7F,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAiBF,MAAA,CAAA8F,sBAAA,CAAAF,MAAA,CAA8B;IAAA,EAAC,oCAAAG,gHAAAH,MAAA;MAAAtG,EAAA,CAAAO,aAAA,CAAAgG,IAAA;MAAA,MAAA7F,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CACtBF,MAAA,CAAAgG,sBAAA,CAAAJ,MAAA,CAA8B;IAAA,EAAC,4BAAAK,wGAAAL,MAAA;MAAAtG,EAAA,CAAAO,aAAA,CAAAgG,IAAA;MAAA,MAAA7F,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CACvCF,MAAA,CAAAkG,qBAAA,CAAAN,MAAA,CAAAO,SAAA,EAAAP,MAAA,CAAAQ,KAAA,CAAqD;IAAA,EAAC,8BAAAC,0GAAAT,MAAA;MAAAtG,EAAA,CAAAO,aAAA,CAAAgG,IAAA;MAAA,MAAA7F,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CACpDF,MAAA,CAAAsG,gBAAA,CAAAV,MAAA,CAAwB;IAAA,EAAC,8BAAAW,0GAAAX,MAAA;MAAAtG,EAAA,CAAAO,aAAA,CAAAgG,IAAA;MAAA,MAAA7F,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CACzBF,MAAA,CAAAwG,gBAAA,CAAAZ,MAAA,CAAwB;IAAA,EAAC,6BAAAa,yGAAAb,MAAA;MAAAtG,EAAA,CAAAO,aAAA,CAAAgG,IAAA;MAAA,MAAA7F,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAC1BF,MAAA,CAAA0G,sBAAA,CAAAd,MAAA,CAA8B;IAAA,EAAC,4BAAAe,wGAAAf,MAAA;MAAAtG,EAAA,CAAAO,aAAA,CAAAgG,IAAA;MAAA,MAAA7F,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAChCF,MAAA,CAAA4G,mBAAA,CAAAhB,MAAA,CAA2B;IAAA,EAAC;IAChDtG,EAAA,CAAAG,YAAA,EAAmB;IAEnBH,EAAA,CAAA2B,UAAA,IAAA4F,iEAAA,iBAAqD;IACvDvH,EAAA,CAAAG,YAAA,EAAM;;;;;;IAzBFH,EAAA,CAAAkB,SAAA,EAA6B;IAc7BlB,EAdA,CAAA0B,UAAA,YAAA8F,aAAA,CAAAC,OAAA,CAA6B,YAClB,oBACQ,uBACG,qBACF,eAAA/G,MAAA,CAAAgH,iBAAA,CAAAF,aAAA,CAAAC,OAAA,CAAAnD,SAAA,EACyC,aAAAkD,aAAA,CAAAG,QAAA,CAC9B,mBAAA3H,EAAA,CAAA4H,eAAA,KAAAC,GAAA,EACV,gBAAAnH,MAAA,CAAAsE,iBAAA,CAAAwC,aAAA,CAAAC,OAAA,CAAAnD,SAAA,EACyC,0BAAA5D,MAAA,CAAAoH,oBAAA,CAAAN,aAAA,CAAAC,OAAA,CAAAnD,SAAA,EACa,4BAAA5D,MAAA,CAAAqH,oCAAA,CAAAP,aAAA,CAAAC,OAAA,CAAAnD,SAAA,EACkB,mBAAA5D,MAAA,CAAAsH,wBAAA,CAAAR,aAAA,CAAAC,OAAA,CAAAnD,SAAA,EACrB,mBAAA5D,MAAA,CAAAuH,wBAAA,CAAAT,aAAA,CAAAC,OAAA,CAAAnD,SAAA,EACA,uBAAA5D,MAAA,CAAAwH,4BAAA,CAAAV,aAAA,CAAAC,OAAA,CAAAnD,SAAA,EACQ,aAAA5D,MAAA,CAAAG,WAAA,KACpD;IAUzBb,EAAA,CAAAkB,SAAA,EAA8C;IAA9ClB,EAAA,CAAA0B,UAAA,SAAAyG,KAAA,GAAAzH,MAAA,CAAA0H,yBAAA,CAAAlG,MAAA,KAA8C;;;;;IA5BvDlC,EAAA,CAAAC,cAAA,UAAkD;IAEhDD,EAAA,CAAA2B,UAAA,IAAA0G,4DAAA,oBAAqF;IA4BvFrI,EAAA,CAAAG,YAAA,EAAM;;;;IA5BuBH,EAAA,CAAAkB,SAAA,EAA8B;IAA9BlB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA0H,yBAAA,CAA8B;;;;;;IAkCvDpI,EAHN,CAAAC,cAAA,cAAsE,aACL,eACrB,gBACb;IACvBD,EAAA,CAAAE,MAAA,GAEA;IAAAF,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAE9DF,EAF8D,CAAAG,YAAA,EAAS,EAC9D,EACH;IAEJH,EADF,CAAAC,cAAA,eAA0B,kBAIO;IAD7BD,EAAA,CAAAI,UAAA,mBAAAkI,+EAAA;MAAAtI,EAAA,CAAAO,aAAA,CAAAgI,IAAA;MAAA,MAAA7H,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA8H,yBAAA,EAA2B;IAAA,EAAC;IAErCxI,EAAA,CAAAgB,SAAA,aAA2B;IAAChB,EAAA,CAAAE,MAAA,wBAC9B;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IAdEH,EAAA,CAAAkB,SAAA,GAEA;IAFAlB,EAAA,CAAAyI,kBAAA,MAAA/H,MAAA,CAAA0H,yBAAA,CAAAlG,MAAA,yBAAAxB,MAAA,CAAAgI,yBAAA,0BAEA;IAA6B1I,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,CAAAhE,MAAA,CAAAiI,UAAA,EAA6B;IAO1D3I,EAAA,CAAAkB,SAAA,GAA4B;IAA5BlB,EAAA,CAAA0B,UAAA,aAAAhB,MAAA,CAAAG,WAAA,KAA4B;;;;;;IAWlCb,EAAA,CAAAC,cAAA,kBAG4B;IAD1BD,EAAA,CAAAI,UAAA,mBAAAwI,kFAAA;MAAA5I,EAAA,CAAAO,aAAA,CAAAsI,IAAA;MAAA,MAAAnI,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAoI,YAAA,EAAc;IAAA,EAAC;IAExB9I,EAAA,CAAAgB,SAAA,aAAqC;IAAChB,EAAA,CAAAE,MAAA,4BACxC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IACTH,EAAA,CAAAgB,SAAA,UAAmC;;;;;;IAQjChB,EAAA,CAAAC,cAAA,kBAI4B;IAD1BD,EAAA,CAAAI,UAAA,mBAAA2I,kFAAA;MAAA/I,EAAA,CAAAO,aAAA,CAAAyI,IAAA;MAAA,MAAAtI,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA2B,QAAA,EAAU;IAAA,EAAC;IAEpBrC,EAAA,CAAAE,MAAA,gBACA;IAAAF,EAAA,CAAAgB,SAAA,YAAsC;IACxChB,EAAA,CAAAG,YAAA,EAAS;;;;IALPH,EAAA,CAAA0B,UAAA,aAAAhB,MAAA,CAAA0H,yBAAA,CAAAlG,MAAA,OAAmD;;;;;;IApUnDlC,EAPV,CAAAC,cAAA,UAA8B,cAGkB,cACA,aACqB,UACxD,aACc;IACfD,EAAA,CAAAgB,SAAA,YAAiC;IACjChB,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,gEAAoD;IAC7DF,EAD6D,CAAAG,YAAA,EAAQ,EAC/D;IAEJH,EADF,CAAAC,cAAA,eAA6C,gBACJ;IACrCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAA2B,UAAA,KAAAsH,yDAAA,sBAGoH;IAK1HjJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IASIH,EAPV,CAAAC,cAAA,cAAuB,gBAEoB,gBAClB,gBAEG,gBAC8B,iBACuB;IACrED,EAAA,CAAAgB,SAAA,cAA4B;IAC9BhB,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,kBAK6B;IAD3BD,EAAA,CAAAkJ,gBAAA,2BAAAC,gFAAA7C,MAAA;MAAAtG,EAAA,CAAAO,aAAA,CAAA6I,GAAA;MAAA,MAAA1I,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAAqJ,kBAAA,CAAA3I,MAAA,CAAAyC,cAAA,CAAAC,UAAA,EAAAkD,MAAA,MAAA5F,MAAA,CAAAyC,cAAA,CAAAC,UAAA,GAAAkD,MAAA;MAAA,OAAAtG,EAAA,CAAAY,WAAA,CAAA0F,MAAA;IAAA,EAAuC;IACvCtG,EAAA,CAAAI,UAAA,mBAAAkJ,wEAAA;MAAAtJ,EAAA,CAAAO,aAAA,CAAA6I,GAAA;MAAA,MAAA1I,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA2C,cAAA,EAAgB;IAAA,EAAC;IAL5BrD,EAAA,CAAAG,YAAA,EAK6B;IAC7BH,EAAA,CAAA2B,UAAA,KAAA4H,yDAAA,sBAG6D;IAIjEvJ,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,gBAAsB,mBAIU;IAD5BD,EAAA,CAAAkJ,gBAAA,2BAAAM,iFAAAlD,MAAA;MAAAtG,EAAA,CAAAO,aAAA,CAAA6I,GAAA;MAAA,MAAA1I,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAAqJ,kBAAA,CAAA3I,MAAA,CAAAyC,cAAA,CAAAsG,YAAA,EAAAnD,MAAA,MAAA5F,MAAA,CAAAyC,cAAA,CAAAsG,YAAA,GAAAnD,MAAA;MAAA,OAAAtG,EAAA,CAAAY,WAAA,CAAA0F,MAAA;IAAA,EAAyC;IACzCtG,EAAA,CAAAI,UAAA,oBAAAsJ,0EAAA;MAAA1J,EAAA,CAAAO,aAAA,CAAA6I,GAAA;MAAA,MAAA1I,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAA2C,cAAA,EAAgB;IAAA,EAAC;IAC3BrD,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChDH,EAAA,CAAA2B,UAAA,KAAAgI,yDAAA,sBAEsB;IAI1B3J,EADE,CAAAG,YAAA,EAAS,EACL;IAIJH,EADF,CAAAC,cAAA,gBAAsB,mBAIU;IAD5BD,EAAA,CAAAkJ,gBAAA,2BAAAU,iFAAAtD,MAAA;MAAAtG,EAAA,CAAAO,aAAA,CAAA6I,GAAA;MAAA,MAAA1I,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAAqJ,kBAAA,CAAA3I,MAAA,CAAAyC,cAAA,CAAA0G,UAAA,EAAAvD,MAAA,MAAA5F,MAAA,CAAAyC,cAAA,CAAA0G,UAAA,GAAAvD,MAAA;MAAA,OAAAtG,EAAA,CAAAY,WAAA,CAAA0F,MAAA;IAAA,EAAuC;IACvCtG,EAAA,CAAAI,UAAA,oBAAA0J,0EAAA;MAAA9J,EAAA,CAAAO,aAAA,CAAA6I,GAAA;MAAA,MAAA1I,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAA2C,cAAA,EAAgB;IAAA,EAAC;IAC3BrD,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC3CH,EAAA,CAAAC,cAAA,mBAAqB;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACjDH,EAAA,CAAAC,cAAA,mBAAuB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAElCF,EAFkC,CAAAG,YAAA,EAAS,EAChC,EACL;IAIJH,EADF,CAAAC,cAAA,gBAAsB,mBAIU;IAD5BD,EAAA,CAAAkJ,gBAAA,2BAAAa,iFAAAzD,MAAA;MAAAtG,EAAA,CAAAO,aAAA,CAAA6I,GAAA;MAAA,MAAA1I,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAAqJ,kBAAA,CAAA3I,MAAA,CAAA6E,UAAA,CAAAyE,YAAA,EAAA1D,MAAA,MAAA5F,MAAA,CAAA6E,UAAA,CAAAyE,YAAA,GAAA1D,MAAA;MAAA,OAAAtG,EAAA,CAAAY,WAAA,CAAA0F,MAAA;IAAA,EAAqC;IACrCtG,EAAA,CAAAI,UAAA,oBAAA6J,0EAAA;MAAAjK,EAAA,CAAAO,aAAA,CAAA6I,GAAA;MAAA,MAAA1I,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAA2C,cAAA,EAAgB;IAAA,EAAC;IAC3BrD,EAAA,CAAAC,cAAA,mBAAqB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzCH,EAAA,CAAAC,cAAA,mBAAqB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzCH,EAAA,CAAAC,cAAA,mBAAqB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzCH,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAIzCF,EAJyC,CAAAG,YAAA,EAAS,EACnC,EACL,EACF,EACF;IAIJH,EADF,CAAAC,cAAA,gBAA2B,gBACJ;IACnBD,EAAA,CAAA2B,UAAA,KAAAuI,sDAAA,qBAA6E;IAwE/ElK,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA2B,UAAA,KAAAwI,sDAAA,mBAA+D;IASjEnK,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA2B,UAAA,KAAAyI,sDAAA,oBAAiE;IAgCrEpK,EADE,CAAAG,YAAA,EAAM,EACF;IASEH,EAJR,CAAAC,cAAA,gBAA+C,gBACE,cACkB,WACxD,cACc;IACfD,EAAA,CAAAgB,SAAA,cAAqC;IACrChB,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,oGAA8D;IACvEF,EADuE,CAAAG,YAAA,EAAQ,EACzE;IAEJH,EADF,CAAAC,cAAA,eAA6C,gBACJ;IACrCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAA2B,UAAA,KAAA0I,yDAAA,sBAG+C;IASrDrK,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAENH,EAAA,CAAAC,cAAA,cAAuB;IASrBD,EARA,CAAA2B,UAAA,KAAA2I,sDAAA,mBAAwE,KAAAC,sDAAA,kBAQtB;IA+BpDvK,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA2B,UAAA,KAAA6I,sDAAA,mBAAsE;IAmBxExK,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,gBAAqC,cAC4B;IAO7DD,EANA,CAAA2B,UAAA,KAAA8I,yDAAA,sBAG4B,KAAAC,sDAAA,kBAGC;IAIzB1K,EAFJ,CAAAC,cAAA,gBAAuD,gBACtB,iBACI;IAAAD,EAAA,CAAAE,MAAA,IAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvFH,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IACvEF,EADuE,CAAAG,YAAA,EAAS,EAAO,EACjF;IAENH,EAAA,CAAA2B,UAAA,KAAAgJ,yDAAA,sBAI4B;IAOpC3K,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;;IArUMH,EAAA,CAAAkB,SAAA,IACF;IADElB,EAAA,CAAAsC,kBAAA,MAAA5B,MAAA,CAAA6E,UAAA,CAAAqF,UAAA,iBACF;IAIG5K,EAAA,CAAAkB,SAAA,EAA+G;IAA/GlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAyC,cAAA,CAAAC,UAAA,IAAA1C,MAAA,CAAAyC,cAAA,CAAAsG,YAAA,cAAA/I,MAAA,CAAAyC,cAAA,CAAA0G,UAAA,WAA+G;IAqB5G7J,EAAA,CAAAkB,SAAA,GAAuC;IAAvClB,EAAA,CAAA6K,gBAAA,YAAAnK,MAAA,CAAAyC,cAAA,CAAAC,UAAA,CAAuC;IAItCpD,EAAA,CAAAkB,SAAA,EAA+B;IAA/BlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAyC,cAAA,CAAAC,UAAA,CAA+B;IAWlCpD,EAAA,CAAAkB,SAAA,GAAyC;IAAzClB,EAAA,CAAA6K,gBAAA,YAAAnK,MAAA,CAAAyC,cAAA,CAAAsG,YAAA,CAAyC;IAIpBzJ,EAAA,CAAAkB,SAAA,GAAkC;IAAlClB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAoK,6BAAA,GAAkC;IAWvD9K,EAAA,CAAAkB,SAAA,GAAuC;IAAvClB,EAAA,CAAA6K,gBAAA,YAAAnK,MAAA,CAAAyC,cAAA,CAAA0G,UAAA,CAAuC;IAYvC7J,EAAA,CAAAkB,SAAA,GAAqC;IAArClB,EAAA,CAAA6K,gBAAA,YAAAnK,MAAA,CAAA6E,UAAA,CAAAyE,YAAA,CAAqC;IAE7BhK,EAAA,CAAAkB,SAAA,EAAY;IAAZlB,EAAA,CAAA0B,UAAA,aAAY;IACZ1B,EAAA,CAAAkB,SAAA,GAAY;IAAZlB,EAAA,CAAA0B,UAAA,aAAY;IACZ1B,EAAA,CAAAkB,SAAA,GAAY;IAAZlB,EAAA,CAAA0B,UAAA,aAAY;IACZ1B,EAAA,CAAAkB,SAAA,GAAW;IAAXlB,EAAA,CAAA0B,UAAA,YAAW;IASiC1B,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAqK,gBAAA,CAAmB;IA2EvE/K,EAAA,CAAAkB,SAAA,EAAmC;IAAnClB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAqK,gBAAA,CAAA7I,MAAA,OAAmC;IAYLlC,EAAA,CAAAkB,SAAA,EAAyB;IAAzBlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAoF,aAAA,OAAyB;IAiDzD9F,EAAA,CAAAkB,SAAA,IACF;IADElB,EAAA,CAAAsC,kBAAA,MAAA5B,MAAA,CAAA0H,yBAAA,CAAAlG,MAAA,iBACF;IAIGlC,EAAA,CAAAkB,SAAA,EAA0C;IAA1ClB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA0H,yBAAA,CAAAlG,MAAA,KAA0C;IAY3ClC,EAAA,CAAAkB,SAAA,GAA4C;IAA5ClB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA0H,yBAAA,CAAAlG,MAAA,OAA4C;IAQ5ClC,EAAA,CAAAkB,SAAA,EAA0C;IAA1ClB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA0H,yBAAA,CAAAlG,MAAA,KAA0C;IAiCxBlC,EAAA,CAAAkB,SAAA,EAA0C;IAA1ClB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA0H,yBAAA,CAAAlG,MAAA,KAA0C;IA2B/DlC,EAAA,CAAAkB,SAAA,GAAuB;IAAvBlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAG,WAAA,OAAuB;IAGpBb,EAAA,CAAAkB,SAAA,EAAqB;IAArBlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAG,WAAA,KAAqB;IAIUb,EAAA,CAAAkB,SAAA,GAA+C;IAA/ClB,EAAA,CAAAsC,kBAAA,KAAA5B,MAAA,CAAA0H,yBAAA,CAAAlG,MAAA,cAA+C;IACxClC,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,CAAAhE,MAAA,CAAAiI,UAAA,EAA6B;IAOpE3I,EAAA,CAAAkB,SAAA,EAAuB;IAAvBlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAG,WAAA,OAAuB;;;;;IA7VlCb,EAFJ,CAAAC,cAAA,cAAgD,cACrB,SACnB;IAAAD,EAAA,CAAAgB,SAAA,YAA8B;IAAChB,EAAA,CAAAE,MAAA,mCAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAE,MAAA,sDAA0C;IAC5DF,EAD4D,CAAAG,YAAA,EAAI,EAC1D;IAENH,EAAA,CAAAC,cAAA,aAAuB;IAYrBD,EAVA,CAAA2B,UAAA,IAAAqJ,8CAAA,kBAAqD,IAAAC,8CAAA,kBAKG,KAAAC,+CAAA,oBAK1B;IAsVlClL,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAhW+BH,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAkC,YAAA,CAAkB;IAKjB5C,EAAA,CAAAkB,SAAA,EAAoB;IAApBlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAmC,cAAA,CAAoB;IAKhD7C,EAAA,CAAAkB,SAAA,EAAsB;IAAtBlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAG,WAAA,MAAsB;;;;;IA4V9Bb,EADF,CAAAC,cAAA,eAAoJ,eAC3G;IACrCD,EAAA,CAAAgB,SAAA,aAAyD;IAEvDhB,EADF,CAAAC,cAAA,UAAK,YACc;IAAAD,EAAA,CAAAE,MAAA,+CAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAE,MAAA,mLAAkI;IAGxJF,EAHwJ,CAAAG,YAAA,EAAI,EAClJ,EACF,EACF;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAgB,SAAA,YAA8D;IAChEhB,EAAA,CAAAG,YAAA,EAAM;;;;;IAcEH,EAAA,CAAAC,cAAA,eAEgC;IAC9BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAsC,kBAAA,MAAA5B,MAAA,CAAAmB,aAAA,CAAAsJ,YAAA,OACF;;;;;IA0BUnL,EADF,CAAAC,cAAA,eAA6F,WACrF;IAAAD,EAAA,CAAAgB,SAAA,aAAiC;IAAAhB,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtDH,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAC5DF,EAD4D,CAAAG,YAAA,EAAO,EAC7D;;;;IADkBH,EAAA,CAAAkB,SAAA,GAAoC;IAApClB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,CAAAhE,MAAA,CAAA0K,eAAA,IAAoC;;;;;;IA6B9CpL,EAAA,CAAAC,cAAA,aAGuE;IAApED,EAAA,CAAAI,UAAA,mBAAAiL,uFAAA;MAAArL,EAAA,CAAAO,aAAA,CAAA+K,IAAA;MAAA,MAAAC,QAAA,GAAAvL,EAAA,CAAAW,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA8K,uBAAA,CAAAD,QAAA,CAAAE,SAAA,CAAAhE,OAAA,CAAAnD,SAAA,CAAyD;IAAA,EAAC;IAACtE,EAAA,CAAAG,YAAA,EAAI;;;;IADxEH,EAAA,CAAA0B,UAAA,YAAA6J,QAAA,CAAAG,QAAA,0CAAkE;;;;;IAGrE1L,EAAA,CAAAC,cAAA,gBAAgE;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAX7FH,EAAA,CAAA2L,uBAAA,GAAqE;IAI7D3L,EAHN,CAAAC,cAAA,SAAoC,SAC9B,eACqC,WACU;IAO7CD,EALA,CAAA2B,UAAA,IAAAiK,mEAAA,iBAGuE,IAAAC,sEAAA,oBAEP;IAEhE7L,EAAA,CAAAgB,SAAA,aAGO;IACPhB,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACH,EACH;IACLH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAE,MAAA,IAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9EH,EAAA,CAAAC,cAAA,cAAqB;IACnBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAE,MAAA,IAGF;IACFF,EADE,CAAAG,YAAA,EAAK,EACF;;;;;;IA9BDH,EAAA,CAAAkB,SAAA,EAA+B;IAA/BlB,EAAA,CAAAqB,UAAA,YAAAkK,QAAA,CAAAO,KAAA,CAA+B;IAGvB9L,EAAA,CAAAkB,SAAA,GAAwC;IAAxClB,EAAA,CAAA+L,WAAA,iBAAAR,QAAA,CAAAO,KAAA,YAAwC;IAExC9L,EAAA,CAAAkB,SAAA,EAAsB;IAAtBlB,EAAA,CAAA0B,UAAA,SAAA6J,QAAA,CAAAS,WAAA,CAAsB;IAKnBhM,EAAA,CAAAkB,SAAA,EAAuB;IAAvBlB,EAAA,CAAA0B,UAAA,UAAA6J,QAAA,CAAAS,WAAA,CAAuB;IAEhBhM,EAAA,CAAAkB,SAAA,EAGZ;IAHYlB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAuE,eAAA,KAAA0H,GAAA,EAAAV,QAAA,CAAAE,SAAA,CAAAS,eAAA,kBAAAX,QAAA,CAAAE,SAAA,CAAAS,eAAA,uBAGZ;IACFlM,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAsC,kBAAA,MAAAiJ,QAAA,CAAAE,SAAA,CAAAhE,OAAA,CAAAjG,IAAA,MACF;IAGoBxB,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAAuB,iBAAA,CAAAgK,QAAA,CAAAE,SAAA,CAAA9D,QAAA,CAA6B;IAChC3H,EAAA,CAAAkB,SAAA,GAAoD;IAApDlB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,CAAA6G,QAAA,CAAAE,SAAA,CAAAhE,OAAA,CAAA9C,KAAA,OAAoD;IAEvE3E,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsC,kBAAA,MAAA5B,MAAA,CAAAgE,WAAA,CAAAhE,MAAA,CAAAkE,4BAAA,CAAA2G,QAAA,CAAAE,SAAA,CAAAhE,OAAA,CAAAnD,SAAA,QACF;IAEEtE,EAAA,CAAAkB,SAAA,GAGF;IAHElB,EAAA,CAAAsC,kBAAA,MAAA5B,MAAA,CAAAgE,WAAA,EAAA6G,QAAA,CAAAE,SAAA,CAAAhE,OAAA,CAAA9C,KAAA,SAAA4G,QAAA,CAAAE,SAAA,CAAA9D,QAAA,GAAAjH,MAAA,CAAAkE,4BAAA,CAAA2G,QAAA,CAAAE,SAAA,CAAAhE,OAAA,CAAAnD,SAAA,IAAAiH,QAAA,CAAAE,SAAA,CAAA9D,QAAA,IAAA4D,QAAA,CAAAY,aAAA,aAGF;;;;;IA9DNnM,EALV,CAAAC,cAAA,aAAoD,aACjC,eACO,eACiB,eAC4B,WACvD;IAAAD,EAAA,CAAAgB,SAAA,aAAiC;IAAAhB,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/DH,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEJH,EADF,CAAAC,cAAA,gBAA+D,YACvD;IAAAD,EAAA,CAAAgB,SAAA,cAA+B;IAAAhB,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,iBAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IACNH,EAAA,CAAA2B,UAAA,KAAAyK,sDAAA,mBAA6F;IAK3FpM,EADF,CAAAC,cAAA,gBAAiD,YACzC;IAAAD,EAAA,CAAAgB,SAAA,cAAqC;IAAAhB,EAAA,CAAAE,MAAA,6BAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClEH,EAAA,CAAAC,cAAA,iBAAsB;IAAAD,EAAA,CAAAE,MAAA,IAA4C;IAEtEF,EAFsE,CAAAG,YAAA,EAAO,EACrE,EACF;IAIJH,EADF,CAAAC,cAAA,eAAkB,eACC;IAAAD,EAAA,CAAAE,MAAA,2CAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAI9CH,EAHN,CAAAC,cAAA,kBAAwD,kBAC3B,UACrB,UACE;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,qBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAEbF,EAFa,CAAAG,YAAA,EAAK,EACX,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IAELD,EAAA,CAAA2B,UAAA,KAAA0K,+DAAA,6BAAqE;IAmCnErM,EADF,CAAAC,cAAA,eAA0B,eACiB;IAAAD,EAAA,CAAAE,MAAA,gCAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5DH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAKpEF,EALoE,CAAAG,YAAA,EAAK,EAC5D,EACC,EACF,EACJ,EACF;IAIAH,EAHN,CAAAC,cAAA,gBAAsB,gBACS,gBACyC,mBAC7C;IAAAD,EAAA,CAAAE,MAAA,gCAAc;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,mBAAkC;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IACjEF,EADiE,CAAAG,YAAA,EAAS,EACpE;IAEJH,EADF,CAAAC,cAAA,gBAAyB,kBACG;IAAAD,EAAA,CAAAE,MAAA,IAA+D;IAC3FF,EAD2F,CAAAG,YAAA,EAAQ,EAC7F;IAEJH,EADF,CAAAC,cAAA,gBAA8B,kBACC;IAAAD,EAAA,CAAAE,MAAA,kCAAqB;IAK5DF,EAL4D,CAAAG,YAAA,EAAQ,EACtD,EACF,EACF,EACF,EACF;;;;IAxF0BH,EAAA,CAAAkB,SAAA,GAAyC;IAAzClB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,CAAAhE,MAAA,CAAA4L,oBAAA,IAAyC;IAIzCtM,EAAA,CAAAkB,SAAA,GAAkC;IAAlClB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,CAAAhE,MAAA,CAAA6L,eAAA,EAAkC;IAEMvM,EAAA,CAAAkB,SAAA,EAA2B;IAA3BlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA0K,eAAA,OAA2B;IAMnEpL,EAAA,CAAAkB,SAAA,GAA4C;IAA5ClB,EAAA,CAAAsC,kBAAA,KAAA5B,MAAA,CAAAgI,yBAAA,kBAA4C;IAmBjC1I,EAAA,CAAAkB,SAAA,IAAoC;IAApClB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA8L,+BAAA,GAAoC;IAoCpCxM,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,CAAAhE,MAAA,CAAAiI,UAAA,EAA6B;IAU5B3I,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,CAAAhE,MAAA,CAAAiI,UAAA,EAA6B;IAGrC3I,EAAA,CAAAkB,SAAA,GAA+D;IAA/DlB,EAAA,CAAAsC,kBAAA,wBAAA5B,MAAA,CAAA0H,yBAAA,CAAAlG,MAAA,gBAA+D;;;;;;IAcrGlC,EAAA,CAAAC,cAAA,kBAA6F;IAAnDD,EAAA,CAAAI,UAAA,mBAAAqM,2EAAA;MAAAzM,EAAA,CAAAO,aAAA,CAAAmM,IAAA;MAAA,MAAAhM,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAoI,YAAA,EAAc;IAAA,EAAC;IAChE9I,EAAA,CAAAgB,SAAA,aAAgC;IAAChB,EAAA,CAAAE,MAAA,wCACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IACTH,EAAA,CAAAgB,SAAA,UAAmC;;;;;;IAjIrChB,EAFJ,CAAAC,cAAA,cAAgD,cACrB,SACnB;IAAAD,EAAA,CAAAgB,SAAA,aAAqC;IAAChB,EAAA,CAAAE,MAAA,0BAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5DH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAE,MAAA,4EAAsD;IACxEF,EADwE,CAAAG,YAAA,EAAI,EACtE;IAKFH,EAHJ,CAAAC,cAAA,aAAuB,eAEH,SACZ;IAAAD,EAAA,CAAAE,MAAA,wCAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,gBAAoC;IAClCD,EAAA,CAAA2B,UAAA,KAAAgL,gDAAA,oBAEgC;IAIpC3M,EADE,CAAAG,YAAA,EAAM,EACF;IAMAH,EAHN,CAAAC,cAAA,gBAAkB,gBACmC,gBAC6C,cAC3E;IAAAD,EAAA,CAAAgB,SAAA,cAAqC;IAAAhB,EAAA,CAAAE,MAAA,mCAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjFH,EAAA,CAAAC,cAAA,mBAA8E;IAA7BD,EAAA,CAAAI,UAAA,mBAAAwM,kEAAA;MAAA5M,EAAA,CAAAO,aAAA,CAAAsM,IAAA;MAAA,MAAAnM,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAoM,gBAAA,EAAkB;IAAA,EAAC;IAC3E9M,EAAA,CAAAgB,SAAA,cAAyF;IAE7FhB,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAA2B,UAAA,KAAAoL,+CAAA,oBAAoD;IAgGxD/M,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,gBAA4C;IAI1CD,EAHA,CAAA2B,UAAA,KAAAqL,kDAAA,sBAA6F,KAAAC,+CAAA,kBAGhE;IAE3BjN,EADF,CAAAC,cAAA,gBAA0B,mBACiD;IAAlCD,EAAA,CAAAI,UAAA,mBAAA8M,kEAAA;MAAAlN,EAAA,CAAAO,aAAA,CAAAsM,IAAA;MAAA,MAAAnM,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAyM,qBAAA,EAAuB;IAAA,EAAC;IACtEnN,EAAA,CAAAgB,SAAA,cAA4C;IAAChB,EAAA,CAAAE,MAAA,4BAC/C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAsF;IAApCD,EAAA,CAAAI,UAAA,mBAAAgN,kEAAA;MAAApN,EAAA,CAAAO,aAAA,CAAAsM,IAAA;MAAA,MAAAnM,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA2M,uBAAA,EAAyB;IAAA,EAAC;IACnFrN,EAAA,CAAAgB,SAAA,cAA8B;IAAChB,EAAA,CAAAE,MAAA,sCACjC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAwC;IACtCD,EAAA,CAAAgB,SAAA,cAA0B;IAAChB,EAAA,CAAAE,MAAA,kCAC7B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAuC;IACrCD,EAAA,CAAAgB,SAAA,cAAuC;IAAChB,EAAA,CAAAE,MAAA,0CAC1C;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAxIyBH,EAAA,CAAAkB,SAAA,IAAoB;IAApBlB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAuB,iBAAA,CAAoB;IAazBjC,EAAA,CAAAkB,SAAA,GAAsE;IAAtElB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA4M,oBAAA,uCAAsE;IAGhEtN,EAAA,CAAAkB,SAAA,EAA0B;IAA1BlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA4M,oBAAA,CAA0B;IAoGgBtN,EAAA,CAAAkB,SAAA,GAAuB;IAAvBlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAG,WAAA,OAAuB;IAGrFb,EAAA,CAAAkB,SAAA,EAAqB;IAArBlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAG,WAAA,KAAqB;;;;;IA4Cbb,EAHN,CAAAC,cAAA,cAAsG,aACrC,UACxD,aACK;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7CH,EAAA,CAAAC,cAAA,iBAAkC;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IACrEF,EADqE,CAAAG,YAAA,EAAQ,EACvE;IACNH,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAEpFF,EAFoF,CAAAG,YAAA,EAAO,EACnF,EACH;;;;;IALSH,EAAA,CAAAkB,SAAA,GAA4B;IAA5BlB,EAAA,CAAAuB,iBAAA,CAAAgM,aAAA,CAAA9F,OAAA,CAAAjG,IAAA,CAA4B;IACFxB,EAAA,CAAAkB,SAAA,GAAiC;IAAjClB,EAAA,CAAAuB,iBAAA,CAAAgM,aAAA,CAAA9F,OAAA,CAAAnD,SAAA,CAAiC;IAEpCtE,EAAA,CAAAkB,SAAA,GAA+C;IAA/ClB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,CAAA6I,aAAA,CAAA9F,OAAA,CAAA9C,KAAA,OAA+C;;;;;IARtF3E,EADF,CAAAC,cAAA,eAA8D,cACpC;IAAAD,EAAA,CAAAgB,SAAA,aAA6C;IAAAhB,EAAA,CAAAE,MAAA,0DAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9GH,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAA2B,UAAA,IAAA6L,6CAAA,kBAAsG;IAU1GxN,EADE,CAAAG,YAAA,EAAK,EACD;;;;IAVwBH,EAAA,CAAAkB,SAAA,GAA2B;IAA3BlB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA+M,wBAAA,CAA2B;;;;;IAiFrCzN,EAAA,CAAAC,cAAA,gBAAgF;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAlBlGH,EAFF,CAAAC,cAAA,eACsB,eAIyF;IAAxGD,EAAA,CAAAI,UAAA,mBAAAsN,qEAAA;MAAA,MAAAC,qBAAA,GAAA3N,EAAA,CAAAO,aAAA,CAAAqN,IAAA,EAAAnN,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAmN,uBAAA,CAAAnN,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,EAAAqJ,qBAAA,CAAAI,UAAA,CAA6F;IAAA,EAAC;IAGtG/N,EAFJ,CAAAC,cAAA,aAAuB,cACiB,eACG;IACrCD,EAAA,CAAAgB,SAAA,aAMU;IACZhB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAyB,cACW;IAChCD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAA2B,UAAA,IAAAqM,sDAAA,oBAAgF;IAClFhO,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,cAA+D,iBAC9B;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE1CH,EADF,CAAAC,cAAA,eAAwB,kBAMqF;IAAzGD,EAAA,CAAAI,UAAA,oBAAA6N,yEAAA;MAAA,MAAAN,qBAAA,GAAA3N,EAAA,CAAAO,aAAA,CAAAqN,IAAA,EAAAnN,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAAmN,uBAAA,CAAAnN,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,EAAAqJ,qBAAA,CAAAI,UAAA,CAA6F;IAAA,EAAC;IAOxH/N,EAZc,CAAAG,YAAA,EAK2G,EACvG,EACF,EACF,EACF,EACF,EACF,EACF;;;;;IArCCH,EAAA,CAAAkB,SAAA,EAA6C;IAC7ClB,EADA,CAAAmB,WAAA,aAAAwM,qBAAA,CAAAO,QAAA,CAA6C,sBAAAP,qBAAA,CAAAQ,QAAA,kBAAAR,qBAAA,CAAAQ,QAAA,CAAAC,UAAA,CACqB;IAM5DpO,EAAA,CAAAkB,SAAA,GAKE;IALFlB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAqO,eAAA,KAAAC,GAAA,GAAAX,qBAAA,CAAAQ,QAAA,kBAAAR,qBAAA,CAAAQ,QAAA,CAAAI,YAAA,UAAAZ,qBAAA,CAAAQ,QAAA,kBAAAR,qBAAA,CAAAQ,QAAA,CAAAI,YAAA,UAAAZ,qBAAA,CAAAQ,QAAA,kBAAAR,qBAAA,CAAAQ,QAAA,CAAAI,YAAA,UAAAZ,qBAAA,CAAAQ,QAAA,kBAAAR,qBAAA,CAAAQ,QAAA,CAAAI,YAAA,SAKE;IAIHvO,EAAA,CAAAkB,SAAA,GACA;IADAlB,EAAA,CAAAsC,kBAAA,MAAAqL,qBAAA,CAAAQ,QAAA,kBAAAR,qBAAA,CAAAQ,QAAA,CAAA3M,IAAA,MACA;IAAOxB,EAAA,CAAAkB,SAAA,EAA4C;IAA5ClB,EAAA,CAAA0B,UAAA,SAAAiM,qBAAA,CAAAQ,QAAA,kBAAAR,qBAAA,CAAAQ,QAAA,CAAAC,UAAA,CAA4C;IAGnDpO,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsC,kBAAA,MAAAqL,qBAAA,CAAAQ,QAAA,kBAAAR,qBAAA,CAAAQ,QAAA,CAAA1M,WAAA,MACF;IAOMzB,EAAA,CAAAkB,SAAA,GAAoH;IACpHlB,EADA,CAAA0B,UAAA,8BAAAiM,qBAAA,CAAAQ,QAAA,kBAAAR,qBAAA,CAAAQ,QAAA,CAAAI,YAAA,UAAA7N,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,CAAoH,YAAAqJ,qBAAA,CAAAO,QAAA,CAC9E;;;;;IAYpDlO,EADF,CAAAC,cAAA,aAAqH,cACrF;IAC5BD,EAAA,CAAAgB,SAAA,YAAsC;IACtChB,EAAA,CAAAE,MAAA,wEACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IA2BMH,EAAA,CAAAC,cAAA,gBAAgF;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAlBlGH,EAFF,CAAAC,cAAA,eACsB,eAIyF;IAAxGD,EAAA,CAAAI,UAAA,mBAAAoO,qEAAA;MAAA,MAAAC,qBAAA,GAAAzO,EAAA,CAAAO,aAAA,CAAAmO,IAAA,EAAAjO,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAmN,uBAAA,CAAAnN,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,EAAAmK,qBAAA,CAAAV,UAAA,CAA6F;IAAA,EAAC;IAGtG/N,EAFJ,CAAAC,cAAA,aAAuB,cACiB,eACG;IACrCD,EAAA,CAAAgB,SAAA,aAMU;IACZhB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAyB,cACW;IAChCD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAA2B,UAAA,IAAAgN,sDAAA,oBAAgF;IAClF3O,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,cAA+D,iBACpB;IACvCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAELH,EADF,CAAAC,cAAA,eAAwB,kBAMqF;IAAzGD,EAAA,CAAAI,UAAA,oBAAAwO,yEAAA;MAAA,MAAAH,qBAAA,GAAAzO,EAAA,CAAAO,aAAA,CAAAmO,IAAA,EAAAjO,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAAmN,uBAAA,CAAAnN,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,EAAAmK,qBAAA,CAAAV,UAAA,CAA6F;IAAA,EAAC;IAOxH/N,EAZc,CAAAG,YAAA,EAK2G,EACvG,EACF,EACF,EACF,EACF,EACF,EACF;;;;;IAvCCH,EAAA,CAAAkB,SAAA,EAA6C;IAC7ClB,EADA,CAAAmB,WAAA,aAAAsN,qBAAA,CAAAP,QAAA,CAA6C,sBAAAO,qBAAA,CAAAN,QAAA,kBAAAM,qBAAA,CAAAN,QAAA,CAAAC,UAAA,CACqB;IAM5DpO,EAAA,CAAAkB,SAAA,GAKE;IALFlB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAqO,eAAA,KAAAC,GAAA,GAAAG,qBAAA,CAAAN,QAAA,kBAAAM,qBAAA,CAAAN,QAAA,CAAAI,YAAA,UAAAE,qBAAA,CAAAN,QAAA,kBAAAM,qBAAA,CAAAN,QAAA,CAAAI,YAAA,UAAAE,qBAAA,CAAAN,QAAA,kBAAAM,qBAAA,CAAAN,QAAA,CAAAI,YAAA,UAAAE,qBAAA,CAAAN,QAAA,kBAAAM,qBAAA,CAAAN,QAAA,CAAAI,YAAA,SAKE;IAIHvO,EAAA,CAAAkB,SAAA,GACA;IADAlB,EAAA,CAAAsC,kBAAA,MAAAmM,qBAAA,CAAAN,QAAA,kBAAAM,qBAAA,CAAAN,QAAA,CAAA3M,IAAA,MACA;IAAOxB,EAAA,CAAAkB,SAAA,EAA4C;IAA5ClB,EAAA,CAAA0B,UAAA,SAAA+M,qBAAA,CAAAN,QAAA,kBAAAM,qBAAA,CAAAN,QAAA,CAAAC,UAAA,CAA4C;IAGnDpO,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsC,kBAAA,MAAAmM,qBAAA,CAAAN,QAAA,kBAAAM,qBAAA,CAAAN,QAAA,CAAA1M,WAAA,MACF;IAGIzB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsC,kBAAA,MAAA5B,MAAA,CAAAgE,WAAA,EAAA+J,qBAAA,CAAAN,QAAA,kBAAAM,qBAAA,CAAAN,QAAA,CAAAxJ,KAAA,aACF;IAII3E,EAAA,CAAAkB,SAAA,GAAuE;IAEvElB,EAFA,CAAA0B,UAAA,UAAA+M,qBAAA,CAAAN,QAAA,kBAAAM,qBAAA,CAAAN,QAAA,CAAAU,WAAA,yBAAuE,UAAAJ,qBAAA,CAAAN,QAAA,kBAAAM,qBAAA,CAAAN,QAAA,CAAAU,WAAA,yBAAAJ,qBAAA,CAAAN,QAAA,kBAAAM,qBAAA,CAAAN,QAAA,CAAAI,YAAA,UAAA7N,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,MAC4F,YAAAmK,qBAAA,CAAAP,QAAA,CAC7H;;;;;IAYpDlO,EADF,CAAAC,cAAA,aAAqH,cACrF;IAC5BD,EAAA,CAAAgB,SAAA,YAAsC;IACtChB,EAAA,CAAAE,MAAA,uEACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAaEH,EANR,CAAAC,cAAA,eACsB,eAE+B,aAC1B,cACiB,eACwB;IAC1DD,EAAA,CAAAgB,SAAA,aAA8B;IAChChB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAyB,cACW;IAChCD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,gBAAiC;IAAAD,EAAA,CAAAE,MAAA,SAAC;IACpCF,EADoC,CAAAG,YAAA,EAAO,EACtC;IACLH,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,cAA+D,iBAI1D;IACDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAgB,SAAA,kBAIW;IAOzBhB,EANY,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF,EACF,EACF;;;;;IAjCCH,EAAA,CAAAkB,SAAA,EAA6C;IAA7ClB,EAAA,CAAAmB,WAAA,aAAA2N,qBAAA,CAAAZ,QAAA,CAA6C;IAQxClO,EAAA,CAAAkB,SAAA,GACA;IADAlB,EAAA,CAAAsC,kBAAA,MAAAwM,qBAAA,CAAAX,QAAA,kBAAAW,qBAAA,CAAAX,QAAA,CAAA3M,IAAA,MACA;IAGAxB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsC,kBAAA,MAAAwM,qBAAA,CAAAX,QAAA,kBAAAW,qBAAA,CAAAX,QAAA,CAAA1M,WAAA,MACF;IAEsBzB,EAAA,CAAAkB,SAAA,GAGlB;IAHkBlB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAuE,eAAA,IAAAwK,GAAA,GAAAD,qBAAA,CAAAX,QAAA,kBAAAW,qBAAA,CAAAX,QAAA,CAAAxJ,KAAA,UAAAmK,qBAAA,CAAAX,QAAA,kBAAAW,qBAAA,CAAAX,QAAA,CAAAxJ,KAAA,OAGlB;IACA3E,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAsC,kBAAA,OAAAwM,qBAAA,CAAAX,QAAA,kBAAAW,qBAAA,CAAAX,QAAA,CAAAxJ,KAAA,qBAAAjE,MAAA,CAAAgE,WAAA,EAAAoK,qBAAA,CAAAX,QAAA,kBAAAW,qBAAA,CAAAX,QAAA,CAAAxJ,KAAA,aACF;IAKI3E,EAAA,CAAAkB,SAAA,GAAgB;IAAhBlB,EAAA,CAAA0B,UAAA,iBAAgB;;;;;IAY9B1B,EADF,CAAAC,cAAA,aAAiH,cACjF;IAC5BD,EAAA,CAAAgB,SAAA,YAAsC;IACtChB,EAAA,CAAAE,MAAA,4DACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAjLRH,EAJN,CAAAC,cAAA,UAA0C,cAEyB,cACtB,kBAC4I;IACjLD,EAAA,CAAAgB,SAAA,aAA4C;IAAChB,EAAA,CAAAE,MAAA,uCAC/C;IACFF,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,cAAyC,kBACsI;IAC3KD,EAAA,CAAAgB,SAAA,aAAqD;IAAChB,EAAA,CAAAE,MAAA,sCACxD;IACFF,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,eAAyC,mBACkJ;IACvLD,EAAA,CAAAgB,SAAA,cAA+C;IAAChB,EAAA,CAAAE,MAAA,uCAClD;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACN,EACF;IAMDH,EAHJ,CAAAC,cAAA,gBAAoD,gBAEqD,gBAChF;IA4CnBD,EA3CA,CAAA2B,UAAA,KAAAqN,+CAAA,qBACsB,KAAAC,+CAAA,mBA0C+F;IAOzHjP,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,gBAA2F,gBACpE;IA8CnBD,EA7CA,CAAA2B,UAAA,KAAAuN,+CAAA,qBACsB,KAAAC,+CAAA,mBA4C+F;IAOzHnP,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,gBAAmG,gBAC5E;IAwCnBD,EAvCA,CAAA2B,UAAA,KAAAyN,+CAAA,qBACsB,KAAAC,+CAAA,mBAsC2F;IAQvHrP,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAMAH,EAHN,CAAAC,cAAA,gBAAkC,cAC+B,WACxD,cACc;IAAAD,EAAA,CAAAE,MAAA,oDAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,cAAiC;IAC/BD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACgB;IAAAD,EAAA,CAAAE,MAAA,IAAuF;IAInIF,EAJmI,CAAAG,YAAA,EAAO,EAC9H,EACF,EACF,EACF;;;;IA/KqCH,EAAA,CAAAkB,SAAA,IAA8E;IAA9ElB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA4O,8BAAA,CAAA5O,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,EAAAiL,IAAA,CAA8E;IA2C3GvP,EAAA,CAAAkB,SAAA,EAA8F;IAA9FlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA4O,8BAAA,CAAA5O,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,EAAAiL,IAAA,CAAArN,MAAA,OAA8F;IAYjElC,EAAA,CAAAkB,SAAA,GAA8E;IAA9ElB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA4O,8BAAA,CAAA5O,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,EAAAkL,IAAA,CAA8E;IA6C3GxP,EAAA,CAAAkB,SAAA,EAA8F;IAA9FlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA4O,8BAAA,CAAA5O,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,EAAAkL,IAAA,CAAAtN,MAAA,OAA8F;IAYjElC,EAAA,CAAAkB,SAAA,GAA0E;IAA1ElB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA+O,+BAAA,CAAA/O,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,EAA0E;IAuCvGtE,EAAA,CAAAkB,SAAA,EAA0F;IAA1FlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA+O,+BAAA,CAAA/O,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,EAAApC,MAAA,OAA0F;IAgB9FlC,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsC,kBAAA,MAAA5B,MAAA,CAAAqH,oCAAA,CAAArH,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,wDACF;IAGoCtE,EAAA,CAAAkB,SAAA,GAAuF;IAAvFlB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,CAAAhE,MAAA,CAAAkE,4BAAA,CAAAlE,MAAA,CAAAoN,4BAAA,CAAAxJ,SAAA,GAAuF;;;;;;IA4FjHtE,EAAA,CAAAC,cAAA,eAAwH;IAA9CD,EAAA,CAAAI,UAAA,mBAAAsP,0FAAA;MAAA1P,EAAA,CAAAO,aAAA,CAAAoP,IAAA;MAAA,MAAAC,SAAA,GAAA5P,EAAA,CAAAW,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAmP,kBAAA,CAAAD,SAAA,CAAAtL,SAAA,CAAmC;IAAA,EAAC;IACrHtE,EAAA,CAAAgB,SAAA,aAAyI;IAC3IhB,EAAA,CAAAG,YAAA,EAAM;;;;;IADUH,EAAA,CAAAkB,SAAA,EAAsH;IAAtHlB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAuE,eAAA,IAAAuL,GAAA,EAAApP,MAAA,CAAAqP,cAAA,CAAAH,SAAA,CAAAtL,SAAA,IAAA5D,MAAA,CAAAqP,cAAA,CAAAH,SAAA,CAAAtL,SAAA,GAAsH;;;;;IAEtItE,EAAA,CAAAgB,SAAA,eAAiF;;;;;;IAsB3EhB,EAAA,CAAAC,cAAA,eAAkI;IAAnDD,EAAA,CAAAI,UAAA,mBAAA4P,gHAAA;MAAAhQ,EAAA,CAAAO,aAAA,CAAA0P,IAAA;MAAA,MAAAC,cAAA,GAAAlQ,EAAA,CAAAW,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAmP,kBAAA,CAAAK,cAAA,CAAA5L,SAAA,CAAwC;IAAA,EAAC;IAC/HtE,EAAA,CAAAgB,SAAA,aAAmJ;IACrJhB,EAAA,CAAAG,YAAA,EAAM;;;;;IADUH,EAAA,CAAAkB,SAAA,EAAgI;IAAhIlB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAuE,eAAA,IAAAuL,GAAA,EAAApP,MAAA,CAAAqP,cAAA,CAAAG,cAAA,CAAA5L,SAAA,IAAA5D,MAAA,CAAAqP,cAAA,CAAAG,cAAA,CAAA5L,SAAA,GAAgI;;;;;IAEhJtE,EAAA,CAAAgB,SAAA,eAAsF;;;;;;IAiB1FhB,EAAA,CAAA2L,uBAAA,GAAsF;IAI9E3L,EAHN,CAAAC,cAAA,eAA2B,eACE,eACU,eACU;IACzCD,EAAA,CAAAgB,SAAA,eAAqC;IACrChB,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAgB,SAAA,aAA+C;IACjDhB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA+B,iBAG6B;IAAnDD,EAAA,CAAAI,UAAA,oBAAA+P,mIAAA;MAAA,MAAAC,mBAAA,GAAApQ,EAAA,CAAAO,aAAA,CAAA8P,IAAA,EAAA5P,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAAgG,sBAAA,CAAA0J,mBAAA,CAAuC;IAAA,EAAC;IAC3DpQ,EAHE,CAAAG,YAAA,EAE0D,EACtD;IACNH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5DH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAA6C;IAIjFF,EAJiF,CAAAG,YAAA,EAAM,EAC3E,EACF,EACF,EACF;;;;;;IARWH,EAAA,CAAAkB,SAAA,GAA+C;IAA/ClB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA2D,kBAAA,CAAA+L,mBAAA,EAA+C;IAG5BpQ,EAAA,CAAAkB,SAAA,GAA0B;IAA1BlB,EAAA,CAAAuB,iBAAA,CAAA6O,mBAAA,CAAA5O,IAAA,CAA0B;IAC1BxB,EAAA,CAAAkB,SAAA,GAA6C;IAA7ClB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,CAAA0L,mBAAA,CAAAzL,KAAA,OAA6C;;;;;IAhBrF3E,EAAA,CAAAC,cAAA,eAAuH;IACrHD,EAAA,CAAA2B,UAAA,IAAA2O,0GAAA,4BAAsF;IAqBxFtQ,EAAA,CAAAG,YAAA,EAAM;;;;;IArBsCH,EAAA,CAAAkB,SAAA,EAA0C;IAA1ClB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA6P,iBAAA,CAAAL,cAAA,CAAA5L,SAAA,EAA0C;;;;;;IAzB1FtE,EAAA,CAAA2L,uBAAA,GAA4E;IAIpE3L,EAHN,CAAAC,cAAA,eAA2B,eACE,eACU,eACU;IAIzCD,EAHA,CAAA2B,UAAA,IAAA6O,0FAAA,mBAAkI,IAAAC,0FAAA,mBAGlD;IAChFzQ,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAgB,SAAA,aAAiL;IACnLhB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA+B,kBAGwB;IAA9CD,EAAA,CAAAI,UAAA,oBAAAsQ,8GAAA;MAAA,MAAAR,cAAA,GAAAlQ,EAAA,CAAAO,aAAA,CAAAoQ,IAAA,EAAAlQ,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAAgG,sBAAA,CAAAwJ,cAAA,CAAkC;IAAA,EAAC;IACtDlQ,EAHE,CAAAG,YAAA,EAEqD,EACjD;IACNH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAwC;IAG1EF,EAH0E,CAAAG,YAAA,EAAM,EACtE,EACF,EACF;IAGNH,EAAA,CAAA2B,UAAA,KAAAiP,2FAAA,mBAAuH;IAuBzH5Q,EAAA,CAAAG,YAAA,EAAM;;;;;;IA1CkCH,EAAA,CAAAkB,SAAA,GAA6C;IAA7ClB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAsE,iBAAA,CAAAkL,cAAA,CAAA5L,SAAA,EAA6C;IAG7CtE,EAAA,CAAAkB,SAAA,EAA8C;IAA9ClB,EAAA,CAAA0B,UAAA,UAAAhB,MAAA,CAAAsE,iBAAA,CAAAkL,cAAA,CAAA5L,SAAA,EAA8C;IAE9DtE,EAAA,CAAAkB,SAAA,GAA8J;IAA9JlB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAuE,eAAA,IAAAsM,GAAA,EAAAnQ,MAAA,CAAAsE,iBAAA,CAAAkL,cAAA,CAAA5L,SAAA,IAAA5D,MAAA,CAAAsE,iBAAA,CAAAkL,cAAA,CAAA5L,SAAA,GAA8J;IAIrKtE,EAAA,CAAAkB,SAAA,GAA0C;IAA1ClB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA2D,kBAAA,CAAA6L,cAAA,EAA0C;IAGvBlQ,EAAA,CAAAkB,SAAA,GAAqB;IAArBlB,EAAA,CAAAuB,iBAAA,CAAA2O,cAAA,CAAA1O,IAAA,CAAqB;IACrBxB,EAAA,CAAAkB,SAAA,GAAwC;IAAxClB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,CAAAwL,cAAA,CAAAvL,KAAA,OAAwC;IAM1C3E,EAAA,CAAAkB,SAAA,EAAqF;IAArFlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAsE,iBAAA,CAAAkL,cAAA,CAAA5L,SAAA,KAAA5D,MAAA,CAAAqP,cAAA,CAAAG,cAAA,CAAA5L,SAAA,EAAqF;;;;;IAzB3HtE,EAAA,CAAAC,cAAA,eAA6G;IAC3GD,EAAA,CAAA2B,UAAA,IAAAmP,oFAAA,6BAA4E;IAiD9E9Q,EAAA,CAAAG,YAAA,EAAM;;;;;IAjDiCH,EAAA,CAAAkB,SAAA,EAAqC;IAArClB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA6P,iBAAA,CAAAX,SAAA,CAAAtL,SAAA,EAAqC;;;;;;IAzBhFtE,EAAA,CAAA2L,uBAAA,GAA2F;IAInF3L,EAHN,CAAAC,cAAA,eAA2B,eACE,eACU,eACU;IAIzCD,EAHA,CAAA2B,UAAA,IAAAoP,oEAAA,mBAAwH,IAAAC,oEAAA,mBAG7C;IAC3EhR,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAgB,SAAA,aAAuK;IACzKhB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA+B,kBAGmB;IAAzCD,EAAA,CAAAI,UAAA,oBAAA6Q,wFAAA;MAAA,MAAArB,SAAA,GAAA5P,EAAA,CAAAO,aAAA,CAAA2Q,IAAA,EAAAzQ,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAAgG,sBAAA,CAAAkJ,SAAA,CAA6B;IAAA,EAAC;IACjD5P,EAHE,CAAAG,YAAA,EAEgD,EAC5C;IACNH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClDH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAGrEF,EAHqE,CAAAG,YAAA,EAAM,EACjE,EACF,EACF;IAGNH,EAAA,CAAA2B,UAAA,KAAAwP,qEAAA,mBAA6G;IAmD/GnR,EAAA,CAAAG,YAAA,EAAM;;;;;;IAtEkCH,EAAA,CAAAkB,SAAA,GAAwC;IAAxClB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAsE,iBAAA,CAAA4K,SAAA,CAAAtL,SAAA,EAAwC;IAGxCtE,EAAA,CAAAkB,SAAA,EAAyC;IAAzClB,EAAA,CAAA0B,UAAA,UAAAhB,MAAA,CAAAsE,iBAAA,CAAA4K,SAAA,CAAAtL,SAAA,EAAyC;IAEzDtE,EAAA,CAAAkB,SAAA,GAAoJ;IAApJlB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAuE,eAAA,IAAAsM,GAAA,EAAAnQ,MAAA,CAAAsE,iBAAA,CAAA4K,SAAA,CAAAtL,SAAA,IAAA5D,MAAA,CAAAsE,iBAAA,CAAA4K,SAAA,CAAAtL,SAAA,GAAoJ;IAI3JtE,EAAA,CAAAkB,SAAA,GAAqC;IAArClB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA2D,kBAAA,CAAAuL,SAAA,EAAqC;IAGlB5P,EAAA,CAAAkB,SAAA,GAAgB;IAAhBlB,EAAA,CAAAuB,iBAAA,CAAAqO,SAAA,CAAApO,IAAA,CAAgB;IAChBxB,EAAA,CAAAkB,SAAA,GAAmC;IAAnClB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,CAAAkL,SAAA,CAAAjL,KAAA,OAAmC;IAMrC3E,EAAA,CAAAkB,SAAA,EAA2E;IAA3ElB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAsE,iBAAA,CAAA4K,SAAA,CAAAtL,SAAA,KAAA5D,MAAA,CAAAqP,cAAA,CAAAH,SAAA,CAAAtL,SAAA,EAA2E;;;;;IAzBjHtE,EAAA,CAAAC,cAAA,eAAsD;IACpDD,EAAA,CAAA2B,UAAA,IAAAyP,8DAAA,6BAA2F;IA6E7FpR,EAAA,CAAAG,YAAA,EAAM;;;;IA7E4BH,EAAA,CAAAkB,SAAA,EAAyD;IAAzDlB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA6P,iBAAA,EAAA7P,MAAA,CAAA2Q,kBAAA,kBAAA3Q,MAAA,CAAA2Q,kBAAA,CAAA/M,SAAA,SAAyD;;;;;;IAnBrFtE,EARZ,CAAAC,cAAA,UAAgC,eAEE,eAEO,eACR,eACU,eACU,eACqB;IAA/BD,EAAA,CAAAI,UAAA,mBAAAkR,8DAAA;MAAAtR,EAAA,CAAAO,aAAA,CAAAgR,IAAA;MAAA,MAAA7Q,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA8Q,kBAAA,EAAoB;IAAA,EAAC;IAC3DxR,EAAA,CAAAgB,SAAA,aAAuG;IACzGhB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAgB,SAAA,aAA8C;IAChDhB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,gBAA+B,kBAGgC;IAAtDD,EAAA,CAAAI,UAAA,oBAAAqR,kEAAA;MAAAzR,EAAA,CAAAO,aAAA,CAAAgR,IAAA;MAAA,MAAA7Q,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAAgG,sBAAA,CAAAhG,MAAA,CAAA2Q,kBAAA,CAA0C;IAAA,EAAC;IAC9DrR,EAHE,CAAAG,YAAA,EAE6D,EACzD;IACNH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChEH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAiD;IAGnFF,EAHmF,CAAAG,YAAA,EAAM,EAC/E,EACF,EACF;IAGNH,EAAA,CAAA2B,UAAA,KAAA+P,+CAAA,mBAAsD;IAgF1D1R,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,gBAAmC;IACjCD,EAAA,CAAAgB,SAAA,aAAsC;IACtChB,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,uLAEtC;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAzGsBH,EAAA,CAAAkB,SAAA,GAAoF;IAApFlB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAuE,eAAA,IAAAuL,GAAA,EAAApP,MAAA,CAAAiR,cAAA,GAAAjR,MAAA,CAAAiR,cAAA,EAAoF;IAO3F3R,EAAA,CAAAkB,SAAA,GAAkD;IAAlDlB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA2D,kBAAA,CAAA3D,MAAA,CAAA2Q,kBAAA,EAAkD;IAG/BrR,EAAA,CAAAkB,SAAA,GAA8B;IAA9BlB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAA2Q,kBAAA,kBAAA3Q,MAAA,CAAA2Q,kBAAA,CAAA7P,IAAA,CAA8B;IAC9BxB,EAAA,CAAAkB,SAAA,GAAiD;IAAjDlB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAgE,WAAA,EAAAhE,MAAA,CAAA2Q,kBAAA,kBAAA3Q,MAAA,CAAA2Q,kBAAA,CAAA1M,KAAA,QAAiD;IAMnD3E,EAAA,CAAAkB,SAAA,EAAoB;IAApBlB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAiR,cAAA,CAAoB;;;AD75BtE,OAAM,MAAOC,wBAAwB;EA+FnC;EACAC,YAAA;IA9FA;IACA,KAAAC,WAAW,GAAclS,oBAAoB;IAC7C,KAAAmS,iBAAiB,GAAc,EAAE;IACjC,KAAAhH,gBAAgB,GAAc,EAAE;IAEhC;IACA,KAAAiH,SAAS,GAAY,KAAK;IAE1B;IACQ,KAAAC,gBAAgB,GAAG,IAAIC,GAAG,EAAoB;IAEtD;IACQ,KAAAC,iBAAiB,GAAG,IAAID,GAAG,EAAqB;IAExD;IACS,KAAAE,cAAc,GAAG1S,oBAAoB;IACrC,KAAAC,YAAY,GAAGA,YAAY;IAEpC;IACA,KAAA0S,mBAAmB,GAAW,CAAC;IAC/B,KAAA1P,WAAW,GAAUhD,YAAY;IAEjC;IACA;IACA,KAAA+C,gBAAgB,GAAQ;MACtB3B,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,eAAe;MACrBC,WAAW,EAAE;KACd;IACD,KAAAQ,iBAAiB,GAAa,EAAE;IAChC,KAAAqQ,oBAAoB,GAAuB,EAAE;IAC7C,KAAAlK,yBAAyB,GAAuB,EAAE;IAElD;IACA,KAAAmK,gBAAgB,GAAe,EAAE;IACjC,KAAAC,iBAAiB,GAAe,EAAE;IAClC,KAAAC,4BAA4B,GAAY,KAAK;IAC7C,KAAApB,kBAAkB,GAAmB,IAAI;IAEzC;IACA,KAAAqB,eAAe,GAAmB,IAAI;IACtC,KAAAjF,wBAAwB,GAAuB,EAAE;IACjD,KAAAK,4BAA4B,GAAmB,IAAI;IAGnD;IACA,KAAAR,oBAAoB,GAAY,IAAI;IACpC,KAAAqF,uBAAuB,GAAY,IAAI;IACvC,KAAAC,sBAAsB,GAAG,IAAIC,GAAG,EAAU;IAE1C;IACA,KAAAC,aAAa,GAAsBhT,kBAAkB;IACrD,KAAAiT,kBAAkB,GAAwB,EAAE;IAC5C,KAAAC,wBAAwB,GAAG,IAAId,GAAG,EAAmB;IACrD,KAAA3F,eAAe,GAAW,CAAC;IAG3B;IACO,KAAA1L,WAAW,GAAW,CAAC;IACvB,KAAAoS,QAAQ,GAAW,CAAC;IAE3B;IACA,KAAAC,iBAAiB,GAAG,IAAIL,GAAG,EAAU;IAErC;IACA,KAAAjQ,YAAY,GAAW,EAAE;IACzB,KAAAC,cAAc,GAAW,EAAE;IAE3B;IACA,KAAAsD,gBAAgB,GAAG,IAAI+L,GAAG,EAAmB;IAC7C,KAAAvJ,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAwK,kBAAkB,GAA8B,IAAI;IAEpD;IACA,KAAAhQ,cAAc,GAAmB;MAC/BC,UAAU,EAAE,EAAE;MACdqG,YAAY,EAAE,KAAK;MACnBI,UAAU,EAAE;KACb;IAED,KAAAtE,UAAU,GAAqB;MAC7BC,WAAW,EAAE,CAAC;MACdwE,YAAY,EAAE,EAAE;MAChBY,UAAU,EAAE,CAAC;MACbwI,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;KACrC;IAED;IACA,KAAAzB,cAAc,GAAY,IAAI;IAC9B,KAAA0B,aAAa,GAAG,IAAIR,GAAG,EAAU;IA4RjC;IACQ,KAAAS,kBAAkB,GAAG,IAAIpB,GAAG,EAAkD;IAEtF;IACQ,KAAAqB,mBAAmB,GAAG,IAAIrB,GAAG,EAAkB;IAEvD;IACQ,KAAAsB,gBAAgB,GAAiE;MACvFC,UAAU,EAAE,CAAC;MACbjO,WAAW,EAAE,CAAC;MACdkO,KAAK,EAAE;KACR;IAED;IACQ,KAAAC,oBAAoB,GAAG,IAAIzB,GAAG,EAAkB;IAExD;IACQ,KAAA0B,cAAc,GAAG,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACtDC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC;EA7Sa;EAEf;;;EAGAC,qBAAqBA,CAAA;IACnB,OAAOC,IAAI,CAACC,KAAK,CAAE,IAAI,CAACtT,WAAW,GAAG,IAAI,CAACoS,QAAQ,GAAI,GAAG,CAAC;EAC7D;EAEA;;;EAGAnS,qBAAqBA,CAACsT,QAAgB;IACpC,MAAMC,KAAK,GAAG,IAAI,CAACpS,iBAAiB,CAACqS,OAAO,CAACF,QAAQ,CAAC;IAEtD,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACpS,iBAAiB,CAACsS,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;KACxC,MAAM;MACL,IAAI,CAACpS,iBAAiB,CAACuS,IAAI,CAACJ,QAAQ,CAAC;;IAGvC,IAAI,CAACK,qBAAqB,EAAE;EAC9B;EAEA;;;EAGArT,gBAAgBA,CAACgT,QAAgB;IAC/B,OAAO,IAAI,CAACnS,iBAAiB,CAACyS,QAAQ,CAACN,QAAQ,CAAC;EAClD;EAEA;;;EAGAvS,aAAaA,CAACuS,QAAgB;IAC5B,MAAMO,MAAM,GAAG,IAAI,CAAChS,WAAW,CAACiS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9T,EAAE,KAAKqT,QAAQ,CAAC;IAC5D,OAAOO,MAAM,GAAGA,MAAM,CAACnT,IAAI,GAAG4S,QAAQ;EACxC;EAEA;;;EAGAK,qBAAqBA,CAAA;IACnB;IACAK,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IAElE;IACA,IAAI,CAAChD,iBAAiB,GAAGnS,oBAAoB;IAE7CkV,OAAO,CAACC,GAAG,CAAC,GAAG,IAAI,CAAChD,iBAAiB,CAAC7P,MAAM,uBAAuB,CAAC;IACpE,IAAI,CAAC8S,yBAAyB,EAAE;IAChC,IAAI,CAAChD,SAAS,GAAG,KAAK;EACxB;EAEA;EACAiD,QAAQA,CAAA;IACNH,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IAClE,IAAI,CAACG,uBAAuB,EAAE;EAChC;EAEA;;;EAGAxR,sBAAsBA,CAAC+D,OAAgB;IACrC,IAAI,CAAC4J,kBAAkB,GAAG5J,OAAO;IACjC,IAAI,CAACkK,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC0B,aAAa,CAAC8B,KAAK,EAAE;IAE1B;IACA,MAAMC,cAAc,GAAG,IAAI,CAAC7E,iBAAiB,CAAC9I,OAAO,CAACnD,SAAS,CAAC;IAChE8Q,cAAc,CAACC,OAAO,CAACC,KAAK,IAAG;MAC7B,IAAI,IAAI,CAACtQ,iBAAiB,CAACsQ,KAAK,CAAChR,SAAS,CAAC,EAAE;QAC3C,IAAI,CAAC+O,aAAa,CAACkC,GAAG,CAACD,KAAK,CAAChR,SAAS,CAAC;;IAE3C,CAAC,CAAC;IAEF;IACA,MAAMkR,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,yBAAyB,CAAC;IACvE,IAAIF,YAAY,EAAE;MAChB;MACA,MAAMG,KAAK,GAAG,IAAIC,SAAS,CAACC,KAAK,CAACL,YAAY,CAAC;MAC/CG,KAAK,CAACG,IAAI,EAAE;MACZ,IAAI,CAACC,QAAQ,GAAGJ,KAAK;;EAEzB;EAEA;;;EAGAnE,kBAAkBA,CAAA;IAChB,IAAI,CAACG,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEA;;;EAGA9B,kBAAkBA,CAACmG,MAAc;IAC/B,IAAI,IAAI,CAAC3C,aAAa,CAAC4C,GAAG,CAACD,MAAM,CAAC,EAAE;MAClC,IAAI,CAAC3C,aAAa,CAAC6C,MAAM,CAACF,MAAM,CAAC;KAClC,MAAM;MACL,IAAI,CAAC3C,aAAa,CAACkC,GAAG,CAACS,MAAM,CAAC;;EAElC;EAEA;;;EAGAjG,cAAcA,CAACiG,MAAc;IAC3B,OAAO,IAAI,CAAC3C,aAAa,CAAC4C,GAAG,CAACD,MAAM,CAAC;EACvC;EAEA;;;EAGAG,gBAAgBA,CAACC,WAAoB;IACnC;IACA,IAAI,CAAC7D,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAE3B;IACA,MAAM6D,QAAQ,GAAa;MACzBC,GAAG,EAAEF,WAAW,CAAC9R,SAAS;MAC1BiS,KAAK,EAAEH,WAAW,CAAC5U,IAAI;MACvBgV,IAAI,EAAEJ,WAAW;MACjB9U,IAAI,EAAE,cAAc;MACpBoK,QAAQ,EAAE,IAAI;MACd+K,QAAQ,EAAE;KACX;IAED;IACA,MAAMrB,cAAc,GAAG,IAAI,CAAC7E,iBAAiB,CAAC6F,WAAW,CAAC9R,SAAS,CAAC;IACpE,IAAI8Q,cAAc,CAAClT,MAAM,GAAG,CAAC,EAAE;MAC7BmU,QAAQ,CAACI,QAAQ,GAAG,IAAI,CAACC,eAAe,CAACtB,cAAc,EAAE,CAAC,CAAC;;IAG7D,IAAI,CAAC7C,gBAAgB,GAAG,CAAC8D,QAAQ,CAAC;EACpC;EAEA;;;EAGQK,eAAeA,CAACC,QAAmB,EAAE7K,KAAa;IACxD,OAAO6K,QAAQ,CAACC,GAAG,CAACnP,OAAO,IAAG;MAC5B,MAAMoP,IAAI,GAAa;QACrBP,GAAG,EAAE7O,OAAO,CAACnD,SAAS;QACtBiS,KAAK,EAAE9O,OAAO,CAACjG,IAAI;QACnBgV,IAAI,EAAE/O,OAAO;QACbnG,IAAI,EAAEwK,KAAK,KAAK,CAAC,GAAG,YAAY,GAAG,WAAW;QAC9CJ,QAAQ,EAAEI,KAAK,GAAG,CAAC;QACnB2K,QAAQ,EAAE;OACX;MAED;MACA,MAAMA,QAAQ,GAAG,IAAI,CAAClG,iBAAiB,CAAC9I,OAAO,CAACnD,SAAS,CAAC;MAC1D,IAAImS,QAAQ,CAACvU,MAAM,GAAG,CAAC,EAAE;QACvB2U,IAAI,CAACJ,QAAQ,GAAG,IAAI,CAACC,eAAe,CAACD,QAAQ,EAAE3K,KAAK,GAAG,CAAC,CAAC;;MAG3D,OAAO+K,IAAI;IACb,CAAC,CAAC;EACJ;EAEA;;;EAGAC,yBAAyBA,CAAA;IACvB;IACA,IAAI,IAAI,CAACf,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACgB,IAAI,EAAE;;IAGtB;IACA,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;;;EAGA9B,uBAAuBA,CAAA;IACrBJ,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IAElE;IACA,IAAI,CAAC1C,mBAAmB,GAAG4E,QAAQ,CAAC,IAAI,CAAC7E,cAAc,CAACrR,EAAE,CAAC;IAC3D,IAAI,CAAC2B,gBAAgB,GAAG,IAAI,CAAC0P,cAAc;IAE3C0C,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACrS,gBAAgB,CAAC;IAEvE;IACA,IAAI,CAACwU,yBAAyB,EAAE;IAEhC,IAAI,CAAClF,SAAS,GAAG,KAAK;EACxB;EAEA;;;EAGQkF,yBAAyBA,CAAA;IAC/B;IACA,IAAI,CAACpF,WAAW,CAACuD,OAAO,CAAC5N,OAAO,IAAG;MACjC,IAAIA,OAAO,CAAChD,eAAe,EAAE;QAC3B;QACA,MAAM0S,QAAQ,GAAG1P,OAAO,CAAChD,eAAe;QACxC,IAAI,CAAC,IAAI,CAAC0N,iBAAiB,CAAC8D,GAAG,CAACkB,QAAQ,CAAC,EAAE;UACzC,IAAI,CAAChF,iBAAiB,CAACiF,GAAG,CAACD,QAAQ,EAAE,EAAE,CAAC;;QAE1C,IAAI,CAAChF,iBAAiB,CAACkF,GAAG,CAACF,QAAQ,CAAE,CAAC3C,IAAI,CAAC/M,OAAO,CAAC;;IAEvD,CAAC,CAAC;EACJ;EAEA6P,WAAWA,CAAA;IACT,IAAI,CAACC,aAAa,EAAE;IACpB;IACA,IAAI,CAACtF,gBAAgB,CAACkD,KAAK,EAAE;IAC7B,IAAI,CAAChD,iBAAiB,CAACgD,KAAK,EAAE;IAC9B,IAAI,CAAC7B,kBAAkB,CAAC6B,KAAK,EAAE;IAC/B,IAAI,CAAC5B,mBAAmB,CAAC4B,KAAK,EAAE;IAChC,IAAI,CAACxB,oBAAoB,CAACwB,KAAK,EAAE;IACjC,IAAI,CAAC3B,gBAAgB,GAAG;MAAEC,UAAU,EAAE,CAAC;MAAEjO,WAAW,EAAE,CAAC;MAAEkO,KAAK,EAAE;IAAE,CAAE;EACtE;EAEA;EACQ6D,aAAaA,CAAA;IACnB,IAAI,CAAC3U,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,cAAc,GAAG,EAAE;EAC1B;EAEA;EACA0N,iBAAiBA,CAAC1J,SAAiB;IACjC,OAAO,IAAI,CAACsL,iBAAiB,CAACkF,GAAG,CAACxQ,SAAS,CAAC,IAAI,EAAE;EACpD;EAEA;EACA7B,iBAAiBA,CAAC6B,SAAiB;IACjC,OAAO,IAAI,CAACsL,iBAAiB,CAAC8D,GAAG,CAACpP,SAAS,CAAC,IAAI,IAAI,CAACsL,iBAAiB,CAACkF,GAAG,CAACxQ,SAAS,CAAE,CAAC3E,MAAM,GAAG,CAAC;EACnG;EAEA;EACAsV,8BAA8BA,CAAC/P,OAAgB;IAC7C,MAAMgQ,iBAAiB,GAAG,IAAI,CAACC,gCAAgC,CAACjQ,OAAO,CAACnD,SAAS,CAAC;IAElFmT,iBAAiB,CAACpC,OAAO,CAAClH,QAAQ,IAAG;MACnC,MAAMwJ,iBAAiB,GAAsB;QAC3CrT,SAAS,EAAEmD,OAAO,CAACnD,SAAS;QAC5ByJ,UAAU,EAAEI,QAAQ,CAACJ,UAAU;QAC/BG,QAAQ,EAAE,IAAI,CAAC0J,iBAAiB,CAACnQ,OAAO,CAACnD,SAAS,EAAE6J,QAAQ,CAACJ,UAAU,CAAC,IAAII,QAAQ,CAACC,UAAU;QAC/FyJ,aAAa,EAAE,IAAIC,IAAI,EAAE;QACzBrQ,OAAO,EAAEA,OAAO;QAChB0G,QAAQ,EAAEA;OACX;MACD,IAAI,CAAC4E,kBAAkB,CAACyB,IAAI,CAACmD,iBAAiB,CAAC;IACjD,CAAC,CAAC;EACJ;EAEA;EACAX,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACrD,oBAAoB,CAACwB,KAAK,EAAE;IACjC,IAAI,CAAC5B,mBAAmB,CAAC4B,KAAK,EAAE;IAEhC;IACA,IAAI4C,aAAa,GAAG,CAAC;IAErB,IAAI,CAAC3P,yBAAyB,CAACiN,OAAO,CAAC5J,SAAS,IAAG;MACjD;MACA,MAAMuM,SAAS,GAAG,CAACvM,SAAS,CAAChE,OAAO,CAAC9C,KAAK,IAAI,CAAC,IAAI8G,SAAS,CAAC9D,QAAQ;MAErE;MACA,MAAM4E,eAAe,GAAG,IAAI,CAAC3H,4BAA4B,CAAC6G,SAAS,CAAChE,OAAO,CAACnD,SAAS,CAAC,GAAGmH,SAAS,CAAC9D,QAAQ;MAE3GoQ,aAAa,IAAIC,SAAS,GAAGzL,eAAe;IAC9C,CAAC,CAAC;IAEF,IAAI,CAACA,eAAe,GAAG1M,aAAa,CAACoY,wBAAwB,CAC3D,IAAI,CAAClF,kBAAkB,EACvB,IAAI,CAACD,aAAa,CACnB;IAED,IAAI,CAACnK,UAAU,GAAGoP,aAAa;EACjC;EAwBA;;;EAGArT,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAI,CAACiP,cAAc,CAACsE,MAAM,CAACvT,KAAK,CAAC;EAC1C;EAEA;;;EAGA+S,gCAAgCA,CAAC7Q,SAAiB;IAChD,MAAMsR,YAAY,GAAGpY,6BAA6B,CAACqY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC/T,SAAS,KAAKuC,SAAS,CAAC;IACzF,OAAOsR,YAAY,CAACvB,GAAG,CAAC0B,WAAW,IACjC,IAAI,CAACxF,aAAa,CAAC8B,IAAI,CAAC2D,CAAC,IAAIA,CAAC,CAACxK,UAAU,KAAKuK,WAAW,CAACvK,UAAU,CAAC,CACtE,CAACqK,MAAM,CAACG,CAAC,IAAIA,CAAC,KAAKC,SAAS,CAAsB;EACrD;EAEA;;;EAGAZ,iBAAiBA,CAAC/Q,SAAiB,EAAE4R,UAAkB;IACrD,MAAMH,WAAW,GAAGvY,6BAA6B,CAAC6U,IAAI,CACpDyD,CAAC,IAAIA,CAAC,CAAC/T,SAAS,KAAKuC,SAAS,IAAIwR,CAAC,CAACtK,UAAU,KAAK0K,UAAU,CAC9D;IACD,OAAOH,WAAW,EAAEI,SAAS,IAAI,KAAK;EACxC;EAEA;;;EAGA9T,4BAA4BA,CAACiC,SAAiB;IAC5C;IACA,IAAI,IAAI,CAAC8M,oBAAoB,CAACsC,GAAG,CAACpP,SAAS,CAAC,EAAE;MAC5C,OAAO,IAAI,CAAC8M,oBAAoB,CAAC0D,GAAG,CAACxQ,SAAS,CAAE;;IAGlD,MAAM8B,UAAU,GAAG,IAAI,CAACoK,kBAAkB,CACvCqF,MAAM,CAACO,EAAE,IAAIA,EAAE,CAACrU,SAAS,KAAKuC,SAAS,IAAI8R,EAAE,CAACzK,QAAQ,CAAC,CACvD0K,MAAM,CAAC,CAACC,KAAK,EAAEF,EAAE,KAAKE,KAAK,IAAIF,EAAE,CAACxK,QAAQ,EAAExJ,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAE9D;IACA,IAAI,CAACgP,oBAAoB,CAACyD,GAAG,CAACvQ,SAAS,EAAE8B,UAAU,CAAC;IAEpD,OAAOA,UAAU;EACnB;EAEA;;;EAGAmQ,iBAAiBA,CAACjS,SAAiB;IACjC;IACA,IAAI,IAAI,CAACoL,gBAAgB,CAACgE,GAAG,CAACpP,SAAS,CAAC,EAAE;MACxC,OAAO,IAAI,CAACoL,gBAAgB,CAACoF,GAAG,CAACxQ,SAAS,CAAE;;IAG9C,MAAMkS,WAAW,GAAa,EAAE;IAChC,MAAM3D,cAAc,GAAG,IAAI,CAACtD,WAAW,CAACsG,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC9T,eAAe,KAAKoC,SAAS,CAAC;IAEpFuO,cAAc,CAACC,OAAO,CAACC,KAAK,IAAG;MAC7ByD,WAAW,CAACvE,IAAI,CAACc,KAAK,CAAChR,SAAS,CAAC;MACjC;MACA,MAAM0U,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,CAACxD,KAAK,CAAChR,SAAS,CAAC;MAChEyU,WAAW,CAACvE,IAAI,CAAC,GAAGwE,gBAAgB,CAAC;IACvC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC/G,gBAAgB,CAACmF,GAAG,CAACvQ,SAAS,EAAEkS,WAAW,CAAC;IAEjD,OAAOA,WAAW;EACpB;EAEA;EACAjT,aAAaA,CAAA;IACX,IAAI,IAAI,CAACP,UAAU,CAACyE,YAAY,KAAK,CAAC,EAAE,OAAO,CAAC;IAChD,OAAOkK,IAAI,CAAC+E,IAAI,CAAC,IAAI,CAAC1T,UAAU,CAACqF,UAAU,GAAG,IAAI,CAACrF,UAAU,CAACyE,YAAY,CAAC;EAC7E;EAEA5E,YAAYA,CAAC8T,IAAY;IACvB,IAAI,CAAC3T,UAAU,CAACC,WAAW,GAAG0T,IAAI;IAClC,IAAI,CAAClE,yBAAyB,EAAE;EAClC;EAEAnP,cAAcA,CAAA;IACZ,MAAM4N,UAAU,GAAG,IAAI,CAAC3N,aAAa,EAAE;IACvC,MAAMqT,OAAO,GAAG,IAAI,CAAC5T,UAAU,CAACC,WAAW;IAE3C;IACA,IAAI,IAAI,CAACgO,gBAAgB,CAACC,UAAU,KAAKA,UAAU,IAC/C,IAAI,CAACD,gBAAgB,CAAChO,WAAW,KAAK2T,OAAO,EAAE;MACjD,OAAO,IAAI,CAAC3F,gBAAgB,CAACE,KAAK;;IAGpC,MAAMA,KAAK,GAAa,EAAE;IAE1B,IAAID,UAAU,IAAI,CAAC,EAAE;MACnB,KAAK,IAAI2F,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI3F,UAAU,EAAE2F,CAAC,EAAE,EAAE;QACpC1F,KAAK,CAACc,IAAI,CAAC4E,CAAC,CAAC;;KAEhB,MAAM;MACL,IAAID,OAAO,IAAI,CAAC,EAAE;QAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE1F,KAAK,CAACc,IAAI,CAAC4E,CAAC,CAAC;QAC1C1F,KAAK,CAACc,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChBd,KAAK,CAACc,IAAI,CAACf,UAAU,CAAC;OACvB,MAAM,IAAI0F,OAAO,IAAI1F,UAAU,GAAG,CAAC,EAAE;QACpCC,KAAK,CAACc,IAAI,CAAC,CAAC,CAAC;QACbd,KAAK,CAACc,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,KAAK,IAAI4E,CAAC,GAAG3F,UAAU,GAAG,CAAC,EAAE2F,CAAC,IAAI3F,UAAU,EAAE2F,CAAC,EAAE,EAAE1F,KAAK,CAACc,IAAI,CAAC4E,CAAC,CAAC;OACjE,MAAM;QACL1F,KAAK,CAACc,IAAI,CAAC,CAAC,CAAC;QACbd,KAAK,CAACc,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,KAAK,IAAI4E,CAAC,GAAGD,OAAO,GAAG,CAAC,EAAEC,CAAC,IAAID,OAAO,GAAG,CAAC,EAAEC,CAAC,EAAE,EAAE1F,KAAK,CAACc,IAAI,CAAC4E,CAAC,CAAC;QAC9D1F,KAAK,CAACc,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChBd,KAAK,CAACc,IAAI,CAACf,UAAU,CAAC;;;IAI1B;IACA,IAAI,CAACD,gBAAgB,GAAG;MACtBC,UAAU;MACVjO,WAAW,EAAE2T,OAAO;MACpBzF,KAAK,EAAE,CAAC,GAAGA,KAAK;KACjB;IAED,OAAOA,KAAK;EACd;EAEA;EACA5I,6BAA6BA,CAAA;IAC3B,MAAMuO,SAAS,GAAG,IAAIxG,GAAG,CAAC,IAAI,CAACd,iBAAiB,CAAC6E,GAAG,CAAC2B,CAAC,IAAIA,CAAC,CAACnE,QAAQ,CAAC,CAACgE,MAAM,CAACrX,EAAE,IAAIA,EAAE,CAAC,CAAC;IACvF,OAAO,IAAI,CAAC4B,WAAW,CAACyV,MAAM,CAACvD,CAAC,IAAIwE,SAAS,CAACpD,GAAG,CAACpB,CAAC,CAAC9T,EAAE,CAAC,CAAC;EAC1D;EAEA;;;EAGAiC,eAAeA,CAAA;IACb,IAAI,CAACG,cAAc,GAAG;MACpBC,UAAU,EAAE,EAAE;MACdqG,YAAY,EAAE,KAAK;MACnBI,UAAU,EAAE;KACb;IACD,IAAI,CAACtE,UAAU,CAACC,WAAW,GAAG,CAAC;IAC/B,IAAI,CAACwP,yBAAyB,EAAE;EAClC;EAEA;;;EAGA3R,cAAcA,CAAA;IACZ,IAAI,CAACkC,UAAU,CAACC,WAAW,GAAG,CAAC;IAC/B,IAAI,CAACwP,yBAAyB,EAAE;EAClC;EAEAA,yBAAyBA,CAAA;IACvB;IACA,MAAM5R,UAAU,GAAG,IAAI,CAACD,cAAc,CAACC,UAAU,CAACkW,IAAI,EAAE,CAACC,WAAW,EAAE;IACtE,MAAM9P,YAAY,GAAG,IAAI,CAACtG,cAAc,CAACsG,YAAY;IACrD,MAAMI,UAAU,GAAG,IAAI,CAAC1G,cAAc,CAAC0G,UAAU;IACjD,MAAM2P,aAAa,GAAGpW,UAAU,CAAClB,MAAM,GAAG,CAAC;IAE3C;IACA,MAAMuX,cAAc,GAAG,IAAI5G,GAAG,EAAU;IACxC,IAAI,IAAI,CAACP,oBAAoB,CAACpQ,MAAM,GAAG,CAAC,IAAI,IAAI,CAACkG,yBAAyB,CAAClG,MAAM,GAAG,CAAC,EAAE;MACrF,MAAMwX,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAACpH,oBAAoB,EAAE,GAAG,IAAI,CAAClK,yBAAyB,CAAC;MAC7FsR,mBAAmB,CAACrE,OAAO,CAAC5J,SAAS,IAAG;QACtC,MAAMsN,WAAW,GAAG,IAAI,CAACD,iBAAiB,CAACrN,SAAS,CAAChE,OAAO,CAACnD,SAAS,CAAC;QACvEyU,WAAW,CAAC1D,OAAO,CAACtU,EAAE,IAAI0Y,cAAc,CAAClE,GAAG,CAACxU,EAAE,CAAC,CAAC;MACnD,CAAC,CAAC;;IAGJ;IACA,MAAM4Y,QAAQ,GAAG,IAAI,CAAC5H,iBAAiB,CAACqG,MAAM,CAAC3Q,OAAO,IAAG;MACvD;MACA,IAAI+R,aAAa,IACb,CAAC/R,OAAO,CAACjG,IAAI,CAAC+X,WAAW,EAAE,CAAC7E,QAAQ,CAACtR,UAAU,CAAC,IAChD,CAAEqE,OAAO,CAAChG,WAAW,EAAE8X,WAAW,EAAE,CAAC7E,QAAQ,CAACtR,UAAU,CAAE,IAC1D,CAACqE,OAAO,CAACnD,SAAS,CAACiV,WAAW,EAAE,CAAC7E,QAAQ,CAACtR,UAAU,CAAC,EAAE;QACzD,OAAO,KAAK;;MAGd;MACA,IAAIqG,YAAY,KAAK,KAAK,IAAIhC,OAAO,CAAC2M,QAAQ,KAAK3K,YAAY,EAAE;QAC/D,OAAO,KAAK;;MAGd;MACA,IAAII,UAAU,KAAK,MAAM,IAAIpC,OAAO,CAAChD,eAAe,EAAE;QACpD,OAAO,KAAK;;MAEd,IAAIoF,UAAU,KAAK,QAAQ,IAAI,CAACpC,OAAO,CAAChD,eAAe,EAAE;QACvD,OAAO,KAAK;;MAGd;MACA,IAAIgV,cAAc,CAACxD,GAAG,CAACxO,OAAO,CAACnD,SAAS,CAAC,EAAE;QACzC,OAAO,KAAK;;MAGd,OAAO,IAAI;IACb,CAAC,CAAC;IAEF;IACA,IAAI,CAACiB,UAAU,CAACqF,UAAU,GAAG+O,QAAQ,CAACzX,MAAM;IAE5C;IACA,IAAI,IAAI,CAACqD,UAAU,CAACyE,YAAY,GAAG,CAAC,EAAE;MACpC,MAAM4P,UAAU,GAAG,CAAC,IAAI,CAACrU,UAAU,CAACC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,UAAU,CAACyE,YAAY;MACnF,MAAM6P,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACrU,UAAU,CAACyE,YAAY;MAC1D,IAAI,CAACe,gBAAgB,GAAG4O,QAAQ,CAACG,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;KAC7D,MAAM;MACL,IAAI,CAAC9O,gBAAgB,GAAG4O,QAAQ;;EAEpC;EAEA;EACAI,iBAAiBA,CAAClT,SAAiB;IACjC;IACA,IAAI,IAAI,CAACoL,gBAAgB,CAACgE,GAAG,CAACpP,SAAS,CAAC,EAAE;MACxC,OAAO,IAAI,CAACoL,gBAAgB,CAACoF,GAAG,CAACxQ,SAAS,CAAE,CAAC3E,MAAM;;IAErD,OAAO,IAAI,CAAC4W,iBAAiB,CAACjS,SAAS,CAAC,CAAC3E,MAAM;EACjD;EAEA8X,gBAAgBA,CAACnT,SAAiB;IAChC;IACA,IAAI,IAAI,CAACyM,kBAAkB,CAAC2C,GAAG,CAACpP,SAAS,CAAC,EAAE;MAC1C,OAAO,IAAI,CAACyM,kBAAkB,CAAC+D,GAAG,CAACxQ,SAAS,CAAE;;IAGhD,MAAMoT,aAAa,GAAG,IAAI,CAACnB,iBAAiB,CAACjS,SAAS,CAAC;IACvD,MAAM8P,QAAQ,GAAGsD,aAAa,CAACrD,GAAG,CAAC7V,EAAE,IACnC,IAAI,CAAC+Q,WAAW,CAAC8C,IAAI,CAAC2D,CAAC,IAAIA,CAAC,CAACjU,SAAS,KAAKvD,EAAE,CAAC,CAC/C,CAACqX,MAAM,CAACG,CAAC,IAAIA,CAAC,KAAKC,SAAS,CAAc;IAE3C,MAAM0B,MAAM,GAAG;MACbC,KAAK,EAAEF,aAAa,CAAC/X,MAAM;MAC3ByU,QAAQ,EAAEA;KACX;IAED;IACA,IAAI,CAACrD,kBAAkB,CAAC8D,GAAG,CAACvQ,SAAS,EAAEqT,MAAM,CAAC;IAE9C,OAAOA,MAAM;EACf;EAEAE,sBAAsBA,CAACvT,SAAiB;IACtC;IACA,IAAI,IAAI,CAAC0M,mBAAmB,CAAC0C,GAAG,CAACpP,SAAS,CAAC,EAAE;MAC3C,OAAO,IAAI,CAAC0M,mBAAmB,CAAC8D,GAAG,CAACxQ,SAAS,CAAE;;IAGjD,MAAMwT,aAAa,GAAG,IAAI,CAACL,gBAAgB,CAACnT,SAAS,CAAC;IACtD,MAAM8B,UAAU,GAAG0R,aAAa,CAAC1D,QAAQ,CAACiC,MAAM,CAAC,CAAC0B,GAAG,EAAE/B,CAAC,KAAK+B,GAAG,IAAI/B,CAAC,CAAC5T,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAErF;IACA,IAAI,CAAC4O,mBAAmB,CAAC6D,GAAG,CAACvQ,SAAS,EAAE8B,UAAU,CAAC;IAEnD,OAAOA,UAAU;EACnB;EAEA;EACA4R,kBAAkBA,CAAA;IAChB,IAAI,CAACjI,oBAAoB,GAAG,EAAE;IAC9B;IACA,IAAI,CAAC0C,yBAAyB,EAAE;EAClC;EAEAwF,iBAAiBA,CAAC/S,OAAY;IAC5B,MAAM4M,KAAK,GAAG,IAAI,CAAC/B,oBAAoB,CAACmI,SAAS,CAC/ChP,SAAS,IAAIA,SAAS,CAAChE,OAAO,CAACnD,SAAS,KAAKmD,OAAO,CAACnD,SAAS,CAC/D;IACD,IAAI+P,KAAK,IAAI,CAAC,EAAE;MACd,IAAI,CAAC/B,oBAAoB,CAACiC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC1C;MACA,IAAI,CAACW,yBAAyB,EAAE;;EAEpC;EAEA0F,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACpI,oBAAoB,CAACsG,MAAM,CAAC,CAACC,KAAK,EAAEpN,SAAS,KAAI;MAC3D,OAAOoN,KAAK,GAAI,CAACpN,SAAS,CAAChE,OAAO,CAAC9C,KAAK,IAAI,CAAC,IAAI8G,SAAS,CAAC9D,QAAS;IACtE,CAAC,EAAE,CAAC,CAAC;EACP;EAEAgT,oBAAoBA,CAAA;IAClB,IAAI,CAACvS,yBAAyB,GAAG,IAAI,CAACkK,oBAAoB,CAACsE,GAAG,CAACgE,IAAI,KAAK;MACtE,GAAGA,IAAI;MACP1O,eAAe,EAAE0O,IAAI,CAAC1O,eAAe,IAAI;KAC1C,CAAC,CAAC;IACH,IAAI,CAACoG,oBAAoB,GAAG,EAAE;IAE9B;IACA,IAAI,CAACuI,6BAA6B,EAAE;IAEpC,IAAI,CAAC7D,gBAAgB,EAAE;IACvB,IAAI,CAAChC,yBAAyB,EAAE;EAClC;EAEA;EACA8F,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC1S,yBAAyB,CAACgQ,MAAM,CAAC2C,CAAC,IAAIA,CAAC,CAAC7O,eAAe,KAAK,WAAW,CAAC;EACtF;EAEAI,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACwO,eAAe,EAAE,CAAClC,MAAM,CAAC,CAACC,KAAK,EAAEpN,SAAS,KAAI;MACxD,OAAOoN,KAAK,GAAI,CAACpN,SAAS,CAAChE,OAAO,CAAC9C,KAAK,IAAI,CAAC,IAAI8G,SAAS,CAAC9D,QAAS;IACtE,CAAC,EAAE,CAAC,CAAC;EACP;EAEAyD,eAAeA,CAAA;IACb,OAAO,IAAI,CAAChD,yBAAyB,CAClCgQ,MAAM,CAAC2C,CAAC,IAAIA,CAAC,CAAC7O,eAAe,KAAK,gBAAgB,CAAC,CACnD0M,MAAM,CAAC,CAACC,KAAK,EAAEpN,SAAS,KAAI;MAC3B,OAAOoN,KAAK,GAAI,CAACpN,SAAS,CAAChE,OAAO,CAAC9C,KAAK,IAAI,CAAC,IAAI8G,SAAS,CAAC9D,QAAS;IACtE,CAAC,EAAE,CAAC,CAAC;EACT;EAEAe,yBAAyBA,CAAA;IACvB,OAAO,IAAI,CAACN,yBAAyB,CAACwQ,MAAM,CAAC,CAACC,KAAK,EAAEpN,SAAS,KAAI;MAChE,OAAOoN,KAAK,GAAGpN,SAAS,CAAC9D,QAAQ;IACnC,CAAC,EAAE,CAAC,CAAC;EACP;EAEAa,yBAAyBA,CAAA;IACvB,IAAI,CAACJ,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAAC4O,gBAAgB,EAAE;IACvB;IACA,IAAI,CAAChC,yBAAyB,EAAE;EAClC;EAEAxO,sBAAsBA,CAACiB,OAAY;IACjC;IACA,MAAMuT,SAAS,GAAG,IAAI,CAAC5S,yBAAyB,CAACqS,SAAS,CACxDhP,SAAS,IAAIA,SAAS,CAAChE,OAAO,CAACnD,SAAS,KAAKmD,OAAO,CAACnD,SAAS,IAC9DmH,SAAS,CAACS,eAAe,KAAK,WAAW,CAC1C;IAED,IAAI8O,SAAS,IAAI,CAAC,EAAE;MAClB,IAAI,CAAC5S,yBAAyB,CAACmM,MAAM,CAACyG,SAAS,EAAE,CAAC,CAAC;;IAGrD;IACA,IAAI,CAAC5S,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACgQ,MAAM,CACpE3M,SAAS,IAAG;MACV,IAAIA,SAAS,CAACS,eAAe,KAAK,gBAAgB,EAAE;QAClD;QACA,OAAO,CAAC,IAAI,CAAC+O,SAAS,CAACxP,SAAS,CAAChE,OAAO,EAAEA,OAAO,CAACnD,SAAS,CAAC;;MAE9D,OAAO,IAAI;IACb,CAAC,CACF;IAED,IAAI,CAAC0S,gBAAgB,EAAE;EACzB;EAEA;EACQiE,SAASA,CAACxT,OAAgB,EAAE0P,QAAgB;IAClD,IAAI1P,OAAO,CAAChD,eAAe,KAAK0S,QAAQ,EAAE;MACxC,OAAO,IAAI;;IAGb;IACA,MAAM+D,MAAM,GAAG,IAAI,CAACpJ,WAAW,CAAC8C,IAAI,CAAC2D,CAAC,IAAIA,CAAC,CAACjU,SAAS,KAAKmD,OAAO,CAAChD,eAAe,CAAC;IAClF,IAAIyW,MAAM,EAAE;MACV,OAAO,IAAI,CAACD,SAAS,CAACC,MAAM,EAAE/D,QAAQ,CAAC;;IAGzC,OAAO,KAAK;EACd;EAEA;;;EAGA/S,qBAAqBA,CAACqD,OAAY;IAChC,OAAO,IAAI,CAAC6K,oBAAoB,CAAC6I,IAAI,CACnC1P,SAAS,IAAIA,SAAS,CAAChE,OAAO,CAACnD,SAAS,KAAKmD,OAAO,CAACnD,SAAS,CAC/D;EACH;EAEA;;;EAGAP,0BAA0BA,CAAC0D,OAAY;IACrC;IACA,MAAM2T,aAAa,GAAG,IAAI,CAAChT,yBAAyB,CAACqS,SAAS,CAC5DhP,SAAS,IAAIA,SAAS,CAAChE,OAAO,CAACnD,SAAS,KAAKmD,OAAO,CAACnD,SAAS,CAC/D;IAED,IAAI8W,aAAa,IAAI,CAAC,EAAE;MACtB;MACA,IAAI,CAAChT,yBAAyB,CAACmM,MAAM,CAAC6G,aAAa,EAAE,CAAC,CAAC;KACxD,MAAM;MACL;MACA,IAAI,CAAChT,yBAAyB,CAACoM,IAAI,CAAC;QAClC/M,OAAO,EAAEA,OAAO;QAChBE,QAAQ,EAAE,CAAC;QACXuE,eAAe,EAAE;OAClB,CAAC;MAEF;MACA,IAAI,CAACsL,8BAA8B,CAAC/P,OAAO,CAAC;;IAG9C;IACA,IAAI,CAACuP,gBAAgB,EAAE;IAEvB;IACA,IAAI,CAAChC,yBAAyB,EAAE;EAClC;EAEA;EACApO,qBAAqBA,CAACC,SAAiB,EAAEC,KAAU;IACjD,IAAIuU,WAAW,GAAGpE,QAAQ,CAACnQ,KAAK,CAACwU,MAAM,CAACC,KAAK,CAAC;IAE9C,IAAIC,KAAK,CAACH,WAAW,CAAC,IAAIA,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAG,CAAC;IAC1D,IAAIA,WAAW,GAAG,EAAE,EAAEA,WAAW,GAAG,EAAE;IAEtC,MAAM5P,SAAS,GAAG,IAAI,CAACrD,yBAAyB,CAACwM,IAAI,CAAC2D,CAAC,IAAIA,CAAC,CAAC9Q,OAAO,CAACnD,SAAS,KAAKuC,SAAS,CAAC;IAC7F,IAAI4E,SAAS,EAAE;MACbA,SAAS,CAAC9D,QAAQ,GAAG0T,WAAW;MAChC,IAAI,CAACrE,gBAAgB,EAAE;;EAE3B;EAEAhQ,gBAAgBA,CAACH,SAAiB;IAChC,MAAM4E,SAAS,GAAG,IAAI,CAACrD,yBAAyB,CAACwM,IAAI,CAAC2D,CAAC,IAAIA,CAAC,CAAC9Q,OAAO,CAACnD,SAAS,KAAKuC,SAAS,CAAC;IAC7F,IAAI4E,SAAS,IAAIA,SAAS,CAAC9D,QAAQ,GAAG,EAAE,EAAE;MACxC8D,SAAS,CAAC9D,QAAQ,EAAE;MACpB,IAAI,CAACqP,gBAAgB,EAAE;;EAE3B;EAEA9P,gBAAgBA,CAACL,SAAiB;IAChC,MAAM4E,SAAS,GAAG,IAAI,CAACrD,yBAAyB,CAACwM,IAAI,CAAC2D,CAAC,IAAIA,CAAC,CAAC9Q,OAAO,CAACnD,SAAS,KAAKuC,SAAS,CAAC;IAC7F,IAAI4E,SAAS,IAAIA,SAAS,CAAC9D,QAAQ,GAAG,CAAC,EAAE;MACvC8D,SAAS,CAAC9D,QAAQ,EAAE;MACpB,IAAI,CAACqP,gBAAgB,EAAE;;EAE3B;EAEA;EACA5P,sBAAsBA,CAACP,SAAiB;IACtC,MAAM4U,UAAU,GAAG,IAAI,CAACtV,gBAAgB,CAACkR,GAAG,CAACxQ,SAAS,CAAC,IAAI,KAAK;IAChE,IAAI,CAACV,gBAAgB,CAACiR,GAAG,CAACvQ,SAAS,EAAE,CAAC4U,UAAU,CAAC;EACnD;EAEA/T,iBAAiBA,CAACb,SAAiB;IACjC,OAAO,IAAI,CAACV,gBAAgB,CAACkR,GAAG,CAACxQ,SAAS,CAAC,IAAI,KAAK;EACtD;EAEAZ,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAACE,gBAAgB,CAACC,IAAI,GAAG,CAAC,EAAE;MAClC;MACA,IAAI,CAACD,gBAAgB,CAACgP,KAAK,EAAE;KAC9B,MAAM;MACL;MACA,IAAI,CAAC/M,yBAAyB,CAACiN,OAAO,CAAC5J,SAAS,IAAG;QACjD,IAAI,IAAI,CAACzG,iBAAiB,CAACyG,SAAS,CAAChE,OAAO,CAACnD,SAAS,CAAC,EAAE;UACvD,IAAI,CAAC6B,gBAAgB,CAACiR,GAAG,CAAC3L,SAAS,CAAChE,OAAO,CAACnD,SAAS,EAAE,IAAI,CAAC;;MAEhE,CAAC,CAAC;;EAEN;EAEA;EACAwD,oBAAoBA,CAACjB,SAAiB;IACpC,OAAO,IAAI,CAACkB,oCAAoC,CAAClB,SAAS,CAAC,GAAG,CAAC;EACjE;EAEAkB,oCAAoCA,CAAClB,SAAiB;IACpD,OAAO,IAAI,CAACkM,kBAAkB,CAACqF,MAAM,CACnCO,EAAE,IAAIA,EAAE,CAACrU,SAAS,KAAKuC,SAAS,IAAI8R,EAAE,CAACzK,QAAQ,CAChD,CAAChM,MAAM;EACV;EAEA8F,wBAAwBA,CAACnB,SAAiB;IACxC,OAAO,IAAI,CAACkM,kBAAkB,CAC3BqF,MAAM,CAACO,EAAE,IAAIA,EAAE,CAACrU,SAAS,KAAKuC,SAAS,IAAI8R,EAAE,CAACzK,QAAQ,IAAIyK,EAAE,CAACxK,QAAQ,EAAExJ,KAAK,KAAK,CAAC,IAAI,CAACgU,EAAE,CAACxK,QAAQ,EAAEC,UAAU,CAAC,CAC/GwI,GAAG,CAAC+B,EAAE,KAAK;MACVnX,IAAI,EAAEmX,EAAE,CAACxK,QAAQ,EAAE3M,IAAI;MACvBka,UAAU,EAAE/C,EAAE,CAACxK,QAAQ,EAAEC;KAC1B,CAAC,CAAC;EACP;EAEAnG,wBAAwBA,CAACpB,SAAiB;IACxC,OAAO,IAAI,CAACkM,kBAAkB,CAC3BqF,MAAM,CAACO,EAAE,IAAIA,EAAE,CAACrU,SAAS,KAAKuC,SAAS,IAAI8R,EAAE,CAACzK,QAAQ,IAAI,CAACyK,EAAE,CAACxK,QAAQ,EAAExJ,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAACgU,EAAE,CAACxK,QAAQ,EAAEC,UAAU,CAAC,CACpHwI,GAAG,CAAC+B,EAAE,KAAK;MACVnX,IAAI,EAAEmX,EAAE,CAACxK,QAAQ,EAAE3M,IAAI;MACvBmD,KAAK,EAAEgU,EAAE,CAACxK,QAAQ,EAAExJ,KAAK;MACzB+W,UAAU,EAAE/C,EAAE,CAACxK,QAAQ,EAAEC;KAC1B,CAAC,CAAC;EACP;EAEAlG,4BAA4BA,CAACrB,SAAiB;IAC5C,OAAO,IAAI,CAACkM,kBAAkB,CAC3BqF,MAAM,CAACO,EAAE,IAAIA,EAAE,CAACrU,SAAS,KAAKuC,SAAS,IAAI8R,EAAE,CAACxK,QAAQ,EAAEC,UAAU,CAAC,CACnEwI,GAAG,CAAC+B,EAAE,KAAK;MACVnX,IAAI,EAAEmX,EAAE,CAACxK,QAAQ,EAAE3M,IAAI;MACvBmD,KAAK,EAAEgU,EAAE,CAACxK,QAAQ,EAAExJ,KAAK;MACzB+W,UAAU,EAAE;KACb,CAAC,CAAC;EACP;EAEA;EACAC,kBAAkBA,CAACC,eAAuB;IACxC,MAAMnF,QAAQ,GAAG,IAAI,CAAClG,iBAAiB,CAACqL,eAAe,CAAC;IACxD,OAAOnF,QAAQ,CAACG,GAAG,CAACtB,KAAK,IAAG;MAC1B,OAAO;QACL7N,OAAO,EAAE6N,KAAK;QACduG,UAAU,EAAE,IAAI,CAACxX,kBAAkB,CAACiR,KAAK,CAAC;QAC1CmG,UAAU,EAAE,IAAI,CAAC/T,iBAAiB,CAAC4N,KAAK,CAAChR,SAAS,CAAC;QACnDqD,QAAQ,EAAE,IAAI,CAACmU,kBAAkB,CAACxG,KAAK,CAAChR,SAAS,CAAC;QAClD8Q,cAAc,EAAE,IAAI,CAACuG,kBAAkB,CAACrG,KAAK,CAAChR,SAAS,CAAC;QACxD0H,WAAW,EAAE,IAAI,CAAChH,iBAAiB,CAACsQ,KAAK,CAAChR,SAAS,CAAC;QACpDyX,qBAAqB,EAAE,IAAI,CAACjU,oBAAoB,CAACwN,KAAK,CAAChR,SAAS,CAAC;QACjE0X,uBAAuB,EAAE,IAAI,CAACjU,oCAAoC,CAACuN,KAAK,CAAChR,SAAS,CAAC;QACnF2X,cAAc,EAAE,IAAI,CAACjU,wBAAwB,CAACsN,KAAK,CAAChR,SAAS,CAAC;QAC9D4X,cAAc,EAAE,IAAI,CAACjU,wBAAwB,CAACqN,KAAK,CAAChR,SAAS,CAAC;QAC9D6X,kBAAkB,EAAE,IAAI,CAACjU,4BAA4B,CAACoN,KAAK,CAAChR,SAAS;OACtE;IACH,CAAC,CAAC;EACJ;EAEA;EACAuW,6BAA6BA,CAAA;IAC3B,IAAI,CAAC9H,kBAAkB,GAAG,EAAE;IAE5B;IACA,IAAI,CAAC3K,yBAAyB,CAACiN,OAAO,CAAC5J,SAAS,IAAG;MACjD,IAAI,CAAC+L,8BAA8B,CAAC/L,SAAS,CAAChE,OAAO,CAAC;IACxD,CAAC,CAAC;IAEF,IAAI,CAACuP,gBAAgB,EAAE;EACzB;EAEAnJ,uBAAuBA,CAAChH,SAAiB,EAAE4R,UAAkB;IAC3D,MAAMd,iBAAiB,GAAG,IAAI,CAAC5E,kBAAkB,CAAC6B,IAAI,CACpD+D,EAAE,IAAIA,EAAE,CAACrU,SAAS,KAAKuC,SAAS,IAAI8R,EAAE,CAAC5K,UAAU,KAAK0K,UAAU,CACjE;IAED,IAAId,iBAAiB,EAAE;MACrB,MAAMxJ,QAAQ,GAAGwJ,iBAAiB,CAACxJ,QAAS;MAE5C;MACA,IAAIA,QAAQ,CAACC,UAAU,IAAIuJ,iBAAiB,CAACzJ,QAAQ,EAAE;QACrD;;MAGF;MACA,IAAIC,QAAQ,CAACU,WAAW,EAAE;QACxB,IAAI,CAACkE,kBAAkB,CACpBqF,MAAM,CAACO,EAAE,IACRA,EAAE,CAACrU,SAAS,KAAKuC,SAAS,IAC1B8R,EAAE,CAACxK,QAAS,CAACI,YAAY,KAAKJ,QAAQ,CAACI,YAAY,IACnDoK,EAAE,CAAC5K,UAAU,KAAK0K,UAAU,CAC7B,CACApD,OAAO,CAACsD,EAAE,IAAIA,EAAE,CAACzK,QAAQ,GAAG,KAAK,CAAC;;MAGvCyJ,iBAAiB,CAACzJ,QAAQ,GAAG,CAACyJ,iBAAiB,CAACzJ,QAAQ;MACxD,IAAI,CAAC8I,gBAAgB,EAAE;;EAE3B;EAEA1H,8BAA8BA,CAACzI,SAAiB;IAI9C,MAAMuV,UAAU,GAAG,IAAI,CAACC,uBAAuB,CAACxV,SAAS,CAAC;IAC1D,OAAO;MACL0I,IAAI,EAAE6M,UAAU,CAAChE,MAAM,CAACO,EAAE,IAAI9Y,aAAa,CAACyc,cAAc,CAAC3D,EAAE,CAACxK,QAAS,CAAC,CAAC;MACzEqB,IAAI,EAAE4M,UAAU,CAAChE,MAAM,CAACO,EAAE,IAAI9Y,aAAa,CAAC0c,cAAc,CAAC5D,EAAE,CAACxK,QAAS,CAAC;KACzE;EACH;EAEAkO,uBAAuBA,CAACxV,SAAiB;IACvC,OAAO,IAAI,CAACkM,kBAAkB,CAACqF,MAAM,CAACO,EAAE,IAAIA,EAAE,CAACrU,SAAS,KAAKuC,SAAS,CAAC;EACzE;EAEA4I,+BAA+BA,CAAC5I,SAAiB;IAC/C,OAAO,IAAI,CAACkM,kBAAkB,CAC3BqF,MAAM,CAACO,EAAE,IAAIA,EAAE,CAACrU,SAAS,KAAKuC,SAAS,IAAI8R,EAAE,CAACxK,QAAQ,EAAEC,UAAU,CAAC;EACxE;EAEA;EACA1H,sBAAsBA,CAACe,OAAY;IACjC,MAAM2T,aAAa,GAAG,IAAI,CAAChT,yBAAyB,CAACqS,SAAS,CAC5DhP,SAAS,IAAIA,SAAS,CAAChE,OAAO,CAACnD,SAAS,KAAKmD,OAAO,CAACnD,SAAS,CAC/D;IAED,IAAI8W,aAAa,IAAI,CAAC,EAAE;MACtB;MACA,IAAI,CAAChT,yBAAyB,CAACmM,MAAM,CAAC6G,aAAa,EAAE,CAAC,CAAC;KACxD,MAAM;MACL;MACA,MAAMoB,YAAY,GAAqB;QACrC/U,OAAO,EAAEA,OAAO;QAChBE,QAAQ,EAAE,CAAC;QACXuE,eAAe,EAAE,WAAW,CAAC;OAC9B;MACD,IAAI,CAAC9D,yBAAyB,CAACoM,IAAI,CAACgI,YAAY,CAAC;MAEjD;MACA,IAAI,CAAChF,8BAA8B,CAAC/P,OAAO,CAAC;;IAG9C,IAAI,CAACuP,gBAAgB,EAAE;EACzB;EAEA3S,kBAAkBA,CAACoD,OAAY;IAC7B,OAAO,IAAI,CAACW,yBAAyB,CAAC+S,IAAI,CACxC1P,SAAS,IAAIA,SAAS,CAAChE,OAAO,CAACnD,SAAS,KAAKmD,OAAO,CAACnD,SAAS,CAC/D;EACH;EAEAwX,kBAAkBA,CAACjV,SAAiB;IAClC,MAAM4E,SAAS,GAAG,IAAI,CAACrD,yBAAyB,CAACwM,IAAI,CAAC2D,CAAC,IAAIA,CAAC,CAAC9Q,OAAO,CAACnD,SAAS,KAAKuC,SAAS,CAAC;IAC7F,OAAO4E,SAAS,GAAGA,SAAS,CAAC9D,QAAQ,GAAG,CAAC;EAC3C;EAEA;EACA6E,+BAA+BA,CAAA;IAC7B,MAAM0N,MAAM,GAAU,EAAE;IACxB,MAAMuC,YAAY,GAAG,IAAI5J,GAAG,EAAU;IAEtC;IACA,MAAM6J,YAAY,GAAG,IAAI,CAACtU,yBAAyB,CAACgQ,MAAM,CAAC2C,CAAC,IAAIA,CAAC,CAAC7O,eAAe,KAAK,WAAW,CAAC;IAElG;IACAwQ,YAAY,CACTC,IAAI,CAAC,CAACtE,CAAC,EAAEuE,CAAC,KAAKvE,CAAC,CAAC5Q,OAAO,CAACjG,IAAI,CAACqb,aAAa,CAACD,CAAC,CAACnV,OAAO,CAACjG,IAAI,CAAC,CAAC,CAC5D6T,OAAO,CAAC5J,SAAS,IAAG;MACnB,IAAI,CAACgR,YAAY,CAACxG,GAAG,CAACxK,SAAS,CAAChE,OAAO,CAACnD,SAAS,CAAC,EAAE;QAClD4V,MAAM,CAAC1F,IAAI,CAAC;UACV/I,SAAS;UACTK,KAAK,EAAE,CAAC;UACRE,WAAW,EAAE,IAAI,CAAChH,iBAAiB,CAACyG,SAAS,CAAChE,OAAO,CAACnD,SAAS,CAAC;UAChEoH,QAAQ,EAAE,IAAI,CAACoR,kBAAkB,CAACrR,SAAS,CAAChE,OAAO,CAACnD,SAAS,CAAC;UAC9D6H,aAAa,EAAE;SAChB,CAAC;QACFsQ,YAAY,CAAClH,GAAG,CAAC9J,SAAS,CAAChE,OAAO,CAACnD,SAAS,CAAC;;IAEjD,CAAC,CAAC;IAEJ,OAAO4V,MAAM;EACf;EAEA1O,uBAAuBA,CAAC3E,SAAiB;IACvC,IAAI,IAAI,CAACqM,iBAAiB,CAAC+C,GAAG,CAACpP,SAAS,CAAC,EAAE;MACzC,IAAI,CAACqM,iBAAiB,CAACgD,MAAM,CAACrP,SAAS,CAAC;KACzC,MAAM;MACL,IAAI,CAACqM,iBAAiB,CAACqC,GAAG,CAAC1O,SAAS,CAAC;;EAEzC;EAEAiW,kBAAkBA,CAACjW,SAAiB;IAClC,OAAO,IAAI,CAACqM,iBAAiB,CAAC+C,GAAG,CAACpP,SAAS,CAAC;EAC9C;EAEA;EACAS,mBAAmBA,CAACG,OAAY;IAC9B,IAAI,CAACqG,4BAA4B,GAAGrG,OAAO;EAC7C;EAEA0F,qBAAqBA,CAAA;IACnB;EAAA;EAGFE,uBAAuBA,CAAA;IACrB;EAAA;EAGF0P,0BAA0BA,CAAA;IACxB;EAAA;EAGF;EACA1a,QAAQA,CAAA;IACN,QAAQ,IAAI,CAACxB,WAAW;MACtB,KAAK,CAAC;QACJ,IAAI,IAAI,CAACoB,iBAAiB,CAACC,MAAM,KAAK,CAAC,EAAE;UACvC,IAAI,CAAC8a,eAAe,CAAC,yDAAyD,CAAC;UAC/E;;QAEF,IAAI,CAACnc,WAAW,GAAG,CAAC;QACpB,IAAI,CAAC4T,qBAAqB,EAAE;QAC5B;MAEF,KAAK,CAAC;QACJ,IAAI,IAAI,CAACrM,yBAAyB,CAAClG,MAAM,KAAK,CAAC,EAAE;UAC/C,IAAI,CAAC8a,eAAe,CAAC,2CAA2C,CAAC;UACjE;;QAEF,IAAI,CAAC,IAAI,CAACC,2BAA2B,EAAE,EAAE;UACvC,MAAMC,iBAAiB,GAAG,IAAI,CAACC,4BAA4B,EAAE;UAC7D,MAAMC,YAAY,GAAGF,iBAAiB,CAACtG,GAAG,CAACyG,IAAI,IAAIA,IAAI,CAAC7b,IAAI,CAAC,CAAC8b,IAAI,CAAC,IAAI,CAAC;UACxE,IAAI,CAACN,eAAe,CAAC,6DAA6DI,YAAY,EAAE,CAAC;UACjG;;QAEF;QACA,IAAI,CAACvc,WAAW,GAAG,CAAC;QACpB,IAAI,CAAC0c,0BAA0B,EAAE;QACjC;;IAGJ,IAAI,CAAChG,aAAa,EAAE;EACtB;EAEAzO,YAAYA,CAAA;IACV,IAAI,IAAI,CAACjI,WAAW,GAAG,CAAC,EAAE;MACxB;MACA,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1B,IAAI,CAACA,WAAW,GAAG,CAAC;QACpB,IAAI,CAAC2c,eAAe,EAAE;QACtB;;MAGF,IAAI,CAAC3c,WAAW,EAAE;MAClB,IAAI,CAAC4c,mBAAmB,EAAE;;EAE9B;EAEA;;;EAGQA,mBAAmBA,CAAA;IACzB,IAAI,CAAClG,aAAa,EAAE;IACpB,IAAI,IAAI,CAAC1W,WAAW,KAAK,CAAC,EAAE;MAC1B,IAAI,CAAC4T,qBAAqB,EAAE;MAC5B,IAAI,CAAClP,UAAU,CAACC,WAAW,GAAG,CAAC;;EAEnC;EAEA;;;EAGAgY,eAAeA,CAAA;IACb,IAAI,CAAClL,oBAAoB,GAAG,CAAC,GAAG,IAAI,CAAClK,yBAAyB,CAAC;IAC/D,IAAI,CAACA,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAACvH,WAAW,GAAG,CAAC;EACtB;EAEA;;;EAGQ0c,0BAA0BA,CAAA;IAChC;IACA,IAAI,CAACnV,yBAAyB,CAACiN,OAAO,CAAC5J,SAAS,IAAG;MACjD,IAAI,CAACmH,sBAAsB,CAAC2C,GAAG,CAAC9J,SAAS,CAAChE,OAAO,CAACnD,SAAS,CAAC;IAC9D,CAAC,CAAC;IAEF;IACA,IAAI,CAAC8D,yBAAyB,CAC3BgQ,MAAM,CAAC2C,CAAC,IAAIA,CAAC,CAAC7O,eAAe,KAAK,WAAW,CAAC,CAC9CmJ,OAAO,CAAC0F,CAAC,IAAI,IAAI,CAAC7H,iBAAiB,CAACqC,GAAG,CAACwF,CAAC,CAACtT,OAAO,CAACnD,SAAS,CAAC,CAAC;IAChE,MAAMoZ,iBAAiB,GAAG,IAAI,CAACtV,yBAAyB,CACrDgQ,MAAM,CAAC2C,CAAC,IAAIA,CAAC,CAAC7O,eAAe,KAAK,WAAW,CAAC,CAC9C0M,MAAM,CAAC,CAAC0B,GAAG,EAAES,CAAC,KAAKT,GAAG,GAAI,CAACS,CAAC,CAACtT,OAAO,CAAC9C,KAAK,IAAI,CAAC,IAAIoW,CAAC,CAACpT,QAAS,EAAE,CAAC,CAAC;IAErE,IAAI,CAACwL,kBAAkB,GAAG;MACxBzQ,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCib,gBAAgB,EAAE,IAAI,CAAC1b,iBAAiB,CAAC2U,GAAG,CAAC7V,EAAE,IAC7C,IAAI,CAAC4B,WAAW,CAACiS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9T,EAAE,KAAKA,EAAE,CAAC,CACxC,CAACqX,MAAM,CAACvD,CAAC,IAAIA,CAAC,KAAK2D,SAAS,CAAC;MAC9BoF,gBAAgB,EAAE,IAAI,CAACxV,yBAAyB,CAACwO,GAAG,CAACmE,CAAC,KAAK;QACzDtT,OAAO,EAAEsT,CAAC,CAACtT,OAAO;QAClBE,QAAQ,EAAEoT,CAAC,CAACpT,QAAQ;QACpBkW,QAAQ,EAAE,CAAC9C,CAAC,CAACtT,OAAO,CAAC9C,KAAK,IAAI,CAAC,IAAIoW,CAAC,CAACpT,QAAQ;QAC7CuE,eAAe,EAAE6O,CAAC,CAAC7O,eAAe;QAClCJ,KAAK,EAAE,IAAI,CAACgS,eAAe,CAAC/C,CAAC,CAACtT,OAAO,CAACnD,SAAS,CAAC;QAChDyZ,aAAa,EAAE,IAAI,CAACC,uBAAuB,CAACjD,CAAC,CAACtT,OAAO,CAACnD,SAAS;OAChE,CAAC,CAAC;MACHyO,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAACqF,MAAM,CAACO,EAAE,IAAIA,EAAE,CAACzK,QAAQ,CAAC;MACrE+P,cAAc,EAAE;QACdvB,YAAY,EAAEgB,iBAAiB;QAC/BtB,UAAU,EAAE,IAAI,CAAC7P,eAAe;QAChCsM,KAAK,EAAE,IAAI,CAAClQ;OACb;MACDA,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BuV,SAAS,EAAE,IAAIpG,IAAI,EAAE,CAACqG,WAAW;KAClC;EACH;EAEA;;;EAGAL,eAAeA,CAACjX,SAAiB;IAC/B,MAAMY,OAAO,GAAG,IAAI,CAACqK,WAAW,CAAC8C,IAAI,CAAC2D,CAAC,IAAIA,CAAC,CAACjU,SAAS,KAAKuC,SAAS,CAAC;IACrE,IAAI,CAACY,OAAO,EAAE,OAAO,CAAC;IAEtB,IAAIqE,KAAK,GAAG,CAAC;IACb,IAAIsS,cAAc,GAAG3W,OAAO;IAE5B,OAAO2W,cAAc,CAAC3Z,eAAe,EAAE;MACrCqH,KAAK,EAAE;MACP,MAAMoP,MAAM,GAAG,IAAI,CAACpJ,WAAW,CAAC8C,IAAI,CAAC2D,CAAC,IAAIA,CAAC,CAACjU,SAAS,KAAK8Z,cAAc,CAAC3Z,eAAe,CAAC;MACzF,IAAI,CAACyW,MAAM,EAAE;MACbkD,cAAc,GAAGlD,MAAM;;IAGzB,OAAOpP,KAAK;EACd;EAEA;;;EAGQkS,uBAAuBA,CAACnX,SAAiB;IAC/C,MAAMwX,IAAI,GAAc,EAAE;IAC1B,MAAM5W,OAAO,GAAG,IAAI,CAACqK,WAAW,CAAC8C,IAAI,CAAC2D,CAAC,IAAIA,CAAC,CAACjU,SAAS,KAAKuC,SAAS,CAAC;IAErE,IAAI,CAACY,OAAO,EAAE,OAAO4W,IAAI;IAEzB,IAAID,cAAc,GAAG3W,OAAO;IAC5B4W,IAAI,CAACC,OAAO,CAACF,cAAc,CAAC;IAE5B,OAAOA,cAAc,CAAC3Z,eAAe,EAAE;MACrC,MAAMyW,MAAM,GAAG,IAAI,CAACpJ,WAAW,CAAC8C,IAAI,CAAC2D,CAAC,IAAIA,CAAC,CAACjU,SAAS,KAAK8Z,cAAc,CAAC3Z,eAAe,CAAC;MACzF,IAAI,CAACyW,MAAM,EAAE;MACbmD,IAAI,CAACC,OAAO,CAACpD,MAAM,CAAC;MACpBkD,cAAc,GAAGlD,MAAM;;IAGzB,OAAOmD,IAAI;EACb;EAEA;;;EAGApB,2BAA2BA,CAAA;IACzB,OAAO,IAAI,CAACsB,gCAAgC,EAAE;EAChD;EAEA;;;EAGQvB,eAAeA,CAACwB,OAAe;IACrC,IAAI,CAAC5b,YAAY,GAAG4b,OAAO;IAC3B,IAAI,CAAC3b,cAAc,GAAG,EAAE;IACxB4b,UAAU,CAAC,MAAM,IAAI,CAAClH,aAAa,EAAE,EAAE,IAAI,CAAC;EAC9C;EAEA;;;EAGQmH,iBAAiBA,CAACF,OAAe;IACvC,IAAI,CAAC3b,cAAc,GAAG2b,OAAO;IAC7B,IAAI,CAAC5b,YAAY,GAAG,EAAE;IACtB6b,UAAU,CAAC,MAAM,IAAI,CAAClH,aAAa,EAAE,EAAE,IAAI,CAAC;EAC9C;EAEA;EACAgH,gCAAgCA,CAAA;IAC9B;IACA,KAAK,MAAM9S,SAAS,IAAI,IAAI,CAACrD,yBAAyB,EAAE;MACtD,MAAMvB,SAAS,GAAG4E,SAAS,CAAChE,OAAO,CAACnD,SAAS;MAC7C,MAAMqa,yBAAyB,GAAG,IAAI,CAACjH,gCAAgC,CAAC7Q,SAAS,CAAC,CAC/EuR,MAAM,CAACiF,IAAI,IAAIA,IAAI,CAACjP,UAAU,CAAC;MAElC;MACA,IAAIuQ,yBAAyB,CAACzc,MAAM,KAAK,CAAC,EAAE;QAC1C;;MAGF;MACA,KAAK,MAAM0c,YAAY,IAAID,yBAAyB,EAAE;QACpD,MAAM9C,UAAU,GAAG,IAAI,CAAC9I,kBAAkB,CAACoI,IAAI,CAC7CxC,EAAE,IAAIA,EAAE,CAACrU,SAAS,KAAKuC,SAAS,IAC1B8R,EAAE,CAAC5K,UAAU,KAAK6Q,YAAY,CAAC7Q,UAAU,IACzC4K,EAAE,CAACzK,QAAQ,CAClB;QAED,IAAI,CAAC2N,UAAU,EAAE;UACf,OAAO,KAAK;;;;IAKlB,OAAO,IAAI;EACb;EAEAsB,4BAA4BA,CAAA;IAC1B,MAAMD,iBAAiB,GAAU,EAAE;IAEnC;IACA,KAAK,MAAMzR,SAAS,IAAI,IAAI,CAACrD,yBAAyB,EAAE;MACtD,MAAMvB,SAAS,GAAG4E,SAAS,CAAChE,OAAO,CAACnD,SAAS;MAC7C,MAAMqa,yBAAyB,GAAG,IAAI,CAACjH,gCAAgC,CAAC7Q,SAAS,CAAC,CAC/EuR,MAAM,CAACiF,IAAI,IAAIA,IAAI,CAACjP,UAAU,CAAC;MAElC;MACA,KAAK,MAAMwQ,YAAY,IAAID,yBAAyB,EAAE;QACpD,MAAM9C,UAAU,GAAG,IAAI,CAAC9I,kBAAkB,CAACoI,IAAI,CAC7CxC,EAAE,IAAIA,EAAE,CAACrU,SAAS,KAAKuC,SAAS,IAC1B8R,EAAE,CAAC5K,UAAU,KAAK6Q,YAAY,CAAC7Q,UAAU,IACzC4K,EAAE,CAACzK,QAAQ,CAClB;QAED,IAAI,CAAC2N,UAAU,EAAE;UACfqB,iBAAiB,CAAC1I,IAAI,CAAC;YACrB,GAAGoK,YAAY;YACfC,WAAW,EAAEpT,SAAS,CAAChE,OAAO,CAACjG;WAChC,CAAC;;;;IAKR,OAAO0b,iBAAiB;EAC1B;EAEA;EACApQ,gBAAgBA,CAAA;IACd,IAAI,CAACQ,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;EACxD;EAEA;EACAwR,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC7c,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACqQ,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAAClK,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAAC2K,kBAAkB,GAAG,EAAE;IAE5B;IACA,IAAI,CAAC5P,cAAc,GAAG;MACpBC,UAAU,EAAE,EAAE;MACdqG,YAAY,EAAE,KAAK;MACnBI,UAAU,EAAE;KACb;IACD,IAAI,CAACtE,UAAU,GAAG;MAChBC,WAAW,EAAE,CAAC;MACdwE,YAAY,EAAE,EAAE;MAChBY,UAAU,EAAE,CAAC;MACbwI,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;KACnC;IAED;IACA,IAAI,CAACjN,gBAAgB,GAAG,IAAI+L,GAAG,EAAmB;IAClD,IAAI,CAACvJ,UAAU,GAAG,CAAC;IACnB,IAAI,CAAC4D,eAAe,GAAG,CAAC;IAExB;IACA,IAAI,CAAC1L,WAAW,GAAG,CAAC;EACtB;EAEA;;;EAGAke,mBAAmBA,CAACtX,OAAY;IAC9B,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACnD,SAAS,EAAE,OAAO,KAAK;IAChD,MAAM0a,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC1M,oBAAoB,EAAE,GAAG,IAAI,CAAClK,yBAAyB,CAAC;IACrF,MAAM2Q,WAAW,GAAG,IAAI,CAACD,iBAAiB,CAACrR,OAAO,CAACnD,SAAS,CAAC;IAC7D,OAAOyU,WAAW,CAACoC,IAAI,CAAC8D,YAAY,IAClCD,WAAW,CAAC7D,IAAI,CAAC+D,GAAG,IAAIA,GAAG,CAACzX,OAAO,CAACnD,SAAS,KAAK2a,YAAY,CAAC,CAChE;EACH;EAEA;;;EAGAE,4BAA4BA,CAAC1X,OAAY;IACvCqN,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEtN,OAAO,CAACjG,IAAI,CAAC;IAC7D,IAAI,CAACkR,eAAe,GAAGjL,OAAO;IAE9B;IACA,MAAMsR,WAAW,GAAG,IAAI,CAACD,iBAAiB,CAACrR,OAAO,CAACnD,SAAS,CAAC;IAC7D,MAAM0a,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC1M,oBAAoB,EAAE,GAAG,IAAI,CAAClK,yBAAyB,CAAC;IACrF,IAAI,CAACqF,wBAAwB,GAAGuR,WAAW,CACxC5G,MAAM,CAAC8G,GAAG,IAAInG,WAAW,CAACrE,QAAQ,CAACwK,GAAG,CAACzX,OAAO,CAACnD,SAAS,CAAC,CAAC;EAC/D;;;uBAx1CWsN,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAwN,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1ErC1f,EAAA,CAAAC,cAAA,aAA6B;UAE3BD,EAAA,CAAA2B,UAAA,IAAAie,uCAAA,iBAA+C;UAanC5f,EANZ,CAAAC,cAAA,aAAiB,aACK,aACM,aACC,aAC0C,UACxD,YACc;UACfD,EAAA,CAAAgB,SAAA,WAAoC;UACpChB,EAAA,CAAAE,MAAA,2BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,YAA2B;UAAAD,EAAA,CAAAE,MAAA,gDAAmC;UAChEF,EADgE,CAAAG,YAAA,EAAI,EAC9D;UAEJH,EADF,CAAAC,cAAA,eAAsB,gBACgB;UAAAD,EAAA,CAAAE,MAAA,IAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClEH,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAE,MAAA,IAAsC;UAE7EF,EAF6E,CAAAG,YAAA,EAAM,EAC3E,EACF;UAGNH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAgB,SAAA,eAIM;UAKhBhB,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF;UAudNH,EApdA,CAAA2B,UAAA,KAAAke,wCAAA,mBAA0C,KAAAC,wCAAA,kBAkF2B,KAAAC,wCAAA,mBAKrB,KAAAC,wCAAA,kBA6WoG,KAAAC,wCAAA,kBAW/E,KAAAC,wCAAA,mBAKrB;UA2JtClgB,EAJR,CAAAC,cAAA,eAA4H,eAC1E,eACnB,eACsB,cACG;UAC9CD,EAAA,CAAAgB,SAAA,aAA+C;UAC/ChB,EAAA,CAAAE,MAAA,sDACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAgB,SAAA,kBAA6F;UAC/FhB,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAwB,SACnB;UACDD,EAAA,CAAAE,MAAA,0CAA4B;UAAAF,EAAA,CAAAC,cAAA,cAAQ;UAAAD,EAAA,CAAAE,MAAA,IAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,4DAC3E;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,eAA8B;UAC5BD,EAAA,CAAAgB,SAAA,aAAsC;UACtChB,EAAA,CAAAE,MAAA,iIACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAA2B,UAAA,KAAAwe,wCAAA,kBAA8D;UAc9DngB,EAAA,CAAAC,cAAA,aAAqB;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAC9CF,EAD8C,CAAAG,YAAA,EAAI,EAC5C;UAEJH,EADF,CAAAC,cAAA,eAA0B,kBACwD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChGH,EAAA,CAAAC,cAAA,kBAA6G;UAA/DD,EAAA,CAAAI,UAAA,mBAAAggB,2DAAA;YAAA,OAAST,GAAA,CAAA5C,0BAAA,EAA4B;UAAA,EAAC;UAClF/c,EAAA,CAAAgB,SAAA,aAAuC;UACvChB,EAAA,CAAAE,MAAA,gCACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;UAOEH,EAJR,CAAAC,cAAA,eAAqH,eAC1D,eAC5B,eACuB,cACI;UAChDD,EAAA,CAAAgB,SAAA,aAAwC;UACxChB,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAgB,SAAA,kBAA6G;UAC/GhB,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAA2B,UAAA,KAAA0e,wCAAA,mBAA0C;UAyM5CrgB,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,kBACwD;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC/FH,EAAA,CAAAC,cAAA,kBAAsE;UACpED,EAAA,CAAAgB,SAAA,aAAuC;UACvChB,EAAA,CAAAE,MAAA,4CACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;UAOEH,EAJR,CAAAC,cAAA,eAAmI,eACjF,eACnB,eACsB,cACY;UACvDD,EAAA,CAAAgB,SAAA,aAA+C;UAC/ChB,EAAA,CAAAE,MAAA,+CACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAgB,SAAA,kBAA6G;UAC/GhB,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAwB,SACnB;UACDD,EAAA,CAAAE,MAAA,oGACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,eAAiC;UAC/BD,EAAA,CAAAgB,SAAA,aAAsC;UACtChB,EAAA,CAAAE,MAAA,6IACF;UACFF,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,eAA0B,kBACwD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChGH,EAAA,CAAAC,cAAA,kBAAoG;UAAvDD,EAAA,CAAAI,UAAA,mBAAAkgB,2DAAA;YAAA,OAASX,GAAA,CAAAb,kBAAA,EAAoB;UAAA,EAAC;UACzE9e,EAAA,CAAAgB,SAAA,aAAiD;UACjDhB,EAAA,CAAAE,MAAA,4BACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;UAOEH,EAJR,CAAAC,cAAA,eAAqI,eAC1E,eAC5B,eACoB,cACe;UACxDD,EAAA,CAAAgB,SAAA,aAAoC;UACpChB,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAgB,SAAA,kBAA6G;UAC/GhB,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAA2B,UAAA,KAAA4e,wCAAA,mBAAgC;UAmHlCvgB,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,kBACwD;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC/FH,EAAA,CAAAC,cAAA,mBAA4G;UAA9DD,EAAA,CAAAI,UAAA,mBAAAogB,4DAAA;YAAA,OAASb,GAAA,CAAA7I,yBAAA,EAA2B;UAAA,EAAC;UACjF9W,EAAA,CAAAgB,SAAA,cAAuC;UACvChB,EAAA,CAAAE,MAAA,sCACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EA1kCmB;;;UAErBH,EAAA,CAAAkB,SAAA,EAAe;UAAflB,EAAA,CAAA0B,UAAA,SAAAie,GAAA,CAAA3N,SAAA,CAAe;UAoB2BhS,EAAA,CAAAkB,SAAA,IAAuB;UAAvBlB,EAAA,CAAAsC,kBAAA,gBAAAqd,GAAA,CAAA9e,WAAA,KAAuB;UACxBb,EAAA,CAAAkB,SAAA,GAAsC;UAAtClB,EAAA,CAAAyI,kBAAA,gBAAAkX,GAAA,CAAA9e,WAAA,OAAA8e,GAAA,CAAA1M,QAAA,KAAsC;UASzEjT,EAAA,CAAAkB,SAAA,GAAyC;UAAzClB,EAAA,CAAA+L,WAAA,UAAA4T,GAAA,CAAA1L,qBAAA,QAAyC;UASnCjU,EAAA,CAAAkB,SAAA,EAAsB;UAAtBlB,EAAA,CAAA0B,UAAA,SAAAie,GAAA,CAAA9e,WAAA,MAAsB;UAkFMb,EAAA,CAAAkB,SAAA,EAAqB;UAArBlB,EAAA,CAAA0B,UAAA,SAAAie,GAAA,CAAA9e,WAAA,KAAqB;UAK3Cb,EAAA,CAAAkB,SAAA,EAAsB;UAAtBlB,EAAA,CAAA0B,UAAA,SAAAie,GAAA,CAAA9e,WAAA,MAAsB;UA6WxCb,EAAA,CAAAkB,SAAA,EAA2G;UAA3GlB,EAAA,CAAA0B,UAAA,SAAAie,GAAA,CAAA9e,WAAA,WAAA8e,GAAA,CAAApB,gCAAA,MAAAoB,GAAA,CAAAxC,4BAAA,GAAAjb,MAAA,KAA2G;UAWnElC,EAAA,CAAAkB,SAAA,EAAqB;UAArBlB,EAAA,CAAA0B,UAAA,SAAAie,GAAA,CAAA9e,WAAA,KAAqB;UAK3Cb,EAAA,CAAAkB,SAAA,EAAsB;UAAtBlB,EAAA,CAAA0B,UAAA,SAAAie,GAAA,CAAA9e,WAAA,MAAsB;UAmKEb,EAAA,CAAAkB,SAAA,IAA2B;UAA3BlB,EAAA,CAAAuB,iBAAA,CAAAoe,GAAA,CAAAjN,eAAA,kBAAAiN,GAAA,CAAAjN,eAAA,CAAAlR,IAAA,CAA2B;UAM3DxB,EAAA,CAAAkB,SAAA,GAAyC;UAAzClB,EAAA,CAAA0B,UAAA,SAAAie,GAAA,CAAAlS,wBAAA,CAAAvL,MAAA,KAAyC;UAkC7ClC,EAAA,CAAAkB,SAAA,IACF;UADElB,EAAA,CAAAsC,kBAAA,uCAAAqd,GAAA,CAAA7R,4BAAA,kBAAA6R,GAAA,CAAA7R,4BAAA,CAAAtM,IAAA,MACF;UAIMxB,EAAA,CAAAkB,SAAA,GAAkC;UAAlClB,EAAA,CAAA0B,UAAA,SAAAie,GAAA,CAAA7R,4BAAA,CAAkC;UA2PtC9N,EAAA,CAAAkB,SAAA,IACF;UADElB,EAAA,CAAAsC,kBAAA,oCAAAqd,GAAA,CAAAtO,kBAAA,kBAAAsO,GAAA,CAAAtO,kBAAA,CAAA7P,IAAA,MACF;UAIMxB,EAAA,CAAAkB,SAAA,GAAwB;UAAxBlB,EAAA,CAAA0B,UAAA,SAAAie,GAAA,CAAAtO,kBAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}