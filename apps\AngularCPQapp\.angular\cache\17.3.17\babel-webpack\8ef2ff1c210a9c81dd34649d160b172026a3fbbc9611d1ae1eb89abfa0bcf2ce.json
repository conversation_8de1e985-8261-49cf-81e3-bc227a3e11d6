{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Directive, Input } from '@angular/core';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_DATE_FORMAT } from '@taiga-ui/core/tokens';\nimport { Observable, combineLatest, map, ReplaySubject } from 'rxjs';\nclass TuiDateFormat extends Observable {\n  constructor() {\n    super(subscriber => combineLatest([this.parent, this.settings]).pipe(map(([parent, settings]) => ({\n      ...parent,\n      ...settings\n    }))).subscribe(subscriber));\n    this.settings = new ReplaySubject(1);\n    this.parent = inject(TUI_DATE_FORMAT, {\n      skipSelf: true\n    });\n  }\n  set tuiDateFormat(format) {\n    this.settings.next(format);\n  }\n  static {\n    this.ɵfac = function TuiDateFormat_Factory(t) {\n      return new (t || TuiDateFormat)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiDateFormat,\n      selectors: [[\"\", \"tuiDateFormat\", \"\"]],\n      inputs: {\n        tuiDateFormat: \"tuiDateFormat\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([tuiProvide(TUI_DATE_FORMAT, TuiDateFormat)]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiDateFormat, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiDateFormat]',\n      providers: [tuiProvide(TUI_DATE_FORMAT, TuiDateFormat)]\n    }]\n  }], function () {\n    return [];\n  }, {\n    tuiDateFormat: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiDateFormat };", "map": {"version": 3, "names": ["i0", "inject", "Directive", "Input", "tui<PERSON><PERSON><PERSON>", "TUI_DATE_FORMAT", "Observable", "combineLatest", "map", "ReplaySubject", "TuiDateFormat", "constructor", "subscriber", "parent", "settings", "pipe", "subscribe", "skipSelf", "tuiDateFormat", "format", "next", "ɵfac", "TuiDateFormat_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-directives-date-format.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Directive, Input } from '@angular/core';\nimport { tuiProvide } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TUI_DATE_FORMAT } from '@taiga-ui/core/tokens';\nimport { Observable, combineLatest, map, ReplaySubject } from 'rxjs';\n\nclass TuiDateFormat extends Observable {\n    constructor() {\n        super((subscriber) => combineLatest([this.parent, this.settings])\n            .pipe(map(([parent, settings]) => ({ ...parent, ...settings })))\n            .subscribe(subscriber));\n        this.settings = new ReplaySubject(1);\n        this.parent = inject(TUI_DATE_FORMAT, { skipSelf: true });\n    }\n    set tuiDateFormat(format) {\n        this.settings.next(format);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDateFormat, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiDateFormat, isStandalone: true, selector: \"[tuiDateFormat]\", inputs: { tuiDateFormat: \"tuiDateFormat\" }, providers: [tuiProvide(TUI_DATE_FORMAT, TuiDateFormat)], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiDateFormat, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiDateFormat]',\n                    providers: [tuiProvide(TUI_DATE_FORMAT, TuiDateFormat)],\n                }]\n        }], ctorParameters: function () { return []; }, propDecorators: { tuiDateFormat: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiDateFormat };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACxD,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,UAAU,EAAEC,aAAa,EAAEC,GAAG,EAAEC,aAAa,QAAQ,MAAM;AAEpE,MAAMC,aAAa,SAASJ,UAAU,CAAC;EACnCK,WAAWA,CAAA,EAAG;IACV,KAAK,CAAEC,UAAU,IAAKL,aAAa,CAAC,CAAC,IAAI,CAACM,MAAM,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAC5DC,IAAI,CAACP,GAAG,CAAC,CAAC,CAACK,MAAM,EAAEC,QAAQ,CAAC,MAAM;MAAE,GAAGD,MAAM;MAAE,GAAGC;IAAS,CAAC,CAAC,CAAC,CAAC,CAC/DE,SAAS,CAACJ,UAAU,CAAC,CAAC;IAC3B,IAAI,CAACE,QAAQ,GAAG,IAAIL,aAAa,CAAC,CAAC,CAAC;IACpC,IAAI,CAACI,MAAM,GAAGZ,MAAM,CAACI,eAAe,EAAE;MAAEY,QAAQ,EAAE;IAAK,CAAC,CAAC;EAC7D;EACA,IAAIC,aAAaA,CAACC,MAAM,EAAE;IACtB,IAAI,CAACL,QAAQ,CAACM,IAAI,CAACD,MAAM,CAAC;EAC9B;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFb,aAAa;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACc,IAAI,kBAD+ExB,EAAE,CAAAyB,iBAAA;MAAAC,IAAA,EACJhB,aAAa;MAAAiB,SAAA;MAAAC,MAAA;QAAAV,aAAA;MAAA;MAAAW,UAAA;MAAAC,QAAA,GADX9B,EAAE,CAAA+B,kBAAA,CACmH,CAAC3B,UAAU,CAACC,eAAe,EAAEK,aAAa,CAAC,CAAC,GADjKV,EAAE,CAAAgC,0BAAA;IAAA,EACuM;EAAE;AAChT;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGjC,EAAE,CAAAkC,iBAAA,CAGXxB,aAAa,EAAc,CAAC;IAC5GgB,IAAI,EAAExB,SAAS;IACfiC,IAAI,EAAE,CAAC;MACCN,UAAU,EAAE,IAAI;MAChBO,QAAQ,EAAE,iBAAiB;MAC3BC,SAAS,EAAE,CAACjC,UAAU,CAACC,eAAe,EAAEK,aAAa,CAAC;IAC1D,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEQ,aAAa,EAAE,CAAC;MAC1EQ,IAAI,EAAEvB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASO,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}