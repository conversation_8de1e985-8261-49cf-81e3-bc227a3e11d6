{"ast": null, "code": "import { AsyncPipe, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ChangeDetectorRef, DestroyRef, EventEmitter, ElementRef, Component, ChangeDetectionStrategy, ContentChildren, forwardRef, Input, Output } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { RouterLinkActive } from '@angular/router';\nimport { tuiInjectElement, tuiIsElement } from '@taiga-ui/cdk/utils/dom';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TUI_ANIMATIONS_SPEED, TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { delay, EMPTY, filter } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiQueryListChanges } from '@taiga-ui/cdk/observables';\nimport { TuiScrollService } from '@taiga-ui/cdk/services';\nimport { tuiMoveFocus } from '@taiga-ui/cdk/utils/focus';\nimport { tuiGetOriginalArrayFromQueryList, tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiGetDuration } from '@taiga-ui/core/utils/miscellaneous';\nconst _c0 = [\"*\"];\nfunction TuiStepperComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c1 = [\"tuiStep\", \"\"];\nfunction TuiStep_tui_icon_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tui-icon\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"icon\", ctx_r0.icon);\n  }\n}\nclass TuiStepperComponent {\n  constructor() {\n    this.steps = EMPTY_QUERY;\n    this.cdr = inject(ChangeDetectorRef);\n    this.el = tuiInjectElement();\n    this.scrollService = inject(TuiScrollService);\n    this.speed = inject(TUI_ANIMATIONS_SPEED);\n    this.destroyRef = inject(DestroyRef);\n    this.$ = inject(ResizeObserverService, {\n      self: true\n    }).pipe(takeUntilDestroyed()).subscribe(() => this.scrollIntoView(this.activeItemIndex));\n    this.activeItemIndex = 0;\n    this.orientation = 'horizontal';\n    this.activeItemIndexChange = new EventEmitter();\n  }\n  ngOnChanges() {\n    this.scrollIntoView(this.activeItemIndex);\n  }\n  indexOf(step) {\n    const index = tuiGetOriginalArrayFromQueryList(this.steps).findIndex(({\n      nativeElement\n    }) => nativeElement === step);\n    return index < 0 ? NaN : index;\n  }\n  isActive(index) {\n    return index === this.activeItemIndex;\n  }\n  activate(index) {\n    if (this.activeItemIndex === index) {\n      return;\n    }\n    this.activeItemIndex = index;\n    this.activeItemIndexChange.emit(index);\n    this.cdr.markForCheck();\n    this.scrollIntoView(index);\n  }\n  get changes$() {\n    // Delay is required to trigger change detection after steps are rendered,\n    // so they can update their \"active\" status\n    return tuiQueryListChanges(this.steps).pipe(delay(0));\n  }\n  onHorizontal(event, step) {\n    if (this.orientation !== 'horizontal' || !event.target) {\n      return;\n    }\n    event.preventDefault();\n    this.moveFocus(event.target, step);\n  }\n  onVertical(event, step) {\n    if (this.orientation !== 'vertical' || !event.target) {\n      return;\n    }\n    event.preventDefault();\n    this.moveFocus(event.target, step);\n  }\n  moveFocus(current, step) {\n    if (!tuiIsElement(current)) {\n      return;\n    }\n    const stepElements = this.steps.toArray().map(({\n      nativeElement\n    }) => nativeElement);\n    const index = stepElements.findIndex(element => element === current);\n    tuiMoveFocus(index, stepElements, step);\n  }\n  scrollIntoView(index) {\n    const step = this.steps.get(index)?.nativeElement;\n    if (!step) {\n      return;\n    }\n    const {\n      clientHeight,\n      clientWidth,\n      offsetTop,\n      offsetLeft\n    } = this.el;\n    const {\n      offsetHeight,\n      offsetWidth,\n      offsetTop: stepOffsetTop,\n      offsetLeft: stepOffsetLeft\n    } = step;\n    const top = stepOffsetTop - offsetTop - clientHeight / 2 + offsetHeight / 2;\n    const left = stepOffsetLeft - offsetLeft - clientWidth / 2 + offsetWidth / 2;\n    this.scrollService.scroll$(this.el, Math.max(0, top), Math.max(0, left), tuiGetDuration(this.speed) / 3).pipe(takeUntilDestroyed(this.destroyRef)).subscribe();\n  }\n  static {\n    this.ɵfac = function TuiStepperComponent_Factory(t) {\n      return new (t || TuiStepperComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiStepperComponent,\n      selectors: [[\"tui-stepper\"], [\"nav\", \"tuiStepper\", \"\"]],\n      contentQueries: function TuiStepperComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiStep, 4, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.steps = _t);\n        }\n      },\n      hostVars: 1,\n      hostBindings: function TuiStepperComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.arrowRight\", function TuiStepperComponent_keydown_arrowRight_HostBindingHandler($event) {\n            return ctx.onHorizontal($event, 1);\n          })(\"keydown.arrowLeft\", function TuiStepperComponent_keydown_arrowLeft_HostBindingHandler($event) {\n            return ctx.onHorizontal($event, -1);\n          })(\"keydown.arrowDown\", function TuiStepperComponent_keydown_arrowDown_HostBindingHandler($event) {\n            return ctx.onVertical($event, 1);\n          })(\"keydown.arrowUp\", function TuiStepperComponent_keydown_arrowUp_HostBindingHandler($event) {\n            return ctx.onVertical($event, -1);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-orientation\", ctx.orientation);\n        }\n      },\n      inputs: {\n        activeItemIndex: \"activeItemIndex\",\n        orientation: \"orientation\"\n      },\n      outputs: {\n        activeItemIndexChange: \"activeItemIndexChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([ResizeObserverService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 3,\n      consts: [[4, \"ngIf\"]],\n      template: function TuiStepperComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, TuiStepperComponent_ng_container_0_Template, 1, 0, \"ng-container\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵprojection(2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.changes$));\n        }\n      },\n      dependencies: [AsyncPipe, NgIf],\n      styles: [\"[_nghost-%COMP%]{scrollbar-width:none;-ms-overflow-style:none;display:flex;scroll-behavior:var(--tui-scroll-behavior);overflow:auto;counter-reset:steps;font:var(--tui-font-text-m)}[_nghost-%COMP%]::-webkit-scrollbar, [_nghost-%COMP%]::-webkit-scrollbar-thumb{display:none}[data-orientation=vertical][_nghost-%COMP%]{flex-direction:column}\"],\n      changeDetection: 0\n    });\n  }\n}\n__decorate([tuiPure], TuiStepperComponent.prototype, \"changes$\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiStepperComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-stepper, nav[tuiStepper]',\n      imports: [AsyncPipe, NgIf],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [ResizeObserverService],\n      host: {\n        '[attr.data-orientation]': 'orientation',\n        '(keydown.arrowRight)': 'onHorizontal($event, 1)',\n        '(keydown.arrowLeft)': 'onHorizontal($event, -1)',\n        '(keydown.arrowDown)': 'onVertical($event, 1)',\n        '(keydown.arrowUp)': 'onVertical($event, -1)'\n      },\n      template: \"<ng-container *ngIf=\\\"changes$ | async\\\" />\\n<ng-content />\\n\",\n      styles: [\":host{scrollbar-width:none;-ms-overflow-style:none;display:flex;scroll-behavior:var(--tui-scroll-behavior);overflow:auto;counter-reset:steps;font:var(--tui-font-text-m)}:host::-webkit-scrollbar,:host::-webkit-scrollbar-thumb{display:none}:host[data-orientation=vertical]{flex-direction:column}\\n\"]\n    }]\n  }], null, {\n    steps: [{\n      type: ContentChildren,\n      args: [forwardRef(() => TuiStep), {\n        read: ElementRef\n      }]\n    }],\n    activeItemIndex: [{\n      type: Input\n    }],\n    orientation: [{\n      type: Input\n    }],\n    activeItemIndexChange: [{\n      type: Output\n    }],\n    changes$: []\n  });\n})();\nclass TuiStep {\n  constructor() {\n    this.stepper = inject(TuiStepperComponent);\n    this.el = tuiInjectElement();\n    this.$ = (inject(RouterLinkActive, {\n      optional: true\n    })?.isActiveChange.asObservable() ?? EMPTY).pipe(filter(Boolean), takeUntilDestroyed()).subscribe(() => this.activate());\n    this.focusVisible = false;\n    this.icons = inject(TUI_COMMON_ICONS);\n    this.stepState = 'normal';\n    this.icon = '';\n  }\n  get isActive() {\n    return this.stepper.isActive(this.index);\n  }\n  get isVertical() {\n    return this.stepper.orientation === 'vertical';\n  }\n  get tabIndex() {\n    return this.isActive ? 0 : -1;\n  }\n  get index() {\n    return this.stepper.indexOf(this.el);\n  }\n  activate() {\n    this.stepper.activate(this.index);\n  }\n  static {\n    this.ɵfac = function TuiStep_Factory(t) {\n      return new (t || TuiStep)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiStep,\n      selectors: [[\"button\", \"tuiStep\", \"\"], [\"a\", \"tuiStep\", \"\", 3, \"routerLink\", \"\"], [\"a\", \"tuiStep\", \"\", \"routerLink\", \"\", \"routerLinkActive\", \"\"]],\n      hostVars: 8,\n      hostBindings: function TuiStep_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TuiStep_click_HostBindingHandler() {\n            return ctx.activate();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"tabIndex\", ctx.tabIndex);\n          i0.ɵɵattribute(\"data-state\", ctx.stepState);\n          i0.ɵɵclassProp(\"_vertical\", ctx.isVertical)(\"_focus-visible\", ctx.focusVisible)(\"_active\", ctx.isActive);\n        }\n      },\n      inputs: {\n        stepState: \"stepState\",\n        icon: \"icon\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c1,\n      ngContentSelectors: _c0,\n      decls: 5,\n      vars: 3,\n      consts: [[\"class\", \"t-marker t-marker_custom\", 3, \"icon\", 4, \"ngIf\"], [1, \"t-marker\", \"t-marker_error\", 3, \"icon\"], [1, \"t-marker\", \"t-marker_pass\", 3, \"icon\"], [1, \"t-marker\", \"t-marker_index\"], [1, \"t-marker\", \"t-marker_custom\", 3, \"icon\"]],\n      template: function TuiStep_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, TuiStep_tui_icon_0_Template, 1, 1, \"tui-icon\", 0);\n          i0.ɵɵelement(1, \"tui-icon\", 1)(2, \"tui-icon\", 2)(3, \"div\", 3);\n          i0.ɵɵprojection(4);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.icon);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"icon\", ctx.icons.error);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"icon\", ctx.icons.check);\n        }\n      },\n      dependencies: [NgIf, TuiIcon],\n      styles: [\"[_nghost-%COMP%]{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;position:relative;display:flex;align-items:center;flex-shrink:0;color:var(--tui-text-action);margin-right:1.5rem;outline:none;cursor:pointer;counter-increment:steps;text-align:start}[_nghost-%COMP%]:disabled{pointer-events:none;color:var(--tui-text-secondary)}[_nghost-%COMP%]:hover{color:var(--tui-text-action-hover)}[_nghost-%COMP%]:not(:last-of-type)._vertical{margin-bottom:1.25rem}._active[_nghost-%COMP%], ._active[_nghost-%COMP%]:hover{color:var(--tui-text-primary);cursor:default}[_nghost-%COMP%]:focus-visible:before{content:\\\"\\\";position:absolute;left:2.75rem;right:0;top:50%;block-size:1.5rem;margin-top:-.75rem;background:var(--tui-service-selection-background)}.t-marker[_ngcontent-%COMP%]{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:relative;display:flex;inline-size:2rem;block-size:2rem;border-radius:100%;margin-right:.75rem;flex-shrink:0;align-items:center;align-self:flex-start;justify-content:center;background:var(--tui-background-neutral-1);color:var(--tui-text-action);border:.5rem solid transparent;box-sizing:border-box}[_nghost-%COMP%]:disabled   .t-marker[_ngcontent-%COMP%]{background:var(--tui-background-neutral-1);color:var(--tui-text-secondary)}.t-marker_index[_ngcontent-%COMP%]:before{content:counter(steps)}[_nghost-%COMP%]:hover   .t-marker_index[_ngcontent-%COMP%]{color:var(--tui-text-action-hover);background:var(--tui-background-neutral-1-hover)}._active[_nghost-%COMP%]   .t-marker_index[_ngcontent-%COMP%]{color:var(--tui-text-primary-on-accent-1);background:var(--tui-background-accent-1)}[_nghost-%COMP%]:not([data-state=normal]):not(._active)   .t-marker_index[_ngcontent-%COMP%], [_nghost-%COMP%]:not(._active)   .t-marker_custom[_ngcontent-%COMP%] ~ .t-marker_index[_ngcontent-%COMP%]{display:none}.t-marker_error[_ngcontent-%COMP%]{background:var(--tui-status-negative-pale);color:var(--tui-status-negative)}[_nghost-%COMP%]:hover   .t-marker_error[_ngcontent-%COMP%]{background:var(--tui-status-negative-pale-hover);color:var(--tui-status-negative)}[_nghost-%COMP%]:not([data-state=error])   .t-marker_error[_ngcontent-%COMP%], ._active[_nghost-%COMP%]   .t-marker_error[_ngcontent-%COMP%]{display:none}[_nghost-%COMP%]:not([data-state=pass])   .t-marker_pass[_ngcontent-%COMP%], ._active[_nghost-%COMP%]   .t-marker_pass[_ngcontent-%COMP%]{display:none}[_nghost-%COMP%]:not([data-state=normal])   .t-marker_custom[_ngcontent-%COMP%], ._active[_nghost-%COMP%]   .t-marker_custom[_ngcontent-%COMP%]{display:none}[_nghost-%COMP%]:hover   .t-marker_custom[_ngcontent-%COMP%]{color:var(--tui-text-action-hover);background:var(--tui-background-neutral-1-hover)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiStep, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'button[tuiStep], a[tuiStep]:not([routerLink]), a[tuiStep][routerLink][routerLinkActive]',\n      imports: [NgIf, TuiIcon],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[attr.data-state]': 'stepState',\n        '[class._vertical]': 'isVertical',\n        '[class._focus-visible]': 'focusVisible',\n        '[class._active]': 'isActive',\n        '[tabIndex]': 'tabIndex',\n        '(click)': 'activate()'\n      },\n      template: \"<tui-icon\\n    *ngIf=\\\"icon\\\"\\n    class=\\\"t-marker t-marker_custom\\\"\\n    [icon]=\\\"icon\\\"\\n/>\\n<tui-icon\\n    class=\\\"t-marker t-marker_error\\\"\\n    [icon]=\\\"icons.error\\\"\\n/>\\n<tui-icon\\n    class=\\\"t-marker t-marker_pass\\\"\\n    [icon]=\\\"icons.check\\\"\\n/>\\n<div class=\\\"t-marker t-marker_index\\\"></div>\\n<ng-content />\\n\",\n      styles: [\":host{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;position:relative;display:flex;align-items:center;flex-shrink:0;color:var(--tui-text-action);margin-right:1.5rem;outline:none;cursor:pointer;counter-increment:steps;text-align:start}:host:disabled{pointer-events:none;color:var(--tui-text-secondary)}:host:hover{color:var(--tui-text-action-hover)}:host:not(:last-of-type)._vertical{margin-bottom:1.25rem}:host._active,:host._active:hover{color:var(--tui-text-primary);cursor:default}:host:focus-visible:before{content:\\\"\\\";position:absolute;left:2.75rem;right:0;top:50%;block-size:1.5rem;margin-top:-.75rem;background:var(--tui-service-selection-background)}.t-marker{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:relative;display:flex;inline-size:2rem;block-size:2rem;border-radius:100%;margin-right:.75rem;flex-shrink:0;align-items:center;align-self:flex-start;justify-content:center;background:var(--tui-background-neutral-1);color:var(--tui-text-action);border:.5rem solid transparent;box-sizing:border-box}:host:disabled .t-marker{background:var(--tui-background-neutral-1);color:var(--tui-text-secondary)}.t-marker_index:before{content:counter(steps)}:host:hover .t-marker_index{color:var(--tui-text-action-hover);background:var(--tui-background-neutral-1-hover)}:host._active .t-marker_index{color:var(--tui-text-primary-on-accent-1);background:var(--tui-background-accent-1)}:host:not([data-state=normal]):not(._active) .t-marker_index,:host:not(._active) .t-marker_custom~.t-marker_index{display:none}.t-marker_error{background:var(--tui-status-negative-pale);color:var(--tui-status-negative)}:host:hover .t-marker_error{background:var(--tui-status-negative-pale-hover);color:var(--tui-status-negative)}:host:not([data-state=error]) .t-marker_error,:host._active .t-marker_error{display:none}:host:not([data-state=pass]) .t-marker_pass,:host._active .t-marker_pass{display:none}:host:not([data-state=normal]) .t-marker_custom,:host._active .t-marker_custom{display:none}:host:hover .t-marker_custom{color:var(--tui-text-action-hover);background:var(--tui-background-neutral-1-hover)}\\n\"]\n    }]\n  }], null, {\n    stepState: [{\n      type: Input\n    }],\n    icon: [{\n      type: Input\n    }]\n  });\n})();\nconst TuiStepper = [TuiStepperComponent, TuiStep];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiStep, TuiStepper, TuiStepperComponent };", "map": {"version": 3, "names": ["AsyncPipe", "NgIf", "i0", "inject", "ChangeDetectorRef", "DestroyRef", "EventEmitter", "ElementRef", "Component", "ChangeDetectionStrategy", "ContentChildren", "forwardRef", "Input", "Output", "takeUntilDestroyed", "RouterLinkActive", "tuiInjectElement", "tuiIsElement", "TuiIcon", "TUI_ANIMATIONS_SPEED", "TUI_COMMON_ICONS", "delay", "EMPTY", "filter", "__decorate", "ResizeObserverService", "EMPTY_QUERY", "tui<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TuiScrollService", "tuiMoveFocus", "tuiGetOriginalArrayFromQueryList", "tuiPure", "tuiGetDuration", "_c0", "TuiStepperComponent_ng_container_0_Template", "rf", "ctx", "ɵɵelementContainer", "_c1", "TuiStep_tui_icon_0_Template", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "icon", "TuiStepperComponent", "constructor", "steps", "cdr", "el", "scrollService", "speed", "destroyRef", "$", "self", "pipe", "subscribe", "scrollIntoView", "activeItemIndex", "orientation", "activeItemIndexChange", "ngOnChanges", "indexOf", "step", "index", "findIndex", "nativeElement", "NaN", "isActive", "activate", "emit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "changes$", "onHorizontal", "event", "target", "preventDefault", "moveFocus", "onVertical", "current", "stepElements", "toArray", "map", "element", "get", "clientHeight", "clientWidth", "offsetTop", "offsetLeft", "offsetHeight", "offsetWidth", "stepOffsetTop", "stepOffsetLeft", "top", "left", "scroll$", "Math", "max", "ɵfac", "TuiStepperComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "TuiStepperComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "TuiStep", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostVars", "hostBindings", "TuiStepperComponent_HostBindings", "ɵɵlistener", "TuiStepperComponent_keydown_arrowRight_HostBindingHandler", "$event", "TuiStepperComponent_keydown_arrowLeft_HostBindingHandler", "TuiStepperComponent_keydown_arrowDown_HostBindingHandler", "TuiStepperComponent_keydown_arrowUp_HostBindingHandler", "ɵɵattribute", "inputs", "outputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "TuiStepperComponent_Template", "ɵɵprojectionDef", "ɵɵtemplate", "ɵɵpipe", "ɵɵprojection", "ɵɵpipeBind1", "dependencies", "styles", "changeDetection", "prototype", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "imports", "OnPush", "providers", "host", "read", "stepper", "optional", "isActiveChange", "asObservable", "Boolean", "focusVisible", "icons", "stepState", "isVertical", "tabIndex", "TuiStep_Factory", "TuiStep_HostBindings", "TuiStep_click_HostBindingHandler", "ɵɵhostProperty", "ɵɵclassProp", "attrs", "TuiStep_Template", "ɵɵadvance", "error", "check", "TuiStepper"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-stepper.mjs"], "sourcesContent": ["import { AsyncPipe, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ChangeDetectorRef, DestroyRef, EventEmitter, ElementRef, Component, ChangeDetectionStrategy, ContentChildren, forwardRef, Input, Output } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { RouterLinkActive } from '@angular/router';\nimport { tuiInjectElement, tuiIsElement } from '@taiga-ui/cdk/utils/dom';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TUI_ANIMATIONS_SPEED, TUI_COMMON_ICONS } from '@taiga-ui/core/tokens';\nimport { delay, EMPTY, filter } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiQueryListChanges } from '@taiga-ui/cdk/observables';\nimport { TuiScrollService } from '@taiga-ui/cdk/services';\nimport { tuiMoveFocus } from '@taiga-ui/cdk/utils/focus';\nimport { tuiGetOriginalArrayFromQueryList, tuiPure } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiGetDuration } from '@taiga-ui/core/utils/miscellaneous';\n\nclass TuiStepperComponent {\n    constructor() {\n        this.steps = EMPTY_QUERY;\n        this.cdr = inject(ChangeDetectorRef);\n        this.el = tuiInjectElement();\n        this.scrollService = inject(TuiScrollService);\n        this.speed = inject(TUI_ANIMATIONS_SPEED);\n        this.destroyRef = inject(DestroyRef);\n        this.$ = inject(ResizeObserverService, { self: true })\n            .pipe(takeUntilDestroyed())\n            .subscribe(() => this.scrollIntoView(this.activeItemIndex));\n        this.activeItemIndex = 0;\n        this.orientation = 'horizontal';\n        this.activeItemIndexChange = new EventEmitter();\n    }\n    ngOnChanges() {\n        this.scrollIntoView(this.activeItemIndex);\n    }\n    indexOf(step) {\n        const index = tuiGetOriginalArrayFromQueryList(this.steps).findIndex(({ nativeElement }) => nativeElement === step);\n        return index < 0 ? NaN : index;\n    }\n    isActive(index) {\n        return index === this.activeItemIndex;\n    }\n    activate(index) {\n        if (this.activeItemIndex === index) {\n            return;\n        }\n        this.activeItemIndex = index;\n        this.activeItemIndexChange.emit(index);\n        this.cdr.markForCheck();\n        this.scrollIntoView(index);\n    }\n    get changes$() {\n        // Delay is required to trigger change detection after steps are rendered,\n        // so they can update their \"active\" status\n        return tuiQueryListChanges(this.steps).pipe(delay(0));\n    }\n    onHorizontal(event, step) {\n        if (this.orientation !== 'horizontal' || !event.target) {\n            return;\n        }\n        event.preventDefault();\n        this.moveFocus(event.target, step);\n    }\n    onVertical(event, step) {\n        if (this.orientation !== 'vertical' || !event.target) {\n            return;\n        }\n        event.preventDefault();\n        this.moveFocus(event.target, step);\n    }\n    moveFocus(current, step) {\n        if (!tuiIsElement(current)) {\n            return;\n        }\n        const stepElements = this.steps.toArray().map(({ nativeElement }) => nativeElement);\n        const index = stepElements.findIndex((element) => element === current);\n        tuiMoveFocus(index, stepElements, step);\n    }\n    scrollIntoView(index) {\n        const step = this.steps.get(index)?.nativeElement;\n        if (!step) {\n            return;\n        }\n        const { clientHeight, clientWidth, offsetTop, offsetLeft } = this.el;\n        const { offsetHeight, offsetWidth, offsetTop: stepOffsetTop, offsetLeft: stepOffsetLeft, } = step;\n        const top = stepOffsetTop - offsetTop - clientHeight / 2 + offsetHeight / 2;\n        const left = stepOffsetLeft - offsetLeft - clientWidth / 2 + offsetWidth / 2;\n        this.scrollService\n            .scroll$(this.el, Math.max(0, top), Math.max(0, left), tuiGetDuration(this.speed) / 3)\n            .pipe(takeUntilDestroyed(this.destroyRef))\n            .subscribe();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStepperComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiStepperComponent, isStandalone: true, selector: \"tui-stepper, nav[tuiStepper]\", inputs: { activeItemIndex: \"activeItemIndex\", orientation: \"orientation\" }, outputs: { activeItemIndexChange: \"activeItemIndexChange\" }, host: { listeners: { \"keydown.arrowRight\": \"onHorizontal($event, 1)\", \"keydown.arrowLeft\": \"onHorizontal($event, -1)\", \"keydown.arrowDown\": \"onVertical($event, 1)\", \"keydown.arrowUp\": \"onVertical($event, -1)\" }, properties: { \"attr.data-orientation\": \"orientation\" } }, providers: [ResizeObserverService], queries: [{ propertyName: \"steps\", predicate: i0.forwardRef(function () { return TuiStep; }), read: ElementRef }], usesOnChanges: true, ngImport: i0, template: \"<ng-container *ngIf=\\\"changes$ | async\\\" />\\n<ng-content />\\n\", styles: [\":host{scrollbar-width:none;-ms-overflow-style:none;display:flex;scroll-behavior:var(--tui-scroll-behavior);overflow:auto;counter-reset:steps;font:var(--tui-font-text-m)}:host::-webkit-scrollbar,:host::-webkit-scrollbar-thumb{display:none}:host[data-orientation=vertical]{flex-direction:column}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\n__decorate([\n    tuiPure\n], TuiStepperComponent.prototype, \"changes$\", null);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStepperComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-stepper, nav[tuiStepper]', imports: [AsyncPipe, NgIf], changeDetection: ChangeDetectionStrategy.OnPush, providers: [ResizeObserverService], host: {\n                        '[attr.data-orientation]': 'orientation',\n                        '(keydown.arrowRight)': 'onHorizontal($event, 1)',\n                        '(keydown.arrowLeft)': 'onHorizontal($event, -1)',\n                        '(keydown.arrowDown)': 'onVertical($event, 1)',\n                        '(keydown.arrowUp)': 'onVertical($event, -1)',\n                    }, template: \"<ng-container *ngIf=\\\"changes$ | async\\\" />\\n<ng-content />\\n\", styles: [\":host{scrollbar-width:none;-ms-overflow-style:none;display:flex;scroll-behavior:var(--tui-scroll-behavior);overflow:auto;counter-reset:steps;font:var(--tui-font-text-m)}:host::-webkit-scrollbar,:host::-webkit-scrollbar-thumb{display:none}:host[data-orientation=vertical]{flex-direction:column}\\n\"] }]\n        }], propDecorators: { steps: [{\n                type: ContentChildren,\n                args: [forwardRef(() => TuiStep), { read: ElementRef }]\n            }], activeItemIndex: [{\n                type: Input\n            }], orientation: [{\n                type: Input\n            }], activeItemIndexChange: [{\n                type: Output\n            }], changes$: [] } });\n\nclass TuiStep {\n    constructor() {\n        this.stepper = inject(TuiStepperComponent);\n        this.el = tuiInjectElement();\n        this.$ = (inject(RouterLinkActive, { optional: true })?.isActiveChange.asObservable() ?? EMPTY)\n            .pipe(filter(Boolean), takeUntilDestroyed())\n            .subscribe(() => this.activate());\n        this.focusVisible = false;\n        this.icons = inject(TUI_COMMON_ICONS);\n        this.stepState = 'normal';\n        this.icon = '';\n    }\n    get isActive() {\n        return this.stepper.isActive(this.index);\n    }\n    get isVertical() {\n        return this.stepper.orientation === 'vertical';\n    }\n    get tabIndex() {\n        return this.isActive ? 0 : -1;\n    }\n    get index() {\n        return this.stepper.indexOf(this.el);\n    }\n    activate() {\n        this.stepper.activate(this.index);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStep, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiStep, isStandalone: true, selector: \"button[tuiStep], a[tuiStep]:not([routerLink]), a[tuiStep][routerLink][routerLinkActive]\", inputs: { stepState: \"stepState\", icon: \"icon\" }, host: { listeners: { \"click\": \"activate()\" }, properties: { \"attr.data-state\": \"stepState\", \"class._vertical\": \"isVertical\", \"class._focus-visible\": \"focusVisible\", \"class._active\": \"isActive\", \"tabIndex\": \"tabIndex\" } }, ngImport: i0, template: \"<tui-icon\\n    *ngIf=\\\"icon\\\"\\n    class=\\\"t-marker t-marker_custom\\\"\\n    [icon]=\\\"icon\\\"\\n/>\\n<tui-icon\\n    class=\\\"t-marker t-marker_error\\\"\\n    [icon]=\\\"icons.error\\\"\\n/>\\n<tui-icon\\n    class=\\\"t-marker t-marker_pass\\\"\\n    [icon]=\\\"icons.check\\\"\\n/>\\n<div class=\\\"t-marker t-marker_index\\\"></div>\\n<ng-content />\\n\", styles: [\":host{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;position:relative;display:flex;align-items:center;flex-shrink:0;color:var(--tui-text-action);margin-right:1.5rem;outline:none;cursor:pointer;counter-increment:steps;text-align:start}:host:disabled{pointer-events:none;color:var(--tui-text-secondary)}:host:hover{color:var(--tui-text-action-hover)}:host:not(:last-of-type)._vertical{margin-bottom:1.25rem}:host._active,:host._active:hover{color:var(--tui-text-primary);cursor:default}:host:focus-visible:before{content:\\\"\\\";position:absolute;left:2.75rem;right:0;top:50%;block-size:1.5rem;margin-top:-.75rem;background:var(--tui-service-selection-background)}.t-marker{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:relative;display:flex;inline-size:2rem;block-size:2rem;border-radius:100%;margin-right:.75rem;flex-shrink:0;align-items:center;align-self:flex-start;justify-content:center;background:var(--tui-background-neutral-1);color:var(--tui-text-action);border:.5rem solid transparent;box-sizing:border-box}:host:disabled .t-marker{background:var(--tui-background-neutral-1);color:var(--tui-text-secondary)}.t-marker_index:before{content:counter(steps)}:host:hover .t-marker_index{color:var(--tui-text-action-hover);background:var(--tui-background-neutral-1-hover)}:host._active .t-marker_index{color:var(--tui-text-primary-on-accent-1);background:var(--tui-background-accent-1)}:host:not([data-state=normal]):not(._active) .t-marker_index,:host:not(._active) .t-marker_custom~.t-marker_index{display:none}.t-marker_error{background:var(--tui-status-negative-pale);color:var(--tui-status-negative)}:host:hover .t-marker_error{background:var(--tui-status-negative-pale-hover);color:var(--tui-status-negative)}:host:not([data-state=error]) .t-marker_error,:host._active .t-marker_error{display:none}:host:not([data-state=pass]) .t-marker_pass,:host._active .t-marker_pass{display:none}:host:not([data-state=normal]) .t-marker_custom,:host._active .t-marker_custom{display:none}:host:hover .t-marker_custom{color:var(--tui-text-action-hover);background:var(--tui-background-neutral-1-hover)}\\n\"], dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: TuiIcon, selector: \"tui-icon\", inputs: [\"icon\", \"background\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiStep, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'button[tuiStep], a[tuiStep]:not([routerLink]), a[tuiStep][routerLink][routerLinkActive]', imports: [NgIf, TuiIcon], changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[attr.data-state]': 'stepState',\n                        '[class._vertical]': 'isVertical',\n                        '[class._focus-visible]': 'focusVisible',\n                        '[class._active]': 'isActive',\n                        '[tabIndex]': 'tabIndex',\n                        '(click)': 'activate()',\n                    }, template: \"<tui-icon\\n    *ngIf=\\\"icon\\\"\\n    class=\\\"t-marker t-marker_custom\\\"\\n    [icon]=\\\"icon\\\"\\n/>\\n<tui-icon\\n    class=\\\"t-marker t-marker_error\\\"\\n    [icon]=\\\"icons.error\\\"\\n/>\\n<tui-icon\\n    class=\\\"t-marker t-marker_pass\\\"\\n    [icon]=\\\"icons.check\\\"\\n/>\\n<div class=\\\"t-marker t-marker_index\\\"></div>\\n<ng-content />\\n\", styles: [\":host{-webkit-appearance:none;appearance:none;padding:0;border:0;background:none;font:inherit;line-height:inherit;text-decoration:none;position:relative;display:flex;align-items:center;flex-shrink:0;color:var(--tui-text-action);margin-right:1.5rem;outline:none;cursor:pointer;counter-increment:steps;text-align:start}:host:disabled{pointer-events:none;color:var(--tui-text-secondary)}:host:hover{color:var(--tui-text-action-hover)}:host:not(:last-of-type)._vertical{margin-bottom:1.25rem}:host._active,:host._active:hover{color:var(--tui-text-primary);cursor:default}:host:focus-visible:before{content:\\\"\\\";position:absolute;left:2.75rem;right:0;top:50%;block-size:1.5rem;margin-top:-.75rem;background:var(--tui-service-selection-background)}.t-marker{transition-property:background;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;position:relative;display:flex;inline-size:2rem;block-size:2rem;border-radius:100%;margin-right:.75rem;flex-shrink:0;align-items:center;align-self:flex-start;justify-content:center;background:var(--tui-background-neutral-1);color:var(--tui-text-action);border:.5rem solid transparent;box-sizing:border-box}:host:disabled .t-marker{background:var(--tui-background-neutral-1);color:var(--tui-text-secondary)}.t-marker_index:before{content:counter(steps)}:host:hover .t-marker_index{color:var(--tui-text-action-hover);background:var(--tui-background-neutral-1-hover)}:host._active .t-marker_index{color:var(--tui-text-primary-on-accent-1);background:var(--tui-background-accent-1)}:host:not([data-state=normal]):not(._active) .t-marker_index,:host:not(._active) .t-marker_custom~.t-marker_index{display:none}.t-marker_error{background:var(--tui-status-negative-pale);color:var(--tui-status-negative)}:host:hover .t-marker_error{background:var(--tui-status-negative-pale-hover);color:var(--tui-status-negative)}:host:not([data-state=error]) .t-marker_error,:host._active .t-marker_error{display:none}:host:not([data-state=pass]) .t-marker_pass,:host._active .t-marker_pass{display:none}:host:not([data-state=normal]) .t-marker_custom,:host._active .t-marker_custom{display:none}:host:hover .t-marker_custom{color:var(--tui-text-action-hover);background:var(--tui-background-neutral-1-hover)}\\n\"] }]\n        }], propDecorators: { stepState: [{\n                type: Input\n            }], icon: [{\n                type: Input\n            }] } });\n\nconst TuiStepper = [TuiStepperComponent, TuiStep];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiStep, TuiStepper, TuiStepperComponent };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,IAAI,QAAQ,iBAAiB;AACjD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAC/K,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AACxE,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,oBAAoB,EAAEC,gBAAgB,QAAQ,uBAAuB;AAC9E,SAASC,KAAK,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC3C,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,gCAAgC,EAAEC,OAAO,QAAQ,mCAAmC;AAC7F,SAASC,cAAc,QAAQ,oCAAoC;AAAC,MAAAC,GAAA;AAAA,SAAAC,4CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA6EiCjC,EAAE,CAAAmC,kBAAA,EACqtB,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,4BAAAJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADxtBjC,EAAE,CAAAsC,SAAA,iBAsDogB,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,MAAA,GAtDvgBvC,EAAE,CAAAwC,aAAA;IAAFxC,EAAE,CAAAyC,UAAA,SAAAF,MAAA,CAAAG,IAsDggB,CAAC;EAAA;AAAA;AAjIxmB,MAAMC,mBAAmB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAGrB,WAAW;IACxB,IAAI,CAACsB,GAAG,GAAG7C,MAAM,CAACC,iBAAiB,CAAC;IACpC,IAAI,CAAC6C,EAAE,GAAGjC,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACkC,aAAa,GAAG/C,MAAM,CAACyB,gBAAgB,CAAC;IAC7C,IAAI,CAACuB,KAAK,GAAGhD,MAAM,CAACgB,oBAAoB,CAAC;IACzC,IAAI,CAACiC,UAAU,GAAGjD,MAAM,CAACE,UAAU,CAAC;IACpC,IAAI,CAACgD,CAAC,GAAGlD,MAAM,CAACsB,qBAAqB,EAAE;MAAE6B,IAAI,EAAE;IAAK,CAAC,CAAC,CACjDC,IAAI,CAACzC,kBAAkB,CAAC,CAAC,CAAC,CAC1B0C,SAAS,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC;IAC/D,IAAI,CAACA,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,WAAW,GAAG,YAAY;IAC/B,IAAI,CAACC,qBAAqB,GAAG,IAAItD,YAAY,CAAC,CAAC;EACnD;EACAuD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACJ,cAAc,CAAC,IAAI,CAACC,eAAe,CAAC;EAC7C;EACAI,OAAOA,CAACC,IAAI,EAAE;IACV,MAAMC,KAAK,GAAGlC,gCAAgC,CAAC,IAAI,CAACiB,KAAK,CAAC,CAACkB,SAAS,CAAC,CAAC;MAAEC;IAAc,CAAC,KAAKA,aAAa,KAAKH,IAAI,CAAC;IACnH,OAAOC,KAAK,GAAG,CAAC,GAAGG,GAAG,GAAGH,KAAK;EAClC;EACAI,QAAQA,CAACJ,KAAK,EAAE;IACZ,OAAOA,KAAK,KAAK,IAAI,CAACN,eAAe;EACzC;EACAW,QAAQA,CAACL,KAAK,EAAE;IACZ,IAAI,IAAI,CAACN,eAAe,KAAKM,KAAK,EAAE;MAChC;IACJ;IACA,IAAI,CAACN,eAAe,GAAGM,KAAK;IAC5B,IAAI,CAACJ,qBAAqB,CAACU,IAAI,CAACN,KAAK,CAAC;IACtC,IAAI,CAAChB,GAAG,CAACuB,YAAY,CAAC,CAAC;IACvB,IAAI,CAACd,cAAc,CAACO,KAAK,CAAC;EAC9B;EACA,IAAIQ,QAAQA,CAAA,EAAG;IACX;IACA;IACA,OAAO7C,mBAAmB,CAAC,IAAI,CAACoB,KAAK,CAAC,CAACQ,IAAI,CAAClC,KAAK,CAAC,CAAC,CAAC,CAAC;EACzD;EACAoD,YAAYA,CAACC,KAAK,EAAEX,IAAI,EAAE;IACtB,IAAI,IAAI,CAACJ,WAAW,KAAK,YAAY,IAAI,CAACe,KAAK,CAACC,MAAM,EAAE;MACpD;IACJ;IACAD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtB,IAAI,CAACC,SAAS,CAACH,KAAK,CAACC,MAAM,EAAEZ,IAAI,CAAC;EACtC;EACAe,UAAUA,CAACJ,KAAK,EAAEX,IAAI,EAAE;IACpB,IAAI,IAAI,CAACJ,WAAW,KAAK,UAAU,IAAI,CAACe,KAAK,CAACC,MAAM,EAAE;MAClD;IACJ;IACAD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtB,IAAI,CAACC,SAAS,CAACH,KAAK,CAACC,MAAM,EAAEZ,IAAI,CAAC;EACtC;EACAc,SAASA,CAACE,OAAO,EAAEhB,IAAI,EAAE;IACrB,IAAI,CAAC9C,YAAY,CAAC8D,OAAO,CAAC,EAAE;MACxB;IACJ;IACA,MAAMC,YAAY,GAAG,IAAI,CAACjC,KAAK,CAACkC,OAAO,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC;MAAEhB;IAAc,CAAC,KAAKA,aAAa,CAAC;IACnF,MAAMF,KAAK,GAAGgB,YAAY,CAACf,SAAS,CAAEkB,OAAO,IAAKA,OAAO,KAAKJ,OAAO,CAAC;IACtElD,YAAY,CAACmC,KAAK,EAAEgB,YAAY,EAAEjB,IAAI,CAAC;EAC3C;EACAN,cAAcA,CAACO,KAAK,EAAE;IAClB,MAAMD,IAAI,GAAG,IAAI,CAAChB,KAAK,CAACqC,GAAG,CAACpB,KAAK,CAAC,EAAEE,aAAa;IACjD,IAAI,CAACH,IAAI,EAAE;MACP;IACJ;IACA,MAAM;MAAEsB,YAAY;MAAEC,WAAW;MAAEC,SAAS;MAAEC;IAAW,CAAC,GAAG,IAAI,CAACvC,EAAE;IACpE,MAAM;MAAEwC,YAAY;MAAEC,WAAW;MAAEH,SAAS,EAAEI,aAAa;MAAEH,UAAU,EAAEI;IAAgB,CAAC,GAAG7B,IAAI;IACjG,MAAM8B,GAAG,GAAGF,aAAa,GAAGJ,SAAS,GAAGF,YAAY,GAAG,CAAC,GAAGI,YAAY,GAAG,CAAC;IAC3E,MAAMK,IAAI,GAAGF,cAAc,GAAGJ,UAAU,GAAGF,WAAW,GAAG,CAAC,GAAGI,WAAW,GAAG,CAAC;IAC5E,IAAI,CAACxC,aAAa,CACb6C,OAAO,CAAC,IAAI,CAAC9C,EAAE,EAAE+C,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,GAAG,CAAC,EAAEG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,IAAI,CAAC,EAAE9D,cAAc,CAAC,IAAI,CAACmB,KAAK,CAAC,GAAG,CAAC,CAAC,CACrFI,IAAI,CAACzC,kBAAkB,CAAC,IAAI,CAACsC,UAAU,CAAC,CAAC,CACzCI,SAAS,CAAC,CAAC;EACpB;EACA;IAAS,IAAI,CAAC0C,IAAI,YAAAC,4BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFvD,mBAAmB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAACwD,IAAI,kBAD+EnG,EAAE,CAAAoG,iBAAA;MAAAC,IAAA,EACJ1D,mBAAmB;MAAA2D,SAAA;MAAAC,cAAA,WAAAC,mCAAAvE,EAAA,EAAAC,GAAA,EAAAuE,QAAA;QAAA,IAAAxE,EAAA;UADjBjC,EAAE,CAAA0G,cAAA,CAAAD,QAAA,EAC2lBE,OAAO,KAAYtG,UAAU;QAAA;QAAA,IAAA4B,EAAA;UAAA,IAAA2E,EAAA;UAD1nB5G,EAAE,CAAA6G,cAAA,CAAAD,EAAA,GAAF5G,EAAE,CAAA8G,WAAA,QAAA5E,GAAA,CAAAW,KAAA,GAAA+D,EAAA;QAAA;MAAA;MAAAG,QAAA;MAAAC,YAAA,WAAAC,iCAAAhF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjC,EAAE,CAAAkH,UAAA,gCAAAC,0DAAAC,MAAA;YAAA,OACJlF,GAAA,CAAAqC,YAAA,CAAA6C,MAAA,EAAqB,CAAC,CAAC;UAAA,CAAL,CAAC,+BAAAC,yDAAAD,MAAA;YAAA,OAAnBlF,GAAA,CAAAqC,YAAA,CAAA6C,MAAA,GAAsB,CAAC,CAAC;UAAA,CAAN,CAAC,+BAAAE,yDAAAF,MAAA;YAAA,OAAnBlF,GAAA,CAAA0C,UAAA,CAAAwC,MAAA,EAAmB,CAAC,CAAC;UAAA,CAAH,CAAC,6BAAAG,uDAAAH,MAAA;YAAA,OAAnBlF,GAAA,CAAA0C,UAAA,CAAAwC,MAAA,GAAoB,CAAC,CAAC;UAAA,CAAJ,CAAC;QAAA;QAAA,IAAAnF,EAAA;UADjBjC,EAAE,CAAAwH,WAAA,qBAAAtF,GAAA,CAAAuB,WAAA;QAAA;MAAA;MAAAgE,MAAA;QAAAjE,eAAA;QAAAC,WAAA;MAAA;MAAAiE,OAAA;QAAAhE,qBAAA;MAAA;MAAAiE,UAAA;MAAAC,QAAA,GAAF5H,EAAE,CAAA6H,kBAAA,CACif,CAACtG,qBAAqB,CAAC,GAD1gBvB,EAAE,CAAA8H,oBAAA,EAAF9H,EAAE,CAAA+H,mBAAA;MAAAC,kBAAA,EAAAjG,GAAA;MAAAkG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAApG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjC,EAAE,CAAAsI,eAAA;UAAFtI,EAAE,CAAAuI,UAAA,IAAAvG,2CAAA,yBACqtB,CAAC;UADxtBhC,EAAE,CAAAwI,MAAA;UAAFxI,EAAE,CAAAyI,YAAA,EACquB,CAAC;QAAA;QAAA,IAAAxG,EAAA;UADxuBjC,EAAE,CAAAyC,UAAA,SAAFzC,EAAE,CAAA0I,WAAA,OAAAxG,GAAA,CAAAoC,QAAA,CACgtB,CAAC;QAAA;MAAA;MAAAqE,YAAA,GAAoX7I,SAAS,EAA8CC,IAAI;MAAA6I,MAAA;MAAAC,eAAA;IAAA,EAAwH;EAAE;AACj2C;AACAvH,UAAU,CAAC,CACPO,OAAO,CACV,EAAEc,mBAAmB,CAACmG,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC;AACnD;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KANqG/I,EAAE,CAAAgJ,iBAAA,CAMXrG,mBAAmB,EAAc,CAAC;IAClH0D,IAAI,EAAE/F,SAAS;IACf2I,IAAI,EAAE,CAAC;MAAEtB,UAAU,EAAE,IAAI;MAAEuB,QAAQ,EAAE,8BAA8B;MAAEC,OAAO,EAAE,CAACrJ,SAAS,EAAEC,IAAI,CAAC;MAAE8I,eAAe,EAAEtI,uBAAuB,CAAC6I,MAAM;MAAEC,SAAS,EAAE,CAAC9H,qBAAqB,CAAC;MAAE+H,IAAI,EAAE;QAChL,yBAAyB,EAAE,aAAa;QACxC,sBAAsB,EAAE,yBAAyB;QACjD,qBAAqB,EAAE,0BAA0B;QACjD,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE;MACzB,CAAC;MAAElB,QAAQ,EAAE,+DAA+D;MAAEQ,MAAM,EAAE,CAAC,ySAAyS;IAAE,CAAC;EAC/Y,CAAC,CAAC,QAAkB;IAAE/F,KAAK,EAAE,CAAC;MACtBwD,IAAI,EAAE7F,eAAe;MACrByI,IAAI,EAAE,CAACxI,UAAU,CAAC,MAAMkG,OAAO,CAAC,EAAE;QAAE4C,IAAI,EAAElJ;MAAW,CAAC;IAC1D,CAAC,CAAC;IAAEmD,eAAe,EAAE,CAAC;MAClB6C,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAE+C,WAAW,EAAE,CAAC;MACd4C,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAEgD,qBAAqB,EAAE,CAAC;MACxB2C,IAAI,EAAE1F;IACV,CAAC,CAAC;IAAE2D,QAAQ,EAAE;EAAG,CAAC;AAAA;AAE9B,MAAMqC,OAAO,CAAC;EACV/D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4G,OAAO,GAAGvJ,MAAM,CAAC0C,mBAAmB,CAAC;IAC1C,IAAI,CAACI,EAAE,GAAGjC,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACqC,CAAC,GAAG,CAAClD,MAAM,CAACY,gBAAgB,EAAE;MAAE4I,QAAQ,EAAE;IAAK,CAAC,CAAC,EAAEC,cAAc,CAACC,YAAY,CAAC,CAAC,IAAIvI,KAAK,EACzFiC,IAAI,CAAChC,MAAM,CAACuI,OAAO,CAAC,EAAEhJ,kBAAkB,CAAC,CAAC,CAAC,CAC3C0C,SAAS,CAAC,MAAM,IAAI,CAACa,QAAQ,CAAC,CAAC,CAAC;IACrC,IAAI,CAAC0F,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,KAAK,GAAG7J,MAAM,CAACiB,gBAAgB,CAAC;IACrC,IAAI,CAAC6I,SAAS,GAAG,QAAQ;IACzB,IAAI,CAACrH,IAAI,GAAG,EAAE;EAClB;EACA,IAAIwB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACsF,OAAO,CAACtF,QAAQ,CAAC,IAAI,CAACJ,KAAK,CAAC;EAC5C;EACA,IAAIkG,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACR,OAAO,CAAC/F,WAAW,KAAK,UAAU;EAClD;EACA,IAAIwG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC/F,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;EACjC;EACA,IAAIJ,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC0F,OAAO,CAAC5F,OAAO,CAAC,IAAI,CAACb,EAAE,CAAC;EACxC;EACAoB,QAAQA,CAAA,EAAG;IACP,IAAI,CAACqF,OAAO,CAACrF,QAAQ,CAAC,IAAI,CAACL,KAAK,CAAC;EACrC;EACA;IAAS,IAAI,CAACkC,IAAI,YAAAkE,gBAAAhE,CAAA;MAAA,YAAAA,CAAA,IAAyFS,OAAO;IAAA,CAAmD;EAAE;EACvK;IAAS,IAAI,CAACR,IAAI,kBAtD+EnG,EAAE,CAAAoG,iBAAA;MAAAC,IAAA,EAsDJM,OAAO;MAAAL,SAAA;MAAAS,QAAA;MAAAC,YAAA,WAAAmD,qBAAAlI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtDLjC,EAAE,CAAAkH,UAAA,mBAAAkD,iCAAA;YAAA,OAsDJlI,GAAA,CAAAiC,QAAA,CAAS,CAAC;UAAA,CAAJ,CAAC;QAAA;QAAA,IAAAlC,EAAA;UAtDLjC,EAAE,CAAAqK,cAAA,aAAAnI,GAAA,CAAA+H,QAsDE,CAAC;UAtDLjK,EAAE,CAAAwH,WAAA,eAAAtF,GAAA,CAAA6H,SAAA;UAAF/J,EAAE,CAAAsK,WAAA,cAAApI,GAAA,CAAA8H,UAsDE,CAAC,mBAAA9H,GAAA,CAAA2H,YAAD,CAAC,YAAA3H,GAAA,CAAAgC,QAAD,CAAC;QAAA;MAAA;MAAAuD,MAAA;QAAAsC,SAAA;QAAArH,IAAA;MAAA;MAAAiF,UAAA;MAAAC,QAAA,GAtDL5H,EAAE,CAAA+H,mBAAA;MAAAwC,KAAA,EAAAnI,GAAA;MAAA4F,kBAAA,EAAAjG,GAAA;MAAAkG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAoC,iBAAAvI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjC,EAAE,CAAAsI,eAAA;UAAFtI,EAAE,CAAAuI,UAAA,IAAAlG,2BAAA,qBAsDogB,CAAC;UAtDvgBrC,EAAE,CAAAsC,SAAA,iBAsDslB,CAAC,iBAAgF,CAAC,YAA8C,CAAC;UAtDztBtC,EAAE,CAAAyI,YAAA,EAsDsuB,CAAC;QAAA;QAAA,IAAAxG,EAAA;UAtDzuBjC,EAAE,CAAAyC,UAAA,SAAAP,GAAA,CAAAQ,IAsDic,CAAC;UAtDpc1C,EAAE,CAAAyK,SAAA,CAsDklB,CAAC;UAtDrlBzK,EAAE,CAAAyC,UAAA,SAAAP,GAAA,CAAA4H,KAAA,CAAAY,KAsDklB,CAAC;UAtDrlB1K,EAAE,CAAAyK,SAAA,CAsDmqB,CAAC;UAtDtqBzK,EAAE,CAAAyC,UAAA,SAAAP,GAAA,CAAA4H,KAAA,CAAAa,KAsDmqB,CAAC;QAAA;MAAA;MAAAhC,YAAA,GAAg1E5I,IAAI,EAA6FiB,OAAO;MAAA4H,MAAA;MAAAC,eAAA;IAAA,EAAgH;EAAE;AACrzG;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KAxDqG/I,EAAE,CAAAgJ,iBAAA,CAwDXrC,OAAO,EAAc,CAAC;IACtGN,IAAI,EAAE/F,SAAS;IACf2I,IAAI,EAAE,CAAC;MAAEtB,UAAU,EAAE,IAAI;MAAEuB,QAAQ,EAAE,yFAAyF;MAAEC,OAAO,EAAE,CAACpJ,IAAI,EAAEiB,OAAO,CAAC;MAAE6H,eAAe,EAAEtI,uBAAuB,CAAC6I,MAAM;MAAEE,IAAI,EAAE;QACrM,mBAAmB,EAAE,WAAW;QAChC,mBAAmB,EAAE,YAAY;QACjC,wBAAwB,EAAE,cAAc;QACxC,iBAAiB,EAAE,UAAU;QAC7B,YAAY,EAAE,UAAU;QACxB,SAAS,EAAE;MACf,CAAC;MAAElB,QAAQ,EAAE,oUAAoU;MAAEQ,MAAM,EAAE,CAAC,ktEAAktE;IAAE,CAAC;EAC7jF,CAAC,CAAC,QAAkB;IAAEmB,SAAS,EAAE,CAAC;MAC1B1D,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAEgC,IAAI,EAAE,CAAC;MACP2D,IAAI,EAAE3F;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkK,UAAU,GAAG,CAACjI,mBAAmB,EAAEgE,OAAO,CAAC;;AAEjD;AACA;AACA;;AAEA,SAASA,OAAO,EAAEiE,UAAU,EAAEjI,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}