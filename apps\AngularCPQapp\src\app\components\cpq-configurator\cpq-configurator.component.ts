/**
 * @fileoverview Composant principal pour la configuration CPQ
 * @description Interface utilisateur pour sélectionner catégories, familles et produits
 */

import { Component, OnInit, OnDestroy } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Inject } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@taiga-ui/cdk';
import { TUI_TREE_CONTENT } from '@taiga-ui/kit';
import { PolymorpheusComponent } from '@tinkoff/ng-polymorpheus';
import { TreeContentComponent } from './tree-content.component';
import { TuiHandler } from '@taiga-ui/cdk';
import { TUI_TREE_CONTENT } from '@taiga-ui/kit';
import { PolymorpheusComponent } from '@taiga-ui/polymorpheus';
import { TreeContentComponent } from './tree-content.component';

import { Product } from '../../models/product/product';
// Import des constantes
import { LUXURY_CARS_CATEGORY, CAR_FAMILIES, LUXURY_CARS_PRODUCTS } from '../../constants/cpq.constants';

// import { DynamicsService } from '../../services/dynamics.service';
import { finalize } from 'rxjs/operators';

import { ProductProperty, PropertySelection, PropertyUtils } from '../../models/property/property';
import { PRODUCT_PROPERTIES, PRODUCT_PROPERTY_ASSOCIATIONS } from '../../constants/property.constants';

// Interface pour les noeuds de l'arbre Taiga UI
interface TreeNode {
  text: string;
  productId: string;
  price: number;
  selected: boolean;
  children?: TreeNode[];
}

// ===== INTERFACES =====
interface ProductSelection {
  product: Product;
  quantity: number;
  selectionSource: 'MAIN_LIST' | 'TREE_INCLUSION';
}

interface FinalConfiguration {
  selectedCategory: any;
  selectedFamilies: any[];
  selectedProducts: any[];
  selectedProperties: PropertySelection[];
  priceBreakdown: {
    mainProducts: number;
    properties: number;
    total: number;
  };
  totalPrice: number;
  timestamp: string;
}

// ===== INTERFACES POUR FILTRES ET RECHERCHE =====
interface ProductFilters {
  searchTerm: string;
  familyFilter: string;
  typeFilter: 'all' | 'main' | 'option';
}

interface PaginationConfig {
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
  availablePageSizes: number[];
}

@Component({
  selector: 'app-cpq-configurator',
  standalone: false,
  templateUrl: './cpq-configurator.component.html',
  styleUrls: ['./cpq-configurator.component.css'],
  providers: [
    {
      provide: TUI_TREE_CONTENT,
      useValue: new PolymorpheusComponent(TreeContentComponent),
    },
  ]
})
export class CpqConfiguratorComponent implements OnInit, OnDestroy {

  // ===== PROPRIÉTÉS PRINCIPALES =====
  allProducts: Product[] = LUXURY_CARS_PRODUCTS;
  availableProducts: Product[] = [];
  filteredProducts: Product[] = [];
  
  // Indicateur de chargement
  isLoading: boolean = false;
  
  // Cache pour les descendants des produits
  private descendantsCache = new Map<string, string[]>();
  
  // Map des enfants directs par ID de parent pour améliorer les performances
  private directChildrenMap = new Map<string, Product[]>();
  
  // Données fixes
  readonly FIXED_CATEGORY = LUXURY_CARS_CATEGORY;
  readonly CAR_FAMILIES = CAR_FAMILIES;
  
  // Données dynamiques depuis D365
  opportunityCategory: number = 0;
  carFamilies: any[] = CAR_FAMILIES;
  
  // État de sélection
  // selectedCategory = this.FIXED_CATEGORY; // Commenté mais conservé pour référence
  selectedCategory: any = {
    id: '0',
    name: 'Chargement...',
    description: 'Chargement de la catégorie...'
  };
  selectedFamilyIds: string[] = [];
  tempSelectedProducts: ProductSelection[] = [];
  confirmedSelectedProducts: ProductSelection[] = [];
  
  // Propriétés pour l'arborescence
  productTreeNodes: TreeNode[] = [];
  selectedTreeNodes: TreeNode[] = [];
  showAssociatedProductsDialog: boolean = false;
  currentRootProduct: Product | null = null;
  
  // Gestion des conflits et modals
  conflictProduct: Product | null = null;
  selectedChildrenProducts: ProductSelection[] = [];
  selectedProductForProperties: Product | null = null;
  private modalRef: any;
  
  // État des panneaux réductibles
  isPricePanelExpanded: boolean = true;
  isProductsPanelExpanded: boolean = true;
  expandedProductDetails = new Set<string>();

  // ===== NOUVELLES PROPRIÉTÉS POUR LES PROPRIÉTÉS =====
  allProperties: ProductProperty[] = PRODUCT_PROPERTIES;
  selectedProperties: PropertySelection[] = [];
  expandedPropertySections = new Map<string, boolean>();
  propertiesPrice: number = 0;

  
  // État de l'interface
  public currentStep: number = 1;
  public maxSteps: number = 3;
  
  // Stocke l'état d'expansion des lignes du tableau
  expandedTableRows = new Set<string>();
  
  // Messages
  errorMessage: string = '';
  successMessage: string = '';
  
  // Hiérarchie et expansion
  expandedProducts = new Map<string, boolean>();
  totalPrice: number = 0;
  
  // Configuration finale
  finalConfiguration: FinalConfiguration | null = null;

  // ===== NOUVELLES PROPRIÉTÉS POUR FILTRES ET RECHERCHE =====
  productFilters: ProductFilters = {
    searchTerm: '',
    familyFilter: 'all',
    typeFilter: 'all'
  };

  pagination: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 25,
    totalItems: 0,
    availablePageSizes: [25, 50, 75, 0] // 0 = tous les éléments
  };

  // Variables pour l'arbre Taiga UI
  treeData: TreeNode[] = [];
  treeHandler: TuiHandler<TreeNode, readonly TreeNode[]> = item => item.children || [];

  // ===== CONSTRUCTEUR =====
  constructor() {}

  /**
   * Obtient le pourcentage de progression
   */
  getProgressPercentage(): number {
    return Math.round((this.currentStep / this.maxSteps) * 100);
  }

  /**
   * Bascule la sélection d'une famille
   */
  toggleFamilySelection(familyId: string): void {
    const index = this.selectedFamilyIds.indexOf(familyId);
    
    if (index > -1) {
      this.selectedFamilyIds.splice(index, 1);
    } else {
      this.selectedFamilyIds.push(familyId);
    }
    
    this.loadAvailableProducts();
  }

  /**
   * Vérifie si une famille est sélectionnée
   */
  isFamilySelected(familyId: string): boolean {
    return this.selectedFamilyIds.includes(familyId);
  }

  /**
   * Obtient le nom d'une famille par son ID
   */
  getFamilyName(familyId: string): string {
    const family = this.carFamilies.find(f => f.id === familyId);
    return family ? family.name : familyId;
  }
  
  /**
   * Charge les produits disponibles selon les familles sélectionnées
   */
  loadAvailableProducts(): void {
    // Utiliser directement tous les produits de LUXURY_CARS_PRODUCTS
    console.log('Affichage de tous les produits LUXURY_CARS_PRODUCTS');
    
    // Utiliser directement tous les produits sans filtrage
    this.availableProducts = LUXURY_CARS_PRODUCTS;
    
    console.log(`${this.availableProducts.length} produits disponibles`);
    this.applyFiltersAndPagination();
    this.isLoading = false;
  }

  // ===== LIFECYCLE HOOKS =====
  ngOnInit(): void {
    console.log('🚗 Initialisation du configurateur Voitures de Luxe');
    this.loadOpportunityCategory();
  }
  
  /**
   * Ouvre le dialogue des produits associés pour un produit racine
   */
  showAssociatedProducts(product: Product): void {
    this.currentRootProduct = product;
    
    // Construire l'arbre pour Taiga UI
    this.buildTaigaTree(product);
    
    // Utiliser Bootstrap pour afficher le modal
    const modalElement = document.getElementById('associatedProductsModal');
    if (modalElement) {
      // @ts-ignore: Ignorer l'erreur TS car nous savons que bootstrap est disponible
      const modal = new bootstrap.Modal(modalElement);
      modal.show();
      this.modalRef = modal;
    }
  }
  
  /**
   * Construit l'arbre pour Taiga UI
   */
  buildTaigaTree(rootProduct: Product): void {
    // Créer le noeud racine
    const rootNode: TreeNode = {
      text: rootProduct.name,
      productId: rootProduct.productid,
      price: rootProduct.price || 0,
      selected: this.isProductConfirmed(rootProduct),
      children: this.buildTaigaTreeChildren(rootProduct.productid)
    };
    
    this.treeData = [rootNode];
  }
  
  /**
   * Construit récursivement les noeuds enfants pour Taiga UI
   */
  buildTaigaTreeChildren(parentId: string): TreeNode[] {
    const children = this.getDirectChildren(parentId);
    return children.map(child => ({
      text: child.name,
      productId: child.productid,
      price: child.price || 0,
      selected: this.isProductConfirmed(child),
      children: this.hasDirectChildren(child.productid) ? this.buildTaigaTreeChildren(child.productid) : undefined
    }));
  }
  
  /**
   * Gère la sélection/désélection d'un noeud dans l'arbre
   */
  handleTreeNodeToggle(node: TreeNode, selected: boolean): void {
    // Trouver le produit correspondant
    const product = this.allProducts.find(p => p.productid === node.productId);
    if (product) {
      if (selected) {
        // Ajouter au panier s'il n'y est pas déjà
        if (!this.isProductConfirmed(product)) {
          this.toggleProductInclusion(product);
        }
      } else {
        // Retirer du panier s'il y est
        if (this.isProductConfirmed(product)) {
          this.toggleProductInclusion(product);
        }
      }
    }
  }
  
  /**
   * Construit l'arborescence des produits associés
   */
  buildProductTree(rootProduct: Product): void {
    // Réinitialiser l'arborescence
    this.productTreeNodes = [];
    this.selectedTreeNodes = [];
    
    // Créer le noeud racine
    const rootNode: TreeNode = {
      key: rootProduct.productid,
      label: rootProduct.name,
      data: rootProduct,
      icon: 'pi pi-folder',
      expanded: true,
      children: []
    };
    
    // Ajouter les enfants directs
    const directChildren = this.getDirectChildren(rootProduct.productid);
    if (directChildren.length > 0) {
      rootNode.children = this.buildChildNodes(directChildren, 1);
    }
    
    this.productTreeNodes = [rootNode];
  }
  
  /**
   * Construit récursivement les noeuds enfants
   */
  private buildChildNodes(products: Product[], level: number): TreeNode[] {
    return products.map(product => {
      const node: TreeNode = {
        key: product.productid,
        label: product.name,
        data: product,
        icon: level === 1 ? 'pi pi-file' : 'pi pi-cog',
        expanded: level < 2,
        children: []
      };
      
      // Ajouter les enfants de ce produit
      const children = this.getDirectChildren(product.productid);
      if (children.length > 0) {
        node.children = this.buildChildNodes(children, level + 1);
      }
      
      return node;
    });
  }
  
  /**
   * Confirme les produits associés sélectionnés dans l'arborescence
   */
  confirmAssociatedProducts(): void {
    // Fermer le modal
    if (this.modalRef) {
      this.modalRef.hide();
    }
    
    // Mettre à jour le prix total
    this.updateTotalPrice();
  }

  /**
   * Charge la catégorie de l'opportunité courante
   */
  loadOpportunityCategory(): void {
    console.log('🚗 Initialisation du configurateur Voitures de Luxe');
    
    // Utiliser directement les constantes
    this.opportunityCategory = parseInt(this.FIXED_CATEGORY.id);
    this.selectedCategory = this.FIXED_CATEGORY;
    
    console.log('Utilisation de la catégorie fixe:', this.selectedCategory);
    
    // Pas besoin de charger les familles car on utilise CAR_FAMILIES directement
    this.buildProductRelationships();
    
    this.isLoading = false;
  }

  /**
   * Précalcule les relations parent-enfant entre les produits
   */
  private buildProductRelationships(): void {
    // Parcourir tous les produits une seule fois
    this.allProducts.forEach(product => {
      if (product.parentproductid) {
        // Ajouter ce produit comme enfant de son parent
        const parentId = product.parentproductid;
        if (!this.directChildrenMap.has(parentId)) {
          this.directChildrenMap.set(parentId, []);
        }
        this.directChildrenMap.get(parentId)!.push(product);
      }
    });
  }

  ngOnDestroy(): void {
    this.clearMessages();
    // Libérer les ressources
    this.descendantsCache.clear();
    this.directChildrenMap.clear();
    this.genealogyInfoCache.clear();
    this.genealogyPriceCache.clear();
    this.propertiesPriceCache.clear();
    this.pageNumbersCache = { totalPages: 0, currentPage: 0, pages: [] };
  }

  // Méthode pour effacer les messages
  private clearMessages(): void {
    this.errorMessage = '';
    this.successMessage = '';
  }

  // Méthode pour obtenir les enfants directs
  getDirectChildren(productId: string): Product[] {
    return this.directChildrenMap.get(productId) || [];
  }

  // Méthode pour vérifier si un produit a des enfants directs
  hasDirectChildren(productId: string): boolean {
    return this.directChildrenMap.has(productId) && this.directChildrenMap.get(productId)!.length > 0;
  }

  // Méthode pour initialiser les propriétés d'un produit
  initializePropertiesForProduct(product: Product): void {
    const productProperties = this.getAvailablePropertiesForProduct(product.productid);
    
    productProperties.forEach(property => {
      const propertySelection: PropertySelection = {
        productid: product.productid,
        propertyid: property.propertyid,
        selected: this.isDefaultProperty(product.productid, property.propertyid) || property.isrequired,
        selectiondate: new Date(),
        product: product,
        property: property
      };
      this.selectedProperties.push(propertySelection);
    });
  }

  // Méthode pour mettre à jour le prix total
  updateTotalPrice(): void {
    // Vider les caches de prix car les sélections ont changé
    this.propertiesPriceCache.clear();
    this.genealogyPriceCache.clear();
    
    // Calculer le prix des produits avec leurs propriétés
    let productsPrice = 0;
    
    this.confirmedSelectedProducts.forEach(selection => {
      // Prix de base du produit
      const basePrice = (selection.product.price || 0) * selection.quantity;
      
      // Prix des propriétés pour ce produit
      const propertiesPrice = this.getPropertiesPriceForProduct(selection.product.productid) * selection.quantity;
      
      productsPrice += basePrice + propertiesPrice;
    });
    
    this.propertiesPrice = PropertyUtils.calculatePropertiesTotal(
      this.selectedProperties,
      this.allProperties
    );
    
    this.totalPrice = productsPrice;
  }

  // Cache pour les informations de généalogie
  private genealogyInfoCache = new Map<string, { count: number; products: Product[] }>();
  
  // Cache pour les prix totaux de généalogie
  private genealogyPriceCache = new Map<string, number>();

  // Cache pour les numéros de page
  private pageNumbersCache: { totalPages: number; currentPage: number; pages: number[] } = {
    totalPages: 0,
    currentPage: 0,
    pages: []
  };

  // Cache pour les prix des propriétés par produit
  private propertiesPriceCache = new Map<string, number>();
  
  // Cache pour le formatteur de prix
  private priceFormatter = new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  });
  
  /**
   * Formate un prix en devise
   */
  formatPrice(price: number): string {
    return this.priceFormatter.format(price);
  }

  /**
   * Obtient les propriétés disponibles pour un produit
   */
  getAvailablePropertiesForProduct(productId: string): ProductProperty[] {
    const associations = PRODUCT_PROPERTY_ASSOCIATIONS.filter(a => a.productid === productId);
    return associations.map(association => 
      this.allProperties.find(p => p.propertyid === association.propertyid)
    ).filter(p => p !== undefined) as ProductProperty[];
  }

  /**
   * Vérifie si une propriété est par défaut pour un produit
   */
  isDefaultProperty(productId: string, propertyId: string): boolean {
    const association = PRODUCT_PROPERTY_ASSOCIATIONS.find(
      a => a.productid === productId && a.propertyid === propertyId
    );
    return association?.isdefault || false;
  }

  /**
   * Obtient le prix total des propriétés pour un produit
   */
  getPropertiesPriceForProduct(productId: string): number {
    // Vérifier si le résultat est déjà en cache
    if (this.propertiesPriceCache.has(productId)) {
      return this.propertiesPriceCache.get(productId)!;
    }
    
    const totalPrice = this.selectedProperties
      .filter(ps => ps.productid === productId && ps.selected)
      .reduce((total, ps) => total + (ps.property?.price || 0), 0);
    
    // Stocker le résultat dans le cache
    this.propertiesPriceCache.set(productId, totalPrice);
    
    return totalPrice;
  }

  /**
   * Obtient tous les descendants d'un produit
   */
  getAllDescendants(productId: string): string[] {
    // Vérifier si le résultat est déjà en cache
    if (this.descendantsCache.has(productId)) {
      return this.descendantsCache.get(productId)!;
    }
    
    const descendants: string[] = [];
    const directChildren = this.allProducts.filter(p => p.parentproductid === productId);
    
    directChildren.forEach(child => {
      descendants.push(child.productid);
      // Récursion pour obtenir les descendants des enfants
      const childDescendants = this.getAllDescendants(child.productid);
      descendants.push(...childDescendants);
    });
    
    // Stocker le résultat dans le cache
    this.descendantsCache.set(productId, descendants);
    
    return descendants;
  }

  // Méthodes pour la pagination
  getTotalPages(): number {
    if (this.pagination.itemsPerPage === 0) return 1;
    return Math.ceil(this.pagination.totalItems / this.pagination.itemsPerPage);
  }

  onPageChange(page: number): void {
    this.pagination.currentPage = page;
    this.applyFiltersAndPagination();
  }

  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const current = this.pagination.currentPage;
    
    // Vérifier si le résultat est déjà en cache
    if (this.pageNumbersCache.totalPages === totalPages && 
        this.pageNumbersCache.currentPage === current) {
      return this.pageNumbersCache.pages;
    }
    
    const pages: number[] = [];

    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (current <= 4) {
        for (let i = 1; i <= 5; i++) pages.push(i);
        pages.push(-1); // Ellipsis
        pages.push(totalPages);
      } else if (current >= totalPages - 3) {
        pages.push(1);
        pages.push(-1); // Ellipsis
        for (let i = totalPages - 4; i <= totalPages; i++) pages.push(i);
      } else {
        pages.push(1);
        pages.push(-1); // Ellipsis
        for (let i = current - 1; i <= current + 1; i++) pages.push(i);
        pages.push(-1); // Ellipsis
        pages.push(totalPages);
      }
    }
    
    // Mettre à jour le cache
    this.pageNumbersCache = {
      totalPages,
      currentPage: current,
      pages: [...pages]
    };

    return pages;
  }

  // Méthodes pour les filtres
  getAvailableFamiliesForFilter(): any[] {
    const familyIds = new Set(this.availableProducts.map(p => p.familyId).filter(id => id));
    return this.carFamilies.filter(f => familyIds.has(f.id));
  }
  
  /**
   * Efface tous les filtres
   */
  clearAllFilters(): void {
    this.productFilters = {
      searchTerm: '',
      familyFilter: 'all',
      typeFilter: 'all'
    };
    this.pagination.currentPage = 1;
    this.applyFiltersAndPagination();
  }
  
  /**
   * Gère les changements de filtres
   */
  onFilterChange(): void {
    this.pagination.currentPage = 1;
    this.applyFiltersAndPagination();
  }

  applyFiltersAndPagination(): void {
    // Utiliser une seule itération pour appliquer tous les filtres
    const searchTerm = this.productFilters.searchTerm.trim().toLowerCase();
    const familyFilter = this.productFilters.familyFilter;
    const typeFilter = this.productFilters.typeFilter;
    const hasSearchTerm = searchTerm.length > 0;
    
    // Créer un ensemble des IDs de produits à masquer (généalogie)
    const productsToHide = new Set<string>();
    if (this.tempSelectedProducts.length > 0 || this.confirmedSelectedProducts.length > 0) {
      const allSelectedProducts = [...this.tempSelectedProducts, ...this.confirmedSelectedProducts];
      allSelectedProducts.forEach(selection => {
        const descendants = this.getAllDescendants(selection.product.productid);
        descendants.forEach(id => productsToHide.add(id));
      });
    }
    
    // Appliquer tous les filtres en une seule passe
    const filtered = this.availableProducts.filter(product => {
      // Filtre par terme de recherche
      if (hasSearchTerm && 
          !product.name.toLowerCase().includes(searchTerm) && 
          !(product.description?.toLowerCase().includes(searchTerm)) && 
          !product.productid.toLowerCase().includes(searchTerm)) {
        return false;
      }
      
      // Filtre par famille
      if (familyFilter !== 'all' && product.familyId !== familyFilter) {
        return false;
      }
      
      // Filtre par type
      if (typeFilter === 'main' && product.parentproductid) {
        return false;
      }
      if (typeFilter === 'option' && !product.parentproductid) {
        return false;
      }
      
      // Masquer les produits de la généalogie
      if (productsToHide.has(product.productid)) {
        return false;
      }
      
      return true;
    });

    // Mettre à jour le total
    this.pagination.totalItems = filtered.length;

    // Appliquer la pagination
    if (this.pagination.itemsPerPage > 0) {
      const startIndex = (this.pagination.currentPage - 1) * this.pagination.itemsPerPage;
      const endIndex = startIndex + this.pagination.itemsPerPage;
      this.filteredProducts = filtered.slice(startIndex, endIndex);
    } else {
      this.filteredProducts = filtered;
    }
  }

  // Méthodes pour la généalogie
  getGenealogyCount(productId: string): number {
    // Utiliser directement le cache des descendants s'il existe
    if (this.descendantsCache.has(productId)) {
      return this.descendantsCache.get(productId)!.length;
    }
    return this.getAllDescendants(productId).length;
  }

  getGenealogyInfo(productId: string): { count: number; products: Product[] } {
    // Vérifier si le résultat est déjà en cache
    if (this.genealogyInfoCache.has(productId)) {
      return this.genealogyInfoCache.get(productId)!;
    }
    
    const descendantIds = this.getAllDescendants(productId);
    const products = descendantIds.map(id => 
      this.allProducts.find(p => p.productid === id)
    ).filter(p => p !== undefined) as Product[];
    
    const result = {
      count: descendantIds.length,
      products: products
    };
    
    // Stocker le résultat dans le cache
    this.genealogyInfoCache.set(productId, result);
    
    return result;
  }

  getGenealogyTotalPrice(productId: string): number {
    // Vérifier si le résultat est déjà en cache
    if (this.genealogyPriceCache.has(productId)) {
      return this.genealogyPriceCache.get(productId)!;
    }
    
    const genealogyInfo = this.getGenealogyInfo(productId);
    const totalPrice = genealogyInfo.products.reduce((sum, p) => sum + (p.price || 0), 0);
    
    // Stocker le résultat dans le cache
    this.genealogyPriceCache.set(productId, totalPrice);
    
    return totalPrice;
  }

  // Méthodes pour la sélection temporaire
  clearTempSelection(): void {
    this.tempSelectedProducts = [];
    // Réappliquer les filtres pour réafficher les produits masqués
    this.applyFiltersAndPagination();
  }

  removeTempProduct(product: any): void {
    const index = this.tempSelectedProducts.findIndex(
      selection => selection.product.productid === product.productid
    );
    if (index >= 0) {
      this.tempSelectedProducts.splice(index, 1);
      // Réappliquer les filtres pour réafficher les produits de la généalogie
      this.applyFiltersAndPagination();
    }
  }

  getTempSelectionTotal(): number {
    return this.tempSelectedProducts.reduce((total, selection) => {
      return total + ((selection.product.price || 0) * selection.quantity);
    }, 0);
  }

  confirmTempSelection(): void {
    this.confirmedSelectedProducts = this.tempSelectedProducts.map(temp => ({
      ...temp,
      selectionSource: temp.selectionSource || 'MAIN_LIST'
    }));
    this.tempSelectedProducts = [];
    
    // Initialiser les propriétés pour les nouveaux produits confirmés
    this.initializePropertiesSelection();
    
    this.updateTotalPrice();
    this.applyFiltersAndPagination();
  }

  // Méthodes pour les produits confirmés
  getMainProducts(): ProductSelection[] {
    return this.confirmedSelectedProducts.filter(s => s.selectionSource === 'MAIN_LIST');
  }

  getMainProductsTotal(): number {
    return this.getMainProducts().reduce((total, selection) => {
      return total + ((selection.product.price || 0) * selection.quantity);
    }, 0);
  }

  getOptionsTotal(): number {
    return this.confirmedSelectedProducts
      .filter(s => s.selectionSource === 'TREE_INCLUSION')
      .reduce((total, selection) => {
        return total + ((selection.product.price || 0) * selection.quantity);
      }, 0);
  }

  getTotalConfirmedQuantity(): number {
    return this.confirmedSelectedProducts.reduce((total, selection) => {
      return total + selection.quantity;
    }, 0);
  }

  clearAllConfirmedProducts(): void {
    this.confirmedSelectedProducts = [];
    this.updateTotalPrice();
    // Réappliquer les filtres pour réafficher tous les produits
    this.applyFiltersAndPagination();
  }

  removeConfirmedProduct(product: any): void {
    // Supprimer le produit principal
    const mainIndex = this.confirmedSelectedProducts.findIndex(
      selection => selection.product.productid === product.productid && 
      selection.selectionSource === 'MAIN_LIST'
    );
    
    if (mainIndex >= 0) {
      this.confirmedSelectedProducts.splice(mainIndex, 1);
    }

    // Supprimer aussi tous ses enfants inclus
    this.confirmedSelectedProducts = this.confirmedSelectedProducts.filter(
      selection => {
        if (selection.selectionSource === 'TREE_INCLUSION') {
          // Vérifier si c'est un enfant du produit supprimé
          return !this.isChildOf(selection.product, product.productid);
        }
        return true;
      }
    );

    this.updateTotalPrice();
  }

  // Méthode pour vérifier si un produit est enfant d'un autre
  private isChildOf(product: Product, parentId: string): boolean {
    if (product.parentproductid === parentId) {
      return true;
    }
    
    // Vérification récursive
    const parent = this.allProducts.find(p => p.productid === product.parentproductid);
    if (parent) {
      return this.isChildOf(parent, parentId);
    }
    
    return false;
  }
  
  /**
   * Vérifie si un produit est temporairement sélectionné
   */
  isTempProductSelected(product: any): boolean {
    return this.tempSelectedProducts.some(
      selection => selection.product.productid === product.productid
    );
  }
  
  /**
   * Sélectionne directement un produit (sans passer par la sélection temporaire)
   */
  toggleTempProductSelection(product: any): void {
    // Vérifier si le produit est déjà sélectionné
    const existingIndex = this.confirmedSelectedProducts.findIndex(
      selection => selection.product.productid === product.productid
    );

    if (existingIndex >= 0) {
      // Désélectionner le produit
      this.confirmedSelectedProducts.splice(existingIndex, 1);
    } else {
      // Ajouter directement le produit au panier
      this.confirmedSelectedProducts.push({
        product: product,
        quantity: 1,
        selectionSource: 'MAIN_LIST'
      });
      
      // Initialiser les propriétés pour ce produit
      this.initializePropertiesForProduct(product);
    }
    
    // Mettre à jour le prix total
    this.updateTotalPrice();
    
    // Mettre à jour l'affichage
    this.applyFiltersAndPagination();
  }

  // Méthodes pour la gestion des quantités
  updateProductQuantity(productId: string, event: any): void {
    let newQuantity = parseInt(event.target.value);
    
    if (isNaN(newQuantity) || newQuantity < 1) newQuantity = 1;
    if (newQuantity > 99) newQuantity = 99;
    
    const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);
    if (selection) {
      selection.quantity = newQuantity;
      this.updateTotalPrice();
    }
  }

  increaseQuantity(productId: string): void {
    const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);
    if (selection && selection.quantity < 99) {
      selection.quantity++;
      this.updateTotalPrice();
    }
  }

  decreaseQuantity(productId: string): void {
    const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);
    if (selection && selection.quantity > 1) {
      selection.quantity--;
      this.updateTotalPrice();
    }
  }

  // Méthodes pour l'expansion des produits
  toggleProductExpansion(productId: string): void {
    const isExpanded = this.expandedProducts.get(productId) || false;
    this.expandedProducts.set(productId, !isExpanded);
  }

  isProductExpanded(productId: string): boolean {
    return this.expandedProducts.get(productId) || false;
  }

  toggleAllProductsExpansion(): void {
    if (this.expandedProducts.size > 0) {
      // Si des produits sont étendus, tout réduire
      this.expandedProducts.clear();
    } else {
      // Si aucun produit n'est étendu, tout étendre
      this.confirmedSelectedProducts.forEach(selection => {
        if (this.hasDirectChildren(selection.product.productid)) {
          this.expandedProducts.set(selection.product.productid, true);
        }
      });
    }
  }

  // Méthodes pour les propriétés
  hasProductProperties(productId: string): boolean {
    return this.getSelectedPropertiesCountForProduct(productId) > 0;
  }

  getSelectedPropertiesCountForProduct(productId: string): number {
    return this.selectedProperties.filter(
      ps => ps.productid === productId && ps.selected
    ).length;
  }

  getProductFreeProperties(productId: string): any[] {
    return this.selectedProperties
      .filter(ps => ps.productid === productId && ps.selected && ps.property?.price === 0 && !ps.property?.isrequired)
      .map(ps => ({
        name: ps.property?.name,
        isRequired: ps.property?.isrequired
      }));
  }

  getProductPaidProperties(productId: string): any[] {
    return this.selectedProperties
      .filter(ps => ps.productid === productId && ps.selected && (ps.property?.price || 0) > 0 && !ps.property?.isrequired)
      .map(ps => ({
        name: ps.property?.name,
        price: ps.property?.price,
        isRequired: ps.property?.isrequired
      }));
  }

  getProductRequiredProperties(productId: string): any[] {
    return this.selectedProperties
      .filter(ps => ps.productid === productId && ps.property?.isrequired)
      .map(ps => ({
        name: ps.property?.name,
        price: ps.property?.price,
        isRequired: true
      }));
  }

  // Méthodes pour l'arborescence des produits
  getProductTreeData(parentProductId: string): any[] {
    const children = this.getDirectChildren(parentProductId);
    return children.map(child => {
      return {
        product: child,
        isSelected: this.isProductConfirmed(child),
        isExpanded: this.isProductExpanded(child.productid),
        quantity: this.getProductQuantity(child.productid),
        directChildren: this.getProductTreeData(child.productid),
        hasChildren: this.hasDirectChildren(child.productid),
        hasSelectedProperties: this.hasProductProperties(child.productid),
        selectedPropertiesCount: this.getSelectedPropertiesCountForProduct(child.productid),
        freeProperties: this.getProductFreeProperties(child.productid),
        paidProperties: this.getProductPaidProperties(child.productid),
        requiredProperties: this.getProductRequiredProperties(child.productid)
      };
    });
  }

  // Méthodes pour la gestion des propriétés
  initializePropertiesSelection(): void {
    this.selectedProperties = [];
    
    // Pour chaque produit confirmé, initialiser ses propriétés
    this.confirmedSelectedProducts.forEach(selection => {
      this.initializePropertiesForProduct(selection.product);
    });
    
    this.updateTotalPrice();
  }

  togglePropertySelection(productId: string, propertyId: string): void {
    const propertySelection = this.selectedProperties.find(
      ps => ps.productid === productId && ps.propertyid === propertyId
    );
    
    if (propertySelection) {
      const property = propertySelection.property!;
      
      // Ne pas permettre de désélectionner les propriétés requises
      if (property.isrequired && propertySelection.selected) {
        return;
      }
      
      // Si la propriété est exclusive, désélectionner les autres du même type
      if (property.isexclusive) {
        this.selectedProperties
          .filter(ps => 
            ps.productid === productId && 
            ps.property!.propertytype === property.propertytype &&
            ps.propertyid !== propertyId
          )
          .forEach(ps => ps.selected = false);
      }
      
      propertySelection.selected = !propertySelection.selected;
      this.updateTotalPrice();
    }
  }

  getGroupedPropertiesForProduct(productId: string): {
    free: PropertySelection[];
    paid: PropertySelection[];
  } {
    const properties = this.getPropertiesForProduct(productId);
    return {
      free: properties.filter(ps => PropertyUtils.isFreeProperty(ps.property!)),
      paid: properties.filter(ps => PropertyUtils.isPaidProperty(ps.property!))
    };
  }

  getPropertiesForProduct(productId: string): PropertySelection[] {
    return this.selectedProperties.filter(ps => ps.productid === productId);
  }

  getRequiredPropertiesForProduct(productId: string): PropertySelection[] {
    return this.selectedProperties
      .filter(ps => ps.productid === productId && ps.property?.isrequired);
  }

  // Méthodes pour la gestion des inclusions
  toggleProductInclusion(product: any): void {
    const existingIndex = this.confirmedSelectedProducts.findIndex(
      selection => selection.product.productid === product.productid
    );

    if (existingIndex >= 0) {
      // Retirer le produit de la sélection
      this.confirmedSelectedProducts.splice(existingIndex, 1);
    } else {
      // Ajouter le produit au panier
      const newSelection: ProductSelection = {
        product: product,
        quantity: 1,
        selectionSource: 'MAIN_LIST' // Tous les produits sont maintenant au même niveau
      };
      this.confirmedSelectedProducts.push(newSelection);
      
      // Initialiser les propriétés pour ce produit
      this.initializePropertiesForProduct(product);
    }

    this.updateTotalPrice();
  }

  isProductConfirmed(product: any): boolean {
    return this.confirmedSelectedProducts.some(
      selection => selection.product.productid === product.productid
    );
  }

  getProductQuantity(productId: string): number {
    const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);
    return selection ? selection.quantity : 1;
  }

  // Méthodes pour le tableau hiérarchique
  getHierarchicalProductsForTable(): any[] {
    const result: any[] = [];
    const processedIds = new Set<string>();
    
    // Séparer les produits sélectionnés directement et les produits inclus
    const mainProducts = this.confirmedSelectedProducts.filter(s => s.selectionSource === 'MAIN_LIST');
    
    // Ajouter tous les produits principaux (niveau 0) et leurs enfants visibles
    mainProducts
      .sort((a, b) => a.product.name.localeCompare(b.product.name))
      .forEach(selection => {
        if (!processedIds.has(selection.product.productid)) {
          result.push({
            selection,
            level: 0,
            hasChildren: this.hasDirectChildren(selection.product.productid),
            expanded: this.isTableRowExpanded(selection.product.productid),
            childrenPrice: 0
          });
          processedIds.add(selection.product.productid);
        }
      });
    
    return result;
  }

  toggleTableRowExpansion(productId: string): void {
    if (this.expandedTableRows.has(productId)) {
      this.expandedTableRows.delete(productId);
    } else {
      this.expandedTableRows.add(productId);
    }
  }

  isTableRowExpanded(productId: string): boolean {
    return this.expandedTableRows.has(productId);
  }

  // Méthodes pour les modals
  openPropertiesModal(product: any): void {
    this.selectedProductForProperties = product;
  }

  showResetConfirmation(): void {
    // Afficher le modal de confirmation
  }

  exportPriceSummaryToPdf(): void {
    // Exporter le récapitulatif en PDF
  }

  confirmParentChildConflict(): void {
    // Confirmer le conflit parent-enfant
  }

  // Méthodes pour la navigation entre étapes
  nextStep(): void {
    switch (this.currentStep) {
      case 1:
        if (this.selectedFamilyIds.length === 0) {
          this.setErrorMessage('Veuillez sélectionner au moins une famille de véhicules');
          return;
        }
        this.currentStep = 2;
        this.loadAvailableProducts();
        break;
        
      case 2:
        if (this.confirmedSelectedProducts.length === 0) {
          this.setErrorMessage('Veuillez sélectionner au moins un produit');
          return;
        }
        if (!this.validatePropertiesSelection()) {
          const missingProperties = this.getMissingRequiredProperties();
          const missingNames = missingProperties.map(prop => prop.name).join(', ');
          this.setErrorMessage(`Veuillez sélectionner les propriétés requises suivantes : ${missingNames}`);
          return;
        }
        // Passer directement à l'étape 3 (récapitulatif)
        this.currentStep = 3;
        this.generateFinalConfiguration();
        break;
    }
    
    this.clearMessages();
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      // Si on est à l'étape 3 (récapitulatif), revenir directement à l'étape 2 (sélection)
      if (this.currentStep === 3) {
        this.currentStep = 2;
        this.backToSelection();
        return;
      }
      
      this.currentStep--;
      this.updateStepInterface();
    }
  }

  /**
   * Met à jour l'interface après changement d'étape
   */
  private updateStepInterface(): void {
    this.clearMessages();
    if (this.currentStep === 2) {
      this.loadAvailableProducts();
      this.pagination.currentPage = 1;
    }
  }

  /**
   * Retourne à la sélection des produits
   */
  backToSelection(): void {
    this.tempSelectedProducts = [...this.confirmedSelectedProducts];
    this.confirmedSelectedProducts = [];
    this.currentStep = 2;
  }

  /**
   * Génère la configuration finale
   */
  private generateFinalConfiguration(): void {
    // Initialiser tous les panneaux de détails de produits comme étendus
    this.confirmedSelectedProducts.forEach(selection => {
      this.expandedProductDetails.add(selection.product.productid);
    });
    
    // Initialiser l'expansion des produits principaux dans le tableau
    this.confirmedSelectedProducts
      .filter(s => s.selectionSource === 'MAIN_LIST')
      .forEach(s => this.expandedTableRows.add(s.product.productid));
    const mainProductsPrice = this.confirmedSelectedProducts
      .filter(s => s.selectionSource === 'MAIN_LIST')
      .reduce((sum, s) => sum + ((s.product.price || 0) * s.quantity), 0);

    this.finalConfiguration = {
      selectedCategory: this.selectedCategory,
      selectedFamilies: this.selectedFamilyIds.map(id => 
        this.carFamilies.find(f => f.id === id)
      ).filter(f => f !== undefined),
      selectedProducts: this.confirmedSelectedProducts.map(s => ({
        product: s.product,
        quantity: s.quantity,
        subtotal: (s.product.price || 0) * s.quantity,
        selectionSource: s.selectionSource,
        level: this.getProductLevel(s.product.productid),
        hierarchyPath: this.getProductHierarchyPath(s.product.productid)
      })),
      selectedProperties: this.selectedProperties.filter(ps => ps.selected),
      priceBreakdown: {
        mainProducts: mainProductsPrice,
        properties: this.propertiesPrice,
        total: this.totalPrice
      },
      totalPrice: this.totalPrice,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Obtient le niveau hiérarchique d'un produit
   */
  getProductLevel(productId: string): number {
    const product = this.allProducts.find(p => p.productid === productId);
    if (!product) return 0;
    
    let level = 0;
    let currentProduct = product;
    
    while (currentProduct.parentproductid) {
      level++;
      const parent = this.allProducts.find(p => p.productid === currentProduct.parentproductid);
      if (!parent) break;
      currentProduct = parent;
    }
    
    return level;
  }

  /**
   * Obtient le chemin hiérarchique d'un produit
   */
  private getProductHierarchyPath(productId: string): Product[] {
    const path: Product[] = [];
    const product = this.allProducts.find(p => p.productid === productId);
    
    if (!product) return path;
    
    let currentProduct = product;
    path.unshift(currentProduct);
    
    while (currentProduct.parentproductid) {
      const parent = this.allProducts.find(p => p.productid === currentProduct.parentproductid);
      if (!parent) break;
      path.unshift(parent);
      currentProduct = parent;
    }
    
    return path;
  }

  /**
   * Vérifie si la configuration est valide pour générer un devis
   */
  validatePropertiesSelection(): boolean {
    return this.areAllRequiredPropertiesSelected();
  }

  /**
   * Définit un message d'erreur
   */
  private setErrorMessage(message: string): void {
    this.errorMessage = message;
    this.successMessage = '';
    setTimeout(() => this.clearMessages(), 5000);
  }

  /**
   * Définit un message de succès
   */
  private setSuccessMessage(message: string): void {
    this.successMessage = message;
    this.errorMessage = '';
    setTimeout(() => this.clearMessages(), 3000);
  }

  // Méthodes pour la validation
  areAllRequiredPropertiesSelected(): boolean {
    // Vérifier uniquement les produits confirmés qui ont des propriétés requises
    for (const selection of this.confirmedSelectedProducts) {
      const productId = selection.product.productid;
      const productRequiredProperties = this.getAvailablePropertiesForProduct(productId)
        .filter(prop => prop.isrequired);
      
      // Si ce produit n'a pas de propriétés requises, passer au suivant
      if (productRequiredProperties.length === 0) {
        continue;
      }
      
      // Vérifier si toutes les propriétés requises de ce produit sont sélectionnées
      for (const requiredProp of productRequiredProperties) {
        const isSelected = this.selectedProperties.some(
          ps => ps.productid === productId && 
                ps.propertyid === requiredProp.propertyid && 
                ps.selected
        );
        
        if (!isSelected) {
          return false;
        }
      }
    }
    
    return true;
  }

  getMissingRequiredProperties(): any[] {
    const missingProperties: any[] = [];
    
    // Vérifier uniquement les produits confirmés
    for (const selection of this.confirmedSelectedProducts) {
      const productId = selection.product.productid;
      const productRequiredProperties = this.getAvailablePropertiesForProduct(productId)
        .filter(prop => prop.isrequired);
      
      // Pour chaque propriété requise de ce produit
      for (const requiredProp of productRequiredProperties) {
        const isSelected = this.selectedProperties.some(
          ps => ps.productid === productId && 
                ps.propertyid === requiredProp.propertyid && 
                ps.selected
        );
        
        if (!isSelected) {
          missingProperties.push({
            ...requiredProp,
            productName: selection.product.name
          });
        }
      }
    }
    
    return missingProperties;
  }

  // Méthodes pour les panneaux
  togglePricePanel(): void {
    this.isPricePanelExpanded = !this.isPricePanelExpanded;
  }

  // Méthode pour la configuration
  resetConfiguration(): void {
    // Réinitialiser toutes les sélections
    this.selectedFamilyIds = [];
    this.tempSelectedProducts = [];
    this.confirmedSelectedProducts = [];
    this.selectedProperties = [];
    
    // Réinitialiser les filtres et la pagination
    this.productFilters = {
      searchTerm: '',
      familyFilter: 'all',
      typeFilter: 'all'
    };
    this.pagination = {
      currentPage: 1,
      itemsPerPage: 25,
      totalItems: 0,
      availablePageSizes: [25, 50, 75, 0]
    };
    
    // Réinitialiser les états
    this.expandedProducts = new Map<string, boolean>();
    this.totalPrice = 0;
    this.propertiesPrice = 0;
    
    // Revenir à l'étape 1
    this.currentStep = 1;
  }

  /**
   * Vérifie si un produit a des enfants sélectionnés
   */
  hasChildrenSelected(product: any): boolean {
    if (!product || !product.productid) return false;
    const allSelected = [...this.tempSelectedProducts, ...this.confirmedSelectedProducts];
    const descendants = this.getAllDescendants(product.productid);
    return descendants.some(descendantId => 
      allSelected.some(sel => sel.product.productid === descendantId)
    );
  }

  /**
   * Affiche le modal de conflit parent-enfant
   */
  showParentChildConflictModal(product: any): void {
    console.log('Conflit détecté pour le produit:', product.name);
    this.conflictProduct = product;
    
    // Obtenir la liste des produits enfants sélectionnés pour l'affichage dans le modal
    const descendants = this.getAllDescendants(product.productid);
    const allSelected = [...this.tempSelectedProducts, ...this.confirmedSelectedProducts];
    this.selectedChildrenProducts = allSelected
      .filter(sel => descendants.includes(sel.product.productid));
  }
}