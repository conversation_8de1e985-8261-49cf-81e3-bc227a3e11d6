{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, computed, Directive, Input } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { tuiDirectiveBinding, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TuiTextfieldComponent } from '@taiga-ui/core/components/textfield';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { TUI_APPEARANCE_OPTIONS, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport * as i2 from '@taiga-ui/core/directives/hint';\nimport { TuiHintDirective } from '@taiga-ui/core/directives/hint';\nimport { TUI_COPY_TEXTS } from '@taiga-ui/kit/tokens';\nimport { Subject, switchMap, timer, map, startWith } from 'rxjs';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\nconst [TUI_COPY_OPTIONS, tuiCopyOptionsProvider] = tuiCreateOptions({\n  icon: '@tui.copy'\n});\nclass TuiCopy {\n  constructor() {\n    this.options = inject(TUI_COPY_OPTIONS);\n    this.copied$ = new Subject();\n    this.doc = inject(DOCUMENT);\n    this.textfield = inject(TuiTextfieldComponent);\n    this.hint = tuiDirectiveBinding(TuiHintDirective, 'tuiHint', toSignal(inject(TUI_COPY_TEXTS).pipe(switchMap(([copy, copied]) => this.copied$.pipe(switchMap(() => timer(3000).pipe(map(() => copy), startWith(copied))), startWith(copy)))), {\n      initialValue: ''\n    }));\n    this.icons = tuiDirectiveBinding(TuiIcon, 'icon', computed((size = this.textfield.options.size()) => tuiIsString(this.options.icon) ? this.options.icon : this.options.icon(size)));\n    this.tuiCopy = '';\n  }\n  get disabled() {\n    return !this.textfield.input?.nativeElement.value;\n  }\n  copy() {\n    this.textfield.input?.nativeElement.select();\n    this.doc.execCommand('copy');\n    this.copied$.next();\n  }\n  static {\n    this.ɵfac = function TuiCopy_Factory(t) {\n      return new (t || TuiCopy)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiCopy,\n      selectors: [[\"tui-icon\", \"tuiCopy\", \"\"]],\n      hostAttrs: [2, \"cursor\", \"pointer\"],\n      hostVars: 6,\n      hostBindings: function TuiCopy_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TuiCopy_click_HostBindingHandler() {\n            return ctx.copy();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"pointer-events\", ctx.disabled ? \"none\" : null)(\"opacity\", ctx.disabled ? \"var(--tui-disabled-opacity)\" : null)(\"border\", ctx.textfield.options.size() === \"s\" ? \"0.25rem solid transparent\" : null);\n        }\n      },\n      inputs: {\n        tuiCopy: \"tuiCopy\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: TUI_APPEARANCE_OPTIONS,\n        useValue: {\n          appearance: 'icon'\n        }\n      }]), i0.ɵɵHostDirectivesFeature([i1.TuiWithAppearance, {\n        directive: i2.TuiHintDirective,\n        inputs: [\"tuiHintAppearance\", \"tuiHintAppearance\", \"tuiHintContext\", \"tuiHintContext\"]\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiCopy, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'tui-icon[tuiCopy]',\n      providers: [{\n        provide: TUI_APPEARANCE_OPTIONS,\n        useValue: {\n          appearance: 'icon'\n        }\n      }],\n      hostDirectives: [TuiWithAppearance, {\n        directive: TuiHintDirective,\n        inputs: ['tuiHintAppearance', 'tuiHintContext']\n      }],\n      host: {\n        style: 'cursor: pointer',\n        '(click)': 'copy()',\n        '[style.pointer-events]': 'disabled ? \"none\" : null',\n        '[style.opacity]': 'disabled ? \"var(--tui-disabled-opacity)\" : null',\n        '[style.border]': 'textfield.options.size() === \"s\" ? \"0.25rem solid transparent\" : null'\n      }\n    }]\n  }], null, {\n    tuiCopy: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_COPY_OPTIONS, TuiCopy, tuiCopyOptionsProvider };", "map": {"version": 3, "names": ["DOCUMENT", "i0", "inject", "computed", "Directive", "Input", "toSignal", "tuiDirectiveBinding", "tuiIsString", "TuiIcon", "TuiTextfieldComponent", "i1", "TUI_APPEARANCE_OPTIONS", "TuiWithAppearance", "i2", "TuiHintDirective", "TUI_COPY_TEXTS", "Subject", "switchMap", "timer", "map", "startWith", "tuiCreateOptions", "TUI_COPY_OPTIONS", "tuiCopyOptionsProvider", "icon", "TuiCopy", "constructor", "options", "copied$", "doc", "textfield", "hint", "pipe", "copy", "copied", "initialValue", "icons", "size", "tuiCopy", "disabled", "input", "nativeElement", "value", "select", "execCommand", "next", "ɵfac", "TuiCopy_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "TuiCopy_HostBindings", "rf", "ctx", "ɵɵlistener", "TuiCopy_click_HostBindingHandler", "ɵɵstyleProp", "inputs", "standalone", "features", "ɵɵProvidersFeature", "provide", "useValue", "appearance", "ɵɵHostDirectivesFeature", "directive", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "hostDirectives", "host", "style"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-directives-copy.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, computed, Directive, Input } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { tuiDirectiveBinding, tuiIsString } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { TuiIcon } from '@taiga-ui/core/components/icon';\nimport { TuiTextfieldComponent } from '@taiga-ui/core/components/textfield';\nimport * as i1 from '@taiga-ui/core/directives/appearance';\nimport { TUI_APPEARANCE_OPTIONS, TuiWithAppearance } from '@taiga-ui/core/directives/appearance';\nimport * as i2 from '@taiga-ui/core/directives/hint';\nimport { TuiHintDirective } from '@taiga-ui/core/directives/hint';\nimport { TUI_COPY_TEXTS } from '@taiga-ui/kit/tokens';\nimport { Subject, switchMap, timer, map, startWith } from 'rxjs';\nimport { tuiCreateOptions } from '@taiga-ui/cdk/utils/di';\n\nconst [TUI_COPY_OPTIONS, tuiCopyOptionsProvider] = tuiCreateOptions({\n    icon: '@tui.copy',\n});\n\nclass TuiCopy {\n    constructor() {\n        this.options = inject(TUI_COPY_OPTIONS);\n        this.copied$ = new Subject();\n        this.doc = inject(DOCUMENT);\n        this.textfield = inject(TuiTextfieldComponent);\n        this.hint = tuiDirectiveBinding(TuiHintDirective, 'tuiHint', toSignal(inject(TUI_COPY_TEXTS).pipe(switchMap(([copy, copied]) => this.copied$.pipe(switchMap(() => timer(3000).pipe(map(() => copy), startWith(copied))), startWith(copy)))), { initialValue: '' }));\n        this.icons = tuiDirectiveBinding(TuiIcon, 'icon', computed((size = this.textfield.options.size()) => tuiIsString(this.options.icon) ? this.options.icon : this.options.icon(size)));\n        this.tuiCopy = '';\n    }\n    get disabled() {\n        return !this.textfield.input?.nativeElement.value;\n    }\n    copy() {\n        this.textfield.input?.nativeElement.select();\n        this.doc.execCommand('copy');\n        this.copied$.next();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCopy, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiCopy, isStandalone: true, selector: \"tui-icon[tuiCopy]\", inputs: { tuiCopy: \"tuiCopy\" }, host: { listeners: { \"click\": \"copy()\" }, properties: { \"style.pointer-events\": \"disabled ? \\\"none\\\" : null\", \"style.opacity\": \"disabled ? \\\"var(--tui-disabled-opacity)\\\" : null\", \"style.border\": \"textfield.options.size() === \\\"s\\\" ? \\\"0.25rem solid transparent\\\" : null\" }, styleAttribute: \"cursor: pointer\" }, providers: [\n            {\n                provide: TUI_APPEARANCE_OPTIONS,\n                useValue: { appearance: 'icon' },\n            },\n        ], hostDirectives: [{ directive: i1.TuiWithAppearance }, { directive: i2.TuiHintDirective, inputs: [\"tuiHintAppearance\", \"tuiHintAppearance\", \"tuiHintContext\", \"tuiHintContext\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiCopy, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'tui-icon[tuiCopy]',\n                    providers: [\n                        {\n                            provide: TUI_APPEARANCE_OPTIONS,\n                            useValue: { appearance: 'icon' },\n                        },\n                    ],\n                    hostDirectives: [\n                        TuiWithAppearance,\n                        {\n                            directive: TuiHintDirective,\n                            inputs: ['tuiHintAppearance', 'tuiHintContext'],\n                        },\n                    ],\n                    host: {\n                        style: 'cursor: pointer',\n                        '(click)': 'copy()',\n                        '[style.pointer-events]': 'disabled ? \"none\" : null',\n                        '[style.opacity]': 'disabled ? \"var(--tui-disabled-opacity)\" : null',\n                        '[style.border]': 'textfield.options.size() === \"s\" ? \"0.25rem solid transparent\" : null',\n                    },\n                }]\n        }], propDecorators: { tuiCopy: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_COPY_OPTIONS, TuiCopy, tuiCopyOptionsProvider };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAClE,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,mCAAmC;AACpF,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,qBAAqB,QAAQ,qCAAqC;AAC3E,OAAO,KAAKC,EAAE,MAAM,sCAAsC;AAC1D,SAASC,sBAAsB,EAAEC,iBAAiB,QAAQ,sCAAsC;AAChG,OAAO,KAAKC,EAAE,MAAM,gCAAgC;AACpD,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,OAAO,EAAEC,SAAS,EAAEC,KAAK,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;AAChE,SAASC,gBAAgB,QAAQ,wBAAwB;AAEzD,MAAM,CAACC,gBAAgB,EAAEC,sBAAsB,CAAC,GAAGF,gBAAgB,CAAC;EAChEG,IAAI,EAAE;AACV,CAAC,CAAC;AAEF,MAAMC,OAAO,CAAC;EACVC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG1B,MAAM,CAACqB,gBAAgB,CAAC;IACvC,IAAI,CAACM,OAAO,GAAG,IAAIZ,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACa,GAAG,GAAG5B,MAAM,CAACF,QAAQ,CAAC;IAC3B,IAAI,CAAC+B,SAAS,GAAG7B,MAAM,CAACQ,qBAAqB,CAAC;IAC9C,IAAI,CAACsB,IAAI,GAAGzB,mBAAmB,CAACQ,gBAAgB,EAAE,SAAS,EAAET,QAAQ,CAACJ,MAAM,CAACc,cAAc,CAAC,CAACiB,IAAI,CAACf,SAAS,CAAC,CAAC,CAACgB,IAAI,EAAEC,MAAM,CAAC,KAAK,IAAI,CAACN,OAAO,CAACI,IAAI,CAACf,SAAS,CAAC,MAAMC,KAAK,CAAC,IAAI,CAAC,CAACc,IAAI,CAACb,GAAG,CAAC,MAAMc,IAAI,CAAC,EAAEb,SAAS,CAACc,MAAM,CAAC,CAAC,CAAC,EAAEd,SAAS,CAACa,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MAAEE,YAAY,EAAE;IAAG,CAAC,CAAC,CAAC;IACnQ,IAAI,CAACC,KAAK,GAAG9B,mBAAmB,CAACE,OAAO,EAAE,MAAM,EAAEN,QAAQ,CAAC,CAACmC,IAAI,GAAG,IAAI,CAACP,SAAS,CAACH,OAAO,CAACU,IAAI,CAAC,CAAC,KAAK9B,WAAW,CAAC,IAAI,CAACoB,OAAO,CAACH,IAAI,CAAC,GAAG,IAAI,CAACG,OAAO,CAACH,IAAI,GAAG,IAAI,CAACG,OAAO,CAACH,IAAI,CAACa,IAAI,CAAC,CAAC,CAAC;IACnL,IAAI,CAACC,OAAO,GAAG,EAAE;EACrB;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,CAAC,IAAI,CAACT,SAAS,CAACU,KAAK,EAAEC,aAAa,CAACC,KAAK;EACrD;EACAT,IAAIA,CAAA,EAAG;IACH,IAAI,CAACH,SAAS,CAACU,KAAK,EAAEC,aAAa,CAACE,MAAM,CAAC,CAAC;IAC5C,IAAI,CAACd,GAAG,CAACe,WAAW,CAAC,MAAM,CAAC;IAC5B,IAAI,CAAChB,OAAO,CAACiB,IAAI,CAAC,CAAC;EACvB;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,gBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFvB,OAAO;IAAA,CAAmD;EAAE;EACvK;IAAS,IAAI,CAACwB,IAAI,kBAD+EjD,EAAE,CAAAkD,iBAAA;MAAAC,IAAA,EACJ1B,OAAO;MAAA2B,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADLzD,EAAE,CAAA2D,UAAA,mBAAAC,iCAAA;YAAA,OACJF,GAAA,CAAAzB,IAAA,CAAK,CAAC;UAAA,EAAC;QAAA;QAAA,IAAAwB,EAAA;UADLzD,EAAE,CAAA6D,WAAA,mBAAAH,GAAA,CAAAnB,QAAA,GACO,MAAM,GAAG,IAAd,CAAC,YAAAmB,GAAA,CAAAnB,QAAA,GAAI,6BAA6B,GAAG,IAArC,CAAC,WAAPmB,GAAA,CAAA5B,SAAA,CAAAH,OAAA,CAAAU,IAAA,CAAuB,CAAC,KAAK,GAAG,GAAG,2BAA2B,GAAG,IAA3D,CAAC;QAAA;MAAA;MAAAyB,MAAA;QAAAxB,OAAA;MAAA;MAAAyB,UAAA;MAAAC,QAAA,GADLhE,EAAE,CAAAiE,kBAAA,CAC2Z,CACtf;QACIC,OAAO,EAAEvD,sBAAsB;QAC/BwD,QAAQ,EAAE;UAAEC,UAAU,EAAE;QAAO;MACnC,CAAC,CACJ,GAN4FpE,EAAE,CAAAqE,uBAAA,EAM9D3D,EAAE,CAACE,iBAAiB;QAAA0D,SAAA,EAAiBzD,EAAE,CAACC,gBAAgB;QAAAgD,MAAA;MAAA;IAAA,EAA4G;EAAE;AAC/M;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KARqGvE,EAAE,CAAAwE,iBAAA,CAQX/C,OAAO,EAAc,CAAC;IACtG0B,IAAI,EAAEhD,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCV,UAAU,EAAE,IAAI;MAChBW,QAAQ,EAAE,mBAAmB;MAC7BC,SAAS,EAAE,CACP;QACIT,OAAO,EAAEvD,sBAAsB;QAC/BwD,QAAQ,EAAE;UAAEC,UAAU,EAAE;QAAO;MACnC,CAAC,CACJ;MACDQ,cAAc,EAAE,CACZhE,iBAAiB,EACjB;QACI0D,SAAS,EAAExD,gBAAgB;QAC3BgD,MAAM,EAAE,CAAC,mBAAmB,EAAE,gBAAgB;MAClD,CAAC,CACJ;MACDe,IAAI,EAAE;QACFC,KAAK,EAAE,iBAAiB;QACxB,SAAS,EAAE,QAAQ;QACnB,wBAAwB,EAAE,0BAA0B;QACpD,iBAAiB,EAAE,iDAAiD;QACpE,gBAAgB,EAAE;MACtB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAExC,OAAO,EAAE,CAAC;MACxBa,IAAI,EAAE/C;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASkB,gBAAgB,EAAEG,OAAO,EAAEF,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}