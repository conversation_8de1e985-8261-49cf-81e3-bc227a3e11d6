{"ast": null, "code": "import { svg<PERSON><PERSON><PERSON><PERSON><PERSON>, CHAR_NO_BREAK_SPACE, CHAR_ZERO_WIDTH_SPACE } from '@taiga-ui/cdk/constants';\nfunction tuiCheckFixedPosition(element) {\n  return !!element && (isFixed(element) || tuiCheckFixedPosition(element.parentElement));\n}\nfunction isFixed(element) {\n  return element.ownerDocument.defaultView?.getComputedStyle(element).getPropertyValue('position') === 'fixed';\n}\n\n/**\n * @description:\n * Cross-browser @media (height/width)\n *\n * 1. window.innerWidth/Width\n * 1.1. gets CSS viewport @media (height/width) which include scrollbars\n * 1.2. initial-scale and zoom variations may cause mobile values to\n *      wrongly scale down to what PPK calls the visual\n *      viewport and be smaller than the @media values\n *  1.3. zoom may cause values to be 1px off due to native rounding\n *\n *  2. document.documentElement.clientHeight/Width\n *  2.1. equals CSS viewport width minus scrollbar width\n *  2.2. matches @media (height) when there is no scrollbar\n *  2.3. available cross-browser\n *  2.4. inaccurate if doctype is missing\n */\nfunction tuiGetViewportHeight({\n  document,\n  innerHeight\n}) {\n  return Math.max(document.documentElement.clientHeight || 0, innerHeight || 0);\n}\nfunction tuiGetViewportWidth({\n  document,\n  innerWidth\n}) {\n  return Math.max(document.documentElement.clientWidth || 0, innerWidth || 0);\n}\n\n/**\n * Creates a cloned range with its boundaries set at word boundaries\n *\n * @param currentRange a range to clone\n * @return modified range\n */\nfunction tuiGetWordRange(currentRange) {\n  const range = currentRange.cloneRange();\n  const {\n    startContainer,\n    startOffset,\n    endContainer,\n    endOffset\n  } = range;\n  const {\n    ownerDocument\n  } = startContainer;\n  if (!ownerDocument) {\n    return range;\n  }\n  const treeWalker = ownerDocument.createTreeWalker(ownerDocument.body, NodeFilter.SHOW_TEXT, svgNodeFilter);\n  treeWalker.currentNode = startContainer;\n  do {\n    const container = treeWalker.currentNode;\n    const textContent = container.textContent || '';\n    const content = container === startContainer ? textContent.slice(0, Math.max(0, startOffset + 1)) : textContent;\n    const offset = Math.max(content.lastIndexOf(' '), content.lastIndexOf(CHAR_NO_BREAK_SPACE), content.lastIndexOf(CHAR_ZERO_WIDTH_SPACE)) + 1;\n    range.setStart(container, 0);\n    if (offset) {\n      range.setStart(container, offset);\n      break;\n    }\n  } while (treeWalker.previousNode());\n  treeWalker.currentNode = endContainer;\n  do {\n    const container = treeWalker.currentNode;\n    const textContent = container.textContent || '';\n    const content = container === endContainer ? textContent.slice(endOffset + 1) : textContent;\n    const offset = [content.indexOf(' '), content.indexOf(CHAR_NO_BREAK_SPACE), content.indexOf(CHAR_ZERO_WIDTH_SPACE)].reduce((result, item) => result === -1 || item === -1 ? Math.max(result, item) : Math.min(result, item), -1);\n    range.setEnd(container, textContent.length);\n    if (offset !== -1) {\n      range.setEnd(container, offset + textContent.length - content.length);\n      break;\n    }\n  } while (treeWalker.nextNode());\n  return range;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiCheckFixedPosition, tuiGetViewportHeight, tuiGetViewportWidth, tuiGetWordRange };", "map": {"version": 3, "names": ["svgNodeFilter", "CHAR_NO_BREAK_SPACE", "CHAR_ZERO_WIDTH_SPACE", "tuiCheckFixedPosition", "element", "isFixed", "parentElement", "ownerDocument", "defaultView", "getComputedStyle", "getPropertyValue", "tuiGetViewportHeight", "document", "innerHeight", "Math", "max", "documentElement", "clientHeight", "tuiGetViewportWidth", "innerWidth", "clientWidth", "tuiGetWordRange", "currentRange", "range", "cloneRange", "startContainer", "startOffset", "endContainer", "endOffset", "<PERSON><PERSON><PERSON><PERSON>", "createTreeWalker", "body", "Node<PERSON><PERSON><PERSON>", "SHOW_TEXT", "currentNode", "container", "textContent", "content", "slice", "offset", "lastIndexOf", "setStart", "previousNode", "indexOf", "reduce", "result", "item", "min", "setEnd", "length", "nextNode"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-utils-dom.mjs"], "sourcesContent": ["import { svg<PERSON><PERSON><PERSON><PERSON><PERSON>, CHAR_NO_BREAK_SPACE, CHAR_ZERO_WIDTH_SPACE } from '@taiga-ui/cdk/constants';\n\nfunction tuiCheckFixedPosition(element) {\n    return (!!element && (isFixed(element) || tuiCheckFixedPosition(element.parentElement)));\n}\nfunction isFixed(element) {\n    return (element.ownerDocument.defaultView\n        ?.getComputedStyle(element)\n        .getPropertyValue('position') === 'fixed');\n}\n\n/**\n * @description:\n * Cross-browser @media (height/width)\n *\n * 1. window.innerWidth/Width\n * 1.1. gets CSS viewport @media (height/width) which include scrollbars\n * 1.2. initial-scale and zoom variations may cause mobile values to\n *      wrongly scale down to what PPK calls the visual\n *      viewport and be smaller than the @media values\n *  1.3. zoom may cause values to be 1px off due to native rounding\n *\n *  2. document.documentElement.clientHeight/Width\n *  2.1. equals CSS viewport width minus scrollbar width\n *  2.2. matches @media (height) when there is no scrollbar\n *  2.3. available cross-browser\n *  2.4. inaccurate if doctype is missing\n */\nfunction tuiGetViewportHeight({ document, innerHeight }) {\n    return Math.max(document.documentElement.clientHeight || 0, innerHeight || 0);\n}\nfunction tuiGetViewportWidth({ document, innerWidth }) {\n    return Math.max(document.documentElement.clientWidth || 0, innerWidth || 0);\n}\n\n/**\n * Creates a cloned range with its boundaries set at word boundaries\n *\n * @param currentRange a range to clone\n * @return modified range\n */\nfunction tuiGetWordRange(currentRange) {\n    const range = currentRange.cloneRange();\n    const { startContainer, startOffset, endContainer, endOffset } = range;\n    const { ownerDocument } = startContainer;\n    if (!ownerDocument) {\n        return range;\n    }\n    const treeWalker = ownerDocument.createTreeWalker(ownerDocument.body, NodeFilter.SHOW_TEXT, svgNodeFilter);\n    treeWalker.currentNode = startContainer;\n    do {\n        const container = treeWalker.currentNode;\n        const textContent = container.textContent || '';\n        const content = container === startContainer\n            ? textContent.slice(0, Math.max(0, startOffset + 1))\n            : textContent;\n        const offset = Math.max(content.lastIndexOf(' '), content.lastIndexOf(CHAR_NO_BREAK_SPACE), content.lastIndexOf(CHAR_ZERO_WIDTH_SPACE)) + 1;\n        range.setStart(container, 0);\n        if (offset) {\n            range.setStart(container, offset);\n            break;\n        }\n    } while (treeWalker.previousNode());\n    treeWalker.currentNode = endContainer;\n    do {\n        const container = treeWalker.currentNode;\n        const textContent = container.textContent || '';\n        const content = container === endContainer ? textContent.slice(endOffset + 1) : textContent;\n        const offset = [\n            content.indexOf(' '),\n            content.indexOf(CHAR_NO_BREAK_SPACE),\n            content.indexOf(CHAR_ZERO_WIDTH_SPACE),\n        ].reduce((result, item) => result === -1 || item === -1\n            ? Math.max(result, item)\n            : Math.min(result, item), -1);\n        range.setEnd(container, textContent.length);\n        if (offset !== -1) {\n            range.setEnd(container, offset + textContent.length - content.length);\n            break;\n        }\n    } while (treeWalker.nextNode());\n    return range;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiCheckFixedPosition, tuiGetViewportHeight, tuiGetViewportWidth, tuiGetWordRange };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,mBAAmB,EAAEC,qBAAqB,QAAQ,yBAAyB;AAEnG,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EACpC,OAAQ,CAAC,CAACA,OAAO,KAAKC,OAAO,CAACD,OAAO,CAAC,IAAID,qBAAqB,CAACC,OAAO,CAACE,aAAa,CAAC,CAAC;AAC3F;AACA,SAASD,OAAOA,CAACD,OAAO,EAAE;EACtB,OAAQA,OAAO,CAACG,aAAa,CAACC,WAAW,EACnCC,gBAAgB,CAACL,OAAO,CAAC,CAC1BM,gBAAgB,CAAC,UAAU,CAAC,KAAK,OAAO;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAC;EAAEC,QAAQ;EAAEC;AAAY,CAAC,EAAE;EACrD,OAAOC,IAAI,CAACC,GAAG,CAACH,QAAQ,CAACI,eAAe,CAACC,YAAY,IAAI,CAAC,EAAEJ,WAAW,IAAI,CAAC,CAAC;AACjF;AACA,SAASK,mBAAmBA,CAAC;EAAEN,QAAQ;EAAEO;AAAW,CAAC,EAAE;EACnD,OAAOL,IAAI,CAACC,GAAG,CAACH,QAAQ,CAACI,eAAe,CAACI,WAAW,IAAI,CAAC,EAAED,UAAU,IAAI,CAAC,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,eAAeA,CAACC,YAAY,EAAE;EACnC,MAAMC,KAAK,GAAGD,YAAY,CAACE,UAAU,CAAC,CAAC;EACvC,MAAM;IAAEC,cAAc;IAAEC,WAAW;IAAEC,YAAY;IAAEC;EAAU,CAAC,GAAGL,KAAK;EACtE,MAAM;IAAEhB;EAAc,CAAC,GAAGkB,cAAc;EACxC,IAAI,CAAClB,aAAa,EAAE;IAChB,OAAOgB,KAAK;EAChB;EACA,MAAMM,UAAU,GAAGtB,aAAa,CAACuB,gBAAgB,CAACvB,aAAa,CAACwB,IAAI,EAAEC,UAAU,CAACC,SAAS,EAAEjC,aAAa,CAAC;EAC1G6B,UAAU,CAACK,WAAW,GAAGT,cAAc;EACvC,GAAG;IACC,MAAMU,SAAS,GAAGN,UAAU,CAACK,WAAW;IACxC,MAAME,WAAW,GAAGD,SAAS,CAACC,WAAW,IAAI,EAAE;IAC/C,MAAMC,OAAO,GAAGF,SAAS,KAAKV,cAAc,GACtCW,WAAW,CAACE,KAAK,CAAC,CAAC,EAAExB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEW,WAAW,GAAG,CAAC,CAAC,CAAC,GAClDU,WAAW;IACjB,MAAMG,MAAM,GAAGzB,IAAI,CAACC,GAAG,CAACsB,OAAO,CAACG,WAAW,CAAC,GAAG,CAAC,EAAEH,OAAO,CAACG,WAAW,CAACvC,mBAAmB,CAAC,EAAEoC,OAAO,CAACG,WAAW,CAACtC,qBAAqB,CAAC,CAAC,GAAG,CAAC;IAC3IqB,KAAK,CAACkB,QAAQ,CAACN,SAAS,EAAE,CAAC,CAAC;IAC5B,IAAII,MAAM,EAAE;MACRhB,KAAK,CAACkB,QAAQ,CAACN,SAAS,EAAEI,MAAM,CAAC;MACjC;IACJ;EACJ,CAAC,QAAQV,UAAU,CAACa,YAAY,CAAC,CAAC;EAClCb,UAAU,CAACK,WAAW,GAAGP,YAAY;EACrC,GAAG;IACC,MAAMQ,SAAS,GAAGN,UAAU,CAACK,WAAW;IACxC,MAAME,WAAW,GAAGD,SAAS,CAACC,WAAW,IAAI,EAAE;IAC/C,MAAMC,OAAO,GAAGF,SAAS,KAAKR,YAAY,GAAGS,WAAW,CAACE,KAAK,CAACV,SAAS,GAAG,CAAC,CAAC,GAAGQ,WAAW;IAC3F,MAAMG,MAAM,GAAG,CACXF,OAAO,CAACM,OAAO,CAAC,GAAG,CAAC,EACpBN,OAAO,CAACM,OAAO,CAAC1C,mBAAmB,CAAC,EACpCoC,OAAO,CAACM,OAAO,CAACzC,qBAAqB,CAAC,CACzC,CAAC0C,MAAM,CAAC,CAACC,MAAM,EAAEC,IAAI,KAAKD,MAAM,KAAK,CAAC,CAAC,IAAIC,IAAI,KAAK,CAAC,CAAC,GACjDhC,IAAI,CAACC,GAAG,CAAC8B,MAAM,EAAEC,IAAI,CAAC,GACtBhC,IAAI,CAACiC,GAAG,CAACF,MAAM,EAAEC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IACjCvB,KAAK,CAACyB,MAAM,CAACb,SAAS,EAAEC,WAAW,CAACa,MAAM,CAAC;IAC3C,IAAIV,MAAM,KAAK,CAAC,CAAC,EAAE;MACfhB,KAAK,CAACyB,MAAM,CAACb,SAAS,EAAEI,MAAM,GAAGH,WAAW,CAACa,MAAM,GAAGZ,OAAO,CAACY,MAAM,CAAC;MACrE;IACJ;EACJ,CAAC,QAAQpB,UAAU,CAACqB,QAAQ,CAAC,CAAC;EAC9B,OAAO3B,KAAK;AAChB;;AAEA;AACA;AACA;;AAEA,SAASpB,qBAAqB,EAAEQ,oBAAoB,EAAEO,mBAAmB,EAAEG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}