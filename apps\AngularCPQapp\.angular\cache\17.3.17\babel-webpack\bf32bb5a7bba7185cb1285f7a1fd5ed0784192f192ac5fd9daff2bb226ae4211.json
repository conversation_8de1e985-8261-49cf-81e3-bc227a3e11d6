{"ast": null, "code": "import { FormArray, FormGroup } from '@angular/forms';\nimport { tuiToInt } from '@taiga-ui/cdk/utils/math';\nimport { InjectionToken, isSignal, signal, inject, effect, DestroyRef, EnvironmentInjector, createComponent } from '@angular/core';\nimport { TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { toSignal } from '@angular/core/rxjs-interop';\nfunction tuiArrayRemove(array, index) {\n  return array.slice(0, Math.max(index, 0)).concat(array.slice(Math.max(index + 1, 0)));\n}\nfunction tuiArrayShallowEquals(a, b) {\n  return a.length === b.length && a.every((item, index) => item === b[index]);\n}\nfunction tuiArrayToggle(array, item, identity) {\n  const index = identity ? array.findIndex(it => identity(it, item)) : array.indexOf(item);\n  return index === -1 ? [...array, item] : tuiArray<PERSON>emove(array, index);\n}\n\n/* internal */\nconst changeDateSeparator = (dateString, newDateSeparator) => dateString.replaceAll(/[^0-9A-Za-zА-Яа-я]/gi, newDateSeparator);\nfunction tuiIsControlEmpty({\n  value = null\n}) {\n  return value === null || value === '' || Array.isArray(value) && !value.length;\n}\nfunction tuiCountFilledControls(control) {\n  if (control instanceof FormArray) {\n    return control.controls.reduce((acc, nestedControl) => acc + tuiCountFilledControls(nestedControl), 0);\n  }\n  if (control instanceof FormGroup) {\n    return Object.values(control.controls).reduce((acc, nestedControl) => acc + tuiCountFilledControls(nestedControl), 0);\n  }\n  return tuiToInt(!tuiIsControlEmpty(control));\n}\nfunction tuiCreateToken(defaults) {\n  return defaults === undefined ? new InjectionToken('') : tuiCreateTokenFromFactory(() => defaults);\n}\nfunction tuiCreateTokenFromFactory(factory) {\n  return factory ? new InjectionToken('', {\n    factory\n  }) : new InjectionToken('');\n}\nfunction tuiIsString(value) {\n  return typeof value === 'string';\n}\nfunction tuiDefaultSort(x, y) {\n  if (x === y) {\n    return 0;\n  }\n  if (tuiIsString(x) && tuiIsString(y)) {\n    return x.localeCompare(y);\n  }\n  return x > y ? 1 : -1;\n}\nfunction tuiDirectiveBinding(token, key, initial, options = {\n  self: true\n}) {\n  const result = isSignal(initial) ? initial : signal(initial);\n  const directive = inject(token, options);\n  const output = directive[`${key.toString()}Change`];\n  // TODO: Figure out why effects are executed all the time and not just when result changes (check with Angular 18)\n  let previous;\n  effect(() => {\n    const value = result();\n    if (previous === value) {\n      return;\n    }\n    if (isSignal(directive[key])) {\n      directive[key].set(value);\n    } else {\n      directive[key] = value;\n    }\n    directive.ngOnChanges?.({});\n    output?.emit?.(value);\n    previous = value;\n  }, TUI_ALLOW_SIGNAL_WRITES);\n  return result;\n}\n\n/** @deprecated remove in v5 */\nfunction tuiDirectiveListener(token, key, options = {\n  self: true\n}) {\n  const prop = inject(token, options)?.[key];\n  return isSignal(prop) ? prop : toSignal(prop);\n}\nfunction tuiDistanceBetweenTouches({\n  touches\n}) {\n  return Math.hypot((touches[0]?.clientX ?? 0) - (touches[1]?.clientX ?? 0), (touches[0]?.clientY ?? 0) - (touches[1]?.clientY ?? 0));\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nfunction tuiEaseInOutQuad(t) {\n  ngDevMode && console.assert(t >= 0 && t <= 1, 'Input must be between 0 and 1 inclusive but received ', t);\n  return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n}\n\n/**\n * Flattens two-dimensional array and calculates resulting length\n *\n * @param array twi dimensional array\n */\nfunction tuiFlatLength(array) {\n  return array.reduce((count, section) => count + section.length, 0);\n}\nconst IFRAME = 'position: fixed; visibility: hidden; pointer-events: none';\nconst BODY = 'height: fit-content; line-height: 1em;';\nconst CLASS = 'tui-font-size-watcher';\nfunction tuiFontSizeWatcher(callback, win = window) {\n  if (win.document.querySelector(`.${CLASS}`)) {\n    return () => {};\n  }\n  const iframe = win.document.createElement('iframe');\n  const resize = () => {\n    const {\n      innerWidth,\n      outerWidth,\n      devicePixelRatio\n    } = win;\n    iframe.width = `${innerWidth === outerWidth ? innerWidth : innerWidth / devicePixelRatio}`;\n  };\n  win.document.body.append(iframe);\n  win.addEventListener('resize', resize);\n  const doc = iframe.contentWindow?.document;\n  const observer = new ResizeObserver(() => callback(doc?.body.offsetHeight || 0));\n  iframe.setAttribute('class', CLASS);\n  iframe.setAttribute('style', IFRAME);\n  doc?.documentElement.style.setProperty('font', '-apple-system-body');\n  doc?.body.setAttribute('style', BODY);\n  doc?.body.insertAdjacentText('beforeend', '.'.repeat(1000));\n  observer.observe(doc?.body || iframe);\n  resize();\n  return () => {\n    observer.disconnect();\n    iframe.remove();\n    win.removeEventListener('resize', resize);\n  };\n}\n\n/**\n * Extracts original array from {@link QueryList} rather than\n * creating a copy like {@link QueryList.toArray} does.\n * @param queryList\n * @returns original array from {@link QueryList}.\n */\nfunction tuiGetOriginalArrayFromQueryList(queryList) {\n  let array = [];\n  queryList.find((_item, _index, originalArray) => {\n    array = originalArray;\n    return true;\n  });\n  return array;\n}\nfunction tuiIsFalsy(value) {\n  return !value;\n}\nfunction tuiIsNumber(value) {\n  return typeof value === 'number';\n}\nfunction tuiIsObject(value) {\n  return typeof value === 'object' && !!value;\n}\nfunction tuiIsPresent(value) {\n  return value !== null && value !== undefined;\n}\n\n/**\n * @deprecated: drop in v5.0\n */\nfunction tuiIsValidUrl(url) {\n  return new RegExp(String.raw`^([a-zA-Z]+:\\/\\/)?` +\n  // protocol\n  String.raw`((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|localhost|` +\n  // domain name\n  String.raw`((\\d{1,3}\\.){3}\\d{1,3}))` +\n  // OR IP (v4) address\n  String.raw`(\\:\\d+)?(\\/[-a-z\\d%_.~+\\:]*)*` +\n  // port and path\n  String.raw`(\\?[)(;&a-z\\d%_.~+=-]*)?` +\n  // query string\n  String.raw`(\\#[-a-z\\d_]*)?$`,\n  // fragment locator\n  'i').test(url);\n}\nfunction tuiMarkControlAsTouchedAndValidate(control) {\n  if (control instanceof FormArray) {\n    control.controls.forEach(nestedControl => {\n      tuiMarkControlAsTouchedAndValidate(nestedControl);\n    });\n  }\n  if (control instanceof FormGroup) {\n    Object.values(control.controls).forEach(nestedControl => {\n      tuiMarkControlAsTouchedAndValidate(nestedControl);\n    });\n  }\n  control.markAsTouched();\n  control.updateValueAndValidity();\n}\n\n/**\n * Checks identity for nullable elements.\n *\n * @param a element a\n * @param b element b\n * @param handler called if both elements are not null\n * @return true if either both are null or they pass identity handler\n */\nfunction tuiNullableSame(a, b, handler) {\n  if (a === null) {\n    return b === null;\n  }\n  if (b === null) {\n    return false;\n  }\n  return handler(a, b);\n}\n\n/**\n * Obfuscates a string by replacing certain characters with a symbol.\n *\n * @param value the input string to obfuscate\n * @param symbol the symbol for obfuscation\n * @return the obfuscated string\n *\n * The function determines which characters to obfuscate using a regular expression and the string's length:\n * - 8 or more: show first 2 and last 2 characters\n * - 4 to 7: show first and last character\n * - less than 4: obfuscate all characters\n * - obfuscates only alphanumeric characters\n */\nfunction tuiObfuscate(value, symbol) {\n  if (!value) {\n    return value;\n  }\n  const match = /[\\p{L}\\p{N}]/gu;\n  let visible = 0;\n  let obfuscateIndexes = getObfuscateIndexes(value, match);\n  if (obfuscateIndexes.length >= 8) {\n    visible = 2;\n  } else if (obfuscateIndexes.length >= 4) {\n    visible = 1;\n  }\n  obfuscateIndexes = obfuscateIndexes.slice(visible, obfuscateIndexes.length);\n  const lastIndex = obfuscateIndexes.length - visible;\n  obfuscateIndexes = obfuscateIndexes.slice(0, lastIndex < 0 ? 0 : lastIndex);\n  const result = value.split('');\n  obfuscateIndexes.forEach(index => {\n    result[index] = symbol;\n  });\n  return result.join('');\n}\nfunction getObfuscateIndexes(value, match) {\n  if (!match) {\n    return Array.from({\n      length: value.length\n    }).map((_, index) => index);\n  }\n  const obfuscateIndexes = [];\n  let matchResult;\n  let count = 0;\n  while ((matchResult = match.exec(value)) !== null && count < value.length) {\n    const start = matchResult.index;\n    const end = match.lastIndex - 1;\n    for (let i = start; i <= end; i++) {\n      obfuscateIndexes.push(i);\n    }\n    count++;\n  }\n  return obfuscateIndexes;\n}\nfunction tuiProvide(provide, useExisting, multi = false) {\n  return {\n    provide,\n    useExisting,\n    multi\n  };\n}\nfunction tuiProvideOptions(provide, options, fallback) {\n  return {\n    provide,\n    useFactory: () => ({\n      ...(inject(provide, {\n        optional: true,\n        skipSelf: true\n      }) || fallback),\n      ...(inject(options, {\n        optional: true\n      }) || options)\n    })\n  };\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nfunction decorateMethod(originalMethod) {\n  let previousArgs = [];\n  let originalFnWasCalledLeastAtOnce = false;\n  let pureValue;\n  return function tuiPureMethodPatched(...args) {\n    const isPure = originalFnWasCalledLeastAtOnce && previousArgs.length === args.length && args.every((arg, index) => arg === previousArgs[index]);\n    if (isPure) {\n      return pureValue;\n    }\n    previousArgs = args;\n    pureValue = originalMethod.apply(this, args);\n    originalFnWasCalledLeastAtOnce = true;\n    return pureValue;\n  };\n}\nfunction decorateGetter(originalGetter, propertyKey, enumerable = true) {\n  return function tuiPureGetterPatched() {\n    const value = originalGetter.call(this);\n    Object.defineProperty(this, propertyKey, {\n      enumerable,\n      value\n    });\n    return value;\n  };\n}\nfunction tuiPure(target, propertyKeyOrContext, descriptor) {\n  if (typeof target === 'function') {\n    const context = propertyKeyOrContext;\n    if (context.kind === 'getter') {\n      return decorateGetter(target, context.name);\n    }\n    if (context.kind === 'method') {\n      return decorateMethod(target);\n    }\n    throw new TuiPureException();\n  }\n  const {\n    get,\n    enumerable,\n    value\n  } = descriptor;\n  const propertyKey = propertyKeyOrContext;\n  if (get) {\n    return {\n      configurable: true,\n      enumerable,\n      get: decorateGetter(get, propertyKey, enumerable)\n    };\n  }\n  if (typeof value !== 'function') {\n    throw new TuiPureException();\n  }\n  const original = value;\n  return {\n    configurable: true,\n    enumerable,\n    get() {\n      let previousArgs = [];\n      let originalFnWasCalledLeastAtOnce = false;\n      let pureValue;\n      const patched = (...args) => {\n        const isPure = originalFnWasCalledLeastAtOnce && previousArgs.length === args.length && args.every((arg, index) => arg === previousArgs[index]);\n        if (isPure) {\n          return pureValue;\n        }\n        previousArgs = args;\n        pureValue = original.apply(this, args);\n        originalFnWasCalledLeastAtOnce = true;\n        return pureValue;\n      };\n      Object.defineProperty(this, propertyKey, {\n        configurable: true,\n        value: patched\n      });\n      return patched;\n    }\n  };\n}\nclass TuiPureException extends Error {\n  constructor() {\n    super(ngDevMode ? 'tuiPure can only be used with functions or getters' : '');\n  }\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Adds 'px' to the number and turns it into a string\n */\nfunction tuiPx(value) {\n  ngDevMode && console.assert(Number.isFinite(value), 'Value must be finite number');\n  return `${value}px`;\n}\nfunction tuiUniqBy(array, key) {\n  return Array.from(array.reduce((map, item) => map.has(item[key]) ? map : map.set(item[key], item), new Map()).values());\n}\nconst MAP = tuiCreateTokenFromFactory(() => {\n  const map = new Map();\n  inject(DestroyRef).onDestroy(() => map.forEach(component => component.destroy()));\n  return map;\n});\nfunction tuiWithStyles(component) {\n  const map = inject(MAP);\n  const environmentInjector = inject(EnvironmentInjector);\n  if (!map.has(component)) {\n    map.set(component, createComponent(component, {\n      environmentInjector\n    }));\n  }\n  return undefined;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPureException, changeDateSeparator, tuiArrayRemove, tuiArrayShallowEquals, tuiArrayToggle, tuiCountFilledControls, tuiCreateToken, tuiCreateTokenFromFactory, tuiDefaultSort, tuiDirectiveBinding, tuiDirectiveListener, tuiDistanceBetweenTouches, tuiEaseInOutQuad, tuiFlatLength, tuiFontSizeWatcher, tuiGetOriginalArrayFromQueryList, tuiIsControlEmpty, tuiIsFalsy, tuiIsNumber, tuiIsObject, tuiIsPresent, tuiIsString, tuiIsValidUrl, tuiMarkControlAsTouchedAndValidate, tuiNullableSame, tuiObfuscate, tuiProvide, tuiProvideOptions, tuiPure, tuiPx, tuiUniqBy, tuiWithStyles };", "map": {"version": 3, "names": ["FormArray", "FormGroup", "tuiToInt", "InjectionToken", "isSignal", "signal", "inject", "effect", "DestroyRef", "EnvironmentInjector", "createComponent", "TUI_ALLOW_SIGNAL_WRITES", "toSignal", "tuiA<PERSON><PERSON><PERSON><PERSON>ove", "array", "index", "slice", "Math", "max", "concat", "tuiArrayShallowEquals", "a", "b", "length", "every", "item", "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "identity", "findIndex", "it", "indexOf", "changeDateSeparator", "dateString", "newDateSeparator", "replaceAll", "tuiIsControlEmpty", "value", "Array", "isArray", "tuiCountFilledControls", "control", "controls", "reduce", "acc", "nestedControl", "Object", "values", "tuiCreateToken", "defaults", "undefined", "tuiCreateTokenFromFactory", "factory", "tuiIsString", "tuiDefaultSort", "x", "y", "localeCompare", "tuiDirectiveBinding", "token", "key", "initial", "options", "self", "result", "directive", "output", "toString", "previous", "set", "ngOnChanges", "emit", "tuiDirectiveListener", "prop", "tuiDistanceBetweenTouches", "touches", "hypot", "clientX", "clientY", "tuiEaseInOutQuad", "t", "ngDevMode", "console", "assert", "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "count", "section", "IFRAME", "BODY", "CLASS", "tuiFontSizeWatcher", "callback", "win", "window", "document", "querySelector", "iframe", "createElement", "resize", "innerWidth", "outerWidth", "devicePixelRatio", "width", "body", "append", "addEventListener", "doc", "contentWindow", "observer", "ResizeObserver", "offsetHeight", "setAttribute", "documentElement", "style", "setProperty", "insertAdjacentText", "repeat", "observe", "disconnect", "remove", "removeEventListener", "tuiGetOriginalArrayFromQueryList", "queryList", "find", "_item", "_index", "originalArray", "tuiIsFalsy", "tuiIsNumber", "tuiIsObject", "tuiIsPresent", "tuiIsValidUrl", "url", "RegExp", "String", "raw", "test", "tuiMarkControlAsTouchedAndValidate", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>ched", "updateValueAndValidity", "tuiNullable<PERSON>ame", "handler", "tuiObfuscate", "symbol", "match", "visible", "obfuscateIndexes", "getObfuscateIndexes", "lastIndex", "split", "join", "from", "map", "_", "matchResult", "exec", "start", "end", "i", "push", "tui<PERSON><PERSON><PERSON>", "provide", "useExisting", "multi", "tuiProvideOptions", "fallback", "useFactory", "optional", "skipSelf", "decorateMethod", "originalMethod", "previousArgs", "originalFnWasCalledLeastAtOnce", "pureValue", "tuiPureMethodPatched", "args", "isPure", "arg", "apply", "decorateGetter", "originalGetter", "propertyKey", "enumerable", "tuiPureGetterPatched", "call", "defineProperty", "tuiPure", "target", "propertyKeyOrContext", "descriptor", "context", "kind", "name", "TuiPureException", "get", "configurable", "original", "patched", "Error", "constructor", "tuiPx", "Number", "isFinite", "tuiUniqBy", "has", "Map", "MAP", "onDestroy", "component", "destroy", "tuiWithStyles", "environmentInjector"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-utils-miscellaneous.mjs"], "sourcesContent": ["import { FormArray, FormGroup } from '@angular/forms';\nimport { tuiToInt } from '@taiga-ui/cdk/utils/math';\nimport { InjectionToken, isSignal, signal, inject, effect, DestroyRef, EnvironmentInjector, createComponent } from '@angular/core';\nimport { TUI_ALLOW_SIGNAL_WRITES } from '@taiga-ui/cdk/constants';\nimport { toSignal } from '@angular/core/rxjs-interop';\n\nfunction tuiArrayRemove(array, index) {\n    return array.slice(0, Math.max(index, 0)).concat(array.slice(Math.max(index + 1, 0)));\n}\n\nfunction tuiArrayShallowEquals(a, b) {\n    return a.length === b.length && a.every((item, index) => item === b[index]);\n}\n\nfunction tuiArrayToggle(array, item, identity) {\n    const index = identity\n        ? array.findIndex((it) => identity(it, item))\n        : array.indexOf(item);\n    return index === -1 ? [...array, item] : tuiArray<PERSON>emove(array, index);\n}\n\n/* internal */\nconst changeDateSeparator = (dateString, newDateSeparator) => dateString.replaceAll(/[^0-9A-Za-zА-Яа-я]/gi, newDateSeparator);\n\nfunction tuiIsControlEmpty({ value = null }) {\n    return value === null || value === '' || (Array.isArray(value) && !value.length);\n}\n\nfunction tuiCountFilledControls(control) {\n    if (control instanceof FormArray) {\n        return control.controls.reduce((acc, nestedControl) => acc + tuiCountFilledControls(nestedControl), 0);\n    }\n    if (control instanceof FormGroup) {\n        return Object.values(control.controls).reduce((acc, nestedControl) => acc + tuiCountFilledControls(nestedControl), 0);\n    }\n    return tuiToInt(!tuiIsControlEmpty(control));\n}\n\nfunction tuiCreateToken(defaults) {\n    return defaults === undefined\n        ? new InjectionToken('')\n        : tuiCreateTokenFromFactory(() => defaults);\n}\nfunction tuiCreateTokenFromFactory(factory) {\n    return factory ? new InjectionToken('', { factory }) : new InjectionToken('');\n}\n\nfunction tuiIsString(value) {\n    return typeof value === 'string';\n}\n\nfunction tuiDefaultSort(x, y) {\n    if (x === y) {\n        return 0;\n    }\n    if (tuiIsString(x) && tuiIsString(y)) {\n        return x.localeCompare(y);\n    }\n    return x > y ? 1 : -1;\n}\n\nfunction tuiDirectiveBinding(token, key, initial, options = { self: true }) {\n    const result = isSignal(initial) ? initial : signal(initial);\n    const directive = inject(token, options);\n    const output = directive[`${key.toString()}Change`];\n    // TODO: Figure out why effects are executed all the time and not just when result changes (check with Angular 18)\n    let previous;\n    effect(() => {\n        const value = result();\n        if (previous === value) {\n            return;\n        }\n        if (isSignal(directive[key])) {\n            directive[key].set(value);\n        }\n        else {\n            directive[key] = value;\n        }\n        directive.ngOnChanges?.({});\n        output?.emit?.(value);\n        previous = value;\n    }, TUI_ALLOW_SIGNAL_WRITES);\n    return result;\n}\n\n/** @deprecated remove in v5 */\nfunction tuiDirectiveListener(token, key, options = { self: true }) {\n    const prop = inject(token, options)?.[key];\n    return isSignal(prop) ? prop : toSignal(prop);\n}\n\nfunction tuiDistanceBetweenTouches({ touches }) {\n    return Math.hypot((touches[0]?.clientX ?? 0) - (touches[1]?.clientX ?? 0), (touches[0]?.clientY ?? 0) - (touches[1]?.clientY ?? 0));\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nfunction tuiEaseInOutQuad(t) {\n    ngDevMode &&\n        console.assert(t >= 0 && t <= 1, 'Input must be between 0 and 1 inclusive but received ', t);\n    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n}\n\n/**\n * Flattens two-dimensional array and calculates resulting length\n *\n * @param array twi dimensional array\n */\nfunction tuiFlatLength(array) {\n    return array.reduce((count, section) => count + section.length, 0);\n}\n\nconst IFRAME = 'position: fixed; visibility: hidden; pointer-events: none';\nconst BODY = 'height: fit-content; line-height: 1em;';\nconst CLASS = 'tui-font-size-watcher';\nfunction tuiFontSizeWatcher(callback, win = window) {\n    if (win.document.querySelector(`.${CLASS}`)) {\n        return () => { };\n    }\n    const iframe = win.document.createElement('iframe');\n    const resize = () => {\n        const { innerWidth, outerWidth, devicePixelRatio } = win;\n        iframe.width = `${innerWidth === outerWidth ? innerWidth : innerWidth / devicePixelRatio}`;\n    };\n    win.document.body.append(iframe);\n    win.addEventListener('resize', resize);\n    const doc = iframe.contentWindow?.document;\n    const observer = new ResizeObserver(() => callback(doc?.body.offsetHeight || 0));\n    iframe.setAttribute('class', CLASS);\n    iframe.setAttribute('style', IFRAME);\n    doc?.documentElement.style.setProperty('font', '-apple-system-body');\n    doc?.body.setAttribute('style', BODY);\n    doc?.body.insertAdjacentText('beforeend', '.'.repeat(1000));\n    observer.observe(doc?.body || iframe);\n    resize();\n    return () => {\n        observer.disconnect();\n        iframe.remove();\n        win.removeEventListener('resize', resize);\n    };\n}\n\n/**\n * Extracts original array from {@link QueryList} rather than\n * creating a copy like {@link QueryList.toArray} does.\n * @param queryList\n * @returns original array from {@link QueryList}.\n */\nfunction tuiGetOriginalArrayFromQueryList(queryList) {\n    let array = [];\n    queryList.find((_item, _index, originalArray) => {\n        array = originalArray;\n        return true;\n    });\n    return array;\n}\n\nfunction tuiIsFalsy(value) {\n    return !value;\n}\n\nfunction tuiIsNumber(value) {\n    return typeof value === 'number';\n}\n\nfunction tuiIsObject(value) {\n    return typeof value === 'object' && !!value;\n}\n\nfunction tuiIsPresent(value) {\n    return value !== null && value !== undefined;\n}\n\n/**\n * @deprecated: drop in v5.0\n */\nfunction tuiIsValidUrl(url) {\n    return new RegExp(String.raw `^([a-zA-Z]+:\\/\\/)?` + // protocol\n        String.raw `((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|localhost|` + // domain name\n        String.raw `((\\d{1,3}\\.){3}\\d{1,3}))` + // OR IP (v4) address\n        String.raw `(\\:\\d+)?(\\/[-a-z\\d%_.~+\\:]*)*` + // port and path\n        String.raw `(\\?[)(;&a-z\\d%_.~+=-]*)?` + // query string\n        String.raw `(\\#[-a-z\\d_]*)?$`, // fragment locator\n    'i').test(url);\n}\n\nfunction tuiMarkControlAsTouchedAndValidate(control) {\n    if (control instanceof FormArray) {\n        control.controls.forEach((nestedControl) => {\n            tuiMarkControlAsTouchedAndValidate(nestedControl);\n        });\n    }\n    if (control instanceof FormGroup) {\n        Object.values(control.controls).forEach((nestedControl) => {\n            tuiMarkControlAsTouchedAndValidate(nestedControl);\n        });\n    }\n    control.markAsTouched();\n    control.updateValueAndValidity();\n}\n\n/**\n * Checks identity for nullable elements.\n *\n * @param a element a\n * @param b element b\n * @param handler called if both elements are not null\n * @return true if either both are null or they pass identity handler\n */\nfunction tuiNullableSame(a, b, handler) {\n    if (a === null) {\n        return b === null;\n    }\n    if (b === null) {\n        return false;\n    }\n    return handler(a, b);\n}\n\n/**\n * Obfuscates a string by replacing certain characters with a symbol.\n *\n * @param value the input string to obfuscate\n * @param symbol the symbol for obfuscation\n * @return the obfuscated string\n *\n * The function determines which characters to obfuscate using a regular expression and the string's length:\n * - 8 or more: show first 2 and last 2 characters\n * - 4 to 7: show first and last character\n * - less than 4: obfuscate all characters\n * - obfuscates only alphanumeric characters\n */\nfunction tuiObfuscate(value, symbol) {\n    if (!value) {\n        return value;\n    }\n    const match = /[\\p{L}\\p{N}]/gu;\n    let visible = 0;\n    let obfuscateIndexes = getObfuscateIndexes(value, match);\n    if (obfuscateIndexes.length >= 8) {\n        visible = 2;\n    }\n    else if (obfuscateIndexes.length >= 4) {\n        visible = 1;\n    }\n    obfuscateIndexes = obfuscateIndexes.slice(visible, obfuscateIndexes.length);\n    const lastIndex = (obfuscateIndexes.length) - visible;\n    obfuscateIndexes = obfuscateIndexes.slice(0, lastIndex < 0 ? 0 : lastIndex);\n    const result = value.split('');\n    obfuscateIndexes.forEach((index) => {\n        result[index] = symbol;\n    });\n    return result.join('');\n}\nfunction getObfuscateIndexes(value, match) {\n    if (!match) {\n        return Array.from({ length: value.length }).map((_, index) => index);\n    }\n    const obfuscateIndexes = [];\n    let matchResult;\n    let count = 0;\n    while ((matchResult = match.exec(value)) !== null && count < value.length) {\n        const start = matchResult.index;\n        const end = match.lastIndex - 1;\n        for (let i = start; i <= end; i++) {\n            obfuscateIndexes.push(i);\n        }\n        count++;\n    }\n    return obfuscateIndexes;\n}\n\nfunction tuiProvide(provide, useExisting, multi = false) {\n    return { provide, useExisting, multi };\n}\n\nfunction tuiProvideOptions(provide, options, fallback) {\n    return {\n        provide,\n        useFactory: () => ({\n            ...(inject(provide, { optional: true, skipSelf: true }) || fallback),\n            ...(inject(options, { optional: true }) || options),\n        }),\n    };\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\nfunction decorateMethod(originalMethod) {\n    let previousArgs = [];\n    let originalFnWasCalledLeastAtOnce = false;\n    let pureValue;\n    return function tuiPureMethodPatched(...args) {\n        const isPure = originalFnWasCalledLeastAtOnce &&\n            previousArgs.length === args.length &&\n            args.every((arg, index) => arg === previousArgs[index]);\n        if (isPure) {\n            return pureValue;\n        }\n        previousArgs = args;\n        pureValue = originalMethod.apply(this, args);\n        originalFnWasCalledLeastAtOnce = true;\n        return pureValue;\n    };\n}\nfunction decorateGetter(originalGetter, propertyKey, enumerable = true) {\n    return function tuiPureGetterPatched() {\n        const value = originalGetter.call(this);\n        Object.defineProperty(this, propertyKey, { enumerable, value });\n        return value;\n    };\n}\nfunction tuiPure(target, propertyKeyOrContext, descriptor) {\n    if (typeof target === 'function') {\n        const context = propertyKeyOrContext;\n        if (context.kind === 'getter') {\n            return decorateGetter(target, context.name);\n        }\n        if (context.kind === 'method') {\n            return decorateMethod(target);\n        }\n        throw new TuiPureException();\n    }\n    const { get, enumerable, value } = descriptor;\n    const propertyKey = propertyKeyOrContext;\n    if (get) {\n        return {\n            configurable: true,\n            enumerable,\n            get: decorateGetter(get, propertyKey, enumerable),\n        };\n    }\n    if (typeof value !== 'function') {\n        throw new TuiPureException();\n    }\n    const original = value;\n    return {\n        configurable: true,\n        enumerable,\n        get() {\n            let previousArgs = [];\n            let originalFnWasCalledLeastAtOnce = false;\n            let pureValue;\n            const patched = (...args) => {\n                const isPure = originalFnWasCalledLeastAtOnce &&\n                    previousArgs.length === args.length &&\n                    args.every((arg, index) => arg === previousArgs[index]);\n                if (isPure) {\n                    return pureValue;\n                }\n                previousArgs = args;\n                pureValue = original.apply(this, args);\n                originalFnWasCalledLeastAtOnce = true;\n                return pureValue;\n            };\n            Object.defineProperty(this, propertyKey, {\n                configurable: true,\n                value: patched,\n            });\n            return patched;\n        },\n    };\n}\nclass TuiPureException extends Error {\n    constructor() {\n        super(ngDevMode ? 'tuiPure can only be used with functions or getters' : '');\n    }\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Adds 'px' to the number and turns it into a string\n */\nfunction tuiPx(value) {\n    ngDevMode && console.assert(Number.isFinite(value), 'Value must be finite number');\n    return `${value}px`;\n}\n\nfunction tuiUniqBy(array, key) {\n    return Array.from(array\n        .reduce((map, item) => (map.has(item[key]) ? map : map.set(item[key], item)), new Map())\n        .values());\n}\n\nconst MAP = tuiCreateTokenFromFactory(() => {\n    const map = new Map();\n    inject(DestroyRef).onDestroy(() => map.forEach((component) => component.destroy()));\n    return map;\n});\nfunction tuiWithStyles(component) {\n    const map = inject(MAP);\n    const environmentInjector = inject(EnvironmentInjector);\n    if (!map.has(component)) {\n        map.set(component, createComponent(component, { environmentInjector }));\n    }\n    return undefined;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiPureException, changeDateSeparator, tuiArrayRemove, tuiArrayShallowEquals, tuiArrayToggle, tuiCountFilledControls, tuiCreateToken, tuiCreateTokenFromFactory, tuiDefaultSort, tuiDirectiveBinding, tuiDirectiveListener, tuiDistanceBetweenTouches, tuiEaseInOutQuad, tuiFlatLength, tuiFontSizeWatcher, tuiGetOriginalArrayFromQueryList, tuiIsControlEmpty, tuiIsFalsy, tuiIsNumber, tuiIsObject, tuiIsPresent, tuiIsString, tuiIsValidUrl, tuiMarkControlAsTouchedAndValidate, tuiNullableSame, tuiObfuscate, tuiProvide, tuiProvideOptions, tuiPure, tuiPx, tuiUniqBy, tuiWithStyles };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACrD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,cAAc,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,mBAAmB,EAAEC,eAAe,QAAQ,eAAe;AAClI,SAASC,uBAAuB,QAAQ,yBAAyB;AACjE,SAASC,QAAQ,QAAQ,4BAA4B;AAErD,SAASC,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAClC,OAAOD,KAAK,CAACE,KAAK,CAAC,CAAC,EAAEC,IAAI,CAACC,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC,CAAC,CAACI,MAAM,CAACL,KAAK,CAACE,KAAK,CAACC,IAAI,CAACC,GAAG,CAACH,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACzF;AAEA,SAASK,qBAAqBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjC,OAAOD,CAAC,CAACE,MAAM,KAAKD,CAAC,CAACC,MAAM,IAAIF,CAAC,CAACG,KAAK,CAAC,CAACC,IAAI,EAAEV,KAAK,KAAKU,IAAI,KAAKH,CAAC,CAACP,KAAK,CAAC,CAAC;AAC/E;AAEA,SAASW,cAAcA,CAACZ,KAAK,EAAEW,IAAI,EAAEE,QAAQ,EAAE;EAC3C,MAAMZ,KAAK,GAAGY,QAAQ,GAChBb,KAAK,CAACc,SAAS,CAAEC,EAAE,IAAKF,QAAQ,CAACE,EAAE,EAAEJ,IAAI,CAAC,CAAC,GAC3CX,KAAK,CAACgB,OAAO,CAACL,IAAI,CAAC;EACzB,OAAOV,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGD,KAAK,EAAEW,IAAI,CAAC,GAAGZ,cAAc,CAACC,KAAK,EAAEC,KAAK,CAAC;AACzE;;AAEA;AACA,MAAMgB,mBAAmB,GAAGA,CAACC,UAAU,EAAEC,gBAAgB,KAAKD,UAAU,CAACE,UAAU,CAAC,sBAAsB,EAAED,gBAAgB,CAAC;AAE7H,SAASE,iBAAiBA,CAAC;EAAEC,KAAK,GAAG;AAAK,CAAC,EAAE;EACzC,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,IAAKC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAI,CAACA,KAAK,CAACb,MAAO;AACpF;AAEA,SAASgB,sBAAsBA,CAACC,OAAO,EAAE;EACrC,IAAIA,OAAO,YAAYxC,SAAS,EAAE;IAC9B,OAAOwC,OAAO,CAACC,QAAQ,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,aAAa,KAAKD,GAAG,GAAGJ,sBAAsB,CAACK,aAAa,CAAC,EAAE,CAAC,CAAC;EAC1G;EACA,IAAIJ,OAAO,YAAYvC,SAAS,EAAE;IAC9B,OAAO4C,MAAM,CAACC,MAAM,CAACN,OAAO,CAACC,QAAQ,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,aAAa,KAAKD,GAAG,GAAGJ,sBAAsB,CAACK,aAAa,CAAC,EAAE,CAAC,CAAC;EACzH;EACA,OAAO1C,QAAQ,CAAC,CAACiC,iBAAiB,CAACK,OAAO,CAAC,CAAC;AAChD;AAEA,SAASO,cAAcA,CAACC,QAAQ,EAAE;EAC9B,OAAOA,QAAQ,KAAKC,SAAS,GACvB,IAAI9C,cAAc,CAAC,EAAE,CAAC,GACtB+C,yBAAyB,CAAC,MAAMF,QAAQ,CAAC;AACnD;AACA,SAASE,yBAAyBA,CAACC,OAAO,EAAE;EACxC,OAAOA,OAAO,GAAG,IAAIhD,cAAc,CAAC,EAAE,EAAE;IAAEgD;EAAQ,CAAC,CAAC,GAAG,IAAIhD,cAAc,CAAC,EAAE,CAAC;AACjF;AAEA,SAASiD,WAAWA,CAAChB,KAAK,EAAE;EACxB,OAAO,OAAOA,KAAK,KAAK,QAAQ;AACpC;AAEA,SAASiB,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC1B,IAAID,CAAC,KAAKC,CAAC,EAAE;IACT,OAAO,CAAC;EACZ;EACA,IAAIH,WAAW,CAACE,CAAC,CAAC,IAAIF,WAAW,CAACG,CAAC,CAAC,EAAE;IAClC,OAAOD,CAAC,CAACE,aAAa,CAACD,CAAC,CAAC;EAC7B;EACA,OAAOD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACzB;AAEA,SAASE,mBAAmBA,CAACC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAEC,OAAO,GAAG;EAAEC,IAAI,EAAE;AAAK,CAAC,EAAE;EACxE,MAAMC,MAAM,GAAG3D,QAAQ,CAACwD,OAAO,CAAC,GAAGA,OAAO,GAAGvD,MAAM,CAACuD,OAAO,CAAC;EAC5D,MAAMI,SAAS,GAAG1D,MAAM,CAACoD,KAAK,EAAEG,OAAO,CAAC;EACxC,MAAMI,MAAM,GAAGD,SAAS,CAAC,GAAGL,GAAG,CAACO,QAAQ,CAAC,CAAC,QAAQ,CAAC;EACnD;EACA,IAAIC,QAAQ;EACZ5D,MAAM,CAAC,MAAM;IACT,MAAM6B,KAAK,GAAG2B,MAAM,CAAC,CAAC;IACtB,IAAII,QAAQ,KAAK/B,KAAK,EAAE;MACpB;IACJ;IACA,IAAIhC,QAAQ,CAAC4D,SAAS,CAACL,GAAG,CAAC,CAAC,EAAE;MAC1BK,SAAS,CAACL,GAAG,CAAC,CAACS,GAAG,CAAChC,KAAK,CAAC;IAC7B,CAAC,MACI;MACD4B,SAAS,CAACL,GAAG,CAAC,GAAGvB,KAAK;IAC1B;IACA4B,SAAS,CAACK,WAAW,GAAG,CAAC,CAAC,CAAC;IAC3BJ,MAAM,EAAEK,IAAI,GAAGlC,KAAK,CAAC;IACrB+B,QAAQ,GAAG/B,KAAK;EACpB,CAAC,EAAEzB,uBAAuB,CAAC;EAC3B,OAAOoD,MAAM;AACjB;;AAEA;AACA,SAASQ,oBAAoBA,CAACb,KAAK,EAAEC,GAAG,EAAEE,OAAO,GAAG;EAAEC,IAAI,EAAE;AAAK,CAAC,EAAE;EAChE,MAAMU,IAAI,GAAGlE,MAAM,CAACoD,KAAK,EAAEG,OAAO,CAAC,GAAGF,GAAG,CAAC;EAC1C,OAAOvD,QAAQ,CAACoE,IAAI,CAAC,GAAGA,IAAI,GAAG5D,QAAQ,CAAC4D,IAAI,CAAC;AACjD;AAEA,SAASC,yBAAyBA,CAAC;EAAEC;AAAQ,CAAC,EAAE;EAC5C,OAAOzD,IAAI,CAAC0D,KAAK,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,EAAEE,OAAO,IAAI,CAAC,KAAKF,OAAO,CAAC,CAAC,CAAC,EAAEE,OAAO,IAAI,CAAC,CAAC,EAAE,CAACF,OAAO,CAAC,CAAC,CAAC,EAAEG,OAAO,IAAI,CAAC,KAAKH,OAAO,CAAC,CAAC,CAAC,EAAEG,OAAO,IAAI,CAAC,CAAC,CAAC;AACvI;;AAEA;AACA,SAASC,gBAAgBA,CAACC,CAAC,EAAE;EACzBC,SAAS,IACLC,OAAO,CAACC,MAAM,CAACH,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,EAAE,uDAAuD,EAAEA,CAAC,CAAC;EAChG,OAAOA,CAAC,GAAG,GAAG,GAAG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAIA,CAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASI,aAAaA,CAACrE,KAAK,EAAE;EAC1B,OAAOA,KAAK,CAAC4B,MAAM,CAAC,CAAC0C,KAAK,EAAEC,OAAO,KAAKD,KAAK,GAAGC,OAAO,CAAC9D,MAAM,EAAE,CAAC,CAAC;AACtE;AAEA,MAAM+D,MAAM,GAAG,2DAA2D;AAC1E,MAAMC,IAAI,GAAG,wCAAwC;AACrD,MAAMC,KAAK,GAAG,uBAAuB;AACrC,SAASC,kBAAkBA,CAACC,QAAQ,EAAEC,GAAG,GAAGC,MAAM,EAAE;EAChD,IAAID,GAAG,CAACE,QAAQ,CAACC,aAAa,CAAC,IAAIN,KAAK,EAAE,CAAC,EAAE;IACzC,OAAO,MAAM,CAAE,CAAC;EACpB;EACA,MAAMO,MAAM,GAAGJ,GAAG,CAACE,QAAQ,CAACG,aAAa,CAAC,QAAQ,CAAC;EACnD,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACjB,MAAM;MAAEC,UAAU;MAAEC,UAAU;MAAEC;IAAiB,CAAC,GAAGT,GAAG;IACxDI,MAAM,CAACM,KAAK,GAAG,GAAGH,UAAU,KAAKC,UAAU,GAAGD,UAAU,GAAGA,UAAU,GAAGE,gBAAgB,EAAE;EAC9F,CAAC;EACDT,GAAG,CAACE,QAAQ,CAACS,IAAI,CAACC,MAAM,CAACR,MAAM,CAAC;EAChCJ,GAAG,CAACa,gBAAgB,CAAC,QAAQ,EAAEP,MAAM,CAAC;EACtC,MAAMQ,GAAG,GAAGV,MAAM,CAACW,aAAa,EAAEb,QAAQ;EAC1C,MAAMc,QAAQ,GAAG,IAAIC,cAAc,CAAC,MAAMlB,QAAQ,CAACe,GAAG,EAAEH,IAAI,CAACO,YAAY,IAAI,CAAC,CAAC,CAAC;EAChFd,MAAM,CAACe,YAAY,CAAC,OAAO,EAAEtB,KAAK,CAAC;EACnCO,MAAM,CAACe,YAAY,CAAC,OAAO,EAAExB,MAAM,CAAC;EACpCmB,GAAG,EAAEM,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,MAAM,EAAE,oBAAoB,CAAC;EACpER,GAAG,EAAEH,IAAI,CAACQ,YAAY,CAAC,OAAO,EAAEvB,IAAI,CAAC;EACrCkB,GAAG,EAAEH,IAAI,CAACY,kBAAkB,CAAC,WAAW,EAAE,GAAG,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EAC3DR,QAAQ,CAACS,OAAO,CAACX,GAAG,EAAEH,IAAI,IAAIP,MAAM,CAAC;EACrCE,MAAM,CAAC,CAAC;EACR,OAAO,MAAM;IACTU,QAAQ,CAACU,UAAU,CAAC,CAAC;IACrBtB,MAAM,CAACuB,MAAM,CAAC,CAAC;IACf3B,GAAG,CAAC4B,mBAAmB,CAAC,QAAQ,EAAEtB,MAAM,CAAC;EAC7C,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuB,gCAAgCA,CAACC,SAAS,EAAE;EACjD,IAAI3G,KAAK,GAAG,EAAE;EACd2G,SAAS,CAACC,IAAI,CAAC,CAACC,KAAK,EAAEC,MAAM,EAAEC,aAAa,KAAK;IAC7C/G,KAAK,GAAG+G,aAAa;IACrB,OAAO,IAAI;EACf,CAAC,CAAC;EACF,OAAO/G,KAAK;AAChB;AAEA,SAASgH,UAAUA,CAAC1F,KAAK,EAAE;EACvB,OAAO,CAACA,KAAK;AACjB;AAEA,SAAS2F,WAAWA,CAAC3F,KAAK,EAAE;EACxB,OAAO,OAAOA,KAAK,KAAK,QAAQ;AACpC;AAEA,SAAS4F,WAAWA,CAAC5F,KAAK,EAAE;EACxB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC,CAACA,KAAK;AAC/C;AAEA,SAAS6F,YAAYA,CAAC7F,KAAK,EAAE;EACzB,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKa,SAAS;AAChD;;AAEA;AACA;AACA;AACA,SAASiF,aAAaA,CAACC,GAAG,EAAE;EACxB,OAAO,IAAIC,MAAM,CAACC,MAAM,CAACC,GAAI,oBAAoB;EAAG;EAChDD,MAAM,CAACC,GAAI,wDAAwD;EAAG;EACtED,MAAM,CAACC,GAAI,0BAA0B;EAAG;EACxCD,MAAM,CAACC,GAAI,+BAA+B;EAAG;EAC7CD,MAAM,CAACC,GAAI,0BAA0B;EAAG;EACxCD,MAAM,CAACC,GAAI,kBAAkB;EAAE;EACnC,GAAG,CAAC,CAACC,IAAI,CAACJ,GAAG,CAAC;AAClB;AAEA,SAASK,kCAAkCA,CAAChG,OAAO,EAAE;EACjD,IAAIA,OAAO,YAAYxC,SAAS,EAAE;IAC9BwC,OAAO,CAACC,QAAQ,CAACgG,OAAO,CAAE7F,aAAa,IAAK;MACxC4F,kCAAkC,CAAC5F,aAAa,CAAC;IACrD,CAAC,CAAC;EACN;EACA,IAAIJ,OAAO,YAAYvC,SAAS,EAAE;IAC9B4C,MAAM,CAACC,MAAM,CAACN,OAAO,CAACC,QAAQ,CAAC,CAACgG,OAAO,CAAE7F,aAAa,IAAK;MACvD4F,kCAAkC,CAAC5F,aAAa,CAAC;IACrD,CAAC,CAAC;EACN;EACAJ,OAAO,CAACkG,aAAa,CAAC,CAAC;EACvBlG,OAAO,CAACmG,sBAAsB,CAAC,CAAC;AACpC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACvH,CAAC,EAAEC,CAAC,EAAEuH,OAAO,EAAE;EACpC,IAAIxH,CAAC,KAAK,IAAI,EAAE;IACZ,OAAOC,CAAC,KAAK,IAAI;EACrB;EACA,IAAIA,CAAC,KAAK,IAAI,EAAE;IACZ,OAAO,KAAK;EAChB;EACA,OAAOuH,OAAO,CAACxH,CAAC,EAAEC,CAAC,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwH,YAAYA,CAAC1G,KAAK,EAAE2G,MAAM,EAAE;EACjC,IAAI,CAAC3G,KAAK,EAAE;IACR,OAAOA,KAAK;EAChB;EACA,MAAM4G,KAAK,GAAG,gBAAgB;EAC9B,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,gBAAgB,GAAGC,mBAAmB,CAAC/G,KAAK,EAAE4G,KAAK,CAAC;EACxD,IAAIE,gBAAgB,CAAC3H,MAAM,IAAI,CAAC,EAAE;IAC9B0H,OAAO,GAAG,CAAC;EACf,CAAC,MACI,IAAIC,gBAAgB,CAAC3H,MAAM,IAAI,CAAC,EAAE;IACnC0H,OAAO,GAAG,CAAC;EACf;EACAC,gBAAgB,GAAGA,gBAAgB,CAAClI,KAAK,CAACiI,OAAO,EAAEC,gBAAgB,CAAC3H,MAAM,CAAC;EAC3E,MAAM6H,SAAS,GAAIF,gBAAgB,CAAC3H,MAAM,GAAI0H,OAAO;EACrDC,gBAAgB,GAAGA,gBAAgB,CAAClI,KAAK,CAAC,CAAC,EAAEoI,SAAS,GAAG,CAAC,GAAG,CAAC,GAAGA,SAAS,CAAC;EAC3E,MAAMrF,MAAM,GAAG3B,KAAK,CAACiH,KAAK,CAAC,EAAE,CAAC;EAC9BH,gBAAgB,CAACT,OAAO,CAAE1H,KAAK,IAAK;IAChCgD,MAAM,CAAChD,KAAK,CAAC,GAAGgI,MAAM;EAC1B,CAAC,CAAC;EACF,OAAOhF,MAAM,CAACuF,IAAI,CAAC,EAAE,CAAC;AAC1B;AACA,SAASH,mBAAmBA,CAAC/G,KAAK,EAAE4G,KAAK,EAAE;EACvC,IAAI,CAACA,KAAK,EAAE;IACR,OAAO3G,KAAK,CAACkH,IAAI,CAAC;MAAEhI,MAAM,EAAEa,KAAK,CAACb;IAAO,CAAC,CAAC,CAACiI,GAAG,CAAC,CAACC,CAAC,EAAE1I,KAAK,KAAKA,KAAK,CAAC;EACxE;EACA,MAAMmI,gBAAgB,GAAG,EAAE;EAC3B,IAAIQ,WAAW;EACf,IAAItE,KAAK,GAAG,CAAC;EACb,OAAO,CAACsE,WAAW,GAAGV,KAAK,CAACW,IAAI,CAACvH,KAAK,CAAC,MAAM,IAAI,IAAIgD,KAAK,GAAGhD,KAAK,CAACb,MAAM,EAAE;IACvE,MAAMqI,KAAK,GAAGF,WAAW,CAAC3I,KAAK;IAC/B,MAAM8I,GAAG,GAAGb,KAAK,CAACI,SAAS,GAAG,CAAC;IAC/B,KAAK,IAAIU,CAAC,GAAGF,KAAK,EAAEE,CAAC,IAAID,GAAG,EAAEC,CAAC,EAAE,EAAE;MAC/BZ,gBAAgB,CAACa,IAAI,CAACD,CAAC,CAAC;IAC5B;IACA1E,KAAK,EAAE;EACX;EACA,OAAO8D,gBAAgB;AAC3B;AAEA,SAASc,UAAUA,CAACC,OAAO,EAAEC,WAAW,EAAEC,KAAK,GAAG,KAAK,EAAE;EACrD,OAAO;IAAEF,OAAO;IAAEC,WAAW;IAAEC;EAAM,CAAC;AAC1C;AAEA,SAASC,iBAAiBA,CAACH,OAAO,EAAEpG,OAAO,EAAEwG,QAAQ,EAAE;EACnD,OAAO;IACHJ,OAAO;IACPK,UAAU,EAAEA,CAAA,MAAO;MACf,IAAIhK,MAAM,CAAC2J,OAAO,EAAE;QAAEM,QAAQ,EAAE,IAAI;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC,IAAIH,QAAQ,CAAC;MACpE,IAAI/J,MAAM,CAACuD,OAAO,EAAE;QAAE0G,QAAQ,EAAE;MAAK,CAAC,CAAC,IAAI1G,OAAO;IACtD,CAAC;EACL,CAAC;AACL;;AAEA;AACA,SAAS4G,cAAcA,CAACC,cAAc,EAAE;EACpC,IAAIC,YAAY,GAAG,EAAE;EACrB,IAAIC,8BAA8B,GAAG,KAAK;EAC1C,IAAIC,SAAS;EACb,OAAO,SAASC,oBAAoBA,CAAC,GAAGC,IAAI,EAAE;IAC1C,MAAMC,MAAM,GAAGJ,8BAA8B,IACzCD,YAAY,CAACpJ,MAAM,KAAKwJ,IAAI,CAACxJ,MAAM,IACnCwJ,IAAI,CAACvJ,KAAK,CAAC,CAACyJ,GAAG,EAAElK,KAAK,KAAKkK,GAAG,KAAKN,YAAY,CAAC5J,KAAK,CAAC,CAAC;IAC3D,IAAIiK,MAAM,EAAE;MACR,OAAOH,SAAS;IACpB;IACAF,YAAY,GAAGI,IAAI;IACnBF,SAAS,GAAGH,cAAc,CAACQ,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IAC5CH,8BAA8B,GAAG,IAAI;IACrC,OAAOC,SAAS;EACpB,CAAC;AACL;AACA,SAASM,cAAcA,CAACC,cAAc,EAAEC,WAAW,EAAEC,UAAU,GAAG,IAAI,EAAE;EACpE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IACnC,MAAMnJ,KAAK,GAAGgJ,cAAc,CAACI,IAAI,CAAC,IAAI,CAAC;IACvC3I,MAAM,CAAC4I,cAAc,CAAC,IAAI,EAAEJ,WAAW,EAAE;MAAEC,UAAU;MAAElJ;IAAM,CAAC,CAAC;IAC/D,OAAOA,KAAK;EAChB,CAAC;AACL;AACA,SAASsJ,OAAOA,CAACC,MAAM,EAAEC,oBAAoB,EAAEC,UAAU,EAAE;EACvD,IAAI,OAAOF,MAAM,KAAK,UAAU,EAAE;IAC9B,MAAMG,OAAO,GAAGF,oBAAoB;IACpC,IAAIE,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE;MAC3B,OAAOZ,cAAc,CAACQ,MAAM,EAAEG,OAAO,CAACE,IAAI,CAAC;IAC/C;IACA,IAAIF,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE;MAC3B,OAAOtB,cAAc,CAACkB,MAAM,CAAC;IACjC;IACA,MAAM,IAAIM,gBAAgB,CAAC,CAAC;EAChC;EACA,MAAM;IAAEC,GAAG;IAAEZ,UAAU;IAAElJ;EAAM,CAAC,GAAGyJ,UAAU;EAC7C,MAAMR,WAAW,GAAGO,oBAAoB;EACxC,IAAIM,GAAG,EAAE;IACL,OAAO;MACHC,YAAY,EAAE,IAAI;MAClBb,UAAU;MACVY,GAAG,EAAEf,cAAc,CAACe,GAAG,EAAEb,WAAW,EAAEC,UAAU;IACpD,CAAC;EACL;EACA,IAAI,OAAOlJ,KAAK,KAAK,UAAU,EAAE;IAC7B,MAAM,IAAI6J,gBAAgB,CAAC,CAAC;EAChC;EACA,MAAMG,QAAQ,GAAGhK,KAAK;EACtB,OAAO;IACH+J,YAAY,EAAE,IAAI;IAClBb,UAAU;IACVY,GAAGA,CAAA,EAAG;MACF,IAAIvB,YAAY,GAAG,EAAE;MACrB,IAAIC,8BAA8B,GAAG,KAAK;MAC1C,IAAIC,SAAS;MACb,MAAMwB,OAAO,GAAGA,CAAC,GAAGtB,IAAI,KAAK;QACzB,MAAMC,MAAM,GAAGJ,8BAA8B,IACzCD,YAAY,CAACpJ,MAAM,KAAKwJ,IAAI,CAACxJ,MAAM,IACnCwJ,IAAI,CAACvJ,KAAK,CAAC,CAACyJ,GAAG,EAAElK,KAAK,KAAKkK,GAAG,KAAKN,YAAY,CAAC5J,KAAK,CAAC,CAAC;QAC3D,IAAIiK,MAAM,EAAE;UACR,OAAOH,SAAS;QACpB;QACAF,YAAY,GAAGI,IAAI;QACnBF,SAAS,GAAGuB,QAAQ,CAAClB,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;QACtCH,8BAA8B,GAAG,IAAI;QACrC,OAAOC,SAAS;MACpB,CAAC;MACDhI,MAAM,CAAC4I,cAAc,CAAC,IAAI,EAAEJ,WAAW,EAAE;QACrCc,YAAY,EAAE,IAAI;QAClB/J,KAAK,EAAEiK;MACX,CAAC,CAAC;MACF,OAAOA,OAAO;IAClB;EACJ,CAAC;AACL;AACA,MAAMJ,gBAAgB,SAASK,KAAK,CAAC;EACjCC,WAAWA,CAAA,EAAG;IACV,KAAK,CAACvH,SAAS,GAAG,oDAAoD,GAAG,EAAE,CAAC;EAChF;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASwH,KAAKA,CAACpK,KAAK,EAAE;EAClB4C,SAAS,IAAIC,OAAO,CAACC,MAAM,CAACuH,MAAM,CAACC,QAAQ,CAACtK,KAAK,CAAC,EAAE,6BAA6B,CAAC;EAClF,OAAO,GAAGA,KAAK,IAAI;AACvB;AAEA,SAASuK,SAASA,CAAC7L,KAAK,EAAE6C,GAAG,EAAE;EAC3B,OAAOtB,KAAK,CAACkH,IAAI,CAACzI,KAAK,CAClB4B,MAAM,CAAC,CAAC8G,GAAG,EAAE/H,IAAI,KAAM+H,GAAG,CAACoD,GAAG,CAACnL,IAAI,CAACkC,GAAG,CAAC,CAAC,GAAG6F,GAAG,GAAGA,GAAG,CAACpF,GAAG,CAAC3C,IAAI,CAACkC,GAAG,CAAC,EAAElC,IAAI,CAAE,EAAE,IAAIoL,GAAG,CAAC,CAAC,CAAC,CACvF/J,MAAM,CAAC,CAAC,CAAC;AAClB;AAEA,MAAMgK,GAAG,GAAG5J,yBAAyB,CAAC,MAAM;EACxC,MAAMsG,GAAG,GAAG,IAAIqD,GAAG,CAAC,CAAC;EACrBvM,MAAM,CAACE,UAAU,CAAC,CAACuM,SAAS,CAAC,MAAMvD,GAAG,CAACf,OAAO,CAAEuE,SAAS,IAAKA,SAAS,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;EACnF,OAAOzD,GAAG;AACd,CAAC,CAAC;AACF,SAAS0D,aAAaA,CAACF,SAAS,EAAE;EAC9B,MAAMxD,GAAG,GAAGlJ,MAAM,CAACwM,GAAG,CAAC;EACvB,MAAMK,mBAAmB,GAAG7M,MAAM,CAACG,mBAAmB,CAAC;EACvD,IAAI,CAAC+I,GAAG,CAACoD,GAAG,CAACI,SAAS,CAAC,EAAE;IACrBxD,GAAG,CAACpF,GAAG,CAAC4I,SAAS,EAAEtM,eAAe,CAACsM,SAAS,EAAE;MAAEG;IAAoB,CAAC,CAAC,CAAC;EAC3E;EACA,OAAOlK,SAAS;AACpB;;AAEA;AACA;AACA;;AAEA,SAASgJ,gBAAgB,EAAElK,mBAAmB,EAAElB,cAAc,EAAEO,qBAAqB,EAAEM,cAAc,EAAEa,sBAAsB,EAAEQ,cAAc,EAAEG,yBAAyB,EAAEG,cAAc,EAAEI,mBAAmB,EAAEc,oBAAoB,EAAEE,yBAAyB,EAAEK,gBAAgB,EAAEK,aAAa,EAAEM,kBAAkB,EAAE+B,gCAAgC,EAAErF,iBAAiB,EAAE2F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAE7E,WAAW,EAAE8E,aAAa,EAAEM,kCAAkC,EAAEI,eAAe,EAAEE,YAAY,EAAEkB,UAAU,EAAEI,iBAAiB,EAAEsB,OAAO,EAAEc,KAAK,EAAEG,SAAS,EAAEO,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}