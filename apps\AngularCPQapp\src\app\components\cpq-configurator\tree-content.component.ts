import { NgClass, CurrencyPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { TuiTreeItemContent } from '@taiga-ui/kit';

@Component({
  selector: 'app-tree-content',
  standalone: true,
  imports: [NgClass, CurrencyPipe],
  template: `
    <div class="tui-tree-content">
      <i 
        class="bi me-2"
        [ngClass]="icon"
      ></i>
      <span class="tui-tree-content-label">{{ context.$implicit.text }}</span>
      <span class="tui-tree-content-price ms-auto">{{ context.$implicit.price | currency:'EUR' }}</span>
      <div class="tui-tree-content-checkbox ms-2">
        <input 
          type="checkbox" 
          class="form-check-input"
          [checked]="context.$implicit.selected"
          (change)="onCheckboxChange($event)"
        >
      </div>
    </div>
  `,
  styles: [`
    .tui-tree-content {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }
    
    .tui-tree-content:hover {
      background-color: #f8f9fa;
    }
    
    .tui-tree-content-label {
      flex: 1;
    }
    
    .tui-tree-content-price {
      font-weight: bold;
      color: #28a745;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TreeContentComponent extends TuiTreeItemContent {
  get icon(): string {
    return this.isExpandable ? 'bi-folder-fill text-warning' : 'bi-file-earmark text-primary';
  }

  onCheckboxChange(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    // Émettre un événement personnalisé pour gérer la sélection
    this.context.$implicit.selected = checked;
  }
}