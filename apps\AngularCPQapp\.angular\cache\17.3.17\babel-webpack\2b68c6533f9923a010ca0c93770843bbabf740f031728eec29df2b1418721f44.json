{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, signal, ChangeDetectorRef, computed, untracked, Directive, Input, INJECTOR, ViewContainerRef, ViewChild, Injectable } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { NgControl, NgModel } from '@angular/forms';\nimport { EMPTY_FUNCTION } from '@taiga-ui/cdk/constants';\nimport { TUI_FALLBACK_VALUE } from '@taiga-ui/cdk/tokens';\nimport { tuiProvide } from '@taiga-ui/cdk/utils';\nimport { identity, Subject, delay, startWith, map, filter, distinctUntilChanged, switchMap, merge, EMPTY } from 'rxjs';\nconst _c0 = [\"viewContainer\"];\nclass TuiValueTransformer {}\nfunction tuiValueTransformerFrom(token) {\n  return {\n    provide: TuiValueTransformer,\n    useFactory: () => inject(token).valueTransformer\n  };\n}\nclass TuiNonNullableValueTransformer extends TuiValueTransformer {\n  fromControlValue(value) {\n    this.prevValue = value;\n    return value;\n  }\n  toControlValue(value) {\n    this.prevValue = value ?? this.prevValue;\n    return this.prevValue;\n  }\n}\nconst TUI_IDENTITY_VALUE_TRANSFORMER = {\n  fromControlValue: identity,\n  toControlValue: identity\n};\nconst FLAGS = {\n  self: true,\n  optional: true\n};\n/**\n * Basic ControlValueAccessor class to build form components upon\n */\nclass TuiControl {\n  constructor() {\n    this.fallback = inject(TUI_FALLBACK_VALUE, FLAGS);\n    this.refresh$ = new Subject();\n    this.pseudoInvalid = signal(null);\n    this.internal = signal(this.fallback);\n    this.control = inject(NgControl, {\n      self: true\n    });\n    this.cdr = inject(ChangeDetectorRef);\n    this.transformer = inject(TuiValueTransformer, FLAGS) ?? TUI_IDENTITY_VALUE_TRANSFORMER;\n    this.value = computed(() => this.internal() ?? this.fallback);\n    this.readOnly = signal(false);\n    this.touched = signal(false);\n    this.status = signal(undefined);\n    this.disabled = computed(() => this.status() === 'DISABLED');\n    this.interactive = computed(() => !this.disabled() && !this.readOnly());\n    this.invalid = computed(() => this.pseudoInvalid() !== null ? !!this.pseudoInvalid() && this.interactive() : this.interactive() && this.touched() && this.status() === 'INVALID');\n    this.mode = computed(() =>\n    // eslint-disable-next-line no-nested-ternary\n    this.readOnly() ? 'readonly' : this.invalid() ? 'invalid' : 'valid');\n    this.onTouched = EMPTY_FUNCTION;\n    this.onChange = EMPTY_FUNCTION;\n    this.control.valueAccessor = this;\n    this.refresh$.pipe(delay(0), startWith(null), map(() => this.control.control), filter(Boolean), distinctUntilChanged(), switchMap(c => merge(c.valueChanges, c.statusChanges, c.events || EMPTY).pipe(startWith(null))), takeUntilDestroyed()).subscribe(() => this.update());\n  }\n  set readOnlySetter(readOnly) {\n    this.readOnly.set(readOnly);\n  }\n  set invalidSetter(invalid) {\n    this.pseudoInvalid.set(invalid);\n  }\n  registerOnChange(onChange) {\n    this.refresh$.next();\n    this.onChange = value => {\n      const internal = untracked(() => this.internal());\n      if (value === internal) {\n        return;\n      }\n      onChange(this.transformer.toControlValue(value));\n      this.internal.set(value);\n      this.update();\n    };\n  }\n  registerOnTouched(onTouched) {\n    this.onTouched = () => {\n      onTouched();\n      this.update();\n    };\n  }\n  setDisabledState() {\n    this.update();\n  }\n  writeValue(value) {\n    // TODO: https://github.com/angular/angular/issues/14988\n    const safe = this.control instanceof NgModel ? this.control.model : value;\n    this.internal.set(this.transformer.fromControlValue(safe));\n    this.update();\n  }\n  update() {\n    this.status.set(this.control.control?.status);\n    this.touched.set(!!this.control.control?.touched);\n    this.cdr.markForCheck();\n  }\n  static {\n    this.ɵfac = function TuiControl_Factory(t) {\n      return new (t || TuiControl)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiControl,\n      inputs: {\n        readOnlySetter: [i0.ɵɵInputFlags.None, \"readOnly\", \"readOnlySetter\"],\n        invalidSetter: [i0.ɵɵInputFlags.None, \"invalid\", \"invalidSetter\"]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiControl, [{\n    type: Directive\n  }], function () {\n    return [];\n  }, {\n    readOnlySetter: [{\n      type: Input,\n      args: ['readOnly']\n    }],\n    invalidSetter: [{\n      type: Input,\n      args: ['invalid']\n    }]\n  });\n})();\nfunction tuiAsControl(control) {\n  return tuiProvide(TuiControl, control);\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Abstract class for host element for dynamically created portals.\n */\nclass TuiPortals {\n  constructor() {\n    this.injector = inject(INJECTOR);\n    this.nothing = inject(TuiPortalService).attach(this);\n  }\n  addComponentChild(component) {\n    const injector = component.createInjector(this.injector);\n    const ref = this.vcr.createComponent(component.component, {\n      injector\n    });\n    ref.changeDetectorRef.detectChanges();\n    return ref;\n  }\n  addTemplateChild(templateRef, context) {\n    return this.vcr.createEmbeddedView(templateRef, context);\n  }\n  static {\n    this.ɵfac = function TuiPortals_Factory(t) {\n      return new (t || TuiPortals)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiPortals,\n      viewQuery: function TuiPortals_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5, ViewContainerRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.vcr = _t.first);\n        }\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPortals, [{\n    type: Directive\n  }], null, {\n    vcr: [{\n      type: ViewChild,\n      args: ['viewContainer', {\n        read: ViewContainerRef\n      }]\n    }]\n  });\n})();\n/**\n * Abstract service for displaying portals\n */\nclass TuiPortalService {\n  attach(host) {\n    this.host = host;\n  }\n  add(component) {\n    return this.safeHost.addComponentChild(component);\n  }\n  remove({\n    hostView\n  }) {\n    this.removeTemplate(hostView);\n  }\n  addTemplate(templateRef, context) {\n    return this.safeHost.addTemplateChild(templateRef, context);\n  }\n  removeTemplate(viewRef) {\n    if (!viewRef.destroyed) {\n      viewRef.destroy();\n    }\n  }\n  get safeHost() {\n    if (!this.host) {\n      throw new TuiNoHostException();\n    }\n    return this.host;\n  }\n  static {\n    this.ɵfac = function TuiPortalService_Factory(t) {\n      return new (t || TuiPortalService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiPortalService,\n      factory: TuiPortalService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiPortalService, [{\n    type: Injectable\n  }], null, null);\n})();\nfunction tuiAsPortal(portal) {\n  return tuiProvide(TuiPortalService, portal);\n}\nclass TuiNoHostException extends Error {\n  constructor() {\n    super(ngDevMode ? 'Portals cannot be used without TuiPortalHostComponent' : '');\n  }\n}\nclass TuiValidationError {\n  constructor(message, context = {}) {\n    this.message = message;\n    this.context = context;\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_IDENTITY_VALUE_TRANSFORMER, TuiControl, TuiNoHostException, TuiNonNullableValueTransformer, TuiPortalService, TuiPortals, TuiValidationError, TuiValueTransformer, tuiAsControl, tuiAsPortal, tuiValueTransformerFrom };", "map": {"version": 3, "names": ["i0", "inject", "signal", "ChangeDetectorRef", "computed", "untracked", "Directive", "Input", "INJECTOR", "ViewContainerRef", "ViewChild", "Injectable", "takeUntilDestroyed", "NgControl", "NgModel", "EMPTY_FUNCTION", "TUI_FALLBACK_VALUE", "tui<PERSON><PERSON><PERSON>", "identity", "Subject", "delay", "startWith", "map", "filter", "distinctUntilChanged", "switchMap", "merge", "EMPTY", "_c0", "TuiValueTransformer", "tuiValueTransformerFrom", "token", "provide", "useFactory", "valueTransformer", "TuiNonNullableValueTransformer", "fromControlValue", "value", "prevValue", "toControlValue", "TUI_IDENTITY_VALUE_TRANSFORMER", "FLAGS", "self", "optional", "TuiControl", "constructor", "fallback", "refresh$", "pseudoInvalid", "internal", "control", "cdr", "transformer", "readOnly", "touched", "status", "undefined", "disabled", "interactive", "invalid", "mode", "onTouched", "onChange", "valueAccessor", "pipe", "Boolean", "c", "valueChanges", "statusChanges", "events", "subscribe", "update", "readOnlySetter", "set", "invalidSetter", "registerOnChange", "next", "registerOnTouched", "setDisabledState", "writeValue", "safe", "model", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵfac", "TuiControl_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "inputs", "ɵɵInputFlags", "None", "ngDevMode", "ɵsetClassMetadata", "args", "tuiAsControl", "TuiPortals", "injector", "nothing", "TuiPortalService", "attach", "addComponentChild", "component", "createInjector", "ref", "vcr", "createComponent", "changeDetectorRef", "detectChanges", "addTemplateChild", "templateRef", "context", "createEmbeddedView", "TuiPortals_Factory", "viewQuery", "TuiPortals_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "read", "host", "add", "safeHost", "remove", "<PERSON><PERSON><PERSON><PERSON>", "removeTemplate", "addTemplate", "viewRef", "destroyed", "destroy", "TuiNoHostException", "TuiPortalService_Factory", "ɵprov", "ɵɵdefineInjectable", "factory", "tui<PERSON><PERSON><PERSON><PERSON>", "portal", "Error", "TuiValidationError", "message"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-classes.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, signal, ChangeDetectorRef, computed, untracked, Directive, Input, INJECTOR, ViewContainerRef, ViewChild, Injectable } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { NgControl, NgModel } from '@angular/forms';\nimport { EMPTY_FUNCTION } from '@taiga-ui/cdk/constants';\nimport { TUI_FALLBACK_VALUE } from '@taiga-ui/cdk/tokens';\nimport { tuiProvide } from '@taiga-ui/cdk/utils';\nimport { identity, Subject, delay, startWith, map, filter, distinctUntilChanged, switchMap, merge, EMPTY } from 'rxjs';\n\nclass TuiValueTransformer {\n}\nfunction tuiValueTransformerFrom(token) {\n    return {\n        provide: TuiValueTransformer,\n        useFactory: () => inject(token).valueTransformer,\n    };\n}\nclass TuiNonNullableValueTransformer extends TuiValueTransformer {\n    fromControlValue(value) {\n        this.prevValue = value;\n        return value;\n    }\n    toControlValue(value) {\n        this.prevValue = value ?? this.prevValue;\n        return this.prevValue;\n    }\n}\nconst TUI_IDENTITY_VALUE_TRANSFORMER = {\n    fromControlValue: identity,\n    toControlValue: identity,\n};\n\nconst FLAGS = { self: true, optional: true };\n/**\n * Basic ControlValueAccessor class to build form components upon\n */\nclass TuiControl {\n    constructor() {\n        this.fallback = inject(TUI_FALLBACK_VALUE, FLAGS);\n        this.refresh$ = new Subject();\n        this.pseudoInvalid = signal(null);\n        this.internal = signal(this.fallback);\n        this.control = inject(NgControl, { self: true });\n        this.cdr = inject(ChangeDetectorRef);\n        this.transformer = inject(TuiValueTransformer, FLAGS) ?? TUI_IDENTITY_VALUE_TRANSFORMER;\n        this.value = computed(() => this.internal() ?? this.fallback);\n        this.readOnly = signal(false);\n        this.touched = signal(false);\n        this.status = signal(undefined);\n        this.disabled = computed(() => this.status() === 'DISABLED');\n        this.interactive = computed(() => !this.disabled() && !this.readOnly());\n        this.invalid = computed(() => this.pseudoInvalid() !== null\n            ? !!this.pseudoInvalid() && this.interactive()\n            : this.interactive() && this.touched() && this.status() === 'INVALID');\n        this.mode = computed(() => \n        // eslint-disable-next-line no-nested-ternary\n        this.readOnly() ? 'readonly' : this.invalid() ? 'invalid' : 'valid');\n        this.onTouched = EMPTY_FUNCTION;\n        this.onChange = EMPTY_FUNCTION;\n        this.control.valueAccessor = this;\n        this.refresh$\n            .pipe(delay(0), startWith(null), map(() => this.control.control), filter(Boolean), distinctUntilChanged(), switchMap((c) => merge(c.valueChanges, c.statusChanges, c.events || EMPTY).pipe(startWith(null))), takeUntilDestroyed())\n            .subscribe(() => this.update());\n    }\n    set readOnlySetter(readOnly) {\n        this.readOnly.set(readOnly);\n    }\n    set invalidSetter(invalid) {\n        this.pseudoInvalid.set(invalid);\n    }\n    registerOnChange(onChange) {\n        this.refresh$.next();\n        this.onChange = (value) => {\n            const internal = untracked(() => this.internal());\n            if (value === internal) {\n                return;\n            }\n            onChange(this.transformer.toControlValue(value));\n            this.internal.set(value);\n            this.update();\n        };\n    }\n    registerOnTouched(onTouched) {\n        this.onTouched = () => {\n            onTouched();\n            this.update();\n        };\n    }\n    setDisabledState() {\n        this.update();\n    }\n    writeValue(value) {\n        // TODO: https://github.com/angular/angular/issues/14988\n        const safe = this.control instanceof NgModel ? this.control.model : value;\n        this.internal.set(this.transformer.fromControlValue(safe));\n        this.update();\n    }\n    update() {\n        this.status.set(this.control.control?.status);\n        this.touched.set(!!this.control.control?.touched);\n        this.cdr.markForCheck();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiControl, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiControl, inputs: { readOnlySetter: [\"readOnly\", \"readOnlySetter\"], invalidSetter: [\"invalid\", \"invalidSetter\"] }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiControl, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return []; }, propDecorators: { readOnlySetter: [{\n                type: Input,\n                args: ['readOnly']\n            }], invalidSetter: [{\n                type: Input,\n                args: ['invalid']\n            }] } });\nfunction tuiAsControl(control) {\n    return tuiProvide(TuiControl, control);\n}\n\n/// <reference types=\"@taiga-ui/tsconfig/ng-dev-mode\" />\n/**\n * Abstract class for host element for dynamically created portals.\n */\nclass TuiPortals {\n    constructor() {\n        this.injector = inject(INJECTOR);\n        this.nothing = inject(TuiPortalService).attach(this);\n    }\n    addComponentChild(component) {\n        const injector = component.createInjector(this.injector);\n        const ref = this.vcr.createComponent(component.component, { injector });\n        ref.changeDetectorRef.detectChanges();\n        return ref;\n    }\n    addTemplateChild(templateRef, context) {\n        return this.vcr.createEmbeddedView(templateRef, context);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPortals, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiPortals, viewQueries: [{ propertyName: \"vcr\", first: true, predicate: [\"viewContainer\"], descendants: true, read: ViewContainerRef }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPortals, decorators: [{\n            type: Directive\n        }], propDecorators: { vcr: [{\n                type: ViewChild,\n                args: ['viewContainer', { read: ViewContainerRef }]\n            }] } });\n/**\n * Abstract service for displaying portals\n */\nclass TuiPortalService {\n    attach(host) {\n        this.host = host;\n    }\n    add(component) {\n        return this.safeHost.addComponentChild(component);\n    }\n    remove({ hostView }) {\n        this.removeTemplate(hostView);\n    }\n    addTemplate(templateRef, context) {\n        return this.safeHost.addTemplateChild(templateRef, context);\n    }\n    removeTemplate(viewRef) {\n        if (!viewRef.destroyed) {\n            viewRef.destroy();\n        }\n    }\n    get safeHost() {\n        if (!this.host) {\n            throw new TuiNoHostException();\n        }\n        return this.host;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPortalService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPortalService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiPortalService, decorators: [{\n            type: Injectable\n        }] });\nfunction tuiAsPortal(portal) {\n    return tuiProvide(TuiPortalService, portal);\n}\nclass TuiNoHostException extends Error {\n    constructor() {\n        super(ngDevMode ? 'Portals cannot be used without TuiPortalHostComponent' : '');\n    }\n}\n\nclass TuiValidationError {\n    constructor(message, context = {}) {\n        this.message = message;\n        this.context = context;\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TUI_IDENTITY_VALUE_TRANSFORMER, TuiControl, TuiNoHostException, TuiNonNullableValueTransformer, TuiPortalService, TuiPortals, TuiValidationError, TuiValueTransformer, tuiAsControl, tuiAsPortal, tuiValueTransformerFrom };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,UAAU,QAAQ,eAAe;AAC3J,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,SAAS,EAAEC,OAAO,QAAQ,gBAAgB;AACnD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,QAAQ,EAAEC,OAAO,EAAEC,KAAK,EAAEC,SAAS,EAAEC,GAAG,EAAEC,MAAM,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAAC,MAAAC,GAAA;AAEvH,MAAMC,mBAAmB,CAAC;AAE1B,SAASC,uBAAuBA,CAACC,KAAK,EAAE;EACpC,OAAO;IACHC,OAAO,EAAEH,mBAAmB;IAC5BI,UAAU,EAAEA,CAAA,KAAMhC,MAAM,CAAC8B,KAAK,CAAC,CAACG;EACpC,CAAC;AACL;AACA,MAAMC,8BAA8B,SAASN,mBAAmB,CAAC;EAC7DO,gBAAgBA,CAACC,KAAK,EAAE;IACpB,IAAI,CAACC,SAAS,GAAGD,KAAK;IACtB,OAAOA,KAAK;EAChB;EACAE,cAAcA,CAACF,KAAK,EAAE;IAClB,IAAI,CAACC,SAAS,GAAGD,KAAK,IAAI,IAAI,CAACC,SAAS;IACxC,OAAO,IAAI,CAACA,SAAS;EACzB;AACJ;AACA,MAAME,8BAA8B,GAAG;EACnCJ,gBAAgB,EAAElB,QAAQ;EAC1BqB,cAAc,EAAErB;AACpB,CAAC;AAED,MAAMuB,KAAK,GAAG;EAAEC,IAAI,EAAE,IAAI;EAAEC,QAAQ,EAAE;AAAK,CAAC;AAC5C;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG7C,MAAM,CAACe,kBAAkB,EAAEyB,KAAK,CAAC;IACjD,IAAI,CAACM,QAAQ,GAAG,IAAI5B,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC6B,aAAa,GAAG9C,MAAM,CAAC,IAAI,CAAC;IACjC,IAAI,CAAC+C,QAAQ,GAAG/C,MAAM,CAAC,IAAI,CAAC4C,QAAQ,CAAC;IACrC,IAAI,CAACI,OAAO,GAAGjD,MAAM,CAACY,SAAS,EAAE;MAAE6B,IAAI,EAAE;IAAK,CAAC,CAAC;IAChD,IAAI,CAACS,GAAG,GAAGlD,MAAM,CAACE,iBAAiB,CAAC;IACpC,IAAI,CAACiD,WAAW,GAAGnD,MAAM,CAAC4B,mBAAmB,EAAEY,KAAK,CAAC,IAAID,8BAA8B;IACvF,IAAI,CAACH,KAAK,GAAGjC,QAAQ,CAAC,MAAM,IAAI,CAAC6C,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACH,QAAQ,CAAC;IAC7D,IAAI,CAACO,QAAQ,GAAGnD,MAAM,CAAC,KAAK,CAAC;IAC7B,IAAI,CAACoD,OAAO,GAAGpD,MAAM,CAAC,KAAK,CAAC;IAC5B,IAAI,CAACqD,MAAM,GAAGrD,MAAM,CAACsD,SAAS,CAAC;IAC/B,IAAI,CAACC,QAAQ,GAAGrD,QAAQ,CAAC,MAAM,IAAI,CAACmD,MAAM,CAAC,CAAC,KAAK,UAAU,CAAC;IAC5D,IAAI,CAACG,WAAW,GAAGtD,QAAQ,CAAC,MAAM,CAAC,IAAI,CAACqD,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAACJ,QAAQ,CAAC,CAAC,CAAC;IACvE,IAAI,CAACM,OAAO,GAAGvD,QAAQ,CAAC,MAAM,IAAI,CAAC4C,aAAa,CAAC,CAAC,KAAK,IAAI,GACrD,CAAC,CAAC,IAAI,CAACA,aAAa,CAAC,CAAC,IAAI,IAAI,CAACU,WAAW,CAAC,CAAC,GAC5C,IAAI,CAACA,WAAW,CAAC,CAAC,IAAI,IAAI,CAACJ,OAAO,CAAC,CAAC,IAAI,IAAI,CAACC,MAAM,CAAC,CAAC,KAAK,SAAS,CAAC;IAC1E,IAAI,CAACK,IAAI,GAAGxD,QAAQ,CAAC;IACrB;IACA,IAAI,CAACiD,QAAQ,CAAC,CAAC,GAAG,UAAU,GAAG,IAAI,CAACM,OAAO,CAAC,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC;IACpE,IAAI,CAACE,SAAS,GAAG9C,cAAc;IAC/B,IAAI,CAAC+C,QAAQ,GAAG/C,cAAc;IAC9B,IAAI,CAACmC,OAAO,CAACa,aAAa,GAAG,IAAI;IACjC,IAAI,CAAChB,QAAQ,CACRiB,IAAI,CAAC5C,KAAK,CAAC,CAAC,CAAC,EAAEC,SAAS,CAAC,IAAI,CAAC,EAAEC,GAAG,CAAC,MAAM,IAAI,CAAC4B,OAAO,CAACA,OAAO,CAAC,EAAE3B,MAAM,CAAC0C,OAAO,CAAC,EAAEzC,oBAAoB,CAAC,CAAC,EAAEC,SAAS,CAAEyC,CAAC,IAAKxC,KAAK,CAACwC,CAAC,CAACC,YAAY,EAAED,CAAC,CAACE,aAAa,EAAEF,CAAC,CAACG,MAAM,IAAI1C,KAAK,CAAC,CAACqC,IAAI,CAAC3C,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAET,kBAAkB,CAAC,CAAC,CAAC,CAClO0D,SAAS,CAAC,MAAM,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;EACvC;EACA,IAAIC,cAAcA,CAACnB,QAAQ,EAAE;IACzB,IAAI,CAACA,QAAQ,CAACoB,GAAG,CAACpB,QAAQ,CAAC;EAC/B;EACA,IAAIqB,aAAaA,CAACf,OAAO,EAAE;IACvB,IAAI,CAACX,aAAa,CAACyB,GAAG,CAACd,OAAO,CAAC;EACnC;EACAgB,gBAAgBA,CAACb,QAAQ,EAAE;IACvB,IAAI,CAACf,QAAQ,CAAC6B,IAAI,CAAC,CAAC;IACpB,IAAI,CAACd,QAAQ,GAAIzB,KAAK,IAAK;MACvB,MAAMY,QAAQ,GAAG5C,SAAS,CAAC,MAAM,IAAI,CAAC4C,QAAQ,CAAC,CAAC,CAAC;MACjD,IAAIZ,KAAK,KAAKY,QAAQ,EAAE;QACpB;MACJ;MACAa,QAAQ,CAAC,IAAI,CAACV,WAAW,CAACb,cAAc,CAACF,KAAK,CAAC,CAAC;MAChD,IAAI,CAACY,QAAQ,CAACwB,GAAG,CAACpC,KAAK,CAAC;MACxB,IAAI,CAACkC,MAAM,CAAC,CAAC;IACjB,CAAC;EACL;EACAM,iBAAiBA,CAAChB,SAAS,EAAE;IACzB,IAAI,CAACA,SAAS,GAAG,MAAM;MACnBA,SAAS,CAAC,CAAC;MACX,IAAI,CAACU,MAAM,CAAC,CAAC;IACjB,CAAC;EACL;EACAO,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACP,MAAM,CAAC,CAAC;EACjB;EACAQ,UAAUA,CAAC1C,KAAK,EAAE;IACd;IACA,MAAM2C,IAAI,GAAG,IAAI,CAAC9B,OAAO,YAAYpC,OAAO,GAAG,IAAI,CAACoC,OAAO,CAAC+B,KAAK,GAAG5C,KAAK;IACzE,IAAI,CAACY,QAAQ,CAACwB,GAAG,CAAC,IAAI,CAACrB,WAAW,CAAChB,gBAAgB,CAAC4C,IAAI,CAAC,CAAC;IAC1D,IAAI,CAACT,MAAM,CAAC,CAAC;EACjB;EACAA,MAAMA,CAAA,EAAG;IACL,IAAI,CAAChB,MAAM,CAACkB,GAAG,CAAC,IAAI,CAACvB,OAAO,CAACA,OAAO,EAAEK,MAAM,CAAC;IAC7C,IAAI,CAACD,OAAO,CAACmB,GAAG,CAAC,CAAC,CAAC,IAAI,CAACvB,OAAO,CAACA,OAAO,EAAEI,OAAO,CAAC;IACjD,IAAI,CAACH,GAAG,CAAC+B,YAAY,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,mBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFzC,UAAU;IAAA,CAAmD;EAAE;EAC1K;IAAS,IAAI,CAAC0C,IAAI,kBAD+EtF,EAAE,CAAAuF,iBAAA;MAAAC,IAAA,EACJ5C,UAAU;MAAA6C,MAAA;QAAAjB,cAAA,GADRxE,EAAE,CAAA0F,YAAA,CAAAC,IAAA;QAAAjB,aAAA,GAAF1E,EAAE,CAAA0F,YAAA,CAAAC,IAAA;MAAA;IAAA,EACgI;EAAE;AACzO;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG5F,EAAE,CAAA6F,iBAAA,CAGXjD,UAAU,EAAc,CAAC;IACzG4C,IAAI,EAAElF;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEkE,cAAc,EAAE,CAAC;MAC3EgB,IAAI,EAAEjF,KAAK;MACXuF,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAEpB,aAAa,EAAE,CAAC;MAChBc,IAAI,EAAEjF,KAAK;MACXuF,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC;EAAE,CAAC;AAAA;AAChB,SAASC,YAAYA,CAAC7C,OAAO,EAAE;EAC3B,OAAOjC,UAAU,CAAC2B,UAAU,EAAEM,OAAO,CAAC;AAC1C;;AAEA;AACA;AACA;AACA;AACA,MAAM8C,UAAU,CAAC;EACbnD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoD,QAAQ,GAAGhG,MAAM,CAACO,QAAQ,CAAC;IAChC,IAAI,CAAC0F,OAAO,GAAGjG,MAAM,CAACkG,gBAAgB,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC;EACxD;EACAC,iBAAiBA,CAACC,SAAS,EAAE;IACzB,MAAML,QAAQ,GAAGK,SAAS,CAACC,cAAc,CAAC,IAAI,CAACN,QAAQ,CAAC;IACxD,MAAMO,GAAG,GAAG,IAAI,CAACC,GAAG,CAACC,eAAe,CAACJ,SAAS,CAACA,SAAS,EAAE;MAAEL;IAAS,CAAC,CAAC;IACvEO,GAAG,CAACG,iBAAiB,CAACC,aAAa,CAAC,CAAC;IACrC,OAAOJ,GAAG;EACd;EACAK,gBAAgBA,CAACC,WAAW,EAAEC,OAAO,EAAE;IACnC,OAAO,IAAI,CAACN,GAAG,CAACO,kBAAkB,CAACF,WAAW,EAAEC,OAAO,CAAC;EAC5D;EACA;IAAS,IAAI,CAAC5B,IAAI,YAAA8B,mBAAA5B,CAAA;MAAA,YAAAA,CAAA,IAAyFW,UAAU;IAAA,CAAmD;EAAE;EAC1K;IAAS,IAAI,CAACV,IAAI,kBAnC+EtF,EAAE,CAAAuF,iBAAA;MAAAC,IAAA,EAmCJQ,UAAU;MAAAkB,SAAA,WAAAC,iBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnCRpH,EAAE,CAAAsH,WAAA,CAAA1F,GAAA,KAmCiHnB,gBAAgB;QAAA;QAAA,IAAA2G,EAAA;UAAA,IAAAG,EAAA;UAnCnIvH,EAAE,CAAAwH,cAAA,CAAAD,EAAA,GAAFvH,EAAE,CAAAyH,WAAA,QAAAJ,GAAA,CAAAZ,GAAA,GAAAc,EAAA,CAAAG,KAAA;QAAA;MAAA;IAAA,EAmCqJ;EAAE;AAC9P;AACA;EAAA,QAAA9B,SAAA,oBAAAA,SAAA,KArCqG5F,EAAE,CAAA6F,iBAAA,CAqCXG,UAAU,EAAc,CAAC;IACzGR,IAAI,EAAElF;EACV,CAAC,CAAC,QAAkB;IAAEmG,GAAG,EAAE,CAAC;MACpBjB,IAAI,EAAE9E,SAAS;MACfoF,IAAI,EAAE,CAAC,eAAe,EAAE;QAAE6B,IAAI,EAAElH;MAAiB,CAAC;IACtD,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAM0F,gBAAgB,CAAC;EACnBC,MAAMA,CAACwB,IAAI,EAAE;IACT,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACAC,GAAGA,CAACvB,SAAS,EAAE;IACX,OAAO,IAAI,CAACwB,QAAQ,CAACzB,iBAAiB,CAACC,SAAS,CAAC;EACrD;EACAyB,MAAMA,CAAC;IAAEC;EAAS,CAAC,EAAE;IACjB,IAAI,CAACC,cAAc,CAACD,QAAQ,CAAC;EACjC;EACAE,WAAWA,CAACpB,WAAW,EAAEC,OAAO,EAAE;IAC9B,OAAO,IAAI,CAACe,QAAQ,CAACjB,gBAAgB,CAACC,WAAW,EAAEC,OAAO,CAAC;EAC/D;EACAkB,cAAcA,CAACE,OAAO,EAAE;IACpB,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE;MACpBD,OAAO,CAACE,OAAO,CAAC,CAAC;IACrB;EACJ;EACA,IAAIP,QAAQA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACF,IAAI,EAAE;MACZ,MAAM,IAAIU,kBAAkB,CAAC,CAAC;IAClC;IACA,OAAO,IAAI,CAACV,IAAI;EACpB;EACA;IAAS,IAAI,CAACzC,IAAI,YAAAoD,yBAAAlD,CAAA;MAAA,YAAAA,CAAA,IAAyFc,gBAAgB;IAAA,CAAoD;EAAE;EACjL;IAAS,IAAI,CAACqC,KAAK,kBAvE8ExI,EAAE,CAAAyI,kBAAA;MAAA1G,KAAA,EAuEYoE,gBAAgB;MAAAuC,OAAA,EAAhBvC,gBAAgB,CAAAhB;IAAA,EAAG;EAAE;AACxI;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAzEqG5F,EAAE,CAAA6F,iBAAA,CAyEXM,gBAAgB,EAAc,CAAC;IAC/GX,IAAI,EAAE7E;EACV,CAAC,CAAC;AAAA;AACV,SAASgI,WAAWA,CAACC,MAAM,EAAE;EACzB,OAAO3H,UAAU,CAACkF,gBAAgB,EAAEyC,MAAM,CAAC;AAC/C;AACA,MAAMN,kBAAkB,SAASO,KAAK,CAAC;EACnChG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC+C,SAAS,GAAG,uDAAuD,GAAG,EAAE,CAAC;EACnF;AACJ;AAEA,MAAMkD,kBAAkB,CAAC;EACrBjG,WAAWA,CAACkG,OAAO,EAAEhC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC/B,IAAI,CAACgC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAChC,OAAO,GAAGA,OAAO;EAC1B;AACJ;;AAEA;AACA;AACA;;AAEA,SAASvE,8BAA8B,EAAEI,UAAU,EAAE0F,kBAAkB,EAAEnG,8BAA8B,EAAEgE,gBAAgB,EAAEH,UAAU,EAAE8C,kBAAkB,EAAEjH,mBAAmB,EAAEkE,YAAY,EAAE4C,WAAW,EAAE7G,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}