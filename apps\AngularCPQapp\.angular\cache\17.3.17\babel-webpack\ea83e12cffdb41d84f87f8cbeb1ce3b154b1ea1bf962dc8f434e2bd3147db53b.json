{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, signal, inject, Directive, Input } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiInjectIconResolver, TUI_ICON_START, TUI_ICON_END } from '@taiga-ui/core/tokens';\nclass TuiIconsStyles {\n  static {\n    this.ɵfac = function TuiIconsStyles_Factory(t) {\n      return new (t || TuiIconsStyles)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiIconsStyles,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"tui-icons\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TuiIconsStyles_Template(rf, ctx) {},\n      styles: [\"[tuiIcons]{--t-icon-start: none;--t-icon-end: none}[tuiIcons]:before,[tuiIcons]:after{content:\\\"\\\";display:var(--t-icon-start);inline-size:1em;block-size:1em;line-height:1em;font-size:1.5rem;flex-shrink:0;box-sizing:content-box;background:currentColor;-webkit-mask:var(--t-icon-start) no-repeat center / contain padding-box;mask:var(--t-icon-start) no-repeat center / contain padding-box}[tuiIcons]:after{display:var(--t-icon-end);-webkit-mask:var(--t-icon-end) no-repeat center / contain padding-box;mask:var(--t-icon-end) no-repeat center / contain padding-box}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiIconsStyles, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tui-icons'\n      },\n      styles: [\"[tuiIcons]{--t-icon-start: none;--t-icon-end: none}[tuiIcons]:before,[tuiIcons]:after{content:\\\"\\\";display:var(--t-icon-start);inline-size:1em;block-size:1em;line-height:1em;font-size:1.5rem;flex-shrink:0;box-sizing:content-box;background:currentColor;-webkit-mask:var(--t-icon-start) no-repeat center / contain padding-box;mask:var(--t-icon-start) no-repeat center / contain padding-box}[tuiIcons]:after{display:var(--t-icon-end);-webkit-mask:var(--t-icon-end) no-repeat center / contain padding-box;mask:var(--t-icon-end) no-repeat center / contain padding-box}\\n\"]\n    }]\n  }], null, null);\n})();\nclass TuiIcons {\n  constructor() {\n    this.resolver = tuiInjectIconResolver();\n    this.nothing = tuiWithStyles(TuiIconsStyles);\n    this.iconStart = signal(inject(TUI_ICON_START, {\n      self: true,\n      optional: true\n    }) || '');\n    this.iconEnd = signal(inject(TUI_ICON_END, {\n      self: true,\n      optional: true\n    }) || '');\n  }\n  // TODO(v5): use signal inputs\n  set iconStartSetter(x) {\n    this.iconStart.set(x);\n  }\n  // TODO(v5): use signal inputs\n  set iconEndSetter(x) {\n    this.iconEnd.set(x);\n  }\n  resolve(icon) {\n    return icon ? `url(${this.resolver(icon.toString())})` : null;\n  }\n  static {\n    this.ɵfac = function TuiIcons_Factory(t) {\n      return new (t || TuiIcons)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiIcons,\n      hostAttrs: [\"tuiIcons\", \"\"],\n      hostVars: 4,\n      hostBindings: function TuiIcons_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-icon-start\", ctx.resolve(ctx.iconStart()))(\"--t-icon-end\", ctx.resolve(ctx.iconEnd()));\n        }\n      },\n      inputs: {\n        iconStartSetter: [i0.ɵɵInputFlags.None, \"iconStart\", \"iconStartSetter\"],\n        iconEndSetter: [i0.ɵɵInputFlags.None, \"iconEnd\", \"iconEndSetter\"]\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiIcons, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      host: {\n        tuiIcons: '',\n        '[style.--t-icon-start]': 'resolve(iconStart())',\n        '[style.--t-icon-end]': 'resolve(iconEnd())'\n      }\n    }]\n  }], null, {\n    iconStartSetter: [{\n      type: Input,\n      args: ['iconStart']\n    }],\n    iconEndSetter: [{\n      type: Input,\n      args: ['iconEnd']\n    }]\n  });\n})();\nclass TuiWithIcons {\n  static {\n    this.ɵfac = function TuiWithIcons_Factory(t) {\n      return new (t || TuiWithIcons)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiWithIcons,\n      standalone: true,\n      features: [i0.ɵɵHostDirectivesFeature([{\n        directive: TuiIcons,\n        inputs: [\"iconStart\", \"iconStart\", \"iconEnd\", \"iconEnd\"]\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiWithIcons, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      hostDirectives: [{\n        directive: TuiIcons,\n        inputs: ['iconStart', 'iconEnd']\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiIcons, TuiWithIcons };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "signal", "inject", "Directive", "Input", "tuiWithStyles", "tuiInjectIconResolver", "TUI_ICON_START", "TUI_ICON_END", "TuiIconsStyles", "ɵfac", "TuiIconsStyles_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TuiIconsStyles_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "class", "TuiIcons", "constructor", "resolver", "nothing", "iconStart", "self", "optional", "iconEnd", "iconStartSetter", "x", "set", "iconEndSetter", "resolve", "icon", "toString", "TuiIcons_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "TuiIcons_HostBindings", "ɵɵstyleProp", "inputs", "ɵɵInputFlags", "tuiIcons", "TuiWithIcons", "TuiWithIcons_Factory", "ɵɵHostDirectivesFeature", "directive", "hostDirectives"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-directives-icons.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, signal, inject, Directive, Input } from '@angular/core';\nimport { tuiWithStyles } from '@taiga-ui/cdk/utils/miscellaneous';\nimport { tuiInjectIconResolver, TUI_ICON_START, TUI_ICON_END } from '@taiga-ui/core/tokens';\n\nclass TuiIconsStyles {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIconsStyles, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiIconsStyles, isStandalone: true, selector: \"ng-component\", host: { classAttribute: \"tui-icons\" }, ngImport: i0, template: '', isInline: true, styles: [\"[tuiIcons]{--t-icon-start: none;--t-icon-end: none}[tuiIcons]:before,[tuiIcons]:after{content:\\\"\\\";display:var(--t-icon-start);inline-size:1em;block-size:1em;line-height:1em;font-size:1.5rem;flex-shrink:0;box-sizing:content-box;background:currentColor;-webkit-mask:var(--t-icon-start) no-repeat center / contain padding-box;mask:var(--t-icon-start) no-repeat center / contain padding-box}[tuiIcons]:after{display:var(--t-icon-end);-webkit-mask:var(--t-icon-end) no-repeat center / contain padding-box;mask:var(--t-icon-end) no-repeat center / contain padding-box}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIconsStyles, decorators: [{\n            type: Component,\n            args: [{ standalone: true, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'tui-icons',\n                    }, styles: [\"[tuiIcons]{--t-icon-start: none;--t-icon-end: none}[tuiIcons]:before,[tuiIcons]:after{content:\\\"\\\";display:var(--t-icon-start);inline-size:1em;block-size:1em;line-height:1em;font-size:1.5rem;flex-shrink:0;box-sizing:content-box;background:currentColor;-webkit-mask:var(--t-icon-start) no-repeat center / contain padding-box;mask:var(--t-icon-start) no-repeat center / contain padding-box}[tuiIcons]:after{display:var(--t-icon-end);-webkit-mask:var(--t-icon-end) no-repeat center / contain padding-box;mask:var(--t-icon-end) no-repeat center / contain padding-box}\\n\"] }]\n        }] });\nclass TuiIcons {\n    constructor() {\n        this.resolver = tuiInjectIconResolver();\n        this.nothing = tuiWithStyles(TuiIconsStyles);\n        this.iconStart = signal(inject(TUI_ICON_START, { self: true, optional: true }) || '');\n        this.iconEnd = signal(inject(TUI_ICON_END, { self: true, optional: true }) || '');\n    }\n    // TODO(v5): use signal inputs\n    set iconStartSetter(x) {\n        this.iconStart.set(x);\n    }\n    // TODO(v5): use signal inputs\n    set iconEndSetter(x) {\n        this.iconEnd.set(x);\n    }\n    resolve(icon) {\n        return icon ? `url(${this.resolver(icon.toString())})` : null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIcons, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiIcons, isStandalone: true, inputs: { iconStartSetter: [\"iconStart\", \"iconStartSetter\"], iconEndSetter: [\"iconEnd\", \"iconEndSetter\"] }, host: { attributes: { \"tuiIcons\": \"\" }, properties: { \"style.--t-icon-start\": \"resolve(iconStart())\", \"style.--t-icon-end\": \"resolve(iconEnd())\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiIcons, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    host: {\n                        tuiIcons: '',\n                        '[style.--t-icon-start]': 'resolve(iconStart())',\n                        '[style.--t-icon-end]': 'resolve(iconEnd())',\n                    },\n                }]\n        }], propDecorators: { iconStartSetter: [{\n                type: Input,\n                args: ['iconStart']\n            }], iconEndSetter: [{\n                type: Input,\n                args: ['iconEnd']\n            }] } });\n\nclass TuiWithIcons {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiWithIcons, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiWithIcons, isStandalone: true, hostDirectives: [{ directive: TuiIcons, inputs: [\"iconStart\", \"iconStart\", \"iconEnd\", \"iconEnd\"] }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiWithIcons, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    hostDirectives: [\n                        {\n                            directive: TuiIcons,\n                            inputs: ['iconStart', 'iconEnd'],\n                        },\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiIcons, TuiWithIcons };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACvH,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,qBAAqB,EAAEC,cAAc,EAAEC,YAAY,QAAQ,uBAAuB;AAE3F,MAAMC,cAAc,CAAC;EACjB;IAAS,IAAI,CAACC,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,cAAc;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACI,IAAI,kBAD+EhB,EAAE,CAAAiB,iBAAA;MAAAC,IAAA,EACJN,cAAc;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADZtB,EAAE,CAAAuB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC+yB;EAAE;AACx5B;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGjC,EAAE,CAAAkC,iBAAA,CAGXtB,cAAc,EAAc,CAAC;IAC7GM,IAAI,EAAEjB,SAAS;IACfkC,IAAI,EAAE,CAAC;MAAEd,UAAU,EAAE,IAAI;MAAEK,QAAQ,EAAE,EAAE;MAAEK,aAAa,EAAE7B,iBAAiB,CAACkC,IAAI;MAAEJ,eAAe,EAAE7B,uBAAuB,CAACkC,MAAM;MAAEC,IAAI,EAAE;QAC3HC,KAAK,EAAE;MACX,CAAC;MAAET,MAAM,EAAE,CAAC,ujBAAujB;IAAE,CAAC;EACllB,CAAC,CAAC;AAAA;AACV,MAAMU,QAAQ,CAAC;EACXC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAGjC,qBAAqB,CAAC,CAAC;IACvC,IAAI,CAACkC,OAAO,GAAGnC,aAAa,CAACI,cAAc,CAAC;IAC5C,IAAI,CAACgC,SAAS,GAAGxC,MAAM,CAACC,MAAM,CAACK,cAAc,EAAE;MAAEmC,IAAI,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,IAAI,EAAE,CAAC;IACrF,IAAI,CAACC,OAAO,GAAG3C,MAAM,CAACC,MAAM,CAACM,YAAY,EAAE;MAAEkC,IAAI,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,IAAI,EAAE,CAAC;EACrF;EACA;EACA,IAAIE,eAAeA,CAACC,CAAC,EAAE;IACnB,IAAI,CAACL,SAAS,CAACM,GAAG,CAACD,CAAC,CAAC;EACzB;EACA;EACA,IAAIE,aAAaA,CAACF,CAAC,EAAE;IACjB,IAAI,CAACF,OAAO,CAACG,GAAG,CAACD,CAAC,CAAC;EACvB;EACAG,OAAOA,CAACC,IAAI,EAAE;IACV,OAAOA,IAAI,GAAG,OAAO,IAAI,CAACX,QAAQ,CAACW,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI;EACjE;EACA;IAAS,IAAI,CAACzC,IAAI,YAAA0C,iBAAAxC,CAAA;MAAA,YAAAA,CAAA,IAAyFyB,QAAQ;IAAA,CAAmD;EAAE;EACxK;IAAS,IAAI,CAACgB,IAAI,kBA5B+ExD,EAAE,CAAAyD,iBAAA;MAAAvC,IAAA,EA4BJsB,QAAQ;MAAApB,SAAA,eAAoK,EAAE;MAAAsC,QAAA;MAAAC,YAAA,WAAAC,sBAAAhC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5B5K5B,EAAE,CAAA6D,WAAA,mBA4BJhC,GAAA,CAAAuB,OAAA,CAAQvB,GAAA,CAAAe,SAAA,CAAU,CAAC,CAAZ,CAAC,iBAARf,GAAA,CAAAuB,OAAA,CAAQvB,GAAA,CAAAkB,OAAA,CAAQ,CAAC,CAAV,CAAC;QAAA;MAAA;MAAAe,MAAA;QAAAd,eAAA,GA5BNhD,EAAE,CAAA+D,YAAA,CAAA3B,IAAA;QAAAe,aAAA,GAAFnD,EAAE,CAAA+D,YAAA,CAAA3B,IAAA;MAAA;MAAAf,UAAA;IAAA,EA4B2S;EAAE;AACpZ;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KA9BqGjC,EAAE,CAAAkC,iBAAA,CA8BXM,QAAQ,EAAc,CAAC;IACvGtB,IAAI,EAAEZ,SAAS;IACf6B,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBiB,IAAI,EAAE;QACF0B,QAAQ,EAAE,EAAE;QACZ,wBAAwB,EAAE,sBAAsB;QAChD,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEhB,eAAe,EAAE,CAAC;MAChC9B,IAAI,EAAEX,KAAK;MACX4B,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEgB,aAAa,EAAE,CAAC;MAChBjC,IAAI,EAAEX,KAAK;MACX4B,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8B,YAAY,CAAC;EACf;IAAS,IAAI,CAACpD,IAAI,YAAAqD,qBAAAnD,CAAA;MAAA,YAAAA,CAAA,IAAyFkD,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACT,IAAI,kBAlD+ExD,EAAE,CAAAyD,iBAAA;MAAAvC,IAAA,EAkDJ+C,YAAY;MAAA5C,UAAA;MAAAC,QAAA,GAlDVtB,EAAE,CAAAmE,uBAAA;QAAAC,SAAA,EAkD4D5B,QAAQ;QAAAsB,MAAA;MAAA;IAAA,EAA8E;EAAE;AAC3P;AACA;EAAA,QAAA7B,SAAA,oBAAAA,SAAA,KApDqGjC,EAAE,CAAAkC,iBAAA,CAoDX+B,YAAY,EAAc,CAAC;IAC3G/C,IAAI,EAAEZ,SAAS;IACf6B,IAAI,EAAE,CAAC;MACCd,UAAU,EAAE,IAAI;MAChBgD,cAAc,EAAE,CACZ;QACID,SAAS,EAAE5B,QAAQ;QACnBsB,MAAM,EAAE,CAAC,WAAW,EAAE,SAAS;MACnC,CAAC;IAET,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAStB,QAAQ,EAAEyB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}