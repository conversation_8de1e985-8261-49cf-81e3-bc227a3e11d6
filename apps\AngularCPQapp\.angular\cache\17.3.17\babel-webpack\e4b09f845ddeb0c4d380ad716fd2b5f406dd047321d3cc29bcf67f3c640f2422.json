{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Directive } from '@angular/core';\nimport { tuiInjectElement, tuiIsHTMLElement, tuiContainsOrAfter } from '@taiga-ui/cdk/utils/dom';\nimport { tuiGetNativeFocused, tuiBlurNativeFocused, tuiGetClosestFocusable } from '@taiga-ui/cdk/utils/focus';\nclass TuiFocusTrap {\n  constructor() {\n    this.doc = inject(DOCUMENT);\n    this.el = tuiInjectElement();\n    this.activeElement = null;\n    this.initialized = false;\n    /**\n     * This would cause currently focused element to lose focus,\n     * but it might cause ExpressionChanged error due to potential HostBinding.\n     * Microtask keeps it in the same frame but allows change detection to run\n     */\n    Promise.resolve().then(() => {\n      /**\n       * The same event can synchronously close already opened focus trap and open another one.\n       * All focus traps have microtask inside its `ngOnDestroy` –\n       * they should be resolved before enabling of new focus trap.\n       * Don't enable any new event listeners before `initialized` equals to `true`!\n       */\n      this.initialized = true;\n      this.activeElement = tuiGetNativeFocused(this.doc);\n      this.el.focus();\n    });\n  }\n  ngOnDestroy() {\n    tuiBlurNativeFocused(this.doc);\n    /**\n     * HostListeners are triggered even after ngOnDestroy\n     * {@link https://github.com/angular/angular/issues/38100}\n     * so we need to delay it but stay in the same sync cycle,\n     * therefore using Promise instead of setTimeout\n     */\n    Promise.resolve().then(() => {\n      if (tuiIsHTMLElement(this.activeElement)) {\n        this.activeElement.focus();\n      }\n    });\n  }\n  onFocusIn(node) {\n    const {\n      firstElementChild\n    } = this.el;\n    if (!tuiContainsOrAfter(this.el, node) && firstElementChild) {\n      tuiGetClosestFocusable({\n        initial: firstElementChild,\n        root: this.el\n      })?.focus();\n    }\n  }\n  static {\n    this.ɵfac = function TuiFocusTrap_Factory(t) {\n      return new (t || TuiFocusTrap)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiFocusTrap,\n      selectors: [[\"\", \"tuiFocusTrap\", \"\"]],\n      hostAttrs: [\"tabIndex\", \"0\"],\n      hostBindings: function TuiFocusTrap_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focusin.zoneless\", function TuiFocusTrap_focusin_zoneless_HostBindingHandler($event) {\n            return ctx.initialized && ctx.onFocusIn($event.target);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiFocusTrap, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiFocusTrap]',\n      host: {\n        tabIndex: '0',\n        '(window:focusin.zoneless)': 'initialized && onFocusIn($event.target)'\n      }\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFocusTrap };", "map": {"version": 3, "names": ["DOCUMENT", "i0", "inject", "Directive", "tuiInjectElement", "tuiIsHTMLElement", "tuiContainsOrAfter", "tuiGetNativeFocused", "tuiBlurNativeFocused", "tuiGetClosestFocusable", "TuiFocusTrap", "constructor", "doc", "el", "activeElement", "initialized", "Promise", "resolve", "then", "focus", "ngOnDestroy", "onFocusIn", "node", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "initial", "root", "ɵfac", "TuiFocusTrap_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostBindings", "TuiFocusTrap_HostBindings", "rf", "ctx", "ɵɵlistener", "TuiFocusTrap_focusin_zoneless_HostBindingHandler", "$event", "target", "ɵɵresolveWindow", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "tabIndex"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/cdk/fesm2022/taiga-ui-cdk-directives-focus-trap.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Directive } from '@angular/core';\nimport { tuiInjectElement, tuiIsHTMLElement, tuiContainsOrAfter } from '@taiga-ui/cdk/utils/dom';\nimport { tuiGetNativeFocused, tuiBlurNativeFocused, tuiGetClosestFocusable } from '@taiga-ui/cdk/utils/focus';\n\nclass TuiFocusTrap {\n    constructor() {\n        this.doc = inject(DOCUMENT);\n        this.el = tuiInjectElement();\n        this.activeElement = null;\n        this.initialized = false;\n        /**\n         * This would cause currently focused element to lose focus,\n         * but it might cause ExpressionChanged error due to potential HostBinding.\n         * Microtask keeps it in the same frame but allows change detection to run\n         */\n        Promise.resolve().then(() => {\n            /**\n             * The same event can synchronously close already opened focus trap and open another one.\n             * All focus traps have microtask inside its `ngOnDestroy` –\n             * they should be resolved before enabling of new focus trap.\n             * Don't enable any new event listeners before `initialized` equals to `true`!\n             */\n            this.initialized = true;\n            this.activeElement = tuiGetNativeFocused(this.doc);\n            this.el.focus();\n        });\n    }\n    ngOnDestroy() {\n        tuiBlurNativeFocused(this.doc);\n        /**\n         * HostListeners are triggered even after ngOnDestroy\n         * {@link https://github.com/angular/angular/issues/38100}\n         * so we need to delay it but stay in the same sync cycle,\n         * therefore using Promise instead of setTimeout\n         */\n        Promise.resolve().then(() => {\n            if (tuiIsHTMLElement(this.activeElement)) {\n                this.activeElement.focus();\n            }\n        });\n    }\n    onFocusIn(node) {\n        const { firstElementChild } = this.el;\n        if (!tuiContainsOrAfter(this.el, node) && firstElementChild) {\n            tuiGetClosestFocusable({\n                initial: firstElementChild,\n                root: this.el,\n            })?.focus();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFocusTrap, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiFocusTrap, isStandalone: true, selector: \"[tuiFocusTrap]\", host: { attributes: { \"tabIndex\": \"0\" }, listeners: { \"window:focusin.zoneless\": \"initialized && onFocusIn($event.target)\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiFocusTrap, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiFocusTrap]',\n                    host: {\n                        tabIndex: '0',\n                        '(window:focusin.zoneless)': 'initialized && onFocusIn($event.target)',\n                    },\n                }]\n        }], ctorParameters: function () { return []; } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiFocusTrap };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACjD,SAASC,gBAAgB,EAAEC,gBAAgB,EAAEC,kBAAkB,QAAQ,yBAAyB;AAChG,SAASC,mBAAmB,EAAEC,oBAAoB,EAAEC,sBAAsB,QAAQ,2BAA2B;AAE7G,MAAMC,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,GAAG,GAAGV,MAAM,CAACF,QAAQ,CAAC;IAC3B,IAAI,CAACa,EAAE,GAAGT,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACU,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;AACR;AACA;AACA;AACA;IACQC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACH,WAAW,GAAG,IAAI;MACvB,IAAI,CAACD,aAAa,GAAGP,mBAAmB,CAAC,IAAI,CAACK,GAAG,CAAC;MAClD,IAAI,CAACC,EAAE,CAACM,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;EACN;EACAC,WAAWA,CAAA,EAAG;IACVZ,oBAAoB,CAAC,IAAI,CAACI,GAAG,CAAC;IAC9B;AACR;AACA;AACA;AACA;AACA;IACQI,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAIb,gBAAgB,CAAC,IAAI,CAACS,aAAa,CAAC,EAAE;QACtC,IAAI,CAACA,aAAa,CAACK,KAAK,CAAC,CAAC;MAC9B;IACJ,CAAC,CAAC;EACN;EACAE,SAASA,CAACC,IAAI,EAAE;IACZ,MAAM;MAAEC;IAAkB,CAAC,GAAG,IAAI,CAACV,EAAE;IACrC,IAAI,CAACP,kBAAkB,CAAC,IAAI,CAACO,EAAE,EAAES,IAAI,CAAC,IAAIC,iBAAiB,EAAE;MACzDd,sBAAsB,CAAC;QACnBe,OAAO,EAAED,iBAAiB;QAC1BE,IAAI,EAAE,IAAI,CAACZ;MACf,CAAC,CAAC,EAAEM,KAAK,CAAC,CAAC;IACf;EACJ;EACA;IAAS,IAAI,CAACO,IAAI,YAAAC,qBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFlB,YAAY;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACmB,IAAI,kBAD+E5B,EAAE,CAAA6B,iBAAA;MAAAC,IAAA,EACJrB,YAAY;MAAAsB,SAAA;MAAAC,SAAA,eAAoF,GAAG;MAAAC,YAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADjGnC,EAAE,CAAAqC,UAAA,8BAAAC,iDAAAC,MAAA;YAAA,OAAAH,GAAA,CAAAtB,WAAA,IACWsB,GAAA,CAAAhB,SAAA,CAAAmB,MAAA,CAAAC,MAAuB,CAAC;UAAA,UADrCxC,EAAE,CAAAyC,eACO,CAAC;QAAA;MAAA;MAAAC,UAAA;IAAA,EAAiM;EAAE;AAClT;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG3C,EAAE,CAAA4C,iBAAA,CAGXnC,YAAY,EAAc,CAAC;IAC3GqB,IAAI,EAAE5B,SAAS;IACf2C,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,IAAI;MAChBI,QAAQ,EAAE,gBAAgB;MAC1BC,IAAI,EAAE;QACFC,QAAQ,EAAE,GAAG;QACb,2BAA2B,EAAE;MACjC;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;;AAEtD;AACA;AACA;;AAEA,SAASvC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}