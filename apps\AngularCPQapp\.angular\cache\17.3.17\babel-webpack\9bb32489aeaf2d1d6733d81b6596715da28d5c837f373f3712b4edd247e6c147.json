{"ast": null, "code": "import { TuiItem } from '@taiga-ui/cdk/directives/item';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, NgForOf, NgIf, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, inject, Injectable, TemplateRef, Component, ChangeDetectionStrategy, ContentChildren, ContentChild, Output } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { Subject, Observable, merge, debounceTime, map, distinctUntilChanged, share } from 'rxjs';\nimport { tuiZonefreeScheduler, tuiZoneOptimized } from '@taiga-ui/cdk/observables';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\nconst _c0 = a0 => ({\n  $implicit: a0\n});\nfunction TuiItemsWithMoreComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiItemsWithMoreComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const template_r1 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", template_r1);\n  }\n}\nfunction TuiItemsWithMoreComponent_div_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TuiItemsWithMoreComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtemplate(1, TuiItemsWithMoreComponent_div_3_ng_container_1_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const index_r3 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"t-item_hidden\", ctx_r3.isHidden(index_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", item_r2);\n  }\n}\nfunction TuiItemsWithMoreComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const template_r1 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", template_r1);\n  }\n}\nfunction TuiItemsWithMoreComponent_ng_template_5_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵelementContainer(1, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.more || null)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c0, ctx_r3.lastIndex()));\n  }\n}\nfunction TuiItemsWithMoreComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TuiItemsWithMoreComponent_ng_template_5_span_0_Template, 2, 4, \"span\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isMoreHidden);\n  }\n}\nclass TuiItemsWithMoreDirective {\n  constructor() {\n    this.el = tuiInjectElement();\n    this.itemsLimit = Infinity;\n    this.required = -1;\n    this.linesLimit = 1;\n    this.side = 'end';\n    // TODO: refactor to signal inputs after Angular update\n    this.change$ = new Subject();\n  }\n  get computedSide() {\n    return this.linesLimit > 1 ? 'end' : this.side;\n  }\n  ngOnChanges() {\n    this.change$.next();\n  }\n  maxWidth() {\n    return Math.max(...Array.from(this.el.children, ({\n      clientWidth\n    }) => clientWidth));\n  }\n  static {\n    this.ɵfac = function TuiItemsWithMoreDirective_Factory(t) {\n      return new (t || TuiItemsWithMoreDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiItemsWithMoreDirective,\n      hostVars: 4,\n      hostBindings: function TuiItemsWithMoreDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--t-min-width\", ctx.maxWidth(), \"px\");\n          i0.ɵɵclassProp(\"_multiline\", ctx.linesLimit > 1);\n        }\n      },\n      inputs: {\n        itemsLimit: \"itemsLimit\",\n        required: \"required\",\n        linesLimit: \"linesLimit\",\n        side: \"side\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiItemsWithMoreDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      host: {\n        '[class._multiline]': 'linesLimit > 1',\n        '[style.--t-min-width.px]': 'maxWidth()'\n      }\n    }]\n  }], null, {\n    itemsLimit: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    linesLimit: [{\n      type: Input\n    }],\n    side: [{\n      type: Input\n    }]\n  });\n})();\nclass TuiItemsWithMoreService extends Observable {\n  constructor() {\n    super(subscriber => this.stream$.subscribe(subscriber));\n    this.el = tuiInjectElement();\n    this.directive = inject(TuiItemsWithMoreDirective);\n    this.stream$ = merge(this.directive.change$, inject(MutationObserverService, {\n      self: true\n    }), inject(ResizeObserverService, {\n      self: true\n    })).pipe(debounceTime(0, tuiZonefreeScheduler()), map(() => this.directive.linesLimit > 1 ? this.getOverflowIndexMultiline() : this.getOverflowIndex(Array.from(this.el.children))), distinctUntilChanged(), tuiZoneOptimized(), share());\n  }\n  getOverflowIndex(children) {\n    const {\n      computedSide,\n      itemsLimit\n    } = this.directive;\n    const {\n      clientWidth\n    } = this.el;\n    const items = Array.from(children, ({\n      clientWidth\n    }) => clientWidth);\n    const index = computedSide === 'start' ? 0 : items.length - 1;\n    const more = children[index]?.tagName === 'SPAN' ? items[index] ?? 0 : 0;\n    const total = items.reduce((sum, width) => sum + width, 0) - more;\n    if (total <= clientWidth && itemsLimit >= items.length) {\n      return computedSide === 'end' ? itemsLimit : 0;\n    }\n    return computedSide === 'start' ? this.getIndexStart(items, total, more) : this.getIndexEnd(items, total, more);\n  }\n  getIndexStart(items, total, more) {\n    const {\n      required,\n      itemsLimit\n    } = this.directive;\n    const {\n      clientWidth\n    } = this.el;\n    const min = Number.isFinite(itemsLimit) ? items.length - itemsLimit - 1 : 0;\n    const last = items.length - 1;\n    const mandatory = required === -1 ? last : required;\n    for (let i = 1; i < last; i++) {\n      if (i === mandatory + 1) {\n        continue;\n      }\n      total -= items[i] ?? 0;\n      if (total + more <= clientWidth) {\n        return tuiClamp(i, mandatory < min ? min + 1 : min, items.length);\n      }\n    }\n    return items.length;\n  }\n  getIndexEnd(items, total, more) {\n    const {\n      required,\n      itemsLimit\n    } = this.directive;\n    const {\n      clientWidth\n    } = this.el;\n    const max = itemsLimit > required ? itemsLimit - 1 : itemsLimit - 2;\n    const last = items.length - 1;\n    const mandatory = required === -1 ? 0 : required;\n    for (let i = last - 1; i > 0; i--) {\n      if (i === mandatory) {\n        continue;\n      }\n      total -= items[i] ?? 0;\n      if (total + more <= clientWidth) {\n        return tuiClamp(i - 1, -1, max);\n      }\n    }\n    return -1;\n  }\n  getOverflowIndexMultiline() {\n    const {\n      children\n    } = this.el;\n    const {\n      linesLimit,\n      itemsLimit\n    } = this.directive;\n    const items = Array.from(children);\n    const rows = new Set(items.map(item => item.offsetTop));\n    const offset = Array.from(rows)[linesLimit - 1];\n    const firstItemLastRow = items.findIndex(i => i.offsetTop === offset);\n    const lastRow = items.slice(firstItemLastRow);\n    const index = firstItemLastRow + this.getOverflowIndex(lastRow);\n    return Math.min(itemsLimit - 1, index);\n  }\n  static {\n    this.ɵfac = function TuiItemsWithMoreService_Factory(t) {\n      return new (t || TuiItemsWithMoreService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TuiItemsWithMoreService,\n      factory: TuiItemsWithMoreService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiItemsWithMoreService, [{\n    type: Injectable\n  }], function () {\n    return [];\n  }, null);\n})();\nclass TuiMore {\n  static ngTemplateContextGuard(_dir, _ctx) {\n    return true;\n  }\n  static {\n    this.ɵfac = function TuiMore_Factory(t) {\n      return new (t || TuiMore)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TuiMore,\n      selectors: [[\"\", \"tuiMore\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiMore, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[tuiMore]'\n    }]\n  }], null, null);\n})();\nclass TuiItemsWithMoreComponent {\n  constructor() {\n    this.items = EMPTY_QUERY;\n    this.directive = inject(TuiItemsWithMoreDirective);\n    this.lastIndexChange = inject(TuiItemsWithMoreService);\n    this.lastIndex = toSignal(this.lastIndexChange, {\n      initialValue: 0\n    });\n  }\n  get isMoreHidden() {\n    const {\n      computedSide\n    } = this.directive;\n    return this.lastIndex() >= this.items.length - 1 && computedSide === 'end' || !this.lastIndex() && computedSide === 'start';\n  }\n  isHidden(index) {\n    const {\n      computedSide,\n      required\n    } = this.directive;\n    return index > this.lastIndex() && index !== required && computedSide === 'end' || index < this.lastIndex() && index !== required && computedSide === 'start';\n  }\n  static {\n    this.ɵfac = function TuiItemsWithMoreComponent_Factory(t) {\n      return new (t || TuiItemsWithMoreComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TuiItemsWithMoreComponent,\n      selectors: [[\"tui-items-with-more\"]],\n      contentQueries: function TuiItemsWithMoreComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TuiMore, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, TuiItem, 5, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.more = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      outputs: {\n        lastIndexChange: \"lastIndexChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MutationObserverService, ResizeObserverService, TuiItemsWithMoreService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          childList: true,\n          characterData: true,\n          subtree: true\n        }\n      }]), i0.ɵɵHostDirectivesFeature([{\n        directive: TuiItemsWithMoreDirective,\n        inputs: [\"itemsLimit\", \"itemsLimit\", \"required\", \"required\", \"side\", \"side\", \"linesLimit\", \"linesLimit\"]\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 6,\n      consts: [[\"template\", \"\"], [4, \"ngIf\"], [3, \"ngTemplateOutlet\", 4, \"ngIf\"], [\"class\", \"t-item\", 3, \"t-item_hidden\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngTemplateOutlet\"], [1, \"t-item\"], [4, \"ngTemplateOutlet\"], [\"class\", \"t-item t-item_more\", 4, \"ngIf\"], [1, \"t-item\", \"t-item_more\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function TuiItemsWithMoreComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TuiItemsWithMoreComponent_ng_container_0_Template, 1, 0, \"ng-container\", 1);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵtemplate(2, TuiItemsWithMoreComponent_ng_container_2_Template, 1, 1, \"ng-container\", 2)(3, TuiItemsWithMoreComponent_div_3_Template, 2, 3, \"div\", 3)(4, TuiItemsWithMoreComponent_ng_container_4_Template, 1, 1, \"ng-container\", 2)(5, TuiItemsWithMoreComponent_ng_template_5_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 4, ctx.items.changes));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.directive.side === \"start\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.directive.side === \"end\");\n        }\n      },\n      dependencies: [AsyncPipe, NgForOf, NgIf, NgTemplateOutlet],\n      styles: [\"[_nghost-%COMP%]{position:relative;display:flex;min-inline-size:0;flex:1;align-items:center;white-space:nowrap}._multiline[_nghost-%COMP%]{flex-wrap:wrap}.t-item[_ngcontent-%COMP%]{flex:0 0 auto}.t-item_hidden[_ngcontent-%COMP%]{position:absolute;bottom:0;visibility:hidden}._multiline[_nghost-%COMP%]   .t-item_more[_ngcontent-%COMP%]:not(:empty){min-inline-size:var(--t-min-width, 0)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TuiItemsWithMoreComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'tui-items-with-more',\n      imports: [AsyncPipe, NgForOf, NgIf, NgTemplateOutlet],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [MutationObserverService, ResizeObserverService, TuiItemsWithMoreService, {\n        provide: WA_MUTATION_OBSERVER_INIT,\n        useValue: {\n          childList: true,\n          characterData: true,\n          subtree: true\n        }\n      }],\n      hostDirectives: [{\n        directive: TuiItemsWithMoreDirective,\n        inputs: ['itemsLimit', 'required', 'side', 'linesLimit']\n      }],\n      template: \"<ng-container *ngIf=\\\"items.changes | async\\\" />\\n<ng-container\\n    *ngIf=\\\"directive.side === 'start'\\\"\\n    [ngTemplateOutlet]=\\\"template\\\"\\n/>\\n<div\\n    *ngFor=\\\"let item of items; let index = index\\\"\\n    class=\\\"t-item\\\"\\n    [class.t-item_hidden]=\\\"isHidden(index)\\\"\\n>\\n    <ng-container *ngTemplateOutlet=\\\"item\\\" />\\n</div>\\n<ng-container\\n    *ngIf=\\\"directive.side === 'end'\\\"\\n    [ngTemplateOutlet]=\\\"template\\\"\\n/>\\n<ng-template #template>\\n    <span\\n        *ngIf=\\\"!isMoreHidden\\\"\\n        class=\\\"t-item t-item_more\\\"\\n    >\\n        <ng-container\\n            [ngTemplateOutlet]=\\\"more || null\\\"\\n            [ngTemplateOutletContext]=\\\"{$implicit: lastIndex()}\\\"\\n        />\\n    </span>\\n</ng-template>\\n\",\n      styles: [\":host{position:relative;display:flex;min-inline-size:0;flex:1;align-items:center;white-space:nowrap}:host._multiline{flex-wrap:wrap}.t-item{flex:0 0 auto}.t-item_hidden{position:absolute;bottom:0;visibility:hidden}:host._multiline .t-item_more:not(:empty){min-inline-size:var(--t-min-width, 0)}\\n\"]\n    }]\n  }], null, {\n    items: [{\n      type: ContentChildren,\n      args: [TuiItem, {\n        read: TemplateRef,\n        descendants: true\n      }]\n    }],\n    more: [{\n      type: ContentChild,\n      args: [TuiMore, {\n        read: TemplateRef\n      }]\n    }],\n    lastIndexChange: [{\n      type: Output\n    }]\n  });\n})();\nconst TuiItemsWithMore = [TuiItemsWithMoreComponent, TuiItemsWithMoreDirective, TuiMore, TuiItem];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiItemsWithMore, TuiItemsWithMoreComponent, TuiItemsWithMoreDirective, TuiItemsWithMoreService, TuiMore };", "map": {"version": 3, "names": ["TuiItem", "AsyncPipe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "i0", "Directive", "Input", "inject", "Injectable", "TemplateRef", "Component", "ChangeDetectionStrategy", "ContentChildren", "ContentChild", "Output", "toSignal", "MutationObserverService", "WA_MUTATION_OBSERVER_INIT", "ResizeObserverService", "EMPTY_QUERY", "tuiInjectElement", "Subject", "Observable", "merge", "debounceTime", "map", "distinctUntilChanged", "share", "tuiZonefreeScheduler", "tuiZoneOptimized", "tui<PERSON><PERSON>", "_c0", "a0", "$implicit", "TuiItemsWithMoreComponent_ng_container_0_Template", "rf", "ctx", "ɵɵelementContainer", "TuiItemsWithMoreComponent_ng_container_2_Template", "ɵɵnextContext", "template_r1", "ɵɵreference", "ɵɵproperty", "TuiItemsWithMoreComponent_div_3_ng_container_1_Template", "TuiItemsWithMoreComponent_div_3_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "item_r2", "index_r3", "index", "ctx_r3", "ɵɵclassProp", "isHidden", "ɵɵadvance", "TuiItemsWithMoreComponent_ng_container_4_Template", "TuiItemsWithMoreComponent_ng_template_5_span_0_Template", "more", "ɵɵpureFunction1", "lastIndex", "TuiItemsWithMoreComponent_ng_template_5_Template", "isMoreHidden", "TuiItemsWithMoreDirective", "constructor", "el", "itemsLimit", "Infinity", "required", "linesLimit", "side", "change$", "computedSide", "ngOnChanges", "next", "max<PERSON><PERSON><PERSON>", "Math", "max", "Array", "from", "children", "clientWidth", "ɵfac", "TuiItemsWithMoreDirective_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "hostVars", "hostBindings", "TuiItemsWithMoreDirective_HostBindings", "ɵɵstyleProp", "inputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "host", "TuiItemsWithMoreService", "subscriber", "stream$", "subscribe", "directive", "self", "pipe", "getOverflowIndexMultiline", "getOverflowIndex", "items", "length", "tagName", "total", "reduce", "sum", "width", "getIndexStart", "getIndexEnd", "min", "Number", "isFinite", "last", "mandatory", "i", "rows", "Set", "item", "offsetTop", "offset", "firstItemLastRow", "findIndex", "lastRow", "slice", "TuiItemsWithMoreService_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "TuiMore", "ngTemplateContextGuard", "_dir", "_ctx", "TuiMore_Factory", "selectors", "selector", "TuiItemsWithMoreComponent", "lastIndexChange", "initialValue", "TuiItemsWithMoreComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "TuiItemsWithMoreComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "outputs", "ɵɵProvidersFeature", "provide", "useValue", "childList", "characterData", "subtree", "ɵɵHostDirectivesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TuiItemsWithMoreComponent_Template", "ɵɵpipe", "ɵɵtemplateRefExtractor", "ɵɵpipeBind1", "changes", "dependencies", "styles", "changeDetection", "imports", "OnPush", "providers", "hostDirectives", "read", "descendants", "TuiItemsWithMore"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/kit/fesm2022/taiga-ui-kit-components-items-with-more.mjs"], "sourcesContent": ["import { TuiItem } from '@taiga-ui/cdk/directives/item';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, NgForOf, <PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, inject, Injectable, TemplateRef, Component, ChangeDetectionStrategy, ContentChildren, ContentChild, Output } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { MutationObserverService, WA_MUTATION_OBSERVER_INIT } from '@ng-web-apis/mutation-observer';\nimport { ResizeObserverService } from '@ng-web-apis/resize-observer';\nimport { EMPTY_QUERY } from '@taiga-ui/cdk/constants';\nimport { tuiInjectElement } from '@taiga-ui/cdk/utils/dom';\nimport { Subject, Observable, merge, debounceTime, map, distinctUntilChanged, share } from 'rxjs';\nimport { tuiZonefreeScheduler, tuiZoneOptimized } from '@taiga-ui/cdk/observables';\nimport { tuiClamp } from '@taiga-ui/cdk/utils/math';\n\nclass TuiItemsWithMoreDirective {\n    constructor() {\n        this.el = tuiInjectElement();\n        this.itemsLimit = Infinity;\n        this.required = -1;\n        this.linesLimit = 1;\n        this.side = 'end';\n        // TODO: refactor to signal inputs after Angular update\n        this.change$ = new Subject();\n    }\n    get computedSide() {\n        return this.linesLimit > 1 ? 'end' : this.side;\n    }\n    ngOnChanges() {\n        this.change$.next();\n    }\n    maxWidth() {\n        return Math.max(...Array.from(this.el.children, ({ clientWidth }) => clientWidth));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiItemsWithMoreDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiItemsWithMoreDirective, isStandalone: true, inputs: { itemsLimit: \"itemsLimit\", required: \"required\", linesLimit: \"linesLimit\", side: \"side\" }, host: { properties: { \"class._multiline\": \"linesLimit > 1\", \"style.--t-min-width.px\": \"maxWidth()\" } }, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiItemsWithMoreDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    host: {\n                        '[class._multiline]': 'linesLimit > 1',\n                        '[style.--t-min-width.px]': 'maxWidth()',\n                    },\n                }]\n        }], propDecorators: { itemsLimit: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], linesLimit: [{\n                type: Input\n            }], side: [{\n                type: Input\n            }] } });\n\nclass TuiItemsWithMoreService extends Observable {\n    constructor() {\n        super((subscriber) => this.stream$.subscribe(subscriber));\n        this.el = tuiInjectElement();\n        this.directive = inject(TuiItemsWithMoreDirective);\n        this.stream$ = merge(this.directive.change$, inject(MutationObserverService, { self: true }), inject(ResizeObserverService, { self: true })).pipe(debounceTime(0, tuiZonefreeScheduler()), map(() => this.directive.linesLimit > 1\n            ? this.getOverflowIndexMultiline()\n            : this.getOverflowIndex(Array.from(this.el.children))), distinctUntilChanged(), tuiZoneOptimized(), share());\n    }\n    getOverflowIndex(children) {\n        const { computedSide, itemsLimit } = this.directive;\n        const { clientWidth } = this.el;\n        const items = Array.from(children, ({ clientWidth }) => clientWidth);\n        const index = computedSide === 'start' ? 0 : items.length - 1;\n        const more = children[index]?.tagName === 'SPAN' ? (items[index] ?? 0) : 0;\n        const total = items.reduce((sum, width) => sum + width, 0) - more;\n        if (total <= clientWidth && itemsLimit >= items.length) {\n            return computedSide === 'end' ? itemsLimit : 0;\n        }\n        return computedSide === 'start'\n            ? this.getIndexStart(items, total, more)\n            : this.getIndexEnd(items, total, more);\n    }\n    getIndexStart(items, total, more) {\n        const { required, itemsLimit } = this.directive;\n        const { clientWidth } = this.el;\n        const min = Number.isFinite(itemsLimit) ? items.length - itemsLimit - 1 : 0;\n        const last = items.length - 1;\n        const mandatory = required === -1 ? last : required;\n        for (let i = 1; i < last; i++) {\n            if (i === mandatory + 1) {\n                continue;\n            }\n            total -= items[i] ?? 0;\n            if (total + more <= clientWidth) {\n                return tuiClamp(i, mandatory < min ? min + 1 : min, items.length);\n            }\n        }\n        return items.length;\n    }\n    getIndexEnd(items, total, more) {\n        const { required, itemsLimit } = this.directive;\n        const { clientWidth } = this.el;\n        const max = itemsLimit > required ? itemsLimit - 1 : itemsLimit - 2;\n        const last = items.length - 1;\n        const mandatory = required === -1 ? 0 : required;\n        for (let i = last - 1; i > 0; i--) {\n            if (i === mandatory) {\n                continue;\n            }\n            total -= items[i] ?? 0;\n            if (total + more <= clientWidth) {\n                return tuiClamp(i - 1, -1, max);\n            }\n        }\n        return -1;\n    }\n    getOverflowIndexMultiline() {\n        const { children } = this.el;\n        const { linesLimit, itemsLimit } = this.directive;\n        const items = Array.from(children);\n        const rows = new Set(items.map((item) => item.offsetTop));\n        const offset = Array.from(rows)[linesLimit - 1];\n        const firstItemLastRow = items.findIndex((i) => i.offsetTop === offset);\n        const lastRow = items.slice(firstItemLastRow);\n        const index = firstItemLastRow + this.getOverflowIndex(lastRow);\n        return Math.min(itemsLimit - 1, index);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiItemsWithMoreService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiItemsWithMoreService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiItemsWithMoreService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return []; } });\n\nclass TuiMore {\n    static ngTemplateContextGuard(_dir, _ctx) {\n        return true;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMore, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiMore, isStandalone: true, selector: \"[tuiMore]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiMore, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[tuiMore]',\n                }]\n        }] });\n\nclass TuiItemsWithMoreComponent {\n    constructor() {\n        this.items = EMPTY_QUERY;\n        this.directive = inject(TuiItemsWithMoreDirective);\n        this.lastIndexChange = inject(TuiItemsWithMoreService);\n        this.lastIndex = toSignal(this.lastIndexChange, {\n            initialValue: 0,\n        });\n    }\n    get isMoreHidden() {\n        const { computedSide } = this.directive;\n        return ((this.lastIndex() >= this.items.length - 1 && computedSide === 'end') ||\n            (!this.lastIndex() && computedSide === 'start'));\n    }\n    isHidden(index) {\n        const { computedSide, required } = this.directive;\n        return ((index > this.lastIndex() && index !== required && computedSide === 'end') ||\n            (index < this.lastIndex() && index !== required && computedSide === 'start'));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiItemsWithMoreComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: TuiItemsWithMoreComponent, isStandalone: true, selector: \"tui-items-with-more\", outputs: { lastIndexChange: \"lastIndexChange\" }, providers: [\n            MutationObserverService,\n            ResizeObserverService,\n            TuiItemsWithMoreService,\n            {\n                provide: WA_MUTATION_OBSERVER_INIT,\n                useValue: {\n                    childList: true,\n                    characterData: true,\n                    subtree: true,\n                },\n            },\n        ], queries: [{ propertyName: \"more\", first: true, predicate: TuiMore, descendants: true, read: TemplateRef }, { propertyName: \"items\", predicate: TuiItem, descendants: true, read: TemplateRef }], hostDirectives: [{ directive: TuiItemsWithMoreDirective, inputs: [\"itemsLimit\", \"itemsLimit\", \"required\", \"required\", \"side\", \"side\", \"linesLimit\", \"linesLimit\"] }], ngImport: i0, template: \"<ng-container *ngIf=\\\"items.changes | async\\\" />\\n<ng-container\\n    *ngIf=\\\"directive.side === 'start'\\\"\\n    [ngTemplateOutlet]=\\\"template\\\"\\n/>\\n<div\\n    *ngFor=\\\"let item of items; let index = index\\\"\\n    class=\\\"t-item\\\"\\n    [class.t-item_hidden]=\\\"isHidden(index)\\\"\\n>\\n    <ng-container *ngTemplateOutlet=\\\"item\\\" />\\n</div>\\n<ng-container\\n    *ngIf=\\\"directive.side === 'end'\\\"\\n    [ngTemplateOutlet]=\\\"template\\\"\\n/>\\n<ng-template #template>\\n    <span\\n        *ngIf=\\\"!isMoreHidden\\\"\\n        class=\\\"t-item t-item_more\\\"\\n    >\\n        <ng-container\\n            [ngTemplateOutlet]=\\\"more || null\\\"\\n            [ngTemplateOutletContext]=\\\"{$implicit: lastIndex()}\\\"\\n        />\\n    </span>\\n</ng-template>\\n\", styles: [\":host{position:relative;display:flex;min-inline-size:0;flex:1;align-items:center;white-space:nowrap}:host._multiline{flex-wrap:wrap}.t-item{flex:0 0 auto}.t-item_hidden{position:absolute;bottom:0;visibility:hidden}:host._multiline .t-item_more:not(:empty){min-inline-size:var(--t-min-width, 0)}\\n\"], dependencies: [{ kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: TuiItemsWithMoreComponent, decorators: [{\n            type: Component,\n            args: [{ standalone: true, selector: 'tui-items-with-more', imports: [AsyncPipe, NgForOf, NgIf, NgTemplateOutlet], changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        MutationObserverService,\n                        ResizeObserverService,\n                        TuiItemsWithMoreService,\n                        {\n                            provide: WA_MUTATION_OBSERVER_INIT,\n                            useValue: {\n                                childList: true,\n                                characterData: true,\n                                subtree: true,\n                            },\n                        },\n                    ], hostDirectives: [\n                        {\n                            directive: TuiItemsWithMoreDirective,\n                            inputs: ['itemsLimit', 'required', 'side', 'linesLimit'],\n                        },\n                    ], template: \"<ng-container *ngIf=\\\"items.changes | async\\\" />\\n<ng-container\\n    *ngIf=\\\"directive.side === 'start'\\\"\\n    [ngTemplateOutlet]=\\\"template\\\"\\n/>\\n<div\\n    *ngFor=\\\"let item of items; let index = index\\\"\\n    class=\\\"t-item\\\"\\n    [class.t-item_hidden]=\\\"isHidden(index)\\\"\\n>\\n    <ng-container *ngTemplateOutlet=\\\"item\\\" />\\n</div>\\n<ng-container\\n    *ngIf=\\\"directive.side === 'end'\\\"\\n    [ngTemplateOutlet]=\\\"template\\\"\\n/>\\n<ng-template #template>\\n    <span\\n        *ngIf=\\\"!isMoreHidden\\\"\\n        class=\\\"t-item t-item_more\\\"\\n    >\\n        <ng-container\\n            [ngTemplateOutlet]=\\\"more || null\\\"\\n            [ngTemplateOutletContext]=\\\"{$implicit: lastIndex()}\\\"\\n        />\\n    </span>\\n</ng-template>\\n\", styles: [\":host{position:relative;display:flex;min-inline-size:0;flex:1;align-items:center;white-space:nowrap}:host._multiline{flex-wrap:wrap}.t-item{flex:0 0 auto}.t-item_hidden{position:absolute;bottom:0;visibility:hidden}:host._multiline .t-item_more:not(:empty){min-inline-size:var(--t-min-width, 0)}\\n\"] }]\n        }], propDecorators: { items: [{\n                type: ContentChildren,\n                args: [TuiItem, { read: TemplateRef, descendants: true }]\n            }], more: [{\n                type: ContentChild,\n                args: [TuiMore, { read: TemplateRef }]\n            }], lastIndexChange: [{\n                type: Output\n            }] } });\n\nconst TuiItemsWithMore = [\n    TuiItemsWithMoreComponent,\n    TuiItemsWithMoreDirective,\n    TuiMore,\n    TuiItem,\n];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TuiItemsWithMore, TuiItemsWithMoreComponent, TuiItemsWithMoreDirective, TuiItemsWithMoreService, TuiMore };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,+BAA+B;AACvD,SAASC,SAAS,EAAEC,OAAO,EAAEC,IAAI,EAAEC,gBAAgB,QAAQ,iBAAiB;AAC5E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,MAAM,QAAQ,eAAe;AAC5J,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,uBAAuB,EAAEC,yBAAyB,QAAQ,gCAAgC;AACnG,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,YAAY,EAAEC,GAAG,EAAEC,oBAAoB,EAAEC,KAAK,QAAQ,MAAM;AACjG,SAASC,oBAAoB,EAAEC,gBAAgB,QAAQ,2BAA2B;AAClF,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,SAAAE,kDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAqBiD/B,EAAE,CAAAiC,kBAAA,EAgJmV,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhJtV/B,EAAE,CAAAiC,kBAAA,KAgJqb,CAAC;EAAA;EAAA,IAAAF,EAAA;IAhJxb/B,EAAE,CAAAmC,aAAA;IAAA,MAAAC,WAAA,GAAFpC,EAAE,CAAAqC,WAAA;IAAFrC,EAAE,CAAAsC,UAAA,qBAAAF,WAgJib,CAAC;EAAA;AAAA;AAAA,SAAAG,wDAAAR,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhJpb/B,EAAE,CAAAiC,kBAAA,EAgJymB,CAAC;EAAA;AAAA;AAAA,SAAAO,yCAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhJ5mB/B,EAAE,CAAAyC,cAAA,YAgJwjB,CAAC;IAhJ3jBzC,EAAE,CAAA0C,UAAA,IAAAH,uDAAA,yBAgJymB,CAAC;IAhJ5mBvC,EAAE,CAAA2C,YAAA,CAgJinB,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAa,OAAA,GAAAZ,GAAA,CAAAH,SAAA;IAAA,MAAAgB,QAAA,GAAAb,GAAA,CAAAc,KAAA;IAAA,MAAAC,MAAA,GAhJpnB/C,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAAgD,WAAA,kBAAAD,MAAA,CAAAE,QAAA,CAAAJ,QAAA,CAgJqjB,CAAC;IAhJxjB7C,EAAE,CAAAkD,SAAA,CAgJomB,CAAC;IAhJvmBlD,EAAE,CAAAsC,UAAA,qBAAAM,OAgJomB,CAAC;EAAA;AAAA;AAAA,SAAAO,kDAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhJvmB/B,EAAE,CAAAiC,kBAAA,KAgJitB,CAAC;EAAA;EAAA,IAAAF,EAAA;IAhJptB/B,EAAE,CAAAmC,aAAA;IAAA,MAAAC,WAAA,GAAFpC,EAAE,CAAAqC,WAAA;IAAFrC,EAAE,CAAAsC,UAAA,qBAAAF,WAgJ6sB,CAAC;EAAA;AAAA;AAAA,SAAAgB,wDAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhJhtB/B,EAAE,CAAAyC,cAAA,aAgJm0B,CAAC;IAhJt0BzC,EAAE,CAAAiC,kBAAA,KAgJ29B,CAAC;IAhJ99BjC,EAAE,CAAA2C,YAAA,CAgJw+B,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAgB,MAAA,GAhJ3+B/C,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAAkD,SAAA,CAgJ24B,CAAC;IAhJ94BlD,EAAE,CAAAsC,UAAA,qBAAAS,MAAA,CAAAM,IAAA,QAgJ24B,CAAC,4BAhJ94BrD,EAAE,CAAAsD,eAAA,IAAA3B,GAAA,EAAAoB,MAAA,CAAAQ,SAAA,GAgJ+8B,CAAC;EAAA;AAAA;AAAA,SAAAC,iDAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhJl9B/B,EAAE,CAAA0C,UAAA,IAAAU,uDAAA,iBAgJm0B,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAgB,MAAA,GAhJt0B/C,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAAsC,UAAA,UAAAS,MAAA,CAAAU,YAgJoxB,CAAC;EAAA;AAAA;AAnK53B,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG5C,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAAC6C,UAAU,GAAGC,QAAQ;IAC1B,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB;IACA,IAAI,CAACC,OAAO,GAAG,IAAIjD,OAAO,CAAC,CAAC;EAChC;EACA,IAAIkD,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACH,UAAU,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAACC,IAAI;EAClD;EACAG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,OAAO,CAACG,IAAI,CAAC,CAAC;EACvB;EACAC,QAAQA,CAAA,EAAG;IACP,OAAOC,IAAI,CAACC,GAAG,CAAC,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACd,EAAE,CAACe,QAAQ,EAAE,CAAC;MAAEC;IAAY,CAAC,KAAKA,WAAW,CAAC,CAAC;EACtF;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,kCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFrB,yBAAyB;IAAA,CAAmD;EAAE;EACzL;IAAS,IAAI,CAACsB,IAAI,kBAD+EhF,EAAE,CAAAiF,iBAAA;MAAAC,IAAA,EACJxB,yBAAyB;MAAAyB,QAAA;MAAAC,YAAA,WAAAC,uCAAAtD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADvB/B,EAAE,CAAAsF,WAAA,kBACJtD,GAAA,CAAAsC,QAAA,CAAS,CAAC,MAAc,CAAC;UADvBtE,EAAE,CAAAgD,WAAA,eAAAhB,GAAA,CAAAgC,UAAA,GACS,CAAW,CAAC;QAAA;MAAA;MAAAuB,MAAA;QAAA1B,UAAA;QAAAE,QAAA;QAAAC,UAAA;QAAAC,IAAA;MAAA;MAAAuB,UAAA;MAAAC,QAAA,GADvBzF,EAAE,CAAA0F,oBAAA;IAAA,EAC2R;EAAE;AACpY;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG3F,EAAE,CAAA4F,iBAAA,CAGXlC,yBAAyB,EAAc,CAAC;IACxHwB,IAAI,EAAEjF,SAAS;IACf4F,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBM,IAAI,EAAE;QACF,oBAAoB,EAAE,gBAAgB;QACtC,0BAA0B,EAAE;MAChC;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEjC,UAAU,EAAE,CAAC;MAC3BqB,IAAI,EAAEhF;IACV,CAAC,CAAC;IAAE6D,QAAQ,EAAE,CAAC;MACXmB,IAAI,EAAEhF;IACV,CAAC,CAAC;IAAE8D,UAAU,EAAE,CAAC;MACbkB,IAAI,EAAEhF;IACV,CAAC,CAAC;IAAE+D,IAAI,EAAE,CAAC;MACPiB,IAAI,EAAEhF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6F,uBAAuB,SAAS7E,UAAU,CAAC;EAC7CyC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAEqC,UAAU,IAAK,IAAI,CAACC,OAAO,CAACC,SAAS,CAACF,UAAU,CAAC,CAAC;IACzD,IAAI,CAACpC,EAAE,GAAG5C,gBAAgB,CAAC,CAAC;IAC5B,IAAI,CAACmF,SAAS,GAAGhG,MAAM,CAACuD,yBAAyB,CAAC;IAClD,IAAI,CAACuC,OAAO,GAAG9E,KAAK,CAAC,IAAI,CAACgF,SAAS,CAACjC,OAAO,EAAE/D,MAAM,CAACS,uBAAuB,EAAE;MAAEwF,IAAI,EAAE;IAAK,CAAC,CAAC,EAAEjG,MAAM,CAACW,qBAAqB,EAAE;MAAEsF,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAACjF,YAAY,CAAC,CAAC,EAAEI,oBAAoB,CAAC,CAAC,CAAC,EAAEH,GAAG,CAAC,MAAM,IAAI,CAAC8E,SAAS,CAACnC,UAAU,GAAG,CAAC,GAC5N,IAAI,CAACsC,yBAAyB,CAAC,CAAC,GAChC,IAAI,CAACC,gBAAgB,CAAC9B,KAAK,CAACC,IAAI,CAAC,IAAI,CAACd,EAAE,CAACe,QAAQ,CAAC,CAAC,CAAC,EAAErD,oBAAoB,CAAC,CAAC,EAAEG,gBAAgB,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC,CAAC;EACpH;EACAgF,gBAAgBA,CAAC5B,QAAQ,EAAE;IACvB,MAAM;MAAER,YAAY;MAAEN;IAAW,CAAC,GAAG,IAAI,CAACsC,SAAS;IACnD,MAAM;MAAEvB;IAAY,CAAC,GAAG,IAAI,CAAChB,EAAE;IAC/B,MAAM4C,KAAK,GAAG/B,KAAK,CAACC,IAAI,CAACC,QAAQ,EAAE,CAAC;MAAEC;IAAY,CAAC,KAAKA,WAAW,CAAC;IACpE,MAAM9B,KAAK,GAAGqB,YAAY,KAAK,OAAO,GAAG,CAAC,GAAGqC,KAAK,CAACC,MAAM,GAAG,CAAC;IAC7D,MAAMpD,IAAI,GAAGsB,QAAQ,CAAC7B,KAAK,CAAC,EAAE4D,OAAO,KAAK,MAAM,GAAIF,KAAK,CAAC1D,KAAK,CAAC,IAAI,CAAC,GAAI,CAAC;IAC1E,MAAM6D,KAAK,GAAGH,KAAK,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,GAAGzD,IAAI;IACjE,IAAIsD,KAAK,IAAI/B,WAAW,IAAIf,UAAU,IAAI2C,KAAK,CAACC,MAAM,EAAE;MACpD,OAAOtC,YAAY,KAAK,KAAK,GAAGN,UAAU,GAAG,CAAC;IAClD;IACA,OAAOM,YAAY,KAAK,OAAO,GACzB,IAAI,CAAC4C,aAAa,CAACP,KAAK,EAAEG,KAAK,EAAEtD,IAAI,CAAC,GACtC,IAAI,CAAC2D,WAAW,CAACR,KAAK,EAAEG,KAAK,EAAEtD,IAAI,CAAC;EAC9C;EACA0D,aAAaA,CAACP,KAAK,EAAEG,KAAK,EAAEtD,IAAI,EAAE;IAC9B,MAAM;MAAEU,QAAQ;MAAEF;IAAW,CAAC,GAAG,IAAI,CAACsC,SAAS;IAC/C,MAAM;MAAEvB;IAAY,CAAC,GAAG,IAAI,CAAChB,EAAE;IAC/B,MAAMqD,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACtD,UAAU,CAAC,GAAG2C,KAAK,CAACC,MAAM,GAAG5C,UAAU,GAAG,CAAC,GAAG,CAAC;IAC3E,MAAMuD,IAAI,GAAGZ,KAAK,CAACC,MAAM,GAAG,CAAC;IAC7B,MAAMY,SAAS,GAAGtD,QAAQ,KAAK,CAAC,CAAC,GAAGqD,IAAI,GAAGrD,QAAQ;IACnD,KAAK,IAAIuD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,EAAEE,CAAC,EAAE,EAAE;MAC3B,IAAIA,CAAC,KAAKD,SAAS,GAAG,CAAC,EAAE;QACrB;MACJ;MACAV,KAAK,IAAIH,KAAK,CAACc,CAAC,CAAC,IAAI,CAAC;MACtB,IAAIX,KAAK,GAAGtD,IAAI,IAAIuB,WAAW,EAAE;QAC7B,OAAOlD,QAAQ,CAAC4F,CAAC,EAAED,SAAS,GAAGJ,GAAG,GAAGA,GAAG,GAAG,CAAC,GAAGA,GAAG,EAAET,KAAK,CAACC,MAAM,CAAC;MACrE;IACJ;IACA,OAAOD,KAAK,CAACC,MAAM;EACvB;EACAO,WAAWA,CAACR,KAAK,EAAEG,KAAK,EAAEtD,IAAI,EAAE;IAC5B,MAAM;MAAEU,QAAQ;MAAEF;IAAW,CAAC,GAAG,IAAI,CAACsC,SAAS;IAC/C,MAAM;MAAEvB;IAAY,CAAC,GAAG,IAAI,CAAChB,EAAE;IAC/B,MAAMY,GAAG,GAAGX,UAAU,GAAGE,QAAQ,GAAGF,UAAU,GAAG,CAAC,GAAGA,UAAU,GAAG,CAAC;IACnE,MAAMuD,IAAI,GAAGZ,KAAK,CAACC,MAAM,GAAG,CAAC;IAC7B,MAAMY,SAAS,GAAGtD,QAAQ,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGA,QAAQ;IAChD,KAAK,IAAIuD,CAAC,GAAGF,IAAI,GAAG,CAAC,EAAEE,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC/B,IAAIA,CAAC,KAAKD,SAAS,EAAE;QACjB;MACJ;MACAV,KAAK,IAAIH,KAAK,CAACc,CAAC,CAAC,IAAI,CAAC;MACtB,IAAIX,KAAK,GAAGtD,IAAI,IAAIuB,WAAW,EAAE;QAC7B,OAAOlD,QAAQ,CAAC4F,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE9C,GAAG,CAAC;MACnC;IACJ;IACA,OAAO,CAAC,CAAC;EACb;EACA8B,yBAAyBA,CAAA,EAAG;IACxB,MAAM;MAAE3B;IAAS,CAAC,GAAG,IAAI,CAACf,EAAE;IAC5B,MAAM;MAAEI,UAAU;MAAEH;IAAW,CAAC,GAAG,IAAI,CAACsC,SAAS;IACjD,MAAMK,KAAK,GAAG/B,KAAK,CAACC,IAAI,CAACC,QAAQ,CAAC;IAClC,MAAM4C,IAAI,GAAG,IAAIC,GAAG,CAAChB,KAAK,CAACnF,GAAG,CAAEoG,IAAI,IAAKA,IAAI,CAACC,SAAS,CAAC,CAAC;IACzD,MAAMC,MAAM,GAAGlD,KAAK,CAACC,IAAI,CAAC6C,IAAI,CAAC,CAACvD,UAAU,GAAG,CAAC,CAAC;IAC/C,MAAM4D,gBAAgB,GAAGpB,KAAK,CAACqB,SAAS,CAAEP,CAAC,IAAKA,CAAC,CAACI,SAAS,KAAKC,MAAM,CAAC;IACvE,MAAMG,OAAO,GAAGtB,KAAK,CAACuB,KAAK,CAACH,gBAAgB,CAAC;IAC7C,MAAM9E,KAAK,GAAG8E,gBAAgB,GAAG,IAAI,CAACrB,gBAAgB,CAACuB,OAAO,CAAC;IAC/D,OAAOvD,IAAI,CAAC0C,GAAG,CAACpD,UAAU,GAAG,CAAC,EAAEf,KAAK,CAAC;EAC1C;EACA;IAAS,IAAI,CAAC+B,IAAI,YAAAmD,gCAAAjD,CAAA;MAAA,YAAAA,CAAA,IAAyFgB,uBAAuB;IAAA,CAAoD;EAAE;EACxL;IAAS,IAAI,CAACkC,KAAK,kBA3F8EjI,EAAE,CAAAkI,kBAAA;MAAAC,KAAA,EA2FYpC,uBAAuB;MAAAqC,OAAA,EAAvBrC,uBAAuB,CAAAlB;IAAA,EAAG;EAAE;AAC/I;AACA;EAAA,QAAAc,SAAA,oBAAAA,SAAA,KA7FqG3F,EAAE,CAAA4F,iBAAA,CA6FXG,uBAAuB,EAAc,CAAC;IACtHb,IAAI,EAAE9E;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAMiI,OAAO,CAAC;EACV,OAAOC,sBAAsBA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACtC,OAAO,IAAI;EACf;EACA;IAAS,IAAI,CAAC3D,IAAI,YAAA4D,gBAAA1D,CAAA;MAAA,YAAAA,CAAA,IAAyFsD,OAAO;IAAA,CAAmD;EAAE;EACvK;IAAS,IAAI,CAACrD,IAAI,kBAtG+EhF,EAAE,CAAAiF,iBAAA;MAAAC,IAAA,EAsGJmD,OAAO;MAAAK,SAAA;MAAAlD,UAAA;IAAA,EAA4D;EAAE;AACxK;AACA;EAAA,QAAAG,SAAA,oBAAAA,SAAA,KAxGqG3F,EAAE,CAAA4F,iBAAA,CAwGXyC,OAAO,EAAc,CAAC;IACtGnD,IAAI,EAAEjF,SAAS;IACf4F,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBmD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMC,yBAAyB,CAAC;EAC5BjF,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6C,KAAK,GAAGzF,WAAW;IACxB,IAAI,CAACoF,SAAS,GAAGhG,MAAM,CAACuD,yBAAyB,CAAC;IAClD,IAAI,CAACmF,eAAe,GAAG1I,MAAM,CAAC4F,uBAAuB,CAAC;IACtD,IAAI,CAACxC,SAAS,GAAG5C,QAAQ,CAAC,IAAI,CAACkI,eAAe,EAAE;MAC5CC,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA,IAAIrF,YAAYA,CAAA,EAAG;IACf,MAAM;MAAEU;IAAa,CAAC,GAAG,IAAI,CAACgC,SAAS;IACvC,OAAS,IAAI,CAAC5C,SAAS,CAAC,CAAC,IAAI,IAAI,CAACiD,KAAK,CAACC,MAAM,GAAG,CAAC,IAAItC,YAAY,KAAK,KAAK,IACvE,CAAC,IAAI,CAACZ,SAAS,CAAC,CAAC,IAAIY,YAAY,KAAK,OAAQ;EACvD;EACAlB,QAAQA,CAACH,KAAK,EAAE;IACZ,MAAM;MAAEqB,YAAY;MAAEJ;IAAS,CAAC,GAAG,IAAI,CAACoC,SAAS;IACjD,OAASrD,KAAK,GAAG,IAAI,CAACS,SAAS,CAAC,CAAC,IAAIT,KAAK,KAAKiB,QAAQ,IAAII,YAAY,KAAK,KAAK,IAC5ErB,KAAK,GAAG,IAAI,CAACS,SAAS,CAAC,CAAC,IAAIT,KAAK,KAAKiB,QAAQ,IAAII,YAAY,KAAK,OAAQ;EACpF;EACA;IAAS,IAAI,CAACU,IAAI,YAAAkE,kCAAAhE,CAAA;MAAA,YAAAA,CAAA,IAAyF6D,yBAAyB;IAAA,CAAmD;EAAE;EACzL;IAAS,IAAI,CAACI,IAAI,kBApI+EhJ,EAAE,CAAAiJ,iBAAA;MAAA/D,IAAA,EAoIJ0D,yBAAyB;MAAAF,SAAA;MAAAQ,cAAA,WAAAC,yCAAApH,EAAA,EAAAC,GAAA,EAAAoH,QAAA;QAAA,IAAArH,EAAA;UApIvB/B,EAAE,CAAAqJ,cAAA,CAAAD,QAAA,EAgJlCf,OAAO,KAA2BhI,WAAW;UAhJbL,EAAE,CAAAqJ,cAAA,CAAAD,QAAA,EAgJmDzJ,OAAO,KAA2BU,WAAW;QAAA;QAAA,IAAA0B,EAAA;UAAA,IAAAuH,EAAA;UAhJlGtJ,EAAE,CAAAuJ,cAAA,CAAAD,EAAA,GAAFtJ,EAAE,CAAAwJ,WAAA,QAAAxH,GAAA,CAAAqB,IAAA,GAAAiG,EAAA,CAAAG,KAAA;UAAFzJ,EAAE,CAAAuJ,cAAA,CAAAD,EAAA,GAAFtJ,EAAE,CAAAwJ,WAAA,QAAAxH,GAAA,CAAAwE,KAAA,GAAA8C,EAAA;QAAA;MAAA;MAAAI,OAAA;QAAAb,eAAA;MAAA;MAAArD,UAAA;MAAAC,QAAA,GAAFzF,EAAE,CAAA2J,kBAAA,CAoIwI,CACnO/I,uBAAuB,EACvBE,qBAAqB,EACrBiF,uBAAuB,EACvB;QACI6D,OAAO,EAAE/I,yBAAyB;QAClCgJ,QAAQ,EAAE;UACNC,SAAS,EAAE,IAAI;UACfC,aAAa,EAAE,IAAI;UACnBC,OAAO,EAAE;QACb;MACJ,CAAC,CACJ,GAhJ4FhK,EAAE,CAAAiK,uBAAA;QAAA9D,SAAA,EAgJmIzC,yBAAyB;QAAA6B,MAAA;MAAA,KAhJ9JvF,EAAE,CAAAkK,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAxI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/B,EAAE,CAAA0C,UAAA,IAAAZ,iDAAA,yBAgJmV,CAAC;UAhJtV9B,EAAE,CAAAwK,MAAA;UAAFxK,EAAE,CAAA0C,UAAA,IAAAR,iDAAA,yBAgJqb,CAAC,IAAAM,wCAAA,gBAAkI,CAAC,IAAAW,iDAAA,yBAAwJ,CAAC,IAAAK,gDAAA,gCAhJptBxD,EAAE,CAAAyK,sBAgJ0uB,CAAC;QAAA;QAAA,IAAA1I,EAAA;UAhJ7uB/B,EAAE,CAAAsC,UAAA,SAAFtC,EAAE,CAAA0K,WAAA,OAAA1I,GAAA,CAAAwE,KAAA,CAAAmE,OAAA,CAgJ8U,CAAC;UAhJjV3K,EAAE,CAAAkD,SAAA,EAgJ0Y,CAAC;UAhJ7YlD,EAAE,CAAAsC,UAAA,SAAAN,GAAA,CAAAmE,SAAA,CAAAlC,IAAA,YAgJ0Y,CAAC;UAhJ7YjE,EAAE,CAAAkD,SAAA,CAgJ6d,CAAC;UAhJhelD,EAAE,CAAAsC,UAAA,YAAAN,GAAA,CAAAwE,KAgJ6d,CAAC;UAhJhexG,EAAE,CAAAkD,SAAA,CAgJsqB,CAAC;UAhJzqBlD,EAAE,CAAAsC,UAAA,SAAAN,GAAA,CAAAmE,SAAA,CAAAlC,IAAA,UAgJsqB,CAAC;QAAA;MAAA;MAAA2G,YAAA,GAAkrBhL,SAAS,EAA8CC,OAAO,EAAmHC,IAAI,EAA6FC,gBAAgB;MAAA8K,MAAA;MAAAC,eAAA;IAAA,EAA+K;EAAE;AACn5D;AACA;EAAA,QAAAnF,SAAA,oBAAAA,SAAA,KAlJqG3F,EAAE,CAAA4F,iBAAA,CAkJXgD,yBAAyB,EAAc,CAAC;IACxH1D,IAAI,EAAE5E,SAAS;IACfuF,IAAI,EAAE,CAAC;MAAEL,UAAU,EAAE,IAAI;MAAEmD,QAAQ,EAAE,qBAAqB;MAAEoC,OAAO,EAAE,CAACnL,SAAS,EAAEC,OAAO,EAAEC,IAAI,EAAEC,gBAAgB,CAAC;MAAE+K,eAAe,EAAEvK,uBAAuB,CAACyK,MAAM;MAAEC,SAAS,EAAE,CACnKrK,uBAAuB,EACvBE,qBAAqB,EACrBiF,uBAAuB,EACvB;QACI6D,OAAO,EAAE/I,yBAAyB;QAClCgJ,QAAQ,EAAE;UACNC,SAAS,EAAE,IAAI;UACfC,aAAa,EAAE,IAAI;UACnBC,OAAO,EAAE;QACb;MACJ,CAAC,CACJ;MAAEkB,cAAc,EAAE,CACf;QACI/E,SAAS,EAAEzC,yBAAyB;QACpC6B,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY;MAC3D,CAAC,CACJ;MAAE+E,QAAQ,EAAE,ytBAAytB;MAAEO,MAAM,EAAE,CAAC,0SAA0S;IAAE,CAAC;EAC1iC,CAAC,CAAC,QAAkB;IAAErE,KAAK,EAAE,CAAC;MACtBtB,IAAI,EAAE1E,eAAe;MACrBqF,IAAI,EAAE,CAAClG,OAAO,EAAE;QAAEwL,IAAI,EAAE9K,WAAW;QAAE+K,WAAW,EAAE;MAAK,CAAC;IAC5D,CAAC,CAAC;IAAE/H,IAAI,EAAE,CAAC;MACP6B,IAAI,EAAEzE,YAAY;MAClBoF,IAAI,EAAE,CAACwC,OAAO,EAAE;QAAE8C,IAAI,EAAE9K;MAAY,CAAC;IACzC,CAAC,CAAC;IAAEwI,eAAe,EAAE,CAAC;MAClB3D,IAAI,EAAExE;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2K,gBAAgB,GAAG,CACrBzC,yBAAyB,EACzBlF,yBAAyB,EACzB2E,OAAO,EACP1I,OAAO,CACV;;AAED;AACA;AACA;;AAEA,SAAS0L,gBAAgB,EAAEzC,yBAAyB,EAAElF,yBAAyB,EAAEqC,uBAAuB,EAAEsC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}