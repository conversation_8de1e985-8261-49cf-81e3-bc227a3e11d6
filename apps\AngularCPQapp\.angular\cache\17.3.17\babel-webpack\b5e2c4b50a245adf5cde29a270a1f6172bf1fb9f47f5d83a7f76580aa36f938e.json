{"ast": null, "code": "import { CHAR_HYPHEN } from '@taiga-ui/cdk/constants';\nimport { tuiRoundWith } from '@taiga-ui/cdk/utils/math';\nimport { TUI_DEFAULT_NUMBER_FORMAT } from '@taiga-ui/core/tokens';\nimport { tuiIsNumber } from '@taiga-ui/cdk/utils/miscellaneous';\n\n/**\n * Convert number to string with replacing exponent part on decimals\n *\n * @param value the number\n * @return string representation of a number\n */\nfunction tuiNumberToStringWithoutExp(value) {\n  const valueAsString = String(value);\n  const [numberPart, expPart] = valueAsString.split('e-');\n  let valueWithoutExp = valueAsString;\n  if (expPart) {\n    const [, fractionalPart = ''] = numberPart?.split('.') ?? [];\n    const decimalDigits = Number(expPart) + (fractionalPart?.length || 0);\n    valueWithoutExp = value.toFixed(decimalDigits);\n  }\n  return valueWithoutExp;\n}\n\n/**\n * Return fractional part of number\n *\n * @param value the number\n * @param precision number of digits of decimal part, null to keep untouched\n * @return the fractional part of number\n */\nfunction tuiGetFractionPartPadded(value, precision) {\n  const [, fractionPartPadded = ''] = tuiNumberToStringWithoutExp(value).split('.');\n  return tuiIsNumber(precision) ? fractionPartPadded.slice(0, Math.max(0, precision)) : fractionPartPadded;\n}\n\n/**\n * Formats number adding a thousand separators and correct decimal separator\n * padding decimal part with zeroes to given length\n *\n * @param value the input number\n * @param settings See {@link TuiNumberFormatSettings}\n * @return the formatted string\n */\nfunction tuiFormatNumber(value, settings = {}) {\n  const {\n    precision,\n    decimalSeparator,\n    thousandSeparator,\n    decimalMode,\n    rounding\n  } = {\n    ...TUI_DEFAULT_NUMBER_FORMAT,\n    decimalMode: 'always',\n    precision: Infinity,\n    ...settings\n  };\n  const rounded = Number.isFinite(precision) ? tuiRoundWith({\n    value,\n    precision,\n    method: rounding\n  }) : value;\n  const integerPartString = String(Math.floor(Math.abs(rounded)));\n  let fractionPartPadded = tuiGetFractionPartPadded(rounded, precision);\n  const hasFraction = Number(fractionPartPadded) > 0;\n  if (Number.isFinite(precision)) {\n    if (decimalMode === 'always' || hasFraction && decimalMode === 'pad') {\n      const zeroPaddingSize = Math.max(precision - fractionPartPadded.length, 0);\n      const zeroPartString = '0'.repeat(zeroPaddingSize);\n      fractionPartPadded = `${fractionPartPadded}${zeroPartString}`;\n    } else {\n      fractionPartPadded = fractionPartPadded.replace(/0*$/, '');\n    }\n  }\n  const remainder = integerPartString.length % 3;\n  const sign = value < 0 ? CHAR_HYPHEN : '';\n  let result = sign + integerPartString.charAt(0);\n  for (let i = 1; i < integerPartString.length; i++) {\n    if (i % 3 === remainder && integerPartString.length > 3) {\n      result += thousandSeparator;\n    }\n    result += integerPartString.charAt(i);\n  }\n  return fractionPartPadded ? result + decimalSeparator + fractionPartPadded : result;\n}\n\n/**\n * Converts a string to an HSL color\n * @param value string to convert\n * @return HSL color string\n */\n// @bad TODO: convert stringHashToHsl to stringHashToRgb\nfunction tuiStringHashToHsl(value) {\n  if (value === '') {\n    return '';\n  }\n  let hash = 0;\n  for (let i = 0; i < value.length; i++) {\n    hash = value.charCodeAt(i) + ((hash << 5) - hash);\n    hash &= hash;\n  }\n  const hue = hash % 360;\n  const saturation = 60 + hash % 5;\n  const lightness = 80 + hash % 5;\n  return `hsl(${hue},${saturation}%,${lightness}%)`;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiFormatNumber, tuiGetFractionPartPadded, tuiNumberToStringWithoutExp, tuiStringHashToHsl };", "map": {"version": 3, "names": ["CHAR_HYPHEN", "tuiRoundWith", "TUI_DEFAULT_NUMBER_FORMAT", "tuiIsNumber", "tuiNumberToStringWithoutExp", "value", "valueAsString", "String", "numberPart", "expPart", "split", "valueWithoutExp", "fractionalPart", "decimalDigits", "Number", "length", "toFixed", "tuiGetFractionPartPadded", "precision", "fractionPartPadded", "slice", "Math", "max", "tuiFormatNumber", "settings", "decimalSeparator", "thousandSeparator", "decimalMode", "rounding", "Infinity", "rounded", "isFinite", "method", "integerPartString", "floor", "abs", "hasFraction", "zeroPaddingSize", "zeroPartString", "repeat", "replace", "remainder", "sign", "result", "char<PERSON>t", "i", "tuiStringHashToHsl", "hash", "charCodeAt", "hue", "saturation", "lightness"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet_CRM/apps/AngularCPQapp/node_modules/@taiga-ui/core/fesm2022/taiga-ui-core-utils-format.mjs"], "sourcesContent": ["import { CHAR_HYPHEN } from '@taiga-ui/cdk/constants';\nimport { tuiRoundWith } from '@taiga-ui/cdk/utils/math';\nimport { TUI_DEFAULT_NUMBER_FORMAT } from '@taiga-ui/core/tokens';\nimport { tuiIsNumber } from '@taiga-ui/cdk/utils/miscellaneous';\n\n/**\n * Convert number to string with replacing exponent part on decimals\n *\n * @param value the number\n * @return string representation of a number\n */\nfunction tuiNumberToStringWithoutExp(value) {\n    const valueAsString = String(value);\n    const [numberPart, expPart] = valueAsString.split('e-');\n    let valueWithoutExp = valueAsString;\n    if (expPart) {\n        const [, fractionalPart = ''] = numberPart?.split('.') ?? [];\n        const decimalDigits = Number(expPart) + (fractionalPart?.length || 0);\n        valueWithoutExp = value.toFixed(decimalDigits);\n    }\n    return valueWithoutExp;\n}\n\n/**\n * Return fractional part of number\n *\n * @param value the number\n * @param precision number of digits of decimal part, null to keep untouched\n * @return the fractional part of number\n */\nfunction tuiGetFractionPartPadded(value, precision) {\n    const [, fractionPartPadded = ''] = tuiNumberToStringWithoutExp(value).split('.');\n    return tuiIsNumber(precision)\n        ? fractionPartPadded.slice(0, Math.max(0, precision))\n        : fractionPartPadded;\n}\n\n/**\n * Formats number adding a thousand separators and correct decimal separator\n * padding decimal part with zeroes to given length\n *\n * @param value the input number\n * @param settings See {@link TuiNumberFormatSettings}\n * @return the formatted string\n */\nfunction tuiFormatNumber(value, settings = {}) {\n    const { precision, decimalSeparator, thousandSeparator, decimalMode, rounding } = {\n        ...TUI_DEFAULT_NUMBER_FORMAT,\n        decimalMode: 'always',\n        precision: Infinity,\n        ...settings,\n    };\n    const rounded = Number.isFinite(precision)\n        ? tuiRoundWith({ value, precision, method: rounding })\n        : value;\n    const integerPartString = String(Math.floor(Math.abs(rounded)));\n    let fractionPartPadded = tuiGetFractionPartPadded(rounded, precision);\n    const hasFraction = Number(fractionPartPadded) > 0;\n    if (Number.isFinite(precision)) {\n        if (decimalMode === 'always' || (hasFraction && decimalMode === 'pad')) {\n            const zeroPaddingSize = Math.max(precision - fractionPartPadded.length, 0);\n            const zeroPartString = '0'.repeat(zeroPaddingSize);\n            fractionPartPadded = `${fractionPartPadded}${zeroPartString}`;\n        }\n        else {\n            fractionPartPadded = fractionPartPadded.replace(/0*$/, '');\n        }\n    }\n    const remainder = integerPartString.length % 3;\n    const sign = value < 0 ? CHAR_HYPHEN : '';\n    let result = sign + integerPartString.charAt(0);\n    for (let i = 1; i < integerPartString.length; i++) {\n        if (i % 3 === remainder && integerPartString.length > 3) {\n            result += thousandSeparator;\n        }\n        result += integerPartString.charAt(i);\n    }\n    return fractionPartPadded ? result + decimalSeparator + fractionPartPadded : result;\n}\n\n/**\n * Converts a string to an HSL color\n * @param value string to convert\n * @return HSL color string\n */\n// @bad TODO: convert stringHashToHsl to stringHashToRgb\nfunction tuiStringHashToHsl(value) {\n    if (value === '') {\n        return '';\n    }\n    let hash = 0;\n    for (let i = 0; i < value.length; i++) {\n        hash = value.charCodeAt(i) + ((hash << 5) - hash);\n        hash &= hash;\n    }\n    const hue = hash % 360;\n    const saturation = 60 + (hash % 5);\n    const lightness = 80 + (hash % 5);\n    return `hsl(${hue},${saturation}%,${lightness}%)`;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tuiFormatNumber, tuiGetFractionPartPadded, tuiNumberToStringWithoutExp, tuiStringHashToHsl };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,yBAAyB;AACrD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,yBAAyB,QAAQ,uBAAuB;AACjE,SAASC,WAAW,QAAQ,mCAAmC;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,2BAA2BA,CAACC,KAAK,EAAE;EACxC,MAAMC,aAAa,GAAGC,MAAM,CAACF,KAAK,CAAC;EACnC,MAAM,CAACG,UAAU,EAAEC,OAAO,CAAC,GAAGH,aAAa,CAACI,KAAK,CAAC,IAAI,CAAC;EACvD,IAAIC,eAAe,GAAGL,aAAa;EACnC,IAAIG,OAAO,EAAE;IACT,MAAM,GAAGG,cAAc,GAAG,EAAE,CAAC,GAAGJ,UAAU,EAAEE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE;IAC5D,MAAMG,aAAa,GAAGC,MAAM,CAACL,OAAO,CAAC,IAAIG,cAAc,EAAEG,MAAM,IAAI,CAAC,CAAC;IACrEJ,eAAe,GAAGN,KAAK,CAACW,OAAO,CAACH,aAAa,CAAC;EAClD;EACA,OAAOF,eAAe;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,wBAAwBA,CAACZ,KAAK,EAAEa,SAAS,EAAE;EAChD,MAAM,GAAGC,kBAAkB,GAAG,EAAE,CAAC,GAAGf,2BAA2B,CAACC,KAAK,CAAC,CAACK,KAAK,CAAC,GAAG,CAAC;EACjF,OAAOP,WAAW,CAACe,SAAS,CAAC,GACvBC,kBAAkB,CAACC,KAAK,CAAC,CAAC,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,SAAS,CAAC,CAAC,GACnDC,kBAAkB;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,eAAeA,CAAClB,KAAK,EAAEmB,QAAQ,GAAG,CAAC,CAAC,EAAE;EAC3C,MAAM;IAAEN,SAAS;IAAEO,gBAAgB;IAAEC,iBAAiB;IAAEC,WAAW;IAAEC;EAAS,CAAC,GAAG;IAC9E,GAAG1B,yBAAyB;IAC5ByB,WAAW,EAAE,QAAQ;IACrBT,SAAS,EAAEW,QAAQ;IACnB,GAAGL;EACP,CAAC;EACD,MAAMM,OAAO,GAAGhB,MAAM,CAACiB,QAAQ,CAACb,SAAS,CAAC,GACpCjB,YAAY,CAAC;IAAEI,KAAK;IAAEa,SAAS;IAAEc,MAAM,EAAEJ;EAAS,CAAC,CAAC,GACpDvB,KAAK;EACX,MAAM4B,iBAAiB,GAAG1B,MAAM,CAACc,IAAI,CAACa,KAAK,CAACb,IAAI,CAACc,GAAG,CAACL,OAAO,CAAC,CAAC,CAAC;EAC/D,IAAIX,kBAAkB,GAAGF,wBAAwB,CAACa,OAAO,EAAEZ,SAAS,CAAC;EACrE,MAAMkB,WAAW,GAAGtB,MAAM,CAACK,kBAAkB,CAAC,GAAG,CAAC;EAClD,IAAIL,MAAM,CAACiB,QAAQ,CAACb,SAAS,CAAC,EAAE;IAC5B,IAAIS,WAAW,KAAK,QAAQ,IAAKS,WAAW,IAAIT,WAAW,KAAK,KAAM,EAAE;MACpE,MAAMU,eAAe,GAAGhB,IAAI,CAACC,GAAG,CAACJ,SAAS,GAAGC,kBAAkB,CAACJ,MAAM,EAAE,CAAC,CAAC;MAC1E,MAAMuB,cAAc,GAAG,GAAG,CAACC,MAAM,CAACF,eAAe,CAAC;MAClDlB,kBAAkB,GAAG,GAAGA,kBAAkB,GAAGmB,cAAc,EAAE;IACjE,CAAC,MACI;MACDnB,kBAAkB,GAAGA,kBAAkB,CAACqB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC9D;EACJ;EACA,MAAMC,SAAS,GAAGR,iBAAiB,CAAClB,MAAM,GAAG,CAAC;EAC9C,MAAM2B,IAAI,GAAGrC,KAAK,GAAG,CAAC,GAAGL,WAAW,GAAG,EAAE;EACzC,IAAI2C,MAAM,GAAGD,IAAI,GAAGT,iBAAiB,CAACW,MAAM,CAAC,CAAC,CAAC;EAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,iBAAiB,CAAClB,MAAM,EAAE8B,CAAC,EAAE,EAAE;IAC/C,IAAIA,CAAC,GAAG,CAAC,KAAKJ,SAAS,IAAIR,iBAAiB,CAAClB,MAAM,GAAG,CAAC,EAAE;MACrD4B,MAAM,IAAIjB,iBAAiB;IAC/B;IACAiB,MAAM,IAAIV,iBAAiB,CAACW,MAAM,CAACC,CAAC,CAAC;EACzC;EACA,OAAO1B,kBAAkB,GAAGwB,MAAM,GAAGlB,gBAAgB,GAAGN,kBAAkB,GAAGwB,MAAM;AACvF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,kBAAkBA,CAACzC,KAAK,EAAE;EAC/B,IAAIA,KAAK,KAAK,EAAE,EAAE;IACd,OAAO,EAAE;EACb;EACA,IAAI0C,IAAI,GAAG,CAAC;EACZ,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxC,KAAK,CAACU,MAAM,EAAE8B,CAAC,EAAE,EAAE;IACnCE,IAAI,GAAG1C,KAAK,CAAC2C,UAAU,CAACH,CAAC,CAAC,IAAI,CAACE,IAAI,IAAI,CAAC,IAAIA,IAAI,CAAC;IACjDA,IAAI,IAAIA,IAAI;EAChB;EACA,MAAME,GAAG,GAAGF,IAAI,GAAG,GAAG;EACtB,MAAMG,UAAU,GAAG,EAAE,GAAIH,IAAI,GAAG,CAAE;EAClC,MAAMI,SAAS,GAAG,EAAE,GAAIJ,IAAI,GAAG,CAAE;EACjC,OAAO,OAAOE,GAAG,IAAIC,UAAU,KAAKC,SAAS,IAAI;AACrD;;AAEA;AACA;AACA;;AAEA,SAAS5B,eAAe,EAAEN,wBAAwB,EAAEb,2BAA2B,EAAE0C,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}